<template>
    <div :class="wrapperClass" :style="wrapperStyle">
        <img
            v-if="urlVisible"
            :src="url"
            alt=""
            class="abc-avatar-image"
        />

        <abc-icon
            v-else
            :icon="iconName"
            :size="iconSize"
        ></abc-icon>
    </div>
</template>

<script>
    export default {
        name: 'AbcAvatar',
        props: {
            url: {
                type: String,
                default: () => '',
            },

            size: {
                type: String,
                default: () => 'small', // small, normal, mid, large
            },

            borderVisible: {
                type: Boolean,
                default: () => true,
            },

            isMale: {
                type: Boolean,
                default: () => true,
            },

            isVip: {
                type: Boolean,
                default: () => false,
            },

            borderHiddenColor: {
                type: String,
                default: () => 'transparent',
            },
        },
        data() {
            return {
                urlVisible: true,
            };
        },
        computed: {
            iconName() {
                if (this.isMale) {
                    if (this.isVip) {
                        return 's-man-vip-color';
                    }
                    return 's-user-color';
                }
                if (this.isVip) {
                    return 's-woman-vip-color';
                }
                return 's-woman-color';
            },
            wrapperClass() {
                return [
                    'abc-avatar-wrapper',
                    this.size,
                ];
            },
            wrapperStyle() {
                if (this.borderVisible) return {};

                return {
                    'border-color': this.borderHiddenColor,
                };
            },
            iconSize() {
                if (this.size === 'small') return '14';

                if (this.size === 'normal') return '14';

                if (this.size === 'mid') return '22';

                return '24';
            },
            iconColor() {
                if (this.isMale) return '#58A0FF';
                return '#FF6082';
            },
        },
        watch: {
            url: {
                handler(val) {
                    if (!val) {
                        this.urlVisible = false;
                        return;
                    }

                    const _img = new Image();
                    _img.onload = async () => {
                        this.urlVisible = true;
                    };

                    _img.onerror = () => {
                        this.urlVisible = false;
                    };

                    _img.src = val;
                },
                immediate: true,
            },
        },
    };
</script>

<style scoped lang="scss">
.abc-avatar-wrapper {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: #ffffff;
    border: 1px solid var(--abc-color-P4);
    border-radius: var(--abc-border-radius-mini);

    &.small {
        width: 20px;
        height: 20px;
    }

    &.normal {
        width: 24px;
        height: 24px;
    }

    &.mid {
        width: 36px;
        height: 36px;
    }

    &.large {
        width: 40px;
        height: 40px;
    }

    .abc-avatar-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
}
</style>
