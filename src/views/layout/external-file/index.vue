<template>
    <div
        ref="abcinput"
        class="content-wrapper external-files-wrapper"
        :class="{
            'no-data': noData, 'external-files-wrapper-hidden-left': hiddenLeft
        }"
    >
        <div
            v-for="(item, index) in currentAttachments"
            :key="item.uuid || item.id || index"
            class="item"
            :style="{
                width: width, height: height
            }"
            @click="previewIt(item,index)"
        >
            <abc-progress v-if="item.imgLoading" :percentage="item.percentage">
            </abc-progress>
            <div class="external-files-preview-wrapper">
                <abc-file-view
                    v-show="item.url && !item.imgLoading"
                    :file="item"
                    :width="width"
                    :height="height"
                    :show-image-name="showImageName"
                ></abc-file-view>
                <div class="preview-operate">
                    <slot name="previewOperate" :item="item" :index="index"></slot>
                </div>
            </div>
            <div v-if="!disabled" class="delete-bar" @click.stop="deleteItem(item, index)"></div>
        </div>

        <div
            v-show="isVisibleAddBtn && !imgLoading && (!disabled || disabledClick) && currentAttachments.length < 20"
            class="file-add"
            :style="{
                width: width, height: height
            }"
            @click="triggerFileInput"
        >
            <div class="add-icon">
                <abc-icon :icon="addIcon" color="var(--abc-color-T3)" size="16"></abc-icon>
                <p v-if="fileText">
                    {{ fileText }}
                </p>
            </div>
            <input
                id="file-add"
                ref="fileInput"
                type="file"
                style="display: none;"
                multiple="multiple"
                :accept="accept"
                @change="fileChange"
            />
        </div>

        <abc-preview
            v-if="showPreview"
            v-model="showPreview"
            :index="previewIndex"
            enable-compress
            :lists="currentAttachments"
        ></abc-preview>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import { createGUID } from 'utils/index';
    import { accessCommonImagePdf } from 'src/assets/configure/access-file';
    import { UploadWaysDialog } from 'src/views/layout/upload-ways-dialog/index.js';
    import { medicalImagingViewerDialogService } from '@/medical-imaging-viewer/store/medical-imaging-viewer-dialog';
    import Compressor from 'compressorjs';

    export default {
        props: {
            selectedIndex: {
                type: Number,
                default: -1,
            },
            fileText: {
                type: String,
                default: '',
            },
            value: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            width: {
                type: String,
                default: '44px',
            },
            height: {
                type: String,
                default: '44px',
            },
            disabled: Boolean,
            disabledClick: {
                type: Boolean,
                default: false,
            },
            hiddenLeft: Boolean,
            // 患者基本信息
            patientInfo: {
                type: Object,
                default: () => ({}),
            },
            // 业务类型
            businessType: {
                type: Number,
            },
            // 业务Id
            businessId: {
                type: String,
                default: '',
            },
            // 业务描述，如当前业务的标题，在mb上展示
            businessDesc: String,
            // 手机上传图片的最大数量
            maxUploadCount: {
                type: Number,
                default: 20,
            },
            maxUploadSize: {
                type: Number,
                default: Infinity,
            },
            uploadDescription: {
                type: String,
                default: '报告附件支持png、jpg、pdf格式',
            },
            /**
             * @description: 示例图片
             * @date: 2024-12-26 11:31:14
             * @author: Horace
             */
            previewUrl: {
                type: String,
                default: '',
            },
            accept: {
                type: [Array, String],
                default() {
                    return accessCommonImagePdf();
                },
            },
            // 图片上传目录
            ossFilepath: {
                type: String,
            },
            unableExecute: {
                type: Boolean,
                default: false,
            },
            showImageName: {
                type: Boolean,
                default: false,
            },
            handleOptions: {
                type: Function,
                default() {
                    return () => true;
                },
            },
            // pc限制的最大数
            isLimitMaxCount: {
                type: Boolean,
                default: false,
            },
            isOnlyPreviewImg: {
                type: Boolean,
                default: false,
            },
            addIcon: {
                type: String,
                default: 's-b-add-line-medium',
            },
            maxCountOverTip: {
                type: String,
                default: '',
            },
            /**
             * @description: 是否启用图片压缩
             * @date: 2025-06-16
             * @author: AI Assistant
             */
            enableCompress: {
                type: Boolean,
                default: false,
            },
            /**
             * @description: 压缩质量 (0-1)
             * @date: 2025-06-16
             * @author: AI Assistant
             */
            compressQuality: {
                type: Number,
                default: 0.8,
            },
            /**
             * @description: 压缩后最大宽度
             * @date: 2025-06-16
             * @author: AI Assistant
             */
            compressMaxWidth: {
                type: Number,
                default: 1400,
            },
            /**
             * @description: 压缩后最大高度
             * @date: 2025-06-16
             * @author: AI Assistant
             */
            compressMaxHeight: {
                type: Number,
                default: 1400,
            },
        },
        data() {
            return {
                currentIndex: -1,
                photoUrl: '',
                uploadWaysDialogVisible: false,
                timer: null,
                showPreview: false,
                previewIndex: 0,
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'isIntranetUser']),
            ...mapGetters('fileSync',[
                'enableFileSync',
            ]),
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },
            imgLoading() {
                return this.currentAttachments.some((item) => item.imgLoading);
            },

            currentAttachments: {
                get() {
                    const newVal = this.value;
                    newVal?.sort((a, b) => a.sort - b.sort);
                    return newVal || [];
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            noData() {
                return this.currentAttachments.length === 0;
            },

            // 是否显示新增按钮
            isVisibleAddBtn() {
                if (
                    this.isLimitMaxCount &&
                    this.currentAttachments.length >= this.maxUploadCount
                ) {
                    return false;
                }
                return true;
            },
        },
        beforeDestroy() {
            clearTimeout(this.timer);
        },
        methods: {
            triggerFileInput(event) {
                if (this.disabledClick) {
                    return ;
                }
                if (this.unableExecute) {
                    this.handleOptions();
                    return;
                }
                if (event && event.target === this.$refs.fileInput) return;
                this.$emit('current-index', this.selectedIndex);
                this.uploadWaysDialogVisible = true;
                new UploadWaysDialog({
                    visible: this.uploadWaysDialogVisible,
                    clinicId: this.clinicId,
                    maxUploadCount: this.maxUploadCount - this.currentAttachments.length,
                    maxUploadLimit: this.maxUploadCount,
                    patientInfo: this.patientInfo,
                    uploadDescription: this.uploadDescription,
                    businessType: this.businessType,
                    businessId: this.businessId,
                    businessDesc: this.businessDesc,
                    previewDesc: '查看示例',
                    previewUrl: this.previewUrl,
                    disabledMobileUpload: this.isIntranetUser,
                    pcUploadWayHandler: () => {
                        this.$refs.fileInput.click();
                    },
                }).generateDialog({ parent: this });
            },

            // 文件上传
            fileChange(e) {
                if (e.currentTarget.files.length === 0) {
                    return;
                }
                if (e.target.files[ 0 ].size > this.maxUploadSize) {
                    e.target.value = null;
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '上传附件不能超过10M',
                    });
                    return;
                }
                this.validateFormatUploadData(e);
            },

            async validateFormatUploadData(event) {
                const files = [];
                const _length = event.currentTarget.files.length;
                for (let i = 0; i < _length; i++) {
                    const file = event.currentTarget.files[i];
                    const isCorrectSize = file.size <= 1024 * 1024 * 100;
                    const isCorrectLength = this.currentAttachments.length + files.length < this.maxUploadCount;
                    if (isCorrectSize && isCorrectLength) {
                        files.push(file);
                    }
                }

                if (this.currentAttachments.length + event.currentTarget.files.length > this.maxUploadCount) {
                    this.$Toast({
                        message: this.maxCountOverTip ||
                            `${event.currentTarget.files.length - files.length}张图片上传失败：最多只能添加${this.maxUploadCount}张图片`,
                        type: 'error',
                    });
                    event.target.value = null;
                    return;
                }
                if (event.currentTarget.files.length !== files.length) {
                    // 提示
                    this.$Toast({
                        message: `${event.currentTarget.files.length - files.length}张图片上传失败：单张图片/PDF大小不能超过100MB`,
                        type: 'error',
                    });
                    event.target.value = null;
                    return;
                }

                // 压缩图片文件
                let compressedFiles = files;
                if (this.enableCompress) {
                    try {
                        compressedFiles = await this.compressFiles(files);
                    } catch (error) {
                        console.warn('文件压缩处理失败:', error);
                        // 压缩失败时使用原文件
                        compressedFiles = files;
                    }
                }

                // 文件发生改变
                // 先生成缩略图
                // 设置一个空缩略图
                compressedFiles.forEach((file, index) => {
                    const UUID = createGUID();
                    const fileTemp = {
                        uuid: UUID,
                        url: '',
                        fileName: file.name,
                        fileSize: file.size,
                        sort: this.getSortNum(this.currentAttachments.length - 1) + index,
                        imgLoading: true,
                        percentage: 0,
                    };
                    this.ossUpload(event, file, UUID, fileTemp);
                });
            },

            waiting(duration) {
                return new Promise((resolve) => {
                    this.timer = setTimeout(() => {
                        resolve();
                    }, duration);
                });
            },

            async handleAddAttachments(outpatientSheetId, fileTemp, url) {
                const {
                    fileName,
                    fileSize,
                    sort,
                    imgLoading,
                } = fileTemp;

                try {
                    this.currentAttachments.push({
                        fileName,
                        fileSize,
                        sort,
                        imgLoading,
                        url,
                        percentage: 100,
                    });
                    await this.waiting(300);
                    this.currentAttachments = this.currentAttachments.map((item) => {
                        return {
                            ...item,
                            imgLoading: this.$set(item, 'imgLoading', false),
                            percentage: this.$set(item, 'percentage', 0),
                        };
                    });
                    this.$emit('file-change');
                } catch (err) {
                    console.error(err);
                }
            },

            async handleDeleteAttachments(index) {
                try {
                    this.currentAttachments.splice(index, 1);
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    this.$emit('file-change');
                } catch (err) {
                    console.error(err);
                }
            },
            async ossUpload(event, file, UUID, fileTemp) {
                try {
                    const imgRes = await this.$abcPlatform.service.oss.clinicUsageUpload(
                        { filePath: this.ossFilepath },
                        file,
                    );
                    this.handleAddAttachments('', fileTemp, imgRes.url);
                    event.target.value = null;
                } catch (err) {
                    console.debug(`catch:${err}`);
                    event.target.value = null;
                    fileTemp.imgLoading = false;
                    this.$Toast({
                        message: '图片上传失败',
                        type: 'error',
                    });
                    const index = this.currentAttachments.findIndex((item) => {
                        return item.uuid === UUID;
                    });
                    this.currentAttachments.splice(index, 1);
                    throw new Error(err);
                }
            },

            previewIt(item,index) {
                if (this.disabledClick) {
                    return;
                }
                if (this.isOnlyPreviewImg) {
                    this.previewIndex = index;
                    this.showPreview = true;
                } else {
                    medicalImagingViewerDialogService.previewImageAttachment({
                        attachments: this.currentAttachments,
                        attachment: item,
                    });
                }
            },

            deleteItem(item, index) {
                const str = '确定删除该文件附件吗？';
                this.$confirm({
                    type: 'warn',
                    title: '删除提示',
                    content: `删除后无法恢复，${str}`,
                    contentStyles: 'width: 400px',
                    onConfirm: () => {
                        this.handleDeleteAttachments(index, item?.id);
                    },
                });
            },
            getSortNum(lastIndex) {
                let sort = 0;
                if (lastIndex < this.currentAttachments.length && lastIndex >= 0) {
                    const lastItem = this.currentAttachments[lastIndex];
                    sort = lastItem && !isNaN(lastItem.sort) ? (lastItem.sort + 1) : 0;
                }
                return sort;
            },

            /**
             * @description: 图片压缩方法
             * @param {File} file - 需要压缩的图片文件
             * @returns {Promise<File>} - 压缩后的文件
             * @author: AI Assistant
             * @date: 2025-06-16
             */
            compressImage(file) {
                return new Promise((resolve) => {
                    // 如果不启用压缩，直接返回原文件
                    if (!this.enableCompress) {
                        resolve(file);
                        return;
                    }

                    // 检查是否为图片文件
                    if (!file.type.startsWith('image/')) {
                        resolve(file);
                        return;
                    }

                    // 如果文件小于1.5MB，不需要压缩
                    const fileSizeMB = file.size / 1024 / 1024;
                    if (fileSizeMB <= 1.5) {
                        resolve(file);
                        return;
                    }


                    new Compressor(file, {
                        quality: this.compressQuality,
                        maxWidth: this.compressMaxWidth,
                        maxHeight: this.compressMaxHeight,
                        convertSize: 2000000, // 1MB以上的图片才转换格式
                        success: (compressedFile) => {
                            // 保持原文件名
                            const resultFile = new File([compressedFile], file.name, {
                                type: compressedFile.type,
                                lastModified: Date.now(),
                            });

                            console.log('图片压缩成功:', {
                                原始大小: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
                                压缩后大小: `${(resultFile.size / 1024 / 1024).toFixed(2)}MB`,
                            });

                            resolve(resultFile);
                        },
                        error: () => {
                            resolve(file);
                        },
                    });
                });
            },

            /**
             * @description: 批量压缩图片
             * @param {Array} files - 文件数组
             * @returns {Promise<Array>} - 压缩后的文件数组
             * @author: AI Assistant
             * @date: 2025-06-16
             */
            async compressFiles(files) {
                if (!files || !files.length) {
                    return files;
                }

                const compressPromises = files.map((file) => this.compressImage(file));
                return Promise.all(compressPromises);
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';
@import 'src/styles/mixin.scss';

.external-files-wrapper {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin: 4px 2px;

    &-hidden-left {
        margin-left: 0 !important;

        .item:first-child {
            margin-left: 0 !important;
        }
    }

    .item,
    .file-add {
        position: relative;
        // width: 44px;
        // height: 44px;
        margin: 4px;
        text-align: center;
        cursor: pointer;
        border-radius: var(--abc-border-radius-small);

        .delete-bar {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 16px;
            height: 16px;
            visibility: hidden;
            background: url('~@/assets/images/<EMAIL>') no-repeat center;
            background-size: contain;
            opacity: 0;
            transition: opacity 0.2s;

            &:hover {
                background: url('~@/assets/images/<EMAIL>') no-repeat center;
                background-size: contain;
            }
        }

        &:hover .delete-bar {
            visibility: visible;
            opacity: 1;
        }

        .view-box {
            border-radius: var(--abc-border-radius-small);
        }
    }

    .file-add {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 1px dashed $P1;

        .add-icon {
            p {
                font-size: 12px;
                color: $T2;
            }
        }

        span {
            margin-top: 8px;
            font-size: 12px;
            color: $T3;
        }

        &:hover {
            border: 1px solid $P1 !important;
        }
    }
}
</style>
