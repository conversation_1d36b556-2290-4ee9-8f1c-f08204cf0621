<template>
    <div class="inspect_project-info--box">
        <div class="inspect_project-info--box-form">
            <h3 class="inspect_project-info--box-form-title">
                基础信息
            </h3>

            <abc-form-item-group grid :grid-column-count="1">
                <abc-form-item
                    :error="errorName"
                    :validate-event="
                        (_, callback) => callback({
                            validate: !errorName.error, message: errorName.message
                        })
                    "
                    label="项目名称"
                    required
                    class="form-item-name"
                >
                    <abc-flex align="center" :gap="8">
                        <abc-autocomplete
                            v-model="projectInfo.name"
                            :class="{ 'is-disabled': goodsDisabled }"
                            :auto-focus-first="false"
                            :disabled="isDeviceInnerFlag"
                            :fetch-suggestions="_debounceFetchSuggestions"
                            :max-length="60"
                            async-fetch
                            custom-item="examinationItem"
                            focus-show
                            popper-class="examination-autocomplete"
                            @enterEvent="selectInspect"
                            @input="() => (errorName.error = false)"
                        >
                            <template slot="suggestions" slot-scope="props">
                                <dt
                                    :class="{ selected: props.index === props.currentIndex }"
                                    class="suggestions-item"
                                    @click="selectInspect(props.suggestion)"
                                >
                                    <div>
                                        {{ props.suggestion.name }}
                                        <template
                                            v-if="props.suggestion.bizExtensions && props.suggestion.bizExtensions.examinationMethod"
                                        >
                                            ({{ props.suggestion.bizExtensions.examinationMethod }})
                                        </template>
                                    </div>
                                    <div style="margin-left: 20px;">
                                        {{ props.suggestion.code }}
                                    </div>
                                </dt>
                            </template>

                            <span v-if="goodsDisabled" slot="append">已停用</span>
                        </abc-autocomplete>

                        <abc-button
                            v-if=" !projectId && isAdmin && !goodsDisabled && !isTemplateClinic"
                            class="model-button"
                            type="blank"
                            :disabled="!isAdmin || isSubDialog"
                            @click="handleSelectTemplate"
                        >
                            模板
                        </abc-button>
                    </abc-flex>
                </abc-form-item>

                <abc-form-item v-if="isSimpleCreateProjectStatus" required label="检查设备">
                    <abc-select
                        v-model="projectInfo.bizRelevantId"
                        :disabled="getDeviceFormItemState().isDisabled"
                        adaptive-width
                    >
                        <abc-option
                            v-for="(option, idx) in getDeviceFormItemState().deviceOptions"
                            :key="idx"
                            v-bind="option"
                        >
                            {{ option.label }}
                        </abc-option>
                    </abc-select>
                </abc-form-item>

                <abc-form-item
                    v-if="!!projectId"
                    label="项目编码"
                >
                    <abc-input
                        v-model="projectInfo.shortId"
                        :max-length="20"
                        type="number-en-char"
                        :disabled="isDisabled"
                        style="width: 100%;"
                        @input="() => (errorItemCode.error = false)"
                    ></abc-input>
                </abc-form-item>

                <abc-form-item label="二级分类">
                    <secondary-classification-select
                        v-model="projectInfo.customTypeId"
                        :disabled="isDeviceInnerFlag"
                        :type-id="primaryClassification.id"
                        adaptive-width
                    ></secondary-classification-select>
                </abc-form-item>

                <abc-form-item
                    label="单位"
                    class="sale-price-unit-box"
                    :validate-event="
                        (_, callback) =>
                            callback({
                                validate: validateUnit,
                                message: '单位不能为空',
                            })
                    "
                >
                    <select-usage
                        v-model="projectInfo.packageUnit"
                        class="sale-unit"
                        style="width: 100%; height: 32px;"
                        :li-width="75.5"
                        :text-center="true"
                        type="customUnit"
                        :disabled="isDeviceInnerFlag"
                        :options="unitArray"
                        @modify-options="showUnitDialog = true"
                    >
                    </select-usage>
                </abc-form-item>

                <abc-form-item label="检查要求">
                    <abc-input
                        v-model="projectInfo.bizExtensions.samplingReq"
                        placeholder="输入检查要求"
                        :disabled="isDisabled"
                        style="width: 100%;"
                    ></abc-input>
                </abc-form-item>
            </abc-form-item-group>
        </div>

        <unit-editor
            v-if="showUnitDialog"
            v-model="showUnitDialog"
            :type="3"
            @update-unit="updateUnit"
        ></unit-editor>
    </div>
</template>

<script>
    import { debounce } from 'utils/lodash';
    import SettingAPI from 'api/settings';
    import { mapGetters } from 'vuex';
    import UnitEditor from 'views/settings/diagnosis-treatment/components/unit-editor';
    import SelectUsage from 'views/layout/select-group';
    import TreatmentApi from 'api/treatment';
    import { ExaminationSubTypeEnum } from 'views/common/enum';
    import { GoodsTypeEnum } from '@abc/constants';

    const SecondaryClassificationSelect = () => import('views/inventory/goods/components/secondary-classification/secondary-select');

    export default {
        name: 'InspectProjectInfoBox',
        components: {
            UnitEditor,
            SelectUsage,
            SecondaryClassificationSelect,
        },
        props: {
            projectId: {
                type: String,
                default: '',
            },
            projectInfo: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            combineType: {
                type: Number,
                default: 0,
            },
            isTemplateClinic: {
                type: Boolean,
                default: false,
            },
            isSimpleCreateProjectStatus: Boolean,
            openReadOnlyModel: Boolean,// 阅读模式
            isModify: Boolean,
            isSubDialog: Boolean,
            deviceModelList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                loading: false,

                isOwnerInputPrice: false,
                showUnitDialog: false,
                sysUnitList: [],
                customUnitList: [],
                innerFlag: 1,
                errorName: {
                    error: false,
                    message: '项目名已存在',
                },
                errorItemCode: {
                    error: false,
                    message: '项目编码重复',
                },
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'goodsPrimaryClassification',
            ]),

            goodsDisabled() {
                return !!this.projectInfo?.disable;
            },
            /**
             * @desc 一级分类
             */
            primaryClassification() {
                return (
                    this.goodsPrimaryClassification.find((item) => {
                        return item.goodsType === GoodsTypeEnum.EXAMINATION && item.goodsSubType === this.projectInfo.subType;
                    }) || { id: '' }
                );
            },
            unitArray() {
                return this.sysUnitList.concat(this.customUnitList);
            },
            validateUnit() {
                return !(this.projectInfo.packagePrice && !this.projectInfo.packageUnit);
            },
            isDisabled() {
                return !this.isAdmin || this.goodsDisabled;
            },
            isDeviceInnerFlag() {
                if (this.isTemplateClinic) {
                    return false;
                }
                return this.isDisabled;
            },
            deviceModelOptions() {
                const deviceModelOptions = this.deviceModelList.map((item) => ({
                    label: item?.name || '',
                    value: item.deviceModelId,
                    innerFlag: item.innerFlag,
                }));

                return deviceModelOptions;
            },
            defaultDeviceModel() {
                return this.deviceModelList.find((item) => item.innerFlag) || {};
            },
            useDeviceModelOptions() {
                if (!this.isTemplateClinic) {
                    return [{
                        label: this.defaultDeviceModel.name,
                        value: this.defaultDeviceModel.deviceModelId,
                    }];
                }
                return this.deviceModelOptions;
            },
        },
        async created() {
            this._debounceFetchSuggestions = debounce(this.fetchSuggestions, 250, true);
            await this.getCustomUnit();
        },
        methods: {
            // 初始化报错 callBack 回掉报错内容
            initError(type = 'init',callBack = {}) {
                if (type === 'init') {
                    this.resetError();
                    this.resetError('errorItemCode');
                }
                if (type === 'change') {
                    if (callBack?.name) {
                        this.setError(callBack.name);
                        // this[callBack.prop].error = false;
                        this.reValidate();
                    }
                }

            },

            reValidate() {
                // 再触发一次验证
                // 滚动到可视区域
                this.$refs.$parent?.$refs.form.validate();
            },

            setError(prop = 'errorName') {
                if ((prop === 'errorName' && this.projectInfo.name) || prop !== 'errorName') {
                    this[prop].error = true;
                }
            },

            resetError(prop = 'errorName') {
                this[prop].error = false;
            },

            handleSelectTemplate() {
                this.$emit('handleSelectTemplate');
            },

            // 拉取单位数据
            async getCustomUnit() {
                try {
                    this.updateData(await TreatmentApi.fetchCustomUnit(3));
                } catch (e) {
                    console.error(e);
                }
            },

            updateUnit(response) {
                // 更新已选择的单位
                if (this.projectInfo.packageUnit) {
                    const selected = this.unitArray.find((it) => it.name === this.projectInfo.packageUnit);
                    if (selected) {
                        const {
                            data: {
                                sysUnitList = [],
                                customUnitList = [],
                            },
                        } = response;
                        const _unitArray = sysUnitList.concat(customUnitList);
                        const updated = _unitArray.find((it) => it.id === selected.id);
                        if (updated && this.projectInfo.packageUnit !== updated.name) {
                            console.log('单位options更新:', updated.name);
                            this.projectInfo.packageUnit = updated.name;
                        }
                        if (!updated) {
                            console.log('单位options已删除:', selected.name);
                            this.projectInfo.packageUnit = '';
                        }
                    }
                }
                this.updateData(response);
            },

            updateData(response) {
                const {
                    data: {
                        sysUnitList,
                        customUnitList,
                    },
                } = response;
                this.sysUnitList = sysUnitList || [];
                this.customUnitList = customUnitList || [];
            },

            async selectInspect(val) {
                this.$emit('selectInspectSuggestion', val);
            },

            async fetchSuggestions(keyword, next) {
                if (!keyword || keyword.trim() === '') {
                    next([]);
                    return;
                }
                try {
                    const { data } = await SettingAPI.examination.searchSystemGoods({
                        keyword,
                        combineType: this.combineType,
                        deviceModelId: this.isTemplateClinic ? null : this.defaultDeviceModel.deviceModelId,
                        subType: ExaminationSubTypeEnum.INSPECT_TYPE,
                        limit: 999,
                        offset: 0,
                        type: 3,
                    }) || {};
                    const suggestion = data.list || [];
                    next(suggestion);
                } catch (e) {
                    console.log(e);
                }
            },

            // 计算设备的禁用状态和设备选项
            getDeviceFormItemState() {
                // 编辑
                if (this.projectId) {
                    return {
                        isDisabled: true,
                        deviceOptions: this.deviceModelOptions,
                    };
                }
                // 非总部
                if (!this.isAdmin) {
                    return {
                        isDisabled: true,
                        deviceOptions: this.deviceModelOptions,
                    };
                }
                // 模板店 - 新建
                if (this.isTemplateClinic) {
                    return {
                        isDisabled: false,
                        deviceOptions: this.deviceModelOptions,
                    };
                }
                // 非模板店 - 新建
                return {
                    isDisabled: false,
                    deviceOptions: [{
                        label: this.defaultDeviceModel.name,
                        value: this.defaultDeviceModel.deviceModelId,
                    }],
                };
            },
        },
    };
</script>

<style lang="scss">
@import '../../../styles/theme';
@import '../../../styles/mixin';

.inspect_project-info--box {
    width: 100%;

    &-form {
        width: 100%;

        .abc-form-item {
            margin-bottom: 0;
        }

        .abc-space-item {
            width: 100%;
        }

        .inspect_project-row {
            display: flex;
            align-items: center;

            .form-item-name {
                flex: 1;
            }

            .model-button {
                width: 56px;
                min-width: 56px !important;
                padding: 0 13px;
                margin-left: 8px;
            }
        }

        &-title {
            margin-bottom: 16px;
            font-weight: bold;
            color: $T1;
        }

        &-item-auto {
            align-items: flex-start !important;

            .abc-form-item-label {
                height: 26px !important;
                line-height: 26px;
            }
        }

        .price-index-fix {
            margin-bottom: 16px !important;

            .abc-input-wrapper .prepend-input {
                z-index: 2020;
            }

            .sale-price {
                display: inline-flex;
                -webkit-align-items: center;
                align-items: center;
            }
        }

        .sale-price-unit-box {
            .abc-input__inner {
                height: 32px;
                padding: 3px 8px;
                text-align: left;
            }
        }

        .sale-price-box {
            display: inline-flex;

            .sale-price-unit-box {
                margin-bottom: 16px !important;
                margin-left: 8px !important;

                .abc-form-item-label {
                    width: 28px !important;
                    margin-right: 8px !important;
                }

                .abc-input__inner {
                    height: 32px;
                }
            }
        }
    }
}

.pay-mode-wrapper-box {
    display: flex;
    align-items: baseline;
    height: 32px;

    .pay-mode-title {
        width: 84px;
        font-size: 14px;
        color: $T2 !important;
    }

    .pay-mode-select {
        margin: 16px 0 0 10px !important;
    }
}
</style>
