@import "src/styles/theme";
@import 'src/styles/mixin';

.patient-section-wrapper {
    --patient-section-height: 36px;
    --patient-section-inner-height: 34px;
    --patient-section-border-inner-radius: 3px;

    display: flex;
    height: var(--patient-section-height);
    font-size: 14px;

    &.patient-section--size {
        &-small {
            --patient-section-height: 32px;
            --patient-section-inner-height: 30px;
        }

        &-medium {
            --patient-section-height: 40px;
            --patient-section-inner-height: 38px;

            .patient-section__form-wrapper {
                .patient-section__form {
                    .patient-display-name {
                        .icon-wrapper {
                            width: 40px;
                        }
                    }

                    .patient-display-mobile {
                        width: 108px;
                    }
                }
            }
        }
    }

    .patient-section__form-wrapper {
        position: relative;

        .patient-section__form {
            display: flex;
            align-items: center;
            height: var(--patient-section-height);
            background-color: #ffffff;
            border: 1px solid $abcCardBorderColor;
            border-radius: var(--abc-border-radius-small);

            &.is-disabled {
                background-color: $abcBgDisabled;
            }

            .abc-form-item {
                margin-right: 0;
                margin-bottom: 0;

                .input-append-unit {
                    position: absolute;
                    top: 0;
                    right: 0;
                    z-index: 2;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    height: 100%;

                    &.input-append-unit2 {
                        width: 14px;
                    }
                }
            }

            .patient-name-autocomplete {
                .prepend-input {
                    width: 40px;
                    height: var(--patient-section-inner-height);
                    background-color: transparent;
                }

                .abc-input__inner {
                    padding-left: 39px;
                    border-radius: var(--patient-section-border-inner-radius) 0 0 var(--patient-section-border-inner-radius);
                }
            }

            .patient-mobile-autocomplete {
                &.no-card-btn-wrapper {
                    .abc-input__inner {
                        border-radius: 0 var(--patient-section-border-inner-radius) var(--patient-section-border-inner-radius) 0;
                    }
                }
            }

            .patient-mobile-autocomplete-allow-read-card {
                &.no-card-btn-wrapper {
                    .abc-input__inner {
                        padding: 3px 2px 3px 1px !important;
                    }
                }
            }

            .abc-input-wrapper {
                .append-input {
                    min-width: 18px;
                    padding: 0 2px;
                    background: transparent;
                    border-color: transparent;
                }
            }

            .abc-input__inner {
                height: var(--patient-section-inner-height);
                padding-right: 4px;
                color: $T1;
                border-color: transparent;
                border-radius: 0;
            }

            .abc-select-wrapper .abc-input__inner {
                line-height: var(--patient-section-inner-height);
            }

            .abc-form-item-content {
                .abc-autocomplete-wrapper {
                    .prepend-input {
                        z-index: 5;
                    }
                }
            }

            .display-item {
                display: flex;
                align-items: center;
                height: 100%;
                line-height: 20px;

                &.is-error {
                    color: $R2 !important;
                }
            }

            .patient-display-name {
                width: 110px;

                .icon-wrapper {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 29px;
                }

                >span {
                    flex: 1;
                }
            }

            .patient-display-sex {
                width: 42px;
                padding-left: 5px;

                .abc-icon {
                    margin-left: 3px;
                    color: $T3;
                }
            }

            .patient-display-age {
                width: 94px;

                .first-display-age {
                    width: 50px;
                    text-align: right;

                    > span {
                        display: inline-block;
                        width: 19px;
                        text-align: left;
                    }
                }

                &.has-day {
                    width: 138px;
                }

                div + div {
                    margin-left: 4px;
                }
            }

            .patient-display-mobile {
                width: 104px;
                padding-left: 8px;

                &.no-mobile {
                    color: $T3;
                }

                &.patient-display-mobile-no-delete {
                    width: 138px;
                }

                &.patient-display-mobile-member {
                    width: 167px !important;
                }

                &.patient-display-mobile-member-has-delete {
                    width: 131px !important;

                    &-shebao {
                        width: 128px !important;
                    }
                }
            }

            .display-marital {
                width: 50px;
                padding-left: 8px;
            }

            .patient-display-id-card {
                width: 160px;
                padding-left: 8px;
            }

            .patient-display-tag {
                display: none;
                width: 124px;
                height: 100%;

                &-box {
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    width: 100%;
                    height: 20px;
                    overflow: hidden;

                    &_wechat {
                        display: flex;
                        align-items: center;
                        justify-content: space-around;
                        width: 20px;
                        height: 20px;
                        margin-right: 4px;
                        background-color: #1ec761;
                        border-radius: var(--abc-border-radius-small);

                        img {
                            width: 14px !important;
                            height: 14px !important;
                            margin-right: 0 !important;
                        }
                    }

                    img {
                        width: 20px;
                        height: 20px;
                        margin-right: 4px;
                    }
                }

                &-item {
                    height: 20px;
                    margin-right: 4px;
                }
            }
        }

        .card-btn-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 34px;
            cursor: pointer;

            .read-panel {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;

                &:hover {
                    background-color: #eff3f6;
                }
            }

            .cis-icon-read-card {
                font-size: 14px;
                color: $T1;
            }

            .abc-delete-icon-wrapper {
                color: #cfd1d9;

                &:hover {
                    color: $T3;
                    background-color: $P6;
                }
            }
        }
    }

    .patient-guardian-info {
        display: inline-block;
        max-width: 242px;
        height: 36px;
        padding: 0 8px;
        margin-left: 6px;
        line-height: 36px;
        background-color: #ffffff;
        background-color: $abcBgDisabled;
        border: 1px solid $abcCardBorderColor;
        border-radius: var(--abc-border-radius-small);

        @include ellipsis;
    }

    .add-patient-wrapper {
        .add-patient-card {
            position: relative;

            .crm-module__package-card__base-card-dentistry {
                position: absolute;
                top: 6px;
                left: -170px;
                z-index: 10;
                width: 464px;
                height: auto;
                border-radius: var(--abc-border-radius-small);
                box-shadow: var(--abc-shadow-1);
            }
        }
    }

    .add-patient-wrapper_flex {
        display: flex;
        margin-left: 4px;

        .crm-group-btn {
            width: var(--patient-section-height) !important;
            min-width: var(--patient-section-height) !important;
            height: var(--patient-section-height) !important;
            color: $T1;
        }

        &--left {
            border-right: 1px solid transparent;
            border-radius: var(--abc-border-radius-small) 0 0 var(--abc-border-radius-small);

            &:hover {
                border-right: 1px solid transparent !important;
            }

            &:active {
                border-right: 1px solid transparent !important;
            }
        }

        &--right {
            margin-left: 0 !important;
            border-radius: 0 var(--abc-border-radius-small) var(--abc-border-radius-small) 0;
        }
    }
}

@media screen and (min-width: 1600px) {
    .patient-section-wrapper {
        .patient-section__form-wrapper {
            .patient-section__form {
                .patient-display-tag {
                    display: flex;
                    align-items: center;
                }

                .has-patient-tag {
                    width: 111px !important;
                }

                .no-card-patient-display-tag {
                    width: 148px !important;
                }
            }
        }
    }
}

// 新版 UI 适配
:root[data-abc-theme="pharmacy"] {
    .patient-section-wrapper {
        --patient-section-border-inner-radius: calc(var(--abc-border-radius-small) - 1px);
    }
}
