<template>
    <div
        :class="['patient-section-wrapper',
                 {
                     [`patient-section--size-${size}`]: !!size,
                 }
        ]"
        data-cy="patient-section-wrapper"
    >
        <div
            ref="patientSection"
            v-abc-click-outside="onClickOutside"
            class="patient-section__form-wrapper"
            @mouseenter="handleMouseEnter"
            @mouseleave="handleMouseLeave"
        >
            <div
                class="patient-section__form"
                :class="{ 'is-disabled': disabledForm }"
                data-cy="patient-section-form"
                @click="handleClick"
                @copy="handleCopy"
            >
                <template v-if="disabledForm">
                    <div class="display-item patient-display-name" @click="selectText('patient-display-name')">
                        <div class="icon-wrapper">
                            <abc-icon icon="patient" :size="patientIconSize" :color="iconColor"></abc-icon>
                        </div>
                        <span class="ellipsis">{{ displayPatientName }}</span>
                    </div>
                    <div class="display-item patient-display-sex">
                        <span>{{ patient.sex }}</span>
                        <abc-icon v-if="hasPatientId" icon="dropdown_triangle" size="14"></abc-icon>
                    </div>
                    <div
                        class="display-item patient-display-age"
                        :class="{
                            'has-day': showAgeDay
                        }"
                        v-html="displayPatientAge"
                    >
                    </div>
                    <div v-if="showMarital" class="display-item display-marital">
                        <span>{{ displayMarital }}</span>
                    </div>
                    <div
                        :class="computedMobileClass"
                        @click="selectText('patient-display-mobile')"
                    >
                        <span>{{ displayMobile || '手机号' }}</span>
                    </div>
                    <div
                        v-if="showIdCard"
                        class="display-item patient-display-id-card"
                        :class="{
                            'is-error': requireIdCard && !displayIdCard
                        }"
                    >
                        <span>{{ displayIdCard || (requireIdCard ? '身份证' : '') }}</span>
                    </div>
                    <div
                        v-if="!isRegistration && needPatientBaseComponentShowTags && showPatientTags"
                        class="patient-display-tag"
                        :class="{
                            'no-card-patient-display-tag': !hasCardBtnWrapper
                        }"
                    >
                        <div
                            class="patient-display-tag-box"
                        >
                            <img v-if="displayMember" src="~assets/images/crm/v-yellow-square.png" alt="vip" />
                            <img v-if="hasChargeModule && displayCharge" src="~assets/images/crm/<EMAIL>" alt="arrears" />
                            <img v-if="displayWechat" src="~assets/images/crm/wechat.png" alt="wechat" />
                            <img v-if="displayQiWei" src="~assets/images/crm/qiwei.png" alt="qiwei" />
                            <div
                                v-for="(tag, index) in showTags"
                                :key="`${tag.tagId}-${index}`"
                                class="patient-display-tag-item"
                            >
                                <tag-item
                                    :tag="tag"
                                    :is-need-delete="false"
                                ></tag-item>
                            </div>
                        </div>
                    </div>
                </template>

                <template v-else>
                    <abc-form-item :validate-event="validatePatientName()" :required="needValidateName" trigger="change">
                        <patient-autocomplete
                            ref="autoComplete"
                            :value="patient.name"
                            class="patient-name-autocomplete"
                            :disabled="disabledForm"
                            placeholder="姓名"
                            :width="isShebaoCard ? 207 : (!isRegistration ? 110 : 139)"
                            :is-scan-code="isScanCode"
                            :max-length="40"
                            :distinguish-half-angle-length="true"
                            :readonly="showAddPatientCard"
                            :focus-show="focusShowPatient"
                            :is-can-see-patient-mobile="isCanSeePatientMobile"
                            data-cy="patient-section-autocomplete"
                            :custom-search-func="customSearchFunc"
                            :show-id-card="showIdCard"
                            :need-public-health-info="needPublicHealthInfo"
                            :fixed-show-fields="fixedShowFields"
                            @change="fetchPatient"
                            @enter="enterEvent"
                            @selectPatient="handleSelectPatient"
                            @input="handleInputPatientName"
                            @blur="handlePatientInfoBlur"
                        >
                            <template #prepend>
                                <abc-icon icon="patient" size="14" :color="iconColor"></abc-icon>
                            </template>
                        </patient-autocomplete>
                    </abc-form-item>
                    <!--性别-->
                    <abc-form-item v-if="!isShebaoCard">
                        <abc-select
                            v-model="patient.sex"
                            class="patient-select"
                            :width="!isRegistration ? 42 : 60"
                            :input-style="!isRegistration ? {
                                paddingLeft: '4px', paddingRight: '18px'
                            } : {}"
                            select-type="sex"
                            :disabled="disabledForm"
                            data-cy="patient-section-sex-input"
                            :adaptive-width="false"
                            @change="$emit('change-patient-sex')"
                            @enter="enterEvent"
                            @blur="handlePatientSexBlur"
                        >
                            <abc-option data-cy="option-男" label="男" value="男"></abc-option>
                            <abc-option data-cy="option-女" label="女" value="女"></abc-option>
                        </abc-select>
                    </abc-form-item>
                    <!--年龄-->
                    <abc-form-item
                        v-if="!isShebaoCard"
                        :required="needValidateAge"
                        class="patient-age-year"
                        trigger="change"
                    >
                        <abc-input
                            v-model.number="patient.age.year"
                            v-abc-focus-selected
                            class="patient-age__year"
                            :width="!isRegistration ? 50 : 60"
                            :input-custom-style="!isRegistration ? {
                                'text-align': 'right', padding: '3px 22px 3px 2px'
                            } : {}"
                            type="number"
                            :config="{
                                supportZero: true, max: 199
                            }"
                            :disabled="disabledForm"
                            data-cy="patient-section-year-input"
                            @change="$emit('change-patient-age')"
                            @enter="enterEvent"
                            @blur="handlePatientInfoBlur"
                        >
                        </abc-input>
                        <span class="input-append-unit">岁</span>
                    </abc-form-item>
                    <abc-form-item
                        v-if="!isShebaoCard"
                        class="patient-age-month"
                        :class="{ 'has-age-day': showAgeDay }"
                        :required="needValidateAge"
                        :validate-event="validateMonth"
                        trigger="change"
                    >
                        <abc-input
                            v-model.number="patient.age.month"
                            v-abc-focus-selected
                            :width="!isRegistration ? 44 : 60"
                            :input-custom-style="!isRegistration ? {
                                'text-align': 'right', padding: '3px 22px 3px 2px'
                            } : {}"
                            type="number"
                            :config="{
                                supportZero: true, max: 11
                            }"
                            :disabled="disabledForm"
                            data-cy="patient-section-month-input"
                            @change="$emit('change-patient-age')"
                            @enter="enterEvent"
                            @blur="handlePatientInfoBlur"
                        >
                        </abc-input>
                        <span class="input-append-unit">月</span>
                    </abc-form-item>
                    <abc-form-item
                        v-if="showAgeDay && !isShebaoCard"
                        class="patient-age-day"
                        :required="needValidateAge"
                        :validate-event="validateDay"
                        trigger="change"
                    >
                        <abc-input
                            v-model.number="patient.age.day"
                            v-abc-focus-selected
                            :width="44"
                            :input-custom-style="{
                                'text-align': 'right', padding: '3px 22px 3px 2px'
                            }"
                            type="number"
                            :config="{
                                supportZero: true, max: 30
                            }"
                            :disabled="disabledForm"
                            data-cy="patient-section-day-input"
                            @change="$emit('change-patient-age')"
                            @enter="enterEvent"
                            @blur="handlePatientInfoBlur"
                        >
                        </abc-input>
                        <span class="input-append-unit">天</span>
                    </abc-form-item>
                    <!--手机号-->
                    <abc-form-item
                        v-abc-focus-selected
                        :required="onlySupportSelect"
                        :validate-event="validateMobile"
                        trigger="change"
                    >
                        <patient-autocomplete
                            ref="autoCompleteMobile"
                            v-model="patient.mobile"
                            class="patient-mobile-autocomplete"
                            :class="{
                                'no-card-btn-wrapper': !hasCardBtnWrapper,
                                'patient-mobile-autocomplete-allow-read-card': hasCrmTag
                            }"
                            input-type="number"
                            :disabled="disabledForm"
                            placeholder="手机号"
                            :is-scan-code="isScanCode"
                            :width="patientMobileAutocompleteWidth"
                            :max-length="11"
                            :readonly="showAddPatientCard"
                            :is-can-see-patient-mobile="isCanSeePatientMobile"
                            data-cy="patient-section-mobile-autocomplete"
                            :custom-search-func="customSearchFunc"
                            :show-id-card="showIdCard"
                            :need-public-health-info="needPublicHealthInfo"
                            :fixed-show-fields="fixedShowFields"
                            @change="fetchPatient"
                            @enter="enterEvent"
                            @selectPatient="handleSelectPatient"
                            @blur="handlePatientInfoBlur"
                        >
                        </patient-autocomplete>
                    </abc-form-item>
                </template>

                <div v-if="hasCardBtnWrapper" class="card-btn-wrapper" @click.stop="">
                    <abc-delete-icon
                        v-if="delButtonVisible"
                        theme="dark"
                        data-cy="patient-section-delete"
                        @delete="clearPatient"
                    ></abc-delete-icon>
                </div>
            </div>

            <template v-if="hasPatient">
                <patient-card-popper
                    v-show="showPatientCard"
                    ref="patient-card-popper"
                    v-model="showPatientCard"
                    :is-registration="isRegistration"
                    :patient-id="patient.id"
                    :is-small="isSmall"
                    :support-one-click-billing="supportOneClickBilling"
                    :default-patient="defaultPatient"
                    :is-can-see-patient-mobile="isCanSeePatientMobile"
                    :require-id-card="requireIdCard"
                    :require-mobile="requireMobile"
                    data-cy="patient-section-popover"
                    @change-patient="updatePatientInfo"
                    @show-card="$emit('show-card')"
                ></patient-card-popper>
            </template>
        </div>

        <!-- 监护人信息 -->
        <div
            v-if="patientGuarDianInfo && showPatientGuardian"
            class="patient-guardian-info"
            :title="patientGuarDianInfo"
        >
            监护人：{{ patientGuarDianInfo }}
        </div>

        <div v-if="!isShebaoCard && (showReadCard || showAddNew)" class="add-patient-wrapper_flex">
            <abc-popover
                v-if="hasCrmTag && showReadCard"
                v-model="showReadCardPopover"
                :visible-arrow="false"
                trigger="hover"
            >
                <abc-button
                    slot="reference"
                    type="ghost"
                    class="crm-group-btn add-patient-wrapper_flex--left"
                    data-cy="patient-section-read-card-btn"
                    icon="read-card"
                >
                </abc-button>

                <read-card-panel v-show="showReadCardPopover" @handleClickShortcut="handleOpenReadPanel"></read-card-panel>
            </abc-popover>

            <div v-if="showAddNew" class="add-patient-wrapper">
                <abc-button
                    v-if="!hasPatientId && !disabled"
                    icon="add_patient"
                    type="ghost"
                    :class="['add-new-patient', 'crm-group-btn', { 'add-patient-wrapper_flex--right': hasCrmTag }]"
                    data-cy="patient-section-new-btn"
                    @click="addNewPatient"
                >
                </abc-button>
                <div v-if="!hasPatient" v-abc-click-outside="addClickOutside" class="add-patient-card">
                    <patient-base-card
                        v-if="showAddPatientCard"
                        ref="add-patient-base-card"
                        v-model="showAddPatientCard"
                        :is-add-patient="true"
                        :support-one-click-billing="supportOneClickBilling"
                        :is-registration="isRegistration"
                        :default-patient="patient"
                        :is-can-see-patient-mobile="isCanSeePatientMobile"
                        :require-id-card="requireIdCard"
                        :require-mobile="requireMobile"
                        data-cy="patient-section-base-card"
                        @add-patient="handleAddPatient"
                        @add-and-use-patient="addAndUsePatient"
                        @close="showAddPatientCard = false"
                    ></patient-base-card>
                </div>
            </div>
        </div>

        <public-health-match-patient
            v-if="publicHealthMatchPatientVisible"
            v-model="publicHealthMatchPatientVisible"
            :list="publicHealthMatchPatientList"
            @confirm="handleMatchConfirm"
        >
        </public-health-match-patient>
    </div>
</template>
<script>
    import {
        mapActions, mapGetters, mapState,
    } from 'vuex';
    import CrmAPI from 'api/crm';
    import PatientAutocomplete from '../patient-autocomplete/patient-autocomplete';
    import PatientBaseCard from '../base-card-dentistry.vue';
    import {
        ANONYMOUS_ID, WxBindStatusEnum,
    } from '@abc/constants';
    import { formatAge } from 'utils/index';
    import {
        validateAge, validateMobile,
    } from 'utils/validate';
    import PatientsAPI from 'api/patients.js';
    import TagItem from 'views/crm/patient-files/card-patient-overview/tag-item';
    import MixinModulePermission from 'views/permission/module-permission';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import { defaultCountryCode } from '@/utils/country-codes.js';
    import IdCardReaderService from 'views/layout/read-card/id-card-reader/id-card-reader-service';
    import { encryptMobile } from 'utils/crm';
    import {
        DEFAULT_CERT_TYPE, MaritalStatusLabel,
    } from 'views/crm/constants.js';
    import ReadCard from 'views/common/read-card';
    import PublicHealthMatchPatient from 'views/layout/patient/patient-section/public-health-match-patient.vue';
    import ReadCardPanel from 'views/layout/read-card/read-card-panel.vue';

    const PatientCardPopper = () => import('views/layout/patient/patient-card-popper.vue');

    export default {
        name: 'PatientCard',
        components: {
            ReadCardPanel,
            PatientAutocomplete,
            PatientBaseCard,
            PatientCardPopper,
            TagItem,
            PublicHealthMatchPatient,
        },
        mixins: [
            MixinModulePermission, ReadCard,
        ],
        props: {
            loading: {
                type: Boolean,
                default: false,
            },
            isSmall: {
                type: Boolean,
                default: false,
            },
            allowShowTags: {
                type: Boolean,
                default: true,
            },
            value: {
                type: Object,
                required: true,
            },
            supportOneClickBilling: {
                type: Boolean,
                default: false,
            },

            defaultPatient: {
                type: Object,
            },

            isScanCode: {
                type: Boolean,
                default: false,
            },

            disabled: {
                type: Boolean,
                default: false,
            },

            openDelay: {
                type: Number,
                default: 150,
            },

            isRequired: {
                type: Boolean,
                default: false,
            },

            // 是否需要hover展示下拉浮层
            needHoverPopover: {
                type: Boolean,
                default: true,
            },

            hiddenPatientMobile: {
                type: Boolean,
                default: false,
            },
            // 是否展示展示情况下的月份
            showDisplayAgeMonth: {
                type: Boolean,
                default: true,
            },
            showAgeDay: {
                type: Boolean,
                default: false,
            },

            // 只读情况下，是否展示婚姻状态
            showMarital: {
                type: Boolean,
                default: false,
            },
            // 只读情况下，是否展示身份证
            showIdCard: {
                type: Boolean,
                default: false,
            },
            source: {
                type: String,
                default: 'outpatient',
            },

            // 聚焦是否推荐患者列表
            focusShowPatient: {
                type: Boolean,
                default: false,
            },
            defaultTags: {
                type: Array,
                default: () => [],
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
            // 监护人信息
            patientGuardian: {
                type: Object,
                default: () => ({ }),
            },
            showPatientGuardian: {
                type: Boolean,
                default: false,
            },
            requireIdCard: {
                type: Boolean,
                default: false,
            },
            requireMobile: {
                type: Boolean,
                default: false,
            },
            // 仅支持选择患者，不支持输入创建
            onlySelectPatient: {
                type: Boolean,
                default: false,
            },
            customSearchFunc: {
                type: Function,
            },
            needPublicHealthInfo: {
                type: Boolean,
                default: false,
            },
            // 显示读卡
            showReadCard: {
                type: Boolean,
                default: true,
            },
            // 显示新增
            showAddNew: {
                type: Boolean,
                default: true,
            },
            isMemberCreate: {
                type: Boolean,
                default: false,
            },

            readCardConfirmText: {
                type: String,
            },
            showPatientTags: {
                type: Boolean,
                default: true,
            },
            // 新增small - 32 / medium - 40；不传，保持原有逻辑：36
            size: {
                type: String,
            },

            // 自定义验证患者姓名方法
            customValidatePatientName: {
                type: Function,
            },

            fixedShowFields: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                showAddPatientCard: false,

                // 是否可以展示下拉的患者卡片浮层
                canShowPatientCard: true,
                // 展示患者卡片popover
                showPopover: false,

                publicHealthMatchPatientVisible: false,
                publicHealthMatchPatientList: [],

                showReadCardPopover: false,
            };
        },
        computed: {
            ...mapState('crm', [
                'originLabels', // 全部标签
            ]),
            ...mapGetters([
                'isEnableIdCardReader',
                'userConfig',
                'isOpenMp',
            ]),
            ...mapGetters('crm', [
                'crmConfigList',
            ]),

            patientGuarDianInfo() {
                const {
                    name = '',
                    relationName = '',
                    mobiles = [],

                } = this.patientGuardian || {};
                return name || relationName ? `${name} ${mobiles[0] || ''}` + `（${relationName}）` : '';
            },
            patientIsOnlyShow() {
                return this.defaultPatient || this.patient;
            },
            needPatientBaseComponentShowTags() {
                const { needPatientBaseComponentShowTags } = getViewDistributeConfig().CRM;
                return needPatientBaseComponentShowTags && this.allowShowTags;
            },

            hasPublicHealthPatient() {
                return !!this.patient?.publicHealthInfo?.id && !this.patient.id;
            },

            hasPatientId() {
                return !!this.patient.id || this.hasPublicHealthPatient;
            },

            disabledForm() {
                // 有公卫信息也不能改
                return this.disabled || !!this.patient?.id || this.hasPublicHealthPatient;
            },
            isRegistration() {
                return this.source === 'registration';
            },
            isOpenCard() {
                return this.source === 'openCard';
            },
            isShebaoCard() {
                return this.source === 'shebaoCard';
            },

            onlySupportSelect() {
                return this.onlySelectPatient || this.isOpenCard || this.isShebaoCard;
            },

            patientTags() {
                return this.patient?.tags || [];
            },
            // 患者标签展示，首先处理标签数据方便查找，再兼容id、tagId这种后端问题，得到有效的患者标签
            showTags() {
                let tags = [];
                this?.originLabels?.forEach((item) => {
                    if (item.tags) {
                        tags = [...tags, ...item.tags];
                    }
                });
                let defaultTags = [];
                if (this.patientIsOnlyShow?.tags?.length) {
                    defaultTags = this.patientIsOnlyShow.tags;
                } else if (this.defaultTags?.length) {
                    defaultTags = this.defaultTags;
                }
                if (defaultTags?.length) {
                    return defaultTags
                        .map((item) => {
                            const target = tags.find((one) => one.id === item.tagId);
                            if (target) {
                                return {
                                    ...item,
                                    tagName: target?.name,
                                };
                            }
                            return false;

                        })
                        .filter((item) => item !== false);
                }
                return [];
            },
            patient: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            displayPatientName() {
                const defaultName = this.loading ? '' : '匿名患者';
                const {
                    name,
                } = this.patient;
                return name || defaultName;
            },
            hasCardBtnWrapper() {
                return !this.disabled && (this.patient.id || this.hasPublicHealthPatient);
            },
            computedMobileClass() {
                return [
                    'display-item',
                    'patient-display-mobile',{
                        'no-mobile': !this.patient.mobile,
                        'no-card-btn-wrapper': !this.hasCardBtnWrapper,
                        'has-patient-tag': !this.isRegistration && this.needPatientBaseComponentShowTags && this.showPatientTags,
                        'patient-display-mobile-no-delete': !this.hasCardBtnWrapper && this.isOpenCard,
                        'patient-display-mobile-member': this.isMemberCreate && !this.delButtonVisible,
                        'patient-display-mobile-member-has-delete': !this.isShebaoCard && this.isMemberCreate && this.delButtonVisible,
                        'patient-display-mobile-member-has-delete-shebao': this.isShebaoCard && this.isMemberCreate && this.delButtonVisible,
                        'is-error': this.requireMobile && !this.displayMobile,
                    },
                ];
            },
            delButtonVisible() {
                if (this.disabled) return false;

                return this.patient.id || this.hasPublicHealthPatient;
            },

            hasCrmTag() {
                return this.isEnableReadCard;
            },
            patientMobileAutocompleteWidth() {
                if (this.isShebaoCard) {
                    return 205;
                }
                if (this.hasCardBtnWrapper) {
                    return !this.isRegistration ? this.hasCrmTag ? 72 : 102 : 135;
                }
                return !this.isRegistration ? this.hasCrmTag ? 92 : 128 : 135;
            },
            displayWechat() {
                return this.isOpenMp && this.patientIsOnlyShow?.wxBindStatus && this.patientIsOnlyShow?.wxBindStatus === WxBindStatusEnum.SUBSCRIBE_AND_BIND;
            },
            displayQiWei() {
                return this.patientIsOnlyShow?.appFlag === 1;
            },
            displayCharge() {
                return this.patientIsOnlyShow?.arrearsFlag === 1;
            },
            displayMember() {
                return this.patientIsOnlyShow?.isMember === 1;
            },
            displayPatientAge() {
                const { age } = this.patient;
                if (!age) return '';
                const {
                    year, month, day,
                } = age;
                let str = '';
                const showDay = year < 1 && day;
                if (!showDay) {
                    str += `<div class="first-display-age">${year || 0} <span>岁</span></div>`;
                }

                if (this.showDisplayAgeMonth) {
                    if (str) {
                        str += `<div>${month || 0} 月</div>`;
                    } else {
                        str += `<div class="first-display-age">${month || 0} <span>月</span></div>`;
                    }
                }

                // 小于1岁展示月天
                if (this.showAgeDay || showDay) {
                    str += `<div>${day || 0} 天</div>`;
                }
                return str;
            },

            patientIconSize() {
                return this.size === 'medium' ? 14 : 12;
            },

            iconColor() {
                const {
                    id,
                    sex,
                } = this.patient;
                if (!id) return '#dadbe0';
                return sex === '女' ? '#ff6082' : '#58a0ff';
            },
            isEnableReadCard() {
                if (this.disabledForm) return false;
                return IdCardReaderService.isEnable();
            },
            hasPatient() {
                const {
                    id,
                } = this.patient;
                return (id && id !== ANONYMOUS_ID) || this.hasPublicHealthPatient;
            },

            needValidateName() {
                if (this.customValidatePatientName) return false;
                const {
                    id,
                } = this.patient;
                // 选择的患者不做校验
                if (id) return false;
                return this.isRequired;
            },

            /**
             * @desc 显示天数时，需要校验天数是否填写
             * <AUTHOR>
             * @date 2020-10-29 11:43:33
             */
            needValidateAge() {
                const {
                    id,
                    age,
                } = this.patient;
                // 选择的患者不做校验
                if (id) return false;
                const {
                    year,
                    month,
                    day,
                } = age || {};
                if (this.showAgeDay) {
                    return this.isRequired && !(month || year || day);
                }
                return this.isRequired && !(month || year);
            },

            /**
             * @desc 是否展示patient card
             * <AUTHOR>
             * @date 2022-05-31 08:56:00
             * 之前会判断 needHoverPopover 为true才会展示弹窗
             */
            showPatientCard() {
                return this.canShowPatientCard &&
                    this.showPopover &&
                    this.needHoverPopover &&
                    !this.hasPublicHealthPatient;
            },

            displayMobile() {
                let {
                    mobile,
                } = this.patient;
                if ((this.hiddenPatientMobile || !this.isCanSeePatientMobile) && mobile) {
                    mobile = encryptMobile(mobile);
                }
                return mobile || '';
            },
            displayMarital() {
                const {
                    marital,
                } = this.patient;
                return MaritalStatusLabel[marital];
            },
            displayIdCard() {
                const {
                    idCard,
                } = this.patient;
                return idCard || '';
            },
        },
        watch: {
            showPatientCard(val) {
                this.$emit('show-card', val);
                // 当patient card展示的时候
                if (val) {
                    this.triggerMouseEvent();
                }
            },
        },
        created() {
            this.fetchCrmConfigList();
        },
        methods: {
            ...mapActions('crm', ['fetchCrmConfigList']),
            getRequired() {
                return this.isRequired;
            },
            validatePatientInfo() {
                let flag = false;
                if (this.showAddNew && !this.hasPatientId) {
                    const validateDataList = ['certificates', 'sourceInfo', 'address'];
                    const {
                        address = {},
                        addressDetail = '',
                    } = this.patient;
                    for (const key in this.crmConfigList) {
                        if (this.crmConfigList?.[key].required &&
                            !validateDataList.includes(key) &&
                            !this.patient[`${key}`]) {
                            flag = true;
                        } else if (this.crmConfigList?.[key].required && validateDataList.includes(key)) {
                            if (key === 'certificates' && !this.patient.idCard) {
                                flag = true;
                            }
                            if (key === 'address' && !addressDetail) {
                                flag = true;
                            }
                            if (key === 'address' && !address.addressCityId) {
                                flag = true;
                            }
                            if (key === 'sourceInfo' && !this.patient.sourceId) {
                                flag = true;
                            }
                        }
                    }
                }
                return flag;
            },
            openPatientCard() {
                this.showAddPatientCard = true;
                this.$nextTick(() => {
                    this.$refs?.['add-patient-base-card']?.onlyValidate();
                });
            },
            handlePatientExist(code = 13503, detail = []) {
                this.showAddPatientCard = true;
                this.$nextTick(() => {
                    this.$refs?.['add-patient-base-card']?.onlyValidate();
                    this.$refs?.['add-patient-base-card']?.handlePatientExist(code, detail);
                });
            },
            selectText(className) {
                const selection = window.getSelection();
                selection.removeAllRanges();
                const range = document.createRange();
                const node = this.$el.querySelector(`.${className} span`);
                range.selectNodeContents(node);
                selection.addRange(range);
            },
            validatePatientName() {
                return this.onlySupportSelect ? this.validatePatient : this.customValidatePatientName;
            },
            validatePatient(value, callback) {
                if (this.hasPatientId) {
                    callback({
                        validate: true,
                    });
                } else {
                    callback({
                        validate: false,
                        message: '请选择已有患者或新建患者',
                    });
                }
            },
            validateMobile,
            validateAge,
            /**
             * @desc 验证年龄月份
             * <AUTHOR>
             * @date 2018/10/18 12:29:01
             */
            validateMonth(value, callback) {
                const {
                    age,
                } = this.patient;
                const {
                    month,
                } = age || {};
                if (!month) {
                    callback({ validate: true });
                    return;
                }
                if (!/^(0?[1-9]|1[0-1])$/.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            /**
             * @desc 验证年龄天数
             * <AUTHOR>
             * @date 2020-10-29 11:20:49
             */
            validateDay(value, callback) {
                const {
                    age,
                } = this.patient;
                const {
                    day,
                } = age || {};
                if (!day) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-9]|2[0-9]|30)$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多30天',
                    });
                } else {
                    callback({ validate: true });
                }
            },

            formatAge,

            updatePatientInfo(val) {
                this.patient = Object.assign({}, this.patient, val);
                this.$emit('update-patient', val);
            },

            /**
             * @desc 姓名和手机号 失焦后请求patientId
             * <AUTHOR>
             * @date 2018/10/22 22:20:56
             */
            async fetchPatient() {
                if (this.needPublicHealthInfo) {
                    return;
                }

                const {
                    id, name, mobile,
                } = this.patient;

                if (id || !name || !mobile) return false;

                const { data } = await PatientsAPI.fetchPatientByNameMobile(name, mobile);
                await this.selectPatient(data);
            },

            handleSelectPatient(patient, isEnterSelect) {
                if (isEnterSelect) {
                    this.canShowPatientCard = false;
                }
                this.selectPatient(patient);
            },
            handlePatientInfoBlur() {
                this.$emit('patient-info-blur');
            },
            handlePatientSexBlur() {
                this.$emit('patient-sex-blur');
            },

            /**
             * @desc autoComplete 面板是是监听的 mousedown 事件，此时患者被选中
             * @desc 搜狗输入法输入字下面有个小横线，失焦之后确认了用户输入再次input ，因此输入框的value 被当前输入覆盖
             * @desc 流程是: 1. 输入患者"杨明" 2. 点击患者"杨明昆" 3. patient 赋值为"杨明昆" 4. 输入法失焦确认"杨明" 5. 重新input "杨明"
             * @解决 mousedown之后患者选择有id，此时短路input
             * @风险 input 触发时机不好说，也有可能在patient赋值之前，那么改动就不生效， 但不影响原来的业务逻辑
             */
            handleInputPatientName(val) {
                if (this.patient?.id) return;
                this.patient.name = val;
            },

            /**
             * @desc 选择患者
             * <AUTHOR>
             * @date 2022-01-17 15:15:04
             * @params patient
             * @params markShebaoCardInfo 是否标记社保刷卡挂号
             * @return
             */
            async selectPatient(patient, markShebaoCardInfo = false, healthCard, originCardInfo = null) {
                console.log('patient=', patient);
                if (!patient) return false;

                this.$emit('startChangePatient', {
                    originId: this.patient.id,
                    id: patient.id,
                });

                const {
                    id,
                    name,
                    age,
                    sex,
                    birthday,
                    created,
                    countryCode,
                    mobile,
                    isMember,
                    idCard,
                    idCardType = DEFAULT_CERT_TYPE,
                    company,
                    profession,
                    marital,
                    weight,
                    sn,
                    tags = [],
                    remark,
                    wxBindStatus = 0,
                    wxNickName = '',
                    isMemberFamily,
                    shebaoCardInfo,
                    patientSource,
                    memberInfo,
                    address,
                    childCareArchives,
                    chronicArchivesInfo,
                    promotionCardPatientListView,
                    pastHistory,
                    appFlag = 0,
                    arrearsFlag = 0,
                    publicHealthInfo,
                    parentName = '',
                } = patient;

                const {
                    addressCityId = null,
                    addressCityName = null,
                    addressDistrictId = null,
                    addressDistrictName = null,
                    addressProvinceId = null,
                    addressProvinceName = null,
                    addressDetail = null,
                } = address || {};

                // 去除age中的0
                if (age && +age.year === 0) {
                    age.year = null;
                }
                if (age && +age.month === 0) {
                    age.month = null;
                }
                Object.assign(this.patient, {
                    id,
                    name,
                    age: age || {},
                    sex,
                    birthday,
                    created,
                    countryCode,
                    mobile,
                    isMember,
                    idCard,
                    idCardType,
                    company,
                    profession,
                    marital,
                    weight,
                    sn,
                    tags,
                    remark,
                    isMemberFamily,
                    wxBindStatus,
                    wxNickName,
                    shebaoCardInfo: markShebaoCardInfo ? shebaoCardInfo : null,
                    healthCard,
                    patientSource,
                    memberInfo,
                    childCareArchives,
                    chronicArchivesInfo,
                    promotionCardPatientListView,

                    address,
                    addressCityId,
                    addressCityName,
                    addressDistrictId,
                    addressDistrictName,
                    addressProvinceId,
                    addressProvinceName,
                    addressDetail,
                    pastHistory,
                    appFlag,
                    arrearsFlag,
                    parentName,
                });

                if (this.needPublicHealthInfo) {
                    this.$set(this.patient, 'publicHealthInfo', publicHealthInfo);
                }

                // 这里需要提前出emit出去
                this.$emit('change-patient', this.patient, markShebaoCardInfo, healthCard, originCardInfo);
            },

            async clearPatient() {
                this.$emit('clear-patient');
                await this.selectPatient({
                    id: null,
                    name: '',
                    sex: '男',
                    birthday: '',
                    age: {
                        year: null,
                        month: null,
                    },
                    sn: '',
                    mobile: '',
                    countryCode: defaultCountryCode,
                    isMember: 0,
                    appFlag: 0,
                    arrearsFlag: 0,
                    wxBindStatus: 0,
                });
            },

            handleMatchConfirm(payload) {
                this.selectPatient(payload);
            },

            async setPatientId(patientId, markShebaoCardInfo, healthCard, originCardInfo) {
                const { data } = await CrmAPI.fetchPatientOverview(patientId);
                await this.selectPatient(data, markShebaoCardInfo, healthCard, originCardInfo);
            },

            getPatientCardVm() {
                const patientCardPopper = this.$refs['patient-card-popper'];
                if (!patientCardPopper) return {};
                return patientCardPopper.$refs['patient-base-card'] || {};
            },

            async handleOpenReadPanel(shortcut) {
                try {
                    if (this._isReadCard) {
                        console.warn('正在读卡中，请稍后再试');
                        return;
                    }
                    this._isReadCard = true;
                    this.showReadCardPopover = false;
                    if (this.needPublicHealthInfo) {
                        await this.getIdCardInfo();

                        if (!this.cardInfo?.idCard) {
                            return;
                        }

                        const { data } = await CrmAPI.fetchBindedPatients(this.cardInfo, {
                            withPublicHealthInfo: 1,
                        });

                        if (!data.rows || data.rows.length === 0) {
                            this.$Toast({
                                message: '未能匹配到患者',
                                type: 'error',
                            });
                            return;
                        }

                        if (data.rows.length === 1) {
                            await this.selectPatient(data.rows[0]);
                            return;
                        }

                        this.publicHealthMatchPatientList = data.rows || [];
                        this.publicHealthMatchPatientVisible = true;
                        this._isReadCard = false;
                        return;
                    }

                    const response = await IdCardReaderService.read({
                        departmentId: this.departmentId,
                        confirmText: this.readCardConfirmText || (this.$route.name.includes('registration') ? '开始挂号' : '开始接诊'),
                        shortcut,
                        onCancel: () => {
                            this._isReadCard = false;
                        },
                    });

                    const {
                        patient,
                        isShebaoCard,
                        shebaoCardInfo,
                    } = response;
                    const { healthCard } = patient || {};
                    // originCardInfo用于医保挂号，患者详情不一定会返回
                    const { originCardInfo } = shebaoCardInfo || {};

                    if (patient && patient.id) {
                        this.setPatientId(patient.id, isShebaoCard, healthCard, originCardInfo);
                    }
                    this._isReadCard = false;
                } catch (e) {
                    console.error(e);
                }
            },
            handleCopy(event) {
                if (!this.disabledForm) return;
                if (window.getSelection().getRangeAt(0).toString().length) {
                    event.preventDefault();
                    const valueStr = (window.getSelection().toString()).replace(/[\u00A0\n]/gi, ' ').replace(/^\s+/,'');
                    if (event.clipboardData) {
                        event.clipboardData.setData('text/plain', valueStr);
                    } else {
                        if (window.clipboardData) {
                            return window.clipboardData.setData('text', valueStr);
                        }
                        this.$emit('copy',valueStr);
                    }
                }
            },
            handleMouseEnter() {
                if (!this.hasPatient || this.isRegistration) return;
                this._timer = setTimeout(() => {
                    this.showPopover = true;
                }, this.openDelay);
            },
            handleMouseLeave() {
                if (this.isRegistration) return;
                const baseCardVM = this.getPatientCardVm();
                const {
                    isVisible,
                    editor,
                    showTagPopover,
                } = baseCardVM;
                if (isVisible || showTagPopover || editor) {
                    // 当打开弹窗时，不关闭
                    return;
                }

                this.canShowPatientCard = true;
                this.showPopover = false;
                clearTimeout(this._timer);
            },
            handleClosePopover() {
                this.showPopover = false;
                this.$refs?.['patient-card-popper']?.handleClose();
            },
            /**
             * 患者信息弹窗展示时，模拟一个鼠标点击过程，触发 click-outside 的执行，关掉其他展示的弹窗
             */
            triggerMouseEvent() {
                const mouseDownEvent = new MouseEvent('mousedown', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                });
                const mouseUpEvent = new MouseEvent('mouseup', {
                    bubbles: true,
                    cancelable: true,
                    view: window,
                });
                this.$refs.patientSection.dispatchEvent(mouseDownEvent);
                this.$refs.patientSection.dispatchEvent(mouseUpEvent);
            },
            handleClick() {
                if (!this.hasPatient || this.isRegistration) return;
                const baseCardVM = this.getPatientCardVm();
                const {
                    isVisible,
                    editor,
                    showTagPopover,
                } = baseCardVM;
                if (isVisible || showTagPopover || editor) {
                    // 当打开弹窗时，不关闭
                    return;
                }
                this.showPopover = true;
                this.canShowPatientCard = true;
                this.resetPatientCardVm();
            },

            resetPatientCardVm() {
                const baseCardVM = this.getPatientCardVm();
                baseCardVM.editor = false;
                if (baseCardVM.$el) {
                    baseCardVM.$el.scrollTop = 0;
                }
            },


            addClickOutside(mousedown, mouseup) {
                const baseCardVM = this.$refs['add-patient-base-card'] || {};
                const {
                    notCloseCard,
                    showCardCover,
                } = baseCardVM;

                if (mouseup.target.classList.contains('abc-dialog-cover') && showCardCover) {
                    baseCardVM?.handleClickCover();
                    return;
                }

                if (!this.showAddPatientCard || notCloseCard) {
                    // 当打开弹窗时，不关闭
                    return;
                }
                const {
                    exit,isTarget,
                } = this.handleCommon(mousedown, mouseup) || {};
                if (!exit || !isTarget) {
                    this.showAddPatientCard = false;
                }
            },
            onClickOutside(mousedown, mouseup) {
                const baseCardVM = this.getPatientCardVm();
                const {
                    selectOption,
                    notCloseCard,
                    showCardCover,
                } = baseCardVM || {};

                if (mouseup.target.classList.contains('abc-dialog-cover')) {
                    if (showCardCover) {
                        baseCardVM?.handleClickCover();
                        return;
                    }
                }

                if (!this.showPopover || notCloseCard) {
                    // 当打开弹窗时，不关闭
                    return;
                }
                // 当标签弹窗显示，点击卡片外部，应当先关闭标签弹窗
                if (selectOption) {
                    baseCardVM.showTagPopover = false;
                    return;
                }
                const {
                    exit,isTarget,
                } = this.handleCommon(mousedown, mouseup) || {};
                if (!exit || !isTarget) {
                    this.resetPatientCardVm();
                    this.showPopover = false;
                }
            },
            getEventPath(evt) {
                if (!evt) return '';
                return evt.path || (evt.composedPath && evt.composedPath()) || '';
            },
            handleCommon(mousedown, mouseup) {
                const classNames = [
                    'abc-tooltip__popper',
                    'card-btn',
                    'thr',
                    'view-labels',
                    'sex-select',
                    'unit-select',
                    'abc-date-picker-popper',
                    'panel-popper',
                    'date-navigator-dropdown-wrapper',
                    'source-select-ul',
                    'child-box',
                    'profession-options',
                    'has-top',
                    'visit-source-dialog',
                    'address-selector_popover-wrapper',
                    'cascader-options-wrapper',
                    'crm-module__package-social__card-box',
                    'country-code-select__wrapper',
                    'country-code-select__item',
                    'cert-type-select',
                    'warn-confirm-dialog',
                    'primaryTherapistId-select',
                    'dutyTherapistId-select',
                    'cert-type-select__item',
                    'crm-tags-wrapper',
                    'tags-labels',
                    'confirm-save-validate',
                ];
                const cardBtnDom = this.$refs['card-btn-ref'];
                let exit = false;
                let isTarget = true;
                const mouseupPath = this.getEventPath(mouseup);
                console.log('mouseupPath', mouseupPath);
                if (mouseupPath) {
                    mouseupPath.forEach((item) => {
                        if (item.classList) {
                            const classList = Array.from(item.classList);
                            classNames.forEach((one) => {
                                if (Array.from(classList).includes(one)) {
                                    exit = true;
                                }
                            });
                            if (Array.from(classList).includes('package-card__card-btn') && item !== cardBtnDom) {
                                isTarget = false;
                            }
                        }
                    });
                }
                return {
                    exit,isTarget,
                };
            },
            addNewPatient() {
                this.showAddPatientCard = true;
            },
            handleAddPatient(data) {
                this.$emit('input', data);
                this.$emit('add-patient', data);
                this.showAddPatientCard = false;
            },
            async addAndUsePatient(id) {
                try {
                    const params = {
                        wx: 1,
                        chronicArchives: 1,
                        promotionCardList: 1,
                    };
                    const { data } = await CrmAPI.fetchPatientOverview(id, params);
                    await this.selectPatient(data);
                } catch (error) {
                    console.log('fetchPatientInfo error', error);
                }
            },
            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                this.$nextTick(() => {
                    this.$emit('enterEvent', e);
                });
            },
        },
    };
</script>
