<template>
    <div class="patient-base-card-popper-wrapper">
        <patient-base-card
            :key="patientId"
            ref="patient-base-card"
            :visible.sync="value"
            :patient-id="patientId"
            :support-one-click-billing="supportOneClickBilling"
            :is-registration="isRegistration"
            :default-patient="defaultPatient"
            :is-small="isSmall"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            :require-id-card="requireIdCard"
            :require-mobile="requireMobile"
            @change-patient="(data) => $emit('change-patient', data)"
            @show-card="$emit('show-card')"
        ></patient-base-card>
    </div>
</template>
<script>
    import PatientBaseCard from './base-card-dentistry.vue';

    export default {
        name: 'PatientCardPopper',
        components: {
            PatientBaseCard,
        },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            patientId: {
                type: String,
            },
            defaultPatient: {
                type: Object,
            },
            isRegistration: {
                type: Boolean,
                default: false,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
            supportOneClickBilling: {
                type: Boolean,
                default: false,
            },
            requireIdCard: {
                type: Boolean,
                default: false,
            },
            requireMobile: {
                type: Boolean,
                default: false,
            },
            isSmall: {
                type: Boolean,
                default: false,
            },
        },
        methods: {
            handleClose() {
                this.$refs['patient-base-card'].handleClose();
            },
        },
    };
</script>
<style lang="scss">
    @import 'src/styles/abc-common.scss';
    // 患者卡片宽度是固定的，有用户反馈鼠标箭头滑动到弹窗边缘就会关闭弹窗，感觉体验不好，所以沙哥让预留了弹窗左右各一个箭头的宽度,后续维护要注意下
    .patient-base-card-popper-wrapper {
        position: absolute;
        top: 38px;
        left: -11px;
        z-index: 10;
        width: 486px;
        height: auto;
        max-height: 576px;
        padding-top: 4px;

        .crm-module__package-card__base-card-dentistry {
            position: relative;
            width: 464px;
            height: auto;
            max-height: 576px;
            margin: 0 auto;
            border-radius: var(--abc-border-radius-small);
            box-shadow: var(--abc-shadow-1);

            &-small {
                max-height: 506px !important;
            }
        }
    }
</style>
