<template>
    <div
        ref="base-card"
        v-abc-loading="isLoading"
        :class="['crm-module__package-card__base-card-dentistry',{
            'overflow-y-hidden': showLiItem,
            'base-card-confirm-style': confirmSave,
            'crm-module__package-card__base-card-dentistry-small': isSmall
        }]"
    >
        <div v-if="showCardCover" class="patient-card-cover" @click.stop="handleClickCover"></div>
        <slot></slot>
        <template v-if="!$slots.default">
            <div v-if="showHead && !isAddPatient" class="patient-head-info">
                <div>
                    <span class="name">{{ patientInfo.name || '新患者' }}</span>
                    <span class="sex">{{ patientInfo.sex }}</span>
                    <span class="age">{{
                        formatAge(patientInfo.age, {
                            monthYear: 12, dayYear: 1, textGap: ' '
                        })
                    }}</span>
                    <span class="mobile">{{ patientInfo.mobile }}</span>
                </div>

                <div v-if="showDelete" class="delete-icon-wrapper">
                    <delete-icon @delete="$emit('close-card')"></delete-icon>
                </div>
            </div>
        </template>
        <div class="content" :class="{ 'crm-basic-content': visibleQrCode || visibleUnbindWX }">
            <!--患者标签 start-->
            <biz-tag-selector
                v-if="!isAddPatient"
                class="thr clearfix"
                :patient-id="patientId"
                :crm-a-p-i="CrmAPI"
                :patient-info="patientInfo"
                :origin-labels="originLabels"
                :view-distribute-config="viewDistributeConfig"
                :is-single-store="isSingleStore"
                :current-clinic="currentClinic"
                @change-patient-tags="handlePatientTagsChange"
                @changeShowTagPopover="changeShowTagPopover"
                @updateTagsByFetch="updateTagsByFetch"
            >
                <!--患者标签 slot start-->
                <abc-flex
                    v-if="displayMember || (hasChargeModule && displayCharge) || displayWechat || displayQiWei"
                    :gap="8"
                    style="height: 20px; margin-right: 8px;"
                    align="center"
                >
                    <img
                        v-if="displayMember"
                        class="patient-display-tag"
                        src="~assets/images/crm/v-yellow-square.png"
                        alt="vip"
                    />
                    <img
                        v-if="hasChargeModule && displayCharge"
                        class="patient-display-tag"
                        src="~assets/images/crm/<EMAIL>"
                        alt="arrears"
                    />

                    <img
                        v-if="displayWechat"
                        class="patient-display-tag"
                        src="~assets/images/crm/wechat.png"
                        alt="wechat"
                    />

                    <img
                        v-if="displayQiWei"
                        class="patient-display-tag"
                        src="~assets/images/crm/qiwei.png"
                        alt="qiwei"
                    />
                </abc-flex>
                <!--患者标签 slot end-->
            </biz-tag-selector>
            <!--患者标签 end-->
            <div v-if="!isAddPatient" class="statistics-line">
                <div class="statistics-line-box">
                    <span style="margin-right: 8px;">
                        门诊 {{ patientBaseInfo.outpatientCount || 0 }} 次，消费 {{ $t('currencySymbol') }}{{
                            formatMoney(patientBaseInfo.cumulativeAmount)
                        }}
                        <span v-if="isNeedDisCharge" style="color: #ff3333;">{{ `（欠费 ${$t('currencySymbol')}${ formatMoney(patientBaseInfo.owePayedAmount)}）` }}</span>
                    </span>
                    <span v-if="patientInfo && patientInfo.mobile && needPatientBaseComponentShowTags">手机{{ patientInfoMobile }}</span>
                </div>
                <abc-divider
                    size="normal"
                    theme="light"
                    margin="small"
                    variant="dashed"
                ></abc-divider>
            </div>
            <div :class="['patient-data',{ 'add-patient': isAddPatient }]">
                <div class="title">
                    {{ titleInfo }}
                </div>
                <div class="modify-btn">
                    <template v-if="!editor && !isAddPatient">
                        <abc-button variant="ghost" size="small" @click="onClickEdit">
                            编辑
                        </abc-button>
                    </template>
                    <template v-else>
                        <abc-button
                            size="small"
                            :disabled="noChangeData && !isAddPatient"
                            :loading="saveLoading || changeCascaderLoading"
                            @click="onClickSave"
                        >
                            保存
                        </abc-button>
                        <abc-button variant="ghost" size="small" @click="onClickCancel">
                            取消
                        </abc-button>
                    </template>
                </div>
            </div>
            <div class="crm-patient-basic-info" :class="{ 'crm-patient-basic-info-add': isAddPatient }">
                <crm-patient-basic-info
                    ref="crmPatientBasicInfo"
                    :post-data-info.sync="postData"
                    :editor.sync="editor"
                    to-element=".crm-module__package-card__base-card-dentistry"
                    teleport-value=".dialog-box"
                    :no-change-data-info.sync="noChangeData"
                    :default-patient="defaultPatient"
                    :patient-basic-info.sync="patientInfo"
                    :show-li-item.sync="showLiItem"
                    :add-old-post-data.sync="addOldPostData"
                    :is-show-visit-source-dialog.sync="isShowVisitSourceDialog"
                    :is-can-see-patient-mobile="isCanSeePatientMobile"
                    :is-add-patient="isAddPatient"
                    :cover-top.sync="coverTop"
                    :cover-height.sync="coverHeight"
                    :visible-unbind-w-x-info.sync="visibleUnbindWX"
                    :visible-qr-code-info.sync="visibleQrCode"
                    :require-id-card="requireIdCard"
                    :require-mobile="requireMobile"
                    @handleCommon="handleCommon"
                    @confirm="confirm"
                ></crm-patient-basic-info>
            </div>
            <template v-if="!isAddPatient && isOpenSocial">
                <abc-divider
                    size="normal"
                    theme="light"
                    variant="dashed"
                    margin="small"
                ></abc-divider>
                <div class="crm-patient-shebao-info">
                    <crm-shebao-info :shebao-info="shebaoCardInfo" :visible-social-card-info.sync="visibleSocialCardInfo"></crm-shebao-info>
                </div>
            </template>
            <abc-divider
                v-if="showLiBox && !isAddPatient"
                size="normal"
                theme="light"
                variant="dashed"
                margin="small"
            ></abc-divider>
            <crm-patient-item-container
                v-if="showLiBox && !isAddPatient"
                :patient-id="patientId"
                :patient-basic-info.sync="patientInfo"
                :show-li-item.sync="showLiItem"
                :support-one-click-billing="supportOneClickBilling"
                :max-height="maxHeight"
                teleport-value=".dialog-box"
                @visible-family-doctor="visibleFamilyDoctor = $event"
                @visible-bind-files="visibleBindFiles = $event"
                @visible-card-info="visibleCardInfo = $event"
                @visible-child-health="visibleChildHealth = $event"
                @handle-common="handleCommon"
                @change-card-holder-handles="changeCardHolderHandles"
                @change-member-handles="changeMemberHandles"
                @change-is-member-family-open="changeIsMemberFamilyOpen"
                @change-coupon-handles="changeCouponHandles"
                @handle-refresh="handleRefresh"
            ></crm-patient-item-container>
        </div>
        <div
            v-if="isShowLiItem"
            class="base-card-cover"
            :style="{
                height: `${coverHeight + 2}px` ,top: !!coverTop && `${coverTop - 4}px`
            }"
        ></div>

        <!--   用于子组件传送内部组件展示在父组件中（水平垂直居中）    -->
        <div :class="['dialog-box',{ 'dialog-wx-box': visibleQrCode }]" :style="{ 'margin-top': `${coverTop}px` }">
        </div>
    </div>
</template>

<script>
    import BizTagSelector from 'src/components-composite/biz-tag-selector';
    import CrmAPI from 'api/crm';
    import fecha from 'utils/fecha';
    import {
        formatAge, formatMoney,
    } from 'utils/index';
    import {
        validateMobile, validateIdCard,
    } from 'utils/validate';
    import {

        professionOptions, maritalOptions,
    } from '@/views/crm/data/options';
    import {
        mapActions,
        mapGetters, mapState,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone';
    import { parseTime } from 'utils/index';
    import MixinModulePermission from 'views/permission/module-permission';
    import {
        DEFAULT_CERT_TYPE, MaritalStatusEnum,
    } from 'views/crm/constants';
    import DeleteIcon from 'src/views/layout/delete-icon/delete-icon';
    import { ANONYMOUS_ID } from '@abc/constants';
    import inputSelect from 'views/common/input-select';
    import AbcAccess from '@/access/utils.js';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import { WxBindStatusEnum } from '@abc/constants';
    import CrmPatientBasicInfo from './crm-patient-basic-info';
    import CrmShebaoInfo from './crm-shebao-info';
    import CrmPatientItemContainer from './crm-patient-item-container.vue';
    import { encryptMobile } from 'utils/crm';

    export default {
        components: {
            DeleteIcon,
            CrmPatientBasicInfo,
            CrmPatientItemContainer,
            CrmShebaoInfo,
            BizTagSelector,
        },
        mixins: [
            MixinModulePermission,inputSelect,
        ],
        provide() {
            return {
                main: this,
            };
        },
        props: {
            // 患者卡片可见性
            visible: {
                type: Boolean,
                default: true,
            },
            isSmall: {
                type: Boolean,
                default: false,
            },
            // 拉取信息中
            loadingInfo: Boolean,
            // 患者信息
            defaultPatient: {
                type: Object,
            },
            patientId: {
                type: String,
                default: '',
            },
            showHead: {
                type: Boolean,
                default: false,
            },
            isAddPatient: {
                type: Boolean,
                default: false,
            },
            // 是否支持一键开出卡项剩余项目功能
            supportOneClickBilling: {
                type: Boolean,
                default: false,
            },
            showLiBox: {
                type: Boolean,
                default: true,
            },
            showDelete: {
                type: Boolean,
                default: false,
            },
            addPatientName: {
                type: String,
                default: '',
            },
            isRegistration: {
                type: Boolean,
                default: false,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
            requireIdCard: {
                type: Boolean,
                default: false,
            },
            requireMobile: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                fecha,
                formatAge,
                formatMoney,
                professionOptions,
                maritalOptions,
                showTagPopover: false,
                editor: false, // 是否编辑状态
                postData: {},
                addOldPostData: null,
                patientInfo: null,
                tagList: [],
                saveLoading: false,
                baseInfoLoading: false,
                visibleSocialInfo: false,
                patientBaseInfo: {
                    outpatientCount: '',
                    payCount: '',
                    cumulativeAmount: '',
                    owePayedAmount: '',
                },
                currentFormItem: '',
                cacheFormConfig: [],
                visibleBindFiles: false, // 可选健康档案列表
                visibleChildHealth: false, // 儿保健康档案详情
                visibleFamilyDoctor: false, // 家庭医生详情
                visibleCardInfo: false, // 卡详情
                isShowVisitSourceDialog: false,
                selectOption: false,
                showLiItem: false,
                visibleQrCode: false,
                visibleUnbindWX: false,
                visibleSocialCardInfo: false,
                maxHeight: 0,
                coverHeight: 0,
                coverTop: 0,
                unBindLoading: false,
                unBindWX: false,
                params: {
                    wx: 1,
                    childCareRecords: 1,
                    promotionCardList: 1,
                    showFamilyDoctor: 1,
                },
                confirmSave: false,
                isCorrectIdCard: false, // 身份证是否校验成功
                submitHasError: false, // 提交出现错误
                cardHolder: false,
                memberHolder: false,
                couponHandles: false,
                isMemberFamily: false,
                noChangeData: false,
                changeCascaderLoading: false,
                CrmAPI,
            };
        },
        computed: {
            ...mapState('crm', [
                'originLabels', // 全部标签
            ]),
            ...mapGetters('crm', ['crmAccessVisibility']),
            ...mapState('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters('clinic', ['isSingleStore']),
            ...mapGetters(['isOpenMp', 'isEnableChildHealth', 'currentClinic', 'isNeedAuthenticationBasicInfo', 'isCanSeePatientMobileInCrm']),
            isOpenSocial() {
                return this.$abcSocialSecurity.isOpenSocial;
            },
            patientInfoMobile() {
                const { mobile = '' } = this.patientInfo;
                return this.isCanSeePatientMobile ? mobile : encryptMobile(mobile);
            },
            shebaoCardInfo() {
                return this.patientInfo?.shebaoCardInfo || {};
            },
            isAllowUseChildHealth() {
                return this.isEnableChildHealth === 1 && this.patientInfo?.age?.year < 18;
            },
            displayWechat() {
                return this.isOpenMp && this.patientInfo?.wxBindStatus && this.patientInfo?.wxBindStatus === WxBindStatusEnum.SUBSCRIBE_AND_BIND;
            },
            displayQiWei() {
                return this.patientInfo?.appFlag === 1;
            },
            displayMember() {
                return this.patientInfo?.isMember === 1;
            },
            displayCharge() {
                return this.patientInfo?.arrearsFlag === 1;
            },
            needPatientBaseComponentShowTags() {
                const { needPatientBaseComponentShowTags } = getViewDistributeConfig().CRM;
                return needPatientBaseComponentShowTags;
            },
            isNeedDisCharge() {
                return this.patientBaseInfo?.owePayedAmount && this.hasChargeModule;
            },
            notCloseCard() {
                return this.isVisible || this.submitHasError || this.confirmSave || this.showTagPopover;
            },
            isVisible() {
                return this.isMemberFamily || this.cardHolder || this.memberHolder || this.couponHandles || this.visibleFamilyDoctor || this.visibleCardInfo || this.visibleChildHealth || this.visibleBindFiles || this.isShowVisitSourceDialog || this.visibleQrCode || this.visibleUnbindWX || this.visibleSocialCardInfo;
            },
            showCardCover() {
                if (this.isAddPatient) {
                    return !isEqual({
                        ...this.postData,
                        address: {
                            ...this.postData?.address,
                            addressDetail: this.postData?.address?.addressDetail || null,
                        },
                    },{
                        ...this.addOldPostData,
                        address: {
                            ...this.addOldPostData?.address,
                            addressDetail: this.addOldPostData?.address?.addressDetail || null,
                        },
                    });
                }
                if (this.isLoading) return false;
                return !this.noChangeData;
            },
            titleInfo() {
                if (this.editor) {
                    return '编辑患者';
                }
                if (this.isAddPatient) {
                    return '新增患者';
                }
                return '患者资料';
            },
            isBindWX() {
                return this.postData?.wxBindStatus >= 2;
            },
            canEditStatus() {
                return this.editor || this.isAddPatient;
            },
            servicePackage() {
                return this.patientInfo?.familyDoctorAbstract?.servicePackName;
            },
            // 患者标签展示，首先处理标签数据方便查找，再兼容id、tagId这种后端问题，得到有效的患者标签
            showTags() {
                let tags = [];
                this.originLabels?.forEach((item) => {
                    if (item.tags) {
                        tags = [...tags, ...item.tags];
                    }
                });
                if (this.patientInfo?.tags) {
                    return this.patientInfo.tags
                        .map((item) => {
                            const target = tags.find((one) => one.id === item.tagId);
                            if (target) {
                                return {
                                    ...item,
                                    tagName: target.name,
                                };
                            }
                            return false;

                        })
                        .filter((item) => item !== false);
                }
                if (this.isAddPatient) {
                    return this.tagList
                        .map((item) => {
                            const target = tags.find((one) => one.id === item.tagId);
                            if (target) {
                                return {
                                    ...item,
                                    tagName: target.name,
                                };
                            }
                            return false;

                        })
                        .filter((item) => item !== false);
                }
                return [];

            },
            // 已经选中的标签ids
            selectedIds() {
                return this.showTags.map((item) => item.tagId);
            },
            // 标签是否有更改
            changeTags() {
                if (this.isTransmit) {
                    const oldTags = this.patient?.tags || [];
                    const newTags = this.patientInfo?.tags || [];
                    if (oldTags.length !== newTags.length) {
                        return true;
                    }
                    if (newTags.length === 0) {
                        return false;
                    }
                    return oldTags.some((item) => !newTags.find((one) => one.tagId === item.tagId));
                }
                return false;
            },
            // 已经绑定的健康档案
            bindHealthFiles() {
                const healthFiles = [];
                const {
                    childCareArchives,
                    childCareRecords,
                } = this.patientInfo || {};
                if (childCareArchives === 1) {
                    // 已关联儿童健康档案
                    let growthCont = '无';
                    if (childCareRecords && childCareRecords.bodyGrowthData) {
                        // 生长记录
                        const {
                            bmi,
                            headSize,
                            height,
                            isBregmaClose,
                            lowerBodyLength,
                            teeth,
                            upperBodyLength,
                            weight,
                        } = childCareRecords.bodyGrowthData;
                        const strs = [];
                        height && strs.push(`身高${height}cm`);
                        weight && strs.push(`体重${weight}kg`);
                        bmi && strs.push(`BMI ${bmi}`);
                        headSize && strs.push(`头围${headSize}cm`);
                        upperBodyLength && strs.push(`上部量${upperBodyLength}cm`);
                        lowerBodyLength && strs.push(`下部量${lowerBodyLength}cm`);
                        isBregmaClose && strs.push(`前囟${isBregmaClose === 1 ? '开启' : '闭合'}`);
                        teeth && strs.push(`牙齿${teeth}颗`);

                        growthCont = strs.join('/');
                    }
                    let evaluationCont = '发育评测：无';
                    if (childCareRecords && childCareRecords.evaluationData) {
                        const {
                            evaluationName,
                            result,
                        } = childCareRecords.evaluationData;
                        const scores = result.map(
                            (item) => `${parseFloat(item.score)}分/${parseFloat(item.totalScore)}分`,
                        );
                        evaluationCont = `${evaluationName}：${scores.join(' ')}`;
                    }
                    const item = {
                        type: 'child',
                        title: '儿童健康档案',
                        contents: [`生长记录：${growthCont}`, evaluationCont],
                    };
                    healthFiles.push(item);
                }
                return healthFiles;
            },

            childFileInfo() {
                const {
                    childCareArchives,
                    childCareRecords,
                } = this.patientInfo || {};
                if (childCareArchives === 1) {
                    // 已关联儿童健康档案
                    if (childCareRecords?.bodyGrowthData) {
                        // 生长记录
                        const {
                            height,
                            weight,
                        } = childCareRecords.bodyGrowthData;
                        return `身高 ${height || 0}cm，体重 ${weight || 0}kg`;
                    }
                }
                return '暂无记录';
            },
            isLoading() {
                return this.loadingInfo || this.baseInfoLoading;
            },
            isOpenFamilyDoctor() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.FAMILY_DOCTOR);
            },
            isShowLiItem() {
                return this.visibleBindFiles || this.visibleFamilyDoctor || this.visibleCardInfo || this.visibleQrCode || this.visibleUnbindWX;
            },
            firstUnitArray() {
                return [{
                    id: 1,
                    name: '岁',
                }, {
                    id: 2,
                    name: '月',
                }];
            },
        },
        watch: {
            visible: {
                handler(val) {
                    if (val) {
                        this.initHandler();
                    }
                },
                deep: true,
                immediate: true,
            },
            showTagPopover: {
                handler(val) {
                    this.selectOption = val;
                },
                immediate: true,
                deep: true,
            },
        },
        updated() {
            this.$emit('update-popover');
        },
        beforeDestroy() {
            clearTimeout(this.timer1);
            clearTimeout(this.timer2);
        },
        created() {
            if (this.patientId === this.defaultPatient?.id) {
                this.patientInfo = clone(this.defaultPatient);
            }
            this.$abcEventBus.$on('fetch-referrer-patient-reward', (val) => {
                this.changeCascaderLoading = val;
            }, this);
        },
        methods: {
            ...mapActions('crm', ['updateTagsByFetch']),
            changeShowTagPopover(val) {
                this.showTagPopover = val;
            },
            handleClose() {
                this.visibleCardInfo = false;
                this.visibleFamilyDoctor = false;
            },
            validateMobile,
            validateIdCard,
            parseTime,
            handlePatientTagsChange(tagsInfo) {
                this.patientInfo = Object.assign({},this.patientInfo, tagsInfo);
                this.$emit('change-patient', this.patientInfo);
            },
            initHandler() {
                if (this.isInitData) return;

                if (this.patientId && !this.patientInfo) {
                    this.patientId !== ANONYMOUS_ID && this.fetchPatientOverview();
                    this.fetchBaseInfo();
                }
                if (!this.isAddPatient) {
                    this.initOriginLabels();
                }

                if (this.patientInfo?.id && !this.isAddPatient && !this.editor) {
                    this.fetchBaseInfo();
                }
                this.isInitData = true;
            },
            handleClickCover() {
                if (this._isAnimation) {
                    return;
                }
                this._isAnimation = true;
                if (this.postData?.address?.addressDetail === '') {
                    this.postData.address.addressDetail = null;
                }
                if (this.isAddPatient && !isEqual(this.postData,this.addOldPostData)) {
                    this.confirmSave = true;
                    this.timer1 = setTimeout(() => {
                        this.confirmSave = false;
                        this._isAnimation = false;
                    }, 300);
                }
                if (!this.isAddPatient && !this.noChangeData) {
                    this.confirmSave = true;
                    this.timer2 = setTimeout(() => {
                        this.confirmSave = false;
                        this._isAnimation = false;
                    }, 300);
                }
            },
            fetchBaseInfo() {
                this.baseInfoLoading = true;
                Promise.all([
                    this.fetchPatientBaseInfo(), // 拉取门诊付费信息
                ])
                    .finally(() => {
                        this.baseInfoLoading = false;
                    });
            },
            /**
             * @desc 获取设置项的标签，要进行前端过滤（这里应该是后台输出时过滤）
             * <AUTHOR> Yang
             * @date 2020-12-29 10:36:45
             */
            async initOriginLabels() {
                await this.updateTagsByFetch();
            },
            async handleRefresh() {
                await this.fetchPatientOverview();
                this.$emit('change-patient', this.patientInfo);
            },
            /**
             * 拉取患者预览信息
             * <AUTHOR>
             * @date 2020-06-29
             */
            async fetchPatientOverview() {
                try {
                    const patientId = this.patientInfo?.id || this.patientId;
                    if (patientId) {
                        const { data } = await CrmAPI.fetchPatientOverview(patientId, this.params);
                        this.patientInfo = Object.assign({}, this.patientInfo, data);
                    }
                } catch (error) {
                    console.log('fetchPatientOverview error', error);
                }
            },
            changeCardHolderHandles(value) {
                this.cardHolder = value;
            },
            changeMemberHandles(value) {
                this.memberHolder = value;
            },
            changeIsMemberFamilyOpen(value) {
                this.isMemberFamily = value;
            },
            changeCouponHandles(value) {
                this.couponHandles = value;
            },
            /**
             * 验证年龄月份
             * <AUTHOR>
             * @date 2020-06-29
             * @param {String} value
             * @param {Function} callback
             */

            async fetchPatientBaseInfo() {
                try {
                    const patientId = this.patientInfo?.id || this.patientId;
                    const { data: res } = await CrmAPI.fetchPatientBaseInfo(patientId);
                    const {
                        outpatientCount = 0,
                        cumulativeAmount = 0,
                        payCount = 0,
                        owePayedAmount = 0,
                    } = res && res.rows[0] || {};
                    this.patientBaseInfo.outpatientCount = outpatientCount;
                    this.patientBaseInfo.payCount = payCount;
                    this.patientBaseInfo.cumulativeAmount = cumulativeAmount;
                    this.patientBaseInfo.owePayedAmount = owePayedAmount;
                } catch (e) {
                    console.log(e);
                }
            },

            /**
             * 当点击编辑患者资料时
             * <AUTHOR>
             * @date 2020-06-29
             */
            onClickEdit() {
                this.$refs.crmPatientBasicInfo.onClickEdit();
            },
            /**
             * 当点击取消编辑
             * <AUTHOR>
             * @date 2020-06-29
             */
            onClickCancel() {
                if (this.editor) {
                    this.$refs.crmPatientBasicInfo.onClickCancel();
                } else {
                    this.$emit('close');
                }
                this.submitHasError = false;
            },
            /**
             * 选择标签时，点击外侧
             * <AUTHOR>
             * @date 2020-06-29
             */
            outside() {
                if (this.showTagPopover) {
                    this.showTagPopover = false;
                }
            },
            /**
             * 取消标签
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} tag 点击取消的标签
             */
            clickCloseLabel(tag) {
                this.clickHandleTag({
                    id: tag.tagId,
                    name: tag.tagName,
                });
            },
            /**
             * 处理标签
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} tag 点击取消的标签
             */
            async clickHandleTag(tag) {
                try {
                    if (this.selectedIds.includes(tag.id)) {
                        // 存在-此时删除该标签
                        await CrmAPI.deletePatientTag(this.patientInfo.id, tag.id);
                    } else {
                        // 不存在-此时打上标签
                        await CrmAPI.addPatientsTags({
                            patients: [this.patientInfo.id],
                            tags: [
                                {
                                    tagId: tag.id,
                                    tagName: tag.name,
                                },
                            ],
                        });
                    }
                    this.patientInfo = await this.getPatientTags();
                    this.$emit('change-patient', this.patientInfo);
                } catch (error) {
                    console.log('clickHandleTag error', error);
                }
            },
            /**
             * 拉取患者最新标签
             * <AUTHOR>
             * @date 2020-06-29
             */
            async getPatientTags() {
                try {
                    const { data } = await CrmAPI.fetchPatientTags(this.patientInfo.id);
                    return Object.assign({}, this.patientInfo, {
                        tags: data,
                    });
                } catch (error) {
                    console.log('getPatientTags error', error);
                }
            },
            // 患者已经存在处理
            handlePatientExist(code = 13503, detail = [], isModify = true) {
                const {
                    name = '',
                    mobile = '',
                    idCard = '',
                    idCardType = DEFAULT_CERT_TYPE,
                    id = '',
                } = detail?.[0] || {};
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    confirmText: isModify ? '添加患者' : '使用患者',
                    showCancel: false,
                    content: code === 13502 ? `证件号[${idCardType}]${idCard}已经被连锁中存在的患者 ${name} 注册` : `同名患者 ${name} ${mobile ? `${this.isCanSeePatientMobileInCrm ? mobile : encryptMobile(mobile)}` : ''} ${idCard ? `[${idCardType}]${idCard}` : ''}已在连锁中存在`,
                    onConfirm: async () => {
                        await this.addPatientFromOtherClinic(id, isModify);
                    },
                });
            },
            // 从其他门店添加患者
            async addPatientFromOtherClinic(id, isModify = false) {
                try {
                    await CrmAPI.handlePatientExist(id);
                    if (isModify) {
                        this.$Toast({
                            message: '添加患者成功',
                            type: 'success',
                        });
                    } else {
                        this.$Toast({
                            message: '使用患者成功',
                            type: 'success',
                        });
                        this.$emit('add-and-use-patient', id);
                    }
                } catch (e) {
                    console.log(e);
                }
            },
            async confirm() {
                this.saveLoading = true;
                try {
                    let params = clone(this.postData);
                    params = {
                        ...params,
                        ...params.address,
                        addressDetail: params.addressDetail,
                    };
                    delete params.address;

                    if (this.patientInfo?.id) {
                        await CrmAPI.updatePatientInfo(this.patientInfo.id, params);
                        this.editor = false;
                        this.$Toast({
                            message: '修改成功',
                            type: 'success',
                        });
                        await this.fetchPatientOverview();
                        this.$emit('change-patient', this.patientInfo);
                    } else {
                        const { data } = this.postData?.id ? await CrmAPI.updatePatientInfo(this.postData.id,params) : await CrmAPI.insertPatientInfo(params);
                        this.$emit('add-patient', data);
                        this.$Toast({
                            message: '添加成功',
                            type: 'success',
                        });
                    }
                    this.submitHasError = false;

                } catch (error) {
                    this.submitHasError = true;
                    console.log('onClickSave error', error);
                    if ([13502, 13503].includes(error.code) && this.crmAccessVisibility) {
                        this.handlePatientExist(error.code, error.detail, !!this.patientInfo?.id);
                    } else if (error?.code === 10409 || error?.code === 409) {
                        // 存在已经被共享的会员
                        this.$Toast({
                            message: error.message,
                            type: 'error',
                        });
                    } else if (error?.code === 13993) {
                        // 身份证被注册
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: error.message,
                        });
                    } else if (error?.code === 13992) {
                        // 该患者信息（姓名和手机号）已经被注册
                        this.$Toast({
                            message: error.message,
                            type: 'error',
                        });
                    }
                }
                this.saveLoading = false;
            },
            /**
             * 当点击保存时
             * <AUTHOR>
             * @date 2020-06-29
             */
            async onClickSave() {
                if (this.saveLoading) return;
                await this.$refs.crmPatientBasicInfo.validate();
            },
            onlyValidate() {
                return this.$refs.crmPatientBasicInfo.onlyValidate();
            },
            /**
             * 地址样式
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} address 地址对象
             * @returns {String}
             */
            getAddress(address) {
                const {
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                } = address || {};
                const arr = [];
                if (addressProvinceName) arr.push(addressProvinceName);
                if (addressCityName) arr.push(addressCityName);
                if (addressDistrictName) arr.push(addressDistrictName);
                if (arr.length !== 0) {
                    return arr.join(' / ');
                }
                return '-';

            },
            getMarital(marital) {
                switch (marital) {
                    case MaritalStatusEnum.single:
                        return '未婚';
                    case MaritalStatusEnum.married:
                        return '已婚';
                    case MaritalStatusEnum.divorced:
                        return '离异';
                    case MaritalStatusEnum.widow:
                        return '丧偶';
                    default:
                        return '';
                }
            },
            /**
             * 当档案号输入时，允许输入数字（可0开头）
             * <AUTHOR>
             * @date 2021-01-05
             */
            onInputSn() {
                if (this.postData) {
                    this.$nextTick(() => {
                        this.postData.sn = this.postData.sn.replace(/\D/g, '');
                    });
                }
            },

            handleCommon() {
                this.showLiItem = true;
                const {
                    clientHeight,
                    offsetHeight,
                    scrollTop,
                } = this.$el;
                this.maxHeight = clientHeight;
                this.coverHeight = offsetHeight;
                this.coverTop = scrollTop;
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

.crm-module__package-card__base-card-dentistry {
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 16px 6px 16px 16px;
    overflow-x: hidden;
    overflow-y: scroll;
    cursor: auto;
    background-color: $S2;
    border: 1px solid var(--abc-color-P7);

    .mobile-required {
        position: relative;

        &::after {
            position: absolute;
            top: 8px;
            right: -7px;
            font-size: 14px;
            color: #ff9933;
            content: "*";
        }
    }

    .patient-display-tag {
        width: 20px;
        height: 20px;
    }

    .patient-card-cover {
        position: fixed;
        top: 58px;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        opacity: 0;
    }

    &.base-card-confirm-style {
        animation: left-right-dou 0.15s ease infinite;
    }

    &.overflow-y-hidden {
        padding: 16px 16px;
        overflow-y: hidden;
        border: 1px solid rgba(0, 0, 0, 0.3);
    }

    .base-card-cover {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 10;
        width: 100%;
        height: 100vh;
        background: $T1;
        opacity: 0.3;
    }

    .btn {
        @include flex(row, flex-end, center);

        position: absolute;
        top: 12px;
        right: 12px;
    }

    .add-patient-head {
        padding: 11px 6px;

        .title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 16px;
            font-size: 16px;
            font-weight: bold;
            color: $S1;
        }
    }

    .patient-head-info {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 32px;
        padding: 0 6px 0 12px;
        font-size: 14px;
        font-weight: 400;
        color: $S1;
        background: $S2;

        .name {
            margin-right: 12px;
            font-weight: bold;
        }

        .sex,
        .age,
        .mobile {
            margin-right: 8px;
        }

        .delete-icon-wrapper {
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 100%;
            margin-left: auto;
        }
    }

    .patient-head-info + .content {
        border-top: 1px solid $P4;
    }

    .content {
        position: relative;
        z-index: 2;
        -webkit-font-smoothing: antialiased;

        .patient-display-tag {
            width: 20px;
            height: 20px;

            &-box_wechat {
                display: flex;
                align-items: center;
                justify-content: space-around;
                width: 20px;
                height: 20px;
                margin-right: 8px;
                margin-bottom: 8px;
                background-color: #1ec761;
                border-radius: var(--abc-border-radius-small);

                img {
                    width: 14px !important;
                    height: 14px !important;
                    margin-right: 0 !important;
                }
            }
        }

        .crm-patient-basic-info {
            margin-top: 12px;
            margin-bottom: 12px;

            &-add {
                margin-bottom: 0 !important;
            }
        }

        .crm-patient-shebao-info {
            margin-bottom: 12px;
        }

        .display-split-line {
            width: 100%;
            height: 1px;
            margin-top: 20px;
            margin-bottom: 16px;
            background-color: $P6;
        }

        .thr {
            .crm-module__package-label__abc-label {
                margin-right: 8px;
            }
        }

        .statistics-line {
            line-height: 1;

            &-box {
                height: auto;

                span {
                    display: inline-block;
                    height: 18px;
                    margin-bottom: 7px;
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 18px;
                    color: $S1;
                }
            }
        }

        .patient-data {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 24px;
            margin: 12px 0 4px;

            &.add-patient {
                margin-top: 6px;
            }

            .title {
                font-size: 13px;
                font-weight: bold;
                line-height: 16px;
                color: $S1;
            }

            .modify-btn {
                display: inline-flex;

                .abc-button {
                    width: 44px;
                    min-width: 44px;
                    height: 24px;
                    padding: 0;
                    font-size: 12px;

                    &.save-btn {
                        min-width: 52px;
                    }

                    & + .abc-button {
                        margin-left: 4px;
                    }
                }
            }
        }

        .text-line {
            width: 100%;
            height: 1px;
            background-color: $P4;
        }

        .card-footer {
            padding: 0 6px;
            margin-bottom: 12px;

            .footer-btn {
                display: inline-flex;
                align-items: center;
                justify-content: flex-end;
                width: 100%;
                height: 26px;
                line-height: 26px;

                .abc-button {
                    min-width: 48px;
                    height: 26px;
                    padding: 0 10px;
                }
            }
        }
    }

    .crm-basic-content {
        position: static !important;
    }

    .dialog-box {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 9999999;
        box-sizing: border-box;
        width: calc(100% - 24px);
        height: auto;
        background: $S2;
        border-radius: var(--abc-border-radius-small);
        transform: translate(-50%, -50%);

        &.dialog-wx-box {
            width: 183px;
            height: 211px;
        }
    }
}

.patient-label-tags__popper {
    padding: 0;

    &.ml15 {
        left: 15px;
    }

    .box {
        width: 246px;
        max-height: 254px;
        overflow-y: overlay;

        .alert {
            @include flex(row, center, center);

            height: 46px;
            margin: 12px 12px 0 12px;
            background-color: #f0f7ff;
            border: 1px solid #e0efff;

            .iconfont {
                margin-right: 8px;
            }
        }
    }
}
</style>
