<template>
    <div v-abc-loading="loading" class="patient-list-card-wrapper">
        <ul>
            <li v-for="item in medicalList" :key="item.name" class="patient-card-item">
                <span class="ellipsis item-name" :title="parseDiagnosis(item.diagnosis)">{{ item.diagnosis | parseDiagnosis }}</span>
                <span class="ellipsis doctor-name">{{ item.doctorName }}</span>
                <span class="ellipsis time">{{ item.diagnosedDate | parseTime('m-d', false) }}</span>
            </li>
        </ul>
    </div>
</template>

<script>
    import { parseDiagnosis } from '@/filters';
    export default {
        name: 'PatientListCard',
        props: {
            medicalList: Array,
            loading: Boolean,
        },
        methods: {
            parseDiagnosis,
        },
    };
</script>

<style lang="scss">
@import '../../../styles/theme';

.patient-list-card-wrapper {
    position: relative;
    width: 276px;
    max-height: 272px;
    padding: 4px 0;
    overflow-y: auto;
    overflow-y: overlay;
    background-color: var(--abc-color-S2);
    border: 1px solid var(--abc-color-P7);
    border-radius: var(--abc-border-radius-small);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

    .empty-card {
        min-height: 120px;
    }

    .patient-card-item {
        display: flex;
        padding: 12px;
        border-bottom: 1px solid $P6;

        .item-name {
            width: 140px;
            margin-right: 22px;
            font-weight: 400;
            color: $T1;
        }

        .doctor-name {
            width: 36px;
            font-size: 12px;
            color: $T2;
        }

        .time {
            width: 52px;
            font-size: 12px;
            color: $T2;
            text-align: right;
        }
    }
}
</style>
