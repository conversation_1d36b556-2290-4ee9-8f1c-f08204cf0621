<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        title="患者信息确认"
        size="large"
    >
        <abc-layout preset="dialog-table">
            <abc-layout-header>
                <abc-tips-card-v2 theme="primary">
                    患者信息与{{ cardName }}信息不一致，更新为{{ cardName }}信息可完成绑定
                </abc-tips-card-v2>
            </abc-layout-header>
            <abc-layout-content>
                <abc-table
                    ref="abcTable"
                    :render-config="renderConfig"
                    :loading="loading"
                    :data-list="dataList"
                >
                    <template #patientInfo="{ trData: row }">
                        <abc-table-cell>
                            <abc-radio-group
                                v-model="value1"
                                style="height: 22px !important;"
                            >
                                <abc-radio :disabled="true" :label="1">
                                    {{ row.patientInfo || '--' }}
                                </abc-radio>
                            </abc-radio-group>
                        </abc-table-cell>
                    </template>
                    <template #shebaoPatientInfo="{ trData: row }">
                        <abc-table-cell>
                            <abc-radio-group
                                v-model="value2"
                                style="height: 22px !important;"
                            >
                                <abc-radio :disabled="true" :label="1">
                                    {{ row.shebaoPatientInfo }}
                                </abc-radio>
                            </abc-radio-group>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-layout-content>
        </abc-layout>

        <template slot="footer">
            <abc-flex style="width: 100%;" justify="flex-end">
                <abc-space>
                    <abc-button
                        type="primary"
                        :loading="updateLoading"
                        @click="handleUpdateAndBind"
                    >
                        更新并绑定
                    </abc-button>
                    <abc-button
                        type="blank"
                        @click="handleSwitchPatient"
                    >
                        切换患者
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    export default {
        name: 'MergeCrmDialog',
        props: {
            value: Boolean,
            mergeBindPatientInfo: {
                type: Object,
                default: () => ({
                    name: '',
                    idCard: '',
                }),
            },
            mergeShebaoPatientInfo: {
                type: Object,
                default: () => ({
                    name: '',
                    idCard: '',
                }),
            },
            updateLoading: Boolean,
        },

        data() {
            return {
                loading: false,
                value1: null,
                value2: 1,
            };
        },

        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(value) {
                    this.$emit('input', value);
                },
            },
            cardName() {
                return '医保卡';
            },
            dataList() {
                return [
                    {
                        name: '姓名',
                        patientInfo: this.mergeBindPatientInfo.name,
                        shebaoPatientInfo: this.mergeShebaoPatientInfo.name,
                    },
                    {
                        name: '身份证号',
                        patientInfo: this.mergeBindPatientInfo.idCard,
                        shebaoPatientInfo: this.mergeShebaoPatientInfo.idCard,
                    },
                ];
            },
            renderConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'name',
                            label: '',
                            style: {
                                width: '80px',
                            },
                        },
                        {
                            key: 'patientInfo',
                            label: '患者信息',
                            style: {
                                minWidth: '120px',
                                width: '160px',
                                flex: 1,
                            },
                        },
                        {
                            key: 'shebaoPatientInfo',
                            label: '医保卡信息',
                            style: {
                                minWidth: '120px',
                                width: '160px',
                                flex: 1,
                            },
                        },

                    ],
                };
            },
        },

        methods: {
            /**
             * 处理选择变更
             * @param {string} value - 选中的值
             */
            handleSelectChange(value) {
                this.selectedInfo = value;
            },

            /**
             * 更新并绑定
             */
            handleUpdateAndBind() {
                this.$emit('update-and-bind');
            },

            /**
             * 切换患者
             */
            handleSwitchPatient() {
                this.$emit('switch-patient');
                this.closeDialog();
            },

            /**
             * 关闭弹窗
             */
            closeDialog() {
                this.showDialog = false;
            },
        },
    };
</script>
