<template>
    <abc-dialog
        v-model="showDialog"
        append-to-body
        class="read-card-bind-patient-dialog"
        :content-styles="contentStyles"
        size="medium"
    >
        <div class="bind-patient-content">
            <abc-tabs-v2
                v-model="bindType"
                type="outline"
                size="middle"
                :option="options"
                style="margin-bottom: 16px;"
                @change="handleTypeChange"
            ></abc-tabs-v2>
            <abc-form
                v-if="!bindType"
                ref="patientForm"
                label-position="top"
                :label-width="100"
                item-block
            >
                <patient-base-card
                    class="bind-patient-base-card"
                    :patient="newPatient"
                    :input-width="282"
                    :source-list="sourceChildList"
                    show-tag
                    :error-data="errorData"
                    :card-info="getCurCardInfo()"
                    show-split-line
                    :clinic-address="clinicAddress"
                    @change="handleBindPatientChange"
                ></patient-base-card>
            </abc-form>
            <abc-form v-else ref="bindPatientForm">
                <abc-flex align="center">
                    <!--                    <abc-form-item :validate-event="handleValidateBindPatient" style="margin-right: 0; margin-bottom: 0;">-->
                    <!--                        <abc-autocomplete-->
                    <!--                            ref="patientSearch"-->
                    <!--                            v-model.trim="keyword"-->
                    <!--                            custom-class="patient-suggestion"-->
                    <!--                            :disabled="!!(bindPatient && bindPatient.id)"-->
                    <!--                            inner-width="auto"-->
                    <!--                            :delay-time="0"-->
                    <!--                            :width="312"-->
                    <!--                            :async-fetch="true"-->
                    <!--                            :fetch-suggestions="handleSearchPatient"-->
                    <!--                            :max-length="20"-->
                    <!--                            focus-show-->
                    <!--                            placeholder="输入绑定患者的姓名/手机号"-->
                    <!--                            clearable-->
                    <!--                            @change="fetchPatient"-->
                    <!--                            @blur="handleBlur"-->
                    <!--                            @enterEvent="selectPatient"-->
                    <!--                            @clear="clearPatient"-->
                    <!--                        >-->
                    <!--                            <template slot="suggestion-header">-->
                    <!--                                <div class="suggestion-title">-->
                    <!--                                    <div class="patient-info">-->
                    <!--                                        患者信息-->
                    <!--                                    </div>-->
                    <!--                                    <div class="mr-info">-->
                    <!--                                        上次就诊-->
                    <!--                                    </div>-->
                    <!--                                </div>-->
                    <!--                            </template>-->

                    <!--                            <template slot="suggestions" slot-scope="props">-->
                    <!--                                <dt-->
                    <!--                                    class="patient-suggestions suggestions-item"-->
                    <!--                                    :class="{ selected: props.index == props.currentIndex }"-->
                    <!--                                    :disabled="props.suggestion.disabled"-->
                    <!--                                    @click="selectPatient(props.suggestion)"-->
                    <!--                                    v-html="patientInfo(props.suggestion)"-->
                    <!--                                >-->
                    <!--                                </dt>-->
                    <!--                            </template>-->
                    <!--                        </abc-autocomplete>-->
                    <!--                    </abc-form-item>-->
                    <!--                    <patient-card :patient-id="bindPatient && bindPatient.id" class="bind-patient-card"></patient-card>-->
                    <patient-section
                        ref="patientSection"
                        v-model="bindPatient"
                        source="shebaoCard"
                        :is-required="true"
                        :is-member-create="true"
                        :allow-show-tags="false"
                        :is-small="true"
                        :focus-show-patient="!bindPatient.id"
                        @change-patient="selectPatient"
                        @clear-patient="clearPatient"
                        @update-patient="selectPatient"
                    ></patient-section>
                </abc-flex>
                <abc-flex v-if="patientMedicalListTotalCount" style="margin-top: 8px;" align="center">
                    <abc-popover trigger="hover" theme="custom">
                        <abc-text slot="reference" theme="primary" class="medical-count">
                            已就诊{{ patientMedicalListTotalCount }}次
                        </abc-text>
                        <patient-medical :medical-list="patientMedicalList"></patient-medical>
                    </abc-popover>
                </abc-flex>
                <div v-if="errorInfo" class="repeat-error-info">
                    <div style="display: flex; margin-bottom: 4px;">
                        <p class="orange-text">
                            该患者已绑定如下医保卡，需解绑后才可绑定
                        </p>
                        <abc-button type="text" style=" height: 20px; margin-left: 16px; line-height: 20px;" @click="unbindShebaoCard">
                            解绑
                        </abc-button>
                    </div>
                    <div class="err-detail-item">
                        <span class="label-text">持卡人</span>：
                        {{ errorInfo.name }}&nbsp;{{ errorInfo.sex }}&nbsp;{{ formatAge(errorInfo.age) }}
                    </div>
                    <div class="err-detail-item">
                        <span class="label-text">卡 号</span>：
                        {{ errorInfo.shebaoCardInfo && errorInfo.shebaoCardInfo.cardNo }}
                    </div>
                    <div class="err-detail-item">
                        <span class="label-text">身份证</span>：
                        {{ errorInfo.shebaoCardInfo && errorInfo.shebaoCardInfo.idCardNo }}
                    </div>
                </div>
            </abc-form>
        </div>
        <div slot="footer" class="patient-footer">
            <abc-button :loading="btnLoading" @click="handleConfirm">
                {{ !bindType ? '确定' : '完成绑定' }}
            </abc-button>
            <abc-button type="blank" @click="handleCancel">
                取消
            </abc-button>
        </div>
        <merge-crm-dialog
            v-if="showMergeDialog"
            v-model="showMergeDialog"
            :merge-bind-patient-info="mergeBindPatientInfo"
            :merge-shebao-patient-info="mergeShebaoPatientInfo"
            :update-loading="updateLoading"
            @switch-patient="clearPatient"
            @update-and-bind="updateAndbind"
        ></merge-crm-dialog>
    </abc-dialog>
</template>

<script>
    import PatientBaseCard from 'views/layout/read-card/patient-base-card.vue';
    import PatientMedical from './patient-medical.vue';

    // const PatientCard = () => import('views/crm/common/package-card');

    import PatientsAPI from 'api/patients.js';
    import ClinicAPI from 'api/clinic.js';
    import {
        formatAge, parseTime,
    } from '@/utils';
    import { RecommendService } from '@/service/recommend.js';
    import clone from 'utils/clone.js';

    import { mapGetters } from 'vuex';
    import CrmAPI from 'api/crm.js';
    import { ReadCardType } from 'views/layout/read-card/costants.js';
    import MergeCrmDialog from './merge-crm-dialog.vue';
    export default {
        name: 'BindPatient',
        components: {
            PatientBaseCard,
            // PatientCard,
            MergeCrmDialog,
            PatientMedical,
            PatientSection: () => import('views/layout/patient/patient-section/index.vue'),
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            cardType: {
                type: Number,
                required: true,
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            idCardInfo: {
                type: Object,
                default: null,
            },
            // 匹配的患者信息大于1，默认带入患者姓名搜索
            // 匹配的患者信息等于1，默认选中匹配的患者
            // 没有匹配的患者，切换到绑定已有患者，代入患者姓名，聚焦搜索
            matchedPatients: {
                type: Array,
                default: () => {
                    return [];
                },
            },
        },
        data() {
            return {
                bindType: 0, // 0 新建患者， 1 绑定患者
                newPatient: null, // 新建患者的数据
                patientBasicInfo: {},
                bindPatient: {
                    id: '',
                    name: '',
                    sex: '男',
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    mobile: '',
                    idCard: '',
                }, // 绑定的患者数据
                keyword: '',
                patientMedicalList: [], // 患者的就诊历史数据
                patientMedicalListTotalCount: 0, // 患者的就诊历史次数
                sourceChildList: [], // 就诊来源列表
                errorData: null,
                clinicAddress: {}, // 诊所地址
                errorInfo: null,
                btnLoading: false,
                showMergeDialog: false,
                mergeBindPatientInfo: {},
                mergeShebaoPatientInfo: {},
                updateLoading: false,
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input',v);
                },
            },
            options() {
                return [
                    {
                        label: '新建患者',
                        value: 0,
                    },
                    {
                        label: '绑定已有患者',
                        value: 1,
                    },
                ];
            },
            ...mapGetters(['currentClinic']),
            contentStyles() {
                if (this.bindType) {
                    return 'padding: 24px;min-height: 165px;height: auto;overflow:unset;';
                }
                return 'padding: 24px;min-height: 405px;height: auto;max-height: 715px;';
            },
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId || null;
            },
            isShebaoCard() {
                return this.cardType === ReadCardType.SHE_BAO;
            },

        },
        created() {
            if (this.matchedPatients.length === 1) {
                this.bindPatient = clone(this.matchedPatients[0]);
                this.handleBlur();
                this.bindType = 1; // 0 新建患者， 1 绑定患者
                this.findHistoryAbs(this.bindPatient.id);
            }
            this.fetchClinicInfo();
            this.getListSource();
            this.keyword = this.isShebaoCard ? this.shebaoCardInfo?.name : this.idCardInfo?.name;
            this.newPatient = this.getCurCardInfo();
        },
        methods: {
            formatAge,

            /**
             * @desc 获取诊所的基本设置，保存诊所地址，新建患者默认带入诊所地址
             * <AUTHOR>
             * @date 2021-10-18 11:20:44
             */
            async fetchClinicInfo() {
                try {
                    const { data } = await ClinicAPI.fetchClinicInformation(this.currentClinic.clinicId);
                    this.clinicAddress = {
                        addressCityId: data.addressCityId || '',
                        addressCityName: data.addressCityName || '',
                        addressProvinceId: data.addressProvinceId || '',
                        addressProvinceName: data.addressProvinceName || '',
                        addressDistrictId: data.addressDistrictId || '',
                        addressDistrictName: data.addressDistrictName || '',
                    };
                } catch (e) {
                    console.warn('获取医疗机构信息失败', e);
                }
            },
            getCurCardInfo() {
                const cardInfo = this.isShebaoCard ? this.shebaoCardInfo : this.idCardInfo;
                return {
                    id: '',
                    name: cardInfo?.name ,
                    sex: cardInfo?.sex,
                    mobile: cardInfo?.mobile,
                    idCard: this.isShebaoCard ? cardInfo?.idCardNo : cardInfo?.idCard,
                    birthday: cardInfo?.birthday,
                    addressDetail: this.isShebaoCard ? null : this.idCardInfo.addressInfo,
                };
            },
            /**
             * @desc 新建患者
             */
            handleBindPatientChange(val) {
                Object.assign(this.newPatient, val);
                this.newPatient.addressCityId = val.address?.addressCityId;
                this.newPatient.addressCityName = val.address?.addressCityName;
                this.newPatient.addressProvinceId = val.address?.addressProvinceId;
                this.newPatient.addressProvinceName = val.address?.addressProvinceName;
                this.newPatient.addressDistrictId = val.address?.addressDistrictId;
                this.newPatient.addressDistrictName = val.address?.addressDistrictName;
            },
            handleTypeChange(index) {
                this.bindType = index;
                if (index) {
                    // eslint-disable-next-line vue/max-len
                    this.bindPatient.name = this.bindPatient.name ? this.bindPatient.name : (this.isShebaoCard ? this.shebaoCardInfo?.name : this.idCardInfo?.name);
                    if (!this.bindPatient.id && this.bindPatient.name) {
                        this.$nextTick(() => {
                            this.$refs?.patientSection?.$refs?.autoComplete?.focusInput();
                        });
                    }
                    // this.$nextTick(() => {
                    //     // 没有匹配到患者，或者没有选中绑定的患者，则触发搜索
                    //     if ((!this.matchedPatients || !this.matchedPatients.length) && !this.bindPatient) {
                    //         $(this.$refs.patientSearch.$el).find('input').focus();
                    //         this.handleFocus();
                    //     }
                    // });
                }
            },
            handleCancel() {
                this.$emit('cancel');
            },
            handleValidateBindPatient(val, callback) {
                if (this.bindPatient && this.bindPatient.id) {
                    callback({
                        validate: true,
                    });
                }
                if (this.keyword && !this.bindPatient) {
                    callback({
                        message: '无同名患者',
                        validate: false,
                    });
                }
                if (!this.keyword) {
                    callback({
                        message: '请选择已有患者',
                        validate: false,
                    });
                }
            },
            handleConfirm(needForce = 0) {
                if (this.bindType) {
                    const currentPatient = this.getCurCardInfo();
                    // his患者姓名或者证件号与医保患者姓名或者证件号不符
                    if ((!!this.bindPatient.id) && (this.bindPatient.name !== currentPatient.name || this.bindPatient.idCard !== currentPatient.idCard)) {
                        this.showMergeDialog = !!this.bindPatient.id;
                        this.mergeBindPatientInfo = this.bindPatient;
                        this.mergeShebaoPatientInfo = currentPatient;
                        return;
                    }
                }
                this.errorData = null;
                const resPatient = this.bindType ? this.bindPatient : this.newPatient;
                if (this.bindType) {
                    // 绑定患者
                    this.$refs.bindPatientForm.validate(async (val) => {
                        if (val) {
                            await this.handleBindPatient(resPatient, needForce);
                        }
                    });
                } else {
                    // 新建患者
                    this.$refs.patientForm.validate(async (val) => {
                        if (val) {
                            await this.handleAddShebaoPatient(resPatient);
                        }
                    });
                }
            },
            /**
             * @desc 绑定现有患者
             */
            async handleBindPatient(patient, needForce = 0) {
                try {
                    this.btnLoading = true;
                    this.errorInfo = null;
                    const postData = this.isShebaoCard ? {
                        shebaoCardInfo: this.shebaoCardInfo,
                    } : this.idCardInfo;
                    postData.cardType = this.cardType;
                    if (needForce === 1) {
                        postData.needForce = 1;
                    }
                    const { data } = await CrmAPI.bindPatientByCard(patient.id, postData);
                    this.btnLoading = false;
                    this.$emit('confirm',data);
                    if (needForce === 1) {
                        this.$Toast({
                            message: '更新并绑定患者成功',
                            type: 'success',
                        });
                    } else {
                        this.$Toast({
                            message: '绑定患者成功',
                            type: 'success',
                        });
                    }
                } catch (e) {
                    if (e.code === 13994) {
                        this.errorInfo = e?.detail || {};
                    }
                    this.btnLoading = false;
                    this.loadingConfirm = false;
                } finally {
                    this.btnLoading = false;
                    this.updateLoading = false;
                    this.showMergeDialog = false;
                }
            },
            /**
             * @desc 解除和患者绑定的社保信息
             * <AUTHOR>
             * @date 2021-12-03 18:29:49
             */
            unbindShebaoCard() {
                const { id } = this.errorInfo;
                if (!id) return ;
                try {
                    CrmAPI.unbindPatientSoicalCard(id);
                    this.errorInfo = null;
                    this.$Toast({
                        message: '解绑成功',
                        type: 'success',
                    });
                } catch (e) {
                    console.warn(e);
                }
            },
            /**
             * @desc 新建患者
             */
            async handleAddShebaoPatient(patient) {
                this.btnLoading = true;
                try {
                    this.btnLoading = true;
                    const postData = {
                        ...patient,
                        cardType: this.cardType,
                        addressInfo: this.idCardInfo?.addressInfo,
                        shebaoCardInfo: this.shebaoCardInfo,
                    };
                    const { data } = await CrmAPI.addPatientWithCard(postData);
                    this.$emit('confirm', data);
                } catch (e) {
                    //    13992 姓名手机号重复  13993 身份证号重复
                    if (e.code === 13992 || e.code === 13993) {
                        // this.errorData.code = e.code;
                        const rows = await this.fetchMatchedPatients(patient);
                        this.errorData = {
                            code: e.code,
                            rows,
                        };
                        this.$nextTick(() => {
                            this.$refs.patientForm.validate();
                        });
                    }
                } finally {
                    this.btnLoading = false;
                    this.loadingConfirm = false;
                    this.btnLoading = false;
                }
            },
            /**
             * @desc 获取重复的患者信息
             */
            async fetchMatchedPatients(patient) {
                try {
                    const { data } = await CrmAPI.fetchMatchedPatients({
                        name: patient.name,
                        mobile: patient.mobile,
                        idCard: patient.idCard,
                    });
                    return data && data.rows || [];

                } catch (e) {
                    console.log('获取匹配的患者信息失败');
                }
            },

            /**
             * @desc 搜索患者
             * <AUTHOR>
             * @date 2021-09-22 17:51:54
             * @params
             * @return
             */
            async handleSearchPatient(queryString, callback) {
                queryString = queryString.trim();

                if (!queryString) {
                    callback([]);
                    return false;
                }

                try {
                    const { data } = await PatientsAPI.fetchPatientsByName(queryString);

                    if (data.name !== queryString || !data) return false;

                    callback(data.list.map((item) => {
                        item.disabled = this.isMemberModule && +item.isMember === 1;
                        return item;
                    }));
                } catch (e) {
                    console.error(e);
                }
            },
            handleFocus() {
                this.keyword = this.isShebaoCard ? this.shebaoCardInfo?.name : this.idCardInfo?.name;
            },
            handleBlur() {
                let str = '';
                if (this.bindPatient && this.bindPatient.id) {
                    if (this.bindPatient.name) {
                        str += this.bindPatient.name;
                    }
                    if (this.bindPatient.sex) {
                        str += ` ${this.bindPatient.sex}`;
                    }
                    if (this.bindPatient.age) {
                        str += ` ${formatAge(this.bindPatient.age)}`;
                    }
                    if (this.bindPatient.mobile) {
                        str += ` ${this.bindPatient.mobile}`;
                    }
                }
                this.keyword = str || this.keyword;
            },
            async fetchPatient() {
                if (!this.bindPatient) return;
                const {
                    id, name, mobile,
                } = this.bindPatient;

                if (id || !name || !mobile) return false;

                const { data } = await PatientsAPI.fetchPatientByNameMobile(name, mobile);
                if (data) {
                    this.id = data.id;

                    this.bindPatient.sex = data.sex || this.bindPatient.sex;

                    if (!this.bindPatient.age.year && !this.bindPatient.age.month) {
                        this.bindPatient.age = data.age;
                    }

                    // 获取病史
                    this.findHistoryAbs(data.id);
                }
            },

            /**
             * @desc 获取患者的历史数据
             * <AUTHOR>
             * @date 2021-09-22 17:53:01
             */
            async findHistoryAbs(id) {
                if (!id) return;
                const { data } = await PatientsAPI.fetchHistoryAbs(id);
                this.patientMedicalList = data && data.result || [];
                this.patientMedicalListTotalCount = data.totalCount;
                console.log(this.patientMedicalList);
            },
            async updateAndbind() {
                this.updateLoading = true;
                try {
                    // const currentPatient = this.getCurCardInfo();
                    // const params = {
                    //     id: this.patientBasicInfo.id,
                    //     name: currentPatient.name,
                    //     sex: this.patientBasicInfo.sex,
                    //     idCardType: this.patientBasicInfo.idCardType, // 社保读卡的患者没有证件类型信息有可能会存在无法更新成功的问题
                    //     idCard: currentPatient.idCard,
                    //     birthday: null,
                    //     age: this.patientBasicInfo.age,
                    // };
                    // await CrmAPI.updatePatientInfo(this.patientBasicInfo.id, params);
                    // // TODO 待接口支持
                    // this.bindPatient.name = currentPatient.name;
                    // this.bindPatient.idCard = currentPatient.idCard;
                    this.errorData = null;
                    this.$refs.bindPatientForm.validate(async (val) => {
                        if (val) {
                            await this.handleBindPatient(this.bindPatient, 1);
                        }
                    });
                } catch (e) {
                    console.log(e);
                    this.updateLoading = false;
                    this.showMergeDialog = false;
                    this.$Toast({
                        message: '患者信息更新绑定失败，请手动从患者卡片或从患者模块更新患者信息后再进行绑定',
                        type: 'error',
                    });
                }
            },
            clearPatient() {
                this.keyword = '';
                this.patientMedicalListTotalCount = 0;
                this.patientMedicalList = [];
                this.bindPatient = {
                    id: '',
                    name: '',
                    sex: '男',
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    mobile: '',
                    idCard: '',
                };
                this.errorInfo = null;
            },
            async selectPatient(patient) {
                if (!patient) return false;

                // 去除age中的0
                const { age } = patient;
                if (+age.year === 0) { age.year = null; }

                if (+age.month === 0) { age.month = null; }
                this.bindPatient = {
                    id: patient.id,
                    name: patient.name,
                    age,
                    sex: patient.sex,
                    mobile: patient.mobile,
                    idCard: patient.idCard,
                };
                this.patientBasicInfo = patient;

                this.handleBlur();
                // 获取病史
                await this.findHistoryAbs(patient.id);
                // const currentPatient = this.getCurCardInfo();
                // // his患者姓名或者证件号与医保患者姓名或者证件号不符
                // if ((!!this.bindPatient.id) && (this.bindPatient.name !== currentPatient.name || this.bindPatient.idCard !== currentPatient.idCard)) {
                //     this.showMergeDialog = !!this.bindPatient.id;
                //     this.mergeBindPatientInfo = this.bindPatient;
                //     this.mergeShebaoPatientInfo = currentPatient;
                // }
            },
            patientInfo(patient) {
                // 会员模块特殊处理
                return `<div class="name ellipsis">
                            <span ${patient.isMember ? 'class="vip"' : ''}>${patient.name}</span>
                        </div>
                        <div class="sex">${patient.sex}</div>
                        <div class="age">${formatAge(patient.age)}</div>
                        <div class="mobile">${patient.mobile || ''}</div>
                        <div class="mr ellipsis">${this.formatMrInfo(patient)}</div>`;
            },
            formatMrInfo({
                shortName, clinicName, lastModified, nodeType,
            }) {
                const timeStr = lastModified ? parseTime(lastModified, 'y-m-d', true) : '';

                let clinicNameStr = '';
                if (nodeType === 0) {
                    clinicNameStr = '';
                } else if (nodeType === 1) {
                    clinicNameStr = '总部';
                } else {
                    clinicNameStr = shortName || clinicName;
                }
                return `${timeStr} ${clinicNameStr || ''}`;
            },
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();

                }
                this.sourceChildList = RecommendService.getInstance().cascaderOptions;
            },
        },
    };
</script>

<style lang="scss">
@import '../../../styles/theme';

.read-card-bind-patient-dialog {
    .abc-tabs {
        margin-bottom: 18px;
    }

    .search-patients {
        display: flex;

        .package-card {
            margin-left: 8px;
        }
    }

    .patient-footer {
        display: flex;
        justify-content: flex-end;
    }

    .medical-count {
        cursor: pointer;
    }

    .bind-patient-card {
        .card-box {
            right: 0;
            left: auto;
        }
    }

    .repeat-error-info {
        margin-top: 8px;

        .orange-text {
            line-height: 20px;
            color: #ff9933;
        }

        .err-detail-item {
            line-height: 20px;
            color: $T2;

            .label-text {
                display: inline-block;
                min-width: 42px;
                text-align: justify;
                text-align-last: justify;
            }
        }
    }
}

.suggestions-wrapper.patient-suggestion {
    top: 36px;

    .suggestion-title {
        display: flex;
        align-items: center;
        padding: 0 16px;

        .patient-info {
            width: 270px;
        }
    }

    .patient-suggestions.suggestions-item {
        z-index: 1009;
        display: flex;
        align-items: center;
        min-height: 36px;
        padding: 0 16px;
        overflow: hidden;
        white-space: nowrap;
        cursor: pointer;
        user-select: none;
        outline: 0;

        &[disabled] {
            color: #687481;
            cursor: not-allowed;
        }

        .name {
            flex: none;
            width: 80px;

            .vip::after {
                display: inline-block;
                width: 12px;
                height: 12px;
                margin-left: 4px;
                content: '';
                background: url('~@/assets/images/vip.png') no-repeat;
                background-size: 12px 12px;
            }
        }

        .mobile {
            width: 110px;
            padding-left: 10px;
        }

        .age,
        .sex {
            width: 40px;
            text-align: right;
        }

        .mr {
            flex: 1;
            color: $T2;
        }

        &.selected {
            .mr {
                color: #ffffff;
            }
        }
    }
}
</style>
