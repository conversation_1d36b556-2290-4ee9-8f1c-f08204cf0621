<template>
    <div id="patient-base-card-wrapper">
        <abc-form-item-group grid :grid-column-count="4" grid-gap="16px 24px">
            <abc-form-item label="姓名" required grid-column="span 2">
                <abc-input
                    v-model.trim="curPatient.name"
                    :max-length="20"
                    trim
                    type="text"
                    @enter="enterEvent"
                    @change="handleChange"
                ></abc-input>
            </abc-form-item>
            <abc-form-item label="性别" required grid-column="span 2">
                <abc-radio-group v-model="curPatient.sex" @enter="enterEvent">
                    <abc-radio-button label="男">
                        男
                    </abc-radio-button>
                    <abc-radio-button label="女">
                        女
                    </abc-radio-button>
                </abc-radio-group>
            </abc-form-item>
            <abc-form-item
                label="手机"
                :required="false"
                grid-column="span 2"
                :validate-event="handleMobileValidate"
            >
                <abc-popover ref="errorPopover" trigger="hover">
                    <abc-input
                        slot="reference"
                        v-model="curPatient.mobile"
                        type="phone"
                        @enter="enterEvent"
                        @change="handleChange"
                    ></abc-input>
                    <div v-if="errorData && errorData.code === 13992" class="error-patient-list">
                        <div v-for="item in errorPatientList" :key="item.id" class="item-wrapper">
                            <div class="patient-item">
                                <div class="base-item">
                                    <span style="width: 60px;">{{ item.name }}</span>
                                    <span style="width: 20px;">{{ item.sex }}</span>
                                    <span style="width: 40px;">{{ formatAge(item.age) }}</span>
                                    <span class="number-text" style="width: 100px;">{{ item.mobile }}</span>
                                    <span class="number-text" style="width: 120px;">{{ item.cardNo }}512199505224089</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </abc-popover>
            </abc-form-item>
            <abc-form-item label="年龄" grid-column="span 2">
                <abc-space is-compact compact-block :border-style="false">
                    <!--没有月和天，就需要年-->
                    <abc-form-item
                        label=""
                        show-red-dot
                        :required="!curPatient.age.month && !curPatient.age.day"
                        class="patient-age-year"
                    >
                        <abc-input
                            v-model.number="curPatient.age.year"
                            v-abc-focus-selected
                            :width="68"
                            :input-custom-style="{
                                padding: '3px 6px', 'text-align': 'center'
                            }"
                            type="number"
                            :config="{
                                supportZero: true, max: 199
                            }"
                            @enter="enterEvent"
                            @change="changeAge"
                        >
                            <span slot="appendInner">岁</span>
                        </abc-input>
                    </abc-form-item>
                    <!--没有年和天，就需要月-->
                    <abc-form-item
                        class="patient-age-month"
                        :required="!curPatient.age.day && !curPatient.age.year"
                        :validate-event="validateMonth"
                    >
                        <abc-input
                            v-model.number="curPatient.age.month"
                            v-abc-focus-selected
                            :width="63"
                            :input-custom-style="{ 'text-align': 'center' }"
                            type="number"
                            :config="{ supportZero: true }"
                            @enter="enterEvent"
                            @change="changeAge"
                        >
                            <span slot="appendInner">月</span>
                        </abc-input>
                    </abc-form-item>
                    <!--没有月和年，就需要天-->
                    <abc-form-item
                        class="patient-age-day"
                        :required="!curPatient.age.month && !curPatient.age.year"
                        :validate-event="validateDay"
                    >
                        <abc-input
                            v-model.number="curPatient.age.day"
                            v-abc-focus-selected
                            :width="64"
                            :input-custom-style="{ 'text-align': 'center' }"
                            type="number"
                            :config="{ supportZero: true }"
                            @enter="enterEvent"
                            @change="changeAge"
                        >
                            <span slot="appendInner">天</span>
                        </abc-input>
                    </abc-form-item>
                </abc-space>
            </abc-form-item>
            <abc-form-item v-abc-focus-selected label="生日" grid-column="span 2">
                <div @keydown.enter="handleEnter">
                    <birthday-picker
                        v-model="curPatient.birthday"
                        @change="changeBirthday"
                    >
                    </birthday-picker>
                </div>
            </abc-form-item>
            <abc-form-item
                label="身份证"
                grid-column="span 2"
                :required="false"
                :validate-event="handleCardIdValidate"
            >
                <abc-popover>
                    <abc-input
                        slot="reference"
                        v-model="curPatient.idCard"
                        type="text"
                        :max-length="18"
                        :disabled="!!(cardInfo && cardInfo.idCard)"
                        @enter="enterEvent"
                        @change="handleChange"
                    ></abc-input>
                    <div v-if="errorData && errorData.code === 13993" class="error-patient-list">
                        <div v-for="item in errorPatientList" :key="item.id" class="item-wrapper">
                            <div class="patient-item">
                                <div class="base-item">
                                    <span style="width: 60px;">{{ item.name }}</span>
                                    <span style="width: 20px;">{{ item.sex }}</span>
                                    <span style="width: 40px;">{{ formatAge(item.age) }}</span>
                                    <span class="number-text" style="width: 100px;">{{ item.mobile }}</span>
                                    <span class="number-text" style="width: 120px;">{{ item.cardNo }}512199505224089</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </abc-popover>
            </abc-form-item>
            <abc-form-item v-if="showTag" label="标签" grid-column="span 4">
                <abc-tag-group class="text" style="padding-top: 6px;">
                    <abc-tag-v2
                        v-for="tag in curPatient.tags"
                        :key="tag.tagId"
                        :closable="tag.editPermit === 1"
                        size="mini"
                        theme="success"
                        shape="round"
                        @close="onClickHandleTag(tag)"
                    >
                        {{ tag.tagName }}
                    </abc-tag-v2>
                    <abc-popover
                        ref="elpop"
                        placement="bottom-start"
                        trigger="manual"
                        :value="showTagPopover"
                        :visible-arrow="false"
                        popper-class="add-label-pop"
                    >
                        <abc-tags-add
                            slot="reference"
                            @click.native="showTagPopover = !showTagPopover"
                        ></abc-tags-add>
                        <div v-abc-click-outside="handleTagPopoverClose" class="box">
                            <view-labels
                                :show-manage="false"
                                :selected-ids="selectedTagIds"
                                :filter="false"
                                @change="onClickHandleTag"
                            ></view-labels>
                        </div>
                    </abc-popover>
                </abc-tag-group>
            </abc-form-item>
        </abc-form-item-group>
        <abc-flex
            v-if="showSplitLine"
            style="width: 100%;"
            align="center"
            justify="space-between"
        >
            <div style="width: 170px;">
                <abc-divider variant="dashed" margin="normal"></abc-divider>
            </div>
            <abc-button variant="text" @click="handleChangeExpand">
                {{ isExpanded ? '收起' : '展开' }}
            </abc-button>
            <div style="width: 170px;">
                <abc-divider variant="dashed" margin="normal"></abc-divider>
            </div>
        </abc-flex>
        <abc-form-item-group
            v-show="isExpanded"
            grid
            :grid-column-count="4"
            grid-gap="16px 24px"
        >
            <abc-form-item label="地址" grid-column="span 4">
                <abc-address-selector
                    v-model="curPatient.address"
                    clearable
                    @input="handleChange"
                    @enter="enterEvent"
                >
                    <div slot="extend" class="address-info" @click="handleAddressChange">
                        {{ clinicAddressStr }}
                        <span class="address-tag">（常用）</span>
                    </div>
                </abc-address-selector>
            </abc-form-item>
            <abc-form-item grid-column="span 4" style="margin-top: -8px;">
                <abc-input
                    v-model.trim="curPatient.addressDetail"
                    placeholder="详细地址"
                    trim
                    type="text"
                    @enter="enterEvent"
                    @change="handleChange"
                ></abc-input>
            </abc-form-item>
            <abc-form-item v-if="isRecommendAndSourceDisplay" grid-column="span 2" label="来源">
                <abc-cascader
                    v-model="cascaderValue"
                    :props="{
                        children: 'children',
                        label: 'name',
                        value: 'id'
                    }"
                    placeholder="不指定"
                    separation="-"
                    :options="sourceList"
                    @enter="enterEvent"
                    @change="handleSourceChange"
                >
                </abc-cascader>
            </abc-form-item>
            <abc-form-item label="职业" grid-column="span 2">
                <abc-select
                    v-model="curPatient.profession"
                    placeholder="请选择患者职业"
                    custom-class="profession-options"
                    @change="handleChange"
                    @enter="enterEvent"
                >
                    <abc-option
                        v-for="item in _professionOptions"
                        :key="item.value"
                        :value="item.value"
                        :label="item.label"
                    ></abc-option>
                </abc-select>
            </abc-form-item>
            <abc-form-item label="工作单位" grid-column="span 2">
                <abc-input
                    v-model="curPatient.company"
                    :max-length="128"
                    @enter="enterEvent"
                    @change="handleChange"
                ></abc-input>
            </abc-form-item>
            <abc-form-item label="档案号" grid-column="span 2">
                <abc-input
                    v-model="curPatient.sn"
                    :max-length="16"
                    type="number-en-char"
                    :ignore-composing="true"
                    @enter="enterEvent"
                    @change="handleChange"
                ></abc-input>
            </abc-form-item>
            <abc-form-item label="备注" :grid-column="isRecommendAndSourceDisplay ? 'span 4' : 'span 2'">
                <abc-input
                    v-model="curPatient.remark"
                    :max-length="300"
                    @change="handleChange"
                ></abc-input>
            </abc-form-item>
        </abc-form-item-group>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';

    import { RecommendService } from '@/service/recommend.js';

    import BirthdayPicker from 'views/layout/birthday-picker/birthday-picker.vue';
    import ViewLabels from 'views/crm/common/package-label/view-labels.vue';

    import clone from 'utils/clone';
    import { professionOptions } from 'views/crm/data/options.js';
    import {
        age2birthday, birthday2age, formatAge,
    } from '@/utils';
    import {
        validateMobile, validateIdCard,
    } from 'utils/validate.js';
    import LocalSotrage from 'utils/localStorage-handler';

    const EDIT_HABIT_KEY = '_edit_patient_card_user_habit';
    import AbcTagsAdd from 'views/crm/component/abc-tags-add';
    export default {
        name: 'PatientBaseCard',
        components: {
            BirthdayPicker,
            ViewLabels,
            AbcTagsAdd,
        },
        props: {
            patient: {
                type: Object,
                required: true,
            },
            // 来源数据
            sourceList: {
                type: Array,
                required: true,
            },
            inputWidth: {
                type: Number,
                default: 238,
            },
            showTag: {
                type: Boolean,
                default: false,
            },
            showSplitLine: {
                type: Boolean,
                default: false,
            },
            cardInfo: {
                type: Object,
                default: () => {
                    return null;
                },
            },
            errorData: {
                validator: (prop) => typeof prop === 'object' || prop === null,
            },
            clinicAddress: {
                validator: (prop) => typeof prop === 'object' || prop === null,
            },
        },
        data() {
            return {
                curPatient: {
                    name: '',
                    sex: '男',
                    mobile: '',
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    birthday: '',
                    idCard: '',
                    sourceId: '',
                    sourceFromm: '',
                    address: {
                        addressCityId: '',
                        addressCityName: '',
                        addressDetail: '',
                        addressDistrictId: '',
                        addressDistrictName: '',
                        addressProvinceId: '',
                        addressProvinceName: '',
                    },
                    profession: '',
                    company: '',
                    sn: '',
                    remark: '',
                    patientSource: {},
                    tags: [],
                },
                cascaderValue: [],

                showTagPopover: false,
                isExpanded: false, // 是否展开
            };
        },
        watch: {
            patient: {
                handler() {
                    Object.assign(this.curPatient, clone(this.patient));
                    this.cascaderValue = this.initCascaderData();
                },
                deep: true,
                immediate: true,
            },
        },
        computed: {
            ...mapGetters(['currentClinic','clinicBasic']),
            // 是否展示推荐和来源
            isRecommendAndSourceDisplay() {
                return this.clinicBasic.isRecommendAndSourceDisplay;
            },
            // 选择患者标签id
            selectedTagIds() {
                const tags = this.curPatient.tags || [];
                return tags.map((item) => item.tagId);
            },
            errorPatientList() {
                return this.errorData && this.errorData.rows || [];
            },
            clinicAddressStr() {
                let str = '';
                const {
                    addressCityName, addressDistrictName, addressProvinceName,
                } = this.clinicAddress;
                const areaList = [addressProvinceName, addressCityName, addressDistrictName].filter((it) => !!it);
                if (areaList.length) {
                    str += areaList.join('/');
                }
                return str;
            },
        },
        created() {
            this._professionOptions = professionOptions;
            if (this.patient.birthday) {
                this.changeBirthday(this.patient.birthday);
            }
            const userHabit = LocalSotrage.get(EDIT_HABIT_KEY, true);
            if (userHabit) {
                this.isExpanded = userHabit.isExpanded;
            }
        },
        methods: {
            formatAge,
            initCascaderData() {
                if (!this.curPatient.patientSource) return [];

                const {
                    id, sourceFromName, sourceFrom, name,
                } = this.curPatient.patientSource;

                return RecommendService.getInstance().initCascaderValue({
                    visitSourceId: id,
                    visitSourceName: name,
                    visitSourceFrom: sourceFrom,
                    visitSourceFromName: sourceFromName,
                });
            },
            validateMobile,
            validateIdCard,
            handleMobileValidate(val, callback) {
                if (this.errorData && this.errorData.code === 13992) {
                    callback({
                        validate: false,
                        message: '当前患者及手机号已存在',
                    });
                } else {
                    validateMobile(val, callback);
                }
            },
            handleCardIdValidate(val, callback) {
                if (this.errorData && this.errorData.code === 13993) {
                    callback({
                        validate: false,
                        message: '当前身份证号已存在',
                    });
                } else {
                    validateIdCard(val, callback);
                }
            },
            /**
             * @desc 年龄月份
             * <AUTHOR>
             * @date 2021-09-18 17:32:14
             * @params
             * @return
             */
            validateMonth(value, callback) {
                if (!this.curPatient.age.month) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-1])$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            /**
             * @desc 校验患者年龄天数
             * <AUTHOR>
             * @date 2021-09-18 17:31:44
             */
            validateDay(value, callback) {
                if (!this.curPatient.age.day) {
                    callback({ validate: true });
                    return;
                }
                const pattern = /^(0?[1-9]|1[0-9]|2[0-9]|30)$/;
                if (!pattern.test(value)) {
                    callback({
                        validate: false,
                        message: '最多30天',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            /**
             * @desc 生日反推年龄
             * <AUTHOR>
             * @date 2021-09-18 17:32:41
             * @params
             * @return
             */
            changeBirthday(birthday) {
                if (birthday) {
                    const {
                        year, month, day,
                    } = birthday2age(birthday);
                    this.curPatient.age.year = year || '';
                    this.curPatient.age.month = month || '';
                    if (!year && !month) {
                        this.curPatient.age.day = day || 1;
                    } else {
                        this.curPatient.age.day = day || '';
                    }
                } else {
                    // 清空操作
                    this.curPatient.age.year = '';
                    this.curPatient.age.month = '';
                    this.curPatient.age.day = '';
                }
                this.handleChange();
            },
            /**
             * @desc 年龄计算生日
             * <AUTHOR>
             * @date 2021-09-18 17:33:12
             */
            changeAge() {
                this.curPatient.birthday = age2birthday(this.curPatient.age);
                this.handleChange();
            },
            onClickHandleTag(tag) {
                const id = tag.id || tag.tagId;
                const name = tag.name || tag.tagName;
                if (this.selectedTagIds.includes(id)) {
                    this.curPatient.tags = this.curPatient.tags.filter((item) => item.tagId !== id);
                } else {
                    this.curPatient.tags.push({
                        tagId: id, tagName: name, editPermit: 1,
                    });
                }
            },
            handleTagPopoverClose() {
                if (this.showTagPopover) {
                    this.showTagPopover = false;
                }
            },
            handleEnter(e) {
                e.preventDefault();
                e.stopPropagation();
                this.enterEvent(e);
            },
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                this.$nextTick(() => {
                    let targetIndex = -1;
                    const inputs = $('#patient-base-card-wrapper .abc-input__inner').not(':disabled,.is-disabled');
                    for (let i = 0; i < inputs.length; i++) {
                        if ($(inputs[i]).hasClass('tagname')) {
                            targetIndex = i;
                        }
                    }
                    if (inputs.index(e.target) > -1) {
                        targetIndex = inputs.index(e.target);
                    }
                    let nextInput = inputs[targetIndex + 1];
                    if (nextInput.tabIndex === -1) {
                        nextInput = inputs[targetIndex + 2];
                    }
                    nextInput &&
                        this.$nextTick(() => {
                            nextInput.focus();
                        });
                });
            },
            /**
             * @desc 当档案号输入时，允许输入数字（可0开头）By QT
             * <AUTHOR>
             * @date 2021-09-18 17:33:35
             */
            onInputSn() {
                if (this.curPatient) {
                    this.$nextTick(() => {
                        this.curPatient.sn = this.curPatient.sn.replace(/\D/g, '');
                    });
                }
            },
            handleSourceChange(val) {
                if (val.length) {
                    if (val.length > 2) {
                        this.curPatient.sourceId = val[val.length - 2].value;
                        this.curPatient.sourceFrom = val[val.length - 1] ? val[val.length - 1].value : null;
                    } else if (['顾客推荐', '员工推荐', '医生推荐','转诊医生'].includes(val[0].label)) {
                        this.curPatient.sourceId = val[0].value;
                        this.curPatient.sourceFrom = val[1] ? val[1].value : null;
                    } else {
                        this.curPatient.sourceId = val[val.length - 1].value;
                        this.curPatient.sourceFrom = null;
                    }
                } else {
                    this.curPatient.sourceId = null;
                    this.curPatient.sourceFrom = null;
                }
                this.curPatient.patientSource = {
                    id: this.curPatient.sourceId,
                    sourceFrom: this.curPatient.sourceFrom,
                    sourceFromName: val && val.length > 1 && val[1].label,
                    name: val && val.length && val[0].label,
                };
                this.handleChange();
            },
            handleChange() {
                this.$emit('change', this.curPatient);
            },
            // 默认带入诊所地址
            handleAddressChange() {
                Object.assign(this.curPatient.address, this.clinicAddress);
            },
            /**
             * @desc 点击展开或者收起，并保存用户习惯
             * <AUTHOR>
             * @date 2021-10-13 11:06:15
             * key: _edit_patient_card_user_habit
             */
            handleChangeExpand() {
                this.isExpanded = !this.isExpanded;
                LocalSotrage.set(EDIT_HABIT_KEY, {
                    isExpanded: this.isExpanded,
                });
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

#patient-base-card-wrapper {
    display: block;
}

.error-patient-list {
    margin-top: 2px;
    background-color: #ffffff;
    border: 1px solid $P6;
    border-radius: var(--abc-border-radius-small);

    .item-wrapper {
        padding: 12px 12px 0 12px;

        &:hover {
            background-color: #eff3f6;
        }

        &:last-child.patient-item {
            border-bottom: none !important;
        }
    }

    .patient-item {
        align-items: flex-start;
        width: 100%;
        height: auto;
        padding-bottom: 12px;
        margin-left: 0;
        border-bottom: 1px dashed $P6;

        .base-item {
            display: flex;
            line-height: 20px;

            > span {
                font-weight: 500;
                color: $T1;
            }

            .number-text {
                color: $T2;
            }
        }

        .abc-radio-input {
            padding-top: 2px;
        }

        .abc-button-text {
            height: 20px;
            margin-left: auto;
            line-height: 20px;
        }

        .expand-item {
            margin-top: 4px;
            line-height: 20px;
            color: $T2;
        }
    }
}

.add-label-pop {
    padding: 0;
    background-color: #ffffff;
    border: 1px solid $P7;
    border-radius: $borderRadiusMini;
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.06);
    transform: translateY(-6px);

    .box {
        width: 285px;
        max-height: 306px;
        overflow: auto;

        .alert {
            @include flex(row, center, center);

            height: 46px;
            margin: 12px 12px 0 12px;
            background-color: #f0f7ff;
            border: 1px solid #e0efff;

            .iconfont {
                margin-right: 8px;
            }
        }
    }
}

.address-selector-extend {
    padding: 0 20px;

    .address-info {
        padding: 6px 0;
        line-height: 20px;
        cursor: pointer;
        border-bottom: 1px dashed $P6;

        .address-tag {
            color: $T2;
        }
    }
}
</style>
