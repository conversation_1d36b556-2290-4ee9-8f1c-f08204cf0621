<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :auto-focus="false"
        title="医保上报"
        content-styles="width: 960px;padding: 24px;min-height: 660px"
        custom-class="social-report__settle-list-dialog"
        append-to-body
    >
        <div class="tools-wrapper">
            <abc-date-picker
                v-model="toolParams.dateRange"
                type="daterange"
                placeholder="选择时间"
                :clearable="false"
                @change="handleDateChange"
            ></abc-date-picker>
            <abc-autocomplete
                v-model.trim="toolParams.patientName"
                custom-class="social-report__search-patient-suggestion"
                placeholder="姓名"
                :width="130"
                :inner-width="300"
                :async-fetch="true"
                clearable
                :fetch-suggestions="fetchPatientData"
                @enterEvent="onClickSelect"
                @clear="onClickClear"
            >
                <template slot="prepend">
                    <abc-search-icon></abc-search-icon>
                </template>
                <template slot="suggestion-header">
                    <div class="suggestion-title">
                        <div class="patient-info">
                            {{ $national.constants.patientWording }}信息
                        </div>
                    </div>
                </template>
                <template slot="suggestions" slot-scope="props">
                    <dt
                        class="suggestions-item"
                        :class="{ selected: props.index == props.currentIndex }"
                        @click="onClickSelect(props.suggestion)"
                    >
                        <div class="name ellipsis">
                            <span>{{ props.suggestion.patientName }}</span>
                        </div>
                        <div class="idCardNo">
                            {{ props.suggestion.idCardNo }}
                        </div>
                    </dt>
                </template>
            </abc-autocomplete>
            <abc-cascader
                v-model="toolParams.medTypeSelected"
                :props="{
                    value: 'value', label: 'name'
                }"
                :options="medicalCategoryOptions"
                :width="95"
                placeholder="就诊类型"
                multiple
                mutually-exclusive
                @change="handleMedicalCategoryChange"
            ></abc-cascader>
            <abc-cascader
                v-model="toolParams.statusSelected"
                :options="$national.options.orderStatusOptions"
                :width="95"
                placeholder="交易类型"
                multiple
                mutually-exclusive
                @change="handleStatusChange"
            ></abc-cascader>
        </div>
        <abc-table
            :loading="loading"
            class="social-report__settle-list-dialog__table"
            :render-config="tableConfig"
            :pagination="tablePagination"
            :data-list="settleDataList"
            :clickable="true"
            @pageChange="handlePageChange"
        >
            <abc-flex slot="topHeader">
                <biz-mixed-selection-filter
                    v-model="warningKey"
                    type="radio"
                    :gap="16"
                    :options="warningList"
                    @change="handleChangeWarningType"
                ></biz-mixed-selection-filter>
            </abc-flex>
            <template #chargedTime="{ trData }">
                <abc-table-cell>
                    {{ $national.tools.getDatetimeFormat(trData.chargedTime) }}
                </abc-table-cell>
            </template>
            <template #patientName="{ trData }">
                <abc-table-cell>
                    {{ trData.patientName || trData.cardOwner }}
                </abc-table-cell>
            </template>
        </abc-table>
    </abc-dialog>
</template>
<script>
    import { mapGetters } from 'vuex';
    import BizMixedSelectionFilter from '@/components-composite/biz-mixed-selection-filter';
    import { isEqual } from '@abc/utils';
    import { debounce } from 'utils/lodash.js';
    import {
        parseTime,
    } from '@abc/utils-date';
    export default {
        name: 'SettleListDialog',
        components: {
            BizMixedSelectionFilter,
        },
        props: {
            // {psnName: string, idCardNo: string}
            queryInfo: {
                type: Object,
            },
        },
        data() {
            return {
                fetchPatientData: null,
                loading: false,
                warningKey: '',
                showDialog: true,
                toolParams: {
                    dateRange: [parseTime(new Date(), 'y-m-d', true), parseTime(new Date(), 'y-m-d', true)],
                    medTypeSelected: [],//就诊类型
                    statusSelected: [],//交易状态
                    icpcIsUpload: '',// ICPC状态
                    medRecordIsUpload: '',// 诊疗记录状态
                    patientName: '',
                },
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                // 后端返回的查询参数列表
                searchParamsDataList: {
                    icpcNotUploadCount: 0,
                    medRecordNotUploadCount: 0,
                    medTypes: [],
                    clrOptinses: [],
                },
                settleData: null,
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'userInfo',
            ]),
            tablePagination() {
                return {
                    showTotalPage: true,
                    pageIndex: this.pageParams.pageIndex,
                    pageSize: this.pageParams.pageSize,
                    count: this.total,
                };
            },
            // 结算数据总条数
            total() {
                const { totalCount } = this.settleData || {};
                return totalCount || 0;
            },
            // 医疗类别
            medicalCategoryOptions() {
                let medicalCategoryOptions = this.$national.options.outpatientMedicalCategoryOptions;
                if (this.$national.vuexGetters('isPharmacy')) {
                    medicalCategoryOptions = this.$national.options.pharmacyMedicalCategoryOptions;
                }
                if (this.isAbcHospital) {
                    medicalCategoryOptions = [
                        ...this.$national.options.outpatientMedicalCategoryOptions,
                        ...this.$national.options.hospitalMedicalCategoryOptions,
                    ];
                }
                if (this.$national.config.isNational) {
                    return (this.searchParamsDataList?.medTypes || []).map((item) => {
                        const target = [
                            ...this.$national.options.medicalCategoryOptions,
                            ...medicalCategoryOptions,
                        ].find((option) => option.value === item);
                        return {
                            value: item,
                            name: target?.name || item,
                        };
                    });
                }
                return medicalCategoryOptions;
            },
            isOpenUploadICPCList() {
                return this.$national.vuexGetters('isOpenUploadICPCList');
            },
            isOpenUploadOutpatientRecord() {
                return this.$national.vuexGetters('isOpenUploadOutpatientRecord');
            },
            // 警告类型
            warningList() {
                const warningList = [];
                if (this.isOpenUploadICPCList) {
                    warningList.push({
                        label: 'ICPC未上报',
                        value: 'icpc',
                        statisticsNumber: this.settleData?.icpcNotUploadCount,
                    });
                }
                if (this.isOpenUploadOutpatientRecord) {
                    warningList.push({
                        label: '诊疗记录未上报',
                        value: 'outpatientRecord',
                        statisticsNumber: this.settleData?.medRecordNotUploadCount,
                    });
                }
                return warningList;
            },
            //表头
            tableConfig() {
                const list = [
                    {
                        key: 'chargedTimeWord',
                        label: '结算时间',
                        style: {
                            width: '134px',
                        },
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {this.$national.tools.getDatetimeFormat16(row.chargedTime)}
                                </abc-table-cell>
                            );
                        },
                    },
                    {
                        key: 'patientNameWord',
                        label: this.$national.constants.patientWording,
                        style: {
                            width: '84px',
                        },
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {row.patientName || row.cardOwner}
                                </abc-table-cell>
                            );
                        },
                    },
                    {
                        key: 'medTypeName',
                        label: '就诊类型',
                        style: {
                            width: '78px',
                        },
                        customRender: (h, row) => {
                            return (
                            <abc-table-cell>
                                {this.$national.tools.showTargetLabel(this.medicalCategoryOptions, row.medType, row.medTypeName)}
                            </abc-table-cell>
                            );
                        },
                    },
                    {
                        key: 'payFee',
                        label: '结算金额',
                        style: {
                            width: '94px',
                            textAlign: 'right',
                        },
                        type: 'money',
                        defaultValue: '0.00',
                        align: 'right',
                        thStyle: {
                            paddingRight: '35px',
                        },
                        tdStyle: {
                            paddingRight: '35px',
                        },
                        customRender: (h, row) => {
                            const {
                                SUCC_REFUND, REFU_CONFIRM,
                            } = this.$national.constants.orderStatusConst;
                            return (
                            <abc-table-cell>
                                <span style={(row.status === SUCC_REFUND || row.status === REFU_CONFIRM) && {
                                    'color': '#96A4B3',
                                    'text-decoration': 'line-through',
                                }}>
                                    {row.payFee}
                                </span>
                            </abc-table-cell>
                            );
                        },
                    },
                    {
                        key: 'doctorName',
                        label: '医生',
                        style: {
                            width: '94px',
                        },
                        show: !this.$national.vuexGetters('isAbcPharmacy'),
                    },
                    {
                        key: 'statusName',
                        label: '收费状态',
                        style: {
                            width: '85px',
                            flex: 'none',
                        },
                        tdStyle: {
                            paddingLeft: '8px',
                        },
                        customRender: (h, row) => {
                            return (
                            <abc-table-cell class="row-refund-status">
                                <span>{ row.statusName.slice(0,2) }</span>
                                { row.isRefunded === 1 && row.status === 2 ?
                                <abc-popover
                                    width="248px"
                                    placement="bottom-start"
                                    trigger="hover"
                                    theme="yellow"
                                    visibleArrow={false}
                                    resident-poppover
                                    popperClass="national-base__account__settle-refund-detail-popper"
                                >
                                    <abc-tag slot="reference" size="mini" class="abc-tag-refund">已退</abc-tag>
                                    <div class="popper-refund-info">
                                        <ul>
                                            <li>{ this.$national.constants.patientWording }：{ row.refundInfo?.patientName }</li>
                                            <li>操作员：{row.refundInfo?.chargedByName}</li>
                                        </ul>
                                        <ul>
                                            <li>{ this.$national.tools.getDatetimeFormat(row.refundInfo?.chargedTime) }</li>
                                            <li>退</li>
                                            <li>￥{ row.refundInfo?.payFee }</li>
                                        </ul>
                                        <ul>
                                            <li>ID：{ row.refundInfo?.transactionId || '-' }</li>
                                        </ul>
                                    </div>
                                </abc-popover> : '' }
                                {(row.status === 3 || row.status === 7) ? (
                                    this.$national.config.isShowSettleAbnormalTips ?
                                        <abc-popover
                                            width="248px"
                                            placement="top-end"
                                            trigger="hover"
                                            theme="yellow"
                                            visibleArrow={false}
                                            resident-poppover
                                            popperClass="national-base__account__settle-refund-detail-popper"
                                        >
                                            <abc-tag slot="reference" size="mini" theme="status" type="warning"
                                            >异常
                                            </abc-tag>
                                            <div class="popper-refund-info">
                                                {row.status === 3 ?
                                                    '医保结算后入账失败，请进入「医保」一一「医保账目」一一「对账」模块中处理异常结算，请注意，收费异常冲正后费用将原路退回参保人账户' :
                                                    '医保退费后入账失败，请进入联系ABC一对一客服处理异常'}
                                            </div>
                                        </abc-popover> :
                                        <abc-tag size="mini" theme="status" type="warning">异常</abc-tag>
                                ) : ''}
                            </abc-table-cell>
                            );
                        },
                    },
                ];
                if (this.isOpenUploadICPCList) {
                    list.push({
                        key: 'icpc',
                        label: 'ICPC',
                        style: {
                            textAlign: 'center',
                        },
                        children: [
                            {
                                key: 'icpcUploadTime',
                                label: '状态',
                                style: {
                                    width: '85px',
                                    flex: 'none',
                                    textAlign: 'center',
                                },
                                customRender: (h, row) => {
                                    if (row.medType !== '990502' && !row.icpcUploadTime && row.isRefunded !== 1) {
                                        return <abc-table-cell style={{ color: '#FF9933' }}>待上传</abc-table-cell>;
                                    } if (row.icpcUploadTime) {
                                        return <abc-table-cell>已上传</abc-table-cell>;
                                    }
                                    return <abc-table-cell>无需上传</abc-table-cell>;
                                },
                            },
                            {
                                key: 'icpcAction',
                                label: '操作',
                                style: {
                                    width: '85px',
                                    flex: 'none',
                                    textAlign: 'center',
                                },
                                customRender: (h, row) => {
                                    const needUploadICPC = !!this.isOpenUploadICPCList && row.medType !== '990502' && row.isRefunded !== 1;
                                    const canReUpload = needUploadICPC && row.icpcUploadTime;
                                    return (
                                        <abc-table-cell>
                                            <abc-button
                                                disabled={!needUploadICPC}
                                                type="text"
                                                onClick={() => this.handleClickUpload(row, { needUploadICPC: true })}
                                            >
                                                {canReUpload ? '重传' : '上传'}
                                            </abc-button>
                                        </abc-table-cell>
                                    );
                                },
                            },
                        ],
                    });
                }
                if (this.isOpenUploadOutpatientRecord) {
                    list.push({
                        key: 'outpatientRecord',
                        label: '诊疗记录',
                        style: {
                            textAlign: 'center',
                        },
                        children: [
                            {
                                key: 'outpatientRecordUplaodStatus',
                                label: '状态',
                                style: {
                                    width: '85px',
                                    flex: 'none',
                                    textAlign: 'center',
                                },
                                customRender: (h, row) => {
                                    const isNeedUpload = this.checkedOutpatientRecordUplaodStatus(row);
                                    if (isNeedUpload) {
                                        return <abc-table-cell style={!row.medRecordUploadTime && { color: '#FF9933' }}>{ row.medRecordUploadTime ? '已上传' : '待上传' }</abc-table-cell>;
                                    }
                                    return <abc-table-cell>无需上传</abc-table-cell>;
                                },
                            },
                            {
                                key: 'outpatientRecordAction',
                                label: '操作',
                                style: {
                                    width: '85px',
                                    flex: 'none',
                                    textAlign: 'center',
                                },
                                customRender: (h, row) => {
                                    const needUploadOutpatientRecord = !!this.isOpenUploadOutpatientRecord && this.checkedOutpatientRecordUplaodStatus(row) && !row.medRecordUploadTime;
                                    return (
                                        <abc-table-cell>
                                            <abc-button
                                                disabled={!needUploadOutpatientRecord}
                                                type="text"
                                                onClick={() => this.handleClickUpload(row, { needUploadOutpatientRecord: true })}
                                            >
                                                上传
                                            </abc-button>
                                        </abc-table-cell>
                                    );
                                },
                            },
                        ],
                    });
                }
                const config = {
                    hasInnerBorder: true,
                    list,
                };

                return config;
            },
            // 展示数据
            settleDataList() {
                const { result } = this.settleData || {};
                return result || [];
            },
        },
        created() {
            this.fetchPatientData = debounce(this.fetchData, 800, true);
        },
        mounted() {
            if (this.queryInfo) {
                this.toolParams.patientName = this.queryInfo.psnName;
                this.toolParams.idCardNo = this.queryInfo.idCardNo;
            }
            this.fetchSettleDataList();
            this.fetchSettleFetchParams();
        },
        methods: {
            /**
             * 关键词搜索
             * <AUTHOR>
             * @date 2022-05-12
             * @param {String} patientName 关键词
             * @param {Function} callback 回调函数
             */
            async fetchData(patientName, callback) {
                if (!patientName) {
                    return;
                }
                const params = {
                    psnName: patientName,
                };
                const response = await this.$national.api.searchPatientIdCardByName(params);
                if (response.status === false) {
                    return;
                }
                const dataList = (response.data || []).map((item) => ({
                    patientName: item.psnName,
                    idCardNo: item.idCardNo,
                }));
                callback(dataList);
            },
            /**
             * 当选择搜索项时
             * <AUTHOR>
             * @date 2022-05-12
             * @param {Object} data 选中项
             */
            onClickSelect(data) {
                this.toolParams.patientName = data?.patientName || '';
                this.toolParams.idCardNo = data?.idCardNo || '';
                this.initFetch();
            },
            /**
             * @desc 清除名字触发
             * <AUTHOR>
             * @date 2022-07-26
             */
            onClickClear() {
                this.toolParams.patientName = '';
                this.toolParams.idCardNo = '';
                this.initFetch();
            },
            close() {
                this.showDialog = false;
            },
            /**
             * @desc 处理数组数据
             * <AUTHOR>
             * @date 2022-06-23
             * @return {String}
             * @param {Array} arr
             */
            handleArrayParams(arr) {
                return (arr[0] || []).map((item) => item.value).join(',');
            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2022-05-12
             * @returns {Object}
             */
            createParams() {
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;
                const {
                    dateRange,
                    medTypeSelected,
                    statusSelected,
                    icpcIsUpload,
                    medRecordIsUpload,
                    patientName,
                    idCardNo,
                } = this.toolParams;
                const params = {
                    offset: pageIndex * pageSize,
                    limit: pageSize,
                    beginDate: dateRange[0],
                    endDate: dateRange[1],
                    medType: this.handleArrayParams(medTypeSelected),
                    status: this.handleArrayParams(statusSelected),
                    icpcIsUpload,
                    medRecordIsUpload,
                    patientName,
                    idCardNo,
                };
                // TODO: 2025-03-17
                // 如果当前是管理员，不传 doctorName，否则传当前用户ID
                const {
                    isAdmin,
                    name,
                } = this.userInfo || {};
                if (!isAdmin) {
                    params.doctorName = name || '';
                }
                return params;
            },
            /**
             * @desc 查询参数列表
             * <AUTHOR>
             * @date 2024-07-10
             */
            async fetchSettleFetchParams() {
                const params = this.createParams();
                const fetchResponse = await this.$national.api.fetchSettleFetchParams({
                    beginDate: params.beginDate,
                    endDate: params.endDate,
                });
                this.searchParamsDataList = fetchResponse.data || {};
            },
            async fetchSettleDataList() {
                this.loading = true;
                const params = this.createParams();
                const fetchResponse = await this.$national.api.fetchSettleDataList(params);
                if (fetchResponse.status === true && isEqual(params, this.createParams())) {
                    if (
                        this.$national.isSocialCom === false &&
                        this.$national.tools.decodeByAES
                    ) {
                        // 非医保电脑，后端下发的是加密后的taskId
                        const result = fetchResponse.data?.result || [];
                        result.forEach((item) => {
                            item.id = this.$national.tools.decodeByAES(`${this.$national.tools.getMonthFormat8()}_${item.idCardNo}`, item.id);
                        });
                    }
                    this.settleData = fetchResponse.data;
                }
                this.loading = false;
            },
            /**
             * 初始化入参，然后拉取数据
             * <AUTHOR>
             * @date 2020-07-10
             */
            initFetch() {
                this.pageParams.pageIndex = 0;
                this.fetchSettleDataList();
            },
            /**
             * 当日期改变时
             */
            handleDateChange() {
                console.log('handleDateChange', this.toolParams.dateRange);
                this.initFetch();
                this.fetchSettleFetchParams();
            },
            /**
             * @desc 结算状态改变时触发
             * <AUTHOR>
             * @date 2022-06-01 17:33:49
             */
            handleStatusChange() {
                this.initFetch();
            },
            /**
             * @desc 就诊类别改变时触发
             * <AUTHOR>
             * @date 2022-06-01 17:34:12
             */
            handleMedicalCategoryChange() {
                this.initFetch();
            },
            /**
             * 当改变分页时
             * <AUTHOR>
             * @date 2022-05-12
             * @param {Number} pageIndex 当前页面
             */
            handlePageChange(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchSettleDataList();
            },
            handleChangeWarningType(value) {
                this.toolParams.icpcIsUpload = '';
                this.toolParams.medRecordIsUpload = '';
                switch (value) {
                    case 'icpc':
                        this.toolParams.icpcIsUpload = '0';
                        break;
                    case 'outpatientRecord':
                        this.toolParams.medRecordIsUpload = '0';
                        break;
                    default:
                        break;
                }
                this.initFetch();
            },
            /**
             * @desc 上传ICPC/诊疗记录
             * <AUTHOR>
             * @date 2025-03-12
             */
            async handleClickUpload(row, params) {
                await this.$abcSocialSecurity.uploadSettleReport({
                    taskId: row.id,
                    ...params,
                });
                this.fetchSettleDataList();
            },
            /**
             * @desc 点击结算数据列表
             * <AUTHOR>
             * @date 2025-03-12
             */
            async handleClickRow(row) {
                const needUploadICPC = !!this.isOpenUploadICPCList && row.medType !== '990502' && !row.icpcUploadTime && row.isRefunded !== 1;
                const needUploadOutpatientRecord = !!this.isOpenUploadOutpatientRecord && this.checkedOutpatientRecordUplaodStatus(row) && !row.medRecordUploadTime;
                if (!needUploadICPC && !needUploadOutpatientRecord) {
                    return;
                }
                await this.$abcSocialSecurity.uploadSettleReport({
                    taskId: row.id,
                    needUploadICPC,
                    needUploadOutpatientRecord,
                });
                this.initFetch();
            },
            checkedOutpatientRecordUplaodStatus(row) {
                const {
                    config, constants,
                } = this.$national;
                let isNeedUpload = true;
                // 如果已退费则不上传
                if (row.isRefunded === 1) {
                    isNeedUpload = false;
                }
                // 住院结算则不上传
                if (row.chargeSheetType === 5) {
                    isNeedUpload = false;
                }
                // 如果医疗类别等于门诊挂号或特殊情况门诊，则无需上传
                if ([constants.medicalCategoryConst.OUTPATIENT_REGISTRATION, constants.medicalCategoryConst.TSQKMZ].includes(row.medType)) {
                    isNeedUpload = false;
                }
                // 如果异地无需上传诊疗记录，且本条数据为异地则不上传
                if (!config.isYDNeedOutpatientRecordUploadList && !this.nationalSocialSecurity.checkIsLocalTrade(row.insuplcAdmdvs)) {
                    isNeedUpload = false;
                }
                return isNeedUpload;
            },
        },
    };
</script>
<style lang="scss">
.social-report__settle-list-dialog {
    &__table {
        flex: 1;
    }

    .abc-dialog-body {
        display: flex;
        flex-direction: column;
    }

    .tools-wrapper {
        display: flex;
        gap: var(--abc-space-m, 8px);
        align-items: center;
        align-self: stretch;
        margin-bottom: var(--abc-space-xl, 16px);
    }
}
</style>
