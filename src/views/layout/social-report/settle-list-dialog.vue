<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :auto-focus="false"
        title="ICPC上报"
        :content-styles="isSinglePatientOrderId ? 'width: 780px;padding: 24px;min-height: 660px' : 'width: 1200px;padding: 24px;min-height: 660px'"
        custom-class="social-report__settle-list-dialog"
        append-to-body
    >
        <div v-if="!isSinglePatientOrderId" class="tools-wrapper">
            <abc-form label-position="inner">
                <abc-form-item label="首次就诊时间">
                    <abc-date-picker
                        v-model="toolParams.dateRange"
                        type="daterange"
                        placeholder="选择时间"
                        :clearable="false"
                        :picker-options="pickerOptions"
                        @change="handleDateChange"
                    ></abc-date-picker>
                </abc-form-item>
            </abc-form>
            <abc-autocomplete
                v-model.trim="toolParams.patientName"
                custom-class="social-report__search-patient-suggestion"
                placeholder="姓名"
                :width="142"
                :inner-width="300"
                :async-fetch="true"
                clearable
                :fetch-suggestions="fetchPatientData"
                @enterEvent="onClickSelect"
                @clear="onClickClear"
            >
                <template slot="prepend">
                    <abc-search-icon></abc-search-icon>
                </template>
                <template slot="suggestion-header">
                    <div class="suggestion-title">
                        <div class="patient-info">
                            {{ $national.constants.patientWording }}信息
                        </div>
                    </div>
                </template>
                <template slot="suggestions" slot-scope="props">
                    <dt
                        class="suggestions-item"
                        :class="{ selected: props.index == props.currentIndex }"
                        @click="onClickSelect(props.suggestion)"
                    >
                        <div class="name ellipsis">
                            <span>{{ props.suggestion.patientName }}</span>
                        </div>
                        <div class="idCardNo">
                            {{ props.suggestion.idCardNo }}
                        </div>
                    </dt>
                </template>
            </abc-autocomplete>
            <abc-cascader
                v-model="toolParams.medTypeSelected"
                :props="{
                    value: 'value', label: 'name'
                }"
                :options="medicalCategoryOptions"
                :width="140"
                placeholder="就诊类型"
                multiple
                mutually-exclusive
                @change="handleMedicalCategoryChange"
            ></abc-cascader>
        </div>
        <div v-else style="margin-bottom: 16px;">
            <abc-descriptions
                :column="3"
                :label-width="56"
            >
                <abc-descriptions-item label="患者">
                    {{ patientDetail.patientName }}
                </abc-descriptions-item>
                <abc-descriptions-item label="医生">
                    {{ patientDetail.doctorName }}
                </abc-descriptions-item>
                <abc-descriptions-item label="险种">
                    {{ patientDetail.insutypeName }}
                </abc-descriptions-item>
                <abc-descriptions-item label="就诊类型">
                    {{ patientDetail.medTypeName }}
                </abc-descriptions-item>
                <abc-descriptions-item label="就诊时间">
                    {{ patientDetail.medTime }}
                </abc-descriptions-item>
            </abc-descriptions>
        </div>
        <abc-table
            :loading="loading"
            class="social-report__settle-list-dialog__table"
            :render-config="tableConfig"
            :pagination="tablePagination"
            :data-list="settleDataList"
            :clickable="true"
            @pageChange="handlePageChange"
        >
            <abc-flex v-if="!isSinglePatientOrderId" slot="topHeader">
                <biz-mixed-selection-filter
                    v-model="warningKey"
                    type="radio"
                    :gap="16"
                    :options="warningList"
                    @change="handleChangeWarningType"
                ></biz-mixed-selection-filter>
            </abc-flex>
            <template #chargedTime="{ trData }">
                <abc-table-cell>
                    {{ $national.tools.getDatetimeFormat(trData.chargedTime) }}
                </abc-table-cell>
            </template>
            <template #patientName="{ trData }">
                <abc-table-cell>
                    {{ trData.patientName || trData.cardOwner }}
                </abc-table-cell>
            </template>
        </abc-table>
    </abc-dialog>
</template>
<script>
    import { mapGetters } from 'vuex';
    import BizMixedSelectionFilter from '@/components-composite/biz-mixed-selection-filter';
    import { isEqual } from '@abc/utils';
    import { debounce } from 'utils/lodash.js';
    import {
        parseTime,
        getMonthStartDate,
    } from '@abc/utils-date';
    export default {
        name: 'SettleListDialog',
        components: {
            BizMixedSelectionFilter,
        },
        props: {
            // {psnName: string, idCardNo: string, beginTime: string, endTime: string, patientOrderId: string}
            queryInfo: {
                type: Object,
            },
        },
        data() {
            return {
                fetchPatientData: null,
                loading: false,
                warningKey: '',
                showDialog: true,
                toolParams: {
                    dateRange: [parseTime(getMonthStartDate(new Date()), 'y-m-d', true), parseTime(new Date(), 'y-m-d', true)],
                    medTypeSelected: [],//就诊类型
                    icpcIsUpload: '',// ICPC状态
                    icpcExpireFlag: '',// ICPC超期状态
                    medRecordIsUpload: '',// 诊疗记录状态
                    patientName: '',
                },
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                // 后端返回的查询参数列表
                searchParamsDataList: {
                    icpcNotUploadCount: 0,
                    icpcNearExpiryCount: 0,
                    medTypes: [],
                    clrOptinses: [],
                },
                settleData: null,
                taskDetail: null,
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'userInfo',
            ]),
            pickerOptions() {
                return { 
                    disabledDate: (date) => (
                        this.$national.tools.m(date).isBefore('2021-06-01') || // 小于这个时间不可取，这个时间国标才发布
                        this.$national.tools.m(date).isAfter(new Date()) // 大于当前时间不可取
                    ),
                    shortcuts: this.$national.tools.createShortcuts(),
                };
            },
            tablePagination() {
                return {
                    showTotalPage: true,
                    pageIndex: this.pageParams.pageIndex,
                    pageSize: this.pageParams.pageSize,
                    count: this.total,
                };
            },
            // 结算数据总条数
            total() {
                const { totalCount } = this.settleData || {};
                return totalCount || 0;
            },
            // 医疗类别
            medicalCategoryOptions() {
                let medicalCategoryOptions = this.$national.options.outpatientMedicalCategoryOptions;
                if (this.$national.vuexGetters('isPharmacy')) {
                    medicalCategoryOptions = this.$national.options.pharmacyMedicalCategoryOptions;
                }
                if (this.isAbcHospital) {
                    medicalCategoryOptions = [
                        ...this.$national.options.outpatientMedicalCategoryOptions,
                        ...this.$national.options.hospitalMedicalCategoryOptions,
                    ];
                }
                if (this.$national.config.isNational) {
                    return (this.searchParamsDataList?.medTypes || []).map((item) => {
                        const target = [
                            ...this.$national.options.medicalCategoryOptions,
                            ...medicalCategoryOptions,
                        ].find((option) => option.value === item);
                        return {
                            value: item,
                            name: target?.name || item,
                        };
                    });
                }
                return medicalCategoryOptions;
            },
            isOpenUploadICPCList() {
                return this.$national.vuexGetters('isOpenUploadICPCList');
            },
            isOpenUploadOutpatientRecord() {
                return this.$national.vuexGetters('isOpenUploadOutpatientRecord');
            },
            // 警告类型
            warningList() {
                const warningList = [
                    {
                        label: '待上报',
                        value: 'wait',
                        statisticsNumber: this.settleData?.icpcNotUploadCount,
                    },
                    {
                        label: '即将过期',
                        value: 'expire',
                        statisticsNumber: this.settleData?.icpcNearExpiryCount,
                    },
                ];
                return warningList;
            },
            //表头
            tableConfig() {
                const list = [
                    {
                        key: 'refChargedTime',
                        label: '首次就诊时间',
                        style: {
                            flex: 'none',
                            width: '140px',
                        },
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {row.refChargedTime ? this.$national.tools.getDatetimeFormat16(row.refChargedTime) : '-'}
                                </abc-table-cell>
                            );
                        },
                        show: !this.isSinglePatientOrderId,
                    },
                    {
                        key: 'chargedTimeWord',
                        label: '结算时间',
                        style: {
                            flex: 'none',
                            width: '140px',
                        },
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {this.$national.tools.getDatetimeFormat16(row.chargedTime)}
                                </abc-table-cell>
                            );
                        },
                    },
                    {
                        key: 'patientNameWord',
                        label: this.$national.constants.patientWording,
                        style: {
                            flex: 'none',
                            width: '100px',
                        },
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {row.patientName || row.cardOwner}
                                </abc-table-cell>
                            );
                        },
                        show: !this.isSinglePatientOrderId,
                    },
                    {
                        key: 'medTypeName',
                        label: '就诊类型',
                        style: {
                            flex: 'none',
                            width: '132px',
                        },
                        customRender: (h, row) => {
                            return (
                            <abc-table-cell>
                                {this.$national.tools.showTargetLabel(this.medicalCategoryOptions, row.medType, row.medTypeName)}
                            </abc-table-cell>
                            );
                        },
                        show: !this.isSinglePatientOrderId,
                    },
                    {
                        key: 'reportDeadline',
                        label: '上报截止时间',
                        style: {
                            flex: '1',
                        },
                        customRender: (h, row) => {
                            let memo = '', isShowError = row.reportDeadlineIsExpire;
                            //已上报和无需上报不显示错误
                            if (row.icpcUploadTime || row.medType === '990502' || row.isRefunded === 1 || row.status === 3 || row.status === 7) {
                                isShowError = false;
                            } else if (row.icpcUploadIsExpire) {
                                isShowError = false;
                                memo = '上报截止时间已过，无法上传';
                            } else if (row.reportDeadlineIsExpire) {
                                memo = '距上报期限已不足3天，请尽快上报';
                            }
                            return (
                                <abc-table-cell>
                                    <abc-tooltip
                                        disabled={ !memo }
                                        placement='top'
                                        content={ memo }
                                    >
                                        <abc-flex gap="8" align="center">
                                            <abc-text theme={ isShowError ? 'warning' : 'black' }>{ this.$national.tools.getDatetimeFormat16(row.reportDeadline) }</abc-text>
                                            { isShowError && <abc-icon icon="s-alert-small-fill" color="var(--abc-color-Y2)"></abc-icon> }
                                        </abc-flex>
                                    </abc-tooltip>
                                </abc-table-cell>
                            );
                        },
                    },
                    {
                        key: 'icpcUploadTime',
                        label: '上报时间',
                        style: {
                            flex: 'none',
                            width: !this.isSinglePatientOrderId ? '200px' : '140px',
                        },
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {row.icpcUploadTime ? this.$national.tools.getDatetimeFormat16(row.icpcUploadTime) : '-'}
                                </abc-table-cell>
                            );
                        },
                    },
                    {
                        key: 'icpcUploadStatus',
                        label: '上报状态',
                        style: {
                            flex: 'none',
                            width: '120px',
                        },
                        customRender: (h, row) => {
                            const colorMap = {
                                待上报: 'primary',
                                已上报: 'success',
                                无需上报: 'default',
                                已超期: 'warning',
                                上报失败: 'danger',
                            };
                            let label = '',memo = row.icpcUploadMessage;
                            if (row.medType === '990502' || row.isRefunded === 1 || row.status === 3 || row.status === 7) {
                                label = '无需上报';
                                memo = '';
                            } else if (row.icpcUploadTime) {
                                label = '已上报';
                            } else if (row.icpcUploadIsExpire) {
                                label = '已超期';
                            } else {
                                label = '待上传';
                            }
                            return (
                                <div class="table-cell">
                                    <abc-tooltip
                                        disabled={ !memo }
                                        maxWidth='300'
                                        placement='top'
                                        content={ memo }
                                    >
                                        <abc-tag-v2
                                            minWidth='84'
                                            variant={ row.medListCodg ? 'outline' : 'light-outline' }
                                            theme={ colorMap[ label ] }
                                            icon={ memo ? 's-b-info-circle-line' : '' }
                                            iconColor={ row.medListCodg ? '#F04A3E' : '#7A8794' }
                                        >
                                            { label }
                                        </abc-tag-v2>
                                    </abc-tooltip>
                                </div>
                            );
                        },
                    },
                    {
                        key: 'operate',
                        label: '操作',
                        style: {
                            flex: 'none',
                            width: '120px',
                        },
                        customRender: (h, row) => {
                            const {
                                icpcUploadTime,
                                icpcUploadIsExpire,
                                medType,
                                isRefunded,
                                status,
                            } = row;
                            let disabled = icpcUploadIsExpire;
                            if (medType === '990502' || isRefunded === 1 || status === 3 || status === 7) {
                                disabled = true;
                            }
                            return (
                                <div class="table-cell">
                                    <abc-check-access>
                                        <abc-button
                                            v-abc-check-electron
                                            type='text'
                                            size='small'
                                            disabled={ disabled }
                                            onClick={
                                                () => this.handleClickUpload(row, { needUploadICPC: true })
                                            }
                                        >
                                            { icpcUploadTime ? '重新上报' : '上报' }
                                        </abc-button>
                                    </abc-check-access>
                                </div>
                            );
                        },
                    },
                ];
                const config = {
                    hasInnerBorder: true,
                    list: list.filter((item) => item.show !== false),
                };

                return config;
            },
            // 展示数据
            settleDataList() {
                const { result } = this.settleData || {};
                //处理截止时间，refChargedTime类型为YYYY-MM-DD HH:mm:s +3天
                result?.forEach((item) => {
                    item.reportDeadlineIsExpire = false;
                    const deadline = item.refChargedTime || item.chargedTime, warnDays = 3 * 24 * 60, reportDays = 7 * 24 * 60;
                    item.reportDeadline = new Date(deadline).getTime() + reportDays * 60 * 1000;
                    // 如果当前时间超期，设置为true
                    item.reportDeadlineIsExpire = (new Date(deadline).getTime() + warnDays * 60 * 1000) < new Date().getTime();
                    //已经过期设置一个状态
                    item.icpcUploadIsExpire = new Date(deadline).getTime() + reportDays * 60 * 1000 < new Date().getTime();
                });
                return result || [];
            },
            // 是否查询单个patientOrderId
            isSinglePatientOrderId() {
                return !!this.queryInfo?.patientOrderId;
            },
            // 患者详情
            patientDetail() {
                //取列表中的数据进行组装
                const resultInfo = this.settleDataList[0] || {};
                const {
                    paymentResult, shebaoCardInfo, 
                } = this.taskDetail || {};
                const medTime = resultInfo?.refChargedTime || paymentResult?.setlTime || '';
                return {
                    patientName: shebaoCardInfo?.name,
                    doctorName: `${paymentResult?.deptName}-${paymentResult?.doctorName}`,
                    insutypeName: this.$national.tools.getInsutypeWording(paymentResult?.insutype),
                    medTypeName: this.$national.tools.getMedicalCategoryWording(paymentResult?.medType),
                    medTime: medTime ? this.$national.tools.getDateFormat8(medTime) : '-',
                };
            },
        },
        created() {
            this.fetchPatientData = debounce(this.fetchData, 800, true);
        },
        mounted() {
            if (this.queryInfo) {
                this.toolParams.patientName = this.queryInfo.psnName;
                this.toolParams.idCardNo = this.queryInfo.idCardNo;
                if (this.queryInfo.beginTime) {
                    this.$set(this.toolParams.dateRange, 0, this.queryInfo.beginTime);
                }
                if (this.queryInfo.endTime) {
                    this.$set(this.toolParams.dateRange, 1, this.queryInfo.endTime);
                }
                if (this.queryInfo.patientOrderId) {
                    this.toolParams.patientOrderId = this.queryInfo.patientOrderId;
                }
            }
            this.fetchSettleDataList();
            !this.isSinglePatientOrderId && this.fetchSettleFetchParams();
        },
        methods: {
            /**
             * 查询结算单详情
             */
            async fetchSettleDetail(taskId) {
                const response = await this.$national.api.fetchSettleDetailToDefault(taskId);
                if (response.status === false) {
                    return;
                }
                this.taskDetail = response.data;
            },
            /**
             * 关键词搜索
             * <AUTHOR>
             * @date 2022-05-12
             * @param {String} patientName 关键词
             * @param {Function} callback 回调函数
             */
            async fetchData(patientName, callback) {
                if (!patientName) {
                    return;
                }
                const params = {
                    psnName: patientName,
                };
                const response = await this.$national.api.searchPatientIdCardByName(params);
                if (response.status === false) {
                    return;
                }
                const dataList = (response.data || []).map((item) => ({
                    patientName: item.psnName,
                    idCardNo: item.idCardNo,
                }));
                callback(dataList);
            },
            /**
             * 当选择搜索项时
             * <AUTHOR>
             * @date 2022-05-12
             * @param {Object} data 选中项
             */
            onClickSelect(data) {
                this.toolParams.patientName = data?.patientName || '';
                this.toolParams.idCardNo = data?.idCardNo || '';
                this.initFetch();
            },
            /**
             * @desc 清除名字触发
             * <AUTHOR>
             * @date 2022-07-26
             */
            onClickClear() {
                this.toolParams.patientName = '';
                this.toolParams.idCardNo = '';
                this.initFetch();
            },
            close() {
                this.showDialog = false;
            },
            /**
             * @desc 处理数组数据
             * <AUTHOR>
             * @date 2022-06-23
             * @return {String}
             * @param {Array} arr
             */
            handleArrayParams(arr) {
                return (arr[0] || []).map((item) => item.value).join(',');
            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2022-05-12
             * @returns {Object}
             */
            createParams() {
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;
                const {
                    dateRange,
                    medTypeSelected,
                    icpcIsUpload,
                    icpcExpireFlag,
                    medRecordIsUpload,
                    patientName,
                    idCardNo,
                    patientOrderId,
                } = this.toolParams;
                const params = {
                    offset: pageIndex * pageSize,
                    limit: pageSize,
                    beginDate: dateRange[0],
                    endDate: dateRange[1],
                    medType: this.handleArrayParams(medTypeSelected),
                    icpcIsUpload,
                    icpcExpireFlag,
                    medRecordIsUpload,
                    patientName,
                    idCardNo,
                    patientOrderId,
                };
                // TODO: 2025-03-17
                // 如果当前是管理员，不传 doctorName，否则传当前用户ID
                const {
                    isAdmin,
                    name,
                } = this.userInfo || {};
                if (!isAdmin) {
                    params.doctorName = name || '';
                }
                return params;
            },
            /**
             * @desc 查询参数列表
             * <AUTHOR>
             * @date 2024-07-10
             */
            async fetchSettleFetchParams() {
                const params = this.createParams();
                const fetchResponse = await this.$national.api.fetchSettleFetchParams({
                    beginDate: params.beginDate,
                    endDate: params.endDate,
                });
                this.searchParamsDataList = fetchResponse.data || {};
            },
            async fetchSettleDataList() {
                this.loading = true;
                const params = this.createParams();
                const fetchResponse = await this.$national.api.fetchSettleDataList(params);
                if (fetchResponse.status === true && isEqual(params, this.createParams())) {
                    if (
                        this.$national.isSocialCom === false &&
                        this.$national.tools.decodeByAES
                    ) {
                        // 非医保电脑，后端下发的是加密后的taskId
                        const result = fetchResponse.data?.result || [];
                        result.forEach((item) => {
                            item.id = this.$national.tools.decodeByAES(`${this.$national.tools.getMonthFormat8()}_${item.idCardNo}`, item.id);
                        });
                    }
                    this.settleData = fetchResponse.data;
                }
                this.loading = false;
                if (this.isSinglePatientOrderId) {
                    !!this.settleDataList[0]?.id && this.fetchSettleDetail(this.settleDataList[0].id);
                }
            },
            /**
             * 初始化入参，然后拉取数据
             * <AUTHOR>
             * @date 2020-07-10
             */
            initFetch() {
                this.pageParams.pageIndex = 0;
                this.fetchSettleDataList();
            },
            /**
             * 当日期改变时
             */
            handleDateChange() {
                console.log('handleDateChange', this.toolParams.dateRange);
                this.initFetch();
                this.fetchSettleFetchParams();
            },
            /**
             * @desc 就诊类别改变时触发
             * <AUTHOR>
             * @date 2022-06-01 17:34:12
             */
            handleMedicalCategoryChange() {
                this.initFetch();
            },
            /**
             * 当改变分页时
             * <AUTHOR>
             * @date 2022-05-12
             * @param {Number} pageIndex 当前页面
             */
            handlePageChange(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchSettleDataList();
            },
            handleChangeWarningType(value) {
                this.toolParams.icpcIsUpload = '';
                this.toolParams.icpcExpireFlag = '';
                switch (value) {
                    case 'wait':
                        this.toolParams.icpcIsUpload = '0';
                        break;
                    case 'expire':
                        this.toolParams.icpcExpireFlag = '1';
                        break;
                    default:
                        break;
                }
                this.initFetch();
            },
            /**
             * @desc 上传ICPC/诊疗记录
             * <AUTHOR>
             * @date 2025-03-12
             */
            async handleClickUpload(row, params) {
                await this.$abcSocialSecurity.uploadSettleReport({
                    taskId: row.id,
                    ...params,
                });
                this.fetchSettleDataList();
            },
            checkedOutpatientRecordUplaodStatus(row) {
                const {
                    config, constants,
                } = this.$national;
                let isNeedUpload = true;
                // 如果已退费则不上传
                if (row.isRefunded === 1) {
                    isNeedUpload = false;
                }
                // 住院结算则不上传
                if (row.chargeSheetType === 5) {
                    isNeedUpload = false;
                }
                // 如果医疗类别等于门诊挂号或特殊情况门诊，则无需上传
                if ([constants.medicalCategoryConst.OUTPATIENT_REGISTRATION, constants.medicalCategoryConst.TSQKMZ].includes(row.medType)) {
                    isNeedUpload = false;
                }
                // 如果异地无需上传诊疗记录，且本条数据为异地则不上传
                if (!config.isYDNeedOutpatientRecordUploadList && !this.nationalSocialSecurity.checkIsLocalTrade(row.insuplcAdmdvs)) {
                    isNeedUpload = false;
                }
                return isNeedUpload;
            },
        },
    };
</script>
<style lang="scss">
.social-report__settle-list-dialog {
    &__table {
        flex: 1;
    }

    .abc-dialog-body {
        display: flex;
        flex-direction: column;
    }

    .tools-wrapper {
        display: flex;
        gap: var(--abc-space-m, 8px);
        align-items: center;
        align-self: stretch;
        margin-bottom: var(--abc-space-xl, 16px);
    }
}
</style>
