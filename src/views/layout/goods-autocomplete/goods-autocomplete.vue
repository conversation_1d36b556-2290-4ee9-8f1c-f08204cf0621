<!--商品选择 autocomplete-->

<template>
    <div class="goods-autocomplete-wrapper" :style="computedStyle">
        <abc-autocomplete
            ref="abcAutocomplete"
            v-model="keyword"
            :disabled="disabled"
            :placeholder="placeholder"
            :custom-class="`good-autocomplete-suggestion ${ customClass}`"
            :size="size"
            :width="width"
            :placement="placement"
            :focus-placeholder="focusPlaceholder"
            :inner-width="innerWidth"
            :delay-time="delayTime"
            :async-fetch="true"
            :focus-show="focusShow"
            :show-price="showPrice"
            :fetch-suggestions="queryGoods"
            :auto-focus-first="autoFocusFirst"
            :resident-sugguestions="supportSearchFilter"
            :close-on-click-outside="handleCloseOnClickOutside"
            data-cy="goods-autocomplete"
            :clearable="clearable"
            :adaptive-width="adaptiveWidth"
            @clear="clearKeyword"
            @focus="handleFocus"
            @blur="blurHandle"
            @enterEvent="selectGoods"
            @enter="onEnter"
        >
            <template v-if="suggestionTitles" slot="suggestion-header">
                <suggestion-header-filter
                    v-if="supportSearchFilter && refAutoComplete?.showSuggestions"
                    :params.sync="filterParams"
                    :filter-cascader-func="filterCascaderFunc"
                    :glasses-form="glassesForm"
                    @change="onFilterChange"
                ></suggestion-header-filter>
                <goods-suggestion-title :titles="suggestionTitles"></goods-suggestion-title>
            </template>

            <template
                #suggestions="{
                    suggestion, index, currentIndex
                }"
            >
                <goods-suggestion-item
                    slot="reference"
                    :items="suggestionItems"
                    :suggestion="suggestion"
                    :index="index"
                    :current-index="currentIndex"
                    @select="selectGoods"
                ></goods-suggestion-item>
            </template>

            <template v-if="supportSearchFilter && suggestions.length === 0" slot="suggestion-footer">
                <abc-content-empty
                    class="search-empty"
                    value="暂无数据"
                    top="0"
                    :icon-size="10"
                ></abc-content-empty>
            </template>

            <template v-if="withSearchIcon" #prepend>
                <abc-search-icon></abc-search-icon>
            </template>
            <template v-else-if="iconName" #prepend>
                <abc-icon :icon="iconName" :size="iconSize" color="var(--abc-color-T3)"></abc-icon>
            </template>
            <div
                v-if="clearIconName && keyword"
                slot="append"
                class="clear-icon"
                @click="clearKeyword"
            >
                <i :class="['iconfont', clearIconName]"></i>
            </div>
        </abc-autocomplete>
    </div>
</template>

<script>
    // api
    import GoodsAPI from 'api/goods/index';
    import GoodsV3API from 'api/goods/index-v3';
    // sdk
    import { mapGetters } from 'vuex';
    import { GoodsTypeEnum } from '@abc/constants';
    import * as repository from 'MfFeEngine/repository';
    import clone from 'utils/clone.js';
    import {
        debounce, isNull,
    } from 'utils/lodash';
    // components
    import SuggestionHeaderFilter from 'src/views/layout/suggestion-header-filter/suggestion-header-filter.vue';
    import GoodsSuggestionTitle from 'views/layout/goods-autocomplete/goods-suggestion-title.vue';
    import GoodsSuggestionItem from 'views/layout/goods-autocomplete/goods-suggestion-item.vue';



    // eslint-disable-next-line vue/one-component-per-file
    export default {
        components: {
            SuggestionHeaderFilter,
            GoodsSuggestionTitle,
            GoodsSuggestionItem,
        },
        props: {
            version: {
                type: [Number, String],
                default: 2,
            },
            isSearchCommon: {
                type: Boolean,
                default: false,
            },
            disabled: {
                // 是否禁用
                type: [Boolean, Number],
                default: false,
            },
            placeholder: {
                type: [String],
                default: '',
            },
            focusPlaceholder: {
                type: String,
                default: '',
            },
            size: {
                // autocomplete 高度大小
                type: [String],
                default: '',
            },
            fromStatistics: {
                type: Boolean,
                default: false,
            },
            isChargeProductStat: {
                type: Boolean,
                default: false,
            },
            customClass: {
                // 自定义class
                type: [String],
            },
            placement: {
                // popover层浮动位置
                type: [String],
                default: 'bottom-start',
            },
            innerWidth: {
                // 弹出框的宽度
                type: [Number, String],
            },
            focusShow: {
                type: Boolean,
            },
            showPrice: {
                type: Boolean,
                default: true,
            },
            delayTime: {
                // 搜索debounce延时
                type: [Number],
                default: 10,
            },
            width: Number,
            iconName: {
                type: String,
                default: 's-add-line-medium',
            },
            iconSize: {
                type: [Number, String],
                default: 12,
            },
            clearIconName: {
                type: String,
                default: '',
            },
            suggestionTitles: {
                type: [Array],
            },
            suggestionItems: {
                type: [Array],
                required: true,
            },
            splitByStock: {
                // 是否需要根据库存分割数据
                type: Boolean,
                default: false,
            },
            splitString: {
                type: String,
                default: '以下药品不在库存中',
            },
            clinicId: [Number, String],
            jsonType: {
                // 需要查询的商品大类型
                // 参考值：
                // type = 1, 药品
                // type = 2, 物资
                // type = 3, 检验检查
                // type = 4, 治疗理疗其他
                // type = 7, 商品
                // type = 11, 套餐

                // 需要查询的商品分项类型
                // 参考值：
                // type = 1, 西药:subType = 1, 中药:subType = 2，中成药:subType = 3;
                // type = 2, 医疗器械:subType = 1, 后勤材料:subType = 2，固定资产subType = 3;
                // type = 3, 检验:subType = 1, 检查:subType = 2;
                // type = 4, 治疗:subType = 1, 理疗:subType = 2;
                // type = 7, 自制成品:subType = 1, 保健药品:subType = 2, 保健食品:subType = 3，其他商品:subType = 4；
                // type = 19, 其他费用
                // [{type: 1, subType: [1,2]}, {type:4,subType:[1,2]}]
                type: [Array],
                required: true,
            },
            diagnosis: {
                // 可以加入诊断信息查询
                type: [String],
            },
            spec: {
                // 中药要根据规格查询
                type: [String],
            },
            // 选中项目后是否自动清除关键字
            autoClear: {
                type: Boolean,
                default: true,
            },
            defaultKeyword: {
                type: String,
                default: '',
            },
            // 默认选中第一个suggestion
            autoFocusFirst: {
                type: Boolean,
                default: true,
            },
            // 聚焦搜索
            focusSearch: {
                type: Boolean,
                default: false,
            },
            withDomainMedicine: {
                type: Number,
            },
            withDeleted: {
                type: Number,
            },

            queryV3Params: {
                type: Object,
                default: () => {
                    return {
                        offset: 0,
                        limit: 100,
                    };
                },
            },

            // 聚焦自动推荐
            autoSuggestion: {
                type: Boolean,
                default: false,
            },
            openSearchFilter: Boolean,
            departmentId: String,
            filterCascaderFunc: Function,
            glassesForm: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            withSearchIcon: {
                type: Boolean,
                default: false,
            },

            // 不允许开出库存不足的药品
            disableNoStockGoods: {
                type: Boolean,
                default: false,
            },

            // 不判断$store.disableNoStockGoods
            ignoreDisableNoStockGoods: {
                type: Boolean,
                default: false,
            },

            clearable: {
                type: Boolean,
                default: false,
            },

            adaptiveWidth: {
                type: Boolean,
                default: false,
            },

            isEnableAiSearch: {
                type: Boolean,
                default: false,
            },

            // 禁用未定价商品
            disabledPackagePriceIsNull: {
                type: Boolean,
                default: false,
            },

            // 自定义过滤函数，用于对suggestions数据进行处理
            customFilterSuggestions: {
                type: Function,
                default: null,
            },
            isSupportManufacturerFilter: {
                type: Boolean,
                default: true,
            },
            selectedManufacturer: String,
            createManufacturerOptions: {
                type: Function,
                default: null,
            },
            clearManufacturerData: {
                type: Function,
                default: null,
            },

            pharmacyNo: {
                type: [Number, String],
                default: undefined,
            },

            // 是否根据未引进商品分割
            splitByNotInitialStock: {
                type: Boolean,
                default: false,
            },
            search: {
                type: String,
                default: '',
            },
        },

        data() {
            return {
                keyword: this.defaultKeyword,
                filterParams: {
                    typeId: [], // 品种类型
                    customTypeId: [], // 子类型
                    customType: undefined,// 定制类型 0 定制 10成品
                    refractiveIndex: undefined,// 折射率
                    spherical: '',// 球镜
                    lenticular: '',// 柱镜
                    addLight: '',// 下加光
                    focalLength: '',// 后顶焦度
                    wearCycle: '',// 佩戴周期
                    material: '', // 材质
                    spec: '', // 规格
                    color: '', // 颜色
                },
                suggestions: [],
                refAutoComplete: null,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'featureSupportFilterEyeGlasses',
            ]),
            supportFilterEyeGlasses() {
                return this.featureSupportFilterEyeGlasses;
            },
            supportSearchFilter() {
                return this.openSearchFilter && this.supportFilterEyeGlasses;
            },
            computedStyle() {
                if (this.adaptiveWidth) {
                    return {
                        width: '100%',
                    };
                }
                return {};
            },
        },
        watch: {
            keyword: {
                handler (val) {
                    if (!val.trim()) {
                        this.$emit('clear');
                    }
                    if (typeof this.clearManufacturerData === 'function') this.clearManufacturerData();
                    this.$emit('update:defaultKeyword', val);
                },
            },
            'refAutoComplete.showSuggestions': {
                handler(val) {
                    if (!val) {
                        this.clearGlassesParams();
                    }
                },
            },
        },

        created() {
            this._outpatient_service_instance = repository.OutpatientService.getInstance();
            this._goods_repo_instance = repository.GoodsRepositoryService.getInstance();

            this._queryGoods = debounce(this.queryGoods, 400, true);
        },

        mounted() {
            if (this.supportSearchFilter || this.isSupportManufacturerFilter) {
                this.refAutoComplete = this.$refs.abcAutocomplete;
            }
        },

        methods: {
            clearQueryString() {
                this.keyword = '';
            },

            handleFocus() {
                this.$emit('focus', this);
            },

            blurHandle() {
                this.$emit('blur', this);
            },

            needStock(item) {
                return (
                    item.type === GoodsTypeEnum.MEDICINE ||
                    item.type === GoodsTypeEnum.MATERIAL ||
                    item.type === GoodsTypeEnum.GOODS
                );
            },

            onEnter(event, selectItem) {
                this.$emit('enter', event, selectItem);
            },

            async queryGoods(keyword, callback) {
                // 解析keyword，使用空格分隔，取第一项为原始keyword，最后一项为parseManufacturer
                let parseManufacturer = '';

                if (keyword && keyword.trim()) {
                    const keyParts = keyword.trim().split(' ').filter((part) => part.trim());
                    if (keyParts.length > 1) {
                        keyword = keyParts[0];
                        parseManufacturer = keyParts[keyParts.length - 1];
                    }
                }

                if (!keyword.trim() && this.focusSearch) {
                    callback([]);
                    return false;
                }
                if (!keyword.trim() && !this.focusShow) {
                    callback([]);
                    return false;
                }
                let res = {};
                // 是否启用远程搜索
                const useRemoteSearch = this._goods_repo_instance?.searchConfig?.useRemoteSearchGoodsAPI ?? true;

                if (this.supportSearchFilter || !useRemoteSearch) {
                    Object.assign(this.queryV3Params, {
                        keyword,
                        jsonType: this.jsonType,
                        withDomainMedicine: this.withDomainMedicine,
                        intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                    });
                    const searchParams = this.mergeFilterParams(this.queryV3Params, this.filterParams);
                    /**
                     * @desc 本地搜索
                     * @param {Object} params
                     * @param {Boolean} 是否启用本地搜索
                     * @param {Number} 参数解析类型 1: 普通解析 2: 优先眼镜格式解析如：r1.61 s-2 c-2
                     * @return {Object} 返回搜索结果
                     */
                    const resData = await this._outpatient_service_instance.searchGoods(
                        searchParams, true, this.supportSearchFilter ? 2 : 1,
                    );
                    res.data = clone(resData);
                } else {
                    if (!keyword.trim() && this.isEnableAiSearch) {
                        res = await GoodsV3API.searchAIRecommendGoods({
                            ...this.queryV3Params,
                            jsonType: this.jsonType,
                            intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                        });
                    } else if (!keyword.trim() && this.autoSuggestion) {
                        res = await GoodsV3API.searchGoods(Object.assign(this.queryV3Params, {
                            keyword,
                            intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                        }));
                    } else if (+this.version === 3) {
                        res = await GoodsV3API.searchGoods(
                            Object.assign(this.queryV3Params, {
                                keyword,
                                jsonType: this.jsonType,
                                spec: this.spec,
                                withDomainMedicine: this.withDomainMedicine,
                                intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                                pharmacyNo: this.pharmacyNo,
                            }),
                        );
                    } else if (this.isSearchCommon) {
                        res = await GoodsV3API.searchCommonGoods(
                            Object.assign(this.queryV3Params, {
                                keyword,
                                excludeJsonType: this.jsonType,
                                // withDomainMedicine: this.withDomainMedicine ?? false,
                                withDeleted: this.withDeleted,
                                // searchSystemGoods: 1,
                                intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                            }),
                        );
                    } else {
                        res = await GoodsAPI.queryStockGoods({
                            keyword,
                            clinicId: this.clinicId,
                            jsonType: this.jsonType,
                            diagnosis: this.diagnosis,
                            searchSystemGoods: this.isChargeProductStat ? 1 : '',
                            spec: this.spec,
                            withDomainMedicine: this.withDomainMedicine,
                            intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                        });
                    }
                }
                const { data } = res;

                if (!data) {
                    if (callback) {
                        callback([]);
                        return false;
                    }
                    this.refAutoComplete.focus();
                    this.refAutoComplete.callbackHandler([]);

                }


                data.list = data.list.map((item) => {
                    item.productName = item.name;
                    item.name = item.name || item.medicineCadn;

                    // 特殊地方不需要判断 “库存不足，不允许开出” 开关等设置
                    if (!this.ignoreDisableNoStockGoods) {
                        // 【库存不足 不允许在在门诊、收费处、执行站开出】|| 在统计模块可以筛选(之前有库存，这个药通过系统发药把库存发完了，还是需要支持搜索)
                        if (this.disableNoStockGoods || (this.$store.getters.disableNoStockGoods && !this.fromStatistics)) {
                            if (this.needStock(item)) {
                                item.disabled = item.noStocks || (item.stockPackageCount + item.stockPieceCount) <= 0 || isNull(item.packagePrice);
                            }
                        }
                    }
                    return item;
                });

                if (this.splitByStock) {
                    // 可能有混合搜索，需要先把不用判断库存的商品算出个数
                    const noNeedStock = data.list.filter((item) => {
                        return !this.needStock(item);
                    }).length;

                    let tipsIndex = data.list.filter((item) => {
                        return this.needStock(item) && !item.noStocks;
                    }).length;

                    tipsIndex += noNeedStock;

                    if (tipsIndex !== data.list.length) {
                        data.list.splice(tipsIndex, 0, {
                            disabled: true,
                            isTips: true,
                            name: this.splitString,
                        });
                    }
                }

                if (this.splitByNotInitialStock && data.list.length > 0) {
                    // noStocks 为 true 表示未初始化入库，后端将 noStocks 数据沉底，找到第一个位置插入一行标题
                    const firstNotImportedIndex = data.list.findIndex((item) => item.noStocks);

                    if (firstNotImportedIndex >= 0) {
                        data.list.splice(firstNotImportedIndex, 0, {
                            disabled: true,
                            isTips: true,
                            isNotInitialStockTips: true,
                            name: '以下商品不在库存中',
                        });
                    }
                }

                // 处理suggestions数据
                const filteredList = data.list || [];
                if (this.isSupportManufacturerFilter && this.createManufacturerOptions) {
                    this.createManufacturerOptions(filteredList);
                }

                // 如果存在自定义过滤函数，则使用该函数处理数据
                if (this.customFilterSuggestions && typeof this.customFilterSuggestions === 'function') {
                    data.list = this.customFilterSuggestions(filteredList);
                }

                if (callback) {
                    callback(data.list);
                } else {
                    this.refAutoComplete.focus();
                    this.refAutoComplete.callbackHandler(data.list);
                }
                this.suggestions = data.list;
            },

            selectGoods(goods) {
                if (this.autoClear) {
                    this.keyword = '';
                } else {
                    this.keyword = (goods && (goods.medicineCadn || goods.name)) || '';
                }
                this.$emit('selectGoods', goods);
            },
            clearKeyword() {
                this.keyword = '';
                this.$emit('clear');
            },
            onFilterChange() {
                this._queryGoods(this.keyword);
            },

            clearGlassesParams() {
                this.filterParams = {
                    typeId: [], // 品种类型
                    customTypeId: [], // 子类型
                    customType: undefined,// 定制类型 0 定制 10成品
                    refractiveIndex: undefined,// 折射率
                    spherical: '',// 球镜
                    lenticular: '',// 柱镜
                    addLight: '',// 下加光
                    focalLength: '',// 后顶焦度
                    wearCycle: '',// 佩戴周期
                    material: '', // 材质
                    spec: '', // 规格
                    color: '', // 颜色
                };
            },
            handleCloseOnClickOutside(e) {
                console.log('handleCloseOnClickOutside', e);
                const eventPath = e?.path || (e?.composedPath?.());
                if (this.isSupportManufacturerFilter && eventPath?.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className.includes('goods-auto-complete-manufacturer-select');
                })) {
                    return false;
                }
                const isInnerEl = e.path.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className?.includes('suggestions-item-filter');
                });
                // 厂家筛选关闭面板时重置搜索结果--产品明确要的效果
                if (this.selectedManufacturer && !isInnerEl) {
                    // 清除选中厂家
                    if (typeof this.clearManufacturerData === 'function') this.clearManufacturerData(true);
                    // 更新列表数据
                    this.queryGoods(this.keyword, (list) => {
                        this.refAutoComplete.isFocus = false;
                        this.refAutoComplete.callbackHandler(list);
                    });
                }
                if (!isInnerEl) {
                    this.$emit('closePanel');
                }
                return !isInnerEl;
            },

            mergeFilterParams(params, mergeParams) {
                const filterParams = clone(mergeParams);
                for (const key in filterParams) {
                    if (
                        filterParams.hasOwnProperty(key) &&
                        (
                            (typeof (filterParams[key]) !== 'number' && !filterParams[key]) ||
                            filterParams[key]?.length === 0
                        )
                    ) {
                        delete filterParams[key];
                        delete params[key];
                    }
                }
                Object.assign(params, filterParams);
                return params;
            },

            focusInput() {
                this.$refs.abcAutocomplete.focus();
            },
        },
    };
</script>
<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .good-autocomplete-suggestion {
        &.suggestions-wrapper {
            min-width: 676px;

            &.has-filter {
                min-width: 790px;

                .abc-scrollbar-wrapper {
                    max-height: 194px;
                }
            }
        }

        .suggestion-title {
            display: flex;
            align-items: center;
            padding: 0 10px;
        }

        .abc-scrollbar-wrapper {
            overflow-y: scroll;

            .suggestions-item {
                padding: 0 0 0 10px;

                .device-icon {
                    color: var(--abc-color-theme2);
                }

                .cooperation-tag {
                    color: var(--abc-color-T2);
                }

                &.selected {
                    .device-icon {
                        color: var(--abc-color-T4);
                    }

                    .cooperation-tag {
                        color: var(--abc-color-T4);
                    }
                }
            }
        }

        .suggestions-item > div {
            font-size: 13px;
            line-height: 18px;
        }

        .suggestion-title,
        .suggestions-item {
            .medicine-name-group {
                flex: 2 !important;
                font-size: 14px;
            }

            .spec {
                flex: 1;
                padding-left: 6px;
            }

            .display-inventory {
                width: 70px;
                padding-left: 6px;
                text-align: right;
            }

            .display-price {
                width: 80px;
                padding-left: 6px;
                text-align: right;
            }

            .medical-fee-grade {
                width: 60px;
                text-align: center;
            }

            .manufacturer {
                width: 84px;
                padding-left: 6px;
                text-overflow: initial;
                white-space: nowrap;
            }
        }

        .suggestions-item.is-not-initial-stock-tips[disabled] {
            span.name {
                color: var(--abc-color-Y1);
            }

            &:not(.selected):hover {
                background-color: var(--abc-color-cp-grey4);
                border-top-color: var(--abc-color-P8);

                & + .suggestions-item {
                    border-top-color: var(--abc-color-P8);
                }
            }

            &:hover {
                background-color: var(--abc-color-white) !important;
            }
        }

        .un-fix-price {
            color: var(--abc-color-O2);
        }
    }

    .medical-autocomplete-space-wrap {
        width: 100%;
        padding: 6px;
    }

    .abc-content-empty-wrapper.search-empty {
        padding: 10px 0;

        .empty-icon {
            img {
                width: 60px;
            }
        }
    }

    .pharmacy-goods-autocomplete {
        .no-stocks {
            span {
                color: var(--abc-color-T2);
            }
        }

        .no-stocks.selected {
            span {
                color: var(--abc-color-white);
            }
        }

        .un-fix-price {
            color: var(--abc-color-T2);
        }
    }
</style>
