<template>
    <abc-select
        v-model="selectedClinic"
        :width="width"
        :max-width="maxWidth"
        select-type="filter"
        :disabled="disabled"
        :style="customStyle"
        :placeholder="placeholder"
        :fetch-suggestions="fetchClinicSuggestions"
        :with-search="subClinics.length > 10"
        :only-bottom-border="onlyBottomBorder"
        :input-style="{ paddingRight: '26px' }"
        :inner-width="224"
        :clearable="clearable"
        :no-icon="noIcon"
        :adaptive-width="adaptiveWidth"
        :size="size"
        @change="changeHandler"
    >
        <abc-option v-if="showAllClinic" :value="''" label="全部门店"></abc-option>
        <template v-for="it in currentClinics">
            <abc-option
                v-if="showClinic(it)"
                :key="it.id"
                :value="it.id"
                :label="showCurrentClinic && it.id === clinicId ? '本店' : it.shortName ? it.shortName : it.name"
            >
                <span v-abc-title="showCurrentClinic && it.id === clinicId ? '本店' : it.shortName ? it.shortName : it.name" class="ellipsis"></span>
            </abc-option>
        </template>
    </abc-select>
</template>

<script>
    import { mapGetters } from 'vuex';
    import clone from 'utils/clone';
    export default {
        name: 'ClinicSelect',
        props: {
            value: {
                type: String,
                validator: (prop) => typeof prop === 'string' || prop === null,
            },
            placeholder: {
                type: String,
                default: '全部门店',
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            clearable: {
                type: Boolean,
                default: false,
            },
            noIcon: {
                type: Boolean,
                default: false,
            },
            width: {
                type: Number,
                default: 160,
            },
            maxWidth: {
                type: Number,
                default: 160,
            },
            adaptiveWidth: {
                type: Boolean,
                default: false,
            },
            showAllClinic: {
                // 是否显示全部门店
                type: Boolean,
                default: true,
            },
            showCurrentClinic: {
                // 当前门店是否显示为本店
                type: Boolean,
                default: false,
            },
            filterCurrentClinic: {
                // 是否过滤当前门店
                type: Boolean,
                default: false,
            },
            customStyle: {
                type: String,
                default: '',
            },
            onlyBottomBorder: {
                type: Boolean,
                default: false,
            },
            filterChainClinics: {
                type: Boolean,
                default: false,
            },
            excludeChainClinics: {
                type: Boolean,
                default: false,
            },
            size: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                originalClinics: [],
                keyword: '',
            };
        },
        computed: {
            ...mapGetters(['subClinics', 'currentClinic']),
            selectedClinic: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },
            currentClinics() {
                const key = this.keyword;
                if (key) {
                    return this.originalClinics.filter((item) => {
                        return (
                            (item.name && item.name.indexOf(key) > -1) ||
                            (item.shortName && item.shortName.indexOf(key) > -1) ||
                            (item.namePy && item.namePy.toLocaleLowerCase().indexOf(key) > -1) ||
                            (item.namePyFirst && item.namePyFirst.toLocaleLowerCase().indexOf(key) > -1)
                        );
                    });
                }
                if (this.filterChainClinics) {
                    return this.originalClinics.filter((item) => {
                        return item.chainAdmin;
                    });
                }
                if (this.excludeChainClinics) {
                    return this.originalClinics.filter((item) => {
                        return !item.chainAdmin;
                    });
                }
                return this.originalClinics;
            },
        },
        async created() {
            if (!this.subClinics || !this.subClinics.length) {
                await this.$store.dispatch('fetchChainSubClinics');
            }
            this.originalClinics = clone(this.subClinics);
        },
        methods: {
            changeHandler(val) {
                this.$emit('change', val);
            },
            fetchClinicSuggestions(key) {
                this.keyword = key.trim();
            },
            showClinic(clinic) {
                if (this.filterCurrentClinic) {
                    return clinic.id !== this.clinicId;
                }
                return true;
            },
        },
    };
</script>
