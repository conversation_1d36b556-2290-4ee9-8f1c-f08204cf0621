<template>
    <div
        class="prescription-table-wrapper infusion-table"
        :class="[
            `infusion${formIndex}`,
            {
                'is-disabled': disabledForm || isEexcuted,
                'has-special-requirement': hasSpecialRequirement
            }
        ]"
        data-cy="输注处方"
    >
        <div class="prescription-header">
            <div class="prescription-title">
                <img
                    class="icon-wrapper"
                    src="~assets/images/icon/transfusion.png"
                    alt=""
                />
                <h5>输注处方{{ translateH }}</h5>
                <cooperation-pharmacy-vendors
                    v-if="!isConsultation && showCooperationPharmacy && cooperationPharmacyList.length"
                    style="margin-left: 12px;"
                    :pharmacy-type.sync="form.pharmacyType"
                    :pharmacy-no.sync="form.pharmacyNo"
                    :pharmacy-name.sync="form.pharmacyName"
                    :form-items="form.prescriptionFormItems"
                    :disabled="disabledForm"
                    data-cy="pr-infusion-table-cooperation"
                ></cooperation-pharmacy-vendors>
            </div>
            <div class="operation">
                <abc-text
                    v-if="showBasicDrugReminder"
                    theme="black"
                    size="mini"
                    style="display: inline-flex; align-items: center; margin-right: 6px;"
                >
                    <abc-icon
                        icon="s-b-info-circle-line"
                        :size="14"
                        color="var(--abc-color-P10)"
                        style="margin-right: 4px;"
                    ></abc-icon>
                    请优先使用基药
                </abc-text>
                <abc-flex
                    v-if="isOpenSource && showPsychotropicNarcoticEmployee"
                    align="center"
                    :gap="4"
                >
                    <abc-text theme="black" size="mini">
                        代办人：{{ psychotropicNarcoticEmployee.name || '- ' }}
                    </abc-text>
                    <abc-button
                        v-if="!disabledForm"
                        data-cy="agent-button"
                        icon="s-b-edited-line"
                        variant="text"
                        theme="default"
                        size="small"
                        @click="onClickPsychotropicNarcoticEmployee(form.psychotropicNarcoticType)"
                    >
                    </abc-button>
                </abc-flex>
                <jing-ma-dropdown
                    v-model="form.psychotropicNarcoticType"
                    data-cy="pr-infusion-table-jing-ma-dropdown"
                    :disabled="disabledForm"
                    @change="changePsychotropicNarcoticType"
                ></jing-ma-dropdown>
                <abc-button
                    v-if="!disabledForm && !disabledAdd && isOpenSource && !form.pharmacyType"
                    data-cy="pr-infusion-table-operation-search"
                    variant="text"
                    theme="default"
                    size="small"
                    icon="s-list-search-line"
                    @click="showGoodsSelectDialog = true"
                >
                </abc-button>
                <abc-check-access :permission-keys="[ROLE_DOCTOR_ID, ROLE_DOCTOR_ASSIST_ID]">
                    <abc-button
                        v-if="isOpenSource"
                        variant="text"
                        theme="default"
                        size="small"
                        icon="save"
                        data-cy="pr-infusion-table-save"
                        style="margin-left: 0;"
                        @click="saveCommon"
                    >
                    </abc-button>
                </abc-check-access>
                <trash-button-v2
                    v-if="!(disabledForm || isEexcuted)"
                    :is-confirm="deleteNeedConfirm"
                    data-cy="pr-infusion-table-trash"
                    @delete="deletePres"
                >
                </trash-button-v2>
            </div>
        </div>


        <!--输液西药处方-->
        <infusion-table
            v-for="(group, index) in groupList"
            ref="infusionTable"
            :key="`${group.groupId }_${ index}`"
            v-model="groupList[index]"
            :p-index="index"
            :is-charged="isCharged"
            :title="`输注处方${translateH}`"
            :form-charge-status="form.chargeStatus"
            :prescription-form="form"
            :show-detail-price="showDetailPrice"
            :verify-outpatient="verifyOutpatient"
            :show-total-price="showTotalPrice"
            :disabled="disabledForm || group.disabled"
            :disabled-add="disabledAdd"
            :patient-info="patientInfo"
            :medical-record="medicalRecord"
            :shebao-card-info="shebaoCardInfo"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="departmentId"
            :need-check-stock="needCheckStock"
            :is-open-source="isOpenSource"
            :show-medical-fee-grade="showMedicalFeeGrade"
            :prescription-total="prescriptionTotal"
            :list-length="groupList.length"
            :doctor-id="doctorId"
            :is-from-template="isFromTemplate"
            @queryVerify="$emit('queryVerify')"
            @addGroup="addGroupHandler"
            @changeGroupId="changeGroupIdHandler"
            @deleteGroup="deleteGroupHandler"
            @expandSpecialRequirement="expandSpecialRequirementHandle"
            @change-pay-type="(val, item) => $emit('change-pay-type', val, item, form)"
        >
        </infusion-table>

        <component
            :is="curCommonDialog"
            v-if="dialogVisible"
            v-model="dialogVisible"
            type="infusion"
            :prescription-form="form"
        ></component>

        <goods-select-dialog
            v-if="showGoodsSelectDialog"
            v-model="showGoodsSelectDialog"
            :default-category-key="CATEGORY_TYPE_ENUM.MEDICINE_WESTERN"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="departmentId"
            :category-range="categoryRange"
            @onSelectGoods="quickSelect"
        ></goods-select-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
// vue lib
    import { mapGetters } from 'vuex';

    // utils
    import { numToChinese } from 'utils/index';
    import {
        itemCountPrice, sum,
    } from 'utils/calculation';
    import { getItemUnitPrice } from 'views/outpatient/utils.js';

    // components
    import InfusionTable from './table.vue';
    import TrashButton from '../common/trash-button';
    import JingMaDropdown from '../common/jing-ma-dropdown.vue';
    import CommonPrescriptionDialog from '../common/common-prescription-dialog';
    import CooperationPharmacyVendors from 'views/outpatient/common/cooperation-pharmacy-vendors/index.vue';
    import CommonPrescriptionHospitalDialog from 'views/layout/prescription/common/common-prescription-hospital-dialog.vue';
    import GoodsSelectDialog from 'views/layout/goods-select-dialog/index.vue';

    // constants
    import {
        OutpatientChargeTypeEnum, PsychotropicNarcoticTypeEnum,
    } from 'views/outpatient/constants.js';
    import { ExecuteStatus } from 'views/common/enum';
    import { ChargeFormStatusEnum } from '@/service/charge/constants.js';
    import {
        ROLE_DOCTOR_ASSIST_ID,
        ROLE_DOCTOR_ID,
    } from 'utils/constants';
    import { CATEGORY_TYPE_ENUM } from 'views/common/goods-search/constants';
    import {
        completePrescriptionItem, getPrescriptionItemStruct,
    } from 'views/layout/prescription/utils';
    import TrashButtonV2 from 'views/layout/prescription/common/trash-button-v2.vue';

    const categoryRange = [
        CATEGORY_TYPE_ENUM.MEDICINE_WESTERN,
        CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_PATENT,
    ];

    export default {
        name: 'InfusionPrescription',
        components: {
            TrashButtonV2,
            GoodsSelectDialog,
            InfusionTable,
            TrashButton,
            JingMaDropdown,
            CooperationPharmacyVendors,
        },
        props: {
            form: {
                type: Object,
                required: true,
            },
            verifyOutpatient: {
                type: Object,
                default: () => ({}),
            },
            disabled: Boolean,
            disabledAdd: Boolean,
            // 判断开出来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            /**
             是否展示医保登记
             */
            showMedicalFeeGrade: {
                type: Boolean,
                default: true,
            },
            // 是否需要验证库存
            needCheckStock: {
                type: Boolean,
                default: true,
            },

            showDetailPrice: {
                type: Boolean,
                default: true,
            },

            showTotalPrice: {
                type: Boolean,
                default: true,
            },
            formsLength: Number,
            formIndex: Number,
            status: Number,
            patientInfo: Object,
            medicalRecord: Object,
            treatOnlineClinicId: [Number, String],
            departmentId: String,
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            templateManagerVersion: {
                type: Number,
                default: 0,
            },
            psychotropicNarcoticEmployee: {
                type: Object,
                default() {
                    return {};
                },
            },
            showCooperationPharmacy: Boolean,
            isConsultation: Boolean,
            doctorId: {
                type: String,
                default: '',
            },
            // 判断是否是处方模板
            isFromTemplate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                CATEGORY_TYPE_ENUM,
                categoryRange,
                ROLE_DOCTOR_ID,
                ROLE_DOCTOR_ASSIST_ID,
                dialogVisible: false,
                expandSpecialRequirement: false,
                groupList: [],
                isEexcuted: false, // 处方是否执行过
                showGoodsSelectDialog: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'cooperationPharmacyList',
                'westernMedicineConfig',
            ]),
            ...mapGetters('outpatientConfig', [
                'outpatientEmployeeConfig',
            ]),

            showPsychotropicNarcoticEmployee() {
                return [
                    PsychotropicNarcoticTypeEnum.JING_1,
                    PsychotropicNarcoticTypeEnum.JING_2,
                    PsychotropicNarcoticTypeEnum.MA_ZUI,
                    PsychotropicNarcoticTypeEnum.DU,
                ].includes(this.form.psychotropicNarcoticType);
            },

            curCommonDialog() {
                if (this.templateManagerVersion === 1) return CommonPrescriptionHospitalDialog;
                return CommonPrescriptionDialog;
            },

            translateH() {
                if (this.formsLength === 1) return '';
                return numToChinese(this.formIndex + 1);
            },
            // 空处方删除不需要确认弹窗
            deleteNeedConfirm() {
                return this.form.prescriptionFormItems.some((item) => {
                    return item.goodsId || item.name;
                });
            },
            prescriptionTotal() {
                const {
                    chargeStatus,
                    receivedPrice,
                    receivableFee,
                    refundedFee,
                } = this.form;

                // 全收（包含退单、退费、部分退）显示实收，未收、部分收显示原价
                // form 上已收、部分收、部分退状态都是1，如果(应收 === 实收 + 已退)就是全收了
                if (
                    chargeStatus > ChargeFormStatusEnum.CHARGED ||
                    (
                        chargeStatus === ChargeFormStatusEnum.CHARGED &&
                        sum(receivableFee) === sum(receivedPrice, Math.abs(refundedFee))
                    )
                ) {
                    return receivedPrice;
                }
                // 未收部分收显示原价
                let total = 0;
                const _arr = [];
                this.form.prescriptionFormItems.forEach((item) => {
                    if (item.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE) {
                        // 有议价情况直接用currentUnitPrice，没有用unitPrice 计算
                        const unitPrice = getItemUnitPrice(item);
                        _arr.push(itemCountPrice(unitPrice, item.unitCount));
                        _arr.push(item.fractionPrice || 0);
                    }
                });
                total = sum(..._arr);
                return total || 0;
            },
            isCharged() {
                return this.form.chargeStatus > 0;
            },
            disabledForm() {
                return this.disabled || this.isCharged;
            },

            hasSpecialRequirement() {
                return this.form.prescriptionFormItems.some((item) => {
                    return item.specialRequirement || item.expandSpecialRequirement;
                });
            },
            showBasicDrugReminder() {
                const { basicDrugReminder } = this.outpatientEmployeeConfig;
                const { infusionSwitch } = basicDrugReminder || {};
                return this.$abcSocialSecurity.config.isGuizhou && infusionSwitch === 1;
            },
        },
        watch: {
            groupList: {
                handler (val) {
                    this.transPrescriptionList(val);
                },
                deep: true,
            },
        },
        created() {
            this.transGroupList();
            this.$store.dispatch('initDoctorWesternPRRemarks');
        },
        methods: {
            /**
             * @desc 转化成 分组
             * <AUTHOR>
             * @date 2018/12/06 15:36:23
             */
            transGroupList() {
                const list = this.form.prescriptionFormItems;
                const cacheMap = new Map();
                this.isEexcuted = list.some((x) => x.executeStatus === ExecuteStatus.FINISHED);

                list.forEach((item) => {
                    const groupItem = cacheMap.get(item.groupId);
                    if (groupItem) {
                        groupItem.list.push(item);
                    } else {
                        cacheMap.set(item.groupId, {
                            groupId: item.groupId,
                            usage: item.usage,
                            freq: item.freq,
                            days: item.days,
                            ivgtt: item.ivgtt,
                            ivgttUnit: item.ivgttUnit,
                            list: [ item ],
                            disabled: this.isEexcuted,
                        });
                    }
                });
                this.groupList = [];
                cacheMap.forEach((item) => {
                    this.groupList.push(item);
                });

                if (this.groupList.length === 0) {
                    this.groupList.push({
                        groupId: 1,
                        usage: '静脉滴注',
                        freq: 'qd',
                        days: null,
                        ivgtt: 60,
                        ivgttUnit: '滴/分钟',
                        list: [],
                    });
                }
            },

            /**
             * @desc 转化成 提交数据结构
             * <AUTHOR>
             * @date 2018/12/13 15:23:18
             */
            transPrescriptionList(groupList) {
                const list = [];
                groupList.forEach((group, pIndex) => {
                    group.list.forEach((item) => {
                        item.groupId = pIndex + 1;
                        list.push(item);
                    });
                });
                this.form.prescriptionFormItems = list;
            },

            /**
             * @desc 该处方添加组，位置是index
             * <AUTHOR>
             * @date 2018/04/12 14:35:10
             */
            addGroupHandler(index, groupId) {
                index = index || 0;
                const usage = this.groupList[index].usage || '';
                const freq = this.groupList[index].freq || '';
                const days = this.groupList[index].days || '';
                let ivgtt = '';
                let ivgttUnit = '';
                if (usage === '静脉滴注') {
                    ivgtt = 60;
                    ivgttUnit = '滴/分钟';
                }
                this.groupList.splice(index + 1, 0, {
                    groupId: groupId + 1,
                    usage,
                    freq,
                    days,
                    ivgtt,
                    ivgttUnit,
                    list: [],
                });
                this.$nextTick(() => {
                    this.$el.querySelectorAll('.prescription-footer .medicine-autocomplete input')[index + 1].focus();
                });
            },

            /**
             * @desc 改变组号需要重排
             * <AUTHOR>
             * @date 2022-01-07 14:54:25
             */
            changeGroupIdHandler() {
                this.groupList.sort((a, b) => {
                    return a.groupId - b.groupId;
                });
            },

            /**
             * @desc index
             * <AUTHOR>
             * @date 2018/04/12 15:57:37
             */
            deleteGroupHandler(index) {
                this.groupList.splice(index, 1);
            },

            /** ----------------------------------------------------------------------
             *  删除西药药处方的按钮
             */
            deletePres() {
                this.$emit('close', this.formIndex);
            },

            // 保存模板
            saveCommon() {
                if (this.form.prescriptionFormItems.length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '处方药品为空，不能保存为模板',
                    });
                    return false;
                }
                this.dialogVisible = true;
            },

            expandSpecialRequirementHandle(val) {
                this.expandSpecialRequirement = val;
            },

            onClickPsychotropicNarcoticEmployee(value) {
                if (!this.isOpenSource) return;
                this.$abcEventBus.$emit('psychotropic-narcotic-info-input', value);
            },

            async quickSelect(goodsList) {
                const infusionTable = this.$refs.infusionTable[this.groupList.length - 1];
                goodsList.forEach(async (goods) => {
                    const prescriptionItem = getPrescriptionItemStruct(goods);
                    infusionTable.selectWesternMedicine(prescriptionItem);
                    await completePrescriptionItem({
                        goods,
                        isInfusion: true,
                        prescriptionItem,
                        patientInfo: this.patientInfo,
                        physicalExamination: this.medicalRecord.physicalExamination,
                        prescriptionFormItems: this.form.prescriptionFormItems,
                        westernMedicineConfig: this.westernMedicineConfig,
                    });
                });
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    infusionTable.$refs.medicineAutoComplete.focusInput(this);
                }, 400);
            },
            outpatientFeeChange() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '输注处方',
                    needCalcFee: true,
                });
            },
            changePsychotropicNarcoticType() {
                this.$emit('queryVerify');
                this.outpatientFeeChange();
            },
        },
    };
</script>
