<template>
    <abc-autocomplete
        ref="abcAutocomplete"
        v-model="westernMedicineCache"
        :disabled="disabled"
        placeholder="输入药名或拼音码"
        size="medium"
        class="medicine-autocomplete"
        custom-class="wm-autocomplete-suggestion"
        :placement="placement"
        :delay-time="10"
        :fetch-suggestions="queryWesternMedicineAsync"
        :async-fetch="true"
        :focus-show="true"
        :popper-options="{
            boundariesElement: 'body',
        }"
        :close-on-click-outside="handleCloseOnClickOutside"
        :data-cy="$attrs['data-cy'] || 'medicine-autocomplete'"
        @focus="focusHandle"
        @blur="$emit('blur')"
        @enterEvent="selectWesternMedicine"
    >
        <template slot="suggestion-header">
            <div class="suggestion-title">
                <div v-if="isShowGoodsAutocompleteShortId" class="goods-code">
                    商品编码
                </div>
                <div class="medicine-name-group">
                    药名
                </div>
                <div class="spec">
                    规格
                </div>
                <div v-if="isOpenSource" class="display-inventory">
                    库存
                </div>
                <div v-if="showDetailPrice" class="display-price">
                    价格
                </div>
                <div class="manufacturer">
                    <manufacturer-select
                        v-if="isSupportManufacturerFilter"
                        v-model="selectedManufacturer"
                        :manufacturer-options="manufacturerOptions"
                        size="tiny"
                        placeholder="厂家"
                        @change="handleManufacturerChange"
                    ></manufacturer-select>
                    <template v-else>
                        厂家
                    </template>
                </div>
                <div class="manufacturer">
                    最近效期
                </div>
                <div class="medical-fee-grade">
                    医保
                </div>
                <!--<div class="medical-restriction">-->
                <!--    医保限制-->
                <!--</div>-->
                <div class="remark">
                    备注
                </div>
            </div>
        </template>

        <template slot="suggestions" slot-scope="props">
            <dt
                slot="reference"
                class="suggestions-item"
                :class="{
                    selected: !props.suggestion.disabled && props.index === props.currentIndex,
                    'is-tips': props.suggestion.isTips,
                    'not-source': isOpenSource && props.suggestion.noStocks,
                }"
                :disabled="props.suggestion.disabled"
                data-cy="medicine-suggestions-item"
                @mousedown="selectWesternMedicine(props.suggestion)"
            >
                <div v-if="isShowGoodsAutocompleteShortId" class="goods-code" :title="props.suggestion.shortId || ''">
                    <template v-if="!needCheckStock || (needCheckStock && !props.suggestion.noStocks)">
                        {{ props.suggestion.shortId || '' }}
                    </template>
                </div>

                <abc-flex gap="small" class="medicine-name-group" :title="props.suggestion | goodsHoverTitle">
                    <abc-text style="max-width: calc(100% - 22px);" class="ellipsis">
                        {{ nameTitle(props.suggestion) }}
                    </abc-text>

                    <biz-goods-info-tag-group
                        :product-info="props.suggestion"
                        :is-fold-tags="true"
                        style="display: inline-flex; flex: 1;"
                    >
                    </biz-goods-info-tag-group>
                </abc-flex>

                <div class="spec gray" :title="props.suggestion.displaySpec || ''">
                    <template v-if="!needCheckStock || (needCheckStock && !props.suggestion.noStocks)">
                        {{ props.suggestion.displaySpec || '' }}
                    </template>
                </div>

                <div v-if="isOpenSource" class="display-inventory" :title="displayInventory(props.suggestion)">
                    {{ displayInventory(props.suggestion) }}
                </div>

                <div v-if="showDetailPrice" class="display-price" :title="displayPrice(props.suggestion)">
                    <template v-if="!needCheckStock || (needCheckStock && !props.suggestion.noStocks)">
                        {{ displayPrice(props.suggestion) }}
                    </template>
                </div>

                <div class="gray manufacturer ellipsis" :title="props.suggestion.manufacturer">
                    <template v-if="!needCheckStock || (needCheckStock && !props.suggestion.noStocks)">
                        {{ props.suggestion.manufacturer }}
                    </template>
                </div>

                <div class="gray manufacturer" :class="isExpired(props.suggestion.minExpiryDate) ? 'warn' : ''" :title="props.suggestion.minExpiryDate || ''">
                    {{ props.suggestion.minExpiryDate || '' }}
                </div>

                <div v-abc-title="displayMedicalFeeGrade(props.suggestion)" class="medical-fee-grade gray">
                </div>

                <div class="gray remark ellipsis" :title="props.suggestion.remark">
                    <span> {{ props.suggestion.remark }}</span>
                </div>
            </dt>
        </template>

        <template v-if="icon" #prepend>
            <abc-icon icon="s-add-line-medium" color="var(--abc-color-T3)"></abc-icon>
        </template>
    </abc-autocomplete>
</template>

<script type="text/ecmascript-6">
    // api
    import GoodsV3API from 'api/goods/index-v3.js';
    import * as repository from 'MfFeEngine/repository';

    import {
        formatMoney, getSpec,
    } from 'src/filters/index';
    import { mapGetters } from 'vuex';
    import { optimizePopoverPosition } from 'utils/dom.js';
    import {
        getPrescriptionItemStruct, completePrescriptionItem,
    } from 'views/layout/prescription/utils.js';
    import { SearchSceneTypeEnum } from 'views/common/enum.js';
    import { isDateExpired } from '@/utils';
    import { PharmacyTypeEnum } from '@abc/constants';
    import { medicalFeeGradeFormatStr } from '@/filters';
    import BizGoodsInfoTagGroup from '@/components-composite/biz-goods-info-tag-group/index.js';
    import ManufacturerSelect from 'views/inventory/common/manufacturer-select/index.vue';
    import useAutoCompleteManufacturerSelect from 'views/inventory/common/manufacturer-select';

    export default {
        name: 'MedicineAutocomplete',
        components: {
            BizGoodsInfoTagGroup, ManufacturerSelect,
        },
        props: {
            disabled: Boolean,
            placeholder: String,
            medicalRecord: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            icon: {
                type: Boolean,
                default: true,
            },
            showDetailPrice: {
                type: Boolean,
                default: true,
            },
            placement: {
                type: String,
                default: 'bottom-start',
            },
            isInfusion: {
                type: Boolean,
                default: false,
            },
            repeatIndex: {
                type: Number,
                default: -1,
            },
            index: Number,

            patientInfo: {
                type: Object,
                default: () => {
                    return {
                        age: {
                            year: '',
                            month: '',
                        },
                        sex: '',
                        weight: '',
                    };
                },
            },

            treatOnlineClinicId: [Number, String],
            pharmacyNo: [Number, String],
            pharmacyType: [Number, String],

            prescriptionFormItems: Array,
            needCheckStock: Boolean,

            showSelfPay: {
                type: Boolean,
                default: false,
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            sceneType: {
                type: Number,
                default: SearchSceneTypeEnum.outpatient,
            },
            wardAreaId: {
                type: String,
                default: undefined,
            },
            departmentId: {
                type: String,
                default: undefined,
            },
            // 是否支持厂家筛选
            isSupportManufacturerFilter: {
                type: Boolean,
                default: true,
            },
        },
        setup() {
            const {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            } = useAutoCompleteManufacturerSelect();

            return {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            };
        },

        data() {
            return {
                westernMedicineCache: this.placeholder || '',
                refAutoComplete: null,
            };
        },

        computed: {
            ...mapGetters([
                'goodsConfig',
                'currentClinic',
                'userInfo',
                'westernMedicineConfig',
                'chainBasic',
                'disableNoStockGoods',
                'multiPharmacyCanUse',
                'isEnableListingPrice',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isShowGoodsAutocompleteShortId() {
                return this.viewDistributeConfig.Inventory.isShowGoodsAutocompleteShortId;
            },

            // 如果没有为空的input，默认是聚焦到天数上的，这里聚焦到单次剂量
            prescriptionFocusDosage() {
                if (!this.chainBasic) return false;
                if (!this.chainBasic.outpatient) return false;
                return !!this.chainBasic.outpatient.prescriptionFocusDosage;
            },
        },
        watch: {
            westernMedicineCache: {
                handler (val) {
                    if (!val.trim()) {
                        this.$emit('clear');
                    }
                    this.clearManufacturerData();
                },
            },
        },
        created() {
            this._outpatient_service_instance = repository.OutpatientService.getInstance();
            this._goods_repo_instance = repository.GoodsRepositoryService.getInstance();
        },
        mounted() {
            this.refAutoComplete = this.$refs.abcAutocomplete;
        },
        methods: {
            getSpec,
            isExpired(minExpiryDate) {
                const month = this.goodsConfig?.stockGoodsConfig?.stockWarnGoodsWillExpiredMonth;
                return isDateExpired(minExpiryDate, month);
            },
            nameTitle(item) {
                if (item.medicineCadn && item.name) {
                    return `${item.medicineCadn}(${item.name})`;
                }
                return item.medicineCadn || item.name;

            },

            /**
             * @desc
             * <AUTHOR>
             * @date 2019/04/11 18:21:20
             * @params
             * @return
             */
            displayPrice(item) {
                if (item.packagePrice && item.packageUnit) {
                    return `${formatMoney(item.packagePrice || 0, false)}/${item.packageUnit}`;
                }
                return '';
            },

            /**
             * @desc display autocomplete 下拉框 库存信息
             * <AUTHOR>
             * @date 2018/08/02 15:11:50
             */
            displayInventory(item) {
                if (item.isTips) return '';
                if (item.noStocks) return '无库存';
                let str = '';

                if (item.stockPackageCount) {
                    str += `${item.stockPackageCount}${item.packageUnit}`;
                }

                if (item.stockPieceCount) {
                    str += `${item.stockPieceCount}${item.pieceUnit}`;
                }

                if (!item.stockPackageCount && !item.stockPieceCount) {
                    str += `${item.stockPackageCount}${item.packageUnit}`;
                }
                return str;
            },

            /** ----------------------------------------------------------------------
             * 输入时根据输入内容 按药名异步查询西药
             * @param queryString
             * @param callback
             */
            async queryWesternMedicineAsync(queryString, callback) {
                // 解析queryString，使用空格分隔，取第一项为原始keyword，最后一项为parseManufacturer
                let parseManufacturer = '';

                if (queryString && queryString.trim()) {
                    const keyParts = queryString.trim().split(' ').filter((part) => part.trim());
                    if (keyParts.length > 1) {
                        queryString = keyParts[0];
                        parseManufacturer = keyParts[keyParts.length - 1];
                    }
                }

                const clinicId = this.treatOnlineClinicId || this.currentClinic.clinicId;
                const { disabledWithDomainSearchMedicine } = this.viewDistributeConfig.Outpatient;
                let withDomainMedicine = 0;
                if (this.isOpenSource) {
                    withDomainMedicine = disabledWithDomainSearchMedicine ? 0 : +!this.disableNoStockGoods;
                }
                const searchParams = {
                    sceneType: this.sceneType,
                    clinicId,
                    wardAreaId: this.wardAreaId,
                    departmentId: this.departmentId,
                    pharmacyType: this.pharmacyType,
                    pharmacyNo: this.pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY ? this.pharmacyNo : null,
                    keyword: queryString.trim(),
                    jsonType: [
                        {
                            type: 1, subType: [1, 3],
                        },
                        { type: 2 },
                    ],
                    sex: this.patientInfo.sex,
                    age: this.patientInfo.age,
                    chiefComplaint: this.medicalRecord.chiefComplaint,
                    diagnosis: this.medicalRecord.diagnosis,
                    goodsIds: queryString.trim() ? undefined : this.prescriptionFormItems.map((item) => {
                        return item.goodsId;
                    }).filter((item) => item),
                    offset: 0,
                    limit: 50,
                    withDomainMedicine,
                    intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                };
                let data;


                // 是否启用远程搜索
                const useRemoteSearch = this._goods_repo_instance?.searchConfig?.useRemoteSearchGoodsAPI ?? true;

                if (!useRemoteSearch) {
                    data = await this._outpatient_service_instance.searchGoods(
                        searchParams,
                    );
                } else {
                    const res = await GoodsV3API.searchGoods(searchParams);
                    data = res?.data;
                }
                if (!data) {
                    callback([]);
                    return false;
                }

                if (this.isOpenSource) {
                    if (this.disableNoStockGoods && !this.multiPharmacyCanUse) {
                        // "不允许开无库存药品" && 没有打开多药房 则需要禁用库存不足的选择
                        data.list = data.list.map((item) => {
                            item.disabled = item.noStocks || (item.stockPackageCount + item.stockPieceCount) <= 0;
                            return item;
                        });
                    }

                    const tipsIndex = data.list.filter((item) => {
                        return !item.noStocks;
                    }).length;

                    if (tipsIndex !== data.list.length) {
                        data.list.splice(tipsIndex, 0, {
                            disabled: true,
                            isTips: true,
                            medicineCadn: '以下药品不在库存中',
                        });
                    }
                }

                // 处理厂家筛选
                const filteredList = data.list || [];
                if (this.isSupportManufacturerFilter && this.createManufacturerOptions) {
                    this.createManufacturerOptions(filteredList);
                }

                // 如果选择了厂家，则过滤结果
                if (this.isSupportManufacturerFilter && this.filterManufacturer) {
                    data.list = this.filterManufacturer(filteredList);
                }

                // 透传 data.query.keyword 保证请求回来和搜索key一致
                let keyword = '';
                if (data.query) {
                    keyword = data.query.keyword || data.query.key;
                }
                callback(data.list, keyword);
            },

            /** ----------------------------------------------------------------------
             * autocomplete 选择西药 || 通过回车录入西药
             * @param selected
             */
            async selectWesternMedicine(selected) {
                if (!selected) return false;
                if (selected.disabled) return false;

                this.westernMedicineCache = '';
                /**
                 * @desc 解决输入法问题
                 * 清空 autocomplete input value
                 * <AUTHOR>
                 * @date 2019/06/27 10:15:27
                 */
                this.$el.querySelector('input').value = '';
                const prescriptionItem = getPrescriptionItemStruct(selected);
                this.$emit('select', prescriptionItem);

                await completePrescriptionItem({
                    goods: selected,
                    prescriptionItem,
                    patientInfo: this.patientInfo,
                    physicalExamination: this.medicalRecord.physicalExamination,
                    isInfusion: this.isInfusion,
                    prescriptionFormItems: this.prescriptionFormItems,
                    westernMedicineConfig: this.westernMedicineConfig,
                });

                this.focusInput(this.$parent);
            },

            getTableTrs($parent) {
                let $el = null;
                if ($parent) {
                    $el = $($parent);
                } else if (this.isInfusion) {
                    $el = $(this.$el).parents('.infusion-prescription-group').find('.prescription-table');
                } else {
                    $el = $(this.$el).parents('.prescription-table-wrapper').find('.prescription-table');
                }
                return $el.find('.table-tr');
            },

            /**
             * @desc 聚焦到第一个没有填的input
             * <AUTHOR>
             * @date 2019/01/23 11:09:03
             */
            focusInput($parent) {
                if (!$parent) return false;

                this.$nextTick(() => {
                    const $tableTrs = this.getTableTrs();
                    let $tableTr = $tableTrs.eq($tableTrs.length - 1);
                    let $inputs = [];

                    if (this.repeatIndex > -1) {
                        // 有重复项目聚焦到它的 数量输入框上
                        $tableTr = $tableTrs.eq(this.repeatIndex);
                    } else {
                        // 修改
                        if ($parent.$refs.tableTr) {
                            let trIndex = $parent.$refs.tableTr.length - 1;
                            if (this.index !== undefined) trIndex = this.index;
                            $tableTr = this.getTableTrs($parent.$el).eq(trIndex);
                            $inputs = $tableTr.find('.table-td').not('.group-select')
                                .find('input.abc-input__inner').not(':disabled');

                        } else {
                            // 输入药品新增
                            $inputs = $tableTr.find('.table-td').not('.group-select')
                                .find('input.abc-input__inner').not(':disabled');
                        }
                    }

                    const blankInputs = $.grep($inputs, (it) => {
                        return !it.value && it.tabIndex !== -1;
                    });

                    // 如果没有为空的input，默认聚焦到天数上
                    // prescriptionFocusDosage 为 1的时候，优先聚焦到单次剂量
                    $inputs = $tableTr
                        .find('.table-td.days, .table-td.count')
                        .find('input.abc-input__inner, .fraction-display.abc-input__inner').not(':disabled');
                    if (this.prescriptionFocusDosage &&
                        (blankInputs.length === 0 || blankInputs[ 0 ] === $inputs[0])) {
                        $inputs = $tableTr
                            .find('.table-td.dosage')
                            .find('input.abc-input__inner, .fraction-display.abc-input__inner');
                        $inputs && $inputs.length && $inputs[0].focus();
                        return;
                    }

                    if (blankInputs.length) {
                        blankInputs[ 0 ].focus();
                    } else {
                        if (blankInputs.length === 0) {
                            // 输入药品新增
                            $inputs && $inputs.length && $inputs[0].focus();
                        }
                    }
                });
            },

            focusHandle() {
                this.$emit('focus', this);
                if (this.isOpenSource) {
                    optimizePopoverPosition(280, this.$el);
                }
            },

            displayMedicalFeeGrade(suggestion) {
                const _arr = [];
                const medicalFeeGradeStr = medicalFeeGradeFormatStr(suggestion, {
                    shebaoCardInfo: this.shebaoCardInfo,
                });
                if (medicalFeeGradeStr) {
                    _arr.push(medicalFeeGradeStr);
                }
                if (suggestion.shebao) {
                    if (this.isEnableListingPrice && suggestion.shebao.listingPrice) {
                        _arr.push('挂网');
                    }
                    if (suggestion.shebao.priceLimit) {
                        _arr.push('限价');
                    }
                }
                if (suggestion.restriction) {
                    _arr.push(suggestion.restriction);
                }

                return _arr.join('/');
            },

            handleManufacturerChange() {
                // 如果不支持厂家筛选，直接返回
                if (!this.isSupportManufacturerFilter) {
                    return;
                }

                // 配合focus-show重新触发查询
                this.$nextTick(() => {
                    this.refAutoComplete?.focus?.();
                });
            },

            handleCloseOnClickOutside(e) {
                const eventPath = e?.path || (e?.composedPath?.());

                // 检查是否点击了厂家筛选下拉框
                if (this.isSupportManufacturerFilter && eventPath?.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className.includes('goods-auto-complete-manufacturer-select');
                })) {
                    return false;
                }

                // 厂家筛选关闭面板时重置搜索结果
                if (this.selectedManufacturer) {
                    // 清除选中厂家
                    this.clearManufacturerData(true);
                    // 更新列表数据
                    this.queryWesternMedicineAsync(this.westernMedicineCache, (list) => {
                        // 手动触发下拉框更新-面板不展示
                        this.refAutoComplete.isFocus = false;
                        this.refAutoComplete.callbackHandler(list);
                    });
                }
                this.$emit('closePanel');

                return true;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .wm-autocomplete-suggestion {
        min-width: 976px;
        margin-left: -1px !important;

        .suggestion-title {
            display: flex;
            align-items: center;
            padding: 0 10px;

            &.second {
                background-color: $S2;
                border-top: 1px solid $P6;
                border-bottom: 1px solid $P6;
                border-radius: 0;
            }
        }

        .abc-scrollbar-wrapper {
            overflow-y: scroll;

            .suggestions-item {
                padding: 0 0 0 10px;
            }
        }

        .suggestions-item > div {
            font-size: 13px;
            line-height: 18px;
        }

        .suggestion-title,
        .suggestions-item {
            .goods-code {
                flex: none !important;
                width: 70px;
                padding-right: 6px;
            }

            .medicine-name-group {
                flex: 1;
                font-size: 14px;
            }

            .spec {
                width: 94px;
                padding-left: 6px;
            }

            .display-inventory {
                width: 70px;
                padding-left: 6px;
                text-align: right;
            }

            .display-price {
                width: 80px;
                padding-right: 12px;
                padding-left: 6px;
                text-align: right;
            }

            .medical-fee-grade {
                width: 100px;
                padding-left: 6px;
                text-align: left;
            }

            .medical-restriction {
                width: 160px;
                padding-left: 6px;
            }

            .manufacturer {
                width: 100px;
                padding-left: 12px;

                +.warn {
                    color: $Y2;
                }
            }

            .remark {
                width: 76px;
                padding-left: 12px;
            }

            .device-name {
                font-size: 12px;
                color: $T2;
            }

            &.selected {
                .device-name,
                .compose-tag {
                    color: #ffffff;
                }
            }
        }
    }
</style>
