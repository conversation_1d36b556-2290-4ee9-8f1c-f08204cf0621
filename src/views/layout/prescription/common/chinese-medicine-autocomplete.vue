<template>
    <abc-autocomplete
        ref="chinese-autocomplete-suggestion"
        v-model="chineseMedicineCache"
        custom-class="chinese-autocomplete-suggestion"
        class="medicine-autocomplete"
        :inner-width="innerWidth"
        :size="size"
        :async-fetch="true"
        :delay-time="10"
        :fetch-suggestions="queryChineseMedicineAsync"
        :placeholder="inputPlaceholder || '输入药名或拼音码'"
        :focus-show="focusShow"
        :close-on-click-outside="handleCloseOnClickOutside"
        :data-cy="$attrs['data-cy'] || 'chinese-medicine-autocomplete'"
        @left="handleLeft"
        @right="handleRight"
        @up="handleUp"
        @down="handleDown"
        @focus="focusHandler()"
        @blur="blurHandler"
        @change="
            (medicineCadn) => {
                $emit('change', medicineCadn);
            }
        "
        @enterEvent="selectChineseMedicine"
    >
        <template #suggestion-header>
            <div class="suggestion-title" @mousedown.stop="">
                <div v-if="isShowGoodsAutocompleteShortId" class="goods-code">
                    商品编码
                </div>
                <div
                    class="medicine-name-group"
                    :title="`转为${changedSpec}`"
                    :class="{ 'support-mix': supportMix }"
                    @mousedown.stop="handleMouseDownChangeSpec"
                    @click.stop="handleChangeSpec"
                >
                    {{ curSpecName }}名
                    <abc-icon
                        v-if="supportMix"
                        icon="qiehuan"
                        size="12"
                    ></abc-icon>
                </div>
                <div class="extend-spec">
                    规格
                </div>
                <div v-if="isOpenSource" class="display-inventory">
                    库存
                </div>
                <div v-if="showDetailPrice" class="display-price">
                    价格
                </div>
                <div class="manufacturer">
                    <manufacturer-select
                        v-if="isSupportManufacturerFilter"
                        v-model="selectedManufacturer"
                        :manufacturer-options="manufacturerOptions"
                        size="tiny"
                        placeholder="产地厂家"
                        @change="handleManufacturerChange"
                    ></manufacturer-select>
                    <template v-else>
                        产地厂家
                    </template>
                </div>
                <div class="min-expiry-date">
                    最近效期
                </div>
                <div class="medical-fee-grade">
                    医保
                </div>
                <!--<div class="medical-restriction">-->
                <!--    医保限制-->
                <!--</div>-->
                <div class="remark">
                    备注
                </div>
            </div>
        </template>

        <template #suggestions="props">
            <dt
                slot="reference"
                class="suggestions-item"
                :class="{
                    selected: !props.suggestion.disabled && props.index === props.currentIndex,
                    'is-tips': props.suggestion.isTips,
                    'not-source': isOpenSource && props.suggestion.noStocks,
                }"
                :disabled="props.suggestion.disabled"
                data-cy="suggestions-item"
                @mousedown="selectChineseMedicine(props.suggestion, $event)"
            >
                <div v-if="props.suggestion.isTips">
                    以下药品不在库存中
                </div>
                <template v-else>
                    <div v-if="isShowGoodsAutocompleteShortId" class="goods-code" :title="props.suggestion.shortId || ''">
                        <template v-if="!needCheckStock || (needCheckStock && !props.suggestion.noStocks)">
                            {{ props.suggestion.shortId || '' }}
                        </template>
                    </div>

                    <div
                        class="medicine-name-group"
                    >
                        <div class="medicine-cadn ellipsis" :title="props.suggestion | goodsHoverTitle">
                            {{ props.suggestion.medicineCadn }}
                        </div>
                        <div class="alias-name ellipsis" :title="props.suggestion.aliasName">
                            {{ props.suggestion.aliasName || '' }}
                        </div>
                        <biz-goods-info-tag-group
                            :product-info="props.suggestion"
                            :is-fold-tags="true"
                            style="display: inline-flex; flex: 1; margin-left: 6px;"
                        >
                        </biz-goods-info-tag-group>
                    </div>
                    <div class="extend-spec gray" :title="props.suggestion.extendSpec">
                        {{ props.suggestion.extendSpec }}
                    </div>

                    <div v-if="isOpenSource" class="display-inventory">
                        {{ props.suggestion | displayInventory }}
                    </div>

                    <div v-if="showDetailPrice" class="display-price">
                        <template v-if="!needCheckStock || (needCheckStock && !props.suggestion.noStocks)">
                            {{ props.suggestion | displayPrice }}
                        </template>
                    </div>
                    <div class="gray manufacturer" :title="props.suggestion.manufacturer">
                        <template v-if="!needCheckStock || (needCheckStock && !props.suggestion.noStocks)">
                            {{ props.suggestion.manufacturer }}
                        </template>
                    </div>

                    <abc-text
                        class="min-expiry-date"
                        :theme="isExpired(props.suggestion.minExpiryDate) ? 'warning' : 'gray'"
                        tag="div"
                        :title="props.suggestion.minExpiryDate || ''"
                    >
                        {{ props.suggestion.minExpiryDate || '' }}
                    </abc-text>

                    <div v-abc-title="displayMedicalFeeGrade(props.suggestion)" class="medical-fee-grade gray">
                    </div>

                    <div class="gray remark ellipsis" :title="props.suggestion.remark">
                        <span> {{ props.suggestion.remark }}</span>
                    </div>
                </template>
            </dt>
        </template>
        <div slot="prepend" class="search-icon">
            <i v-show="!noIcon" class="iconfont cis-icon-plus"></i>
        </div>
    </abc-autocomplete>
</template>

<script type="text/ecmascript-6">
    // api
    import GoodsApi from 'api/goods/index';
    import GoodsV3API from 'api/goods/index-v3.js';
    import * as repository from 'MfFeEngine/repository';

    import {
        formatMoney, medicalFeeGradeFormatStr,
    } from 'src/filters/index';
    import { mapGetters } from 'vuex';
    import { optimizePopoverPosition } from 'utils/dom.js';
    import { getPrescriptionItemStruct } from 'views/layout/prescription/utils.js';
    import { isDateExpired } from '@/utils';
    import BizGoodsInfoTagGroup from 'src/components-composite/biz-goods-info-tag-group';
    import ManufacturerSelect from 'views/inventory/common/manufacturer-select/index.vue';
    import useAutoCompleteManufacturerSelect from 'views/inventory/common/manufacturer-select';

    export default {
        name: 'ChineseMedicineAutocomplete',
        components: {
            BizGoodsInfoTagGroup,
            ManufacturerSelect,
        },
        filters: {
            displayPrice(medicine) {
                if (medicine.piecePrice) {
                    return `${formatMoney(medicine.piecePrice || 0, false)}/${medicine.pieceUnit || 'g'}`;
                }
                return '';
            },
            displayInventory(item) {
                if (item.isTips) return '';
                if (item.noStocks) return '无库存';

                return `${item.stockPieceCount || 0}${item.pieceUnit || 'g'}`;
            },
        },
        props: {
            disabled: Boolean,
            inputPlaceholder: String,
            medicalRecord: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            icon: {
                type: Boolean,
                default: true,
            },
            showDetailPrice: {
                type: Boolean,
                default: true,
            },
            placement: {
                type: String,
                default: 'bottom-start',
            },
            specification: {
                type: [String],
                default: '',
            },
            innerWidth: {
                type: Number,
                default: 630,
            },
            size: {
                type: String,
                default: 'small',
            },
            focusShow: {
                type: Boolean,
                default: true,
            },
            noIcon: {
                type: Boolean,
                default: false,
            },
            treatOnlineClinicId: [Number, String],
            pharmacyType: [Number, String],
            pharmacyNo: [Number, String],
            defaultKeyword: {
                type: String,
                default: '',
            },

            /**
             * @desc 是否开启词根搜索 （如之前搜索"熟地黄"，但是没有库存的情况下，再次focus，会搜出"地黄"相关）
             * <AUTHOR> Yang
             * @date 2020-09-07 14:19:49
             */
            searchByRoot: {
                type: Boolean,
                default: false,
            },

            patientInfo: {
                type: Object,
                default: () => {
                    return {
                        age: {
                            year: '',
                            month: '',
                        },
                        sex: '',
                        weight: '',
                        mobile: '',
                    };
                },
            },

            prescriptionFormItems: Array,
            needCheckStock: Boolean,
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },

            // 是否支持切换规格
            supportMix: {
                type: [Boolean,Number],
                default: false,
            },
            cmSpec: {
                type: String,
                default: '',
            },
            // 是否支持厂家筛选
            isSupportManufacturerFilter: {
                type: Boolean,
                default: true,
            },
        },
        setup() {
            const {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            } = useAutoCompleteManufacturerSelect();

            return {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            };
        },

        data() {
            return {
                chineseMedicineCache: this.defaultKeyword,
                curSpec: this.cmSpec || this.specification || '中药饮片',
                refAutoComplete: null,
            };
        },

        computed: {
            ...mapGetters([
                'currentClinic',
                'goodsConfig',
                'userInfo',
                'disableNoStockGoods',
                'isEnableListingPrice',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isShowGoodsAutocompleteShortId() {
                return this.viewDistributeConfig.Inventory.isShowGoodsAutocompleteShortId;
            },

            curSpecName() {
                if (!this.curSpec) return '饮片';
                return this.curSpec.replace('中药', '');
            },
            changedSpec() {
                return this.specification === '中药饮片' ? '中药颗粒' : '中药饮片';
            },
        },

        watch: {
            specification(val) {
                if (this.cmSpec) return; // 如果已选择药品,则使用药品的类型
                this.curSpec = val || '中药饮片';
            },
            cmSpec(val) {
                if (!val) return; // 如果未选择药品,则使用选择器选择的类型
                this.curSpec = val || '中药饮片';
            },
            chineseMedicineCache: {
                handler (val) {
                    if (!val.trim()) {
                        this.$emit('clear');
                    }
                    if (typeof this.clearManufacturerData === 'function') this.clearManufacturerData();
                },
            },
        },

        created() {
            this._outpatient_service_instance = repository.OutpatientService.getInstance();
            this._goods_repo_instance = repository.GoodsRepositoryService.getInstance();
        },

        mounted() {
            if (this.isSupportManufacturerFilter) {
                this.refAutoComplete = this.$refs['chinese-autocomplete-suggestion'];
            }
        },

        methods: {
            clearQueryString() {
                this.chineseMedicineCache = '';
            },

            // 防止blur清空已经输入的值
            handleMouseDownChangeSpec() {
                if (!this.supportMix) return;
                this._isChangeSpec = true;
            },

            handleChangeSpec() {
                if (!this.supportMix) return;
                this.curSpec = this.curSpec === '中药饮片' ? '中药颗粒' : '中药饮片';
                this.$nextTick(() => {
                    const $chineseAutocomplete = this.$refs['chinese-autocomplete-suggestion'];
                    $chineseAutocomplete.focus();
                });
            },

            /** ----------------------------------------------------------------------
             * 输入时根据输入内容 异步查询中药数据
             * @param queryString
             * @param callback 进入autocomplete组件的回调函数
             */
            async queryChineseMedicineAsync(queryString, callback) {
                queryString = queryString.trim();

                // 解析queryString，处理数字和厂家
                let parseManufacturer = '';

                if (queryString) {
                    const parts = queryString.split(' ').filter((part) => part.trim());
                    if (parts.length > 1) {
                        queryString = parts[0];
                        parseManufacturer = parts[parts.length - 1];
                    }
                }

                const spec = this.curSpec;

                const { disabledWithDomainSearchMedicine } = this.viewDistributeConfig.Outpatient;
                const clinicId = this.treatOnlineClinicId || this.currentClinic.clinicId;
                const {
                    chiefComplaint,diagnosis,
                } = this.medicalRecord || {};

                const searchParams = {
                    keyword: queryString.trim(),
                    clinicId,
                    spec,
                    chiefComplaint,
                    diagnosis,
                    jsonType: [{
                        type: 1, subType: [2],
                    }],
                    sex: this.patientInfo.sex,
                    age: this.patientInfo.age,
                    goodsIds: queryString.trim() ? undefined : this.prescriptionFormItems.map((item) => {
                        return item.goodsId;
                    }).filter((item) => item),
                    offset: 0,
                    limit: 50,
                    withDomainMedicine: disabledWithDomainSearchMedicine ? 0 : +!this.disableNoStockGoods,
                    pharmacyType: this.pharmacyType,
                    pharmacyNo: this.pharmacyNo,
                    searchByRoot: +this.searchByRoot,
                    needAlias: 1, // 需要别名
                    intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                };

                let data;
                // 是否启用远程搜索
                const useRemoteSearch = this._goods_repo_instance?.searchConfig?.useRemoteSearchGoodsAPI ?? true;

                if (!useRemoteSearch) {
                    data = await this._outpatient_service_instance.searchGoods(
                        searchParams,
                    );
                } else {
                    const res = await GoodsV3API.searchGoods(searchParams);
                    data = res?.data;
                }
                if (!data) {
                    callback([]);
                    return false;
                }

                if (!this.pharmacyType && this.isOpenSource) {
                    if (this.disableNoStockGoods) {
                        data.list = data.list.map((item) => {
                            item.disabled = item.noStocks || (item.stockPackageCount + item.stockPieceCount) <= 0;
                            return item;
                        });
                    }

                    const tipsIndex = data.list.filter((item) => {
                        return !item.noStocks;
                    }).length;

                    if (tipsIndex !== data.list.length) {
                        data.list.splice(tipsIndex, 0, {
                            disabled: true,
                            isTips: true,
                        });
                    }
                }

                // 处理厂家筛选
                const filteredList = data.list || [];
                if (this.isSupportManufacturerFilter && this.createManufacturerOptions) {
                    this.createManufacturerOptions(filteredList);
                }

                // 如果选择了厂家，则过滤结果
                if (this.isSupportManufacturerFilter && this.filterManufacturer) {
                    data.list = this.filterManufacturer(filteredList);
                }

                this._noAutocomplete = data.list.length === 0;

                let queryKey = '';
                if (data.query) {
                    queryKey = data.query.keyword || data.query.key || '';
                }
                // 透传 data.query.key 保证请求回来和搜索key一致
                callback(data.list, queryKey || '');
            },

            focusHandler() {
                this.$emit('focus', this);
                if (this.isOpenSource) {
                    optimizePopoverPosition(280, this.$el);
                }
                $('#medicine-hover-popover').remove();
            },

            blurHandler() {
                if (this._isChangeSpec) {
                    this._isChangeSpec = false;
                    return;
                }
                this.$emit('blur', this);
            },

            /** ----------------------------------------------------------------------
             * autocomplete 选择中药
             * @param medicine
             * @param index
             */
            async selectChineseMedicine(medicine) {
                if (!medicine || medicine.disabled) return false;

                this._isSelectAutocomplete = true;
                medicine.pieceUnit = medicine.pieceUnit || 'g';
                const prescriptionItem = getPrescriptionItemStruct(medicine);
                // 同时获取该药的种类(饮片/颗粒)
                prescriptionItem.cMSpec = medicine.cMSpec;

                this.$emit('select', prescriptionItem, this.index);

                this.chineseMedicineCache = '';
                // 重置规格
                this.curSpec = this.specification;

                // 是诊所药的话会去换一次medicineid
                if (prescriptionItem.goodsId) {
                    if (!this.isOpenSource) return false;
                    const { data } = await GoodsApi.fetchGoods(prescriptionItem.goodsId);
                    data && Object.assign(prescriptionItem, {
                        unitPrice: data.piecePrice || 0,
                    });
                }
            },

            handleLeft(e) {
                this.$emit('left', e);
            },
            handleRight(e) {
                this.$emit('right', e);
            },
            handleUp(e) {
                this.$emit('up', e);
            },
            handleDown(e) {
                this.$emit('down', e);
            },

            // 最近效期
            isExpired(minExpiryDate) {
                const month = this.goodsConfig?.stockGoodsConfig?.stockWarnGoodsWillExpiredMonth;
                return isDateExpired(minExpiryDate, month);
            },

            displayMedicalFeeGrade(suggestion) {
                const _arr = [];
                const medicalFeeGradeStr = medicalFeeGradeFormatStr(suggestion, {
                    shebaoCardInfo: this.shebaoCardInfo,
                    showShort: true,
                });
                if (medicalFeeGradeStr) {
                    _arr.push(medicalFeeGradeStr);
                }
                if (suggestion.shebao) {
                    if (this.isEnableListingPrice && suggestion.shebao.listingPrice) {
                        _arr.push('挂网');
                    }
                    if (suggestion.shebao.priceLimit) {
                        _arr.push('限价');
                    }
                    if (suggestion.restriction) {
                        _arr.push(`限${suggestion.restriction}`);
                    }
                }
                return _arr.join('/');
            },

            handleManufacturerChange() {
                // 如果不支持厂家筛选，直接返回
                if (!this.isSupportManufacturerFilter) {
                    return;
                }

                if (this.focusShow) {
                    // 配合focus-show重新触发查询
                    this.$nextTick(() => {
                        this.refAutoComplete?.focus?.();
                    });
                } else {
                    // 当厂家筛选发生变化时，重新搜索
                    this.queryChineseMedicineAsync(this.chineseMedicineCache, (list) => {
                        // 手动触发下拉框更新
                        if (this.refAutoComplete) {
                            this.refAutoComplete.focus();
                            this.refAutoComplete.callbackHandler(list);
                        }
                    });
                }
            },

            handleCloseOnClickOutside(e) {
                // 如果不支持厂家筛选，直接返回 true
                if (!this.isSupportManufacturerFilter) {
                    this.$emit('closePanel');
                    return true;
                }

                const eventPath = e?.path || (e?.composedPath?.());

                // 检查是否点击了厂家筛选下拉框
                if (eventPath?.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className.includes('goods-auto-complete-manufacturer-select');
                })) {
                    return false;
                }

                // 厂家筛选关闭面板时重置搜索结果
                if (this.selectedManufacturer) {
                    // 清除选中厂家
                    if (typeof this.clearManufacturerData === 'function') {
                        this.clearManufacturerData(true);
                    }
                    // 更新列表数据
                    this.queryChineseMedicineAsync(this.chineseMedicineCache, (list) => {
                        // 手动触发下拉框更新-面板不展示
                        if (this.refAutoComplete) {
                            this.refAutoComplete.isFocus = false;
                            this.refAutoComplete.callbackHandler(list);
                        }
                    });
                }

                this.$emit('closePanel');

                return true;
            },
        },

    };
</script>
