<template>
    <div
        class="medical-fee-grade-td-wrapper"
        :class="{
            'gray': item.payType !== 10,
            'is-disabled': disabled,
            'cant-select-pay-type': !(showPayTypeSelect || showSelfPayHoverInfo),
        }"
    >
        <abc-select
            v-if="showPayTypeSelect"
            v-model="payType"
            :show-value="payTypeShowText"
            class="count-center"
            :custom-class="`pay-type-selector ${showDot ? 'show-dot' : ''}` "
            :input-style="inputStyle"
            :width="width"
            :inner-width="100"
            :tabindex="-1"
            :disabled="disabled"
            @enter="(e, el, value) => $emit('enter', e, el, value)"
        >
            <abc-option :label="medicalFeeGradeOption" :value="ShebaoPayTypeEnum.OVERALL"></abc-option>
            <abc-option label="强制自费" :value="ShebaoPayTypeEnum.SELF"></abc-option>
            <abc-option v-if="isCanSelfFunded" label="处方全自费" :value="ShebaoPayTypeEnum.FORM_SELF"></abc-option>
        </abc-select>

        <div v-else class="pay-type-text" :class="showDot ? 'show-dot' : ''">
            {{ item.productInfo.medicalFeeGrade | medicalFeeGrade2Str }}
            <template v-if="selfPayPropNumber">
                ({{ selfPayPropNumber }})
            </template>
            <!--{{ item.productInfo.covidDictFlag ? '[新冠]' : '' }}-->
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import { medicalFeeGrade2Str } from '@/filters';
    import { getSelfPayPropNumber } from 'views/common/social-info.js';
    import { ShebaoPayTypeEnum } from '@/views/outpatient/constants.js';

    export default {
        name: 'MedicalFeeGradeTd',
        components: {
        },
        props: {
            item: {
                type: Object,
                required: true,
            },
            disabled: [Boolean, Number],
            showDot: {
                type: Boolean,
                default: false,
            },
            width: {
                type: Number,
                default: 30,
            },
            inputStyle: {
                type: Object,
                default: () => {
                    return {
                        padding: '0 2px',
                    };
                },
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            isCanSelfFunded: {
                type: Boolean,
                default: true,
            },
            showPayTypeSelect: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                ShebaoPayTypeEnum,
            };
        },
        computed: {
            payType: {
                get() {
                    const {
                        productInfo,
                        payType,
                    } = this.item;
                    const { shebaoPayMode } = productInfo || {};
                    // payType不为null时，就取payType（10是个账，其他都是统筹）,如果payType为null，就取的goods上面的payMode
                    if (payType !== null) return payType;
                    return shebaoPayMode === 1 ? 10 : 0;
                },
                set(val) {
                    this.$emit('change-pay-type', val);
                },
            },

            selfPayPropNumber() {
                return getSelfPayPropNumber(this.item.productInfo, this.shebaoCardInfo);
            },

            showSelfPayHoverInfo() {
                return this.$abcSocialSecurity.isOpenSocial && (this.$abcSocialSecurity.config.isShandongQingdao);
            },

            payTypeShowText() {
                const {
                    payType,
                    productInfo,
                } = this.item;
                if (payType === 10 || this.payType === 10) return '自';
                return medicalFeeGrade2Str(productInfo.medicalFeeGrade, true);
            },
            medicalFeeGradeOption() {
                if (!this.item || !this.item.productInfo) return '';
                switch (this.item.productInfo.medicalFeeGrade) {
                    case 1:
                        return '医保(甲)';
                    case 2:
                        return '医保(乙)';
                    case 3:
                        return '医保(丙)';
                    default:
                        return '医保';
                }
            },
        },
        methods: {
            handleEditClick() {
                if (!this.showPayTypeSelect) return false;
                this.canEdit = true;
                this.$nextTick(() => {
                    this.$refs.selector.$el.querySelector('input').focus();
                });
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .medical-fee-grade-td-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        border: none;

        &.abc-tipsy--n:hover::before,
        &.abc-tipsy--n:focus::before {
            top: 0;
        }

        &.abc-tipsy--n:hover::after,
        &.abc-tipsy--n:focus::after {
            bottom: 100%;
        }

        .abc-select-wrapper .abc-input__inner {
            height: 100%;
            padding: 3px;
            font-size: 14px;
            border-color: transparent;
            border-radius: 0;
        }

        .iconfont {
            display: none;
        }

        .pay-type-text {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            font-size: 14px;
        }

        .abc-select-wrapper,
        .pay-type-text {
            position: relative;

            &.show-dot::after {
                position: absolute;
                top: 0;
                right: 1px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 4px;
                height: 100%;
                font-size: 14px;
                color: $T2;
                content: '·';
            }
        }

        .abc-select-wrapper {
            height: 100%;

            &:not(.is-disabled):hover,
            &:not(.is-disabled).is-focus {
                &::after {
                    display: none;
                }
            }
        }

        &.gray {
            .pay-type-text,
            .abc-input__inner {
                color: $T2;
            }
        }
    }
</style>
