<template>
    <div
        class="prescription-table-wrapper external-table"
        :class="[`external${ formIndex }`, { 'is-disabled': disabledForm }]"
        data-cy="外治处方"
    >
        <div class="prescription-header">
            <div class="prescription-title">
                <img class="icon-wrapper" src="~assets/images/icon/external.png" alt="" />
                <h5>外治处方{{ translateH }}</h5>

                <template v-if="!isIgnoreExternalUsageType">
                    <abc-select
                        v-model="form.usageType"
                        :width="60"
                        :disabled="disabledForm"
                        class="unit-select"
                        data-cy="pr-external-table-usage-type-select"
                        @enter="enterEvent"
                        @change="changeUsageType"
                    >
                        <abc-option
                            v-for="option in UsageTypeOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                            :data-cy="`option-usage-type-${option.label}`"
                        ></abc-option>
                    </abc-select>

                    <abc-select
                        v-if="form.usageType !== ExternalPRUsageTypeEnum.guaSha"
                        :key="form.usageType"
                        v-model="form.usageSubType"
                        :width="84"
                        :disabled="disabledForm"
                        class="unit-select"
                        data-cy="pr-external-table-usage-sub-type-select"
                        @enter="enterEvent"
                        @change="changeUsageSubType"
                    >
                        <abc-option
                            v-for="option in subTypeOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                            :data-cy="`option-usage-sub-type-${option.label}`"
                        ></abc-option>
                    </abc-select>

                    <abc-select
                        v-if="isXianPeiTie"
                        ref="specSelect"
                        v-model="form.specification"
                        :width="60"
                        :disabled="disabledForm"
                        class="unit-select"
                        data-cy="pr-external-table-spec-select"
                        @change="outpatientFeeChange"
                    >
                        <abc-option label="饮片" value="中药饮片"></abc-option>
                        <abc-option label="颗粒" value="中药颗粒"></abc-option>
                    </abc-select>

                    <abc-tooltip-info
                        v-if="isXianPeiTie"
                        placement="top"
                        style="display: inline-flex; align-items: center; margin-left: 8px;"
                    >
                        <div style="width: 300px;">
                            <p>现配贴和成品贴一样以贴敷项目作为划价依据，现配贴内输入的药味只用于配方记录和打印展示，药味不纳入划价和扣库</p>
                        </div>
                    </abc-tooltip-info>
                </template>
            </div>

            <div class="operation">
                <jing-ma-dropdown
                    v-model="form.psychotropicNarcoticType"
                    data-cy="pr-external-table-jing-ma-dropdown"
                    :is-need-jing-ma-du="false"
                    :disabled="disabledForm"
                    @change="outpatientFeeChange"
                ></jing-ma-dropdown>
                <abc-button
                    v-if="isOpenSource"
                    variant="text"
                    theme="default"
                    size="small"
                    icon="save"
                    data-cy="pr-external-table-save-btn"
                    style="margin-left: 0;"
                    @click="saveCommon"
                >
                </abc-button>
                <trash-button-v2
                    v-if="!disabledForm"
                    :is-confirm="deleteNeedConfirm"
                    data-cy="pr-external-table-trash-btn"
                    @delete="deletePres"
                >
                </trash-button-v2>
            </div>
        </div>

        <div class="prescription-table">
            <div
                v-for="(item, index) in form.prescriptionFormItems"
                :key="item.id || item.keyId"
                class="external-group"
                :data-cy="`external-group-${index}`"
            >
                <div class="external-item-wrapper">
                    <div class="table-group-no">
                        <abc-select
                            v-if="item.sort"
                            v-model="item.sort"
                            :show-value="item.sort"
                            class="count-center"
                            custom-class="group-selector"
                            :width="30"
                            :inner-width="45"
                            :disabled="disabledForm"
                            data-cy="pr-external-group-no-select"
                            @change="changeGroupIdHandler"
                        >
                            <abc-option data-cy="option-1" :label="1" :value="1"></abc-option>
                            <abc-option data-cy="option-2" :label="2" :value="2"></abc-option>
                            <abc-option data-cy="option-3" :label="3" :value="3"></abc-option>
                            <abc-option data-cy="option-4" :label="4" :value="4"></abc-option>
                            <abc-option data-cy="option-5" :label="5" :value="5"></abc-option>
                            <abc-option data-cy="option-6" :label="6" :value="6"></abc-option>
                            <abc-option data-cy="option-7" :label="7" :value="7"></abc-option>
                            <abc-option data-cy="option-8" :label="8" :value="8"></abc-option>
                            <abc-option data-cy="option-9" :label="9" :value="9"></abc-option>
                        </abc-select>
                        <!-- 兼容老数据，老数据 sort 为 0，取 index 排序 -->
                        <abc-text v-else>
                            {{ index + 1 }}
                        </abc-text>
                    </div>
                    <abc-form-item v-if="currentEditIndex === index || !item.name" class="table-td-goods-autocomplete" :required="requiredItem(item)">
                        <goods-autocomplete
                            ref="goodsAutocomplete"
                            size="medium"
                            :icon-size="16"
                            :default-keyword="item.medicineCadn || item.name"
                            :clinic-id="clinicId"
                            :show-price="showDetailPrice"
                            :suggestion-items="_suggestionItems"
                            :placeholder="placeholderText"
                            :json-type="jsonType"
                            auto-suggestion
                            focus-show
                            inner-width="780px"
                            data-cy="pr-external-goods-autocomplete"
                            :query-v3-params="{
                                biz: 'prescriptionExternal',
                                usageType: form.usageType,
                                clinicId: clinicId,
                                sex: patientInfo.sex,
                                age: patientInfo.age,
                                chiefComplaint: medicalRecord.chiefComplaint,
                                diagnosis: medicalRecord.diagnosis,
                                offset: 0,
                                limit: 50,
                                withDomainMedicine: 0,
                                goodsIds: prItemsGoodsIds,
                            }"
                            @focus="focusHandler"
                            @blur="($autocomplete) => blurHandle(item, $autocomplete)"
                            @selectGoods="(goods) => selectGoods(goods, item, index)"
                            @dblclick.native="handleDoubleClick"
                        >
                        </goods-autocomplete>
                    </abc-form-item>
                    <div
                        v-else
                        class="item-detail"
                        :class="{
                            'is-disabled': disabledForm,
                        }"
                        @click="editCurrentItem(index)"
                    >
                        <div
                            class="item-name"
                            :class="{
                                'abc-tipsy abc-tipsy--n': isDisabledGoods(item).flag && !disabledForm,
                            }"
                            :data-tipsy="isDisabledGoods(item).tips"
                        >
                            <p class="ellipsis" style="display: flex; align-items: center;">
                                {{ item.name }}
                                <biz-goods-info-tag-group
                                    v-if="showAntimicrobial(item.productInfo)"
                                    :product-info="item.productInfo"
                                    style="display: inline-flex; margin-left: 4px;"
                                >
                                </biz-goods-info-tag-group>
                            </p>
                            <i
                                v-if="isDisabledGoods(item).flag && !disabledForm"
                                class="iconfont cis-icon-Attention"
                                style=" margin-left: 4px; font-size: 14px;"
                            ></i>
                            <form-item-status v-if="form.chargeStatus" :item="item"></form-item-status>
                        </div>
                    </div>

                    <medical-fee-grade-td
                        v-if="displayMedicalFeeGrade(item)"
                        class="table-td"
                        :item="item"
                        :input-style="{
                            fontSize: '15px'
                        }"
                        :disabled="disabledForm"
                        :shebao-card-info="shebaoCardInfo"
                        :show-pay-type-select="showPayTypeSelect"
                        :width="60"
                        @change-pay-type="val =>$emit('change-pay-type', val, item, form)"
                        @enter="enterEvent"
                    >
                    </medical-fee-grade-td>

                    <div v-if="isTieFu && isNotTreatment(item.productInfo)" class="external-unit-count">
                        <template v-if="disabledForm">
                            <div style=" display: inline-block; width: 47px; text-align: center;">
                                {{ item.externalUnitCount }}
                            </div>
                            <span
                                class="unit"
                                :class="{
                                    'small-font': item.unit && item.unit.length >= 4
                                }"
                                :title="item.unit"
                            >{{ item.unit || '' }}</span>
                        </template>

                        <template v-else>
                            <abc-form-item required>
                                <abc-input
                                    ref="externalUnitCount"
                                    v-model.number="item.externalUnitCount"
                                    v-abc-focus-selected
                                    class="count-center no-border-input"
                                    :width="47"
                                    type="number"
                                    placeholder="每穴"
                                    :config="{
                                        max: 99999,
                                        min: 0,
                                    }"
                                    data-cy="pr-external-unit-count-input"
                                    @input="inputDosageHandle(item)"
                                    @enter="enterEvent"
                                    @change="$emit('queryVerify')"
                                >
                                </abc-input>
                            </abc-form-item>

                            <abc-form-item>
                                <abc-select
                                    v-model="item.unit"
                                    :title="item.unit"
                                    :width="48"
                                    size="small"
                                    no-icon
                                    class="input-select"
                                    :index="index"
                                    :tabindex="-1"
                                    data-cy="pr-external-unit-select"
                                    @change="changeUnit(item)"
                                >
                                    <abc-option
                                        v-for="it in unitArray(item)"
                                        :key="it.name"
                                        :label="it.name"
                                        :value="it.name"
                                        :data-cy="`option-unit-${it.name}}`"
                                    >
                                    </abc-option>
                                </abc-select>
                            </abc-form-item>
                        </template>
                    </div>

                    <div
                        v-if="isOpenSource && showDetailPrice"
                        class="unit-price"
                    >
                        <span v-if="isTieFu">
                            <abc-money :value="(item.unitPrice * (item.externalUnitCount || 1))" is-show-space></abc-money>
                        </span>
                        <span v-else>
                            <abc-money :value="item.unitPrice" is-show-space></abc-money>
                        </span>
                    </div>

                    <abc-flex
                        v-if="!disabledForm"
                        align="center"
                        justify="flex-end"
                        class="btns"
                    >
                        <abc-button
                            v-if="form.prescriptionFormItems.length !== 1"
                            class="prescription-external-table-delete-button"
                            data-cy="pr-external-group-delete-btn"
                            variant="text"
                            theme="danger"
                            size="small"
                            @click="deleteExternalGroup(index)"
                        >
                            删除
                        </abc-button>
                        <abc-button
                            v-if="!disabledAdd"
                            class="add-group"
                            variant="text"
                            size="small"
                            data-cy="pr-external-group-add-btn"
                            @click="addExternalGroup(index,item.sort)"
                        >
                            加一组
                        </abc-button>
                    </abc-flex>
                </div>

                <div v-if="isXianPeiTie" class="external-medicine-wrapper">
                    <chinese-p-r-table
                        :prescription-form="form"
                        :prescription-items.sync="item.externalGoodsItems"
                        :need-check-stock="false"
                        support-mix
                        :show-detail-price="showDetailPrice"
                        :disabled="disabledForm"
                        :patient-info="patientInfo"
                        :medical-record="medicalRecord"
                        :treat-online-clinic-id="clinicId"
                        :department-id="departmentId"
                        @change="outpatientFeeChange"
                    ></chinese-p-r-table>
                </div>

                <draggable
                    v-if="item.acupoints?.length"
                    v-model="item.acupoints"
                    :disabled="disabledForm"
                    ghost-class="abc-sortable-ghost"
                    class="external-acupoint-wrapper"
                    data-cy="external-acupoint-wrapper"
                    @start="startDrag"
                    @end="dragging = false"
                >
                    <transition-group
                        name="list-complete"
                        :css="false"
                        class="acupoint-item-wrapper"
                        tag="div"
                        mode="out-in"
                    >
                        <div
                            v-for="(acupoint, acuIndex) in item.acupoints"
                            :key="`${acupoint.id}_${index}_${acuIndex}`"
                            :class="['acupoint-item', `acupoint-item-${ index }-${ acuIndex}`]"
                            :data-cy="`acupoint-item-${acuIndex}`"
                        >
                            <template v-if="acupoint.name">
                                <abc-popover
                                    v-model="acupoint.showPositionCountPopover"
                                    trigger="manual"
                                    placement="bottom-start"
                                    theme="white"
                                    class="acupoint-position"
                                    :visible-arrow="false"
                                    :popper-style="{ padding: 0 }"
                                >
                                    <abc-input
                                        slot="reference"
                                        v-model="acupoint.position"
                                        v-abc-focus-selected
                                        :disabled="disabledForm"
                                        class="acupoint-position"
                                        :width="32"
                                        :readonly="acupoint.readonly"
                                        data-cy="acupoint-item-position"
                                        @click="changePosition(acupoint, item)"
                                        @input="val => onInputAcuPosition(acupoint, val)"
                                        @change="calcItemUnitCount(item)"
                                    >
                                    </abc-input>
                                    <count-select
                                        :visible.sync="acupoint.showPositionCountPopover"
                                        :current-value="acupoint.position"
                                        :name="acupoint.name"
                                        :type="acupoint.type"
                                        @onOptionClick="val => onChangeCount(item, acupoint, val)"
                                    ></count-select>
                                </abc-popover>
                            </template>

                            <template v-if="currentEditAcupointIndex === `${index }-${ acuIndex}` || !acupoint.name">
                                <acupoint-autocomplete
                                    :default-keyword="acupoint.name"
                                    :placeholder="acupointPlaceholder(item)"
                                    data-cy="pr-external-acupoint-autocomplete"
                                    @focus="currentEditAcupointIndex = `${index }-${ acuIndex}`"
                                    @blur="($autocomplete) => blurAcupointHandle(acupoint, $autocomplete)"
                                    @select="
                                        (newAcupoint) => selectAcupoint(newAcupoint, acupoint, item, index, acuIndex)
                                    "
                                ></acupoint-autocomplete>
                            </template>
                            <div
                                v-else
                                :key="`${acupoint.name }_${ index}`"
                                class="acupoint-name ellipsis"
                                :class="{ 'is-disabled': disabledForm }"
                                data-cy="acupoint-item-name"
                                @click="editCurrentAcupoint(index, acuIndex)"
                            >
                                <span class="ellipsis" :title="acupoint.name">{{ acupoint.name }}</span>

                                <delete-icon
                                    v-if="!disabledForm && currentEditAcupointIndex !== acuIndex && acupoint.name"
                                    data-cy="pr-external-acupoint-item-delete"
                                    @delete="deleteAcupoint(item, acuIndex)"
                                ></delete-icon>
                            </div>

                            <div
                                v-if="!acupoint.name && !disabledForm"
                                class="select-acupoint-btn"
                                data-cy="pr-external-acupoint-item-select"
                                @click="showSelectAcupointDialog(item)"
                            >
                                <i class="iconfont cis-icon-meridian"></i>
                            </div>
                        </div>
                    </transition-group>
                </draggable>

                <div class="external-footer" data-cy="pr-external-footer">
                    <abc-space :size="footerSpaceSize">
                        <div class="dose-count">
                            <abc-form-item :required="item.goodsId && !disabledForm">
                                <abc-input
                                    v-model.number="item.dosage"
                                    v-abc-focus-selected
                                    class="count-center"
                                    :width="82"
                                    :input-custom-style="{ padding: '3px 21px 3px 6px' }"
                                    :disabled="disabledForm"
                                    :max-length="4"
                                    type="number"
                                    size="small"
                                    only-bottom-border
                                    data-cy="pr-external-dosage-input"
                                    @enter="enterEvent"
                                    @input="inputDosageHandle(item)"
                                    @change="$emit('queryVerify')"
                                >
                                </abc-input>
                                <span class="input-append-unit">次</span>
                            </abc-form-item>
                        </div>
                        <div class="freq">
                            <abc-form-item>
                                <select-usage
                                    v-model="item.freq"
                                    placeholder="频率"
                                    :width="72"
                                    :max-length="10"
                                    size="small"
                                    placement="bottom-start"
                                    data-cy="pr-external-freq-input"
                                    only-bottom-border
                                    :readonly="false"
                                    :disabled="disabledForm"
                                    :options="ExternalFreqOptions"
                                    @enter="enterEvent"
                                    @change="outpatientFeeChange"
                                >
                                </select-usage>
                            </abc-form-item>
                        </div>
                        <abc-space
                            v-if="isBasedOnAcupoint && showDosageCount(item.acupoints)"
                            :size="footerSpaceSize + 12"
                            class="total-count"
                        >
                            <p style="line-height: 28px;">
                                每次 {{ dosageCount(item) }} {{ externalUnit(item) }}
                            </p>
                            <p style="line-height: 28px;">
                                共 {{ dosageCount(item) * (item.dosage || 0) }} {{ externalUnit(item) }}
                            </p>
                        </abc-space>
                        <div class="requirement">
                            <abc-form-item>
                                <select-usage
                                    v-model="item.specialRequirement"
                                    placeholder="备注"
                                    :width="114"
                                    li-width="90"
                                    size="small"
                                    placement="bottom-start"
                                    only-bottom-border
                                    data-cy="pr-external-specialRequirement-select"
                                    :readonly="false"
                                    need-hover-text
                                    :disabled="disabledForm"
                                    :options="requirementOptions"
                                    @enter="enterEvent"
                                    @change="outpatientFeeChange"
                                >
                                </select-usage>
                            </abc-form-item>
                        </div>
                    </abc-space>
                    <abc-space :size="footerSpaceSize">
                        <abc-form-item>
                            <abc-select
                                :key="acupointLabel(item)"
                                v-model="item.billingType"
                                :disabled="disabledForm"
                                reference-mode="text"
                                reference-text-justify="end"
                                :width="90"
                                only-bottom-border
                                :inner-width="98"
                                data-cy="pr-external-billingType-select"
                                @change="val => onChangeBillingType(item, val)"
                            >
                                <abc-option
                                    :label="acupointLabel(item)"
                                    :value="ExternalPRBillingTypeEnum.ACUPOINT"
                                ></abc-option>
                                <abc-option label="手动计数" :value="ExternalPRBillingTypeEnum.MANUAL"></abc-option>
                            </abc-select>
                        </abc-form-item>
                        <div class="total-count">
                            <abc-form-item :required="item.goodsId && !disabledForm">
                                <abc-input
                                    v-model.number="item.unitCount"
                                    v-abc-focus-selected
                                    :width="128"
                                    :input-custom-style="{
                                        paddingLeft: '30px',
                                    }"
                                    :disabled="disabledForm"
                                    :max-length="4"
                                    type="number"
                                    size="small"
                                    data-cy="pr-external-unitCount-input"
                                    only-bottom-border
                                    @enter="enterEvent"
                                    @input="inputUnitCount(item)"
                                    @change="$emit('queryVerify')"
                                >
                                    <span slot="prepend" style="color: var(--abc-color-T1);">共</span>
                                    <span
                                        slot="appendInner"
                                        style="color: var(--abc-color-T1);"
                                        :style="{
                                            fontSize: item.unit.length > 3 ? '10px' : '14px',
                                        }"
                                    >{{ item.unit.slice(0, 4) }}</span>
                                </abc-input>
                            </abc-form-item>
                        </div>
                        <div class="total-info">
                            <form-status v-if="disabled" :forms="[item]"></form-status>
                            <div v-if="showTotalPrice" class="total-price">
                                <span v-if="!disabled"><abc-money
                                    :value="totalPrice(item)"
                                    is-show-space
                                    data-cy="pr-external-total-price"
                                ></abc-money></span>
                                <biz-charge-stat-popover
                                    v-else
                                    :title="`外治处方${translateH}`"
                                    :forms="[item]"
                                    style="display: inline-block;"
                                >
                                    <abc-money
                                        :value="totalPrice(item)"
                                        is-show-space
                                        data-cy="pr-external-total-price"
                                    ></abc-money>
                                </biz-charge-stat-popover>
                            </div>
                        </div>
                    </abc-space>
                </div>
            </div>
        </div>

        <component
            :is="curCommonDialog"
            v-if="showDialog"
            v-model="showDialog"
            type="external"
            :prescription-form="form"
        ></component>
    </div>
</template>

<script type="text/ecmascript-6">
    // lib
    import { mapGetters } from 'vuex';
    import Draggable from 'vuedraggable';

    // api
    import GoodsAPI from '@/api/goods';

    // utils
    import {
        isNotNull, numToChinese,
    } from 'utils/index';
    import {
        createGUID, isNumber,
    } from 'utils/index';
    import { isDisabledGoods } from 'utils/validate';
    import { medicalFeeGrade2Str } from 'src/filters/index';
    import localStorage from 'src/utils/localStorage-handler';
    import { optimizePopoverPosition } from 'utils/dom.js';
    import clone from 'utils/clone';

    // 自定义组件
    import DeleteIcon from '../../delete-icon/delete-icon';
    import GoodsAutocomplete from 'src/views/layout/goods-autocomplete/goods-autocomplete';
    import TrashButton from '../common/trash-button';
    import AcupointAutocomplete from '../common/acupoint-autocomplete';
    import CommonPrescriptionDialog from '../common/common-prescription-dialog';
    import SelectUsage from '../../select-group/index.vue';
    import ChinesePRTable from '../common/chinese-pr-table.vue';
    import AcupointSelectDialog from 'src/views/layout/acupoint-select-dialog';
    import JingMaDropdown from '../common/jing-ma-dropdown.vue';
    import CommonPrescriptionHospitalDialog from 'views/layout/prescription/common/common-prescription-hospital-dialog.vue';
    import MedicalFeeGradeTd from '../common/medical-fee-grade-td.vue';
    import BizChargeStatPopover from '@/components-composite/biz-charge-stat-popover';
    import FormStatus from 'src/views/outpatient/common/form-status.vue';
    import FormItemStatus from 'src/views/outpatient/common/form-item-status.vue';
    import CountSelect from 'src/views/layout/prescription/external/acupoint-count-select.vue';
    import BizGoodsInfoTagGroup from '@/components-composite/biz-goods-info-tag-group/index.js';

    // constant
    import {
        GoodsSubTypeEnum,
        GoodsTypeEnum,
        FeeComposeTypeEnum,
    } from '@abc/constants';
    import {
        ExternalPRUsageTypeEnum,
        UsageTypeOptions,
        TieFuSubOptions,
        ZhenCiSubOptions,
        AiJiuSubOptions,
        BaGuanSubOptions,
        TuiNaSubOptions,
        ExternalFreqOptions,
        ExternalTieFuRequirement,
        ExternalZhenJiuRequirement,
        TieFuUsageSubTypeEnum,
        ExternalPRBillingTypeEnum, ExternalAcupointTypeEnum,
    } from '../constant';
    import {
        itemCountPrice,
        sum,
    } from 'utils/calculation.js';
    import {
        clearItemBargainHandler,
        getItemUnitPrice,
        isAllowAddByAntimicrobialDrugManagement,
        getItemComposeChildren,
    } from 'views/outpatient/utils.js';
    import { ChargeFormStatusEnum } from '@/service/charge/constants.js';
    import {
        ExecuteStatus, SearchSceneTypeEnum,
    } from 'views/common/enum';
    import { GoodsTypeEnumStr } from '@abc/constants/src/goods';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import TrashButtonV2 from 'views/layout/prescription/common/trash-button-v2.vue';

    export default {
        name: 'ExternalPrescription',
        components: {
            TrashButtonV2,
            GoodsAutocomplete,
            AcupointAutocomplete,
            DeleteIcon,
            TrashButton,
            Draggable,
            SelectUsage,
            ChinesePRTable,
            JingMaDropdown,
            MedicalFeeGradeTd,
            BizChargeStatPopover,
            FormStatus,
            FormItemStatus,
            CountSelect,
            BizGoodsInfoTagGroup,
        },
        props: {
            // 判断开出来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            showDetailPrice: {
                type: Boolean,
                default: true,
            },
            showTotalPrice: {
                type: Boolean,
                default: true,
            },
            form: {
                type: Object,
                required: true,
            },
            formsLength: Number,
            formIndex: Number,
            disabled: Boolean,
            disabledAdd: Boolean,
            patientInfo: {
                type: Object,
                default() {
                    return {};
                },
            },
            medicalRecord: {
                type: Object,
                default() {
                    return {};
                },
            },
            treatOnlineClinicId: [String, Number],
            departmentId: String,
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            templateManagerVersion: {
                type: Number,
                default: 0,
            },
            /**
             是否展示医保登记
             */
            showMedicalFeeGrade: {
                type: Boolean,
                default: true,
            },
            doctorId: {
                type: String,
                default: '',
            },
            // 判断是否是处方模板
            isFromTemplate: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                ExternalPRUsageTypeEnum,
                ExternalPRBillingTypeEnum,
                UsageTypeOptions,
                ExternalFreqOptions,
                ExternalAcupointTypeEnum,
                queryString: '',

                currentEditIndex: -1,
                currentEditAcupointIndex: -1, // 当前编辑的穴位index

                showDialog: false,
                showAcupointDialog: false,

                dragging: false,
                prescriptionTotal: 0,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'userInfo',
                'clinicConfig',
                'chinesePRUsageDefault',
                'dispensingConfig',
                'chineseMedicineConfig',
                'chainBasic',
                'isEnableListingPrice',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
                'featureTherapy',
            ]),

            isIgnoreExternalUsageType() {
                return this.chainBasic.outpatient.isIgnoreExternalUsageType;
            },

            outpatientVDConfig() {
                return this.viewDistributeConfig?.Outpatient || {};
            },
            supportAddOtherFee() {
                return this.outpatientVDConfig.supportAddOtherFee;
            },
            curCommonDialog() {
                if (this.templateManagerVersion === 1) return CommonPrescriptionHospitalDialog;
                return CommonPrescriptionDialog;
            },

            // 空处方删除不需要确认弹窗
            deleteNeedConfirm() {
                return this.form.prescriptionFormItems.some((item) => {
                    return item.goodsId || item.name || this.requiredItem(item);
                });
            },

            prItemsGoodsIds() {
                return this.form.prescriptionFormItems.map((item) => {
                    return item.goodsId;
                }).filter((item) => item);
            },

            translateH() {
                if (this.formsLength === 1) return '';
                return numToChinese(this.formIndex + 1);
            },

            clinicId() {
                return this.treatOnlineClinicId || (this.currentClinic && this.currentClinic.clinicId);
            },

            /**
             * @desc 现配贴包含中药相关
             * <AUTHOR>
             * @date 2021-07-06 10:09:27
             */
            isXianPeiTie() {
                return this.form.usageType === ExternalPRUsageTypeEnum.tieFu &&
                    this.form.usageSubType === TieFuUsageSubTypeEnum.xianPeiTie;
            },

            // 成品贴
            isChenPinTie() {
                return this.form.usageType === ExternalPRUsageTypeEnum.tieFu &&
                    this.form.usageSubType === TieFuUsageSubTypeEnum.chenPinTie;
            },

            isTieFu() {
                return this.isChenPinTie || this.isXianPeiTie;
            },

            jsonType() {
                const _arr = [
                    {
                        type: GoodsTypeEnum.TREATMENT,
                        subType: [
                            GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Treatment,
                            GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy,
                        ], // 治疗理疗
                    },
                    {
                        type: GoodsTypeEnum.MATERIAL,
                        subType: [
                            GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials,
                        ], // 医疗器械
                    },
                ];
                // 贴敷-成品贴/现配 增加商品、中成药两个类型
                if (this.isTieFu) {
                    _arr.push({
                        type: GoodsTypeEnum.MEDICINE,
                        subType: [
                            GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM, // 中成药
                        ],
                    });
                    _arr.push({
                        type: GoodsTypeEnum.GOODS, // 商品
                    });
                }
                if (this.supportAddOtherFee) {
                    _arr.push({
                        type: GoodsTypeEnum.OTHER, // 其他
                    });
                }

                return _arr;
            },

            isExecute() {
                return this.form.prescriptionFormItems.some((formItem) => {
                    return formItem.executeStatus === ExecuteStatus.FINISHED;
                });
            },

            disabledForm() {
                return this.disabled || this.isExecute;
            },

            placeholderText() {
                return '输入项目名或拼音码';
            },

            subTypeOptions() {
                switch (this.form.usageType) {
                    case ExternalPRUsageTypeEnum.tieFu:
                        return TieFuSubOptions;
                    case ExternalPRUsageTypeEnum.zhenCi:
                        return ZhenCiSubOptions;
                    case ExternalPRUsageTypeEnum.aiJiu:
                        return AiJiuSubOptions;
                    case ExternalPRUsageTypeEnum.baGuan:
                        return BaGuanSubOptions;
                    case ExternalPRUsageTypeEnum.tuiNa:
                        return TuiNaSubOptions;
                    default:
                        return [];
                }
            },
            requirementOptions() {
                switch (this.form.usageType) {
                    case ExternalPRUsageTypeEnum.tieFu:
                        return ExternalTieFuRequirement;
                    case ExternalPRUsageTypeEnum.zhenCi:
                    case ExternalPRUsageTypeEnum.aiJiu:
                    case ExternalPRUsageTypeEnum.baGuan:
                    case ExternalPRUsageTypeEnum.guaSha:
                    case ExternalPRUsageTypeEnum.tuiNa:
                        return ExternalZhenJiuRequirement;
                    default:
                        return [];
                }
            },
            // 以穴位为基础，其它以部位
            isBasedOnAcupoint() {
                if (this.isIgnoreExternalUsageType) return true;
                return [
                    ExternalPRUsageTypeEnum.tieFu,
                    ExternalPRUsageTypeEnum.zhenCi,
                    ExternalPRUsageTypeEnum.aiJiu,
                ].includes(this.form.usageType);
            },
            /**
             * @desc 能否选择 是否自费 payType
             * <AUTHOR>
             * @date 2021-07-19 18:23:14
             */
            showPayTypeSelect() {
                return (
                    this.$abcSocialSecurity.isOpenSocial &&
                    (
                        this.$abcSocialSecurity.config.isZhejiang ||
                        this.$abcSocialSecurity.config.isFujianFuzhou ||
                        this.$abcSocialSecurity.config.isLiaoningPanjin
                    )
                );
            },
            footerSpaceSize() {
                if (!this.isOpenSource) return 8;
                return 12;
            },
        },
        watch: {
            'form.specification': {
                // 当外治处方中选择的物品类型(饮片/颗粒)改变时,将其存入localStorage中
                // 当作用户习惯,方便下次新增外治处方时设置默认值
                handler(val) {
                    if (!val) return;
                    localStorage.setObj('_PRESCRIPTION_SPECIFICATION_', 'external', val);
                },
            },
        },
        created() {
            this._suggestionItems = [
                {
                    prop: 'displayName',
                    style: 'flex:1;margin-right: 5px',
                    render: (createElement, suggestion) => {
                        const isShowAntimicrobial = this.showAntimicrobial(suggestion);
                        return (
                            <div style="flex:1; margin-right: 5px;">
                                <div style="display: inline-block;">{ suggestion.displayName }</div>
                                {
                                    isShowAntimicrobial ? (
                                        <BizGoodsInfoTagGroup product-info={suggestion} style="display: inline-flex; margin-left: 4px;">
                                        </BizGoodsInfoTagGroup>
                                    ) : null
                                }
                            </div>
                        );
                    },
                },
                {
                    prop: 'type',
                    style: 'width: 80px;margin-right: 5px',
                    className: 'gray',
                    formatFunction: (item) => {
                        const {
                            type,
                        } = item;
                        if (type === GoodsTypeEnum.OTHER) {
                            return '其他费用';
                        }
                        if (type === GoodsTypeEnum.TREATMENT) {
                            if (this.featureTherapy) {
                                return '治疗理疗';
                            }
                            return '治疗';
                        }
                        if (type === GoodsTypeEnum.COMPOSE) {
                            return '套餐';
                        }
                        if (type === GoodsTypeEnum.MEDICINE) {
                            return '药品';
                        }
                        if (type === GoodsTypeEnum.GOODS) {
                            return '商品';
                        }
                        return GoodsTypeEnumStr[type] || '';
                    },
                },
                {
                    prop: 'medicalFeeGrade',
                    className: 'gray',
                    style: 'width: 138px;text-align: left;padding-left: 6px',
                    formatFunction: (suggestion) => {
                        const _arr = [];
                        const medicalFeeGradeString = medicalFeeGrade2Str(suggestion.medicalFeeGrade);
                        if (medicalFeeGradeString) {
                            _arr.push(medicalFeeGradeString);
                        }
                        if (suggestion.shebao) {
                            if (this.isEnableListingPrice && suggestion.shebao.listingPrice) {
                                _arr.push('挂网');
                            }
                            if (suggestion.shebao.priceLimit) {
                                _arr.push('限价');
                            }
                        }
                        return _arr.join('/');
                    },
                },
                {
                    prop: 'remark',
                    className: 'gray ellipsis',
                    style: 'width: 112px; margin-right: 5px',
                    formatFunction: (suggestion) => {
                        return suggestion.remark;
                    },
                    titleFunction: (suggestion) => {
                        return suggestion.remark;
                    },
                },
                {
                    prop: 'packagePrice',
                    type: 'money',
                    style: 'width: 80px;text-align: right;margin-right: 10px',
                },
            ];
            this._key = `${this.currentClinic.clinicId}_${this.userInfo.id}`;
            this.$abcEventBus.$on('autoFocusSearch', this.autoFocusSearch, this);
        },

        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
            this.$abcEventBus.$offVmEvent(this._uid);
        },

        methods: {
            isDisabledGoods,

            isWesternMedicine(productInfo) {
                return productInfo.type === GoodsTypeEnum.MEDICINE &&
                    [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(productInfo.subType);
            },
            showAntimicrobial(productInfo) {
                if (!productInfo) return false;
                return productInfo.type === GoodsTypeEnum.MEDICINE &&
                    [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(productInfo.subType) &&
                    (isNotNull(productInfo.antibiotic) || !!productInfo.dangerIngredient);
            },

            focusHandler(goodsAutocomplete) {
                if (this.isOpenSource) {
                    optimizePopoverPosition(280, goodsAutocomplete.$el);
                }
            },

            changeUsageType() {
                this.form.usageSubType = 0;
                this.form.prescriptionFormItems.forEach((item) => {
                    item.externalGoodsItems = [];
                    item.unit = item.productInfo?.packageUnit || this.externalUnit(item);
                    // 切换用法时 非治疗理疗要清空，只有贴敷-成品贴能开药品、耗材、物资等
                    if (this.isNotTreatment(item.productInfo)) {
                        Object.assign(item, {
                            goodsId: null,
                            name: '',
                            type: '',
                            subType: '',
                            unitPrice: '',
                            externalUnitCount: null,
                            externalGoodsItems: [],
                            unit: '',
                            unitCount: 0,
                            productInfo: {},
                        });
                        this.calcItemUnitCount(item);
                    }
                });
                this.outpatientFeeChange();
            },

            /**
             * @desc 切换二级分类需要前端记忆
             * <AUTHOR>
             * @date 2021-07-06 10:21:05
             */
            changeUsageSubType(val, index, oldVal) {
                if (val === oldVal) return;
                this.form.prescriptionFormItems.forEach((item) => {
                    item.externalGoodsItems = [];
                    // 切换用法时 非治疗理疗要清空，只有贴敷-成品贴能开药品、耗材、物资等
                    if (this.isNotTreatment(item.productInfo)) {
                        Object.assign(item, {
                            goodsId: null,
                            name: '',
                            type: '',
                            subType: '',
                            unitPrice: '',
                            externalUnitCount: null,
                            externalGoodsItems: [],
                            unit: '',
                            unitCount: 0,
                            productInfo: {},
                        });
                        this.calcItemUnitCount(item);
                    }
                });
                let usageTypeObj = localStorage.getObj('external_pr_last_use_usage_type', this._key, true);
                const newTypeObj = {
                    [this.form.usageType]: this.form.usageSubType,
                };
                if (usageTypeObj) {
                    Object.assign(usageTypeObj, newTypeObj);
                } else {
                    usageTypeObj = newTypeObj;
                }

                if (this.isXianPeiTie) {
                    const specification = localStorage.getObj('_PRESCRIPTION_SPECIFICATION_', 'external', true) || '中药饮片';
                    this.form.specification = specification;
                } else {
                    this.form.specification = null;
                }

                localStorage.setObj('external_pr_last_use_usage_type', this._key, usageTypeObj);
                this.outpatientFeeChange();
            },

            requiredItem(item) {
                return (item.acupoints || []).filter((acu) => acu.name).length > 0;
            },

            showSelectAcupointDialog(item) {
                new AcupointSelectDialog({
                    selected: item.acupoints,
                    onChange: (acupoints) => {
                        this.changeAcupoint(item, acupoints);
                    },
                }).generateDialog({ parent: this });
            },
            changeAcupoint(currentEditItem, acupoints) {
                const newAcupoints = clone(acupoints);
                currentEditItem.acupoints = newAcupoints.map((item) => {
                    item.acupointType = item.exTreatType;
                    if (!item.position) {
                        this.changePosition(item, currentEditItem, true);
                    }
                    return item;
                });
                this.calcItemUnitCount(currentEditItem);
                this.insertAfter(currentEditItem);
            },
            dosageCount(item) {
                let count = 0;
                item.acupoints.forEach((it) => {
                    if (it.name) {
                        if (isNumber(it.position)) {
                            count += parseFloat(it.position);
                        } else if (it.position === '双') {
                            count += 2;
                        } else if (it.position !== '-') {
                            count++;
                        }
                    }
                });
                this.$set(item, 'acupointUnitCount', count || 1);
                return count || 1;
            },

            calcItemUnitCount(item) {
                const {
                    dosage,
                    acupoints,
                    externalUnitCount,
                    billingType,
                } = item;
                if (!dosage || !acupoints || billingType === ExternalPRBillingTypeEnum.MANUAL) return;
                if (this.isTieFu && externalUnitCount) {
                    item.unitCount = item.dosage * externalUnitCount * this.dosageCount(item);
                } else {
                    item.unitCount = item.dosage * this.dosageCount(item);
                }
                this.outpatientFeeChange();
                this.$emit('queryVerify');
            },

            async onInputAcuPosition(acupoint, val) {
                if (val === '') {
                    await this.$nextTick();
                    acupoint.position = '-';
                    this.$set(acupoint, 'position', '-');
                }
            },

            inputDosageHandle(item) {
                if (item.expectedTotalPrice) {
                    item.expectedTotalPrice = null;
                    if (item.sourceUnitPrice !== item.unitPrice) {
                        item.expectedUnitPrice = item.unitPrice;
                    }
                }
                this.calcItemUnitCount(item);
            },

            inputUnitCount(item) {
                if (item.expectedTotalPrice) {
                    item.expectedTotalPrice = null;
                    if (item.sourceUnitPrice !== item.unitPrice) {
                        item.expectedUnitPrice = item.unitPrice;
                    }
                }
                this.outpatientFeeChange();
            },

            totalPrice(item) {
                const {
                    chargeStatus,
                    receivedPrice,
                    receivableFee,
                    refundedFee,
                } = this.form;

                // 全收（包含退单、退费、部分退）显示实收，未收、部分收显示原价
                // form 上已收、部分收、部分退状态都是1，如果(应收 === 实收 + 已退)就是全收了
                if (
                    chargeStatus > ChargeFormStatusEnum.CHARGED ||
                    (
                        chargeStatus === ChargeFormStatusEnum.CHARGED &&
                        sum(receivableFee) === sum(receivedPrice, Math.abs(refundedFee))
                    )
                ) {
                    return receivedPrice;
                }
                // 未收部分收显示原价
                const _arr = [];
                // 有议价情况直接用currentUnitPrice，没有用unitPrice 计算
                const unitPrice = getItemUnitPrice(item);
                _arr.push(itemCountPrice(unitPrice, item.unitCount || 0));
                _arr.push(item.fractionPrice || 0);
                return sum(..._arr) || 0;
            },
            /**
             * @desc 选择穴位位置
             * 0-不能选左右，1-可以同时选左右，2-只能选左，3-只能选右
             * <AUTHOR> Yang
             * @date 2020-09-15 16:40:08
             */
            changePosition(acupoint, item, init = false) {
                if (this.disabledForm) return false;
                if (acupoint.name === '阿是穴' || acupoint.name === '夹脊') {
                    if (init) {
                        acupoint.position = 1;
                        this.calcItemUnitCount(item);
                    } else {
                        this.$set(acupoint, 'showPositionCountPopover', true);
                    }
                    return;
                }
                switch (acupoint.type) {
                    case 0: {
                        acupoint.position = '单';
                        this.$set(acupoint, 'readonly', true);
                        break;
                    }
                    case 1: {
                        if (init) {
                            acupoint.position = '双';
                            this.$set(acupoint, 'readonly', true);
                            this.calcItemUnitCount(item);
                        } else {
                            this.$set(acupoint, 'showPositionCountPopover', true);
                        }
                        return;
                    }
                    case 2: {
                        acupoint.position = '左';
                        this.$set(acupoint, 'readonly', true);
                        break;
                    }
                    case 3: {
                        acupoint.position = '右';
                        this.$set(acupoint, 'readonly', true);
                        break;
                    }
                    default: {
                        if (init) {
                            acupoint.position = '-';
                            this.calcItemUnitCount(item);
                        } else {
                            this.$set(acupoint, 'showPositionCountPopover', true);
                        }
                        return;
                    }
                }
                this.calcItemUnitCount(item);
            },

            onChangeCount(item, acupoint, val) {
                acupoint.position = val;
                this.$set(acupoint, 'showPositionCountPopover', false);
                this.calcItemUnitCount(item);
                this.$emit('queryVerify');
            },

            editCurrentAcupoint(index, acuIndex) {
                if (this.disabledForm) return false;
                this.currentEditAcupointIndex = `${index}-${acuIndex}`;
                this.$nextTick(() => {
                    this.$el.querySelector(`.acupoint-item-${index}-${acuIndex}`)
                        .querySelector('.abc-autocomplete-wrapper input')
                        .focus();
                });
            },
            deleteAcupoint(item, index) {
                item.acupoints.splice(index, 1);
                this.calcItemUnitCount(item);
                this.$emit('queryVerify');
            },

            /**
             * @desc 失焦的时候判断是否选择，没有选择清空输入框
             * <AUTHOR> Yang
             * @date 2020-09-11 17:44:42
             */
            blurHandle(item, $autocomplete) {
                this.currentEditIndex = -1;
                if (!item.name) {
                    if ($autocomplete && typeof $autocomplete.clearQueryString === 'function') {
                        $autocomplete.clearQueryString();
                    }
                }
            },
            /**
             * 判断开出的中西成药的抗菌等级是否满足开出条件
             * @param {Object} medicine
             * @return {boolean}
             */
            isAllowAdd(medicine) {
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                const isSuccess = isAllowAddByAntimicrobialDrugManagement(medicine, this.doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                if (!isSuccess) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: [medicine] }).generateDialogAsync({ parent: this });
                    }, 100);
                }
                return isSuccess;
            },
            async selectGoods(goods, item, index) {
                try {
                    if (!goods) return false;
                    if (goods.disabled) return false;
                    this.currentEditIndex = -1;

                    // 判断开出的中西成药的抗菌等级是否满足开出条件
                    // 通过模板开出时不进行校验
                    if (!this.isFromTemplate && this.isWesternMedicine(goods)) {
                        const newMedicine = {
                            productInfo: goods,
                            name: goods.displayName || goods.medicineCadn || goods.name,
                        };
                        const isSuccess = this.isAllowAdd(newMedicine);
                        if (!isSuccess) return;
                    }

                    const unit = goods.packageUnit || this.externalUnit(goods);
                    Object.assign(item, {
                        goodsId: goods.goodsId,
                        name: goods.displayName,
                        medicineCadn: goods.medicineCadn,
                        type: goods.type,
                        subType: goods.subType,
                        useDismounting: null,
                        unitPrice: goods.packagePrice,
                        unit,
                        unitCount: this.getDefaultBillingType(goods, unit) === ExternalPRBillingTypeEnum.ACUPOINT ? 0 : null,
                        externalUnitCount: this.isTieFu ? 1 : null,
                        productInfo: goods,
                        payType: null,
                        pharmacyType: goods.pharmacyType,
                        pharmacyNo: goods.pharmacyNo,
                        billingType: this.getDefaultBillingType(goods, unit),
                    });
                    await this.fetchFeeComposeInfo(goods, item);
                    this.calcItemUnitCount(item);
                    if (this.isTieFu) {
                        if (this.isNotTreatment(item)) {
                            this._timer = setTimeout(() => {
                                this.$refs.externalUnitCount[index]?.$el
                                    .querySelector('input')?.focus();
                            }, 100);
                        } else {
                            this.focusGoodsItemInput(item, index);
                        }
                    } else {
                        this.focusAcupointInput(item, index);
                    }
                    this.outpatientFeeChange();
                    this.$emit('queryVerify');
                } catch (e) {
                    console.error(e);
                }
            },

            async fetchFeeComposeInfo(goods, formItem) {
                if (!goods.id) return;
                if (
                    [
                        FeeComposeTypeEnum.FEE_TYPE_COMPOSE_GOODS,
                        FeeComposeTypeEnum.FEE_TYPE_COMPOSE_FEE,
                    ].indexOf(goods.feeComposeType) !== -1
                ) {
                    // 支持费用项组合||套餐 需要拉一下goods详细信息，用于展示费用项||子项
                    const { data } = await GoodsAPI.fetchGoods(goods.id, {
                        withStock: 1,
                        feeComposeType: goods.feeComposeType,
                        sceneType: SearchSceneTypeEnum.outpatient,
                        departmentId: this.departmentId,
                    });
                    formItem.productInfo = data;

                    if (formItem.composeChildren && formItem.composeChildren.length) {
                        formItem.composeChildren = formItem.composeChildren.map((x) => {
                            return {
                                ...x,
                                goodsPrimaryId: x.productInfo.composeId,
                                composeChildren: getItemComposeChildren(x),
                            };
                        });
                    } else {
                        formItem.composeChildren = getItemComposeChildren(data);
                    }
                }
            },

            addExternalGroup(index, sort) {
                this.form.prescriptionFormItems.push(
                    {
                        sort: sort + 1,
                        keyId: createGUID(),
                        goodsId: null,
                        name: '',
                        type: '',
                        subType: '',
                        freq: '1日1次',
                        usage: '',
                        dosage: '',
                        unitCount: 0,
                        unit: '',
                        externalUnitCount: null,
                        unitPrice: 0,
                        specialRequirement: '',
                        productInfo: null,
                        externalGoodsItems: [],
                        acupoints: [
                            {
                                id: null,
                                name: '',
                                type: 0,
                                position: '单',
                            },
                        ],
                        billingType: ExternalPRBillingTypeEnum.ACUPOINT,
                    },
                );
                this.$nextTick(() => {
                    this.$el
                        .querySelectorAll('.external-group')[index + 1]?.querySelector('.table-td-goods-autocomplete')?.querySelector('input')?.focus();
                });
                this.outpatientFeeChange();
            },

            deleteExternalGroup(index) {
                this.form.prescriptionFormItems.splice(index, 1);
                this.$emit('queryVerify');
                this.outpatientFeeChange();
            },

            editCurrentItem(index) {
                if (this.disabledForm) return false;
                this.currentEditIndex = index;
                this.$nextTick(() => {
                    this.$el?.querySelectorAll('.external-group')[index]?.querySelector('.table-td-goods-autocomplete')?.querySelector('input')?.focus();
                });
            },
            handleDoubleClick(event) {
                if (!event.target.value) return;
                event.target.selectionStart = 0;
                event.target.selectionEnd = event.target.value.length;
            },
            changeCount(medicine) {
                if (medicine.expectedTotalPrice) {
                    medicine.expectedTotalPrice = null;
                    if (medicine.sourceUnitPrice !== medicine.unitPrice) {
                        medicine.expectedUnitPrice = medicine.unitPrice;
                    }
                }
            },

            startDrag() {
                this.dragging = true;
                $('#medicine-hover-popover').remove();
            },

            showDeleteIcon(medicine) {
                return !this.disabledForm && (medicine.goodsId || medicine.name);
            },


            /** ----------------------------------------------------------------------
             *  删除中药处方的按钮
             */
            deletePres() {
                this.$emit('close', this.formIndex);
                this.$emit('queryVerify');
            },

            blurAcupointHandle() {
                this.currentEditAcupointIndex = -1;
            },
            selectAcupoint(newAcupoint, acupoint, item, index, acuIndex) {
                if (!newAcupoint.id && newAcupoint.name === acupoint.name) return;
                const repeatIndex = item.acupoints.findIndex((it) => {
                    return (it.id && it.id === newAcupoint.id) ||
                        (it.name && it.name === newAcupoint.name);
                });
                // 有重复的做toast提示
                if (repeatIndex > -1 && repeatIndex !== acuIndex) {
                    this.$Toast({
                        message: `已添加${newAcupoint.name}`,
                        type: 'error',
                        duration: 1000,
                        referenceEl: this.$el.querySelectorAll('.external-group')[index]
                            .querySelectorAll('.acupoint-item')[acuIndex],
                    });
                    return false;
                }

                acupoint.id = newAcupoint.id;
                acupoint.name = newAcupoint.name;
                acupoint.type = newAcupoint.type;
                acupoint.position = newAcupoint.position;
                acupoint.acupointType = newAcupoint.acupointType;
                this.changePosition(acupoint, item, true);
                this.insertAfter(item);
                this.calcItemUnitCount(item);
                this.focusAcupointInput(item, index);
                this.$emit('queryVerify');
            },

            /**
             * @desc 默认增加一个穴位
             * <AUTHOR> Yang
             * @date 2020-09-18 15:35:16
             */
            insertAfter(item) {
                if (!item.acupoints.find((it) => !it.id && !it.name)) {
                    item.acupoints.push({
                        id: null,
                        name: '',
                        type: 0,
                        position: '单',
                    });
                }
            },

            /**
             * @desc 现配贴 聚焦到第一个非空药品输入框
             * <AUTHOR>
             * @date 2021-07-07 15:10:20
             */
            focusGoodsItemInput(item, index) {
                if (!item.externalGoodsItems) return;
                const _goodsIndex = item.externalGoodsItems.findIndex((it) => !it.goodsId && !it.name);
                // 如果没找到符合条件的元素，_goodsIndex 会是 -1
                if (_goodsIndex === -1) return;

                this._timer = setTimeout(() => {
                    const externalGroups = this.$el.querySelectorAll('.external-group');
                    if (!externalGroups || !externalGroups[index]) return;

                    const tableRows = externalGroups[index].querySelectorAll('.chinese-prescription-table .table-td');
                    if (!tableRows || !tableRows[_goodsIndex]) return;

                    const inputElement = tableRows[_goodsIndex].querySelector('input');
                    if (inputElement) {
                        inputElement.focus();
                    }
                }, 10);
            },

            /**
             * @desc 聚焦到下一个穴位输入框
             * <AUTHOR> Yang
             * @date 2020-09-21 09:21:49
             */
            focusAcupointInput(item, index) {
                const acuIndex = item.acupoints.findIndex((it) => !it.id && !it.name);
                this._timer = setTimeout(() => {
                    this.$el.querySelector(`.acupoint-item-${index}-${acuIndex}`)
                        .querySelector('input')
                        .focus();
                },10);
            },

            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                const inputs = $(this.$el).find('.abc-input__inner').not(':disabled');

                const targetIndex = inputs.index(e.target);

                let nextInput = inputs[targetIndex + 1];
                if (!nextInput) {
                    return false;
                }

                if (nextInput.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }
                nextInput && this.$nextTick(() => {
                    nextInput.select();
                    nextInput.focus();
                });
            },

            // 保存模板
            saveCommon() {
                if (this.form.prescriptionFormItems.filter((item) => item.goodsId).length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '处方项目为空，不能保存为模板',
                    });
                    return false;
                }
                this.showDialog = true;
            },

            /**
             * @desc 门诊单内容变更
             * <AUTHOR>
             * @date 2022/06/06 10:47:44
             */
            outpatientFeeChange() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '外治处方',
                    needCalcFee: true,
                });
            },

            isNotTreatment(productInfo) {
                if (!productInfo) return false;
                const { type } = productInfo || {};
                return type !== GoodsTypeEnum.TREATMENT;
            },

            /**
             * @desc 可能的单位列表
             * <AUTHOR>
             * @date 2018/04/13 11:01:33
             */
            unitArray(wm) {
                const res = [];
                const dismounting = wm.productInfo && wm.productInfo.dismounting;
                const pieceUnit = wm.productInfo && wm.productInfo.pieceUnit;
                const packageUnit = wm.productInfo && wm.productInfo.packageUnit;
                if (dismounting) {
                    if (pieceUnit) {
                        res.push({ name: pieceUnit });
                    }
                    if (packageUnit && packageUnit !== pieceUnit) {
                        res.push({ name: packageUnit });
                    }
                } else {
                    if (packageUnit) {
                        res.push({ name: packageUnit });
                    }
                }
                return res;
            },

            changeUnit(item) {
                if (item.unit !== item.pieceUnit && ~~item.unitCount !== item.unitCount) {
                    item.unitCount = '';
                }
                item.useDismounting = +(
                    item.productInfo &&
                    item.productInfo.dismounting &&
                    item.unit === item.productInfo.pieceUnit &&
                    item.unit !== item.productInfo.packageUnit
                );
                const {
                    piecePrice,
                    packagePrice,
                } = item.productInfo;
                item.unitPrice = item.useDismounting ? piecePrice : packagePrice;

                if (item.expectedTotalPrice) {
                    item.expectedTotalPrice = null;
                    if (item.sourceUnitPrice !== item.unitPrice) {
                        item.expectedUnitPrice = item.unitPrice;
                    }
                }
                clearItemBargainHandler(item);
                this.$emit('queryVerify');
            },

            displayMedicalFeeGrade(item) {
                return this.showMedicalFeeGrade &&
                    this.isOpenSource &&
                    item.productInfo &&
                    item.productInfo.medicalFeeGrade;
            },

            onChangeBillingType(item, val) {
                if (val === ExternalPRBillingTypeEnum.MANUAL) {
                    item.unitCount = undefined;
                    this.outpatientFeeChange();
                } else {
                    this.calcItemUnitCount(item);
                }
                this.$emit('queryVerify');
            },

            /**
             * @desc 选择goods时，计费类型规则
             * @desc 1、bizExtensions已经存在就用goods上的
             * @desc 2、看item的单位unit，包含数字（阿拉伯或者中文数据）默认为手动计数
             * @desc 3、默认为按穴位计数
             * <AUTHOR>
             * @date 2024/06/27 14:08:49
             * @param
             * @return
             */
            getDefaultBillingType(goods, unit) {
                const {
                    bizExtensions,
                } = goods;
                const { billingType } = bizExtensions || {};
                if (billingType) return billingType;
                if (/[一二三四五六七八九十\d]+/.test(unit)) return ExternalPRBillingTypeEnum.MANUAL;
                return ExternalPRBillingTypeEnum.ACUPOINT;
            },

            showDosageCount(acupoints) {
                if (!acupoints || acupoints.length === 0) return false;
                const availableAcupoints = acupoints.filter((item) => item.name);
                if (availableAcupoints.length === 0) return false;
                return true;
            },

            externalUnit(item) {
                const { unit } = item;
                let acupointUnit = '';
                if (this.isIgnoreExternalUsageType) {
                    if (unit.includes('穴')) {
                        acupointUnit = '穴';
                    } else {
                        acupointUnit = '部位';
                    }
                } else {
                    if (this.isAllBodyPosition(item)) {
                        acupointUnit = '部位';
                    } else {
                        acupointUnit = '穴';
                    }
                }
                this.$set(item, 'acupointUnit', acupointUnit);
                return acupointUnit;
            },
            // 全部是穴位
            isAllAcupoint(item) {
                return item.acupoints?.some((it) => it.name) && item.acupoints.every((it) => it.acupointType === ExternalAcupointTypeEnum.ACUPOINT || !it.name);
            },
            // 全部是部位
            isAllBodyPosition(item) {
                return item.acupoints?.some((it) => it.name) && item.acupoints.every((it) => it.acupointType === ExternalAcupointTypeEnum.BODY_POSITION || !it.name);
            },
            acupointLabel(item) {
                const { unit } = item;
                if (this.isIgnoreExternalUsageType) {
                    if (unit.includes('穴')) {
                        return '按穴位计数';
                    }
                    return '按部位计数';

                }
                if (this.isAllAcupoint(item)) {
                    return '按穴位计数';
                }
                if (this.isAllBodyPosition(item)) {
                    return '按部位计数';
                }
                if (this.isBasedOnAcupoint) return '按穴位计数';
                return '按部位计数';
            },
            acupointPlaceholder(item) {
                const { unit } = item;
                if (this.isIgnoreExternalUsageType) {
                    if (unit.includes('穴')) {
                        return '穴位';
                    }
                    return '部位';
                }
                if (this.isAllAcupoint(item)) {
                    return '穴位';
                }
                if (this.isAllBodyPosition(item)) {
                    return '部位';
                }
                if (this.isBasedOnAcupoint) {
                    return '穴位';
                }
                return '部位';
            },

            autoFocusSearch(keyId) {
                if (this.form.keyId !== keyId) return;
                this.$nextTick(() => {
                    this.$refs.goodsAutocomplete[0].$refs.abcAutocomplete.focus();
                });
            },

            // 改变组号需要重排
            changeGroupIdHandler() {
                this.form.prescriptionFormItems.sort((a, b) => {
                    return a.sort - b.sort;
                });
            },
        },
    };
</script>
