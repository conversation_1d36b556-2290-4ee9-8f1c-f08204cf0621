<template>
    <div class="medical-insurance-restriction-wrapper" :style="wrapperStyle">
        <div class="verify-item">
            <div class="verify-item__title">
                <abc-space :size="4" align="start">
                    医保合规
                    <abc-tooltip-info
                        :max-width="420"
                        placement="top-start"
                        style="cursor: pointer;"
                        class="test"
                    >
                        <abc-flex
                            class="medical-insurance-restriction-popover"
                            vertical
                            :gap="8"
                        >
                            <abc-space>
                                <h4>医保合规助手</h4>
                                <abc-text v-if="isDeepseekEnable" theme="gray-light">
                                    内置 DeepSeek 引擎
                                </abc-text>
                            </abc-space>
                            <p>遵循医保目录、政策等考核要求，在疾病和药品大数据基础上，使用互联网技术为诊所提供的一套智能检查工具。</p>
                            <p>在门诊及收费等多个环节，系统会根据医保目录，检查用药违规风险，如药品限定医疗机构等级、就医方式、患者特征、诊断、病种、医师级别等规则；也会根据医保考核经验，检查医疗行为违规风险，如就诊频次过高、医保支付金额过高等规则。</p>
                            <p>开启合规助手，可以极大程度上帮助诊所降低医保合规风险，减少违规损失。同时，因为医保政策的复杂性和多变性，风控结果仅用于辅助参考，一切以医保实际结算为准。</p>
                        </abc-flex>
                    </abc-tooltip-info>
                </abc-space>
            </div>

            <div class="verify-item__content">
                <ul>
                    <template v-if="restrictSwitch">
                        <template v-if="dangerWarnArr.length">
                            <template v-for="(it, index) in dangerWarnArr">
                                <li
                                    v-if="!showMore ? index === 0 : true"
                                    :key="it.hint + index"
                                    :class="{
                                        warn: it.level === 'WARN',
                                        danger: it.level === 'DANGER',
                                        done: !!it.isDeal,
                                        'has-border': dangerWarnArr.length > 1,
                                    }"
                                >
                                    <div class="status">
                                        <template v-if="it.level === 'DANGER'">
                                            <abc-icon icon="Attention" size="14px"></abc-icon>风险
                                        </template>
                                        <template v-else-if="it.level === 'WARN'">
                                            <abc-icon icon="Attention" size="14px"></abc-icon>提醒
                                        </template>
                                    </div>

                                    <div
                                        class="abstract"
                                    >
                                        {{ it.hint }}
                                    </div>

                                    <template v-if="it.dealType">
                                        <abc-select
                                            v-if="it.dealWay === DealWayEnum.PAY_MODE || it.dealWay === DealWayEnum.All"
                                            v-model="it.shebaoPayMode"
                                            reference-mode="text"
                                            reference-text-justify="end"
                                            :width="80"
                                            size="tiny"
                                            style="margin-left: 16px;"
                                            @change="onShebaoPayModeChange(it)"
                                        >
                                            <abc-option :value="0" label="优先统筹"></abc-option>
                                            <abc-option :value="1" label="优先个账"></abc-option>
                                            <abc-option :value="2" label="不过医保"></abc-option>
                                        </abc-select>
                                        <abc-button
                                            v-if="it.dealWay === DealWayEnum.SIGNATURE || it.dealWay === DealWayEnum.All"
                                            variant="text"
                                            size="small"
                                            :width="92"
                                            :theme="it.isSignature ? 'default' : 'primary'"
                                            @click="onClickSign(it)"
                                        >
                                            {{ it.isSignature ? '取消签字' : '确认签字' }}
                                        </abc-button>
                                    </template>
                                </li>
                            </template>
                            <li v-if="dangerWarnArr.length > 1">
                                <abc-flex
                                    align="center"
                                    justify="space-between"
                                    style="width: 100%;"
                                >
                                    <abc-button
                                        v-if="showMore"
                                        variant="text"
                                        size="small"
                                        theme="default"
                                        icon="s-up-line-medium"
                                        icon-position="right"
                                        @click="onClickShowMore"
                                    >
                                        收起
                                    </abc-button>
                                    <abc-button
                                        v-else
                                        variant="text"
                                        size="small"
                                        theme="default"
                                        icon="s-dowline-medium"
                                        icon-position="right"
                                        @click="onClickShowMore"
                                    >
                                        展开更多 ({{ dangerWarnArr.length - 1 }})
                                    </abc-button>

                                    <abc-dropdown @change="onShebaoPayModeBatchChange">
                                        <abc-button
                                            slot="reference"
                                            variant="text"
                                            size="small"
                                            :width="92"
                                        >
                                            批量处理
                                        </abc-button>
                                        <abc-dropdown-item
                                            label="优先统筹"
                                            :value="0"
                                        ></abc-dropdown-item>
                                        <abc-dropdown-item
                                            label="优先个账"
                                            :value="1"
                                        ></abc-dropdown-item>
                                        <abc-dropdown-item
                                            label="不过医保"
                                            :value="2"
                                        ></abc-dropdown-item>
                                    </abc-dropdown>
                                </abc-flex>
                            </li>
                        </template>
                        <li v-else :class="verifyClass">
                            <div class="status">
                                <abc-icon icon="chosen" size="14px"></abc-icon>{{ verifyStr }}
                            </div>
                        </li>
                    </template>
                    <li v-else class="none">
                        <span style="line-height: 26px;">
                            未开启
                            <abc-tooltip-info
                                placement="top-start"
                                :max-width="450"
                                content="可前往「医保-合规风控」中开启，开启后将按医保目录、政策要求对处方进行智能评估，提示出可能存在的违规风险"
                            >
                            </abc-tooltip-info>
                        </span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import Vue, {
        onBeforeMount, ref,
    } from 'vue';
    import ShebaoRestrictAPI from 'api/shebao-restrict';
    import { ChargeStatusEnum } from '@/service/charge/constants';
    import localStorage from 'utils/localStorage-handler';
    import { getUseDeepseekEnableStore } from '@/module-federation-dynamic/deepseek';
    import { storeToRefs } from 'pinia';
    import Logger from '@/utils/logger';
    import { DealWayEnum } from '@/common/constants/shebao-restrict.js';


    const storeKey = '_medical_insurance_show_more_';

    export default {
        name: 'MedicalInsuranceRestriction',
        props: {
            verifyLists: {
                validator: (prop) => typeof prop === 'object' || prop === null,
                required: true,
            },
            behaviorVerifyLists: {
                validator: (prop) => typeof prop === 'object' || prop === null,
                required: true,
            },
            shebaoChargeType: {
                type: Number,
                required: true,
            },
            businessId: String,
            hasCdss: Boolean,
        },

        setup() {
            const isDeepseekEnable = ref(false);

            const loadDeepseekModules = async () => {
                try {
                    const { useDeepseekEnableStore } = await getUseDeepseekEnableStore();
                    if (!useDeepseekEnableStore) {
                        throw new Error('无法加载 Deepseek 模块');
                    }
                    const store = useDeepseekEnableStore();

                    const { isDeepseekEnable: _isDeepseekEnable } = storeToRefs(store);
                    isDeepseekEnable.value = _isDeepseekEnable;
                } catch (error) {
                    console.error('Failed to load Deepseek modules:', error);
                    Logger.report({
                        scene: 'deepseek_report_scene',
                        data: {
                            info: 'Failed to load Deepseek modules',
                            message: error.message || 'unknown',
                            error,
                        },
                    });
                }
            };

            onBeforeMount(() => {
                loadDeepseekModules();
            });

            return {
                isDeepseekEnable,
            };
        },

        data() {
            return {
                DealWayEnum,
                restrictRuleResultViews: [],
                showMore: localStorage.get(storeKey, true) || 0,
            };
        },
        computed: {
            ...mapGetters('shebaoRestrict',[
                'restrictSwitch',
            ]),

            wrapperStyle() {
                if (this.hasCdss) {
                    return {
                        borderTop: '1px dashed var(--abc-color-P6)',
                        borderRadius: '0 0 var(--abc-border-radius-small) var(--abc-border-radius-small)',
                    };
                }
                return {
                    borderTop: 'none',
                    borderRadius: 'var(--abc-border-radius-small)',
                };

            },
            arrDanger() {
                const arrDanger = [];
                this.verifyLists?.concat(this.behaviorVerifyLists)?.forEach((item) => {
                    if (item?.verifyDetails) {
                        item.verifyDetails.forEach((it) => {
                            if (it.level === 'DANGER') {
                                arrDanger.push({
                                    ...it,
                                    dealType: item.dealType,
                                });
                            }
                        });
                    }
                });
                return arrDanger;
            },
            arrWarn() {
                const arrWarn = [];

                this.verifyLists?.concat(this.behaviorVerifyLists)?.forEach((item) => {
                    if (item?.verifyDetails) {
                        item.verifyDetails.forEach((it) => {
                            if (it.level === 'WARN') {
                                arrWarn.push({
                                    ...it,
                                    dealType: item.dealType,
                                });
                            }
                        });
                    }
                });
                return arrWarn;
            },
            dangerWarnArr() {
                if (!this.$abcSocialSecurity.isOpenSocial) return [];
                return Vue.observable(this.arrDanger.concat(this.arrWarn));
            },

            showNormalTips() {
                return this.shebaoChargeType === 1 && this.restrictSwitch === 1;
            },
            verifyClass() {
                if (!this.$abcSocialSecurity.isOpenSocial) return 'none';

                if (this.arrDanger.length) {
                    return 'danger';
                }
                if (this.arrWarn.length) {
                    return 'warn';
                }
                if (this.arrDanger.length === 0 && this.arrWarn.length === 0) {
                    return 'pass';
                }
                return 'none';
            },
            verifyStr() {
                if (!this.$abcSocialSecurity.isOpenSocial) return '门店未开通医保支付';

                if (this.shebaoChargeType === 1) {
                    if (this.restrictSwitch === 1) {
                        if (this.dangerWarnArr.length) {
                            return `风险，${this.dangerWarnArr[0].hint}`;
                        }
                        return '通过';
                    }
                    return '通过';
                }

                return '通过';
            },

            verifyStatus() {
                if (!this.$abcSocialSecurity.isOpenSocial) return '';
                if (this.shebaoChargeType === 1) return 'NONE';
                const dangerArr = [];
                const warnArr = [];

                this.verifyLists?.forEach((item) => {
                    if (item.level === 'DANGER') {
                        dangerArr.push(item);
                    } else if (item.level === 'WARN') {
                        warnArr.push(item);
                    }
                });
                if (dangerArr.length) {
                    return 'DANGER';
                }
                if (warnArr.length) {
                    return 'WARN';
                }
                return 'PASS';
            },
        },
        watch: {},

        created() {
            this.fetchVerifyResult();
        },

        methods: {
            onClickShowMore() {
                if (this.showMore) {
                    this.showMore = 0;
                } else {
                    this.showMore = 1;
                }
                localStorage.set(storeKey, this.showMore);
            },

            onShebaoPayModeChange(it) {
                const currentKeyId = it.prescriptionFormDetails?.[0]?.keyId;
                this.dangerWarnArr.forEach((x) => {
                    const { prescriptionFormDetails } = x;
                    const {
                        keyId,
                    } = prescriptionFormDetails?.[0] || {};
                    if (currentKeyId === keyId) {
                        x.shebaoPayMode = it.shebaoPayMode;
                    }
                });
                this.$emit('change-shebao-pay-mode', it);
            },

            onShebaoPayModeBatchChange(val) {
                this.dangerWarnArr.forEach((it) => {
                    if (it.dealType) {
                        it.shebaoPayMode = val;
                        this.onShebaoPayModeChange(it);
                    }
                });
            },

            onClickSign(it) {
                it.isSignature = !it.isSignature;
                this.$emit('shebao-restrict-signature', it);
            },

            /**
             * @desc 获取风险，如果是已收费，单独显示，未收费的，下次完诊更新
             */
            async fetchVerifyResult() {
                try {
                    if (!this.$abcSocialSecurity.isOpenSocial) return;
                    if (!this.businessId) return;
                    const {
                        businessId,
                        restrictRuleResultViews,
                    } = await ShebaoRestrictAPI.fetchVerifyResult(this.businessId);
                    if (businessId !== this.businessId) return;
                    this.restrictRuleResultViews = restrictRuleResultViews;
                } catch (e) {
                    console.error(e);
                }
            },

            /**
             * @desc 提交风险，未收费的，如果已经保存直接更新
             */
            async updateShebaoRestrictResult(businessId) {
                try {
                    const {
                        restrictRuleResultViews,
                        dangerWarnArr,
                    } = this;
                    const unChargedResult = restrictRuleResultViews.find((x) => x.payStatus === ChargeStatusEnum.UN_CHARGE);
                    let restrictRuleResultViewId;
                    if (unChargedResult) {
                        // 未收费的已经保存过的风险
                        const {
                            businessId: itemBusinessId,
                            id,
                        } = unChargedResult;
                        if (itemBusinessId !== businessId) return;
                        restrictRuleResultViewId = id;
                    }
                    // 没有风险不新增，只更新
                    if (!dangerWarnArr.length && !restrictRuleResultViewId) return;
                    const restrictCount = dangerWarnArr.length;
                    const restrictSettingCount = dangerWarnArr.filter((x) => !!x.isDeal).length;
                    const data = {
                        businessId,
                        restrictRuleResultViews: [{
                            businessId,
                            id: restrictRuleResultViewId,
                            restrictCount,
                            restrictSettingCount,
                            restrictInfo: JSON.stringify({
                                keyId: businessId + restrictRuleResultViewId,
                                dangerWarnArr,
                            }),
                        }],
                    };
                    await ShebaoRestrictAPI.updateVerifyResult(data);
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .medical-insurance-restriction-wrapper {
        position: relative;
        width: 100%;
        margin-top: 0;
        background-color: var(--abc-color-div-white);
        border-top: 1px dashed var(--abc-color-P6);
        border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

        .pass {
            color: var(--abc-color-G2);
        }

        .warn {
            color: var(--abc-color-Y2);
        }

        .none {
            color: var(--abc-color-T2);

            .iconfont {
                color: #d2d3d7;
            }
        }

        .danger {
            color: #ff3333;

            .iconfont {
                color: #ff3333;
            }
        }

        .verify-item {
            display: flex;
            min-height: 38px;
            padding: 0 var(--abc-paddingLR-l);
            font-size: var(--abc-font-size-normal);

            .verify-item__title {
                display: flex;
                min-width: 108px;
                line-height: 40px;
                color: var(--abc-color-T2);
            }

            .verify-item__content {
                flex: 1;
                width: 0;

                li {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 7px 0;

                    .abstract {
                        flex: 1;
                        font-size: var(--abc-font-size-normal);
                        line-height: 26px;
                    }

                    .opt-btn-wrapper {
                        position: relative;
                        margin-left: auto;

                        .cancel-btn {
                            color: var(--abc-color-T2);
                        }
                    }

                    &.done {
                        .status,
                        .abstract,
                        .cis-icon-Attention {
                            color: var(--abc-color-T3);
                            text-decoration: line-through;
                        }
                    }

                    &.has-border {
                        border-bottom: 1px dashed var(--abc-color-P8);
                    }
                }

                .status {
                    align-self: flex-start;
                    min-width: 46px;
                    padding-left: var(--abc-paddingLR-sm);
                    margin-right: 24px;
                    font-size: var(--abc-font-size-normal);
                    line-height: 26px;

                    .iconfont {
                        margin-right: 4px;
                        font-size: var(--abc-font-size-normal);
                    }
                }
            }

            .medicine-restricts-wrapper {
                padding: var(--abc-padding-normal);
                margin-top: 16px;
                font-size: var(--abc-font-size-mini);
                color: var(--abc-color-T2);
                border: 1px dashed var(--abc-color-P3);
                border-radius: var(--abc-border-radius-mini);
            }
        }

        .outpatient-verify-popper {
            position: absolute;
            bottom: 99%;
            left: 50px;
            z-index: 9999;
            width: 565px;
            max-height: 700px;
            padding: 16px;
            overflow-y: auto;
            overflow-y: overlay;
            background: rgba(255, 253, 236, 1);
            border: 1px solid rgba(203, 184, 22, 1);
            box-shadow: 2px 2px 0 0 rgba(0, 0, 0, 0.1);
        }

        .content-regulation {
            display: flex;
            flex-direction: column;
            font-size: var(--abc-font-size-mini);

            .left-content {
                margin-bottom: 8px;
                font-weight: bold;
                line-height: 16px;

                .normal-tips {
                    font-size: var(--abc-font-size-mini);
                    font-weight: normal;
                    color: var(--abc-color-T2);
                }
            }

            .right-content-item + .right-content-item {
                margin-top: 8px;
            }

            .right-content-title {
                margin-bottom: 4px;
                font-weight: bold;
                line-height: 22px;
            }

            .label-status {
                display: flex;
                color: var(--abc-color-T2);

                .cn-name {
                    display: flex;
                    align-items: center;
                    color: var(--abc-color-T2);
                }

                .status {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    min-width: 56px;
                    margin-left: auto;
                }

                i {
                    margin-right: 4px;
                    font-size: var(--abc-font-size-mini);
                }

                &.pass .status {
                    color: var(--abc-color-G2);
                }

                &.warn {
                    color: var(--abc-color-Y2);
                }

                &.danger {
                    color: var(--abc-color-R2);

                    .iconfont {
                        color: var(--abc-color-R2);
                    }
                }
            }

            .verify-detail-wrapper {
                padding: var(--abc-padding-normal);
                margin-top: 4px;
                margin-bottom: 10px;
                font-weight: bold;
                border: 1px dashed var(--abc-color-P3);
                border-radius: var(--abc-border-radius-mini);

                li {
                    line-height: 16px;

                    & + li {
                        margin-top: 4px;
                    }
                }

                .drug-restricts {
                    color: var(--abc-color-T2);
                }
            }
        }

        // select文本模式 tiny 高26、size 12，组件库目前满足不了，设计龙哥说这里覆写
        .abc-select-wrapper.text-mode {
            .abc-select-text-area {
                font-size: var(--abc-font-size-normal);
            }

            &:hover {
                color: var(--abc-color-T2);
                background: var(--abc-color-cp-grey4);
                border-radius: var(--abc-border-radius-small);
            }
        }
    }

    .medical-insurance-restriction-popover {
        padding: 8px 6px;
        font-size: var(--abc-font-size-mini);
        line-height: 16px;
        color: var(--abc-color-T2);
        text-align: justify;

        h4 {
            font-weight: bold;
            color: var(--abc-color-T1);
        }
    }
</style>
