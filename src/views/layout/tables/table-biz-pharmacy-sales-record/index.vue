<template>
    <div class="hospital-biz-pharmacy-sales-record-table-wrapper">
        <abc-table
            ref="abcTable"
            class="hospital-biz-pharmacy-sales-record-table"
            :render-config="renderConfig"
            :data-list="curDataList"
            :loading="loading"
            :custom-tr-class="customTrClass"
            @handleClickTr="handleClickTr"
        >
            <template #orderNo="{ trData }">
                <abc-table-cell>
                    {{ trData.orderNo }}
                </abc-table-cell>
            </template>
            <template #status="{ trData }">
                <abc-table-cell>
                    <abc-tag-v2 :theme="chargeStatusTheme(trData.status)" variant="outline">
                        {{ chargeStatusLabel[trData.status] || '' }}
                    </abc-tag-v2>
                </abc-table-cell>
            </template>
            <template #source="{ trData }">
                <abc-table-cell :title="getSourceStr(trData)">
                    <div class="ellipsis">
                        {{ getSourceStr(trData) }}
                    </div>
                </abc-table-cell>
            </template>
            <template #grossProfitRate="{ trData }">
                <abc-table-cell>
                    <abc-flex
                        justify="end"
                        align="center"
                        style="width: 100%; height: 100%;"
                        @click.stop="openQueryProfitDialog(trData)"
                    >
                        <abc-text :theme="trData.grossProfitRate < 0 ? 'warning-light' : 'primary'">
                            {{ getProfitRateStr(trData) }}
                        </abc-text>
                    </abc-flex>
                </abc-table-cell>
            </template>
            <template #grossProfit="{ trData }">
                <abc-table-cell>
                    <abc-flex
                        justify="end"
                        align="center"
                        style="width: 100%; height: 100%;"
                    >
                        <abc-text>
                            {{ getProfitStr(trData) }}
                        </abc-text>
                    </abc-flex>
                </abc-table-cell>
            </template>
        </abc-table>
    </div>
</template>

<script>
    import TableConfig from './index.js';
    import {
        ChargeSheetTypeEnum,
        ChargeStatusEnum,
        ChargeStatusLabel,
    } from '@/service/charge/constants';
    import { formatMoney } from '@abc/utils';

    export default {
        name: 'BizPharmacySalesRecordProTable',
        props: {
            dataList: {
                type: Array,
                default: () => [],
            },
            loading: {
                type: Boolean,
                default: false,
            },
            summary: {
                type: Object,
            },

            // 展示开单来源
            showSource: {
                type: Boolean,
                default: false,
            },

            // 展示毛利率
            showProfit: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                ChargeStatusEnum,
                paginationLimit: 10,
            };
        },
        computed: {
            chargeStatusLabel() {
                return ChargeStatusLabel;
            },
            renderConfig() {
                return TableConfig.getRenderConfig({
                    showSource: this.showSource,
                    showProfit: this.showProfit,
                });
            },
            curDataList() {
                return this.dataList.map((it) => {
                    it.customerName = it.memberInfo?.name || it.customerInfo?.name || '';
                    it.customerMobile = it.memberInfo?.mobile || it.customerInfo?.mobile || '';
                    it.sellerName = it.seller?.name || '' ;
                    it.cashierName = it.cashiers?.map((cashier) => cashier.name)?.join('、') || it.cashier?.name || '';
                    it.pharmacistName = it.pharmacist?.name || '';
                    return it;
                });
            },

            tablePagination() {
                return {
                    showTotalPage: true,
                    pageIndex: 0,
                    pageSize: this.paginationLimit,
                    count: this.totalCount,
                };
            },
        },
        methods: {
            chargeStatusTheme(status) {
                let type = '';

                switch (status) {
                    case ChargeStatusEnum.UN_CHARGE:
                    case ChargeStatusEnum.PART_CHARGED:
                        type = 'primary';
                        break;
                    case ChargeStatusEnum.CHARGED:
                    case ChargeStatusEnum.PART_REFUND:
                        type = 'success';
                        break;
                    case ChargeStatusEnum.REFUND:
                        type = 'danger';
                        break;
                    case ChargeStatusEnum.CLOSED:
                        type = 'default';
                        break;
                    default:
                        break;
                }

                return type;
            },
            customTrClass() {
                const _arr = ['clickable'];
                return _arr.join(' ');
            },
            handleClickTr(item) {
                this.$emit('click-tr', item);
            },
            getSourceStr(item) {
                const {
                    type,
                    coSourceClinicName,
                } = item;
                if (type === ChargeSheetTypeEnum.DIRECT_SALE) {
                    return '零售';
                }
                if (type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                    return `合作诊所-${coSourceClinicName || ''}`;
                }
                return '';
            },

            getProfitRateStr(item) {
                const {
                    status,
                    grossProfitRate,
                } = item;

                if (
                    [
                        ChargeStatusEnum.CHARGED,
                        ChargeStatusEnum.PART_REFUND,
                    ].indexOf(status) === -1
                ) {
                    return '-';
                }
                return `${((grossProfitRate || 0) * 100).toFixed(2)}%`;
            },
            getProfitStr(item) {
                const {
                    status,
                    grossProfit,
                } = item;

                if (
                    [
                        ChargeStatusEnum.CHARGED,
                        ChargeStatusEnum.PART_REFUND,
                    ].indexOf(status) === -1
                ) {
                    return '-';
                }
                return `${formatMoney(grossProfit || 0)}`;
            },
            openQueryProfitDialog(item) {
                this.$emit('click-profit', item);
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .hospital-biz-pharmacy-sales-record-table-wrapper {
        position: relative;
    }
</style>

