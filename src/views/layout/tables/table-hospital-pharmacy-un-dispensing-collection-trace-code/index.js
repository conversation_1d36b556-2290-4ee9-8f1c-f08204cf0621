import BaseProTable from '@/views/layout/tables/base-pro-table.js';

export default class HospitalPharmacyUnDispensingCollectionTraceCodeTable extends BaseProTable {
    name = 'HospitalPharmacyUnDispensingCollectionTraceCodeTable';

    // 由产品.设计提供的静态配置, 开发只能修改key
    static staticConfig = {
        hasInnerBorder: true,
        list: [
            {
                key: 'goodsName',
                label: '药品名称',
                style: {
                    flex: 1,
                    minWidth: '200px',
                    textAlign: 'left',
                },
            },
            {
                key: 'spec',
                label: '规格',
                style: {
                    width: '100px',
                    maxWidth: '100px',
                    minWidth: '100px',
                    textAlign: 'left',
                },
            },
            {
                key: 'batches',
                label: '退药批次',
                style: {
                    width: '100px',
                    maxWidth: '100px',
                    minWidth: '100px',
                    textAlign: 'left',
                },
            },
            {
                key: 'unitCount',
                label: '退药数量',
                style: {
                    width: '74px',
                    maxWidth: '74px',
                    minWidth: '74px',
                    textAlign: 'left',
                },
            },
            {
                key: 'traceCode',
                label: '追溯码',
                style: {
                    width: '400px',
                    maxWidth: '400px',
                    minWidth: '400px',
                    textAlign: 'left',
                },
            },
            {
                key: 'comment',
                label: '拒退原因',
                style: {
                    width: '200px',
                    maxWidth: '200px',
                    minWidth: '200px',
                    textAlign: 'left',
                },
            },
        ],
    };

    static summaryRenderKeys = [];

    static getRenderConfig() {
        return this.staticConfig;
    }
}
