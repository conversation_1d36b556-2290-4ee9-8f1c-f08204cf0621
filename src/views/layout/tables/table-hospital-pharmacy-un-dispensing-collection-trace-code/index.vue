<template>
    <div class="hospital-hospital-pharmacy-un-dispensing-collection-trace-code-table-wrapper">
        <abc-table
            class="hospital-hospital-pharmacy-un-dispensing-collection-trace-code-table"
            type="excel"
            :render-config="renderConfig"
            :data-list="dataList"
            fill-height
            :fill-reference-el="fillReferenceEl"
            :show-all-checkbox="false"
            :show-checked="false"
            :tr-click-trigger-checked="false"
            :fixed-tr-height="false"
            :show-hover-tr-bg="false"
        >
            <template #goodsName="{ trData: item }">
                <abc-flex v-if="item.isChineseForm" vertical style="width: 100%;">
                    <abc-table-cell
                        v-for="(chineseItem, chineseItemIdx) in item.dispensingFormItems"
                        :key="chineseItem.id"
                        align="center"
                        :style="chineseItemIdx < item.dispensingFormItems.length - 1 ? { borderBottom: '1px solid var(--abc-color-card-divider-color)' } : {}"
                    >
                        <abc-text class="ellipsis" :title="chineseItem.renderData ? (chineseItem.renderData.goodsName || '') : ''">
                            {{ chineseItem.renderData ? (chineseItem.renderData.goodsName || '-') : '-' }}
                        </abc-text>
                        <abc-text
                            v-if="[dispenseOrderOperationTypeEnum.REJECT_RETURN, dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN].includes(item.dispenseType)"
                            theme="gray-light"
                            size="small"
                            style="margin-left: 8px;"
                        >
                            拒退
                        </abc-text>
                    </abc-table-cell>
                </abc-flex>
                <abc-table-cell v-else align="center">
                    <abc-text class="ellipsis" :title="item.renderData ? (item.renderData.goodsName || '') : ''">
                        {{ item.renderData ? (item.renderData.goodsName || '-') : '-' }}
                    </abc-text>
                    <abc-text
                        v-if="[dispenseOrderOperationTypeEnum.REJECT_RETURN, dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN].includes(item.dispenseType)"
                        theme="gray-light"
                        size="small"
                        style="margin-left: 8px;"
                    >
                        拒退
                    </abc-text>
                </abc-table-cell>
            </template>

            <template #spec="{ trData: item }">
                <abc-flex v-if="item.isChineseForm" vertical style="width: 100%;">
                    <abc-table-cell
                        v-for="(chineseItem, chineseItemIdx) in item.dispensingFormItems"
                        :key="chineseItem.id"
                        align="center"
                        :style="chineseItemIdx < item.dispensingFormItems.length - 1 ? { borderBottom: '1px solid var(--abc-color-card-divider-color)' } : {}"
                    >
                        <abc-text class="ellipsis" :title="chineseItem.renderData ? (chineseItem.renderData.spec || '') : ''">
                            {{ chineseItem.renderData ? (chineseItem.renderData.spec || '-') : '-' }}
                        </abc-text>
                    </abc-table-cell>
                </abc-flex>
                <abc-table-cell v-else>
                    <abc-text class="ellipsis" :title="item.renderData ? (item.renderData.spec || '') : ''">
                        {{ item.renderData ? (item.renderData.spec || '-') : '-' }}
                    </abc-text>
                </abc-table-cell>
            </template>

            <template #batches="{ trData: item }">
                <abc-flex v-if="item.isChineseForm" vertical style="width: 100%;">
                    <abc-table-cell
                        v-for="(chineseItem, chineseItemIdx) in item.dispensingFormItems"
                        :key="chineseItem.id"
                        align="center"
                        :style="chineseItemIdx < item.dispensingFormItems.length - 1 ? { borderBottom: '1px solid var(--abc-color-card-divider-color)' } : {}"
                    >
                        -
                    </abc-table-cell>
                </abc-flex>
                <template v-else-if="showBatches(item)">
                    <template v-if="item.dispensingFormItemBatches.length <= 2">
                        <abc-table-cell
                            v-for="itemBatch in item.dispensingFormItemBatches"
                            :key="itemBatch.batchId"
                            style="color: var(--abc-color-theme1);"
                        >
                            <abc-text class="ellipsis" :title="itemBatch.batchNo || ''">
                                {{ itemBatch.batchNo || '-' }}
                            </abc-text>
                        </abc-table-cell>
                    </template>
                    <abc-table-cell v-else style="color: var(--abc-color-theme1);">
                        <abc-text class="ellipsis" :title="`${item.dispensingFormItemBatches[0].batchNo} 等${item.dispensingFormItemBatches.length}个批次`">
                            {{ item.dispensingFormItemBatches[0].batchNo }} 等{{ item.dispensingFormItemBatches.length }}个批次
                        </abc-text>
                    </abc-table-cell>
                </template>
                <abc-table-cell v-else>
                    -
                </abc-table-cell>
            </template>

            <template #unitCount="{ trData: item }">
                <abc-flex v-if="item.isChineseForm" vertical style="width: 100%;">
                    <abc-table-cell
                        v-for="(chineseItem, chineseItemIdx) in item.dispensingFormItems"
                        :key="chineseItem.id"
                        align="center"
                        :style="chineseItemIdx < item.dispensingFormItems.length - 1 ? { borderBottom: '1px solid var(--abc-color-card-divider-color)' } : {}"
                    >
                        <abc-text v-if="[dispenseOrderOperationTypeEnum.REJECT_RETURN, dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN].includes(item.dispenseType)">
                            0{{ chineseItem.unit || 'g' }}*{{ chineseItem.doseCount }}
                        </abc-text>
                        <abc-text
                            v-else
                            class="ellipsis"
                            :title="chineseItem.renderData ? (chineseItem.renderData.statusCountStr || '') : ''"
                        >
                            {{ chineseItem.renderData ? (chineseItem.renderData.statusCountStr || '-') : '-' }}
                        </abc-text>
                    </abc-table-cell>
                </abc-flex>
                <abc-table-cell v-else>
                    <abc-text v-if="[dispenseOrderOperationTypeEnum.REJECT_RETURN, dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN].includes(item.dispenseType)">
                        {{ item.originApplyCount ? formatCount(item.productInfo, 0, item.useDismounting) : '-' }}
                    </abc-text>
                    <abc-text v-else class="ellipsis" :title="item.originApplyCount ? formatCount(item.productInfo, item.originApplyCount, item.useDismounting) : ''">
                        {{ item.originApplyCount ? formatCount(item.productInfo, item.originApplyCount, item.useDismounting) : '-' }}
                    </abc-text>
                </abc-table-cell>
            </template>

            <template #traceCode="{ trData: item }">
                <template v-if="item.isChineseForm">
                    <template v-for="(chineseItem, chineseItemIdx) in item.dispensingFormItems">
                        <abc-table-cell
                            v-if="[dispenseOrderOperationTypeEnum.REJECT_RETURN, dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN].includes(item.dispenseType)"
                            :key="`hospital-pharmacy-collection-trace-code-reject-un-dispensing-chinses-${chineseItem.id}`"
                            :style="chineseItemIdx < item.dispensingFormItems.length - 1 ? { borderBottom: '1px solid var(--abc-color-card-divider-color)' } : {}"
                        >
                            -
                        </abc-table-cell>
                        <template v-else-if="isCompatibleHistoryData(chineseItem) || isNoTraceCodeGoods(chineseItem)">
                            <abc-flex
                                v-if="canSetTraceCode(chineseItem)"
                                :key="`hospital-pharmacy-un-dispensing-collection-trace-code-compatible-history-chinese-${chineseItem.id}`"
                                style="height: var(--abc-table-cell-height-default);"
                                :style="chineseItemIdx < item.dispensingFormItems.length - 1 ? { borderBottom: '1px solid var(--abc-color-card-divider-color)' } : {}"
                            >
                                <abc-select
                                    v-model="chineseItem.selectedTraceCodeList"
                                    multiple
                                    correct-multiple-clear-value
                                    multi-label-mode="text"
                                    adaptive-width
                                    :inner-width="250"
                                    with-search
                                    clearable
                                    :max-tag="1"
                                    size="small"
                                    :disabled="chineseItem.disabledTraceCodeList"
                                    disabled-input-enter
                                    :fetch-suggestions="(key) => handleSearch(key, chineseItem)"
                                >
                                    <abc-option
                                        v-for="(option, key) in chineseItem._traceableCodeListOptions"
                                        :key="`hospital-pharmacy-un-dispensing-collection-trace-code-compatible-history-select-option-chinese-${chineseItem.id}-${key}`"
                                        :value="option.keyId"
                                        :label="option.no"
                                    >
                                        {{ option.no }}
                                        <abc-text style="margin-left: 8px;" theme="gray">
                                            {{ option.unit ? `${option.count}${option.unit}` : `x${option.count}` }}
                                        </abc-text>
                                    </abc-option>
                                </abc-select>
                            </abc-flex>
                            <abc-table-cell
                                v-else
                                :key="`hospital-pharmacy-collection-trace-code-no-trace-code-chinese-${chineseItem.id}`"
                                :style="chineseItemIdx < item.dispensingFormItems.length - 1 ? { borderBottom: '1px solid var(--abc-color-card-divider-color)' } : {}"
                            >
                                <abc-text theme="gray" class="ellipsis" :title="isNoTraceCodeGoods(chineseItem) ? '无码商品' : '发药时未采集追溯码'">
                                    {{ isNoTraceCodeGoods(chineseItem) ? '无码商品' : '发药时未采集追溯码' }}
                                </abc-text>
                            </abc-table-cell>
                        </template>
                        <refund-trace-code-operation
                            v-else
                            :key="`hospital-pharmacy-un-dispensing-collection-trace-code-new-chinese-${chineseItem.id}`"
                            :ref="`traceCodeOperation-${chineseItem.id}`"
                            :code="chineseItem"
                            :custom-operation-style="{ minHeight: '40px' }"
                            @submit="(list) => handleRefundTraceCodeOperationSubmit(chineseItem, list)"
                        ></refund-trace-code-operation>
                    </template>
                </template>
                <template v-else>
                    <abc-table-cell
                        v-if="[dispenseOrderOperationTypeEnum.REJECT_RETURN, dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN].includes(item.dispenseType)"
                        :key="`hospital-pharmacy-collection-trace-code-reject-un-dispensing-${item.id}`"
                    >
                        -
                    </abc-table-cell>
                    <template v-else-if="isCompatibleHistoryData(item) || isNoTraceCodeGoods(item)">
                        <abc-flex
                            v-if="canSetTraceCode(item)"
                            :key="`hospital-pharmacy-collection-trace-code-compatible-history-${item.id}`"
                            style="height: var(--abc-table-cell-height-default);"
                        >
                            <abc-select
                                v-model="item.selectedTraceCodeList"
                                multiple
                                correct-multiple-clear-value
                                multi-label-mode="text"
                                adaptive-width
                                :inner-width="250"
                                with-search
                                clearable
                                :max-tag="1"
                                size="small"
                                :disabled="item.disabledTraceCodeList"
                                disabled-input-enter
                                :fetch-suggestions="(key) => handleSearch(key, item)"
                            >
                                <abc-option
                                    v-for="(option, key) in item._traceableCodeListOptions"
                                    :key="`hospital-pharmacy-un-dispensing-collection-trace-code-compatible-history-select-option-${item.id}-${key}`"
                                    :value="option.keyId"
                                    :label="option.no"
                                >
                                    {{ option.no }}
                                    <abc-text style="margin-left: 8px;" theme="gray">
                                        {{ option.unit ? `${option.count}${option.unit}` : `x${option.count}` }}
                                    </abc-text>
                                </abc-option>
                            </abc-select>
                        </abc-flex>
                        <abc-table-cell
                            v-else
                            :key="`hospital-pharmacy-collection-trace-code-no-trace-code-${item.id}`"
                        >
                            <abc-text theme="gray" class="ellipsis" :title="isNoTraceCodeGoods(item) ? '无码商品' : '发药时未采集追溯码'">
                                {{ isNoTraceCodeGoods(item) ? '无码商品' : '发药时未采集追溯码' }}
                            </abc-text>
                        </abc-table-cell>
                    </template>
                    <refund-trace-code-operation
                        v-else
                        :key="`hospital-pharmacy-un-dispensing-collection-trace-code-new-${item.id}`"
                        :ref="`traceCodeOperation-${item.id}`"
                        :code="item"
                        :custom-operation-style="{ minHeight: '40px' }"
                        @submit="(list) => handleRefundTraceCodeOperationSubmit(item, list)"
                    ></refund-trace-code-operation>
                </template>
            </template>

            <template #comment="{ trData: item }">
                <abc-input
                    v-if="[dispenseOrderOperationTypeEnum.REJECT_RETURN, dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN].includes(item.dispenseType)"
                    v-model="item.renderData.comment"
                    v-abc-focus-selected
                    adaptive-width
                    placeholder="输入拒退原因"
                ></abc-input>
            </template>
        </abc-table>
    </div>
</template>

<script>
    import TableConfig from './index.js';
    import {
        canSetTraceCode, handleSearch,
        isCompatibleHistoryData, isNoTraceCodeGoods, showBatches,
    } from 'views/pharmacy/utils.js';
    import { formatCount } from '@/views-hospital/pharmacy/utils/index.js';
    import { dispenseOrderOperationTypeEnum } from '@/views-hospital/pharmacy/utils/constant';
    import RefundTraceCodeOperation from 'views/pharmacy/refund-trace-code-operation.vue';

    export default {
        name: 'HospitalPharmacyUnDispensingCollectionTraceCodeProTable',
        components: { RefundTraceCodeOperation },
        props: {
            dataList: {
                type: Array,
                default: () => [],
            },
            fillReferenceEl: {
                type: HTMLElement,
                default: null,
            },
        },
        data() {
            return {
                dispenseOrderOperationTypeEnum,
            };
        },
        computed: {
            renderConfig() {
                return TableConfig.getRenderConfig();
            },
        },
        methods: {
            handleSearch,
            showBatches,
            formatCount,
            isCompatibleHistoryData,
            isNoTraceCodeGoods,
            canSetTraceCode,
            handleRefundTraceCodeOperationSubmit(item, list) {
                this.$set(item, 'selectedTraceCodeList', list);
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/abc-common.scss";

    .hospital-hospital-pharmacy-un-dispensing-collection-trace-code-table-wrapper {
        position: relative;
        height: 100%;

        .hospital-hospital-pharmacy-un-dispensing-collection-trace-code-table {
            height: 100% !important;
        }

        .hospital-pharmacy-un-dispensing-collection-trace-code-table-chinese-tr {
            width: 100%;
            height: 40px;
            min-height: var(--abc-table-cell-height-default);
            padding: 0 var(--abc-table-cell-padding-default);
            padding-left: calc(var(--abc-table-cell-padding-default) + 4px);
            line-height: 22px;
            border-bottom: 1px solid var(--abc-color-card-divider-color);
        }
    }
</style>

