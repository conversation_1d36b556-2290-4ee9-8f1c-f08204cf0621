<template>
    <abc-container class="abc-clinics-gsp-container">
        <abc-card :border="false" style="border-radius: 0;">
            <abc-tabs-v2
                :value="currentTab"
                :option="options"
                style="padding-left: 8px;"
                size="huge"
                @change="onChangeTab"
            ></abc-tabs-v2>
        </abc-card>

        <abc-card
            :border="false"
            :shadow="false"
            style="flex: 1; overflow-y: auto; border-top: 1px solid var(--abc-color-P8); border-radius: 0;"
        >
            <router-view no-border-radius></router-view>
        </abc-card>
    </abc-container>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { createAbcPage } from '@/core/page/factory';

    export default {
        name: 'ClinicsGsp',
        mixins: [
            createAbcPage({
                theme: 'pharmacy',
            }),
        ],
        data() {
            return {
                supName: '@PharmacyGsp',
            };
        },
        computed: {
            ...mapGetters([
                'gspTodo',
                'gspDestroyTodo',
                'materialMaintenanceTodoCount',
            ]),
            todoData() {
                return {
                    'storageTodo': Number(this.gspTodo + this.materialMaintenanceTodoCount),
                    'gspTodo': this.gspTodo,
                    'gspDestroyTodo': this.gspDestroyTodo,
                    'materialMaintenanceTodoCount': this.materialMaintenanceTodoCount,
                };
            },
            // 当前选中的tab
            currentTab() {
                const target = this.options.find((item) => this.$route.name.startsWith(item.value));
                return target?.value || '';
            },
            // 路由选项
            options() {
                const children = this.findChildren(this.$router.options.routes, this.supName);
                return children.map((item) => {
                    let noticeNumber = 0;
                    if (item.meta.todoKey === 'purchaseTodoAll') {
                        noticeNumber = (item.children || []).reduce((accumulator, val) => {
                            if (val && val.meta && this.todoData && this.todoData[val.meta.todoKey]) {
                                return accumulator + this.todoData[val.meta.todoKey];
                            }
                            return accumulator;
                        }, 0);
                    } else {
                        noticeNumber = (this.todoData && this.todoData[item.meta.todoKey]) || 0;
                    }
                    return {
                        value: item.name,
                        label: item.meta.name,
                        noticeNumber,
                        maxNoticeNumber: 99,
                    };
                });
            },
        },
        methods: {
            /**
             * 找到子路由选项
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Array} routes
             * @param {String} supName
             * @returns {Array}
             */
            findChildren(routes, supName) {
                let children = [];
                for (let i = 0; i < routes.length; i++) {
                    const item = routes[i];
                    if (item.name === supName) {
                        children = item.children;
                        break;
                    }
                    if (item.children) {
                        children = this.findChildren(item.children, supName);
                        if (children.length) {
                            break;
                        }
                    }
                }
                return children;
            },
            /**
             * 当切换tab时触发的事件
             * <AUTHOR>
             * @date 2023-12-25
             * @param {String} value
             */
            onChangeTab(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.abc-clinics-gsp-container {
    display: flex;
    flex: 1;
    flex-direction: column;

    .abc-layout-content {
        display: flex;
        flex-direction: column;
    }
}
</style>
