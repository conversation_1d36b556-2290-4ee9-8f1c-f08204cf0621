<template>
    <abc-container class="abc-physical-examination-integrated-report-manager-container">
        <abc-manage-tabs
            size="huge"
            :option="tabsOption"
            style="background: var(--abc-color-bg-section);"
            @change="handleTabsChange"
        ></abc-manage-tabs>
        <router-view></router-view>
    </abc-container>
</template>

<script>
    import { AbcManageTabs } from 'views/settings/components/abc-manage';
    const tabsOption = [
        {
            label: '个人报告',
            value: '@PhysicalExaminationIntegratedReportManagerIndividual',
        },
        {
            label: '团队报告',
            value: '@PhysicalExaminationIntegratedReportManagerTeam',
        },
        // {
        //     label: '公卫报告',
        //     value: '@PhysicalExaminationIntegratedReportManagerCommonHealthy',
        // },
    ];
    export default {
        name: 'PhysicalExaminationIntegratedReportManager',
        components: { AbcManageTabs },
        data() {
            return {
                tabsOption,
            };
        },
        methods: {
            handleTabsChange(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>

<style lang="scss">
    @import './_index.scss';

    .abc-physical-examination-integrated-report-manager-container {
        display: flex;
        flex-direction: column;

        .abc-manage-tabs.abc-tabs {
            width: 100%;
            background: var(--abc-color-bg-section);
            border-bottom-color: var(--abc-color-card-border-color);
        }
    }
</style>


