<template>
    <abc-dialog
        v-if="isShowDialog"
        v-show="dialogVisible"
        v-model="isShowDialog"
        append-to-body
        :auto-focus="false"
        :title="title"
        size="large"
        custom-class="preview-report-dialog"
        :content-styles="`padding: 0;height: calc(100vh - 285px);`"
    >
        <section ref="previewContent" v-abc-loading="loading" class="preview-content">
            <iframe
                ref="iframe"
                scrolling="no"
                :srcdoc="previewHTML"
                frameborder="0"
                style="width: 100%; height: 100%;"
            ></iframe>
        </section>
        <div slot="footer" class="dialog-footer">
            <div v-if="(!isWaitReportReleased && isOpenMp) && !isReadonly" style="margin-right: auto;">
                <abc-button
                    v-if="releaseButtonVisible "
                    :loading="btnLoading"
                    variant="ghost"
                    @click="handleChangeWeClinicStatus(weClinicStatus.show)"
                >
                    发送到客户微信
                </abc-button>

                <abc-button
                    v-if="cancelReleaseButtonVisible"
                    :loading="btnLoading"
                    variant="ghost"
                    @click="handleChangeWeClinicStatus(weClinicStatus.hide)"
                >
                    取消发送微信
                </abc-button>
            </div>

            <abc-space>
                <abc-button
                    v-if="!isReadonly"
                    :loading="checkLoading"
                    :theme="isWaitReportReleased ? 'primary' : 'danger'"
                    :variant="isWaitReportReleased ? 'fill' : 'ghost'"
                    @click="handleReportStatus"
                >
                    {{ isWaitReportReleased ? '发布报告' : '取消发布' }}
                </abc-button>

                <abc-button v-if="isReportReleased" @click="handlePrintReport">
                    打印
                </abc-button>

                <template v-if="isReportReleased">
                    <abc-button theme="primary" variant="outline" @click="handleDownload">
                        <template v-if="downloadStatusObj.isFailed">
                            <abc-space :size="4">
                                <abc-icon icon="n-alert-fill" size="14" color="#FF9933"></abc-icon>

                                <span>
                                    重新下载
                                </span>
                            </abc-space>
                        </template>

                        <template v-else-if="downloadStatusObj.isInline">
                            <abc-space :size="4">
                                <abc-loading-spinner small blue></abc-loading-spinner>

                                <span>
                                    报告生成中
                                </span>
                            </abc-space>
                        </template>

                        <template v-else>
                            下载
                        </template>
                    </abc-button>
                </template>

                <abc-button theme="primary" variant="ghost" @click="isShowDialog = false">
                    取消
                </abc-button>
            </abc-space>
        </div>
    </abc-dialog>
</template>

<script>
    import PrintMixin from '@/views/physical-examination/integrated/report-manager/mixin/print';
    import {
        OrderStatusEnum, PEOrderActionEnum,
    } from 'views/physical-examination/constants';
    import PhysicalExaminationAPI from 'api/physical-examination/pe-order';
    import { ABCPrintConfigKeyMap } from '@/printer/constants';
    import AbcPrinter from '@/printer';
    import { debounce } from 'utils/lodash';
    import { injectContentToHTML } from '@/printer/utils/electron';
    import PreAbcPrint from '@/printer/index-v2/pre-print-handler';
    import {
        PEGroupReportDownloadStatusEnum,
        WE_CLINIC_RELEASE_STATUS,
    } from 'views/physical-examination/integrated/report-manager/utils/constants';
    import { mapGetters } from 'vuex';
    import { printIndividualReport } from 'views/physical-examination/integrated/report-manager/utils/print';
    import {
        Print2HtmlService,
    } from 'views/physical-examination/integrated/report-manager/utils/html-to-pdf/print-html-service';
    import { formatDate } from '@abc/utils-date';
    import md5 from 'md5';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';

    export default {
        name: 'PreviewIndividualReportDialog',

        components: {
            AbcLoadingSpinner,
        },

        mixins: [PrintMixin],

        props: {
            value: {
                type: Boolean,
                required: true,
            },
            dataSource: {
                type: Object,
                required: true,
            },
            isReadonly: {
                type: Boolean,
                required: false,
            },
            title: {
                type: String,
                default: '预览体检报告',
            },
        },

        data() {
            return {
                OrderStatusEnum,
                previewHTML: '',
                checkLoading: false,
                printData: null,
                loading: false,
                dialogVisible: true,

                btnLoading: false,
                weClinicStatus: {
                    show: 1,
                    hide: 0,
                },
                printStatus: 0,
                pdfUrl: '',
                downloadStatus: '',
                downloadBtnLoading: false,
            };
        },

        computed: {
            ...mapGetters([
                'isOpenMp',
            ]),
            releaseButtonVisible() {
                if (!this.dataSource) return false;

                return this.dataSource.weClinicReleaseStatus === this.weClinicStatus.hide;
            },


            cancelReleaseButtonVisible() {
                if (!this.dataSource) return false;

                return this.dataSource.weClinicReleaseStatus === this.weClinicStatus.show;
            },

            isShowDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            //待发布
            isWaitReportReleased() {
                return this.dataSource.status === this.OrderStatusEnum.WAIT_REPORT_RELEASED;
            },
            //已发布
            isReportReleased() {
                return this.printStatus === this.OrderStatusEnum.REPORT_RELEASED;
            },

            downloadStatusObj() {
                return {
                    isInline: this.downloadStatus === PEGroupReportDownloadStatusEnum.atLine,
                    isCreated: this.downloadStatus === PEGroupReportDownloadStatusEnum.created,
                    isDownloaded: this.downloadStatus === PEGroupReportDownloadStatusEnum.downloaded,
                    isFailed: this.downloadStatus === PEGroupReportDownloadStatusEnum.failed,
                };
            },
        },

        created() {
            this.initDownload();
        },

        mounted() {
            this.debouncePrint = debounce(this.printReport, 250, true);
            PreAbcPrint.setGlobalConfig();
            this.init();
        },

        methods: {
            async initDownload() {
                if (this.isWaitReportReleased) {
                    return;
                }
                // 查询是否存在下载任务
                const res = await this.fetchReportPDFUrl();
                this.downloadStatus = res.reportTaskStatus;
                if (res) {
                    // 已经生成pdf准备下载
                    if (res.fileConvertTask) {
                        this.downloadBtnLoading = true;
                        this.downloadStatus = PEGroupReportDownloadStatusEnum.atLine;
                        // 轮询任务状态
                        this.poolingQueryDownloadStatus();
                    }
                }
            },

            init() {
                this.setIndividualReportPreviewHTML();
                // 拉取打印配置
                this.$store.dispatch('fetchPrintAllConfigIfNeed');
                AbcPrinter.setGlobalConfig();
            },

            handlePrintReport() {
                this.debouncePrint();
                this.dialogVisible = false;
            },

            async handleChangeWeClinicStatus(status) {
                const title = status === WE_CLINIC_RELEASE_STATUS.RELEASE ?
                    '发送报告到客户微信' :
                    '取消发送报告到客户微信';
                const confirmFn = async () => {
                    try {
                        this.btnLoading = true;
                        await PhysicalExaminationAPI.releasePEReportToWeClinic({
                            rows: [
                                {
                                    weClinicReleaseStatus: status,
                                    id: this.dataSource.id,
                                },
                            ],
                        });

                        // 前端更改数据，不发送请求
                        this.$emit('update-we-clinic-status', status);
                        this.$Toast({
                            message: '操作成功',
                            type: 'success',
                        });
                    } catch (e) {
                        console.error(e);
                    } finally {
                        this.btnLoading = false;
                    }
                };

                if (status === WE_CLINIC_RELEASE_STATUS.RELEASE) {
                    const alertInstance = this.$alert({
                        type: 'info',
                        title,
                        footerPrepend: (
                            <abc-flex justify="flex-end">
                                <abc-space>
                                    <abc-button onClick={() => { confirmFn(); alertInstance.close(); }}>
                                        确定
                                    </abc-button>
                                    <abc-button theme="primary" variant="ghost" onClick={() => alertInstance.close()}>
                                        取消
                                    </abc-button>
                                </abc-space>
                            </abc-flex>
                        ),
                        showCancel: false,
                        showConfirm: false,
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title,
                        onConfirm: confirmFn,
                    });
                }
            },

            async setIndividualReportPreviewHTML() {
                this.loading = true;
                try {
                    const data = await this.fetchPrintPreview(this.dataSource.id);
                    this.printStatus = data.status;
                    this.printData = this.covertPrintData(data);
                    console.debug(this.printData, 'individual-report-print-data');
                    const { peIndividualReport } = window.AbcPackages.AbcTemplates;
                    const abcPrintInstance = new window.AbcPackages.AbcPrint({
                        template: peIndividualReport, // 对应模板的key
                        printConfigKey: ABCPrintConfigKeyMap.peIndividualReport,
                        page: {
                            size: 'A4',
                            orientation: window.AbcPackages.AbcPrint.Orientation.portrait,
                            pageSizeReduce: {
                                left: 15,
                                right: 15,
                                top: 15,
                                bottom: 15,
                            },
                            customStyles: {
                                border: '1pt solid #ced0da',
                                margin: '12px auto',
                                boxShadow: 'rgba(0, 0, 0, 0.15) 0px 0px 4px 2px',
                            },
                        },
                        originData: this.printData,
                        extra: {
                            isEnableTransferNetworkImage: false,
                        },
                    });

                    await abcPrintInstance.init();
                    const html = await abcPrintInstance.splitPreview();
                    this.previewHTML = injectContentToHTML(html, `
                        <style>
                            html, body {
                                height: 100%;
                                overflow-y: auto;
                            }

                            body::-webkit-scrollbar {
                                width: 10px;
                                height: 14px; // 横向滚动条的宽度是height
                            }

                            body::-webkit-scrollbar-thumb { /* 滚动条里面小方块 */
                                cursor: pointer;
                                background: #e6eaed;
                                background-clip: content-box;
                                border: 1px solid transparent;
                                border-radius: var(--abc-border-radius-small);
                            }

                            body::-webkit-scrollbar-thumb:hover { /* 滚动条里面小方块 */
                                cursor: pointer;
                                background: #dee2e6;
                                background-clip: content-box;
                                border: 1px solid #dee2e6;
                                border-radius: var(--abc-border-radius-small);
                            }

                            body::-webkit-scrollbar-track { /* 滚动条里面轨道 */
                                background: transparent;
                                opacity: 0;
                            }

                            body::-webkit-scrollbar-thumb { /* 滚动条里面小方块 */
                                visibility: hidden;
                            }

                            body:hover::-webkit-scrollbar-thumb {
                                visibility: visible;
                            }

                            body:hover::-webkit-scrollbar-track { /* 滚动条里面轨道 */
                                background: transparent;
                                opacity: 0;
                            }

                             .abc-page {
                                zoom: 0.6;
                            }
                        </style>
                    `);
                } catch (error) {
                    console.error(error);
                }

                this.loading = false;
            },

            async handleReportStatus() {
                const {
                    id,
                } = this.dataSource;

                this.handleUpdateIndividualReportStatus(id);
            },

            handleUpdateIndividualReportStatus(id) {
                if (this.isWaitReportReleased) {
                    //去发布 打开发布确认弹窗
                    this.$emit('publish-report');
                }

                const cancelContent = this.releaseButtonVisible ?
                    '' :
                    '取消发布后，客户手机端将无法查看报告，再次发布后可查看';

                if (this.isReportReleased) {
                    //取消发布
                    this.$confirm({
                        type: 'warn',
                        title: '确认取消发布',
                        content: cancelContent,
                        onConfirm: () => {
                            this.updateIndividualReportStatus(id,PEOrderActionEnum.CANCEL_FINAL_REPORT_RELEASED);
                        },
                    });
                }
            },

            async updateIndividualReportStatus(id,action) {
                try {
                    this.checkLoading = true;
                    const params = {
                        action,
                        ids: [id],
                    };
                    await PhysicalExaminationAPI.updatePEOrderStatusByBatch(params);
                    this.$Toast({
                        message: '取消发布成功',
                        type: 'success',
                    });
                    this.$emit('refresh-list');
                    this.isShowDialog = false;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.checkLoading = false;
                }
            },

            printReport() {
                printIndividualReport({
                    data: this.printData,
                    onPrintSuccess: async () => {
                        try {
                            // 打印成功的回调
                            const postData = {
                                rows: [
                                    {
                                        id: this.printData.id,
                                        status: 10,
                                    },
                                ],
                            };
                            await PhysicalExaminationAPI.updatePEOrderPrintStatusByBatch(postData);
                            this.$emit('refresh-list');
                            this.isShowDialog = false;
                        } catch (e) {
                            console.error(e);
                        }
                    },
                    onPrintCancel: () => {
                        this.isShowDialog = false;
                    },
                });
            },

            async fetchReportPDFUrl() {
                try {
                    return await PhysicalExaminationAPI.fetchIndividualReportPDFUrl(this.dataSource.id);
                } catch (e) {
                    console.error(e);
                }
            },

            // 轮询下载状态
            poolingQueryDownloadStatus(maxRetry = 300) {
                if (this._isDestroyed) return;

                if (maxRetry <= 0) {
                    this.downloadBtnLoading = false;
                    this.downloadStatus = PEGroupReportDownloadStatusEnum.failed;
                    this.$Toast({
                        type: 'error',
                        message: '生成pdf超时，请稍后重试',
                    });
                    console.error('下载超时');
                    return;
                }

                const timer = window.setTimeout(async () => {
                    const res = await this.fetchReportPDFUrl();
                    this.downloadStatus = res.reportTaskStatus;
                    window.clearTimeout(timer);

                    if (res?.reportUrl) {
                        this.downloadPDF(res.reportUrl);
                        this.downloadBtnLoading = false;
                    } else {
                        this.poolingQueryDownloadStatus(--maxRetry);
                    }
                }, 1000);
            },

            downloadPDF(url) {
                const a = document.createElement('a');
                a.href = url;
                a.download = `${this.getFileName()}.pdf`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
            },

            getFileName() {
                return `${this.printData.patient.name || '匿名患者'}${this.printData.name}${formatDate(Date.now(), 'YYYY-MM-DD')}`;
            },

            // 生成 html file
            async createHtmlFile() {
                const { peIndividualReport } = window.AbcPackages.AbcTemplates;
                const print2HtmlService = new Print2HtmlService({
                    templateKey: peIndividualReport,
                    data: this.printData,
                });
                const html = await print2HtmlService.getHtml();

                const blob = new Blob([html], { type: 'text/plain' });
                const md5Code = md5(html);
                return {
                    file: new File([blob], `${md5Code}-${Date.now()}.html`),
                    md5Code,
                };
            },

            // 上传 html file 到 osss
            async uploadHtmlFile2OSS(file) {
                // 上传到 oss
                const res = await this.$abcPlatform.service.$oss.uploadForTemp(
                    { filePath: 'physical-examination' },
                    file,
                );

                return res.url;
            },

            // 生成下载任务
            async generateDownloadTask() {
                try {
                    const {
                        file, md5Code,
                    } = await this.createHtmlFile();
                    const htmlFileUrl = await this.uploadHtmlFile2OSS(file);

                    await PhysicalExaminationAPI.createIndividualReportPDF(this.dataSource.id, {
                        businessId: this.dataSource.id,
                        sourceMd5: md5Code,
                        sourcePath: new URL(htmlFileUrl).pathname.slice(1),
                        fileName: `${this.getFileName()}.pdf`,
                    });
                } catch (e) {
                    console.error(e);
                }
            },

            async handleDownload() {
                if (this.downloadBtnLoading) {
                    return;
                }

                const res = await this.fetchReportPDFUrl();

                if (res.reportUrl) {
                    this.downloadPDF(res.reportUrl);
                } else {
                    this.downloadStatus = PEGroupReportDownloadStatusEnum.atLine;
                    this.downloadBtnLoading = true;
                    await this.generateDownloadTask();
                    this.poolingQueryDownloadStatus();
                }
            },
        },
    };
</script>

<style scoped lang="scss">
.preview-report-dialog {
    .preview-content {
        position: relative;
        height: 100%;
        overflow: hidden;

        iframe {
            width: inherit;
            height: inherit;
            margin: 0 auto;
        }
    }
}
</style>
