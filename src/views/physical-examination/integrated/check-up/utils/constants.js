import {
    formatAge, formatMoney,
} from 'utils/index.js';
import { formatDate } from '@abc/utils-date';
import {
    OrderStatusLabelEnum,
    OrderStatusColorEnum,
    PEBusinessTypeEnum,
    PEOrderTypeEnum,
    PEPayStatusLabel,
} from 'views/physical-examination/constants.js';
import { MaritalStatusLabel } from 'views/crm/constants.js';
import { DEFAULT_CERT_TYPE } from '@/views/crm/constants';
import { PESourceType } from 'views/physical-examination/order/individual/helper/constant';

export const OrderTableHeader = [
    {
        label: '客户',
        width: 180,
        prop: 'displayPatientStr',
        fixed: true,
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        render: (h, row) => {
            const {
                patient,
            } = row;
            const {
                name,
                sex,
                age,
            } = patient || {};
            return (
                <div class="order-table-td-cell blue">
                    <span class="ellipsis" title={name} style="min-width: 58px;max-width: 64px">{name}</span>
                    <span>{sex}</span>
                    <span>{formatAge(age)}</span>
                    {
                        row.sourceType === PESourceType.ONLINE_THIRD_WE_APP ? (
                            <abc-tag-v2
                                variant="outline"
                                shape="square"
                                size="tiny"
                                theme="primary"
                                style={{ marginLeft: '6px' }}

                            >
                                线上
                            </abc-tag-v2>
                        ) : null
                    }
                </div>
            );
        },
    },
    {
        label: '婚姻',
        width: 60,
        prop: 'marital',
        fixed: true,
        align: 'center',
        titleAlign: 'center',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                patient,
            } = row;
            const {
                marital,
            } = patient || {};
            return MaritalStatusLabel[marital] || '';
        },
    },
    {
        label: '体检单号',
        width: 130,
        prop: 'no',
        fixed: true,
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
    },
    {
        label: '手机',
        width: 110,
        prop: 'mobile',
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                patient,
            } = row;
            const {
                mobile,
            } = patient || {};
            return mobile || '';
        },
    },
    {
        label: '身份证',
        width: 164,
        prop: 'idCard',
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                patient,
            } = row;
            const {
                idCard,
            } = patient || {};
            return idCard || '';
        },
    },
    {
        label: '个检/团检',
        width: 80,
        prop: 'type',
        align: 'center',
        titleAlign: 'center',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                type,
            } = row;
            if (type === PEOrderTypeEnum.INDIVIDUAL) {
                return '个检';
            }
            if (type === PEOrderTypeEnum.GROUP) {
                return '团检';
            }
            return '';
        },
    },
    {
        label: '体检类型',
        width: 80,
        prop: 'businessType',
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                businessType,
            } = row;
            if (businessType === PEBusinessTypeEnum.GENERAL) {
                return '普通体检';
            }
            if (businessType === PEBusinessTypeEnum.PUBLIC_HEALTH) {
                return '公卫体检';
            }
            return '';
        },
    },
    {
        label: '体检套餐',
        width: 190,
        prop: 'name',
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
    },
    {
        label: '预约体检日期',
        width: 100,
        prop: 'businessTime',
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                businessTime,
            } = row;
            return formatDate(businessTime, 'YYYY-MM-DD');
        },
    },
    {
        label: '体检状态',
        width: 80,
        prop: 'status',
        align: 'center',
        titleAlign: 'center',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        render: (h, row) => {
            const {
                status,
            } = row;
            const statusStr = OrderStatusLabelEnum[status];
            const color = OrderStatusColorEnum[status];
            return (
                <div class="order-table-td-cell center" style={{
                    color,
                }}>
                    {statusStr}
                </div>
            );
        },
    },
    {
        label: '订单金额',
        width: 90,
        prop: 'totalFee',
        align: 'right',
        titleAlign: 'right',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                totalFee,
            } = row;
            return formatMoney(totalFee);
        },
    },
    {
        label: '结算状态',
        width: 80,
        prop: 'chargeStatus',
        align: 'center',
        titleAlign: 'center',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                chargeStatus, businessType,
            } = row;

            if (businessType === PEBusinessTypeEnum.PUBLIC_HEALTH) {
                return '无需结算';
            }

            return PEPayStatusLabel[chargeStatus] || '';
        },
    },
    {
        label: '销售人',
        width: 72,
        prop: 'sellerName',
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                salesEmployee,
            } = row;
            return salesEmployee && salesEmployee.name || '';
        },
    },
    {
        label: '下单时间',
        width: 160,
        prop: 'orderCreated',
        align: 'left',
        titleAlign: 'left',
        thStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        tdStyle: {
            borderLeft: 'none',
            borderRight: 'none',
        },
        formatter: (row) => {
            const {
                orderCreated,
            } = row;
            return formatDate(orderCreated, 'YYYY-MM-DD HH:mm:ss');
        },
    },
];

export const PeTableConfig = Object.freeze(
    {
        hasInnerBorder: false,
        list: [
            {
                label: '客户',
                key: 'displayPatientStr',
                style: {
                    textAlign: 'left',
                    width: '180px',
                    maxWidth: '180px',
                },
                'pinned': 'left',
                // eslint-disable-next-line no-unused-vars
                customRender(h, row) {
                    const {
                        patient,
                    } = row;
                    const {
                        name,
                        sex,
                        age,
                    } = patient || {};
                    return (
                        <abc-table-cell class="custom-table-td-cell">
                            <span class="ellipsis" title={name}
                                style="min-width: 58px;max-width: 64px">{name}</span>
                            <span>{sex}</span>
                            <span>{formatAge(age)}</span>

                            {
                                row.sourceType === PESourceType.ONLINE_THIRD_WE_APP ? (
                                    <abc-tag-v2
                                        variant="outline"
                                        shape="square"
                                        size="tiny"
                                        theme="primary"
                                        style={{ marginLeft: '6px' }}
                                    >
                                        线上
                                    </abc-tag-v2>
                                ) : null
                            }
                        </abc-table-cell>
                    );
                },
            },
            {
                label: '婚姻',
                key: 'marital',
                'pinned': 'left',
                style: {
                    textAlign: 'center',
                    width: '60px',
                    maxWidth: '60px',
                },
                dataFormatter: (_, row) => {
                    const {
                        patient,
                    } = row;
                    const {
                        marital,
                    } = patient || {};
                    return MaritalStatusLabel[marital] || '';
                },
            },
            {
                label: '体检单号',
                'pinned': 'left',
                key: 'no',
                style: {
                    textAlign: 'left',
                    width: '130px',
                    maxWidth: '130px',
                },
            },
            {
                label: '手机',
                key: 'mobile',
                style: {
                    textAlign: 'left',
                    width: '110px',
                    maxWidth: '110px',
                },
                dataFormatter: (_, row) => {
                    const {
                        patient,
                    } = row;
                    const {
                        mobile,
                    } = patient || {};
                    return mobile || '';
                },
            },
            {
                label: '证件',
                key: 'idCard',
                style: {
                    textAlign: 'left',
                    width: '214px',
                    maxWidth: '214px',
                },
                dataFormatter: (_, row) => {
                    const {
                        patient,
                    } = row;
                    const {
                        idCard,
                        idCardType,
                    } = patient || {};
                    return idCard ? `[${idCardType || DEFAULT_CERT_TYPE}]\n${idCard}` : '';
                },
            },
            {
                label: '个检/团检',
                key: 'type',
                style: {
                    textAlign: 'center',
                    width: '80px',
                    maxWidth: '80px',
                },
                dataFormatter: (_, row) => {
                    const {
                        type,
                    } = row;
                    if (type === PEOrderTypeEnum.INDIVIDUAL) {
                        return '个检';
                    }
                    if (type === PEOrderTypeEnum.GROUP) {
                        return '团检';
                    }
                    return '';
                },
            },
            {
                label: '体检类型',
                key: 'businessType',
                style: {
                    textAlign: 'left',
                    width: '80px',
                    maxWidth: '80px',
                },
                dataFormatter: (_, row) => {
                    const {
                        businessType,
                    } = row;
                    if (businessType === PEBusinessTypeEnum.GENERAL) {
                        return '普通体检';
                    }
                    if (businessType === PEBusinessTypeEnum.PUBLIC_HEALTH) {
                        return '公卫体检';
                    }
                    return '';
                },
            },
            {
                label: '体检套餐',
                key: 'name',
                style: {
                    textAlign: 'left',
                    flex: 1,
                    minWidth: '160px',
                },
            },
            {
                label: '预约体检日期',
                key: 'businessTime',
                style: {
                    textAlign: 'left',
                    width: '100px',
                    maxWidth: '100px',
                },
                dataFormatter: (_, row) => {
                    const {
                        businessTime,
                    } = row;
                    return formatDate(businessTime, 'YYYY-MM-DD');
                },
            },
            {
                label: '体检状态',
                key: 'status',
                style: {
                    textAlign: 'center',
                    width: '80px',
                    maxWidth: '80px',
                },
                // eslint-disable-next-line no-unused-vars
                customRender(h, row) {
                    const {
                        status,
                    } = row;
                    const statusStr = OrderStatusLabelEnum[status];
                    const color = OrderStatusColorEnum[status];

                    return (
                        <abc-table-cell style={{
                            color,
                        }}>
                            {statusStr}
                        </abc-table-cell>
                    );
                },
            },
            {
                label: '订单金额',
                key: 'totalFee',
                colType: 'money',
                style: {
                    textAlign: 'right',
                    width: '90px',
                    maxWidth: '90px',
                },
            },
            {
                label: '销售人',
                key: 'sellerName',
                style: {
                    textAlign: 'left',
                    width: '72px',
                    maxWidth: '72px',
                },
                dataFormatter: (_, row) => {
                    const {
                        salesEmployee,
                    } = row;
                    return salesEmployee && salesEmployee.name || '';
                },
            },
            {
                label: '下单时间',
                key: 'orderCreated',
                style: {
                    textAlign: 'left',
                    width: '160px',
                    maxWidth: '160px',
                },
                dataFormatter: (_, row) => {
                    const {
                        orderCreated,
                    } = row;
                    return formatDate(orderCreated, 'YYYY-MM-DD HH:mm:ss');
                },
            },
        ],
    },
);

export const SelectPeTableConfig = Object.freeze(
    {
        hasInnerBorder: false,
        list: [
            // 单选框
            {
                label: '',
                key: 'id',
                style: {
                    width: '40px',
                    maxWidth: '40px',
                },

                // eslint-disable-next-line no-unused-vars
                customRender(h, row) {
                    return (
                        <abc-table-cell>
                            <abc-radio label={ row.id }>
                                &zwnj;
                            </abc-radio>
                        </abc-table-cell>
                    );
                },
            },
            {
                label: '客户',
                key: 'displayPatientStr',
                style: {
                    textAlign: 'left',
                    width: '180px',
                    maxWidth: '180px',
                },
                // eslint-disable-next-line no-unused-vars
                customRender(h, row) {
                    const {
                        patient,
                    } = row;
                    const {
                        name,
                        sex,
                        age,
                    } = patient || {};
                    return (
                        <abc-table-cell class="custom-table-td-cell">
                            <span class="ellipsis" title={name}
                                style="min-width: 58px;max-width: 64px">{name}</span>
                            <span>{sex}</span>
                            <span>{formatAge(age)}</span>

                            {
                                row.sourceType === PESourceType.ONLINE_THIRD_WE_APP ? (
                                    <abc-tag-v2
                                        variant="outline"
                                        shape="square"
                                        size="tiny"
                                        theme="primary"
                                        style={{ marginLeft: '6px' }}
                                    >
                                        线上
                                    </abc-tag-v2>
                                ) : null
                            }
                        </abc-table-cell>
                    );
                },
            },
            {
                label: '体检单号',
                'pinned': 'left',
                key: 'no',
                style: {
                    textAlign: 'left',
                    width: '130px',
                    maxWidth: '130px',
                },
            },
            {
                label: '预约体检日期',
                key: 'businessTime',
                style: {
                    textAlign: 'left',
                    width: '100px',
                    maxWidth: '100px',
                },
                dataFormatter: (_, row) => {
                    const {
                        businessTime,
                    } = row;
                    return formatDate(businessTime, 'YYYY-MM-DD');
                },
            },
            {
                label: '体检套餐',
                key: 'name',
                style: {
                    textAlign: 'left',
                    flex: 1,
                    minWidth: '160px',
                },
            },
            {
                label: '体检状态',
                key: 'status',
                style: {
                    textAlign: 'center',
                    width: '80px',
                    maxWidth: '80px',
                },
                // eslint-disable-next-line no-unused-vars
                customRender(h, row) {
                    const {
                        status,
                    } = row;
                    const statusStr = OrderStatusLabelEnum[status];
                    const color = OrderStatusColorEnum[status];

                    return (
                        <abc-table-cell style={{
                            color,
                        }}>
                            {statusStr}
                        </abc-table-cell>
                    );
                },
            },
        ],
    },
);
