<template>
    <abc-dialog
        v-model="showDialog"
        append-to-body
        content-styles="padding: 24px; width: 624px;"
        title="预诊"
    >
        <abc-form>
            <component
                :is="switchComponent"
                v-model="cloneMedicalRecord"
                fixed
                :disabled="disabled"
                :patient="patient"
                :switch-setting="switchSetting"
                disabled-sync-tooth-btn
                :post-data="postData"
            >
            </component>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button style="margin-left: auto;" :loading="btnLoading" @click="confirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="showDialog = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import Clone from 'utils/clone';
    import { mapGetters } from 'vuex';
    import { getMRComponentByType } from '@/views-hospital/outpatient/utils/index.js';
    import { getRegistrationMRStruct } from '@/views/outpatient/common/medical-record/utils.js';
    export default {
        props: {
            value: Bo<PERSON>an,
            disabled: Boolean,
            medicalRecord: {
                type: Object,
                default() {
                    return {};
                },
            },

            patient: {
                type: Object,
                default: () => ({}),
            },
            changeMedicalRecord: Function,
            postData: {
                type: Object,
                default: () => ({}),
            },
        },

        data() {
            return {
                mobileCache: '',
                members: [],
                cloneMedicalRecord: Clone(this.medicalRecord),
                btnLoading: false,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewComponents',
                'viewDistributeConfig',
            ]),
            switchComponent() {
                return getMRComponentByType(this.medicalRecord.type);
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            switchSetting() {
                return getRegistrationMRStruct(this.medicalRecord.type);
            },
        },

        created() {
            this.$abcEventBus.$on('GENERAL_INSPECTION', (payload) => {
                if (this.disabled) return;

                const { str } = payload;
                console.log('AbcGeneralInspectionService: GENERAL_INSPECTION', str);
                this.cloneMedicalRecord.physicalExamination = (this.cloneMedicalRecord.physicalExamination || '') + str;
            }, this);
        },

        methods: {
            /**
             * @desc 确认预诊信息
             * <AUTHOR>
             * @date 2019-10-16 17:50:19
             */
            async confirm() {
                // 当前可以编辑，才触发change事件
                if (!this.disabled) {
                    if (this.changeMedicalRecord) {
                        try {
                            this.btnLoading = true;
                            await this.changeMedicalRecord(this.cloneMedicalRecord);
                        } catch (e) {
                            this.btnLoading = false;
                            console.log('changeMedicalRecord error: ', e);
                        }
                    } else {
                        this.$emit('change', this.cloneMedicalRecord);
                    }
                }
                this.showDialog = false;
            },
        },
    };
</script>
