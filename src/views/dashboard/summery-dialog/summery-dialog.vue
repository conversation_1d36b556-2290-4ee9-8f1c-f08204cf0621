<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        size="hugely"
        responsive
        title="今日看板"
        custom-class="summery-dialog-wrapper"
        :auto-focus="false"
    >
        <div class="center">
            <div class="user-info">
                {{ userInfo?.name }}
            </div>
            <div v-if="showDateStr" class="today">
                {{ fetchParams.beginDate }}
                <template v-if="fetchParams.beginDate !== fetchParams.endDate">
                    ~ {{ fetchParams.endDate }}
                </template>
            </div>
        </div>
        <div class="summery-handler">
            <div>
                <div class="operate-bar">
                    <abc-date-picker-bar
                        v-model="currentDateLabel"
                        :options="datePickerBarOptions"
                        value-format="YYYY-MM-DD"
                        :picker-options="pickerOptions"
                        @change="changeDate"
                    >
                    </abc-date-picker-bar>
                </div>
            </div>
        </div>
        <!-- 医生视图 -->
        <div v-if="type === 'outpatient'" v-abc-loading="loading" class="outpatient-summery">
            <abc-flex style="width: 100%;" :gap="12" justify="space-between">
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C1"
                    :title="executeInfo.outpatientCount"
                    content="门诊人次"
                    icon="s-stethoscope-fill"
                >
                </abc-statistic>
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C2"
                    :title="executeInfo && executeInfo.retailPersonTime || 0"
                    content="零售人次"
                    icon="s-shopping-fill"
                >
                </abc-statistic>
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C3"
                    :title="executeInfo && executeInfo.prescriptionCount || 0"
                    content="处方量"
                    icon="s-order-1-fill"
                >
                </abc-statistic>
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C4"
                    :title="executeInfo.firstVisitCount"
                    content="初诊人次"
                    icon="s-user-small"
                >
                </abc-statistic>
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C5"
                    :title="executeInfo.reVisitCount"
                    content="复诊人次"
                    icon="s-group-fill"
                >
                </abc-statistic>
                <abc-statistic
                    style="flex: 1;"
                    variant="colorful"
                    theme="C6"
                    :title="(executeInfo && executeInfo.chargeAmount || 0) | formatMoney"
                    :content="revenueText"
                    icon="s-bill-fill"
                >
                </abc-statistic>
                <abc-statistic
                    v-if="doctorExecuteFeeDashboard"
                    style="flex: 1;"
                    variant="colorful"
                    theme="C7"
                    :title="(executeInfo && executeInfo.executionAmount || 0) | formatMoney"
                    content="执行金额"
                    icon="s-currency-fill"
                >
                </abc-statistic>
            </abc-flex>

            <!-- 医生有查看门诊收入权限，显示挂号费、诊疗费 -->
            <div v-if="doctorRegistrationFeeInDashboard" class="registration-fee-wrapper">
                <div class="title">
                    {{ $t('registrationFeeName') }}用
                </div>

                <abc-table
                    :render-config="registrationTableConfig"
                    :data-list="registrationInfo"
                    custom-class="registration-fee-table"
                    empty-content="暂无数据"
                >
                    <template v-for="config in registrationTableConfig.list" :slot="config['key']" slot-scope="{ trData }">
                        <abc-table-cell :key="config.key">
                            <abc-text v-if="config.key === 'header'" theme="gray" size="normal">
                                {{ trData[config['key']] }}
                            </abc-text>
                            <span v-else :class="config['class']">
                                {{ (config['formatter'] && config['formatter'](trData)) || trData[config['key']] }}
                            </span>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </div>

            <div v-if="doctorOutpatientFeeInDashboard" class="fee-distribution-wrapper">
                <div class="title">
                    诊疗费用
                </div>

                <abc-table-fixed2
                    class="outpatient-table"
                    :header="tableHeader"
                    :data="tableData"
                    :empty-opt="{ label: '暂无数据' }"
                >
                </abc-table-fixed2>
            </div>
        </div>
        <!-- 收费员视图 -->
        <template v-if="type === 'charge'">
            <div v-abc-loading="loading" class="charge-summery">
                <div v-if="totalData && totalData.length" class="summer-table-wrapper" :style="chargerTableStlyes">
                    <div class="summery-table">
                        <div class="table-title">
                            <div class="employee-name">
                                收费员
                            </div>
                            <div class="total-amount">
                                收费
                            </div>
                            <div class="outpatient-amount">
                                退费
                            </div>
                            <div class="recharge-amount">
                                还款
                            </div>
                            <div class="charge-amount">
                                欠费
                            </div>
                            <div class="recharge-amount">
                                退欠费
                            </div>
                        </div>
                        <ul class="table-body">
                            <li
                                v-for="(item, index) in totalData"
                                :key="item.employeeId"
                                class="table-tr"
                                :class="{
                                    'selected-tr': currentIndex === index,
                                    'bold-tr': item.employeeId === '99999999999999999999999999999999',
                                }"
                                @click="clickEvent(item, index)"
                            >
                                <div class="employee-name">
                                    {{ item.employeeName }}
                                </div>
                                <div class="total-amount">
                                    {{ formatMoney(item.payedAmount) }}
                                </div>
                                <div class="outpatient-amount">
                                    {{ formatMoney(item.refundAmount) }}
                                </div>
                                <div class="recharge-amount">
                                    {{ formatMoney(item.repayAmount) }}
                                </div>
                                <div class="charge-amount">
                                    {{ formatMoney(item.owePayedAmount) }}
                                </div>
                                <div class="recharge-amount">
                                    {{ formatMoney(item.oweRefundAmount) }}
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="current-name">
                    {{ currentName }}
                </div>
                <div class="pie-chart-wrapper">
                    <div v-if="isFeeTypeEmpty" class="pie-chart-empty">
                        <abc-content-empty></abc-content-empty>
                    </div>
                    <abc-pie-chart
                        v-else
                        id="pieChartFeeType"
                        v-abc-loading="loading"
                        :data="feeTypeDistributionData.data"
                        class-name="pie-chart-style"
                        :legend="feeTypeDistributionData.legend"
                        :center="['80', '96']"
                        :radius="['38', '70']"
                        height="180px"
                        width="530px"
                    >
                    </abc-pie-chart>

                    <div class="cutline"></div>
                    <div v-if="isPayModeEmpty" class="pie-chart-empty">
                        <abc-content-empty></abc-content-empty>
                    </div>
                    <abc-pie-chart
                        v-else
                        id="pieChartPayMode"
                        v-abc-loading="loading"
                        :data="payModeDistributionData.data"
                        class-name="pie-chart-style"
                        :legend="payModeDistributionData.legend"
                        :center="['90', '96']"
                        :radius="['38', '70']"
                        height="180px"
                        width="530px"
                    >
                    </abc-pie-chart>
                </div>
            </div>
        </template>
    </abc-dialog>
</template>

<script>
    import {
        mapGetters, mapActions,
    } from 'vuex';
    import { formatMoney } from 'utils/index';
    import ModulePermission from 'views/permission/module-permission';

    import DashboardAPI from 'api/dashboard';
    import { toMoney } from '@/filters';
    import FeeTypeDistributionModel from 'views/statistics/operation/overview/adapter/fee-type-distribution';
    import PayModeDistributionModel from 'views/statistics/operation/overview/adapter/pay-mode-distribution';
    import { AbcDatePickerBar } from '@abc/ui-pc';
    const {
        DatePickerBarOptions, LABEL_DATE_PICKER,
    } = AbcDatePickerBar;
    import {
        prevDate, formatDate,
    } from '@abc/utils-date';

    export default {
        name: 'SummeryDialog',
        mixins: [ModulePermission],
        props: {
            value: Boolean,
            type: String,
        },
        data() {
            return {
                currentDateLabel: DatePickerBarOptions.DAY.label,
                datePickerBarOptions: [
                    {
                        label: DatePickerBarOptions.DAY.label,
                        name: DatePickerBarOptions.DAY.name,
                        getValue() {
                            return [new Date(), new Date()];
                        },
                    },
                    {
                        label: DatePickerBarOptions.YESTERDAY.label,
                        name: DatePickerBarOptions.YESTERDAY.name,
                        getValue() {
                            return [
                                prevDate(new Date()),
                                prevDate(new Date()),
                            ];
                        },
                    },
                    DatePickerBarOptions.WEEK,
                    DatePickerBarOptions.MONTH,
                ],
                pickerStartDate: '',
                pickerOptions: {
                    onPick: ({ minDate }) => {
                        if (minDate) {
                            this.pickerStartDate = minDate.getTime();
                        }
                    },
                    disabledDate: (date) => {
                        const day365 = 365 * 24 * 3600 * 1000;
                        if (this.pickerStartDate !== '') {
                            let maxTime = this.pickerStartDate + day365;
                            const minTime = this.pickerStartDate - day365;
                            if (maxTime > new Date()) {
                                maxTime = new Date();
                            }
                            return date.getTime() > maxTime ||
                                date.getTime() < minTime ||
                                date.getTime() > Date.now();
                        }
                        return date.getTime() > Date.now();
                    },
                },

                fetchParams: {
                    beginDate: '',
                    endDate: '',
                    employeeId: '',
                },
                // 收入/支付构成
                feeTypeDistributionData: FeeTypeDistributionModel.buildEmptyData(14, 210, 14),
                payModeDistributionData: PayModeDistributionModel.buildEmptyData(14, 210, 14),
                loading: true,
                totalData: null,
                currentName: '',
                currentIndex: '',
                executeInfo: {
                    executionAmount: 0,//执行金额
                    chargeAmount: 0,//开单金额
                    prescriptionCount: 0,//处方数
                    outpatientCount: 0,
                    chargeCount: 0,
                    totalFee: 0,
                    firstVisitCount: 0,
                    reVisitCount: 0,
                    retailPersonTime: 0,
                },

                registrationInfo: [
                    {
                        type: 'number',
                        header: '号源数量',
                        total: 0,
                        treated: 0,
                        treating: 0,
                        returned: 0,
                    },
                    {
                        type: 'money',
                        header: '实收金额',
                        total: 0.0,
                        treated: 0.0,
                        treating: 0.0,
                        returned: 0.0,
                    },
                ],

                tableData: [],
                tableHeader: [],
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'currentClinic',
                'clinicConfig',
                'chargerPermissionInDashboard',
                'doctorOutpatientFeeInDashboard',
                'doctorRegistrationFeeInDashboard',
                'doctorExecuteFeeDashboard',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            revenueText() {
                if (this.doctorOutpatientFeeInDashboard && this.doctorRegistrationFeeInDashboard) {
                    return '开单金额';
                } if (this.doctorRegistrationFeeInDashboard) {
                    return '挂号金额';
                } if (this.doctorOutpatientFeeInDashboard) {
                    return '诊疗金额';
                }
                return '';
            },

            showDateStr() {
                return this.currentDateLabel !== LABEL_DATE_PICKER;
            },
            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },
            dateTime() {
                const time = new Date();
                const date = formatDate(time, 'YYYY-MM-DD');
                return date;
            },


            chargerTableStlyes() {
                if (this.chargerPermissionInDashboard === 1) {
                    return 'height: 80px;';
                }
                return '';
            },

            registrationTableConfig() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'header',
                            label: '',
                            style: {
                                flex: 1,
                                width: '80px',
                                textAlign: 'center',
                            },
                            class: ['header'],
                        },
                        {
                            key: 'total',
                            label: '总挂号',
                            style: {
                                flex: 1,
                                width: '80px',
                                textAlign: 'right',
                            },

                            formatter(item) {
                                if (item.type === 'money') {
                                    return toMoney(item.total);
                                }
                                return item.totalCount;
                            },
                        },
                        {
                            key: 'treated',
                            label: '已诊号',
                            style: {
                                flex: 1,
                                width: '80px',
                                textAlign: 'right',
                            },
                            formatter(item) {
                                if (item.type === 'money') {
                                    return toMoney(item.treated);
                                }
                                return item.treated;
                            },
                        },
                        {
                            key: 'treating',
                            label: '未诊号',
                            style: {
                                flex: 1,
                                width: '80px',
                                textAlign: 'right',
                            },
                            formatter(item) {
                                if (item.type === 'money') {
                                    return toMoney(item.treating);
                                }
                                return item.treating;
                            },
                        },
                        {
                            key: 'returned',
                            label: '退号',
                            style: {
                                flex: 1,
                                width: '80px',
                                textAlign: 'right',
                            },
                            formatter(item) {
                                if (item.type === 'money') {
                                    return toMoney(item.returned);
                                }
                                return item.returned;
                            },
                        },
                    ],
                };
            },

            isPayModeEmpty() {
                return !this.payModeDistributionData.data || this.payModeDistributionData.data.length === 0;
            },

            isFeeTypeEmpty() {
                return !this.feeTypeDistributionData.data || this.feeTypeDistributionData.data.length === 0;
            },
        },
        async created() {
            if (this.chargerPermissionInDashboard === 1) {
                this.fetchParams.employeeId = this.userInfo.id;
            }
            await this.fetchDataPermission();
            this.changeDate([formatDate(new Date()), formatDate(new Date())]);
        },
        methods: {
            ...mapActions(['fetchDataPermission']),
            formatMoney,
            changeDate(picker) {
                if (picker && picker.length === 2) {
                    this.fetchParams.beginDate = picker[0];
                    this.fetchParams.endDate = picker[1];
                } else {
                    this.fetchParams.beginDate = formatDate(new Date());
                    this.fetchParams.endDate = formatDate(new Date());
                    this.currentDateLabel = DatePickerBarOptions.DAY.label;
                }
                this.getExecuteAmountData();
                this.fetchData();
            },

            async fetchData() {
                if (this.type === 'outpatient') {
                    await this.fetchEmployeeData();
                } else {
                    await this.fetchIncomeData();
                }
            },

            async fetchIncomeData() {
                this.loading = true;
                const { data } = await DashboardAPI.fetchEmployeeIncomeDistribution(this.fetchParams);
                this.totalData = data;
                this.currentIndex = 0;
                if (data[0]) {
                    this.initFeeTypeData(data[0]);
                    this.initPayModeData(data[0]);
                    this.currentName = data[0].employeeName;
                }
                this.loading = false;
            },

            async getExecuteAmountData() {
                try {
                    const {
                        beginDate, endDate,
                    } = this.fetchParams;
                    let scope = '';
                    if (this.doctorOutpatientFeeInDashboard && this.doctorRegistrationFeeInDashboard) {
                        scope = '';
                    } else if (this.doctorRegistrationFeeInDashboard) {
                        scope = 1;
                    } else if (this.doctorOutpatientFeeInDashboard) {
                        scope = 2;
                    } else if (!this.doctorOutpatientFeeInDashboard && !this.doctorRegistrationFeeInDashboard) {
                        scope = 3;
                    }

                    const res = await DashboardAPI.fetchWorkbenchExecuteAmount({
                        beginDate,
                        endDate,
                        employeeId: this.userInfo.id,
                        clinicId: this.clinicId,
                        scope,
                    });

                    this.executeInfo = res?.data;

                } catch (e) {
                    console.log(e);
                }

            },
            /**
             * @desc 初始化费用类型分布
             * <AUTHOR>
             * @date 2019/08/23 13:17:36
             * @params
             * @return
             */
            initFeeTypeData(data) {
                if (!data || !data.feeTypes) {
                    this.feeTypeDistributionData = FeeTypeDistributionModel.buildEmptyData(14, 210, 14);
                    return;
                }
                const feeTypeDistributionModel = new FeeTypeDistributionModel(data.feeTypes);
                this.feeTypeDistributionData = feeTypeDistributionModel.data;
            },

            /**
             * @desc 初始化支付方式分布
             * <AUTHOR>
             * @date 2019/08/23 13:17:36
             * @params
             * @return
             */
            initPayModeData(data) {
                if (!data || !data.payModes) {
                    this.payModeDistributionData = PayModeDistributionModel.buildEmptyData(14, 210, 14);
                    return;
                }
                const payModeDistributionModel = new PayModeDistributionModel(data.payModes);
                this.payModeDistributionData = payModeDistributionModel.data;
            },
            clickEvent(item, index) {
                this.loading = true;
                this.currentIndex = index;
                this.initFeeTypeData(this.totalData[index]);
                this.initPayModeData(this.totalData[index]);
                this.currentName = item.employeeName;
                this.loading = false;
            },

            async fetchEmployeeData () {
                this.loading = true;
                const {
                    beginDate, endDate,
                } = this.fetchParams;
                let promiseDistribution = Promise.resolve();

                if (this.doctorOutpatientFeeInDashboard) {
                    promiseDistribution = DashboardAPI.fetchDoctorIncomeDistribution({
                        beginDate,
                        endDate,
                        employeeId: this.userInfo.id,
                    });
                }

                let promiseRegistration = Promise.resolve();
                if (this.doctorRegistrationFeeInDashboard) {
                    promiseRegistration = DashboardAPI.fetchDoctorRegistration({
                        beginDate,
                        endDate,
                        employeeId: this.userInfo.id,
                    });
                }

                try {
                    const [
                        { data: distributionData = null } = {},
                        { data: registrationData = null } = {},
                    ] = await Promise.all(
                        [ promiseDistribution, promiseRegistration].map((promise) => {
                            return promise.catch((err) => {
                                return err;
                            });
                        }),
                    );
                    if (beginDate !== this.fetchParams.beginDate || endDate !== this.fetchParams.endDate) {
                        return;
                    }


                    if (this.doctorOutpatientFeeInDashboard && distributionData) {
                        this.tableData = distributionData.data || [];
                        this.tableHeader = distributionData.header || [];
                    }

                    // 处理挂号数据
                    if (this.doctorRegistrationFeeInDashboard && registrationData) {
                        const countObj = {
                            header: '号源数量',
                            type: 'number',
                        };
                        const feeObj = {
                            header: '实收金额',
                            type: 'money',
                        };
                        Object.keys(registrationData).forEach((key) => {
                            let index = key.indexOf('Count');
                            if (index > -1) {
                                countObj[key.substring(0, index)] = registrationData[key];
                            } else {
                                index = key.indexOf('Fee');
                                if (index > -1) {
                                    feeObj[key.substring(0, index)] = registrationData[key];
                                }
                            }
                        });
                        console.log(countObj, feeObj);
                        this.registrationInfo = [countObj, feeObj];
                    }
                } catch (err) {
                    console.error('拉取员工工作汇总失败：fetchEmployeeData', err);
                }

                this.loading = false;
            },
        },
    };
</script>

<style lang="scss">
    @import '~styles/theme.scss';
    @import 'summery-dialog';
</style>
