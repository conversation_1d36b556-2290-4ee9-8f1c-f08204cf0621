<template>
    <abc-card
        v-if="dashboardTopBanner"
        class="operation-message-container"
        padding-size="none"
        style="margin-top: 0;"
        @click.native="handleMessageClick"
    >
        <abc-image :src="dashboardTopBanner.banner">
        </abc-image>

        <dialog-aggregated-payment v-if="manager.visible"></dialog-aggregated-payment>
    </abc-card>
</template>

<script>
    import AbcCmsManagerService from '@/service/cms/cms-manager-service';
    import {
        CMS_CUSTOM_BUSINESS_ACTION, CMS_CUSTOM_BUSINESS_CONSTANT,
    } from '@/service/cms/constant';
    import OneCodePayOperationModal from 'views/settings/aggregate-payment/components/one-code-pay-operation';
    import { navigateToBannerUrl } from '@/core/navigate-helper';
    import { mapGetters } from 'vuex';
    import { parseAbcProtocolUrl } from '@/service/cms/custom-business-utils';
    import { BusinessKeyMap } from '@/assets/configure/buried-point';
    import AggregatedPaymentDialogManager
        from 'views/settings/aggregate-payment/components/dialog-aggregated-payment/manager';
    import DialogAggregatedPayment
        from 'views/settings/aggregate-payment/components/dialog-aggregated-payment/index.vue';

    export default {
        name: 'OperationMessage',
        components: { DialogAggregatedPayment },
        props: {
            dashboardTopBanner: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                messageList: [],
                pageNum: 1,
                pageSize: 5,
                total: 0,
                manager: AggregatedPaymentDialogManager.getInstance(),
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'userInfo', 'isChainAdmin']),
        },
        mounted() {
            this.cmsService = AbcCmsManagerService?.getCmsService();
        },
        beforeDestroy() {
            this._oneCodePayOperationModal = null;
            this._oneCodePayOpen = null;
        },
        methods: {
            /**
             * 获取CMS运营消息
             */

            /**
             * @desc: 检查协议是否标准 abc://open-dialog/yimafu?region=sichuan
             * @author: ff
             * @time: 2025/5/27
             */
            checkProtocol(uri) {
                return uri.startsWith('abc://');
            },

            /**
             * 标记消息为已读
             * @param {String|Number} messageId 消息ID
             */

            handleMessageClick() {
                this.cmsService.reportClickPushItem(this.dashboardTopBanner.id);

                if (this.dashboardTopBanner?.linkType === 4) {

                    // abc://open-dialog/yimafu?region=sichuan
                    const isAbcProtocol = this.checkProtocol(this.dashboardTopBanner?.desc);
                    if (isAbcProtocol) {
                        const uri = this.dashboardTopBanner?.desc;

                        const {
                            action, business,
                        } = parseAbcProtocolUrl(uri);
                        if (action === CMS_CUSTOM_BUSINESS_ACTION.OPEN_DIALOG && business === CMS_CUSTOM_BUSINESS_CONSTANT.YIMAFU) {
                            this.openOneCodePayOperation();
                        }
                    }
                } else {
                    navigateToBannerUrl(this.dashboardTopBanner.link, '_blank', this.currentClinic, this.dashboardTopBanner.linkType);
                }
            },

            openOneCodePayOperation() {
                this._oneCodePayOperationModal = new OneCodePayOperationModal({
                    onConfirm: () => {
                        this.$abcPlatform.service.track.report({
                            key: BusinessKeyMap.CLK_ONE_CODE_PAY_TOP_BANNER.key,
                            extendData: {
                                id: this.userInfo.id,
                                name: this.userInfo.name,
                                roleIds: this.userInfo.roleIds,
                                roleNames: this.userInfo.roleNames,
                                mobile: this.userInfo.mobile,
                                isAdmin: this.userInfo.isAdmin,
                            },
                        });

                        this.manager.openModal(this.isChainAdmin, true);

                    },
                }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>

<style lang="scss">
.operation-message-container {
    width: 432px;
    height: 235px;
    overflow: hidden;
    cursor: pointer;
}
</style>
