<template>
    <abc-layout
        has-sidebar
        :class="[
            'app-wrapper',
            'app-wrapper-v2',
            {
                'is-expired': showEditionNotice, 'is-hospital-dashboard ': isHospitalDashboard
            },
        ]"
        :style="containerStyle"
    >
        <abc-layout-sidebar width="auto">
            <abc-menu todo-theme="red" :use-abc-menu-common="true"></abc-menu>
        </abc-layout-sidebar>
        <abc-layout class="main-layout">
            <abc-layout-header class="main-layout-header">
                <app-header
                    :show-nav-menu="false"
                    :show-logo="false"
                    :show-service="false"
                    :is-enable-remote-code="false"
                    is-enable-upgrade-btn
                    is-enable-window-manager
                    show-organization-icon
                    is-enable-drag
                    :z-index="1702"
                ></app-header>
            </abc-layout-header>
            <abc-layout-content :class="['main-layout-content']">
                <off-line-tip></off-line-tip>
                <data-privacy-tips></data-privacy-tips>
                <edition-header
                    v-if="showEditionNotice"
                    :is-trial="isTrial"
                    :seller-info="currentSellerInfo"
                    :is-ineffective="isIneffective"
                    :effective-days="effectiveDays"
                    @close="visibleEditionHeader = false"
                ></edition-header>
                <cms-urgent-notice v-if="urgentNoticeInfo" :urgent-notice-info="urgentNoticeInfo" @close="closeUrgentNoticeInfo"></cms-urgent-notice>
                <edition-alert
                    v-if="visibleExpiredAlert"
                    :is-expired="isExpired"
                    :is-ineffective="isIneffective"
                    :is-trial="isTrial"
                    :seller-info="currentSellerInfo"
                ></edition-alert>

                <div
                    id="container-wrapper"
                    :class="['container-wrapper', {
                        'b2b-mall-pharmacy': isPharmacyMall
                    }]"
                >
                    <announcement-float-window :list="renderNotifyList"></announcement-float-window>
                    <router-view ref="container-view"></router-view>
                    <mfe-router-view></mfe-router-view>
                </div>
            </abc-layout-content>
        </abc-layout>

        <medicine-instructions
            v-if="showDialog"
            v-model="showDialog"
            :store="$store"
        ></medicine-instructions>

        <dialog-update-log
            v-if="!!fullUpdateNoticeInfo && visibleUpdateLog"
            v-model="visibleUpdateLog"
            :push="fullUpdateNoticeInfo"
        ></dialog-update-log>

        <dialog-wechat-communicate
            v-if="visibleCommunicate"
            :stick-conversation-id="stickConversationId"
        ></dialog-wechat-communicate>

        <!-- 提示更新密码弹框 -->
        <notice-update-password-dialog
            v-if="visibleUpdatePasswordDialog"
            v-model="visibleUpdatePasswordDialog"
        ></notice-update-password-dialog>

        <!-- 工作台弹窗 -->
        <cms-dashboard-dialog
            v-if="showCmcDashboardDialog"
            v-model="showCmcDashboardDialog"
            :push-info="dashboardDialog"
        ></cms-dashboard-dialog>

        <register-appointment-simple-dialog
            v-if="isShowRegisterAppointmentSimpleDialog"
            v-model="isShowRegisterAppointmentSimpleDialog"
        >
        </register-appointment-simple-dialog>

        <!-- 医疗图片查看器 -->
        <medical-imaging-viewer-dialog v-if="visibleMedicalImagingViewerDialog" v-model="visibleMedicalImagingViewerDialog"></medical-imaging-viewer-dialog>

        <!--医疗图片查看器新版-->
        <medical-imaging-viewer-v2-dialog v-if="visibleMedicalImagingViewerV2Dialog" v-model="visibleMedicalImagingViewerV2Dialog"></medical-imaging-viewer-v2-dialog>

        <product-enablement-report-dialog
            v-if="visibleProductEnablementReportDialog"
            v-model="visibleProductEnablementReportDialog"
            @handleConfirmClick="handleProductEnablementReportConfirm"
        ></product-enablement-report-dialog>
        <evaluation-dialog v-if="visibleEvaluationDialog" v-model="visibleEvaluationDialog" :push-id="currentSopConfig.id"></evaluation-dialog>
        <micro-clinic-msg-alert v-if="microClinicMsgVisible" v-model="microClinicMsgVisible" :msg-total="microClinicMsgConfig.msgTotal"></micro-clinic-msg-alert>
        <!--全局弹出层-->
        <slot name="popup-layer"></slot>
    </abc-layout>
</template>

<script>
    import AbcMenu from 'views/abc-menu.vue';
    import AppHeader from 'views/layout/app-header/app-header.vue';
    import OffLineTip from '@/views/layout/off-line-tip.vue';
    import EditionHeader from '@/views/edition/expire-alert/expire-header.vue';
    import EditionAlert from '@/views/edition/expire-alert/index.vue';
    import ModulePermission from 'src/views/permission/module-permission';
    import AnnouncementFloatWindow from 'views/layout/announcement/float-window.vue';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';

    import Socket from 'utils/socket';
    import {
        getCssStyleVal, off, on,
    } from 'utils/dom';
    import {
        mapActions, mapGetters, mapState,
    } from 'vuex';
    import UserActionDetector from 'utils/user-action-detector';
    import AbcSocket from 'views/common/single-socket.js';
    import TreatmentAutoSocketListening from 'views/treatment/treatment-auto/treatment-auto-socket-listening.js';
    import PharmacyAutoSocketListening from 'views/pharmacy/pharmacy-auto/pharmacy-auto-socket-listening.js';
    import RegistrationAutoSocketListening
        from 'src/views-dentistry/registration/registration-auto/registration-auto-socket-listening.js';
    import ShebaoOpenInvoice from 'views/cashier/invoice/shebao-write-invoice.js';
    import YibaoPrescription from 'views/layout/prescription/yibao-prescription.js';
    import ShebaoOpenChargeSettlement from 'views/hospital/discharge-settlement/shebao-open-settlement';
    import ChargeAutoPlaySocketListening from 'views/cashier/auto-play-voice/charge-auto-play-socket-listening.js';
    import Storage from 'utils/localStorage-handler.js';
    import { debounce } from 'utils/lodash';
    import { CmsResourceType } from '@/service/cms/constant.js';
    import AbcCmsManagerService from '@/service/cms/cms-manager-service.js';
    import { AbcSocketMessageManager } from '@/service/message/manager.js';
    import { useMessageStore } from '@/service/message/message-store.js';
    import InspectReportSyncMixin from 'src/views/common/inspect-report-sync-mixin.js';
    import { medicalImagingViewerDialogStore } from '@/medical-imaging-viewer/store/medical-imaging-viewer-dialog';
    import {
        medicalImagingViewerDialogStore as medicalImagingViewerV2DialogStore,
    } from '@/medical-imaging-viewer-v2/store/medical-imaging-viewer-dialog';
    import { MessageIdEnum } from '@/service/message/constant.js';
    import { AnnouncementDialog } from 'views/layout/announcement/notice-dialog/index.js';
    import {
        productEnablementReportDialogService,
        productEnablementReportDialogStore,
    } from 'views/layout/product-enablement-report-dialog/product-enablement-report-dialog';
    import {
        AppEvents, AppTabId, NavigateHelper,
    } from '@/core/index.js';
    import {
        sendBindClinicEmployee, sendLayoutReady,
    } from '@/core/electron-tab-helper.js';
    import LayoutManager from 'views/layout/abc-container/layout-manager.js';
    import { ROLE_DOCTOR_ID } from 'utils/constants';
    import * as repository from 'MfFeEngine/repository';
    import { TodoService } from '@/service/todo';
    import Clone from 'utils/clone';
    import LocalStore from 'utils/localStorage-handler';
    import Store from 'utils/localStorage-handler';
    import MicroClinicOpenMsgDialog from '@/views/edition/micro-clinic-open-msg-dialog';
    import trackLogger, { TRACK_EVENTS } from 'utils/track-logger';
    import { useMessageConfig } from 'views/settings/message-notification-item/use-message-config';
    import { computed } from 'vue';
    import useDictionary from '@/hooks/business/use-dictionary';
    import GoodsAPIV3 from '@/api/goods/index-v3';
    import DialogNotice from 'views/layout/dialog-notice';
    import AbcLocalSearchTroubleshootDialog from 'views/inventory/components/local-search-troubleshoot-dialog';
    import {
        getTargetHost, isDev, isTest,
    } from '@/assets/configure/build-env.js';
    import { AutoWriteInvoiceStorageKey } from 'views/settings/charge-setting/invoice-setting/constant';
    import AutoWriteMedicalElectronicInvoiceService
        from 'views/cashier/invoice/write-invoice-core-v2/auto-write-invoice/auto-write-medical-electronic-invoice-service';
    import { checkUpgradeGuideOnce } from 'views/layout/upgrade-guide-dialog/upgrade-guide-helper';
    import { MFE_ROUTER_NAME } from 'abc-micro-frontend';
    import { checkDoubleNetworkTipsOnce } from 'views/layout/double-network-tips-dialog/double-network-tips';
    import useIsOpenSocialChainAdmin from 'views/inventory/hooks/useIsOpenSocialChainAdmin';
    import {
        getMfeBasePathKey, navigate2ModuleKey,
    } from 'utils/provide-keys';
    import InPatientPharmacyAutoSocketListening
        from '@/views-hospital/pharmacy/utils/audio-auto-play/pharmacy-auto-socket-listening';
    import { navigateToInvoiceConfig } from '@/core/navigate-helper';

    const MedicineInstructions = () => import('@/views/layout/medicine-instructions/medicine-instructions');
    const DialogWechatCommunicate = () => import('views/crm/common/package-communicate/dialog-wechat-communicate.vue');
    const DialogUpdateLog = () => import('@/views/layout/update-log/index.vue');
    const NoticeUpdatePasswordDialog = () => import('views/layout/notice-update-password-dialog');
    const CmsUrgentNotice = () => import('@/views/layout/cms-notice/urgent-notice-header.vue');
    const ProductEnablementReportDialog = () => import('@/views/layout/product-enablement-report-dialog/index.vue');
    const EvaluationDialog = () => import('@/views/layout/product-enablement-report-dialog/evaluation-dialog.vue');
    const DataPrivacyTips = () => import('views/layout/data-privacy-tips');
    const CmsDashboardDialog = () => import('views/layout/cms-notice/cms-dashboard-dialog.vue');
    const MedicalImagingViewerDialog = () => import(/* webpackPrefetch: true */ '@/medical-imaging-viewer/medical-imaging-viewer-dialog');
    const MedicalImagingViewerV2Dialog = () => import('@/medical-imaging-viewer-v2/medical-imaging-viewer-v2-dialog');

    const RegisterAppointmentSimpleDialog = () => import('views/layout/register-appointment-simple-dialog/index');
    const MicroClinicMsgAlert = () => import('@/views/edition/micro-clinic-msg-alert/index.vue');
    import TrackService from '@/service/user-track-report';

    export default {
        name: 'LayoutV2',
        components: {
            AbcMenu,
            MedicalImagingViewerDialog,
            MedicalImagingViewerV2Dialog,
            DataPrivacyTips,
            AppHeader,
            OffLineTip,
            MedicineInstructions,
            DialogWechatCommunicate,
            DialogUpdateLog,
            EditionHeader,
            EditionAlert,
            NoticeUpdatePasswordDialog,
            CmsDashboardDialog,
            CmsUrgentNotice,
            RegisterAppointmentSimpleDialog,//预约小工具看板
            ProductEnablementReportDialog,
            EvaluationDialog,
            AnnouncementFloatWindow,
            MicroClinicMsgAlert,
        },
        mixins: [Socket, ModulePermission, InspectReportSyncMixin],
        provide() {
            // 向下提供一些全局方法
            return {
                [getMfeBasePathKey]: this.getMfeBasePath,
                [navigate2ModuleKey]: this.navigate2Module,
            };
        },
        setup() {
            const {
                messageConfig,
                smsBalanceIsEnough,
            } = useMessageConfig();
            const {
                init,
                dictionary,
            } = useDictionary({ GoodsAPIV3 });

            const { getSocialStatusInChainAdmin } = useIsOpenSocialChainAdmin();

            const microClinicMsgConfig = computed(() => {
                if (!messageConfig.value) {
                    return {
                        msgTotal: 500,
                    };
                }
                return {
                    msgTotal: messageConfig.value.smsQuotaFree,
                };
            });
            return {
                init,
                dictionary,
                microClinicMsgConfig,
                smsBalanceIsEnough,
                getSocialStatusInChainAdmin,
            };
        },

        data() {
            return {
                showDialog: false,
                visibleUpdateLog: true,
                visibleEditionHeader: true,
                pharmacySocketMessageManager: null,
                inpatientPharmacySocketMessageManager: null,
                treatmentSocketMessageManager: null,
                chargeSocketMessageManager: null,
                registrationSocketMessageManager: null,
                shebaoWriteInvoice: null,
                urgentNoticeInfo: null,
                fullUpdateNoticeInfo: null,
                dashboardDialog: null,
                showCmcDashboardDialog: false,
                isShowRegisterAppointmentSimpleDialog: false,
                messageStore: null,
                shebaoOpenSettlement: null,
                microClinicMsgVisible: false,
                allowNoticeMicroClinicMsg: false,
                allowMicroClinicCharge: false,
                microClinicOpenMsgDialogVisible: false,
                microClinicMsgRelease: null,
                microClinicMsgAuth: null,
                microClinicMsgFiling: null,
                renderNotifyList: [],
            };
        },
        computed: {
            ...mapState('crm', ['visibleCommunicate', 'stickConversationId']),
            ...mapState('globalModal', [
                'visibleNoticeUpdatePassword',
            ]),
            ...mapGetters([
                'modules',
                'clinics',
                'currentClinic',
                'userInfo',
                'isChainAdmin',
                'isSingleStore',
                'clinicConfig',
                'isElectron',
                'printBillConfig',
                'printMedicalListConfig',
                'isOpenScanQrCodeRegister',
                'isMedicalDevelopmentClicked',
                'clinicBasicConfig',
                'currentSellerInfo',
                'isEnableRegUpgrade',
                'pharmacyRuleList',
                'isTestLogin',
                'isPharmacy',
            ]),
            ...mapGetters('edition', ['isExpired','isIneffective', 'effectiveDays', 'visibleExpiredAlert', 'isTrial', 'isExpireSoon']),
            ...mapGetters('viewDistribute', ['featureTheme','featureTherapy', 'viewDistributeConfig']),
            ...mapGetters('theme', ['curUseTheme', 'curTypeId']),
            ...mapState('hospitalGlobal', ['currentWardAreaId']),
            /**
             * 子模块的 basePath，用于从主工程跳转到子模块时，作为 base 路由拼接
             * @returns {(function(): *)|string|*}
             */
            mfeBasePath() {
                const moduleRoute = this.$router.options.routes.find((route) => route.name === MFE_ROUTER_NAME);
                return moduleRoute?.meta?.mfeBasePath || '/';
            },
            // 客户开通微诊所 客户未购买短信条数 并且剩余条数刚好是200 100 50 20
            needNoticeMicroClinicMsg() {
                return this.allowNoticeMicroClinicMsg && (this.isChainAdmin || this.isSingleStore || this.isClinicAdmin) && !this.smsBalanceIsEnough && this.microClinicMsgConfig.msgTotal <= 500;
            },
            isHospitalDashboard() {
                return window.appTabId === AppTabId.HOSPITAL_DASHBOARD;
            },
            containerStyle() {
                const {
                    navMenuHeight,
                    navMenuWidth,
                    containerTop,
                    containerWidth,
                    headerContainerHeight = 0,
                    centerContainerWidth,

                    containerTopHeadHeight = 0,
                    containerTopHeadBottomHeight = 0,

                    leftContainerWidth,

                    rightContainerWidth,
                    rightContainerRight,

                    containerOffsetLeft,
                } = LayoutManager.state;

                return {
                    '--navMenuHeight': navMenuHeight ? getCssStyleVal(navMenuHeight) : '100%',
                    '--navMenuWidth': navMenuWidth ? getCssStyleVal(navMenuWidth) : '100%',
                    '--containerTop': getCssStyleVal(containerTop),
                    '--containerWidth': getCssStyleVal(containerWidth),
                    '--headerContainerHeight': getCssStyleVal(headerContainerHeight),
                    '--centerContainerWidth': getCssStyleVal(centerContainerWidth),
                    '--containerTopHeadHeight': getCssStyleVal(containerTopHeadHeight),
                    '--containerTopHeadBottomHeight': getCssStyleVal(containerTopHeadBottomHeight),
                    '--containerOffsetLeft': getCssStyleVal(containerOffsetLeft),
                    '--leftContainerWidth': getCssStyleVal(leftContainerWidth),
                    '--rightContainerWidth': getCssStyleVal(rightContainerWidth),
                    '--rightContainerRight': getCssStyleVal(rightContainerRight),
                };
            },

            // 是否显示修改密码提示框
            visibleUpdatePasswordDialog: {
                get() {
                    return this.visibleNoticeUpdatePassword;
                },

                set(val) {
                    if (val) {
                        this.$store.commit('globalModal/SHOW_NOTICE_UPDATE_PASSWORD');
                    } else {
                        Storage.setObj(
                            'update_password_noticed',
                            this.userInfo?.id,
                            1,
                        );
                        this.$store.commit('globalModal/HIDE_NOTICE_UPDATE_PASSWORD');
                    }
                },
            },

            // 是否显示精准医疗发展计划弹窗 未浏览过 & 川渝地区的诊所 & 2022.7.31之前
            visibleMedicalDevelopment() {
                const includePartCode = [
                    '510000', // 四川
                    '500000', // 重庆
                ];
                console.log(includePartCode.includes(this.clinicBasicConfig.addressProvinceId));
                return (this.isMedicalDevelopmentClicked === 0) && (
                    includePartCode.includes(this.clinicBasicConfig.addressProvinceId)
                ) && (
                    Date.now() <= new Date(2022,7,1).getTime() - 1000 // 设置为8.1是为了获取7.31 0点
                );
            },
            // 是否显示就医疗图片查看器
            visibleMedicalImagingViewerDialog() {
                return medicalImagingViewerDialogStore.visible;
            },

            visibleMedicalImagingViewerV2Dialog() {
                return medicalImagingViewerV2DialogStore.visible;
            },

            operationAnnouncement() {
                const messages = this.messageStore?.state?.messages || [];
                const msgType = [
                    MessageIdEnum.CmsSysMaintenancePreviewNotice,
                    MessageIdEnum.CmsSysMedicalInsuranceNotice,
                    MessageIdEnum.SHEBAO_DAILY_RECONCILIATION_EXCEPTION_NOTIFY,
                    MessageIdEnum.SHEBAO_SETTLEMENT_EXCEPTION_NOTIFY,
                    MessageIdEnum.TRACE_CODE_TODAY_REPORT_TIP,
                ];
                return messages.filter((item) => {
                    const date = new Date();
                    const endTime = item?.messageBody?.data?.endTime || '';
                    return msgType.includes(item?.messageBody?.data?.type) &&
                        !item?.detailReadCount &&
                        (!endTime || date.getTime() < new Date(endTime).getTime());
                });
            },
            updateNotice() {
                const messages = this.messageStore?.state?.messages || [];
                const msgType = [ MessageIdEnum.CmsSysMaintenanceNotice ];
                const msgList = messages.filter((item) => {
                    const date = new Date();
                    const endTime = item?.messageBody?.data?.endTime || '';
                    return msgType.includes(item?.messageBody?.data?.type) &&
                        !item?.detailReadCount &&
                        (!endTime || date.getTime() < new Date(endTime).getTime());
                }) || [];
                return msgList;
            },
            // 是否显示产品启用报告
            visibleProductEnablementReportDialog: {
                get() {
                    const staffSopSheet = productEnablementReportDialogService.getStaffSopSheet();
                    return !!staffSopSheet;
                },
                set() {
                    productEnablementReportDialogService.updateSopConfigStaffSopSheet(null);
                },
            },
            // 是否显示产品启用报告评论弹窗
            visibleEvaluationDialog: {
                get() {
                    const employeeEvaluationForm = productEnablementReportDialogService.getEmployeeEvaluationForm();
                    return !!employeeEvaluationForm && !this.visibleProductEnablementReportDialog;
                },
                set() {
                    productEnablementReportDialogService.updateSopConfigEmployeeEvaluationForm(null);
                },
            },
            // 客户SOP配置
            currentSopConfig() {
                return productEnablementReportDialogStore.sopConfig;
            },
            /**
             * 是否显示版本提示黄条
             * @return {*|boolean}
             */
            showEditionNotice() {
                return (this.isExpired || this.isIneffective || this.isExpireSoon) && this.visibleEditionHeader;
            },
            isDashboard() {
                return this.$route.name === '@PharmacyHome';
            },
            isPharmacyMall() {
                return this.isPharmacy && this.$route.path?.indexOf('/mall') > -1;
            },
        },

        watch: {
            isOpenScanQrCodeRegister: {
                handler() {
                    this.initFetchScanQrCodePatientList();
                },
                immediate: true,
            },
            updateNotice: {
                handler() {
                    if (this.updateNotice?.length && !this.announcementInstance) {
                        const message = this.updateNotice[0];
                        this.announcementInstance = new AnnouncementDialog({
                            message,
                            close: async () => {
                                this.socketMsgManager?.readOneNotice(message.id);
                                this.announcementInstance = null;
                                const checkResult = await this.$store.dispatch('checkFrontEndVersion');
                                // 版本如果有更新，刷新页面
                                if (!checkResult) {
                                    location.reload();
                                }
                            },
                        }).generateDialog({ parent: this });
                    }
                },
                deep: true,
            },
            needNoticeMicroClinicMsg: {
                handler(value) {
                    if (value) {
                        this.microClinicMsgVisible = true;
                    }
                },
                immediate: true,
            },
            operationAnnouncement: {
                async handler(val) {
                    this.renderNotifyList = [];
                    for (const item of val) {
                        if (item.messageBody?.data?.type === MessageIdEnum.SHEBAO_DAILY_RECONCILIATION_EXCEPTION_NOTIFY) {
                            const extendData = item.messageBody?.data?.extendData || {};
                            const {
                                beginDate, endDate,
                            } = extendData;
                            const checkResponse = await this.$abcSocialSecurity.checkIsShowAutoCheckingNotice({
                                beginDate,
                                endDate,
                            });
                            if (checkResponse?.status) {
                                this.renderNotifyList.push(item);
                            }
                        } else {
                            this.renderNotifyList.push(item);
                        }
                    }
                },
                deep: true,
            },
        },

        created() {
            trackLogger.record(TRACK_EVENTS.LAYOUT_CREATE_START);
            this.init();

            if (!this.currentClinic) {
                NavigateHelper.navigateToEntry();
                return false;
            }

            LayoutManager.init({
                isFullScreen: this.viewDistributeConfig.isFullScreen,
                isFixedLeftNav: this.viewDistributeConfig.AppHeader.isFixedLeftNav,
                navMenuToWideThreshold: 1440,
            });

            // 试用获取销售信息
            this.isTrial && this.$store.dispatch('getClinicEmployeeSeller');
            this.cmsService = AbcCmsManagerService.getCmsService();
            this.cmsService.initPushList(this.updateCmcPushInfo);
            this.socketMsgManager = AbcSocketMessageManager.getInstance();
            this.socketMsgManager.start();

            if (!this.modules) {
                this.$store.dispatch('fetchModules');
            }
            this.$store.dispatch('refreshFullEmployees');
            this.$store.dispatch('virtualPharmacy/initVirtualPharmacyConfig');
            this.$store.dispatch('fetchPrintAllConfigIfNeed'); //打印配置

            this.$store.dispatch('initChargeConfig');
            this.$store.dispatch('initGoodsConfig');
            this.$store.dispatch('initTraceCodeConfig'),
            this.$store.dispatch('weClinic/fetchOpeningInfo').then(() => {
                this.$store.dispatch('weShop/fetchWeShopAuditStatus'); // 微商城审核状态
            });
            this.$store.dispatch('weClinic/fetchWeappStatus');
            this.$store.dispatch('fetchCallConfig'); // 叫号设置
            this.$store.dispatch('fetchPurchaseTodoCount'); // 采购待办项提醒
            // this.$store.dispatch('fetchMallCount');//商城待付款数量
            this.$store.dispatch('initOutpatientRegistrationsConfig'); // 门诊预约设置
            if (this.hasPharmacyModule) {
                this.$store.dispatch('getDispensingSummaryByDateRange');
            }

            this.$store.dispatch('treatment/fetchTreatmentSystemUnitIfNeed');
            // 因为微信预约弹窗需要用到所以需要在layout中拉取数据

            if (this.featureTherapy) {
                this.isEnableRegUpgrade ? this.$store.dispatch('initNewTherapyRegistrationsConfig') : this.$store.dispatch('initTherapyReservationConfig');
            }

            // 是否是口腔店店开启的语音提示
            if (this.viewDistributeConfig.Registration.audioOpen) {
                this.$store.dispatch('fetchRegistrationVoiceSetting'); // 预约挂号语音设置
            }
            this._resizeHandler = debounce(this.calcClientWidth, 500, true);
            this.$abcEventBus.$on('open-register-appointment-simple-dialog', () => {
                this.isShowRegisterAppointmentSimpleDialog = true;
            }, this);

            this.messageStore = useMessageStore();
            window.remote?.app?.onClinicLoginSuccess?.();

            // 部分打印配置迁移
            const { clinicId } = this.currentClinic;
            const pharmacyData = Clone(LocalStore.getObj('_pharmacy_print_setting_', clinicId, true));
            const pharmacyDataOld = Clone(LocalStore.getObj('_pharmacy_setting_', clinicId, true));
            if (!pharmacyData && !!pharmacyDataOld) {
                LocalStore.setObj('_pharmacy_print_setting_', clinicId, Clone(pharmacyDataOld));
            }
            this.initClinicMrConfig();
            if (this.isChainAdmin) {
                this.getSocialStatusInChainAdmin();
            }
        },
        mounted() {
            trackLogger.record(TRACK_EVENTS.LAYOUT_MOUNTED_START);
            trackLogger.end();
            sendLayoutReady();
            let clinicName = '';
            let userName = '';
            let isDoctorRole = '';
            let { appTabName } = this.viewDistributeConfig.AppLayout;
            if (appTabName) {
                appTabName = `${appTabName}-`;
            }
            if (this.currentClinic) {
                clinicName = `-${
                    this.currentClinic.chainAdmin ?
                        '总部' :
                        this.currentClinic.clinicName
                }`;
            }
            userName = this.userInfo?.name || '';
            isDoctorRole = this.userInfo?.roleIds?.includes?.(ROLE_DOCTOR_ID);
            document.title = `${appTabName}${userName}${clinicName}`;

            sendBindClinicEmployee({
                chainId: this.currentClinic.chainId,
                clinicId: this.currentClinic.clinicId,
                employeeId: this.userInfo.id,
            });

            on(document, 'keydown', this.toggleF1);

            on(document, 'keydown', this.preventCtrlShiftJ);

            // 屏蔽F1帮助
            on(window, 'help', this.preventHelp);

            // 页面初始化拉取一下患者沟通未读消息数
            this.acFetchUnreadMessageCount();
            // 页面初始化拉取一下患者随访今日待办数
            this.acFetchRevisitRecords();

            // 30 分钟无操作自动登出
            if (this.clinicConfig.isDengbaoClinic) {
                UserActionDetector.getInstance({
                    checkInterval: 1800 * 1000,
                }).start(async () => {
                    console.info('检测到用户长时间无操作，自动登出');
                    this.logout();
                });
            }

            // 启动 todoService
            const { socket } = AbcSocket.getSocket();
            this.$app.getService(TodoService.NAME).start();

            // 药房自动打单和语音播放 socket 监听，全局监听触发药房相关设置
            if (this.hasPharmacyModule) {
                this.pharmacySocketMessageManager = new PharmacyAutoSocketListening(
                    socket,
                );
                // 住院药房领药申请、退药申请
                this.inpatientPharmacySocketMessageManager = new InPatientPharmacyAutoSocketListening(socket);

                this.pharmacySocketMessageManager.start();
                this.inpatientPharmacySocketMessageManager.start();
            }
            // 执行站自动打单 socket 监听，全局监听触发执行站相关设置
            if (this.hasNurseModule) {
                this.treatmentSocketMessageManager = new TreatmentAutoSocketListening(
                    socket,
                );
                this.treatmentSocketMessageManager.start();
            }

            // 收费处 网诊和续方的语音提示
            if (this.hasChargeModule) {
                this.chargeSocketMessageManager = new ChargeAutoPlaySocketListening(socket);
                this.chargeSocketMessageManager.start();
            }
            // 预约签到，挂号成功语音提示 (口腔医生角色全局)
            if (this.viewDistributeConfig.Registration.audioOpen && isDoctorRole) {
                this.registrationSocketMessageManager = new RegistrationAutoSocketListening(socket);
                this.registrationSocketMessageManager.start();
            }

            // 外诊处方开票
            if (this.isElectron) {
                this.shebaoWriteInvoice = ShebaoOpenInvoice.getInstance({
                    toBillPrintSetting: () => {
                        navigateToInvoiceConfig(this.currentClinic);
                    },
                });
                this.shebaoWriteInvoice.start();
                this.yibaoPrescripiton = YibaoPrescription.getInstance();
                this.yibaoPrescripiton.start();
                this.shebaoOpenSettlement = ShebaoOpenChargeSettlement.getInstance(this);
                this.shebaoOpenSettlement.start();
            }

            // goods消息
            // const { socket } = AbcSocket.getSocket();
            repository.GoodsRepositoryService.getInstance().start(socket);
            repository.ClinicConfigService.getInstance().registerConfigProvider({
                getClinicConfig: this.getClinicConfig,
            });
            // !当无权限时，需执行所有“stop”行为。
            this.$app.on(AppEvents.APP_EVENT_UN_AUTH, () => {
                const goodsRepoInstance = repository.GoodsRepositoryService.getInstance();
                if (goodsRepoInstance) {
                    goodsRepoInstance.stop();
                }
            });

            this.checkPasswordSecurity();

            on(window,'resize', this._resizeHandler);
            this.$on('hook:beforeDestroy', () => {
                off(window, 'resize', this._resizeHandler);
            });
            this.calcClientWidth();
            this.handleAutoWriteInvoice();

            // 打开本地搜索分析工具
            window.openLocalSearchTroubleshootDialog = () => {

                if (this.isTestLogin || isTest || isDev) {
                    new AbcLocalSearchTroubleshootDialog({
                        visible: true,
                    }).generateDialogAsync({
                        parent: this,
                    });
                    return;
                }

                const vm = this.$confirm({
                    title: '请输入启动密码',
                    closeAfterConfirm: false,
                    content: () => (
                        <div style="display:flex;flex-direction: column;">
                            <abc-input-password
                                value={this._password}
                                free-input
                                max-length={10}
                                onInput={(v) => {
                                    vm.setState(() => {
                                        this._password = v;
                                    });
                                }}
                            ></abc-input-password>
                        </div>),
                    onConfirm: () => {
                        if (this._password === '123123123') {
                            new AbcLocalSearchTroubleshootDialog({
                                visible: true,
                            }).generateDialogAsync({
                                parent: this,
                            });
                            vm.close();
                            this._password = '';
                        } else {
                            this.$Toast.error('密码错误，请重试');
                        }
                    },
                });
            };

            checkUpgradeGuideOnce();
            checkDoubleNetworkTipsOnce();

            const trackServiceVm = TrackService.getInstance(this.curTypeId);
            trackServiceVm.startTracking();
            this.$on('hook:beforeDestroy', () => {
                trackServiceVm.stopTracking();
            });
        },
        beforeDestroy() {
            const goodsRepoInstance = repository.GoodsRepositoryService.getInstance();
            if (goodsRepoInstance) {
                goodsRepoInstance.stop();
            }
            this.$app.getService(TodoService.NAME).stop();
            this.pharmacySocketMessageManager && this.pharmacySocketMessageManager.stop();
            this.inpatientPharmacySocketMessageManager && this.inpatientPharmacySocketMessageManager.stop();
            this.socketMsgManager?.stop();
            this.treatmentSocketMessageManager?.stop();
            this.chargeSocketMessageManager?.stop();
            this.registrationSocketMessageManager?.stop();
            this.shebaoWriteInvoice && this.shebaoWriteInvoice.stop();
            this.shebaoOpenSettlement && this.shebaoOpenSettlement.stop();
            off(document, 'keydown', this.toggleF1);
            off(document, 'keydown', this.preventCtrlShiftJ);
            off(window, 'help', this.preventHelp);
            if (this.clinicConfig.isDengbaoClinic) {
                UserActionDetector.getInstance().stop();
            }
            this._noticeDialog?.destroyDialog();
        },
        methods: {
            ...mapActions('outpatientConfig', [
                'initClinicMrConfig',
            ]),
            openWeClinicMsgDialog(status, failReason = '',type = 0) {
                this._microClinicOpenMsgDialogInstance = new MicroClinicOpenMsgDialog({
                    value: true,
                    status,
                    failReason,
                    type,
                    onClose: () => {
                        if (this._microClinicOpenMsgDialogInstance) {
                            this._microClinicOpenMsgDialogInstance.destroyDialog();
                            this._microClinicOpenMsgDialogInstance = null;
                        }
                    },
                });
                this._microClinicOpenMsgDialogInstance.generateDialog({
                    parent: this,
                });
            },
            initFetchScanQrCodePatientList() {
                // 没有开启自助登记或者是总部都不使用以下功能
                if (!this.isOpenScanQrCodeRegister || this.isChainAdmin) {
                    return false;
                }
                // 订阅挂号模块的已登记人员
                this.fetchScanQrCodePatientList();
                const { socket } = AbcSocket.getSocket();
                this._socket = socket;
                this._socket.on('registration.epidemic_registered_number', this.fetchScanQrCodePatientList);
                this.$on('hook:beforeDestroy', () => {
                    this._socket.off('registration.epidemic_registered_number', this.fetchScanQrCodePatientList);
                });
            },
            calcClientWidth() {
                this.$store.commit('SET_CLIENT_WIDTH', document.documentElement.clientWidth);
            },
            ...mapActions('crm', [
                'acFetchUnreadMessageCount',
                'acFetchRevisitRecords',
            ]),
            ...mapActions(['fetchScanQrCodePatientList']),
            toggleF1(event) {
                const KEY_F1 = 112;
                if (event.keyCode === KEY_F1) {
                    event.cancelBubble = true;
                    event.returnValue = false;
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.showDialog = true;
                    return false;
                }
            },
            preventCtrlShiftJ(event) {
                if (event.keyCode === 74 && event.ctrlKey && event.shiftKey) {
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    return false;
                }
            },
            preventHelp(event) {
                event.preventDefault();
                return false;
            },
            async logout() {
                try {
                    await this.$store.dispatch('handleEmployeeLogout');
                } catch (e) {
                    console.log('logout err', e);
                } finally {
                    NavigateHelper.navigateToLogin();
                }
            },
            /**
             * 检查密码安全性
             */
            checkPasswordSecurity() {
                // 是否已经提醒过
                const isNoticed = Storage.getObj('update_password_noticed', this.userInfo.id, true);
                // 需要修改密码并且没有提醒过，才进行提醒
                if (this.userInfo.needUpdatePassword && !isNoticed) {
                    this.visibleUpdatePasswordDialog = true;
                }
            },

            updateCmcPushInfo(cmsPushList) {
                this.urgentNoticeInfo = this.cmsService.getPushItemByResType(CmsResourceType.URGENT_NOTICE);
                if (this.urgentNoticeInfo) {
                    this.cmsService.reportReadPushItemList(this.urgentNoticeInfo.id);
                }
                // this.fullUpdateNoticeInfo = this.cmsService.getPushItemByResType(CmsResourceType.FULL_UPDATE_NOTICE);
                // if (this.fullUpdateNoticeInfo) {
                //     this.cmsService.reportReadPushItemList(this.fullUpdateNoticeInfo.id);
                // }

                this.dashboardDialog = this.cmsService.getPushItemByResType(CmsResourceType.DASHBOARD_DIALOG);
                if (this.dashboardDialog) {
                    this.showCmcDashboardDialog = true;
                    this.cmsService.reportReadPushItemList(this.dashboardDialog.id);
                }

                // 剩余短信提示 是否允许提示
                const smsItem = this.cmsService.getPushItemByResType(CmsResourceType.MESSAGE_SMS_FREE_QUOTA_NOT_ENOUGH);
                this.allowNoticeMicroClinicMsg = !!smsItem;
                if (this.allowNoticeMicroClinicMsg) {
                    this.cmsService.reportReadPushItemList(smsItem.id);
                }

                productEnablementReportDialogService.initSopConfig(cmsPushList);
                // 处理小程序发布/认证/备案流程start
                // 发布
                this.microClinicMsgRelease = this.cmsService.getPushItemByResType(CmsResourceType.MC_RELEASE);
                if (this.microClinicMsgRelease) {
                    this.openWeClinicMsgDialog(this.microClinicMsgRelease.extendData.status, this.microClinicMsgRelease.extendData.failReason, 2);
                    this.cmsService.reportReadPushItemList(this.microClinicMsgRelease.id);
                }
                // 认证
                this.microClinicMsgAuth = this.cmsService.getPushItemByResType(CmsResourceType.MC_AUTH);
                if (this.microClinicMsgAuth) {
                    this.openWeClinicMsgDialog(this.microClinicMsgAuth.extendData.status, '');
                    this.cmsService.reportReadPushItemList(this.microClinicMsgAuth.id);
                }
                // 备案
                this.microClinicMsgFiling = this.cmsService.getPushItemByResType(CmsResourceType.MC_FILING);
                if (this.microClinicMsgFiling) {
                    this.openWeClinicMsgDialog(this.microClinicMsgFiling.extendData.status, '', 1);
                    this.cmsService.reportReadPushItemList(this.microClinicMsgFiling.id);
                }
                // 处理小程序发布/认证/备案流程end


                // 追溯码未开通提示
                const traceCodeTips = this.cmsService.getPushItemByResType(CmsResourceType.TRACE_CODE_TIPS);
                if (traceCodeTips && this.$abcSocialSecurity.isOpenSocial) {
                    this._noticeDialog = new AnnouncementDialog({
                        message: {
                            messageBody: {
                                data: {
                                    type: CmsResourceType.TRACE_CODE_TIPS,
                                    title: '医保公告',
                                    content: (`
                                        <h1 class="ql-align-center">定点医药机构医保药品耗材追溯码采集通知</h1>
                                        <p style="margin-top: 24px;">根据医保相关要求，定点医药机构需采集医保药品耗材追溯码，遵循“有码则采，应采尽采，应扫尽扫”的原则。未按要求采集追溯码，可能会面临医保监管的处罚。若您所在地区已有明确采集追溯码的要求，请尽快开启。</p>
                                    `),
                                },
                            },
                        },
                        customFooter: true,
                        showCancel: !!this.hasAdminModule,
                        confirmText: this.hasAdminModule ? '开启追溯码采集' : '知道了',
                        cancelText: '暂不开启',
                        onConfirm: () => {
                            const toPath = `${this.mfeBasePath}social/inventory/inventory`;
                            this.hasAdminModule && this.navigate2Module(toPath);
                        },
                    });
                    this._noticeDialog.generateDialog({ parent: this });
                    this.cmsService.reportReadPushItemList(traceCodeTips.id);
                }

                const orderCloudAuthExpired = this.cmsService.getPushItemByResType(CmsResourceType.ORDER_CLOUD_AUTH_EXPIRED);
                if (orderCloudAuthExpired) {
                    this._noticeDialog = new DialogNotice({
                        title: orderCloudAuthExpired.title,
                        content: orderCloudAuthExpired.extendData?.desc || '',
                        onConfirm: () => {
                            this.handleAuthOrderCloud(orderCloudAuthExpired);
                        },
                    }).generateDialog();
                    this.cmsService.reportReadPushItemList(orderCloudAuthExpired.id);
                }
            },
            // 使用方法获取计算属性mfeBasePath，保证是最新的
            getMfeBasePath() {
                return this.mfeBasePath;
            },
            /**
             * 跳转到微前端子模块，需要独立控制，原因：
             * 主工程注册的路由为 {path: '/*' }，当子模块进入到子模块的内部路由时，主工程感知不到路由变化，主工程路由仍为 {path: '/*'}
             * 再次点击导航时，主工程的 vue-router 会认为路由没有发生变化，并回调 onAbort。此时子工程干不知道路由变化，主工程应调用 notifyPlatformRouterChange 告知子模块
             * 子模块收到通知后 redirect 到子模块主页
             */
            navigate2Module(toPath) {
                this.$router.push(toPath, null, () => {
                    const route = this.$router.match(toPath);
                    this.$abcPlatform.notifyPlatformRouterChange(route);
                });
            },
            async handleAuthOrderCloud(item) {
                if (!item.extendData) return;
                const {
                    ecMallId,
                } = item.extendData;
                let authWindow;

                const onMessage = (message) => {
                    if (message.origin.indexOf('global') !== -1) {
                        if (message.data === 'ec_auth_success') {
                            this.$abcEventBus.$emit('ec-auth-success');
                            this.visible = false;
                            window.removeEventListener('message', onMessage);
                            authWindow.close();
                        }
                    }
                };

                window.addEventListener('message', onMessage);
                const redirectUri = encodeURIComponent(`https://${getTargetHost()}/ec-oauth-callback`);
                const state = encodeURIComponent(`${this.currentClinic?.chain?.shortId}@${location.hostname}@${ecMallId}`);
                const thirdUrl = 'https://fuwu.pinduoduo.com/service-market/auth';
                const mmsUrl = 'https://mms.pinduoduo.com';

                const url = `${thirdUrl}?response_type=code&client_id=${item.clientId}&redirect_uri=${redirectUri}&state=${state}`;
                if (window.remote?.session) {
                    try {
                        const mmsCookies = await window.remote.session.defaultSession.cookies.get({ url: mmsUrl });
                        for (const cookie of mmsCookies) {
                            await window.remote.session.defaultSession.cookies.remove(mmsUrl, cookie.name);
                        }
                    } catch (error) {
                        console.error(error);
                    }

                    try {
                        const cookies = await window.remote.session.defaultSession.cookies.get({ url: thirdUrl });
                        for (const cookie of cookies) {
                            await window.remote.session.defaultSession.cookies.remove(thirdUrl, cookie.name);
                        }
                    } catch (error) {
                        console.error(error);
                    }
                }
                const currentWidth = window.innerWidth;
                const currentHeight = window.innerHeight;

                const newWidth = Math.round(currentWidth * 0.8);
                const newHeight = Math.round(currentHeight * 0.8);

                authWindow = window.open(url, '_blank', `width=${newWidth},height=${newHeight}`);
            },

            closeUrgentNoticeInfo() {
                this.urgentNoticeInfo = null;
                if (this.$refs['container-view']?.$children[0]?._isVue) {
                    this.$refs['container-view']?.$children[0]?.calcStylePosition();
                }
            },
            /**
             * @description: 产品启用报告确认已读
             * @date: 2022-12-28 13:54:13
             * @author: Horace
             * @return
             */
            handleProductEnablementReportConfirm() {
                this.cmsService.reportReadPushItemList(this.currentSopConfig.id);
            },

            getClinicConfig() {
                return {
                    isSingleMode: this.isSingleStore,
                    currentWardAreaId: this.currentWardAreaId,
                    pharmacyRuleList: this.pharmacyRuleList,
                    getDefaultPharmacy,
                    userId: this.userInfo?.id ?? '',
                    clinicId: this.currentClinic?.clinicId ?? '',
                    chainId: this.currentClinic?.chainId ?? '',
                };
            },
            async handleAutoWriteInvoice() {
                // 自动开票
                const autoAdviceId = Store.get(`${AutoWriteInvoiceStorageKey}_${this.currentClinic.clinicId}`, true)?.autoInvoiceDeviceId;
                if (autoAdviceId) {
                    const { socket } = AbcSocket.getSocket();
                    const { medicalElectronicAPIConfig } = await this.$store.dispatch('invoice/fetchInvoiceConfig'); // 发票配置
                    this.autoWriteInvoiceService = AutoWriteMedicalElectronicInvoiceService.getInstance(socket, this.currentClinic.clinicId, medicalElectronicAPIConfig);
                    this.autoWriteInvoiceService.start();
                }
            },
        },
    };
</script>

<style lang="scss">
    @import "src/styles/mixin.scss";

    body {
        background: linear-gradient(180deg, #3975c6 0.3%, #4872c8 87%);
    }

    .app-wrapper-v2 {
        max-width: 100%;
        padding-top: 0;
        margin: 0 auto;
        background: transparent;

        .container-wrapper {
            border-radius: var(--abc-border-radius-large);

            &::after {
                display: none;
            }
        }

        .overflow-scroll {
            overflow-y: scroll;
        }

        &.is-expired {
            padding-top: 44px;
        }

        .edition-module__expire-alert__header {
            top: 0;
        }

        .app-header {
            height: 48px;
            line-height: 48px;

            .header-wrapper {
                max-width: 100%;
                height: 48px;
                margin: 0;
                line-height: 48px;
                color: #ffffff;

                .logo-wrapper {
                    img {
                        width: 124px;
                        height: 40px;
                    }
                }

                .app-remain-drag {
                    flex: 1;
                    -webkit-app-region: drag;
                }

                .header-fixed-right {
                    max-width: unset !important;
                    padding-right: 0 !important;

                    .app-header--round-btn {
                        width: 32px;
                        min-width: 32px;
                        height: 24px;
                        margin-right: 12px;
                        border: none;

                        &:hover {
                            background: unset;
                        }

                        &:active {
                            background: unset;
                        }
                    }

                    .user-setting {
                        margin-right: 28px;
                    }

                    .user-dropdown {
                        width: 32px;

                        .avatar-img {
                            width: 26px !important;
                            height: 26px !important;
                            border-radius: var(--abc-border-radius-mini);
                        }
                    }
                }
            }

            .header__left-wrapper {
                padding-left: 0;
            }

            .app-clinic-dropdown-wrapper .app-clinic-dropdown__reference .app-notice-reference,
            .app-notice-wrapper,
            .wechat-alert,
            .theme-selector-wrapper,
            .full-screen-btn,
            .app-zoom-btn-wrapper,
            .app-window-manager {
                .iconfont {
                    font-size: 16px !important;
                    line-height: 16px !important;
                    color: rgba(255, 255, 255, 0.6);
                }
            }

            .header-button--red:hover {
                .iconfont {
                    color: $T4 !important;
                }

                background: $R6 !important;

                &:active {
                    background: #f15c51 !important;
                }
            }

            .header-button--transparent {
                color: rgba(255, 255, 255, 0.6) !important;

                svg {
                    font-size: 16px !important;
                    line-height: 16px !important;
                }

                &:hover {
                    color: rgba(255, 255, 255) !important;
                    background: rgba(255, 255, 255, 0.15) !important;

                    .iconfont {
                        color: $T4 !important;
                    }

                    &:active {
                        background: rgba(255, 255, 255, 0.25) !important;
                    }
                }
            }

            .app-notice-count {
                pointer-events: none;
            }
        }

        .b2b-mall-pharmacy {
            overflow-x: hidden;
            overflow-y: scroll;
            background: #f5f7fb;
            border-radius: var(--abc-border-radius-medium);
        }

        .main-layout {
            width: 0;

            .main-layout-header {
                padding: 0;
            }

            .main-layout-content {
                height: 0;
                padding: 0 8px 8px 0;

                &.no-layout-padding {
                    padding-right: 0;
                }

                .b2b-app-header {
                    position: sticky !important;
                    top: 0 !important;
                    z-index: 990;
                }

                .b2b-mall-content-wrapper {
                    padding-top: 0 !important;
                    margin-top: 0 !important;
                }

                #app.b2b-mall .b2b-mall-wrapper.white-background .b2b-mall-content-wrapper-top {
                    top: -74px !important;
                    margin-top: 0 !important;
                }

                .b2b-mall-wrapper.white-background .b2b-mall-content-wrapper-top {
                    top: -74px !important;
                    margin-top: 0 !important;
                }

                ::v-deep .b2b-mall-wrapper.white-background .b2b-mall-content-wrapper-top {
                    top: -74px !important;
                    margin-top: 0 !important;
                }

                .mall-bottom-choice-model {
                    right: 32px !important;
                }

                .mall-scroll-top-model {
                    right: 32px !important;
                }

                .b2b-goods-detail-wrapper {
                    margin-top: 0 !important;
                }

                .mall-home_tabs {
                    position: sticky;
                    top: 76px;
                    margin-top: -60px !important;
                }
            }
        }

        #abc-module-container #abc-container,
        .social-security-lite-module #abc-container {
            border-radius: $abcContainerBorderRadius;
        }

        #abc-container {
            max-width: 100% !important;
            background: var(--abc-color-bg-main);
            border-radius: var(--abc-border-radius-large);
            box-shadow: none;

            > .abc-card-wrapper + .abc-card-wrapper {
                margin-top: 0;
            }

            .pharmacy-content-card {
                border-radius: 0 0 var(--abc-container-border-radius) var(--abc-container-border-radius);
            }

            #abc-container-left,
            #abc-container-right {
                height: calc(100% - var(--containerTop) - var(--headerContainerHeight) - 8px);
            }
        }

        .dashboard-container {
            &#abc-container {
                height: 100%;
                padding-right: calc(var(--abc-paddingTB-xl) - 10px);
                overflow-y: scroll;

                @include scrollBar;

                #abc-container-center {
                    height: auto;
                    scrollbar-gutter: unset;
                    overflow: unset;
                }

                .dashboard-main-content {
                    overflow: unset;
                }
            }
        }
    }

    // 以下样式需要调整到组件 ========== start
    .pharmacy__app-cont-card .abc-tabs-item {
        padding: 0 20px !important;
    }

    .abc-layout-content {
        display: flex;
        flex-direction: column;
    }

    .abc-descriptions-show {
        .abc-descriptions-item__label {
            padding: 10px !important;
        }

        .abc-descriptions-item__content {
            padding: 10px !important;
        }
    }

    .abc-descriptions-edit {
        .abc-descriptions-item__label {
            padding: 10px !important;
        }

        .abc-descriptions-item__content {
            padding: 0 !important;
        }

        .show-text-box {
            display: flex;
            align-items: center;
            height: 40px;
            padding: 0 10px;
        }

        .abc-form-item {
            margin: 0 !important;

            .abc-input__inner {
                height: 40px !important;
                border: 0 !important;
                border-radius: 0 !important;
            }
        }

        .abc-form-item,
        .abc-form-item-content,
        .abc-input-wrapper,
        .abc-select-wrapper,
        .abc-date-picker,
        .abc-input__inner {
            width: 100% !important;
        }

        .abc-select-wrapper.is-hover {
            & > .abc-input__inner:not([disabled]):not(.is-disabled) {
                border: 0 !important;
            }
        }

        .abc-descriptions__view > .abc-descriptions-row:first-child {
            & > .abc-descriptions-col:last-child {
                .abc-input__inner {
                    border-top-right-radius: 4px !important;
                }

                .abc-date-time-picker {
                    .abc-date-picker {
                        .abc-input__inner {
                            border-top-right-radius: 0 !important;
                        }
                    }
                }
            }
        }

        .abc-descriptions__view > .abc-descriptions-row:last-child {
            & > .abc-descriptions-col:last-child {
                .abc-input__inner {
                    border-bottom-right-radius: 4px !important;
                }

                .abc-date-time-picker {
                    .abc-date-picker {
                        .abc-input__inner {
                            border-top-right-radius: 0 !important;
                        }
                    }
                }
            }
        }

        .abc-radio-group {
            display: flex;
            align-items: center;
            height: 100%;

            .abc-radio + .abc-radio {
                margin-left: 24px;
            }
        }

        .abc-checkbox-group {
            display: flex;
            align-items: center;
            height: 100%;

            .abc-checkbox-wrapper + .abc-checkbox-wrapper {
                margin-left: 24px;
            }
        }

        .abc-date-time-picker__new-style {
            display: flex;
            border-right: 1px solid $P6;

            &.no-border-right {
                border-right: 0;
            }

            .abc-date-picker {
                width: 90px !important;
            }

            .abc-time-picker {
                width: 60px;
            }
        }
    }
    // 以下样式需要调整到组件 ========== end
</style>
