<template>
    <div class="retail-western-medicine-autocomplete">
        <abc-autocomplete
            ref="abcAutocomplete"
            :key="closeSwitch"
            v-model="keyword"
            :fetch-suggestions="queryItemAsync"
            :show-suggestions="showSuggestions"
            :custom-class="`wm-autocomplete-suggestion medicine-autocomplete-suggestion ${ customClass}`"
            :async-fetch="true"
            :keyboard-event="['/']"
            focus-show
            :delay-time="10"
            :placeholder="placeholder"
            :close-on-click-outside="handleCloseOnClickOutside"
            @enterEvent="selectItem"
        >
            <template slot="suggestion-header">
                <div class="suggestion-title">
                    <div v-if="isShowGoodsAutocompleteShortId" class="goods-code">
                        商品编码
                    </div>
                    <div class="name" style="flex: 2;">
                        药名
                    </div>
                    <div class="spec" style="flex: 1; padding-left: 10px;">
                        规格
                    </div>
                    <div class="stock" style="flex: 1; padding-right: 10px; text-align: right;">
                        库存
                    </div>
                    <div class="price" style="width: 80px; padding-right: 10px; text-align: right;">
                        价格
                    </div>
                    <div style="width: 48px; text-align: center;">
                        医保
                    </div>
                    <div class="manufacturer" style="width: 110px; padding-left: 10px;">
                        <manufacturer-select
                            v-if="isSupportManufacturerFilter"
                            v-model="selectedManufacturer"
                            :manufacturer-options="manufacturerOptions"
                            size="tiny"
                            placeholder="厂家"
                            @change="handleManufacturerChange"
                        ></manufacturer-select>
                        <template v-else>
                            厂家
                        </template>
                    </div>
                    <div class="remark" style="width: 112px; padding-left: 10px;">
                        备注
                    </div>
                    <div class="min-expiry-date" style="width: 90px; padding-left: 10px; text-align: center;">
                    </div>
                </div>
            </template>

            <template slot="suggestions" slot-scope="props">
                <dt
                    slot="reference"
                    class="suggestions-item"
                    :class="{
                        selected: props.index === props.currentIndex,
                        'is-tips': props.suggestion.isTips,
                        'not-source': isNoStock(props.suggestion),
                    }"
                    :disabled="props.suggestion.disabled"
                    @click="selectItem(props.suggestion)"
                >
                    <div
                        v-if="isShowGoodsAutocompleteShortId"
                        class="goods-code"
                        :title="props.suggestion.shortId || ''"
                        style="width: 70px; padding-left: 6px;"
                    >
                        <template v-if="!isNoStock(props.suggestion)">
                            {{ props.suggestion.shortId || '' }}
                        </template>
                    </div>

                    <div class="name" style="flex: 2; overflow: hidden;" :title="props.suggestion | goodsHoverTitle">
                        <abc-flex :gap="4" align="center">
                            <span class="ellipsis" style="max-width: calc(100% - 22px);">
                                {{ getName(props.suggestion) }}
                            </span>

                            <biz-goods-info-tag-group
                                v-if="showAntibioticAndDangerIngredient(props.suggestion)"
                                :product-info="props.suggestion"
                                :is-fold-tags="true"
                                style="display: inline-flex; flex: 1;"
                            >
                            </biz-goods-info-tag-group>

                            <span v-if="props.suggestion.deviceInfo && !props.suggestion.deviceInfo.innerFlag" class="device-name">
                                {{ props.suggestion.deviceInfo.deviceUuid || '' }}
                            </span>

                            <compose-tag v-if="props.suggestion.type === 11"></compose-tag>

                            <!--云检标签-->
                            <biz-exam-business-tag
                                is-cloud-tag
                                :cloud-supplier-flag="props.suggestion.cloudSupplierFlag"
                                :disabled="props.suggestion.disabled"
                            ></biz-exam-business-tag>

                            <!--外包标签-->
                            <biz-exam-business-tag
                                is-out-sourcing-tag
                                :coop-flag="props.suggestion.coopFlag"
                                :type="props.suggestion.type"
                                :sub-type="props.suggestion.subType"
                            ></biz-exam-business-tag>
                        </abc-flex>
                    </div>

                    <template v-if="!props.suggestion.isTips">
                        <div
                            v-if="fromPharmacy === PharmacyTypeEnum.AIR_PHARMACY"
                            class="gray spec"
                            :title="props.suggestion.spec || ''"
                            style="flex: 1; padding-left: 10px;"
                        >
                            {{ props.suggestion.spec || '' }}
                        </div>

                        <div
                            v-else
                            class="gray spec"
                            :title="getSpec(props.suggestion)"
                            style="flex: 1; padding-left: 10px;"
                        >
                            {{ props.suggestion | getSpec }}
                        </div>

                        <div
                            class="stock"
                            :title="displayInventory(props.suggestion)"
                            style="flex: 1; padding-right: 10px; text-align: right;"
                        >
                            <template v-if="!isNoStock(props.suggestion)">
                                {{ displayInventory(props.suggestion) }}
                            </template>
                        </div>

                        <div
                            v-if="!isSysItem(props.suggestion)"
                            class="gray price"
                            style="width: 80px; padding-right: 10px; text-align: right;"
                        >
                            <template v-if="!isNoStock(props.suggestion)">
                                <abc-money :value="props.suggestion.packagePrice" :is-format-money="false"></abc-money>
                            </template>
                        </div>

                        <div class="gray" style="width: 48px; text-align: center;">
                            {{ props.suggestion.medicalFeeGrade | medicalFeeGrade2Str }}
                        </div>

                        <div
                            class="gray manufacturer"
                            :title="props.suggestion.manufacturer"
                            style="width: 110px; padding-left: 10px;"
                        >
                            {{ props.suggestion.manufacturer }}
                        </div>

                        <div
                            class="gray remark ellipsis"
                            :title="props.suggestion.remark"
                            style="width: 112px; padding-left: 10px;"
                        >
                            {{ props.suggestion.remark }}
                        </div>

                        <div
                            class="gray min-expiry-date"
                            style="width: 90px; padding-left: 10px; text-align: center;"
                        >
                            {{ props.suggestion.minExpiryDate }}
                        </div>
                    </template>
                </dt>
            </template>

            <div slot="prepend" class="search-icon">
                <abc-icon icon="s-add-line-medium" color="var(--abc-color-T3)"></abc-icon>
            </div>
        </abc-autocomplete>
    </div>
</template>

<script type="text/ecmascript-6">
    import { getSpec } from 'src/filters';
    import ComposeTag from 'src/views/outpatient/common/compose-tag';
    import { mapGetters } from 'vuex';
    import {
        GoodsSubTypeEnum,
        GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import GoodsAPI from 'api/goods';
    import GoodsV3API from 'api/goods/index-v3';
    import BizGoodsInfoTagGroup from '@/components-composite/biz-goods-info-tag-group/index.js';
    import BizExamBusinessTag from 'src/components-composite/biz-exam-business-tag';
    import ManufacturerSelect from 'views/inventory/common/manufacturer-select/index.vue';
    import useAutoCompleteManufacturerSelect from 'views/inventory/common/manufacturer-select';

    export default {
        name: 'TreatmentMedicineItemAutocomplete',
        components: {
            BizExamBusinessTag,
            BizGoodsInfoTagGroup,
            ComposeTag,
            ManufacturerSelect,
        },
        props: {
            version: {
                type: [Number, String],
                default: 2,
            },
            closeSwitch: Boolean,
            placeholder: {
                type: String,
                default: '输入药品或项目拼音码',
            },
            customClass: {
                type: String,
                default: '',
            },
            canKeyboardCharge: {
                type: Boolean,
                default: true,
            },
            queryFunction: {
                type: Function,
            },
            splitByStock: {
                // 是否需要根据库存分割数据
                type: Boolean,
                default: false,
            },
            disabledChangeSpecification: {
                type: Boolean,
                default: false,
            },
            withDomainMedicine: {
                type: Number,
            },
            // 0本地药房，1空中药房
            fromPharmacy: {
                type: Number,
                default: 0,
            },
            focusShow: {
                type: Boolean,
            },
            // 聚焦搜索
            focusSearch: {
                type: Boolean,
                default: false,
            },
            clinicId: [Number, String],
            departmentId: [Number, String],
            jsonType: {
                type: [Array],
                required: true,
            },
            diagnosis: {
                // 可以加入诊断信息查询
                type: [String],
            },
            spec: {
                // 中药要根据规格查询
                type: [String],
            },
            queryV3Params: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            // 聚焦自动推荐
            autoSuggestion: {
                type: Boolean,
                default: false,
            },
            // 是否支持厂家筛选
            isSupportManufacturerFilter: {
                type: Boolean,
                default: true,
            },
        },
        setup() {
            const {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            } = useAutoCompleteManufacturerSelect();

            return {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            };
        },
        data() {
            return {
                PharmacyTypeEnum,
                keyword: '',
                showSuggestions: false,
                searchType: '',
                refAutoComplete: null,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'clinicConfig',
                'disableNoStockGoods',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isShowGoodsAutocompleteShortId() {
                return this.viewDistributeConfig.Inventory.isShowGoodsAutocompleteShortId;
            },
        },
        watch: {
            keyword: {
                handler (val) {
                    if (!val.trim()) {
                        this.$emit('clear');
                    }
                    if (typeof this.clearManufacturerData === 'function') this.clearManufacturerData();
                },
            },
        },
        mounted() {
            if (this.isSupportManufacturerFilter) {
                this.refAutoComplete = this.$refs.abcAutocomplete;
            }
        },
        methods: {
            getSpec,
            isSysItem(goods) {
                return goods.type === GoodsTypeEnum.REGISTRATION ||
                    goods.type === GoodsTypeEnum.EXPRESS_DELIVERY ||
                    goods.type === GoodsTypeEnum.DECOCTION;
            },

            needStock(item) {
                return (
                    item.type === GoodsTypeEnum.MEDICINE ||
                    item.type === GoodsTypeEnum.MATERIAL ||
                    item.type === GoodsTypeEnum.GOODS
                );
            },

            isNoStock(goods) {
                return this.isGoods(goods) && goods.noStocks;
            },

            async queryItemAsync(keyword, callback) {
                // 解析keyword，使用空格分隔，取第一项为原始keyword，最后一项为parseManufacturer
                let parseManufacturer = '';

                if (keyword && keyword.trim()) {
                    const keyParts = keyword.trim().split(' ').filter((part) => part.trim());
                    if (keyParts.length > 1) {
                        keyword = keyParts[0];
                        parseManufacturer = keyParts[keyParts.length - 1];
                    }
                }

                if (!keyword.trim() && this.focusSearch) {
                    callback([]);
                    return false;
                }
                if (!keyword.trim() && !this.focusShow) {
                    callback([]);
                    return false;
                }
                let res;
                if (!keyword.trim() && this.autoSuggestion) {
                    res = await GoodsV3API.searchGoods(Object.assign(this.queryV3Params, {
                        keyword,
                        intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                    }));
                } else if (+this.version === 3) {
                    res = await GoodsV3API.searchGoods(
                        Object.assign(this.queryV3Params, {
                            keyword,
                            jsonType: this.jsonType,
                            withDomainMedicine: this.withDomainMedicine,
                            intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                        }),
                    );
                } else {
                    res = await GoodsAPI.queryStockGoods({
                        keyword,
                        clinicId: this.clinicId,
                        departmentId: this.departmentId,
                        jsonType: this.jsonType,
                        diagnosis: this.diagnosis,
                        spec: this.spec,
                        withDomainMedicine: this.withDomainMedicine,
                        intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                    });
                }
                const { data } = res;

                if (!data) {
                    callback([]);
                    return false;
                }
                if (this.disableNoStockGoods) {
                    data.list = data.list.map((item) => {
                        if (this.needStock(item)) {
                            item.disabled = item.noStocks || (item.stockPackageCount + item.stockPieceCount) <= 0;
                        }
                        return item;
                    });
                }

                if (this.splitByStock) {
                    // 可能有混合搜索，需要先把不用判断库存的商品算出个数
                    const noNeedStock = data.list.filter((item) => {
                        return !this.needStock(item);
                    }).length;

                    let tipsIndex = data.list.filter((item) => {
                        return this.needStock(item) && !item.noStocks;
                    }).length;

                    tipsIndex += noNeedStock;

                    if (tipsIndex !== data.list.length) {
                        data.list.splice(tipsIndex, 0, {
                            disabled: true,
                            isTips: true,
                            name: this.splitString,
                        });
                    }
                }

                // 处理厂家筛选
                const filteredList = data.list || [];
                if (this.isSupportManufacturerFilter && this.createManufacturerOptions) {
                    this.createManufacturerOptions(filteredList);
                }

                // 如果选择了厂家，则过滤结果
                if (this.isSupportManufacturerFilter && this.filterManufacturer) {
                    data.list = this.filterManufacturer(filteredList);
                }

                data.list.forEach((item) => {
                    item.productName = item.name;
                    item.name = item.name || item.medicineCadn;
                });

                if (callback) {
                    callback(data.list);
                } else if (this.refAutoComplete) {
                    this.refAutoComplete.focus();
                    this.refAutoComplete.callbackHandler(data.list);
                }
            },
            /**
             * @desc 拼接商品name
             * <AUTHOR>
             * @date 2019/03/30 11:20:12
             */
            getName(item) {
                if (item.type === 1 && item.subType === 2) {
                    return item.medicineCadn || item.name;
                }
                if (item.medicineCadn) {
                    return item.medicineCadn + (item.name ? `(${item.name})` : '');
                }
                return item.name;

            },

            /**
             * 西药和西成药才展示限制级和精麻毒标签
             * @return {boolean}
             */
            showAntibioticAndDangerIngredient(item) {
                return item.type === GoodsTypeEnum.MEDICINE &&
                    (
                        item.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine ||
                        item.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM
                    );
            },

            isGoods(item) {
                return item.type === GoodsTypeEnum.MEDICINE || item.type === GoodsTypeEnum.MATERIAL || item.type === GoodsTypeEnum.GOODS || item.type === 24;
            },

            /**
             * @desc display autocomplete 下拉框 库存信息
             * <AUTHOR>
             * @date 2018/08/02 15:11:50
             */
            displayInventory(item) {
                if (item.type !== 1 && item.type !== 2 && item.type !== 7) return '';
                let str = '';
                if (item.stockPackageCount) {
                    str += `${item.stockPackageCount}${item.packageUnit}`;
                }
                if (item.stockPieceCount) {
                    str += `${item.stockPieceCount}${item.pieceUnit}`;
                }
                if (!item.stockPackageCount && !item.stockPieceCount) {
                    if (item.packageUnit) {
                        str += `0${item.packageUnit || ''}`;
                    } else {
                        str += `0${item.pieceUnit || 'g'}`;
                    }
                }
                return str;
            },

            /**
             * @desc 选择后处理函数
             * <AUTHOR>
             * @date 2019/03/25 19:15:58
             */
            async selectItem(selected) {
                if (!selected) return false;
                this.$emit('select', selected);
                this.keyword = '';
            },

            handleManufacturerChange() {
                // 如果不支持厂家筛选，直接返回
                if (!this.isSupportManufacturerFilter) {
                    return;
                }

                // 配合focus-show重新触发查询
                this.$nextTick(() => {
                    this.refAutoComplete.focus();
                });
            },

            handleCloseOnClickOutside(e) {
                const eventPath = e?.path || (e?.composedPath?.());

                // 检查是否点击了厂家筛选下拉框
                if (this.isSupportManufacturerFilter && eventPath?.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className.includes('goods-auto-complete-manufacturer-select');
                })) {
                    return false;
                }

                // 厂家筛选关闭面板时重置搜索结果
                if (this.isSupportManufacturerFilter && this.selectedManufacturer) {
                    // 清除选中厂家
                    if (typeof this.clearManufacturerData === 'function') {
                        this.clearManufacturerData(true);
                    }
                    // 更新列表数据
                    this.queryItemAsync(this.keyword, (list) => {
                        // 手动触发下拉框更新-面板不展示
                        if (this.refAutoComplete) {
                            this.refAutoComplete.isFocus = false;
                            this.refAutoComplete.callbackHandler(list);
                        }
                    });
                }

                this.$emit('closePanel');

                return true;
            },
        },
    };
</script>
