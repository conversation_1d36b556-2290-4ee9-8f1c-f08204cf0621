<template>
    <div v-abc-loading:page="loading">
        <abc-container-center-top-head>
            <h2>
                医嘱执行
                <abc-badge
                    v-if="needExecutiveCount"
                    style="margin-top: 2px;"
                    :value="needExecutiveCount"
                    variant="count"
                ></abc-badge>
            </h2>
            <abc-text
                v-if="recordCount"
                tag="div"
                size="mini"
                theme="success-light"
                style="margin-left: 16px;"
            >
                {{ recordCount }}
            </abc-text>
            <div v-if="enableShowHistorySheet" class="buttons-wrapper">
                <abc-tooltip
                    v-if="!isClosed && executeFormItems && executeFormItems.length && disabledForm"
                    :disabled="!isDisabledExecuteButton && !isNeedAst"
                >
                    <span slot="content">{{ disabledExecuteButtonTips(isNeedAst) }}</span>
                    <div style="margin-right: 4px;">
                        <abc-check-access>
                            <abc-button
                                :disabled="isDisabledExecuteButton || isNeedAst"
                                @click="openDialog"
                            >
                                执行
                            </abc-button>
                        </abc-check-access>
                    </div>
                </abc-tooltip>

                <abc-button
                    v-if="isCanEditDelete && disabledForm"
                    type="blank"
                    @click="handleUpdateTreatmentForm"
                >
                    修改
                </abc-button>

                <abc-button
                    v-if="isCanEditDelete && !disabledForm"
                    :loading="inputCalcLoading"
                    @click="saveTreatmentFormSubmit"
                >
                    保存
                </abc-button>

                <abc-button
                    v-if="isCanEditDelete && !disabledForm"
                    type="blank"
                    style="margin-right: 4px;"
                    @click="fetch"
                >
                    取消
                </abc-button>

                <abc-button
                    v-if="isCanEditDelete && disabledForm"
                    type="danger"
                    style="margin-right: 4px;"
                    @click="handleBeforeDelete"
                >
                    删除
                </abc-button>

                <push-payment-popper
                    v-if="isShowPushPaymentBtn"
                    :source-type="1"
                    :patient="patient"
                    :charge-order-create-source="chargeOrderCreateSource"
                    :patient-order-id="patientOrderId"
                    :is-partial-charge="chargeStatus === ChargeStatusEnum.PART_CHARGED"
                    style="margin-right: 4px;"
                    @update-patient-info="handleUpdatePatientInfo"
                    @payment-success="fetch"
                >
                </push-payment-popper>

                <print-popper
                    v-if="enableShowExecutedRecords"
                    v-model="printSelect"
                    size="small"
                    :width="64"
                    placement="bottom"
                    style="margin-left: 0;"
                    :box-style="{
                        left: '-48px', width: '128px'
                    }"
                    :options="printOptions"
                    @print="printHandler"
                    @select-print-setting="openPrintConfigSettingDialog"
                ></print-popper>
                <!--<abc-button-->
                <!--    slot="reference"-->
                <!--    type="blank"-->
                <!--    style="margin-left: 4px;"-->
                <!--    class="app-printer-config-button"-->
                <!--    @click="showSettingsDialog = true"-->
                <!--&gt;-->
                <!--    <abc-icon icon="set"></abc-icon>-->
                <!--</abc-button>-->
            </div>
        </abc-container-center-top-head>
        <abc-container-center-main-content>
            <abc-tips-card-v2
                v-if="disableByLock"
                theme="warning"
                style="margin-bottom: 16px;"
                align="center"
                border-radius
            >
                {{ `收费员${lockedInfo.employeeName}正在退费中，退费结束前不可操作` }}
            </abc-tips-card-v2>
            <div v-if="enableShowHistorySheet">
                <div v-if="openClinicName" class="treatment-execute-cross">
                    <i class="iconfont cis-icon-Attention"></i>
                    开单门店：{{ openClinicName }}
                </div>

                <abc-form ref="postDataForm">
                    <div
                        class="patient-detail-wrapper"
                        style="margin-bottom: 16px;"
                    >
                        <div class="patient-info">
                            <patient-section
                                :key="patient?.id"
                                v-model="patient"
                                size="medium"
                                :disabled="true"
                                :loading="loading"
                                :default-patient="selectedPatient"
                                :need-hover-popover="enableShowExecutedRecords"
                                :hidden-patient-mobile="!enableShowExecutedRecords"
                                :is-can-see-patient-mobile="isCanSeePatientMobileInNurse"
                                @update-patient="changePatientInfo"
                            ></patient-section>
                        </div>
                    </div>
                </abc-form>

                <!--诊断-->
                <diagnosis
                    v-if="disabledForm"
                    :diagnosis="diagnosis"
                    :name="currentName"
                    :created="createdTime"
                    :total-fee="totalFee"
                    :received-fee="receivedFee"
                    :status-name="statusName"
                    :source="source"
                    :charge-status="chargeStatus"
                ></diagnosis>
                <template v-if="disabledForm">
                    <!--执行项目-->
                    <goods-item
                        :product-forms="productForms"
                        :is-closed="isClosed"
                        @refresh="refreshDetail"
                        @openDialog="openDialog"
                    ></goods-item>

                    <!--西药处方-->
                    <w-m-list
                        :western-medicines="prescriptionWesternForms"
                        :is-charge-clinic="!openClinicName"
                        :source="source"
                        :is-closed="isClosed"
                        @openDialog="openDialog"
                        @refresh="refreshDetail"
                    ></w-m-list>
                    <!--输液处方-->
                    <i-n-list
                        :infusion-medicines="prescriptionInfusionForms"
                        :is-charge-clinic="!openClinicName"
                        :source="source"
                        :is-closed="isClosed"
                        @openDialog="openDialog"
                        @refresh="refreshDetail"
                    ></i-n-list>
                    <!--中药处方-->
                    <c-m-list :chinese-p-rs="prescriptionChineseForms" :source="source"></c-m-list>
                    <!--外治处方-->
                    <external-list
                        v-if="prescriptionExternalForms.length"
                        :forms="prescriptionExternalForms"
                        :source="source"
                        :is-closed="isClosed"
                        @openDialog="openDialog"
                    ></external-list>

                    <advise v-if="doctorAdvice" :doctor-advice="doctorAdvice"></advise>
                </template>
                <template v-else>
                    <abc-form ref="postDataForm">
                        <treatment-bill-form
                            ref="billForm"
                            :key="$route.params.id"
                            :input-calc-loading.sync="inputCalcLoading"
                            :post-data="postData"
                            :is-treatment-add="false"
                            :loading="loading"
                        ></treatment-bill-form>
                    </abc-form>
                </template>
            </div>
            <abc-content-empty
                v-else
                top="310px"
                value="当前账号无权限查看本单"
                icon-name="s-emptyIcon-permission"
                icon-color="#ffffff"
            ></abc-content-empty>
            <treatment-sidebar
                :is-hospital-sheet="isHospitalSheet"
                :pre-door-address="preDoorAddress"
                :history="actionsHistory"
                :has-check-auth="enableShowExecutedRecords"
                :charge-sheet-id="$route.params.id"
                @refresh="refreshDetail"
            ></treatment-sidebar>
            <!--<pre>{{res.data}}</pre>-->
        </abc-container-center-main-content>

        <execute-dialog
            v-if="visible"
            v-model="visible"
            :is-hospital-sheet="isHospitalSheet"
            :pre-door-address="preDoorAddress"
            :patient-info="patient"
            :product="currentItem"
            :form-items="executeFormItems"
            :form-item-id="executedId"
            :detail="postData"
            :charge-sheet-id="$route.params.id"
            @refresh="refreshDetail"
        ></execute-dialog>

        <select-print-dialog
            v-if="showMedicineTagDialog"
            v-model="showMedicineTagDialog"
            :tag-forms="tagForms"
            @confirm="selectPrintConfirm"
        ></select-print-dialog>

        <treatment-settings v-if="showSettingsDialog" v-model="showSettingsDialog"></treatment-settings>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        mapGetters, mapActions,
    } from 'vuex';
    import TreatmentApi from 'api/treatment';
    import WMList from './common/wmlist';
    import INList from './common/inlist';
    import CMList from './common/cmlist';
    import ExternalList from './common/external-list';
    import Diagnosis from './common/diagnosis';
    import GoodsItem from './common/goods-item';
    import PatientSection from 'views/layout/patient/patient-section/index.vue';
    import TreatmentSidebar from './sidebar/treatment-sidebar';
    const ExecuteDialog = () => import('./treatment-execute-dialog/execute-dialog');
    import SelectPrintDialog from 'views/cashier/select-print-dialog.vue';
    import PushPaymentPopper from 'views/common/components/push-payment.vue';
    import TreatmentSettings from './treatment-settings/treatment-settings.vue';
    import TreatmentBillForm from './common/treatmen-bill-form/treatment-bill-form';

    import PrintPopper from 'views/print/popper';
    import TreatmentAPI from 'api/treatment';
    import {
        getAbcPrintOptions,
    } from '@/printer/print-handler';
    import TpsAPI from 'api/tps';

    import {
        SourceFormTypeEnum, ChargeStatusEnum, ChargeSheetTypeEnum,
    } from '@/service/charge/constants';
    import { GoodsTypeEnum } from '@abc/constants';

    import clone from 'utils/clone';
    import ChargeStatusCommon from 'views/treatment/common/charge-status-common';
    import Advise from 'views/pharmacy/advise.vue';
    import PrintAPI from 'api/print.js';
    import AbcPrinter from '@/printer/index.js';
    import { ANONYMOUS_DOCTOR_ID } from 'src/assets/configure/constants';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import {
        createGUID, numToChinese,
    } from '@/utils';
    import { AstEnum } from 'views/layout/prescription/constant';
    import ChargeAPI from 'api/charge';
    import i18n from '@/i18n';
    import { SelectPrintFunctionalDialog } from 'views/cashier/select-print-dialog';
    import AbcSocket from 'views/common/single-socket.js';
    import {
        ChargeBusinessSceneEnum, LockBusinessKeyEnum,
    } from '@/common/constants/business-lock';
    import PatientOrderLockService from 'utils/patient-order-lock-service';
    import Logger from 'utils/logger';

    export default {
        name: 'TreatmentDetail',
        components: {
            PatientSection,
            WMList,
            INList,
            CMList,
            ExternalList,
            Diagnosis,
            GoodsItem,
            PrintPopper,
            ExecuteDialog,
            TreatmentSidebar,
            PushPaymentPopper,
            Advise,
            SelectPrintDialog, // 选择标签打印内容弹窗
            TreatmentSettings,
            TreatmentBillForm,
        },
        mixins: [ ChargeStatusCommon ],
        provide() {
            return {
                chargeForm: this,
            };
        },
        data() {
            // 获取详情数据
            return {
                loading: true,
                wList: [],
                cList: [],
                eList: [],
                tList: [],
                patient: {
                    id: null,
                    name: '',
                    sex: '男',
                    age: {
                        year: null,
                        month: null,
                    },
                    mobile: '',
                    isMember: null,
                },
                /* eslint-disable */
                patientOrderId: '',
                currentPatientOrderId: '',
                /* eslint-enable */

                // 是否是住院单
                isHospitalSheet: false,
                // 患者上次上门地点
                preDoorAddress: '',

                dispensingStatus: false,

                detailData: null,
                source: '',
                chargeOrderCreateSource: '', // 执行站收费单创建源
                diagnosis: '',
                currentName: '',
                createdTime: '',
                totalFee: '',
                receivedFee: '',
                statusName: '',
                openClinicName: '', // 开单门店

                prescriptionWesternForms: [], // 西药处方
                prescriptionChineseForms: [], // 中药处方
                prescriptionInfusionForms: [], // 输液处方
                prescriptionExternalForms: [], // 外治处方
                prescriptionGlassesForms: [],

                examinationForms: [], // 执行项目
                treatmentForms: [],
                productForms: [],

                currentItem: '', // 当前执行的项目

                visible: false,
                btnLoading: false,
                printable: null,
                printSelect: '治疗执行单',
                printData: null,
                printType: 1,

                actionsHistory: { },
                executedId: '',
                executeFormItems: [],

                chargeStatus: 0,

                onlyExecuteAfterPaid: 0,

                doctorAdvice: null,

                showMedicineTagDialog: false,
                isFirstPrintAdviceTag: false,
                printAdviceTagLoading: false,
                isFirstPrintPatientTag: false,
                printPatientTagLoading: false,

                ChargeStatusEnum,
                showSettingsDialog: false,
                isAnyExecuted: false, // 执行单是否被执行过
                disabledForm: true,
                postData: {},
                inputCalcLoading: false,

                enableShowHistorySheet: true, // 执行单查看权限
                enableShowExecutedRecords: true, // 执行历史查看权限

                isClosed: false, // 执行单状态 false-开启 true-关闭
                lockedInfo: null, // 锁单信息
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'treatment',
                'westernMedicineConfig',
                'clinicConfig',
                'printPrescriptionConfig',
                'isOpenMp',
                'draftTreatmentNews',
                'currentClinic',
                'isCanSeePatientMobileInNurse',
                'chainBasic',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),


            /**
             * @desc 是否支持打印执行凭证
             * @return {boolean}
             */
            isEnableExecuteCertificate() {
                return !!this.chainBasic.isEnableExecuteCertificate;
            },
            /**
            * @desc 是否展示推送支付按钮
             *  开通微诊所 && 非匿名患者 && 待收费状态 || 部分收费
            */
            isShowPushPaymentBtn() {
                const {
                    chargeStatus = 0,
                    isOpenMp = false,
                    hasPatient = false,
                } = this;

                return (chargeStatus === ChargeStatusEnum.UN_CHARGE || chargeStatus === ChargeStatusEnum.PART_CHARGED) && isOpenMp && hasPatient;
            },

            hasPatient() {
                return this.patient && this.patient.id !== ANONYMOUS_DOCTOR_ID && this.patient.id !== '';
            },

            selectedPatient() {
                return this.treatment.selectedPatient;
            },

            isGuangZhou() {
                return this.$abcSocialSecurity.isOpenSocial && this.$abcSocialSecurity.config.isGuangdongGuangzhou;
            },

            recordCount() {
                if (!this.isGuangZhou) return '';
                const strArr = [];
                const { shebaoCardInfo } = this.selectedPatient || {};
                const { extend } = shebaoCardInfo || {};
                const { annualFundPay } = extend || {};
                if (Number(annualFundPay ?? 0)) {
                    const feeStr = `本年统筹累计：${i18n.t('currencySymbol')}${annualFundPay}`;
                    strArr.push(feeStr);
                }
                return strArr.join('，');
            },


            // printable 状态 以及 取值
            // executeTransfusionSheet 输注执行单 （只包含输液项）
            // executeInfusionSheet 治疗单（输液 + 治疗）
            // executeTreatmentSheet 理疗单（理疗）
            // executeTherapySheet 理疗 + 治疗
            /**
             * @desc 判断是否能够打印输注单，如果开启了包含治疗项目的输注单，需要判断治疗项目
             */
            disabledInfusionTreatment() {
                // 包含治疗项目
                if (this.isPrintIncludeTreatment) {
                    return this.printable && !this.printable.executeInfusionSheet;
                }
                return this.printable && !this.printable.executeTransfusionSheet;
            },

            // 打印治疗执行单执行单 治疗+理疗
            disabledExecuteTherapySheet() {
                return this.printable && !this.printable.executeTherapySheet;
            },
            // 是否能打印样本条码
            disabledPrintExaminationBarCode() {
                return !this.printable?.examinationExaminationBarCode;
            },
            // 判断是否能打印检验单
            disabledExamination() {
                return this.printable && !this.printable.examinationExamination;
            },
            disabledPrintInspect() {
                return this.printable && !this.printable.examinationInspection;
            },
            treatmentQuickList() {
                return this.treatment && this.treatment.quickList || [];
            },
            needExecutiveCount() {
                let count = 0;
                this.productForms.forEach((form) => {
                    if (
                        form.sourceFormType === SourceFormTypeEnum.TREATMENT ||
                        form.sourceFormType === SourceFormTypeEnum.OTHER_FEE ||
                        form.sourceFormType === SourceFormTypeEnum.ADDITIONAL_FORM ||
                        form.sourceFormType === SourceFormTypeEnum.COMPOSE
                    ) {
                        form.chargeFormItems.forEach((item) => {
                            if (item.productInfo &&
                                item.productInfo.type === GoodsTypeEnum.TREATMENT &&
                                (item.executedUnitCount < item.unitCount) &&
                                item.status <= 1) {
                                count++;
                            }
                        });
                    }
                });
                return count;
            },

            // 检验检验单打印选项
            examPrintOptions() {
                return this.viewDistributeConfig.Print.examPrintOptions;
            },

            printOptions() {
                return [
                    {
                        value: this._printOptions.INFUSION_EXECUTE.label,
                        disabled: this.disabledInfusionTreatment,
                        tips: `没有${this._printOptions.INFUSION_EXECUTE.label}`,
                    },
                    {
                        value: this._printOptions.TREATMENT_EXECUTE.label,
                        disabled: this.disabledExecuteTherapySheet,
                        tips: `没有${this._printOptions.TREATMENT_EXECUTE.label}`,
                    },
                    {
                        value: this._printOptions.EXAMINATION_CODE.label,
                        disabled: this.disabledPrintExaminationBarCode,
                        tips: `没有${this._printOptions.EXAMINATION_CODE.label}`,
                    },
                    {
                        value: this.examPrintOptions.examination.label,
                        disabled: this.disabledExamination,
                        tips: `没有${this.examPrintOptions.examination.label}`,
                    },
                    {
                        value: this.examPrintOptions.inspection.label,
                        disabled: this.disabledPrintInspect,
                        tips: `没有${this.examPrintOptions.inspection.label}`,
                    },
                    {
                        value: this._printOptions.MEDICINE_TAG.label,
                        disabled: !this.tagForms || this.tagForms.length <= 0,
                        tips: `没有${this._printOptions.MEDICINE_TAG.label}`,
                    },
                    {
                        value: this._printOptions.PATIENT_TAG.label,
                        disabled: !this.printable || !this.printable.patientTag,
                        tips: `没有${this._printOptions.PATIENT_TAG.label}`,
                    },
                ];
            },
            /**
             * @desc 输注单是否包含了治疗项目
             * true  包含治疗项项目
             */
            isPrintIncludeTreatment() {
                return this.printPrescriptionConfig.infusionExecute.includeTreatment;
            },

            formProvider() {
                return [{ chargeFormItems: this.executeFormItems }];
            },
            // 用药标签选择内容
            tagForms() {
                let forms = [];
                const wesForms = clone(this.prescriptionWesternForms).map((form) => {
                    form.isFullChecked = true;
                    form.formItems = form.chargeFormItems.map((formItem) => {
                        formItem.checked = true;
                        return formItem;
                    });
                    delete form.chargeFormItems;
                    return form;
                }) || [];
                const infusionForm = clone(this.prescriptionInfusionForms).map((form) => {
                    form.isFullChecked = true;
                    form.formItems = form.chargeFormItems.map((formItem) => {
                        formItem.checked = true;
                        return formItem;
                    });
                    delete form.chargeFormItems;
                    return form;
                }) || [];
                const chineseForm = clone(this.prescriptionChineseForms).map((form) => {
                    form.isFullChecked = true;
                    form.formItems = form.chargeFormItems.map((formItem) => {
                        formItem.checked = true;
                        return formItem;
                    });
                    delete form.chargeFormItems;
                    return form;
                }) || [];
                forms = wesForms.concat(infusionForm).concat(chineseForm);
                return forms;
            },
            // 执行是否需要皮试
            isNeedAst() {
                return this.executeFormItems.every((x) => x.isNeedPs === true);
            },
            /**
             * @desc 执行单能否编辑、删除
             * @desc 执行站开单&&未收费&&未执行&&自己门店开出(自己门店开的没有clinicName字段)
             * @return {Boolean}
             */
            isCanEditDelete() {
                return this.source === ChargeSheetTypeEnum.THERAPY &&
                    this.chargeStatus === ChargeStatusEnum.UN_CHARGE &&
                    !this.isAnyExecuted &&
                    !this.openClinicName;

            },
        },
        watch: {
            // 如果路由有变化，会再次执行该方法
            '$route': ['fetch'],
        },
        created() {
            this.fetch();
            this.fetchPrintAllConfigIfNeed();
            this.$store.dispatch('fetchClinicBasic');
            this._printOptions = this.viewDistributeConfig.Print.printOptions;
            this.$abcEventBus.$on('treatment-detail-loading', (bool) => {
                this.loading = bool;
            }, this);
            this._handleSocketLock = (data) => {
                this.onLockSocket(data, 'lock');
            };
            this._handleSocketUnlock = (data) => {
                this.onLockSocket(data, 'unlock');
            };
            // socket监听
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.on('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },
        beforeDestroy() {
            this._socket.off('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.off('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },
        methods: {
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({
                    scene: 'treatment', isEnableExecuteCertificate: this.isEnableExecuteCertificate,
                }).generateDialogAsync({ parent: this });
            },
            ...mapActions([
                'updatePatientInfo',
                'fetchPrintAllConfigIfNeed',
                'refreshTreatmentQuickList',
            ]),
            /**
             * @desc 判断是否需要执行
             * <AUTHOR>
             * @date 2020-08-10 14:37:11
             * @params
             * @return
             */
            needExecutive(item, isExternal = false) {
                if (item) {
                    // 套餐的执行项能否执行取productInfo里面的needExecutive
                    // 非套餐执行项能否执行取item上的needExecutive
                    const {
                        composeType, needExecutive: needExecutiveItem, productInfo,
                    } = item;
                    let needExecutive;
                    if (composeType !== 2) {
                        needExecutive = needExecutiveItem;
                    } else {
                        needExecutive = productInfo && item.productInfo.needExecutive;
                    }
                    // 外治处方的诊疗、药品、商品都可以执行
                    if (isExternal) {
                        return (
                            item.executedUnitCount < item.unitCount &&
                            item.status <= 1 &&
                            needExecutive
                        );
                    }
                    return (
                        (item.productType === GoodsTypeEnum.TREATMENT || item.productType === GoodsTypeEnum.NURSE) &&
                        item.executedUnitCount < item.unitCount &&
                        item.status <= 1 &&
                        needExecutive
                    );
                }
                return false;
            },

            /**
             * @desc 判断处方是否需要执行
             * <AUTHOR>
             * @date 2022/04/25 15:55:50
             * @params
             * @return
             */
            needInflusionExecutive(item) {
                if (item) {
                    return item.needExecutive === 1 &&
                        item.executedUnitCount < item.usageInfo?.executedTotalCount &&
                        item.status <= 1;
                }
                return false;
            },

            /**
             * @desc 统计所有项目被执行次数
             * <AUTHOR>
             * @date 2022/11/24 16:35:15
             * @return {Number}
             */
            calcExecutedUnitCount(item) {
                if (this.isAnyExecuted) return;
                const { executedUnitCount } = item;
                if (+executedUnitCount > 0) {
                    this.isAnyExecuted = true;
                }
            },

            changePatientInfo(patientInfo) {
                this.fetch();
                this.$store.dispatch('setSelectedPatient', {
                    type: 'treatment',
                    patientInfo,
                });
                this.$store.dispatch('updatePatientInfo', {
                    type: 'treatment',
                    info: patientInfo,
                });
            },
            async fetch() {
                this.loading = true;
                this.disabledForm = true;
                this.isAnyExecuted = false;
                this.printData = null;
                await this.getCheckAuth();
                if (!this.enableShowHistorySheet) {
                    if (this.enableShowExecutedRecords) {
                        this.fetchHistory(this.$route.params.id);
                    } else {
                        this.loading = false;
                    }
                    return;
                }
                try {
                    const { data } = await TreatmentApi.fetchDetail(this.$route.params.id);
                    this.postData = data;
                    this.isClosed = Boolean(data.isClosed);
                    console.warn('是否收费后执行:', data.onlyExecuteAfterPaid);
                    this.doctorAdvice = data.medicalRecord && data.medicalRecord.doctorAdvice;
                    this.onlyExecuteAfterPaid = data.onlyExecuteAfterPaid;
                    this.chargeStatus = data.status;
                    this.prescriptionWesternForms = data.chargeForms.filter((form) => {
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN;
                    });
                    this.prescriptionInfusionForms = data.chargeForms.filter((form) => {
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION;
                    });

                    // 需要输注处方先皮试完成才能执行
                    this.prescriptionWesternForms = this.checkInfusionFormsStatus(this.prescriptionWesternForms);
                    this.prescriptionInfusionForms = this.checkInfusionFormsStatus(this.prescriptionInfusionForms);

                    this.prescriptionChineseForms = data.chargeForms.filter((form) => {
                        if (form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY) {
                            form.chargeFormItems = form.chargeFormItems.filter((item) => {
                                return item.productType !== GoodsTypeEnum.EXPRESS_DELIVERY &&
                                    item.productType !== GoodsTypeEnum.DECOCTION &&
                                    item.productType !== GoodsTypeEnum.INGREDIENT;
                            });
                        }
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE ||
                            form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY;
                    });
                    this.prescriptionExternalForms = data.chargeForms.filter((form) => {
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL;
                    });
                    this.productForms = data.chargeForms.filter((form) => {
                        return form.sourceFormType === SourceFormTypeEnum.EXAMINATION ||
                            form.sourceFormType === SourceFormTypeEnum.TREATMENT ||
                            form.sourceFormType === SourceFormTypeEnum.OTHER_FEE ||
                            form.sourceFormType === SourceFormTypeEnum.ADDITIONAL_FORM ||
                            form.sourceFormType === SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM ||
                            form.sourceFormType === SourceFormTypeEnum.MATERIAL ||
                            form.sourceFormType === SourceFormTypeEnum.GIFT ||
                            form.sourceFormType === SourceFormTypeEnum.COMPOSE ||
                            form.sourceFormType === SourceFormTypeEnum.NURSING ||
                            form.sourceFormType === SourceFormTypeEnum.EYEGLASSES ||
                            form.sourceFormType === SourceFormTypeEnum.SURGERY;
                    });
                    this.executeFormItems = this.filterExecuteForms(this.productForms);
                    this.executeFormItems = this.executeFormItems.concat(this.filterExecuteForms(this.prescriptionExternalForms));
                    // 含输注处方
                    this.executeFormItems = this.executeFormItems.concat(this.filterExecuteForms(
                        data.chargeForms.filter((form) => {
                            return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN ||
                                form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION;
                        }),
                    ));
                    this.source = data.source;
                    this.chargeOrderCreateSource = data.type;
                    this.statusName = data.statusName;

                    const { getDisplayDiagnosisText } = this.viewDistributeConfig;
                    this.diagnosis = this.source === ChargeSheetTypeEnum.THERAPY ? data.diagnosis : getDisplayDiagnosisText(data.medicalRecord);

                    this.createdTime = this.source === ChargeSheetTypeEnum.DIRECT_SALE ?
                        data.chargeSheetSummary.chargedTime :
                        data.chargeSheetSummary.diagnosedDate;
                    this.totalFee = data.chargeSheetSummary && data.chargeSheetSummary.totalFee || '';
                    this.receivedFee = data.chargeSheetSummary && data.chargeSheetSummary.receivedFee || '';
                    if (
                        this.source === ChargeSheetTypeEnum.DIRECT_SALE ||
                        this.source === ChargeSheetTypeEnum.THERAPY ||
                        this.source === ChargeSheetTypeEnum.EXAMINATION_INSPECTION
                    ) {
                        this.currentName = (data.sellerName || '未指定') + (data.sellerDepartmentName ? `-${data.sellerDepartmentName}` : '');
                    } else {
                        this.currentName = (data.chargeSheetSummary.doctorName || '未指定') + (data.departmentName ? `-${data.departmentName}` : '');
                    }
                    this.printable = data.printable; // 打印状态判断

                    this.patient = data.patient;
                    this.patientOrderId = data.patientOrderId;
                    this.currentPatientOrderId = data.patientOrderId;
                    this.isHospitalSheet = !!data.hospitalSheetId;

                    this.openClinicName = data.clinicName; // 开单门店

                    if (this.enableShowExecutedRecords) {
                        this.fetchHistory(this.$route.params.id);
                    }
                    await this.getLockInfo();
                    this.loading = false;
                } catch (e) {
                    console.error(e);
                    this.loading = false;
                }
            },

            // 获取查看权限
            async getCheckAuth() {
                this.loading = true;
                try {
                    const res = await TreatmentApi.getExecutedDetailCheckAuth(this.$route.params.id);
                    const {
                        enableShowHistorySheet, enableShowExecutedRecords,
                    } = res.data;
                    this.enableShowHistorySheet = enableShowHistorySheet === 1;
                    this.enableShowExecutedRecords = enableShowExecutedRecords === 1;
                    // this.loading = false;
                } catch (e) {
                    this.loading = false;
                    this.enableShowHistorySheet = false;
                    this.enableShowExecutedRecords = false;
                    console.error(e);
                }
            },
            async openDialog(item) {
                if (item) {
                    this.currentItem = item;
                    this.executedId = item.id;
                }
                this.visible = true;
            },

            async refreshDetail() {
                await this.fetch();
                const chargeSheetId = this.$route.params.id;
                const { statusName } = this;
                this.$store.dispatch('setSelectedItem', {
                    type: 'treatment',
                    selectedItem: this.treatmentQuickList.find((item) => {
                        if (item.id === chargeSheetId) {
                            item.statusName = statusName;
                            return item;
                        }
                    }),
                });
            },
            /**
             * @desc 获取打印二维码
             * <AUTHOR>
             * @date 2021-12-09 16:18:52
             */
            async fetchQrCode() {
                let qrcode = '';
                if (this.isOpenMp) {
                    try {
                        qrcode = await TpsAPI.genQrCode(this.postData.patientOrderId);
                        return qrcode;
                    } catch (e) {
                        qrcode = '';
                    }
                }
                return qrcode;
            },

            /**
             * @desc 获取打印数据
             * <AUTHOR>
             * @date 2021-12-10 11:22:32
             */
            async fetchPrintData(printType) {
                let printData = null;
                let isPrintQrcode;
                const printLoading = this.$Loading({
                    text: '准备打印...',
                    customClass: 'print-loading-wrapper',
                });

                try {
                    // 治疗单
                    if (printType === this._printOptions.TREATMENT_EXECUTE.label) {
                        const { data } = await PrintAPI.chargeExecutedPrint(this.$route.params.id, 5);
                        printData = data;
                        if (this.$abcSocialSecurity.config.isGuangdongShenzhen) {
                            printData.isGuangdongShenzhen = true;
                        }
                    }
                    // 输注单
                    if (printType === this._printOptions.INFUSION_EXECUTE.label) {
                        const { data } = await PrintAPI.chargeExecutedPrint(this.$route.params.id, 1);
                        printData = data;
                    }
                    // 样本条码
                    if (printType === this._printOptions.EXAMINATION_CODE.label) {
                        const { data } = await PrintAPI.printExaminationBarCode(this.$route.params.id);
                        printData = {
                            rows: data.rows || [],
                        };
                        isPrintQrcode = false;
                    }
                    // 检验单
                    if (printType === this._printOptions.EXAMINATION.label) {
                        const { data } = await PrintAPI.chargeExaminationPrint(this.$route.params.id, 1);
                        printData = data;
                    }
                    // 检查单
                    if (printType === this._printOptions.INSPECT.label) {
                        const { data } = await PrintAPI.chargeExaminationPrint(this.$route.params.id, 2);
                        printData = {
                            ...data, title: '检验单',
                        };
                    }
                    // 检验申请单
                    if (printType === this._printOptions.EXAMINATION_APPLY_SHEET.label) {
                        const { data } = await PrintAPI.printExamApplySheet(this.patientOrderId, {
                            type: 1,
                        });
                        data.rows = data.rows || [];
                        printData = {
                            rows: data.rows.filter((r) => !!r.businessFormItems?.length),
                        };
                    }
                    // 检查申请单
                    if (printType === this._printOptions.INSPECT_APPLY_SHEET.label) {
                        const { data } = await PrintAPI.printExamApplySheet(this.patientOrderId, {
                            type: 2,
                        });
                        data.rows = data.rows || [];
                        printData = {
                            rows: data.rows.filter((r) => !!r.businessFormItems?.length),
                        };
                    }
                    if (printData && isPrintQrcode) {
                        // 二维码
                        printData.qrCode = await this.fetchQrCode();
                    }
                    return printData;
                } catch (e) {
                    console.error(e);
                } finally {
                    printLoading.close();
                }
            },

            // 打印
            async printHandler(selected) {

                if (selected.includes(this._printOptions.MEDICINE_TAG.label)) {
                    // this.showMedicineTagDialog = true;
                    const guid = createGUID();

                    Logger.reportAnalytics('medicine-tag-print', {
                        type: '用药标签打印',
                        from: '执行站',
                        info: '开始打印',
                        guid,
                    });

                    await this.printMedicineTagHandler(guid);
                } else if (selected.includes(this._printOptions.PATIENT_TAG.label)) {
                    this.printPatientTag();

                } else {
                    AbcPrinter.abcPrint(async () => {
                        const printPropsList = [];
                        for (let i = 0; i < selected.length; i++) {
                            let select = selected[i];
                            let printData = await this.fetchPrintData(select);
                            if (
                                [
                                    this._printOptions.EXAMINATION_APPLY_SHEET.label,
                                    this._printOptions.INSPECT_APPLY_SHEET.label,
                                ].includes(select) && !printData?.rows?.length
                            ) {
                                // 检查检验申请单不存在，转为打印检查检验单
                                const map = {
                                    [this._printOptions.EXAMINATION_APPLY_SHEET.label]: this._printOptions.EXAMINATION.label,
                                    [this._printOptions.INSPECT_APPLY_SHEET.label]: this._printOptions.INSPECT.label,
                                };

                                select = map[select];

                                printData = await this.fetchPrintData(select);
                            }
                            const printTaskOptions = getAbcPrintOptions(select, printData);
                            if (printTaskOptions) {
                                printPropsList.push(printTaskOptions);
                            }
                        }
                        return printPropsList;
                    });
                }
            },
            /**
             * @desc 选择对应的用药标签打印
             * <AUTHOR>
             * @date 2021-08-25 14:27:24
             * @params
             * @return
             */
            selectPrintConfirm(params) {
                const { selectedData } = params;
                const {
                    doctorName, patientOrderNo,
                } = this.postData;
                const patient = clone(this.patient);
                const printOptions = getAbcPrintOptions('用药标签', {
                    forms: selectedData.w.concat(selectedData.c),
                    patient,
                    doctorName,
                    patientOrderNo,
                    clinicName: this.currentClinic.clinicName,
                    // 手动打印,不做自动打印筛选
                    isManualPreview: true,
                });
                AbcPrinter.abcPrint(printOptions);
                this.showSelectPrint = false;
            },
            /**
             * 打印用药标签
             */
            async printMedicineTagHandler(guid) {
                try {
                    this.loading = true;
                    const chargeSheetId = this.$route.params.id;

                    Logger.reportAnalytics('medicine-tag-print', {
                        type: '用药标签打印',
                        from: '执行站',
                        guid,
                        info: '请求参数',
                        chargeSheetId,
                    });

                    const { data } = await TreatmentApi.fetchDetail(chargeSheetId, true);

                    Logger.reportAnalytics('medicine-tag-print', {
                        type: '用药标签打印',
                        from: '执行站',
                        info: '请求返回值',
                        guid,
                        data: clone(data),
                    });

                    let prescriptionWesternForms = data.chargeForms.filter((form) => {
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN;
                    });
                    let prescriptionInfusionForms = data.chargeForms.filter((form) => {
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION;
                    });
                    prescriptionWesternForms = this.checkInfusionFormsStatus(prescriptionWesternForms);
                    prescriptionInfusionForms = this.checkInfusionFormsStatus(prescriptionInfusionForms);
                    const prescriptionChineseForms = data.chargeForms.filter((form) => {
                        if (form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY) {
                            form.chargeFormItems = form.chargeFormItems.filter((item) => {
                                return item.productType !== GoodsTypeEnum.EXPRESS_DELIVERY &&
                                    item.productType !== GoodsTypeEnum.DECOCTION &&
                                    item.productType !== GoodsTypeEnum.INGREDIENT;
                            });
                        }
                        return form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE ||
                            form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY;
                    });

                    const wsForms = (prescriptionWesternForms || []).map((form) => {
                        form.isFullChecked = true;
                        form.formItems = form.chargeFormItems.map((formItem) => {
                            formItem.checked = true;
                            return formItem;
                        });
                        delete form.chargeFormItems;
                        return form;
                    });
                    const infusionForms = (prescriptionInfusionForms || []).map((form) => {
                        form.isFullChecked = true;
                        form.formItems = form.chargeFormItems.map((formItem) => {
                            formItem.checked = true;
                            return formItem;
                        });
                        delete form.chargeFormItems;
                        return form;
                    });
                    const chineseForms = (prescriptionChineseForms || []).map((form) => {
                        form.isFullChecked = true;
                        form.formItems = form.chargeFormItems.map((formItem) => {
                            formItem.checked = true;
                            return formItem;
                        });
                        delete form.chargeFormItems;
                        return form;
                    });

                    const onConfirm = (params) => {
                        Logger.reportAnalytics('medicine-tag-print', {
                            type: '用药标签打印',
                            from: '执行站',
                            info: '选择打印的药品',
                            guid,
                            params: clone(params),
                        });

                        const { selectedData } = params;
                        const {
                            doctorName, patientOrderNo, patient,
                            departmentName,
                        } = data;

                        Logger.reportAnalytics('medicine-tag-print', {
                            type: '用药标签打印',
                            from: '执行站',
                            info: '患者相关信息',
                            guid,
                            data: {
                                patient: clone(patient),
                                doctorName,
                                patientOrderNo,
                            },
                        });

                        const printOptions = getAbcPrintOptions('用药标签', {
                            forms: selectedData.w.concat(selectedData.c),
                            patient,
                            doctorName,
                            patientOrderNo,
                            clinicName: this.currentClinic.clinicName,
                            departmentName,
                            // 手动打印,不做自动打印筛选
                            isManualPreview: true,
                        });
                        AbcPrinter.abcPrint(printOptions);
                    };

                    const tagForms = wsForms.concat(infusionForms).concat(chineseForms);

                    Logger.reportAnalytics('medicine-tag-print', {
                        type: '用药标签打印',
                        from: '执行站',
                        info: '处理打印数据',
                        guid,
                        tagForms: clone(tagForms),
                    });

                    await new SelectPrintFunctionalDialog({
                        value: true, tagForms, onConfirm,
                    }).generateDialogAsync({ parent: this });
                } catch (e) {
                    console.error('打印用药标签失败', e);
                } finally {
                    this.loading = false;
                }
            },
            /**
             * @desc 打印患者标签
             * <AUTHOR>
             * @date 2020/2/18
             */
            async printPatientTag() {
                const { data } = await PrintAPI.patientTagPrint(this.patientOrderId);
                const printOptions = getAbcPrintOptions('患者标签', data);
                AbcPrinter.abcPrint(printOptions);
            },

            handleUpdatePatientInfo(patient) {
                this.patient = patient;
            },

            /**
             * @desc 获取执行记录历史
             */
            async fetchHistory(id) {
                this.loading = true;
                try {
                    const { data } = await TreatmentAPI.fetchActionsHistory(id);
                    this.actionsHistory = data;
                    // 患者上次上门的地点
                    this.preDoorAddress = data.executeRecords?.find((item) => !!item.homeCareAddress)?.homeCareAddress || '';
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },
            /**
             * @desc 过滤需要执行的form
             * <AUTHOR>
             * @date 2020-08-10 11:12:24
             */
            filterExecuteForms(productForms) {
                const product = clone(productForms);
                // 中西药处方个数
                const westernLength = product.filter((x) => x.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN).length;
                let westernIndex = 0;

                // 输注处方个数
                const infusionLength = product.filter((x) => x.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION).length;
                let infusionIndex = 0;

                const chargeFormItems = [];
                // 过滤需要执行的项目
                product.forEach((form) => {
                    // item 上需要知道来源
                    form.chargeFormItems.forEach((formItem) => {
                        formItem.sourceFormType = form.sourceFormType;
                    });
                    if (
                        form.sourceFormType === SourceFormTypeEnum.TREATMENT ||
                        form.sourceFormType === SourceFormTypeEnum.NURSING
                    ) {
                        // 兼容数据，之前治疗理疗都在sourceFormType = 3 里面，通过productType区分套餐和治疗理疗
                        form.chargeFormItems.forEach((formItem) => {
                            if (formItem.productType === GoodsTypeEnum.COMPOSE) {
                                const composeChildren = [];
                                formItem.composeChildren.forEach((composeItem) => {
                                    if (this.needExecutive(composeItem)) {
                                        composeChildren.push(composeItem);
                                    }
                                    this.calcExecutedUnitCount(composeItem);
                                });
                                if (composeChildren && composeChildren.length) {
                                    const chargeItem = clone(formItem);
                                    chargeItem.composeChildren = composeChildren;
                                    chargeFormItems.push({
                                        ...chargeItem, formStatus: form.status,
                                    });
                                }
                            } else {
                                if (this.needExecutive(formItem)) {
                                    if (formItem.composeChildren) delete formItem.composeChildren;
                                    chargeFormItems.push({
                                        ...formItem, formStatus: form.status,
                                    });
                                }
                                this.calcExecutedUnitCount(formItem);
                            }
                        });
                    } else if (form.sourceFormType === SourceFormTypeEnum.COMPOSE) {
                        // 套餐
                        form.chargeFormItems.forEach((formItem) => {
                            const composeChildren = [];
                            formItem.composeChildren.forEach((composeItem) => {
                                if (this.needExecutive(composeItem)) {
                                    composeChildren.push(composeItem);
                                }
                                this.calcExecutedUnitCount(composeItem);
                            });
                            if (composeChildren && composeChildren.length) {
                                const chargeItem = clone(formItem);
                                chargeItem.composeChildren = composeChildren;
                                chargeFormItems.push({
                                    ...chargeItem, formStatus: form.status,
                                });
                            }
                        });
                    } else if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN ||
                        form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION) {
                        // 中西药处方+输注处方
                        let name = '';
                        if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN) {
                            name = westernLength > 1 ? `中西成药处方${numToChinese(westernIndex + 1)}` : '中西成药处方';
                        } else {
                            name = infusionLength > 1 ? `输注处方${numToChinese(infusionIndex + 1)}` : '输注处方';
                        }

                        form.composeChildren = [];
                        form.name = name;
                        if (!form.isNeedPs) {
                            form.chargeFormItems.forEach((item) => {
                                if (this.needInflusionExecutive(item) && !(item.ast === AstEnum.PI_SHI && !item.astResult)) {
                                    item.unitCount = item.usageInfo?.executedTotalCount;
                                    item.unit = '次';
                                    form.composeChildren.push(item);
                                }
                                this.calcExecutedUnitCount(item);
                            });
                        }

                        if (form.composeChildren.length > 0) {
                            chargeFormItems.push({
                                ...form, formStatus: form.status,
                            });
                        }

                        if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN) {
                            westernIndex++;
                        } else {
                            infusionIndex++;
                        }
                    } else if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL) {
                        form.chargeFormItems.forEach((formItem) => {
                            if (this.needExecutive(formItem, true)) {
                                chargeFormItems.push({
                                    ...formItem, formStatus: form.status,
                                });
                            }
                        });

                    } else {
                        form.chargeFormItems.forEach((formItem) => {
                            // 满足条件的chargeFormItem 保留
                            if (this.needExecutive(formItem)) {
                                chargeFormItems.push({
                                    ...formItem, formStatus: form.status,
                                });
                            }
                            this.calcExecutedUnitCount(formItem);
                        });
                    }
                });
                return chargeFormItems;
            },

            /**
             * @desc 处方皮试状态及可执行状态
             * <AUTHOR>
             * @date 2022/04/14 15:33:49
             */
            checkInfusionFormsStatus(arr) {
                return arr.map((x) => {
                    x.isNeedPs = false;
                    x.hasExecuteItem = false;
                    for (const item of x.chargeFormItems) {
                        if (item.ast === AstEnum.PI_SHI && !item.astResult) {
                            x.isNeedPs = true;
                        }
                        if (this.needInflusionExecutive(item)) {
                            x.hasExecuteItem = true;
                        }
                    }
                    return x;
                });
            },

            // 执行单删除确认
            handleBeforeDelete() {
                this.$confirm({
                    type: 'warn',
                    title: '删除确认',
                    content: '删除后不能恢复，确定删除该执行单？',
                    closeAfterConfirm: false,
                    onConfirm: this.deleteTreatment,
                });
            },

            // 执行单删除
            async deleteTreatment(modelVm) {
                modelVm.confirmLoading = true;
                try {
                    await ChargeAPI.deleteChargeSheet(this.$route.params.id, true);
                    // 更新quicklist
                    await this.refreshTreatmentQuickList();
                    // 选中第一个
                    this.selectFirst();
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    modelVm.confirmLoading = false;
                    modelVm.close();
                } catch (err) {
                    modelVm.confirmLoading = false;
                    modelVm.close();
                    const { message } = err;
                    message && this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: message,
                        onClose: () => {
                            this.refreshTreatmentQuickList();
                            this.fetch();
                        },
                    });
                }
            },

            // 选中第一个 quickList item
            selectFirst() {
                let selectedItem;
                if (this.draftTreatmentNews.length) {
                    selectedItem = this.draftTreatmentNews[0];
                } else {
                    if (this.treatmentQuickList.length > 0) {
                        selectedItem = this.treatmentQuickList[0];
                    } else {
                        selectedItem = null;
                    }
                }
                console.log(selectedItem);
                this.select(selectedItem);
            },

            // 选中某一个
            select(selectedItem) {
                this.$store.dispatch('setSelectedItem', {
                    type: 'treatment',
                    selectedItem,
                });

                if (!selectedItem) {
                    this.replaceRouter('/treatment');
                    return false;
                }
                if (selectedItem.draftId) {
                    this.replaceRouter(`/treatment/add/${selectedItem.draftId}`);
                } else if (selectedItem.id) {
                    this.replaceRouter(`/treatment/${selectedItem.id}`);
                } else {
                    this.replaceRouter('/treatment');
                }
            },

            replaceRouter(path) {
                this.$router.replace({
                    path,
                });
            },

            // 修改执行单
            handleUpdateTreatmentForm() {
                this.disabledForm = false;
                this.postData.chargeForms.forEach((form) => {
                    form?.chargeFormItems.forEach((item) => {
                        this.$set(item, 'checked', true);
                    });
                });
            },

            // 保存
            async saveTreatmentFormSubmit() {
                try {
                    this.inputCalcLoading = true;

                    this.postData.chargeForms.forEach((form, PIndex) => {
                        form.sort = PIndex;
                        form.chargeFormItems.forEach((item, index) => {
                            item.sort = index;
                        });
                    });

                    await ChargeAPI.saveOrder({
                        id: this.$route.params.id,
                        ...this.postData,
                    }, true);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.fetch();
                    this.inputCalcLoading = false;
                    this.$store.dispatch('refreshTreatmentQuickList', false);
                } catch (err) {
                    this.inputCalcLoading = false;
                    const {
                        code, message,
                    } = err;
                    if (code === 17010) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    } else {
                        message && this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                            onClose: () => {
                                this.refreshTreatmentQuickList();
                                this.fetch();
                            },
                        });
                    }
                }
            },

            async getLockInfo() {
                try {
                    this.lockedInfo = null;
                    const data = await PatientOrderLockService.getLockInfo({
                        patientOrderId: this.patientOrderId,
                        businessKeys: [LockBusinessKeyEnum.CHARGE],
                    });
                    const {
                        result, identity, value,
                    } = data || {};
                    const { businessScene } = value || {};
                    if (businessScene !== ChargeBusinessSceneEnum.CHARGE_SHEET_REFUND) return;
                    if (result === 1 && identity) {
                        this.lockedInfo = data;
                    }
                } catch (err) {
                    Logger.error({
                        scene: 'getLockInfo treatment',
                        err,
                    });
                }
            },

            onLockSocket(socketData, type) {
                const {
                    key, businessKey, value,
                } = socketData || {};
                const { businessScene } = value || {};
                if (businessKey !== LockBusinessKeyEnum.CHARGE) return;
                if (businessScene !== ChargeBusinessSceneEnum.CHARGE_SHEET_REFUND) return;
                if (key === this.patientOrderId) {
                    if (type === 'lock') {
                        this.lockedInfo = socketData;
                    } else {
                        this.lockedInfo = null;
                        this.fetch();
                    }
                }
            },
        },
    };
</script>
