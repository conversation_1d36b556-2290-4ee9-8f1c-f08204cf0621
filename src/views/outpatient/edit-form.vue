<template>
    <div v-abc-loading:page="loading">
        <abc-container-center-top-head data-cy="outpatient-top-head">
            <div class="outpatient-form-tab-wrapper">
                <abc-tabs-v2
                    v-model="outpatientFormTab"
                    size="huge"
                    :option="outpatientFormTabOptions"
                    :disable-indicator="outpatientFormTabOptions.length < 2"
                    :border="false"
                    data-cy="outpatient-form-tab"
                    @change="changeOutpatientFormTab"
                ></abc-tabs-v2>
            </div>
            <air-pharmacy-tag v-if="postData.airPharmacyOrderId"></air-pharmacy-tag>
            <abc-text tag="div" size="mini" class="outpatient-record-count">
                {{ recordCount }}
            </abc-text>
            <reservation-popover
                v-if="referralSource && isRegistrationRefer"
                :key="patientOrderId"
                :registration-sheet-id="referralSource.registrationSheetId"
                from-source="outpatient"
                :is-fix-order-mode="isFixOrderMode"
                title="预约/挂号信息"
            >
                <abc-text slot="popoverReference">
                    {{ referralInfo }}
                </abc-text>
            </reservation-popover>

            <div v-if="!outpatientFormTab" class="buttons-wrapper">
                <template v-if="isOpenOnlineAudit && isDiagnosis && isOnline">
                    <template v-if="!currentAuditResult">
                        <abc-button :loading="auditPassLoading" @click="auditOnline(AuditResultEnum.PASS)">
                            审核通过
                        </abc-button>
                        <abc-button :loading="auditFailLoading" @click="auditOnline(AuditResultEnum.FAIL)">
                            审核不通过
                        </abc-button>
                    </template>
                    <template v-else>
                        <abc-tag-v2
                            v-if="currentAuditResult === AuditResultEnum.PASS"
                            theme="success"
                            size="huge"
                            style="margin-right: 4px;"
                        >
                            审核通过
                        </abc-tag-v2>
                        <abc-tag-v2
                            v-if="currentAuditResult === AuditResultEnum.FAIL"
                            theme="danger"
                            size="huge"
                            style="margin-right: 4px;"
                        >
                            审核不通过
                        </abc-tag-v2>
                    </template>
                </template>

                <patient-social
                    v-if="!!postData.shebaoCardInfo"
                    v-model="visibleSocialInfo"
                    :shebao-card-info="postData.shebaoCardInfo"
                    style="margin-right: 4px;"
                >
                    <social-btn
                        active
                        :shebao-card-info="postData.shebaoCardInfo"
                        @click="visibleSocialInfo = !visibleSocialInfo"
                    ></social-btn>
                </patient-social>

                <abc-button
                    :loading="refreshing"
                    :disabled="!canChargeReview || disableByLock || disableByChargeLock"
                    variant="ghost"
                    data-cy="outpatient-form-charge-review-btn"
                    @click="handleChargeReview"
                >
                    费用预览
                </abc-button>

                <template v-if="isDiagnosis && !isEditStatus">
                    <abc-button
                        :loading="refreshing"
                        data-cy="outpatient-form-edit-btn"
                        @click="handleUpdateOutpatient"
                    >
                        {{ !isContinueDiagnosed ? '修改' : '继续接诊' }}
                    </abc-button>
                </template>
                <template v-else>
                    <template v-if="status">
                        <abc-check-access>
                            <abc-button
                                :loading="finishLoading"
                                style="width: 86px; min-width: 74px;"
                                :disabled="disableByLock"
                                data-cy="outpatient-form-save-btn"
                                @click="save('patientForm')"
                            >
                                {{ !isContinueDiagnosed ? '保存' : '完成接诊' }}
                            </abc-button>
                        </abc-check-access>
                        <abc-button
                            variant="ghost"
                            :disabled="disabledForm"
                            data-cy="outpatient-form-cancel-btn"
                            @click="cancelUpdateDialog"
                        >
                            取消
                        </abc-button>
                    </template>
                    <template v-else>
                        <abc-button
                            v-if="permissionHasAssistant"
                            variant="ghost"
                            :disabled="disableByLock"
                            data-cy="outpatient-form-pre-confirm-btn"
                            @click="preDiagnosisConfirm"
                        >
                            提交预诊
                        </abc-button>

                        <abc-check-access>
                            <abc-button
                                :loading="finishLoading"
                                :disabled="disableByLock"
                                style="min-width: 86px;"
                                data-cy="outpatient-form-save-btn"
                                @click="save('patientForm')"
                            >
                                完成接诊
                            </abc-button>
                        </abc-check-access>
                        <abc-button
                            v-if="isDraft"
                            variant="ghost"
                            data-cy="outpatient-form-delete-btn"
                            @click="deleteDraft"
                        >
                            删除
                        </abc-button>
                        <abc-button
                            v-else
                            variant="ghost"
                            :disabled="disabledForm"
                            data-cy="outpatient-form-cancel-btn"
                            @click="cancelUpdateDialog"
                        >
                            取消
                        </abc-button>
                    </template>
                </template>

                <push-payment-popper
                    v-if="isShowPushPaymentBtn"
                    :source-type="2"
                    :patient="postData.patient"
                    :patient-order-id="patientOrderId"
                    style="margin-left: 4px;"
                    data-cy="outpatient-form-push-payment-btn"
                    @update-patient-info="handleUpdatePatientInfo"
                    @payment-success="fetchDetail"
                >
                </push-payment-popper>

                <print-popper
                    v-if="isDiagnosis"
                    size="small"
                    :width="64"
                    placement="bottom"
                    :box-style="{ width: '124px' }"
                    :options="printOptions"
                    style="margin-left: 4px;"
                    data-cy="outpatient-form-print-btn"
                    @print="printHandler"
                    @select-print-setting="openPrintConfigSettingDialog"
                >
                </print-popper>

                <abc-button
                    v-if="showDischargeBtn"
                    style="margin-left: 4px;"
                    variant="ghost"
                    data-cy="outpatient-form-charge-btn"
                    @click="handleClickDischarge"
                >
                    出院结算
                </abc-button>

                <more-button
                    v-if="showMoreButton"
                    :is-draft="isDraft"
                    :is-consultation="isConsultation"
                    :outpatient-status="status"
                    :post-data="postData"
                    data-cy="outpatient-form-more-btn"
                    :outpatient-sheet-id="outpatientSheetId"
                    @showReferralDialog="isShowReferralDialog = true"
                ></more-button>
            </div>
        </abc-container-center-top-head>

        <abc-container-center-main-content class="outpatient-main-content">
            <abc-tips-card-v2
                v-if="showLockTips && !outpatientFormTab"
                theme="warning"
                style="margin-bottom: 16px;"
                align="center"
                border-radius
            >
                {{ lockTips }}
            </abc-tips-card-v2>
            <chronic-care-record
                v-if="outpatientFormTab === 1"
                :patient="postData.patient"
                @change="changeChronicCareRecord"
            ></chronic-care-record>
            <abc-form v-else ref="patientForm" :key="`_postForm${ postData.id}`">
                <!--患者模块-->
                <patient-reg-form
                    ref="outpatient-form-wrapper-form"
                    :disabled-patient-detail="disabledPatientDetail"
                    :disabled="disabledForm || isHospitalDischarge"
                    :registration-fee-status="registrationFeeStatus"
                    :status="status"
                    :check-source="true"
                    :charge-status="chargeStatus"
                    :patient-guardian="patientGuardian"
                    :show-patient-guardian="isBeiJing && isConsultation"
                    :post-data="postData"
                    card-type="add"
                    :loading="loading"
                    :is-consultation="isConsultation"
                    :doctor-enable-categories="doctorEnableCategories"
                    :doctor-registration-fees="doctorRegistrationFees"
                    size="medium"
                    @refreshOutpatientDetail="refreshOutpatientDetail"
                    @change-patient-info="handleOutpatientInfoChange"
                    @updatePostDataCache="handleUpdatePatientInfo"
                    @change-patient="changePatientHandler"
                    @change-doctor="changeDoctorHandler"
                    @change-shebao-charge-type="onChangeShebaoChargeType"
                    @change-department-doctor-type="changeDepDoctor"
                ></patient-reg-form>

                <div v-if="hospitalInfo" class="outpatient-form-item">
                    <hospital-info-card
                        :hospital-info="hospitalInfo"
                        :disabled="disabledForm || isHospitalDischarge"
                        :disabled-change-type="status > 0"
                        :default-hospital-type="source === OutpatientSheetTypeEnum.REGISTRATION ? 0 : 1"
                        @change="changeHospital"
                    ></hospital-info-card>
                </div>

                <!--病例模块-->
                <medical-record-module
                    id="relatedProduct"
                    :key="`disableByLock${ disableByLock}`"
                    :post-data="postData"
                    :selected-patient="selectedPatient"
                    :switch-setting="switchSetting.medicalRecord"
                    :disabled="disabledMRForm"
                    :status="status"
                    :is-hospital-sheet="isHospitalSheet"
                    :disabled-diagnosis="false"
                    :product-forms="postData.productForms"
                    :is-support-medical-document="isSupportMedicalDocument"
                    :is-open-source="true"
                    :must-at-least-one-western-disease="mustAtLeastOneWesternDisease"
                    :sorted-mr-struct="sortedMrStruct"
                    @useTemplate="useMRTemplate"
                    @select-outpatient-record="copyMRHandler"
                    @delete-questionForm="fetchDetail"
                    @add-prescription="addCPrescription"
                ></medical-record-module>

                <!--诊疗项目-->
                <diagnosis-treatment
                    ref="diagnosisTreatmentRef"
                    :disabled="disabledForm || isHospitalDischarge"
                    :disabled-add="disableAddPrescription"
                    :treat-online-clinic-id="treatOnlineClinicId"
                    :post-data="postData"
                    :forms.sync="postData.productForms"
                    :prescription-forms="prescriptionForms"
                    :doctor-info="{
                        departmentId: postData.departmentId,
                        departmentName: postData.departmentName,
                        doctorId: postData.doctorId,
                        doctorName: postData.doctorName,
                    }"
                    :medical-record="postData.medicalRecord"
                    :patient-info="postData.patient"
                    :shebao-card-info="postData.shebaoCardInfo"
                    :switch-setting="switchSetting.diagnosisTreatment"
                    :is-open-source="true"
                    @outpatient-verify="handleOutpatientInfoChange"
                    @rest-product-change="handleRestProductChange"
                ></diagnosis-treatment>
                <prescription-group
                    ref="prescriptionGroup"
                    :key="`prescriptionGroup${ isEditStatus}`"
                    :post-data="postData"
                    :selected-patient="selectedPatient"
                    :verify-outpatient="verifyOutpatient"
                    :disabled="disabledForm || isHospitalDischarge"
                    :disabled-add="disableAddPrescription"
                    :treat-online-clinic-id="treatOnlineClinicId"
                    :need-check-stock="needCheckStock"
                    :is-open-source="true"
                    show-cooperation-pharmacy
                    :is-consultation="isConsultation"
                    :switch-setting="switchSetting.prescription"
                    @outpatient-verify="handleOutpatientInfoChange"
                    @select-outpatient-record="copyPRHandler"
                ></prescription-group>

                <div v-if="doctorAdviceInForm" class="content-wrapper prescription-wrapper">
                    <div id="doctor-advice" class="doctor-advice" :class="{ 'is-disabled': disabledForm || disableUpdateMedicalRecord }">
                        <label>医嘱事项</label>
                        <abc-form-item>
                            <doctor-advice
                                v-model="postData.medicalRecord.doctorAdvice"
                                :disabled="disabledForm || disableUpdateMedicalRecord"
                                :diagnosis="postData.medicalRecord.diagnosis"
                            ></doctor-advice>
                        </abc-form-item>
                    </div>
                </div>

                <template v-if="!loading && (hasMedicine || hasProduct) && !disabledForm">
                    <div class="tips-summary">
                        <!--西药药物相互作用提示-->
                        <outpatient-verify
                            v-if="hasMedicine"
                            :verify-outpatient="verifyOutpatient"
                            :rest-related-product-count="restRelatedProductCount"
                            :post-data="postData"
                            :doctor-name="postData.doctorName"
                            @scroll-to-diagnosis-treatment="handleGoDiagnosisTreatment"
                            @confirm-signature="handleConfirmSignature"
                            @confirm-set-jing-ma-du="handleSetJingMaDu"
                        ></outpatient-verify>
                        <medical-insurance-restriction
                            ref="medicalInsuranceRestriction"
                            :verify-lists="medicalInsuranceRestriction"
                            :behavior-verify-lists="behaviorRestriction"
                            :shebao-charge-type="postData.shebaoChargeType"
                            :business-id="patientOrderId"
                            :has-cdss="hasMedicine"
                            @change-shebao-pay-mode="onChangeShebaoPayMode"
                            @shebao-restrict-signature="onShebaoRestrictSignature"
                        ></medical-insurance-restriction>
                    </div>
                </template>

                <div class="outpatient-bottom-info">
                    <abc-text
                        v-if="postData.airPharmacyOrderId"
                        class="patient-order-number"
                        theme="gray"
                        style="margin-right: auto;"
                    >
                        订单号：{{ postData.airPharmacyOrderId }}
                    </abc-text>
                    <abc-text v-if="postData.patientOrderNo" theme="gray" class="patient-order-number">
                        诊号：{{ postData.patientOrderNo | formatPatientOrderNo }}
                    </abc-text>

                    <div
                        v-if="postData.reserveDate"
                        class="default-visit-time"
                        :class="{
                            'custom-date': !!postData.diagnosedDateStr
                        }"
                    >
                        <abc-text theme="gray" class="label">
                            就诊时间：
                        </abc-text>
                        <abc-text v-if="postData.diagnosedDate" theme="gray">
                            {{ postData.diagnosedDate | parseTime('y-m-d h:i:s', true) }}
                        </abc-text>
                        <abc-dropdown
                            v-else
                            :disabled="!canChangeDiagnosedDate"
                            placement="bottom-start"
                            style="width: auto;"
                            @change="handleVisitTimeChange"
                        >
                            <template #reference>
                                <abc-button
                                    variant="text"
                                    theme="default"
                                    size="small"
                                >
                                    {{ defaultDiagnosedDateStr }}
                                </abc-button>
                            </template>
                            <abc-dropdown-item :value="reserveDateStr">
                                <span style="display: inline-block; width: 70px;">预约时间：</span>
                                {{ reserveDateStr }}
                            </abc-dropdown-item>
                            <abc-dropdown-item :value="todayStr">
                                <span style="display: inline-block; width: 70px;">今天：</span>
                                {{ todayStr }}
                            </abc-dropdown-item>
                        </abc-dropdown>
                    </div>
                </div>

                <!--hack-->
                <div style="width: 100%; height: 1px;"></div>
            </abc-form>
        </abc-container-center-main-content>

        <!--右侧边栏-->
        <outpatient-sidebar
            v-if="!outpatientFormTab"
            :key="sideBarKey"
            :disabled="disabledForm"
            :medical-record="postData.medicalRecord"
            :is-consultation="isConsultation"
            :consultation-id="consultationId"
            :patient-info="selectedPatient || postData.patient"
            :switch-setting="switchSetting.medicalRecord"
            :outpatient-status="outpatientStatus"
            :diagnosed-date="postData.diagnosedDate"
            :is-draft="isDraft"
            :exam-report-finished-ids="examReportFinishedIds"
            @copy="copyHandler"
            @add2MedicalRecordHandle="add2MedicalRecordHandle"
            @addCPrescription="addCPrescription"
        >
        </outpatient-sidebar>

        <img
            v-if="doctorSignImgUrl"
            :src="doctorSignImgUrl"
            style="display: none;"
            alt=""
        />

        <charge-review-dialog
            v-if="showReviewDialog"
            v-model="showReviewDialog"
            :is-consultation="isConsultation"
            :treat-online-clinic-id="treatOnlineClinicId"
            :registration-fee-status="registrationFeeStatus"
            :post-data="postData"
            :hospital-info="hospitalInfo"
            :is-charge-locked="isChargeLocked"
            @confirm="chargeReviewConfirmHandle"
        ></charge-review-dialog>

        <!--发起转诊-->
        <referral-dialog
            v-if="isShowReferralDialog"
            v-model="isShowReferralDialog"
            :post-data="postData"
        ></referral-dialog>

        <!-- 监护人记录弹框 -->
        <guardian-record-dialog
            v-if="showGuardianRecordDialog"
            v-model="showGuardianRecordDialog"
            @patient-guardian-change="handlePatientGuardianChange"
        ></guardian-record-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import outpatientCommon from './common';
    import OutpatientSidebar from './sidebar/index.vue';
    import PushPaymentPopper from 'views/common/components/push-payment.vue';
    import ReferralDialog from 'views/outpatient/common/referral-dialog';
    import AirPharmacyTag from 'src/views/layout/air-pharmacy-tag/index.vue';

    // API
    import OutpatientAPI from 'api/outpatient';
    import CAAPI from 'api/ca-signature';
    import SocialApi from 'api/social';

    // utils
    import {
        isEqual, debounce,
    } from 'utils/lodash';
    import clone from 'utils/clone';
    import {
        validateMobile, validateAge,
    } from 'utils/validate';
    import { parseTime } from 'utils/index';
    import {
        mapGetters, mapActions, mapState,
    } from 'vuex';
    import PrintPopper from 'views/print/popper';
    import Printer from 'views/print';
    import ConfirmTreatmentDialog from './common/confirm-treatment-dialog';
    import { WxBindStatusEnum } from '@abc/constants';
    import { ChargeStatusEnum } from '@/service/charge/constants';
    import Logger from 'utils/logger';

    // constants
    import {
        createPostData,
        OutpatientStatusEnum,
    } from './constants';
    import { OutpatientSheetTypeEnum } from './constants.js';

    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import AbcSocket from 'views/common/single-socket.js';
    import AbcAccess from '@/access/utils.js';
    import reservationPopover from 'views/treatment/common/reservation-popover/index';
    import { RESERVATION_MODE_TYPE } from 'views/settings/registered-reservation/constant';
    import SocialHistoryPrescription from 'views/layout/social-history-prescription';
    import CrmAPI from 'api/crm';
    import { DEFAULT_CERT_TYPE } from 'views/crm/constants';
    import { encryptMobile } from 'utils/crm';

    const CA_CERT = {
        doctorId: '',
        status: '', // 状态 0-正常 1-注销
        cardNumber: '', // 身份证号
        caBeginDate: '', // 证书申请日期
        caEndDate: '', // 证书有效期至
        validDays: 0, // 剩余有效天数
    };

    export default {
        name: 'OutpatientEditForm',
        components: {
            PrintPopper,
            PushPaymentPopper,
            OutpatientSidebar,
            ReferralDialog,
            reservationPopover,
            AirPharmacyTag,
        },
        mixins: [
            outpatientCommon,
        ],
        provide() {
            return {
                outpatientEditForm: this,
            };
        },
        data() {
            return {
                accessMap: AbcAccess.accessMap,
                OutpatientSheetTypeEnum,
                isEditStatus: false,
                isFromAdd: false,
                saveLoading: false,
                refreshing: false,
                isUpdate: false,
                isDraft: 0,
                isEquals: [],
                status: 0,
                source: 0,
                isOnline: 0,
                chargeStatus: 0,
                registrationFeeStatus: 0,

                validateMobile,
                validateAge,
                doctorSignImgUrl: '',
                showElectronPrintPreview: false,
                printType: 'advise',
                printable: {},
                executedType: 1,
                patientOrderId: '',

                treatOnlineClinicId: '', // 用于搜索库存信息
                outpatientSheetId: '', // 用于打印

                restRelatedProductCount: 0,

                isShowReferralDialog: false,
                referralSource: null, // 转诊信息
                isReserved: 0, // 挂号: 0 , 预约: 1

                caCert: CA_CERT,
                isDefaultSelectCaProtocol: false, // 是否默认勾选使用CA电子签名开具处方

            };
        },
        computed: {
            ...mapGetters([
                'prescriptionTemplates', // 常用处方列表
                'draftOutpatients',
                'westernMedicineConfig',
                'outpatient',
                'chargeConfig', // 收费设置
                'isOpenMp',
                'weChatPayConfig',
                'goodsPriceInOutpatient',
                'currentClinic',
                'needCheckStock',
                'currentRegion',
                'isOpenScanQrCodeRegister',//是否开启了流行病学自助登记
                'scanQrCodePatientList',//流行病学已自助登记人员历史列表
                'userInfo',
                'registrationsConfig',
                'isEnableRegUpgrade',
                'lockInventoryConfigs',// 锁库配置
                'clinicBasic',
                'isCanSeePatientMobileInCrm',
            ]),
            ...mapState('socialPc', [
                'basicInfo',
            ]),
            ...mapGetters('crm', ['crmAccessVisibility']),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
                'featureOralProcess',
            ]),

            // 是否显示CA电子签名
            isShowCaSignature() {
                return this.clinicBasic.isEnableCA;
            },

            showMoreButton() {
                // 口腔草稿也要展示义齿加工入口
                if (this.isDraft) {
                    return this.featureOralProcess;
                }

                return (this.status === OutpatientStatusEnum.UN_DIAGNOSIS || this.status === OutpatientStatusEnum.DIAGNOSIS);
            },

            // 获取门诊单上所有的输注处方和中西成药处方
            prescriptionForms() {
                // 相同组只加一次
                const {
                    prescriptionInfusionForms = [], prescriptionWesternForms = [],
                } = this.postData;
                const arr = [];
                prescriptionInfusionForms && prescriptionInfusionForms.forEach((item) => {
                    item && item.prescriptionFormItems.forEach((prescriptionFormItem) => {
                        arr.push({
                            ...prescriptionFormItem,
                            keyId: item.keyId,
                        });
                    });
                });
                prescriptionWesternForms && prescriptionWesternForms.forEach((item) => {
                    item && item.prescriptionFormItems.forEach((prescriptionFormItem) => {

                        arr.push({
                            ...prescriptionFormItem,
                            keyId: item.keyId,
                        });
                    });
                });
                return arr;

            },
            // 门诊单未被收费
            outpatientSheetIsUnCharge() {
                return this.chargeStatus === ChargeStatusEnum.UN_CHARGE;
            },

            chainId() {
                return this.currentClinic && this.currentClinic.chainId;
            },

            /**
            * @desc 门诊允许医生查看总价(管理中设置)
             * <AUTHOR>
             * @date 2021/04/08 10:28:24
             */
            outpatientShowTotalPrice() {
                return this.goodsPriceInOutpatient === 0 || this.goodsPriceInOutpatient === 1;
            },
            autoSendOrderInfoSwitch() {
                return this.weChatPayConfig.weChatPaySwitch === 2 &&
                    this.weChatPayConfig.jsapiPayStatus === 2 &&
                    this.isOpenMp &&
                    this.chargeConfig.autoSendOrderInfoSwitch;
            },
            /**
             * @desc 判断是否是网诊, 当在网诊tab，拉的quicklist列表都是网诊
             * <AUTHOR>
             * @date 2020/02/16 20:30:03
             */
            isConsultation() {
                return !!this.isOnline;
            },

            /**
             * desc [当在拉取网诊列表时，url上的咨询id都会变化，因此在拉取之后设置为咨询id]
             */
            consultationId() {
                return this.postData.consultationSheetId || '';
            },

            disabledPatientDetail() {
                // 不能修改患者的情况： 挂号过来的 || 已诊的 || 已收费的 || 网诊
                return this.source === OutpatientSheetTypeEnum.REGISTRATION ||
                    this.isDiagnosis ||
                    this.chargeStatus > 0 ||
                    this.isConsultation;
            },

            // 已诊
            isDiagnosis() {
                return this.status === OutpatientStatusEnum.DIAGNOSIS;
            },

            // 不能修改门诊内容
            disabledForm() {
                return this.isDiagnosis && !this.isEditStatus || this.disableByLock || this.disableByChargeLock;
            },


            outpatientStatus() {
                return this.outpatient.selectedItem && this.outpatient.selectedItem.status || 0;
            },

            updatedTips() {
                const typeList = [];
                if (this.isEquals.indexOf('patient') > -1) {
                    typeList.push('资料');
                }
                if (this.isEquals.indexOf('medicalRecord') > -1) {
                    typeList.push('病历');
                }
                this.isEquals.forEach((it) => {
                    if (['productForms',
                         'prescriptionChineseForms',
                         'prescriptionWesternForms',
                         'prescriptionInfusionForms',
                         'prescriptionExternalForms'].indexOf(it) > -1) {
                             typeList.push('处方');
                         }
                });
                const typeSet = new Set(typeList);
                return [...typeSet].join('/');
            },
            electronPrintData() {
                const _printData = this.printData;
                _printData.printType = this.printType;
                return _printData;
            },

            /**
             * @desc 锁单提示，已诊点修改才显示
             * <AUTHOR>
             * @date 2022/08/04 11:00:50
             */
            showLockTips() {
                return (this.disableByLock || this.disableByChargeLock) && (this.isEditStatus || this.status === OutpatientStatusEnum.UN_DIAGNOSIS);
            },
            /**
             * @desc 转诊信息
             * <AUTHOR>
             * @date 2023/01/10 15:20:10
             */
            referralInfo() {
                return `[${this.isReserved === 0 ? '挂号' : '预约'}: 转诊自${this.referralSource?.doctorName ?? '不指定医生'}]`;
            },
            isFixOrderMode() {
                if (this.isEnableRegUpgrade || this.viewDistributeConfig.Settings.reservation.fetchReservationNotFromAPI) {
                    // modeType 0: 固定号源模式 1: 灵活时间模式
                    return this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
                }
                return true;
            },
            lockFlag() {
                // 获取门诊锁库配置，0不锁，10开单锁，20 收费锁
                return this.lockInventoryConfigs?.find((config) => config.sceneType === 0)?.lockFlag;
            },
            // 签名生效中
            isValidCaSignature() {
                return this.caCert.status === '0' && this.caCert.validDays > 0;
            },
            isSupportMedicalDocument() {
                return this.viewDistributeConfig.Outpatient.isSupportMedicalDocument;
            },
            // 是否符合显示医保历史处方
            isShowHistoryPrescriptionTool() {
                return this.$abcSocialSecurity.isOpenSocial &&
                    this.$abcSocialSecurity.isElectron &&
                    this.$abcSocialSecurity.config.isLiaoningShenyang &&
                    this.basicInfo.isOpenHistoryPrescription &&
                    this.status === OutpatientStatusEnum.UN_DIAGNOSIS &&
                    !!this.patientOrderId;
            },

        },
        watch: {
            // 如果路由有变化，会再次执行该方法
            '$route.path': {
                async handler() {
                    this.outpatientFormTab = 0;
                    this.clearWatchDraftTimer();
                    // 解锁
                    this.sendChargeUnlock();
                    await this.sendOutpatientUnlock();
                    this.initDetailHandle();
                },
            },
        },

        created() {
            this._prescriptionStatusStr = `${this.currentClinic.userId}_prescription_status`;
            this.initDetailHandle();
            this.parseTime = parseTime;
            this._compareUpdate = debounce(this.compareUpdate, 250, true);
            this._calcFee = debounce(this.refreshFee, 200, true);

            this._sendChargeLock = debounce(this.sendChargeLock, 1000);
            this._sendOutpatientLock = debounce(this.sendOutpatientLock, 1000);
            // 处方费用变化
            this.$abcEventBus.$on('outpatient-fee-change', (data) => {
                this._sendChargeLock();
                const { needCalcFee } = data;
                needCalcFee && this._calcFee();
            }, this);
            // 收费单解锁
            this.$abcEventBus.$on('outpatient-fee-change-unlock', () => {
                this.sendChargeUnlock();
            }, this);

            // socket监听
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;

            this._handleSocketLock = (data) => {
                this.onOutpatientLockSocket(data, 'lock');
            };
            this._handleSocketUnlock = (data) => {
                this.onOutpatientLockSocket(data, 'unlock');
            };
            this._socket.on('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.on('patientOrder.sheet_unlock', this._handleSocketUnlock);

            // 监听检查单状态
            this._handleSocketExamToOutpatient = (data) => {
                const { patientOrderId } = this.postData;
                const {
                    patientOrderId: patientOrderIdFromData, examinationSheetId,
                } = data[0];
                if (patientOrderId === patientOrderIdFromData) {
                    const index = this.postData?.medicalRecord?.examItems
                        .findIndex((x) => x.examinationSheetId === examinationSheetId);
                    if (index > -1) {
                        this.postData?.medicalRecord?.examItems.splice(index, 1, data[0]);
                    } else {
                        this.postData?.medicalRecord?.examItems.push(data[0]);
                    }
                    // 重新给_postDataCache赋值，防止点修改，状态未更新
                    this._postDataCache.medicalRecord.examItems = this.postData.medicalRecord.examItems;
                }
            };
            this._socket.on('examination.sheet.to_outpatient', this._handleSocketExamToOutpatient);

            // 监听病历设置开关的主动变化
            this.$abcEventBus.$on('medical-record-struct-change', (data) => {
                this.postData.medicalRecord.type = data.type || 0;
                this.initMedicalRecordStruct(true);
            }, this);

            this.$store.dispatch('fetchEmployeeListByPractice');
            this.$store.dispatch('fetchAntimicrobialDrugManagementData');
        },

        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
            this.sendChargeUnlock();
            this.sendOutpatientUnlock();
            this._socket.off('examination.sheet.to_outpatient', this._handleSocketExamToOutpatient);
            this._socket.off('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.off('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },

        methods: {
            async initDetailHandle() {
                await this.fetchDetail();
                this.$emit('change-doctor', {
                    doctorId: this.postData.doctorId,
                    doctorName: this.postData.doctorName,
                });
                if (this.isShowHistoryPrescriptionTool) {
                    new SocialHistoryPrescription({
                        store: this.$store,
                        value: true,
                        sheetId: this.$route.params.id,
                    }).generateDialog({ parent: this });
                }
                // 初始化门诊单后调用一次ai推荐
                if (this.isEnableAiSearch) {
                    await this.$nextTick();
                    this.$abcEventBus.$emit('get-ai-examination-suggestion');
                }
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'outpatient' }).generateDialogAsync({ parent: this });
            },
            handleGoDiagnosisTreatment() {
                if (!this.$refs.diagnosisTreatmentRef) return;
                this.$nextTick(() => {
                    const el = document.querySelector('#relatedProduct');
                    if (el) {
                        el.scrollIntoView({
                            block: 'nearest', behavior: 'smooth' , inline: 'nearest',
                        });
                    }
                });
                this._timer = setTimeout(() => {
                    this.$refs.diagnosisTreatmentRef.showRelatedProduct = true;
                }, 500);

            },
            ...mapActions([
                'refreshOutpatientQuickList',
            ]),

            ...mapActions('call', [
                'delCurrentCalling',
            ]),
            handleRestProductChange(count) {
                this.restRelatedProductCount = count;
            },

            chargeReviewConfirmHandle() {
                this.postData.prescriptionChineseForms.forEach((form) => {
                    if (!form.prescriptionFormItems.find((it) => !it.goodsId && !it.name) && !form.chargeStatus) {
                        form.prescriptionFormItems.push({
                            unit: 'g',
                            goodsId: null,
                            unitCount: '',
                            medicineCadn: '',
                            name: '',
                            specialRequirement: '',
                        });
                    }
                });
                this.outpatientTotalPrice = this.postData.totalPrice;
                if (this._confirmTreatment) {
                    this._confirmTreatment.outpatientTotalPrice = this.outpatientTotalPrice;
                }

                // 已诊 费用预览 确定，直接点开 修改按钮
                if (this.isDiagnosis) {
                    this.updateOutpatient();
                }
                this.handleOutpatientInfoChange();
            },

            compareUpdate() {
                if (this.postData.id !== this.$route.params.id) return;

                this.isEquals = this.getDiffCompareKeys();
                this.isUpdate = !!this.isEquals.length;
                if (!this.loading && this.postData.status === 0) {
                    if (this.isUpdate) {
                        this.$store.dispatch('SetDraft', {
                            key: 'outpatients',
                            record: clone(Object.assign(this.postData, { draftId: this.postData.id })),
                        });
                    }
                }
            },

            cancelUpdateDialog() {
                if (this.isUpdate || (this.status === -2)) {
                    // 点击取消后弹出框
                    const _this = this;
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: `患者${this.updatedTips}已发生修改，是否取消修改？`,
                        onConfirm() {
                            _this.cancelAdd();
                        },
                    });
                } else {
                    this.cancelAdd();
                }
            },

            async lockStockRefresh() {
                // 开启锁库，刷新数据
                if (this.lockFlag) {
                    try {
                        this.refreshing = true;
                        await this.fetchDetail(false, false);
                    } catch (e) {
                        console.error('开启锁库，刷新数据失败', e);
                    } finally {
                        this.refreshing = false;
                    }
                }
            },
            async updateOutpatient(refresh = false) {
                /**
                 * @desc 编辑门诊单之前先拉一遍锁状态，已经上锁提示用户，未上锁则加锁
                 * <AUTHOR>
                 * @date 2022/08/02 11:02:58
                 */
                try {
                    if (refresh) {
                        await this.fetchDetail();
                    } else {
                        await this.getOutpatientLockInfo();
                    }
                    if (!this.lockIdentity) {
                        this._sendOutpatientLock();
                    }
                } catch (e) {
                    console.error(e);
                }

                this.isEditStatus = true;

                this.updateExternalPR(this._postDataCache);
                this.updateExternalPR(this.postData);
                this.updateChinesePR();
                this.handleOutpatientInfoChange(true);
            },
            // 点击修改按钮
            async handleUpdateOutpatient() {
                if (this.isCheckSameDatePaySocial) {
                    this.updateOutpatientPreCheck(async () => {
                        await this.updateOutpatient(true);
                    });
                    return;
                }
                await this.updateOutpatient(true);
            },
            // 点击费用预览按钮
            async handleChargeReview() {
                // 不是编辑状态，且是已诊状态，才刷新门诊单数据
                if (!this.isEditStatus && this.isDiagnosis) {
                    await this.lockStockRefresh();
                }
                this.reviewCharge('patientForm');
            },
            clearDraft() {
                this.$store.dispatch('ClearDraft', {
                    key: 'outpatients', draftId: this.postData.id,
                });
            },

            /**
             * 点击取消按钮
             */
            async cancelAdd() {
                this.isEquals = [];
                this.clearDraft();
                this.sendChargeUnlock();
                this.sendOutpatientUnlock();
                this.fetchDetail();
            },

            resetDataHandler() {
                // 切换后重置
                this.verifyOutpatient = null;
                this.hospitalInfo = null;
                // 每次获取详情需要把 treatOnlineClinicId 清空
                this.treatOnlineClinicId = '';
                this.finishLoading = false;
            },

            async fetchDetail(needClearDraft = false, needLoading = true) {
                this.clearChargeLockTimeOut();
                this.clearOutpatientLockTimeOut();
                this.clearWatchDraftTimer();
                this.clearEquals();
                if (needLoading) {
                    this.loading = true;
                }

                this.resetDataHandler();

                const { data } = await OutpatientAPI.fetch(this.$route.params.id);
                if (data.id !== this.$route.params.id) return;

                this.compatibleHandler(data);
                if (data.consultationSheet) {
                    data.consultationSheetId = data.consultationSheet.id;
                    data.fee = data.consultationSheet.fee;
                    this.$store.commit('read_consultations_item_by_id', data.id);
                    this.treatOnlineClinicId = data.consultationSheet.clinicId;
                }

                this.patientGuardian = data.patientGuardian;
                this.isOnline = data.isOnline;
                this.diagnoseCount = data.diagnoseCount;
                this.outpatientSheetId = data.id;
                this.registrationFeeStatus = data.registrationFeeStatus;
                this.printable = data.printable;
                this.referralSource = data.referralSource;
                this.isReserved = data.isReserved;
                this._postDataCache = createPostData();
                this._postDataCache.revisitStatus = data.revisitStatus || 1;

                /**
                 * @desc needClearDraft 的情况下 需要清除草稿；
                 * @desc 待诊并且不是服务端草稿，不需要清除草稿; 此时需要更新草稿中的 dataSignature
                 * <AUTHOR> Yang
                 * @date 2020-08-21 16:17:39
                 */
                let updateDataSignature = false;
                if (needClearDraft) {
                    if (data.status === 0 && !data.isDraft) {
                        updateDataSignature = true;
                    } else {
                        this.clearDraft();
                    }
                }

                if (data.status === 0) {
                    await this.unDiagnosisHandler(data, updateDataSignature);
                } else {
                    await this.diagnosedHandler(data);
                }
                // 状态最后给保证页面切换不闪动
                this.chargeStatus = data.chargeStatus;
                this.status = data.status;
                this.subStatus = data.subStatus;
                this.source = data.source;
                this.isDraft = data.isDraft;
                this.patientOrderId = data.patientOrderId;
                this.isEditStatus = false;
                this._onDetailLoaded && this._onDetailLoaded();

                this.loading = false;

                this.getAuditResult();

                this.fetchShebaoRestrict();

                await Promise.all([
                    this.initMRHandler(),
                    this.fetchOutpatientVerify(),
                    this.initEpidemiologicalHistoryHandler(),
                ]);

                this._watchDraftTimer = setTimeout(() => {
                    this.registerWatchDraft();
                }, 500);

                /**
                 * @desc 获取业务锁
                 * <AUTHOR>
                 * @date 2022/08/02 10:41:07
                 */
                this.watchIsUpdateOnce();

                await this.fetchDoctorCaInfo();
                // 如果CA电子签名处于有效期内,再去请求相关数据
                if (this.isValidCaSignature) {
                    this.fetchIsUseCaSignature();
                }
                await this.getOutpatientLockInfo();

                this.$abcEventBus.$emit('outpatient-detail-loaded');
            },

            /**
             * 获取医生CA电子签名信息
             */
            async fetchDoctorCaInfo() {
                if (this.isConsultation && this.isShowCaSignature) {
                    try {
                        const cacheCaCert = await CAAPI.doctorCaInfo({
                            doctorId: this.postData.doctorId, isValid: true,
                        });
                        if (cacheCaCert.data?.body) {
                            this.caCert = clone(cacheCaCert.data.body);
                        } else {
                            this.caCert = CA_CERT;
                        }
                    } catch (e) {
                        console.error(e);
                        this.caCert = CA_CERT;
                    }
                }
            },

            async fetchIsUseCaSignature() {
                try {
                    const isUseCaSignatureResp = await CAAPI.isUseCaSignature({ doctorId: this.postData.doctorId });
                    this.isDefaultSelectCaProtocol = Boolean(isUseCaSignatureResp.data?.prescriptionUseCa);
                } catch (e) {
                    console.error(e);
                    this.isDefaultSelectCaProtocol = false;
                }
            },

            /**
             * @desc 注册草稿监听
             * <AUTHOR>
             * @date 2022-08-03 08:53:59
             */
            registerWatchDraft() {
                if (this.disabledMRForm) return;
                this._postDataCache = clone(this.postData);
                this._postDataUnWatch = this.$watch('postData', () => {
                    this._compareUpdate();
                }, {
                    deep: true,
                });
            },


            async doctorSignInHandler() {
                const { data } = await SocialApi.checkDoctorSignInfo(this.postData.doctorId);
                const {
                    validate,//是否签到 true-已签到
                    reminded,//是否提醒 true-已提醒
                } = data || {};
                if (!validate && !reminded) {
                    // 没有签到 && 没有提醒  时，去提醒
                    return new Promise((resolve) => {
                        this.$message({
                            type: 'warn',
                            title: '未完成签到',
                            dialogContentStyles: 'min-width: 240px; width: 240px',
                            content: ['当前时段暂无有效签到，本次就诊使用医保结算可能导致无法报销；若不使用医保结算请忽视'],
                            onConfirm: async () => {
                                await SocialApi.markDoctorSignIn(this.postData.doctorId);
                                resolve();
                            },
                            showCancel: false,
                            showClose: false,
                            confirmText: '确定',
                        });
                    });
                }
            },

            async save(patientForm) {
                const validateFlag = this.$refs?.['outpatient-form-wrapper-form']?.$refs?.['patient-section']?.validatePatientInfo();
                if (validateFlag) {
                    this.$refs?.['outpatient-form-wrapper-form']?.$refs?.['patient-section']?.openPatientCard();
                    return;
                }
                this.$refs[patientForm].validate(async (valid) => {
                    if (valid) {
                        this.finishLoading = true;

                        // 提交前预检测抗菌用药是否符合规范
                        const isAllowed = this.preCheckAntimicrobial(this.postData);
                        if (!isAllowed) {
                            this.finishLoading = false;
                            return false;
                        }

                        await this.validateSocialCode(clone(this.postData.medicalRecord));
                        // 只有杭州才进行签到确认
                        if (this.$abcSocialSecurity.config.isZhejiangHangzhou) {
                            await this.doctorSignInHandler();
                        }
                        const flag = await this.preSaveHandle(this.postData, async () => {
                            await this.refreshFee();
                            this.generateConfirmTreatment();
                            this.finishLoading = false;
                        });
                        if (flag) {
                            this.finishLoading = false;
                            return false;
                        }

                        this.finishLoading = true;
                        await this.refreshFee();
                        this.generateConfirmTreatment();
                        this.finishLoading = false;
                    }
                    console.log('error submit!!');
                    this.finishLoading = false;
                    return false;
                });
            },

            generateConfirmTreatment() {
                this._confirmTreatment = new ConfirmTreatmentDialog({
                    title: this.status ? '修改门诊单' : '完成就诊',
                    canChargeReview: this.canChargeReview && !this.disableByLock && !this.disableByChargeLock,
                    chargeStatus: this.chargeStatus,
                    outpatientShowTotalPrice: this.outpatientShowTotalPrice,
                    outpatientTotalPrice: this.outpatientTotalPrice,
                    patient: this.postData.patient,
                    medicalRecord: this.postData.medicalRecord,
                    autoSendOrderInfoSwitch: this.autoSendOrderInfoSwitch,
                    isConsultation: this.isConsultation,
                    updatePatientInfo: this.updatePatient,
                    submit: this.submit,
                    chainId: this.chainId,
                    reviewCharge: () => {
                        this.showReviewDialog = true;
                    },
                    shebaoChargeTypeDesc: this.shebaoChargeTypeDesc,
                    doctorId: this.postData.doctorId,
                    patientOrderId: this.postData.patientOrderId,
                    isShowCaSignature: this.isShowCaSignature,
                    isDefaultSelectCaProtocol: this.isDefaultSelectCaProtocol,
                    caCert: this.caCert,
                    postData: this.postData,
                }).generateDialog({
                    parent: this,
                });
            },

            updatePatient(e) {
                this.postData.patient = e;
            },

            async submit(emitData, needPrint, successCallback) {
                if (this.saveLoading) return false;

                this.saveLoading = true;

                try {
                    let data;
                    // 已诊并且只更新了病历就或者没有更新只提交病历
                    if (this.isDiagnosis && isEqual(this.isEquals, ['medicalRecord'])) {
                        // 发病时间校验, 传给后端时不能带毫秒
                        if (this.postData.medicalRecord && this.postData.medicalRecord.symptomTime) {
                            const symptomTimeDate = new Date(this.postData.medicalRecord.symptomTime);
                            symptomTimeDate.setHours(0, 0, 0, 0);
                            this.postData.medicalRecord.symptomTime = symptomTimeDate.toISOString();
                        }
                        this.preSubmitMRHandle();
                        const res = await OutpatientAPI.updateMedicalRecord(this.postData.medicalRecord.id, {
                            ...this.postData.medicalRecord,
                        });
                        data = {
                            medicalRecord: res.data,
                            id: this.postData.id,
                            status: this.postData.status,
                            statusName: this.postData.statusName,
                        };
                    } else {
                        this.preSubmitHandle();
                        const res = await OutpatientAPI.update(this.postData.id, {
                            action: 0,
                            region: this.currentRegion,
                            ...this.postData,
                        });
                        data = res.data;
                        this.updateShebaoRestrictResult(data);
                        this.updateGoodsExtensions();
                    }
                    this.closeDialog();
                    this.saveLoading = false;
                    this.successHandler(data, needPrint);
                    typeof successCallback === 'function' && successCallback();
                    this.$emit('open-caller');
                } catch (err) {
                    this.saveLoading = false;
                    this.failHandler(err);
                    this.closeDialog();
                    if ([13502, 13503].includes(err.code) && this.crmAccessVisibility) {
                        this.handlePatientExist(err.code, err.detail);
                    }
                }
            },

            async successHandler(data, needPrint) {
                const hasBind = this.autoSendOrderInfoSwitch &&
                    this.postData.patient &&
                    this.postData.patient.wxBindStatus === WxBindStatusEnum.SUBSCRIBE_AND_BIND;

                let message = '';
                if (this.isDiagnosis) {
                    message = '修改成功';
                    if (hasBind) {
                        message += '，患者待支付的订单信息将更新';
                    }
                } else {
                    message = '接诊完成';
                    if (hasBind) {
                        message += `，请患者在微${this.$app.institutionTypeWording}内支付`;
                    }
                }
                this.$Toast({
                    message,
                    type: 'success',
                });

                this.delCurrentCalling();
                this.clearDraft();
                if (needPrint) {
                    const { cache } = Printer;
                    cache.set({
                        outpatient: needPrint.filter((item) => {
                            return item;
                        }),
                        outpatientNeedPrint: needPrint.filter((item) => {
                            return item;
                        }).length > 0,
                    });
                }

                this._isScroll = true;

                if (!this.outpatient.scrollParams.keyword) {
                    // 改变状态成功后 获取quicklist 当天最新状态
                    await this.refreshOutpatientQuickList();
                }
                const selectedItem = this.outpatient.quickList.find((item) => {
                    return item.id === data.id;
                });
                if (selectedItem) {
                    Object.assign(selectedItem, {
                        status: data.status,
                        statusName: data.statusName,
                    });
                    this.$store.dispatch('setSelectedItem', {
                        type: 'outpatient',
                        selectedItem,
                    });
                }

                // 记录中药处方用法+计量+频率
                if (this.postData.prescriptionChineseForms.length) {
                    const usageData = {};
                    this.postData.prescriptionChineseForms.forEach((pr) => {
                        usageData[pr.usage] = {};
                        usageData[pr.usage].usage = pr.usage;
                        usageData[pr.usage].dailyDosage = pr.dailyDosage;
                        usageData[pr.usage].freq = pr.freq;
                        usageData[pr.usage].usageLevel = pr.usageLevel;
                    });
                    console.log(usageData);
                    this.$store.dispatch('setChinesePRUsageDefault', usageData);
                }

                /**
                 * @desc 提交后id不一致，需要跳转到新Id，ex：提交同时删除了草稿
                 * <AUTHOR> Yang
                 * @date 2021-03-23 18:58:04
                 */
                if (this.$route.params.id !== data.id) {
                    this.replaceRouter(`outpatient/${data.id}`);
                    return false;
                }
                this.sendOutpatientUnlock();
                this.fetchDetail();
                this.refreshSideBarKey();
                this.gotoSocialSettle();
                if (this.clinicBasic.isEnableAIExperience) {
                    Logger.report({
                        scene: 'outpatientSubmit',
                    });
                }
            },

            closeDialog() {
                this.saveLoading = false;
                this.chargeDialogVisible = false;
            },

            /**
             * @desc 直接点打印，但是内容有修改，需要先提交，再打印
             * <AUTHOR>
             * @date 2018/10/12 11:27:41
             */
            printHandler(selected) {
                // 是编辑状态的时候, 当有修改 需要提示用户是否保存并打印
                if (this.isUpdate && this.isEditStatus) {
                    this.$confirm({
                        type: 'confirm',
                        title: '提示',
                        content: `修改了患者 ${this.postData.patient.name} 的${this.updatedTips}，是否保存并打印？`,
                        onConfirm: async () => {
                            // 提交前预检测抗菌用药是否符合规范
                            const isAllowed = this.preCheckAntimicrobial(this.postData);
                            if (!isAllowed) return false;

                            const flag = await this.preSaveHandle(this.postData, () => {
                                this.submit(null, selected);
                            });
                            if (!flag) {
                                this.submit(null, selected);
                            }
                        },
                    });
                } else {
                    this.print(selected);
                }
            },

            /**
             * @desc 删除草稿
             * <AUTHOR> Yang
             * @date 2020-12-17 16:52:33
             * @params
             * @return
             */
            deleteDraft() {
                if (this._isDeleting) return false;
                this.$confirm({
                    type: 'warn',
                    title: '删除确认',
                    content: '删除后不能恢复。确定删除该草稿？',
                    onConfirm: this.deleteDraftSubmit,
                });
            },
            async deleteDraftSubmit() {
                try {
                    this.isEquals = [];
                    this.isUpdate = false;
                    this._isDeleting = true;
                    await OutpatientAPI.deleteOutpatientDraft(this.outpatientSheetId, 0);
                    this.clearDraft();
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    await this.refreshOutpatientQuickList();
                    this.selectFirst();
                } catch (e) {
                    this.refreshOutpatientQuickList();
                    const {
                        code, message,
                    } = e;
                    if (code === 16003) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                            onClose: () => {
                                this.selectFirst();
                            },
                        });
                    }
                    console.error(e);
                } finally {
                    this._isDeleting = false;
                }
            },
            /**
             * @desc 选中第一个QL
             * <AUTHOR>
             * @date 2019/02/27 17:58:03
             */
            selectFirst() {
                let selectedItem;

                if (this.outpatient.quickList.length > 0) {
                    selectedItem = this.outpatient.quickList[0];

                    // 需要读取下草稿中的患者信息来拉取患者就诊历史
                    if (selectedItem && selectedItem.status < 1 && this.draftOutpatients) {
                        const draftObj = this.draftOutpatients.find((it) => {
                            return it.draftId === selectedItem.id;
                        });
                        if (draftObj) {
                            Object.assign(selectedItem, {
                                patient: draftObj.patient,
                            });
                        }
                    }

                } else {
                    selectedItem = null;
                }
                if (this.$route.path.indexOf('outpatient') === -1) {
                    console.log('快速切换head tab 会导致在非当前tab下执行该方法', this.$route.path, 'outpatient');
                    return;
                }

                // 快速接诊创建云端草稿，patient为null，补充一个空结构
                if (selectedItem && !selectedItem.patient) {
                    selectedItem.patient = {};
                }

                const id = selectedItem && selectedItem.id || null;
                if (id) {
                    this.replaceRouter(`outpatient/${id}`);
                } else {
                    this.replaceRouter('outpatient');
                }

                this.$store.dispatch('setSelectedItem', {
                    type: 'outpatient',
                    selectedItem,
                });
            },
            async addPatientFromOtherClinic(id) {
                try {
                    await CrmAPI.handlePatientExist(id);
                    this.$Toast({
                        message: '添加患者成功',
                        type: 'success',
                    });
                } catch (e) {
                    console.log(e);
                }
            },
            handlePatientExist(code = 13503, detail = []) {
                const {
                    name = '',
                    mobile = '',
                    idCard = '',
                    idCardType = DEFAULT_CERT_TYPE,
                    id = '',
                } = detail?.[0] || {};
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    confirmText: '添加患者',
                    showCancel: false,
                    content: code === 13502 ? `证件号[${idCardType}]${idCard}已经被连锁中存在的患者 ${name} 注册` : `同名患者 ${name} ${mobile ? `${this.isCanSeePatientMobileInCrm ? mobile : encryptMobile(mobile)}` : ''} ${idCard ? `[${idCardType}]${idCard}` : ''}已在连锁中存在`,
                    onConfirm: async () => {
                        await this.addPatientFromOtherClinic(id);
                    },
                });
            },

            /**
             * @desc 确认提交预诊
             * <AUTHOR> Yang
             * @date 2020-12-28 14:26:47
             */
            preDiagnosisConfirm() {
                if (this.status) return false;
                // const { patient } = this.postData;
                // const {
                //     name,
                //     age,
                //     sex,
                // } = patient;
                // if (!name || (!age.year && !age.month && !age.day) || !sex) {
                //     this.$alert({
                //         type: 'warn',
                //         title: '提示',
                //         content: '请填写完整的患者信息',
                //     });
                //     return false;
                // }
                const validateFlag = this.$refs?.['outpatient-form-wrapper-form']?.$refs?.['patient-section']?.validatePatientInfo();
                if (validateFlag) {
                    this.$refs?.['outpatient-form-wrapper-form']?.$refs?.['patient-section']?.openPatientCard();
                    return;
                }
                const {
                    doctorId, doctorName,
                } = this.postData;
                if (!doctorId) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '请选择主诊医生',
                    });
                    return false;
                }

                // 提交前预检测抗菌用药是否符合规范
                const isAllowed = this.preCheckAntimicrobial(this.postData);
                if (!isAllowed) {
                    return false;
                }

                this.$confirm({
                    title: `提交预诊后主诊医生 ${doctorName || '未选择'} 可查看到预诊内容`,
                    content: ['主诊医生可在预诊内容基础上继续完善病历和医嘱，高效完成接诊'],
                    confirmLoading: this.preLoading,
                    closeAfterConfirm: false,
                    onConfirm: (modelVm) => {
                        this.preDiagnosisSubmit(modelVm);
                    },
                });
            },

            /**
             * @desc 提交预诊
             * <AUTHOR> Yang
             * @date 2021-01-14 11:36:53
             */
            async preDiagnosisSubmit(modelVm) {
                // 已诊不能再预诊
                if (this.status) return false;
                if (this.preLoading) return false;
                modelVm.confirmLoading = this.preLoading = true;
                try {
                    this.preSubmitHandle();
                    const { data } = await OutpatientAPI.update(this.postData.id, {
                        action: 2,
                        ...this.postData,
                    });
                    // 门诊内容 变更需要更新dataSignature
                    this.postData.dataSignature = data.dataSignature;
                    this._postDataCache = clone(this.postData);
                    this.isUpdate = false;
                    this.clearDraft();

                    modelVm.confirmLoading = this.preLoading = false;
                    modelVm.close();

                    this.$Toast({
                        message: '提交预诊成功',
                        type: 'success',
                    });
                    await this.refreshOutpatientQuickList();
                    const selectedItem = this.outpatient.quickList.find((item) => {
                        return item.id === data.id;
                    });
                    if (selectedItem) {
                        Object.assign(selectedItem, {
                            patient: data.patient,
                            status: data.status,
                            statusName: data.statusName,
                            isDraft: data.isDraft,
                            created: data.created,
                        });
                        this.$store.dispatch('setSelectedItem', {
                            type: 'outpatient',
                            selectedItem,
                        });
                    }
                    /**
                     * @desc 提交后id不一致，需要跳转到新Id，ex：提交同时删除了草稿
                     * <AUTHOR> Yang
                     * @date 2021-03-23 18:58:04
                     */
                    if (this.$route.params.id !== data.id) {
                        this.replaceRouter(`outpatient/${data.id}`);
                        return false;
                    }
                    this.sendOutpatientUnlock();
                    await this.fetchDetail(true);
                    this.updateChinesePR();
                } catch (e) {
                    this.refreshOutpatientQuickList();
                    const {
                        code, message, detail,
                    } = e;
                    // TODO CRM
                    if ([13502, 13503].includes(code) && this.crmAccessVisibility) {
                        this.handlePatientExist(code, detail);
                    } else if (code === 16003) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                            onClose: () => {
                                this.selectFirst();
                            },
                        });
                    }
                    console.error(e);
                } finally {
                    modelVm.confirmLoading = this.preLoading = false;
                }
            },

            /**
             * @desc
             * <AUTHOR>
             * @date 2022/08/21 21:54:57
             * @param
             * @return
             */
            clearWatchDraftTimer() {
                if (this._watchDraftTimer) {
                    clearTimeout(this._watchDraftTimer);
                }
                if (this._postDataUnWatch) {
                    this._postDataUnWatch();
                }
            },
            /**
             * @desc 待诊单加锁
             * <AUTHOR>
             * @date 2022/08/02 16:46:26
             */
            watchIsUpdateOnce() {
                this._isUpdateUnWatch = this.$watch('isUpdate', (val) => {
                    //待诊
                    if (val && !this.isDraft && this.status === OutpatientStatusEnum.UN_DIAGNOSIS) {
                        this._sendOutpatientLock();
                        this._isUpdateUnWatch();
                    }
                });
            },

            async initMRHandler(defaultType) {
                const { medicalRecord } = this.postData;
                defaultType = defaultType ?? medicalRecord.type;

                this.$store.commit('outpatientConfig/SET_EMP_MR_KEY_CONFIG', {
                    key: 'type',
                    value: defaultType,
                });
                const { medicalRecordV2: configMR } = this.outpatientEmployeeConfig;

                this.postData.medicalRecord.type = configMR.type ?? defaultType;
                Object.assign(this._postDataCache.medicalRecord, {
                    type: this.postData.medicalRecord.type,
                });
                this.initMedicalRecordStruct();
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
