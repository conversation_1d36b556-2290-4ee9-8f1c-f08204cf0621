import {
    createGUID, isNotNull, isNumber,
} from 'utils/index.js';
import clone from 'utils/clone.js';
import {
    ShebaoPayModeByTypeEnum, ShebaoPayTypeByModeEnum,
} from 'views/outpatient/constants';
import { ShebaoPayMode } from 'views/inventory/goods/archives/components/social-code-autocomplete/constant';
import {
    GoodsSubTypeEnum, GoodsTypeEnum, PharmacyTypeEnum, FeeComposeTypeEnum,
} from '@abc/constants';
import { getInitSurgeryReq } from '@/views-hospital/medical-prescription/utils/constants';
import { RestrictedLevelBusiness } from 'views/settings/antimicrobial-management/common/contants';

export const OutpatientFormKeys = [
    {
        formsKey: 'productForms',
        itemsKey: 'productFormItems',
    }, {
        formsKey: 'prescriptionChineseForms',
        itemsKey: 'prescriptionFormItems',
    }, {
        formsKey: 'prescriptionWesternForms',
        itemsKey: 'prescriptionFormItems',
    }, {
        formsKey: 'prescriptionInfusionForms',
        itemsKey: 'prescriptionFormItems',
    }, {
        formsKey: 'prescriptionExternalForms',
        itemsKey: 'prescriptionFormItems',
    },
];

/**
 * @desc 算费后读取相关数据
 * <AUTHOR>
 * @date 2019/10/11 19:22:41
 * @params originData 传入postData
 * @params formsKey 对应postData上的form
 * @params itemsKey 对应form上的items
 * @params dataForm 算费返回数据form
 * @params dataItem 算费返回数据form上的item
 * @params vueVm 当前实例
 * @return
 */
export function findAndAssignment(originData, formsKey, itemsKey, dataForm, dataItem, vueVm) {
    originData[formsKey].forEach((form) => {
        if (!form.keyId) {
            console.error('当前form没有keyId，请初始化');
        }
        if (!dataForm.keyId) {
            console.error('当前dataForm没有keyId，请初始化');
        }
        if (form.keyId === dataForm.keyId) {
            form.totalPrice = dataForm.totalPrice;
            // 用于用户编辑
            vueVm.$set(form, 'userInputFormTotalPriceCache', dataForm.totalPrice);
            vueVm.$set(form, 'totalPrice', dataForm.totalPrice);
            vueVm.$set(form, 'sourceTotalPrice', dataForm.sourceTotalPrice);
            vueVm.$set(form, 'canAdjustment', dataForm.canAdjustment);
            vueVm.$set(form, 'canAdjustmentFee', dataForm.canAdjustmentFee);
            // form上的总金额是否修改
            vueVm.$set(form, 'isTotalPriceChanged', Number(!!dataForm.expectedTotalPrice));

            if (dataForm.isDecoction) {
                vueVm.$set(form, 'processBagUnitCount', dataForm.processBagUnitCount);
                vueVm.$set(form, 'totalProcessCount', dataForm.totalProcessCount);
            }

            // 空中药房需不需要加工认后台字段
            if (dataForm.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                vueVm.$set(form, 'isDecoction', dataForm.isDecoction);

                if (dataForm.deliveryInfo) {
                    vueVm.$set(form, 'deliveryInfo', {
                        ...dataForm.deliveryInfo,
                        displayTotalPrice: dataForm.deliveryInfo.totalPrice,
                    });
                } else {
                    vueVm.$set(form, 'deliveryInfo', null);
                }
                if (dataForm.processInfo) {
                    vueVm.$set(form, 'processInfo', {
                        ...dataForm.processInfo,
                        displayTotalPrice: dataForm.processInfo.totalPrice,
                    });
                } else {
                    vueVm.$set(form, 'processInfo', null);
                }
                if (dataForm.ingredientInfo) {
                    vueVm.$set(form, 'ingredientInfo', {
                        ...dataForm.ingredientInfo,
                        displayTotalPrice: dataForm.ingredientInfo.totalPrice,
                    });
                } else {
                    vueVm.$set(form, 'ingredientInfo', null);
                }
            }

            form[itemsKey].forEach((item) => {
                if (!item.keyId) {
                    console.error('当前 item 没有keyId，请初始化');
                }
                if (!dataItem.keyId) {
                    console.error('当前 dataItem 没有keyId，请初始化');
                }

                if (item.keyId === dataItem.keyId) {

                    vueVm.$set(item, 'totalPriceRatio', dataItem.totalPriceRatio);
                    vueVm.$set(item, 'expectedTotalPriceRatio', dataItem.expectedTotalPriceRatio);
                    vueVm.$set(item, 'totalPrice', dataItem.totalPrice);
                    vueVm.$set(item, 'expectedUnitPrice', dataItem.expectedUnitPrice);
                    vueVm.$set(item, 'expectedTotalPrice', dataItem.expectedTotalPrice);
                    vueVm.$set(item, 'sourceTotalPrice', dataItem.sourceTotalPrice);
                    vueVm.$set(item, 'sourceUnitPrice', dataItem.sourceUnitPrice);
                    vueVm.$set(item, 'stockPackageCount', dataItem.stockPackageCount);
                    vueVm.$set(item, 'stockPieceCount', dataItem.stockPieceCount);
                    vueVm.$set(item, 'currentUnitPrice', dataItem.currentUnitPrice);
                    vueVm.$set(item, 'canAdjustment', dataItem.canAdjustment);
                    vueVm.$set(item, 'canAdjustmentFee', dataItem.canAdjustmentFee);
                    // item上是否议单价
                    vueVm.$set(item, 'isUnitPriceChanged', Number(!!dataItem.expectedUnitPrice));
                    // item上是否议总价
                    vueVm.$set(item, 'isTotalPriceChanged', Number(!!dataItem.expectedTotalPrice));
                    // 算费后的批次信息
                    vueVm.$set(item, 'batchInfos', dataItem.batchInfos);
                    // 套餐、费用项子项
                    vueVm.$set(item, 'composeChildren', dataItem.composeChildren);
                    /**
                     * @desc 需要透传给后台的字段
                     * @desc formFlatPrice:  form上平摊下来的金额
                     * @desc sheetFlatPrice: sheet上平摊下来的金额
                     * @desc fractionPrice:  拆零价格
                     * <AUTHOR>
                     * @date 2022-03-11 08:56:26
                     */
                    item.formFlatPrice = dataItem.formFlatPrice;
                    item.sheetFlatPrice = dataItem.sheetFlatPrice;
                    item.fractionPrice = dataItem.fractionPrice;

                    item.unitPrice = dataItem.unitPrice;
                }
            });
        }
    });
}

/**
 * @desc 门诊算费回调
 * <AUTHOR>
 * @date 2022-03-18 13:31:26
 * @params originData 需要被赋值的原始数据postData
 * @params data 算费返回数据
 * @params 实例
 */
export function calcDataCallback(originData, data, vueVm) {
    originData.adjustmentFee = data.adjustmentFee;
    originData.totalPrice = data.totalPrice > 0 ? data.totalPrice : 0;
    originData.registrationFee = Number(data.registrationFee || 0).toFixed(2);
    // sheet上的总金额是否修改
    vueVm.$set(originData, 'isTotalPriceChanged', Number(!!data.expectedTotalPrice));
    vueVm.$set(originData, 'sourceTotalPrice', data.sourceTotalPrice);
    vueVm.$set(originData, 'canAdjustment', data.canAdjustment);
    vueVm.$set(originData, 'canAdjustmentFee', data.canAdjustmentFee);

    OutpatientFormKeys.forEach(({
        formsKey, itemsKey,
    }) => {
        data[formsKey].forEach((form) => {
            form[itemsKey].forEach((item) => {
                findAndAssignment(originData, formsKey, itemsKey, form, item, vueVm);
            });
        });
    });
}

export function initKeyId(originData) {
    if (!originData) return;
    OutpatientFormKeys.forEach(({
        formsKey, itemsKey,
    }) => {
        originData[formsKey].forEach((form) => {
            form.keyId = form.keyId || createGUID();
            form[itemsKey].forEach((item) => {
                item.keyId = item.keyId || createGUID();
                (item.composeChildren || []).forEach((child) => {
                    child.keyId = child.keyId || createGUID();
                });
            });
        });
    });
}

/**
 * @desc 重置算费状态
 * <AUTHOR>
 * @date 2022-03-11 12:48:59
 */
export function resetCalcHandler(originData) {
    originData.expectedTotalPrice = null;
    OutpatientFormKeys.forEach(({
        formsKey, itemsKey,
    }) => {
        originData[formsKey].forEach((form) => {
            form.keyId = form.keyId || createGUID();
            form.expectedTotalPrice = null;
            form[itemsKey].forEach((item) => {
                item.keyId = item.keyId || createGUID();
                // item.expectedUnitPrice = null;
                // item.expectedTotalPriceRatio = null;
                // item.expectedTotalPrice = null;
            });
        });
    });
}

/**
 * @desc 清空form item上的议价
 * <AUTHOR>
 * @date 2022-03-24 09:37:31
 */
export function clearItemBargainHandler(originItem) {
    const hasBargain = !!originItem.expectedUnitPrice;
    originItem.currentUnitPrice = null;
    originItem.formFlatPrice = null;
    originItem.sheetFlatPrice = null;
    originItem.fractionPrice = null;
    originItem.isUnitPriceChanged = null;
    originItem.isTotalPriceChanged = null;
    originItem.expectedUnitPrice = null;
    originItem.expectedTotalPrice = null;

    const {
        productInfo,
    } = originItem || {};
    const {
        piecePrice,
        packagePrice,
    } = productInfo || {};
    const unitPrice = originItem.useDismounting ? piecePrice : packagePrice;
    originItem.unitPrice = unitPrice || 0;

    return hasBargain;
}

/**
 * @desc 清空form上的议价
 * <AUTHOR>
 * @date 2022-03-24 09:37:21
 */
export function clearFormBargainHandler(originForm, itemsKey = 'prescriptionFormItems') {
    const hasBargain = originForm[itemsKey].some((it) => {
        return it.expectedUnitPrice;
    });

    originForm.isTotalPriceChanged = null;
    originForm.expectedTotalPrice = null;
    originForm[itemsKey].forEach((item) => {
        clearItemBargainHandler(item);
    });
    return hasBargain;
}

/**
 * @desc 清空所有议价
 * <AUTHOR>
 * @date 2022-03-24 08:54:49
 */
export function clearAllBargainHandler(originData) {
    originData.expectedTotalPrice = null;
    OutpatientFormKeys.forEach(({
        formsKey, itemsKey,
    }) => {
        originData[formsKey].forEach((form) => {
            clearFormBargainHandler(form, itemsKey);
        });
    });
}


/**
 * @desc 获取单价，判断议价后的单价
 * <AUTHOR>
 * @date 2022-03-28 17:57:03
 * @params
 * @return
 */
export function getItemUnitPrice(originItem) {
    const {
        currentUnitPrice,
        unitPrice,
    } = originItem;
    if (isNumber(currentUnitPrice)) {
        return currentUnitPrice;
    }
    return unitPrice || 0;
}

export function setItemChildrenPayType(composeChildren, keyId, shebaoPayMode, vueVm) {
    if (!keyId || !composeChildren || !composeChildren.length) return false;

    // 遍历当前层级查找匹配项
    for (let i = 0; i < composeChildren.length; i++) {
        const child = composeChildren[i];
        if (child.keyId === keyId) {
            vueVm.$set(child, 'shebaoPayMode', shebaoPayMode);
            vueVm.$set(child, 'payType', ShebaoPayTypeByModeEnum[shebaoPayMode]);
            return;
        }
    }

    // 当前层级没有找到匹配项，递归查找子层级
    for (let i = 0; i < composeChildren.length; i++) {
        const child = composeChildren[i];
        if (child.composeChildren && child.composeChildren.length) {
            setItemChildrenPayType(child.composeChildren, keyId, shebaoPayMode, vueVm);
        }
    }
}

export function getShebaoRestrictItemStruct(composeChildren) {
    if (!composeChildren || !composeChildren.length) return null;

    return composeChildren.map((item) => {
        const {
            keyId,
            name,
            payType,
            productInfo,
        } = item;
        const {
            feeComposeType,
            shebaoPayMode,
            shebao,
            type,
            subType,
        } = productInfo || {};
        return {
            keyId: keyId || createGUID(),
            name,
            feeComposeType,
            goodsShebaoPayMode: shebaoPayMode || ShebaoPayMode.OVERALL,
            shebaoPayMode: ShebaoPayModeByTypeEnum[payType] || shebaoPayMode || ShebaoPayMode.OVERALL,
            shebao,
            type,
            subType,
            payType,
            children: getShebaoRestrictItemStruct(item.composeChildren),
        };
    });
}

export function simplifyShebaoRestrictData(postData) {
    let prescriptionWesternForms = clone(postData.prescriptionWesternForms || []);
    let prescriptionInfusionForms = clone(postData.prescriptionInfusionForms || []);
    let prescriptionChineseForms = clone(postData.prescriptionChineseForms || []);
    let prescriptionExternalForms = clone(postData.prescriptionExternalForms || []);
    let productForms = clone(postData.productForms || []);

    prescriptionWesternForms = prescriptionWesternForms.filter((form) => {
        form.prescriptionFormItems = form.prescriptionFormItems.map((item) => {
            return {
                chargeStatus: item.chargeStatus,
                days: item.days,
                dosage: item.dosage,
                dosageUnit: item.dosageUnit,
                freq: item.freq,
                keyId: item.keyId,
                name: item.name,
                shebaoCode: item.productInfo?.shebaoNationalCode,
                goodsShebaoPayMode: item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                shebaoPayMode: ShebaoPayModeByTypeEnum[item.payType] || item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                usage: item.usage,
                type: item.type,
                subType: item.subType,
                payType: item.payType,
                unitCount: item.unitCount,
                unit: item.unit,
                doseCount: item.doseCount,
                verifySignatures: (item.verifySignatures || []).map((x) => x.type),
            };
        }).filter((it) => {
            return !it.chargeStatus;
        });
        return form.prescriptionFormItems.length > 0;
    });

    prescriptionInfusionForms = prescriptionInfusionForms.filter((form) => {
        form.prescriptionFormItems = form.prescriptionFormItems.map((item) => {
            return {
                chargeStatus: item.chargeStatus,
                days: item.days,
                dosage: item.dosage,
                dosageUnit: item.dosageUnit,
                freq: item.freq,
                keyId: item.keyId,
                name: item.name,
                shebaoCode: item.productInfo?.shebaoNationalCode,
                goodsShebaoPayMode: item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                shebaoPayMode: ShebaoPayModeByTypeEnum[item.payType] || item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                usage: item.usage,
                type: item.type,
                subType: item.subType,
                payType: item.payType,
                unitCount: item.unitCount,
                unit: item.unit,
                doseCount: item.doseCount,
                verifySignatures: (item.verifySignatures || []).map((x) => x.type),
            };
        }).filter((it) => {
            return !it.chargeStatus;
        });
        return form.prescriptionFormItems.length > 0;
    });

    prescriptionChineseForms = prescriptionChineseForms.filter((form) => {
        form.prescriptionFormItems = form.prescriptionFormItems.map((item) => {
            return {
                chargeStatus: item.chargeStatus,
                days: item.days,
                dosage: item.dosage,
                dosageUnit: item.dosageUnit,
                freq: item.freq,
                keyId: item.keyId,
                name: item.name,
                shebaoCode: item.productInfo?.shebaoNationalCode,
                goodsShebaoPayMode: item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                shebaoPayMode: ShebaoPayModeByTypeEnum[item.payType] || item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                usage: item.usage,
                type: item.type,
                subType: item.subType,
                payType: item.payType,
                unitCount: item.unitCount,
                unit: item.unit,
                doseCount: item.doseCount,
                verifySignatures: (item.verifySignatures || []).map((x) => x.type),
            };
        }).filter((it) => {
            return !it.chargeStatus;
        });
        return form.prescriptionFormItems.length > 0;
    });
    prescriptionExternalForms = prescriptionExternalForms.filter((form) => {
        form.prescriptionFormItems = form.prescriptionFormItems.map((item) => {
            return {
                keyId: item.keyId,
                chargeStatus: item.chargeStatus,
                days: item.days,
                dosage: item.dosage,
                dosageUnit: item.dosageUnit,
                freq: item.freq,
                name: item.name,
                shebaoCode: item.productInfo?.shebaoNationalCode,
                goodsShebaoPayMode: item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                shebaoPayMode: ShebaoPayModeByTypeEnum[item.payType] || item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                usage: item.usage,
                type: item.type,
                subType: item.subType,
                payType: item.payType,
                unitCount: item.unitCount,
                unit: item.unit,
                doseCount: item.doseCount,
                children: getShebaoRestrictItemStruct(item.composeChildren),
                verifySignatures: (item.verifySignatures || []).map((x) => x.type),
            };
        }).filter((it) => {
            return !it.chargeStatus;
        });
        return form.prescriptionFormItems.length > 0;
    });

    // productForms 不过滤都给社保他们自己过滤
    productForms = productForms.filter((form) => {
        form.productFormItems = form.productFormItems.map((item) => {
            return {
                keyId: item.keyId,
                chargeStatus: item.chargeStatus,
                name: item.name,
                type: item.type,
                subType: item.subType,
                payType: item.payType,
                composeType: item.composeType,
                combineType: item.productInfo?.combineType,
                feeComposeType: item.productInfo?.feeComposeType,
                shebaoCode: item.productInfo?.shebaoNationalCode,
                goodsShebaoPayMode: item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                shebaoPayMode: ShebaoPayModeByTypeEnum[item.payType] || item.productInfo?.shebaoPayMode || ShebaoPayMode.OVERALL,
                productInfo: item.productInfo,
                children: getShebaoRestrictItemStruct(item.composeChildren),
                verifySignatures: (item.verifySignatures || []).map((x) => x.type),
            };
        }).filter((it) => {
            return !it.chargeStatus;
        });
        return form.productFormItems.length > 0;
    });

    const { extendDiagnosisInfos } = postData.medicalRecord || {};
    return {
        departmentId: postData.departmentId,
        patient: postData.patient,
        prescriptionWesternForms,
        prescriptionInfusionForms,
        prescriptionChineseForms,
        prescriptionExternalForms,
        productForms,
        diagnosisInfos: extendDiagnosisInfos && extendDiagnosisInfos[0]?.value,
    };
}

/**
 * @desc 提交数据请求体冗余字段清理
 * <AUTHOR>
 * @date 2024/01/15 16:59:44
 * @param {Object} postData
 * @return {Object} data
 */
export const getSimplePostData = (postData) => {
    const cloneData = clone(postData);
    delete cloneData.printable;
    delete cloneData.registrationInfo;
    delete cloneData.productInfos;
    const {
        medicalRecord,
        productForms,
        prescriptionWesternForms,
        prescriptionInfusionForms,
        prescriptionChineseForms,
        prescriptionExternalForms,
    } = cloneData;

    if (medicalRecord) {
        delete medicalRecord.examItems;
    }
    productForms.forEach((form) => {
        form.productFormItems.forEach((item) => {
            delete item.examinationResult;
            delete item.productInfo;
            delete item.realProductInfo;
        });
    });
    [
        ...prescriptionWesternForms,
        ...prescriptionInfusionForms,
        ...prescriptionChineseForms,
        ...prescriptionExternalForms,
    ].forEach((form) => {
        form.prescriptionFormItems.forEach((item) => {
            delete item.productInfo;
            delete item.realProductInfo;
        });
    });

    return cloneData;
};

export function formatSurgeryDetail(item, goods, departmentId, doctorId, children) {
    const {
        type, subType, name,
    } = goods;
    // 手术需要将productInfo中的手术操作放在item上的children中
    const surgeryDetail = getInitSurgeryReq();
    surgeryDetail.surgeryDepartmentId = departmentId;
    surgeryDetail.surgeryDoctorId = doctorId;
    surgeryDetail.name = name;
    if (type === GoodsTypeEnum.SURGERY && subType === GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY) {
        item.children = children || [];
        surgeryDetail.intendedSurgeries = item.children.map((it) => ({
            name: it.name,
            code: it.operationCode,
            level: it.surgeryGradeCode,
            site: it.surgerySiteCode,
            healingIncisionLevel: it.surgeryIncisionHealingCode,
        }));
        item.surgeryDetail = clone(surgeryDetail);
    } else if (type === GoodsTypeEnum.COMPOSE) {
        item.composeChildren = children || [];
        item.composeChildren.forEach((child) => {
            const {
                type: childType, subType: childSubType,
            } = child;
            child.keyId = createGUID();
            child.productId = child.goodsId;
            if (childType === GoodsTypeEnum.SURGERY && childSubType === GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY) {
                child.children = child.children || [];
                surgeryDetail.intendedSurgeries = child.children.map((it) => ({
                    name: it.name,
                    code: it.operationCode,
                    level: it.surgeryGradeCode,
                    site: it.surgerySiteCode,
                    healingIncisionLevel: it.surgeryIncisionHealingCode,
                }));
                child.surgeryDetail = clone(surgeryDetail);
            }
        });
    }
}

/**
 * 获取指定职称列表的成员
 */
export function getEmployeeListByPractice(employeeListByPractice, practiceList) {
    if (!practiceList || !practiceList.length) return [];
    return employeeListByPractice.filter((x) => {
        const { practiceInfo } = x;
        if (!practiceInfo || !practiceInfo.length) {
            return false;
        }
        for (const el of practiceInfo) {
            if (practiceList.includes(el.title)) {
                return true;
            }
        }
        return false;
    });
}

/**
 * 判断开出的中西成药的抗菌等级是否满足开出条件
 * @param {Object} item
 * @param {string} doctorId
 * @param {Object} antimicrobialDrugManagementData
 * @param {Array} employeeListByPractice
 * @return {boolean}
 */
export function isAllowAddByAntimicrobialDrugManagement(item, doctorId, antimicrobialDrugManagementData, employeeListByPractice) {
    const { productInfo } = item;
    if (!productInfo) return true;
    if (antimicrobialDrugManagementData?.enable) {
        if (
            item.productInfo.type === GoodsTypeEnum.MEDICINE &&
            [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(item.productInfo.subType) &&
            isNotNull(item.productInfo.antibiotic) &&
            !doctorId
        ) {
            return false;
        }

        const { rules } = antimicrobialDrugManagementData;
        const { antibiotic } = productInfo;
        const rule = (rules || []).find((x) => x.restrictedLevel === antibiotic);
        if (rule) {
            const {
                availableBusiness, availableTitles,
            } = rule;
            if (!availableTitles.length) return true;
            if ((availableBusiness || []).includes(RestrictedLevelBusiness.OUTPATIENT)) {
                const employeeList = getEmployeeListByPractice(employeeListByPractice, availableTitles);
                const findAllowEmployee = employeeList.find((x) => x.employeeId === doctorId);
                if (!findAllowEmployee) {
                    return false;
                }
            }
        }
    }
    return true;
}

export function getItemComposeChildren(data) {
    const {
        feeComposeType, children, feeComposeList,
    } = data || {};
    if (![
        FeeComposeTypeEnum.FEE_TYPE_COMPOSE_GOODS,
        FeeComposeTypeEnum.FEE_TYPE_COMPOSE_FEE,
    ].includes(feeComposeType)) return null;
    // 组合项
    if (feeComposeType === FeeComposeTypeEnum.FEE_TYPE_COMPOSE_FEE) {
        if (!feeComposeList || !feeComposeList.length) return null;
        return feeComposeList.map((x) => {
            return {
                keyId: x.keyId || createGUID(),
                goodsId: x.goodsId,
                productId: x.goodsId,
                name: x.name || x.displayName,
                payType: null,
                productInfo: x,
                productPrimaryId: x.composeId,
                goodsPrimaryId: x.composeId,
            };
        });
    }
    // 套餐
    if (feeComposeType === FeeComposeTypeEnum.FEE_TYPE_COMPOSE_GOODS) {
        if (!children || !children.length) return null;
        return children.map((x) => {
            return {
                keyId: x.keyId || createGUID(),
                goodsId: x.goodsId,
                productId: x.goodsId,
                name: x.name || x.displayName,
                payType: null,
                composeChildren: getItemComposeChildren(x),
                productInfo: x,
                productPrimaryId: x.composeId,
                goodsPrimaryId: x.composeId,
            };
        });
    }
}

export * from '@/common/utils/outpatient.js';
