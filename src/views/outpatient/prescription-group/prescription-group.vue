<template>
    <div
        class="outpatient-form-item prescription-group"
        :class="{
            'is-disabled': disabled || (disabledAdd && !isExistPrescription),
            'no-data': noData,
        }"
        data-cy="处方医嘱"
    >
        <div class="title" style="padding-right: 7px;">
            <h3>处方医嘱</h3>

            <div v-if="!(disabled || disabledAdd)" class="btn-group">
                <abc-button
                    v-if="currentSwitchSetting.westernSwitch"
                    variant="ghost"
                    theme="default"
                    size="small"
                    data-cy="pr-western-btn"
                    @click="addPR(PrescriptionTypeEnum.WESTERN)"
                >
                    {{ _westernPrescriptionNameText }}
                </abc-button>
                <abc-button
                    v-if="supportGlasses && currentSwitchSetting.glassesSwitch"
                    data-cy="pr-cn-btn"
                    variant="ghost"
                    theme="default"
                    size="small"
                    style="margin-left: 6px;"
                    :disabled="!!postData.prescriptionGlassesForms.length"
                    @click="addPR(PrescriptionTypeEnum.GLASSES)"
                >
                    配镜处方
                </abc-button>
                <abc-button
                    v-if="currentSwitchSetting.infusionSwitch"
                    data-cy="pr-infusion-btn"
                    variant="ghost"
                    theme="default"
                    size="small"
                    style="margin-left: 6px;"
                    @click="addPR(PrescriptionTypeEnum.INFUSION)"
                >
                    输注处方
                </abc-button>
                <abc-button
                    v-if="showAddCNPR"
                    data-cy="pr-cn-btn"
                    variant="ghost"
                    theme="default"
                    size="small"
                    style="margin-left: 6px;"
                    @click="addPR(PrescriptionTypeEnum.CHINESE)"
                >
                    中药处方
                </abc-button>

                <abc-popover
                    v-if="showAddExternalPR"
                    v-abc-click-outside="
                        () => {
                            showExternalOptions = false
                        }
                    "
                    :value="showExternalOptions"
                    placement="bottom"
                    width="auto"
                    trigger="manual"
                    theme="white"
                    :visible-arrow="false"
                    :popper-style="{ padding: 0 }"
                >
                    <abc-button
                        slot="reference"
                        data-cy="pr-external-btn"
                        variant="ghost"
                        theme="default"
                        size="small"
                        style="margin-left: 6px;"
                        @click="addExternalPRSpecial"
                    >
                        外治处方
                    </abc-button>

                    <div class="add-external-pr-popover">
                        <div
                            class="external-pr-item"
                            data-cy="pr-external-tiefu-btn"
                            @click="addExternalPR(ExternalPRUsageTypeEnum.tieFu, TieFuUsageSubTypeEnum.chenPinTie)"
                        >
                            贴敷处方
                        </div>
                        <div
                            class="external-pr-item"
                            data-cy="pr-external-zhenjiu-btn"
                            @click="addExternalPR(ExternalPRUsageTypeEnum.zhenCi, ZhenCiUsageSubTypeEnum.haoZhen)"
                        >
                            针灸处方
                        </div>
                        <div
                            class="external-pr-item"
                            data-cy="pr-external-aijiu-btn"
                            @click="addExternalPR(ExternalPRUsageTypeEnum.aiJiu, AiJiuUsageSubTypeEnum.aiZhuJiu)"
                        >
                            艾灸处方
                        </div>
                        <div
                            class="external-pr-item"
                            data-cy="pr-external-baguan-btn"
                            @click="addExternalPR(ExternalPRUsageTypeEnum.baGuan, BaGuanUsageSubTypeEnum.cupping)"
                        >
                            拔罐处方
                        </div>
                        <div
                            class="external-pr-item"
                            data-cy="pr-external-baguan-btn"
                            @click="addExternalPR(ExternalPRUsageTypeEnum.guaSha, null)"
                        >
                            刮痧处方
                        </div>
                        <div
                            class="external-pr-item"
                            data-cy="pr-external-baguan-btn"
                            @click="addExternalPR(ExternalPRUsageTypeEnum.tuiNa, TuiNaUsageSubTypeEnum.adultTuiNa)"
                        >
                            推拿处方
                        </div>
                    </div>
                </abc-popover>

                <template v-if="currentSwitchSetting.prescriptionTemplateSwitch">
                    <div class="cut-line"></div>

                    <outpatient-history-popover
                        v-if="_showHistoryPrescription && selectedPatient && selectedPatient.id"
                        v-model="showOutpatientHistoryPopover"
                        :patient-id="selectedPatient.id"
                        :with-prescription-info="1"
                        @select-outpatient-record="selectPrescription"
                    >
                        <abc-button
                            variant="ghost"
                            theme="default"
                            size="small"
                            style="margin-left: 6px;"
                            data-cy="pr-history-btn"
                            @click="handlerClickPrHistory"
                        >
                            历史处方{{ recordCount }}
                        </abc-button>
                    </outpatient-history-popover>

                    <abc-button
                        variant="ghost"
                        theme="default"
                        size="small"
                        icon="s-b-data-1-line"
                        style="margin-left: 6px;"
                        data-cy="pr-template-btn"
                        @click="commonPRDialogVisible = true"
                    >
                        处方模板
                    </abc-button>

                    <component :is="currentSettingPopover" data-cy="pr-more-setting" @change-default-pr="changeDefaultPr"></component>
                </template>
            </div>
        </div>

        <!--西药处方-->
        <western-prescription
            v-for="(form, index) in postData.prescriptionWesternForms"
            :id="`${form.keyId}`"
            :key="`${form.id || form.keyId || index }western`"
            :form="form"
            :disabled="disabled || !!form.chargeStatus"
            :disabled-add="disabledAdd"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="postData.departmentId"
            :verify-outpatient="verifyOutpatient"
            :patient-info="postData.patient"
            :medical-record="postData.medicalRecord"
            :forms-length="postData.prescriptionWesternForms.length"
            :shebao-card-info="postData.shebaoCardInfo"
            :psychotropic-narcotic-employee="postData.psychotropicNarcoticEmployee"
            :form-index="index"
            :need-check-stock="needCheckStock"
            :show-medical-fee-grade="showMedicalFeeGrade"
            :is-open-source="isOpenSource"
            :show-total-price="outpatientShowTotalPrice"
            :show-detail-price="outpatientShowDetailPrice"
            :template-manager-version="templateManagerVersion"
            :show-cooperation-pharmacy="showCooperationPharmacy"
            :is-consultation="isConsultation"
            :doctor-id="postData.doctorId"
            :is-from-template="isFromTemplate"
            @close="deleteWesternPrescription"
            @queryVerify="$emit('outpatient-verify')"
            @change-pay-type="onChangePayType"
        >
        </western-prescription>

        <!--输液处方-->
        <infusion-prescription
            v-for="(form, index) in postData.prescriptionInfusionForms"
            :id="`${form.keyId}`"
            :key="`${form.id || form.keyId || index }infusion`"
            ref="infusionPrescriptions"
            :form="form"
            :verify-outpatient="verifyOutpatient"
            :show-total-price="outpatientShowTotalPrice"
            :show-detail-price="outpatientShowDetailPrice"
            :disabled="disabled || !!form.chargeStatus"
            :disabled-add="disabledAdd"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="postData.departmentId"
            :form-index="index"
            :forms-length="postData.prescriptionInfusionForms.length"
            :patient-info="postData.patient"
            :medical-record="postData.medicalRecord"
            :shebao-card-info="postData.shebaoCardInfo"
            :psychotropic-narcotic-employee="postData.psychotropicNarcoticEmployee"
            :need-check-stock="needCheckStock"
            :is-open-source="isOpenSource"
            :show-medical-fee-grade="showMedicalFeeGrade"
            :template-manager-version="templateManagerVersion"
            :show-cooperation-pharmacy="showCooperationPharmacy"
            :is-consultation="isConsultation"
            :doctor-id="postData.doctorId"
            :is-from-template="isFromTemplate"
            @close="deleteInfusionPrescription"
            @queryVerify="$emit('outpatient-verify')"
            @mousedown.native="onClickInfusionPrescription(index)"
            @change-pay-type="onChangePayType"
        >
        </infusion-prescription>

        <!--中药处方-->
        <chinese-prescription
            v-for="(form, index) in postData.prescriptionChineseForms"
            :id="`${form.keyId}`"
            ref="chineseMedicine"
            :key="`${form.id || form.keyId || index }chinese`"
            :forms="postData.prescriptionChineseForms"
            :form="form"
            :show-total-price="outpatientShowTotalPrice"
            :show-detail-price="outpatientShowDetailPrice"
            :disabled="disabled || !!form.chargeStatus"
            :disabled-add="disabledAdd"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="postData.departmentId"
            :patient-info="postData.patient"
            :medical-record="postData.medicalRecord"
            :shebao-card-info="postData.shebaoCardInfo"
            :form-index="index"
            :forms-length="postData.prescriptionChineseForms.length"
            :need-check-stock="needCheckStock"
            :is-open-source="isOpenSource"
            :is-consultation="isConsultation"
            :can-change-pharmacy-type="canChangePharmacyType"
            :template-manager-version="templateManagerVersion"
            @queryVerify="$emit('outpatient-verify')"
            @add-prescription-form-item="(data) => $emit('add-prescription-form-item', data)"
            @close="deleteChinesePrescription"
            @click.native="onClickChinesePrescription(index)"
        >
        </chinese-prescription>

        <!-- 外治处方 -->
        <external-prescription
            v-for="(form, index) in postData.prescriptionExternalForms"
            :id="`${form.keyId}`"
            :key="`${form.id || form.keyId || index }external`"
            :form="form"
            :form-index="index"
            :forms-length="postData.prescriptionExternalForms.length"
            :patient-info="postData.patient"
            :medical-record="postData.medicalRecord"
            :shebao-card-info="postData.shebaoCardInfo"
            :show-detail-price="outpatientShowDetailPrice"
            :disabled="disabled || !!form.chargeStatus"
            :disabled-add="disabledAdd"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="postData.departmentId"
            :need-check-stock="needCheckStock"
            :is-open-source="isOpenSource"
            :show-medical-fee-grade="showMedicalFeeGrade"
            :template-manager-version="templateManagerVersion"
            :doctor-id="postData.doctorId"
            :is-from-template="isFromTemplate"
            @queryVerify="$emit('outpatient-verify')"
            @close="deleteExternalPrescription"
            @change-pay-type="onChangePayType"
        ></external-prescription>

        <!-- 配镜处方 -->
        <glasses-prescription
            v-for="(form, index) in postData.prescriptionGlassesForms"
            :id="`${form.keyId}`"
            :key="`${form.id || form.keyId || index }glasses`"
            :form="form"
            :form-index="index"
            :disabled="disabled || !!form.chargeStatus"
            show-trash-button
            :is-open-source="isOpenSource"
            @close="deleteGlassesPrescription"
        >
        </glasses-prescription>

        <div>
            <!--这个div千万不能删除，原因咨询jason-->
        </div>

        <!--选择常用处方-->
        <prescription-template-dialog
            v-if="commonPRDialogVisible"
            v-model="commonPRDialogVisible"
            :version="templateManagerVersion"
            @useTemplate="useTemplate"
        ></prescription-template-dialog>

        <!--选择使用中药处方模板的方式-->
        <select-chinese-p-r-method-dialog
            v-if="callMethodVisible"
            v-model="callMethodVisible"
            :template-name="selectTemplateItem.name"
            @confirm="onCallConfirm"
        ></select-chinese-p-r-method-dialog>

        <!--选择使用输注处方模板的方式-->
        <select-infusion-method-dialog
            v-if="callInfusionMethodVisible"
            v-model="callInfusionMethodVisible"
            :template-name="selectTemplateItem.name"
            @confirm="onInfusionCallConfirm"
        ></select-infusion-method-dialog>
    </div>
</template>

<script>
    import OutpatientAPI from 'api/outpatient';
    import SettingsAPI from 'api/settings';

    import WesternPrescription from '../../layout/prescription/western/index.vue';
    import ChinesePrescription from '../../layout/prescription/chinese/index.vue';
    import InfusionPrescription from '../../layout/prescription/infusion/index.vue';
    import ExternalPrescription from '../../layout/prescription/external/index.vue';
    import GlassesPrescription from '../../layout/prescription/glasses/index.vue';
    import selectChinesePRMethodDialog from './select-chinese-pr-method-dialog';
    const PrescriptionTemplateDialog = () => import('../common/prescription-template-dialog/prescription-template-dialog');
    import OutpatientHistoryPopover from 'src/views/outpatient/common/outpatient-history-popover';

    import { mapGetters } from 'vuex';
    import prescriptionHandle from '../mixins/prescription-handle';
    import {
        createGUID,
    } from 'utils/index';
    import localStorage from 'utils/localStorage-handler';
    import { OwnerType } from 'src/views/layout/templates-manager/constants';
    import { SourceFormTypeEnum } from '@/service/charge/constants';
    import {
        ExternalPRUsageTypeEnum,
        TieFuUsageSubTypeEnum,
        ZhenCiUsageSubTypeEnum,
        GlassesMaterialTypeEnum,
        AiJiuUsageSubTypeEnum,
        BaGuanUsageSubTypeEnum,
        TuiNaUsageSubTypeEnum,
        ExternalPRBillingTypeEnum,
    } from '../../layout/prescription/constant';
    import {
        GoodsTypeIdEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import {
        DepartmentTypeStatus, PsychotropicNarcoticTypeEnum, PrescriptionDefaultTypeEnum,
    } from 'views/outpatient/constants';
    import { SearchSceneTypeEnum } from 'views/common/enum.js';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import { PrescriptionTypeEnum } from 'src/views/outpatient/constants';
    import SelectInfusionMethodDialog from './select-infusion-method-dialog.vue';
    import {
        isAllowAddByAntimicrobialDrugManagement,
    } from 'views/outpatient/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import Logger from 'utils/logger';
    import useOutpatientCommon from '@/hooks/business/use-outpatient-common/index';

    export default {
        name: 'PrescriptionGroup',

        components: {
            SelectInfusionMethodDialog,
            PrescriptionTemplateDialog,
            selectChinesePRMethodDialog,

            WesternPrescription,
            ChinesePrescription,
            InfusionPrescription,
            ExternalPrescription,
            GlassesPrescription,

            OutpatientHistoryPopover,
        },

        mixins: [prescriptionHandle],

        props: {
            postData: {
                type: Object,
                required: true,
            },
            verifyOutpatient: {
                type: Object,
                default: () => ({}),
            },

            disabled: Boolean,
            disabledAdd: Boolean,
            treatOnlineClinicId: [Number, String],
            switchSetting: {
                type: Object,
                default: () => {
                    return {
                        chineseSwitch: 1,
                        infusionSwitch: 1,
                        westernSwitch: 1,
                        externalSwitch: 1,
                        glassesSwitch: 1,
                        prescriptionTemplateSwitch: 1,
                    };
                },
            },
            // 是否需要验证库存
            needCheckStock: {
                type: Boolean,
                default: true,
            },
            /**
             是否展示医保登记
             */
            showMedicalFeeGrade: {
                type: Boolean,
                default: true,
            },
            selectedPatient: {
                type: Object,
            },
            // 判断开单来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            templateManagerVersion: {
                type: Number,
                default: 0,
            },
            showCooperationPharmacy: Boolean,
            isConsultation: Boolean,
            // 判断是否是处方模板
            isFromTemplate: {
                type: Boolean,
                default: false,
            },
        },

        setup() {
            const { handleChangePayType } = useOutpatientCommon();

            return {
                handleChangePayType,
            };
        },

        data() {
            return {
                TuiNaUsageSubTypeEnum,
                BaGuanUsageSubTypeEnum,
                AiJiuUsageSubTypeEnum,
                PrescriptionTypeEnum,
                ExternalPRUsageTypeEnum,
                TieFuUsageSubTypeEnum,
                ZhenCiUsageSubTypeEnum,
                commonPRDialogVisible: false,
                callMethodVisible: false,
                callInfusionMethodVisible: false,
                showOutpatientHistoryPopover: false,
                showExternalOptions: false,

                selectTemplateItem: {},
                // 就近一次操作中药处方的索引
                lastChineseChangeIndex: -1,
                // 就近一次操作输注处方的索引
                lastInfusionChangeIndex: -1,
            };
        },

        computed: {
            ...mapGetters('airPharmacy', ['clinicCanUseAirPharmacy']),
            ...mapGetters('virtualPharmacy', ['virtualPharmacyIsOpen']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
                'viewComponents',
                'featureAirPharmacy',
            ]),
            ...mapGetters([
                'chinesePRUsageDefault',
                'currentClinic',
                'goodsPriceInOutpatient',
                'pharmacyRuleList',
                'allDepartmentDoctors',
                'userInfo',
                'cooperationPharmacyList',
                'chainBasic',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig', 'featureSupportGlassesPrescription']),
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),

            // 定制--是否忽略外部处方的用法类型
            isIgnoreExternalUsageType() {
                return this.chainBasic.outpatient.isIgnoreExternalUsageType;
            },

            supportGlasses() {
                return this.featureSupportGlassesPrescription;
            },

            piecesDefaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList, {
                    departmentId: this.departmentId,
                    goodsInfo: {
                        typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                    },
                });
            },
            granuleDefaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList, {
                    departmentId: this.departmentId,
                    goodsInfo: {
                        typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE,
                    },
                });
            },

            currentSettingPopover() {
                return this.viewComponents.prescriptionSettingPopover;
            },

            /**
             * @desc 能否切换药房类型
             * 1. 当hospitalPatientOrderId有值代表长护门诊单，不能切换
             * <AUTHOR>
             * @date 2022-02-21 15:02:56
             */
            canChangePharmacyType() {
                return this.featureAirPharmacy && !this.postData.hospitalPatientOrderId;
            },

            prescriptionIds() {
                const {
                    prescriptionWesternForms = [], prescriptionInfusionForms = [],
                } = this.postData;
                const arr = [];
                prescriptionWesternForms && prescriptionWesternForms.forEach((item) => {
                    arr.push(item.keyId);
                });
                prescriptionInfusionForms && prescriptionInfusionForms.forEach((item) => {
                    arr.push(item.keyId);
                });

                const uniqueArr = [];
                arr.forEach((item) => {
                    const isExist = uniqueArr.find((it) => it === item);
                    if (!isExist) {
                        uniqueArr.push(item);
                    }
                });
                return uniqueArr;
            },

            /**
             * @desc 门诊允许医生查看药品价格(管理中设置)
             * <AUTHOR>
             * @date 2021/04/08 10:28:24
             */
            outpatientShowTotalPrice() {
                return [0, 1].indexOf(this.goodsPriceInOutpatient) > -1;
            },
            /**
             * @desc 门诊允许医生查看药品明细(管理中设置)
             * <AUTHOR>
             * @date 2021/04/08 10:28:24
             */
            outpatientShowDetailPrice() {
                return this.goodsPriceInOutpatient === 1;
            },

            noData() {
                return (
                    this.postData.prescriptionWesternForms.length === 0 &&
                    this.postData.prescriptionInfusionForms.length === 0 &&
                    this.postData.prescriptionChineseForms.length === 0 &&
                    this.postData.prescriptionExternalForms.length === 0 &&
                    this.postData.prescriptionGlassesForms?.length === 0
                );
            },

            currentSwitchSetting() {
                return Object.assign(
                    {},
                    {
                        chineseSwitch: 0,
                        infusionSwitch: 0,
                        westernSwitch: 0,
                        externalSwitch: 0,
                        glassesSwitch: 0,
                        prescriptionTemplateSwitch: 1,
                    },
                    this.switchSetting,
                );
            },
            recordCount() {
                let str = '';
                if (this.selectedPatient && this.selectedPatient.outPatientTimes) {
                    str = `(${this.selectedPatient.outPatientTimes})`;
                }
                return str;
            },

            // 是否支持添加中药处方
            showAddCNPR() {
                const { supportAddCNPR } = this.viewDistributeConfig.Outpatient;
                return this.currentSwitchSetting.chineseSwitch && supportAddCNPR;
            },

            // 是否支持添加外治处方
            showAddExternalPR() {
                const { supportAddExternalPR } = this.viewDistributeConfig.Outpatient;
                return this.currentSwitchSetting.externalSwitch && supportAddExternalPR;
            },
            /**
             * @desc 是否标记儿科
             * @desc 患者年龄≤14岁&&医生为儿科科室(科室类型为儿保科室 || 类型为门诊科室且诊疗科目为儿科/小儿外科及儿童保健科)
             * <AUTHOR>
             * @date 2022/12/06 15:59:25
             * @return {Boolean}
             */
            isChildTag() {
                const {
                    doctorId, departmentId,
                } = this.postData || {};
                const doctor = this.allDepartmentDoctors?.find((item) => {
                    return (
                        item.doctorId === doctorId &&
                        item.departmentId === departmentId
                    );
                });
                if (doctor) {
                    const {
                        departmentType, mainMedicalName,
                    } = doctor;
                    const { age = {} } = this.postData?.patient || {};
                    const {
                        year, month, day,
                    } = age || {};
                    return (
                        typeof year === 'number' &&
                        (year < 14 || (year === 14 && month === 0 && day === 0)) &&
                        (
                            departmentType === DepartmentTypeStatus.CHILD_HEALTH_TYPE ||
                            (
                                departmentType === DepartmentTypeStatus.OUT_PATIENT_TYPE &&
                                ['儿科', '小儿外科', '儿童保健科' ].includes(mainMedicalName)
                            )
                        )
                    );
                }
                return false;
            },
            departmentId() {
                return this.postData.departmentId;
            },

            isExistPrescription() {
                return this.postData.prescriptionWesternForms?.length > 0 ||
                    this.postData.prescriptionInfusionForms?.length > 0 ||
                    this.postData.prescriptionChineseForms?.length > 0 ||
                    this.postData.prescriptionExternalForms?.length > 0 ||
                    this.postData.prescriptionGlassesForms?.length > 0;
            },
        },

        created() {
            const {
                westernPrescriptionNameText,
                showHistoryPrescription,
            } = this.viewDistributeConfig.Outpatient;
            this._westernPrescriptionNameText = westernPrescriptionNameText;
            this._showHistoryPrescription = showHistoryPrescription;

            if (this.currentClinic) {
                this._key = `${this.currentClinic.clinicId}_${this.currentClinic.userId}`;
            }
            this.$store.dispatch('virtualPharmacy/initVirtualPharmacyConfig');
        },

        methods: {
            onClickChinesePrescription(index) {
                this.lastChineseChangeIndex = index;
            },
            onClickInfusionPrescription(index) {
                this.lastInfusionChangeIndex = index;
            },

            /**
             * @desc 添加项目
             * <AUTHOR>
             * @date 2019/03/28 12:04:42
             * @param type: 处方类型 1：西药，2：输液，3：中药, 4：外治, 5: 配镜
             * @param needScroll: 是否需要滚动到可视区域
             * @param prescriptionFormItems: 处方项目，默认[]
             * @param formInfo: 系统处方模板过来可能会加一些form上的数据 {
             *     doseCount,
             *     usage,
             *     dailyDosage,
             *     freq,
             *     usageLevel,
             *     usageDays,
             * }
             */
            addPR(type, needScroll = true, prescriptionFormItems = [], formInfo) {
                const keyId = createGUID();
                const psychotropicNarcoticType = this.isChildTag ?
                    PsychotropicNarcoticTypeEnum.CHILD :
                    undefined;
                switch (type) {
                    case PrescriptionTypeEnum.WESTERN: {
                        this.postData.prescriptionWesternForms.push({
                            keyId,
                            prescriptionFormItems,
                            psychotropicNarcoticType,
                            pharmacyType: null,
                            pharmacyNo: null,
                            pharmacyName: '',
                        });
                        break;
                    }
                    case PrescriptionTypeEnum.INFUSION: {
                        this.postData.prescriptionInfusionForms.push({
                            keyId,
                            prescriptionFormItems,
                            psychotropicNarcoticType,
                            pharmacyType: null,
                            pharmacyNo: null,
                            pharmacyName: '',
                        });
                        break;
                    }
                    case PrescriptionTypeEnum.CHINESE: {
                        let {
                            dailyDosage, freq, usageLevel, usage, usageDays,
                        } = this.chinesePRUsageDefault['煎服'];
                        let doseCount = '';

                        if (formInfo) {
                            doseCount = formInfo.doseCount;
                            usage = formInfo.usage;
                            dailyDosage = formInfo.dailyDosage;
                            freq = formInfo.freq;
                            usageLevel = formInfo.usageLevel;
                            usageDays = formInfo.usageDays;
                        }

                        let pharmacyType = PharmacyTypeEnum.LOCAL_PHARMACY;
                        let pharmacyNo = '';
                        let pharmacyName = '';
                        // isOpenSource为true则代表来自门诊或者儿保门诊
                        if (this.isOpenSource && (
                            this.clinicCanUseAirPharmacy ||
                            this.virtualPharmacyIsOpen ||
                            this.cooperationPharmacyList.length
                        )) {
                            pharmacyType = localStorage.getObj('outpatient_last_use_pharmacy_type', this._key, true);
                            pharmacyType = pharmacyType || PharmacyTypeEnum.LOCAL_PHARMACY;
                        }

                        // 快递信息默认赋值其它处方的
                        let deliveryInfo = null;
                        for (const item of this.postData.prescriptionChineseForms) {
                            if (item.deliveryInfo) {
                                deliveryInfo = item.deliveryInfo;
                                break;
                            }
                        }
                        const { prescription } = this.outpatientEmployeeConfig;
                        const { defaultOpenPrescription } = prescription || {};
                        const specification = defaultOpenPrescription === PrescriptionDefaultTypeEnum.CHINESE_GRANULE ? '中药颗粒' : '中药饮片';

                        if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                            if (specification === '中药颗粒') {
                                pharmacyNo = this.granuleDefaultPharmacy?.no ?? '';
                                pharmacyName = this.granuleDefaultPharmacy?.name || '';
                            } else {
                                pharmacyNo = this.piecesDefaultPharmacy?.no ?? '';
                                pharmacyName = this.piecesDefaultPharmacy?.name || '';
                            }
                        }

                        this.postData.prescriptionChineseForms.push({
                            keyId,
                            doseCount,
                            usage,
                            dailyDosage,
                            freq,
                            usageLevel,
                            usageDays,
                            requirement: '',
                            isDecoction: false,
                            contactMobile: '', // 电话
                            usageType: '', // 加工制法 type
                            usageSubType: '', // 加工制法 subType
                            processBagUnitCount: '', // 加工袋数
                            specification,
                            prescriptionFormItems,
                            psychotropicNarcoticType,

                            pharmacyType,
                            pharmacyNo,
                            pharmacyName,
                            usageScopeId: '',
                            medicineStateScopeId: '',
                            vendorId: '',
                            vendorUsageScopeId: '',
                            vendorName: '',
                            finishedRateMin: null,
                            finishedRate: null,
                            deliveryInfo,
                        });
                        break;
                    }
                    case PrescriptionTypeEnum.GLASSES: {
                        if (this.postData.prescriptionGlassesForms.length) return;
                        this.postData.prescriptionGlassesForms.push({
                            keyId,
                            psychotropicNarcoticType,
                            glassesType: GlassesMaterialTypeEnum.FRAME,
                            usage: '远用',
                            glassesParams: {
                                items: [],
                            },
                            requirement: '',
                            optometristId: this.userInfo.id,
                            optometristName: '',
                        });
                        break;
                    }
                    default:
                        console.warn('不支持的type类型', type);
                }

                if (needScroll) {
                    this.$nextTick(() => {
                        const el = document.querySelector(`#${CSS.escape(keyId)}`);
                        if (el) {
                            el.scrollIntoView({
                                block: 'nearest', behavior: 'smooth' , inline: 'nearest',
                            });
                        }
                    });
                }
            },

            /**
             * @desc 添加外治处方
             * <AUTHOR>
             * @date 2021-04-28 10:30:24
             * @params
             * @return
             */
            addExternalPR(usageType = ExternalPRUsageTypeEnum.tieFu, usageSubType = TieFuUsageSubTypeEnum.chenPinTie, needScroll = true) {
                if (!this.postData.prescriptionExternalForms) {
                    this.$set(this.postData, 'prescriptionExternalForms', []);
                }
                const usageTypeObj = localStorage.getObj('external_pr_last_use_usage_type', this._key, true);
                if (usageTypeObj && typeof usageTypeObj[usageType] === 'number') {
                    usageSubType = usageTypeObj[usageType];
                }
                const keyId = createGUID();
                const psychotropicNarcoticType = this.isChildTag ?
                    PsychotropicNarcoticTypeEnum.CHILD :
                    undefined;
                // 获取外治处方中上次修改时的物品类型(饮片/颗粒)
                let specification = null;
                if (usageType === ExternalPRUsageTypeEnum.tieFu && usageSubType === TieFuUsageSubTypeEnum.xianPeiTie) {
                    specification = localStorage.getObj('_PRESCRIPTION_SPECIFICATION_', 'external', true) || '中药饮片';
                }
                // 定制-忽略外治处方类型
                if (this.isIgnoreExternalUsageType) {
                    usageType = null;
                    usageSubType = null;
                    specification = null;
                }
                this.postData.prescriptionExternalForms.push({
                    keyId,
                    usageType,
                    usageSubType,
                    doseCount: 1,
                    prescriptionFormItems: [
                        {
                            sort: 1,
                            keyId: createGUID(),
                            goodsId: null,
                            name: '',
                            type: '',
                            subType: '',
                            unitPrice: '',
                            externalUnitCount: null,
                            freq: '1日1次',
                            externalGoodsItems: [],
                            acupoints: [
                                {
                                    id: null,
                                    name: '',
                                    type: 0,
                                    position: '-',
                                },
                            ],
                            unit: '',
                            unitCount: 0,
                            billingType: ExternalPRBillingTypeEnum.ACUPOINT,
                        },
                    ],
                    specification,
                    psychotropicNarcoticType,
                });
                this.showExternalOptions = false;
                if (needScroll) {
                    this.$nextTick(() => {
                        const el = document.querySelector(`#${CSS.escape(keyId)}`);
                        if (el) {
                            el.scrollIntoView({
                                block: 'nearest', behavior: 'smooth' , inline: 'nearest',
                            });
                        }
                    });
                }
            },

            addExternalPRSpecial() {
                if (this.isIgnoreExternalUsageType) {
                    this.addExternalPR();
                    return;
                }
                this.showExternalOptions = !this.showExternalOptions;
            },
            deleteWesternPrescription(index) {
                this.postData.prescriptionWesternForms.splice(index, 1);
                this.$emit('outpatient-verify');
                this.outpatientFeeChange();
            },

            deleteInfusionPrescription(index) {
                this.postData.prescriptionInfusionForms.splice(index, 1);
                if (this.lastInfusionChangeIndex > -1) {
                    this.lastInfusionChangeIndex--;
                }
                this.$emit('outpatient-verify');
                this.outpatientFeeChange();
            },

            deleteChinesePrescription(index) {
                this.postData.prescriptionChineseForms.splice(index, 1);
                if (this.lastChineseChangeIndex > -1) {
                    this.lastChineseChangeIndex--;
                }
                this.$emit('outpatient-verify');
                this.outpatientFeeChange();
            },
            deleteExternalPrescription(index) {
                this.postData.prescriptionExternalForms.splice(index, 1);
                this.$emit('outpatient-verify');
                this.outpatientFeeChange();
            },
            deleteGlassesPrescription(index) {
                this.postData.prescriptionGlassesForms.splice(index, 1);
            },

            /**
             * 判断开出的中西成药的抗菌等级是否满足开出条件
             * @param {Object} item
             * @return {boolean}
             */
            isAllowAdd(item) {
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                return isAllowAddByAntimicrobialDrugManagement(item, this.postData.doctorId, antimicrobialDrugManagementData, employeeListByPractice);
            },

            /**
             * 根据抗菌用药分级过滤掉不满足的item
             */
            filterByAntimicrobialDrugManagement(detail) {
                const wsFilterFormItems = [], infusionFilterFormItems = [], externalFilterItems = [];
                for (const wsForm of (detail.prescriptionWesternForms || [])) {
                    wsForm.prescriptionFormItems = (wsForm.prescriptionFormItems || []).filter((item) => {
                        const isSuccess = this.isAllowAdd(item);
                        if (!isSuccess) {
                            wsFilterFormItems.push(item);
                        }
                        return isSuccess;
                    });
                }
                for (const infusionForm of (detail.prescriptionInfusionForms || [])) {
                    infusionForm.prescriptionFormItems = (infusionForm.prescriptionFormItems || []).filter((item) => {
                        const isSuccess = this.isAllowAdd(item);
                        if (!isSuccess) {
                            infusionFilterFormItems.push(item);
                        }
                        return isSuccess;
                    });
                }
                for (const externalForm of (detail.prescriptionExternalForms || [])) {
                    externalForm.prescriptionFormItems = (externalForm.prescriptionFormItems || []).filter((item) => {
                        const isSuccess = this.isAllowAdd(item);
                        if (!isSuccess) {
                            externalFilterItems.push(item);
                        }
                        return isSuccess;
                    });
                }
                const filterFormItems = wsFilterFormItems.concat(infusionFilterFormItems).concat(externalFilterItems);
                if (filterFormItems.length) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: filterFormItems }).generateDialogAsync({ parent: this });
                    }, 100);
                }
            },

            /**
             * @desc 使用处方模板
             * <AUTHOR>
             * @date 2020/05/21 14:47:59
             */
            useTemplate(item) {
                this.clearBlankPrForms();
                const {
                    detail, category,
                } = item;

                // 根据抗菌用药分级过滤掉不满足的item
                this.filterByAntimicrobialDrugManagement(detail);

                const {
                    prescriptionChineseForms = [],prescriptionInfusionForms = [],
                } = detail || {};
                const hasChinesePr = prescriptionChineseForms.length;
                const hasInfusionPr = prescriptionInfusionForms.length;

                const hasUnChargeChineseForm = this.postData.prescriptionChineseForms.filter(
                    (form) => !form.chargeStatus,
                ).length;
                const hasUnChargeInfusionForm = this.postData.prescriptionInfusionForms.filter(
                    (form) => !form.chargeStatus,
                ).length;
                if ((category > 2 || hasChinesePr) && hasUnChargeChineseForm) {
                    const hasFormItem = this.postData.prescriptionChineseForms.filter(
                        (form) => form.prescriptionFormItems.filter((it) => it.goodsId || it.name).length,
                    ).length;
                    if (hasFormItem) {
                        // 合并汤头，只针对 category=3|4, 经典方剂，临床验方 || 有中药处方
                        this.selectTemplateItem = item;
                        this.callMethodVisible = true;
                    } else {
                        this.confirmUseTemplate(item, true);
                    }
                } else if (hasInfusionPr && hasUnChargeInfusionForm) {
                    const hasFormItem = this.postData.prescriptionInfusionForms.filter(
                        (form) => form.prescriptionFormItems.filter((it) => it.goodsId || it.name).length,
                    ).length;
                    if (hasFormItem) {
                        // 合并输注处方，只针对有输注处方且未收费
                        this.selectTemplateItem = item;
                        this.callInfusionMethodVisible = true;
                    } else {
                        this.confirmUseTemplate(item, true);
                    }
                } else {
                    this.commonPRDialogVisible = false;
                    this.confirmUseTemplate(item, false);
                }
            },
            /**
             * @desc 确认使用处方模板
             * <AUTHOR>
             * @date 2020/05/21 14:48:50
             */
            async confirmUseTemplate(template, isMerge) {
                const { id } = template;

                await this.westernPRHandle(template);
                await this.infusionPRHandle(template, isMerge);
                await this.chinesePRHandle(template, isMerge);

                this.$emit('outpatient-verify');
                await this.applyTemplate(id);
                this.outpatientFeeChange();
            },

            /**
             * @desc 对模板的处方form进行处理, 并收集goodsId
             * <AUTHOR> Yang
             * @date 2020-09-18 16:28:55
             */
            templateFormHandle(templateForms, goodsIdsArray) {
                if (templateForms) {
                    templateForms.forEach((form) => {
                        form.keyId = createGUID();
                        delete form.id;
                        form.prescriptionFormItems = form.prescriptionFormItems.map((item) => {
                            if (item.goodsId && goodsIdsArray.indexOf(item.goodsId) === -1) {
                                goodsIdsArray.push(item.goodsId);
                            }
                            item.fadeOut = false;
                            item.specialRequirement = item.specialRequirement || '';
                            item.specialRequirement = item.specialRequirement.replace(/【自备】/g, '');
                            item.payType = null;
                            delete item.chargeId;
                            delete item.chargedId;
                            delete item.refundId;
                            delete item.id;
                            delete item.prescriptionId;
                            delete item.stockPackageCount;
                            delete item.stockPieceCount;
                            return item;
                        });
                    });
                }
            },

            /**
             * @desc 对模板中的西药处方
             * <AUTHOR> Yang
             * @date 2020-09-18 16:34:54
             */
            async westernPRHandle(template) {
                const { detail } = template;
                let {
                    prescriptionWesternForms = [],
                    prescriptionExternalForms = [],
                } = detail;

                const goodsIdsArray = [];

                this.templateFormHandle(prescriptionWesternForms, goodsIdsArray);
                this.templateFormHandle(prescriptionExternalForms, goodsIdsArray);
                // 请求 处方模板中的库存  获取药品的库存信息
                let stockGoodsArray = [];
                // 查询输液和西药库存
                if (goodsIdsArray.length) {
                    const { data } = await SettingsAPI.commonPrescription.fetchPrescriptionTemplateStock({
                        goodsIds: goodsIdsArray,
                        withShebaoCode: 1,
                        clinicId: this.treatOnlineClinicId || undefined,
                        sceneType: SearchSceneTypeEnum.outpatient,
                        departmentId: this.postData.departmentId || undefined,
                    });
                    stockGoodsArray = (data && data.list) || [];
                }

                // 使用新库存 覆盖 处方模板中的库存
                prescriptionWesternForms = this.updatePrescriptionForm(prescriptionWesternForms, stockGoodsArray);
                // 选择的模板加入到 postDta 中
                if (prescriptionWesternForms && prescriptionWesternForms.length) {
                    this.postData.prescriptionWesternForms = this.postData.prescriptionWesternForms.concat(
                        prescriptionWesternForms,
                    );
                }

                prescriptionExternalForms.forEach((form) => {
                    if (this.isIgnoreExternalUsageType) {
                        form.usageType = null;
                        form.usageSubType = null;
                        form.specification = null;
                    }
                    form.prescriptionFormItems.forEach((item) => {
                        if (!item.acupoints.find((it) => !it.id && !it.name)) {
                            item.acupoints.push({
                                id: null,
                                name: '',
                                type: 0,
                                position: '单',
                            });
                        }
                    });
                });
                prescriptionExternalForms = this.updatePrescriptionForm(
                    prescriptionExternalForms,
                    stockGoodsArray,
                    SourceFormTypeEnum.PRESCRIPTION_EXTERNAL,
                );
                if (prescriptionExternalForms && prescriptionExternalForms.length) {
                    this.postData.prescriptionExternalForms = this.postData.prescriptionExternalForms.concat(
                        prescriptionExternalForms,
                    );
                }
            },

            /**
             * @desc 对模板中的中药处方进行处理
             * <AUTHOR> Yang
             * @date 2020-09-18 16:34:13
             */
            async chinesePRHandle(template, isMerge) {
                const {
                    ownerType, detail,
                } = template;
                const { prescriptionChineseForms = [] } = detail;

                prescriptionChineseForms.forEach((form) => {
                    form.keyId = createGUID();
                    form.pharmacyType = form.pharmacyType || PharmacyTypeEnum.LOCAL_PHARMACY;
                    delete form.id;
                    form.specification = form.specification || '中药饮片';
                    this.$set(form, 'usage', form.usage || '');
                    this.$set(form, 'dailyDosage', form.dailyDosage || '');
                    this.$set(form, 'freq', form.freq || '');
                    this.$set(form, 'usageLevel', form.usageLevel || '');
                    this.$set(form, 'usageDays', form.usageDays || '');
                    this.$set(form, 'requirement', form.requirement || '');
                    this.$set(form, 'specialRequirement', form.specialRequirement || '');

                    form.prescriptionFormItems.forEach((item) => {
                        item.keyId = createGUID();
                        item.name = item.medicineCadn || item.name;
                        item.fadeOut = false;
                        this.$set(item, 'stockPackageCount', 0);
                        this.$set(item, 'stockPieceCount', 0);
                        this.$set(item, 'specialRequirement', item.specialRequirement || '');
                        item.payType = null;
                        delete item.chargeId;
                        delete item.chargedId;
                        delete item.refundId;
                        delete item.id;
                        delete item.prescriptionId;
                    });
                });

                if (prescriptionChineseForms.length) {
                    // 合并处方（exp：添加汤头）
                    if (isMerge) {
                        this.mergeChinesePrescription(prescriptionChineseForms);
                    } else {
                        await this.updateChineseFormGoodsInfo(prescriptionChineseForms);

                        // 系统中药处方没有用法等信息，直接走新建流程
                        if (ownerType === OwnerType.SYSTEM) {
                            prescriptionChineseForms.forEach((form) => {
                                this.addPR(3, true, form.prescriptionFormItems, {
                                    dailyDosage: form.dailyDosage,
                                    doseCount: form.doseCount,
                                    freq: form.freq,
                                    usage: form.usage,
                                    usageLevel: form.usageLevel,
                                    usageDays: form.usageDays,
                                });
                            });
                        } else {
                            this.postData.prescriptionChineseForms = this.postData.prescriptionChineseForms
                                .filter((pc) => {
                                    return (
                                        pc.prescriptionFormItems &&
                                        pc.prescriptionFormItems.filter((item) => {
                                            return item.name;
                                        }).length > 0
                                    );
                                })
                                .concat(prescriptionChineseForms);
                        }
                    }
                    prescriptionChineseForms.forEach((form) => {
                        form.prescriptionFormItems.forEach((item) => {
                            this.$emit('add-prescription-form-item', item);
                        });
                    });
                }
            },

            /**
             * @desc 对模板中的输注处方进行处理
             * <AUTHOR>
             * @date 2023-12-26 11:03:58
             */
            async infusionPRHandle(template,isMerge) {
                const {
                    detail,
                } = template;
                const { prescriptionInfusionForms = [] } = detail;
                const goodsIdsArray = [];
                this.templateFormHandle(prescriptionInfusionForms, goodsIdsArray);
                if (prescriptionInfusionForms.length) {
                    //合并或者更新输注处方
                    await this.updateInfusionPrescription(prescriptionInfusionForms,goodsIdsArray,isMerge);
                }
            },

            /**
             * desc [增加模板使用次数]
             */
            async applyTemplate(templateId) {
                if (!templateId) return false;
                try {
                    await OutpatientAPI.applyTemplate('prescription', templateId);
                } catch (error) {
                    console.log('fetchTemplateUsedCount error', error);
                }
            },

            /**
             * desc [当点击调用处理]
             */
            onCallConfirm(method) {
                this.callMethodVisible = false;
                this.commonPRDialogVisible = false;
                this.confirmUseTemplate(this.selectTemplateItem, method === 'merge');
            },

            /**
             * desc [当输注处方点击调用处理]
             */
            onInfusionCallConfirm(method) {
                this.callInfusionMethodVisible = false;
                this.commonPRDialogVisible = false;
                this.confirmUseTemplate(this.selectTemplateItem, method === 'merge');
            },

            /**
             * desc [多个中药汤头，组合成一个处方]
             * 1、处方模板中有多个处方，需要先合并，相同药品需要去掉一个，
             * 2、模板中药品与门诊处方单药品重复，保留门诊处方单的药品信息
             * 3、合并到就近一次编辑的门诊处方
             */
            async mergeChinesePrescription(chineseForms) {
                const index =
                    this.lastChineseChangeIndex === -1 ?
                        this.postData.prescriptionChineseForms.length - 1 :
                        this.lastChineseChangeIndex;
                const mergeForm = this.postData.prescriptionChineseForms[index];

                // 将输入位清空
                mergeForm.prescriptionFormItems = mergeForm.prescriptionFormItems.filter((item) => {
                    return item.name;
                });
                /**
                 * @desc 需要更新本地药房的商品信息
                 * <AUTHOR> Yang
                 * @date 2020-07-15 18:46:45
                 */
                if (mergeForm.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                    await this.updateChineseFormGoodsInfo(chineseForms, {
                        specification: mergeForm.specification,
                        pharmacyNo: mergeForm.pharmacyNo,
                        pharmacyType: mergeForm.pharmacyType,
                    });
                }

                chineseForms.forEach((cForm) => {
                    cForm?.prescriptionFormItems?.forEach((item) => {
                        const existIndex = mergeForm.prescriptionFormItems.findIndex((it) => it.goodsId === item.goodsId);
                        if (existIndex === -1 || !item.goodsId) {
                            mergeForm.prescriptionFormItems.push(item);
                        }
                    });
                });

                // 之前清空了中药输入位，此时需要添加
                mergeForm.prescriptionFormItems.push({
                    unit: 'g',
                    goodsId: null,
                    medicineCadn: '',
                    name: '',
                    specialRequirement: '',
                    unitCount: '',
                });

                const lastForm = chineseForms[chineseForms.length - 1];
                Object.assign(mergeForm, {
                    dailyDosage: lastForm.dailyDosage || mergeForm.dailyDosage,
                    doseCount: lastForm.doseCount || mergeForm.doseCount,
                    freq: lastForm.freq || mergeForm.freq,
                    requirement: lastForm.requirement || mergeForm.requirement,
                    usage: lastForm.usage || mergeForm.usage,
                    usageLevel: lastForm.usageLevel || mergeForm.usageLevel,
                });

                this.$nextTick(() => {
                    /**
                     * @desc  添加完成后 空中药房更新商品信息
                     * <AUTHOR> Yang
                     * @date 2020-07-15 19:36:32
                     */
                    if (mergeForm.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY ||
                        mergeForm.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                        const $form = this.$children.find((child) => {
                            return child.form && child.form.keyId === mergeForm.keyId;
                        });
                        $form.$refs['vendor-popover'].fetchVendors();
                    }
                });
            },

            /**
             * @desc [多个输注处方，组合成一个处方]
             */
            async updateInfusionPrescription(infusionForms,goodsIdsArray,isMerge = false) {
                // 请求 处方模板中的库存  获取药品的库存信息
                let stockGoodsArray = [];
                // 查询输液和西药库存
                if (goodsIdsArray.length) {
                    const { data } = await SettingsAPI.commonPrescription.fetchPrescriptionTemplateStock({
                        goodsIds: goodsIdsArray,
                        withShebaoCode: 1,
                        clinicId: this.treatOnlineClinicId || undefined,
                        sceneType: SearchSceneTypeEnum.outpatient,
                        departmentId: this.postData.departmentId || undefined,
                    });
                    stockGoodsArray = (data && data.list) || [];
                }
                // 使用新库存 覆盖 处方模板中的库存
                infusionForms = this.updatePrescriptionForm(infusionForms, stockGoodsArray);

                if (infusionForms && infusionForms.length) {
                    if (isMerge) {
                        //需要合并 找到上一次点击过的输注组index
                        const index =
                            this.lastInfusionChangeIndex === -1 ?
                                this.postData.prescriptionInfusionForms.length - 1 :
                                this.lastInfusionChangeIndex;
                        const mergeForm = this.postData.prescriptionInfusionForms[index];
                        const maxMergeFormGroupId = mergeForm.prescriptionFormItems.reduce((maxId, item) => Math.max(maxId, item.groupId), null);
                        infusionForms.forEach((cForm) => {
                            cForm?.prescriptionFormItems?.forEach((item) => {
                                if (item.goodsId) {
                                    item.groupId = item.groupId + maxMergeFormGroupId;
                                    mergeForm.prescriptionFormItems.push(item);
                                }
                            });
                        });
                        this.$refs.infusionPrescriptions[index].transGroupList();
                    } else {
                        this.postData.prescriptionInfusionForms = this.postData.prescriptionInfusionForms.concat(
                            infusionForms,
                        );
                    }
                }
            },
            /**
             * @desc 中医智能诊断点击方药后新增处方
             * <AUTHOR>
             * @date 2020/05/11 14:20:59
             */
            async copyPRFromSidebar(data) {
                await this.updateChineseFormGoodsInfo([data]);

                // 清掉空处方
                this.postData.prescriptionChineseForms = this.postData.prescriptionChineseForms.filter((form) => {
                    return form.prescriptionFormItems.filter((item) => {
                        return item.name;
                    }).length;
                });

                this.addPR(3, true, data.prescriptionFormItems);
                this.$emit('outpatient-verify');
            },

            selectPrescription(data) {
                this.$emit('select-outpatient-record', data, true);
            },

            /**
             * @desc 设置切换默认处方类型
             * <AUTHOR>
             * @date 2022-03-03 16:18:49
             */
            changeDefaultPr(defaultOpenPrescription) {
                this.clearBlankPrForms();
                if (!this.noData) return;
                switch (defaultOpenPrescription) {
                    case PrescriptionDefaultTypeEnum.WESTERN:
                        this.addPR(PrescriptionTypeEnum.WESTERN, false);
                        break;
                    case PrescriptionDefaultTypeEnum.CHINESE_PIECES: // 中药-饮片
                    case PrescriptionDefaultTypeEnum.CHINESE_GRANULE: // 中药-颗粒
                        this.addPR(PrescriptionTypeEnum.CHINESE, false);
                        break;
                    case PrescriptionDefaultTypeEnum.INFUSION:
                        this.addPR(PrescriptionTypeEnum.INFUSION, false);
                        break;
                    case PrescriptionDefaultTypeEnum.GLASSES:
                        this.addPR(PrescriptionTypeEnum.GLASSES, false);
                        break;
                    default:
                        console.log('选择无默认，不做操作');
                        break;
                }
            },

            /**
             * @desc 清空空处方
             * <AUTHOR>
             * @date 2022-03-03 16:23:53
             */
            clearBlankPrForms() {
                this.postData.prescriptionWesternForms = this.postData.prescriptionWesternForms.filter((form) => {
                    return form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
                });
                this.postData.prescriptionInfusionForms = this.postData.prescriptionInfusionForms.filter((form) => {
                    return form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
                });
                this.postData.prescriptionChineseForms = this.postData.prescriptionChineseForms.filter((form) => {
                    return form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
                });
                this.postData.prescriptionExternalForms = this.postData.prescriptionExternalForms.filter((form) => {
                    return form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
                });
                this.postData.prescriptionGlassesForms = this.postData.prescriptionGlassesForms.filter((form) => {
                    return form.optometristId || form.requirement || form.glassesParams?.items.some((x) => x.leftEyeValue || x.rightEyeValue);
                });
            },

            /**
             * @desc 门诊单内容变更
             * <AUTHOR>
             * @date 2022/06/06 10:47:44
             */
            outpatientFeeChange() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '删除处方',
                    needCalcFee: true,
                });
            },

            handlerClickPrHistory() {
                this.showOutpatientHistoryPopover = !this.showOutpatientHistoryPopover;
                Logger.report({
                    scene: 'outpatient.pr.history',
                });
            },

            onChangePayType(val, item, form) {
                this.handleChangePayType(val, item, form);
                this.outpatientFeeChange();
                this.$emit('outpatient-verify');
            },
        },
    };
</script>
