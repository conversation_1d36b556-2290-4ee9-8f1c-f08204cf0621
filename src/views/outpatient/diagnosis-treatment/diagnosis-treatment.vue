<template>
    <div
        class="outpatient-form-item outpatient-diagnosis-treatment"
        :class="{
            'is-disabled': disabled || (disabledAdd && (!productForms.length || allFormItemIsDisabled)),
            'no-data': productForms.length === 0,
            'has-item-remark': hasItemRemark,
            'no-table-body': productForms.length === 0 && disabled
        }"
        data-cy="诊疗项目"
    >
        <div class="title" style="padding-right: 7px;">
            <h3 class="outpatient-related-product-recommend-wrapper">
                诊疗项目
            </h3>
            <div v-if="relatedProductCount && !disabled && !disabledAdd">
                <related-product-recommend-popover
                    v-model="showRelatedProduct"
                    :prescription-forms="prescriptionForms"
                    :existed-products="existedProducts"
                    @add-product="batchSelect"
                    @product-count-change="handleRelatedProductCountChange"
                    @rest-product-change="handleRestProductChange"
                >
                    <div class="add-related-product-btn-wrapper">
                        <abc-popover trigger="hover" placement="top-start">
                            <span slot="reference">
                                <abc-link @click="handleShowRelatedProducts">
                                    关联项目({{ relatedProductCount }})
                                </abc-link>
                            </span>
                        </abc-popover>
                    </div>
                </related-product-recommend-popover>
            </div>

            <div class="btn-group">
                <abc-space v-if="!disabled && !disabledAdd" :size="6">
                    <template v-if="isEnableAiSearch">
                        <abc-popover
                            theme="yellow"
                            placement="top"
                            size="large"
                            trigger="hover"
                            :disabled="aiGoodsSuggestionResStatus === AIGoodsSuggestionStatusEnum.loading"
                            visible-arrow
                            :popper-style="{ padding: 0 }"
                            class="diagnosis-treatment-ai-suggestion-popover"
                        >
                            <div slot="reference">
                                <abc-text
                                    v-if="aiGoodsList.length && aiGoodsSuggestionResStatus === AIGoodsSuggestionStatusEnum.success"
                                    theme="success"
                                    style="cursor: pointer;"
                                    size="small"
                                    class="diagnosis-treatment-ai-suggestion-reference ellipsis"
                                    tag="div"
                                    :title="`推荐：${aiGoodsList[0].displayName}，${aiGoodsList[0].purpose}`"
                                >
                                    推荐：{{ aiGoodsList[0].displayName }}，{{ aiGoodsList[0].purpose }}
                                </abc-text>
                                <abc-flex
                                    v-if="aiGoodsSuggestionResStatus"
                                    align="center"
                                    gap="4"
                                    justify="flex-end"
                                >
                                    <template v-if="aiGoodsSuggestionResStatus === AIGoodsSuggestionStatusEnum.loading">
                                        <abc-loading-spinner gray size="tiny"></abc-loading-spinner>
                                        <abc-text size="small" theme="gray">
                                            推荐思考中...
                                        </abc-text>
                                    </template>
                                    <template v-if="aiGoodsSuggestionResStatus === AIGoodsSuggestionStatusEnum.fail">
                                        <abc-icon icon="s-alert-triangle-line" size="14" color="var(--abc-color-T3)"></abc-icon>
                                        <abc-text size="small" theme="gray">
                                            推荐失败
                                        </abc-text>
                                    </template>
                                    <template v-if="aiGoodsSuggestionResStatus === AIGoodsSuggestionStatusEnum.empty">
                                        <abc-icon icon="s-info-circle-line" size="14" color="var(--abc-color-T3)">
                                        </abc-icon>
                                        <abc-text size="small" theme="gray">
                                            暂无推荐项目
                                        </abc-text>
                                    </template>
                                </abc-flex>
                            </div>
                            <abc-flex
                                :class="[{
                                    'diagnosis-treatment-ai-suggestion-list': aiGoodsSuggestionResStatus === AIGoodsSuggestionStatusEnum.success,
                                    'diagnosis-treatment-ai-suggestion-empty': aiGoodsSuggestionResStatus !== AIGoodsSuggestionStatusEnum.success
                                }]"
                                gap="0"
                                vertical
                            >
                                <abc-flex
                                    justify="flex-start"
                                    align="center"
                                    gap="small"
                                    style="width: 100%; padding: 9px 16px; border-bottom: 1px solid var(--abc-color-P8, #eaedf1);"
                                >
                                    <abc-icon icon="s-abcai-color"></abc-icon>
                                    <abc-text size="normal" theme="black" bold>
                                        AI 推荐
                                    </abc-text>
                                </abc-flex>

                                <abc-flex vertical gap="14" style="padding: 12px 16px 16px;">
                                    <template v-if="aiGoodsList.length">
                                        <abc-flex
                                            v-for="(item, index) in aiGoodsList"
                                            :key="`${item.id}-${index}`"
                                            justify="space-between"
                                            gap="16"
                                            align="center"
                                        >
                                            <abc-text
                                                size="normal"
                                                tag="div"
                                                style="max-width: 536px;"
                                                class="ellipsis"
                                            >
                                                <abc-text bold>
                                                    {{ item.displayName }}：
                                                </abc-text>
                                                {{ item.purpose }}
                                            </abc-text>
                                            <accept-button @click="acceptAIGoods(item)"></accept-button>
                                        </abc-flex>
                                    </template>
                                    <abc-flex
                                        v-if="aiGoodsSuggestionResStatus === AIGoodsSuggestionStatusEnum.empty"
                                        align="center"
                                        gap="4"
                                        style="margin-bottom: 24px;"
                                    >
                                        <abc-icon icon="s-info-circle-line" color="var(--abc-color-T3)"></abc-icon>
                                        <abc-text theme="gray">
                                            暂无检查检验项目推荐
                                        </abc-text>
                                    </abc-flex>
                                    <abc-flex
                                        v-if="aiGoodsSuggestionResStatus === AIGoodsSuggestionStatusEnum.fail"
                                        align="center"
                                        gap="4"
                                        style="margin-bottom: 24px;"
                                    >
                                        <abc-icon icon="s-alert-triangle-line" color="var(--abc-color-T3)"></abc-icon>
                                        <abc-text theme="gray">
                                            推荐失败，编辑病历重新分析
                                        </abc-text>
                                    </abc-flex>
                                    <abc-divider variant="dashed" margin="none"></abc-divider>
                                    <abc-flex>
                                        <abc-text size="mini" theme="gray">
                                            * AI 推荐仅供参考，临床决策需医生综合判断
                                        </abc-text>
                                    </abc-flex>
                                </abc-flex>
                            </abc-flex>
                        </abc-popover>
                    </template>

                    <div v-if="isEnableAiSearch && aiGoodsSuggestionResStatus" class="cut-line" style="margin-left: 2px;"></div>

                    <abc-button
                        v-if="currentSwitchSetting.inspection"
                        variant="ghost"
                        theme="default"
                        size="small"
                        data-cy="diagnosis-treatment-inspect-btn"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.INSPECTION)"
                    >
                        检查
                    </abc-button>
                    <abc-button
                        v-if="currentSwitchSetting.examination"
                        variant="ghost"
                        theme="default"
                        size="small"
                        data-cy="diagnosis-treatment-examination-btn"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.ASSAY)"
                    >
                        检验
                    </abc-button>
                    <abc-button
                        v-if="currentSwitchSetting.treatment"
                        variant="ghost"
                        theme="default"
                        size="small"
                        data-cy="diagnosis-treatment-treatment-btn"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.TREATMENT)"
                    >
                        治疗
                    </abc-button>
                    <abc-button
                        v-if="featureSupportFilterEyeGlasses && currentSwitchSetting.eyeglasses"
                        variant="ghost"
                        theme="default"
                        size="small"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.EYEGLASSES)"
                    >
                        眼镜
                    </abc-button>
                    <abc-button
                        v-if="supportAddNursing && currentSwitchSetting.nursing"
                        variant="ghost"
                        theme="default"
                        size="small"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.NURSE)"
                    >
                        护理
                    </abc-button>
                    <abc-button
                        v-if="isSupportSurgery && currentSwitchSetting.surgery"
                        variant="ghost"
                        theme="default"
                        size="small"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.SURGERY)"
                    >
                        手术
                    </abc-button>
                    <abc-button
                        v-if="currentSwitchSetting.material"
                        variant="ghost"
                        theme="default"
                        size="small"
                        data-cy="diagnosis-treatment-material-btn"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.MATERIAL)"
                    >
                        耗材
                    </abc-button>
                    <abc-button
                        v-if="currentSwitchSetting.materialGoods"
                        variant="ghost"
                        theme="default"
                        size="small"
                        data-cy="diagnosis-treatment-goods-btn"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.PRODUCT)"
                    >
                        商品
                    </abc-button>
                    <abc-button
                        v-if="currentSwitchSetting.compose"
                        variant="ghost"
                        theme="default"
                        size="small"
                        data-cy="diagnosis-treatment-compose-btn"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.COMPOSE)"
                    >
                        套餐
                    </abc-button>
                    <abc-button
                        v-if="supportAddOtherFee && currentSwitchSetting.other"
                        variant="ghost"
                        theme="default"
                        size="small"
                        data-cy="diagnosis-treatment-other-btn"
                        @click="openGoodsDialog(CATEGORY_TYPE_ENUM.OTHER)"
                    >
                        其他
                    </abc-button>
                </abc-space>
                <recommend-popover
                    v-if="!disabled || isOpenSource"
                    v-model="selectedButton"
                    :clinic-id="clinicId"
                    :medical-record="medicalRecord"
                    :patient-info="patientInfo"
                    class="recommend-popper-wrapper"
                    style="margin-left: auto;"
                    @select="quickSelect"
                >
                    <template v-if="currentSwitchSetting.settingSwitch">
                        <div v-if="hasCutLine && !disabled && !disabledAdd" class="cut-line"></div>
                        <abc-button
                            v-if="currentSwitchSetting.templateSwitch && !disabled && !disabledAdd"
                            variant="ghost"
                            theme="default"
                            size="small"
                            style="margin-left: 6px;"
                            icon="s-b-data-1-line"
                            data-cy="diagnosis-treatment-template-btn"
                            @click="templateDialogVisible = true"
                        >
                            诊疗模板
                        </abc-button>
                        <abc-check-access :permission-keys="[ROLE_DOCTOR_ID, ROLE_DOCTOR_ASSIST_ID]">
                            <abc-button
                                v-if="currentSwitchSetting.templateSwitch && isOpenSource"
                                data-cy="diagnosis-treatment-save-btn"
                                icon="save"
                                variant="text"
                                theme="default"
                                size="small"
                                class="diagnosis-treatment-save-btn"
                                style="margin-left: 6px;"
                                @click="saveCommon"
                            >
                            </abc-button>
                        </abc-check-access>
                        <diagnosis-treatment-setting-popover
                            v-if="!disabled && !disabledAdd"
                            class="diagnosis-treatment-more-setting"
                            style="margin-left: 6px;"
                            data-cy="diagnosis-treatment-more-setting"
                        ></diagnosis-treatment-setting-popover>
                    </template>
                </recommend-popover>
            </div>
        </div>

        <ul v-if="productForms.length || !disabled" class="goods-item-list">
            <template v-for="(form, formIndex) in productForms">
                <li
                    v-for="(item, index) in form.productFormItems"
                    :id="item.id || item.keyId"
                    :key="`${item.id || item.keyId || index }${ index}${item.productId}`"
                    :class="{ 'is-disabled': disabledItem(item, form) }"
                    class="goods-item"
                    :data-cy="getItemDataCy(productForms, item.id || item.keyId || item.productId)"
                >
                    <tooth-selector
                        v-if="productFormCanAddToothNo"
                        v-model="item.toothNos"
                        :disabled="disabled || !!disabledItem(item, form)"
                        :fixed="fixed"
                        :tooth-selector-width="toothSelectorWidth"
                        :quick-select-mode="needCheckStock ? 'copy' : undefined"
                        :copy-tooth-nos-info="copyToothNosInfo"
                    ></tooth-selector>
                    <div v-else class="index">
                        {{ item.index }}
                    </div>

                    <div class="name">
                        <abc-form-item
                            v-if="currentEditIndex === item.index || !item.name"
                            required
                            style="width: 100%;"
                        >
                            <goods-autocomplete
                                ref="goodsAutocomplete"
                                version="3"
                                :clinic-id="clinicId"
                                class="goods-autocomplete-wrapper"
                                :default-keyword="item.name"
                                :placeholder="placeholderStr(item)"
                                focus-show
                                :suggestion-items="_suggestionItems"
                                inner-width="850px"
                                :suggestion-titles="_suggestionGoodsTitles"
                                :json-type="jsonType(item)"
                                :query-v3-params="commonQueryParams"
                                icon-name=""
                                data-cy="item-goods-autocomplete"
                                is-support-manufacturer-filter
                                :selected-manufacturer="selectedManufacturerGoods"
                                :create-manufacturer-options="createManufacturerOptionsGoods"
                                :custom-filter-suggestions="filterManufacturerGoods"
                                :clear-manufacturer-data="clearManufacturerDataGoods"
                                @closePanel="closePanel"
                                @blur="($autocomplete) => blurHandle($autocomplete, item)"
                                @selectGoods="(goods) => selectGoods(goods, item)"
                                @dblclick.native="handleDoubleClick"
                            ></goods-autocomplete>
                        </abc-form-item>

                        <div
                            v-else
                            class="name-describe"
                            data-cy="item-name"
                            @click="editProduct(item, form)"
                        >
                            <compose-popper
                                v-if="item.type === 11"
                                class="ellipsis"
                                :disabled="disabled"
                                :show-tag="false"
                                :treatment="item"
                            >
                                <form-item-status v-if="form.chargeStatus" :item="item"></form-item-status>
                            </compose-popper>

                            <div
                                v-else-if="displayGoodsView(item)"
                                v-abc-goods-hover-popper:remote="{
                                    goods: item,
                                    onlyStock: true,
                                    pharmacyNo: item.pharmacyNo,
                                    openDelay: 300,
                                    showShebaoCode: true,
                                    showCostPrice: canViewCostPrice,
                                }"
                                class="material-goods"
                                :class="{
                                    'abc-tipsy abc-tipsy--n': showWarnTips(item, form),
                                    'no-stock': showWarnTips(item, form),
                                }"
                                :data-tipsy="warnTips(item, form)"
                            >
                                <span class="name-text ellipsis">
                                    {{ item.name }}
                                </span>

                                <span class="spec ellipsis" style="display: flex; gap: 6px; align-items: center;">
                                    <span class="ellipsis">{{ item.productInfo | getSpec }}</span>

                                    <!--库存不足提示-->
                                    <abc-icon
                                        v-if="showWarnTips(item, form)"
                                        icon="n-alert-fill"
                                        :color="$store.state.theme.style.Y2"
                                    ></abc-icon>

                                    <form-item-status v-if="form.chargeStatus" :item="item"></form-item-status>
                                </span>
                            </div>
                            <abc-flex
                                v-else
                                v-abc-goods-hover-popper="{
                                    goods: item,
                                    openDelay: 300,
                                    showShebaoCode: true,
                                    showCostPrice: canViewCostPrice,
                                    showGenderLimited: isLimitedGoods(item)
                                }"
                                :class="{
                                    'abc-tipsy abc-tipsy--n': isDisabledGoods(item, form),
                                }"
                                data-tipsy="项目已被停用或删除"
                                :gap="6"
                                align="center"
                            >
                                <span class="ellipsis ellipsis-goods-name">
                                    {{ item.name }}
                                </span>

                                <abc-icon
                                    v-if="isDisabledGoods(item, form)"
                                    icon="n-alert-fill"
                                    :color="$store.state.theme.style.Y2"
                                ></abc-icon>

                                <abc-icon
                                    v-if="isLimitedGoods(item, form) && !isDisabledGoods(item, form)"
                                    icon="n-alert-fill"
                                    :color="$store.state.theme.style.R1"
                                ></abc-icon>

                                <form-item-status v-if="form.chargeStatus" :item="item"></form-item-status>

                                <!--云检标签-->
                                <biz-exam-business-tag
                                    is-cloud-tag
                                    :cloud-supplier-flag="item.productInfo?.cloudSupplierFlag"
                                ></biz-exam-business-tag>

                                <!--外包标签-->
                                <biz-exam-business-tag
                                    is-out-sourcing-tag
                                    :coop-flag="item.productInfo?.coopFlag"
                                    :type="item.productInfo?.type"
                                    :sub-type="item.productInfo?.subType"
                                ></biz-exam-business-tag>
                            </abc-flex>
                        </div>
                    </div>

                    <template v-if="!isFromTemplate && hasExaminationGoods(item).length">
                        <template v-if="hasResultExaminations(item.examinationResult).length">
                            <div class="view" style="margin-right: 20px;">
                                <abc-link
                                    v-if="hasResultExaminations(item.examinationResult).length === 1"
                                    data-cy="item-查看报告"
                                    @click="viewReport(hasResultExaminations(item.examinationResult)[0])"
                                >
                                    报告智能解读
                                </abc-link>
                                <abc-dropdown v-else @change="viewReport">
                                    <div slot="reference" style="display: inline-block;">
                                        <abc-button
                                            variant="text"
                                            size="small"
                                            data-cy="item-查看报告"
                                        >
                                            报告智能解读
                                        </abc-button>
                                    </div>
                                    <abc-dropdown-item
                                        v-for="eResult in hasResultExaminations(item.examinationResult)"
                                        :key="eResult.examinationSheetId"
                                        :label="eResult.name"
                                        :value="eResult"
                                    >
                                    </abc-dropdown-item>
                                </abc-dropdown>
                            </div>
                        </template>

                        <template v-if="notFinishExaminationGoods(item).length && supportExamApplySheetView">
                            <div class="view" style="margin-right: 20px;">
                                <abc-button
                                    v-if="notFinishExaminationGoods(item).length === 1"
                                    variant="text"
                                    size="small"
                                    @click="viewApplySheet(item)"
                                >
                                    申请单
                                </abc-button>
                                <abc-dropdown v-else @change="viewApplySheet">
                                    <div slot="reference" style="display: inline-block;">
                                        <abc-button variant="text" size="small">
                                            申请单
                                        </abc-button>
                                    </div>
                                    <abc-dropdown-item
                                        v-for="goods in notFinishExaminationGoods(item)"
                                        :key="goods.id"
                                        :label="goods.name"
                                        :value="goods"
                                    >
                                    </abc-dropdown-item>
                                </abc-dropdown>
                            </div>
                        </template>
                    </template>

                    <template v-if="isSupportSurgery && !isFromTemplate && hasSurgeryGoods(item).length">
                        <div class="view" style="margin-right: 20px;">
                            <abc-button
                                v-if="isSurgery(item)"
                                variant="text"
                                size="small"
                                @click="handleOpenSurgeryDetailDialog(item)"
                            >
                                记录单
                            </abc-button>
                            <abc-dropdown v-else-if="isCompose(item)" @change="handleOpenSurgeryDetailDialog">
                                <div slot="reference" style="display: inline-block;">
                                    <abc-button variant="text" size="small">
                                        记录单
                                    </abc-button>
                                </div>
                                <abc-dropdown-item
                                    v-for="goods in hasSurgeryGoods(item)"
                                    :key="goods.id"
                                    :label="goods.name"
                                    :value="goods"
                                >
                                </abc-dropdown-item>
                            </abc-dropdown>
                        </div>
                    </template>

                    <!--固定表格宽度，防止表格上下错位-->
                    <div class="table-td" style="width: 60px; height: 100%;">
                        <medical-fee-grade-td
                            v-if="displayMedicalFeeGrade(item)"
                            :item="item"
                            :shebao-card-info="shebaoCardInfo"
                            :show-pay-type-select="showPayTypeSelect"
                            :disabled="!!(disabled || disabledItem(item, form))"
                            :style="tableStyleConfig.feeGrade"
                            :width="60"
                            @change-pay-type="val => onChangePayType(val, item, form)"
                            @enter="enterEvent"
                        >
                        </medical-fee-grade-td>
                    </div>

                    <div v-if="!featureSupportRatioPrice" class="goods-type" data-cy="item-type">
                        {{ goodsTypeStr(item) }}
                    </div>

                    <template v-if="hasNeedDayCol">
                        <div v-if="needFreq" class="freq">
                            <abc-form-item v-if="needDays(item)">
                                <abc-select
                                    v-model="item.freq"
                                    :disabled="!!(disabled || disabledItem(item, form))"
                                    :index="index"
                                    :show-value="item.freq"
                                    adaptive-width
                                    :input-style="{
                                        padding: '3px 6px', textAlign: 'center'
                                    }"
                                    class="no-border-input count-center"
                                    data-cy="select-freq"
                                    focus-show-options
                                    no-icon
                                    placeholder="频率"
                                    @enter="enterEvent"
                                    @change="val => selectFreq(val, item)"
                                >
                                    <abc-option
                                        v-for="it in freqList"
                                        :key="it.id"
                                        :data-cy="`option-freq-${it.label}`"
                                        :label="it.label"
                                        :value="it.label"
                                    >
                                    </abc-option>
                                </abc-select>
                            </abc-form-item>
                        </div>
                        <div class="dosage">
                            <template v-if="needDays(item)">
                                <template v-if="disabled || disabledItem(item, form)">
                                    <div style=" display: inline-block; width: 47px; text-align: center;">
                                        {{ item.dailyDosage }}
                                    </div>
                                    <span
                                        class="unit"
                                        :class="{
                                            'small-font': item.unit && item.unit.length >= 4
                                        }"
                                        :title="item.unit"
                                    >{{ item.unit && item.unit.substr(0,4) || '' }}</span>
                                </template>

                                <template v-else>
                                    <abc-form-item>
                                        <abc-input
                                            v-model.number="item.dailyDosage"
                                            v-abc-focus-selected
                                            class="count-center"
                                            :width="47"
                                            :placeholder="dailyDosagePlaceholder"
                                            type="number"
                                            :config="{ max: 999 }"
                                            data-cy="item-dailyDosage"
                                            @input="changeDosageDays(item)"
                                            @enter="enterEvent"
                                        >
                                        </abc-input>
                                    </abc-form-item>
                                    <span
                                        class="unit"
                                        :class="{
                                            'small-font': item.unit && item.unit.length >= 4
                                        }"
                                        :title="item.unit"
                                    >{{ item.unit && item.unit.substr(0,4) || '' }}</span>
                                </template>
                            </template>
                        </div>
                        <div class="days">
                            <abc-form-item v-if="needDays(item)">
                                <div v-if="disabled || disabledItem(item, form)" class="is-readonly">
                                    {{ item.days }}
                                </div>

                                <abc-input
                                    v-else
                                    v-model="item.days"
                                    v-abc-focus-selected
                                    :width="58"
                                    :input-custom-style="{ padding: '3px 24px 3px 3px' }"
                                    margin="0"
                                    type="number"
                                    :config="{ max: 999 }"
                                    class="count-center"
                                    data-cy="item-days"
                                    @enter="enterEvent"
                                    @input="changeDosageDays(item)"
                                >
                                </abc-input>
                                <span class="input-append-unit">天</span>
                            </abc-form-item>
                        </div>
                    </template>

                    <div class="unit-count" :style="tableStyleConfig.unitCount">
                        <template v-if="disabled || disabledItem(item, form)">
                            <div
                                style=" display: inline-block; width: 47px; text-align: center;"
                                :style="tableStyleConfig.count"
                            >
                                {{ item.unitCount }}
                            </div>

                            <span
                                class="unit"
                                :class="{
                                    'small-font': item.unit && item.unit.length >= 4
                                }"
                                :style="tableStyleConfig.unit"
                                :title="item.unit"
                            >{{ item.unit && item.unit.substr(0,4) || '' }}</span>
                        </template>

                        <template v-else>
                            <abc-form-item required :style="tableStyleConfig.count">
                                <abc-input
                                    v-model.number="item.unitCount"
                                    v-abc-focus-selected
                                    class="count-center"
                                    type="number"
                                    placeholder="总量"
                                    :config="{
                                        max: 99999,
                                        supportZero: true,
                                        formatLength: getUnitCountFormatLength(item),
                                    }"
                                    data-cy="item-unitCount"
                                    @input="changeCount(item)"
                                    @enter="enterEvent"
                                >
                                </abc-input>
                            </abc-form-item>

                            <abc-form-item :style="tableStyleConfig.unit">
                                <abc-select
                                    v-if="item.type === 2 || item.type === 7"
                                    v-model="item.unit"
                                    :title="item.unit"
                                    adaptive-width
                                    class="input-select"
                                    custom-class="prescription-select-options"
                                    :index="index"
                                    :tabindex="-1"
                                    data-cy="item-unit"
                                    @change="changeUnit(item)"
                                >
                                    <abc-option
                                        v-for="it in unitArray(item)"
                                        :key="it.name"
                                        :label="it.name"
                                        :value="it.name"
                                    >
                                    </abc-option>
                                </abc-select>
                                <span
                                    v-else
                                    class="unit"
                                    :style="tableStyleConfig.unit"
                                    :class="{
                                        'small-font': item.unit && item.unit.length >= 4
                                    }"
                                    :title="item.unit"
                                >{{ item.unit && item.unit.substr(0,4) || '' }}</span>
                            </abc-form-item>
                        </template>
                    </div>
                    <!--折扣-->
                    <div
                        v-if="featureSupportRatioPrice && showPrice && isOpenSource"
                        class="ratio"
                        :style="tableStyleConfig.ratio"
                    >
                        <abc-form-item>
                            <price-radio-popover
                                :disabled="!!(disabled || disabledItem(item, form) || readonlyItem(item))"
                                @change-price-radio="val => changePriceRadio(item, val)"
                            >
                                <abc-input
                                    v-abc-focus-selected
                                    :value="displayTotalPriceRatio(item)"
                                    class="count-center"
                                    placeholder="折扣"
                                    :disabled="!!(disabled || disabledItem(item, form))"
                                    type="money"
                                    :config="{
                                        supportZero: true,
                                        max: 100
                                    }"
                                    data-cy="item-ratio"
                                    :readonly="readonlyItem(item)"
                                    @input="val => inputPriceRadioHandler(item, val)"
                                    @enter="enterEvent"
                                    @change="changeTotalPriceRatio(item)"
                                >
                                    <span slot="appendInner">%</span>
                                </abc-input>
                            </price-radio-popover>
                        </abc-form-item>
                    </div>
                    <!--总价-->
                    <div v-if="showPrice" class="total" :style="tableStyleConfig.total">
                        <template v-if="!featureSupportRatioPrice || !isOpenSource || readonlyItem(item)">
                            <span>{{ getTotalPrice(item) }}</span>
                        </template>
                        <template v-else>
                            <abc-form-item required>
                                <price-adjustment-popover :item="item">
                                    <abc-input
                                        v-model="item.totalPrice"
                                        v-abc-focus-selected
                                        class="count-center"
                                        type="money"
                                        :disabled="!!(disabled || disabledItem(item, form))"
                                        :tabindex="-1"
                                        :input-custom-style="{
                                            textAlign: 'right !important',
                                            paddingRight: '10px',
                                        }"
                                        :config="{
                                            formatLength: 2,
                                            supportZero: true,
                                            max: 9999999
                                        }"
                                        data-cy="item-total-price"
                                        @enter="enterEvent"
                                        @change="changeTotalPrice(item)"
                                    ></abc-input>
                                </price-adjustment-popover>
                            </abc-form-item>
                        </template>
                    </div>

                    <template v-if="chargeItemSupportDoctorNurse">
                        <div
                            class="doctor-info"
                            :title="`${item.doctorName || '' } - ${ item.departmentName || ''}`"
                            :style="tableStyleConfig.doctorInfo"
                        >
                            <div v-if="disabled || disabledItem(item, form)" class="is-readonly ellipsis">
                                {{ item.doctorName }}
                            </div>
                            <department-doctor-autocomplete
                                v-else
                                :department-id.sync="item.departmentId"
                                :department-name.sync="item.departmentName"
                                :doctor-id.sync="item.doctorId"
                                :doctor-name.sync="item.doctorName"
                                :display-department-name="false"
                                :target-doctor-id="userInfo?.id"
                                class="no-border-input"
                                :disabled="disabled"
                                placeholder="医生"
                            ></department-doctor-autocomplete>
                        </div>
                        <div class="nurse-info" :style="tableStyleConfig.nurseInfo">
                            <div v-if="disabled || disabledItem(item, form)" class="is-readonly ellipsis" :title="item.nurseName">
                                {{ item.nurseName }}
                            </div>
                            <nurse-autocomplete
                                v-else
                                :nurse-id.sync="item.nurseId"
                                :nurse-name.sync="item.nurseName"
                                class="no-border-input"
                                :disabled="disabled"
                                placeholder="护士"
                            ></nurse-autocomplete>
                        </div>
                    </template>

                    <div class="item-remark" :title="item.remark" :style="tableStyleConfig.remark">
                        <goods-remark
                            v-if="isMaterial(item) || isGoods(item) || isEyeGlasses(item)"
                            ref="medicineRemarks"
                            v-model="item.remark"
                            class="no-border-input"
                            placeholder="备注"
                            max-length="50"
                            placement="bottom-start"
                            :readonly="false"
                            :disabled="!!(disabled || disabledItem(item, form))"
                            support-tag
                            :pr-form-item="item"
                            :department-id="curDepartmentId"
                            :tabindex="-1"
                            :show-medicine-source-options="needCheckStock"
                            @focus="() => handleFocusRemark('medicineRemarks')"
                            @blur="() => handleBlurRemark(item)"
                            @enter="enterEvent"
                            @changePharmacySource="outpatientFeeChange"
                            @change="outpatientFeeChange"
                        >
                        </goods-remark>

                        <template v-else>
                            <div v-if="disabled || disabledItem(item, form)" class="ellipsis readonly-remark">
                                {{ item.remark || '' }}
                            </div>
                            <abc-input
                                v-else
                                :ref="goodsItemKey(formIndex,index)"
                                v-model="item.remark"
                                :max-length="200"
                                :input-custom-style="currentFocusItem === goodsItemKey(formIndex,index) ? {
                                    'padding-right': '28px'
                                } : {}"
                                class="no-border-input"
                                placeholder="备注"
                                data-cy="item-remark"
                                @focus="() => handleFocusRemark(goodsItemKey(formIndex,index))"
                                @blur="handleBlurRemark"
                                @change="outpatientFeeChange"
                            >
                                <div
                                    v-if="showDTRemarkMeridian && currentFocusItem === goodsItemKey(formIndex,index)"
                                    slot="append"
                                    class="input-meridian-wrapper"
                                    data-cy="item-remark-meridian"
                                    @mousedown.prevent.stop="handleClickRemarkAcupoint(item)"
                                >
                                    <div class="icon-meridian-wrapper">
                                        <abc-icon icon="meridian"></abc-icon>
                                    </div>
                                </div>
                            </abc-input>
                        </template>
                    </div>

                    <div v-if="!disabledItem(item, form) && !disabled" class="delete-item">
                        <delete-icon data-cy="item-delete" type="dark" @delete="deleteGoods(formIndex, index)"></delete-icon>
                    </div>
                </li>
            </template>

            <li class="search-autocomplete">
                <tooth-selector
                    v-if="!disabled && productFormCanAddToothNo && !disabledAdd"
                    v-model="defaultToothNos"
                    :tooth-selector-width="toothSelectorWidth"
                    :disabled="disabled"
                    :fixed="fixed"
                    :quick-select-mode="needCheckStock ? 'copy' : undefined"
                    :copy-tooth-nos-info="copyToothNosInfo"
                ></tooth-selector>
                <abc-form-item v-if="!disabled && !disabledAdd" style="flex: 1;">
                    <goods-autocomplete
                        ref="defaultSearch"
                        data-cy="diagnosis-treatment-goods-autocomplete"
                        version="3"
                        :clinic-id="clinicId"
                        size="medium"
                        :icon-size="16"
                        class="goods-autocomplete-wrapper"
                        :custom-class="featureSupportFilterEyeGlasses ? 'has-filter' : ''"
                        placeholder="输入项目名或拼音码"
                        focus-show
                        inner-width="1000px"
                        :suggestion-items="_suggestionItems"
                        :suggestion-titles="_suggestionTitles"
                        :json-type="jsonType4Search"
                        :query-v3-params="commonQueryParams"
                        :icon-name="productFormCanAddToothNo ? '' : 's-add-line-medium'"
                        :department-id="curDepartmentId"
                        :filter-cascader-func="filterCascaderFunc"
                        :glasses-form="glassesForm"
                        open-search-filter
                        is-support-manufacturer-filter
                        :selected-manufacturer="selectedManufacturer"
                        :create-manufacturer-options="createManufacturerOptions"
                        :custom-filter-suggestions="filterManufacturer"
                        :clear-manufacturer-data="clearManufacturerData"
                        @focus="focusHandler()"
                        @blur="($autocomplete) => blurHandle($autocomplete)"
                        @selectGoods="(goods) => singleSelectGoods(goods)"
                    ></goods-autocomplete>
                </abc-form-item>

                <span class="total-price">
                    <form-status v-if="disabled" :forms="productForms"></form-status>
                    <template v-if="showPrice">
                        <span v-if="!disabled"><abc-money
                            :value="productTotalPrice"
                            data-cy="diagnosis-treatment-total-price"
                            is-show-space
                        ></abc-money></span>
                        <biz-charge-stat-popover
                            v-else
                            title="诊疗项目"
                            :forms="productForms"
                        >
                            <abc-money
                                :value="productTotalPrice"
                                is-show-space
                                data-cy="diagnosis-treatment-total-price"
                            ></abc-money>
                        </biz-charge-stat-popover>
                    </template>
                </span>
            </li>
        </ul>

        <div>
            <!--这个div千万不能删除，原因咨询jason-->
        </div>

        <!--检查报告-->
        <report-detail-dialog
            v-if="showReport"
            v-model="showReport"
            :patient-id="patientInfo?.id"
            :examination-id="examinationSheetId"
            :type="curExamType"
        ></report-detail-dialog>

        <!--选择诊疗模板-->
        <d-t-template-dialog
            v-if="templateDialogVisible"
            v-model="templateDialogVisible"
            :owner-type="3"
            :version="templateManagerVersion"
            @useTemplate="useTemplate"
        ></d-t-template-dialog>

        <goods-select-dialog
            v-if="showGoodsSelectDialog"
            v-model="showGoodsSelectDialog"
            :default-category-key="currentCategoryKey"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="curDepartmentId"
            :category-range="categoryRange"
            :glasses-form="glassesForm"
            @onSelectGoods="quickSelect"
        ></goods-select-dialog>

        <exam-apply-sheet-dialog
            v-if="examApplySheetDialogVisible"
            v-model="examApplySheetDialogVisible"
            :form="applySheetInfo"
            :disabled="disabled"
            :is-un-diagnosis="isUnDiagnosis"
            :patient-order-no="patientOrderNo"
            :patient-order-id="patientOrderId"
            :current-select-sub-type="currentApplySheetSubType"
            :device-type="currentApplySheetDeviceType"
            :is-ophthalmology="currentApplySheetIsOphthalmology"
            @ok="handleApplySheetOnOk"
        ></exam-apply-sheet-dialog>

        <component
            :is="curCommonDialog"
            v-if="showAddDialog"
            v-model="showAddDialog"
            :source-data="postData"
            template-type="diagnosis-treatment"
            title="保存为诊疗模板"
        ></component>
    </div>
</template>

<script>
    import SettingAPI from 'api/settings';
    import AiAPI from 'api/ai';

    import GoodsAutocomplete from 'src/views/layout/goods-autocomplete/goods-autocomplete';
    import DeleteIcon from 'views/layout/delete-icon/delete-icon';
    import ComposePopper from 'src/views/layout/compose-popper/compose-popper';
    import RecommendPopover from './recommend-popover';
    import DiagnosisTreatmentSettingPopover from 'src/views/outpatient/common/outpatient-setting-popover/diagnosis-treatment-setting-popover';
    const DTTemplateDialog = () => import('../common/diagnosis-treatment-template-dialog');
    import ToothSelector from '@/views-dentistry/outpatient/common/medical-record/tooth-selector.vue';
    import DepartmentDoctorAutocomplete from 'views/layout/department-doctor-autocomplete/index';
    import NurseAutocomplete from 'views/layout/nurse-autocomplete/index';
    import GoodsRemark from 'views/layout/goods-remark/index.vue';
    const ReportDetailDialog = () => import('src/views/outpatient/common/report-detail-dialog.vue');
    import ExamApplySheetDialog from 'src/views/outpatient/diagnosis-treatment/exam-apply-sheet-dialog/index.vue';
    import GoodsSelectDialog from 'src/views/layout/goods-select-dialog/index.vue';
    import BizChargeStatPopover from '@/components-composite/biz-charge-stat-popover';
    import FormStatus from 'src/views/outpatient/common/form-status.vue';
    import FormItemStatus from 'src/views/outpatient/common/form-item-status.vue';
    import { handleClickRemarkIcon } from 'views/common/acupoint-selection';
    import MedicalFeeGradeTd from 'src/views/layout/prescription/common/medical-fee-grade-td.vue';
    import {
        getDeepseekAcceptButton,
    } from '@/module-federation-dynamic/deepseek';

    import prescriptionHandle from '../mixins/prescription-handle';
    import { CATEGORY_TYPE_ENUM } from 'src/views/common/goods-search/constants';
    import {
        ROLE_DOCTOR_ASSIST_ID,
        ROLE_DOCTOR_ID,
    } from 'utils/constants';

    import {
        isShortage, isDisabledGoods,
    } from 'utils/validate';
    import { mapGetters } from 'vuex';
    import {
        checkCanShowDeviceFlag, createGUID,
    } from 'utils/index';
    import {
        medicalFeeGradeFormatStr,
    } from 'src/filters/index';
    import { goodsHoverTitle } from 'src/filters/goods.js';
    import { ChargeItemStatusEnum } from '@/service/charge/constants';

    import {
        GoodsTypeEnum,
        GoodsSubTypeEnum,
        ExecuteStatus,
        FeeComposeTypeEnum,
        GoodsTypeIdEnum,
    } from '@abc/constants';

    import GoodsAPI from 'api/goods';
    import { SourceFormTypeEnum } from '@/service/charge/constants';
    import {
        itemCountPrice, sum,
    } from 'utils/calculation.js';
    import {
        getOutpatientToothNosInfo, getToothNosInfo,
    } from '@/views/outpatient/common/medical-record/utils.js';
    import {
        keepLastIndex, optimizePopoverPosition,
    } from 'utils/dom.js';
    import RelatedProductRecommendPopover from '@/views/layout/prescription/infusion/dialog/related-product-recommend-popover.vue';
    import {
        getUsageParams, getTypeGoodsList,
    } from 'src/views/layout/prescription/utils.js';
    import templateUseMethodDialog from './dialog-template-use-method/index.js';
    import CdssAPI from 'api/cdss';
    import {
        clearItemBargainHandler,
        formatSurgeryDetail,
        getItemUnitPrice,
        hasSurgeryGoods,
        isCompose,
        isSurgery,
        isAllowAddByAntimicrobialDrugManagement,
        getItemComposeChildren,
    } from 'views/outpatient/utils.js';
    import { OutpatientStatusEnum } from '../constants';
    import { SearchSceneTypeEnum } from 'views/common/enum.js';
    import CommonAddTemplateDialog from 'views/layout/templates-manager/common-add-template-dialog';
    import CommonAddTemplateHospitalDialog from 'views/layout/templates-manager/common-add-template-hospital-dialog';
    import { SurgeryDetailDialog } from 'src/views/outpatient/common/surgery-detail-dialog';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import BizExamBusinessTag from 'src/components-composite/biz-exam-business-tag';
    import { ExternalFreqOptions } from 'src/views/layout/prescription/constant.js';
    import BizGoodsInfoTagGroup from 'src/components-composite/biz-goods-info-tag-group';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';
    import ManufacturerSelect from 'views/inventory/common/manufacturer-select/index.vue';
    import useAutoCompleteManufacturerSelect from 'views/inventory/common/manufacturer-select';
    import useOutpatientCommon from '@/hooks/business/use-outpatient-common/index';


    export default {
        name: 'DiagnosisTreatment',
        components: {
            BizGoodsInfoTagGroup,
            BizExamBusinessTag,
            GoodsAutocomplete,
            DeleteIcon,
            ComposePopper,
            RecommendPopover,
            DiagnosisTreatmentSettingPopover,
            DTTemplateDialog,
            ToothSelector,
            RelatedProductRecommendPopover,
            DepartmentDoctorAutocomplete,
            NurseAutocomplete,
            GoodsRemark,
            ReportDetailDialog,
            GoodsSelectDialog,
            ExamApplySheetDialog,
            MedicalFeeGradeTd,
            BizChargeStatPopover,
            FormStatus,
            FormItemStatus,
            AbcLoadingSpinner,
            AcceptButton: () => ({
                // eslint-disable-next-line no-async-promise-executor
                component: new Promise(async (resolve) => {
                    const module = await getDeepseekAcceptButton();
                    resolve(module);
                }),
            }),
        },
        mixins: [prescriptionHandle],

        props: {
            postData: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            forms: {
                type: Array,
                required: true,
                default: () => {
                    return [];
                },
            },
            fixed: {
                type: Boolean,
                default: false,
            },
            prescriptionForms: {
                type: Array,
                default: () => [],
            },
            disabled: Boolean,
            disabledAdd: Boolean,
            needCheckStock: {
                type: Boolean,
                default: true,
            },
            medicalRecord: Object,
            patientInfo: Object,
            treatOnlineClinicId: [String, Number],
            switchSetting: {
                type: Object,
                default: () => {
                    return {
                        examination: 1,
                        treatment: 1,
                        other: 1,
                        materialGoods: 1,
                        compose: 1,
                        templateSwitch: 1,
                        settingSwitch: 1,
                        inspection: 1,
                        material: 1,
                        // todo lxl 门诊开手术 诊疗项目设置
                        surgery: 1, // 手术
                    };
                },
            },
            ownerType: {
                type: Number,
                default: 3,
            },

            showPrice: {
                type: Boolean,
                default: true,
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },

            // 医生科室信息，只读
            doctorInfo: {
                type: Object,
                default: () => {},
            },
            canAddTooth: {
                type: Boolean,
                default: false,
            },
            supportDoctorNurse: {
                type: Boolean,
                default: false,
            },
            showRemarkMeridian: {
                type: Boolean,
                default: true,
            },
            templateManagerVersion: {
                type: Number,
                default: 0,
            },
            patientOrderNo: {
                type: Number,
                default: undefined,
            },
            patientOrderId: {
                type: String,
                default: undefined,
            },
            status: {
                type: Number,
                default: 0,
            },
            /**
             是否展示医保登记
             */
            showMedicalFeeGrade: {
                type: Boolean,
                default: true,
            },
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            isFromTemplate: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            } = useAutoCompleteManufacturerSelect();

            const {
                selectedManufacturer: selectedManufacturerGoods,
                manufacturerOptions: manufacturerOptionsGoods,
                createManufacturerOptions: createManufacturerOptionsGoods,
                filterManufacturer: filterManufacturerGoods,
                clearManufacturerData: clearManufacturerDataGoods,
            } = useAutoCompleteManufacturerSelect();

            const { handleChangePayType } = useOutpatientCommon();

            return {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,

                selectedManufacturerGoods,
                manufacturerOptionsGoods,
                createManufacturerOptionsGoods,
                filterManufacturerGoods,
                clearManufacturerDataGoods,
                handleChangePayType,
            };
        },

        data() {
            return {
                ROLE_DOCTOR_ID,
                ROLE_DOCTOR_ASSIST_ID,
                CATEGORY_TYPE_ENUM,
                GoodsTypeIdEnum,
                examinationKeyword: '',
                treatmentKeyword: '',

                selectedButton: 0,
                showReport: false,
                templateDialogVisible: false,
                examinationSheetId: '',
                examinationType: null,
                currentEditIndex: -1,
                currentFocusItem: null,
                expandRemark: false,
                defaultToothNos: [],
                relatedProductCount: null,
                showRelatedProduct: false,
                relatedProducts: [],

                examApplySheetDialogVisible: false,
                currentApplySheetSubType: GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test,
                currentApplySheetDeviceType: undefined,

                showGoodsSelectDialog: false,
                currentCategoryKey: undefined,
                currentApplySheetIsOphthalmology: false,
                showAddDialog: false,

                curExamType: '',
                cacheMedicalRecord: {
                    chiefComplaint: '',
                    presentHistory: '',
                },
                aiGoodsList: [],
                aiGoodsSuggestionResStatus: 0,
            };
        },

        computed: {
            ...mapGetters([
                'userInfo',
                'clinicConfig',
                'currentClinic',
                'infusionRelatedConfigs',
                'clinicDoctors',
                'clinicNurseList',
                'isCanSeeGoodsCostPriceInOutpatient', // 医生是否可以看到成本价
                'examinationProjectCanMerge', // 检验项目是否开启合并配置
                'inspectProjectCanMerge', // 检查项目是否开启合并配置
                'isEnableListingPrice',
                'isIntranetUser',
                'chargeConfig',
            ]),
            ...mapGetters('viewDistribute', [
                'featureTherapy',
                'featureCompose',
                'featureFeeCompose',
                'viewDistributeConfig',
                'featureSupportFilterEyeGlasses',
                'featureSupportRatioPrice',
            ]),
            ...mapGetters('examination', ['isEnableCloudSupplier' ]),
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustOutpatientPrice',
            ]),
            isShowGoodsAutocompleteShortId() {
                return this.viewDistributeConfig.Inventory.isShowGoodsAutocompleteShortId;
            },
            // 单项议价权限: 开启了医生可单项议价&&当前医生有权限
            canSingleBargain() {
                return !!this.chargeConfig.doctorSingleBargainSwitch && this.employeeCanAdjustOutpatientPrice;
            },

            isEnableAiSearch() {
                return this.isEnableCloudSupplier && !this.isIntranetUser;
            },

            curDepartmentId() {
                return this.doctorInfo?.departmentId || '';
            },

            /**
             * 医生能否查看成本价
             */
            canViewCostPrice() {
                return this.isCanSeeGoodsCostPriceInOutpatient;
            },
            /**
             * @desc 能否选择 是否自费 payType
             * <AUTHOR>
             * @date 2021-07-19 18:23:14
             */
            showPayTypeSelect() {
                return (
                    this.$abcSocialSecurity.isOpenSocial &&
                    (
                        this.$abcSocialSecurity.config.isZhejiang ||
                        this.$abcSocialSecurity.config.isFujianFuzhou ||
                        this.$abcSocialSecurity.config.isLiaoningPanjin
                    )
                );
            },

            canUseCompose() {
                return this.featureCompose && this.currentSwitchSetting.compose;
            },

            chargeItemSupportDoctorNurse() {
                if (!this.needCheckStock) return false;
                return this.viewDistributeConfig.chargeItemSupportDoctorNurse || this.supportDoctorNurse;
            },

            curNurseList() {
                return this.clinicNurseList.map((item) => {
                    return {
                        id: item.employeeId,
                        name: item.employeeName,
                    };
                });
            },

            categoryRange() {
                return this.viewDistributeConfig?.GoodsSearch?.outpatientCategoryRange || [];
            },

            outpatientVDConfig() {
                return this.viewDistributeConfig?.Outpatient || {};
            },
            tableStyleConfig() {
                return this.outpatientVDConfig.tableStyleConfig.diagnosisTable;
            },
            productFormCanAddToothNo() {
                return this.outpatientVDConfig.productFormCanAddToothNo || this.canAddTooth;
            },
            showDTRemarkMeridian() {
                return this.outpatientVDConfig.showDTRemarkMeridian || this.showRemarkMeridian;
            },
            showDTDayCol() {
                return this.outpatientVDConfig.showDTDayCol;
            },
            supportAddOtherFee() {
                return this.outpatientVDConfig.supportAddOtherFee;
            },
            isSupportSurgery() {
                return this.outpatientVDConfig.isSupportSurgery;
            },
            supportExamApplySheetView() {
                return this.outpatientVDConfig.supportExamApplySheetView;
            },

            supportAddNursing() {
                return this.outpatientVDConfig.supportAddNursing;
            },
            treatmentBtnText() {
                if (this.featureTherapy) return '治疗理疗';
                return '治疗项目';
            },

            existedProducts() {
                const { forms = [] } = this;
                const arr = [];
                forms && forms.forEach((item) => {
                    item && item.productFormItems.forEach((product) => {
                        arr.push(product);
                    });
                });
                return arr;
            },

            currentSwitchSetting() {
                return Object.assign(
                    {},
                    {
                        inspection: 1,
                        material: 1,
                        examination: 1,
                        treatment: 1,
                        other: 1,
                        materialGoods: 1,
                        compose: 1,
                        // todo lxl 门诊开手术 诊疗项目设置
                        surgery: 1, // 手术
                        templateSwitch: 1,
                        settingSwitch: 1,
                        supportInputDays: 1,
                    },
                    this.switchSetting,
                );
            },
            hasCutLine() {
                return (
                    this.currentSwitchSetting.examination ||
                    this.currentSwitchSetting.treatment ||
                    this.currentSwitchSetting.other ||
                    this.currentSwitchSetting.materialGoods ||
                    this.currentSwitchSetting.compose
                );
            },
            productForms() {
                let index = 0;
                this.forms.forEach((form) => {
                    form.productFormItems.forEach((item) => {
                        index += 1;
                        item.index = index;
                    });
                });
                return this.forms;
            },

            hasNeedDayCol() {
                if (!this.showDTDayCol) return false;
                if (!this.currentSwitchSetting.supportInputDays) return false;

                return this.productForms.some((form) => {
                    return form.productFormItems.filter((item) => {
                        return this.needDays(item);
                    }).length;
                });
            },

            needFreq() {
                return this.currentSwitchSetting.supportInputDays === 1;
            },

            dailyDosagePlaceholder() {
                if (this.needFreq) {
                    return '每次';
                }
                return '每天';
            },

            clinicId() {
                return this.treatOnlineClinicId || (this.currentClinic && this.currentClinic.clinicId);
            },

            jsonType4Search() {
                const _arr = [
                    { type: GoodsTypeEnum.EXAMINATION },
                    { type: GoodsTypeEnum.TREATMENT },
                    { type: GoodsTypeEnum.MATERIAL },
                    { type: GoodsTypeEnum.GOODS },
                ];
                if (this.supportAddOtherFee) {
                    _arr.push({ type: GoodsTypeEnum.OTHER });
                }
                if (this.supportAddNursing) {
                    _arr.push({ type: GoodsTypeEnum.NURSE });
                }
                if (this.featureCompose) {
                    _arr.push({ type: GoodsTypeEnum.COMPOSE });
                }
                if (this.featureSupportFilterEyeGlasses) {
                    _arr.push({ type: GoodsTypeEnum.EYEGLASSES });
                }
                if (this.isSupportSurgery) {
                    _arr.push({
                        type: GoodsTypeEnum.SURGERY,
                        subType: [
                            GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY,
                        ],
                    });
                }
                return _arr;
            },
            commonQueryParams() {
                const {
                    sex, age,
                } = this.patientInfo || {};
                const {
                    chiefComplaint, diagnosis,
                } = this.medicalRecord || {};

                const params = {
                    clinicId: this.clinicId,
                    sceneType: SearchSceneTypeEnum.outpatient,
                    departmentId: this.doctorInfo?.departmentId,
                    sex,
                    age,
                    chiefComplaint,
                    diagnosis,
                    offset: 0,
                    limit: 50,
                };
                // 模板中用汇总
                // https://www.tapd.cn/43780818/bugtrace/bugs/view?bug_id=1143780818001064703
                if (!this.needCheckStock) {
                    params.queryStockOrderType = 40;
                }
                return params;
            },
            hasItemRemark() {
                return this.currentFocusItem || this.productForms.some((form) => {
                    return form.productFormItems.filter((it) => {
                        return it.remark;
                    }).length > 0;
                });
            },
            productTotalPrice() {
                let total = 0;
                const _arr = [];
                this.productForms.forEach((form) => {
                    form.productFormItems.forEach((item) => {
                        const {
                            chargeStatus,
                            receivableFee,
                            receivedPrice,
                            refundedFee,
                        } = item;
                        // 全收（包含退单、退费、部分退）显示实收，未收、部分收显示原价
                        // item 上已收、部分收、部分退状态都是1，如果(应收 === 实收 + 已退)就是全收了
                        if (
                            chargeStatus > ChargeItemStatusEnum.CHARGED ||
                            (
                                chargeStatus === ChargeItemStatusEnum.CHARGED &&
                                sum(receivableFee) === sum(receivedPrice, Math.abs(refundedFee))
                            )
                        ) {
                            _arr.push(receivedPrice);
                        } else {
                            // 有议价情况直接用currentUnitPrice，没有用unitPrice 计算
                            const unitPrice = getItemUnitPrice(item);
                            _arr.push(itemCountPrice(unitPrice, item.unitCount));
                            _arr.push(item.fractionPrice || 0);
                        }
                    });
                });
                total = sum(..._arr);
                return total || 0;
            },

            toothSelectorWidth() {
                let { maxWidth } = getOutpatientToothNosInfo(null, this.productForms);
                const { maxQuadrantWidth } = getToothNosInfo(this.defaultToothNos);
                if (maxQuadrantWidth > maxWidth) {
                    maxWidth = maxQuadrantWidth;
                }
                return maxWidth;
            },

            copyToothNosInfo() {
                return getOutpatientToothNosInfo(this.medicalRecord, this.productForms);
            },

            lastSelectDoctorNurseInfo() {
                const res = {
                    departmentId: null,
                    departmentName: null,
                    doctorId: null,
                    doctorName: null,
                    nurseId: null,
                    nurseName: null,
                };

                if (!this.chargeItemSupportDoctorNurse) return res;

                this.productForms.forEach((form) => {
                    form.productFormItems.forEach((item) => {
                        if (item.departmentId) {
                            Object.assign(res, {
                                departmentId: item.departmentId,
                                departmentName: item.departmentName,
                            });
                        }
                        if (item.doctorId) {
                            Object.assign(res, {
                                doctorId: item.doctorId,
                                doctorName: item.doctorName,
                            });
                        }
                        if (item.nurseId) {
                            Object.assign(res, {
                                nurseId: item.nurseId,
                                nurseName: item.nurseName,
                            });
                        }
                    });
                });
                // 没有默认当前接诊医生
                Object.assign(res, {
                    departmentId: res.departmentId || this.doctorInfo?.departmentId,
                    departmentName: res.departmentName || this.doctorInfo?.departmentName,
                    doctorId: res.doctorId || this.doctorInfo?.doctorId,
                    doctorName: res.doctorName || this.doctorInfo?.doctorName,
                });

                return res;
            },

            applySheetInfo() {
                const isExaminationItem = (type, subType) => (
                    type === GoodsTypeEnum.EXAMINATION && subType === GoodsSubTypeEnum[type].Inspect
                );

                const isInspectItem = (type, subType) => (
                    type === GoodsTypeEnum.EXAMINATION &&
                    subType === GoodsSubTypeEnum[type].Test
                );

                // 获取未完成的检验检查项目
                const getNotFinishExamItems = (checkExamItem) => {
                    try {
                        return this.forms.reduce((res, f) => {
                            const examGoodsItems = f.productFormItems.reduce((result,formItem) => {
                                const {
                                    type: fType, subType: fSubType,
                                } = formItem;

                                const examResult = this.hasResultExaminations(formItem.examinationResult);

                                if (checkExamItem(fType, fSubType)) {
                                    if (!examResult.length) {
                                        result.push({
                                            ...formItem.productInfo,
                                            purpose: formItem.purposeOfExamination,
                                            count: formItem.unitCount,
                                            productFormItemId: formItem.id,
                                            applySheetId: formItem.examinationResult?.[0]?.examinationApplySheetId,
                                        });
                                    }
                                }

                                // 套餐
                                if (fType === GoodsTypeEnum.COMPOSE) {
                                    if (!examResult.length) {
                                        const examGoodsItemsInCompose = formItem.productInfo?.children?.filter((c) => {
                                            return checkExamItem(c.type, c.subType);
                                        }).map((item) => {
                                            item.isFromCompose = true;
                                            return {
                                                ...item,
                                                count: formItem.unitCount,
                                                productFormItemId: formItem.id,
                                                applySheetId: formItem.examinationResult?.[0]?.examinationApplySheetId,
                                            };
                                        }) || [];

                                        result.push(...examGoodsItemsInCompose);
                                    }
                                }

                                return result;
                            }, []);

                            res.push(...examGoodsItems);

                            return res;
                        }, []);
                    } catch (error) {
                        console.error(error);
                    }
                    return [];
                };

                // 当前门诊内所有新增的检验项目
                const examinationItems = getNotFinishExamItems(isExaminationItem);

                // 当前门诊内所有新增的检查项目
                const inspectItems = getNotFinishExamItems(isInspectItem);

                return {
                    patientInfo: this.patientInfo,
                    doctorInfo: this.doctorInfo,
                    medicalRecord: this.medicalRecord,
                    patientOrderNo: this.patientOrderNo,
                    patientOrderId: this.patientOrderId,
                    examinationItems,
                    inspectItems,
                };
            },

            isUnDiagnosis() {
                return this.status === OutpatientStatusEnum.UN_DIAGNOSIS;
            },

            curCommonDialog() {
                if (this.templateManagerVersion === 1) return CommonAddTemplateHospitalDialog;
                return CommonAddTemplateDialog;
            },

            glassesForm() {
                const { prescriptionGlassesForms } = this.postData;
                if (!prescriptionGlassesForms?.length) return;
                const {
                    glassesParams,
                    glassesType,
                } = this.postData.prescriptionGlassesForms[0] || {};
                return {
                    usageInfo: {
                        glassesParams,
                        glassesType,
                    },
                };
            },

            allFormItemIsDisabled() {
                return this.forms.every((form) => {
                    return form.productFormItems.every((item) => {
                        return this.disabledItem(item, form);
                    });
                });
            },

            freqList() {
                return ExternalFreqOptions.map((x, index) => {
                    return {
                        id: index,
                        label: x.name,
                        time: x.time,
                    };
                });
            },
            AIGoodsSuggestionStatusEnum() {
                return {
                    init: 0,
                    loading: 1,
                    success: 2,
                    fail: 3,
                    empty: 4,
                };
            },
        },
        watch: {
            prescriptionForms: {
                handler() {
                    this.getAllRelatedProducts();
                },
                immediate: true,
            },
            isUnDiagnosis: {
                handler(v) {
                    if (v) {
                        return;
                    }
                    // 已诊状态下，如果可修改诊疗项目，缓存已提交检查检验项目，用于区分追加项目
                    if (!this.disabled) {
                        this.cacheOriginFromList();
                    }
                },
                immediate: true,
            },
        },

        async created() {
            this._suggestionItems = [
                {
                    prop: 'shortId',
                    style: 'width:70px;margin-right: 5px;flex: none',
                    titleFunction: (suggestion) => {
                        return suggestion.shortId;
                    },
                    formatFunction: (suggestion) => {
                        return suggestion.shortId;
                    },
                },
                {
                    prop: 'name',
                    titleFunction: goodsHoverTitle,
                    render: (h, row) => {
                        const { isEnableAiSearch } = this;
                        return (
                            <abc-flex gap={4} align="center" key={row.id} flex="1" style="margin-right: 5px;">
                                <span style="font-size: 14px;">
                                    {row.name}
                                </span>

                                { isEnableAiSearch && <BizExamBusinessTag
                                    is-cloud-tag
                                    cloud-supplier-flag={row.cloudSupplierFlag}
                                    disabled={row.disabled}
                                ></BizExamBusinessTag>}

                                <abc-text size="mini" theme="success-light" class="ellipsis" title={row.examinePurpose || row.purpose}>{((row.examinePurpose || row.purpose) && isEnableAiSearch) ? `推荐：${row.examinePurpose || row.purpose}` : ''}</abc-text>

                                <BizExamBusinessTag
                                    is-out-sourcing-tag
                                    coop-flag={row.coopFlag}
                                    type={row.type}
                                    sub-type={row.subType}
                                ></BizExamBusinessTag>

                                <BizGoodsInfoTagGroup
                                    product-info={row}
                                    is-fold-tags
                                    style={'flex: 1;'}
                                ></BizGoodsInfoTagGroup>
                            </abc-flex>
                        );
                    },
                },
                {
                    prop: 'deviceInfo',
                    render: (h, row) => {
                        // 检验多设备标识
                        return checkCanShowDeviceFlag(row) ? (
                            <div
                                style={{
                                    'margin-right': '16px',
                                    lineHeight: '14px',
                                    marginTop: '2px',
                                    marginLeft: '4px',
                                }}
                            >
                                <abc-tooltip
                                    placement="top"
                                    content="已关联设备"
                                    zIndex={10000}
                                >
                                    <abc-icon
                                        icon="n-device-feedback-fill"
                                        size={14}
                                        class="device-icon"
                                    ></abc-icon>
                                </abc-tooltip>
                            </div>
                            ) : null;
                    },
                },
                {
                    prop: 'displaySpec',
                    className: 'gray',
                    style: 'margin-right: 5px;width: 105px',
                    formatFunction: (suggestion) => {
                        return suggestion.displaySpec;
                    },
                    titleFunction: (suggestion) => {
                        return suggestion.displaySpec;
                    },
                },
                {
                    prop: 'displayInventory',
                    className: 'gray',
                    style: 'width: 48px;margin-right: 5px',
                    formatFunction: (suggestion) => {
                        return this.displayInventory(suggestion);
                    },
                    titleFunction: (suggestion) => {
                        return this.displayInventory(suggestion);
                    },
                },
                {
                    prop: 'packagePrice',
                    type: 'money',
                    style: 'width: 80px;text-align: right;',
                },

                {
                    prop: 'typeStr',
                    className: 'gray',
                    style: 'width: 45px;margin-right: 5px;text-align: center;',
                    formatFunction: (suggestion) => {
                        return this.formTypeStr(suggestion);
                    },
                },
                {
                    prop: 'medicalFeeGrade',
                    className: 'gray',
                    style: 'width: 98px;text-align: left;padding-left: 6px',
                    formatFunction: (suggestion) => {
                        const _arr = [];
                        const medicalFeeGradeStr = medicalFeeGradeFormatStr(suggestion, {
                            shebaoCardInfo: this.shebaoCardInfo,
                        });
                        if (medicalFeeGradeStr) {
                            _arr.push(medicalFeeGradeStr);
                        }
                        if (suggestion.shebao) {
                            if (this.isEnableListingPrice && suggestion.shebao.listingPrice) {
                                _arr.push('挂网');
                            }
                            if (suggestion.shebao.priceLimit) {
                                _arr.push('限价');
                            }
                        }
                        return _arr.join('/');
                    },
                },
                {
                    prop: 'manufacturer',
                    className: 'gray',
                    style: 'width: 100px;padding:4px;margin-right: 5px;',
                    formatFunction: (suggestion) => {
                        return suggestion.manufacturer;
                    },
                    titleFunction: (suggestion) => {
                        return suggestion.manufacturer;
                    },
                },
                {
                    prop: 'remark',
                    className: 'gray',
                    style: 'width: 76px;margin-right: 5px;',
                    formatFunction: (suggestion) => {
                        return suggestion.remark;
                    },
                    titleFunction: (suggestion) => {
                        return suggestion.remark;
                    },
                },

            ].filter((item) => {
                if (item.prop === 'shortId') {
                    return this.isShowGoodsAutocompleteShortId;
                }
                return true;
            });
            this._suggestionTitles = [
                {
                    label: '商品编码',
                    style: 'width:70px;margin-right: 5px;flex: none;',
                },
                {
                    label: '项目名/商品名',
                    style: 'flex:1;margin-right: 5px;font-size: 14px;',
                },
                {
                    label: '单位/规格',
                    style: 'margin-right: 5px;width: 105px',
                },
                {
                    label: '库存',
                    style: 'width: 48px;margin-right: 5px',
                },
                {
                    label: '价格',
                    style: 'width: 80px;text-align: right;',
                },

                {
                    label: '类别',
                    className: 'gray',
                    style: 'width: 45px;text-align: center;margin-right: 5px;',
                },
                {
                    label: '医保',
                    style: 'width: 98px;text-align: left;padding-left: 6px',
                },
                {
                    label: '厂家',
                    className: 'gray',
                    render: () => {
                        const handleManufacturerChange = () => {
                            // 配合focus-show重新触发查询
                            this.$nextTick(() => {
                                this.$refs.defaultSearch?.focusInput?.();
                            });
                        };
                        return <ManufacturerSelect
                                size="tiny"
                                placeholder="厂家"
                                value={this.selectedManufacturer}
                                manufacturerOptions={this.manufacturerOptions}
                                style='width: 100px;margin-right: 5px'
                                onInput={(v) => {
                                    this.selectedManufacturer = v;
                                }}
                                onChange={handleManufacturerChange}
                            >
                        </ManufacturerSelect>;
                    },
                },
                {
                    label: '备注',
                    className: 'gray',
                    style: 'width: 76px;margin-right: 5px',
                },

            ].filter((item) => {
                if (item.label === '商品编码') {
                    return this.isShowGoodsAutocompleteShortId;
                }
                return true;
            });

            this._suggestionGoodsTitles = [
                {
                    label: '商品编码',
                    style: 'width:70px;margin-right: 5px;flex: none;',
                },
                {
                    label: '项目名/商品名',
                    style: 'flex:1;margin-right: 5px;font-size: 14px;',
                },
                {
                    label: '单位/规格',
                    style: 'margin-right: 5px;width: 105px',
                },
                {
                    label: '库存',
                    style: 'width: 48px;margin-right: 5px',
                },
                {
                    label: '价格',
                    style: 'width: 80px;text-align: right;',
                },

                {
                    label: '类别',
                    className: 'gray',
                    style: 'width: 45px;text-align: center;margin-right: 5px;',
                },
                {
                    label: '医保',
                    style: 'width: 98px;text-align: left;padding-left: 6px',
                },
                {
                    label: '厂家',
                    className: 'gray',
                    render: () => {
                        const handleManufacturerChange = () => {
                            const goodsAutocomplete = this.$refs.goodsAutocomplete?.[0];
                            if (goodsAutocomplete) {
                                // 配合focus-show重新触发查询
                                this.$nextTick(() => {
                                    goodsAutocomplete.focusInput();
                                });
                            }
                        };
                        return <ManufacturerSelect
                            size="tiny"
                            placeholder="厂家"
                            value={this.selectedManufacturerGoods}
                            manufacturerOptions={this.manufacturerOptionsGoods}
                            style='width: 100px;margin-right: 5px'
                            onInput={(v) => {
                                this.selectedManufacturerGoods = v;
                            }}
                            onChange={handleManufacturerChange}
                        >
                        </ManufacturerSelect>;
                    },
                },
                {
                    label: '备注',
                    className: 'gray',
                    style: 'width: 76px;margin-right: 5px',
                },

            ].filter((item) => {
                if (item.label === '商品编码') {
                    return this.isShowGoodsAutocompleteShortId;
                }
                return true;
            });
            this.$store.dispatch('initAllDepartmentDoctors');
            this.$store.dispatch('initNurseList');
            this.$store.dispatch('initClinicBasic');
            this.getAllRelatedProducts();
            this.fetchExamConfig();
            this.$abcEventBus.$on('add-list', (list) => {
                this.batchSelect(list);
            }, this);
            this.$store.dispatch('initDoctorWesternPRRemarks');

            this.$abcEventBus.$on('get-ai-examination-suggestion', () => {
                this.fetchAISuggestion();
            }, this);

        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },

        methods: {
            handleClickRemarkIcon,
            hasSurgeryGoods,
            handleClickRemarkAcupoint(item) {
                this.handleClickRemarkIcon(item, this.outpatientFeeChange);
            },
            focusHandler() {
                if (!this.fixed) {
                    optimizePopoverPosition(280, this.$refs.defaultSearch.$el);
                }
            },
            handleRestProductChange(count) {
                this.$emit('rest-product-change', count);
            },
            async getAllRelatedProducts() {
                const usageParams = getUsageParams(this.prescriptionForms);
                const res = getTypeGoodsList(usageParams, this.infusionRelatedConfigs);
                let newRes = [];
                // 合并相同项目，并累加次数
                newRes = res.reduce((acc, cur) => {
                    const hasValue = acc.find(
                        (currentItem) => currentItem.goodsItem.id === cur.goodsItem.id && currentItem.goodsItem.name === cur.goodsItem.name,
                    );
                    if (hasValue) {
                        hasValue.unitCount += cur.unitCount;
                    } else {
                        acc.push(cur);
                    }
                    return acc;
                }, []);
                this.relatedProducts = newRes;
                const goodsIdList = [];
                this.existedProducts && this.existedProducts.forEach((item) => {
                    goodsIdList.push(item.productId);
                });

                goodsIdList.forEach((item) => {
                    this.relatedProducts = this.relatedProducts.filter((it) => it.goodsId !== item);
                });

                this.relatedProductCount = this.relatedProducts && this.relatedProducts.length;
                this.$emit('rest-product-change', this.relatedProductCount);
            },
            handleRelatedProductCountChange(count) {
                this.relatedProductCount = count || 0;
            },

            handleShowRelatedProducts() {
                this.showRelatedProduct = true;
            },
            goodsTypeStr(item) {
                const {
                    type,
                    subType,
                } = item;
                let str = '';
                if (type === GoodsTypeEnum.EXAMINATION) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test) {
                        str = '检查';
                    } else if (subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect) {
                        str = '检验';
                    }
                } else if (type === GoodsTypeEnum.TREATMENT) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Treatment) {
                        str = '治疗';
                    } else if (subType === GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy) {
                        str = '理疗';
                    }
                } else if (type === GoodsTypeEnum.OTHER) {
                    str = '其他';
                } else if (type === GoodsTypeEnum.MATERIAL) {
                    str = '物资';
                } else if (type === GoodsTypeEnum.GOODS) {
                    str = '商品';
                } else if (type === GoodsTypeEnum.COMPOSE) {
                    str = '套餐';
                } else if (type === GoodsTypeEnum.NURSE) {
                    str = '护理';
                } else if (type === GoodsTypeEnum.EYEGLASSES) {
                    str = '眼镜';
                } else if (type === GoodsTypeEnum.SURGERY && subType === GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY) {
                    str = '手术';
                }
                return str;
            },

            goodsItemKey(formIndex, index) {
                return `goods-item-${formIndex}-${index}`;
            },

            handleFocusRemark(index) {
                this.currentFocusItem = index;

                if (this.currentFocusItem === 'medicineRemarks') return;
                try {
                    const $currentFocusItem = this.$refs[this.currentFocusItem][0];
                    keepLastIndex($currentFocusItem.$el.querySelector('input'));
                } catch (err) {
                    console.error(err);
                }

            },
            handleBlurRemark() {
                this.currentFocusItem = null;
            },

            /**
             * @desc display autocomplete 下拉框 库存信息
             * <AUTHOR>
             * @date 2018/08/02 15:11:50
             */
            displayInventory(item) {
                if (item.isTips) return '';
                if (item.noStocks) return '';
                let str = '';

                if (item.stockPackageCount) {
                    str += `${item.stockPackageCount}${item.packageUnit}`;
                }

                if (item.stockPieceCount) {
                    str += `${item.stockPieceCount}${item.pieceUnit}`;
                }

                if (!item.stockPackageCount && !item.stockPieceCount) {
                    str += `${item.stockPackageCount ?? 0}${item.packageUnit ?? ''}`;
                }

                return str;
            },

            /**
             * @desc 商品的开单数量可以输入2位小数
             */
            getUnitCountFormatLength(item) {
                if (!item.productInfo) return 0;

                const dismounting = !!item.productInfo.dismounting; // 允许拆零
                const isFormUnit = item.unit === item.productInfo.pieceUnit; // 开药量单位选择小单位
                const isGoods = item.type === GoodsTypeEnum.GOODS; // 是否是商品
                const isMaterial = item.type === GoodsTypeEnum.MATERIAL; // 是否是耗材

                if ((isGoods && dismounting && isFormUnit) || isMaterial) {
                    return 2;
                }
                return 0;

            },
            selectType(buttonType) {
                this.selectedButton = buttonType;
            },
            /**
             * @desc 能够查看检查检验报告
             * <AUTHOR> Yang
             * @date 2020-10-22 09:22:22
             */
            hasResultExaminations(result) {
                if (Array.isArray(result)) {
                    return result.filter((item) => {
                        return item.status === 1;
                    });
                }
                return [];
            },

            /**
             * @desc 失焦的时候判断是否选择，没有选择清空输入框
             * <AUTHOR>
             * @date 2020/05/07 11:36:04
             */
            blurHandle($autocomplete, item) {
                if (item && !item.name) {
                    if ($autocomplete && typeof $autocomplete.clearQueryString === 'function') {
                        $autocomplete.clearQueryString();
                    }
                }
            },
            closePanel() {
                this.currentEditIndex = -1;
            },

            /**
             * @desc 需不需要填写天数
             * <AUTHOR> Yang
             * @date 2020-06-18 15:57:47
             */
            needDays(item) {
                return this.isTreatment(item) || this.isCompose(item);
            },

            /**
             * @desc 是检查治疗
             * <AUTHOR>
             * @date 2020/04/13 15:02:45
             */
            isExamination(item) {
                return item.type === GoodsTypeEnum.EXAMINATION;
            },
            /**
             * @desc 是治疗理疗
             * <AUTHOR>
             * @date 2020/04/13 15:08:41
             */
            isTreatment(item) {
                return item.type === GoodsTypeEnum.TREATMENT;
            },
            /**
             * 是手术
             * @return {boolean}
             */
            isSurgery(item) {
                return item.type === GoodsTypeEnum.SURGERY && item.subType === GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY;
            },
            /**
             * @desc 是材料
             * <AUTHOR>
             * @date 2020/04/13 15:03:44
             */
            isMaterial(item) {
                return item.type === GoodsTypeEnum.MATERIAL;
            },
            /**
             * @desc 是商品
             * <AUTHOR>
             * @date 2020/04/13 15:04:29
             */
            isGoods(item) {
                return item.type === GoodsTypeEnum.GOODS;
            },
            /**
             * @desc 是其他费用
             * <AUTHOR>
             * @date 2020/04/13 15:05:02
             */
            isOther(item) {
                return item.type === GoodsTypeEnum.OTHER;
            },
            /**
             * @desc 套餐
             */
            isCompose(item) {
                return item.type === GoodsTypeEnum.COMPOSE;
            },
            // 护理医嘱
            isNurse(item) {
                return item.type === GoodsTypeEnum.NURSE;
            },
            isEyeGlasses(item) {
                return item.type === GoodsTypeEnum.EYEGLASSES;
            },

            editProduct(item, form) {
                if (this.disabled || this.disabledItem(item, form)) return false;
                this.currentEditIndex = item.index;
                this.$nextTick(() => {
                    const $inputs = $(this.$el)
                        .find(`#${item.id || item.keyId}`)
                        .find('.goods-autocomplete-wrapper input');
                    $inputs.first().focus();
                });
                this.getAllRelatedProducts();
            },

            handleDoubleClick(event) {
                if (!event.target.value) return;
                event.target.selectionStart = 0;
                event.target.selectionEnd = event.target.value.length;
            },

            placeholderStr(item) {
                if (this.isMaterial(item) || this.isGoods(item)) {
                    return '材料商品';
                } if (this.isOther(item)) {
                    return '其他费用';
                } if (this.isCompose(item)) {
                    return '套餐';
                } if (this.isSurgery(item)) {
                    return '手术';
                }
                return '';

            },

            jsonType(item) {
                if (this.isExamination(item)) {
                    return [{ type: GoodsTypeEnum.EXAMINATION }];
                } if (this.isOther(item)) {
                    return [{ type: GoodsTypeEnum.OTHER }];
                } if (this.isTreatment(item)) {
                    return [{ type: GoodsTypeEnum.TREATMENT }];
                } if (this.isMaterial(item) || this.isGoods(item)) {
                    return [{ type: GoodsTypeEnum.MATERIAL }, { type: GoodsTypeEnum.GOODS }];
                } if (this.isCompose(item)) {
                    return [{ type: GoodsTypeEnum.COMPOSE }];
                } if (this.isNurse(item)) {
                    return [{ type: GoodsTypeEnum.NURSE }];
                } if (this.isEyeGlasses(item)) {
                    return [{ type: GoodsTypeEnum.EYEGLASSES }];
                } if (this.isSurgery(item)) {
                    return [{
                        type: GoodsTypeEnum.SURGERY,
                        subType: [
                            GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY,
                        ],
                    }];
                }
                return [];
            },

            disabledItem(item, form) {
                return this.isCharged(item, form) || this.isExecuted(item, form) || this.hasResultExaminations(item.examinationResult).length;
            },

            isCharged(e, form) {
                return form.chargeStatus > 0 && e.chargeStatus && e.chargeStatus !== 0;
            },
            isExecuted(e) {
                return e.executeStatus === ExecuteStatus.FINISHED;
            },
            standardUnitStr(item) {
                return item.standardUnit || (item.productInfo && item.productInfo.standardUnit) || '';
            },
            formTypeStr(item) {
                return this.goodsTypeStr(item) || '';

            },
            getTotalPrice(item) {
                const {
                    unitCount = 0,
                } = item;
                const _arr = [];
                // 有议价情况直接用currentUnitPrice，没有用unitPrice 计算
                const unitPrice = getItemUnitPrice(item);
                _arr.push(itemCountPrice(unitPrice, unitCount));
                _arr.push(item.fractionPrice || 0);
                return `${this.$t('currencySymbol')} ${(sum(..._arr) || 0).toFixed(2)}`;
            },

            isDisabledGoods(item, form) {
                if (this.isCharged(item, form) || this.disabled || !this.needCheckStock) return false;
                return isDisabledGoods(item).flag;
            },

            isLimitedGoods(item) {
                //1: 男 2:女 0:不限
                const GenderEnumStr = Object.freeze({
                    '男': 1,
                    '女': 2,
                    '不限': 0,
                });
                const goodsGender = +item.productInfo?.gender || 0;
                const sex = GenderEnumStr[this.patientInfo?.sex || '不限'] || 0;
                return goodsGender !== 0 && sex !== 0 && goodsGender !== sex;
            },

            isShortage(item, form) {
                if (this.isCharged(item, form) || this.disabled || !this.needCheckStock) return false;
                return isShortage(item).flag || item.noStocks;
            },

            /**
             * @desc 药品项目停用 || 库存不足 || 无库存信息
             * <AUTHOR> Yang
             * @date 2021-03-30 15:46:13
             */
            showWarnTips(item, form) {
                return this.isDisabledGoods(item, form) || this.isShortage(item, form);
            },

            warnTips(item) {
                return isDisabledGoods(item).tips || isShortage(item).tips || '无库存';
            },

            /**
             * @desc 可能的单位列表
             * <AUTHOR>
             * @date 2018/04/13 11:01:33
             */
            unitArray(wm) {
                const res = [];
                const dismounting = wm.productInfo && wm.productInfo.dismounting;
                const pieceUnit = wm.productInfo && wm.productInfo.pieceUnit;
                const packageUnit = wm.productInfo && wm.productInfo.packageUnit;
                if (dismounting) {
                    if (pieceUnit) {
                        res.push({ name: pieceUnit });
                    }
                    if (packageUnit && packageUnit !== pieceUnit) {
                        res.push({ name: packageUnit });
                    }
                } else {
                    if (packageUnit) {
                        res.push({ name: packageUnit });
                    }
                }
                return res;
            },

            /**
             * @desc 找到存在列表中，当次新增的（未收费，未执行的item）
             * <AUTHOR> Yang
             * @date 2020-11-16 19:16:36
             */
            findExistProduct(goods) {
                let product = null;
                this.productForms.forEach((form) => {
                    form.productFormItems.forEach((item) => {
                        if (!this.disabledItem(item, form) && item.productId === goods.goodsId) {
                            product = item;
                        }
                    });
                });
                return product;
            },

            quickSelect(goodsList) {
                this.batchSelect(goodsList.map((item) => {
                    return {
                        goods: item,
                        initialData: {
                            unitCount: 1,
                        },
                    };
                }));
            },

            singleSelectGoods(goods) {
                // const existItem = this.findExistProduct(goods);
                this.selectGoods(goods);
            },

            async batchSelect(selectedList, needFetchGoodsInfo = true) {
                let goodsList = [];
                if (needFetchGoodsInfo) {
                    const goodsIds = selectedList.map((item) => item.goods.id);
                    const { data } = await SettingAPI.commonPrescription.fetchPrescriptionTemplateStock({
                        clinicId: this.clinicId,
                        sceneType: SearchSceneTypeEnum.outpatient,
                        departmentId: this.curDepartmentId,
                        goodsIds,
                    });
                    goodsList = data.list;
                }
                let filterItems = [];
                for (const item of selectedList) {
                    const {
                        goods,
                        initialData,
                    } = item;
                    const newGoods = goodsList.find((goodsItem) => goods.id && goodsItem.id === goods.id);
                    if (newGoods) {
                        Object.assign(goods, newGoods);
                    }

                    // 判断选择的套餐的子项中的西成药是否符合抗菌等级限制
                    const {
                        isSuccess, list,
                    } = await this.isAllowAdd({
                        goods, needFetch: false, needOpenDialog: false,
                    });
                    if (!isSuccess) {
                        filterItems = filterItems.concat(list);
                        continue;
                    }

                    initialData.useDismounting = +(
                        goods.dismounting &&
                        initialData.unit === goods.pieceUnit &&
                        initialData.unit !== goods.packageUnit
                    );
                    if (initialData.useDismounting) {
                        initialData.unit = goods.pieceUnit;
                        initialData.unitPrice = goods.piecePrice || 0;
                    } else {
                        initialData.unit = goods.packageUnit;
                        initialData.unitPrice = goods.packagePrice || 0;
                    }

                    const productFormItem = this.getProductFormItem(goods, initialData);
                    this.addProductItem(productFormItem);
                    this._lastCreateItem = productFormItem;

                    await this.fetchFeeComposeInfo(goods, productFormItem);
                }

                // 如果有套餐因为子项的抗菌等级限制不合规, 则弹窗提示
                if (filterItems.length) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: filterItems }).generateDialogAsync({ parent: this });
                    }, 100);
                }

                this.focusUnitCountInput(this._lastCreateItem);
                this.outpatientFeeChange();
                this.$emit('outpatient-verify');
            },


            getProductFormItem(goods, initialData = {}) {

                let toothNos = [];
                if (this.productFormCanAddToothNo) {
                    toothNos = this.defaultToothNos;
                    // 使用后删除
                    this.defaultToothNos = [];
                }

                const {
                    departmentId,
                    departmentName,
                    doctorId,
                    doctorName,
                    nurseId,
                    nurseName,
                } = this.lastSelectDoctorNurseInfo || {};
                const needDefaultFreq = this.showDTDayCol && this.needFreq && this.needDays(goods);

                /**
                 * @desc 构造前端使用
                 * <AUTHOR>
                 * @date 2019/03/28 15:11:32
                 */
                return {
                    keyId: createGUID(),
                    name: goods.name,
                    productId: goods.id,
                    type: goods.type,
                    subType: goods.subType,

                    standardUnit: goods.standardUnit,
                    stockPieceCount: goods.stockPieceCount,
                    stockPackageCount: goods.stockPackageCount,

                    freq: needDefaultFreq ? '1日1次' : '',
                    dailyDosage: initialData?.dailyDosage || '',
                    days: initialData?.days || 1,
                    remark: initialData?.remark || '',
                    unitCount: initialData?.unitCount || 1,
                    useDismounting: initialData?.useDismounting || 0,
                    unit: initialData?.unit || goods.packageUnit || '次',
                    unitPrice: initialData?.unitPrice || goods.packagePrice || 0,

                    payType: null,
                    toothNos, // 牙位编号
                    departmentId,
                    departmentName,
                    doctorId,
                    doctorName,
                    nurseId,
                    nurseName,

                    pharmacyType: goods.pharmacyType,
                    pharmacyNo: goods.pharmacyNo,
                    pharmacyName: goods.pharmacyName,
                    composeChildren: null,
                    productInfo: goods,
                };
            },

            /**
             * 判断开出的套餐的子项的抗菌等级是否满足开出条件
             * @return {Promise<{list: *[], isSuccess: boolean}>}
             */
            async isAllowAdd({
                goods, needFetch, needOpenDialog,
            }) {
                // 不是套餐直接放行
                if (goods.type !== GoodsTypeEnum.COMPOSE) {
                    return {
                        isSuccess: true,
                        list: [],
                    };
                }

                let children = goods.children || [];
                if (needFetch) {
                    const { data } = await GoodsAPI.fetchGoods(goods.id, {
                        withStock: 1,
                        feeComposeType: goods.feeComposeType,
                        sceneType: SearchSceneTypeEnum.outpatient,
                        departmentId: this.curDepartmentId,
                    });
                    children = data.children;
                }
                const westernMedicine = (children || []).filter((item) => {
                    return item.type === GoodsTypeEnum.MEDICINE && [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(item.subType);
                });
                // 如果套餐子项没有包含中西成药, 则直接放行
                if (!westernMedicine.length) {
                    return {
                        isSuccess: true,
                        list: [],
                    };
                }

                const { doctorId } = this.postData;
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                const filterMedicine = [];
                westernMedicine.forEach((item) => {
                    const medicineItem = {
                        productInfo: item,
                        name: item.displayName || item.medicineCadn || item.name,
                    };
                    const isSuccess = isAllowAddByAntimicrobialDrugManagement(medicineItem, doctorId, antimicrobialDrugManagementData, employeeListByPractice);
                    if (!isSuccess) {
                        filterMedicine.push(medicineItem);
                    }
                });
                if (filterMedicine.length) {
                    if (needOpenDialog) {
                        // eslint-disable-next-line abc/no-timer-id
                        setTimeout(() => {
                            new AntimicrobialDrugManagementModal({ list: filterMedicine }).generateDialogAsync({ parent: this });
                        }, 100);
                    }
                    return {
                        isSuccess: false,
                        list: filterMedicine,
                    };
                }

                return {
                    isSuccess: true,
                    list: [],
                };
            },

            async selectGoods(goods, item, needFetchGoodsInfo = true) {
                if (!goods) return false;
                if (goods.disabled) return false;

                const { isSuccess } = await this.isAllowAdd({
                    goods, needFetch: true, needOpenDialog: true,
                });
                if (!isSuccess) return false;

                this.currentEditIndex = -1;

                /**
                 * @desc 构造前端使用
                 * <AUTHOR>
                 * @date 2019/03/28 15:11:32
                 */
                const productFormItem = this.getProductFormItem(goods);

                // 修改
                if (item) {
                    // 因为要合并到item上，item有自己的id会重复，所以删除掉goods上的id，用productId代替
                    Object.assign(item, productFormItem);
                } else {
                    // 新增
                    this.addProductItem(productFormItem);
                }

                this.focusUnitCountInput(productFormItem);

                if (!goods.id) return false;
                if (
                    [
                        GoodsTypeEnum.MATERIAL,
                        GoodsTypeEnum.GOODS,
                    ].indexOf(goods.type) > -1
                ) {
                    const { physicalExamination } = this.medicalRecord || {};
                    const { data } = await CdssAPI.fetchMedicineUsage({
                        type: goods.type,
                        goodsId: goods.id,
                        patientInfo: this.patientInfo,
                        physicalExamination,
                    });
                    if (data) {
                        const {
                            useDismounting,
                        } = data;

                        const {
                            dismounting,
                            pieceUnit,
                            piecePrice,
                            packageUnit,
                            packagePrice,
                        } = goods;
                        productFormItem.useDismounting = useDismounting && dismounting;

                        if (productFormItem.useDismounting) {
                            productFormItem.unit = pieceUnit;
                            productFormItem.unitPrice = piecePrice || 0;
                        } else {
                            productFormItem.unit = packageUnit;
                            productFormItem.unitPrice = packagePrice || 0;
                        }
                        if (item) {
                            Object.assign(item, productFormItem);
                        }
                    }
                } else if (needFetchGoodsInfo) {
                    // 这里加await医保控费需要goods的children
                    await this.fetchFeeComposeInfo(goods, item ? item : productFormItem);
                }
                this.outpatientFeeChange();
                this.$emit('outpatient-verify');
            },

            focusUnitCountInput(item) {
                if (!item) return;
                this._timer = setTimeout(() => {
                    const $inputs = $(this.$el).find(`#${item.keyId}`).find('.unit-count input');
                    $inputs.eq(0).focus();
                }, 250);
            },

            async fetchFeeComposeInfo(goods, productFormItem) {
                if (!goods.id) return;
                if (
                    [
                        FeeComposeTypeEnum.FEE_TYPE_COMPOSE_GOODS,
                        FeeComposeTypeEnum.FEE_TYPE_COMPOSE_FEE,
                    ].indexOf(goods.feeComposeType) !== -1 ||
                    goods.type === GoodsTypeEnum.EXAMINATION
                ) {
                    // 支持费用项组合||套餐 需要拉一下goods详细信息，用于展示费用项||子项
                    const { data } = await GoodsAPI.fetchGoods(goods.id, {
                        withStock: 1,
                        feeComposeType: goods.feeComposeType,
                        sceneType: SearchSceneTypeEnum.outpatient,
                        departmentId: this.curDepartmentId,
                    });
                    productFormItem.productInfo = data;

                    // 手术和套餐需要处理手术申请单
                    if ((isSurgery(goods) || isCompose(goods)) && data) {
                        const {
                            departmentId = '', doctorId = '',
                        } = this.doctorInfo || {};
                        formatSurgeryDetail(productFormItem, goods, departmentId, doctorId, data.children);
                    }
                    if (productFormItem.composeChildren && productFormItem.composeChildren.length) {
                        productFormItem.composeChildren = productFormItem.composeChildren.map((x) => {
                            return {
                                ...x,
                                productPrimaryId: x.composeId,
                                composeChildren: getItemComposeChildren(x),
                            };
                        });
                    } else {
                        productFormItem.composeChildren = getItemComposeChildren(data);
                    }
                }
            },

            /**
             * @desc 添加商品
             * 顺序：0. 套餐、
             *     1.检查、检验、
             *     2.治疗、理疗、费用
             *     3.材料
             *     4.商品
             * <AUTHOR>
             * @date 2020/04/02 10:11:14
             */
            addProductItem(goods) {
                if (this.disabled) return false;

                let sourceFormType;
                let sort = -1;
                switch (goods.type) {
                    case GoodsTypeEnum.EXAMINATION: {
                        sourceFormType = SourceFormTypeEnum.EXAMINATION;
                        sort = 0;
                        break;
                    }
                    case GoodsTypeEnum.TREATMENT: {
                        sourceFormType = SourceFormTypeEnum.TREATMENT;
                        sort = 1;
                        break;
                    }
                    case GoodsTypeEnum.OTHER: {
                        sourceFormType = SourceFormTypeEnum.OTHER_FEE;
                        sort = 2;
                        break;
                    }
                    case GoodsTypeEnum.NURSE: {
                        sourceFormType = SourceFormTypeEnum.NURSING;
                        sort = 2;
                        break;
                    }
                    case GoodsTypeEnum.SURGERY: {
                        sourceFormType = SourceFormTypeEnum.SURGERY;
                        sort = 2;
                        break;
                    }
                    case GoodsTypeEnum.MATERIAL: {
                        sourceFormType = SourceFormTypeEnum.MATERIAL;
                        sort = 3;
                        break;
                    }
                    case GoodsTypeEnum.GOODS: {
                        sourceFormType = SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM;
                        sort = 4;
                        break;
                    }
                    case GoodsTypeEnum.COMPOSE: {
                        sourceFormType = SourceFormTypeEnum.COMPOSE;
                        sort = 5;
                        break;
                    }
                    case GoodsTypeEnum.EYEGLASSES: {
                        sourceFormType = SourceFormTypeEnum.EYEGLASSES;
                        sort = 6;
                        break;
                    }
                    default: {
                        console.error('goods.type 不对');
                        break;
                    }

                }
                if (!sourceFormType) {
                    console.error('这里的商品类型没有对应到sourceFormType，不得行');
                    return false;
                }

                const productForm = this.productForms.find(
                    (form) => form.sourceFormType === sourceFormType && !form.chargeStatus,
                );
                if (productForm) {
                    productForm.productFormItems.push(goods);
                } else {

                    let pharmacyType,
                        pharmacyNo,
                        pharmacyName;

                    if (this.isMaterial(goods) || this.isGoods(goods)) {
                        pharmacyType = goods.pharmacyType;
                        pharmacyNo = goods.pharmacyNo;
                        pharmacyName = goods.pharmacyName;
                    }
                    this.productForms.push({
                        pharmacyType,
                        pharmacyNo,
                        pharmacyName,
                        sourceFormType,
                        sort,
                        keyId: createGUID(),
                        productFormItems: [goods],
                    });
                }

                this.productForms.sort((prevForm, nextForm) => {
                    return prevForm.sort - nextForm.sort;
                });

                let index = 0;
                this.productForms.forEach((form) => {
                    form.productFormItems.sort((prevItem, nextItem) => {
                        return prevItem.subType - nextItem.subType;
                    });
                    form.productFormItems.forEach((item) => {
                        index += 1;
                        item.index = index;
                    });
                });
            },

            changeCount(item) {
                if (item.expectedTotalPrice) {
                    item.expectedTotalPrice = null;
                    if (item.sourceUnitPrice !== item.unitPrice) {
                        item.expectedUnitPrice = item.unitPrice;
                    }
                }
                this.$set(item, 'expectedTotalPriceRatio', null);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', '');
                this.outpatientFeeChange();
                this.$emit('outpatient-verify');
            },
            displayTotalPriceRatio(item) {
                const { totalPriceRatio } = item;
                if (!totalPriceRatio || totalPriceRatio > 1) return '';
                return Math.round((totalPriceRatio ?? 1) * 100);
            },
            inputPriceRadioHandler(item, val) {
                this.$set(item, 'totalPriceRatio', val / 100);
            },
            changeTotalPrice(item) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPriceRatio', null);
                this.$set(item, 'expectedTotalPrice', item.totalPrice);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },
            changeTotalPriceRatio(item) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPriceRatio', item.totalPriceRatio);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },
            changePriceRadio(item, val) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'totalPriceRatio', val);
                this.$set(item, 'expectedTotalPriceRatio', val);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.outpatientFeeChange();
            },
            changeDosageDays(item) {
                item.unitCount = this.calCountHandler(item) || '';
                this.changeCount(item);
            },

            changeUnit(item) {
                if (item.unit !== item.pieceUnit && ~~item.unitCount !== item.unitCount) {
                    item.unitCount = '';
                }
                item.useDismounting = +(
                    item.productInfo &&
                    item.productInfo.dismounting &&
                    item.unit === item.productInfo.pieceUnit &&
                    item.unit !== item.productInfo.packageUnit
                );
                const {
                    piecePrice, packagePrice,
                } = item.productInfo;
                item.unitPrice = item.useDismounting ? piecePrice : packagePrice;

                if (item.expectedTotalPrice) {
                    item.expectedTotalPrice = null;
                    if (item.sourceUnitPrice !== item.unitPrice) {
                        item.expectedUnitPrice = item.unitPrice;
                    }
                }
                clearItemBargainHandler(item);
                this.outpatientFeeChange();
                this.$emit('outpatient-verify');
            },

            deleteGoods(FormIndex, index) {
                this.productForms[FormIndex].productFormItems.splice(index, 1);
                this.filterProductForms();
                this.getAllRelatedProducts();
                this.outpatientFeeChange();
                this.$emit('outpatient-verify');
            },

            filterProductForms() {
                const productForms = this.productForms.filter((form) => {
                    return form.productFormItems.length;
                });
                this.$emit('update:forms', productForms);
            },

            /**
             * @desc 查看检验报告
             * <AUTHOR>
             * @date 2019/12/19 16:01:55
             */
            viewReport(item) {
                const {
                    examinationSheetId,
                    mergeSheetId,
                    type,
                } = item;

                this.showReport = true;
                this.curExamType = type;

                // 因为历史遗留原因，后端无法合并门诊单中的诊疗项目
                // 需要前端根据配置判断使用 检验单id 或者 合并后的单子的 id
                // 有疑问 @蒋晓风

                const isCanMerge = this.curExamType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect ?
                    this.examinationProjectCanMerge : (
                        this.curExamType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test ? this.inspectProjectCanMerge : false
                    );

                if (isCanMerge) {
                    this.examinationSheetId = mergeSheetId;
                } else {
                    this.examinationSheetId = examinationSheetId;
                }
            },

            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                // 找到所有的非disabled的input输入框
                const inputs = $(this.$el).find('.goods-item-list .count-center input').not(':disabled');
                const targetIndex = inputs.index(e.target);
                const nextInput = inputs[targetIndex + 1];
                if (nextInput) {
                    this.$nextTick(() => {
                        nextInput.selectionStart = 0;
                        nextInput.selectionEnd = nextInput.value.length;
                        nextInput.select();
                        nextInput.focus();

                        // magic code
                        this._timer = setTimeout(() => {
                            nextInput.select();
                            nextInput.focus();
                        }, 50);
                    });
                } else {
                    $(this.$el).find('.search-autocomplete input').focus();
                }
            },
            async useTemplate(templateData) {
                if (!templateData || !templateData.detail) return false;

                const goodsArray = [];
                let productFormsTemplate = templateData.detail.productForms;
                let productForms = [];
                /**
                 * @desc 导入数据会存在没有productId的情况，需要过滤掉，不让他复制出来
                 * <AUTHOR> Yang
                 * @date 2020-09-15 12:45:15
                 */
                productFormsTemplate = productFormsTemplate.filter((form) => {
                    form.productFormItems = form.productFormItems.filter((item) => item.productId);
                    return form.productFormItems.length;
                });

                // 针对使用的诊疗模板中的套餐做抗菌等级校验, 不符合的过滤掉
                let filterItems = [];
                const cacheProductFormsTemplate = [];
                for (const form of productFormsTemplate) {
                    if (form.sourceFormType !== SourceFormTypeEnum.COMPOSE) {
                        cacheProductFormsTemplate.push(form);
                        continue;
                    }
                    const cacheProductFormItems = [];
                    for (const item of form.productFormItems) {
                        if (!item.productInfo) {
                            cacheProductFormItems.push(item);
                            continue;
                        }
                        const {
                            isSuccess, list,
                        } = await this.isAllowAdd({
                            goods: item.productInfo, needFetch: false, needOpenDialog: false,
                        });
                        if (!isSuccess) {
                            filterItems = filterItems.concat(list);
                            continue;
                        }
                        cacheProductFormItems.push(item);
                    }
                    form.productFormItems = cacheProductFormItems;
                    if (form.productFormItems.length) {
                        cacheProductFormsTemplate.push(form);
                    }
                }
                productFormsTemplate = cacheProductFormsTemplate;
                // 如果有套餐因为子项的抗菌等级限制不合规, 则弹窗提示
                if (filterItems.length) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: filterItems }).generateDialogAsync({ parent: this });
                    }, 100);
                }

                productForms = this.getProductForm(productFormsTemplate, goodsArray, true);
                // 获得所有的药品库存
                const postGoodsId = {
                    sceneType: SearchSceneTypeEnum.outpatient,
                    departmentId: this.curDepartmentId,
                    goodsIds: goodsArray.map((x) => x.goodsId),
                };
                let goodsStock = [];
                // 如果goodsId 为空 获取药品的库存信息 ;
                if (postGoodsId.goodsIds.length) {
                    const { data } = await SettingAPI.commonPrescription.fetchPrescriptionTemplateStock(postGoodsId);
                    goodsStock = (data && data.list) || [];
                }
                productForms = this.updateProductFormGoodsInfo(productForms, goodsStock);

                // 处理手术申请单
                const {
                    departmentId = '', doctorId = '',
                } = this.doctorInfo || {};
                for (const productForm of productForms) {
                    for (const productFormItem of productForm.productFormItems) {
                        const { productInfo } = productFormItem;
                        // 手术和套餐需要处理手术申请单
                        if ((isSurgery(productFormItem) || isCompose(productFormItem)) && productInfo) {
                            formatSurgeryDetail(productFormItem, productFormItem, departmentId, doctorId, productInfo.children);
                        }
                    }
                }

                const unChargeForms = this.productForms.filter((form) => !form.chargeStatus);
                if (unChargeForms.length) {
                    new templateUseMethodDialog({
                        originProductForms: this.productForms,
                        productForms,
                        templateName: templateData.name,
                        onChange: (results) => {
                            this.$emit('update:forms', results);
                            this.outpatientFeeChange();
                            this.$emit('outpatient-verify');
                        },
                    }).generateDialog({ parent: this });
                } else {
                    this.$emit(
                        'update:forms',
                        this.productForms
                            .filter((form) => {
                                // 过滤掉已收的项目
                                return form.chargeStatus > 0;
                            })
                            .concat(productForms),
                    );
                    this.outpatientFeeChange();
                    this.$emit('outpatient-verify');
                }
            },

            /**
             * @desc 门诊单内容变更
             * <AUTHOR>
             * @date 2022/06/06 10:47:44
             */
            outpatientFeeChange() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '诊疗项目',
                    needCalcFee: true,
                });
            },

            /**
             * @desc 判断当前项目是否包含检查检验项目
             * <AUTHOR>
             * @date 2022/03/28
             */
            hasExaminationGoods(item) {
                if (item?.type === GoodsTypeEnum.EXAMINATION) {
                    return [ item ];
                }
                if (item?.type === GoodsTypeEnum.COMPOSE) {
                    return item?.productInfo?.children?.filter((g) => g.type === GoodsTypeEnum.EXAMINATION) || [];
                }
                return [];
            },

            /**
             * @desc 过滤未完成的检查检验项目
             * <AUTHOR>
             * @date 2022/03/28
             */
            notFinishExaminationGoods(item) {
                if (item?.type === GoodsTypeEnum.EXAMINATION) {
                    if (!this.hasResultExaminations(item.examinationResult).length) {
                        return [ item ];
                    }
                }
                if (item?.type === GoodsTypeEnum.COMPOSE) {
                    const examinationGoodsItems = item?.productInfo?.children?.filter(
                        (g) => g.type === GoodsTypeEnum.EXAMINATION,
                    ) || [];
                    const finishExaminationGoodsItems = this.hasResultExaminations(item.examinationResult);
                    return examinationGoodsItems?.filter(
                        (e) => !finishExaminationGoodsItems.some((fg) => fg.productId === e.id),
                    );
                }
                return [];
            },

            // 查看申请单
            viewApplySheet(item) {
                const productInfo = item.productInfo || item;

                const {
                    subType, deviceType, extendSpec,
                } = productInfo;

                this.currentApplySheetSubType = subType;

                if (subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect) {
                    this.currentApplySheetDeviceType = undefined;
                } else {
                    this.currentApplySheetDeviceType = deviceType;
                    this.currentApplySheetIsOphthalmology = +extendSpec === 20;
                }

                this.examApplySheetDialogVisible = true;
            },

            handleApplySheetOnOk({
                purpose, updatePurposeFormItems,
            }) {
                const productIds = updatePurposeFormItems.map((fItem) => fItem.id);

                const newForm = this.forms.map((f) => {
                    f.productFormItems = f.productFormItems.map((formItem) => {
                        if (!formItem.id && productIds.includes(formItem.productId)) {
                            formItem.purposeOfExamination = purpose;
                        }
                        return formItem;
                    });

                    return f;
                });

                this.$emit('update:forms', newForm);
            },

            cacheOriginFromList() {
                this.cacheFromList = JSON.parse(JSON.stringify(this.forms));
            },
            openGoodsDialog(defaultKey) {
                this.currentCategoryKey = defaultKey;
                this.showGoodsSelectDialog = true;
            },
            displayMedicalFeeGrade(item) {
                return this.showMedicalFeeGrade &&
                    this.isOpenSource &&
                    item.productInfo &&
                    item.productInfo.medicalFeeGrade;
            },
            saveCommon() {
                if (this.forms.length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '诊疗项目为空，不能保存为模板',
                    });
                    return false;
                }
                this.showAddDialog = true;
            },
            displayGoodsView(item) {
                const { type } = item;
                return [
                    GoodsTypeEnum.MATERIAL,
                    GoodsTypeEnum.GOODS,
                    GoodsTypeEnum.EYEGLASSES,
                ].includes(type);
            },

            filterCascaderFunc(x) {
                const { id } = x;
                return [
                    GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
                    GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL,
                    GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS,
                    GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT,
                    GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE,
                    GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD,
                    GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT,
                    GoodsTypeIdEnum.EYEGLASSES,
                    GoodsTypeIdEnum.LENS,
                    GoodsTypeIdEnum.BRACKET,
                    GoodsTypeIdEnum.OK_LENS,
                    GoodsTypeIdEnum.SOFT_LENS,
                    GoodsTypeIdEnum.RIGID_LENS,
                    GoodsTypeIdEnum.SUNGLASSES,
                ].includes(Number(id));
            },

            // 确保检查检验项目合并设置存在
            fetchExamConfig() {
                this.$store.dispatch('examination/fetchExaminationSettings');
                this.$store.dispatch('inspect/fetchInspectSettings');
            },

            handleOpenSurgeryDetailDialog(item) {
                const submitCallback = (surgeryDetail) => {
                    surgeryDetail.isInit = false;
                    this.$set(item, 'surgeryDetail', surgeryDetail);
                };
                new SurgeryDetailDialog({
                    item,
                    extendDiagnosisInfos: this.medicalRecord.extendDiagnosisInfos?.[0]?.value || [],
                    disabled: !!this.disabled,
                    submitCallback,
                }).generateDialogAsync({ parent: this });
            },

            getItemDataCy(arr, id) {
                const flatArr = arr.flatMap((x) => x.productFormItems);
                const index = flatArr.findIndex((x) => {
                    const compareId = x.id || x.keyId || x.productId;
                    return compareId === id;
                });
                return `item-${index}`;
            },

            selectFreq(val, item) {
                item.unitCount = this.calCountHandler(item) || '';
                this.$set(item, 'expectedTotalPriceRatio', null);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', '');
                this.outpatientFeeChange();
                this.$emit('outpatient-verify');
            },

            /**
             * @desc 计算总量 freq * days * dailyDosage(兼容之前的数据，还用这个字段”每日用量“，本身应该叫”每次用量“)
             */
            calCountHandler(item) {
                const {
                    freq, days, dailyDosage, unitCount: originUnitCount,
                } = item;
                if ((this.needFreq && !freq) || !days || !dailyDosage) return originUnitCount;
                let freqTime = 1;
                if (this.needFreq) {
                    const currentFreq = this.freqList.find((x) => x.label === freq);
                    if (!currentFreq) return;
                    freqTime = 24 / currentFreq.time * days;
                } else {
                    freqTime = days;
                }
                if (!freqTime || freqTime === Infinity) return;
                const rateUnitCount = Math.floor(dailyDosage * freqTime * 100);
                let unitCount = Math.ceil(rateUnitCount / 100);
                if (isNaN(unitCount)) {
                    unitCount = '';
                }
                return unitCount;
            },

            async fetchAISuggestion() {
                if (!this.isEnableAiSearch) {
                    this.aiGoodsSuggestionResStatus = this.AIGoodsSuggestionStatusEnum.init;
                    return ;
                }
                if (this.disabled || this.disabledAdd) {
                    this.aiGoodsSuggestionResStatus = this.AIGoodsSuggestionStatusEnum.init;
                    return;
                }
                const {
                    presentHistory, chiefComplaint,
                } = this.postData.medicalRecord;

                if (!chiefComplaint) {
                    this.aiGoodsSuggestionResStatus = this.AIGoodsSuggestionStatusEnum.init;
                    return;
                }

                if (presentHistory !== this.cacheMedicalRecord.presentHistory || chiefComplaint !== this.cacheMedicalRecord.chiefComplaint) {
                    this.aiGoodsSuggestionResStatus = this.AIGoodsSuggestionStatusEnum.loading;
                    this.cacheMedicalRecord = {
                        presentHistory,
                        chiefComplaint,
                    };
                    try {
                        const { data } = await AiAPI.getExaminationGoodsSuggestion({
                            patient: this.postData.patient,
                            medicalRecord: {
                                outpatientSheetId: this.$route.params.id,
                                presentHistory,
                                chiefComplaint,
                            },
                            offset: 0,
                            limit: 10,
                        });
                        const { medicalRecord } = data;
                        if (medicalRecord.presentHistory === presentHistory && medicalRecord.chiefComplaint === chiefComplaint) {
                            this.aiGoodsList = data.list;
                        }
                        if (this.aiGoodsList.length) {
                            this.aiGoodsSuggestionResStatus = this.AIGoodsSuggestionStatusEnum.success;
                        }
                        if (!this.aiGoodsList.length) {
                            this.aiGoodsSuggestionResStatus = this.AIGoodsSuggestionStatusEnum.empty;
                        }
                    } catch (e) {
                        this.aiGoodsSuggestionResStatus = this.AIGoodsSuggestionStatusEnum.fail;
                        this.aiGoodsList = [];
                        console.error('获取检验推荐错误', e);
                    }
                }
            },
            acceptAIGoods(item) {
                this.$abcPlatform.service.report.reportEventSLS('ai_examination_accept', '采纳AI推荐检验项目');

                this.singleSelectGoods(item);
            },
            customFilterSuggestions(list) {
                if (!list || !Array.isArray(list) || !this.aiGoodsList || !this.aiGoodsList.length) {
                    return list;
                }

                // 对 aiGoodsList 进行去重，确保没有重复的 id
                const uniqueAiGoodsList = [];
                const seenIds = new Set();

                for (const item of this.aiGoodsList) {
                    if (!seenIds.has(item.id)) {
                        seenIds.add(item.id);
                        uniqueAiGoodsList.push(item);
                    }
                }

                // 创建一个集合来存储去重后的 aiGoodsList 中商品的 id
                const aiGoodsIdSet = new Set(uniqueAiGoodsList.map((item) => item.id));

                // 过滤掉列表中已经存在于 aiGoodsList 中的商品
                const filteredList = list.filter((item) => !aiGoodsIdSet.has(item.id));

                // 将去重后的 aiGoodsList 插入到列表最前面
                return [...uniqueAiGoodsList, ...filteredList];
            },

            isItemCanAdjustment(item) {
                if (!this.canSingleBargain) return false;
                return !!item.canAdjustment;
            },

            readonlyItem(item) {
                return !this.isItemCanAdjustment(item);
            },

            onChangePayType(val, item, form) {
                this.handleChangePayType(val, item, form, 'productFormItems');
                this.outpatientFeeChange();
                this.$emit('outpatient-verify');
            },
        },
    };
</script>
<style lang="scss">
@import "src/styles/mixin.scss";

.outpatient-related-product-recommend-wrapper {
    position: relative;
}

.diagnosis-treatment-ai-suggestion-popover {
    .diagnosis-treatment-ai-suggestion-reference {
        max-width: 280px;
        text-align: right;

        &:hover {
            color: var(--abc-color-G2);
        }
    }

    .diagnosis-treatment-ai-suggestion-list {
        min-width: 300px;
        max-width: 600px;
    }

    .diagnosis-treatment-ai-suggestion-empty {
        width: 300px;
    }
}

.outpatient-treatment-diagnosis-surgery-apply-item {
    overflow-y: auto;

    @include scrollBar();

    div {
        padding: var(--abc-padding-normal);
        cursor: pointer;

        &:hover {
            background-color: var(--abc-color-P4);
        }
    }

    div + div {
        border-top: 1px solid var(--abc-color-P6);
    }
}

.recommend-popper-wrapper {
    .diagnosis-treatment-save-btn + .diagnosis-treatment-more-setting {
        margin-left: 0 !important;
    }
}

@media screen and (max-width: 1659px) {
    .diagnosis-treatment-ai-suggestion-popover {
        .diagnosis-treatment-ai-suggestion-reference {
            max-width: 140px;
        }
    }
}

@media screen and (max-width: 1280px) {
    .diagnosis-treatment-ai-suggestion-popover {
        .diagnosis-treatment-ai-suggestion-reference {
            max-width: 120px;
        }
    }
}
</style>
