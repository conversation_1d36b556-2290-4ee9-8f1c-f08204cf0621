import {
    mapActions, mapGetters, mapState,
} from 'vuex';

/**
 * @desc API
 * <AUTHOR>
 * @date 2020/05/11 09:42:07
 */
import SettingAPI from 'api/settings';
import OutpatientAPI from 'api/outpatient';
import CdssAPI from 'api/cdss/index';
import GoodsAPI from 'api/goods';

/**
 * @desc utils
 * <AUTHOR>
 * @date 2020/05/11 09:41:50
 */
import { validateStock } from 'utils/validate';
import clone from 'utils/clone';
import {
    debounce, isEqual,
} from '../../utils/lodash';
import AbcSocket from 'src/views/common/single-socket';
import { getDefaultPharmacy } from 'views/common/pharmacy.js';
import i18n from '@/i18n';
import {
    MedicalRecordTypeEnum,
    getMrTypePropertyKey,
    initExternalMedicalRecord,
} from 'views/outpatient/common/medical-record/utils.js';
import { formatDate } from '@abc/utils-date';
import { getViewDistributeConfig } from '@/views-distribute/utils.js';
import { asyncForEach } from 'utils/lodash.js';
import PatientOrderLockService from 'utils/patient-order-lock-service';
import {
    ChargeBusinessSceneEnum,
    LockBusinessKeyEnum, LockStatusEnum,
} from '@/common/constants/business-lock';
/**
 * @desc 引入组件
 * <AUTHOR>
 * @date 2020/05/11 09:42:53
 */
import MedicalRecordModule from './medical-record-module/medical-record-module.vue';
const MultiMedicalRecordModule = () => import('src/views/layout/multi-medical-record-module/index.vue');
import Interactions from '../layout/interactions.vue';
import DoctorAdvice from './common/medical-record/doctor-advise.vue';
import PatientRegForm from './common/outpatient-patient-reg-form/index.vue';
import OutpatientVerify from './common/outpatient-verify.vue';
import JingMaCompleteDialog from './common/jing-ma-complete-dialog';
import ThreeDaysRepeatModal from 'src/components/three-days-repeat/index';
const ChargeReviewDialog = () => import('./common/review-charge-dialog/review-charge-dialog');
const DiagnosisTreatment = () => import('./diagnosis-treatment/diagnosis-treatment');
import RevisitCreate from 'views/layout/revisit-create/index.vue';
const PrescriptionGroup = () => import('./prescription-group/prescription-group');
const MedicalRecordTemplateDialog = () => import('./common/medical-template-dialog');
import PatientSocial from 'views/crm/common/package-social/index.vue';
import SocialBtn from 'views/crm/common/package-social/social-btn.vue';
const ChronicCareRecord = () => import('src/views/chronic-care/record/index');
import MoreButton from './common/more-button-group/more-button';
import MixinModulePermission from 'src/views/permission/module-permission';
import templateUseMethodDialog from 'src/views/outpatient/diagnosis-treatment/dialog-template-use-method/index.js';
import SocialSettleListReport from 'src/views/layout/social-report/index';
const HospitalInfoCard = () => import('src/views/hospital/hospital-info-card.vue');
import MedicalInsuranceRestriction from 'src/views/layout/medical-insurance-restriction.vue';
import GuardianRecordDialog from 'views/outpatient/common/guardian-record-dialog/index.vue';

/**
 * @desc mixins
 * <AUTHOR>
 * @date 2020/05/11 09:44:27
 */
import prescriptionHandle from './mixins/prescription-handle';
import medicalRecordStruct from './mixins/medical-record-struct';
import printHandle from './mixins/print-handle';
import preSubmitHandle from './mixins/pre-submit-handle';
import inputSelect from 'views/common/input-select';
import diagnosisSocialCodeHandle from 'src/views/common/diagnosis-social-code-handle';

/**
 * @desc const
 * <AUTHOR>
 * @date 2020/05/11 09:45:28
 */
import {
    createMedicalRecord,
    createPostData,
    OutpatientChargeTypeEnum,
    outpatientCompareKey,
    OutpatientStatusEnum,
    OutpatientStatusEnumNew,
    PRCFormCompareKey,
    PsychotropicNarcoticTypeEnum,
    ShebaoPayTypeByModeEnum,
} from './constants.js';
import {
    GoodsSubTypeEnum, GoodsTypeEnum, GoodsTypeIdEnum, PharmacyTypeEnum,
} from '@abc/constants';
import {
    PRCItemCompareKey, AuditResultEnum,
} from './constants';
import {
    ChargeStatusEnum, SourceFormTypeEnum,
} from '@/service/charge/constants';
import HospitalAPI from 'api/hospital';
import DischargeSettlementDialog from 'views/hospital/discharge-settlement';
import { OutpatientHospitalStatusEnum } from 'views/hospital/constants.js';
import {
    calcDataCallback,
    formatSurgeryDetail,
    initKeyId,
    isCompose,
    isSurgery,
    resetCalcHandler,
    simplifyShebaoRestrictData,
    setItemChildrenPayType,
    getItemComposeChildren,
} from 'views/outpatient/utils.js';
import { getEpidemiologicalHistoryByPatientId } from 'utils/handle-scan-qr-code-patient-list';
import {
    createGUID, parseTime,
} from 'utils/index.js';
import ShebaoRestrictAPI from 'api/shebao-restrict.js';
import { isNotNull } from '@/utils';
import {
    getFindMedicineItemStruct,
    resetStockByPharmacyNo,
    updateOutpatientComposeGoods,
    completePrescriptionItem,
} from 'views/layout/prescription/utils.js';
import Logger from 'utils/logger';
import localStorage from 'utils/localStorage-handler.js';
import { RevisitStatus } from '@/assets/configure/constants.js';
import Printer from 'views/print/index.js';
import {
    ExecuteStatus, SearchSceneTypeEnum,
} from 'views/common/enum';
import { ROLE_CONSULTANT_ID } from 'utils/constants';
import { RegistrationCategory } from '@/views-hospital/registered-fee/constant';
import {
    ExternalPRBillingTypeEnum,
    ExternalPRUsageTypeEnum,
    TieFuUsageSubTypeEnum,
    ZhenCiUsageSubTypeEnum,
    AiJiuUsageSubTypeEnum,
    BaGuanUsageSubTypeEnum,
    TuiNaUsageSubTypeEnum,
} from 'views/layout/prescription/constant';
import {
    ShebaoChargeTypeLabel, ShebaoChargeTypeEnum,
} from 'src/views/outpatient/common/shebao-charge-type-config.js';
import GoodsV3API from 'api/goods/index-v3';
import AiAPI from 'api/ai';
import { REGISTERED_FEE_MODE_TYPE } from 'views/settings/diagnosis-treatment/registered-fee/components/constants';
import { GoodsIngredient2PrescriptionFormPsychotropicNarcoticType } from 'views/common/inventory/constants';
import { OutpatientLimitEnum } from 'views/settings/outpatient-setting/constants';
import { isObject } from '@/utils/lodash';
import SelectTemplateUseDialog from 'views/outpatient/medical-record-module/dialog/select-template-use-dialog';
import { ReferralFlagEnum } from '@/common/constants/registration';
import { SignatureTypeEnum } from '@/common/constants/outpatient.js';
import { MedicalTypeSelectionModeEnum } from 'views/settings/outpatient-setting/constants.js';

export default {
    provide() {
        return {
            outpatientEditForm: this,
        };
    },
    components: {
        MedicalRecordModule,
        MultiMedicalRecordModule,
        Interactions,
        PatientRegForm,
        DoctorAdvice,
        OutpatientVerify,
        ChargeReviewDialog,
        DiagnosisTreatment,
        RevisitCreate,
        PrescriptionGroup,
        MedicalRecordTemplateDialog,
        PatientSocial,
        SocialBtn,
        ChronicCareRecord,
        MoreButton,
        HospitalInfoCard,
        MedicalInsuranceRestriction,
        GuardianRecordDialog,
    },
    mixins: [
        inputSelect,
        prescriptionHandle,
        medicalRecordStruct,
        printHandle,
        preSubmitHandle,
        diagnosisSocialCodeHandle,
        MixinModulePermission,
    ],
    computed: {
        ...mapGetters([
            'outpatient',
            'draftOutpatients',
            'userInfo',
            'currentClinic',
            'clinicConfig',
            'currentRegion',
            'printPrescriptionConfig',
            'isOpenMp',
            'chargeConfig',
            'westernMedicineConfig',
            'chainBasic',
            'isElectron',
            'allDepartmentDoctors',
            'userInfo',
            'pharmacyRuleList',
            'isEnableEyeInspectReportV2',
            'cooperationPharmacyList',
            'clinicBasic',
            'chainBasic',
            'isIntranetUser',
        ]),
        ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig', 'clinicMedicalRecordConfig']),
        ...mapGetters('airPharmacy', ['clinicCanUseAirPharmacy']),
        ...mapGetters('virtualPharmacy', ['virtualPharmacyIsOpen']),
        ...mapGetters('chronicCare', ['clinicChronicCareProjects']),
        ...mapGetters('viewDistribute', [
            'featureChronicRecovery',
            'viewComponents',
            'viewDistributeConfig',
        ]),
        ...mapGetters('shebaoRestrict', [
            'restrictSwitch',
        ]),
        ...mapState('socialPc', [
            'basicInfo', // 配置基本信息
        ]),
        showIcpcReport() {
            return this.$abcSocialSecurity.isOpenSocial &&
                this.$abcSocialSecurity.isElectron &&
                this.$abcSocialSecurity.config.isLiaoningShenyang;
        },
        sortedMrStruct() {
            let mrStruct = [];
            if (this.status === OutpatientStatusEnum.UN_DIAGNOSIS) {
                let type = MedicalRecordTypeEnum.WESTERN;
                const config = this.outpatientEmployeeConfig;
                if (config && config.medicalRecord) {
                    type = config.medicalRecord.type || MedicalRecordTypeEnum.WESTERN;
                }
                mrStruct = this.clinicMedicalRecordConfig[getMrTypePropertyKey()[type]];
            } else {
                mrStruct = this.clinicMedicalRecordConfig[getMrTypePropertyKey()[this.postData.medicalRecord.type]];
            }
            if (!(mrStruct && mrStruct.length)) {
                Logger.report({
                    scene: 'outpatient.common.sortedMrStruct',
                    data: {
                        id: this.postData.id,
                        status: this.status,
                        clinicMedicalRecordConfig: this.clinicMedicalRecordConfig,
                        outpatientEmployeeConfig: this.outpatientEmployeeConfig,
                        type: this.postData.medicalRecord.type,
                        mrStruct,
                    },
                });
            }

            return mrStruct && mrStruct.map((x) => {
                return {
                    ...x,
                    required: x.required === 1 && !this.isHospitalSheet,
                };
            });
        },
        isIgnoreExternalUsageType() {
            return this.chainBasic.outpatient.isIgnoreExternalUsageType;
        },
        isOpenOnlineAudit() {
            return this.clinicBasic.isOpenVirtualAudit;
        },
        // 校验医保同日支付：开关开启&&诊断时间与当前时间不是同一天
        isCheckSameDatePaySocial() {
            const { diagnosedDate } = this.postData;
            const diagnosedDateStr = formatDate(diagnosedDate, 'YYYY-MM-DD');
            const currentDateStr = formatDate(new Date(), 'YYYY-MM-DD');
            return this.$abcSocialSecurity.isOpenSameDayMedicalSettleLimit && diagnosedDateStr !== currentDateStr;
        },
        mustAtLeastOneWesternDisease() {
            return this.$abcSocialSecurity.isOpenSocial && this.$abcSocialSecurity.config.isNeedHisDiagnosisWithICD10;
        },
        shebaoChargeTypeDesc() {
            const { shebaoChargeType = 1 } = this.postData;
            return ShebaoChargeTypeLabel[shebaoChargeType];
        },

        doctorAdviceInForm() {
            const { doctorAdviceInForm } = this.viewDistributeConfig.Outpatient;
            return doctorAdviceInForm;
        },

        isBeiJing() {
            return this.currentRegion === 'beijing';
        },
        piecesDefaultPharmacy() {
            return getDefaultPharmacy(this.pharmacyRuleList, {
                departmentId: this.postData.departmentId,
                goodsInfo: {
                    typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                },
            });
        },
        granuleDefaultPharmacy() {
            return getDefaultPharmacy(this.pharmacyRuleList, {
                departmentId: this.postData.departmentId,
                goodsInfo: {
                    typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE,
                },
            });
        },

        // 待诊
        unDiagnosis() {
            return this.isDraft ||
                this.status === OutpatientStatusEnum.UN_DIAGNOSIS ||
                this.status === OutpatientStatusEnum.DRAFT;
        },

        reserveDateStr() {
            return parseTime(this.postData.reserveDate, 'y-m-d', true);
        },
        canChangeDiagnosedDate() {
            return this.unDiagnosis && this.reserveDateStr !== this.todayStr;
        },

        selectedPatient() {
            return this.outpatient.selectedPatient;
        },

        isPrintIncludeTreatment() {
            return this.printPrescriptionConfig.infusionExecute.includeTreatment;
        },

        // 检验检验单打印选项
        examPrintOptions() {
            return this.viewDistributeConfig.Print.examPrintOptions;
        },
        showPsychotropicNarcoticTips() {
            return !!this.verifyOutpatient?.verifyItems?.FITNESS?.find((it) => it.name === 'ControlledSubstancesRule' && it.checkItems?.length);
        },

        printOptions() {
            const res = [
                {
                    value: this._printOptions.MEDICAL.label,
                    disabled: false,
                    printCount: this.printable.printCount,
                },
                {
                    value: this._printOptions.PRESCRIPTION.label,
                    disabled: this.disabledPrintPR,
                    tips: `没有${this._printOptions.PRESCRIPTION.label}`,
                },
                {
                    value: this._printOptions.TREATMENT_EXECUTE.label,
                    disabled: this.disabledPrintTreatmentSheet,
                    tips: `没有${this._printOptions.TREATMENT_EXECUTE.label}`,
                },
                {
                    value: this._printOptions.INFUSION_EXECUTE.label,
                    disabled: this.disabledPrintInfusion,
                    tips: `没有${this._printOptions.INFUSION_EXECUTE.label}`,
                },
                {
                    value: this.examPrintOptions.examination.label,
                    disabled: this.disabledPrintExamination,
                    tips: `没有${this.examPrintOptions.examination.label}`,
                },
                {
                    value: this.examPrintOptions.inspection.label,
                    disabled: this.disabledPrintInspect,
                    tips: `没有${this.examPrintOptions.inspection.label}`,
                },
                {
                    value: this._printOptions.EYE_INSPECT_REPORT_CUSTOM.label,
                    disabled: this.disabledPrintInspectV2,
                    hide: !this.isEnableEyeInspectReportV2,
                },
                {
                    value: this._printOptions.MEDICAL_CERTIFICATE.label,
                    disabled: false,
                },
                {
                    value: this._printOptions.HOSPITAL_HOSPITALIZATION_CERTIFICATE.label,
                    disabled: this.disabledPrintHospitalizationCertificate,
                    hide: !this.viewDistributeConfig.Outpatient.isShowPrintHospitalizationCertificate,
                },
            ];

            return res.filter((item) => !item.hide);
        },
        // 是否有理疗项
        hasPhysicalFormFlag() {
            // flag = true 存在理疗项，可以打印
            const flag = this.postData.productForms.some((form) => {
                const formFlag = form.productFormItems.some((item) => {
                    const subType = item.subType || (item.productInfo && item.productInfo.subType);
                    const type = item.type || (item.productInfo && item.productInfo.type);
                    // 存在单独的理疗项
                    if (type === GoodsTypeEnum.TREATMENT && subType === GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy) return true;
                    // 判断套餐里面的理疗项
                    if (type === GoodsTypeEnum.COMPOSE) {
                        const children = item.children || (item.productInfo && item.productInfo.children) || [];
                        const childrenFlag = children.some((child) => {
                            if (child.type === GoodsTypeEnum.TREATMENT && child.subType === GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy) {
                                return true;
                            }
                        });
                        return childrenFlag;
                    }
                });
                return formFlag;
            });
            return flag;
        },

        // 是否有治疗项
        hasExaminationFlag() {
            let tFlag = false;
            tFlag = this.postData.productForms.some((form) => {
                const formFlag = form.productFormItems.some((item) => {
                    const type = item.type || (item.productInfo && item.productInfo.type);
                    if (type === GoodsTypeEnum.EXAMINATION) {
                        return true;
                    }
                    if (type === GoodsTypeEnum.COMPOSE) {
                        const children = item.children || (item.productInfo && item.productInfo.children) || [];
                        const childrenFlag = children.some((child) => {
                            if (child.type === GoodsTypeEnum.EXAMINATION) {
                                return true;
                            }
                        });
                        return childrenFlag;
                    }
                });
                return formFlag;
            });
            return tFlag;
        },
        // 输注执行单 仅输液处方
        disabledPrintInfusion() {
            if (this.isPrintIncludeTreatment) {
                return (
                    !this.postData.printable.executeTransfusionSheet && !this.postData.printable.executeInfusionSheet
                );
            }
            return !this.postData.printable.executeTransfusionSheet;
        },

        /**
         * @desc 治疗单 治疗理疗项
         */
        disabledPrintTreatmentSheet() {
            return !this.postData.printable.executeTherapySheet;
        },

        // 判断是否能打印检验单
        disabledPrintExamination() {
            return !this.postData.printable.examinationExamination;
        },
        disabledPrintInspect() {
            return !this.postData.printable.examinationInspection;
        },

        disabledPrintInspectV2() {
            return !this.postData.printable.examinationInspectionReportV2;
        },

        // 没有处方的时候禁用打印处方
        disabledPrintPR() {
            return !this.postData.printable.prescription;
        },

        //判断能否打印住院证
        disabledPrintHospitalizationCertificate() {
            return !this.postData.printable.hospitalSheet;
        },
        hasMedicine() {
            const arr = [];
            this.postData.prescriptionChineseForms.forEach((form) => {
                form.prescriptionFormItems.forEach((item) => {
                    if (item.name) {
                        arr.push(item);
                    }
                });
            });
            this.postData.prescriptionWesternForms.forEach((form) => {
                form.prescriptionFormItems.forEach((item) => {
                    arr.push(item);
                });
            });
            this.postData.prescriptionInfusionForms.forEach((form) => {
                form.prescriptionFormItems.forEach((item) => {
                    arr.push(item);
                });
            });
            this.postData.prescriptionExternalForms.forEach((form) => {
                form.prescriptionFormItems.forEach((item) => {
                    arr.push(item);
                });
            });
            return !!arr.length;
        },
        hasProduct() {
            const arr = [];
            this.postData.productForms.forEach((form) => {
                form.productFormItems.forEach((item) => {
                    if (item.name) {
                        arr.push(item);
                    }
                });
            });
            return !!arr.length;
        },
        isGuangZhou() {
            return this.$abcSocialSecurity.isOpenSocial && this.$abcSocialSecurity.config.isGuangdongGuangzhou;
        },
        recordCount() {
            const strArr = [];
            let str = '';
            if (this.status > 0) {
                str = this.diagnoseCount === 1 ? '首次就诊' : `第${this.diagnoseCount}次就诊`;
            } else {
                if (this.selectedPatient && this.selectedPatient.outPatientTimes >= 0) {
                    str =
                        this.selectedPatient.outPatientTimes === 0 ?
                            '首次就诊' :
                            `第${this.selectedPatient.outPatientTimes + 1}次就诊`;
                }
            }
            if (str) {
                strArr.push(str);
            }
            const { shebaoCardInfo } = this.selectedPatient || {};
            if (this.isGuangZhou && shebaoCardInfo) {
                const {
                    lastModified, extend,
                } = shebaoCardInfo || {};
                let currentYearAnnualFundPay = 0;
                // 非本年度统筹费用清0
                if (lastModified && new Date(lastModified).getFullYear() !== new Date().getFullYear()) {
                    currentYearAnnualFundPay = 0;
                } else {
                    const { annualFundPay } = extend || {};
                    if (Number(annualFundPay)) {
                        currentYearAnnualFundPay = annualFundPay;
                    }
                }
                const feeStr = `本年统筹累计：${i18n.t('currencySymbol')}${currentYearAnnualFundPay}`;
                strArr.push(feeStr);
            }
            if (this.postData.referralFlag === ReferralFlagEnum.REVISIT_IN_THREE_DAYS) {
                strArr.push('本次为三日内复诊');
            }
            return strArr.join('，');
        },

        canChargeReview() {
            let count = 0;

            this.postData.productForms.forEach((form) => {
                count += form.productFormItems.filter((item) => {
                    return !item.chargeStatus;
                }).length;
            });
            this.postData.prescriptionChineseForms.forEach((form) => {
                if (!form.chargeStatus) {
                    count += form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
                }
            });
            this.postData.prescriptionWesternForms.forEach((form) => {
                if (!form.chargeStatus) {
                    count += form.prescriptionFormItems.length;
                }
            });
            this.postData.prescriptionInfusionForms.forEach((form) => {
                if (!form.chargeStatus) {
                    count += form.prescriptionFormItems.length;
                }
            });
            this.postData.prescriptionExternalForms.forEach((form) => {
                if (!form.chargeStatus) {
                    count += form.prescriptionFormItems.filter((item) => item.goodsId).length;
                }
            });
            return count;
        },
        /**
         * @desc 慢病档案相关信息
         * <AUTHOR> Yang
         * @date 2021-01-18 10:37:50
         */
        chronicArchivesInfo() {
            const { chronicArchivesInfo } = this.selectedPatient || {};
            return chronicArchivesInfo || {};
        },
        outpatientFormTabOptions() {
            const _arr = [
                {
                    label: '门诊',
                    value: 0,
                },
            ];
            if (this.featureChronicRecovery && this.chainBasic.isEnableChronicRecovery) {
                const { archivesCount } = this.chronicArchivesInfo;
                if (this.clinicChronicCareProjects.length || archivesCount > 0) {
                    _arr.push({
                        label: '慢病管理',
                        value: 1,
                        statisticsNumber: archivesCount > 0 ? archivesCount : undefined,
                    });
                }
            }

            return _arr;
        },

        // 是否展示修改按钮
        showUpdateBtn() {
            return !this.isHospitalDischarge;
        },

        // 是否展示出院明细按钮
        showDischargeBtn() {
            if (!this.hospitalInfo) return false;
            if (!this.isHospitalSheet) return false;
            return this.hospitalInfo.status === OutpatientHospitalStatusEnum.INPATIENT;
        },

        // 住院单是否结算，结算后，相应门诊单不可以修改，不展示出院明细
        isHospitalDischarge() {
            if (!this.hospitalInfo) return false;
            return [
                OutpatientHospitalStatusEnum.DISCHARGE,
                OutpatientHospitalStatusEnum.DISCHARGE_SETTLED,
            ].indexOf(this.hospitalInfo.status) > -1;
        },

        // 是否开启长护接诊
        isHospitalSheet() {
            return !!this.postData.hospitalPatientOrderId;
        },
        /**
         * @desc 是否展示推送支付按钮
         *  开通微诊所 && 非编辑状态 && 待收费状态 && 收费设置开启医生可推送订单给患者 && 门诊tab
         */
        isShowPushPaymentBtn() {
            if (this.isIntranetUser) return false;
            const selectedTab = +this.$route.query.tab || 1;
            const {
                status = 0,
                isOpenMp = false,
                outpatientSheetIsUnCharge = false,
                isEditStatus = false,
            } = this;

            return status === 1 && isOpenMp && outpatientSheetIsUnCharge && !isEditStatus && this.chargeConfig.doctorCanPushChargeSheetSwitch && selectedTab === 1;
        },
        // 回诊
        isContinueDiagnosed() {
            return this.status === 1 && this.subStatus === 1;
        },

        // 是否开启多号种
        isShowRegistrationCategory() {
            return this.viewDistributeConfig.Settings.schedule.isShowRegistrationCategory;
        },

        outpatientSettings() {
            return this.clinicBasic.outpatient.settings;
        },

        // 病历编辑时间限制
        disableUpdateMedicalRecord() {
            // 待诊的不限制
            const {
                diagnosedDate,
                currentTime,
            } = this.postData;
            if (!diagnosedDate || !currentTime) return false;
            const {
                isOpenEditMedicalRecordDiagnosed, medicalRecordUpdateLimit,
            } = this.outpatientSettings;
            if (!isOpenEditMedicalRecordDiagnosed) return true;
            switch (medicalRecordUpdateLimit) {
                case OutpatientLimitEnum.ONE_DAY:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 1);
                case OutpatientLimitEnum.TWO_DAYS:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 2);
                case OutpatientLimitEnum.THREE_DAYS:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 3);
                case OutpatientLimitEnum.SEVEN_DAYS:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 7);
                case OutpatientLimitEnum.HALF_MONTH:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 14);
                case OutpatientLimitEnum.WEEK_2:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 14);
                case OutpatientLimitEnum.WEEK_3:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 21);
                case OutpatientLimitEnum.ONE_MONTH:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 30);
                case OutpatientLimitEnum.TWO_MONTH:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 60);
                case OutpatientLimitEnum.THREE_MONTH:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 90);
                default:
                    return false;
            }
        },

        // 处方、诊疗项目新增时间限制
        disableAddPrescription() {
            // 待诊的不限制
            const {
                diagnosedDate,
                currentTime,
            } = this.postData;
            if (!diagnosedDate || !currentTime) return false;
            const {
                isOpenEditPrescriptionDiagnosed, prescriptionUpdateLimit,
            } = this.outpatientSettings;
            if (!isOpenEditPrescriptionDiagnosed) return true;
            switch (prescriptionUpdateLimit) {
                case OutpatientLimitEnum.ONE_DAY:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 1);
                case OutpatientLimitEnum.TWO_DAYS:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 2);
                case OutpatientLimitEnum.THREE_DAYS:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 3);
                case OutpatientLimitEnum.SEVEN_DAYS:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 7);
                case OutpatientLimitEnum.HALF_MONTH:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 14);
                case OutpatientLimitEnum.WEEK_2:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 14);
                case OutpatientLimitEnum.WEEK_3:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 21);
                case OutpatientLimitEnum.ONE_MONTH:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 30);
                case OutpatientLimitEnum.TWO_MONTH:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 60);
                case OutpatientLimitEnum.THREE_MONTH:
                    return this.currentDateOutLimit(diagnosedDate, currentTime, 90);
                default:
                    return false;
            }
        },
        lockIdentity() {
            const { value } = this.triggerLockedInfo || {};
            return value;
        },
        lockDoctorId() {
            const { employeeId } = this.triggerLockedInfo || {};
            return employeeId;
        },
        // 锁单disable-门诊锁
        disableByLock() {
            if (!this.lockedInfo) return false;
            const {
                businessKey, employeeId,
            } = this.lockedInfo || {};
            if (businessKey === LockBusinessKeyEnum.OUTPATIENT) {
                const { id } = this.userInfo || {};
                return employeeId !== id;
            }
            return false;
        },
        // 锁单disable-收费锁
        disableByChargeLock() {
            if (!this.lockedInfo) return false;
            const {
                businessKey, value,
            } = this.lockedInfo || {};
            const { businessScene } = value || {};
            if (businessKey !== LockBusinessKeyEnum.CHARGE) return false;
            if (
                businessKey === LockBusinessKeyEnum.CHARGE &&
                businessScene !== ChargeBusinessSceneEnum.CHARGE_SHEET_PAY
            ) return false;
            return !!this.lockedInfo;
        },
        lockTips() {
            const {
                businessKey, employeeId, employeeName, value,
            } = this.lockedInfo || {};
            const {
                businessScene, businessDetail,
            } = value || {};
            if (businessKey === LockBusinessKeyEnum.OUTPATIENT) {
                const { id } = this.userInfo || {};
                if (employeeId !== id) {
                    return `医生${employeeName}正在编辑此门诊单，编辑结束前其他人不可同时编辑`;
                }
            }
            if (
                businessKey === LockBusinessKeyEnum.CHARGE &&
                businessScene === ChargeBusinessSceneEnum.CHARGE_SHEET_PAY
            ) {
                const { addedLockStatus } = businessDetail || {};
                if (
                    addedLockStatus === LockStatusEnum.LOCK_PAY_BY_PATIENT_FROM_WE_CLINIC ||
                    addedLockStatus === LockStatusEnum.LOCK_PAY_BY_PATIENT_FROM_DEVICE
                ) {
                    return '患者正在自助支付，当前仅能修改病历';
                }
                return `收费员${employeeName}正在收费中，当前仅能修改病历`;
            }
            return '';
        },
        disabledMRForm() {
            return this.isDiagnosis && !this.isEditStatus || this.disableByLock || this.disableUpdateMedicalRecord;
        },
        isRegistrationRefer() {
            return this.postData.referralFlag === ReferralFlagEnum.REFER;
        },

        // 检查检验已出报告的 ids -> 用于AI 诊疗 对比内容是否变更，进行重新思考
        examReportFinishedIds() {
            if (!this.postData ||
                !this.postData.productForms ||
                !Array.isArray(this.postData.productForms)) return [];

            const ids = [];
            this.postData.productForms.forEach((form) => {
                if (!form || !form.productFormItems || !Array.isArray(form.productFormItems)) return;

                form.productFormItems.forEach((item) => {
                    if (!item || !item.examinationResult || !Array.isArray(item.examinationResult)) return;

                    item.examinationResult.forEach((result) => {
                        if (!result || result.status !== 1) return; // status 1 表示已完成

                        ids.push(result.examinationSheetId);
                    });
                });
            });

            return ids;
        },
        // 就诊类型选择模式
        medicalTypeSelectionMode() {
            return this.clinicBasic.outpatient.settings.medicalTypeSelectionMode;
        },
    },
    created() {
        this._printOptions = this.viewDistributeConfig.Print.printOptions;

        this.fetchPrintAllConfigIfNeed(); // 拉取打印配置
        this.outpatientFormTab = +this.$route.query['form-tab'] || 0;

        this._postDataCache = createPostData();
        this._cadns = [];

        this._fetchOutpatientVerify = debounce(this.fetchOutpatientVerify, 1000, true);
        this._fetchShebaoRestrict = debounce(this.fetchShebaoRestrict, 1000, true);

        if (this.chainBasic.isEnableChronicRecovery) {
            this.$store.dispatch('chronicCare/initProjects');
        }

        /**
         * @desc 监听门诊im消息，当有新的im消息过来，需要根据
         * <AUTHOR>
         * @date 2020/02/17 14:15:48
         */
        const { socket } = AbcSocket.getSocket();
        this._socket = socket;
        this._socket.on('outpatient.new_question_sheet', this.addNewQuestionForm);

        this.$store.dispatch('shebaoRestrict/initRestrictConfig');

        // 处方修改为精麻毒需要完善患者和代办人信息
        this.$abcEventBus.$on('psychotropic-narcotic-info-input', (val) => {
            if ([
                PsychotropicNarcoticTypeEnum.JING_1,
                PsychotropicNarcoticTypeEnum.JING_2,
                PsychotropicNarcoticTypeEnum.MA_ZUI,
                PsychotropicNarcoticTypeEnum.DU,
            ].includes(val)) {
                this.generateJingMaCompleteDialog(false);
            }
        }, this);

        this.$abcEventBus.$on('use-deepseek-suggestion', this.handlerUseDeepseekSuggestion, this);

        if (this.currentClinic) {
            this._key = `${this.currentClinic.clinicId}_${this.currentClinic.userId}`;
        }

        if (this.showIcpcReport) {
            const {
                query, beginDate, endDate,
            } = this.$route;
            if (query.icpc) {
                new SocialSettleListReport({
                    queryInfo: {
                        beginTime: beginDate,
                        endTime: endDate,
                    },
                    store: this.$store,
                }).generateDialog({ parent: this });
            }
        }
    },

    beforeDestroy() {
        this._socket.off('outpatient.new_question_sheet', this.addNewQuestionForm);
        clearTimeout(this._tickTimer);
        this.destroyJingMaCompleteDialog();
        this._auditTimer && clearTimeout(this._auditTimer);
        this.$abcEventBus.$offVmEvent(this._uid);
        this.destroyThreeDaysRepeatDialog();
    },

    watch: {
        outpatientFormTab(val) {
            this.$router.replace({
                query: {
                    ...this.$route.query,
                    'form-tab': val,
                },
            });
        },
    },
    data() {
        return {
            AuditResultEnum,
            RegistrationCategory,
            diagnoseCount: 0,
            loading: false,
            showQuickDialog: false,
            showReviewDialog: false,

            postData: createPostData(),
            lastDiagnosedTime: null,

            validateStock,

            outpatientTotalPrice: 0,

            verifyOutpatient: null,
            medicalInsuranceRestriction: null, // 医保限价-用药合规
            behaviorRestriction: null, // 医保限价-行为合规

            printData: {},
            prPrintData: {},
            executedPrintData: {},

            saveLoading: false,
            finishLoading: false,
            preLoading: false,

            visibleRevisitCreate: false, // 新增随访弹窗
            visibleSocialInfo: false, // 医保刷卡信息

            outpatientFormTab: 0,

            hospitalInfo: null,

            showGuardianRecordDialog: false, // 北京地区小于6岁需要登记监护人信息
            patientGuardian: null,


            pickerOptions: {
                disabledDate(date) {
                    return date > new Date();
                },
            },
            defaultDiagnosedDateStr: parseTime(new Date(), 'y-m-d', true),
            todayStr: parseTime(new Date(), 'y-m-d', true),
            subStatus: 0,
            calcLoading: false,
            doctorEnableCategories: [RegistrationCategory.ORDINARY],
            doctorRegistrationFees: [],
            sideBarKey: createGUID(),
            currentAuditResult: null, // 当前门诊单审核结果
            auditPassLoading: false,
            auditFailLoading: false,

            isChargeLocked: false, // 当前门诊单的收费单锁单状态
            lockChargeIdentity: null, // 锁收费单标识

            lockedInfo: null, // 锁单信息
            triggerLockedInfo: null, // 当前门诊触发锁单
        };
    },
    methods: {
        ...mapActions([
            'updatePatientInfo',
            'fetchPrintAllConfigIfNeed',
            'refreshOutpatientQuickList',
        ]),
        ...mapActions('outpatientConfig', [
            'fetchDepartmentDoctorConfig',
            'updateEmployeeMRConfig',
        ]),

        /**
         * @desc 微诊所填写 已推送后的一般问诊单后，需要插入一条问诊单数据
         * <AUTHOR> Yang
         * @date 2021-01-25 19:02:15
         */
        addNewQuestionForm(data) {
            console.log(data);
            console.log(this.postData.id);
            const {
                outpatientSheetId, id, name,
            } = data;
            if (outpatientSheetId === this.postData.id) {
                this.postData.questionSheets.push({
                    id,
                    name,
                });
            }
        },
        async handlePatientGuardianChange(guardianInfo) {
            this.postData.patientGuardian = guardianInfo;
            this.patientGuardian = guardianInfo;
            this.save('patientForm');
        },

        //  北京地区年龄小于6岁需要填写监护人信息 & 校验精麻药品
        ononlinePreHandler() {
            if (this.showPsychotropicNarcoticTips) {
                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: '处方包含精神药品/麻醉药品，在线复诊时禁止开出，请从处方中删除',
                });
                return true;
            }

            if (!this.postData?.patientGuardian && this.postData.patient.age.year < 6) {
                this.showGuardianRecordDialog = true;
                return true;
            }
        },

        reviewCharge(patientForm) {
            this.$refs[patientForm].validate(
                (valid) => {
                    if (valid) {
                        this.showReviewDialog = true;
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                },
                ['patient-form', 'medical-record-item', 'patient-section-wrapper'],
            );
        },

        async handleOutpatientInfoChange(immediate = false) {
            await this.$nextTick();
            if (immediate) {
                this.fetchOutpatientVerify();
                this.fetchShebaoRestrict();
                return;
            }
            this._fetchOutpatientVerify();
            this._fetchShebaoRestrict();
        },

        /**
         * @desc 药物相互作用的触发条件和门诊用药审核一致
         * <AUTHOR>
         * @date 2019/08/09 14:38:13
         */
        async fetchOutpatientVerify() {
            if (this.disabledForm) return;
            if (!this.hasMedicine) {
                this.verifyOutpatient = null;
            }
            let length = 0;
            length +=
                (this.postData.prescriptionChineseForms && this.postData.prescriptionChineseForms.length) || 0;
            length +=
                (this.postData.prescriptionInfusionForms && this.postData.prescriptionInfusionForms.length) || 0;
            length +=
                (this.postData.prescriptionWesternForms && this.postData.prescriptionWesternForms.length) || 0;
            if (length) {
                const data = await CdssAPI.fetchOutpatientVerify(this.simplifyVerifyData());
                this.verifyOutpatient = data;
            }
        },
        simplifyVerifyData() {
            const prescriptionWesternForms = clone(this.postData.prescriptionWesternForms || []);
            const prescriptionInfusionForms = clone(this.postData.prescriptionInfusionForms || []);
            const prescriptionChineseForms = clone(this.postData.prescriptionChineseForms || []);
            return {
                isControlledSubstances: this.isBeiJing && !!this.isOnline, // 北京网诊
                region: this.currentRegion,
                patient: this.postData.patient,
                prescriptionWesternForms: prescriptionWesternForms.map((form) => {
                    form.isPrescriptionTip = form.psychotropicNarcoticType ? 1 : 0;
                    form.prescriptionFormItems = form.prescriptionFormItems.map((item) => {
                        return {
                            keyId: item.keyId || createGUID(),
                            id: item.id,
                            goodsId: item.goodsId,
                            medicineCadn: item.medicineCadn,
                            name: item.name,
                            ast: item.ast,
                            usage: item.usage,
                            ivgtt: item.ivgtt,
                            IvgttUnit: item.IvgttUnit,
                            freq: item.freq,
                            dosage: item.dosage,
                            dosageUnit: item.dosageUnit,
                            days: item.days,
                            manufacturer: item.manufacturer,
                            ingredient: item.productInfo?.dangerIngredient || null,
                        };
                    });
                    return form;
                }),
                prescriptionInfusionForms: prescriptionInfusionForms.map((form) => {
                    form.isPrescriptionTip = form.psychotropicNarcoticType ? 1 : 0;
                    form.prescriptionFormItems = form.prescriptionFormItems.map((item) => {
                        return {
                            keyId: item.keyId || createGUID(),
                            id: item.id,
                            goodsId: item.goodsId,
                            medicineCadn: item.medicineCadn,
                            name: item.name,
                            ast: item.ast,
                            usage: item.usage,
                            ivgtt: item.ivgtt,
                            IvgttUnit: item.IvgttUnit,
                            freq: item.freq,
                            dosage: item.dosage,
                            dosageUnit: item.dosageUnit,
                            days: item.days,
                            ingredient: item.productInfo?.dangerIngredient || null,
                        };
                    });
                    return form;
                }),
                prescriptionChineseForms: prescriptionChineseForms.map((form) => {
                    form.prescriptionFormItems = form.prescriptionFormItems.map((item) => {
                        return {
                            keyId: item.keyId || createGUID(),
                            id: item.id,
                            goodsId: item.goodsId,
                            medicineCadn: item.medicineCadn,
                            name: item.name,
                            manufacturer: item.manufacturer,
                            type: item.type,
                            subType: item.subType,
                            usage: item.usage,
                            dosage: item.dosage,
                            unitCount: item.unitCount,
                            unit: item.unit || 'g',
                            sort: item.sort,
                            specialRequirement: item.specialRequirement,
                        };
                    });
                    return form;
                }),
            };
        },
        /**
         * @desc 获取医保限价
         * <AUTHOR>
         * @date 2023-04-06 11:15:36
         */
        async fetchShebaoRestrict() {
            if (this.disabledForm) {
                this.medicalInsuranceRestriction = null;
                this.behaviorRestriction = null;
                return;
            }
            if (this.restrictSwitch === 0) return null;

            if (this.restrictSwitch === 2) {
                if (!this.$abcSocialSecurity.isOpenSocial) {
                    this.medicalInsuranceRestriction = null;
                    this.behaviorRestriction = null;
                    return;
                }
                // 自费
                if (this.postData.shebaoChargeType === 1) {
                    this.medicalInsuranceRestriction = null;
                    this.behaviorRestriction = null;
                    return;
                }
            }

            const postData = simplifyShebaoRestrictData(this.postData);

            let length = 0;
            length += (postData.prescriptionChineseForms?.length) || 0;
            length += (postData.prescriptionInfusionForms?.length) || 0;
            length += (postData.prescriptionWesternForms?.length) || 0;
            length += (postData.prescriptionExternalForms?.length) || 0;
            length += (postData.productForms?.length) || 0;
            if (length) {
                const data = await ShebaoRestrictAPI.verify(postData);
                this.medicalInsuranceRestriction = data?.medicateVerifyLists || [];
                this.behaviorRestriction = data?.behaviorVerifyLists || [];
            } else {
                this.medicalInsuranceRestriction = null;
                this.behaviorRestriction = null;
            }
        },

        /**
         * @desc 改变费用类型
         */
        onChangeShebaoChargeType() {
            this.fetchShebaoRestrict();
            this.validateChronicDiseaseInstitution();
        },

        /**
         * @desc 慢特病签约机构校验
         */
        validateChronicDiseaseInstitution() {
            if (!this.$abcSocialSecurity.config.isShandongQingdao) return;
            if (this.postData.shebaoChargeType !== ShebaoChargeTypeEnum.CHRONIC_SPECIAL_DISEASE) return;
            const { shebaoCardInfo } = this.selectedPatient || {};
            if (!shebaoCardInfo) return;
            const { personnelDesignatedInfo } = shebaoCardInfo.extend?.cardInfo || {};
            if (!personnelDesignatedInfo || personnelDesignatedInfo.length === 0) {
                this.$alert({
                    type: 'warn',
                    title: '患者无慢特病签约机构',
                    content: '请确认本次就诊类型',
                });
                return;
            }
            const { hospitalCode } = this.$abcSocialSecurity.basicInfo || {};
            // 每个都是本机构
            if (personnelDesignatedInfo.every((item) => item.value === hospitalCode)) return;
            // 存在本机构签约
            const isExist = personnelDesignatedInfo.some((item) => item.value === hospitalCode);
            let title = '';

            if (!isExist) {
                title = '患者未在本机构签约';
            } else {
                title = '患者存在多个签约机构，请向患者确认本次就诊病种';
            }
            const h = this.$createElement;
            const content = h(
                'abc-flex',
                {
                    props: {
                        vertical: true,
                        gap: 16,
                    },
                },
                personnelDesignatedInfo.map((item) => {
                    return h(
                        'abc-flex',
                        {
                            props: {
                                vertical: true,
                            },
                        },
                        [
                            h('abc-text', item.diseName),
                            h('abc-text', `${item.value} | ${item.label}`),
                        ],
                    );
                }),
            );

            this.$alert({
                type: 'warn',
                size: 'large',
                title,
                content,
            });
        },

        /**
         * @desc
         * <AUTHOR>
         * @date 2024/05/27 16:13:28
         * @param
         * @return
         */
        updateShebaoRestrictResult(postData) {
            try {
                const { patientOrderId } = postData;
                const restrictRef = this.$refs.medicalInsuranceRestriction;
                restrictRef?.updateShebaoRestrictResult(patientOrderId);
            } catch (e) {
                console.error(e);
            }
        },

        copyMRHandler(data, content = '当前病历和处方信息不可编辑，无法复制', needMatchCode = true, isCoverAll = false) {
            // 门诊可编辑状态才能 复制处方
            if (this.disabledMRForm) {
                this.$alert({
                    title: '提示',
                    content,
                });
                return false;
            }

            let {
                type,
            } = data?.medicalRecord ?? {};

            type = this.postData.medicalRecord.type;

            const {
                diagnosis,
            } = data?.medicalRecord ?? {};
            Object.assign(this.postData.medicalRecord, {
                type,
                diagnosis,
            });

            if (!data) return;

            // 长护门诊单不能修改诊断信息
            if (this.isHospitalSheet) {
                data.medicalRecord.extendDiagnosisInfos = this.postData.medicalRecord.extendDiagnosisInfos;
            }

            this.useMRTemplate(data.medicalRecord, needMatchCode, isCoverAll);
        },

        /**
         * @desc 接收 PatientMedicalRecord copy 提供的处方id
         * <AUTHOR>
         * @date 2018/03/23 18:13:59
         * @params onlyPrescription 是否只复制处方
         */
        async copyPRHandler(copyData, onlyPrescription = false, params = {}) {
            if (!copyData) return false;
            let {
                content,
                isOut, // 纯外部用药直接走findName流程
            } = params;
            const {
                fromUseAi,
                resultId,
            } = params;
            content = content || '当前病历和处方信息不可编辑，无法复制';
            isOut = isOut || false;
            // 门诊可编辑状态才能 复制处方
            if (this.disabledForm) {
                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content,
                });
                return false;
            }
            // 完成接诊都要到第一个tab下，进行提交
            if (typeof this.changeTab === 'function') {
                this.changeTab(0);
            }

            /**
             * 过滤掉 西药处方 和 输注处方 和 套餐 中不符合抗菌用药管理的 item
             */
            this.filterByAntimicrobial(copyData, onlyPrescription);

            const goodsArray = [];
            let prescriptionWesternForms = [];
            let prescriptionInfusionForms = [];
            let prescriptionExternalForms = [];
            let prescriptionGlassesForms = [];
            let productForms = [];

            // 删除 原病历信息中的状态，拿到所有的goodsID
            prescriptionWesternForms = this.getPrescriptionForm(
                copyData.prescriptionWesternForms,
                goodsArray,
                true,
            );
            prescriptionInfusionForms = this.getPrescriptionForm(
                copyData.prescriptionInfusionForms,
                goodsArray,
                true,
            );

            if (this.isIgnoreExternalUsageType) {
                copyData.prescriptionExternalForms = copyData.prescriptionExternalForms.map((form) => {
                    form.usageType = null;
                    form.usageSubType = null;
                    form.specification = null;
                    return form;
                });
            }
            prescriptionExternalForms = this.getPrescriptionForm(
                copyData.prescriptionExternalForms,
                goodsArray,
                true,
            );

            // 配镜处方不需要判断goods,所以直接copy即可
            prescriptionGlassesForms = clone((copyData.prescriptionGlassesForms || []).map((form) => {
                delete form.chargeStatus;
                return form;
            }));
            if (!onlyPrescription) {

                const {
                    doctorAdvice = '',
                } = copyData.medicalRecord || {};
                if (copyData.isSingleFormConcat) {
                    this.postData.medicalRecord.doctorAdvice = this.postData.medicalRecord.doctorAdvice || '';
                    if (this.postData.medicalRecord.doctorAdvice) {
                        this.postData.medicalRecord.doctorAdvice += '<br>';
                    }
                    this.postData.medicalRecord.doctorAdvice += doctorAdvice;
                } else {
                    this.postData.medicalRecord.doctorAdvice = doctorAdvice;
                }

                /**
                     * @desc 导入数据会存在没有productId的情况，需要过滤掉，不让他复制出来
                     * <AUTHOR> Yang
                     * @date 2020-09-15 12:45:15
                     */
                copyData.productForms = copyData.productForms.filter((form) => {
                    form.productFormItems = form.productFormItems.filter((item) => item.productId);
                    return form.productFormItems.length;
                });
                productForms = this.getProductForm(copyData.productForms, goodsArray, true);
            }

            /**
             * @desc 这里要走分支
             * @desc 本地药房 复制本地药房走老逻辑stocks直接查
             * @desc 合作药房
             * @desc    1、若合作药房存在走stocks直接查
             * @desc    2、若合作药房不可用切到第一个合作药房走findName换药
             * @desc    3、若合作药房都不可用切到本地药房走findName换药
             * <AUTHOR>
             * @date 2024/08/20 16:17:41
             */
            let finalQueryStocksArray = [];
            let finalFindNameArray = [];
            if (isOut) {
                prescriptionWesternForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((item) => {
                        finalFindNameArray.push({
                            ...item,
                            medicineCadn: item.name,
                            medicineDosageForm: item.medicineDosageForm,
                            medicineIngredient: item.name.replace(new RegExp(item.medicineDosageForm, 'g'), ''),
                        });
                    });
                });
            } else {
                const {
                    queryStocksArray, findNameArray,
                } = this.getSocksOrFindNameGoods(goodsArray);
                finalQueryStocksArray = queryStocksArray;
                finalFindNameArray = findNameArray;
            }


            let goodsStock = [];
            // 如果goodsId 为空 获取药品的库存信息 ;
            if (finalQueryStocksArray.length) {
                const queryGoodsList = this.getQueryGoodsList(finalQueryStocksArray).map((item) => {
                    return {
                        ...item,
                        departmentId: this.postData.departmentId || undefined,
                        sceneType: SearchSceneTypeEnum.outpatient,
                    };
                });
                // 获得所有的药品库存
                const postGoodsId = {
                    clinicId: this.treatOnlineClinicId || undefined,
                    queryGoodsList,
                    withShebaoCode: 1,
                    itemGoodsFirst: 1, // 项目goods优先，会把收费项换掉（场景：诊所升级医院）
                };
                const { data } = await SettingAPI.commonPrescription.fetchPrescriptionTemplateStock(postGoodsId);
                goodsStock = (data && data.list) || [];
            }
            // 根据名字换药
            if (finalFindNameArray.length) {
                const list = finalFindNameArray
                    .map((it) => {
                        return getFindMedicineItemStruct(it);
                    });
                const reqItems = this.getQueryGoodsList(finalFindNameArray).map((x) => {
                    return {
                        jsonTypeWithCustomTypeList: [
                            {
                                typeId: GoodsTypeIdEnum.MEDICINE_WESTERN,
                            },
                            {
                                typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT,
                            },
                            {
                                typeId: GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
                            },
                            {
                                typeId: GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL,
                            },
                            {
                                typeId: GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS,
                            },
                        ],
                        list: x.goods,
                        pharmacyType: x.pharmacyType,
                        pharmacyNo: x.pharmacyNo,
                        departmentId: this.postData.departmentId || undefined,
                        sceneType: SearchSceneTypeEnum.outpatient,
                    };
                });

                if (list.length > 0) {
                    const findApi = isOut ? 'findMedicinesV2' : 'findMedicines';
                    const { list: resList } = await GoodsV3API[findApi]({
                        reqItems,
                    });
                    const goodsList = resList?.length ? resList[0].goodsList : [];
                    const findGoodsList = goodsList.map((item) => item.goods).filter((x) => !!x);
                    goodsStock = goodsStock.concat(findGoodsList);
                    const notFindGoods = goodsList.filter((item) => !item.goods);
                    prescriptionWesternForms.forEach((form) => {
                        form.prescriptionFormItems.forEach((item) => {
                            notFindGoods.forEach((it) => {
                                if (it.keyId === item.keyId && it.medicineIngredient) {
                                    item.name = it.medicineIngredient;
                                }
                            });
                        });
                    });
                }
            }
            // ，判断使用的药品单位以及价格
            prescriptionWesternForms = this.updatePrescriptionForm(prescriptionWesternForms, goodsStock);
            prescriptionInfusionForms = this.updatePrescriptionForm(prescriptionInfusionForms, goodsStock);
            prescriptionExternalForms = this.updatePrescriptionForm(
                prescriptionExternalForms,
                goodsStock,
                SourceFormTypeEnum.PRESCRIPTION_EXTERNAL,
            );

            if (!onlyPrescription) {
                productForms = this.updateProductFormGoodsInfo(productForms, goodsStock);

                // 处理手术申请单
                const {
                    departmentId = '', doctorId = '',
                } = this.postData;
                for (const productForm of productForms) {
                    for (const productFormItem of productForm.productFormItems) {
                        const {
                            productInfo,
                        } = productFormItem;
                        // 手术和套餐需要处理手术申请单
                        if ((isSurgery(productFormItem) || isCompose(productFormItem)) && productInfo) {
                            formatSurgeryDetail(productFormItem, productFormItem, departmentId, doctorId, productInfo.children);
                        }
                        if (productFormItem.composeChildren && productFormItem.composeChildren.length) {
                            productFormItem.composeChildren = productFormItem.composeChildren.map((x) => {
                                return {
                                    ...x,
                                    composeChildren: getItemComposeChildren(x),
                                };
                            });
                        } else {
                            productFormItem.composeChildren = getItemComposeChildren(productInfo);
                        }
                    }
                }

                if (copyData.isSingleFormConcat) {
                    const unChargeForms = this.postData.productForms.filter((form) => !form.chargeStatus);
                    if (copyData.productForms.length && unChargeForms.length) {
                        new templateUseMethodDialog({
                            originProductForms: this.postData.productForms,
                            productForms,
                            title: '复制历史项目',
                            onChange: (results) => {
                                this.postData.productForms = results;
                            },
                        }).generateDialog({ parent: this });
                    } else {
                        this.postData.productForms = this.postData.productForms.concat(productForms);
                    }
                } else {
                    this.postData.productForms = this.postData.productForms.filter((form) => {
                        return form.chargeStatus > 0;
                    });

                    this.postData.productForms = this.postData.productForms.concat(productForms);
                }
            }

            if (isOut) {
                const { physicalExamination } = this.postData.medicalRecord || {};
                prescriptionWesternForms.forEach((form) => {
                    asyncForEach(form.prescriptionFormItems, async (item) => {
                        if (item.goodsId) {
                            await completePrescriptionItem({
                                goods: item.productInfo,
                                prescriptionItem: item,
                                patientInfo: this.postData.patient,
                                physicalExamination,
                                isInfusion: false,
                                prescriptionFormItems: form.prescriptionFormItems,
                            });
                        }
                    });
                });
            }

            this.postData.prescriptionWesternForms = this.postData.prescriptionWesternForms
                .filter((form) => {
                    return form.chargeStatus > 0 || copyData.isSingleFormConcat;
                })
                .concat(prescriptionWesternForms);

            this.postData.prescriptionInfusionForms = this.postData.prescriptionInfusionForms
                .filter((form) => {
                    return form.chargeStatus > 0 || copyData.isSingleFormConcat;
                })
                .concat(prescriptionInfusionForms);

            this.postData.prescriptionExternalForms = this.postData.prescriptionExternalForms
                .filter((form) => {
                    return form.chargeStatus > 0 || copyData.isSingleFormConcat;
                })
                .concat(
                    prescriptionExternalForms.map((form) => {
                        form.prescriptionFormItems.forEach((item) => {
                            if (!item.acupoints.find((it) => !it.id && !it.name)) {
                                item.acupoints.push({
                                    id: null,
                                    name: '',
                                    type: 0,
                                    position: '-',
                                });
                            }
                            if (!item.billingType) {
                                const { bizExtensions } = item.productInfo;
                                const { billingType } = bizExtensions || {};
                                if (billingType) {
                                    item.billingType = billingType;
                                    return;
                                }
                                if (/[一二三四五六七八九十\d]+/.test(item.unit)) {
                                    item.billingType = ExternalPRBillingTypeEnum.MANUAL;
                                } else {
                                    item.billingType = ExternalPRBillingTypeEnum.ACUPOINT;
                                }
                            }
                        });
                        return form;
                    }),
                );

            // 在复制非配镜处方时,不要清除已创建的配镜处方
            if (prescriptionGlassesForms.length) {
                this.postData.prescriptionGlassesForms = prescriptionGlassesForms;
            }

            // 中药包含空中药房
            const prescriptionChineseForms = this.getPrescriptionForm(copyData.prescriptionChineseForms);
            let beforePharmacyType = '';
            let _pharmacyTypeTips = false;
            for (const form of prescriptionChineseForms) {
                // 复制的处方中包含空中药房(代煎代配药房)，但是本店目前空中药房(代煎代配药房)是不可用的，需要提示并且自动转换成本地药房
                if (
                    (!this.clinicCanUseAirPharmacy && form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) ||
                    (!this.virtualPharmacyIsOpen && form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) ||
                    (!this.cooperationPharmacyList.length && form.pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY)
                ) {
                    beforePharmacyType = form.pharmacyType;
                    form.pharmacyName = '本地药房';
                    form.pharmacyNo = 0;//0 代表本地药房，1 代表虚拟药房
                    form.pharmacyType = PharmacyTypeEnum.LOCAL_PHARMACY;
                    form.isDecoction = false;
                    _pharmacyTypeTips = true;
                }
                if (form.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                    const {
                        goodsReqLength, goodsMatchLength,
                    } = await this.updateChineseFormGoodsInfo([form], {
                        departmentId: this.postData.departmentId,
                        sceneType: SearchSceneTypeEnum.outpatient,
                    });
                    if (fromUseAi) {
                        this.reportUseAiResult(resultId, {
                            goodsReqLength,
                            goodsMatchLength,
                        });
                    }
                    // 本地药房加工信息清空
                    // form.usageType = undefined;
                    // form.usageSubType = undefined;
                    // form.processBagUnitCount = undefined;
                    // form.totalProcessCount = undefined;
                } else if (form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                    /**
                     * @desc 空中药房直接取拉取门诊详情中 productInfos 里面的信息
                     * <AUTHOR> Yang
                     * @date 2020-08-04 16:25:02
                     */
                    this.updateAirPharmacyGoodsInfo(form, copyData.productInfos);
                } else if (form.pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY) {
                    await this.updateChineseFormGoodsInfo([form], {
                        departmentId: this.postData.departmentId,
                        sceneType: SearchSceneTypeEnum.outpatient,
                    });
                }
                this.insertAfterCM(form);
            }
            if (_pharmacyTypeTips) {
                let pharmacyName = '';
                switch (beforePharmacyType) {
                    case PharmacyTypeEnum.AIR_PHARMACY:
                        pharmacyName = '空中药房';
                        break;
                    case PharmacyTypeEnum.VIRTUAL_PHARMACY:
                        pharmacyName = '代煎代配药房';
                        break;
                    case PharmacyTypeEnum.COOPERATION_PHARMACY:
                        pharmacyName = '合作药房';
                        break;
                    default:
                        pharmacyName = '';
                }
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: `本店未开通"${pharmacyName}"，该处方将复制为本地药房的处方`,
                });
                beforePharmacyType = '';
            }

            this.postData.prescriptionChineseForms = this.postData.prescriptionChineseForms
                .filter((form) => {
                    return form.chargeStatus > 0 || copyData.isSingleFormConcat;
                })
                .concat(prescriptionChineseForms);

            // 清空空处方
            this.postData.prescriptionWesternForms = this.postData.prescriptionWesternForms.filter((form) => {
                return form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
            });
            this.postData.prescriptionInfusionForms = this.postData.prescriptionInfusionForms.filter((form) => {
                return form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
            });
            this.postData.prescriptionChineseForms = this.postData.prescriptionChineseForms.filter((form) => {
                return form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
            });
            this.postData.prescriptionExternalForms = this.postData.prescriptionExternalForms.filter((form) => {
                return form.prescriptionFormItems.filter((item) => {
                    const acupointsCount = item.acupoints?.filter((acupoint) => acupoint.name).length;
                    const externalGoodsItemsCount = item.externalGoodsItems?.filter((x) => x.name).length;
                    return item.goodsId || item.name || acupointsCount || externalGoodsItemsCount;
                }).length;
            });
            const copyForms = [
                ...prescriptionWesternForms,
                ...prescriptionInfusionForms,
                ...prescriptionChineseForms,
                ...prescriptionExternalForms,
            ];
            const lastKeyId = copyForms[copyForms.length - 1]?.keyId;

            this.$nextTick(() => {
                this.handleOutpatientInfoChange(true);
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '复制处方',
                    needCalcFee: true,
                });
                this.scrollIntoView(lastKeyId);
            });

        },

        copyDoctorAdvice(copyData) {
            if (this.disabledForm) {
                this.$alert({
                    title: '提示',
                    content: '当前病历和处方信息不可编辑，无法写入',
                });
                return false;
            }
            const {
                doctorAdvice = '',
            } = copyData.medicalRecord || {};
            if (!doctorAdvice) return;
            if (!this.postData.medicalRecord.doctorAdvice) {
                this.postData.medicalRecord.doctorAdvice = doctorAdvice;
                return;
            }
            const isDiff = this.postData.medicalRecord.doctorAdvice !== doctorAdvice;
            if (isDiff) {
                new SelectTemplateUseDialog({
                    diffNameArr: ['医嘱建议'],
                    onAppend: () => {
                        const temp = `${this.postData.medicalRecord.doctorAdvice}<br>${doctorAdvice}`;
                        const temArr = temp.split('<br>');
                        const resultArr = temArr.map((item, index) => {
                            return item.replace(/^\d+\./, `${index + 1}.`);
                        });
                        this.postData.medicalRecord.doctorAdvice = resultArr.join('<br>');
                    },
                    onOverwrite: () => {
                        this.postData.medicalRecord.doctorAdvice = doctorAdvice;
                    },
                }).generateDialogAsync();
            }
        },

        async scrollIntoView(id) {
            if (!id) return;
            await this.$nextTick();
            const el = document.querySelector(`#${CSS.escape(id)}`);
            if (el) {
                el.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                });
            }
        },

        /**
         * @desc 在后面新增一个中药item
         * <AUTHOR>
         * @date 2020/04/23 11:31:58
         */
        insertAfterCM(form) {
            if (!form.prescriptionFormItems.find((item) => !item.goodsId && !item.name)) {
                form.prescriptionFormItems.push({
                    unit: 'g',
                    goodsId: null,
                    medicineCadn: '',
                    name: '',
                    specialRequirement: '',
                    unitCount: '',
                });
            }
        },

        updateAirPharmacyGoodsInfo(form, productInfos) {
            // 重置空中药房form keyId
            form.keyId = createGUID();
            form.prescriptionFormItems.forEach((item) => {
                item.keyId = createGUID();
            });
            // 需要对药品基础信息/库存信息进行更新
            // data.productInfos该详单里面药品的实时价格和库存
            if (productInfos && productInfos.length) {
                productInfos.forEach((product) => {
                    // 中药
                    if (product.type === 1 && product.subType === 2) {
                        form.prescriptionFormItems.forEach((item) => {
                            if (product.id === item.goodsId) {
                                item.stockPackageCount = product.stockPackageCount;
                                item.stockPieceCount = product.stockPieceCount;
                                item.realProductInfo = Object.assign({}, item.productInfo, product);
                            }
                        });
                    }
                });
            }
        },

        /**
         * @desc 在完成门诊前、复制病历后 重新算费
         * @desc 口腔算费不需要清空
         * <AUTHOR>
         * @date 2019/10/24 16:58:18
         */
        async refreshFee() {
            /**
             * @desc 提交前需要去算费，去掉已经收费的项目
             * <AUTHOR>
             * @date 2019/10/15 20:48:28
             */
            this.calcLoading = true;
            initKeyId(this.postData);
            const calcData = {
                id: this.postData.id,
                clinicId: this.treatOnlineClinicId || this.currentClinic.clinicId,
                expectedTotalPrice: this.postData.expectedTotalPrice,
                adjustmentFee: this.postData.adjustmentFee,
                productForms: clone(this.postData.productForms || []).map((form) => {
                    form.productFormItems = form.productFormItems.filter((item) => {
                        return !item.chargeStatus;
                    });
                    return form;
                }),
                prescriptionChineseForms: clone(this.postData.prescriptionChineseForms).filter((form) => {
                    form.prescriptionFormItems = form.prescriptionFormItems.filter((item) => {
                        return item.name && item.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE;
                    });
                    return !form.chargeStatus;
                }),
                prescriptionWesternForms: clone(this.postData.prescriptionWesternForms).filter((form) => {
                    form.prescriptionFormItems = form.prescriptionFormItems.filter((item) => {
                        return item.name && item.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE;
                    });
                    return !form.chargeStatus && form.prescriptionFormItems.length;
                }),
                prescriptionInfusionForms: clone(this.postData.prescriptionInfusionForms).filter((form) => {
                    form.prescriptionFormItems = form.prescriptionFormItems.filter((item) => {
                        return item.name && item.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE;
                    });
                    return !form.chargeStatus && form.prescriptionFormItems.length;
                }),
                prescriptionExternalForms: clone(this.postData.prescriptionExternalForms).filter((form) => {
                    return !form.chargeStatus;
                }),
            };
            if (!this.registrationFeeStatus && !this.treatOnlineClinicId && !this.isHospitalSheet) {
                calcData.registrationFee = this.postData.registrationFee;
            }

            try {
                const { data } = await OutpatientAPI.calculate(calcData);
                this.outpatientTotalPrice = data.totalPrice;
                // 挂号费已收 网诊 长护门诊单 不需要修改挂号费
                if (this.registrationFeeStatus || this.treatOnlineClinicId || this.isHospitalSheet) {
                    data.registrationFee = +(this.postData.registrationFee || 0);
                }
                calcDataCallback(this.postData, data, this);
                resetCalcHandler(this.postData);
            } catch (error) {
                console.log('error', error);
                this.$Toast({
                    message: error.message,
                    type: 'error',
                });
            } finally {
                this.calcLoading = false;
            }
        },

        /**
         * @desc 点右侧智能诊断添加方剂
         * <AUTHOR>
         * @date 2020/05/11 14:24:37
         */
        addCPrescription(pr) {
            if (!this.$refs.prescriptionGroup) {
                console.error('need components prescriptionGroup');
                return false;
            }

            const name = pr.symptomName || pr.name;

            this.$confirm({
                title: '提示',
                content: `是否同时调用该方药对应的处方模板“${name}”？`,
                onConfirm: async () => {
                    pr.name = name;
                    pr.category = 4;
                    pr.ownerType = 4;
                    if (pr.prescriptionFormItems) {
                        pr.detail = {
                            prescriptionWesternForms: [],
                            prescriptionInfusionForms: [],
                            prescriptionExternalForms: [],
                            prescriptionGlassesForms: [],
                            prescriptionChineseForms: [
                                {
                                    doseCount: 1,
                                    prescriptionFormItems: pr.prescriptionFormItems,
                                },
                            ],
                        };
                    } else {
                        const res = await OutpatientAPI.fetchTemplateDetail('prescription', pr.id);
                        pr = res.data;
                    }
                    this.$refs.prescriptionGroup.useTemplate(clone(pr));
                },
            });

        },

        /**
         * @desc 点击右侧在线问诊增加病历图片
         * <AUTHOR>
         * @date 2021-07-16 14:57:44
         */
        add2MedicalRecordHandle(data) {
            // 门诊可编辑状态才能添加
            if (this.disabledForm) {
                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: '当前单据未开启编辑，无法添加',
                });
                return false;
            }
            this.postData.medicalRecord.attachments.push(data);
        },
        /**
         * @desc
         */
        getDiffCompareKeys() {
            return outpatientCompareKey.filter((key) => {
                // 中药form单独处理
                if ([
                    'productForms',
                    'prescriptionWesternForms',
                    'prescriptionInfusionForms',
                    'prescriptionChineseForms',
                    'prescriptionExternalForms',
                ].includes(key)) {
                    const forms = this.postData[key] || [];
                    const formsCache = this._postDataCache[key] || [];
                    return this.comparePRCUpdate(forms, formsCache, PRCFormCompareKey);
                }
                /**
                 * @desc medicalRecord attachments 上传图片现在是直接上云，不用比对草稿
                 * <AUTHOR>
                 * @date 2022-01-24 18:39:11
                 */
                if (key === 'medicalRecord') {
                    const medicalRecordCache = clone(this._postDataCache.medicalRecord);
                    const medicalRecord = clone(this.postData.medicalRecord);
                    delete medicalRecord.attachments;
                    delete medicalRecord.auxiliaryExaminations;
                    delete medicalRecordCache.attachments;
                    delete medicalRecordCache.auxiliaryExaminations;
                    if (Array.isArray(medicalRecord.extendDiagnosisInfos)) {
                        medicalRecord.extendDiagnosisInfos = medicalRecord.extendDiagnosisInfos.map((x) => {
                            x.value = (x.value || []).map((y) => {
                                return {
                                    code: y.code,
                                    diseaseType: y.diseaseType,
                                    hint: y.hint,
                                    name: y.name,
                                };
                            });
                            return x;
                        });
                    }
                    if (Array.isArray(medicalRecordCache.extendDiagnosisInfos)) {
                        medicalRecordCache.extendDiagnosisInfos = medicalRecordCache.extendDiagnosisInfos.map((x) => {
                            x.value = (x.value || []).map((y) => {
                                return {
                                    code: y.code,
                                    diseaseType: y.diseaseType,
                                    hint: y.hint,
                                    name: y.name,
                                };
                            });
                            return x;
                        });
                    }

                    return !isEqual(medicalRecordCache, medicalRecord);
                }

                if (key === 'registrationFee') {
                    const registrationFeeCache = Number(this._postDataCache.registrationFee || 0).toFixed(2);
                    const registrationFee = Number(this.postData.registrationFee || 0).toFixed(2);
                    return !isEqual(registrationFeeCache, registrationFee);
                }

                if (key === 'expectedTotalPrice') {
                    const expectedTotalPriceCache = this._postDataCache.expectedTotalPrice ?? '';
                    const expectedTotalPrice = this.postData.expectedTotalPrice ?? '';
                    return !isEqual(expectedTotalPriceCache, expectedTotalPrice);
                }

                // 处理就诊日期
                if (key === 'diagnosedDate') {
                    const {
                        diagnosedDateStr, diagnosedDate,
                    } = this.postData;
                    if (diagnosedDateStr && diagnosedDate) {
                        const diagnosedDateTime = parseTime(diagnosedDate, 'y-m-d h:i:s', true);
                        const diagnosedDateStrTime = parseTime(diagnosedDateStr, 'y-m-d h:i:s', true);
                        if (diagnosedDateTime !== diagnosedDateStrTime) return true;
                    }
                    return !!diagnosedDateStr;
                }

                return !isEqual(this._postDataCache[key], this.postData[key]);

            });
        },

        /**
         * @desc 判断中药处方是否发生修改
         * <AUTHOR> Yang
         * @date 2020-09-09 16:12:24
         */
        comparePRCUpdate(list, listCache, compareKey) {
            let flag = false;

            const _len = list.length;

            if (_len !== listCache.length) {
                return true;
            }
            for (let index = 0; index < _len; index++) {
                const form = list[index];
                const formCache = listCache[index];

                // eslint-disable-next-line no-loop-func
                compareKey.forEach((key) => {
                    if (!isEqual(form[key], formCache[key])) {
                        flag = true;
                    }
                });
                const formItems = form.prescriptionFormItems || form.productFormItems;
                // 判断下一层prescriptionFormItems，判断逻辑相同，所以直接递归
                if (formItems) {
                    const formItemsCache = formCache.prescriptionFormItems || formCache.productFormItems;
                    if (
                        this.comparePRCUpdate(
                            formItems.filter((it) => it.goodsId || it.name),
                            formItemsCache.filter((it) => it.goodsId || it.name),
                            PRCItemCompareKey,
                        )
                    ) {
                        flag = true;
                    }
                }
            }
            return flag;
        },

        /**
         * @desc 选择门诊form的tab
         * <AUTHOR> Yang
         * @date 2020-12-29 17:10:01
         */
        changeOutpatientFormTab(index, tab) {
            if (this.loading) return false;
            this.outpatientFormTab = tab.value;
            this.$refs?.['patient-reg-form']?.handleClosePopover();
        },
        replaceRouter(path) {
            const { routeBasePath } = this.viewDistributeConfig.Outpatient;
            this.$router.replace({
                path: routeBasePath + path,
                query: {
                    tab: this.selectedTab,
                },
            });
        },

        /**
         * @desc 患者档案有 新增||修改
         * <AUTHOR> Yang
         * @date 2021-01-18 18:47:43
         */
        changeChronicCareRecord() {
            this.$store.dispatch('setSelectedPatient', {
                type: 'outpatient',
                patientId: this.postData.patient.id,
                refresh: true,
            });
        },

        /**
         * @desc 修改患者
         * <AUTHOR>
         * @date 2021-10-12 15:08:34
         * @param patient {Object} 患者信息
         * @param markShebaoCardInfo {boolean} 是否标记社保刷卡挂号
         */
        changePatientHandler(patient, markShebaoCardInfo) {
            this.fetchHospitalInfoByPatientId(this.postData.patient);
            this.initEpidemiologicalHistoryHandler();
            this.handleOutpatientInfoChange();
            this.initShebaoChargeType(this.postData.patient, markShebaoCardInfo);
            this.initPatientHealthCard(patient.healthCard);
            this.validateChronicDiseaseInstitution();
        },

        initPatientHealthCard(healthCard) {
            if (!isObject(healthCard)) return;
            this.postData.healthCard = healthCard;
        },

        /**
         * 初始化就诊类型: 门店如果开通了医保 && (绑定过社保卡 || 刷卡的患者) 初始为普通门诊，否则为自付
         * @param patient {Object} 患者信息
         * @param markShebaoCardInfo {boolean} 是否标记社保刷卡挂号
         * @desc 就诊类型选择 EMPTY 默认为空 ONLY_SELF_PAY 只有自费
         */
        async initShebaoChargeType(patient, markShebaoCardInfo) {
            if (this.medicalTypeSelectionMode === MedicalTypeSelectionModeEnum.EMPTY) {
                this.postData.shebaoChargeType = undefined;
                return;
            }
            if (this.medicalTypeSelectionMode === MedicalTypeSelectionModeEnum.ONLY_SELF_PAY) {
                this.postData.shebaoChargeType = ShebaoChargeTypeEnum.SEFL_PAY;
                localStorage.set('shebao_charge_type_last_selected', {
                    userId: this.userInfo.id,
                    shebaoChargeType: ShebaoChargeTypeEnum.SEFL_PAY,
                }, true);
                return;
            }
            // 没开通医保，默认自费
            if (!this.$abcSocialSecurity.isOpenSocial) {
                this.postData.shebaoChargeType = ShebaoChargeTypeEnum.SEFL_PAY;
                return;
            }
            const { shebaoCardInfo } = patient || {};
            if (shebaoCardInfo || markShebaoCardInfo) {
                this.postData.shebaoChargeType = ShebaoChargeTypeEnum.GENERAL_CLINIC;
            } else {
                // 取本地存储的当前用户上次选择的费别
                const storageObj = localStorage.get('shebao_charge_type_last_selected', true, true);
                if (storageObj && this.userInfo.id === storageObj.userId) {
                    // 上次存的慢特病，则默认选中普通门诊
                    if (storageObj.shebaoChargeType === ShebaoChargeTypeEnum.CHRONIC_SPECIAL_DISEASE) {
                        storageObj.shebaoChargeType = ShebaoChargeTypeEnum.GENERAL_CLINIC;
                    }
                    this.postData.shebaoChargeType = storageObj.shebaoChargeType;
                } else {
                    this.postData.shebaoChargeType = ShebaoChargeTypeEnum.SEFL_PAY;
                }
            }
        },

        async initEpidemiologicalHistoryHandler() {
            if (this.disabledForm) return;
            const {
                patient,
                medicalRecord,
                created,
            } = this.postData;
            const epidemiologicalHistory = await getEpidemiologicalHistoryByPatientId(patient?.id, created);
            this.postData.medicalRecord.epidemiologicalHistory = medicalRecord?.epidemiologicalHistory || epidemiologicalHistory;
        },
        changeDoctorHandler() {
            this.updateDoctorSignImgUrl();

            // 变更科室后需要更新处方默认药房
            this.batchUpdatePRFormsDefaultPharmacy(this.postData.productForms);
            this.batchUpdatePRFormsDefaultPharmacy(this.postData.prescriptionWesternForms);
            this.batchUpdatePRFormsDefaultPharmacy(this.postData.prescriptionInfusionForms);
            this.batchUpdatePRFormsDefaultPharmacy(this.postData.prescriptionExternalForms);
            this.batchUpdatePRCFormsDefaultPharmacy();
            updateOutpatientComposeGoods({
                productForms: this.postData.productForms,
                departmentId: this.postData.departmentId,
            });
            this.$emit('change-doctor', {
                doctorId: this.postData.doctorId,
                doctorName: this.postData.doctorName,
            });
        },

        /**
         * @desc 批量更新处方默认药房
         * <AUTHOR>
         * @date 2023-05-25 16:43:01
         */
        batchUpdatePRFormsDefaultPharmacy(PRForms) {
            PRForms.forEach((form) => {
                // 合作药房不更新
                if (form.pharmacyType !== PharmacyTypeEnum.COOPERATION_PHARMACY) {
                    (form.prescriptionFormItems || form.productFormItems)?.forEach((item) => {
                        const pharmacy = getDefaultPharmacy(this.pharmacyRuleList, {
                            departmentId: this.postData.departmentId,
                            goodsInfo: item.productInfo,
                        });
                        if (pharmacy) {
                            item.pharmacyType = pharmacy.type;
                            item.pharmacyNo = pharmacy.no;
                            item.pharmacyName = pharmacy.name;
                            resetStockByPharmacyNo(item);
                        }
                    });
                }
            });
        },

        /**
         * @desc 中药处方批量更新处方默认药房
         * <AUTHOR>
         * @date 2023-06-03 14:47:49
         */
        batchUpdatePRCFormsDefaultPharmacy() {
            this.postData.prescriptionChineseForms.forEach((form) => {
                if (form.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                    const defaultPharmacy = getDefaultPharmacy(this.pharmacyRuleList, {
                        departmentId: this.postData.departmentId,
                        goodsInfo: {
                            typeId: form.specification === '中药颗粒' ?
                                GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE :
                                GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                        },
                        processInfo: {
                            usageType: form.usageType,
                            usageSubType: form.usageSubType,
                        },
                    });

                    Object.assign(form, {
                        pharmacyType: defaultPharmacy.type,
                        pharmacyNo: defaultPharmacy.no,
                        pharmacyName: defaultPharmacy.name,
                    });
                    form.prescriptionFormItems?.forEach((item) => {
                        item.pharmacyType = defaultPharmacy.type;
                        item.pharmacyNo = defaultPharmacy.no;
                        item.pharmacyName = defaultPharmacy.name;
                        resetStockByPharmacyNo(item);
                    });
                }
            });
        },

        refreshOutpatientDetail() {
            if (!this.isEditStatus) {
                this.fetchDetail();
            }
        },

        /**
         * @desc 当拉取详情完成后会调用
         * <AUTHOR>
         * @date 2022-01-06 18:46:50
         */
        registerDetailLoaded(callback) {
            this._onDetailLoaded = callback;
        },

        /**
         * @desc 更新住院信息, 当选择长护需要把住院信息id带过去
         * <AUTHOR>
         * @date 2022-02-11 15:52:57
         */
        changeHospital(data) {
            const { hospitalPatientOrderId } = data;
            this.postData.hospitalPatientOrderId = hospitalPatientOrderId;
        },

        // 获取住院信息，待诊使用patientId拉，已诊使用hospitalPatientOrderId拉
        async fetchHospitalInfoByPatientId(patient) {
            this.hospitalInfo = null;
            this.postData.hospitalPatientOrderId = undefined;

            // 没有登记长护信息不用拉取长护数据
            if (!this.basicInfo) return;
            if (!this.basicInfo.hospitalZCM) return;
            if (!patient) return;
            const { id } = patient;
            if (!id) return;
            try {
                const { data } = await HospitalAPI.fetchHospitalInfoByPatientId(id);
                if (data && data.id) {
                    this.hospitalInfo = data;
                    this.postData.medicalRecord.extendDiagnosisInfos = data.extendDiagnosisInfos;
                } else {
                    this.hospitalInfo = null;
                    this.postData.medicalRecord.extendDiagnosisInfos = [];
                }
            } catch (err) {
                console.error(err);
            }
        },
        async fetchHospitalInfoById(hospitalPatientOrderId) {
            this.hospitalInfo = null;
            // 没有登记长护信息不用拉取长护数据
            if (!this.basicInfo) return;
            if (!this.basicInfo.hospitalZCM) return;
            if (!hospitalPatientOrderId) return;
            try {
                const { data } = await HospitalAPI.fetchHospitalInfoById(hospitalPatientOrderId);
                this.hospitalInfo = (data && data.id) ? data : null;
            } catch (err) {
                console.error(err);
            }
        },

        handleClickDischarge() {
            new DischargeSettlementDialog({
                hospitalInfo: this.hospitalInfo,
                onSuccess: () => {
                    this.fetchDetail();
                },
                chargeConfig: this.chargeConfig,
            }).generateDialog({ parent: this });
        },

        copyHandler(data, type) {
            this.$emit('copy', data, type);
        },

        /**
         * @desc 审方确认签字
         * <AUTHOR>
         * @date 2022-06-06 15:47:36
         */
        handleConfirmSignature(signedMedicineMap) {
            this.postData.prescriptionChineseForms.forEach((form) => {
                form.prescriptionFormItems.forEach((item) => {
                    const signatures = signedMedicineMap.get(item.keyId);
                    this.$set(item, 'verifySignatures', signatures || null);
                });
            });
            this.postData.prescriptionWesternForms.forEach((form) => {
                form.prescriptionFormItems.forEach((item) => {
                    const signatures = signedMedicineMap.get(item.keyId);
                    this.$set(item, 'verifySignatures', signatures || null);
                });
            });
            this.postData.prescriptionInfusionForms.forEach((form) => {
                form.prescriptionFormItems.forEach((item) => {
                    const signatures = signedMedicineMap.get(item.keyId);
                    this.$set(item, 'verifySignatures', signatures || null);
                });
            });
            this.updateDoctorSignImgUrl();
        },

        onShebaoRestrictSignature(data) {
            const {
                isSignature, prescriptionFormDetails,
            } = data || {};
            if (!prescriptionFormDetails || !prescriptionFormDetails.length) return;
            const { keyId } = prescriptionFormDetails[0] || {};
            let isSetSignature = false;
            for (const form of this.postData.prescriptionChineseForms) {
                if (keyId === form.keyId) {
                    this.$set(form, 'verifySignatureStatus', isSignature ? 1 : null);
                    break;
                }
                if (isSetSignature) break;
                for (const item of form.prescriptionFormItems) {
                    if (keyId === item.keyId) {
                        const verifySignatures = item.verifySignatures || [];
                        if (isSignature) {
                            verifySignatures.push({
                                keyId: item.keyId,
                                goodsId: item.goodsId,
                                cadn: item.name,
                                type: SignatureTypeEnum.SHEBAO_RESTRICT,
                                status: 1,
                            });
                        } else {
                            verifySignatures.splice(
                                verifySignatures.findIndex((x) => x.keyId === item.keyId && x.type === SignatureTypeEnum.SHEBAO_RESTRICT),
                                1,
                            );
                        }
                        this.$set(item, 'verifySignatures', verifySignatures);
                        isSetSignature = true;
                        break;
                    }
                }
            }
            this.fetchShebaoRestrict();
        },

        handleSetJingMaDu({
            isSet, detail, val,
        }) {
            const {
                prescriptionName, index,
            } = detail;

            let prescriptionForm = null;
            if (prescriptionName === '中西成药处方') {
                prescriptionForm = this.postData.prescriptionWesternForms[index];
            } else if (prescriptionName === '输注处方') {
                prescriptionForm = this.postData.prescriptionInfusionForms[index];
            }
            if (prescriptionForm) {
                if (isSet) {
                    const psychotropicNarcoticType = GoodsIngredient2PrescriptionFormPsychotropicNarcoticType[val];
                    if (psychotropicNarcoticType) {
                        this.$set(prescriptionForm, 'psychotropicNarcoticType', psychotropicNarcoticType);
                    }
                } else {
                    this.$set(prescriptionForm, 'psychotropicNarcoticType', null);
                }
            }
        },

        updateDoctorSignImgUrl() {
            // 更新医生电子签名
            const doctor = this.allDepartmentDoctors?.find((item) => { return item.doctorId === this.postData.doctorId; });
            if (doctor) {
                this.doctorSignImgUrl = doctor.handSign;
            }
        },

        handleVisitTimeChange(date) {
            this.defaultDiagnosedDateStr = date;
            if (date === this.todayStr) {
                // 选择当天，清空诊断时间
                this.postData.diagnosedDateStr = undefined;
            } else {
                this.postData.diagnosedDateStr = date;
            }
        },

        /**
         * @desc 上报锁单日志
         * <AUTHOR>
         * @date 2023/06/25 09:56:18
         */
        reportLockLog(params, scene = 'outpatient.sheet.lock') {
            Logger.report({
                scene,
                data: {
                    ...params,
                    url: window.location.href,
                    outpatientSheetId: this.postData.id,
                    employeeId: this.userInfo?.id,
                    employeeName: this.userInfo?.name,
                    mobile: this.userInfo?.mobile,
                    scene,
                },
            });
        },


        /**
         * @desc 兼容数据，后台返回数据不满足前端需求，需要对数据做一些处理
         * <AUTHOR>
         * @date 2023-08-24 11:10:52
         * @param data --后台传回 outpatient sheet 数据
         */
        compatibleHandler(data) {
            data.productForms = data.productForms || [];
            // 没有费别类型设置默认费别，刷了医保卡为医保，没刷自费
            if (!data.shebaoChargeType) {
                if (this.medicalTypeSelectionMode === MedicalTypeSelectionModeEnum.EMPTY) {
                    data.shebaoChargeType = undefined;
                } else if (this.medicalTypeSelectionMode === MedicalTypeSelectionModeEnum.ONLY_SELF_PAY) {
                    data.shebaoChargeType = ShebaoChargeTypeEnum.SEFL_PAY;
                    localStorage.set('shebao_charge_type_last_selected', {
                        userId: this.userInfo.id,
                        shebaoChargeType: ShebaoChargeTypeEnum.SEFL_PAY,
                    }, true);
                } else {
                    if (data.shebaoCardInfo) {
                        data.shebaoChargeType = 2;
                    } else {
                        // 取本地存储的当前用户上次选择的费别
                        const storageObj = localStorage.get('shebao_charge_type_last_selected', true, true);
                        if (storageObj && this.userInfo.id === storageObj.userId) {
                            // 上次存的慢特病，默认选中普通门诊
                            if (storageObj.shebaoChargeType === ShebaoChargeTypeEnum.CHRONIC_SPECIAL_DISEASE) {
                                storageObj.shebaoChargeType = ShebaoChargeTypeEnum.GENERAL_CLINIC;
                            }
                            data.shebaoChargeType = storageObj.shebaoChargeType;
                        } else {
                            data.shebaoChargeType = ShebaoChargeTypeEnum.SEFL_PAY;
                        }
                    }
                }
            }
            let index = 0;
            data.productForms.forEach((form) => {
                form.productFormItems.forEach((item) => {
                    index += 1;
                    item.index = index;
                });
            });

            const medicalRecord = createMedicalRecord();
            if (data.medicalRecord) {
                // 创建默认病历结构
                data.medicalRecord = Object.assign(medicalRecord, data.medicalRecord);
            } else {
                // 受门诊个人设置： 默认病历类型影响
                const config = this.outpatientEmployeeConfig;
                if (config && config.medicalRecord) {
                    medicalRecord.type = config.medicalRecord.type || 0;
                }
                data.medicalRecord = medicalRecord;
            }
            data.patient = data.patient || {
                id: null,
                name: '',
                mobile: '',
                sex: '男',
                age: {
                    year: null,
                    month: null,
                    day: null,
                },
                wxOpenId: null,
                isMember: null,
                wxBindStatus: 0,
                appFlag: 0,
                arrearsFlag: 0,
                idCard: null,
                idCardType: null,
                countryCode: '',
                address: {
                    addressProvinceId: null,
                    addressProvinceName: null,
                    addressCityId: null,
                    addressCityName: null,
                    addressDistrictId: null,
                    addressDistrictName: null,
                },
            };
            data.psychotropicNarcoticEmployee = data.psychotropicNarcoticEmployee || {
                name: '',
                sex: '',
                age: {
                    year: null,
                    month: null,
                    day: null,
                },
                idCard: '',
            };
        },

        /**
         * @desc 帮助用户进行一些初始化数据的默认，
         * - 此时是系统默认，不会触发草稿、修改监听
         * - 比如默认科室-医生，挂号费，病历类型，处方等
         * <AUTHOR>
         * @date 2023-08-24 11:14:29
         */
        async initPostData(data, isChildcare) {
            /**
             * @desc 默认填充科室、医生
             * <AUTHOR>
             * @date 2023-08-24 13:56:37
             */
            await this.initDepartmentDoctor(isChildcare);

            this.initConsultant();

            /**
             * @desc 默认填充初复诊状态
             * <AUTHOR>
             * @date 2023-10-27 10:51:38
             */
            await this.initRevisitStatus(data);

            /**
             * @desc 当挂号费已收，需要重置挂号费为最新
             * <AUTHOR>
             * @date 2022-05-27 17:24:05
             */
            if (data.registrationFeeStatus > 0) {
                this.postData.registrationFee = data.registrationFee;
                // 获取医生的挂号费和号种列表
                this.isShowRegistrationCategory && await this.initDoctorRegistrationConfig();
            } else {
                /**
                 * @desc 默认填充挂号费
                 * <AUTHOR>
                 * @date 2023-08-24 13:56:37
                 */
                await this.initRegistrationFee();
            }

            this._tickTimer = setTimeout(() => {
                /**
                 * @desc 待诊情况，根据设置默认打开一个处方
                 * <AUTHOR>
                 * @date 2023-08-24 13:56:37
                 */
                if (this.$refs.prescriptionGroup) {
                    // $refs 形式调用需要注意 Vue 在更新 DOM 时是异步执行；
                    // 同一个 watcher 被多次触发，只会被推入到队列中一次 数据会在下一个事件循环“tick”中更新；
                    // 所以此时要在nextTick中
                    const { changeDefaultPr = () => { } } = this.$refs.prescriptionGroup;
                    const { defaultOpenPrescription } = this.outpatientEmployeeConfig.prescription;
                    changeDefaultPr(defaultOpenPrescription);
                }

                // 同步草稿对比数据
                this._postDataCache = clone(this.postData);
            }, 0);
        },


        /**
         * @desc 初始化 科室-医生 相关数据
         * 只有医生才做默认选中规则
         * 只有待诊情况下 + 没有选择过 初始化才改变默认||改变科室医生
         * <AUTHOR>
         * @date 2019/04/17 14:24:50
         */
        async initDepartmentDoctor(isChildcare = 0) {
            const {
                departmentId,
                doctorId,
            } = this.postData;
            if (departmentId && doctorId) return false;

            const {
                id: curUserId,
                name: curUserName,
            } = this.userInfo || {};

            const allRegFeeList = await this.$store.dispatch('fetchCurrentDoctorRegsFee', {
                isChildcare,
            });
            const curUserRegFeeList = allRegFeeList?.filter((it) => it.doctorId === curUserId);
            // 当前用户自己有科室才默认
            if (!curUserRegFeeList || curUserRegFeeList.length === 0) return;

            const key = `${this.currentClinic.clinicId}_${curUserId}_last_selected_depart`;
            const lastObj = localStorage.get(key, true);

            const hasSelectedIndex = curUserRegFeeList.findIndex((item) => {
                return item.departmentId === lastObj?.departmentId;
            });

            if (hasSelectedIndex > -1) {
                this.postData.departmentId = lastObj.departmentId;
                this.postData.departmentName = lastObj.departmentName;
            } else {
                this.postData.departmentId = curUserRegFeeList[0]?.departmentId;
                this.postData.departmentName = curUserRegFeeList[0]?.departmentName;
            }

            if (!doctorId) {
                this.postData.doctorId = curUserId;
                this.postData.doctorName = curUserName;
            }
            // 是否代录
            this.postData.outpatientSource = +(this.postData.doctorId !== this.userInfo?.id);
        },

        // 初始化咨询师
        initConsultant() {
            const { consultantId } = this.postData;
            if (consultantId) return;
            const {
                id,
                roleIds,
            } = this.userInfo || {};
            if (roleIds.includes(ROLE_CONSULTANT_ID)) {
                this.postData.consultantId = id;
            }
        },

        async fetchRevisitStatus() {
            try {
                const {
                    doctorId,
                    patient,
                } = this.postData;
                const { data } = await OutpatientAPI.getPatientVisitStatus({
                    doctorId,
                    patientId: patient.id,
                });
                this.postData.revisitStatus = data.revisitStatus;
                this.postData._revisitStatus = data.revisitStatus;
                this.lastDiagnosedTime = data.lastDiagnosedTime;
            } catch (e) {
                Logger.error({
                    scene: 'fetchRevisitStatus',
                    err: e,
                });
            }
        },

        async initRevisitStatus(remoteData) {
            const {
                doctorId,
                patient,
                revisitStatus,
            } = this.postData;

            // 挂号过来的，存一份原始初复诊、挂号费状态
            if (remoteData.doctorId) {
                this.postData._revisitStatus = remoteData.revisitStatus;
                this.postData._registrationFee = remoteData.registrationFee;
            }

            // 当前页面上没有患者，默认初诊
            if (!patient.id || !doctorId) {
                this.postData.revisitStatus = RevisitStatus.FIRST;
                this.lastDiagnosedTime = null;
                return;
            }

            // 不认远程、认草稿
            if (revisitStatus > 0) return;

            await this.fetchRevisitStatus();
        },

        /**
         * @desc 初始化挂号费
         * <AUTHOR>
         * @date 2023-08-24 11:29:33
         */
        async initRegistrationFee(isReset = false) {
            const {
                doctorId,
                revisitStatus,
                registrationFee,
                registrationCategory,
                referralFlag,
            } = this.postData;

            // 获取医生的挂号费和号种列表
            await this.initDoctorRegistrationConfig();

            // 有挂号费!==null代表挂号处有修改，不再认初复诊挂号费
            if (!isReset && (!doctorId || registrationFee !== null)) return;

            const doctorFee = this.doctorRegistrationFees.find((item) => item.registrationCategory === registrationCategory) || {};
            const {
                regUnitPrice,
                revisitedRegUnitPrice,
                referralRegUnitPrice,
                referralRevisitedRegUnitPrice,
                isDiffForRevisited,
                revisitedFeeCustomUseRule,
            } = doctorFee || {};

            // 是转诊且开启了多号种(目前就医院有多号种)
            const isReferralAndShowRegistrationCategory = referralFlag === 1 && this.isShowRegistrationCategory;

            // 根据医生的模式取费用(只有医院有转诊费)
            // 初诊/首诊
            const firstVisitedPrice = isReferralAndShowRegistrationCategory ? referralRegUnitPrice : regUnitPrice || 0;
            // 复诊/再诊
            const reVisitedPrice = isReferralAndShowRegistrationCategory ? referralRevisitedRegUnitPrice : revisitedRegUnitPrice || 0;
            if (isDiffForRevisited === REGISTERED_FEE_MODE_TYPE.SHORT_DIFFERENT) {
                if (!this.lastDiagnosedTime) {
                    this.postData.registrationFee = firstVisitedPrice;
                } else {
                    const { effectiveDays } = revisitedFeeCustomUseRule || {};
                    const reserveTime = this.postData?.reserveDate ? new Date(this.postData.reserveDate) : new Date();
                    const reserveDate = new Date(reserveTime.getFullYear(), reserveTime.getMonth(), reserveTime.getDate());
                    const lastTime = new Date(this.lastDiagnosedTime);
                    const lastDate = new Date(lastTime.getFullYear(), lastTime.getMonth(), lastTime.getDate());
                    const differenceDays = Math.round(Math.abs((reserveDate - lastDate) / (24 * 60 * 60 * 1000)));
                    this.postData.registrationFee = differenceDays + 1 > effectiveDays ? firstVisitedPrice : reVisitedPrice;
                }
            } else {
                this.postData.registrationFee = (revisitStatus === RevisitStatus.FIRST) ? firstVisitedPrice : reVisitedPrice || 0;
            }
            this.postData._revisitStatus = revisitStatus;
            this.postData._registrationFee = this.postData.registrationFee;
        },
        async initDoctorRegistrationConfig() {
            const {
                departmentId,
                doctorId,
            } = this.postData;

            // 获取医生的挂号费和号种列表
            const { data } = await SettingAPI.registeredFee.loadDoctorRegistrationFeeByCategories({
                departmentId,
                doctorId,
            });
            this.doctorEnableCategories = data.enableCategories;
            this.doctorRegistrationFees = data.registrationFees || [];
        },

        // 初始化病历部分，可能受预诊影响，这里规则是医生没填，预诊填了就覆盖
        initMedicalRecordHandler(medicalRecord, data) {
            const {
                dentistryExaminations, // 口腔检查
                auxiliaryExaminations,
                extendDiagnosisInfos,
                treatmentPlans,
                disposals,
            } = medicalRecord;

            // 兼容一些null的情况
            if (!dentistryExaminations) {
                medicalRecord.dentistryExaminations = [];
            }
            if (!auxiliaryExaminations) {
                medicalRecord.auxiliaryExaminations = [];
            }
            if (!extendDiagnosisInfos) {
                medicalRecord.extendDiagnosisInfos = [];
            }
            if (!treatmentPlans) {
                medicalRecord.treatmentPlans = [];
            }
            if (!disposals) {
                medicalRecord.disposals = [];
            }

            // 病历附件都用服务器端
            medicalRecord.attachments = data.attachments || [];

            // 预诊信息
            const keyList = [
                'chiefComplaint',
                'presentHistory',
                'pastHistory',
                'physicalExamination',
                'epidemiologicalHistory',
                'dentistryExaminations',
            ];
            keyList.forEach((key) => {
                // 有字符串、数组，都只用判断length不存在就代表没有值
                if ((!medicalRecord[key] || medicalRecord[key].length === 0) && data[key]) {
                    medicalRecord[key] = data[key];
                }
            });
        },

        /**
         * @desc 待诊门诊处理
         * <AUTHOR>
         * @date 2023-08-24 14:16:09
         */
        async unDiagnosisHandler(data, updateDataSignature = false, outpatientSheetId) {
            if ([
                OutpatientStatusEnumNew.WAITING,
                OutpatientStatusEnumNew.DIAGNOSING,
                OutpatientStatusEnumNew.TO_BE_TRIAGED,
            ].indexOf(data.status) === -1) return;

            // 受门诊个人设置： 默认病历类型影响
            const config = this.outpatientEmployeeConfig;
            if (config?.medicalRecord) {
                data.medicalRecord.type = config.medicalRecord.type || 0;
            }

            // 待诊情况需要读一下草稿，读不到就用后台数据
            let _draftObj = null;
            try {
                if (this.draftOutpatients) {
                    _draftObj = this.draftOutpatients.find((it) => {
                        const outpatientId = outpatientSheetId || this.$route.params.id;
                        // id 和 draftId 必须和路由id一致
                        return it.draftId === outpatientId && it.id === outpatientId;
                    });
                }
            } catch (err) {
                console.error(err);
            }

            // 判断是否在草稿中，有草稿直接用草稿
            if (_draftObj) {
                _draftObj.questionSheets = data.questionSheets || [];
                // 配镜处方兼容老草稿
                _draftObj.prescriptionGlassesForms = _draftObj.prescriptionGlassesForms || [];
                if (updateDataSignature) {
                    _draftObj.dataSignature = data.dataSignature;
                }
                if (_draftObj.medicalRecord) {
                    this.initMedicalRecordHandler(_draftObj.medicalRecord, data.medicalRecord);
                }
                if (!_draftObj.psychotropicNarcoticEmployee) {
                    _draftObj.psychotropicNarcoticEmployee = {
                        name: '',
                        sex: '',
                        age: {
                            year: null,
                            month: null,
                            day: null,
                        },
                        idCard: '',
                    };
                }
                this.postData = clone(_draftObj);
            } else {
                Object.assign(this._postDataCache, data);
                this.postData = clone(this._postDataCache);
            }

            await this.$nextTick();
            // 需要根据业务对待诊做一些数据初始化
            await this.initPostData(data);

            //  待诊下，需要拉一下患者医院信息
            this.fetchHospitalInfoByPatientId(this.postData.patient);
        },

        /**
         * @desc 已诊门诊单处理
         * <AUTHOR>
         * @date 2023-08-24 14:28:42
         */
        async diagnosedHandler(data) {
            const {
                multiMedicalRecord, defaultMedicalRecordType,
            } = getViewDistributeConfig().Outpatient;
            if (!multiMedicalRecord) {
                data.medicalRecord.type = defaultMedicalRecordType;
            }
            Object.assign(this._postDataCache, data);

            // 需要对药品基础信息/库存信息进行更新
            // data.productInfos该详单里面药品的实时价格和库存
            if (data.productInfos && data.productInfos.length) {
                this.initProductInfo(this._postDataCache, data);
            }
            this.postData = clone(this._postDataCache);
            this.postData._revisitStatus = data.revisitStatus;
            this.postData._registrationFee = data.registrationFee;
            this.clearDraft();
            await this.fetchHospitalInfoById(data.hospitalPatientOrderId);
            if (this.postData.chargeStatus === ChargeStatusEnum.UN_CHARGE) {
                // 获取医生的号种列表
                await this.initDoctorRegistrationConfig();
            }
            this.loading = false;

            /**
             * @desc 开通微诊所需要去获取二维码
             * <AUTHOR>
             * @date 2019/03/26 17:44:30
             */
            this.printData = clone(data);
            this.doctorSignImgUrl = this.printData.doctorSignImgUrl;

            // 判断是否需要打印
            this.$nextTick(async () => {
                $(document).scrollTop(9999999);
                // 后端无法保证完成打印时金额计算完毕
                // 加入1.5s延迟
                this._timer = setTimeout(() => {
                    const { cache } = Printer;
                    const {
                        outpatient, outpatientNeedPrint,
                    } = cache.get();

                    if (outpatientNeedPrint) {
                        this.print(outpatient);
                        cache.set({ outpatientNeedPrint: false });
                    }
                }, 1500);
            });
            this.postData.adjustmentFee = this.canChargeReview ? data.adjustmentFee : null;
        },

        // 根据门诊单状态与锁库配置判断是否需要锁库
        needUpdateStockCount(flag, status, chargeStatus) {
            // 对开启的不同锁库进行判断
            if (flag && status === OutpatientStatusEnum.DIAGNOSIS) {
                return (flag === 10 && chargeStatus === ChargeStatusEnum.UN_CHARGE) || (flag === 20 && chargeStatus === ChargeStatusEnum.CHARGED);
            }
            return false;
        },

        /**
         * @desc 初始化药品信息
         * <AUTHOR>
         * @date 2019/10/30 15:36:08
         */
        initProductInfo(postData, data) {
            const {
                status, chargeStatus, productInfos,
            } = data;

            const needUpdateStockCount = this.needUpdateStockCount(this.lockFlag, status, chargeStatus);
            // 更新库存数据
            function updateProductInfo(item, product, needUpdateStockCount, isGoods = false) {
                if (needUpdateStockCount) {
                    // 算出当前锁定的数量
                    const count = item.unitCount * (item.doseCount || 1);

                    let lockPieceCount = 0;
                    let lockPackageCount = 0;
                    if (item.productInfo?.type === 1 && item.productInfo?.subType === 2) {
                        lockPackageCount = 0;
                        lockPieceCount = count;
                    } else {
                        if (!item.useDismounting) {
                            lockPackageCount = count;
                            lockPieceCount = 0;
                        } else {
                            lockPieceCount = count % (product.pieceNum || 1);
                            lockPackageCount = ~~(count / (product.pieceNum || 1));
                        }
                    }
                    // 最新药品可售数量
                    const stockPieceCount = (product.stockPieceCount || 0) + lockPieceCount;
                    const stockPackageCount = (product.stockPackageCount || 0) + lockPackageCount;
                    // 更新可售数量
                    item.stockPieceCount = stockPieceCount;
                    item.stockPackageCount = stockPackageCount;
                    if (item.productInfo) {
                        item.productInfo.stockPieceCount = stockPieceCount;
                        item.productInfo.stockPackageCount = stockPackageCount;
                    }
                    item.realProductInfo = Object.assign({}, item.productInfo, product);
                } else {
                    item.realProductInfo = Object.assign({}, item.productInfo, product);
                    // 物资保持之前的逻辑特殊处理（productInfo也是最新数据）
                    if (isGoods) {
                        Object.assign(item.productInfo, product);
                    }
                }
            }

            productInfos.forEach((product) => {
                // 药品
                if (product.type === 1) {
                    // 中药
                    if (product.subType === 2) {
                        postData.prescriptionChineseForms.forEach((form) => {
                            form.prescriptionFormItems.forEach((item) => {
                                if (product.id === item.goodsId) {
                                    // 中药需要剂数
                                    item.doseCount = form.doseCount || 1;
                                    updateProductInfo(item, product, needUpdateStockCount);
                                }
                            });
                        });
                    } else {
                        postData.prescriptionWesternForms.forEach((form) => {
                            form.prescriptionFormItems.forEach((item) => {
                                if (product.id === item.goodsId) {
                                    updateProductInfo(item, product, needUpdateStockCount);
                                }
                            });
                        });
                        postData.prescriptionInfusionForms.forEach((form) => {
                            form.prescriptionFormItems.forEach((item) => {
                                if (product.id === item.goodsId) {
                                    updateProductInfo(item, product, needUpdateStockCount);
                                }
                            });
                        });
                    }
                } else {
                    postData.productForms.forEach((form) => {
                        // 物资材料，商品
                        if (
                            form.sourceFormType === SourceFormTypeEnum.ADDITIONAL_SALE_PRODUCT_FORM ||
                            form.sourceFormType === SourceFormTypeEnum.MATERIAL ||
                            form.sourceFormType === SourceFormTypeEnum.COMPOSE
                        ) {
                            form.productFormItems.forEach((item) => {
                                if (product.id === item.productId) {
                                    updateProductInfo(item, product, needUpdateStockCount, true);
                                }
                            });
                        }
                    });
                }
            });
        },


        updateChinesePR() {
            if (this.disableAddPrescription) return;
            // 中药处方会特殊判断是否修改，这里直接加入到postData就行了
            this.postData.prescriptionChineseForms.forEach((form) => {
                if (!form.prescriptionFormItems.find((item) => !item.name) && !form.chargeStatus) {
                    form.prescriptionFormItems.push({
                        unit: 'g',
                        goodsId: null,
                        medicineCadn: '',
                        unitCount: '',
                        name: '',
                        specialRequirement: '',
                    });
                }
            });
        },

        updateExternalPR(data) {
            if (this.disableAddPrescription) return;
            // 外治处方会默认新加一个空的穴位
            data.prescriptionExternalForms.forEach((form) => {
                if (!form.chargeStatus) {
                    const isExecute = form.prescriptionFormItems.some((formItem) => {
                        return formItem.executeStatus === ExecuteStatus.FINISHED;
                    });
                    if (!isExecute) {
                        form.prescriptionFormItems.forEach((item) => {
                            if (!item.acupoints.find((it) => !it.id && !it.name)) {
                                item.acupoints.push({
                                    id: null,
                                    name: '',
                                    type: 0,
                                    position: '单',
                                });
                            }
                        });
                    }
                }
            });
        },

        clearEquals() {
            this.isEquals = [];
        },

        refreshSideBarKey() {
            this.sideBarKey = createGUID();
        },

        /**
         * @desc 切换科室后需要初始病历设置
         * <AUTHOR>
         * @date 2023-03-03 14:29:34
         */
        async changeDepDoctor({
            isDefault, mainMedicalType,
        }) {
            const { multiMedicalRecord } = getViewDistributeConfig().Outpatient;
            // 非默认科室 && 多病历
            if (!isDefault && multiMedicalRecord) {
                let type = MedicalRecordTypeEnum.WESTERN;
                if (mainMedicalType === 2) {
                    type = MedicalRecordTypeEnum.CHINESE;
                } else if (mainMedicalType === 3) {
                    type = MedicalRecordTypeEnum.ORAL;
                } else if (mainMedicalType === 4) {
                    type = MedicalRecordTypeEnum.OPHTHALMOLOGY;
                }
                if (this.postData.medicalRecord.type !== type) {
                    this.initMRHandler(type);
                }
            }
            this.fetchShebaoRestrict();
        },

        onChangeShebaoPayMode(it) {
            const {
                shebaoPayMode,
                prescriptionFormDetails,
            } = it;
            const {
                prescriptionFormId,
                prescriptionFormItemId,
                keyId,
            } = prescriptionFormDetails?.[0] || {};
            // 设置所有item payType
            this.postData.productForms.forEach((form) => {
                if (form.keyId === prescriptionFormId || form.id === prescriptionFormId) {
                    form.productFormItems.forEach((item) => {
                        if (item.keyId === prescriptionFormItemId || item.id === prescriptionFormItemId) {
                            // item 不合规
                            if (keyId === item.keyId || keyId === item.id) {
                                this.$set(item, 'shebaoPayMode', shebaoPayMode);
                                this.$set(item, 'payType', ShebaoPayTypeByModeEnum[shebaoPayMode]);
                            } else {
                                // children不合规
                                setItemChildrenPayType(item.composeChildren, keyId, shebaoPayMode, this);
                            }
                        }
                    });
                }
            });
            this.postData.prescriptionWesternForms.forEach((form) => {
                if (form.keyId === prescriptionFormId || form.id === prescriptionFormId) {
                    form.prescriptionFormItems.forEach((item) => {
                        if (item.keyId === prescriptionFormItemId || item.id === prescriptionFormItemId) {
                            this.$set(item, 'shebaoPayMode', shebaoPayMode);
                            this.$set(item, 'payType', ShebaoPayTypeByModeEnum[shebaoPayMode]);
                        }
                    });
                }
            });
            this.postData.prescriptionInfusionForms.forEach((form) => {
                if (form.keyId === prescriptionFormId || form.id === prescriptionFormId) {
                    form.prescriptionFormItems.forEach((item) => {
                        if (item.keyId === prescriptionFormItemId || item.id === prescriptionFormItemId) {
                            this.$set(item, 'shebaoPayMode', shebaoPayMode);
                            this.$set(item, 'payType', ShebaoPayTypeByModeEnum[shebaoPayMode]);
                        }
                    });
                }
            });
            this.postData.prescriptionChineseForms.forEach((form) => {
                if (form.keyId === prescriptionFormId || form.id === prescriptionFormId) {
                    form.prescriptionFormItems.forEach((item) => {
                        if (item.keyId === prescriptionFormItemId || item.id === prescriptionFormItemId) {
                            this.$set(item, 'shebaoPayMode', shebaoPayMode);
                            this.$set(item, 'payType', ShebaoPayTypeByModeEnum[shebaoPayMode]);
                        }
                    });
                }
            });
            this.postData.prescriptionExternalForms.forEach((form) => {
                if (form.keyId === prescriptionFormId || form.id === prescriptionFormId) {
                    form.prescriptionFormItems.forEach((item) => {
                        if (item.keyId === prescriptionFormItemId || item.id === prescriptionFormItemId) {
                            // item 不合规
                            if (keyId === item.keyId || keyId === item.id) {
                                this.$set(item, 'shebaoPayMode', shebaoPayMode);
                                this.$set(item, 'payType', ShebaoPayTypeByModeEnum[shebaoPayMode]);
                            } else {
                                // children不合规
                                setItemChildrenPayType(item.composeChildren, keyId, shebaoPayMode, this);
                            }
                        }
                    });
                }
            });
            this.fetchShebaoRestrict();
        },

        // 更新外治处方的商品扩展信息-billingType 计费方式
        async updateGoodsExtensions() {
            try {
                const list = [];
                this.postData.prescriptionExternalForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((item) => {
                        const {
                            billingType, productInfo,
                        } = item;
                        const {
                            goodsId, bizExtensions,
                        } = productInfo;
                        const { billingType: goodsBillingType } = bizExtensions || {};
                        if (!goodsBillingType || goodsBillingType !== billingType) {
                            list.push({
                                goodsId,
                                bizExtension: {
                                    billingType,
                                },
                            });
                        }
                    });
                });
                if (!list.length) return;
                await GoodsAPI.postGoodsExtensions({
                    list,
                });
            } catch (e) {
                console.error(e);
            }
        },

        // 患者需要完善
        needCompletePatientRequired() {
            const {
                name,
                sex,
                age,
                countryCode,
                mobile,
                idCard,
                address,
            } = this.postData.patient;
            if (!age.year && !age.month) return true;
            const {
                addressProvinceId = '',
                addressCityId = '',
                addressDistrictId = '',
            } = address || {};
            const validateObj = {
                name,
                sex,
                countryCode,
                mobile,
                idCard,
                addressProvinceId,
                addressCityId,
                addressDistrictId,
            };
            return Object.keys(validateObj).some((key) => {
                return !validateObj[key];
            });
        },

        generateJingMaCompleteDialog(needInitValidate = true) {
            if (!this.postData.patient.id && !this.postData.patient.name) {
                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: '请先填写患者信息',
                });
                return;
            }
            this._jingMaCompleteDialog = new JingMaCompleteDialog({
                patient: this.postData.patient,
                psychotropicNarcoticEmployee: this.postData.psychotropicNarcoticEmployee,
                needInitValidate,
            });
            this._jingMaCompleteDialog.generateDialogAsync({
                parent: this,
            });
        },

        destroyJingMaCompleteDialog() {
            this._jingMaCompleteDialog && this._jingMaCompleteDialog.destroyDialog();
        },

        /**
         * @desc goods按 pharymacyType + pharymacyNo 分组
         * @date 2024/08/12 18:55:06
         * @param {Array} [{pharmacyType: 1, pharmacyNo: 2, goodsId: '111'}, {pharmacyType: 1, pharmacyNo: 2, goodsId: '222'}]
         * @return {Array} [{pharmacyType: 1, pharmacyNo: 2, goodsIds: ['111', '222']}]
         */
        getQueryGoodsList(goodsList) {
            // 按pharymacyType + pharymacyNo 分组
            const groupedGoods = goodsList.reduce((accumulator, current) => {
                const {
                    pharmacyType, pharmacyNo, goodsId,
                } = current;
                const key = `${pharmacyType}_${pharmacyNo}`;
                if (!accumulator[key]) {
                    accumulator[key] = {
                        key,
                        pharmacyType,
                        pharmacyNo: pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY ? pharmacyNo : undefined,
                        goodsIds: [],
                        goods: [],
                    };
                }
                accumulator[key].goodsIds.push({
                    goodsId,
                });
                accumulator[key].goods.push(current);
                return accumulator;
            }, {});
            // 转换为数组
            return Object.keys(groupedGoods).map((key) => groupedGoods[key]);
        },
        /**
         * @desc 区分哪些goods走stocks查询，哪些走换药
         * <AUTHOR>
         * @date 2024/08/20 16:50:03
         */
        getSocksOrFindNameGoods(goodsList) {
            const queryStocksArray = [];
            const findNameArray = [];
            goodsList.forEach((item) => {
                // 本地药房/合作项目药房
                if (
                    item.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY ||
                    item.pharmacyType === PharmacyTypeEnum.CO_ITEM_PHARMACY
                ) {
                    queryStocksArray.push(item);
                } else if (item.pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY) {
                    // 合作药房未开通-切换成本地药房
                    if (!this.cooperationPharmacyList.length) {
                        findNameArray.push(Object.assign(item, {
                            pharmacyType: PharmacyTypeEnum.LOCAL_PHARMACY,
                            pharmacyNo: null,
                        }));
                    } else {
                        const itemPharmacy = this.cooperationPharmacyList.find((x) => x.no === item.pharmacyNo);
                        if (itemPharmacy) {
                            // 合作药房存在
                            queryStocksArray.push(item);
                        } else {
                            // 合作药房不存在-使用可用的第一个合作药房
                            findNameArray.push(Object.assign(item, {
                                pharmacyType: PharmacyTypeEnum.COOPERATION_PHARMACY,
                                pharmacyNo: this.cooperationPharmacyList[0].no,
                            }));
                        }
                    }
                }
            });

            return {
                queryStocksArray,
                findNameArray,
            };
        },

        /**
         * @desc 修改门诊单前校验
         */
        updateOutpatientPreCheck(confirmCallback, cancelCallback) {
            this.$confirm({
                type: 'warn',
                title: '医保同日就诊结算限制',
                content: '修改时添加项目、处方将生成新收费单，此单无法医保支付。是否继续修改？',
                showClose: false,
                confirmText: '继续修改',
                cancelText: '重新接诊',
                onConfirm: confirmCallback,
                onCancel: () => {
                    if (cancelCallback) {
                        cancelCallback();
                    } else {
                        this.$abcEventBus.$emit('add-patient-outpatient-draft', this.postData.patient);
                    }
                },
            });
        },


        getAuditResult() {
            const _key = 'audit_online_outpatient';
            const auditObj = localStorage.get(_key, true, true) || {};
            const { id } = this.$route.params;
            this.currentAuditResult = auditObj[id];
        },

        auditOnline(result) {
            try {
                const auditType = result === AuditResultEnum.PASS ? 'Pass' : 'Fail';
                const loadingKey = `audit${auditType}Loading`;
                this[loadingKey] = true;
                const _key = 'audit_online_outpatient';
                const auditObj = localStorage.get(_key, true, true) || {};
                const { id } = this.$route.params;
                auditObj[id] = result;
                this._auditTimer = setTimeout(() => {
                    localStorage.set(_key, auditObj, true);
                    this.getAuditResult();
                    this[loadingKey] = false;
                    this.$Toast({
                        message: '操作成功',
                        type: 'success',
                    });
                }, 200 + 500 * Math.random());
            } catch (e) {
                console.error(e);
            }
        },

        /**
         * @desc 当前日期是否超出目标日期limit范围， 超出-true,未超出-false
         * <AUTHOR>
         * @date 2024/02/04 11:04:12
         * @param {Date} targetDate
         * @param {Date} currentDate
         * @param {Number} limit
         * @return {Boolean}
         */
        currentDateOutLimit(targetDate, currentDate, limit) {
            const now = parseTime(new Date(currentDate), 'y-m-d', true);
            const target = parseTime(new Date(targetDate), 'y-m-d', true);
            const timeDiff = Math.abs(new Date(now).getTime() - new Date(target).getTime());
            const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
            return daysDiff >= limit;
        },

        /**
         * @desc 处理ICPC
         */
        async gotoSocialSettle() {
            try {
                if (!this.$abcSocialSecurity.config.isLiaoningShenyang) return;
                const { data } = await OutpatientAPI.getUpgradeAbleICPC(this.patientOrderId);
                if (data) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        confirmText: '前往处理',
                        content: '病历主诉、诊断变更，请再次上传ICPC信息，以确保顺利回款',
                        onConfirm: () => {
                            const { name } = this.postData.patient || {};
                            new SocialSettleListReport({
                                queryInfo: {
                                    psnName: name,
                                },
                                store: this.$store,
                            }).generateDialog({ parent: this });
                        },
                    });
                }
            } catch (err) {
                Logger.error({
                    scene: 'gotoSocialSettle',
                    err,
                });
            }
        },

        /**
         * @desc 获取业务锁
         * <AUTHOR>
         * @date 2022/08/02 10:32:07
         */
        async getOutpatientLockInfo() {
            if (this.isDraft) {
                this.lockedInfo = null;
                this.triggerLockedInfo = null;
                return;
            }
            try {
                this.lockedInfo = null;
                const data = await PatientOrderLockService.getLockInfo({
                    patientOrderId: this.patientOrderId,
                    businessKeys: [LockBusinessKeyEnum.OUTPATIENT, LockBusinessKeyEnum.CHARGE],
                    chargeBusinessScene: ChargeBusinessSceneEnum.CHARGE_SHEET_PAY,
                });
                const {
                    businessKey, result, value, employeeId,
                } = data || {};
                if (result === 1 && value) {
                    this.lockedInfo = data;
                    if (businessKey === LockBusinessKeyEnum.OUTPATIENT) {
                        const { id } = this.userInfo || {};
                        if (employeeId === id) {
                            this.triggerLockedInfo = data;
                            this.sendOutpatientRenew();
                        }
                    }
                }
            } catch (err) {
                console.error(err);
            }
        },

        /**
         * @desc 锁单续期
         * <AUTHOR>
         * @date 2022/08/01 16:46:03
         */
        async sendOutpatientRenew(key = this.patientOrderId) {
            if (!this.lockIdentity) {
                this.clearOutpatientLockTimeOut();
                return;
            }
            if (key !== this.patientOrderId) {
                this.clearOutpatientLockTimeOut();
                return;
            }
            try {
                const res = await OutpatientAPI.lockOutpatientRenew(this.patientOrderId, {
                    businessKey: LockBusinessKeyEnum.OUTPATIENT,
                    value: this.lockIdentity,
                });
                const { result } = res.data || {};
                if (result === 1) {
                    this.continueOutpatientLock(this.sendOutpatientRenew, key);
                } else {
                    this.clearOutpatientLockTimeOut();
                }
            } catch (err) {
                console.error(err);
                this.clearOutpatientLockTimeOut();
            }
        },
        /**
         * @desc 页面驻留时门诊单lock续期
         * <AUTHOR>
         * @date 2022/08/02 11:24:34
         */
        continueOutpatientLock(callback, key, timeout = 1000 * 50) {
            this.clearOutpatientLockTimeOut();
            this._lockOutpatientTimeoutId = setTimeout(() => {
                callback(key);
            }, timeout);
        },

        /**
         * @desc 清除门诊单定时器
         * <AUTHOR>
         * @date 2022/08/02 11:25:57
         */
        clearOutpatientLockTimeOut() {
            if (this._lockOutpatientTimeoutId) {
                clearTimeout(this._lockOutpatientTimeoutId);
            }
            if (this._isUpdateUnWatch) {
                this._isUpdateUnWatch();
            }
        },
        /**
         * @desc 业务解锁
         * <AUTHOR>
         * @date 2022/08/01 16:59:26
         */
        async sendOutpatientUnlock() {
            try {
                if (!this.lockIdentity) return;
                const { id } = this.userInfo || {};
                if (this.lockDoctorId !== id) return;
                await OutpatientAPI.unlockOutpatient(this.patientOrderId, {
                    businessKey: LockBusinessKeyEnum.OUTPATIENT,
                    value: this.lockIdentity,
                });
            } catch (err) {
                console.error(err);
            } finally {
                this.isUpdate = false;
                this.triggerLockedInfo = null;
                this.clearOutpatientLockTimeOut();
            }
        },
        /**
         * @desc 业务加锁
         * <AUTHOR>
         * @date 2022/07/29 09:58:18
         */
        async sendOutpatientLock() {
            if (this.lockIdentity) return;
            try {
                const res = await OutpatientAPI.lockOutpatient(this.patientOrderId, LockBusinessKeyEnum.OUTPATIENT);
                const {
                    result, key,
                } = res.data || {};
                if (result === 1) {
                    this.triggerLockedInfo = res.data;
                    this.continueOutpatientLock(this.sendOutpatientRenew, key);
                }
            } catch (err) {
                console.error(err);
                this.clearOutpatientLockTimeOut();
            }
        },
        /**
         * @desc 锁收费单续期
         * <AUTHOR>
         * @date 2022/09/05 12:24:33
         */
        async sendChargeRenew(key) {
            if (!this.lockChargeIdentity) {
                this.clearChargeLockTimeOut();
                return;
            }
            if (key !== this.patientOrderId) {
                this.clearChargeLockTimeOut();
                return;
            }
            try {
                const res = await OutpatientAPI.lockOutpatientRenew(this.patientOrderId, {
                    businessKey: LockBusinessKeyEnum.CHARGE,
                    value: this.lockChargeIdentity,
                });
                const { result } = res.data || {};
                if (result === 1) {
                    this.continueChargeLock(this.sendChargeRenew, key);
                } else {
                    this.clearChargeLockTimeOut();
                }
            } catch (err) {
                console.error(err);
                this.clearChargeLockTimeOut();
            }
        },
        /**
         * @desc 收费单解锁
         * <AUTHOR>
         * @date 2022/05/31 11:31:52
         */
        async sendChargeUnlock() {
            try {
                if (!this.isChargeLocked) return;
                if (!this.lockChargeIdentity) return;
                await OutpatientAPI.unlockOutpatient(this.patientOrderId, {
                    businessKey: LockBusinessKeyEnum.CHARGE,
                    value: this.lockChargeIdentity,
                });
            } catch (err) {
                console.error(err);
            } finally {
                this.isChargeLocked = false;
                this.lockChargeIdentity = null;
                this.clearChargeLockTimeOut();
            }
        },
        /**
         * @desc 页面驻留时收费单lock续期
         * <AUTHOR>
         * @date 2022/06/06 15:15:36
         */
        continueChargeLock(callback, key, timeout = 1000 * 50) {
            this.clearChargeLockTimeOut();
            this._lockChargeTimeoutId = setTimeout(() => {
                callback(key);
            }, timeout);
        },
        /**
         * @desc 清除收费单定时器
         * <AUTHOR>
         * @date 2022/07/29 09:57:42
         */
        clearChargeLockTimeOut() {
            if (this._lockChargeTimeoutId) {
                clearTimeout(this._lockChargeTimeoutId);
            }
        },
        /**
         * @desc 收费锁单
         * <AUTHOR>
         * @date 2022/05/31 11:14:46
         */
        async sendChargeLock() {
            // 非已诊单
            if (!this.isDiagnosis) {
                this.clearChargeLockTimeOut();
                return;
            }

            // 已诊非编辑状态
            if (this.isDiagnosis && !this.isEditStatus) {
                this.clearChargeLockTimeOut();
                return;
            }
            // 草稿中不处理
            if (this.loading || this.isDraft) {
                this.clearChargeLockTimeOut();
                return;
            }
            try {
                this.isChargeLocked = true;
                const res = await OutpatientAPI.lockOutpatient(this.patientOrderId, LockBusinessKeyEnum.CHARGE);
                const {
                    result, key, value,
                } = res.data || {};
                if (result === 1) {
                    this.lockChargeIdentity = value;
                    this.continueChargeLock(this.sendChargeRenew, key);
                }
            } catch (e) {
                console.error(e);
                this.isChargeLocked = false;
                this.clearChargeLockTimeOut();
            }
        },
        /**
         * @desc 加锁socket消息
         * <AUTHOR>
         * @date 2022/08/01 17:59:33
         */
        onOutpatientLockSocket(socketData, type) {
            const {
                key, employeeId, businessKey, value,
            } = socketData || {};
            if (businessKey !== LockBusinessKeyEnum.OUTPATIENT && businessKey !== LockBusinessKeyEnum.CHARGE) return;
            const { businessScene } = value || {};
            const { id } = this.userInfo || {};
            if (businessKey === LockBusinessKeyEnum.OUTPATIENT) {
                // 锁单来源非自己
                if (key === this.patientOrderId && employeeId !== id) {
                    if (type === 'lock') {
                        this.lockedInfo = socketData;
                    } else {
                        this.lockedInfo = null;
                        this.fetchDetail();
                    }
                }
            }
            if (
                businessKey === LockBusinessKeyEnum.CHARGE &&
                businessScene === ChargeBusinessSceneEnum.CHARGE_SHEET_PAY &&
                key === this.patientOrderId
            ) {
                if (type === 'lock') {
                    this.lockedInfo = socketData;
                } else {
                    this.lockedInfo = null;
                    // 收费解锁后只更新费用项
                    this.updateChargeItems();
                }
            }
        },
        // 收费解锁后更新费用项
        async updateChargeItems() {
            const { data } = await OutpatientAPI.fetch(this.$route.params.id);
            Object.assign(this.postData, {
                productForms: data.productForms,
                prescriptionWesternForms: data.prescriptionWesternForms,
                prescriptionInfusionForms: data.prescriptionInfusionForms,
                prescriptionChineseForms: data.prescriptionChineseForms,
                prescriptionExternalForms: data.prescriptionExternalForms,
            });
            Object.assign(this._postDataCache, {
                productForms: data.productForms,
                prescriptionWesternForms: data.prescriptionWesternForms,
                prescriptionInfusionForms: data.prescriptionInfusionForms,
                prescriptionChineseForms: data.prescriptionChineseForms,
                prescriptionExternalForms: data.prescriptionExternalForms,
            });
            this.isEquals = this.isEquals.filter((x) => ![
                'productForms',
                'prescriptionChineseForms',
                'prescriptionWesternForms',
                'prescriptionInfusionForms',
                'prescriptionExternalForms',
            ].includes(x));
            this.getOutpatientLockInfo();
        },

        async handlerUseDeepseekSuggestion(data) {
            try {
                if (!data) return;
                const {
                    type, value, resultId,
                } = data;
                if (type === 'medicalRecord') {
                    this.useAiMedicalRecord(clone(value));
                } else if (type === 'doctorAdvice') {
                    this.copyDoctorAdvice({
                        medicalRecord: {
                            doctorAdvice: value,
                        },
                    }, false, '当前病历和处方信息不可编辑，无法写入');
                    this.scrollIntoView('doctor-advice');
                    this.initMedicalRecordStruct();
                } else if (type === 'prescriptionChineseForm') {
                    this.copyPRHandler({
                        isSingleFormConcat: true,
                        prescriptionChineseForms: [
                            {
                                specification: '中药饮片',
                                pharmacyType: PharmacyTypeEnum.LOCAL_PHARMACY,
                                prescriptionFormItems: value,
                            },
                        ],
                    }, false, {
                        content: '当前病历和处方信息不可编辑，无法写入',
                        fromUseAi: true,
                        resultId,
                    });
                } else if (type === 'prescriptionWesternForms') {
                    const medicineList = value.map((x) => {
                        return {
                            groupId: null,
                            keyId: createGUID(),
                            pharmacyType: PharmacyTypeEnum.LOCAL_PHARMACY,
                            type: GoodsTypeEnum.MEDICINE,
                            name: x.name,
                            medicineDosageForm: x.medicineDosageForm,
                        };
                    });
                    this.useAiWesternTemplate(medicineList, resultId);
                } else if (type === 'prescriptionExternalForms') {
                    this.useAiExternalTemplate(value);
                }
            } catch (err) {
                console.error(err);
            }
        },

        async useAiWesternTemplate(data, resultId) {
            try {
                if (!data || data.length < 0) return false;
                if (this.disabledForm) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '当前病历和处方信息不可编辑，无法写入',
                    });
                    return false;
                }
                // 完成接诊都要到第一个tab下，进行提交
                if (typeof this.changeTab === 'function') {
                    this.changeTab(0);
                }

                const findNameArray = data.map((item) => {
                    return {
                        ...item,
                        medicineCadn: item.name,
                        medicineDosageForm: item.medicineDosageForm,
                        medicineIngredient: item.name.replace(new RegExp(item.medicineDosageForm, 'g'), ''),
                    };
                });

                const prescriptionFormItems = findNameArray.map((item) => {
                    return {
                        keyId: item.keyId || createGUID(),
                        ast: item.ast,
                        days: item.days,
                        dosage: item.dosage,
                        dosageUnit: item.dosageUnit,
                        freq: item.freq,
                        goodsId: item.goodsId,
                        groupId: item.groupId,
                        ivgtt: item.ivgtt,
                        ivgttUnit: item.ivgttUnit,
                        manufacturer: item.manufacturer,
                        medicineCadn: item.medicineCadn,
                        name: item.name,
                        cMSpec: item.cMSpec,
                        productInfo: item.productInfo,
                        sort: item.sort,
                        specialRequirement: item.specialRequirement,
                        specification: item.specification,
                        subType: item.subType,
                        type: item.type,
                        unit: item.unit,
                        doseCount: item.doseCount || 1,
                        unitCount: item.unitCount,
                        unitPrice: item.sourceUnitPrice || item.unitPrice,
                        usage: item.usage,
                        useDismounting: item.useDismounting,
                        stockPackageCount: 0,
                        stockPieceCount: 0,
                        payType: null,
                        pharmacyType: item.pharmacyType,
                        pharmacyNo: item.pharmacyNo,
                    };
                });

                const reqItems = this.getQueryGoodsList(findNameArray).map((x) => {
                    return {
                        jsonTypeWithCustomTypeList: [
                            {
                                typeId: GoodsTypeIdEnum.MEDICINE_WESTERN,
                            },
                            {
                                typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT,
                            },
                            {
                                typeId: GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
                            },
                            {
                                typeId: GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL,
                            },
                            {
                                typeId: GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS,
                            },
                        ],
                        list: x.goods,
                        pharmacyType: x.pharmacyType,
                        pharmacyNo: x.pharmacyNo,
                        departmentId: this.postData.departmentId || undefined,
                        sceneType: SearchSceneTypeEnum.outpatient,
                    };
                });

                const { list } = await GoodsV3API.findMedicinesV2({
                    reqItems,
                });
                const goodsList = list?.length ? list[0].goodsList : [];

                prescriptionFormItems.forEach((item) => {
                    goodsList.forEach((x) => {
                        if (item.keyId === x.keyId) {
                            if (x.goods) {
                                item.goodsId = x.goods.goodsId;
                                item.name = x.goods.displayName;
                                item.productInfo = x.goods;
                            } else if (x.medicineIngredient) {
                                item.name = x.medicineIngredient;
                                item.medicineCadn = x.medicineIngredient;
                            }
                        }
                    });
                });
                asyncForEach(prescriptionFormItems, async (item) => {
                    if (item.goodsId) {
                        await completePrescriptionItem({
                            goods: item.productInfo,
                            prescriptionItem: item,
                            patientInfo: this.postData.patient,
                            physicalExamination: this.postData.medicalRecord?.physicalExamination,
                            isInfusion: false,
                            prescriptionFormItems,
                        });
                    }
                });

                // 实现往this.postData.prescriptionWesternForms最后一个form的prescriptionFormItems中插入prescriptionFormItems的内容
                // 如果加入过程中长度大于5，prescriptionWesternForms push一个form继续在新的form的prescriptionFormItems中继续插入剩余内容
                if (prescriptionFormItems && prescriptionFormItems.length > 0) {
                    // 确保prescriptionWesternForms存在
                    if (!this.postData.prescriptionWesternForms) {
                        this.postData.prescriptionWesternForms = [];
                    }

                    // 如果没有form，创建第一个form
                    if (this.postData.prescriptionWesternForms.length === 0) {
                        this.postData.prescriptionWesternForms.push({
                            prescriptionFormItems: [],
                            pharmacyType: PharmacyTypeEnum.LOCAL_PHARMACY,
                            keyId: createGUID(),
                        });
                    }

                    // 获取最后一个form
                    let currentForm = this.postData.prescriptionWesternForms[this.postData.prescriptionWesternForms.length - 1];

                    // 遍历prescriptionFormItems，将其添加到form中
                    for (let i = 0; i < prescriptionFormItems.length; i++) {
                        // 如果当前form的prescriptionFormItems长度已经达到或超过5，创建新的form
                        if (currentForm.prescriptionFormItems && currentForm.prescriptionFormItems.length >= 5) {
                            // 创建新的form
                            const newForm = {
                                prescriptionFormItems: [],
                                pharmacyType: PharmacyTypeEnum.LOCAL_PHARMACY,
                                keyId: createGUID(),
                            };

                            // 添加到prescriptionWesternForms
                            this.postData.prescriptionWesternForms.push(newForm);

                            // 更新当前form为新创建的form
                            currentForm = newForm;
                        }

                        // 确保prescriptionFormItems存在
                        if (!currentForm.prescriptionFormItems) {
                            currentForm.prescriptionFormItems = [];
                        }

                        // 添加项到当前form的prescriptionFormItems
                        currentForm.prescriptionFormItems.push(prescriptionFormItems[i]);
                    }

                    const lastKeyId = this.postData.prescriptionWesternForms[this.postData.prescriptionWesternForms.length - 1]?.keyId;

                    this.$nextTick(() => {
                        this.handleOutpatientInfoChange(true);
                        this.refreshFee();
                        this.scrollIntoView(lastKeyId);
                    });
                }
                this.reportUseAiResult(resultId, {
                    goodsReqLength: reqItems[0].list.length,
                    goodsMatchLength: goodsList.filter((x) => !!x.goods)?.length,
                });

            } catch (err) {
                Logger.error({
                    scene: 'useAiWesternTemplate',
                    err,
                });
            }
        },

        async useAiMedicalRecord(value) {
            const data = await initExternalMedicalRecord(value, this.postData.medicalRecord.type);

            if (!this.postData.patient.id && data.obstetricalHistory) {
                this.postData.patient.sex = '女';
            }

            this.copyMRHandler({
                medicalRecord: data,
            }, '当前病历和处方信息不可编辑，无法写入', false, true);
            this.scrollIntoView('relatedProduct');
        },

        /**
         * @desc 采纳率上报
         */
        async reportUseAiResult(resultId, data) {
            try {
                if (!resultId) return;
                const {
                    goodsReqLength,
                    goodsMatchLength,
                } = data;

                const adoptionMatchRate = Number((goodsMatchLength / goodsReqLength).toFixed(4));
                await AiAPI.putAnalysisResult(resultId, {
                    extendInfo: {
                        adoptionMatchRate,
                    },
                });
            } catch (err) {
                console.log('err', err);

                Logger.error({
                    scene: 'updateAiResult',
                    err,
                });
            }
        },
        async useAiExternalTemplate(data) {
            if (this.disabledForm) {
                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: '当前病历和处方信息不可编辑，无法写入',
                });
                return false;
            }

            const {
                usageType, acupoints, dosage, externalGoodsItems, freq, specialRequirement,
            } = data || {};

            const useAcupoints = acupoints || [];
            useAcupoints.push({
                id: null,
                name: '',
                type: 0,
                position: '单',
            });

            let usageSubType = null;
            let specification = null;
            let externalGoodsItemsList = null;

            const usageTypeObj = localStorage.getObj('external_pr_last_use_usage_type', this._key, true);
            if (usageTypeObj && typeof usageTypeObj[usageType] === 'number') {
                usageSubType = usageTypeObj[usageType];
            }
            switch (usageType) {
                case ExternalPRUsageTypeEnum.tieFu:
                    usageSubType = isNotNull(usageSubType) ? usageSubType : TieFuUsageSubTypeEnum.xianPeiTie;
                    if (usageSubType === TieFuUsageSubTypeEnum.xianPeiTie) {
                        specification = localStorage.getObj('_PRESCRIPTION_SPECIFICATION_', 'external', true) || '中药饮片';
                        if (externalGoodsItems.length) {
                            const reqList = externalGoodsItems.map((item) => {
                                return {
                                    keyId: createGUID(),
                                    medicineCadn: item.name,
                                    pieceUnit: item.unit,
                                    cMSpec: specification,
                                    unitCount: item.unitCount,
                                };
                            });
                            const medicineTypeId = specification === '中药颗粒' ? GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE : GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES;
                            const { list } = await GoodsV3API.findMedicines({
                                reqItems: [
                                    {
                                        jsonTypeWithCustomTypeList: [
                                            {
                                                typeId: GoodsTypeIdEnum.CHINESE_MEDICINE,
                                            },
                                            {
                                                typeId: medicineTypeId,
                                            },
                                        ],
                                        pharmacyType: PharmacyTypeEnum.LOCAL_PHARMACY,
                                        departmentId: this.postData.departmentId,
                                        sceneType: SearchSceneTypeEnum.outpatient,
                                        list: reqList,
                                    },
                                ],
                            });
                            if (list && list.length) {
                                const { goodsList } = list[0];
                                externalGoodsItemsList = goodsList.map((item) => {
                                    const { unitCount } = reqList.find((x) => x.keyId === item.keyId);
                                    if (item.goods) {
                                        const {
                                            cMSpec, goodsId, medicineCadn, manufacturer, subType, type, unit,
                                        } = item.goods;

                                        return {
                                            cMSpec,
                                            goodsId,
                                            name: medicineCadn,
                                            medicineCadn,
                                            manufacturer,
                                            subType,
                                            type,
                                            unit,
                                            unitCount,
                                        };
                                    }
                                    return {
                                        name: item.medicineCadn,
                                        unitCount,
                                    };

                                });
                            }
                        }
                    }

                    break;
                case ExternalPRUsageTypeEnum.zhenCi:
                    usageSubType = isNotNull(usageSubType) ? usageSubType : ZhenCiUsageSubTypeEnum.haoZhen;
                    break;
                case ExternalPRUsageTypeEnum.aiJiu:
                    usageSubType = isNotNull(usageSubType) ? usageSubType : AiJiuUsageSubTypeEnum.aiZhuJiu;
                    break;
                case ExternalPRUsageTypeEnum.baGuan:
                    usageSubType = isNotNull(usageSubType) ? usageSubType : BaGuanUsageSubTypeEnum.cupping;
                    break;
                case ExternalPRUsageTypeEnum.tuiNa:
                    usageSubType = isNotNull(usageSubType) ? usageSubType : TuiNaUsageSubTypeEnum.adultTuiNa;
                    break;
                default:
                    break;
            }
            const prescriptionExternalForm = {
                keyId: createGUID(),
                doseCount: 1,
                usageType,
                usageSubType,
                specification,
                prescriptionFormItems: [
                    {
                        keyId: createGUID(),
                        externalGoodsItems: externalGoodsItemsList,
                        acupoints: useAcupoints,
                        dosage,
                        freq,
                        specialRequirement,
                        goodsId: null,
                        name: '',
                        type: '',
                        subType: '',
                        unitPrice: '',
                        externalUnitCount: null,
                        unit: '',
                        unitCount: 0,
                        billingType: ExternalPRBillingTypeEnum.ACUPOINT,
                    },
                ],
            };
            this.postData.prescriptionExternalForms = this.postData.prescriptionExternalForms.filter((form) => {
                return form.prescriptionFormItems.filter((item) => {
                    const acupointsCount = item.acupoints?.filter((acupoint) => acupoint.name).length;
                    const externalGoodsItemsCount = item.externalGoodsItems?.filter((x) => x.name).length;
                    return item.goodsId || item.name || acupointsCount || externalGoodsItemsCount;
                }).length;
            });
            this.postData.prescriptionExternalForms.push(prescriptionExternalForm);
            const lastKeyId = this.postData.prescriptionExternalForms[this.postData.prescriptionExternalForms.length - 1]?.keyId;
            this.$nextTick(() => {
                this.handleOutpatientInfoChange(true);
                this.refreshFee();
                this.scrollIntoView(lastKeyId);
                this.$abcEventBus.$emit('autoFocusSearch', lastKeyId);
            });
        },

        async getThreeDaysRepeatData() {
            try {
                if (this.postData.chargeStatus > ChargeStatusEnum.UN_CHARGE) return;
                if (!this.$abcSocialSecurity.config.isLiaoningShenyang) return;
                if (!this.postData.patient.id) return;
                if (this.postData.referralFlag === ReferralFlagEnum.REVISIT_IN_THREE_DAYS) return;
                const { data } = await OutpatientAPI.fetchPatientRevisitUse(this.postData.patient.id, {
                    departmentId: this.postData.departmentId,
                    doctorId: this.postData.doctorId,
                    patientOrderId: this.postData.patientOrderId,
                    withShebaoCharge: 1,
                });
                return data?.rows;
            } catch (err) {
                Logger.error({
                    scene: 'getThreeDaysRepeatData',
                    err,
                });
            }
        },

        generateThreeDaysRepeatDialog({
            tableData,
            continueSubmit,
        }) {
            this._threeDaysRepeatModal = new ThreeDaysRepeatModal({
                value: true,
                title: '三日复诊确认',
                confirmSelectText: '三日复诊',
                confirmDirectText: '继续完成接诊',
                titleDesc: '患者三天内有相同科室的就诊记录，请关联一条初诊记录或继续完成接诊：',
                tableData,
                continueSubmit,
                selectSubmit: (params) => {
                    const {
                        referralPatientOrderId,
                        referralFlag,
                    } = params;
                    this.postData.referralPatientOrderId = referralPatientOrderId;
                    this.postData.referralFlag = referralFlag;
                    this.referralSource = {};
                    continueSubmit();
                },
            });
            this._threeDaysRepeatModal.generateDialogAsync({
                parent: this,
            });
        },

        destroyThreeDaysRepeatDialog() {
            this._threeDaysRepeatModal && this._threeDaysRepeatModal.destroyDialog();
        },

        handleUpdatePatientInfo(patient) {
            this.postData.patient = patient;
            this._postDataCache.patient = clone(this.postData.patient);
        },

        async failHandler(err) {
            const _this = this;
            const {
                code, detail, message,
            } = err;
            if (code === 16007 || code === 16052) {
                this.refreshOutpatientQuickList();

                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: message,
                    onClose() {
                        _this.fetchDetail(true);
                    },
                });
            } else if (code === 16008) {
                this.$alert({
                    type: 'warn',
                    title: '药品信息发生变化!',
                    content: [message],
                    onClose: () => {
                        // 计算西药处方价格
                        this.postData.prescriptionWesternForms.forEach((pr) => {
                            pr.prescriptionFormItems.forEach((item) => {
                                if (item.id === detail.id || item.goodsId === detail.goodsId) {
                                    Object.assign(item, detail);
                                }
                            });
                        });
                        // 计算输液处方价格
                        this.postData.prescriptionInfusionForms.forEach((pr) => {
                            pr.prescriptionFormItems.forEach((item) => {
                                if (item.id === detail.id || item.goodsId === detail.goodsId) {
                                    Object.assign(item, detail);
                                }
                            });
                        });
                    },
                });
            } else if (code === 16003) {
                this.refreshOutpatientQuickList();
                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: message,
                    onClose: () => {
                        this.selectFirst();
                    },
                });
            } else if (code === 16067) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: ['门诊单价格发生变化，请在费用预览处确认价格！'],
                    showCancel: false,
                    onConfirm: () => {
                        this.showReviewDialog = true;
                    },
                });
            } else if (code === 15500) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: ['门诊单初复诊状态未更新'],
                    showCancel: false,
                    confirmText: '更新',
                    closeAfterConfirm: false,
                    onConfirm: async (modelVm) => {
                        modelVm.confirmLoading = true;
                        await this.fetchRevisitStatus();
                        await this.initRegistrationFee(true);
                        modelVm.confirmLoading = false;
                        modelVm.close();
                    },
                });
            }

            // 外治处方会默认新加一个空的穴位
            this.postData.prescriptionExternalForms.forEach((form) => {
                if (!form.chargeStatus) {
                    form.prescriptionFormItems.forEach((item) => {
                        if (!item.acupoints.find((it) => !it.id && !it.name)) {
                            item.acupoints.push({
                                id: null,
                                name: '',
                                type: 0,
                                position: '单',
                            });
                        }
                    });
                }
            });
        },
    },
};
