import { getViewDistributeConfig } from '@/views-distribute/utils';
import AbcAccess from '@/access/utils';
import { MedicalRecordKeyLabelObj } from '@/common/constants/outpatient.js';
import SocialAPI from 'api/social.js';
import clone from 'utils/clone';

export * from '@/common/utils/format-diagnosis';

import { ClearKeyArr } from '@/common/utils/medical-record';

export const MedicalRecordTypeEnum = Object.freeze({
    // 西医格式
    WESTERN: 0,
    // 中医格式
    CHINESE: 1,
    // 口腔
    ORAL: 2,
    // 眼科格式
    OPHTHALMOLOGY: 3,
    // 专业口腔复诊
    REV_ORAL: 4,
});

export const getMrTypeOralPropertyKey = () => {
    let oralKey = 'oral';
    const { multiMedicalRecord } = getViewDistributeConfig().Outpatient;
    // 口腔诊所
    if (!multiMedicalRecord) {
        oralKey = 'dentistry';
    } else if (AbcAccess.getAccessByKey(AbcAccess.accessMap.MULTI_MEDICAL_RECORD_ORAL)) {
        // 购买了专业口腔
        oralKey = 'dentistry';
    } else {
        // 普通口腔
        oralKey = 'oral';
    }
    return oralKey;
};

export const getMrTypePropertyKey = () => {
    return {
        // 西医格式
        [MedicalRecordTypeEnum.WESTERN]: 'western',
        // 中医格式
        [MedicalRecordTypeEnum.CHINESE]: 'chinese',
        // 专业口腔
        [MedicalRecordTypeEnum.ORAL]: getMrTypeOralPropertyKey(),
        // 眼科格式
        [MedicalRecordTypeEnum.OPHTHALMOLOGY]: 'ophthalmology',
        // 专业口腔复诊
        [MedicalRecordTypeEnum.REV_ORAL]: 'revisitDentistry',
    };
};





function getListStructObj(str) {
    let value = null;
    const _reg = /[(（]([是|否|有|无])[)）]/;
    const matchResult = str.match(_reg);
    if (matchResult) {
        value = matchResult[1];
    }
    const _reg2 = /(）（有\S+症状）)|(）（从事\S+相关工作）)$/;
    const _reg3 = /(（有\S+旅居史）)|(（有\S+接触史）)$/;
    return {
        label: str.replace(_reg, '').replace(_reg2, '）').replace(_reg3, ''),
        value,
    };
}



export function getEpidemiologicalHistoryObj(str) {
    const symptomList = [];
    const suspiciousList = [];

    if (!str) {
        return {
            symptomList,
            suspiciousList,
        };
    }

    str = str.trim();

    const suspiciousReg = /(患者（及陪同者）|陪同者)：/;
    const reg = /[；;。.]/;
    const list = str.split(reg);

    list.filter((item) => {
        // 过滤自动插入的字段
        return item;
    }).forEach((item) => {
        const _matchResult = item.match(suspiciousReg);
        const arr = item.split('：');
        if (_matchResult && arr.length === 2) {
            symptomList.push({
                label: arr[0],
                isSuspicious: true,
            });
            symptomList.push(getListStructObj(arr[1]));
        } else {
            symptomList.push(getListStructObj(item));
        }
    });

    let _index = symptomList.findIndex((it) => it.isSuspicious);
    if (_index === -1) {
        // 只选患者，默认不拼接，需要找到第一个流调项目前插入 isSuspicious 标记
        _index = symptomList.findIndex((it) => it.label.match(/(\d+)天内/));
        if (_index > -1) {
            symptomList.splice(_index, 0, {
                label: '',
                isSuspicious: true,
            });
        }
    }

    return {
        symptomList,
        suspiciousList,
    };
}


export function getObstetricalHistoryList(str) {
    const reg = /[；;,，。.]/;
    return str.trim()
        .split(reg)
        .map((item) => {
            const oDiv = document.createElement('div');
            oDiv.innerHTML = item.toString();

            if (item.indexOf('class="menstruation"') > -1) {

                if (item.trim().replace(/<br>/g, '') === '<span class="menstruation"></span>') {
                    return '';
                }

                let menophaniaAge = '';
                let menopauseDate = '';
                let menstruationDays = [];
                let menstrualCycle = [];
                let menopauseAge = '';

                const $menstruation = oDiv.firstChild;
                if ($menstruation.firstChild && $menstruation.firstChild.nodeName === '#text') {
                    menophaniaAge = +$menstruation.firstChild.data.trim();
                }
                if ($menstruation.lastChild && $menstruation.lastChild.nodeName === '#text') {
                    const _str = $menstruation.lastChild.textContent.trim();
                    if (_str.indexOf('LMP') > -1) {
                        menopauseDate = _str.replace('LMP', '').trim();
                    } else {
                        menopauseAge = +_str.trim();
                    }
                }
                Array.prototype.forEach.call(($menstruation.children || []), (child) => {
                    if (child) {
                        menstruationDays = child.firstElementChild?.textContent.trim().split('~') || [];
                        menstrualCycle = child.lastElementChild?.textContent.trim().split('~') || [];
                    }
                });

                return {
                    type: 'menstruation',
                    menophaniaAge,
                    menstruationDays: menstruationDays.map((it) => +it),
                    menstrualCycle: menstrualCycle.map((it) => +it),
                    menopauseTab: menopauseDate ? 1 : 0,
                    menopauseDate,
                    menopauseAge,
                };

            }
            if (item.indexOf('class="pregnant"') > -1) {
                let pregnantCount = '';
                let birthCount = '';
                Array.prototype.forEach.call(oDiv.children, (child) => {
                    if (child) {
                        const reg = /孕\s+(\d+)(?:\s+)?产\s+(\d+)/;
                        const matchResult = child.textContent.match(reg);
                        if (matchResult) {
                            pregnantCount = matchResult[1] ? +matchResult[1] : 1;
                            birthCount = matchResult[2] ? +matchResult[2] : 1;
                        }
                    }
                });
                return {
                    type: 'pregnant',
                    birthCount,
                    pregnantCount,
                };

            }
            return item;

        }).filter((item) => {
            return item && item !== '<br>';
        });
}

export function formatRangeStr2(range) {
    if (range[0] === range[1]) return range[0];
    return `${range[0]}-${range[1]}`;
}



/**
 * @desc 生成诊断提示html
 * <AUTHOR>
 * @date 2021-07-15 10:06:00
 */
export function getWarnDiagnosisHtml(diagnosisInfos, needWarn = false, needAtLeastOneWestern = false) {
    if (!diagnosisInfos) return '';
    let hasWesternDiagnosis = false;
    if (needAtLeastOneWestern) {
        // hint为医保ICD10都是西医诊断 - by 小枫
        hasWesternDiagnosis = diagnosisInfos.some((it) => it.hint === '医保ICD10');
    }
    return diagnosisInfos.map((it) => {
        let { name } = it;
        const {
            code,
            diseaseType,
        } = it;
        if (diseaseType === '4') {
            name += '【规】';
        }
        if (needAtLeastOneWestern && !hasWesternDiagnosis) {
            return `<a data-tipsy="医保结算要求必须开立至少一条西医诊断，若不使用医保结算请忽略此提示" class="abc-tipsy--n shortage-tips">
                            <i class="name">${name}</i>
                    </a>`;
        }
        if (needWarn) {
            if (code) {
                return name;
            }
            return `<a data-tipsy="医保结算要求使用标准诊断名称，若不使用医保结算请忽略此提示" class="abc-tipsy--n shortage-tips">
                            <i class="name">${name}</i>
                    </a>`;
        }
        return name;
    }).join('<i contenteditable="false">，</i>');
}

export function getDefaultMRStruct(mrType, defaultVal = 0) {
    if (mrType === MedicalRecordTypeEnum.OPHTHALMOLOGY) {
        return {
            chiefComplaint: 1, // 主诉
            presentHistory: 1, // 现病史
            pastHistory: 1, // 既往史
            wearGlassesHistory: 1, // 戴镜史

            allergicHistory: defaultVal, // 过敏史
            personalHistory: defaultVal, // 个人史
            familyHistory: defaultVal, //家庭史
            obstetricalHistory: defaultVal, // 月经婚育史
            epidemiologicalHistory: defaultVal, // 流行病史

            physicalExamination: defaultVal, // 体格检查
            eyeExamination: 1, // 眼部检查
            auxiliaryExaminations: defaultVal, // 辅助检查
            diagnosis: 1, // 诊断
            symptomTime: 0, // 发病日期

            treatmentPlans: defaultVal, // 治疗计划
            disposals: defaultVal, // 处置
            doctorAdvice: 1, // 医嘱事项
            eyeExaminationProducts: [
                'eyelid',
                'cornea',
                'conjunctiva',
                'atria',
                'iris',
                'tearOrgan',
                'pupil',
                'lens',
                'sclera',
                'vitreum',
                'fundus',
            ],
        };
    }
    if (mrType === MedicalRecordTypeEnum.ORAL) {
        const oralKey = getMrTypeOralPropertyKey();
        if (oralKey === 'dentistry') {
            return {
                chiefComplaint: 1, // 主诉
                presentHistory: 1, // 现病史
                pastHistory: 1, // 既往史
                familyHistory: defaultVal, //家庭史

                allergicHistory: defaultVal, // 过敏史
                personalHistory: defaultVal, // 个人史
                obstetricalHistory: defaultVal, // 月经婚育史
                epidemiologicalHistory: defaultVal, // 流行病史
                physicalExamination: defaultVal, // 体格检查

                dentistryExaminations: 1, // 口腔检查
                auxiliaryExaminations: defaultVal, // 辅助检查
                diagnosis: 1, // 诊断
                symptomTime: 0, // 发病日期
                treatmentPlans: defaultVal, // 治疗计划

                disposals: 1, // 处置
                doctorAdvice: defaultVal, // 医嘱建议
            };
        }
        return {
            chiefComplaint: 1, // 主诉
            presentHistory: 1, // 现病史
            pastHistory: 1, // 既往史
            allergicHistory: defaultVal, // 过敏史
            familyHistory: defaultVal, //家庭史
            personalHistory: defaultVal, //个人史
            obstetricalHistory: defaultVal, // 月经婚育史
            epidemiologicalHistory: defaultVal, // 流行病史
            physicalExamination: 1, // 体格检查
            chineseExamination: 0, // 望闻问切
            oralExamination: 1, // 口腔检查
            auxiliaryExaminations: defaultVal, // 辅助检查
            diagnosis: 1, // 诊断
            symptomTime: 0, // 发病日期
            syndrome: 0, // 辩证
            therapy: 0, // 治法
            chinesePrescription: 0, // 方药
            doctorAdvice: 1, // 医嘱事项
        };
    }
    if (mrType === MedicalRecordTypeEnum.CHINESE) {
        return {
            chiefComplaint: 1, // 主诉
            presentHistory: 1, // 现病史
            pastHistory: 1, // 既往史
            allergicHistory: defaultVal, // 过敏史
            familyHistory: defaultVal, //家庭史
            personalHistory: defaultVal, //个人史
            obstetricalHistory: defaultVal, // 月经婚育史
            epidemiologicalHistory: defaultVal, // 流行病史
            physicalExamination: 0, // 体格检查
            chineseExamination: 1, // 望闻问切
            oralExamination: 0, // 口腔检查
            auxiliaryExaminations: 0, // 辅助检查
            syndromeTreatment: defaultVal, // 辨证论治
            diagnosis: 1, // 诊断
            symptomTime: 0, // 发病日期
            syndrome: 1, // 辩证
            therapy: defaultVal, // 治法
            chinesePrescription: defaultVal, // 方药
            doctorAdvice: 1, // 医嘱事项
        };
    }
    return {
        chiefComplaint: 1, // 主诉
        presentHistory: 1, // 现病史
        pastHistory: 1, // 既往史
        allergicHistory: defaultVal, // 过敏史
        familyHistory: defaultVal, //家庭史
        personalHistory: defaultVal, //个人史
        obstetricalHistory: defaultVal, // 月经婚育史
        epidemiologicalHistory: defaultVal, // 流行病史
        physicalExamination: 1, // 体格检查
        chineseExamination: 0, // 望闻问切
        oralExamination: 0, // 口腔检查
        auxiliaryExaminations: defaultVal, // 辅助检查
        diagnosis: 1, // 诊断
        symptomTime: 0, // 发病日期
        syndrome: 0, // 辩证
        therapy: 0, // 治法
        chinesePrescription: 0, // 方药
        doctorAdvice: 1, // 医嘱事项
    };
}


export function getRegistrationMRStruct(mrType) {
    if (mrType === MedicalRecordTypeEnum.ORAL) {
        return {
            chiefComplaint: 1, // 主诉
            presentHistory: 1, // 现病史
            pastHistory: 1, // 既往史
            dentistryExaminations: 1, // 口腔检查
            epidemiologicalHistory: 1, // 流行病学史
            preDiagnosisAttachments: 1, // 附件上传
        };
    }
    return {
        chiefComplaint: 1, // 主诉
        presentHistory: 1, // 现病史
        pastHistory: 1, // 既往史
        allergicHistory: 1, // 过敏史
        physicalExamination: 1, // 体格检查
        epidemiologicalHistory: 1, // 流行病学史
        preDiagnosisAttachments: 1, // 附件上传
        personalHistory: 1, // 个人史
    };
}

// 将json转换成table需要的格式
// chinese: {allergicHistory: {sort: 1, required: 0}} => chinese: [{sort: 1, key: 'allergicHistory', required: 0}]
export const transObj2Arr = (obj, params) => {
    const result = {};
    const {
        disabledKeysMap, hiddenKeysMap,
    } = params;
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const disabledKeys = disabledKeysMap[key] || [];
            const hiddenKeysKeys = hiddenKeysMap[key] || [];
            const subObject = obj[key];
            let array = [];
            for (const subKey in subObject) {
                if (Object.prototype.hasOwnProperty.call(subObject, subKey)) {
                    const label = MedicalRecordKeyLabelObj[subKey];
                    if (label) {
                        const item = subObject[subKey];
                        const disabled = disabledKeys.includes(subKey);
                        const hidden = hiddenKeysKeys.includes(subKey);
                        array.push({
                            key: subKey,
                            sort: item.sort,
                            required: item.required,
                            disabled,
                            label,
                            hidden,
                        });
                    }
                }
            }
            const { doctorAdviceInForm } = getViewDistributeConfig().Outpatient;
            // 诊所线医嘱建议在表单中，病历中过滤掉
            if (doctorAdviceInForm) {
                array = array.filter((item) => item.key !== 'doctorAdvice');
            }
            result[key] = array.sort((a, b) => a.sort - b.sort);
        }
    }
    return result;
};

// 转成提交的格式
// chinese: [{sort: 1, key: 'allergicHistory', required: 0}] => chinese: {allergicHistory: {sort: 1, required: 0}}
export const revertArrToObj = (obj) => {
    const result = {};
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const array = obj[key];
            const subObject = {};
            for (const item of array) {
                subObject[item.key] = {
                    required: item.required,
                    sort: item.sort,
                };
            }
            result[key] = subObject;
        }
    }

    return result;
};

/**
 * @desc 支持设置 key: a.b.c 的值
 */
export const setNestedValue = (obj, key, val) => {
    const keys = key.split('.');
    let current = obj;

    for (let i = 0; i < keys.length; i++) {
        const k = keys[i];
        if (i === keys.length - 1) {
            current[k] = val;
        } else {
            current[k] = current[k] || {};
            current = current[k];
        }
    }
};

export function formatMedicalRecordQuickOptions(groupList, datCyPrefix) {
    return groupList.map((group) => {
        if (group.list.length) {
            group.list = group.list.reduce((acc, item) => {
                const _item = item.map((val,idx) => ({
                    label: val,
                    value: val,
                    dataCy: `${datCyPrefix}-${val}`,
                    isBreak: idx === group.list.length - 1,
                }));

                return [...acc, ..._item];
            }, []);
        }

        return group;
    });
}

/**
 * @desc 初始化外部病历数据
 * @param {Object} value - 外部病历数据
 * @returns {Object} - 系统病历数据
 */
export async function initExternalMedicalRecord(value, currentType) {
    const data = clone(value);
    const _arrKey = ['auxiliaryExaminations', 'disposals', 'treatmentPlans'];
    for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key) && data[key]) {
            if (key === 'diagnosis') {
                const diagnosis = data.diagnosis.replace(/[?？]/g, '');
                const { data: result } = await SocialAPI.querySocialCodeByDiagnosis([
                    {
                        name: diagnosis,
                    },
                ]);
                data.extendDiagnosisInfos = [{
                    value: result.diagnosisInfos,
                }];
                delete data.diagnosis;
            }
            if (key === 'oralExamination') {
                if (
                    getMrTypeOralPropertyKey() === 'dentistry' &&
                    currentType === MedicalRecordTypeEnum.ORAL
                ) {
                    data.dentistryExaminations = [
                        {
                            toothNos: [],
                            value: data.oralExamination,
                        },
                    ];
                    delete data.oralExamination;
                } else {
                    const examination = [
                        {
                            describes: [data.oralExamination],
                            positions: [],
                        },
                    ];
                    data.oralExamination = JSON.stringify(examination);
                }
            }

            if (_arrKey.includes(key)) {
                const originVal = data[key];
                data[key] = [
                    {
                        toothNos: null,
                        value: originVal,
                    },
                ];
            }
            if (ClearKeyArr.includes(key)) {
                data[key] = null;
            }
        }
    }
    return data;
}

export * from '@/common/utils/medical-record';
