<template>
    <div v-if="showRegisInfo && regisReferralInfo" class="medical-record-item">
        <abc-form-item>
            <abc-flex align="center" style="padding: 0 10px;">
                <span class="registration-info" :title="regisReferralInfo" data-cy="mr-regis-referral-info">
                    {{ regisReferralInfo }}
                </span>

                <pre-diagnosis-button
                    v-if="preDiagnosisButtonVisible"
                    :disabled="!hasWechatPreDiagnosis"
                    style="flex-shrink: 0; margin-left: 12px;"
                    :post-data="postData"
                ></pre-diagnosis-button>
            </abc-flex>
        </abc-form-item>
    </div>
</template>

<script>
    import { RecommendService } from '@/service/recommend';
    import { OutpatientFromTypeEnum } from 'views/outpatient/constants';
    import { mapGetters } from 'vuex';
    import { ReferralFlagEnum } from '@/common/constants/registration';
    import PreDiagnosisButton from './pre-diagnosis-button.vue';
    import propertyAPI from 'api/property';

    export default {
        name: 'MedicalRecordRegisInfo',

        components: {
            PreDiagnosisButton,
        },

        props: {
            postData: {
                type: Object,
                default: () => {
                    return {};
                },
            },
        },

        data() {
            return {
                enablePreDiagnosis: false,
            };
        },

        computed: {
            ...mapGetters([
                'clinicBasic',
            ]),
            isRecommendAndSourceDisplay() {
                return this.clinicBasic.isRecommendAndSourceDisplay;
            },
            showRegisInfo() {
                return this.postData.outpatientFrom === OutpatientFromTypeEnum.REGISTER;
            },
            // 预约(挂号)信息
            registrationInfo() {
                return this.postData?.registrationInfo;
            },
            // 转诊信息
            referralSource() {
                return this.postData?.referralSource;
            },
            // 是否转诊
            isRegistrationRefer() {
                return this.postData.referralFlag === ReferralFlagEnum.REFER;
            },
            visitSourceOption() {
                if (!this.registrationInfo) return [];
                const {
                    visitSourceId,
                    visitSourceFrom,
                    visitSourceFromName,
                } = this.registrationInfo;
                return RecommendService.getInstance().initCascaderValue({
                    visitSourceId,
                    visitSourceName: null,
                    visitSourceFrom,
                    visitSourceFromName,
                });
            },
            displayRecommendedInformation() {
                if (!this.isRecommendAndSourceDisplay) {
                    return '';
                }
                if (this.visitSourceOption.length === 0) {
                    return '';
                } if (this.visitSourceOption.length === 1) {
                    return `${this.visitSourceOption[0].label}`;
                } if (this.visitSourceOption.length === 2) {
                    return `${this.visitSourceOption[0].label}-${this.visitSourceOption[1].label}`;
                }
                return `${this.visitSourceOption[0].label}-${this.visitSourceOption[1].label}-${this.visitSourceOption[2].label}`;

            },
            // 转诊信息
            regisReferralInfo() {
                if (!this.registrationInfo && !this.referralSource) return '';
                const {
                    reserveStart,
                    reserveEnd,
                    registrationProducts,
                    visitRemark,
                    departmentName,
                } = this.registrationInfo;
                const textArr = [];
                if (reserveStart && reserveEnd) {
                    textArr.push(`预约时间：${reserveStart}~${reserveEnd}`);
                }
                if (departmentName) {
                    textArr.push(`预约科室：${departmentName}`);
                }
                if (registrationProducts?.length) {
                    textArr.push(`项目：${registrationProducts.map((item) => item.displayName).join('、')}`);
                }
                if (this.displayRecommendedInformation) {
                    textArr.push(`推荐：${this.displayRecommendedInformation}`);
                }
                if (visitRemark) {
                    textArr.push(`备注：${visitRemark}`);
                }
                if (this.referralSource && this.isRegistrationRefer) {
                    textArr.push(`转诊：${this.referralSource?.doctorName ?? '不指定医生'}转出`);
                }
                return textArr.join(' / ');
            },

            preDiagnosisButtonVisible() {
                return !!this.postData &&
                    !!this.postData.registrationInfo &&
                    this.enablePreDiagnosis;
            },

            hasWechatPreDiagnosis() {
                return !!this.postData.medicalRecord &&
                    !!this.postData.medicalRecord.chiefComplaint &&
                    (this.postData.registrationInfo.extendFlag & 1) > 0;
            },
        },

        async created() {
            const promises = [
                propertyAPI.getV3('chainBasic.deepseek.preConsultation', 'chain'),
            ];

            if (this.postData.doctorId) {
                promises.push(propertyAPI.getV3('employeeBasic.deepseek.preConsultation', 'employee', {
                    scopeId: this.postData.doctorId,
                }));
            } else {
                promises.push(Promise.resolve({ data: 0 }));
            }

            const [chainRes, employeeRes] = await Promise.all(promises);

            this.enablePreDiagnosis = chainRes?.data === 1 || employeeRes?.data === 1;
        },
    };
</script>

<style lang="scss">
    @import "styles/abc-common.scss";

    .medical-record-item {
        .registration-info {
            display: inline-block;
            height: 36px;
            line-height: 36px;
            color: #aea97f;

            @include ellipsis(1);
        }
    }
</style>
