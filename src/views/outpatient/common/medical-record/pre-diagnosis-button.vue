<template>
    <abc-popover
        width="348px"
        placement="right"
        trigger="click"
        theme="white"
        size="large"
        :offset="150"
        popper-class="pre-diagnosis-popover-wrapper"
        @show="handlePopoverShow"
    >
        <abc-button
            slot="reference"
            shape="round"
            variant="text"
            theme="default"
            size="small"
            :class="[
                'pre-diagnosis-button-wrapper',
                {
                    'is-not-start': disabled,
                }
            ]"
            :icon="disabled ? 's-prediagnosis-2-line' : 's-prediagnosis-1-line'"
        >
            预诊记录
        </abc-button>

        <div v-abc-loading="loading" class="pre-diagnosis-record-wrapper">
            <template v-if="messages.length > 0 && !loading">
                <abc-flex align="center" justify="space-between" style="padding-bottom: 8px; border-bottom: 1px dashed var(--abc-color-P6);">
                    <abc-text
                        theme="gray-light"
                        tag="div"
                        size="mini"
                    >
                        AI 预诊
                    </abc-text>

                    <abc-text
                        theme="gray-light"
                        tag="div"
                        size="mini"
                    >
                        {{ startTime }}
                    </abc-text>
                </abc-flex>

                <abc-flex
                    v-for="(o, key) in messages"
                    :key="key"
                    vertical
                    gap="2"
                    style="margin-top: 16px;"
                >
                    <abc-text tag="div" size="mini" theme="gray">
                        {{ o.isMine ? postData.patient.name : '问询' }}
                    </abc-text>

                    <abc-text v-if="o.content.type === 'text'" tag="div" :bold="o.isMine">
                        {{ o.content.data }}
                    </abc-text>

                    <abc-image
                        v-else
                        :src="o.content.data"
                        fit="cover"
                        width="104"
                        height="104"
                        style="border-radius: 8px;"
                    ></abc-image>
                </abc-flex>
            </template>

            <abc-content-empty v-if="messages.length === 0 && !loading" value="暂无预诊记录"></abc-content-empty>
        </div>
    </abc-popover>
</template>

<script>
    import AiAPI from 'api/ai';
    import {
        safeParseJson, isJsonString, cleanJsonString, isObject,
    } from '@/utils/ai-helper';
    import { formatDate } from '@abc/utils-date';
    import OSSAPI from 'api/oss';

    export default {
        name: 'PreDiagnosisButton',

        props: {
            postData: {
                type: Object,
                default: () => {
                    return {
                        doctorName: '',
                        patient: {
                            name: '',
                        },
                        registrationInfo: {
                            registrationSheetId: '',
                        },
                    };
                },
            },

            disabled: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                loading: true,

                messages: [],
                startTime: '',

                START_TEXT: '您好，请开始',
            };
        },

        methods: {
            formatDateV2(dateStr) {
                if (!dateStr) return '';

                const date = new Date(dateStr);
                if (isNaN(date.getTime())) return ''; // Handle invalid dates

                const month = `${date.getMonth() + 1}`.padStart(2, '0');
                const day = `${date.getDate()}`.padStart(2, '0');

                return `${month}月${day}日`;
            },

            handleMessageContent(content) {
                if (!content || !content.startsWith('image___')) {
                    return {
                        type: 'text',
                        data: content,
                    };
                }

                const splitArray = content.split('___');

                return {
                    type: 'image',
                    data: splitArray[1],
                };
            },

            formatAIHistoryContent(content) {
                const {
                    tips, question, final,
                } = content;

                if (+final) {
                    return '预诊已完成，马上为您生成预诊病历。';
                }

                return `${tips || ''}${question || ''}` || '暂无内容';
            },

            async handlePopoverShow() {
                try {
                    if (this.disabled) {
                        this.messages = [];
                        this.startTime = '';
                        return;
                    }

                    this.loading = true;
                    const res = await AiAPI.getPreDiagnosisResult(this.postData.registrationInfo.registrationSheetId);

                    if (!res?.data?.rows) {
                        this.messages = [];
                        this.startTime = '';
                        return;
                    }

                    const defaultMessage = {
                        isMine: false,
                        content: {
                            type: 'text',
                            data: `${this.postData.patient.name}您好，您已成功预约${this.formatDateV2(this.postData.registrationInfo.reserveDate)}${this.postData.registrationInfo.departmentName || ''}的门诊，我是${this.postData.doctorName}医生，我们将进行一个简单的预问诊，以便更充分地了解您的情况。`,
                        },
                    };

                    const { rows } = res.data;
                    const formatedCleanRows = rows
                        .filter((o) => !!o.content)
                        .reverse()
                        .map((o) => {
                            const {
                                content, request,
                            } = o;

                            if (isJsonString(content)) {
                                return {
                                    answer: safeParseJson(cleanJsonString(content)),
                                    question: request.text,
                                };
                            }

                            return {
                                answer: content,
                                question: request.text,
                            };
                        });

                    const historyMessages = [];
                    formatedCleanRows.forEach((row) => {
                        const {
                            question, answer,
                        } = row;

                        if (question !== this.START_TEXT) {
                            historyMessages.push({
                                content: this.handleMessageContent(question),
                                isMine: true,
                            });
                        }

                        if (answer && isObject(answer)) {
                            historyMessages.push({
                                content: {
                                    type: 'text',
                                    data: this.formatAIHistoryContent(answer),
                                },
                                isMine: false,
                            });

                            return;
                        }

                        historyMessages.push({
                            content: {
                                type: 'text',
                                data: answer || '服务器异常',
                            },
                            isMine: false,
                        });
                    });

                    const imageMessage = historyMessages.find((o) => o.content.type === 'image');
                    if (imageMessage) {
                        const imageUrl = imageMessage.content.data;
                        const { data } = await OSSAPI.getSignFileUrl(imageUrl);
                        imageMessage.content.data = data.signFileUrl;
                    }

                    this.messages = [
                        defaultMessage,
                        ...historyMessages,
                    ];

                    this.startTime = formatDate(rows[rows.length - 1].created, 'YYYY-MM-DD HH:mm');
                } catch (e) {
                    console.error(e);
                    this.messages = [];
                    this.startTime = '';
                } finally {
                    this.loading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";

.pre-diagnosis-button-wrapper {
    height: auto !important;
    padding: 4px 8px !important;
    font-size: 12px !important;
    color: #a7a078 !important;
    border: 1px solid var(--abc-color-P6) !important;

    &.is-not-start {
        color: var(--abc-color-T3) !important;

        &.abc-button {
            &:not(.is-disabled) {
                &.is-controlled-active {
                    color: var(--abc-color-T3) !important;
                }
            }
        }
    }

    &.abc-button {
        &:not(.is-disabled) {
            &.is-controlled-active {
                color: #a7a078 !important;
            }
        }
    }
}

.pre-diagnosis-popover-wrapper {
    padding-right: 0 !important;
}

.pre-diagnosis-record-wrapper {
    position: relative;
    height: 400px;
    padding-right: 6px;
    overflow-y: scroll;

    @include scrollBar();
}
</style>
