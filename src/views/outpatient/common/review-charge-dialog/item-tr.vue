<template>
    <div class="tr" :class="{ 'is-charged': item.chargeStatus }">
        <!--项目名称-->
        <div class="td name">
            <div class="ellipsis td-cell">
                {{ item.name }}
            </div>
        </div>
        <div class="td width-64 grade">
            <medical-fee-grade-td
                v-if="item.productInfo && item.productInfo.medicalFeeGrade && item.productInfo.shebaoPayMode < 2"
                :item="item"
                :width="63"
                :show-pay-type-select="showPayTypeSelect"
                :shebao-card-info="shebaoCardInfo"
                @change-pay-type="handleChangePayType"
            >
            </medical-fee-grade-td>
        </div>
        <!--单价-->
        <div class="td price width-86 align-right">
            <div v-if="canSingleBargain" @mouseenter="hoverUnitPrice = true" @mouseleave="hoverUnitPrice = false">
                <abc-form-item required>
                    <abc-input
                        v-model="unitPrice"
                        v-abc-focus-selected
                        v-abc-adjust-price-popper="{
                            methods: val => adjustPriceChange(val, true),
                            originTotalFee: isSupportDecimalsFourMedicine(item) ? moneyDigit(item.sourceUnitPrice, 4) : formatMoney(item.sourceUnitPrice),
                        }"
                        size="small"
                        :width="85"
                        :class="{ 'is-edit': item.isUnitPriceChanged }"
                        :disabled="!!item.chargeStatus"
                        :readonly="inputCalcLoading"
                        :input-custom-style="{ textAlign: 'right' }"
                        type="money"
                        :config="getUnitPriceConfig"
                        @focus="hoverUnitPrice = false"
                        @change="changeUnitPrice"
                        @enter="enterEvent"
                    >
                    </abc-input>
                    <abc-loading v-if="inputCalcLoading && showLoading === 'unitPrice'" small no-cover></abc-loading>
                    <div v-show="item.isUnitPriceChanged && hoverUnitPrice" class="bargain-origin-price-popover">
                        议价前：{{ $t('currencySymbol') }}{{ isSupportDecimalsFourMedicine(item) ? moneyDigit(item.sourceUnitPrice, 4) : formatMoney(item.sourceUnitPrice) }}
                    </div>
                </abc-form-item>
            </div>
            <div v-else class="td-cell">
                {{ isSupportDecimalsFourMedicine(item) ? moneyDigit(item.unitPrice, 4) : formatMoney(item.unitPrice) }}
            </div>
        </div>
        <!--数量-->
        <div class="td count width-86 align-right gray">
            <div class="td-cell">
                {{ item.unitCount }} {{ item.unit || '次' }}
            </div>
        </div>
        <!--金额-->
        <div class="td price width-86 align-right">
            <goods-batch-info-popover
                :item="item"
                :batches="item.batchInfos"
            >
                <div v-if="canSingleBargain" @mouseenter="hoverTotalPrice = true" @mouseleave="hoverTotalPrice = false">
                    <abc-form-item required>
                        <abc-input
                            v-model="totalPrice"
                            v-abc-focus-selected
                            v-abc-adjust-price-popper="{
                                methods: val => adjustPriceChange(val),
                                originTotalFee: item.sourceTotalPrice,
                            }"
                            :class="{ 'is-edit': item.isTotalPriceChanged }"
                            size="small"
                            :width="86"
                            :disabled="!!item.chargeStatus"
                            :readonly="inputCalcLoading"
                            :input-custom-style="{ textAlign: 'right' }"
                            type="money"
                            :config="{
                                formatLength: 2, supportZero: true, max: 9999999
                            }"
                            @focus="hoverTotalPrice = false"
                            @enter="enterEvent"
                            @change="changeTotalPrice"
                        >
                        </abc-input>

                        <abc-loading v-if="inputCalcLoading && showLoading === 'totalPrice'" small no-cover></abc-loading>
                        <div v-show="item.isTotalPriceChanged && hoverTotalPrice" class="bargain-origin-price-popover">
                            议价前：<abc-money :value="item.sourceTotalPrice"></abc-money>
                        </div>
                    </abc-form-item>
                </div>
                <div v-else class="td-cell">
                    {{ totalPrice | formatMoney }}
                </div>
            </goods-batch-info-popover>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        isChineseMedicine, isSupportDecimalsFourMedicine,
    } from 'src/filters/index';
    import { moneyDigit } from 'utils';
    import { formatMoney } from 'src/filters/index';
    import { getItemUnitPrice } from 'views/outpatient/utils.js';
    import MedicalFeeGradeTd from 'views/layout/prescription/common/medical-fee-grade-td.vue';
    const GoodsBatchInfoPopover = () => import('src/views/layout/goods-batch-info-popover/index.vue');

    export default {
        name: 'ChargeReviewDialog',
        components: {
            MedicalFeeGradeTd,
            GoodsBatchInfoPopover,
        },
        props: {
            item: {
                type: Object,
                required: true,
            },
            inputCalcLoading: Boolean,
            canSingleBargain: Boolean,
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
        },
        data() {
            return {
                hoverUnitPrice: false,
                hoverTotalPrice: false,
                showLoading: '',

                unitPrice: getItemUnitPrice(this.item),
                totalPrice: this.item.totalPrice,
            };
        },

        computed: {
            getUnitPriceConfig() {
                if (isChineseMedicine(this.item)) {
                    return {
                        formatLength: 5, supportZero: true, max: 9999999,
                    };
                }
                if (isSupportDecimalsFourMedicine(this.item)) {
                    return {
                        formatLength: 4, supportZero: true, max: 9999999,
                    };
                }
                return {
                    formatLength: 2, supportZero: true, max: 9999999,
                };
            },
            /**
             * @desc 能否选择 是否自费 payType
             * <AUTHOR>
             * @date 2021-07-19 18:23:14
             */
            showPayTypeSelect() {
                return (
                    this.$abcSocialSecurity.isOpenSocial &&
                    (
                        this.$abcSocialSecurity.config.isZhejiang ||
                        this.$abcSocialSecurity.config.isFujianFuzhou ||
                        this.$abcSocialSecurity.config.isLiaoningPanjin
                    )
                );
            },
        },
        watch: {
            'item.currentUnitPrice': function () {
                this.unitPrice = getItemUnitPrice(this.item);
            },
            'item.unitPrice': function () {
                this.$nextTick(() => {
                    this.unitPrice = getItemUnitPrice(this.item);
                });
            },
            'item.totalPrice': function (val) {
                this.totalPrice = val;
            },
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            moneyDigit,
            formatMoney,
            isChineseMedicine,
            isSupportDecimalsFourMedicine,
            changeUnitPrice() {
                this.showLoading = 'totalPrice';
                this.$set(this.item, 'expectedUnitPrice', this.unitPrice);
                this.$set(this.item, 'expectedTotalPrice', null);
                this.$emit('change');
                this._timer = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },
            changeTotalPrice() {
                this.showLoading = 'unitPrice';
                this.$set(this.item, 'expectedUnitPrice', null);
                this.$set(this.item, 'expectedTotalPrice', this.totalPrice);
                this.$emit('change');
                this._timer = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },

            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                const inputs = $('.abc-form .abc-input__inner').not(':disabled').not(function () {
                    const tabIndex = $(this).attr('tabindex');
                    return parseInt(tabIndex) === -1;
                });
                const tab = $(e.target).attr('tabindex'); let nextInput = null;
                if (tab === 'last') {
                    nextInput = inputs[ inputs.length - 1 ];
                } else {
                    const targetIndex = inputs.index(e.target);
                    nextInput = inputs[ targetIndex + 1 ];
                }

                nextInput && this.$nextTick(() => {
                    nextInput.selectionStart = 0;
                    nextInput.selectionEnd = nextInput.value.length;
                    nextInput.select();
                    nextInput.focus();

                    // magic code
                    this._timer = setTimeout(
                        () => {
                            nextInput.select();
                            nextInput.focus();
                        },
                        50,
                    );
                });
            },

            handleChangePayType(val) {
                this.$emit('change-pay-type', val, this.item);
            },

            adjustPriceChange(val, isUnitPrice) {
                if (isUnitPrice) {
                    this.unitPrice = val;
                    this.changeUnitPrice();
                } else {
                    this.totalPrice = val;
                    this.changeTotalPrice();
                }
            },
        },
    };
</script>
