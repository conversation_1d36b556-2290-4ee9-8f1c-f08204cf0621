<template>
    <div class="tr" :class="{ 'is-charged': medicine.chargeStatus }">
        <!--药名-->
        <div class="td name">
            <div
                v-abc-goods-hover-popper:remote="{
                    goods: medicine,
                    showPrice: showDetailPrice,
                    showCostPrice: canViewCostPrice,
                }"
                class="td-cell"
            >
                <span class="ellipsis">{{ medicine.name }}</span>
                <span class="describe">{{ medicine.productInfo | getSpec }}</span>

                <div
                    class="describe ellipsis"
                    style="width: 86px; margin-left: auto;"
                    :title="(medicine.productInfo && medicine.productInfo.manufacturer) || ''"
                >
                    {{ (medicine.productInfo && medicine.productInfo.manufacturer) || '' }}
                </div>
            </div>
        </div>

        <div class="td width-80 grade">
            <medical-fee-grade-td
                v-if="medicine.productInfo && medicine.productInfo.medicalFeeGrade && medicine.productInfo.shebaoPayMode < 2"
                :width="80"
                :item="medicine"
                :shebao-card-info="shebaoCardInfo"
                :show-pay-type-select="showPayTypeSelect"
                @change-pay-type="handleChangePayType"
            >
            </medical-fee-grade-td>
        </div>

        <!--单价-->
        <div class="td price width-86 align-right">
            <div
                v-if="showDetailPrice"
                :class="{ 'is-edit': medicine.isUnitPriceChanged }"
                @mouseenter="hoverUnitPrice = true"
                @mouseleave="hoverUnitPrice = false"
            >
                <abc-form-item v-if="canSingleBargain" required>
                    <abc-input
                        v-model="unitPrice"
                        v-abc-focus-selected
                        v-abc-adjust-price-popper="{
                            adjustmentFormatLength: 4,
                            methods: val => adjustPriceChange(val, true),
                            originTotalFee: isSupportDecimalsFourMedicine(medicine) ? moneyDigit(medicine.sourceUnitPrice, 4) : formatMoney(medicine.sourceUnitPrice, false),
                        }"
                        size="small"
                        :width="85"
                        :disabled="disabledBargain"
                        :readonly="inputCalcLoading"
                        :input-custom-style="{ textAlign: 'right' }"
                        type="money"
                        :config="getUnitPriceConfig(medicine)"
                        @focus="hoverUnitPrice = false"
                        @enter="enterEvent"
                        @change="changeUnitPrice"
                    >
                    </abc-input>
                    <abc-loading v-if="inputCalcLoading && showLoading === 'unitPrice'" small no-cover></abc-loading>
                </abc-form-item>
                <div v-else class="td-cell">
                    {{ isSupportDecimalsFourMedicine(medicine) ? moneyDigit(unitPrice, 4) : formatMoney(unitPrice, false) }}
                </div>

                <div v-show="medicine.isUnitPriceChanged && hoverUnitPrice" class="bargain-origin-price-popover">
                    议价前：{{ $t('currencySymbol') }}{{ isSupportDecimalsFourMedicine(medicine) ? moneyDigit(medicine.sourceUnitPrice, 4) : formatMoney(medicine.sourceUnitPrice, false) }}
                </div>
            </div>
            <div v-else class="td-cell">
                --
            </div>
        </div>

        <!--数量-->
        <div class="td count width-86 align-right gray">
            <div v-if="medicineType === 'chinese'" class="td-cell" style="padding-left: 2px;">
                {{ formDoseCount || '' }} × {{ medicine.unitCount }} {{ medicine.unit || 'g' }}
            </div>
            <div v-else class="td-cell">
                {{ medicine.unitCount }} {{ medicine.unit || 'g' }}
            </div>
        </div>

        <!--金额-->
        <div class="td price width-86 align-right">
            <div
                v-if="showDetailPrice"
                :class="{ 'is-edit': medicine.isTotalPriceChanged }"
                @mouseenter="hoverTotalPrice = true"
                @mouseleave="hoverTotalPrice = false"
            >
                <goods-batch-info-popover
                    :item="medicine"
                    :batches="medicine.batchInfos"
                >
                    <abc-form-item v-if="canSingleBargain" required>
                        <abc-input
                            v-model="totalPrice"
                            v-abc-focus-selected
                            v-abc-adjust-price-popper="{
                                methods: val => adjustPriceChange(val),
                                originTotalFee: medicine.sourceTotalPrice,
                            }"
                            size="small"
                            :width="86"
                            type="money"
                            :disabled="disabledBargain"
                            :readonly="inputCalcLoading"
                            :input-custom-style="{ textAlign: 'right' }"
                            :config="{
                                formatLength: 2, supportZero: true, max: 9999999
                            }"
                            @focus="hoverTotalPrice = false"
                            @enter="enterEvent"
                            @change="changeTotalPrice"
                        >
                        </abc-input>
                        <abc-loading v-if="inputCalcLoading && showLoading === 'totalPrice'" small no-cover></abc-loading>
                    </abc-form-item>

                    <div v-else class="td-cell">
                        {{ totalPrice | formatMoney }}
                    </div>
                </goods-batch-info-popover>

                <div v-show="medicine.isTotalPriceChanged && hoverTotalPrice" class="bargain-origin-price-popover">
                    议价前：<abc-money :value="medicine.sourceTotalPrice"></abc-money>
                </div>
            </div>
            <div v-else class="td-cell">
                --
            </div>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import { PharmacyTypeEnum } from '@abc/constants';
    import GoodsModel from 'src/views/common/goods-model.js';
    import { isShortage } from 'utils/validate';
    import { moneyDigit } from 'utils';
    import { formatMoney } from '@/filters';
    import {
        isChineseMedicine, isSupportDecimalsFourMedicine,
    } from 'src/filters/index';
    import { getItemUnitPrice } from 'views/outpatient/utils.js';
    import MedicalFeeGradeTd from 'views/layout/prescription/common/medical-fee-grade-td.vue';
    const GoodsBatchInfoPopover = () => import('src/views/layout/goods-batch-info-popover/index.vue');

    export default {
        name: 'ChargeReviewMedicineTr',
        components: {
            MedicalFeeGradeTd,
            GoodsBatchInfoPopover,
        },
        props: {
            medicine: {
                type: Object,
                required: true,
            },
            inputCalcLoading: Boolean,
            canSingleBargain: Boolean,
            showDetailPrice: Boolean,
            pharmacyType: {
                type: Number,
                default: PharmacyTypeEnum.LOCAL_PHARMACY,
            },
            medicineType: {
                type: String,
                default: '',
            },
            formDoseCount: {
                type: [Number,String],
                default: 1,
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            canViewCostPrice: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                unitPrice: getItemUnitPrice(this.medicine),
                totalPrice: this.medicine.totalPrice,
                hoverUnitPrice: false,
                hoverTotalPrice: false,
                showLoading: '',
            };
        },

        computed: {
            disabledBargain() {
                if (this.medicine.chargeStatus) return true;
                // 虚拟药房不判断库存不足情况，但是需要判断 无商品资料 和 禁用
                if (this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    return !!this.noGoodsInfoTips || !!this.disabledSellTips;
                }
                return !!this.noGoodsInfoTips || !!this.disabledSellTips || !!isShortage(this.medicine).flag;
            },

            goodsModel() {
                return new GoodsModel(this.medicine.productInfo);
            },

            noGoodsInfoTips () {
                return this.goodsModel.noGoodsInfoTips;
            },
            disabledSellTips () {
                return this.goodsModel.disabledSellTips;
            },
            /**
             * @desc 能否选择 是否自费 payType
             * <AUTHOR>
             * @date 2021-07-19 18:23:14
             */
            showPayTypeSelect() {
                return (
                    this.$abcSocialSecurity.isOpenSocial &&
                    (
                        this.$abcSocialSecurity.config.isZhejiang ||
                        this.$abcSocialSecurity.config.isFujianFuzhou ||
                        this.$abcSocialSecurity.config.isLiaoningPanjin
                    )
                );
            },
        },
        watch: {
            'medicine.currentUnitPrice': function () {
                this.unitPrice = getItemUnitPrice(this.medicine);
            },
            'medicine.unitPrice': function () {
                this.$nextTick(() => {
                    this.unitPrice = getItemUnitPrice(this.medicine);
                });
            },
            'medicine.totalPrice': function (val) {
                this.totalPrice = val;
            },
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            formatMoney,
            moneyDigit,
            isChineseMedicine,
            isSupportDecimalsFourMedicine,
            getUnitPriceConfig(item) {
                if (isChineseMedicine(item) || isSupportDecimalsFourMedicine(item)) {
                    return {
                        formatLength: 4, supportZero: true, max: 9999999,
                    };
                }
                return {
                    formatLength: 2, supportZero: true, max: 9999999,
                };
            },

            changeUnitPrice() {
                this.showLoading = 'totalPrice';
                this.$set(this.medicine, 'expectedUnitPrice', this.unitPrice);
                this.$set(this.medicine, 'expectedTotalPrice', null);
                this.$emit('change');
                this._timer = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },
            changeTotalPrice() {
                this.showLoading = 'unitPrice';
                this.$set(this.medicine, 'expectedUnitPrice', null);
                this.$set(this.medicine, 'expectedTotalPrice', this.totalPrice);
                this.$emit('change');
                this._timer = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },
            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                const inputs = $('.charge-review-dialog-content .abc-charge-table').find('.abc-input__inner').not(':disabled').not(function () {
                    const tabIndex = $(this).attr('tabindex');
                    return parseInt(tabIndex) === -1;
                });
                const tab = $(e.target).attr('tabindex'); let nextInput = null;
                if (tab === 'last') {
                    nextInput = inputs[ inputs.length - 1 ];
                } else {
                    const targetIndex = inputs.index(e.target);
                    nextInput = inputs[ targetIndex + 1 ];
                }

                nextInput && this.$nextTick(() => {
                    nextInput.selectionStart = 0;
                    nextInput.selectionEnd = nextInput.value.length;
                    nextInput.select();
                    nextInput.focus();

                    // magic code
                    this._timer = setTimeout(
                        () => {
                            nextInput.select();
                            nextInput.focus();
                        },
                        50,
                    );
                });
            },

            handleChangePayType(val) {
                this.$emit('change-pay-type', val, this.medicine);
            },

            adjustPriceChange(val, isUnitPrice) {
                if (isUnitPrice) {
                    this.unitPrice = val;
                    this.changeUnitPrice();
                } else {
                    this.totalPrice = val;
                    this.changeTotalPrice();
                }
            },
        },
    };
</script>
