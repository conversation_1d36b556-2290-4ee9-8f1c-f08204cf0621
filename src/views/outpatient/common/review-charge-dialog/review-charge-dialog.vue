<template>
    <abc-dialog
        v-if="showDialog"
        ref="dialog"
        v-model="showDialog"
        title="费用预览"
        append-to-body
        custom-class="charge-review-dialog"
        :auto-focus="false"
        content-styles="width: 816px;"
        tabindex="-1"
    >
        <span slot="title-append" class="dialog-title-notice">提示：此处可预览费用，也可修改项目和药品的单价、金额、合计以及整单金额</span>

        <div
            v-abc-loading="contentLoading"
            class="charge-review-dialog-content clearfix"
        >
            <div class="clear-bargain-btn" @click="clearBargainHandle"></div>

            <abc-tabs
                v-if="hospitalInfo && postData.hospitalPatientOrderId"
                v-model="selectedTab"
                :option="tabOptions"
                size="middle"
                @change="changeTab"
            ></abc-tabs>

            <template v-if="selectedTab">
                <hospital-info-card
                    :hospital-info="hospitalInfo"
                    disabled
                    disabled-expand
                    disabled-switch-outpatient-type
                ></hospital-info-card>

                <changhu-settlement-table
                    style="margin-top: 16px;"
                    :data="hospitalChargeSheetList"
                    :shebao-card-info="postData.shebaoCardInfo"
                    :scroll-load-config="{
                        fetchData: fetchHospitalChargeList,
                        isLast,
                    }"
                >
                </changhu-settlement-table>
            </template>

            <abc-form v-else ref="feeForm" :key="$route.params.id">
                <template v-if="RETCount">
                    <abc-excel-table>
                        <div class="table-header">
                            <h5 class="th name">
                                诊疗项目
                            </h5>
                            <div class="th width-64 grade">
                                医保
                            </div>
                            <div class="th price width-86 align-right">
                                单价
                            </div>
                            <div class="th count width-86 align-right">
                                数量
                            </div>
                            <div class="th price width-86 align-right">
                                金额
                            </div>
                        </div>
                        <div class="table-body">
                            <!--直接门诊展示挂号费用-->
                            <div v-if="needContainReg" class="tr">
                                <!--项目名称-->
                                <div class="td name">
                                    <div class="td-cell">
                                        {{ $t('registrationFeeName') }} <span class="gray" style="margin-left: 8px;"> {{ regName }} </span>
                                    </div>
                                </div>
                                <div class="td width-64 grade">
                                </div>
                                <!--单价-->
                                <div class="td price width-86 align-right">
                                    <div
                                        :class="{ 'is-edit': +postData.registrationFee !== +data.registrationFee }"
                                        @mouseenter="hoverUnitPrice = true"
                                        @mouseleave="hoverUnitPrice = false"
                                    >
                                        <abc-form-item v-if="canSingleBargain" required>
                                            <abc-input
                                                v-model="data.registrationFee"
                                                v-abc-focus-selected
                                                v-abc-adjust-price-popper="{
                                                    methods: val => adjustPriceChange(data, 'registrationFee', val, changeRegUnitPrice),
                                                    originTotalFee: postData.registrationFee,
                                                }"
                                                size="small"
                                                :width="85"
                                                :disabled="disabledChangePrice"
                                                :input-custom-style="{ textAlign: 'right' }"
                                                type="money"
                                                :config="{
                                                    formatLength: 2, supportZero: true, max: 9999999
                                                }"
                                                @focus="hoverUnitPrice = false"
                                                @change="changeRegUnitPrice"
                                                @enter="enterEvent"
                                            >
                                            </abc-input>
                                            <abc-loading
                                                v-if="calcLoading && showLoading === 'unitPrice'"
                                                small
                                                no-cover
                                            ></abc-loading>
                                        </abc-form-item>
                                        <div v-else class="td-cell">
                                            {{ postData.registrationFee | formatMoney }}
                                        </div>

                                        <div
                                            v-show="hoverUnitPrice && +postData.registrationFee !== +data.registrationFee"
                                            class="bargain-origin-price-popover"
                                        >
                                            议价前：<abc-money :value="postData.registrationFee"></abc-money>
                                        </div>
                                    </div>
                                </div>

                                <!--数量-->
                                <div class="td count width-86 align-right gray">
                                    <div class="td-cell">
                                        1 次
                                    </div>
                                </div>

                                <!--金额-->
                                <div class="td price width-86 align-right">
                                    <div
                                        :class="{ 'is-edit': +postData.registrationFee !== +data.registrationFee }"
                                        @mouseenter="hoverTotalPrice = true"
                                        @mouseleave="hoverTotalPrice = false"
                                    >
                                        <abc-form-item v-if="canSingleBargain" required>
                                            <abc-input
                                                v-model="data.registrationFee"
                                                v-abc-focus-selected
                                                v-abc-adjust-price-popper="{
                                                    methods: val => adjustPriceChange(data, 'registrationFee', val, changeRegTotalPrice),
                                                    originTotalFee: postData.registrationFee,
                                                }"
                                                size="small"
                                                :width="86"
                                                :disabled="disabledChangePrice"
                                                :input-custom-style="{ textAlign: 'right' }"
                                                type="money"
                                                :config="{
                                                    formatLength: 2, supportZero: true, max: 9999999
                                                }"
                                                @focus="hoverTotalPrice = false"
                                                @change="changeRegTotalPrice"
                                                @enter="enterEvent"
                                            >
                                            </abc-input>
                                            <abc-loading
                                                v-if="calcLoading && showLoading === 'totalPrice'"
                                                small
                                                no-cover
                                            ></abc-loading>
                                        </abc-form-item>

                                        <div v-else class="td-cell">
                                            {{ postData.registrationFee | formatMoney }}
                                        </div>

                                        <div
                                            v-show="hoverTotalPrice && +postData.registrationFee !== +data.registrationFee"
                                            class="bargain-origin-price-popover"
                                        >
                                            议价前：<abc-money :value="postData.registrationFee"></abc-money>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <template v-for="form in data.productForms">
                                <template v-for="(item, index) in form.productFormItems">
                                    <item-tr
                                        v-if="availableFormItemTr(item)"
                                        :key="`e${ form.keyId || form.id }${ index}`"
                                        :item="item"
                                        :can-single-bargain="canSingleBargain && !!item.canAdjustment"
                                        :input-calc-loading="calcLoading"
                                        :shebao-card-info="postData.shebaoCardInfo"
                                        @change="changeHandler"
                                        @change-pay-type="(val, it) => onChangePayType(val, it, form, 'productFormItems')"
                                    ></item-tr>
                                </template>
                            </template>
                        </div>
                    </abc-excel-table>
                </template>

                <template v-for="form in data.prescriptionWesternForms">
                    <template v-if="availablePRFormTable(form)">
                        <abc-excel-table :key="form.id || form.keyId">
                            <div class="table-header">
                                <h5 class="th name">
                                    {{ westernPrescriptionNameText }}
                                    <abc-text
                                        v-if="form.pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY"
                                        theme="gray"
                                        size="mini"
                                        style=" margin-left: 8px; font-weight: normal;"
                                    >
                                        合作药店：{{ form.pharmacyName }}
                                    </abc-text>
                                </h5>
                                <div class="th width-80 grade align-center">
                                    医保
                                </div>
                                <div class="th price width-86 align-right">
                                    单价
                                </div>
                                <div class="th count width-86 align-right">
                                    数量
                                </div>
                                <div class="th price width-86 align-right">
                                    金额
                                </div>
                            </div>
                            <div class="table-body">
                                <template v-for="(medicine, index) in form.prescriptionFormItems">
                                    <!--西药-->
                                    <medicine-tr
                                        v-if="availableFormItemTr(medicine)"
                                        :key="`western${ medicine.id }${ index}`"
                                        medicine-type="western"
                                        :medicine="medicine"
                                        :show-detail-price="outpatientShowDetailPrice"
                                        :can-single-bargain="canSingleBargain && !!medicine.canAdjustment"
                                        :input-calc-loading="calcLoading"
                                        :shebao-card-info="postData.shebaoCardInfo"
                                        :can-view-cost-price="canViewCostPrice"
                                        @change="changeHandler()"
                                        @change-pay-type="(val, it) => onChangePayType(val, it, form)"
                                    ></medicine-tr>
                                </template>
                                <div class="tr">
                                    <div class="td flex1 align-right gray">
                                        <div class="td-cell">
                                            {{ form.prescriptionFormItems.length }} 项合计
                                        </div>
                                    </div>
                                    <div class="td width-86 align-right">
                                        <abc-popover
                                            v-if="outpatientShowTotalPrice"
                                            trigger="hover"
                                            placement="bottom-end"
                                            :class="{ 'is-edit': form.isTotalPriceChanged }"
                                            :disabled="!form.isTotalPriceChanged"
                                            :visible-arrow="false"
                                            theme="yellow"
                                        >
                                            <abc-form-item slot="reference" required>
                                                <abc-input
                                                    v-model="form.userInputFormTotalPriceCache"
                                                    v-abc-focus-selected
                                                    v-abc-adjust-price-popper="{
                                                        methods: val => adjustPriceChange(
                                                            form,
                                                            'userInputFormTotalPriceCache',
                                                            val,
                                                            () => handleChangeFormTotalPrice(form)
                                                        ),
                                                        originTotalFee: form.sourceTotalPrice,
                                                    }"
                                                    size="small"
                                                    :width="86"
                                                    type="money"
                                                    :readonly="calcLoading"
                                                    :disabled="!canBargain || !form.canAdjustment"
                                                    :input-custom-style="{ textAlign: 'right' }"
                                                    :config="{
                                                        formatLength: 2, supportZero: true, max: 9999999
                                                    }"
                                                    @change="handleChangeFormTotalPrice(form)"
                                                >
                                                </abc-input>
                                            </abc-form-item>

                                            <div class="bargain-origin-price-popover" style="top: -1px; margin-top: 0;">
                                                议价前：<abc-money :value="form.sourceTotalPrice"></abc-money>
                                            </div>
                                        </abc-popover>

                                        <div v-else class="td-cell">
                                            --
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </abc-excel-table>
                    </template>
                </template>

                <template v-for="form in data.prescriptionInfusionForms">
                    <template v-if="availablePRFormTable(form)">
                        <abc-excel-table :key="form.id || form.keyId">
                            <div class="table-header">
                                <h5 class="th name">
                                    输注处方
                                    <abc-text
                                        v-if="form.pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY"
                                        theme="gray"
                                        size="mini"
                                        style=" margin-left: 8px; font-weight: normal;"
                                    >
                                        合作药店：{{ form.pharmacyName }}
                                    </abc-text>
                                </h5>
                                <div class="th width-64 grade">
                                    医保
                                </div>
                                <div class="th price width-86 align-right">
                                    单价
                                </div>
                                <div class="th count width-86 align-right">
                                    数量
                                </div>
                                <div class="th price width-86 align-right">
                                    金额
                                </div>
                            </div>
                            <div class="table-body">
                                <template v-for="(medicine, index) in form.prescriptionFormItems">
                                    <medicine-tr
                                        v-if="availableFormItemTr(medicine)"
                                        :key="`infusion${ medicine.id }${ index}`"
                                        :medicine="medicine"
                                        medicine-type="western"
                                        :show-detail-price="outpatientShowDetailPrice"
                                        :can-single-bargain="canSingleBargain && !!medicine.canAdjustment"
                                        :input-calc-loading="calcLoading"
                                        :shebao-card-info="postData.shebaoCardInfo"
                                        :can-view-cost-price="canViewCostPrice"
                                        @change="changeHandler"
                                        @change-pay-type="(val, it) => onChangePayType(val, it, form)"
                                    ></medicine-tr>
                                </template>

                                <div class="tr">
                                    <div class="td flex1 align-right gray">
                                        <div class="td-cell">
                                            {{ form.prescriptionFormItems.length }} 项合计
                                        </div>
                                    </div>
                                    <div class="td width-86 align-right">
                                        <abc-popover
                                            v-if="outpatientShowTotalPrice"
                                            trigger="hover"
                                            placement="bottom-end"
                                            :class="{ 'is-edit': form.isTotalPriceChanged }"
                                            :disabled="!form.isTotalPriceChanged"
                                            theme="yellow"
                                            :visible-arrow="false"
                                        >
                                            <abc-form-item slot="reference" required>
                                                <abc-input
                                                    v-model="form.userInputFormTotalPriceCache"
                                                    v-abc-focus-selected
                                                    v-abc-adjust-price-popper="{
                                                        methods: val => adjustPriceChange(
                                                            form,
                                                            'userInputFormTotalPriceCache',
                                                            val,
                                                            () => handleChangeFormTotalPrice(form)
                                                        ),
                                                        originTotalFee: form.sourceTotalPrice,
                                                    }"
                                                    :disabled="!canBargain || !form.canAdjustment"
                                                    :readonly="calcLoading"
                                                    size="small"
                                                    :width="86"
                                                    type="money"
                                                    :input-custom-style="{ textAlign: 'right' }"
                                                    :config="{
                                                        formatLength: 2, supportZero: true, max: 9999999
                                                    }"
                                                    @change="handleChangeFormTotalPrice(form)"
                                                >
                                                </abc-input>
                                            </abc-form-item>

                                            <div class="bargain-origin-price-popover" style="top: -1px; margin-top: 0;">
                                                议价前：<abc-money :value="form.sourceTotalPrice"></abc-money>
                                            </div>
                                        </abc-popover>

                                        <span v-else class="td-cell">--</span>
                                    </div>
                                </div>
                            </div>
                        </abc-excel-table>
                    </template>
                </template>

                <template v-for="form in data.prescriptionChineseForms">
                    <template v-if="availablePRFormTable(form)">
                        <abc-excel-table :key="form.id || form.keyId">
                            <div class="table-header">
                                <div class="th name">
                                    <h5>
                                        中药处方
                                        <abc-text
                                            v-if="form.pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY"
                                            theme="gray"
                                            size="mini"
                                            style=" margin-left: 8px; font-weight: normal;"
                                        >
                                            合作药店：{{ form.pharmacyName }}
                                        </abc-text>
                                    </h5>
                                    <div
                                        v-if="form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY ||
                                            form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY"
                                        class="air-pharmacy-tips"
                                    >
                                        空中药房：{{ form.vendorName || form.pharmacyName || '' }}
                                    </div>
                                </div>
                                <div class="th width-80 grade align-center">
                                    医保
                                </div>
                                <div class="th price width-86 align-right">
                                    单价
                                </div>
                                <div class="th count width-86 align-right">
                                    数量
                                </div>
                                <div class="th price width-86 align-right">
                                    金额
                                </div>
                            </div>
                            <div class="table-body">
                                <template v-for="(medicine, index) in form.prescriptionFormItems">
                                    <medicine-tr
                                        v-if="availableFormItemTr(medicine)"
                                        :key="`chinese${ form.keyId || medicine.id }${ index}`"
                                        type="chinese"
                                        medicine-type="chinese"
                                        :medicine="medicine"
                                        :show-detail-price="outpatientShowDetailPrice"
                                        :pharmacy-type="form.pharmacyType"
                                        :form-dose-count="form.doseCount"
                                        :can-single-bargain="
                                            canSingleBargain && !!medicine.canAdjustment &&
                                                form.pharmacyType !== PharmacyTypeEnum.AIR_PHARMACY
                                        "
                                        :input-calc-loading="calcLoading"
                                        :shebao-card-info="postData.shebaoCardInfo"
                                        :can-view-cost-price="canViewCostPrice"
                                        @change="changeHandler"
                                        @change-pay-type="(val, it) => onChangePayType(val, it, form)"
                                    ></medicine-tr>
                                </template>

                                <div class="tr">
                                    <div class="td flex1 align-right gray">
                                        <div class="td-cell">
                                            {{ countFormItems(form) }} 味合计
                                        </div>
                                    </div>
                                    <div class="td width-86 align-right">
                                        <abc-popover
                                            v-if="outpatientShowTotalPrice"
                                            trigger="hover"
                                            placement="bottom-end"
                                            :class="{ 'is-edit': form.isTotalPriceChanged }"
                                            :disabled="!form.isTotalPriceChanged"
                                            theme="yellow"
                                            :visible-arrow="false"
                                        >
                                            <abc-form-item slot="reference" required>
                                                <abc-input
                                                    v-model="form.userInputFormTotalPriceCache"
                                                    v-abc-focus-selected
                                                    v-abc-adjust-price-popper="{
                                                        methods: val => adjustPriceChange(
                                                            form,
                                                            'userInputFormTotalPriceCache',
                                                            val,
                                                            () => handleChangeFormTotalPrice(form)
                                                        ),
                                                        originTotalFee: form.sourceTotalPrice,
                                                    }"
                                                    size="small"
                                                    :disabled="!(form.vendorId || (canBargain && !!form.canAdjustment))"
                                                    :readonly="calcLoading"
                                                    :width="86"
                                                    type="money"
                                                    :input-custom-style="{ textAlign: 'right' }"
                                                    :config="{
                                                        formatLength: 2, supportZero: true, max: 9999999
                                                    }"
                                                    @enter="enterEvent"
                                                    @change="handleChangeFormTotalPrice(form)"
                                                >
                                                </abc-input>
                                            </abc-form-item>

                                            <div v-if="form.isTotalPriceChanged" class="bargain-origin-price-popover" style="top: -1px; margin-top: 0;">
                                                议价前：<abc-money :value="form.sourceTotalPrice"></abc-money>
                                            </div>
                                        </abc-popover>
                                        <div v-else class="td-cell">
                                            --
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </abc-excel-table>
                    </template>
                </template>

                <template v-for="form in data.prescriptionExternalForms">
                    <template v-if="availablePRFormTable(form)">
                        <abc-excel-table :key="form.id || form.keyId">
                            <div class="table-header">
                                <h5 class="th name">
                                    外治处方
                                </h5>
                                <div class="th width-64 grade">
                                    医保
                                </div>
                                <div class="th price width-86 align-right">
                                    单价
                                </div>
                                <div class="th count width-86 align-right">
                                    数量
                                </div>
                                <div class="th price width-86 align-right">
                                    金额
                                </div>
                            </div>
                            <div class="table-body">
                                <template v-for="(item, index) in form.prescriptionFormItems">
                                    <item-tr
                                        v-if="availableFormItemTr(item)"
                                        :key="`external${ form.keyId || form.id }${ index}`"
                                        :item="item"
                                        :can-single-bargain="!!canSingleBargain && !!item.canAdjustment"
                                        :input-calc-loading="calcLoading"
                                        :shebao-card-info="postData.shebaoCardInfo"
                                        @change="changeHandler"
                                        @change-pay-type="(val, it) => onChangePayType(val, it, form)"
                                    ></item-tr>
                                </template>

                                <div class="tr">
                                    <div class="td flex1 align-right gray">
                                        <div class="td-cell">
                                            {{ form.prescriptionFormItems.length }} 项合计
                                        </div>
                                    </div>
                                    <div class="td width-86 align-right">
                                        <abc-popover
                                            v-if="outpatientShowTotalPrice"
                                            trigger="hover"
                                            placement="bottom-end"
                                            :class="{ 'is-edit': form.isTotalPriceChanged }"
                                            :disabled="!form.isTotalPriceChanged"
                                            theme="yellow"
                                            :visible-arrow="false"
                                        >
                                            <abc-form-item slot="reference" required>
                                                <abc-input
                                                    v-model="form.userInputFormTotalPriceCache"
                                                    v-abc-focus-selected
                                                    v-abc-adjust-price-popper="{
                                                        methods: val => adjustPriceChange(
                                                            form,
                                                            'userInputFormTotalPriceCache',
                                                            val,
                                                            () => handleChangeFormTotalPrice(form)
                                                        ),
                                                        originTotalFee: form.sourceTotalPrice,
                                                    }"
                                                    :disabled="!canBargain || !form.canAdjustment"
                                                    :readonly="calcLoading"
                                                    size="small"
                                                    :width="86"
                                                    type="money"
                                                    :input-custom-style="{ textAlign: 'right' }"
                                                    :config="{
                                                        formatLength: 2, supportZero: true, max: 9999999
                                                    }"
                                                    @change="handleChangeFormTotalPrice(form)"
                                                >
                                                </abc-input>
                                            </abc-form-item>

                                            <div class="bargain-origin-price-popover" style="top: -1px; margin-top: 0;">
                                                议价前：<abc-money :value="form.sourceTotalPrice"></abc-money>
                                            </div>
                                        </abc-popover>

                                        <div v-else class="td-cell">
                                            --
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </abc-excel-table>
                    </template>
                </template>
            </abc-form>
        </div>
        <div slot="footer" class="dialog-footer">
            <template v-if="selectedTab">
                <div class="settle-amount">
                    <span>结算费用：{{ $t('currencySymbol') }} </span>{{ totalFee | formatMoney }}
                </div>
                <abc-button type="blank" @click="cancel">
                    关闭
                </abc-button>
            </template>
            <template v-else>
                <span v-if="data.adjustmentFee" class="adjustment">
                    议价：{{ adjustmentFeeSymbol }}{{ data.adjustmentFee | formatMoney }}
                </span>

                <template v-if="outpatientShowTotalPrice">
                    <span class="total-fee">整单金额</span>
                    <abc-input
                        v-if="canBargain"
                        v-model="data.totalPrice"
                        v-abc-focus-selected
                        v-abc-adjust-price-popper="{
                            methods: val => adjustPriceChange(data, 'totalPrice', val, handleChangeTotalPrice),
                            originTotalFee: data.totalPrice - data.adjustmentFee,
                        }"
                        type="money"
                        :config="{
                            formatLength: 2, supportZero: true, max: 9999999
                        }"
                        :input-custom-style="{
                            textAlign: 'right',
                            fontSize: '20px',
                            paddingRight: '12px',
                        }"
                        :disabled="!data.canAdjustment && canSingleBargain"
                        :width="118"
                        style="margin: 0 24px 0 6px;"
                        @change="handleChangeTotalPrice"
                    >
                        <label slot="prepend" class="prepend">
                            <abc-currency-symbol-icon></abc-currency-symbol-icon>
                        </label>
                    </abc-input>
                    <span v-else style=" margin-right: 20px; margin-left: 6px; font-size: 20px; color: #ff9933;">
                        {{ data.totalPrice | formatMoney }}
                    </span>
                </template>
                <abc-button :loading="calcLoading || btnLoading" need-loaded-click @click="submit">
                    确定
                </abc-button>
                <abc-button type="blank" @click="cancel">
                    取消
                </abc-button>
            </template>
        </div>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import AbcExcelTable from 'src/components/excel-table/index.vue';
    import MedicineTr from './medicine-tr.vue';
    import HospitalInfoCard from 'src/views/hospital/hospital-info-card.vue';
    import ChanghuSettlementTable from '@/views/hospital/settlement-table/index.vue';

    import ItemTr from './item-tr.vue';
    import Clone from 'utils/clone';
    import {
        on, off,
    } from 'utils/dom';
    import { PharmacyTypeEnum } from '@abc/constants';
    import {
        mapGetters,
    } from 'vuex';

    import OutpatientAPI from 'api/outpatient';
    import ChargeAPI from 'api/charge.js';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import {
        isProductForm, isPrescriptionForm,
    } from 'src/views/cashier/utils/index.js';
    import {
        calcDataCallback, OutpatientFormKeys, resetCalcHandler,
    } from 'views/outpatient/utils.js';
    import { add } from '@/utils/math.js';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import { ReferralFlagEnum } from '@/common/constants/registration';
    import useOutpatientCommon from '@/hooks/business/use-outpatient-common/index';

    export default {
        name: 'ChargeReviewDialog',

        components: {
            AbcExcelTable,
            MedicineTr,
            ItemTr,
            HospitalInfoCard,
            AbcCurrencySymbolIcon,
            ChanghuSettlementTable,
        },
        props: {
            value: Boolean,
            isConsultation: Boolean,
            postData: {
                type: Object,
                required: true,
            },
            registrationFeeStatus: [ Number, String, Boolean ],
            treatOnlineClinicId: [ Number, String ],

            hospitalInfo: {
                type: Object,
                default: null,
            },
            isChargeLocked: Boolean,
        },
        setup() {
            const { handleChangePayType } = useOutpatientCommon();

            return {
                handleChangePayType,
            };
        },
        data() {
            return {
                OutpatientChargeTypeEnum,
                PharmacyTypeEnum,
                contentLoading: false,
                calcLoading: false,
                btnLoading: false,
                isLast: false,
                data: {
                    clinicId: this.treatOnlineClinicId,
                    expectedTotalPrice: this.postData.expectedTotalPrice,
                    totalPrice: this.postData.totalPrice,
                    sourceTotalPrice: this.postData.totalPrice,
                    adjustmentFee: this.postData.adjustmentFee || null,
                    registrationFee: this.postData.registrationFee,
                    productForms: Clone(this.postData.productForms),
                    prescriptionChineseForms: Clone(this.postData.prescriptionChineseForms),
                    prescriptionWesternForms: Clone(this.postData.prescriptionWesternForms),
                    prescriptionInfusionForms: Clone(this.postData.prescriptionInfusionForms),
                    prescriptionExternalForms: Clone(this.postData.prescriptionExternalForms),
                },
                calcData: {},

                hoverUnitPrice: false,
                hoverTotalPrice: false,
                hoverAirPharmacy: false,
                showLoading: '',

                systemAdjustment: 0,

                selectedTab: 0,
                tabOptions: [
                    {
                        label: '本次费用',
                        value: 0,
                    },
                    {
                        label: '累计费用',
                        value: 1,
                    },
                ],
                fetchParams: {
                    offset: 0,
                    limit: 5,
                },
                hospitalChargeSheetList: [],
                hospitalSheetSummary: {},
                innerChargeLocked: this.isChargeLocked,
                isClickCancel: false, // 标记已经点了取消
            };
        },
        computed: {
            ...mapGetters([
                'goodsPriceInOutpatient',
                'chargeConfig',
                'currentClinic',
                'userInfo',
                'isCanSeeGoodsCostPriceInOutpatient',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustOutpatientPrice',
            ]),
            canSingleBargain() {
                return !!this.chargeConfig.doctorSingleBargainSwitch && this.employeeCanAdjustOutpatientPrice;
            },
            canBargain() {
                return !!this.chargeConfig.doctorBargainSwitch && this.employeeCanAdjustOutpatientPrice;
            },
            isThreeDaysRepeat() {
                return this.postData.referralFlag === ReferralFlagEnum.REVISIT_IN_THREE_DAYS;
            },
            /**
             * 医生能否查看成本价
             */
            canViewCostPrice() {
                return this.isCanSeeGoodsCostPriceInOutpatient;
            },
            westernPrescriptionNameText() {
                return this.viewDistributeConfig.Outpatient.westernPrescriptionNameText;
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            regName() {
                const {
                    departmentName, doctorName,
                } = this.postData;
                if (departmentName === '默认') {
                    return `${doctorName || ''}`;
                }
                return `${departmentName ? `${departmentName} - ` : ''}${doctorName || ''}`;

            },
            disabledChangePrice() {
                const { doctorRegisteredBargainSwitch } = this.chargeConfig;
                return !!(this.registrationFeeStatus && this.registrationFeeStatus !== 0) ||
                    (doctorRegisteredBargainSwitch !== 1) ||
                    this.isThreeDaysRepeat;
            },
            RETCount() {
                let count = 0;
                if (this.needContainReg) {
                    count += 1; // 挂号费
                }

                this.data.productForms && this.data.productForms.forEach((form) => {
                    count += form.productFormItems.filter((item) => {
                        return !item.chargeStatus;
                    }).length;
                });

                return count || '';
            },
            RETFee() {
                let fee = 0;
                if (this.needContainReg) {
                    fee += +this.data.registrationFee; // 挂号费
                }
                this.data.productForms && this.data.productForms.forEach((form) => {
                    form.productFormItems.forEach((item) => {
                        fee += item.chargeStatus ? 0 : +item.totalPrice;
                    });
                });

                return fee || 0;
            },

            /**
             * @desc 门诊允许医生查看明细(管理中设置)
             * <AUTHOR>
             * @date 2021/04/08 10:28:24
             */
            outpatientShowDetailPrice() {
                return this.goodsPriceInOutpatient === 1;
            },

            /**
            * @desc 门诊允许医生查看总价(管理中设置)
             * <AUTHOR>
             * @date 2021/04/08 10:28:24
             */
            outpatientShowTotalPrice() {
                return this.goodsPriceInOutpatient === 0 || this.goodsPriceInOutpatient === 1;
            },

            totalFee() {
                const {
                    needPayFee,
                    receivedFee,
                } = this.hospitalSheetSummary;
                return this.isSettled ? receivedFee : needPayFee;
            },
            // 是否包含挂号费
            needContainReg() {
                if (this.hospitalInfo) return false;
                if (this.isConsultation) return false;
                return !this.registrationFeeStatus;
            },

            // 议价费符号
            adjustmentFeeSymbol() {
                if (+this.data.adjustmentFee > 0) return '+';
                return '';
            },
        },
        async created() {
            this.$store.dispatch('initChargeConfig');
            this.contentLoading = true;
            resetCalcHandler(this.data);
            await this.calcFee(true);
            this.contentLoading = false;
        },
        mounted() {
            on(this.$el, 'keydown', this.keydownHandle);
            $(this.$el).focus();
        },
        beforeDestroy() {
            off(this.$el, 'keydown', this.keydownHandle);
            this.timeoutId && clearTimeout(this.timeoutId);
        },

        methods: {
            availablePRFormTable(form) {
                if (form.chargeStatus) return false;
                return form.prescriptionFormItems.filter((it) => {
                    return this.availableFormItemTr(it);
                }).length;
            },

            availableFormItemTr(item) {
                if (item.chargeStatus) return false;
                if (item.chargeType === OutpatientChargeTypeEnum.NO_CHARGE) return false;
                return item.goodsId || item.name || item.medicineCadn;
            },

            /**
             * @desc 整单议价
             * <AUTHOR>
             * @date 2022-03-11 09:03:21
             */
            handleChangeTotalPrice() {
                this.data.adjustmentFee = null;
                this.data.expectedTotalPrice = this.data.totalPrice;
                this.timeoutId = setTimeout(() => {
                    this.calcFee();
                }, 1);
            },

            /**
             * @desc 修改处方上的总金额
             * <AUTHOR>
             * @date 2022-03-11 08:59:20
             */
            handleChangeFormTotalPrice(form) {
                const {
                    canAdjustmentFee,
                    userInputFormTotalPriceCache,
                    sourceTotalPrice,
                } = form;
                const minPrice = add(sourceTotalPrice, canAdjustmentFee);
                if (userInputFormTotalPriceCache < minPrice) {
                    form.userInputFormTotalPriceCache = minPrice;
                    this.$Toast.error(`仅有部分收费项可议价，因此议价下限为${minPrice.toFixed(2)}`);
                }
                form.expectedTotalPrice = form.userInputFormTotalPriceCache;
                this.calcFee();
            },

            changeAirPharmacyTotalPrice(form) {
                form.expectedTotalPrice = form.totalPrice;
                this.calcFee();
            },
            changeHandler() {
                this.calcFee();
            },

            /**
             * @desc 设置处方强制自费
             * <AUTHOR>
             * @date 2022/05/27 15:52:29
             */
            onChangePayType(val, item,form, key = 'prescriptionFormItems') {
                this.handleChangePayType(val, item, form, key);
                this.calcFee();
            },
            changeRegistrationFee() {
                this.calcFee();
            },

            changeRegUnitPrice() {
                this.showLoading = 'totalPrice';
                this.calcFee();
                this.timeoutId = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },
            changeRegTotalPrice() {
                this.showLoading = 'unitPrice';
                this.calcFee();
                this.timeoutId = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },


            async calcFee(isCreated = false) {
                this.calcLoading = true;
                const calcData = this.getCalcData();
                try {
                    const { data } = await OutpatientAPI.calculate(calcData);
                    if (!this.needContainReg) {
                        data.registrationFee = +(this.data.registrationFee || 0);
                    }
                    calcDataCallback(this.data, data, this);
                    resetCalcHandler(this.data);
                    if (!isCreated) {
                        this.outpatientFeeChange();
                    }
                } catch (e) {
                    this.calcLoading = false;
                    const {
                        code,
                        message,
                    } = e;
                    if (code === 17257) {
                        this.showDialog = false;
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    }
                } finally {
                    this.calcLoading = false;
                }
            },
            /**
             * @desc 获取算费结构
             * <AUTHOR>
             * @date 2022-03-11 10:55:19
             */
            getCalcData() {
                const productForms = Clone(this.data.productForms || []).filter((form) => {
                    form.productFormItems = form.productFormItems.filter((item) => {
                        return this.availableFormItemTr(item);
                    });
                    return form.productFormItems.length;
                });

                const prescriptionChineseForms = Clone(this.data.prescriptionChineseForms || []).filter((form) => {
                    if (form.chargeStatus) return false;
                    form.prescriptionFormItems = form.prescriptionFormItems.filter((item) => {
                        return this.availableFormItemTr(item);
                    });
                    return form.prescriptionFormItems.length;
                });

                const prescriptionInfusionForms = Clone(this.data.prescriptionInfusionForms || []).filter((form) => {
                    if (form.chargeStatus) return false;
                    form.prescriptionFormItems = form.prescriptionFormItems.filter((item) => {
                        return this.availableFormItemTr(item);
                    });
                    return form.prescriptionFormItems.length;
                });

                const prescriptionWesternForms = Clone(this.data.prescriptionWesternForms || []).filter((form) => {
                    if (form.chargeStatus) return false;
                    form.prescriptionFormItems = form.prescriptionFormItems.filter((item) => {
                        return this.availableFormItemTr(item);

                    });
                    return !form.chargeStatus && form.prescriptionFormItems.length;
                });

                const prescriptionExternalForms = Clone(this.data.prescriptionExternalForms || []).filter((form) => {
                    if (form.chargeStatus) return false;
                    form.prescriptionFormItems = form.prescriptionFormItems.filter((item) => {
                        return this.availableFormItemTr(item);
                    });
                    return !form.chargeStatus && form.prescriptionFormItems.length;
                });

                const calcData = {
                    id: this.postData.id,
                    clinicId: this.data.clinicId || this.currentClinic.clinicId,
                    expectedTotalPrice: this.data.expectedTotalPrice,
                    adjustmentFee: this.data.adjustmentFee,
                    productForms,
                    prescriptionChineseForms,
                    prescriptionWesternForms,
                    prescriptionInfusionForms,
                    prescriptionExternalForms,
                };
                if (this.needContainReg) {
                    calcData.registrationFee = this.data.registrationFee;
                }
                return calcData;
            },

            submit() {
                if (this.calcLoading) return false;
                if (!this.$refs.feeForm) return false;
                this.$refs.feeForm.validate(async (valid) => {
                    if (valid) {
                        try {
                            this.btnLoading = true;
                            await OutpatientAPI.saveAdjustmentPrice(this.postData.id, this.getCalcData());
                            this.$emit('update:postData', Object.assign(this.postData, {
                                ...this.data,
                                // 【【客户反馈】完成就诊的处方单子修改保存出现提示；】
                                // https://www.tapd.cn/43780818/bugtrace/bugs/view/1143780818001065145
                                clinicId: this.data.clinicId || this.currentClinic.clinicId,
                            }));
                            this.showDialog = false;
                            this.$emit('confirm');
                            this.btnLoading = false;
                        } catch (e) {
                            this.btnLoading = false;
                        }
                    }
                });
            },

            cancel() {
                this.isClickCancel = true;
                this.showDialog = false;
                if (!this.innerChargeLocked) {
                    this.outpatientFeeChangeUnlock();
                }
            },

            enterEvent(e) {
                const inputs = $('.charge-review-dialog-content .abc-charge-table').find('.abc-input__inner').not(':disabled').not(function () {
                    const tabIndex = $(this).attr('tabindex');
                    return parseInt(tabIndex) === -1;
                });
                const tab = $(e.target).attr('tabindex'); let nextInput = null;
                if (tab === 'last') {
                    nextInput = inputs[ inputs.length - 1 ];
                } else {
                    const targetIndex = inputs.index(e.target);
                    nextInput = inputs[ targetIndex + 1 ];
                }

                nextInput && this.$nextTick(() => {
                    nextInput.selectionStart = 0;
                    nextInput.selectionEnd = nextInput.value.length;
                    nextInput.select();
                    nextInput.focus();

                    // magic code
                    this.timeoutId = setTimeout(() => {
                                                    nextInput.select();
                                                    nextInput.focus();
                                                },
                                                50,
                    );
                });
            },
            keydownHandle(e) {
                const KEY_ENTER = 13;
                const KEY_ESC = 27;
                if (e.keyCode === KEY_ENTER) {
                    e.preventDefault();
                    e.stopPropagation();
                    if (document.activeElement) {
                        document.activeElement.blur();
                        $(this.$el).focus();
                    }
                    this.timeoutId = setTimeout(() => {
                        this.submit();
                    }, 1);
                } else if (e.keyCode === KEY_ESC) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.cancel();
                }
            },

            changeTab(index, item) {
                this.selectedTab = item.value;
                this.initHandler();
            },

            initHandler() {
                this.fetchParams.offset = 0;
                this.hospitalChargeSheetList = [];
                this.fetchHospitalChargeList(true);
            },

            async fetchHospitalChargeList(showLoading = false) {
                if (!this.selectedTab) return;
                if (!this.hospitalInfo) return;
                this.contentLoading = showLoading && true;
                const { data } = await ChargeAPI.fetchHospitalChargeList(this.hospitalInfo.hospitalSheetId, this.fetchParams);
                this.hospitalSheetSummary = data.hospitalSheetSummary;
                if (this.hospitalChargeSheetList.length >= data.total) {
                    this.isLast = true;
                }
                if (data.offset === this.fetchParams.offset) {
                    this.formatChargeSheetHandler(data.rows);
                    this.fetchParams.offset += this.fetchParams.limit;
                    this.hospitalChargeSheetList = this.hospitalChargeSheetList.concat(data.rows || []);
                }
                this.contentLoading = false;
                this.$nextTick(() => {
                    this.$refs.dialog.updateDialogHeight();
                });
            },

            formatChargeSheetHandler(data) {
                data.forEach((chargeSheet) => {
                    chargeSheet.chargeForms.forEach((form, formIndex) => {
                        if (isProductForm(form)) {
                            form.formSortIndex = (form.sort || formIndex) + 10;
                        } else if (isPrescriptionForm(form)) {
                            form.formSortIndex = (form.sort || formIndex) + 100;
                        }
                    });
                    chargeSheet.chargeForms.sort((a, b) => a.formSortIndex - b.formSortIndex);
                    let sortCount = 1;
                    chargeSheet.chargeForms.forEach((form) => {
                        form.chargeFormItems.forEach((item) => {
                            this.$set(item, 'sortIndex', sortCount);
                            sortCount++;
                        });
                    });
                });
            },

            /**
             * @desc 清空所有议价，还原单子原价
             * <AUTHOR>
             * @date 2022-03-12 14:43:38
             */
            clearBargainHandle() {
                this.data.expectedTotalPrice = null;
                OutpatientFormKeys.forEach(({
                    formsKey, itemsKey,
                }) => {
                    this.data[formsKey].forEach((form) => {
                        form.expectedTotalPrice = null;
                        form[itemsKey].forEach((item) => {
                            item.currentUnitPrice = null;
                            item.formFlatPrice = null;
                            item.sheetFlatPrice = null;
                            item.fractionPrice = null;
                            item.expectedUnitPrice = null;
                            item.expectedTotalPrice = null;
                        });
                    });
                });
                this.calcFee();
            },

            /**
             * @desc 门诊单内容变更
             * <AUTHOR>
             * @date 2022/06/06 10:47:44
             */
            outpatientFeeChange() {
                // 处理外部未变更，input更改后直接点取消，错误加锁的场景
                if (!this.isClickCancel) {
                    this.$abcEventBus.$emit('outpatient-fee-change', {
                        from: '费用预览',
                        needCalcFee: false,
                    });
                }
            },

            /**
             * @desc 收费单解锁
             * <AUTHOR>
             * @date 2022/06/06 10:47:44
             */
            outpatientFeeChangeUnlock() {
                this.$abcEventBus.$emit('outpatient-fee-change-unlock', '费用预览');
            },

            countFormItems(form) {
                return form.prescriptionFormItems.filter((item) => {
                    return this.availableFormItemTr(item);
                }).length;
            },

            adjustPriceChange(data, key, val, callback) {
                data[key] = val;
                callback();
                // if (!isTotal) {
                //     this.changeRegUnitPrice();
                // } else {
                //     this.handleChangeTotalPrice();
                // }
            },
        },
    };
</script>
