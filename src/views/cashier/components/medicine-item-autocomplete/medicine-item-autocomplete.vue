<template>
    <div class="retail-western-medicine-autocomplete">
        <abc-autocomplete
            ref="medicine-autocomplete"
            :key="closeSwitch"
            v-model="itemCache"
            :fetch-suggestions="queryItemAsync"
            :show-suggestions="showSuggestions"
            :custom-class="`medicine-item-autocomplete-suggestion ${ customClass}`"
            :async-fetch="true"
            :keyboard-event="['/']"
            focus-show
            :delay-time="10"
            data-cy="charge-retail-western-medicine-autocomplete"
            :placeholder="placeholder"
            :resident-sugguestions="supportSearchFilter"
            :close-on-click-outside="handleCloseOnClickOutside"
            size="medium"
            :max-length="80"
            @keydown.native.stop=""
            @enter="enterHandler"
            @blur="blurBarcode"
            @focus="focusBarcode"
            @enterEvent="selectItem"
        >
            <template slot="suggestion-header">
                <suggestion-header-filter
                    v-if="supportSearchFilter && refAutoComplete?.showSuggestions"
                    :glasses-form="glassesForm"
                    :params.sync="filterParams"
                    @change="onFilterChange"
                ></suggestion-header-filter>

                <div class="suggestion-title" :class="[supportSearchFilter ? 'second' : '']">
                    <div v-if="isShowGoodsAutocompleteShortId" class="goods-code">
                        商品编码
                    </div>
                    <div class="name" style="flex: 2;">
                        药名
                    </div>
                    <div
                        v-if="showFeeType"
                        style="width: 40px;"
                    >
                        类型
                    </div>
                    <div class="spec" style="width: 94px; padding-left: 6px;">
                        规格
                    </div>
                    <div class="stock" style="width: 70px; padding-right: 6px; text-align: right;">
                        库存
                    </div>
                    <div class="price" style="width: 80px; padding-right: 12px; text-align: right;">
                        价格
                    </div>
                    <div style="width: 138px; padding-left: 12px; text-align: left;">
                        医保
                    </div>
                    <div class="manufacturer" style="width: 100px; padding-left: 10px;">
                        <manufacturer-select
                            v-if="isSupportManufacturerFilter"
                            v-model="selectedManufacturer"
                            :manufacturer-options="manufacturerOptions"
                            size="tiny"
                            placeholder="厂家"
                            @change="handleManufacturerChange"
                        ></manufacturer-select>
                        <template v-else>
                            厂家
                        </template>
                    </div>
                    <div class="min-expiry-date" style="width: 90px; padding-left: 10px; text-align: center;">
                        最近效期
                    </div>
                    <div class="remark" style="width: 112px; padding-left: 10px;">
                        备注
                    </div>
                </div>
            </template>

            <template slot="suggestions" slot-scope="props">
                <dt
                    slot="reference"
                    class="suggestions-item"
                    :class="{
                        selected: props.index === props.currentIndex,
                        'is-tips': props.suggestion.isTips,
                        'not-source': isNoStock(props.suggestion),
                    }"
                    :disabled="props.suggestion.disabled"
                    @click="selectItem(props.suggestion)"
                >
                    <div
                        v-if="isShowGoodsAutocompleteShortId"
                        class="goods-code"
                        :title="props.suggestion.shortId || ''"
                        style="width: 70px; padding-left: 6px;"
                    >
                        <template v-if="!isNoStock(props.suggestion)">
                            {{ props.suggestion.shortId || '' }}
                        </template>
                    </div>

                    <div class="name" style="flex: 2; overflow: hidden;" :title="props.suggestion | goodsHoverTitle">
                        <abc-flex :gap="4" align="center">
                            <span class="ellipsis">
                                {{ getName(props.suggestion) }}
                            </span>

                            <biz-goods-info-tag-group
                                v-if="showAntibioticAndDangerIngredient(props.suggestion)"
                                :product-info="props.suggestion"
                                :is-fold-tags="true"
                                style="flex: 1;"
                            ></biz-goods-info-tag-group>

                            <span v-if="checkCanShowDeviceFlag(props.suggestion)" class="device-name">
                                <abc-tooltip placement="top" content="已关联设备" :z-index="10000">
                                    <abc-icon
                                        icon="n-device-feedback-fill"
                                        :size="14"
                                        class="device-icon"
                                    ></abc-icon>
                                </abc-tooltip>
                            </span>

                            <compose-tag v-if="props.suggestion.type === 11"></compose-tag>

                            <!--云检项目标识-->
                            <biz-exam-business-tag
                                is-cloud-tag
                                :cloud-supplier-flag="props.suggestion.cloudSupplierFlag"
                                :disabled="props.suggestion.disabled"
                            ></biz-exam-business-tag>

                            <!--云检标签-->
                            <biz-exam-business-tag
                                is-out-sourcing-tag
                                :coop-flag="props.suggestion.coopFlag"
                                :type="props.suggestion.type"
                                :sub-type="props.suggestion.subType"
                            ></biz-exam-business-tag>
                        </abc-flex>
                    </div>

                    <template v-if="!props.suggestion.isTips">
                        <div
                            v-if="showFeeType"
                            style="width: 40px;"
                        >
                            {{ getFeeTypeStr(props.suggestion) }}
                        </div>

                        <div
                            v-if="fromPharmacy === PharmacyTypeEnum.AIR_PHARMACY"
                            class="gray spec"
                            :title="props.suggestion.spec || ''"
                            style="width: 94px; padding-left: 6px;"
                        >
                            {{ props.suggestion.spec || '' }}
                        </div>

                        <div
                            v-else
                            class="gray spec"
                            :title="getSpec(props.suggestion)"
                            style="width: 94px; padding-left: 6px;"
                        >
                            {{ props.suggestion | getSpec }}
                        </div>

                        <div
                            class="stock"
                            :title="displayInventory(props.suggestion)"
                            style="width: 70px; padding-right: 6px; text-align: right;"
                        >
                            <template v-if="!isNoStock(props.suggestion)">
                                {{ displayInventory(props.suggestion) }}
                            </template>
                        </div>

                        <div
                            v-if="!isSysItem(props.suggestion)"
                            class="gray price"
                            style="width: 80px; padding-right: 12px; text-align: right;"
                        >
                            <template v-if="!isNoStock(props.suggestion)">
                                <abc-money :value="props.suggestion.packagePrice" :is-show-space="true" :to-fixed2="false"></abc-money>
                            </template>
                        </div>

                        <div class="gray" style="width: 138px; padding-left: 12px; text-align: left;">
                            <span>{{ displayMedicalFeeGrade(props.suggestion) }}</span>
                        </div>

                        <div
                            class="gray manufacturer"
                            :title="props.suggestion.manufacturer"
                            style="width: 100px; padding-left: 10px;"
                        >
                            {{ props.suggestion.manufacturer }}
                        </div>

                        <div
                            class="gray min-expiry-date"
                            :class="{ expired: isExpired(props.suggestion.minExpiryDate) }"
                            :title="props.suggestion.minExpiryDate || ''"
                            :style="{
                                width: '90px',
                                'padding-left': '10px',
                                'text-align': 'center',
                            }"
                        >
                            {{ props.suggestion.minExpiryDate || '' }}
                        </div>

                        <div
                            class="gray remark ellipsis"
                            :title="props.suggestion.remark"
                            style="width: 112px; padding-left: 10px;"
                        >
                            {{ props.suggestion.remark }}
                        </div>
                    </template>
                </dt>
            </template>

            <template v-if="suggestions.length === 0" slot="suggestion-footer">
                <abc-content-empty
                    class="search-empty"
                    value="暂无数据"
                    top="0"
                    :icon-size="10"
                ></abc-content-empty>
            </template>

            <div v-if="icon" slot="prepend" class="search-icon">
                <abc-icon icon="s-add-line-medium" color="var(--abc-color-T3)"></abc-icon>
            </div>

            <div v-if="keyboardEnable" slot="append" class="keyboard-icon">
                <keyboard-popover
                    :disabled-change-specification="disabledChangeSpecification"
                    :show-glasses-tips="supportSearchFilter"
                ></keyboard-popover>
            </div>
        </abc-autocomplete>
        <div v-if="keyboardEnable" class="search-type-str">
            {{ searchType }}
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import * as repository from 'MfFeEngine/repository';
    // API
    import ChargeAPI from 'api/charge';

    // components
    import KeyboardPopover from 'src/views/layout/keyboard-describe-popover/keyboard-describe-popover';
    import SuggestionHeaderFilter from 'src/views/layout/suggestion-header-filter/suggestion-header-filter.vue';

    // utils
    import {
        getSpec, medicalFeeGradeFormatStr,
    } from 'src/filters';
    import ComposeTag from 'src/views/outpatient/common/compose-tag';
    import { mapGetters } from 'vuex';
    import {
        GoodsSubTypeEnum,
        GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import { debounce } from 'utils/lodash';
    import { SourceFormTypeEnum } from '@/service/charge/constants.js';
    import {
        checkCanShowDeviceFlag,
        isDateExpired,
        isBarcode,
        checkHasAbcDialog,
    } from '@/utils';
    import clone from 'utils/clone';
    import BarcodeDetectorV2 from 'utils/barcode-detector-v2';
    import TraceCode from '@/service/trace-code/service';
    import BizGoodsInfoTagGroup from '@/components-composite/biz-goods-info-tag-group/index.js';
    import BizExamBusinessTag from 'src/components-composite/biz-exam-business-tag';
    import ManufacturerSelect from 'views/inventory/common/manufacturer-select/index.vue';
    import useAutoCompleteManufacturerSelect from 'views/inventory/common/manufacturer-select';

    export default {
        name: 'MedicineItemAutocomplete',
        components: {
            BizExamBusinessTag,
            BizGoodsInfoTagGroup,
            KeyboardPopover,
            ComposeTag,
            SuggestionHeaderFilter,
            ManufacturerSelect,
        },
        props: {
            closeSwitch: Boolean,
            keyboardEnable: {
                type: Boolean,
                default: true,
            },
            placeholder: {
                type: String,
                default: '输入药品或项目拼音码',
            },
            customClass: {
                type: String,
                default: '',
            },
            canKeyboardCharge: {
                type: Boolean,
                default: true,
            },
            queryFunction: {
                type: Function,
            },
            disabledChangeSpecification: {
                type: Boolean,
                default: false,
            },
            // 0本地药房，1空中药房
            fromPharmacy: {
                type: Number,
                default: 0,
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            icon: {
                type: Boolean,
                default: true,
            },
            departmentId: [String],
            processType: [String, Number],
            processSubType: [String, Number],
            openSearchFilter: Boolean,
            chargeForms: {
                type: Array,
                default() {
                    return [];
                },
            },

            // 禁止扫码回调
            disabledScanBarcode: {
                type: Boolean,
                default: false,
            },
            // 是否支持厂家筛选
            isSupportManufacturerFilter: {
                type: Boolean,
                default: true,
            },
        },
        setup() {
            const {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            } = useAutoCompleteManufacturerSelect();

            return {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            };
        },
        data() {
            return {
                PharmacyTypeEnum,
                itemCache: '',
                showSuggestions: false,
                searchType: '',
                filterParams: {
                    typeId: [], // 品种类型
                    customTypeId: [], // 子类型
                    customType: undefined,// 定制类型 0 定制 10成品
                    refractiveIndex: undefined,// 折射率
                    spherical: '',// 球镜
                    lenticular: '',// 柱镜
                    addLight: '',// 下加光
                    focalLength: '',// 后顶焦度
                    wearCycle: '',// 佩戴周期
                    material: '', // 材质
                    spec: '', // 规格
                    color: '', // 颜色
                },
                suggestions: [],
                refAutoComplete: null,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'clinicConfig',
                'goodsConfig',
                'disableNoStockGoods',
                'isEnableListingPrice',
            ]),
            ...mapGetters('viewDistribute', [
                'featureProcess',
                'viewDistributeConfig',
                'featureSupportFilterEyeGlasses',
            ]),
            isShowGoodsAutocompleteShortId() {
                return this.viewDistributeConfig.Inventory.isShowGoodsAutocompleteShortId;
            },

            // 零售收费搜索框区分医嘱和费用项
            showFeeType() {
                return this.viewDistributeConfig.Cashier.showFeeType;
            },
            // 是否搜索眼镜
            hasEyeglasses() {
                return this.viewDistributeConfig.Inventory.hasEyeglasses;
            },
            supportFilterEyeGlasses() {
                return this.featureSupportFilterEyeGlasses;
            },
            supportSearchFilter() {
                return this.openSearchFilter && this.supportFilterEyeGlasses;
            },
            glassesForm() {
                return this.chargeForms.find((form) => form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_GLASSES);
            },
        },
        watch: {
            'refAutoComplete.showSuggestions': {
                handler(val) {
                    if (!val) {
                        this.clearGlassesParams();
                    }
                },
            },
            disabledScanBarcode: {
                handler(val) {
                    this.$nextTick(() => {
                        val ? this.stopBarcodeDetect() : this.startBarcodeDetect();
                    });
                },
                immediate: true,
            },
            itemCache: {
                handler (val) {
                    if (!val.trim()) {
                        this.$emit('clear');
                    }
                    if (typeof this.clearManufacturerData === 'function') this.clearManufacturerData();
                },
            },
        },
        created() {
            this._goods_repo_instance = repository.GoodsRepositoryService.getInstance();
            this._queryItemAsync = debounce(this.queryItemAsync, 400, true);
            this.$abcEventBus.$on('click-glasses-tr-name', (data) => {
                const {
                    spherical,
                    lenticular,
                } = data;
                this.filterParams.spherical = spherical;
                this.filterParams.lenticular = lenticular;
                this.queryItemAsync(this.itemCache);
            }, this);
        },
        mounted() {
            if (this.supportSearchFilter || this.isSupportManufacturerFilter) {
                this.refAutoComplete = this.$refs['medicine-autocomplete'];
            }
        },
        beforeDestroy() {
            this.stopBarcodeDetect();
        },
        methods: {
            startBarcodeDetect() {
                if (this._barcodeDetector) return;
                this._barcodeDetector = BarcodeDetectorV2.getInstance();
                this._barcodeDetector.startDetect(this.handleBarcode);
            },
            stopBarcodeDetect() {
                if (!this._barcodeDetector) return;
                this._barcodeDetector.stopDetect(this.handleBarcode);
                this._barcodeDetector = null;
            },
            checkCanShowDeviceFlag,
            getSpec,
            isExpired(minExpiryDate) {
                const month = this.goodsConfig?.stockGoodsConfig?.stockWarnGoodsWillExpiredMonth;
                return isDateExpired(minExpiryDate, month);
            },
            isSysItem(goods) {
                return goods.type === GoodsTypeEnum.REGISTRATION ||
                    goods.type === GoodsTypeEnum.EXPRESS_DELIVERY ||
                    goods.type === GoodsTypeEnum.DECOCTION;
            },
            isNoStock(goods) {
                return this.isGoods(goods) && goods.noStocks;
            },

            /** ------------------------------------------------------------------------------------
             * 根据输入queryString 做快捷键处理
             * 查询诊所的所有检查项目，中药，西药
             * @param queryString input输入的内容
             * @param callback
             */
            async queryItemAsync(queryString, callback) {
                queryString = queryString.trim();

                // 解析queryString，使用空格分隔，取第一项为原始keyword，最后一项为parseManufacturer
                let parseManufacturer = '';

                if (queryString) {
                    const keyParts = queryString.split(' ').filter((part) => part.trim());
                    if (keyParts.length > 1) {
                        queryString = keyParts[0];
                        parseManufacturer = keyParts[keyParts.length - 1];
                    }
                }

                // 快捷键切换类型（目前是只有零售收费单支持）
                if (this.keyboardEnable) {
                    if (/^\//.test(queryString) && !this.disabledChangeSpecification) {
                        this.itemCache = '';
                        if (this.searchType === '') {
                            this.searchType = '饮片';
                        } else if (this.searchType === '饮片') {
                            this.searchType = '颗粒';
                        } else if (this.searchType === '颗粒') {
                            this.searchType = '成药';
                        } else if (this.searchType === '成药') {
                            this.searchType = '';
                        }
                        queryString = '';
                    }

                    if (/^\./.test(queryString)) {
                        return false;
                    }

                    if (/^\*/.test(queryString)) {
                        return false;
                    }
                }

                if (/^-/.test(queryString)) {
                    return false;
                }

                if (!queryString && !this.supportSearchFilter) {
                    if (callback) {
                        callback([]);
                    } else {
                        this.refAutoComplete.focus();
                        this.refAutoComplete.callbackHandler([]);
                    }
                    return false;
                }
                try {
                    let data;
                    if (typeof this.queryFunction === 'function') {
                        data = await this.queryFunction(queryString);
                    } else {
                        const params = {
                            clinicId: this.currentClinic.clinicId,
                            departmentId: this.departmentId,
                            processType: this.processType,
                            processSubType: this.processSubType,
                            intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                            key: queryString,
                            type: '',
                            subType: '',
                            cMSpec: '',
                            sysType: [], // 系统收费项目type
                        };

                        if (this.keyboardEnable) {
                            if (this.searchType === '饮片') {
                                params.type = 1;
                                params.subType = 2;
                                params.cMSpec = '中药饮片';
                            } else if (this.searchType === '颗粒') {
                                params.type = 1;
                                params.subType = 2;
                                params.cMSpec = '中药颗粒';
                            } else if (this.searchType === '成药') {
                                params.type = 1;
                                params.subType = [1, 3];
                                params.cMSpec = '';
                            } else {
                                params.type = [
                                    GoodsTypeEnum.MEDICINE,
                                    GoodsTypeEnum.MATERIAL,
                                    GoodsTypeEnum.EXAMINATION,
                                    GoodsTypeEnum.TREATMENT,
                                    GoodsTypeEnum.GOODS,
                                    GoodsTypeEnum.COMPOSE,
                                    GoodsTypeEnum.EXPRESS_DELIVERY,
                                    GoodsTypeEnum.DECOCTION,
                                    GoodsTypeEnum.INGREDIENT,
                                    GoodsTypeEnum.FAMILY_DOCTOR_SIGN,
                                    GoodsTypeEnum.OTHER,
                                    GoodsTypeEnum.PROCESSING,
                                    GoodsTypeEnum.NURSE,
                                    GoodsTypeEnum.CONSULTATION,
                                    GoodsTypeEnum.SAMPLE,
                                ];
                            }
                        } else {
                            /**
                             * @desc 门诊收费单
                             * @desc 2：物资；3：检查；4：治疗；7：商品；11：套餐 19: 其他
                             * <AUTHOR>
                             * @date 2019/10/28 12:40:58
                             */
                            params.type = [
                                GoodsTypeEnum.MATERIAL,
                                GoodsTypeEnum.EXAMINATION,
                                GoodsTypeEnum.TREATMENT,
                                GoodsTypeEnum.GOODS,
                                GoodsTypeEnum.COMPOSE,
                                GoodsTypeEnum.OTHER,
                                GoodsTypeEnum.NURSE,
                            ];
                            params.sysType = [];
                        }

                        if (this.hasEyeglasses) {
                            params.type.push(GoodsTypeEnum.EYEGLASSES);
                        }
                        let res;
                        // 是否启用远程搜索
                        const useRemoteSearch = this._goods_repo_instance?.searchConfig?.useRemoteSearchGoodsAPI ?? true;
                        if (this.supportSearchFilter || !useRemoteSearch) {
                            Object.assign(params, this.filterParams);
                            const searchParams = this.mergeFilterParams(params, this.filterParams);
                            /**
                             * @desc 本地搜索
                             * @param {Object} params
                             * @param {Boolean} 是否启用本地搜索
                             * @param {Number} 参数解析类型 1: 普通解析 2: 优先眼镜格式解析如：r1.61 s-2 c-2
                             * @return {Object} 返回搜索结果
                             */
                            res = await this._goods_repo_instance.searchChargeGoods(
                                searchParams, true, this.supportSearchFilter ? 2 : 1,
                            );
                            data = res;
                        } else {
                            res = await ChargeAPI.fetchAllGoods(params);
                            data = res.data;
                        }
                    }
                    if (!data) {
                        if (callback) {
                            callback([]);
                        } else {
                            this.refAutoComplete.focus();
                            this.refAutoComplete.callbackHandler([]);
                        }
                    }

                    if (
                        this.disableNoStockGoods &&
                        this.fromPharmacy !== PharmacyTypeEnum.VIRTUAL_PHARMACY &&
                        !this.multiPharmacyCanUse
                    ) {
                        // "不允许开无库存药品" &&
                        // 非虚拟药房（都没库存） &&
                        // 打开多药房 则可以选择库存不足的药品不受开关控制
                        data.list = data.list.map((item) => {
                            if (this.isGoods(item)) {
                                item.disabled = item.noStocks || (item.stockPackageCount + item.stockPieceCount) <= 0;
                            }
                            return item;
                        });
                    }

                    // 处理厂家筛选
                    const filteredList = data.list || [];
                    if (this.isSupportManufacturerFilter && this.createManufacturerOptions) {
                        this.createManufacturerOptions(filteredList);
                    }

                    // 如果选择了厂家，则过滤结果
                    if (this.isSupportManufacturerFilter && this.filterManufacturer) {
                        data.list = this.filterManufacturer(filteredList);
                    }

                    // 透传 data.query.key 保证请求回来和搜索key一致
                    let keyword = '';
                    if (data.query) {
                        keyword = data.query.keyword || data.query.key;
                    }
                    if (callback) {
                        callback(data.list, keyword || '');
                    } else {
                        this.refAutoComplete.focus();
                        this.refAutoComplete.callbackHandler(data.list, keyword || '');
                    }
                    this.suggestions = data.list || [];
                    this.keywordTraceableCode = data.keywordTraceableCode;
                } catch (err) {
                    this.suggestions = [];
                    if (callback) {
                        callback([]);
                    } else {
                        this.refAutoComplete.focus();
                        this.refAutoComplete.callbackHandler([]);
                    }
                }
            },
            /**
             * @desc 拼接商品name
             * <AUTHOR>
             * @date 2019/03/30 11:20:12
             */
            getName(item) {
                if (item.type === 1 && item.subType === 2) {
                    return item.medicineCadn || item.name;
                }
                if (item.medicineCadn) {
                    return item.medicineCadn + (item.name ? `(${item.name})` : '');
                }
                return item.name;

            },

            /**
             * 西药和西成药才展示限制级和精麻毒标签
             * @return {boolean}
             */
            showAntibioticAndDangerIngredient(item) {
                return item.type === GoodsTypeEnum.MEDICINE &&
                    (
                        item.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine ||
                        item.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM
                    );
            },

            isGoods(item) {
                return item.type === GoodsTypeEnum.MEDICINE || item.type === GoodsTypeEnum.MATERIAL || item.type === GoodsTypeEnum.GOODS || item.type === 24;
            },

            /**
             * @desc display autocomplete 下拉框 库存信息
             * <AUTHOR>
             * @date 2018/08/02 15:11:50
             */
            displayInventory(item) {
                if (item.type !== 1 && item.type !== 2 && item.type !== 7 && item.type !== 24) return '';
                let str = '';
                if (item.stockPackageCount) {
                    str += `${item.stockPackageCount}${item.packageUnit}`;
                }
                if (item.stockPieceCount) {
                    str += `${item.stockPieceCount}${item.pieceUnit}`;
                }
                if (!item.stockPackageCount && !item.stockPieceCount) {
                    if (item.packageUnit) {
                        str += `0${item.packageUnit || ''}`;
                    } else {
                        str += `0${item.pieceUnit || 'g'}`;
                    }
                }
                return str;
            },

            /**
             * @desc autocomplete 响应回车事件
             *  /  切换分类  全部、饮片、颗粒、西成药
             *  .  删除指定  .2 删除第二个药品
             *  *  付数     *3 中药剂数 置为3
             *  -  结算     调起收费
             * <AUTHOR>
             * @date 2018/12/29 12:18:37
             */
            async enterHandler(event) {
                const { value } = event.currentTarget;

                if (this.keyboardEnable) {
                    if (/^\.\d+$/.test(value)) {
                        this.$emit('deleteItem', value.replace(/[^\d]/g, ''));
                        this.itemCache = '';
                        return false;
                    }

                    if (/^\*\d+/.test(value)) {
                        let doseCount = value.replace(/\*/g, '');
                        doseCount = Math.floor(doseCount);
                        if (!doseCount || +doseCount === 0 || isNaN(doseCount)) {
                            doseCount = '';
                        }
                        this.$emit('inputDoseCount', doseCount);
                        this.itemCache = '';
                        return false;
                    }
                }

                if (/^-$/.test(value) && this.canKeyboardCharge) {
                    event.cancelBubble = true;
                    event.returnValue = false;
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.$el.querySelector('input').blur();
                    this.itemCache = '';
                    this.$emit('submit');
                    return false;
                }

                if (isBarcode(value) || TraceCode.isTraceableCode(value)) {
                    this.handleBarcode(event, value);
                }
            },
            async handleBarcode(e, barcode) {
                const hasDialog = checkHasAbcDialog();
                if (hasDialog) return;

                this.itemCache = '';
                if (!barcode) return;

                const params = {
                    clinicId: this.currentClinic.clinicId,
                    key: barcode,
                };
                if (this.keyboardEnable) {
                    params.type = [
                        GoodsTypeEnum.MEDICINE,
                        GoodsTypeEnum.MATERIAL,
                        GoodsTypeEnum.EXAMINATION,
                        GoodsTypeEnum.TREATMENT,
                        GoodsTypeEnum.GOODS,
                        GoodsTypeEnum.COMPOSE,
                        GoodsTypeEnum.EXPRESS_DELIVERY,
                        GoodsTypeEnum.DECOCTION,
                        GoodsTypeEnum.INGREDIENT,
                        GoodsTypeEnum.FAMILY_DOCTOR_SIGN,
                        GoodsTypeEnum.OTHER,
                        GoodsTypeEnum.PROCESSING,
                        GoodsTypeEnum.NURSE,
                        GoodsTypeEnum.CONSULTATION,
                        GoodsTypeEnum.SAMPLE,
                    ];
                } else {
                    /**
                     * @desc 门诊收费单
                     * @desc 2：物资；3：检查；4：治疗；7：商品；11：套餐 19: 其他
                     * <AUTHOR>
                     * @date 2019/10/28 12:40:58
                     */
                    params.type = [
                        GoodsTypeEnum.MATERIAL,
                        GoodsTypeEnum.EXAMINATION,
                        GoodsTypeEnum.TREATMENT,
                        GoodsTypeEnum.GOODS,
                        GoodsTypeEnum.COMPOSE,
                        GoodsTypeEnum.OTHER,
                        GoodsTypeEnum.NURSE,
                    ];
                    params.sysType = [];
                }
                const { data } = await ChargeAPI.fetchAllGoods(params);
                const {
                    keywordTraceableCode,
                    query,
                    list,
                } = data;
                if (barcode === query.key) {
                    if (list.length) {
                        this.selectItem(list[ 0 ], {
                            scanBarcode: true,
                            keywordTraceableCode,
                        });
                        this.itemCache = '';
                    } else {
                        if (!this.keyboardEnable) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: [ '门诊开出的收费单，不能添加药品。' ],
                                onClose: () => {
                                    $('.retail-western-medicine-autocomplete input').focus();
                                },
                            });
                            return;
                        }
                        if (isBarcode(barcode) || keywordTraceableCode) {
                            let str = '条形码/追溯码未绑定商品档案，可在库存档案中完成绑定。';
                            if (keywordTraceableCode?.goods) {
                                str = '该追溯码绑定商品无法销售！';
                            }
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: [ str ],
                                onClose: () => {
                                    $('.retail-western-medicine-autocomplete input').focus();
                                },
                            });
                        }
                        document.activeElement?.blur();
                    }
                }
            },
            blurBarcode() {
                this.$emit('blur');
            },
            focusBarcode() {
                this.$emit('focus');
            },

            /**
             * @desc 选择后处理函数
             * <AUTHOR>
             * @date 2019/03/25 19:15:58
             */
            async selectItem(selected, options = {}, event) {
                if (!selected) return false;
                if (this.keywordTraceableCode) {

                    // 遇到了扫码枪卡顿的情况，导致此时触发了搜索了 回车响应了autocomplete默认事件
                    // 此时回车后需要修正实际no
                    const inputVal = event?.target?.value;

                    if (TraceCode.isTraceableCode(inputVal,false)) {
                        if (inputVal.indexOf(this.keywordTraceableCode.no) === 0) {
                            this.keywordTraceableCode.no = inputVal;
                            if (this.keywordTraceableCode.traceableCodeNoInfo) {
                                this.keywordTraceableCode.traceableCodeNoInfo.no = inputVal;
                            }
                        }
                        Object.assign(options, {
                            keywordTraceableCode: this.keywordTraceableCode,
                        });
                    }
                }
                this.$emit('select', selected, options);
                this.itemCache = '';
                this.keywordTraceableCode = null;
            },

            getFeeTypeStr(item) {
                const {
                    type,
                } = item;
                if (type === GoodsTypeEnum.OTHER) {
                    return '费用项';
                }
                return '医嘱';
            },

            onFilterChange() {
                this._queryItemAsync(this.itemCache);
            },

            clearGlassesParams() {
                this.filterParams = {
                    typeId: [], // 品种类型
                    customTypeId: [], // 子类型
                    customType: undefined,// 定制类型 0 定制 10成品
                    refractiveIndex: undefined,// 折射率
                    spherical: '',// 球镜
                    lenticular: '',// 柱镜
                    addLight: '',// 下加光
                    focalLength: '',// 后顶焦度
                    wearCycle: '',// 佩戴周期
                    material: '', // 材质
                    spec: '', // 规格
                    color: '', // 颜色
                };
            },
            handleCloseOnClickOutside(e) {
                const eventPath = e?.path || (e?.composedPath?.());

                const isInnerEl = eventPath.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className?.includes('suggestions-item-filter');
                });

                // 检查是否点击了厂家筛选下拉框
                if (this.isSupportManufacturerFilter && eventPath?.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className.includes('goods-auto-complete-manufacturer-select');
                })) {
                    return false;
                }

                // 厂家筛选关闭面板时重置搜索结果
                if (this.isSupportManufacturerFilter && this.selectedManufacturer && !isInnerEl) {
                    // 清除选中厂家
                    if (typeof this.clearManufacturerData === 'function') {
                        this.clearManufacturerData(true);
                    }
                    // 更新列表数据
                    this.queryItemAsync(this.itemCache, (list) => {
                        // 手动触发下拉框更新-面板不展示
                        if (this.refAutoComplete) {
                            this.refAutoComplete.isFocus = false;
                            this.refAutoComplete.callbackHandler(list);
                        }
                    });
                }

                if (!isInnerEl) {
                    this.$emit('closePanel');
                }

                return !isInnerEl;
            },

            handleManufacturerChange() {
                // 如果不支持厂家筛选，直接返回
                if (!this.isSupportManufacturerFilter) {
                    return;
                }
                // 当厂家筛选发生变化时，重新搜索
                // 配合focus-show重新触发查询
                this.$nextTick(() => {
                    this.refAutoComplete?.focus?.();
                });
            },
            mergeFilterParams(params, mergeParams) {
                const filterParams = clone(mergeParams);
                for (const key in filterParams) {
                    if (
                        filterParams.hasOwnProperty(key) &&
                        (
                            (typeof (filterParams[key]) !== 'number' && !filterParams[key]) ||
                            filterParams[key]?.length === 0
                        )
                    ) {
                        delete filterParams[key];
                        delete params[key];
                    }
                }
                Object.assign(params, filterParams);
                return params;
            },

            displayMedicalFeeGrade(suggestion) {
                const _arr = [];
                const medicalFeeGradeStr = medicalFeeGradeFormatStr(suggestion, {
                    shebaoCardInfo: this.shebaoCardInfo,
                });
                if (medicalFeeGradeStr) {
                    _arr.push(medicalFeeGradeStr);
                }
                if (suggestion.shebao) {
                    if (this.isEnableListingPrice && suggestion.shebao.listingPrice) {
                        _arr.push('挂网');
                    }
                    if (suggestion.shebao.priceLimit) {
                        _arr.push('限价');
                    }
                }
                return _arr.join('/');
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss"></style>
<style lang="scss">
@import "styles/abc-common.scss";

.medical-autocomplete-space-wrap {
    width: 100%;
    padding: 6px;
}

.abc-content-empty-wrapper.search-empty {
    padding: 10px 0;

    .empty-icon {
        img {
            width: 60px;
        }
    }
}
</style>
