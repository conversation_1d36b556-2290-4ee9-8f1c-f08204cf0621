<template>
    <abc-dialog
        v-model="showDialog"
        title="推送支付订单到患者手机"
        content-styles="padding: 0px 24px 24px;"
        class="send-order-dialog-wrapper"
        :shadow="showAdjustment"
    >
        <adjustment-dialog
            v-if="showAdjustment"
            slot="left-extend"
            v-model="showAdjustment"
            :class="{
                'charge-dialog-left-extend': true,
                'adjustment-confirm-style': adjustmentConfirm,
            }"
            :personal-local-storage-key="personalLocalStorageKey"
            :need-pay-fee="receivableFee || 0"
            :origin-total-fee="originTotalFee"
            @change="changeExpectedAdjustmentFeeHandle"
        ></adjustment-dialog>

        <div v-abc-loading="viewLoading" class="dialog-content">
            <div class="send-order-dialog-header">
                <div class="send-order-icon">
                    <i class="iconfont cis-icon-send"></i>
                </div>
                <div class="receivable-fee">
                    <abc-money :value="receivableFee" value-tag="b" :is-show-space="true"></abc-money>
                </div>

                <div class="charge-odd-fee-wrapper">
                    <template v-if="isSystemAutoOdd && chargeSheetSummary.oddFee">
                        {{ sysAdjustmentDesc }}：{{ chargeSheetSummary.oddFee | formatMoney }}
                    </template>

                    <div v-if="draftAdjustmentFee" class="adjustment-fee">
                        {{ draftAdjustmentFee >= 0 ? '+' : '-' }}{{ Math.abs(draftAdjustmentFee) | formatMoney }}
                    </div>
                </div>

                <abc-button
                    v-if="canAdjustment"
                    :class="{
                        'yijia-button': true,
                        checked: !!showAdjustment,
                    }"
                    variant="ghost"
                    @click="showAdjustment = true"
                >
                    整单议价
                </abc-button>
            </div>

            <div class="order-detail">
                <h5>{{ viewData.title }}</h5>
                <p>{{ viewData.nowTime }}</p>

                <p style="margin-top: 13px;">
                    {{ viewData.detail }}
                </p>
                <p style="margin-top: 25px;">
                    订单项目：{{ viewData.medicalItems }}
                </p>
                <p>支付金额：{{ receivableFee }}元</p>
                <p>订单日期：{{ viewData.time }}</p>
                <p>{{ viewData.remark }}</p>
            </div>
        </div>

        <div slot="footer" class="dialog-footer">
            <div>
                <div v-if="status > 0 && !viewLoading" class="attention-card">
                    已部分支付的订单不可推送
                </div>
            </div>
            <abc-button
                :disabled="status > 0 || (status === 1 && receivableFee === 0)"
                :loading="btnLoading"
                style="margin-left: auto;"
                @click="confirm"
            >
                <i class="iconfont cis-icon-send"></i>
                推送
            </abc-button>

            <abc-button variant="ghost" @click="showDialog = false">
                关闭
            </abc-button>
        </div>

        <div
            v-if="showAdjustment"
            slot="shadow"
            style="width: 100%; height: 100%;"
            @click="clickEmptyDou"
        ></div>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import ChargeAPI from '@/api/charge';
    import AdjustmentDialog from '@/service/charge/components/dialog-charge/dialog-adjustment.vue';
    import AbcChargeService from '@/service/charge';

    export default {
        name: 'SendOrderDialog',
        components: {
            AdjustmentDialog,
        },
        props: {
            value: Boolean,
            loading: Boolean,
            isReplay: Boolean,
            status: Number,
            chargeSheetSummary: {
                type: Object,
                required: true,
            },
            postData: Object,
            chargeSheetId: String,
        },
        data() {
            return {
                adjustmentConfirm: false,
                showAdjustment: false,
                btnLoading: false, // 按钮loading
                receivableFee: 0, // 当前应收金额
                viewData: {
                    clinicName: '',
                    detail: '',
                    isAttention: '',
                    medicalItems: '',
                    needPay: '',
                    nowTime: '',
                    orderId: '',
                    remark: '',
                    time: '',
                    title: '',
                },
                viewLoading: false,
                receivableFeeHasOdd: false, // 实收是否有小数
                personalLocalStorageKey: '',
            };
        },
        watch: {
            loading() {
                this.initReceivableFee();
            },

            receivableFee(val) {
                const _arr = val.toString().split('.');
                this.receivableFeeHasOdd = _arr[1] && +_arr[1] > 0;
            },
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'currentClinic',
                'chargeConfig', // 收费设置
            ]),
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustPrice',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                async set(val) {
                    this.$emit('input', val);
                },
            },

            // 是否议价按钮
            canAdjustment() {
                return !!this.chargeConfig.bargainSwitch && this.employeeCanAdjustPrice && !this.disableAdjustmentBtn;
            },

            /**
             * @desc 系统自动抹零，用户不可以取消，修改
             * <AUTHOR> Yang
             * @date 2021-04-13 16:35:19
             */
            isSystemAutoOdd() {
                return this.chargeConfig.roundingType > 0;
            },

            // 系统议价文字描述
            sysAdjustmentDesc() {
                switch (this.chargeConfig.roundingType) {
                    case 1:
                        return '凑整到角';
                    case 2:
                        return '凑整到元';
                    case 3:
                        return '抹零到角';
                    case 4:
                        return '抹零到元';
                    case 5:
                        return '四舍五入到角';
                    case 6:
                        return '四舍五入到元';
                    default:
                        // 未设置或不处理
                        return '';
                }
            },

            /**
             * @desc 原价 + 折扣 + 医生议价 后的￥
             * <AUTHOR> Yang
             * @date 2020-09-04 16:08:01
             */
            originTotalFee() {
                return this.chargeSheetSummary.afterRoundingDiscountedTotalFee;
            },

            /**
             * @desc 收费处议价
             * <AUTHOR> Yang
             * @date 2020-09-04 16:11:19
             */
            draftAdjustmentFee() {
                return this.chargeSheetSummary.draftAdjustmentFee;
            },
        },

        async created() {
            this.$store.dispatch('initChargeConfig');
            this.fetchSendOrderDetail();
            this.initReceivableFee();
            this._chargeService = new AbcChargeService({
                chargeSheetId: this.chargeSheetId,
                chargeStatus: this.status, // 收费状态
                chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                postData: this.postData, // 提交的收费单数据
                isReplay: this.isReplay, // 重新收费
            });
            this.personalLocalStorageKey = `${this.currentClinic.clinicId}_${this.currentClinic.userId}`;
        },

        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },

        methods: {

            /**
             * desc [点击遮罩，提示需要确认议价]
             */
            clickEmptyDou() {
                if (this.adjustmentConfirm) return;
                this.adjustmentConfirm = true;
                this._timer = setTimeout(() => {
                    this.adjustmentConfirm = false;
                }, 300);
            },

            async changeExpectedAdjustmentFeeHandle(data) {
                this.postData.expectedAdjustmentFee = data.expectedAdjustmentFee;
                await this.calcFee();
            },

            /**
             * @desc 推送弹窗内算费
             * <AUTHOR> Yang
             * @date 2021-04-13 20:20:29
             * @params
             * @return
             */
            async calcFee() {
                try {
                    this.btnLoading = true;
                    await this._chargeService.calcFee(null, (calcResult) => {
                        this.receivableFee = calcResult.needPayFee;
                    });
                    this.btnLoading = false;
                } catch (err) {
                    this.btnLoading = false;
                }
            },

            /**
             * @desc 初始化 receivableFee
             * 系统议价情况
             * <AUTHOR>
             * @date 2019/10/22 14:12:22
             */
            initReceivableFee() {
                this.receivableFee = this.chargeSheetSummary.needPayFee;
            },

            /**
             * @desc 获取发送订单信息
             * <AUTHOR>
             * @date 2020/02/13 15:14:44
             */
            async fetchSendOrderDetail() {
                try {
                    this.viewLoading = true;
                    const _postData = AbcChargeService.transSimplePostData(this.postData);
                    const { data } = await ChargeAPI.fetchChargesOrderDetail(_postData);
                    this.viewData = data;
                    this.viewLoading = false;
                } catch (e) {
                    const {
                        code, message,
                    } = e || {};
                    let content = '';
                    if (code === 17013 || code === 17050 || code === 17051) {
                        content = message || '药品信息发生变化，请重新收费';
                    } else {
                        content = message;
                    }
                    if (content) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content,
                        });
                    }
                    this.viewLoading = false;
                    this.showDialog = false;
                }
            },

            async confirm() {
                try {
                    this.btnLoading = true;
                    const params = {
                        expectedAdjustmentFee: this.postData.expectedAdjustmentFee, // 用户修改金额后的 期望议价
                        receivableFee: this.receivableFee,
                        isRenew: +this.isReplay,
                    };
                    const _postData = AbcChargeService.transSimplePostData(this.postData, params);
                    const { data } = await ChargeAPI.pushOrder(_postData);
                    this.$Toast({
                        message: '推送成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.$emit('sendSuccess', data.chargeSheetId);
                    this.btnLoading = false;
                } catch (e) {
                    this.btnLoading = false;
                    const {
                        code, message,
                    } = e || {};
                    let content = '';
                    if (code === 17013 || code === 17050 || code === 17051) {
                        content = message || '药品信息发生变化，请重新收费';
                        if (content) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content,
                                onClose: () => {
                                    this.$emit('refresh');
                                },
                            });
                        }
                    } else {
                        if (message) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: message,
                            });
                        }
                    }
                }
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme';

    @mixin justify($width) {
        width: $width;
        text-align: justify;
        text-align-last: justify;
        text-justify: distribute-all-lines;
    }

    .send-order-dialog-wrapper {
        .abc-dialog {
            width: 428px;
        }

        .send-order-dialog-header {
            box-sizing: border-box;
            width: 100%;
            padding: 15px 0 0;

            .yijia-button {
                position: absolute;
                top: 15px;
                right: 24px;
                height: 28px;
                padding: 0 6px;
                color: #626d77;
                border: 1px solid #d3dbe1;
            }

            .send-order-icon {
                position: absolute;
                top: 15px;
                left: 24px;
                width: 32px;
                height: 32px;
                margin-top: 0;
                line-height: 32px;
                text-align: center;
                background-color: $P4;
                border-radius: 50%;

                .iconfont {
                    color: $G2;
                }
            }

            .abc-button-blank {
                background-color: transparent;
            }

            .checked {
                background-color: #e6eaee;
            }

            button:hover {
                background-color: #eff3f6;
            }

            .receivable-fee {
                font-size: 16px;
                color: $T1;
                text-align: center;

                span {
                    color: $Y2;
                }

                b {
                    font-size: 24px;
                    line-height: 28px;
                    color: $Y2;
                }
            }

            .charge-odd-fee-wrapper {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                height: 26px;
                font-size: 12px;
                color: $T2;
                text-align: center;

                .adjustment-fee {
                    position: absolute;
                    top: 0;
                    right: 0;
                    height: 26px;
                    line-height: 26px;
                }
            }
        }

        .dialog-content {
            > .order-detail {
                width: 380px;
                padding: 12px;
                margin: 0 auto;
                background: rgba(245, 247, 251, 1);
                border-radius: var(--abc-border-radius-small);

                > h5 {
                    font-size: 12px;
                    font-weight: normal;
                    color: $T1;
                }

                > p {
                    width: 348px;
                    margin-top: 8px;
                    font-size: 12px;
                    line-height: 12px;
                    color: #8d9aa8;
                }

                > .line {
                    width: 356px;
                    height: 1px;
                    margin-top: 8px;
                    background: rgba(218, 219, 224, 1);
                }
            }

            > .tips {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 12px;
                font-size: 12px;
                color: #8d9aa8;
            }
        }

        .abc-dialog-footer {
            padding: 15px 24px;

            .dialog-footer {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                height: 38px;

                .cis-icon-send {
                    font-size: 14px;
                }

                > button {
                    min-width: 60px;
                }

                .attention-card {
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                    color: $Y2;

                    .iconfont {
                        margin-right: 4px;
                        font-size: 12px;
                        color: $P4;
                    }
                }
            }
        }

        .charge-dialog-left-extend {
            position: absolute;
            top: 40px;
            right: -282px;
            width: 280px;
            background-color: #ffffff;
            border-radius: var(--abc-dialog-border-radius);
        }

        .adjustment-confirm-style {
            animation: left-right-dou 0.15s ease infinite;
        }

        .attention-card-popover {
            display: inline-block;

            .iconfont {
                &:focus {
                    outline: none;
                }
            }
        }
    }

    .abc-popover-content {
        box-sizing: border-box;
        min-height: 64px;
        padding: 12px 16px;
        overflow-y: auto;
        overflow-y: overlay;
        font-size: 14px;
        color: $Y2;
        background: #fffdec;
        border: 1px solid #eed922;
        box-shadow: 2px 2px 0 0 rgba(0, 0, 0, 0.1);
    }
</style>
