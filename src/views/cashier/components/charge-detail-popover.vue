<template>
    <abc-popover
        ref="charge-detail"
        v-model="showPopover"
        placement="bottom-end"
        trigger="manual"
        theme="white"
        :visible-arrow="false"
        width="300px"
        popper-class="charge-detail-popover"
    >
        <slot slot="reference" name="default"></slot>
        <abc-flex
            ref="charge-detail-popover"
            v-abc-click-outside="outside"
            v-abc-loading="loading"
            data-cy="charge-detail-dialog-content"
            vertical
            :gap="10"
            class="charge-detail-popover__content"
        >
            <h1 class="charge-detail-popover__content__title">
                费用详情
            </h1>
            <abc-flex vertical :gap="4" class="charge-detail-popover__content__section">
                <p v-for="item in medicalBill" :key="item.name">
                    <span class="label">{{ item.name }}</span>
                    <abc-money :value="item.totalFee || 0" :show-symbol="false"></abc-money>
                </p>
            </abc-flex>
            <abc-divider variant="dashed" margin="mini"></abc-divider>
            <abc-flex vertical :gap="4" class="charge-detail-popover__content__section">
                <p>
                    <span class="label">原价</span>
                    <abc-money :value="summary.sourceTotalFee || 0" :show-symbol="false"></abc-money>
                </p>
                <p v-if="discountFee">
                    <span class="label">优惠</span>
                    <span>{{ discountFee }}</span>
                </p>
                <p v-if="adjustmentFee">
                    <span class="label">议价</span>
                    <abc-money :value="adjustmentFee || 0" :show-symbol="false"></abc-money>
                </p>
                <p>
                    <span class="label">应收<abc-text
                        v-if="!!isOpenSocial && summary.sheBaoReceivableFee"
                        theme="gray"
                        size="mini"
                    >（含医保 <abc-money :value="summary.sheBaoReceivableFee" :show-symbol="false"></abc-money>）
                    </abc-text>
                    </span>
                    <abc-text bold>
                        <abc-money :value="summary.receivableFee || 0" :show-symbol="false"></abc-money>
                    </abc-text>
                </p>
            </abc-flex>
            <template v-if="bargainSwitch">
                <abc-divider margin="none"></abc-divider>
                <abc-flex
                    justify="space-between"
                    align="center"
                    class="charge-detail-popover-bottom"
                    data-cy="charge-detail-dialog-bargaining"
                    @click="onClickAdjustment"
                >
                    <abc-text>整单议价</abc-text>
                    <abc-text theme="gray-light">
                        <abc-icon icon="n-right-line-medium"></abc-icon>
                    </abc-text>
                </abc-flex>
            </template>
        </abc-flex>
    </abc-popover>
</template>

<script type="text/ecmascript-6">
    import { sum } from 'utils/calculation';
    import AbcAdjustmentDialog from 'src/service/charge/components/dialog-charge/dialog-adjustment.js';
    import { mapGetters } from 'vuex';

    export default {
        name: 'ChargeDetailPopover',
        props: {
            value: Boolean,
            postData: {
                type: Object,
                default() {
                    return {};
                },
            },
            summary: {
                type: Object,
                default() {
                    return {};
                },
            },
            loading: Boolean,
        },
        computed: {
            ...mapGetters([
                'chargeConfig',
                'isCanSeeGoodsCostPriceInCashier',
            ]),
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustPrice',
            ]),
            showPopover: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            bargainSwitch() {
                return !!this.chargeConfig.bargainSwitch && this.employeeCanAdjustPrice && !!this.postData.canAdjustment;
            },
            medicalBill() {
                return this.postData.medicalBill;
            },
            isOpenSocial() {
                return this.$abcSocialSecurity.isOpenSocial;
            },
            adjustmentFee() {
                return sum(this.summary.adjustmentFee || 0, this.summary.unitAdjustmentFee || 0);
            },
            discountFee() {
                let sign = '';
                let priceStr = '';
                if (this.summary.discountFee === 0) {
                    return 0;
                }
                if (this.summary.discountFee < 0) {
                    sign = '-';
                }
                priceStr = Math.abs(this.summary.discountFee).toFixed(2);
                return `${sign}${priceStr}`;
            },
        },
        watch: {
            showPopover(val) {
                if (!val) {
                    this.closeAdjustmentDialog();
                }
            },
        },
        methods: {
            outside(e) {
                const isTarget = e.path.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return (
                        item.className.includes('reference-price-info') ||
                        item.className.includes('adjustment-popover-wrapper')
                    );
                });
                if (!isTarget && this.showPopover) {
                    this.showPopover = false;
                }
            },

            onClickAdjustment() {
                if (this._adjustmentDialog) return;
                const popoverEl = this.$refs['charge-detail-popover'].$el;
                const {
                    top,
                    bottom,
                    right,
                } = popoverEl.getBoundingClientRect();
                const popoverStyles = {
                    position: 'fixed',
                    zIndex: 1993,
                    width: '280px',
                    left: `${right + 16 + 4}px`,
                };
                const popoverHeight = bottom - top + 32;
                const profitHeight = this.isCanSeeGoodsCostPriceInCashier ? 24 : 0;
                if (popoverHeight >= 219) {
                    popoverStyles.top = `${bottom + 16 - 219 - profitHeight}px`;
                } else {
                    popoverStyles.top = `${top - 16 - profitHeight}px`;
                }

                this._adjustmentDialog = new AbcAdjustmentDialog({
                    needPayFee: this.summary.receivableFee,
                    originTotalFee: this.summary.afterRoundingDiscountedTotalFee,
                    isShowProfit: this.isCanSeeGoodsCostPriceInCashier,
                    totalCostPrice: this.summary.totalCostPrice,
                    popoverStyles,
                    canAdjustment: this.postData.canAdjustment,
                    canAdjustmentFee: this.postData.canAdjustmentFee,
                    draftAdjustmentFee: this.summary.draftAdjustmentFee,
                    onConfirm: (val) => {
                        this.$emit('adjustmentTotalFee', val);
                        this.showPopover = false;
                    },
                    onClose: (e) => {
                        const isTarget = e && e.path.some((item) => {
                            if (!item.className) return false;
                            if (typeof item.className !== 'string') return false;
                            return item.className.includes('charge-detail-popover');
                        });
                        if (!isTarget) {
                            this.closeAdjustmentDialog();
                        }
                    },
                });
                this._adjustmentDialog.generateDialogAsync({
                    parent: this,
                });
            },

            closeAdjustmentDialog() {
                if (this._adjustmentDialog) {
                    this._adjustmentDialog.destroyDialog();
                    this._adjustmentDialog = null;
                }
            },
        },
    };
</script>

<style lang="scss">
    @import "styles/abc-common.scss";

    .charge-detail-popover {
        padding: var(--abc-paddingTB-xl);

        .charge-detail-popover__content {
            &__title {
                margin: 0;
                font-size: 16px;
                font-weight: bold;
                line-height: 24px;
            }

            &__section {
                p {
                    display: inline-flex;
                    align-items: center;
                    justify-content: space-between;
                    line-height: 22px;

                    .label {
                        color: var(--abc-color-T2);
                    }
                }
            }
        }

        &-bottom {
            padding: 0 10px;
            margin: -4px -10px -10px;
            line-height: 32px;
            cursor: pointer;
            border-radius: var(--abc-border-radius-mini);

            &:hover {
                background-color: var(--abc-color-cp-grey4);
            }
        }
    }
</style>
