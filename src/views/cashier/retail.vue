<template>
    <div v-abc-loading:page="loading" data-cy="charge-main-center" class="main-content-wrapper">
        <abc-container-center-top-head class="cashier-center-top-head">
            <h2>{{ chargeTitle }}</h2>
            <abc-text
                v-if="recordCount"
                tag="div"
                size="mini"
                theme="success-light"
                style="margin-left: 16px;"
            >
                {{ recordCount }}
            </abc-text>

            <div class="buttons-wrapper">
                <div class="amount">
                    <abc-money
                        style="font-weight: bold; line-height: 28px;"
                        :value="topHeaderFee"
                        :symbol-icon-size="16"
                        :symbol-style="{
                            marginRight: '3px',
                            position: 'relative',
                            top: '1px',
                        }"
                    ></abc-money>
                </div>

                <template v-if="!noChargeForms">
                    <abc-check-access>
                        <abc-button
                            title="F9"
                            :loading="inputCalcLoading || buttonLoading"
                            style="width: 64px;"
                            data-cy="charge-retail-confirm-button"
                            @click="submit('postDataForm')"
                        >
                            收费
                        </abc-button>
                    </abc-check-access>

                    <abc-tooltip v-if="!isDisabledCollectTraceCode" content="本单项目均无需采集追溯码" :disabled="needTraceCodeFormItems.length !== 0">
                        <div style="margin: 0 4px;">
                            <abc-button
                                variant="ghost"
                                :disabled="needTraceCodeFormItems.length === 0"
                                :loading="inputCalcLoading || buttonLoading"
                                data-cy="charge-retail-trace-code-button"
                                @click="handleOpenTraceCodeDialog"
                            >
                                追溯码
                            </abc-button>
                        </div>
                    </abc-tooltip>
                </template>

                <abc-tooltip v-else content="没有收费项目">
                    <div style="margin-right: 4px;">
                        <abc-button
                            style="width: 64px;"
                            data-cy="charge-retail-confirm-button"
                            disabled
                        >
                            收费
                        </abc-button>
                    </div>
                </abc-tooltip>
                <abc-check-access>
                    <abc-button
                        :disabled="noChargeForms && !hasGlassesPrescription"
                        :loading="buttonLoading"
                        variant="ghost"
                        style="width: 64px;"
                        data-cy="charge-retail-hang-up-button"
                        @click="hangUpOrder"
                    >
                        挂单
                    </abc-button>
                </abc-check-access>

                <abc-button
                    variant="ghost"
                    style="width: 64px;"
                    data-cy="charge-retail-delete-button"
                    @click="cancelUpdateDialog"
                >
                    删除
                </abc-button>

                <abc-check-access>
                    <abc-button
                        v-if="canSendOrder"
                        variant="ghost"
                        :loading="buttonSaveLoading"
                        data-cy="charge-retail-send-pay-button"
                        @click="onBeforePushClick"
                    >
                        推送支付
                    </abc-button>
                </abc-check-access>

                <abc-check-access v-if="!disabledPrintAdviceTag">
                    <abc-button
                        variant="ghost"
                        style="margin-left: 4px;"
                        :disabled="printAdviceTagLoading"
                        @click="showSelectPrint = true"
                    >
                        {{ printAdviceTagLoading ? '正在打印' : '打印标签' }}
                    </abc-button>
                </abc-check-access>

                <abc-button
                    slot="reference"
                    variant="ghost"
                    icon="s-b-settings-line"
                    style="margin-left: 4px;"
                    data-cy="charge-retail-settings-button"
                    @click="handleClickSettings"
                >
                </abc-button>
            </div>
        </abc-container-center-top-head>

        <abc-container-center-main-content class="cashier-wrapper cashier-main-content">
            <abc-form ref="postDataForm" class="direct-charge-form-wrapper">
                <retail-post-data-form
                    :key="$route.params.id"
                    ref="cashier-post-form"
                    :calc-switch="calcSwitch"
                    :post-data="postData"
                    :loading="loading"
                    :charge-sheet-type="chargeSheetType"
                    :input-calc-loading.sync="inputCalcLoading"
                    :close-switch="closeSwitch"
                    :disabled-keyboard="startCharging"
                    :charge-sheet-summary="chargeSheetSummary"
                    :disabled-scan-barcode="disabledScanBarcode || startCharging"
                    :is-disabled-collect-trace-code="isDisabledCollectTraceCode"
                    @copy="copyPRHandler"
                    @submit="submit('postDataForm')"
                    @change-visit-source="changeVisitSource"
                ></retail-post-data-form>
            </abc-form>
        </abc-container-center-main-content>

        <select-print-dialog
            v-if="showSelectPrint"
            v-model="showSelectPrint"
            :d="postData.chargeForms"
            type="direct"
            @confirm="selectPrintConfirm"
        ></select-print-dialog>

        <send-order-dialog
            v-if="showSendOrderDialog"
            v-model="showSendOrderDialog"
            :charge-sheet-summary="chargeSheetSummary"
            :status="-1"
            :loading="inputCalcLoading"
            :post-data="postData"
            @sendSuccess="sendSuccess"
        ></send-order-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        mapGetters, mapActions,
    } from 'vuex';

    // 自定义组件
    import RetailPostDataForm from './retail-post-data-form/retail-post-data-form';
    import SubmitAirPharmacyOrder from '../air-pharmacy/submit-order-dialog';
    import SelectPrintDialog from './select-print-dialog.vue';
    import SendOrderDialog from './components/send-order-dialog/send-order-dialog';
    import ChargeNoticeDialog from './components/charge-notice-dialog';
    import CashierSettingsDialog from './components/cashier-settings-dialog';

    // API
    import ChargeAPI from 'api/charge';
    import CrmAPI from 'api/crm';
    import { encryptMobile } from 'utils/crm';
    import { DEFAULT_CERT_TYPE } from 'views/crm/constants';
    // utils
    import { isEqual } from 'utils/lodash';
    import clone from 'utils/clone';
    import localStorage from 'utils/localStorage-handler';
    import inputSelect from 'views/common/input-select';
    import BarcodeHandler from '../inventory/mixins/barcode-handler';
    import { createGUID } from 'utils/index';
    import {
        on, off,
    } from 'utils/dom';
    import {
        GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import {
        SourceFormTypeEnum,
        ChargeSheetTypeEnum,
        CreateCashierPostData,
    } from '@/service/charge/constants.js';
    import {
        getTraceCodeChargeItems, getWarnChargeItemGroup,
    } from './utils/index';
    import diagnosisSocialCodeHandle from 'src/views/common/diagnosis-social-code-handle';
    import PreSubmitHandle from './mixins/pre-submit-handle';
    import successSubmitHandle from './mixins/success-submit-handle';
    import AbcChargeDialog from '@/service/charge/components/dialog-charge';
    import { ChargeStatusEnum } from '@/service/charge/constants.js';
    import { getAbcPrintOptions } from '@/printer/print-handler.js';
    import AbcPrinter from '@/printer/index.js';
    import AbcAccess from '@/access/utils.js';
    import i18n from '@/i18n';
    import CollectionTraceCodeDialog from '@/service/trace-code/dialog-collection-trace-code';
    import { CollectionTraceCodeCheck } from 'views/settings/trace-code/constants';
    import { resetStockByPharmacyNo } from 'views/layout/prescription/utils';
    import TraceCode, {
        SceneTypeEnum, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import { isAllowAddByAntimicrobialDrugManagement } from 'views/outpatient/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import DialogSelfPayPatient from 'views/cashier/components/dialog-self-pay-patient';
    import Printer from 'views/print';
    import Logger from 'utils/logger';
    import SimpleEventBus from 'views/cashier/meanwhile-print-manager/simple-event-bus';
    const compareKey = [
        'patient',
        'chargeForms',
        'memberId',
        'sellerId',
        'promotions',
        'deliveryInfo',
    ];
    const comparePatientKey = [
        'age',
        'id',
        'mobile',
        'name',
        'sex',
    ];
    export default {
        components: {
            RetailPostDataForm,
            SelectPrintDialog,
            SendOrderDialog,
        },
        mixins: [
            inputSelect,
            BarcodeHandler,
            diagnosisSocialCodeHandle,
            PreSubmitHandle,
            successSubmitHandle,
        ],
        data() {
            return {
                ChargeSheetTypeEnum,
                loading: false,
                inputCalcLoading: false,
                isUpdate: false,
                closeSwitch: false,
                calcSwitch: false,
                status: 0,

                postData: CreateCashierPostData(),

                chargeSheetSummary: {
                    adjustmentFee: '',
                    draftAdjustmentFee: '',
                    discountFee: 0,
                    needPayFee: '',
                    receivableFee: '',
                    receivedFee: '',
                    totalFee: '',
                    oddFee: '',
                },

                itemCache: '',
                showSuggestions: false,

                buttonLoading: false,
                buttonSaveLoading: false,
                startCharging: false,

                showSelectPrint: false,
                printAdviceTagLoading: false,
                isFirstPrintAdviceTag: true,

                shortageMedicines: [],

                printOpt: {
                    printSelect: '',
                    finishSelect: [],
                },

                newChargeSheetId: undefined,

                showSendOrderDialog: false,
                visibleSocialInfo: false,
                disabledScanBarcode: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'clinicConfig',
                'goodsConfig',
                'cashier',
                'draftCashierNews',
                'userInfo',
                'isOpenMp',
                'chargeConfig',
                'weChatPayConfig',
                'printBillConfig',
                'disableNoStockGoods',
                'needCheckStock',
                'pharmacyList',
                'traceCodeConfig',
                'isCanSeeGoodsCostPriceInCashier',
                'isCanSeePatientMobileInCrm',
            ]),
            ...mapGetters('airPharmacy', ['systemConfig']),
            ...mapGetters('crm', ['crmAccessVisibility']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig', 'featureSupportFilterEyeGlasses']),
            // 追溯码提醒
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            //是否启用采集强控制模式
            hasEnableCollCheckStrictMode() {
                return TraceCode.hasEnableCollCheckStrictMode;
            },
            isDisabledCollectTraceCode() {
                //没有采集提醒 或 开启发药强采且采集提醒为发药 就 禁用追溯码采集
                return !this.traceCodeCollectionCheck || (this.hasEnableCollCheckStrictMode && this.traceCodeCollectionCheck === CollectionTraceCodeCheck.dispensing);
            },
            //是否启用追溯码暂存功能 不校验追溯码
            isEnableTraceTempSave() {
                return this.hasEnableCollCheckStrictMode || !!TraceCode.requireCollectTraceCode;
            },
            needTraceCodeFormItems() {
                return getTraceCodeChargeItems(this.postData.chargeForms);
            },
            selectedPatient() {
                return this.cashier.selectedPatient;
            },
            isGuangZhou() {
                return this.$abcSocialSecurity.isOpenSocial && this.$abcSocialSecurity.config.isGuangdongGuangzhou;
            },
            recordCount() {
                if (!this.isGuangZhou) return '';
                const strArr = [];
                const { shebaoCardInfo } = this.selectedPatient || {};
                const { extend } = shebaoCardInfo || {};
                const { annualFundPay } = extend || {};
                if (Number(annualFundPay ?? 0)) {
                    const feeStr = `本年统筹累计：${i18n.t('currencySymbol')}${annualFundPay}`;
                    strArr.push(feeStr);
                }
                return strArr.join('，');
            },

            /**
             * @desc 能否 看到推送按钮
             * <AUTHOR>
             * @date 2020/02/19 21:46:12
             */
            canSendOrder() {
                return this.weChatPayConfig.weChatPaySwitch === 2 &&
                    this.weChatPayConfig.jsapiPayStatus === 2 &&
                    this.isOpenMp;
            },

            chargeTitle() {
                const { chargeTitleUnify } = this.viewDistributeConfig.Cashier;
                return chargeTitleUnify ? '收费单' : '零售收费';
            },

            sellerSendOrderInfoSwitch() {
                return this.chargeConfig && this.chargeConfig.sellerSendOrderInfoSwitch;
            },

            /**
             * @desc 收费头部展示的费用，需要对其做 收费设置 的处理
             * <AUTHOR>
             * @date 2019/07/04 14:44:04
             */
            topHeaderFee() {
                const needPayFee = this.chargeSheetSummary.needPayFee || 0;
                return needPayFee < 0 ? 0 : needPayFee;
            },
            noChargeForms() {
                return this.postData.chargeForms.filter((form) => {
                    return form.chargeFormItems.length;
                }).length === 0;
            },
            // 配镜处方没有收费项-不能收费、可以挂单
            hasGlassesPrescription() {
                return this.postData.chargeForms.some((form) => form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_GLASSES);
            },

            isRequired() {
                return !!(this.postData.patient.name ||
                    this.postData.patient.age.year ||
                    this.postData.patient.age.month ||
                    this.postData.patient.mobile);
            },
            requiredSeller() {
                return this.clinicConfig && this.clinicConfig.chargeRequiredSeller;
            },
            disabledPrintAdviceTag() {
                return !(
                    this.postData.chargeForms.length > 0 &&
                    this.postData.chargeForms.filter((item) => {
                        if (item.itemType === 1 || item.itemType === 5) {
                            const count = item.count * (item.doseCount || 0);
                            return count > 0;
                        }
                        return false;

                    }).length
                );
            },

            /**
             * @desc 诊所是否开启了自助支付（微信支付开通，jsapi开通，微诊所开通，设置开通）
             * <AUTHOR> Yang
             * @date 2020-09-03 10:01:30
             */
            autoSendOrderInfoSwitch() {
                return this.weChatPayConfig.weChatPaySwitch === 2 &&
                    this.weChatPayConfig.jsapiPayStatus === 2 &&
                    this.isOpenMp &&
                    this.chargeConfig.autoSendOrderInfoSwitch;
            },
            // 是否为整单收费模式
            isWholeBillCharge() {
                return !!this.chargeConfig.wholeSheetOperateEnable;
            },
            //同时发药按钮显示
            isDaijianCenterDispensing() {
                const { chargeForms = [] } = this.postData || {};
                return !!chargeForms.find((form) => {
                    return form.chargeFormItems?.find((item) => {
                        return this.pharmacyList.find((pharmacyItem) => {
                            return item.pharmacyNo === pharmacyItem.no && pharmacyItem.externalPharmacyConfig?.chargeDisableAutoDispense;
                        });
                    });

                });
            },
            chargeSheetType() {
                const { type } = this.postData;
                // 兼容老草稿
                return type || ChargeSheetTypeEnum.DIRECT_SALE;
            },
        },
        watch: {
            '$route': function() {
                if (this.newChargeSheetId) {
                    this.$store.dispatch('ClearDraft', {
                        key: 'cashier', draftId: this.draftId,
                    });
                }
                this.initRetail();
                this.initWeChatPayConfig();
                this.initDispensingConfig();
            },
            postData: {
                handler (val) {
                    const isEquals1 = compareKey.filter((key) => {
                        return !isEqual(this._postDataCache[ key ], val[ key ]);
                    },
                    );
                    const isEquals2 = comparePatientKey.filter((key) => {
                        return !isEqual(this._postDataCache.patient[ key ], val.patient[ key ]);
                    },
                    );
                    const isEquals = isEquals1.concat(isEquals2);
                    this.isUpdate = !!isEquals.length;
                    this.createDraft();
                },
                deep: true,
            },
            'cashier.selectedPatient': {
                handler (patientInfo) {
                    if (patientInfo) {
                        const {
                            isAttention, wxBindStatus,
                        } = patientInfo;
                        this.postData.patient.isAttention = isAttention;
                        this.postData.patient.wxBindStatus = wxBindStatus;
                    }
                },
                deep: true,
            },
        },
        async created() {
            this.$store.dispatch('initChargeConfig');
            this._key = `${this.currentClinic.clinicId}_${this.currentClinic.userId}`;
            this._postDataCache = CreateCashierPostData();
            this.draftId = this.$route.params.id;
            this.initRetail();
            this.initAirPharmacySystemConfig();

            this.$store.dispatch('fetchEmployeeListByPractice');
            this.$store.dispatch('fetchAntimicrobialDrugManagementData');
        },
        mounted() {
            // 新建焦点落在姓名上
            $('.patient-wrapper').find('input').eq(0).focus();
            on(document, 'keydown', this.keydownF9Handle);
        },
        beforeDestroy() {
            off(document, 'keydown', this.keydownF9Handle);
            this.timeOutId && clearTimeout(this.timeOutId);
        },
        methods: {
            ...mapActions([
                'refreshCashierQuickList',
                'initWeChatPayConfig',
                'initDispensingConfig',
            ]),
            ...mapActions('airPharmacy', ['initAirPharmacySystemConfig']),

            async handleOpenTraceCodeDialog() {
                if (this.needTraceCodeFormItems.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '本单项目均无需采集追溯码',
                    });
                    return;
                }
                this.buttonLoading = true;
                this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                    formItems: this.needTraceCodeFormItems,
                    requiredTraceCode: this.traceCodeCollectionCheck === CollectionTraceCodeCheck.charge,
                    sceneType: SceneTypeEnum.CHARGE,
                    isEnableTraceTempSave: this.isEnableTraceTempSave,
                    onClose: () => {
                        this._collectionTraceCodeDialog = null;
                        this.disabledScanBarcode = false;
                    },
                    onConfirm: () => {
                        this.createDraft();
                    },
                });
                this.disabledScanBarcode = true;
                await this._collectionTraceCodeDialog.generateDialogAsync();
                this.buttonLoading = false;
            },

            handleClickSettings() {
                new CashierSettingsDialog({}).generateDialog({
                    parent: this,
                });
            },

            /**
             * desc [判断是否共享会员]
             */
            async exitShareMember(patientId) {
                if (patientId) {
                    try {
                        const { data } = await CrmAPI.selectShareMemberInfo(patientId);
                        if (data.memberInfo) {
                            return {
                                mobile: data.memberInfo.memberCardId,
                                memberId: data.memberInfo.patientId,
                                memberTypeName: data.memberInfo.memberTypeInfo && data.memberInfo.memberTypeInfo.memberTypeName,
                            };
                        }
                    } catch (error) {
                        console.log('exitShareMember error', error);
                    }
                }
                return null;
            },
            /**
             * @desc 初始化直接收费
             * <AUTHOR>
             * @date 2018/07/25 16:38:01
             */
            async initRetail() {
                this.draftId = +this.$route.params.id;
                this.loading = true;
                (this.draftCashierNews || []).forEach((item) => {
                    if (+this.draftId === +item.draftId) {
                        // 加工费结构调整，兼容一下老的零售开单草稿
                        if (item.processInfos && item.processInfos.length && item.chargeForms) {
                            const decoctionForm = item.chargeForms.find((form) => form.sourceFormType === SourceFormTypeEnum.DECOCTION);
                            if (decoctionForm) {
                                decoctionForm.processInfo = item.processInfos[0];
                            }
                        }
                        this.postData = clone(item);
                        // deliveryInfo 结构是新增 兼容下之前的草稿
                        if (!this.postData.deliveryInfo) {
                            this.postData.deliveryInfo = {};
                        }
                    }
                });
                this.postData.chargeForms.forEach((form) => {
                    form.keyId = form.keyId || createGUID();
                    form.chargeFormItems.forEach((item) => {
                        item.keyId = item.keyId || createGUID();
                    });
                });

                this.postData.dispensing = localStorage.getObj('cashier_delivery_medicine', this._key, true) || false;
                this._postDataCache.dispensing = this.postData.dispensing;

                this.calcSwitch = true;
                this.loading = false;

                if (this.postData.retailType === 2) {
                    this.$store.dispatch('initDoctors');
                }
                // 点击直接收费聚焦在患者姓名输入框
                this.$nextTick(() => {
                    this.calcSwitch = false;
                    const nameInput = $('.direct-charge-form-wrapper .patient-section-wrapper .abc-autocomplete-wrapper > input').eq(0);
                    !nameInput.val() && nameInput.focus();
                });
            },

            isStockGoods(goods) {
                const type = goods.productType || goods.type;
                return type === GoodsTypeEnum.MEDICINE ||
                    type === GoodsTypeEnum.MATERIAL ||
                    type === GoodsTypeEnum.GOODS;
            },

            /**
             * 提交前预检测抗菌用药是否符合规范
             * @return {boolean}
             */
            validateAntimicrobial() {
                const medicineChargeForms = this.postData.chargeForms.filter((form) => form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL);
                const composeChargeForms = this.postData.chargeForms.filter((form) => form.sourceFormType === SourceFormTypeEnum.COMPOSE);
                const {
                    doctorId, sellerId,
                } = this.postData;
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;

                const filterMedicineFormItems = [], filterComposeFormItems = [];
                if (medicineChargeForms.length) {
                    medicineChargeForms.forEach((form) => {
                        const chargeFormItems = form.chargeFormItems || [];
                        chargeFormItems.forEach((item) => {
                            const isSuccess = isAllowAddByAntimicrobialDrugManagement(item, doctorId || sellerId, antimicrobialDrugManagementData, employeeListByPractice);
                            if (!isSuccess) {
                                filterMedicineFormItems.push(item);
                            }
                        });
                    });
                }
                if (composeChargeForms.length) {
                    composeChargeForms.forEach((form) => {
                        const chargeFormItems = form.chargeFormItems || [];
                        chargeFormItems.forEach((item) => {
                            const { composeChildren } = item;
                            (composeChildren || []).forEach((children) => {
                                const isSuccess = isAllowAddByAntimicrobialDrugManagement(children, doctorId || sellerId, antimicrobialDrugManagementData, employeeListByPractice);
                                if (!isSuccess) {
                                    filterComposeFormItems.push(children);
                                }
                            });
                        });
                    });
                }
                const filterFormItems = filterMedicineFormItems.concat(filterComposeFormItems);
                if (filterFormItems.length) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: filterFormItems }).generateDialogAsync({ parent: this });
                    }, 100);
                    return false;
                }
                return true;
            },

            /**
             * @desc 点击收费按钮后先进行库存验证
             * <AUTHOR>
             * @date 2018/07/25 16:39:28
             */
            async submit(formName) {
                this.preSubmitHandle();

                await this.validateSocialCode(this.postData);

                if (!this.validateAntimicrobial()) return;

                if (this.validateCompany().flag) return;

                const airPharmacyForm = this.postData.chargeForms.find((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY;
                });
                if (airPharmacyForm) {
                    const airPharmacyFormItems = airPharmacyForm.chargeFormItems.filter((item) => {
                        return item.productType !== GoodsTypeEnum.EXPRESS_DELIVERY &&
                            item.productType !== GoodsTypeEnum.DECOCTION &&
                            item.productType !== GoodsTypeEnum.INGREDIENT;
                    }) || [];
                    if (airPharmacyFormItems.length === 0) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '空中药房没有添加药品',
                        });
                        return false;
                    }
                }
                this.validatePostDataForm(formName);
            },

            async asyncCollectionTraceCodeDialog(isSelectedSocialPay = false) {
                // eslint-disable-next-line no-async-promise-executor
                return new Promise(async (resolve, reject) => {
                    this.buttonLoading = true;
                    this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                        formItems: this.needTraceCodeFormItems,
                        requiredTraceCode: this.traceCodeCollectionCheck === CollectionTraceCodeCheck.charge,
                        confirmInnerSet: false,
                        sceneType: SceneTypeEnum.CHARGE,
                        isSelectedSocialPay: !!isSelectedSocialPay,
                        isEnableTraceTempSave: this.isEnableTraceTempSave,
                        onConfirm: (flatFormItems,action) => {
                            TraceCode.setChargeFormsTraceCodeList(flatFormItems, this.postData.chargeForms);
                            if (action === 'tempSave') {
                                reject();
                            } else {
                                resolve();
                            }
                        },
                        onClose: () => {
                            this.disabledScanBarcode = false;
                            this._collectionTraceCodeDialog = null;
                            reject();
                        },
                    });
                    this.disabledScanBarcode = true;
                    await this._collectionTraceCodeDialog.generateDialogAsync();
                    this.buttonLoading = false;
                });
            },
            async validateTraceCodeFn(isSelectedSocialPay = false) {
                try {
                    const res = await TraceCode.validate({
                        scene: TraceCodeScenesEnum.PHARMACY,
                        sceneType: SceneTypeEnum.CHARGE,
                        dataList: TraceCode.getFlatItems(this.needTraceCodeFormItems),
                        needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckPharmacy(),
                    });
                    console.log(res);
                    if (!res.flag) {
                        // 一个追溯码都没，需要提醒
                        await this.asyncCollectionTraceCodeDialog(isSelectedSocialPay);
                    }
                    return true;
                } catch (e) {
                    return false;
                }
            },
            /**
             * @desc 收费
             * <AUTHOR>
             * @date 2022/10/10 14:31:35
             */
            validatePostDataForm(formName) {
                if (!this.$refs[formName]) return;
                this.$refs[ formName ].validate(async (valid) => {
                    if (valid) {
                        const {
                            noCheckedItems = [],
                            noGoodsInfoItems = [],
                            disabledItems = [],
                            shortageItems = [],
                        } = getWarnChargeItemGroup(this.postData.chargeForms);

                        if (this.isWholeBillCharge && (disabledItems.length > 0 || shortageItems.length > 0)) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: '收费单内包含停售/库存不足商品，请调整后再收费',
                            });
                            return false;
                        }

                        if (noGoodsInfoItems.length > 0 || disabledItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品未创建或者已被停用：',
                                content: [`<span>${noGoodsInfoItems.concat(disabledItems).map((item) => {
                                    return item.name || item.medicineCadn;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }
                        if (this.disableNoStockGoods && shortageItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品库存不足：',
                                content: [`<span>${shortageItems.map((item) => {
                                    let str = '';
                                    if (item.parentName) {
                                        str += `【${item.parentName}】`;
                                    }
                                    str += item.name || item.medicineCadn;
                                    return str;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }

                        this._chargeDialogInstance = new AbcChargeDialog({
                            pcRouterVm: this.$router,
                            pcStoreVm: this.$store,
                            needLockInventory: true,
                            chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                            postData: this.postData, // 提交的收费单数据
                            isDaijianCenterDispensing: this.isDaijianCenterDispensing,
                            isShowProfit: this.isCanSeeGoodsCostPriceInCashier,
                            chargeStatus: ChargeStatusEnum.RETAIL,
                            onPartChargeSuccess: this.partChargeSuccess,
                            onChargeSuccess: this.chargeSuccess,
                            onChargeError: this.chargeError,
                            onClose: this.closeChargeDialog,
                            validateTraceCodeFn: this.validateTraceCodeFn,
                        });

                        // 需要提示的商品：没有勾选的，库存不足的（包含套餐中的）
                        if (noCheckedItems.length > 0 || shortageItems.length > 0) {
                            let arr = noCheckedItems;
                            if (this.needCheckStock) {
                                arr = arr.concat(shortageItems);
                            }
                            // 列表为空，重置状态
                            if (arr.length <= 0) {
                                this.startCharging = true;
                                this._chargeDialogInstance.generateDialog({
                                    parent: this,
                                });
                                return false;
                            }
                            new ChargeNoticeDialog({
                                data: arr,
                                confirm: () => {
                                    this.startCharging = true;
                                    this._chargeDialogInstance.generateDialog({
                                        parent: this,
                                    });
                                },
                            }).generateDialogAsync({
                                parent: this,
                            });
                        } else {
                            this.startCharging = true;
                            this._chargeDialogInstance.generateDialog({
                                parent: this,
                            });
                        }
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                });
            },

            validateCompany() {
                let flag = false;
                const deliveryForm = this.postData.chargeForms.find((item) => {
                    return item.sourceFormType === SourceFormTypeEnum.EXPRESS_DELIVERY;
                });
                if (deliveryForm) {
                    const {
                        deliveryPayType,
                        deliveryCompany,
                    } = this.postData.deliveryInfo;
                    const { chargeFormItems } = deliveryForm;
                    const checked = chargeFormItems && chargeFormItems[0] && deliveryForm.chargeFormItems[0].checked;
                    if (checked && deliveryPayType === 1 && (!deliveryCompany || !deliveryCompany.id)) {
                        flag = true;
                    }
                }
                const airPharmacyForms = this.postData.chargeForms.filter((form) => {
                    return form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY;
                });
                airPharmacyForms.forEach((form) => {
                    const checkedMedicines = form.chargeFormItems.filter((item) => {
                        // 有选中的药品才判断是否选择地址
                        return item.productType !== GoodsTypeEnum.EXPRESS_DELIVERY &&
                            item.productType !== GoodsTypeEnum.DECOCTION &&
                            item.productType !== GoodsTypeEnum.INGREDIENT;
                    }).filter((item) => {
                        return item.checked;
                    });
                    const { deliveryInfo } = form;
                    const { deliveryCompany } = deliveryInfo || {};
                    if (checkedMedicines.length && (!deliveryCompany || !deliveryCompany.id)) {
                        flag = true;
                    }
                });

                return {
                    flag,
                    tips: '请选择快递公司',
                };
            },

            /**
             * @desc 收费成功回调
             * <AUTHOR> Yang
             * @date 2020-08-03 17:48:29
             */
            async chargeSuccess(data) {
                this.initChargeId(data.id);
                this.redirectRouter(data);
                this.setAirOrderRemark(this.postData);

                // 收费完成后同时打印
                const uuid = createGUID();
                const { cache } = Printer;
                const printCache = cache.get();
                const {
                    isChargeMeanwhilePrint, cashier,
                } = printCache || {};
                const cacheCashier = clone(cashier);
                Logger.report({
                    scene: 'new_cashier_meanwhile_print',
                    data: {
                        uuid,
                        info: '收费同时打印_收费完成',
                        chargeSheetId: data.id,
                        cashier: cacheCashier,
                    },
                });
                if (isChargeMeanwhilePrint) {
                    SimpleEventBus.getInstance().emit('cashier-meanwhile-print', {
                        uuid,
                        chargeSheetId: data.id,
                        cashier: cacheCashier,
                    });
                }

                this._timer = setTimeout(() => {
                    if (data && data.isAirPharmacyCanPay) {
                        SubmitAirPharmacyOrder({
                            orderIds: data.airPharmacyOrderIds,
                            systemConfig: this.systemConfig,
                        });
                    } else if (data?.canImprovePatientFlag && this.$abcSocialSecurity.isSupportSelfPayInputCardNoRemid) {
                        new DialogSelfPayPatient({
                            chargeSheetId: data.id,
                            postData: this.postData,
                            onConfirm: () => {
                                this.$router.push({
                                    query: {
                                        ...this.$route.query,
                                        timeStamp: Date.now(),
                                    },
                                });
                            },
                        }).generateDialogAsync({ parent: this });
                    }
                }, 500);
            },

            async partChargeSuccess(data) {
                this.initChargeId(data.id);
                this.setAirOrderRemark(this.postData);
                await this.selectNewQuickListItem(data);
            },

            /**
             * @desc 零售开单是本地草稿，收费成功需要跳转到新的单子路由去
             * <AUTHOR>
             * @date 2021-09-26 17:32:51
             */
            redirectRouter(data) {
                this.selectNewQuickListItem(data);
                const { routeBasePath } = this.viewDistributeConfig.Cashier;
                this.$router.push(`${routeBasePath}cashier/${data.id}`);
            },

            async selectNewQuickListItem(data) {
                // 改变状态成功后 获取quicklist 当天最新状态
                await this.refreshCashierQuickList();
                // 默认选中刚添加的
                await this.$store.dispatch('setSelectedItem', {
                    type: 'cashier',
                    selectedItem: this.cashier.quickList.find((item) => { return item.id === data.id; }),
                });
            },
            async addPatientFromOtherClinic(id) {
                try {
                    await CrmAPI.handlePatientExist(id);
                    this.$Toast({
                        message: '添加患者成功',
                        type: 'success',
                    });
                } catch (e) {
                    console.log(e);
                }
            },
            handlePatientExist(code = 13503, detail = []) {
                const {
                    name = '',
                    mobile = '',
                    idCard = '',
                    idCardType = DEFAULT_CERT_TYPE,
                    id = '',
                } = detail?.[0] || {};
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    confirmText: '添加患者',
                    showCancel: false,
                    content: code === 13502 ? `证件号[${idCardType}]${idCard}已经被连锁中存在的患者 ${name} 注册` : `同名患者 ${name} ${mobile ? `${this.isCanSeePatientMobileInCrm ? mobile : encryptMobile(mobile)}` : ''} ${idCard ? `[${idCardType}]${idCard}` : ''}已在连锁中存在`,
                    onConfirm: async () => {
                        await this.addPatientFromOtherClinic(id);
                    },
                });
            },
            /**
             * @desc 收费失败
             * <AUTHOR> Yang
             * @date 2020-08-03 17:48:07
             */
            chargeError(err) {
                const {
                    code, detail,
                } = err || {};
                // TODO CRM
                if ([13502, 13503].includes(code) && this.crmAccessVisibility) {
                    this.handlePatientExist(code, detail);
                } else if (code === 17090) {
                    // 空中药房 快递费发生改变
                    this.postData.chargeForms.forEach((form) => {
                        if (form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY) {
                            form.deliveryInfo.deliveryCompany = {
                                id: '',
                                name: '',
                            };
                            const deliveryItem = form.chargeFormItems.find((item) => {
                                return item.productType === GoodsTypeEnum.EXPRESS_DELIVERY;
                            });
                            if (deliveryItem) {
                                deliveryItem.productInfo = {
                                    ...form.deliveryInfo,
                                };
                            }
                        }
                    });
                }
            },

            async closeChargeDialog() {
                this.startCharging = false;
                if (this.newChargeSheetId) {
                    this.redirectRouter({ id: this.newChargeSheetId });
                } else {
                    this.calcSwitch = true;
                    this.$nextTick(() => {
                        this.calcSwitch = false;
                    });
                }
                this.$nextTick(() => {
                    if (!this.featureSupportFilterEyeGlasses) {
                        $('.search-header .abc-input__inner').focus();
                    }
                });
                // 关闭收费弹窗需要清除固定
                this.postData.chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        item.isFixedData = 0;
                        item.composeChildren?.forEach((child) => {
                            child.isFixedData = 0;
                        });
                    });
                });
            },

            selectPrintConfirm(params) {
                const { selectedData } = params;
                const printOptions = getAbcPrintOptions('用药标签', {
                    forms: selectedData.w.concat(selectedData.c),
                    patient: this.postData.patient,
                });
                AbcPrinter.abcPrint(printOptions);
                this.showSelectPrint = false;
            },

            keydownF9Handle(event) {
                if (this._collectionTraceCodeDialog) return;
                if (!AbcAccess.check()) return;

                const KEY_F9 = 120;
                if (event.keyCode === KEY_F9) {
                    $(this.$el).find('.seller-selector input').blur();
                    event.cancelBubble = true;
                    event.returnValue = false;
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();

                    // 调起收费弹窗的时候需要让下拉框失焦
                    this.closeSwitch = true;
                    this.$nextTick(() => {
                        this.closeSwitch = false;
                        $('.abc-select-wrapper.is-show-popper input').click();
                    });
                    if (document.activeElement) {
                        document.activeElement.blur();
                    }
                    /**
                     * @desc 页面上会处理一些逻辑，延迟触发提交验证
                     * <AUTHOR>
                     * @date 2020/03/13 10:18:27
                     */
                    this.timeOutId = setTimeout(() => {
                        if (this._chargeDialogInstance) {
                            this._chargeDialogInstance.destroyDialog();
                            this._chargeDialogInstance = null;
                        } else {
                            if (!this.inputCalcLoading) {
                                this.submit('postDataForm');
                            }
                        }
                    }, 1);
                }
            },

            /**
             * @desc 删除开单收费
             * <AUTHOR>
             * @date 2019/10/28 10:26:07
             */
            cancelUpdateDialog() {
                if (this.isUpdate) {
                    // 点击取消后弹出框
                    this.$confirm({
                        type: 'warn',
                        title: '删除确认',
                        content: '删除后不能恢复。是否确定删除本收费单？',
                        onConfirm: () => {
                            this.cancelAdd();
                        },
                    });
                } else {
                    this.cancelAdd();
                }
            },

            async cancelAdd() {
                this.$Toast({
                    message: '删除成功',
                    type: 'success',
                });
                this.clearDraft();
                this._isDelete = true;
                this.$store.commit('update_cashier_quick_list_summary', {
                    normalCount: this.cashier.summary.normalCount - 1,
                });
                // 取消后 根据quicklist情况是否默认选中第一个
                this.selectFirst();
            },

            /**
             * @desc 开单收费创建成功后，清除草稿
             * <AUTHOR>
             * @date 2019/10/28 14:28:57
             */
            initChargeId(id) {
                this.newChargeSheetId = id;
                this.clearDraft();
            },

            createDraft() {
                // 收费单落地后不存草稿
                if (this.newChargeSheetId) return;
                this.$store.dispatch('SetDraft', {
                    key: 'cashier',
                    record: clone(Object.assign(this.postData, { draftId: this.draftId })),
                });
            },
            clearDraft() {
                this.$store.dispatch('ClearDraft', {
                    key: 'cashier', draftId: this.draftId,
                });
                Logger.report({
                    scene: 'retail_clear_draft',
                    data: {
                        info: '零售收费清除草稿',
                        draftId: this.draftId,
                        paramsId: +this.$route.params.id,
                    },
                });
            },

            /**
             * 提交前预检测抗菌用药是否符合规范
             * @return {boolean}
             */
            filterByAntimicrobial(postData) {
                const {
                    doctorId, sellerId,
                } = postData;
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;

                const filterMedicineFormItems = [], filterComposeFormItems = [];
                postData.chargeForms = (postData.chargeForms || []).filter((form) => {
                    // 中西成药
                    if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_WESTERN || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_INFUSION || form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL) {
                        form.chargeFormItems = (form.chargeFormItems || []).filter((item) => {
                            const isSuccess = isAllowAddByAntimicrobialDrugManagement(item, doctorId || sellerId, antimicrobialDrugManagementData, employeeListByPractice);
                            if (!isSuccess) {
                                filterMedicineFormItems.push(item);
                            }
                            return isSuccess;
                        });
                        return form.chargeFormItems.length;
                    }
                    if (form.sourceFormType === SourceFormTypeEnum.COMPOSE) {
                        // 套餐
                        form.chargeFormItems = (form.chargeFormItems || []).filter((item) => {
                            let flag = true;
                            (item.composeChildren || []).forEach((children) => {
                                const isSuccess = isAllowAddByAntimicrobialDrugManagement(children, doctorId || sellerId, antimicrobialDrugManagementData, employeeListByPractice);
                                if (!isSuccess) {
                                    filterComposeFormItems.push(children);
                                    flag = false;
                                }
                            });
                            return flag;
                        });
                        return form.chargeFormItems.length;
                    }

                    return true;
                });

                const filterFormItems = filterMedicineFormItems.concat(filterComposeFormItems);
                if (filterFormItems.length) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: filterFormItems }).generateDialogAsync({ parent: this });
                    }, 100);
                }

                return !!postData.chargeForms.length;
            },

            /**
             * @desc 接收 PatientMedicalRecord copy 复制处方
             * <AUTHOR>
             * @date 2018/03/23 18:13:59
             */
            async copyPRHandler(mr) {
                if (mr.patientOrderId) {
                    this.loading = true;
                    const { data } = await ChargeAPI.outpatientCopyToCharge(mr.patientOrderId);

                    if (!this.filterByAntimicrobial(data)) {
                        this.loading = false;
                        return false;
                    }

                    // 需要对药品基础信息/库存信息进行更新
                    // data.productInfos该详单里面药品的实时价格和库存
                    if (data.productInfos && data.productInfos.length) {
                        data.chargeForms.forEach((form) => {
                            form.chargeFormItems.forEach((item) => {
                                const newMedicine = data.productInfos.find((info) => {
                                    return info.id === item.productId;
                                });
                                if (item.productInfo && newMedicine) {
                                    item.realProductInfo = Object.assign({}, item.productInfo, newMedicine || {});
                                }
                            });
                        });
                    }

                    /**
                     * @desc 复制处方可能会存在多个空中药房，只取第一个
                     * <AUTHOR> Yang
                     * @date 2020-07-28 15:34:34
                     */
                    const localChargeForms = [];
                    const airPharmacyForms = [];
                    let hasCooperationPharmacy = false;
                    data.chargeForms.forEach((form) => {
                        form.keyId = form.keyId || createGUID();
                        form.chargeFormItems.forEach((item) => {
                            item.keyId = item.keyId || createGUID();
                            item.checked = true;
                            resetStockByPharmacyNo(item);
                            // 复制出来的不使用合规设置
                            if (item.usageInfo && item.usageInfo.payType !== null) {
                                item.usageInfo.payType = null;
                            }
                            item.specialRequirement = item.specialRequirement?.replace(/【自备】/g, '') || '';
                        });
                        if (form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY) {
                            airPharmacyForms.push(form);
                        } else {
                            if (form.pharmacyType === PharmacyTypeEnum.COOPERATION_PHARMACY) {
                                hasCooperationPharmacy = true;
                            } else {
                                localChargeForms.push(form);
                            }
                        }
                    });

                    this.postData.sellerId = data.sellerId;
                    this.postData.sellerDepartmentId = data.sellerDepartmentId;
                    this.postData.chargeForms = localChargeForms.concat(airPharmacyForms[0] || []);
                    this.chargeSheetSummary = data.chargeSheetSummary;
                    this.postData.memberId = data.memberId;
                    this.postData.memberInfo = data.memberInfo;
                    this.postData.promotions = data.promotions || [];
                    this.postData.giftRulePromotions = data.giftRulePromotions || [];
                    this.postData.couponPromotions = data.couponPromotions || [];
                    this.postData.patientPointsInfo = data.patientPointsInfo;
                    this.postData.patientCardPromotions = data.patientCardPromotions || [];
                    this.postData.patientPointDeductProductPromotions = data.patientPointDeductProductPromotions || [],
                    this.loading = false;
                    if (hasCooperationPharmacy) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '开往合作药店的处方，无法复制零售开单。',
                        });
                    }
                } else {
                    this.$Toast({
                        title: '缺少patientOrderId',
                    });
                }
            },

            /**
             * @desc 挂单
             * <AUTHOR>
             * @date 2019/11/21 17:37:45
             */
            async hangUpOrder() {
                this.$confirm({
                    title: '确定挂单？',
                    content: '零售收费单挂单后，该单据会共享为所有收费员可见。',
                    onConfirm: () => {
                        this.hangUpOrderSubmit();
                    },
                });
            },
            /**
             * @desc 挂单/保存都是落地收费单，两种场景
             * <AUTHOR>
             * @date 2024/06/18 15:56:10
             * @param {Boolean} isSave 是否为保存
             * @return
             */
            async hangUpOrderSubmit(isSave = false) {
                try {
                    if (isSave) {
                        this.buttonSaveLoading = true;
                    } else {
                        this.buttonLoading = true;
                    }
                    this.postData.chargeForms.forEach((form, Pindex) => {
                        form.sort = Pindex;
                        form.chargeFormItems.forEach((item, index) => {
                            item.sort = index;
                        });
                    });
                    const apiName = isSave ? 'saveOrder' : 'hangUpOrder';
                    const { data } = await ChargeAPI[apiName]({
                        ...this.postData,
                    });

                    // 1.清除草稿
                    this.clearDraft();
                    // 2.更新quicklist状态数量
                    await this.refreshCashierQuickList();
                    // 3.默认选中刚添加的
                    const selectedItem = this.cashier.quickList.find((item) => {
                        if (isSave) {
                            return item.id === data.id;
                        }
                        return item.id === data.chargeSheetId;
                    });
                    this.$store.dispatch('setSelectedItem', {
                        type: 'cashier',
                        selectedItem,
                    });
                    const message = isSave ? '保存成功' : '挂单成功';
                    this.$Toast({
                        message,
                        type: 'success',
                    });
                    if (isSave) {
                        this.select(selectedItem);
                    } else {
                        this.selectFirst();
                    }
                    if (isSave) {
                        this.buttonSaveLoading = false;
                    } else {
                        this.buttonLoading = false;
                    }
                } catch (err) {
                    // TODO CRM 挂单逻辑处理
                    if ([13502, 13503].includes(err.code) && this.crmAccessVisibility) {
                        this.handlePatientExist(err.code, err.detail);
                    } else {
                        const { message } = err;
                        message && this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    }

                    if (isSave) {
                        this.buttonSaveLoading = false;
                    } else {
                        this.buttonLoading = false;
                    }
                }
            },

            /**
             * @desc 判断是否选中第一个 quicklist item
             * <AUTHOR>
             * @date 2019/02/27 13:56:30
             */
            selectFirst() {
                let selectedItem;

                if (this.draftCashierNews.length) {
                    selectedItem = this.draftCashierNews[ 0 ];
                } else {
                    if (this.cashier.quickList.length > 0) {
                        selectedItem = this.cashier.quickList[ 0 ];
                    } else {
                        selectedItem = null;
                    }
                }
                this.select(selectedItem);
            },

            /**
             * @desc 选择到某一个单子
             * <AUTHOR>
             * @date 2019/11/21 18:03:17
             */
            select(selectedItem) {
                this.$store.dispatch('setSelectedItem', {
                    type: 'cashier',
                    selectedItem,
                });

                this._selectedTab = +this.$route.query.tab || 1;

                if (!selectedItem) {
                    this.replaceRouter('cashier');
                    return false;
                }

                if (selectedItem.draftId) {
                    this.replaceRouter(`cashier/add/${selectedItem.draftId}`);
                } else if (selectedItem.id) {
                    this.replaceRouter(`cashier/${selectedItem.id}`);
                } else {
                    this.replaceRouter('cashier');
                }
            },

            replaceRouter(path) {
                const { routeBasePath } = this.viewDistributeConfig.Cashier;
                this.$router.replace({
                    path: routeBasePath + path,
                    query: {
                        tab: this._selectedTab,
                    },
                });
            },
            handleUpdateInfo(e) {
                this.postData.patient = e;
            },
            /**
             * @desc 推送
             * <AUTHOR>
             * @date 2020/02/13 15:04:06
             */
            async sendOrder() {
                await this.validateSocialCode(this.postData);

                if (!this.autoSendOrderInfoSwitch && this.validateCompany().flag) return;

                this.$refs.postDataForm.validate((valid) => {
                    if (valid) {
                        const {
                            noGoodsInfoItems,
                            disabledItems,
                            shortageItems,
                        } = getWarnChargeItemGroup(this.postData.chargeForms);

                        if (noGoodsInfoItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品无商品资料：',
                                content: [`<span>${noGoodsInfoItems.map((item) => {
                                    return item.name || item.medicineCadn;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }
                        if (disabledItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品已停用不可推送，请修改后重试：',
                                content: [`<span>${disabledItems.map((item) => {
                                    return item.name || item.medicineCadn;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }
                        if (this.disableNoStockGoods && shortageItems.length > 0) {
                            this.$alert({
                                type: 'warn',
                                title: '以下商品库存不足不可推送，请修改后重试：',
                                content: [`<span>${shortageItems.map((item) => {
                                    let str = '';
                                    if (item.parentName) {
                                        str += `【${item.parentName}】`;
                                    }
                                    str += item.name || item.medicineCadn;
                                    return str;
                                }).join('、')}</span>`],
                            });
                            return false;
                        }

                        if (this.needCheckStock && shortageItems.length > 0) {
                            ChargeNoticeDialog({
                                data: shortageItems,
                                headTips: '库存不足的药品物资不可推送，请修改后重试',
                                showConfirm: false,
                                confirm: () => {
                                    this.showSendOrderDialog = true;
                                },
                            });
                        } else {
                            this.showSendOrderDialog = true;
                        }
                    }
                });
            },
            sendSuccess(id) {
                this.initChargeId(id);
                this.chargeSuccess({
                    id,
                });
            },
            changeVisitSource(data) {
                this.$set(this.postData, 'visitSourceId', data?.visitSourceId || null);
                this.$set(this.postData, 'visitSourceFrom', data?.visitSourceFrom || null);
                this.$set(this.postData, 'visitSourceFromName', data?.visitSourceFromName || null);
            },

            onBeforePushClick() {
                this.$confirm({
                    type: 'warn',
                    title: '是否生成收费单？',
                    content: '当前收费单为草稿状态，需要先生成待收费单才可使用推送支付。',
                    onConfirm: () => {
                        this.hangUpOrderSubmit(true);
                    },
                });
            },
        },
    };
</script>
