<template>
    <div class="retail-post-data-form">
        <div class="patient-detail-wrapper" style="margin-bottom: 16px;">
            <div class="patient-info">
                <patient-section
                    :key="selectedPatient && selectedPatient.id"
                    ref="patient-section"
                    v-model="postData.patient"
                    :is-required="isRequired"
                    :is-cashier="true"
                    :loading="loading"
                    size="medium"
                    :support-one-click-billing="!readonly && !disabledEditOrder"
                    :disabled="disabledPatientForm"
                    :default-patient="selectedPatient"
                    :is-can-see-patient-mobile="isCanSeePatientMobileInCashier"
                    :custom-validate-patient-name="validatePatientName"
                    @enterEvent="e => enterEvent(e, false)"
                    @update-patient="changePatientInfo"
                    @change-patient="changePatientHandler"
                ></patient-section>
            </div>
            <abc-space class="seller-selector-wrapper" is-compact compact-block>
                <template v-if="showAddOutpatient">
                    <abc-form-item>
                        <abc-select
                            v-model="postData.type"
                            :show-value="chargeTypeDesc"
                            :inner-width="94"
                            :width="64"
                            size="medium"
                        >
                            <abc-option
                                v-for="item in chargeSheetTypeOptions"
                                :key="item.value"
                                :value="item.value"
                                :label="item.label"
                            >
                            </abc-option>
                        </abc-select>
                    </abc-form-item>
                    <abc-form-item v-if="chargeSheetType === ChargeSheetTypeEnum.DIRECT_OUTPATIENT_ADDITIONAL" required>
                        <outpatient-sheet-select
                            v-model="postData.patientOrderId"
                            :patient-id="postData.patient.id"
                            size="medium"
                        ></outpatient-sheet-select>
                    </abc-form-item>
                </template>
                <abc-form-item
                    v-if="
                        !hiddenSeller &&
                            (
                                !isSupportAddOutpatient ||
                                chargeSheetType !== ChargeSheetTypeEnum.DIRECT_OUTPATIENT_ADDITIONAL
                            )
                    "
                    :required="requiredSeller"
                >
                    <abc-input
                        v-if="clonePrescriptionType === 1"
                        :width="132"
                        disabled
                        placeholder="业绩关联人"
                        :value="postData.doctorName"
                        size="medium"
                    ></abc-input>
                    <abc-input
                        v-else-if="chargeSheetType === ChargeSheetTypeEnum.THERAPY || chargeSheetType === ChargeSheetTypeEnum.EXAMINATION_INSPECTION"
                        :width="132"
                        disabled
                        placeholder="业绩关联人"
                        :value="sellerName"
                        size="medium"
                    ></abc-input>
                    <abc-input
                        v-else-if="chargeSheetType === ChargeSheetTypeEnum.MEDICAL_PLAN"
                        :width="132"
                        disabled
                        placeholder="开单人"
                        :value="postData.doctorName"
                        size="medium"
                    ></abc-input>
                    <abc-popover
                        v-else
                        placement="bottom-start"
                        :open-delay="600"
                        :offset="10"
                        :disabled="!postData.sellerId"
                        trigger="hover"
                        theme="yellow"
                        style="display: inline-block;"
                    >
                        <employee-department-selector
                            slot="reference"
                            :department-id.sync="postData.sellerDepartmentId"
                            :doctor-id.sync="postData.sellerId"
                            placeholder="开单人"
                            data-cy="charge-seller-select"
                            size="medium"
                            @change="changeSeller"
                            @enterEvent="sellerEnterEvent"
                        ></employee-department-selector>
                        <div>开单人</div>
                    </abc-popover>
                </abc-form-item>

                <abc-form-item v-if="isSupportAddConsultant">
                    <abc-popover
                        placement="bottom-start"
                        :open-delay="600"
                        :offset="10"
                        :disabled="!postData.consultantId"
                        trigger="hover"
                        theme="yellow"
                        style="display: inline-block;"
                    >
                        <abc-select
                            slot="reference"
                            v-model="postData.consultantId"
                            clearable
                            :disabled="chargeSheetType === ChargeSheetTypeEnum.MEDICAL_PLAN"
                            :width="74"
                            :inner-width="100"
                            placeholder="咨询师"
                            size="medium"
                        >
                            <abc-option
                                v-for="consultant in consultantList"
                                :key="consultant.employeeId"
                                :value="consultant.employeeId"
                                :label="consultant.employeeName"
                            ></abc-option>
                        </abc-select>
                        <div>咨询师</div>
                    </abc-popover>
                </abc-form-item>
            </abc-space>

            <template v-if="isOutpatient && registrationInfo">
                <abc-input
                    :width="140"
                    disabled
                    style="margin-right: 6px;"
                    :value="doctorStr"
                    size="medium"
                ></abc-input>
                <abc-input
                    v-model="doctorFeeStr"
                    :width="86"
                    type="money"
                    disabled
                    :input-custom-style="{
                        padding: '0 3px 0 32px', textAlign: 'left'
                    }"
                    size="medium"
                >
                    <label slot="prepend" class="prepend" style="width: 24px; font-size: 14px; text-align: center;">
                        <abc-currency-symbol-icon></abc-currency-symbol-icon>
                    </label>
                </abc-input>
            </template>
        </div>

        <hospital-info-card
            v-if="hospitalInfo"
            :hospital-info="hospitalInfo"
            style="margin-bottom: 16px;"
            disabled
        ></hospital-info-card>

        <div v-if="!readonly && !disabledEditOrder" class="search-header">
            <medicine-item-autocomplete
                ref="medicine-autocomplete"
                :close-switch="closeSwitch"
                :shebao-card-info="postData.shebaoCardInfo"
                :department-id="postData.sellerDepartmentId"
                :charge-forms="postData.chargeForms"
                :process-type="processFormProcessInfo.processType"
                :process-sub-type="processFormProcessInfo.processSubType"
                open-search-filter
                :disabled-scan-barcode="disabledScanBarcode || isDisabledScanBarcode"
                placeholder="扫条形码 / 扫追溯码 / 输入名称或拼音首字母"
                @deleteItem="deleteItem"
                @submit="$emit('submit')"
                @inputDoseCount="inputDoseCount"
                @select="selectItem"
            ></medicine-item-autocomplete>
            <abc-button
                v-if="canAddAirPharmacy && !isIntranetUser"
                type="ghost"
                :disabled="!!airPharmacyForm"
                size="large"
                icon="s-add-line-medium"
                @click="addAirPharmacy"
            >
                空中药房
            </abc-button>
        </div>

        <form-table
            ref="cashierFormTable"
            :status="-1"
            :outpatient-status="outpatientStatus"
            :patient-id="postData.patient.id"
            edit-count
            :count-style="{
                width: '154px', maxWidth: '154px', minWidth: '154px'
            }"
            :input-calc-loading="calcLoading"
            :charge-forms.sync="postData.chargeForms"
            :charge-sheet-type="chargeSheetType"
            :is-replay="isReplay"
            :post-data="postData"
            :readonly="readonly || disabledEditOrder"
            :current-active-index.sync="currentActiveIndex"
            :clone-prescription-type="clonePrescriptionType"
            @changeRegistration="changeRegistration"
            @enterEvent="enterEvent"
            @enterNext="enterNext"
            @change="changeHandler"
        ></form-table>

        <air-pharmacy-table
            v-if="airPharmacyForm"
            :key="airPharmacyForm.id || airPharmacyForm.keyId"
            ref="cashierFormTable"
            :status="-1"
            :post-data="postData"
            :charge-sheet-type="chargeSheetType"
            :current-active-index.sync="currentActiveIndex"
            :patient="postData.patient"
            :patient-id="postData.patient.id"
            :air-pharmacy-form="airPharmacyForm"
            :can-single-bargain="formCanSingleBargain"
            :need-check-stock="!isVirtualPharmacy"
            edit-count
            :count-style="{
                width: '154px', maxWidth: '154px', minWidth: '154px'
            }"
            :input-calc-loading="calcLoading"
            :is-replay="isReplay"
            :start-index="airPharmacyFormItemStartIndex"
            :can-add-medicine="canAddAirPharmacy"
            :readonly="readonly || disabledEditOrder"
            @deleteForm="deleteAirPharmacyForm"
            @submit="$emit('submit')"
            @change="changeHandler"
            @changePharmacyLoading="val => airPharmacyLoading = val "
        ></air-pharmacy-table>

        <discount-table
            :member-id.sync="postData.memberId"
            :use-member-flag="postData.useMemberFlag"
            :charge-status="-1"
            :post-data="postData"
            :is-replay="isReplay"
            :charge-sheet-id="chargeSheetId"
            :charge-forms="postData.chargeForms"
            :promotions.sync="postData.promotions"
            :gift-rule-promotions.sync="postData.giftRulePromotions"
            :coupon-promotions.sync="postData.couponPromotions"
            :member-info.sync="postData.memberInfo"
            :patient-points-info.sync="postData.patientPointsInfo"
            :patient-card-promotions.sync="postData.patientCardPromotions"
            :patient-point-deduct-product-promotions.sync="postData.patientPointDeductProductPromotions"
            :mall-verifications.sync="postData.mallVerifications"
            :verify-info-views="postData.verifyInfoViews"
            :summary="chargeSheetSummary"
            :readonly="readonly"
            :show-header="false"
            :support-visit-source="true"
            @change="(flag)=>{
                changeHandler(true, true, flag)
            }"
            @change-visit-source="(data) => $emit('change-visit-source', data)"
        ></discount-table>

        <!--右侧边栏-->
        <cashier-sidebar
            :charge-status="-1"
            :clone-prescription-type="clonePrescriptionType"
            :clone-prescription-info="clonePrescriptionInfo"
            :attachments="attachments"
            @addItem="
                (data) => {
                    selectItem(data, {
                        fromSidebar: true
                    });
                }
            "
            @copy="(mr, type) => $emit('copy', mr, type)"
            @transcription="transcriptionHandle"
        ></cashier-sidebar>

        <write-by-photo
            v-if="showPhotoDialog"
            v-model="showPhotoDialog"
            :post-data="postData"
            :charge-sheet-type="chargeSheetType"
            :clone-prescription-type="clonePrescriptionType"
            :dose-count="clonePrescriptionInfo.doseCount"
            :remarks="clonePrescriptionInfo.remarks"
            :attachments="attachments"
            @change="changeClonePrescription"
        ></write-by-photo>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import enterEvent from '../mixins/enter-event';
    import keyboardEvent from '../mixins/keyup-keydown-event';
    /**
     * @desc 自定义组件
     * <AUTHOR>
     * @date 2020/05/27 11:54:52
     */
    import MedicineItemAutocomplete from '../components/medicine-item-autocomplete/medicine-item-autocomplete';
    import PatientSection from 'views/layout/patient/patient-section/index.vue';
    import FormTable from '../table/table.vue';
    import AirPharmacyTable from '../../air-pharmacy/table.vue';
    const DiscountTable = () => import('../discount/table.vue');
    import EmployeeDepartmentSelector from 'views/common/components/employee-department-selector/index';
    import CashierSidebar from '../sidebar/index.vue';
    import WriteByPhoto from '../write-by-photo/write-by-photo';
    import HospitalInfoCard from 'src/views/hospital/hospital-info-card.vue';
    import OutpatientSheetSelect from 'src/views/cashier/components/outpatient-sheet-select.vue';

    /**
     * @desc 引入API
     * <AUTHOR>
     * @date 2020/05/27 11:55:02
     */
    import GoodsApi from 'api/goods/index';
    import CrmAPI from 'api/crm';
    import Outpatient from 'api/outpatient';

    /**
     * @desc 工具类
     * <AUTHOR>
     * @date 2020/05/27 11:55:13
     */
    import { debounce } from 'utils/lodash';
    import {
        createGUID,
        parseTime,
    } from 'utils/index';
    import AbcChargeService from '@/service/charge';
    import Store from 'utils/localStorage-handler';

    /**
     * @desc 常量
     * <AUTHOR>
     * @date 2020/05/27 11:55:29
     */
    import {
        GoodsTypeEnum, ANONYMOUS_ID, PharmacyTypeEnum, GoodsSubTypeEnum,
    } from '@abc/constants';
    import {
        SourceFormTypeEnum, ChargeSheetTypeEnum, UseMemberFlagEnum,
    } from '@/service/charge/constants.js';
    import {
        getChargeItemStruct, isStockGoods, setChargeForm, updateChargeComposeGoods,
    } from 'views/cashier/utils/index.js';
    import GoodsModel from 'views/common/goods-model.js';
    import { resetStockByPharmacyNo } from 'views/layout/prescription/utils.js';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import { SearchSceneTypeEnum } from 'views/common/enum.js';
    import { isAllowAddByAntimicrobialDrugManagement } from 'views/outpatient/utils';
    import AntimicrobialDrugManagementModal from 'views/outpatient/common/antimicrobial-drug-limit-modal';
    import TraceCode, {
        AliPackageLevelEnum,
        AliPackageLevelLabel, ShebaoTraceableCodeDismountingFlagEnum,
        TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import { OutpatientStatusEnumNew } from 'views/outpatient/constants.js';
    import TraceCodeLimitDialog from '@/service/trace-code/dialog-limit-trace-code';

    export default {
        components: {
            MedicineItemAutocomplete,
            PatientSection,
            FormTable,
            AirPharmacyTable,
            DiscountTable,
            EmployeeDepartmentSelector,
            CashierSidebar,
            WriteByPhoto,
            HospitalInfoCard,
            OutpatientSheetSelect,
            AbcCurrencySymbolIcon,
        },

        mixins: [enterEvent, keyboardEvent],

        props: {
            postData: {
                type: Object,
                required: true,
            },
            hiddenSeller: {
                type: Boolean,
                default: false,
            },
            isServerDraft: {
                type: [Boolean, Number],
                default: false,
            },
            isReplay: Boolean,
            chargeSheetType: [Number, String],
            chargeSheetId: [String, Number],
            chargeSheetSummary: Object,
            inputCalcLoading: Boolean,
            closeSwitch: Boolean,
            calcSwitch: Boolean,
            disabledKeyboard: Boolean,
            registrationInfo: Object,
            sendToPatientStatus: Number,
            clonePrescriptionType: Number,
            clonePrescriptionInfo: Object,
            attachments: Array,
            readonly: Boolean,
            outpatientStatus: Number,

            // 能否编辑单据，不包括收费相关的折扣信息
            disabledEditOrder: {
                type: Boolean,
                default: false,
            },

            hospitalInfo: {
                type: Object,
            },
            loading: {
                type: Boolean,
                default: false,
            },

            // 禁止扫码响应
            disabledScanBarcode: {
                type: Boolean,
                default: false,
            },

            isDisabledCollectTraceCode: {
                type: Boolean,
                default: false,
            },

        },

        data() {
            return {
                ChargeSheetTypeEnum,
                fromAutocomplete: false,
                showDeliverAddress: false,
                currentViewImageIndex: 0,
                showPhotoDialog: false,
                airPharmacyLoading: true,
                hasInterceptAliPackageLevel: false, //是否开启中码、大码拦截
                hasCodeSafeOpened: false,
                traceCodeCallbackQueue: [], // 处理采集后追溯码回调队列
                hasEnableDismountingMode: false, //是否启用拆零不采模式
            };
        },
        computed: {
            ...mapGetters([
                'chainBasic',
                'chinesePRUsageDefault',
                'cashier',
                'chargeConfig',
                'pharmacyRuleList',
                'isCanSeePatientMobileInCashier',
                'isIntranetUser',
            ]),
            ...mapGetters('airPharmacy', ['usageScopes', 'clinicCanUseAirPharmacy']),
            ...mapGetters('virtualPharmacy', ['virtualPharmacyIsOpen']),
            ...mapGetters('consult', ['consultantList']),
            ...mapGetters('adjustPriceSetting',[
                'employeeCanAdjustPrice',
            ]),

            processFormProcessInfo() {
                const processForm = this.postData.chargeForms.find((form) => form.sourceFormType === SourceFormTypeEnum.DECOCTION);
                if (!processForm) {
                    return {
                        processType: undefined,
                        processSubType: undefined,
                    };
                }
                return {
                    processType: processForm.processInfo?.type,
                    processSubType: processForm.processInfo?.subType,
                };
            },

            chargeSheetTypeOptions() {
                return [
                    {
                        label: '零售开单',
                        value: ChargeSheetTypeEnum.DIRECT_SALE,
                    },
                    {
                        label: '门诊开单',
                        value: ChargeSheetTypeEnum.DIRECT_OUTPATIENT_ADDITIONAL,
                    },
                ];
            },

            chargeTypeDesc() {
                const { type } = this.postData;
                const map = {
                    [ChargeSheetTypeEnum.DIRECT_SALE]: '零售',
                    [ChargeSheetTypeEnum.DIRECT_OUTPATIENT_ADDITIONAL]: '门诊',
                };
                return map[type];
            },

            // 门诊的单子-包含门诊开单和补录
            isOutpatient() {
                return [
                    ChargeSheetTypeEnum.OUTPATIENT,
                    ChargeSheetTypeEnum.DIRECT_OUTPATIENT_ADDITIONAL,
                ].includes(this.chargeSheetType);
            },

            formCanSingleBargain() {
                const form = this.airPharmacyForm;
                return this.chargeConfig.singleBargainSwitch && this.employeeCanAdjustPrice &&
                    (form?.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY || form?.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY);
            },

            isVirtualPharmacy() {
                return this.airPharmacyForm?.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },

            selectedPatient() {
                return this.cashier.selectedPatient;
            },


            /**
             * @desc 不能编辑患者信息
             * <AUTHOR> Yang
             * @date 2020-12-15 13:59:12
             * @params （服务端的单子但是不是挂单草稿 || 给患者推送过 || readonly || 续方 || disabledEditOrder）
             */
            disabledPatientForm() {
                return (!!this.chargeSheetId && !this.isServerDraft) ||
                    this.chargeSheetType !== ChargeSheetTypeEnum.DIRECT_SALE ||
                    this.sendToPatientStatus > 0 ||
                    this.readonly ||
                    this.clonePrescriptionType > 0 ||
                    this.disabledEditOrder;
            },

            isRequired() {
                if (this.airPharmacyForm) return true;
                if (this.needValidatePatientName) return true;
                return !!(
                    this.postData.patient.name ||
                    this.postData.patient.age.year ||
                    this.postData.patient.age.month ||
                    this.postData.patient.mobile
                );
            },
            calcLoading: {
                get() {
                    return this.inputCalcLoading;
                },
                set(val) {
                    this.$emit('update:inputCalcLoading', val);
                },
            },

            requiredSeller() {
                return this.chainBasic && !!this.chainBasic.chargeRequiredSeller;
            },

            doctorStr() {
                if (!this.registrationInfo) return '';
                const {
                    departmentName,
                    doctorName,
                } = this.registrationInfo;

                let str = '';
                if (departmentName) {
                    str += `${departmentName}-`;
                }
                if (doctorName) {
                    str += doctorName;
                }
                return str;
            },
            doctorFeeStr() {
                return this.registrationInfo?.fee?.toFixed(2);
            },
            sellerName() {
                if (this.postData.sellerDepartmentName) {
                    return `${this.postData.sellerDepartmentName}-${this.postData.sellerName}`;
                }
                return this.postData.sellerName;
            },

            airPharmacyForm() {
                return this.postData.chargeForms.find(
                    (form) => form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY ||
                        form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY ||
                        form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY,
                );
            },
            airPharmacyFormItemStartIndex() {
                let length = 0;
                this.postData.chargeForms
                    .filter((form) => form.sourceFormType !== SourceFormTypeEnum.AIR_PHARMACY &&
                        form.pharmacyType !== PharmacyTypeEnum.VIRTUAL_PHARMACY)
                    .forEach((form) => {
                        length += form.chargeFormItems.length;
                    });
                return length;
            },
            canAddAirPharmacy() {
                // 如果不能编辑患者 && 没有患者则不能再添加空中药房
                if (
                    this.disabledPatientForm &&
                    (!this.postData.patient.id || this.postData.patient.id === ANONYMOUS_ID)
                ) {
                    return false;
                }
                // 零售收费 || 医生拍照续方 可以添加空中药房
                // clonePrescriptionType 续方类型(0 非续方；1 医生拍照；2 患者拍照；3 患者历史收费单续方)
                // 有usageScopes，代表有空中药房，或者虚拟药房
                return (
                    (this.virtualPharmacyIsOpen || this.clinicCanUseAirPharmacy) &&
                    (this.chargeSheetType === ChargeSheetTypeEnum.DIRECT_SALE || this.clonePrescriptionType > 0)
                );
            },

            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            isSupportAddOutpatient() {
                const { supportAddOutpatientSheet } = this.viewDistributeConfig.Cashier;
                return supportAddOutpatientSheet;
            },

            isSupportAddConsultant() {
                const { supportAddConsultant } = this.viewDistributeConfig.Cashier;
                return supportAddConsultant;
            },

            showAddOutpatient() {
                return this.isSupportAddOutpatient &&
                    (
                        this.chargeSheetType === ChargeSheetTypeEnum.DIRECT_OUTPATIENT_ADDITIONAL ||
                        this.chargeSheetType === ChargeSheetTypeEnum.DIRECT_SALE
                    );
            },

            chargeItemSupportDoctorNurse() {
                return this.viewDistributeConfig.chargeItemSupportDoctorNurse;
            },
            lastSelectDoctorNurseInfo() {
                const res = {
                    departmentId: null,
                    departmentName: null,
                    doctorId: null,
                    doctorName: null,
                    nurseId: null,
                    nurseName: null,
                };

                if (!this.chargeItemSupportDoctorNurse) {
                    return res;
                }
                this.postData.chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        if (item.departmentId) {
                            Object.assign(res, {
                                departmentId: item.departmentId,
                                departmentName: item.departmentName,
                            });
                        }
                        if (item.doctorId) {
                            Object.assign(res, {
                                doctorId: item.doctorId,
                                doctorName: item.doctorName,
                            });
                        }
                        if (item.nurseId) {
                            Object.assign(res, {
                                nurseId: item.nurseId,
                                nurseName: item.nurseName,
                            });
                        }
                    });
                });
                return res;
            },

            /**
             * @desc 深圳地区，如果零售包含对码药品，需要校验患者必填
             * <AUTHOR> Yang
             * @date 2024-11-27 17:13:06
             */
            needValidatePatientName() {
                if (!this.$abcSocialSecurity.isOpenSocial) {
                    return false;
                }
                if (!this.$abcSocialSecurity.isSupportSelfPayInputCardNoRemid) {
                    return false;
                }
                return this.postData.chargeForms.some((form) => {
                    return form.chargeFormItems.some((item) => {
                        const {
                            productInfo,
                        } = item;
                        const childHas = item.composeChildren?.some((child) => {
                            return child?.productInfo?.shebao?.nationalCode;
                        });
                        return childHas || productInfo?.shebao?.nationalCode;
                    });
                });
            },
        },
        watch: {
            calcSwitch() {
                if (this.airPharmacyForm) {
                    if (!this.airPharmacyLoading) {
                        this._calcFee();
                    }
                } else {
                    this._calcFee();
                }
            },
        },
        created() {
            this.$abcEventBus.$on('add-list', (list) => {
                const formatList = list.map((item) => {
                    return {
                        ...item.goods,
                        ...item.initialData,
                    };
                });
                this.batchAdd(formatList);
            }, this);
            this._calcFee = debounce(this.calcFee, 200, true);
            this.hasInterceptAliPackageLevel = TraceCode.hasInterceptAliPackageLevel();
            this.hasEnableDismountingMode = TraceCode.hasEnableDismountingMode;
            this.hasCodeSafeOpened = TraceCode.hasCodeSafeOpened;
        },
        mounted() {
            this._openAllCallbackQueueDialog = debounce(() => {
                if (this.traceCodeCallbackQueue.length > 0) {
                    this.processDialogQueue();
                }
            }, 300, true);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            validatePatientName(value, callback) {
                const {
                    patient,
                } = this.postData;
                if (!this.isRequired || patient.id || patient.name) {
                    callback({
                        validate: true,
                    });
                } else {
                    callback({
                        validate: false,
                        message: this.needValidatePatientName ? '医保要求姓名必填' : '姓名不能为空',
                    });
                }
            },
            changeHandler(immediate = false, isChangeDiscount = false, useMemberFlag = UseMemberFlagEnum.USE_DEFAULT) {
                // 有引起价格变动，需要清空积分；折扣变动组件内部处理；
                if (!isChangeDiscount && this.postData.patientPointsInfo) {
                    this.postData.patientPointsInfo.checked = false;
                }
                // 判断是否需要处理
                if (useMemberFlag) {
                    this.postData.useMemberFlag = useMemberFlag;
                }
                if (immediate) {
                    this.calcFee();
                } else {
                    this._calcFee();
                }
            },
            /** ------------------------------------------------------------------------------------
             * patientForm改变后
             * @param patient
             */
            async changePatientHandler(patient) {
                const {
                    id,
                    shebaoCardInfo,
                } = patient;
                this.postData.memberId = '';
                this.postData.memberInfo = null;
                this.postData.useMemberFlag = UseMemberFlagEnum.USE_DEFAULT;
                // 切换患者需要清空卡项
                this.postData.patientCardPromotions = [];
                this.postData.verifyInfoViews = [];
                this.changeHandler(true);
                await this.$store.dispatch('setSelectedPatient', {
                    type: 'cashier',
                    patientId: id,
                });
                const { selectedPatient } = this.cashier;
                if (selectedPatient) {
                    const {
                        tags = [],
                        appFlag = 0,
                        arrearsFlag = 0,
                    } = selectedPatient;
                    this.postData.patient.tags = tags;
                    this.postData.patient.appFlag = appFlag;
                    this.postData.patient.arrearsFlag = arrearsFlag;
                    this.postData.cacheShebaoCardInfo = shebaoCardInfo;
                }
                this.changeChargeType(id);
            },
            /**
             * desc [判断是否共享会员]
             */
            async exitShareMember(patientId) {
                if (patientId) {
                    try {
                        const { data } = await CrmAPI.selectShareMemberInfo(patientId);
                        if (data.memberInfo) {
                            return {
                                mobile: data.memberInfo.memberCardId,
                                memberId: data.memberInfo.patientId,
                                memberTypeName: data.memberInfo.memberTypeInfo && data.memberInfo.memberTypeInfo.memberTypeName,
                            };
                        }
                    } catch (error) {
                        console.log('exitShareMember error', error);
                    }
                }
                return null;
            },

            changeSeller(seller) {
                this.postData.sellerName = seller.name;

                this.postData.chargeForms.forEach((form) => {
                    if (
                        form.sourceFormType !== SourceFormTypeEnum.PRESCRIPTION_CHINESE &&
                        form.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY
                    ) {
                        form.chargeFormItems?.forEach((item) => {
                            const goodsModel = new GoodsModel(item.productInfo);
                            let pharmacy = null;
                            if (goodsModel.isStockGoods) {
                                pharmacy = getDefaultPharmacy(this.pharmacyRuleList, {
                                    departmentId: this.postData.sellerDepartmentId,
                                    goodsInfo: item.productInfo,
                                });
                            }
                            if (pharmacy) {
                                item.pharmacyType = pharmacy.type;
                                item.pharmacyNo = pharmacy.no;
                                item.pharmacyName = pharmacy.name;
                                resetStockByPharmacyNo(item);
                            }
                        });
                    }
                });

                updateChargeComposeGoods({
                    chargeForms: this.postData.chargeForms,
                    departmentId: this.postData.sellerDepartmentId,
                });
            },

            changePatientInfo(patientInfo) {
                // 草稿单，此时患者信息直接同步
                Object.assign(this.postData.patient, patientInfo);

                this.$store.dispatch('updatePatientInfo', {
                    type: 'cashier',
                    info: patientInfo,
                });
                this.$store.dispatch('setSelectedPatient', {
                    type: 'cashier',
                    patientInfo,
                });
                this.changeHandler();
            },

            changeRegistration(registration) {
                this.postData.registration = registration;
                this.changeHandler(true);
            },
            async batchAdd(list) {
                if (this.readonly) {
                    this.$Toast({
                        message: '当前收费单已被锁定',
                        type: 'error',
                    });
                    return false;
                }

                list.forEach(async (selected) => {
                    if (this.readonly || this.disabledEditOrder) {
                        this.$Toast({
                            message: '当前收费单已被锁定',
                            type: 'error',
                        });
                        return false;
                    }

                    if (!selected) return false;
                    if (selected.disabled) return false;

                    // 门诊过来的收费单，不能再添加
                    if (
                        this.isOutpatient &&
                        selected.type === GoodsTypeEnum.REGISTRATION &&
                        this.registrationInfo &&
                        this.registrationInfo.status
                    ) {
                        this.$Toast({
                            message: `门诊单${this.$t('registrationFeeName')}已收`,
                            type: 'error',
                        });
                        return false;
                    }

                    this.fromAutocomplete = true;

                    const chargeItem = getChargeItemStruct(selected);
                    // 新加的本地草稿，代表可编辑，删除
                    chargeItem.isDraftChargeItem = true;

                    const {
                        departmentId,
                        departmentName,
                        doctorId,
                        doctorName,
                        nurseId,
                        nurseName,
                    } = this.lastSelectDoctorNurseInfo || {};
                    chargeItem.departmentId = departmentId;
                    chargeItem.departmentName = departmentName;
                    chargeItem.doctorId = doctorId;
                    chargeItem.doctorName = doctorName;
                    chargeItem.nurseId = nurseId;
                    chargeItem.nurseName = nurseName;

                    // 扫码来的 || 从右侧sidebar 需要自动设置1
                    if (!isStockGoods(selected.type)) {
                        chargeItem.unitCount = 1;
                    }

                    setChargeForm(this.postData.chargeForms, selected, chargeItem);

                    this.$nextTick(() => {
                        $(`#${chargeItem.keyId}`).find('.td.count input').eq(0).focus();
                    });

                    try {
                        const { data } = await GoodsApi.fetchGoods(chargeItem.productId, {
                            pharmacyNo: chargeItem.productType === GoodsTypeEnum.COMPOSE ? undefined : chargeItem.pharmacyNo,
                            sceneType: SearchSceneTypeEnum.outpatient,
                            departmentId: this.postData.sellerDepartmentId,
                        });
                        chargeItem.productInfo = data;
                        chargeItem.unitPrice = data.packagePrice || 0;
                    } catch (err) {
                        console.error(err);
                    }
                });

                console.log('this.postData.chargeForms', this.postData.chargeForms);

                this.changeHandler(true);
            },

            /**
             * 判断开出的 中西成药 和 套餐 的抗菌等级是否满足开出条件
             * @return {Promise<boolean>}
             */
            async isAllowAdd(goods, fromSidebar) {
                if (!(goods.type === GoodsTypeEnum.MEDICINE && (goods.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine || goods.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM) || goods.type === GoodsTypeEnum.COMPOSE)) return true;
                const {
                    antimicrobialDrugManagementData, employeeListByPractice,
                } = this.$store.getters;
                const {
                    doctorId, sellerId,
                } = this.postData;

                if (goods.type === GoodsTypeEnum.COMPOSE) {
                    let children = goods.children || [];
                    if (!fromSidebar) {
                        const { data } = await GoodsApi.fetchGoods(goods.id, {
                            sceneType: SearchSceneTypeEnum.outpatient,
                            departmentId: this.postData.sellerDepartmentId,
                        });
                        children = data.children;
                    }
                    const westernMedicine = (children || []).filter((item) => {
                        return item.type === GoodsTypeEnum.MEDICINE && [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(item.subType);
                    });
                    // 如果套餐子项没有包含中西成药, 则直接放行
                    if (!westernMedicine.length) return true;

                    const filterComposeFormItems = [];
                    for (const item of children) {
                        const newMedicine = {
                            name: item.displayName || item.medicineCadn || item.name, productInfo: item,
                        };
                        const isSuccess = isAllowAddByAntimicrobialDrugManagement(newMedicine, doctorId || sellerId, antimicrobialDrugManagementData, employeeListByPractice);
                        if (!isSuccess) {
                            filterComposeFormItems.push(newMedicine);
                        }
                    }
                    if (filterComposeFormItems.length) {
                        // eslint-disable-next-line abc/no-timer-id
                        setTimeout(() => {
                            new AntimicrobialDrugManagementModal({ list: filterComposeFormItems }).generateDialogAsync({ parent: this });
                        }, 100);
                        return false;
                    }
                    return true;
                }

                const item = {
                    name: goods.displayName || goods.medicineCadn || goods.name, productInfo: goods,
                };
                const isSuccess = isAllowAddByAntimicrobialDrugManagement(item, doctorId || sellerId, antimicrobialDrugManagementData, employeeListByPractice);
                if (!isSuccess) {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        new AntimicrobialDrugManagementModal({ list: [item] }).generateDialogAsync({ parent: this });
                    }, 100);
                }
                return isSuccess;
            },

            /**
             * @desc 选择后处理函数
             * <AUTHOR>
             * @date 2019/03/25 19:15:58
             */
            async selectItem(selected, options) {
                const {
                    scanBarcode,
                    fromSidebar,
                    keywordTraceableCode,
                } = options || {};
                if (this.readonly || this.disabledEditOrder) {
                    this.$Toast({
                        message: '当前收费单已被锁定',
                        type: 'error',
                    });
                    return false;
                }

                if (!selected) return false;
                if (selected.disabled) return false;

                // 门诊过来的收费单，不能再添加
                if (
                    this.isOutpatient &&
                    selected.type === GoodsTypeEnum.REGISTRATION &&
                    this.registrationInfo &&
                    this.registrationInfo.status
                ) {
                    this.$Toast({
                        message: `门诊单${this.$t('registrationFeeName')}已收`,
                        type: 'error',
                    });
                    return false;
                }

                if (!(await this.isAllowAdd(selected, fromSidebar))) return false;

                this.fromAutocomplete = true;
                const isTraceableCode = TraceCode.isTraceableCode(keywordTraceableCode?.no);
                if (!isTraceableCode && keywordTraceableCode?.no) {
                    if (TraceCode.hasInterceptTraceCodeFormatLength()) {
                        this.$Toast({
                            type: 'error',
                            message: '药品添加成功，但无法采集追溯码（医保要求药品追溯码必须为20位或30位，且不应存在汉字或其他符号）',
                        });
                    }
                }
                const traceCodeInfo = (isTraceableCode && !this.isDisabledCollectTraceCode) && {
                    no: keywordTraceableCode.no,
                    traceableCodeNoInfo: keywordTraceableCode.traceableCodeNoInfo,
                    traceableCodeList: keywordTraceableCode.traceableCodeList,
                };

                const chargeItem = getChargeItemStruct(selected, traceCodeInfo && {
                    traceableCodeList: [traceCodeInfo],
                });
                // 新加的本地草稿，代表可编辑，删除
                chargeItem.isDraftChargeItem = true;

                // 该动作不能阻塞流程
                traceCodeInfo && TraceCode.getMaxTraceCountList({
                    dataList: [chargeItem],
                    scene: TraceCodeScenesEnum.PHARMACY,
                }).then((res) => {
                    const [maxCountObj] = res;
                    const {
                        list,shebaoDismountingFlag,traceableCodeRule,
                    } = maxCountObj;
                    this.postData.chargeForms.forEach((form) => {
                        form.chargeFormItems.forEach((formItem) => {
                            if (formItem.keyId === chargeItem.keyId) {
                                formItem.shebaoDismountingFlag = shebaoDismountingFlag;
                                formItem.traceableCodeRule = traceableCodeRule;
                            }
                        });
                    });
                    const traceItem = list[0];
                    /**
                     * 医保拆零标志 shebaoDismountingFlag 二进制
                     * 第0位：是否允许拆零 0-不拆零 1-拆零 在10中0是第0位
                     * 第1位：是否允许编辑是否拆零 0-不允许 1-允许 在10中1是第1位
                     * 10->2 不拆零允许编辑; 11->3 拆零允许编辑;00->0 不拆零不允许编辑; 01->1 拆零不允许编辑
                     */
                    if (this.hasEnableDismountingMode && shebaoDismountingFlag === ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_NO_EDIT) {
                        //拆零不允许编辑
                        this.postData.chargeForms.forEach((form) => {
                            form.chargeFormItems.forEach((formItem) => {
                                if (formItem.keyId === chargeItem.keyId) {
                                    formItem.traceableCodeList = [];
                                }
                            });
                        });
                        this.$Toast({
                            type: 'error',
                            message: '根据医保最新要求，拆零追溯码无需采集',
                        });
                    } else if (this.hasCodeSafeOpened && [AliPackageLevelEnum.MEDIUM,AliPackageLevelEnum.BIG].includes(traceItem?.aliPackageLevel) && this.hasInterceptAliPackageLevel) {
                        this.postData.chargeForms.forEach((form) => {
                            form.chargeFormItems.forEach((formItem) => {
                                if (formItem.keyId === chargeItem.keyId) {
                                    formItem.traceableCodeList = formItem.traceableCodeList.filter((code) => code.no !== traceItem.no);
                                }
                            });
                        });
                        this.$Toast({
                            type: 'error',
                            message: `商品已添加，但本次采码为${AliPackageLevelLabel[traceItem?.aliPackageLevel]}，请在发药前检查并采集小包装上的追溯码`,
                        });
                    } else {
                        const isDismounting = TraceCode.isShouldApplyPieceUnit(traceItem);
                        traceCodeInfo.unit = chargeItem.unit;
                        traceCodeInfo.count = traceItem ? (isDismounting ? traceItem.hisPieceCount : traceItem.hisPackageCount) : 1;
                        traceCodeInfo.hisPackageCount = traceItem.hisPackageCount;
                        traceCodeInfo.hisPieceCount = traceItem.hisPieceCount;
                        traceCodeInfo.hisLeftTotalPieceCount = traceItem.hisLeftTotalPieceCount;

                        //首次采集追溯码，并且从码上放心转换规格失败的
                        if (traceItem?.newTraceCodeNo) {
                            if (!this.traceCodeCallbackQueue.some((item) => item.id === traceCodeInfo.no)) {
                                this.traceCodeCallbackQueue.push({
                                    id: traceCodeInfo.no,
                                    callback: () => {
                                        this.openLimitSetDialog(chargeItem, traceCodeInfo);
                                    },
                                });
                            }
                        }
                    }
                }).catch((e) => {
                    console.error(e);
                });
                const {
                    departmentId,
                    departmentName,
                    doctorId,
                    doctorName,
                    nurseId,
                    nurseName,
                } = this.lastSelectDoctorNurseInfo || {};
                chargeItem.departmentId = departmentId;
                chargeItem.departmentName = departmentName;
                chargeItem.doctorId = doctorId;
                chargeItem.doctorName = doctorName;
                chargeItem.nurseId = nurseId;
                chargeItem.nurseName = nurseName;

                // 扫码来的 || 从右侧sidebar 需要自动设置1
                if (scanBarcode || fromSidebar || !isStockGoods(selected.type)) {
                    chargeItem.unitCount = 1;
                }

                setChargeForm(this.postData.chargeForms, selected, chargeItem, scanBarcode);

                this.$nextTick(() => {
                    // if (scanBarcode) {
                    //     $(this.$el).find('.search-header .abc-input__inner')[0].focus();
                    // } else {
                    $(`#${chargeItem.keyId}`).find('.td.count input').eq(0).focus();
                    // }
                });

                if (!fromSidebar) {
                    try {
                        const { data } = await GoodsApi.fetchGoods(chargeItem.productId, {
                            pharmacyNo: chargeItem.productType === GoodsTypeEnum.COMPOSE ? undefined : chargeItem.pharmacyNo,
                            sceneType: SearchSceneTypeEnum.outpatient,
                            departmentId: this.postData.sellerDepartmentId,
                        });
                        if (chargeItem.productType === GoodsTypeEnum.COMPOSE) {
                            chargeItem.composeChildren = data.children?.map((child) => {
                                return {
                                    ...getChargeItemStruct(child),
                                    unitCount: child.composeUseDismounting ? child.composePieceCount : child.composePackageCount, // 单位数量
                                    unit: child.composeUseDismounting ? child.pieceUnit : child.packageUnit,
                                    doseCount: 1, // 包数，中药x剂，西药物质都是1
                                };
                            });
                        }
                        chargeItem.productInfo = data;
                        chargeItem.unitPrice = data.packagePrice || 0;
                    } catch (err) {
                        console.error(err);
                    }
                }
                this.changeHandler(true);
                this._openAllCallbackQueueDialog && this._openAllCallbackQueueDialog();
            },

            sysItemFocusInput(index) {
                let keyId = null;
                if (this.postData.chargeForms[index].chargeFormItems[0]) {
                    keyId = this.postData.chargeForms[index].chargeFormItems[0].keyId;
                }
                if (keyId) {
                    $(`#${keyId}`).find('input').eq(0).focus();
                }
            },

            deleteItem(itemNo) {
                this.postData.chargeForms.forEach((form) => {
                    form.chargeFormItems = form.chargeFormItems.filter((item) => {
                        return item.sortIndex !== +itemNo;
                    });
                });
                this.changeHandler(true);
            },

            /**
             * @desc 快捷键填充中药付数
             * <AUTHOR>
             * @date 2019/10/18 14:02:10
             */
            inputDoseCount(doseCount) {
                this.postData.chargeForms.forEach((form) => {
                    if (form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE) {
                        form.doseCount = doseCount;
                        form.chargeFormItems.forEach((item) => {
                            item.doseCount = doseCount;
                        });
                    }
                });
                this.changeHandler();
            },

            /**
             * @desc 算费
             * <AUTHOR>
             * @date 2019/03/30 20:49:34
             */
            async calcFee() {
                try {
                    this.calcLoading = true;
                    await new AbcChargeService({
                        chargeSheetId: this.chargeSheetId,
                        chargeSheetSummary: this.chargeSheetSummary,
                        postData: this.postData,
                        isReplay: this.isReplay,
                    }).calcFee();

                    const deliveryForm = this.postData.chargeForms.find((form) => {
                        return form.sourceFormType === SourceFormTypeEnum.EXPRESS_DELIVERY;
                    });
                    if (deliveryForm && deliveryForm.chargeFormItems[0]) {
                        this.postData.deliveryInfo.deliveryFee = deliveryForm.chargeFormItems[0].unitPrice;
                    }
                    this.calcLoading = false;
                } catch (error) {
                    this.calcLoading = false;
                    console.log('calcFee error', error);
                }
            },

            /**
             * @desc 触发处方转录dialog
             * <AUTHOR>
             * @date 2020/05/27 11:31:16
             */
            transcriptionHandle(index) {
                this.currentViewImageIndex = index;
                this.showPhotoDialog = true;
            },
            /**
             * @desc 处方转录确认修改
             * <AUTHOR>
             * @date 2020/05/27 11:49:11
             */
            changeClonePrescription(data) {
                Object.assign(this.postData, data);
                this.changeHandler(true);
            },

            /**
             * @desc 添加空中药房
             * <AUTHOR> Yang
             * @date 2020-07-02 17:35:07
             * @params
             * @return
             */
            addAirPharmacy() {
                const comment = Store.get('air_pharmacy_charge_order_remark') || '';
                const {
                    dailyDosage,
                    freq,
                    usageLevel,
                    usage,
                    usageDays,
                } = this.chinesePRUsageDefault['煎服'];

                // 本地药房有地址信息，空中药房添加自动填充
                const {
                    addressProvinceId,
                    addressProvinceName,
                    addressCityId,
                    addressCityName,
                    addressDistrictId,
                    addressDistrictName,
                    addressDetail,
                    deliveryName,
                    deliveryMobile,
                } = this.postData.deliveryInfo || {};

                const tempForm = {
                    sourceFormType: SourceFormTypeEnum.AIR_PHARMACY,
                    keyId: createGUID(),
                    specification: '中药颗粒',
                    doseCount: '',

                    vendorId: '',
                    vendorName: '',
                    pharmacyType: 1,
                    pharmacyNo: '',
                    pharmacyName: '',
                    usageScopeId: '',
                    vendorUsageScopeId: '',
                    medicineStateScopeId: '',
                    medicalRecord: {
                        chiefComplaint: '',
                        diagnosis: '',
                        doctorId: '',
                        comment,
                    },
                    usageInfo: {
                        usage,
                        dailyDosage,
                        freq,
                        usageLevel,
                        usageDays,
                        requirement: '',
                        specialRequirement: '',
                    },
                    deliveryInfo: {
                        addressProvinceId,
                        addressProvinceName,
                        addressCityId,
                        addressCityName,
                        addressDistrictId,
                        addressDistrictName,
                        addressDetail,
                        deliveryName,
                        deliveryMobile,
                        deliveryCompany: {
                            id: '',
                            name: '',
                        },
                    },
                    deliveryRule: {
                        id: '',
                    },
                    processRule: {
                        id: '',
                    },
                    totalPrice: '',
                    expectedTotalPrice: '',
                    chargeFormItems: [
                        {
                            checked: true,
                            keyId: createGUID(),
                            name: '快递费',
                            unitCount: 1,
                            doseCount: 1,
                            productType: GoodsTypeEnum.EXPRESS_DELIVERY,
                            unitPrice: null,
                            totalPrice: null,
                            productInfo: {},
                        },
                        {
                            checked: true,
                            keyId: createGUID(),
                            name: '加工费',
                            unitCount: 1,
                            doseCount: 1,
                            productType: GoodsTypeEnum.DECOCTION,
                            unitPrice: null,
                            totalPrice: null,
                            sourceUnitPrice: '',
                            sourceTotalPrice: '',
                        },
                    ],
                };

                this.postData.chargeForms.push(tempForm);
                this.$nextTick(() => {
                    $('.air-pharmacy-medicine-autocomplete').find('input').eq(0).focus();
                });
            },
            /**
             * @desc 删除空中药房
             * <AUTHOR> Yang
             * @date 2020-07-03 10:13:52
             */
            deleteAirPharmacyForm() {
                const index = this.postData.chargeForms.findIndex(
                    (form) => form.sourceFormType === SourceFormTypeEnum.AIR_PHARMACY ||
                        form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY,
                );
                this.postData.chargeForms.splice(index, 1);
            },
            /**
             * @desc 开单人选择焦点到药品
             * <AUTHOR>
             * @date 2022/10/18 17:45:06
             */
            sellerEnterEvent() {
                try {
                    const elAutoWrap = document.getElementsByClassName('retail-western-medicine-autocomplete')[0];
                    const elInput = elAutoWrap.getElementsByClassName('abc-input__inner');
                    elInput[0].focus();
                } catch (e) {
                    console.error(e);
                }

            },
            /**
             * @desc 开启补录门诊单后，切换患者获取患者待诊门诊单，如果有切换到补录模式
             * <AUTHOR>
             * @date 2023/09/13 10:13:53
             * @param {String} patientId
             */
            async changeChargeType(patientId) {
                try {
                    if (!this.showAddOutpatient) return;
                    if (!patientId) return;
                    const params = {
                        limit: 100,
                        offset: 0,
                        patientId,
                        beginDate: parseTime(new Date(), 'y-m-d', true),
                        endDate: parseTime(new Date(), 'y-m-d', true),
                        status: OutpatientStatusEnumNew.DIAGNOSED,
                    };
                    const { rows } = await Outpatient.getOutpatientList(params);
                    if (rows.length === 0) {
                        this.postData.type = ChargeSheetTypeEnum.DIRECT_SALE;
                    } else {
                        this.postData.type = ChargeSheetTypeEnum.DIRECT_OUTPATIENT_ADDITIONAL;
                    }
                } catch (e) {
                    console.error(e);
                }
            },

            openLimitSetDialog(traceCodeUseInfo, code) {
                return new Promise((resolve) => {
                    new TraceCodeLimitDialog({
                        title: '请确认追溯码可用上限',
                        isShowTips: false,
                        showClose: false,
                        isShowLeftCountSet: true,
                        traceCodeInfo: code,
                        goods: traceCodeUseInfo.productInfo,
                        onConfirm: () => {
                            resolve();
                        },
                        onClose: () => {
                            //取消本次采集 移除当前码
                            this.postData.chargeForms.forEach((form) => {
                                form.chargeFormItems.forEach((formItem) => {
                                    if (formItem.keyId === traceCodeUseInfo.keyId) {
                                        formItem.traceableCodeList = formItem.traceableCodeList.filter((c) => c.no !== code.no);
                                    }
                                });
                            });
                            resolve();
                        },
                    }).generateDialogAsync({ parent: this });
                });
            },
            // 添加一个新的队列处理方法
            async processDialogQueue() {
                if (this.traceCodeCallbackQueue.length === 0) {
                    return;
                }

                // 取出队列中的第一个回调并执行
                const queueItem = this.traceCodeCallbackQueue.shift();
                if (queueItem && typeof queueItem.callback === 'function') {
                    await queueItem.callback();
                }

                // 处理队列中的下一个回调
                this.processDialogQueue();
            },
        },
    };
</script>
