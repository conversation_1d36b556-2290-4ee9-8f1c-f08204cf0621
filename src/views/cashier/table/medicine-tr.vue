<template>
    <div
        :id="item.keyId"
        class="tr"
        :class="{
            return: false,
            uncharged: itemIsReturned || itemIsRefund,
            'is-active': item.index === currentActiveIndex,
        }"
        :data-cy="`tr-item-${item.name}`"
        @click="changeActiveIndex(item.index)"
    >
        <div v-if="showCheckbox" class="td checkbox-td no-right-border" data-cy="td-选中">
            <abc-checkbox
                v-if="isNoChargeItem || isDisabled"
                disabled
                :checked="true"
            ></abc-checkbox>
            <abc-checkbox
                v-else-if="(isCharged && item.status) || itemIsReturned"
                v-model="isChecked"
                disabled
                :checked="isChecked"
            ></abc-checkbox>
            <abc-checkbox v-else v-model="item.checked" @change="changeHandler"></abc-checkbox>
        </div>

        <div v-if="!canAddToothNo" class="td width-32 align-center no-right-border" data-cy="td-序号">
            {{ sortIndex || item.sortIndex }}
        </div>

        <!--药名-->
        <div class="td name" :class="{ 'no-right-border': showManufacturerCol }" data-cy="td-名称">
            <tooth-selector
                v-if="showAddToothNo(item)"
                v-model="item.toothNos"
                :disabled="!canEdit"
                quick-select-mode="copy"
                :copy-tooth-nos-info="copyToothNosInfo"
            ></tooth-selector>
            <abc-tooltip :disabled="!showWarnTips" :content="warnTips" :arrow-offset="20">
                <div
                    :class="{
                        'has-special-requirement': item.specialRequirement,
                    }"
                    :style="{
                        paddingLeft: !canAddToothNo ? '' : '8px',
                        maxWidth: showAddToothNo(item) ? 'calc(100% - 58px)' : '100%',
                        paddingRight: '8px',
                    }"
                >
                    <span
                        v-abc-goods-hover-popper:remote="{
                            showCostPrice: canViewCostPrice,
                            goods: item,
                            pharmacyNo: item.pharmacyNo,
                            showF1:
                                item.productType !== GoodsTypeEnum.MATERIAL && item.productType !== GoodsTypeEnum.GOODS,
                            showShebaoCode: true
                        }"
                        class="ellipsis"
                    >
                        {{ item.name }}
                    </span>
                    <span class="spec">{{ item.productInfo | getSpec }}</span>

                    <div v-if="item.usageInfo && item.usageInfo.payType === 10" class="medical-fee-grade">
                        【自】
                    </div>
                    <abc-tooltip
                        v-else-if="showMedicalFeeGrade(item)"
                        :disabled="!showSelfPayProp"
                        :content="showSelfPayProp"
                    >
                        <div class="gray medical-fee-grade">
                            【{{ item.productInfo.medicalFeeGrade | medicalFeeGrade2Str }}】
                        </div>
                    </abc-tooltip>
                    <abc-text v-if="showGoodShortId && item.productInfo" theme="gray" style="margin-left: 8px;">
                        {{ item.productInfo.shortId }}，
                        <abc-text>
                            {{ item.unitCount * item.doseCount }}
                        </abc-text>
                    </abc-text>

                    <span v-if="showWarnTips" class="shortage-tips"><i class="iconfont cis-icon-Attention"></i></span>

                    <template v-if="canAddSpecialRequirement && isChinesePrescriptionForm">
                        <div v-if="isCharged || disabledCount" class="special-requirement">
                            {{ item.specialRequirement }}
                        </div>
                        <select-usage
                            v-else
                            v-model="item.specialRequirement"
                            class="special-requirement"
                            type="cashierSpecialRequirement"
                            placeholder="煎法"
                            :icon="item.specialRequirement ? 'cis-icon-cross_small' : ''"
                            placement="bottom-start"
                            :tabindex="-1"
                            @enter="enterEvent"
                            @icon-click="clearSpecialRequirement(item)"
                        >
                        </select-usage>
                    </template>

                    <div v-if="hasRepeat" class="repeat-item">
                        重复
                    </div>

                    <template v-if="isCharged">
                        <img v-if="itemIsRefund" src="~assets/images/icon-return.png" alt="" />
                    </template>
                    <img v-if="itemIsReturned" src="~assets/images/icon-uncharged.png" alt="" />
                </div>
            </abc-tooltip>
        </div>

        <div
            v-if="showManufacturerCol"
            class="td describe width-80"
            :class="{ 'no-right-border': featureSupportRatioPrice }"
            :title="(item.productInfo && item.productInfo.manufacturer) || ''"
            data-cy="td-厂家"
        >
            <div class="td-cell" style="padding: 0;">
                <div class="ellipsis">
                    {{ (item.productInfo && item.productInfo.manufacturer) || '' }}
                </div>
            </div>
        </div>

        <div v-if="supportSelectPharmacy" class="td width-86" data-cy="td-药房">
            <abc-select
                v-if="isChinesePrescriptionForm"
                v-model="chargeForm.pharmacyNo"
                :width="86"
                class="no-border-input"
                :index="index"
                :disabled="disabledSelectPharmacy"
                :tabindex="-1"
                no-icon
                :inner-width="220"
                @focus="changeActiveIndex(item.index)"
                @change="handleChangePharmacy(chargeForm)"
            >
                <abc-option
                    v-for="it in formatEnableLocalPharmacyList"
                    :key="it.id"
                    :label="it.name"
                    :value="it.no"
                    style="padding: 0 10px;"
                >
                    <span style="max-width: 85px;" class="ellipsis">{{ it.name }}</span>

                    <div
                        v-if="item.productInfo && it.no === item.productInfo.pharmacyNo"
                        class="default-tag"
                        :style="{
                            color: themeStyle.T2,
                            marginLeft: '4px',
                            fontSize: '12px',
                        }"
                    >
                        默认
                    </div>

                    <abc-text
                        size="mini"
                        theme="gray-light"
                        style=" max-width: 60px; margin-left: auto;"
                        class="ellipsis"
                    >
                        {{ it.pharmacyTip }}
                    </abc-text>
                </abc-option>
            </abc-select>
            <abc-select
                v-else
                v-model="item.pharmacyNo"
                :width="86"
                :inner-width="220"
                class="no-border-input"
                :index="index"
                :disabled="disabledSelectPharmacy"
                :tabindex="-1"
                no-icon
                @focus="changeActiveIndex(item.index)"
                @change="changePharmacy(item)"
            >
                <abc-option
                    v-for="it in formatEnableLocalPharmacyList"
                    :key="it.id"
                    :label="it.name"
                    :value="it.no"
                    style="padding: 0 10px;"
                >
                    <span style="max-width: 85px;" class="ellipsis">{{ it.name }}</span>

                    <div
                        v-if="item.productInfo && it.no === item.productInfo.pharmacyNo"
                        class="default-tag"
                        :style="{
                            color: themeStyle.T2,
                            marginLeft: '4px',
                            fontSize: '12px',
                        }"
                    >
                        默认
                    </div>

                    <abc-text
                        size="mini"
                        theme="gray-light"
                        style=" max-width: 60px; margin-left: auto;"
                        class="ellipsis"
                    >
                        {{ it.pharmacyTip }}
                    </abc-text>
                </abc-option>
            </abc-select>
        </div>

        <!--单价-->
        <div v-if="showPriceCol && !featureSupportRatioPrice" class="td price width-76 align-right" data-cy="td-单价">
            <div
                :class="{ 'is-edit': isEditedUnitPrice }"
                @mouseenter="hoverUnitPrice = true"
                @mouseleave="hoverUnitPrice = false"
            >
                <div v-if="isCharged && !allowSwitchDisplayPrice" class="td-cell">
                    {{ chargedUnitPrice(item) }}
                </div>
                <div v-else-if="isCharged && allowSwitchDisplayPrice" class="td-cell">
                    {{ chargedUnitPriceTab(item) }}
                </div>

                <abc-form-item v-else required>
                    <abc-input
                        v-model="item.unitPrice"
                        v-abc-focus-selected
                        v-abc-adjust-price-popper="{
                            adjustmentFormatLength: 4,
                            methods: val => adjustUnitPriceChange(item, val),
                            originTotalFee: item.sourceUnitPrice,
                        }"
                        size="small"
                        :width="75"
                        :input-custom-style="{ textAlign: 'right' }"
                        :tabindex="-1"
                        :disabled="disabledMedicinePrice"
                        type="money"
                        :config="getUnitPriceConfig"
                        @enter="enterNext"
                        @focus="focusUnitPrice(item)"
                        @change="changeUnitPrice"
                    >
                    </abc-input>
                    <abc-loading v-if="inputCalcLoading && showLoading === 'unitPrice'" small no-cover></abc-loading>
                </abc-form-item>

                <div v-if="isEditedUnitPrice && hoverUnitPrice" class="bargain-origin-price-popover">
                    议价前：<abc-money :value="originUnitPrice"></abc-money>
                </div>
            </div>
        </div>

        <!--数量-->
        <div class="td count width-50 align-right" data-cy="td-数量">
            <template v-if="isNoChargeItem">
                <div v-if="isCharged" class="td-cell">
                    自备
                </div>
                <abc-input
                    v-else
                    value="自备"
                    size="small"
                    :width="49"
                    disabled
                    :input-custom-style="{
                        textAlign: 'right', paddingLeft: '4px'
                    }"
                >
                </abc-input>
            </template>

            <div v-else-if="isCharged" class="td-cell">
                {{ item.unitCount }}
            </div>

            <template v-else>
                <abc-form-item
                    v-if="isChinesePrescriptionForm"
                    :validate-event="validateUnitCount"
                >
                    <abc-input
                        v-model.number="item.unitCount"
                        v-abc-focus-selected
                        size="small"
                        :width="49"
                        type="number"
                        :disabled="disabledCount"
                        :input-custom-style="{ textAlign: 'right' }"
                        :config="{
                            formatLength: 2, max: 9999999, supportZero: true
                        }"
                        @blur="
                            () => {
                                changeActiveIndex(-1);
                            }
                        "
                        @enter="enterEvent"
                        @input="changeHandler(item)"
                        @focus="changeActiveIndex(item.index)"
                        @change="handleChangeUnitCount(item)"
                    >
                    </abc-input>
                </abc-form-item>
                <abc-form-item v-else required>
                    <abc-input
                        v-model.number="item.unitCount"
                        v-abc-focus-selected
                        size="small"
                        :width="49"
                        :disabled="disabledCount"
                        :input-custom-style="{ textAlign: 'right' }"
                        type="number"
                        :config="countConfig(item)"
                        @focus="changeActiveIndex(item.index)"
                        @blur="
                            () => {
                                changeActiveIndex(-1);
                            }
                        "
                        @enter="enterEvent"
                        @input="changeHandler(item)"
                    >
                    </abc-input>
                </abc-form-item>
            </template>
        </div>

        <!--单位-->
        <div class="td width-55 align-center" data-cy="td-单位">
            <div v-if="isCharged" class="td-cell is-disabled" :title="`${item.unit}` || '次'">
                {{ item.unit }}
            </div>

            <template v-else>
                <!--未收费时，都想展示下拉-->
                <abc-select
                    v-if="isChinesePrescriptionForm"
                    :show-value="(item.productInfo && item.productInfo.pieceUnit) || 'g'"
                    :width="54"
                    size="small"
                    class="no-border-input"
                    data-cy="td-unit-select"
                    :disabled="disabledCount"
                    :index="index"
                    :tabindex="-1"
                    no-icon
                    @focus="changeActiveIndex(item.index)"
                    @change="selectUnit"
                >
                    <abc-option
                        class="is-hover"
                        :label="(item.productInfo && item.productInfo.pieceUnit) || 'g'"
                        :value="(item.productInfo && item.productInfo.pieceUnit) || 'g'"
                    >
                    </abc-option>
                </abc-select>
                <abc-form-item v-else :required="canEdit">
                    <abc-select
                        v-model="item.unit"
                        :title="item.unit"
                        :width="54"
                        size="small"
                        class="no-border-input"
                        data-cy="td-unit-select"
                        :disabled="disabledCount"
                        :index="index"
                        :tabindex="-1"
                        no-icon
                        @focus="changeActiveIndex(item.index)"
                        @change="selectUnit"
                    >
                        <abc-option
                            v-for="it in unitArray(item)"
                            :key="it.name"
                            :label="it.name"
                            :value="it.name"
                        >
                        </abc-option>
                    </abc-select>
                </abc-form-item>
            </template>
        </div>

        <template v-if="chargeItemSupportDoctorNurse">
            <div
                class="td width-66 doctor-info"
                :title="selectDepartmentDoctorName"
                data-cy="td-医生"
            >
                <div v-if="isCharged" class="td-cell ellipsis" :title="item.doctorName">
                    {{ item.doctorName || '' }}
                </div>
                <department-doctor-autocomplete
                    v-else
                    :department-id.sync="item.departmentId"
                    :department-name.sync="item.departmentName"
                    :doctor-id.sync="item.doctorId"
                    :doctor-name.sync="item.doctorName"
                    :display-department-name="false"
                    class="no-border-input"
                    :disabled="isCharged || disabledCount"
                    :width="65"
                    no-icon
                ></department-doctor-autocomplete>
            </div>
            <div class="td width-66 nurse-info" data-cy="td-护士">
                <div v-if="isCharged" class="td-cell ellipsis" :title="item.nurseName">
                    {{ item.nurseName || '' }}
                </div>
                <nurse-autocomplete
                    v-else
                    :nurse-id.sync="item.nurseId"
                    :nurse-name.sync="item.nurseName"
                    class="no-border-input"
                    :disabled="isCharged || disabledCount"
                    :width="65"
                ></nurse-autocomplete>
            </div>
        </template>

        <!--折扣-->
        <div
            v-if="showPriceCol && featureSupportRatioPrice"
            class="td width-80 align-center"
            data-cy="td-折扣"
        >
            <div v-if="isCharged" class="td-cell ellipsis">
                {{ displayTotalPriceRatio(item) }} %
            </div>
            <abc-form-item v-else>
                <price-radio-popover
                    :disabled="disabledMedicinePrice || !item.unitCount"
                    @change-price-radio="val => changePriceRadio(item, val)"
                >
                    <abc-input
                        v-abc-focus-selected
                        :value="displayTotalPriceRatio(item)"
                        :width="79"
                        placeholder="折扣"
                        type="money"
                        :disabled="disabledMedicinePrice || !item.unitCount"
                        :config="{
                            supportZero: true,
                            max: 100
                        }"
                        @input="val => inputPriceRadioHandler(item, val)"
                        @enter="enterEvent"
                        @change="changeTotalPriceRatio(item)"
                    >
                        <span slot="appendInner">%</span>
                    </abc-input>
                </price-radio-popover>
            </abc-form-item>
        </div>

        <!--金额-->
        <div v-if="showPriceCol" class="td price width-76 align-right" data-cy="td-金额">
            <div
                :class="{ 'is-edit': isEditedTotalPrice }"
                @mouseenter="hoverTotalPrice = true"
                @mouseleave="hoverTotalPrice = false"
            >
                <div v-if="isCharged && !allowSwitchDisplayPrice" class="td-cell">
                    {{ item.totalPrice | formatMoney }}
                </div>
                <div v-else-if="isCharged && allowSwitchDisplayPrice" class="td-cell">
                    {{ chargedDisplayDiscountedTotalPrice(item) | formatMoney }}
                </div>
                <abc-form-item v-else required>
                    <abc-input
                        v-model="item.totalPrice"
                        v-abc-focus-selected
                        v-abc-adjust-price-popper="{
                            methods: val => adjustPriceChange(item, val),
                            originTotalFee: item.sourceTotalPrice,
                        }"
                        size="small"
                        :width="75"
                        type="money"
                        :tabindex="-1"
                        :disabled="disabledMedicinePrice || !item.unitCount"
                        :input-custom-style="{ textAlign: 'right' }"
                        :config="{
                            formatLength: 2, supportZero: true, max: 9999999
                        }"
                        @focus="focusTotalPrice(item)"
                        @change="changeTotalPrice"
                    >
                    </abc-input>
                    <abc-loading v-if="inputCalcLoading && showLoading === 'totalPrice'" small no-cover></abc-loading>
                </abc-form-item>

                <div
                    v-if="(isEditedTotalPrice || hasBatchInfos || item.unitAdjustmentFeeLastModifiedByName) && hoverTotalPrice"
                    class="bargain-origin-price-popover"
                >
                    <batch-info-popover
                        v-if="hasBatchInfos"
                        :charge-item="item"
                        :is-edited-price="isEditedTotalPrice"
                        :origin-price="originTotalPrice"
                    >
                    </batch-info-popover>
                    <template v-else>
                        <p v-if="isEditedTotalPrice && !item.unitAdjustmentFeeLastModifiedByName">
                            议价前：<abc-money :value="originTotalPrice"></abc-money>
                        </p>
                        <template v-else-if="item.unitAdjustmentFeeLastModifiedByName">
                            <p>
                                <span class="title">原价</span>：
                                <abc-money :value="item.sourceTotalPrice"></abc-money>
                            </p>
                            <p>
                                <span class="title">议价人</span>：
                                {{ item.unitAdjustmentFeeLastModifiedByName }}
                            </p>
                        </template>
                    </template>
                </div>
            </div>
        </div>

        <div
            v-if="showRemarkCol"
            class="td remark no-right-border"
            :class="[tableExpandRemark ? 'width-86' : 'width-60']"
            data-cy="td-备注"
        >
            <div v-if="isChinesePrescriptionForm" class="td-cell is-disabled"></div>
            <goods-remark
                v-else-if="isMaterialOrGoods(item)"
                v-model="item.remark"
                class="no-border-input"
                max-length="50"
                placement="bottom-start"
                :readonly="readonly"
                :disabled="!itemIsUnCharged"
                :input-style="{
                    padding: '3px 4px', fontSize: '12px'
                }"
                :width="tableExpandRemark ? 86 : 60"
                :pr-form-item="item"
                :department-id="sellerDepartmentId"
                :tabindex="-1"
                :support-tag="!supportSelectPharmacy"
                :show-medicine-source-options="!supportSelectPharmacy"
                :can-edit-remark="true"
                @focus="handleFocusRemark"
                @blur="handleBlurRemark"
                @enter="enterEvent"
                @change="handlerChangeRemark"
                @changePharmacySource="$emit('change')"
            >
            </goods-remark>

            <goods-remark
                v-else
                v-model="item.specialRequirement"
                class="no-border-input"
                max-length="50"
                placement="bottom-start"
                :readonly="readonly"
                :disabled="!itemIsUnCharged"
                :input-style="{
                    padding: '3px 4px', fontSize: '12px'
                }"
                :width="tableExpandRemark ? 86 : 60"
                :department-id="sellerDepartmentId"
                :pr-form-item="item"
                :tabindex="-1"
                :support-tag="!supportSelectPharmacy"
                :show-medicine-source-options="!supportSelectPharmacy"
                :can-edit-remark="true"
                @focus="handleFocusRemark"
                @blur="handleBlurRemark"
                @enter="enterEvent"
                @change="handlerChangeRemark"
                @changePharmacySource="$emit('change')"
            >
            </goods-remark>
        </div>

        <div class="delete-item" data-cy="td-删除">
            <delete-icon v-if="canDelete" @delete="deleteItem"></delete-icon>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';

    import {
        isShortage, isDisabledGoods,
    } from 'utils/validate';
    import { debounce } from 'utils/lodash';
    import DeleteIcon from '../../layout/delete-icon/delete-icon';
    import SelectUsage from '../../layout/select-group/index.vue';
    import DepartmentDoctorAutocomplete from 'views/layout/department-doctor-autocomplete/index.vue';
    import NurseAutocomplete from 'views/layout/nurse-autocomplete/index.vue';
    import GoodsRemark from 'views/layout/goods-remark/index.vue';
    const BatchInfoPopover = () => import('../components/batch-info-popover.vue');

    import Common from './common';
    import {
        GoodsTypeEnum, GoodsSubTypeEnum,
    } from '@abc/constants';
    import GoodsModel from '../../common/goods-model.js';
    import {
        isChineseMedicine, isSupportDecimalsFourMedicine,
    } from 'src/filters/index';
    import { moneyDigit } from 'utils';
    import { formatMoney } from 'src/filters/index';
    import { getSelfPayPropTips } from 'views/common/social-info.js';
    import ToothSelector from '@/views-dentistry/outpatient/common/medical-record/tooth-selector.vue';
    import themeStyle from '@/styles/theme.module.scss';
    import { resetStockByPharmacyNo } from 'views/layout/prescription/utils.js';
    import { OutpatientChargeTypeEnum } from '@/common/constants/outpatient';
    import { PharmacyTypeEnum } from 'views/common/enum';
    import TraceCode, {
        ShebaoTraceableCodeDismountingFlagEnum, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import cloneDeep from 'lodash.clonedeep';
    import { isNotNull } from '@/utils';

    export default {
        name: 'MedicineTr',
        components: {
            DeleteIcon,
            SelectUsage,
            DepartmentDoctorAutocomplete,
            NurseAutocomplete,
            GoodsRemark,
            ToothSelector,
            BatchInfoPopover,
        },
        mixins: [Common],
        props: {
            chargeForm: Object,
            item: Object,

            isCharged: Boolean,
            canAddSpecialRequirement: Boolean,
            tableExpandRemark: Boolean,
            showCheckbox: Boolean,
            canDelete: Boolean,
            canEdit: Boolean,
            inputCalcLoading: Boolean,
            canSingleBargain: [Boolean, Number],
            index: Number,
            currentActiveIndex: Number,
            hasRepeat: Boolean,
            sellerDepartmentId: String,

            canViewCostPrice: {
                type: Boolean,
                default: false,
            },

            needCheckStock: {
                type: Boolean,
                default: true,
            },

            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            unitPriceTab: {
                type: Number,
                default: 0, // 0单价 1原价
            },

            chargeItemSupportDoctorNurse: {
                type: Boolean,
                default: false,
            },

            // 可复制牙位信息包含 allToothNos 所有牙位列表，maxWidth 牙位最宽宽度
            copyToothNosInfo: {
                type: Object,
                default: () => {},
            },

            supportSelectPharmacy: {
                type: Boolean,
                default: false,
            },

            // 自定义序号，优先级高于item上的sortIndex
            sortIndex: {
                type: Number,
            },
            readonly: Boolean,
            isDisabled: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                themeStyle,
                GoodsTypeEnum,
                GoodsSubTypeEnum,
                hoverUnitPrice: false,
                hoverTotalPrice: false,
                showLoading: '',
            };
        },
        computed: {
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
                'featureSupportRatioPrice',
            ]),
            ...mapGetters([
                'enableLocalPharmacyList',
                'userInfo',
                'chainBasic',
                'traceCodeConfig',
            ]),

            curSelectProductInfo() {
                return this.item?.productInfo || null;
            },

            formatEnableLocalPharmacyList() {
                if (!this.enableLocalPharmacyList) return [];

                if (!this.curSelectProductInfo) {
                    return this.enableLocalPharmacyList;
                }

                const {
                    packageUnit, pieceUnit, pharmacyGoodsStockList,
                } = this.curSelectProductInfo;

                return this.enableLocalPharmacyList.map((o) => {
                    if (!pharmacyGoodsStockList) {
                        return {
                            ...o,
                        };
                    }

                    const stock = pharmacyGoodsStockList.find((x) => x.pharmacyNo === o.no);
                    if (!stock) {
                        return {
                            ...o,
                            pharmacyTip: `0${packageUnit || pieceUnit}`,
                        };
                    }

                    let str = '';
                    if (packageUnit) {
                        str += `${stock.stockPackageCount || 0}${packageUnit}`;
                    }
                    if (pieceUnit) {
                        str += `${stock.stockPieceCount || 0}${pieceUnit}`;
                    }
                    return {
                        ...o,
                        pharmacyTip: str,
                    };
                });
            },

            showGoodShortId() {
                const { charge } = this.chainBasic || {};
                const { showGoodsShortId } = charge || {};
                return showGoodsShortId;
            },

            getUnitPriceConfig() {
                const {
                    productType,
                    productSubType,
                } = this.item;

                if (isChineseMedicine({
                    type: productType, subType: productSubType,
                }) || isSupportDecimalsFourMedicine({
                    type: productType, subType: productSubType,
                })) {
                    return {
                        formatLength: 4, supportZero: true, max: 9999999,
                    };
                }

                return {
                    formatLength: 2, supportZero: true, max: 9999999,
                };
            },
            doseCount: {
                get() {
                    return this.item.doseCount || 1;
                },
                set(val) {
                    this.item.doseCount = val;
                },
            },

            showWarnTips() {
                if (this.isCharged || this.itemIsReturned || !this.needCheckStock || this.isNoChargeItem) return false;
                return this.noGoodsInfoTips || this.disabledSellTips || isShortage(this.item).flag;
            },

            goodsModel() {
                return new GoodsModel(this.item.productInfo);
            },

            noGoodsInfoTips () {
                return this.goodsModel.noGoodsInfoTips;
            },
            disabledSellTips () {
                return this.goodsModel.disabledSellTips;
            },

            warnTips() {
                return isDisabledGoods(this.item).tips || isShortage(this.item).tips || '无库存';
            },

            showSelfPayProp() {
                return getSelfPayPropTips(this.item.productInfo, this.shebaoCardInfo);
            },
            // 判断是否是重庆地区
            isChongQing() {
                return this.$abcSocialSecurity.config.isChongqingGb;
            },
            // 判断是否是河南地区
            isHenan() {
                return this.$abcSocialSecurity.config.isHenan;
            },
            allowSwitchDisplayPrice() {
                return this.isChongQing || this.isHenan;
            },

            selectDepartmentDoctorName() {
                const {
                    departmentName,
                    doctorName,
                } = this.item.usageInfo || {};
                const _arr = [];
                if (doctorName) {
                    _arr.push(doctorName);
                }
                if (departmentName) {
                    _arr.push(departmentName);
                }
                return _arr.join(' - ');
            },
            canAddToothNo() {
                return this.viewDistributeConfig?.Cashier?.productFormCanAddToothNo;
            },
        },
        created() {
            this._debounceChange = debounce(this.debounceChange, 200, true);
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
        },
        methods: {
            showAddToothNo(item) {
                if (!this.canAddToothNo) return false;

                if ([
                    GoodsTypeEnum.MATERIAL,
                ].indexOf(item.productType) === -1) return false;

                return item.toothNos && item.toothNos.length || this.canEdit;
            },

            moneyDigit,
            formatMoney,
            isChineseMedicine,
            isSupportDecimalsFourMedicine,

            isMaterialOrGoods(item) {
                return [
                    GoodsTypeEnum.MATERIAL,
                    GoodsTypeEnum.GOODS,
                ].indexOf(item.productType) > -1;
            },

            changeActiveIndex(index) {
                this.$emit('changeActiveIndex', index);
            },

            chargedUnitPrice(item) {
                const {
                    productType,
                    productSubType,
                } = item;

                if (isChineseMedicine({
                    type: productType, subType: productSubType,
                })) {
                    return moneyDigit(item.unitPrice, 5);
                }

                if (isSupportDecimalsFourMedicine({
                    type: productType, subType: productSubType,
                })) {
                    return moneyDigit(item.unitPrice, 4);
                }

                return moneyDigit(item.unitPrice, 2);
            },
            chargedDisplayDiscountedTotalPrice(item) {
                let totalPrice = item.displayDiscountedTotalPrice ?? item.totalPrice;
                if (this.unitPriceTab) {
                    totalPrice = item.totalPrice;
                }
                return totalPrice;
            },
            // 重庆社保
            chargedUnitPriceTab(item) {
                const price = this.unitPriceTab ? item.unitPrice : (item.displayDiscountedUnitPrice ?? item.unitPrice);
                const {
                    productType,
                    productSubType,
                } = item;

                if (isChineseMedicine({
                    type: productType, subType: productSubType,
                })) {
                    return moneyDigit(price, 5);
                }

                if (isSupportDecimalsFourMedicine({
                    type: productType, subType: productSubType,
                })) {
                    return moneyDigit(price, 4);
                }

                return moneyDigit(price, 2);
            },

            changeUnitPrice() {
                this.showLoading = 'totalPrice';
                this.$set(this.item, 'expectedTotalPrice', null);
                this.$set(this.item, 'expectedUnitPrice', this.item.unitPrice);

                this.$emit('change');
                this._timer = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },
            handleChangeUnitCount(item) {
                this.$set(item, 'expectedTotalPriceRatio', null);
                this.$emit('change');
            },
            changeTotalPrice() {
                this.showLoading = 'unitPrice';
                this.$set(this.item, 'expectedUnitPrice', null);
                this.$set(this.item, 'expectedTotalPriceRatio', null);
                this.$set(this.item, 'expectedTotalPrice', this.item.totalPrice);
                this.$set(this.item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(this.item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);

                this.$emit('change');
                this._timer = setTimeout(() => {
                    this.showLoading = '';
                }, 1000);
            },

            adjustUnitPriceChange(item, val) {
                item.unitPrice = val;
                this.changeUnitPrice();
            },

            adjustPriceChange(item, val) {
                item.totalPrice = val;
                this.changeTotalPrice();
            },

            focusUnitPrice(item) {
                this.hoverUnitPrice = false;
                this.changeActiveIndex(item.index);
            },

            focusTotalPrice(item) {
                this.hoverTotalPrice = false;
                this.changeActiveIndex(item.index);
            },

            // blurHandler(item) {
            //     if(+item.unitCount === 0) {
            //         item.unitCount = '';
            //     }
            // },

            /**
             * @desc 改变选中项目，需要向上触发去重新算费
             * <AUTHOR>
             * @date 2019/04/03 20:48:53
             */
            changeHandler() {
                if (this.item.expectedTotalPrice) {
                    // https://www.tapd.cn/43780818/prong/stories/view/1143780818001064258
                    // 不是所有选择、反选都清空总价改成单价议价，当剂数变了之后才走这个逻辑
                    const doseCount = this.item.expectedDoseCount || this.item.doseCount;
                    if (doseCount !== this.item.originalDoseCount) {
                        this.item.expectedTotalPrice = null;
                        if (this.originUnitPrice !== +this.item.unitPrice) {
                            this.item.expectedUnitPrice = this.item.unitPrice;
                        }
                    }
                }
                this.$emit('change');

                // 重新计算追溯码拆零标识
                this.changeShebaoDismountingFlag();
            },
            /**
             * @desc 删除
             * <AUTHOR>
             * @date 2019/03/26 12:13:13
             */
            deleteItem() {
                this.$emit('deleteItem');
            },
            /**
             * @desc 开药量 是否允许输入 小数的判断
             * @desc 允许拆零的西药/中成药，当开药量单位选择 小单位 且 小单位是ml 时，可以输入 3位小数
             * @desc 允许拆零的商品，当开药量单位选择 小单位 时，可以输入 2位小数
             * <AUTHOR>
             * @date 2019/03/24 12:44:49
             */
            countConfig(item) {
                if (!item.productInfo) {
                    return {
                        max: 9999999, supportZero: true,
                    };
                }

                const isWM = item.productType === GoodsTypeEnum.MEDICINE; // 是西药/中成药
                const isMaterial = item.productType === GoodsTypeEnum.MATERIAL; // 是物资
                const dismounting = !!item.productInfo.dismounting; // 允许拆零
                const isFormUnit = item.unit === item.productInfo.pieceUnit; // 开药量单位选择小单位
                const formUnitIsML = item.productInfo.pieceUnit === 'ml'; // 开药小单位是ml
                const isGoods = item.productType === GoodsTypeEnum.GOODS;
                if (isWM && dismounting && isFormUnit && formUnitIsML) {
                    return {
                        formatLength: 3, max: 9999999, supportZero: true,
                    };
                }
                if (isGoods && dismounting && isFormUnit) {
                    return {
                        max: 9999999, supportZero: true, formatLength: 2,
                    };
                }
                if (isWM || isMaterial) {
                    return {
                        max: 9999999, supportZero: true, formatLength: 2,
                    };
                }
                return {
                    max: 9999999, supportZero: true,
                };

            },
            /**
             * @desc 显示输入框，包装单位和制剂单位可选（盒、片）
             * <AUTHOR>
             * @date 2018/04/13 11:01:33
             */
            unitArray(wm) {
                const res = [];
                const dismounting = wm.productInfo && wm.productInfo.dismounting;
                const pieceUnit = wm.productInfo && wm.productInfo.pieceUnit;
                const packageUnit = wm.productInfo && wm.productInfo.packageUnit;
                if (dismounting) {
                    if (pieceUnit) {
                        res.push({ 'name': pieceUnit });
                    }
                    if (packageUnit && packageUnit !== pieceUnit) {
                        res.push({ 'name': packageUnit });
                    }
                } else {
                    res.push({ 'name': packageUnit });
                }
                return res;
            },
            /**
             * @desc 选择西药开药量单位 由单位决定该药是否使用拆零
             * <AUTHOR>
             * @date 2018/07/10 15:40:20
             * @params unit
             * @params index
             */
            selectUnit(unit) {
                const { item } = this;
                item.unit = unit;
                const curUnitCount = item.productInfo && item.productInfo.type === GoodsTypeEnum.GOODS ?
                    item.unitCount : ~~item.unitCount;
                if (item.unit !== item.pieceUnit && curUnitCount !== item.unitCount) {
                    item.unitCount = '';
                }
                item.useDismounting = +(item.productInfo &&
                    item.productInfo.dismounting &&
                    item.unit === item.productInfo.pieceUnit &&
                    item.unit !== item.productInfo.packageUnit);
                const {
                    piecePrice,
                    packagePrice,
                } = item.productInfo;
                item.unitPrice = item.useDismounting ? piecePrice : packagePrice;

                if (this.item.expectedTotalPrice) {
                    this.item.expectedTotalPrice = null;
                    if (this.originUnitPrice !== +this.item.unitPrice) {
                        this.item.expectedUnitPrice = this.item.unitPrice;
                    }
                }

                /**
                 * 该需求要求修改单位后不清空已采集的追溯码
                 * 【【Bug转需求】追溯码：门诊、收费修改项目后追溯码不清空】
                 * https://www.tapd.cn/tapd_fe/22044681/story/detail/1122044681001092175
                 */
                // this.initTraceableCodeList();

                // 重新计算追溯码拆零标识
                this.changeShebaoDismountingFlag();

                this.$emit('change');
            },

            // 修改了单位需要清空追溯码
            initTraceableCodeList() {
                this.item.traceableCodeList = [];
            },

            /**
             * 重新计算追溯码拆零标识 以及 traceableCodeRule
             */
            changeShebaoDismountingFlag() {
                if (
                    this.traceCodeConfig?.collectionCheck &&
                    this.item.checked &&
                    this.item.unitCount &&
                    this.item.sourceItemType !== OutpatientChargeTypeEnum.NO_CHARGE &&
                    this.item.pharmacyType !== PharmacyTypeEnum.AIR_PHARMACY &&
                    TraceCode.formItemSupportTraceCode(this.item)
                ) {
                    TraceCode.getMaxTraceCountList({
                        dataList: [this.item],
                        scene: TraceCodeScenesEnum.PHARMACY,
                        isNeedTraceableCodeRule: false,
                    }).then((res) => {
                        const resultItemInfo = res[0] ?? {};
                        this.$set(this.item, 'traceableCodeRule', resultItemInfo.traceableCodeRule);
                        if (isNotNull(resultItemInfo.shebaoDismountingFlag)) {
                            this.$set(this.item, 'shebaoDismountingFlag', resultItemInfo.shebaoDismountingFlag);
                        }
                        // 如果开启了拆零不采集模式
                        if (TraceCode.hasEnableDismountingMode) {
                            if (
                                [ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_NO_EDIT, ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_EDIT].includes(this.item.shebaoDismountingFlag) &&
                                this.item.traceableCodeList?.length
                            ) {
                                // 如果变为了拆零，并且已经采集了追溯码，则暂存并清空
                                this.item._cacheTraceableCodeList = cloneDeep(this.item.traceableCodeList);
                                this.$set(this.item, 'traceableCodeList', []);
                            } else if (
                                [ShebaoTraceableCodeDismountingFlagEnum.NO_DISMOUNTING_NO_EDIT, ShebaoTraceableCodeDismountingFlagEnum.NO_DISMOUNTING_EDIT].includes(this.item.shebaoDismountingFlag) &&
                                this.item._cacheTraceableCodeList?.length
                            ) {
                                // 如果变为了不拆零，并且之前采集过追溯码，则恢复已采集的追溯码
                                this.$set(this.item, 'traceableCodeList', cloneDeep(this.item._cacheTraceableCodeList));
                                this.item._cacheTraceableCodeList = [];
                            }
                        }
                    }).catch((e) => {
                        console.error(e);
                    });
                }
            },

            /**
             * @desc 向上传递enter event事件，=》../table
             * <AUTHOR>
             * @date 2019/10/25 11:29:12
             */
            enterEvent(event) {
                this.$emit('enterEvent', event);
            },
            enterNext(event) {
                this.$emit('enterNext', event);
            },

            /**
             * @desc 删除煎法
             * <AUTHOR> Yang
             * @date 2020-08-14 10:34:06
             */
            clearSpecialRequirement(item) {
                item.specialRequirement = '';
            },

            handleFocusRemark() {
                this.$emit('expandRemark', true);
            },
            handleBlurRemark() {
                this.$emit('expandRemark', false);
            },
            validateUnitCount(value, callback) {
                const {
                    needExecutive, executedUnitCount, unit,
                } = this.item || {};
                if (!value) {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                    return;
                }
                if (needExecutive && executedUnitCount && value < executedUnitCount) {
                    callback({
                        validate: false,
                        message: `收费数量不能低于已执行数量（${executedUnitCount}${unit || '次'}）`,
                    });
                    return;
                }
                callback({
                    validate: true,
                });
            },

            changePharmacy(item) {
                const pharmacy = this.formatEnableLocalPharmacyList.find((it) => it.no === item.pharmacyNo);
                if (!pharmacy) return;
                item.pharmacyType = pharmacy.type;
                item.pharmacyNo = pharmacy.no;
                item.pharmacyName = pharmacy.name;
                resetStockByPharmacyNo(item);
                this.$emit('change');
            },

            /**
             * @desc 中药批量切换药房
             * <AUTHOR>
             * @date 2022-10-19 09:37:15
             */
            handleChangePharmacy(chargeForm) {
                const pharmacy = this.formatEnableLocalPharmacyList.find((it) => it.no === chargeForm.pharmacyNo);
                if (!pharmacy) return;
                chargeForm.pharmacyType = pharmacy.type;
                chargeForm.pharmacyNo = pharmacy.no;
                chargeForm.pharmacyName = pharmacy.name;
                chargeForm.chargeFormItems.forEach((item) => {
                    item.pharmacyType = pharmacy.type;
                    item.pharmacyNo = pharmacy.no;
                    item.pharmacyName = pharmacy.name;
                    resetStockByPharmacyNo(item);
                });
                this.$emit('change');
            },
            displayTotalPriceRatio(item) {
                const { totalPriceRatio } = item;
                if (!totalPriceRatio || totalPriceRatio > 1) return '';
                return Math.round((totalPriceRatio ?? 1) * 100);
            },
            inputPriceRadioHandler(item, val) {
                this.$set(item, 'totalPriceRatio', val / 100);
            },
            changeTotalPriceRatio(item) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPriceRatio', item.totalPriceRatio);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.$emit('change');
            },
            changePriceRadio(item, val) {
                this.$set(item, 'expectedUnitPrice', null);
                this.$set(item, 'expectedTotalPrice', null);
                this.$set(item, 'totalPriceRatio', val);
                this.$set(item, 'expectedTotalPriceRatio', val);
                this.$set(item, 'unitAdjustmentFeeLastModifiedBy', this.userInfo.id);
                this.$set(item, 'unitAdjustmentFeeLastModifiedByName', this.userInfo.name);
                this.$emit('change');
            },

            handlerChangeRemark(val) {
                if (this.itemIsUnCharged) return;
                this.$emit('updateRemark', val);
            },
            showMedicalFeeGrade(item) {
                const { productInfo } = item;
                const {
                    shebaoPayMode, medicalFeeGrade, shebaoNationalCode,
                } = productInfo || {};

                return (shebaoPayMode === 0 || (!shebaoPayMode && shebaoNationalCode)) && medicalFeeGrade;
            },
            /**
             * 西药和西成药才展示限制级和精麻毒标签
             * @return {boolean}
             */
            showAntibioticAndDangerIngredient(item) {
                return item.productType === GoodsTypeEnum.MEDICINE &&
                    (
                        item.productSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine ||
                        item.productSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM
                    );
            },
        },
    };
</script>
