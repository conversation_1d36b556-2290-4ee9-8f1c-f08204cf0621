<template>
    <div>
        <abc-table
            v-if="showEmpty || (goodsListTableData && goodsListTableData.length)"
            custom
            :support-delete-tr="!disabled"
            type="excel"
            :table-min-height="40"
            style="overflow: hidden;"
        >
            <abc-table-body>
                <template v-if="goodsListTableData && goodsListTableData.length">
                    <abc-table-tr v-for="(goodItem,index) in goodsListTableData" :key="`range${index}_${goodItem.id}`" @delete-tr="handleDelete(goodItem, index)">
                        <abc-table-td :custom-td="true">
                            <abc-table-cell>
                                <div style="flex: 1; overflow: hidden;" class="ellipsis">
                                    <span class="product-item-info-wrapper">
                                        <template v-if="goodItem.type === 2">
                                            <span v-if="showTypeName && !showPoints" style="display: inline-block; min-width: 28px;">单品</span>
                                            <template v-if="goodItem?.goods">
                                                <span
                                                    v-abc-goods-hover-popper="{
                                                        goods: goodItem.goods,
                                                        showF1: goodItem.goods?.type === GoodsTypeEnum.MEDICINE,
                                                        showShebaoCode: true
                                                    }"
                                                    class="good-name"
                                                    :class="{
                                                        'has-much-info': showLabeInput && showDiscountRadio || isPresentRights && showLabeInput,
                                                        'is-discount': isDiscountRights,
                                                        'is-disabled': goodItem.goods && goodItem.goods?.disableSell || goodItem.goods && goodItem.goods?.status === GoodsItemStatusEnum.HAS_DELETED
                                                    }"
                                                > {{ goodItem.goods | goodsFullName }}
                                                    <abc-text theme="gray-light" size="mini">{{ goodItem.goods | goodsSpec }} {{ goodItem.goods?.manufacturer || '' }}</abc-text>
                                                    <abc-button
                                                        v-if="goodItem.goods?.disableSell && goodItem.goods?.status !== GoodsItemStatusEnum.HAS_DELETED"
                                                        type="ghost"
                                                        no-border-radius
                                                        class="goods-status"
                                                        size="small"
                                                        disabled
                                                    >已停用</abc-button>
                                                    <abc-button
                                                        v-if="goodItem.goods?.status === GoodsItemStatusEnum.HAS_DELETED"
                                                        class="goods-status"
                                                        no-border-radius
                                                        type="ghost"
                                                        disabled
                                                        size="small"
                                                        disable
                                                    >已删除</abc-button>
                                                </span>
                                                <abc-popover
                                                    v-if="goodItem.isConflict"
                                                    trigger="hover"
                                                    style="display: inline-flex;"
                                                    theme="yellow"
                                                >
                                                    <abc-icon slot="reference" size="14" icon="Attention"></abc-icon>
                                                    <p>{{ goodItem.conflictMessage }}</p>
                                                </abc-popover>
                                            </template>
                                            <abc-text v-else>{{ goodItem.name || goodItem.displayName }}</abc-text>
                                        </template>
                                        <template v-else>
                                            <span v-if="showTypeName && !showPoints" style="display: inline-block; min-width: 28px;">分类</span>
                                            <span class="good-name" :class="{ 'has-much-info': showLabeInput && showDiscountRadio }" :title="goodItem.name">
                                                <template v-if="showPoints">
                                                    {{ goodItem.goodsName }}
                                                </template>
                                                <template v-else>
                                                    <template v-if="goodItem.name">
                                                        {{ transGoodsClassificationName(goodItem.name || goodItem.goodsCMSpec) }}
                                                    </template>

                                                    <template v-else>
                                                        <abc-tips
                                                            icon
                                                            theme="warning"
                                                        >
                                                            本项已删除
                                                        </abc-tips>
                                                    </template>
                                                </template>
                                                <abc-popover
                                                    v-if="goodItem.isConflict"
                                                    trigger="hover"
                                                    style="display: inline-flex;"
                                                    theme="yellow"
                                                >
                                                    <abc-icon slot="reference" :size="14" icon="Attention"></abc-icon>
                                                    <p>{{ goodItem.conflictMessage }}</p>
                                                </abc-popover>
                                            </span>
                                        </template>
                                    </span>
                                </div>
                            </abc-table-cell>
                        </abc-table-td>
                        <abc-table-td
                            v-if="showExceptItems || showDiscountRadio || isPresentRights"
                            :width="tdWidth"
                            :custom-td="true"
                        >
                            <abc-table-cell :justify="onlyShowException ? 'center' : 'flex-end'">
                                <div
                                    :style="{
                                        'display': 'flex',
                                        'flex-shrink': 0,
                                        'justify-content': 'flex-end',
                                        'padding-right': onlyShowException ? '0' : '8px'
                                    }"
                                >
                                    <abc-popover
                                        v-if="showExceptItems && goodItem.type === GoodsTypeEnum.MEDICINE && !noExceptItems(goodItem)"
                                        :disabled="!showExceptItemsPopover || !goodItem?.exceptItems?.length"
                                        append-to-body
                                        trigger="hover"
                                        placement="top"
                                        theme="yellow"
                                        :popper-style="{
                                            padding: 0,
                                        }"
                                    >
                                        <div slot="reference">
                                            <abc-button
                                                type="text"
                                                :disabled="disabled"
                                                @click="openExceptGoodsListDialog(goodItem, index)"
                                            >
                                                例外{{ goodItem.exceptItems ? goodItem.exceptItems.length : 0 }}
                                            </abc-button>
                                        </div>
                                        <abc-flex vertical style="max-height: 280px; padding: 8px; overflow: auto;">
                                            <abc-text v-for="(item, idx) of exceptItemsPopover(goodItem.exceptItems)" :key="idx">
                                                {{ item }}
                                            </abc-text>
                                        </abc-flex>
                                    </abc-popover>
                                    <abc-text v-else-if="showDiscountRadio && goodItem.type === 2" :title="getGoodsPackagePrice(goodItem, true)" theme="gray-light">
                                        {{ `原价：${$t('currencySymbol')} ${getGoodsPackagePrice(goodItem, true)}` }}
                                    </abc-text>
                                </div>
                                <abc-flex
                                    align="center"
                                    justify="center"
                                    wrap="nowrap"
                                >
                                    <abc-radio-group
                                        v-if="showDiscountRadio"
                                        v-model="goodItem.discountType"
                                        style="display: flex; align-items: center;"
                                        @change="handleInput(goodItem)"
                                    >
                                        <abc-radio
                                            :label="0"
                                            class="span-bold"
                                            :disabled="isMemberTypeInDiscount || disabled"
                                        >
                                            折扣
                                        </abc-radio>
                                        <abc-radio
                                            :disabled="goodItem.type !== 2 || isMemberTypeInDiscount || disabled"
                                            :label="1"
                                            class="span-bold"
                                        >
                                            特价
                                        </abc-radio>
                                    </abc-radio-group>
                                    <abc-radio-group
                                        v-if="isPresentRights"
                                        v-model="goodItem.isGivingLimit"
                                        style="display: flex; align-items: center;"
                                        @change="handleLimitTypeChange(goodItem.isGivingLimit,index)"
                                    >
                                        <abc-radio
                                            :label="1"
                                            class="span-bold"
                                            :disabled="disabled"
                                        >
                                            固定次
                                        </abc-radio>
                                        <abc-radio
                                            :label="0"
                                            :disabled="disabled"
                                            class="span-bold"
                                        >
                                            无限次
                                        </abc-radio>
                                    </abc-radio-group>
                                </abc-flex>
                            </abc-table-cell>
                        </abc-table-td>
                        <abc-table-td v-if="showLabeInput || showPoints" :custom-td="true" :width="88">
                            <abc-table-cell v-if="showLabeInput" style="padding: 0;">
                                <abc-form-item
                                    v-if="isPresentRights"
                                    :validate-event="validateGivingCount"
                                    :validate-params="goodItem"
                                >
                                    <abc-input
                                        v-model.number="goodItem.givingCount"
                                        v-abc-focus-selected
                                        type="number"
                                        :input-custom-style="{
                                            'text-align': 'center','width': '100%'
                                        }"
                                        :max-length="5"
                                        size="small"
                                        :disabled="!goodItem.isGivingLimit || disabled"
                                        :config="{
                                            max: 10000, formatLength: 0
                                        }"
                                        @focus="handleFocus(index)"
                                        @input="handlePresentInput(goodItem)"
                                        @blur="handlePresentBlur(goodItem)"
                                    >
                                        <template slot="appendInner">
                                            <span v-if="isPresentRights">次</span>
                                            <span v-else>
                                                <span v-if="!goodItem.discountType">折</span>
                                                <span v-else>元</span>
                                            </span>
                                        </template>
                                    </abc-input>
                                </abc-form-item>
                                <template v-if="!isPresentRights">
                                    <abc-form-item
                                        v-if="goodItem.discountType"
                                        :validate-event="validateDiscount"
                                        :validate-params="goodItem"
                                        style="margin-bottom: 0;"
                                    >
                                        <abc-popover
                                            :disabled="goodItem.type !== 2"
                                            trigger="focus"
                                            theme="yellow"
                                            style="height: 100%;"
                                            placement="top-start"
                                            append-to-body
                                        >
                                            <template slot="reference">
                                                <abc-input
                                                    v-model.number="goodItem.discount"
                                                    v-abc-focus-selected
                                                    size="small"
                                                    :disabled="isMemberTypeInDiscount || disabled"
                                                    :class="{ 'is-edit': getGoodsPackagePrice(goodItem) && goodItem.discount > goodItem.goods.packagePrice }"
                                                    type="number"
                                                    width="100%"
                                                    :input-custom-style="{
                                                        'text-align': 'center','width': '100%'
                                                    }"
                                                    :config="{
                                                        formatLength: goodItem.goodsSubType === 2 ? 4 : 2, supportZero: false, max: 9999999
                                                    }"
                                                    @focus="handleFocus(index)"
                                                    @input="handleDiscountInput(goodItem)"
                                                    @blur="handleDiscountBlur(goodItem)"
                                                >
                                                    <template slot="appendInner">
                                                        <span v-if="isPresentRights">次</span>
                                                        <span v-else>
                                                            <span v-if="!goodItem.discountType">折</span>
                                                            <span v-else>元</span>
                                                        </span>
                                                    </template>
                                                </abc-input>
                                            </template>
                                            <div>
                                                {{ (getGoodsPackagePrice(goodItem) && goodItem.discount > goodItem.goods?.packagePrice) ? '特价金额大于原价' : `折扣比例：${getDiscountRatio(goodItem) }` }}
                                            </div>
                                        </abc-popover>
                                    </abc-form-item>
                                    <abc-form-item
                                        v-else
                                        :validate-event="validateDiscountType"
                                        :validate-params="goodItem"
                                        style="margin-bottom: 0;"
                                    >
                                        <abc-popover
                                            :disabled="goodItem.type !== 2"
                                            trigger="focus"
                                            theme="yellow"
                                            style="height: 100%;"
                                            placement="top-start"
                                            append-to-body
                                        >
                                            <template slot="reference">
                                                <abc-input
                                                    v-model.number="goodItem.discount"
                                                    v-abc-focus-selected
                                                    :disabled="isMemberTypeInDiscount || disabled"
                                                    :input-custom-style="{
                                                        'text-align': 'center','width': '100%'
                                                    }"
                                                    :width="80"
                                                    type="number"
                                                    size="small"
                                                    :config="{
                                                        max: 10, formatLength: 2, supportZero: true
                                                    }"
                                                    @focus="handleFocus(index)"
                                                    @input="handleInput(goodItem)"
                                                    @blur="handleDiscountPriceBlur(goodItem)"
                                                >
                                                    <template slot="appendInner">
                                                        <span v-if="isPresentRights">次</span>
                                                        <span v-else>
                                                            <span v-if="!goodItem.discountType">折</span>
                                                            <span v-else>元</span>
                                                        </span>
                                                    </template>
                                                </abc-input>
                                            </template>
                                            <div>
                                                {{ `折后价：${$t('currencySymbol')} ${getDiscountPrice(goodItem) }` }}
                                            </div>
                                        </abc-popover>
                                    </abc-form-item>
                                </template>
                            </abc-table-cell>
                            <template v-if="showPoints">
                                <abc-form-item :validate-event="validateDeductionUnitCount" :validate-params="goodItem" style="margin-bottom: 0;">
                                    <abc-input
                                        ref="pointInputRef"
                                        v-model.number="goodItem.deductionUnitCount"
                                        v-abc-focus-selected
                                        type="number"
                                        size="small"
                                        :input-custom-style="{ 'text-align': 'center' }"
                                        width="88"
                                        :disabled="disabled"
                                        :max-length="5"
                                        :config="{
                                            max: 99999, formatLength: 0
                                        }"
                                        @input="handleDeductionUnitCount(goodItem)"
                                        @blur="handleDeductionUnitCount(goodItem)"
                                    ></abc-input>
                                </abc-form-item>
                                <template slot="appendInner">
                                    积分
                                </template>
                            </template>
                        </abc-table-td>
                    </abc-table-tr>
                </template>
                <template v-else-if="showEmpty">
                    <abc-content-empty
                        v-if="showEmpty"
                        size="small"
                        top="50%"
                        style="height: 127px;"
                    >
                    </abc-content-empty>
                </template>
            </abc-table-body>
            <abc-table-footer v-if="filterGoodsList && filterGoodsList.length">
                <abc-table-td custom-td style="padding: 12px; font-size: 14px;">
                    <abc-pagination
                        size="small"
                        :count="filterGoodsList.length"
                        :pagination-params="pageParams"
                        show-total-page
                        :current-page="pageParams.pageIndex"
                        @current-change="changePageHandler"
                    >
                    </abc-pagination>
                </abc-table-td>
            </abc-table-footer>
        </abc-table>

        <!--选择例外的商品-->
        <select-goods-item-list
            v-if="showExceptGoodsListDialog"
            v-model="showExceptGoodsListDialog"
            :show-points="showPoints"
            :disabled="disabled"
            :is-expect="showExceptGoodsListDialog"
            :product-types="exceptGoodsType"
            :sub-type="exceptGoodsSubType"
            :c-m-spec="exceptGoodsCMSpec"
            :custom-type-id-list="exceptGoodsCustomTypeIdList"
            :select-goods-list="selectedExceptGoodsList"
            :show-search-types="false"
            @change="changeExceptGoodsList"
        ></select-goods-item-list>
        <!--选择例外的医生-->
        <select-doctors-dialog
            v-if="showExceptDoctorsListDialog"
            v-model="showExceptDoctorsListDialog"
            :disabled="disabled"
            :goods-id="goodsId"
            :select-goods-list="selectedExceptGoodsList"
            :registration-category="registrationCategory"
            @change="changeExceptGoodsList"
        >
        </select-doctors-dialog>
    </div>
</template>

<script>
    import { isEqual } from 'utils/lodash';
    import {
        goodsSpec, goodsFullName,
    } from '@/filters';
    import {
        GoodsItemStatusEnum, GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import SelectDoctorsDialog from 'views/marketing/components/select-doctors-dlalog.vue';
    import SelectGoodsItemList from 'views/marketing/components/select-goods-type-list/select-goods-item-list.vue';
    import GoodsTransferDialog from 'components/dialog-goods-transfer';
    import { mapGetters } from 'vuex';
    import { OPT_TYPES } from 'views/marketing/hooks/use-data-operation-tracker';
    import clone from 'utils/clone.js';

    export default {
        name: 'SelectGoodsTypeTable',
        components: {
            SelectGoodsItemList,
            SelectDoctorsDialog,
        },
        props: {
            // 是否是会员卡或者折扣活动的活动类型
            isMemberOrDiscount: {
                type: Boolean,
                default: false,
            },
            showPoints: {
                type: Boolean,
                default: false,
            },
            // 是否是折扣活动处的会员活动类型
            isMemberTypeInDiscount: {
                type: Boolean,
                default: false,
            },

            // 是否是营销卡项 - 控制是否展示例外
            isMarketingCard: {
                type: Boolean,
                default: true,
            },
            // 是否是服务赠送权益-单选服务
            isPresentRights: {
                type: Boolean,
                default: false,
            },
            // 是否是服务赠送权益-单选服务
            isPresentRightsGoods: {
                type: Boolean,
                default: false,
            },
            // 是否展示单品，分类名
            showTypeName: {
                type: Boolean,
                default: false,
            },
            // 为true, 展示单品和分类，为false 只展示单品
            onlyShowTypeList: {
                type: Boolean,
                default: true,
            },
            // 是否展示折扣输入框 - 默认不展示
            showLabeInput: {
                type: Boolean,
                default: false,
            },
            // 是否展示特价单选按钮- 默认不展示
            showDiscountRadio: {
                type: Boolean,
                default: false,
            },
            isDiscountRights: {
                type: Boolean,
                default: false,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            showExceptItems: {
                type: Boolean,
                default: false,
            },
            goodsList: {
                type: Array,
                default: () => [],
            },
            onlyOriginalPrice: {
                type: [ Number, Boolean ],
                default: true,
            },
            showError: Boolean,
            selectGoodsType: {
                type: Array,
                default: () => {
                    return [ GoodsTypeEnum.MEDICINE,
                             GoodsTypeEnum.MATERIAL,
                             GoodsTypeEnum.EXAMINATION,
                             GoodsTypeEnum.TREATMENT,
                             GoodsTypeEnum.OTHER,
                             GoodsTypeEnum.GOODS,
                             GoodsTypeEnum.EYEGLASSES,
                             GoodsTypeEnum.COMPOSE,
                             GoodsTypeEnum.SURGERY];
                },
            },
            onlyShowException: {
                type: Boolean,
                default: false,
            },
            hideExceptAndPrice: {
                type: Boolean,
                default: false,
            },
            showEmpty: {
                type: Boolean,
                default: false,
            },
            filterParams: {
                type: Object,
                default: () => {},
            },
            filterFunc: {
                type: Function,
                default: null,
            },
            hideGoodsList: {
                type: Boolean,
                default: false,
            },
            showExceptItemsPopover: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                GoodsItemStatusEnum,
                GoodsTypeEnum,
                showGoodsListDialog: false,
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                // 参与范围 选择例外数据参数
                showExceptGoodsListDialog: false,
                showExceptDoctorsListDialog: false, // 参与医生
                exceptIndex: 0,
                exceptGoodsType: [],
                exceptGoodsSubType: '',
                exceptGoodsTypeId: [],
                exceptGoodsCMSpec: '',
                exceptGoodsCustomTypeIdList: [],
                selectedExceptGoodsList: [],
                currentGoodsList: [],
                localGoodsList: this.goodsList,
                showDiscountInformation: false,
                activeIndex: -1,
                goodsId: '',

                registrationCategory: 0,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters([
                'isChainAdmin',
                'isSingleStore',
            ]),
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            filterGoodsList() {
                if (this.filterFunc) {
                    return this.filterFunc(this.currentGoodsList, this.filterParams);
                }
                return this.currentGoodsList || [];
            },
            // 当前页商品列表
            goodsListTableData() {
                let startIndex = this.pageParams.pageIndex * this.pageParams.pageSize;
                let endIndex = startIndex + this.pageParams.pageSize;
                const len = this.filterGoodsList.length;
                if (endIndex > len) {
                    endIndex = len;
                }
                if (startIndex < 0 || startIndex > len) {
                    startIndex = 0;
                }
                return this.filterGoodsList.slice(startIndex, endIndex);
            },
            maxPage() {
                const len = this.currentGoodsList && this.currentGoodsList.length || 0;
                return Math.ceil(len / this.pageParams.pageSize);
            },
            tableRenderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: '',
                        },
                    ],
                };
            },
            tdWidth() {
                if (this.onlyShowException) {
                    return 88;
                } if (this.hideExceptAndPrice) {
                    return 172;
                }
                return null;
            },
        },
        watch: {
            goodsList: {
                handler(val, oldVal) {
                    if (!isEqual(val, oldVal)) {
                        if (this.hideGoodsList) {
                            this.currentGoodsList = val.filter((item) => item.type !== 2);
                        } else {
                            this.currentGoodsList = (val);
                        }
                    }
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            goodsSpec,
            goodsFullName,
            getDiscountRatio(goodItem) {
                const {
                    goods = {}, discount = 0,
                } = goodItem;
                const { packagePrice = 0 } = goods;
                // 避免x/0出现Infinity和NaN
                if (packagePrice === 0) return '-%';
                return `${Number((discount / packagePrice) * 100).toFixed(2)}%`;
            },
            getDiscountPrice(goodItem) {
                const {
                    goods = {}, discount = 0,
                } = goodItem;
                const { packagePrice = 0 } = goods;
                return `${Number((discount * (10 * packagePrice)) / 100).toFixed(2)}`;
            },
            getGoodsPackagePrice(goodItem, needFormat = false) {
                let packagePrice = goodItem?.goods?.packagePrice || 0;
                if (needFormat) {
                    packagePrice = packagePrice.toFixed(2);
                }
                return packagePrice;
            },
            validateGivingCount(val, callback, params) {
                if (params.isGivingLimit && !val) {
                    callback({
                        validate: false,
                        message: '请填写赠送次数',
                    });
                }
                callback({
                    validate: true,
                });
            },
            validateDeductionUnitCount(val, callback) {
                if (!val) {
                    callback({
                        validate: false,
                        message: '请填写积分',
                    });
                }
                callback({
                    validate: true,
                });
            },

            changePageHandler(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
            },
            validateDiscount(val, callback, params) {
                if (params.discountType && !val) {
                    callback({
                        validate: false,
                        message: '请填写特价',
                    });
                }
                callback({
                    validate: true,
                });

            },
            validateDiscountType(val, callback, params) {
                if (!val) {
                    if (!params.discountType) {
                        callback({
                            validate: false,
                            message: '请填写折扣',
                        });
                    } else {
                        callback({
                            validate: false,
                            message: '请填写特价',
                        });
                    }
                }
                callback({
                    validate: true,
                });
            },
            handleLimitTypeChange(val, index) {
                if (!val) {
                    this.currentGoodsList[index].givingCount = '';
                }
                this.$emit('handleOptTracker',this.currentGoodsList[index],OPT_TYPES.UPDATE);
                this.$emit('update:goodsList', this.currentGoodsList);
            },

            handleInput(goodItem) {
                this.$emit('handleOptTracker',goodItem,OPT_TYPES.UPDATE);
                this.$emit('update:goodsList', this.currentGoodsList);
            },

            handlePresentInput(goodItem) {
                this.$emit('handleOptTracker',goodItem,OPT_TYPES.UPDATE);
                this.$emit('update:goodsList', this.currentGoodsList);
            },
            handlePresentBlur() {
                this.showDiscountInformation = false;
                this.$emit('update:goodsList', this.currentGoodsList);
            },
            handleDeductionUnitCount(goodItem) {
                this.$emit('handleOptTracker',goodItem,OPT_TYPES.UPDATE);
                this.$emit('update:goodsList', this.currentGoodsList);
            },
            handleFocus(index) {
                this.activeIndex = index;
                this.showDiscountInformation = true;
            },
            handleDiscountInput(goodItem) {
                this.$emit('handleOptTracker',goodItem,OPT_TYPES.UPDATE);
                this.$emit('update:goodsList', this.currentGoodsList);
            },
            handleDiscountBlur() {
                this.showDiscountInformation = false;
                this.$emit('update:goodsList', this.currentGoodsList);
            },
            handleDiscountPriceBlur() {
                this.showDiscountInformation = false;
            },
            /**
             * @desc 快递费煎药费不显示例外
             * <AUTHOR>
             * @date 2020/4/21
             */
            noExceptItems(item) {
                return item.goodsType === 13 || item.goodsType === 14;
            },
            /**
             * @desc 选择活动范围
             * <AUTHOR>
             * @date 2020/4/18
             */
            changeGoodsList(list) {
                if (this.showPoints) {
                    const focusIndex = this.currentGoodsList?.length || 0;
                    this.timer = setTimeout(() => {
                        this.$refs.pointInputRef[focusIndex]?.focus();
                    }, 50);
                }
                this.currentGoodsList = list;
                this.currentGoodsList.forEach((item) => {
                    if (item.discountType === undefined) {
                        this.$set(item, 'discountType', 0);
                    }
                    if (this.isPresentRights) {
                        if (item.isGivingLimit === undefined) {
                            this.$set(item, 'isGivingLimit', 1);
                        }
                    }
                });
                this.$emit('update:goodsList', this.currentGoodsList);

                this.pageParams.pageIndex = 0;
                this.goodsError = false;
            },
            /**
             * @desc 删除某个项目或者某种类型
             * <AUTHOR>
             * @date 2020/4/21
             */
            handleDelete(trData, index) {
                // 有filterFunc说明是过滤了的
                if (this.filterFunc) {
                    const idx = this.currentGoodsList.findIndex((it) => {
                        if (it.id) return it.id === trData.id;
                        return it.goodsId === trData.goodsId;
                    });
                    const splicedItem = this.currentGoodsList.splice(idx, 1);
                    splicedItem[0] && this.$emit('handleOptTracker',splicedItem[0],OPT_TYPES.DELETE);
                } else {
                    this.$emit('handleOptTracker',this.currentGoodsList[this.pageParams.pageIndex * this.pageParams.pageSize + index],OPT_TYPES.DELETE);
                    this.currentGoodsList.splice(this.pageParams.pageIndex * this.pageParams.pageSize + index, 1);
                }

                if (this.goodsListTableData.length === 0 && this.pageParams.pageIndex > 0) {
                    this.pageParams.pageIndex--;
                }
                this.$emit('update:goodsList', this.currentGoodsList);
            },

            /**
             * @desc 打开例外窗口
             * 需要记录当前分类，type subType cMSpec确定例外数据范围
             * <AUTHOR>
             * @date 2020/4/18
             */
            openExceptGoodsListDialog(item) {
                if (!item.originExceptItems) {
                    item.originExceptItems = clone(item.exceptItems) || [];
                }
                this.exceptGoodsTypeId = Number(item.goodsId) === item.goodsType ? [] : [item.goodsId];
                this.exceptGoodsSubType = item.goodsSubType;
                this.exceptGoodsCMSpec = item.goodsCMSpec;
                this.selectedExceptGoodsList = item.exceptItems || [];
                this.exceptGoodsType = [ item.goodsType ];
                this.exceptGoodsCustomTypeIdList = item.customTypeId ? [ item.customTypeId ] : [];
                this.exceptIndex = this.currentGoodsList.findIndex((it) => it.goodsIdKey === item.goodsIdKey && it.name === item.name);
                // 挂号费，微信沟通 [GoodsTypeIdEnum.REGISTRATION, GoodsTypeIdEnum.TREAT_ONLINE]
                if ([GoodsTypeIdEnum.REGISTRATION, GoodsTypeIdEnum.TREAT_ONLINE].includes(Number(item.goodsId))) {
                    this.goodsId = item.goodsId;

                    if (item.goodsSubType === 0) {
                        this.registrationCategory = 0;
                    } else if (item.goodsSubType === 3) {
                        this.registrationCategory = 2;
                    } else if (item.goodsSubType === 2) {
                        this.registrationCategory = 1;
                    } else {
                        this.registrationCategory = item.goodsSubType;
                    }

                    this.showExceptDoctorsListDialog = true;
                    return;
                }
                if (this.viewDistributeConfig.Marketing.usePharmacyGoodsSelect) {
                    this.openGoodsPharmacyDialog();
                    return;
                }
                this.showExceptGoodsListDialog = true;
            },

            openGoodsPharmacyDialog() {
                const selectedList = this.selectedExceptGoodsList.map((item) => ({
                    ...item,
                    displayName: item.goods?.displayName || item.displayName,
                    displaySpec: item.goods?.displaySpec || item.displaySpec,
                    id: item.goods?.id || item.goodsId,
                }));

                new GoodsTransferDialog({
                    supportGoodsTypeIdMap: this.viewDistributeConfig.Inventory.supportGoodsTypeIdMap,
                    profitClassificationOptions: this.profitClassificationList,
                    goodsAutocompleteProps: {
                        currentClinic: this.currentClinic,
                        isSingleStore: this.isSingleStore,
                        isChainAdmin: this.isChainAdmin,
                    },
                    // 过滤参数
                    hiddenGoodsType: true,
                    goodsType: this.exceptGoodsType.join(','),
                    goodsTypeId: this.exceptGoodsTypeId,
                    goodsCustomType: this.exceptGoodsCustomTypeIdList,

                    onConfirm: this.changeExceptGoodsListPharmacy,
                    selectedList,
                    confirmButtonDisabled: false,
                }).generateDialogAsync({
                    parent: this,
                });
            },

            /**
             * @desc  更新分类项目中的例外项目 非药店
             * <AUTHOR>
             * @date 2020/4/18
             */
            changeExceptGoodsList(list = []) {
                const item = this.currentGoodsList[this.exceptIndex];
                const newList = list.map((it) => {
                    return {
                        ...it,
                        goodsCMSpec: item.goodsCMSpec,
                        customTypeId: item.customTypeId,
                    };
                });

                // 找出新增项，设置 optType 为 ADD
                const addedItems = newList.filter((newItem) =>
                    !(item.originExceptItems ?? []).some((currentItem) => currentItem.goodsId === newItem.goodsId),
                ).map((newItem) => ({
                    ...newItem,
                    optType: OPT_TYPES.ADD,
                    id: null,
                }));

                // 找出被删除的项，设置 optType 为 DELETE
                const deletedItems = (item.originExceptItems ?? []).filter((currentItem) =>
                    !newList.some((newItem) => newItem.goodsId === currentItem.goodsId),
                ).map((_item) => ({
                    ..._item,
                    optType: OPT_TYPES.DELETE,
                }));

                // 合并结果，只包含新增项和删除项
                const finalList = [...addedItems, ...deletedItems];
                this.currentGoodsList[this.exceptIndex].exceptItems = newList;
                this.currentGoodsList[this.exceptIndex].exceptOperationTrackerItems = finalList;
                this.$emit('handleOptTracker', this.currentGoodsList[this.exceptIndex], OPT_TYPES.UPDATE);
                this.$emit('except-goods-change', finalList, this.exceptIndex);
                this.$emit('update:goodsList', this.currentGoodsList);
            },
            // 药店
            changeExceptGoodsListPharmacy(list = []) {
                const item = this.currentGoodsList[this.exceptIndex];
                const newGoodsList = [...this.currentGoodsList];
                const newList = list.map((it) => {
                    return {
                        ...it,
                        goodsCMSpec: item.goodsCMSpec,
                        customTypeId: item.customTypeId,
                        type: 2,
                    };
                });
                // 找出新增项，设置 optType 为 ADD
                const addedItems = newList.filter((newItem) =>
                    !(item.originExceptItems ?? []).some((currentItem) => currentItem.goodsId === newItem.goodsId),
                ).map((newItem) => ({
                    ...newItem,
                    optType: OPT_TYPES.ADD,
                    id: null,
                }));

                // 找出被删除的项，设置 optType 为 DELETE
                const deletedItems = (item.originExceptItems ?? []).filter((currentItem) =>
                    !newList.some((newItem) => newItem.goodsId === currentItem.goodsId),
                ).map((_item) => ({
                    ..._item,
                    optType: OPT_TYPES.DELETE,
                }));

                // 合并结果，只包含新增项和删除项
                const finalList = [...addedItems, ...deletedItems];
                newGoodsList[this.exceptIndex].exceptItems = newList;
                newGoodsList[this.exceptIndex].exceptOperationTrackerItems = finalList;
                this.currentGoodsList = newGoodsList;
                this.$emit('handleOptTracker', this.currentGoodsList[this.exceptIndex], OPT_TYPES.UPDATE);
                this.$emit('except-goods-change', newList, this.exceptIndex);
                this.$emit('update:goodsList', this.currentGoodsList);
            },
            showInformationPopover(goodItem,index) {
                return this.showDiscountInformation && this.activeIndex === index && goodItem.discount && this.getGoodsPackagePrice(goodItem);
            },
            exceptItemsPopover(exceptItems = []) {
                if (!exceptItems?.length) {
                    return [];
                }
                return exceptItems?.map((it) => it.goods?.displayName) || [];
            },
        },

    };
</script>
