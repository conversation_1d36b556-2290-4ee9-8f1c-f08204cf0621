<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title=""
        append-to-body
        size="hugely"
        :auto-focus="false"
        data-cy="select-goods-item-list-dialog"
        content-styles="height:680px;padding: 0"
    >
        <abc-transfer-v2
            lite
            size="large"
            :result-list="selected"
            :width="1200"
            :props="{
                label: 'displayName',
            }"
            :confirm-button-disabled="false"
            @confirm="confirm"
            @cancel="showDialog = false"
            @del="deleteItem"
        >
            <abc-layout preset="dialog-table" style="padding: 24px;">
                <abc-layout-header>
                    <abc-space>
                        <abc-cascader
                            v-if="showSearchTypes"
                            ref="goodsTypesRef"
                            v-model="selectedTypes"
                            :options="goodsTypeOptions"
                            placeholder="选择分类"
                            multiple
                            :collapse="true"
                            :mutually-exclusive="exclusive"
                            :width="128"
                            :props="{
                                label: 'name',
                                value: 'id',
                                children: 'children'
                            }"
                            :value-props="{
                                _label: 'name',
                                _value: 'id'
                            }"
                            @change="handleChangeType"
                        >
                        </abc-cascader>
                        <goods-tag-filter-item
                            v-model="goodsTag"
                            :can-delete="false"
                            @change-tag="handleTagChange"
                        >
                        </goods-tag-filter-item>
                        <abc-input
                            v-model="scrollParams.key"
                            v-abc-focus-selected
                            :width="300"
                            :placeholder="placeholderStr"
                            data-cy="select-goods-item-list-dialog-search"
                            @input="_debounceSearch"
                        >
                            <abc-search-icon slot="prepend"></abc-search-icon>
                        </abc-input>
                    </abc-space>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        :loading="loading"
                        fill-height
                        :custom-tr-key="itemKey"
                        :show-all-checkbox="true"
                        :render-config="tableRenderConfig"
                        :data-list="list"
                        :disabled-item-func="disabledItemFunc"
                        :pagination="tablePagination"
                        @changeChecked="handleChangeChecked"
                        @sortChange="handleConfirmTableSortChange"
                        @changeAllChecked="handleAllCheckedChange"
                        @pageSizeChange="handlePageSizeChange"
                        @pageChange="handlePageChange"
                    >
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-transfer-v2>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import GoodsApi from 'api/goods';
    import GoodsV3API from 'api/goods/index-v3';
    import {
        debounce, isNull,
    } from 'utils/lodash';
    import TableConfig from '../table-config/table-select-goods-dialog';
    import TableTypeConfig from '../table-config/table-select-goods-dialog-type';
    import {
        goodsFullName, goodsSpec, goodsTypeName, isChineseMedicine,
    } from '@/filters/goods.js';
    import clone from 'utils/clone';
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import {
        number, toMoney,
    } from '@/filters';
    import { mapGetters } from 'vuex';
    import OverflowFlexTagsWrapper from 'views/registration/components/overflow-flex-tags-wrapper.vue';
    import GoodsTagFilterItem from 'views/statistics/common/pro-stat-toolbar/good-tag-filter-item.vue';

    export default {
        name: 'SelectGoodsItemList',
        components: { GoodsTagFilterItem },
        filters: {
            price(product) {
                if (isChineseMedicine(product)) {
                    return product.piecePrice;
                }
                return product.packagePrice;

            },
        },
        props: {
            // 是否是老带新
            fromReferrer: {
                type: Boolean,
                default: false,
            },
            // 是否是抵扣权益
            isPresentRightsGoods: {
                type: Boolean,
                default: false,
            },
            // 是否是抵扣权益多选(自选服务)
            isCustomService: {
                type: Boolean,
                default: false,
            },
            data: [],
            subType: {
                type: [ String, Number ],
                default: '',
            },
            cMSpec: {
                type: [ String, Number ],
                default: '',
            },
            selectGoodsList: Array,
            productTypes: Array,
            value: Boolean,
            placeholder: String,
            paramsDisable: {
                // 是否查询可用的项目，检查治疗可以设置启用停用
                type: [ Number, String ],
            },
            customTypeIdList: {
                type: Array,
                default: () => [],
            },
            feeTypeIdList: {
                type: Array,
                default: () => [],
            },
            // 禁止操作的 goods
            disabledGoodsIdList: {
                type: Array,
                default: () => [],
            },
            showSearchTypes: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                loading: false,
                active: 0,
                list: [],
                typeList: [],
                uncheckeds: [],
                allChecked: false,
                isLast: false,
                scrollParams: {
                    key: '',
                    offset: 0,
                    limit: 20,
                    withCostPrice: 1,
                    jsonTypeWithCustomTypeList: [ ],
                    typeId: '',
                    goodsTagIdList: [],
                },
                selected: [],
                selectedType: [],
                timer: null,
                goodsAllTypes: [],
                selectedTypes: [], // 选中的药品分类
                exclusive: false,
                selectedAll: false,
                deduplicationSet: new Set(),

                // 表格配置
                renderConfig: {
                    hasInnerBorder: false,
                    list: [],
                },
                total: 0,
                goodsTag: [],
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters([
                'isChainAdmin',
                'isSingleStore',
            ]),
            isFilterOperations() {
                return this.viewDistributeConfig.Marketing.isFilterOperations;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            isCheckAll: {
                get() {
                    return (this.list && this.list.filter((item) => item.checked).length === this.list.length && this.list.length > 0);
                },
                set(val) {
                    this.checkAll(val);
                },
            },
            goodsTypeOptions() {
                return this.goodsAllTypes.map((item) => {
                    return {
                        ...item,
                        childrenLength: item.children?.length || 0,
                    };
                }) || [];
            },
            placeholderStr() {
                if (this.placeholder) {
                    return this.placeholder;
                }
                return Number(this.scrollParams.type) === 4 ? '输入治疗 / 理疗项' : '输入项目名称';

            },
            selectedCount() {
                if (this.showTypes) {
                    return (this.selected && this.selected.length || 0) + (this.selectedType && this.selectedType.length || 0);
                }
                return this.selected && this.selected.length;
            },
            tableRenderConfig() {
                return this.getTableRenderConfig();
            },
            tablePagination() {
                const {
                    limit,
                    offset,
                } = this.scrollParams;
                return {
                    showTotalPage: true,
                    pageIndex: (offset / limit),
                    pageSize: limit,
                    pageSizes: [10, 20, 50, 100],
                    count: this.total,
                };
            },
        },
        async created() {
            const {
                productTypes, cMSpec = '', subType,feeTypeIdList,
            } = this;
            if (productTypes.length === 1) {
                this.scrollParams.jsonTypeWithCustomTypeList = [
                    {
                        type: '',
                    },
                ];
                this.scrollParams.jsonTypeWithCustomTypeList[0].type = productTypes[0];
                if (feeTypeIdList && feeTypeIdList.length > 0) {
                    this.scrollParams.feeTypeIdList = feeTypeIdList;
                }
                if (subType) {
                    this.scrollParams.jsonTypeWithCustomTypeList[0].subType = subType;
                }
                if (cMSpec) {
                    this.scrollParams.jsonTypeWithCustomTypeList[0].CMSpec = cMSpec;
                }
                if (this.customTypeIdList?.length) {
                    this.scrollParams.jsonTypeWithCustomTypeList[0].customTypeIdList = this.customTypeIdList;
                }
            } else {
                this.scrollParams.type = this.productTypes;
            }
            if (this.isCustomService) {
                this.selected = clone(this.selectGoodsList && this.selectGoodsList[0] && this.selectGoodsList[0].goodsList) || [];
            } else {
                this.selected = clone(this.selectGoodsList);
            }
            this.selected.forEach((product) => {
                product.displayName = product?.goods?.displayName || product?.name || product?.goodsName || product.displayName || '';
                product.displaySpec = product?.goods?.displaySpec || product?.goodsCMSpec || product.displaySpec || '';
            });
            this.loading = true;
            this.fetchData();
            this.uncheckeds = [];
            this._debounceSearch = debounce(() => {
                this.list = [];
                this.scrollParams.offset = 0;
                this.loading = true;
                this.fetchData();
            }, 250, true);
            this.fetchAllGoodsTypes();
        },
        beforeDestroy() {
            clearTimeout(this.timer);
        },
        methods: {
            goodsSpec,
            disabledItemFunc(item) {
                return this.disabledGoodsIdList?.indexOf(item.goodsId) > -1;
            },
            transformData(data) {
                const result = [];
                const map = new Map();

                for (let i = 0; i < data.length; i++) {
                    const obj = data[i];
                    const typeId = obj[0].id;
                    let customTypeId = '';
                    const count = obj[0]?.children?.length || 0;
                    if (obj?.[1]?.id) {
                        customTypeId = obj[1].id;
                    }

                    if (map.has(typeId)) {
                        map.get(typeId).customTypeIds.push(customTypeId);
                    } else {
                        map.set(typeId, {
                            customTypeIds: [customTypeId], length: 0, count,
                        });
                    }
                }

                for (const [key, value] of map) {
                    const entry = { typeId: key };
                    if (value.customTypeIds[0]) {
                        entry.customTypeIdList = value.customTypeIds;
                    }
                    entry.length = value.customTypeIds.length;
                    entry.count = value.count;
                    const {
                        count, length, customTypeIdList, typeId,
                    } = entry;
                    if (count > length) {
                        result.push({
                            customTypeIdList,
                        });
                    } else {
                        result.push({
                            typeId,
                            customTypeIdList,
                        });
                    }
                }
                return result;
            },

            handleChangeType() {
                const arr = this.transformData(this.$refs.goodsTypesRef.getOriValue(this.selectedTypes));
                if (arr?.length) {
                    this.scrollParams.jsonTypeWithCustomTypeList = arr;
                    this.scrollParams.type = [];
                } else {
                    // 清空选择的类型，要把当前渲染列表数据清空，
                    this.scrollParams.type = this.productTypes;
                    this.scrollParams.jsonTypeWithCustomTypeList = [];
                    this.list = [];
                }
                this.scrollParams.offset = 0;
                this.loading = true;
                this.fetchData();
            },
            async fetchAllGoodsTypes() {
                const { data } = await GoodsApi.fetchGoodsClassificationV3({
                    needCustomType: 1,
                });
                const types = [
                    GoodsTypeIdEnum.EXAMINATION_ASSAY,
                    GoodsTypeIdEnum.EXAMINATION_INSPECTION,
                    GoodsTypeIdEnum.TREATMENT_TREATMENT,
                    GoodsTypeIdEnum.TREATMENT_PHYSIOTHERAPY,
                    GoodsTypeIdEnum.OTHER_GOODS,
                ];
                const list = data?.list?.map((item) => {
                    const children = item.customTypes || [];
                    if (children.length) {
                        children.push({
                            id: -item.id,
                            name: '未指定',
                            sort: 999,
                            typeId: +item.id,
                        });
                    }
                    return {
                        ...item,
                        children,
                    };
                }) || [];
                this.goodsAllTypes = this.isPresentRightsGoods ? list?.filter((goodsItem) => types.includes(+goodsItem.id)) : list;
                if (this.isFilterOperations) {
                    const filters = ['87','88'];
                    const namesToExclude = ['手术医嘱','术后医嘱'];

                    this.goodsAllTypes = this.goodsAllTypes.filter((item) => {
                        return !filters.includes(item.id) && !namesToExclude.includes(item.name);
                    });
                }
            },
            hasProductInfo(type) {
                return [
                    GoodsTypeEnum.MEDICINE,
                    GoodsTypeEnum.MATERIAL,
                    GoodsTypeEnum.GOODS,
                ].indexOf(type) > -1;
            },
            initTypes(modules, initExpand = false) {
                return modules.map((item) => {
                    // 初始化选中态
                    const index = this.selectedType.findIndex((selectItem) => {
                        return selectItem.goodsIdKey === item.goodsIdKey;
                    });
                    item.checked = index > -1;
                    if (Array.isArray(item.children) && item.children.length) {
                        item.isLeaf = false;
                        item.children = this.initTypes(item.children);
                        if (initExpand) {
                            item.expand = !!item.children.find((it) => it.checked);
                        }
                        // 父节点的count 目前没有返回，暂时手动计算
                        item.count = 0;
                        item.count = item.children.reduce((acc, cur) => {
                            return (acc || 0) + (cur.count || 0);
                        }, 0);
                    } else {
                        item.isLeaf = true;
                        if (item.goodsType === 13 || item.goodsType === 14) {
                            item.count = 1;
                        }
                        if (initExpand) {
                            item.expand = false;
                        }
                    }
                    return Object.assign({}, item, {
                        checked: item.checked || false,
                        isLeaf: item.isLeaf,
                        expand: item.expand,
                        checkStatus: false,
                        count: item.count,
                    });
                });
            },

            initSelectedAll(typeList = []) {
                const flatTypeList = [];
                const processItem = (item) => {
                    const newItem = {
                        name: item.name,
                        goodsType: item.goodsType,
                        goodsSubType: item.goodsSubType,
                        goodsCMSpec: item.goodsCMSpec,
                        goodsId: item.id,
                        pharmacyType: item?.pharmacyType || item?.isAirPharmacy || 0,
                        isAirPharmacy: item?.pharmacyType || item?.isAirPharmacy || 0,
                        type: 1,
                        count: item.count,
                        exceptItems: [],
                        goodsIdKey: `${item.id}-${item.pharmacyType}`,
                        customTypeId: item.customTypeId,
                    };

                    flatTypeList.push(newItem);

                    if (item.children?.length) {
                        item.children.forEach((child) => processItem(child));
                    }
                };

                typeList.forEach(processItem);

                this.selectedAll = flatTypeList.length === this.selectedType.length;
            },

            handleSelectedAllClick() {
                this.selectedAll = !this.selectedAll;
                this.handleSelectedAll(this.selectedAll);
            },
            handleSelectedAll(val) {
                const typeItem = (item, checked = true) => {
                    const newItem = {
                        ...item,
                        checked,
                    };

                    if (item.expand && item.children?.length) {
                        newItem.children = item.children.map((child) => typeItem(child, checked));
                    }
                    return newItem;
                };

                const processItem = (item) => {
                    const newItem = {
                        name: item.name,
                        goodsType: item.goodsType,
                        goodsSubType: item.goodsSubType,
                        goodsCMSpec: item.goodsCMSpec,
                        goodsId: item.id,
                        pharmacyType: item?.pharmacyType || item?.isAirPharmacy || 0,
                        isAirPharmacy: item?.pharmacyType || item?.isAirPharmacy || 0,
                        type: 1,
                        count: item.count,
                        exceptItems: [],
                        goodsIdKey: `${item.id}-${item.pharmacyType}`,
                        customTypeId: item.customTypeId,
                        expand: item.expand || false,
                    };

                    const isExist = this.selectedType.find((it) => it.goodsId === newItem.goodsId);
                    if (!isExist) {
                        this.selectedType.push(newItem);
                    }

                    if (item.expand && item.children?.length) {
                        item.children.forEach((child) => processItem(child));
                    }
                };

                if (val) {
                    this.typeList = this.typeList.map((item) => typeItem(item));
                    this.typeList.forEach(processItem);
                } else {
                    this.typeList = this.typeList.map((item) => typeItem(item, false));
                    this.selectedType = [];
                }
            },
            clearKey() {
                this.isLast = false;
                this.list = [];
                this.scrollParams.offset = 0;
                this.loading = true;
                this.fetchData();
            },
            unique(arr) {
                const result = [];
                const obj = {};
                for (let i = 0; i < arr.length; i++) {
                    if (!obj[ arr[ i ].goodsId && arr[ i ].goods.goodsId ]) {
                        result.push(arr[ i ]);
                        obj[ arr[ i ].goodsId && arr[ i ].goods.goodsId ] = true;
                    }
                }
                return result;
            },
            // 全选功能，只选中当前页
            checkAll(checked) {
                if (checked) {
                    // 遍历当前页面数据，设置选中状态
                    this.list.forEach((item) => {
                        if (!this.disabledItemFunc(item)) {
                            item.displayName = item?.goods?.displayName || item?.name || item?.goodsName || item.displayName || '';
                            item.displaySpec = item?.goods?.displaySpec || item?.goodsCMSpec || item.displaySpec || '';
                            item.checked = true;
                        }
                    });
                    // 将当前页选中的商品添加到已选列表
                    const data = this.selected.concat(this.list && this.list.filter((item) => item.checked === true));
                    this.selected = this.unique(data);
                } else {
                    // 取消当前页选中
                    const currentGoodsIds = this.list.map((item) => item.goodsId);
                    // 取消当前页的选中状态
                    this.list.forEach((item) => {
                        item.checked = false;
                    });
                    // 从已选列表中移除当前页的商品
                    this.selected = this.selected.filter((item) => !currentGoodsIds.includes(item.goodsId)); 
                }
            },
            
            // 处理全选变化
            handleAllCheckedChange(checked) {
                this.checkAll(checked);
            },

            changeCheck(checked, item) {
                if (checked) {
                    this.selected && this.selected.unshift(item);
                    this.selected.forEach((product) => {
                        product.displayName = product?.goods?.displayName || product?.name || product?.goodsName || product.displayName || '';
                        product.displaySpec = product?.goods?.displaySpec || product?.goodsCMSpec || product.displaySpec || '';
                    });
                } else {
                    this.selected = this.selected && this.selected.filter((product) => product.goodsId !== item.goodsId);
                }
            },
            itemKey(item) {
                const {
                    type,
                    goodsId,
                    goodsType,
                    goodsSubType,
                } = item;
                return `${type}_${goodsId}_${goodsType}_${goodsSubType}`;
            },
            // abcTable选择框
            handleChangeChecked(item) {
                if (this.disabledItemFunc(item)) return;
                this.changeCheck(item.checked, item);
            },
            deleteItem(item) {
                this.list.forEach((it) => {
                    if (it.goodsId === item.goodsId) {
                        it.checked = false;
                    }
                });
                this.changeCheck(false, item);
            },
            confirm() {
                let data;
                if (this.showTypes) {
                    data = [...this.selectedType].concat([...this.selected]);
                } else {
                    if (this.isCustomService) {
                        const customSelected = this.selected && this.selected.filter((item) => !item.goodsList);
                        data = customSelected && customSelected.length ? [...customSelected] : [];
                    } else {
                        data = [...this.selected];
                    }

                }
                this.$emit('change', data, this.uncheckeds);
                this.showDialog = false;
            },

            // 更改CustomTypes字段名为Children字段名兼容abc-tree组件展示子级
            changeCustomTypesToChildren(list = []) {
                return list.map((item) => {
                    if (Array.isArray(item?.customTypes) && item?.customTypes?.length) {
                        item.customTypes.forEach((it) => {
                            it.goodsCMSpec = item.goodsCMSpec;
                            it.goodsType = item.goodsType;
                            it.goodsSubType = item.goodsSubType;
                            it.pharmacyType = item.pharmacyType;
                            it.isAirPharmacy = item.isAirPharmacy;
                            // 转换二级分类id
                            it.customTypeId = it.id;
                        });
                        item.children = item.customTypes;
                        delete item.customTypes;
                    }
                    if (Array.isArray(item?.children) && item?.children?.length) {
                        this.changeCustomTypesToChildren(item.children);
                    }

                    return {
                        ...item,
                    };
                });
            },

            generateGoodsIdkey(list = []) {
                const newList = list;
                return newList.map((item) => {
                    if (!item.goodsIdKey) {
                        item.goodsIdKey = `${item.id}-${item.pharmacyType}`;
                    }

                    if (Array.isArray(item?.children) && item?.children?.length) {
                        item.children = this.generateGoodsIdkey(item.children);
                    }

                    return {
                        ...item,
                        goodsIdKey: item.goodsIdKey,
                    };
                });
            },

            // 分页获取数据
            async fetchData() {
                try {
                    this.loading = true;
                    this.scrollParams.disable = 0;
                    
                    if (this.paramsDisable !== undefined) {
                        this.scrollParams.disable = +this.paramsDisable;
                    }
                    // 总部为汇总，不然总部没有入过库售价都是0
                    if (this.isChainAdmin || this.isSingleStore) {
                        this.scrollParams.queryStockOrderType = 40;
                    }
                    
                    const { data } = await GoodsV3API.searchGoods(this.scrollParams);
                    const {
                        list, query,
                    } = data;
                    
                    if (query.keyword !== this.scrollParams.key && this.scrollParams.offset !== query.offset) {
                        return;
                    }

                    // 处理数据，标记已选中的项
                    const newList = list.map((item) => {
                        return {
                            checked: !!this.selected && !!this.selected.find((it) => {
                                return it.goodsId === item.goodsId;
                            }),
                            goodsId: item.goodsId,
                            goodsType: item.type,
                            goodsSubType: item.subType,
                            goodsCMSpec: item.CMSpec,
                            goods: item,
                            productInfo: item,
                            type: 2,
                            pharmacyType: 0,
                        };
                    });
                    
                    // 更新列表和总数
                    this.list = newList;
                    this.total = data.total || 0;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            
            // 处理页码变化
            handlePageChange(page) {
                this.scrollParams.offset = (page - 1) * this.scrollParams.limit;
                this.fetchData();
            },
            
            // 处理每页条数变化
            handlePageSizeChange(limit) {
                this.scrollParams.limit = limit;
                this.fetchData();
            },

            getTableRenderConfig() {
                const baseConfig = {
                    'displayName': {
                        customRender: (_, row) => {
                            if (this.hasProductInfo(row.goods.type)) {
                                return (
                                <abc-table-cell
                                    v-abc-goods-hover-popper={{
                                        goods: row.goods,
                                        openDelay: 300,
                                        showStock: false,
                                    }}
                                    class="name ellipsis"
                                >
                                    <abc-flex gap="8" align="center" class="ellipsis">
                                        <abc-text class="ellipsis">
                                            {goodsFullName(row.goods)}
                                        </abc-text>
                                        <abc-text size="small" theme="gray-light" class="ellipsis">
                                            {goodsSpec(row.goods)}
                                        </abc-text>
                                    </abc-flex>
                                </abc-table-cell>
                                );
                            }
                            return (
                                <abc-table-cell
                                    class="name ellipsis"
                                >
                                    <abc-flex gap="8" align="center" class="ellipsis">
                                        <abc-text class="ellipsis">
                                            {goodsFullName(row.goods)}
                                        </abc-text>
                                        <abc-text size="small" theme="gray-light" class="ellipsis">
                                            {goodsSpec(row.goods)}
                                        </abc-text>
                                    </abc-flex>
                                </abc-table-cell>
                            );
                        },
                    },
                    'packagePrice': {
                        customRender: (_, row) => {
                            if (isNull(row.goods?.packagePrice)) {
                                return (<abc-table-cell class="ellipsis price un-fix-price">
                                        未定价
                                    </abc-table-cell>);
                            }
                            return (<abc-table-cell class="ellipsis price">
                                        {this.$t('currencySymbol') + toMoney(this.$options.filters.price(row.goods))}
                                    </abc-table-cell>);
                        },
                    },
                    'profitRat': {
                        dataFormatter: (_, row) => {
                            if (row.goods.profitRat === null || row.goods.profitRat === undefined) {return '--';}
                            return `${(row.goods.profitRat) | number}%`;
                        },
                    },
                    'stockPieceCount': {
                        dataFormatter: (_, row) => {
                            const { goods } = row;
                            let totalStock,stockUnit,pieceStock = '',pieceUnit = '';
                            if (goods.subType === 2 && goods.type === 1) {
                                totalStock = goods.stockPieceCount;
                                stockUnit = goods.pieceUnit;
                            } else {
                                totalStock = goods.stockPackageCount;
                                stockUnit = goods.packageUnit;
                                if (goods.stockPieceCount !== null && goods.pieceUnit !== null && goods.stockPieceCount !== 0) {
                                    pieceStock = goods.stockPieceCount;
                                    pieceUnit = goods.pieceUnit;
                                }
                            }
                            if (totalStock === null || totalStock === undefined || stockUnit === null) {return '--';}

                            return `${totalStock}${stockUnit}${pieceStock}${pieceUnit}`;
                        },
                    },
                    'goodsTagList': {
                        customRender: (_, row) => {
                            const goodsTagList = (row.goods?.goodsTagList || [])
                                .filter((item) => item.name)
                                .map((item) => ({
                                    tagId: item.tagId,
                                    tagName: item.name,
                                    viewMode: 0,
                                }));
                            return goodsTagList.length ?
                                <abc-table-cell>
                                    <OverflowFlexTagsWrapper tags={goodsTagList} variant="outline" size="tiny"/>
                                </abc-table-cell> :
                                <abc-table-cell>--</abc-table-cell>;
                        },
                    },
                };

                if (this.isPresentRightsGoods) {
                    return TableTypeConfig.extendConfig({
                        ...baseConfig,
                        'typeName': {
                            dataFormatter: (_, row) => goodsTypeName(row.goods),
                        },
                    });
                }
                return TableConfig.extendConfig({
                    ...baseConfig,
                    'name': {
                        dataFormatter: (_, row) => (row.goods.type === 1 ? row.goods.name : '--'),
                    },
                    'manufacturer': {
                        dataFormatter: (_, row) => (row.goods.manufacturer || '--'),
                    },
                });
            },

            handleConfirmTableSortChange({
                orderBy, orderType,
            }) {
                if (orderBy) {
                    this.sortList(this.list, orderBy, orderType);
                }
            },

            sortList(list, orderBy, orderType) {
                return list.sort((a, b) => {
                    if (typeof a.goods[orderBy] === 'number' && typeof b.goods[orderBy] !== 'number') {
                        return -1;
                    }
                    if (typeof a.goods[orderBy] !== 'number' && typeof b.goods[orderBy] === 'number') {
                        return 1;
                    }
                    if (a.goods[orderBy] === undefined || a.goods[orderBy] === null) return 1;
                    if (b.goods[orderBy] === undefined || b.goods[orderBy] === null) return -1;
                    if (orderType === 'asc') {
                        return a.goods[orderBy] - b.goods[orderBy];
                    }
                    return b.goods[orderBy] - a.goods[orderBy];
                });

            },

            handleTagChange(goodsTag, tagIds) {
                this.loading = true;
                this.list = [];
                this.scrollParams.offset = 0;
                this.isLast = false;
                this.goodsTag = goodsTag;
                this.scrollParams.goodsTagIdList = tagIds;
                this.fetchData();
            },
        },
    };
</script>
