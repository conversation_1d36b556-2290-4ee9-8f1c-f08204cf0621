<template>
    <div class="full-reduction-coupon-wrapper">
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            :title="title"
            :auto-focus="!id"
            size="xlarge"
            custom-class="coupon-form-dialog"
            content-styles="max-height: 600px;"
        >
            <abc-form
                ref="discountForm"
                v-abc-loading="loading"
                item-block
                item-no-margin
                label-position="left"
                class="full-reduction-coupon-form"
            >
                <biz-setting-form :label-width="56" no-limit-width>
                    <biz-setting-form-group>
                        <biz-setting-form-item label="优惠券名">
                            <abc-form-item required>
                                <abc-input
                                    v-model="postData.name"
                                    v-abc-focus-selected
                                    type="text"
                                    :width="340"
                                    :max-length="20"
                                    show-max-length-tips
                                    :disabled="isInvalid"
                                    data-cy="abc-input-coupon-name"
                                    @input="filterEmoji"
                                ></abc-input>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <marketing-select-card-item
                            v-if="isChain"
                            has-divider
                            label="可用门店"
                            edit-btn-text="选择门店"
                            :tag-width="148"
                            :tag-data.sync="postData.clinics"
                            :get-icon-function="() => 's-organization-color'"
                            :is-show-card="postData.isAllClinics === 0"
                            :disabled="isInvalid || isTakeOff || isActiveReceive"
                            @openDialog="showClinicsDialog = true"
                        >
                            <template
                                v-if="!postData.isAllClinics && !postData.clinics.length && clinicsError"
                                #tips
                            >
                                未选择指定门店
                            </template>
                            <template #radio-group>
                                <abc-radio-group
                                    v-model="postData.isAllClinics"
                                    :item-block="true"
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                >
                                    <abc-radio
                                        :disabled="(isInvalid || isTakeOff || isActiveReceive) || disabledAllSubClinic"
                                        :label="1"
                                        class="span-bold"
                                    >
                                        所有门店
                                    </abc-radio>
                                    <abc-radio
                                        :disabled="isInvalid || isTakeOff || isActiveReceive"
                                        :label="0"
                                        class="span-bold"
                                    >
                                        指定门店
                                    </abc-radio>
                                </abc-radio-group>
                            </template>
                        </marketing-select-card-item>

                        <biz-setting-form-item label="使用范围" has-divider :use-inner-layout="false">
                            <select-goods-type-list
                                :goods-type-list.sync="postData.goodsList"
                                :disabled="isInvalid || isTakeOff || isActiveReceive"
                                @handleOptTracker="handleOptTracker"
                            >
                                <template #tips>
                                    <abc-text
                                        v-if="goodsError"
                                        size="mini"
                                        theme="danger-light"
                                        class="error"
                                    >
                                        未选择参与活动商品
                                    </abc-text>
                                </template>
                                <template #search>
                                    <abc-autocomplete
                                        v-if="postData.goodsList.length"
                                        v-model.trim="keyword"
                                        :width="220"
                                        :inner-width="560"
                                        placeholder="商品名/首字母"
                                        clearable
                                        :fetch-suggestions.sync="searchGoodsInfo"
                                        :async-fetch="true"
                                        @enterEvent="selectGoods"
                                        @clear="clearKeyword"
                                        @blur="handleBlur"
                                    >
                                        <abc-icon slot="prepend" icon="search"></abc-icon>

                                        <template slot="suggestions" slot-scope="{ suggestion }">
                                            <dt
                                                class="suggestions-item"
                                                @click="selectGoods(suggestion)"
                                            >
                                                <div
                                                    style="width: 240px; min-width: 240px; max-width: 240px; padding-right: 10px;"
                                                    class="ellipsis"
                                                    :title="suggestion | goodsFullName"
                                                >
                                                    {{ suggestion | goodsFullName }}
                                                </div>
                                                <div style="width: 140px; padding-right: 10px;" class="ellipsis" :title="suggestion | goodsDisplaySpec">
                                                    {{ suggestion | goodsDisplaySpec }}
                                                </div>
                                                <div style="flex: 1; padding-right: 10px;" class="ellipsis" :title="suggestion.manufacturer">
                                                    {{ suggestion.manufacturer }}
                                                </div>
                                            </dt>
                                        </template>
                                    </abc-autocomplete>
                                </template>
                                <template #table>
                                    <select-goods-type-table
                                        :goods-list.sync="postData.goodsList"
                                        :disabled="isInvalid || isTakeOff || isActiveReceive"
                                        :filter-params="filterParams"
                                        :filter-func="filterGoodsList"
                                        :show-empty="!!postData.goodsList.length"
                                        is-member-or-discount
                                        only-show-exception
                                        show-except-items
                                        @handleOptTracker="handleOptTracker"
                                    >
                                    </select-goods-type-table>
                                </template>
                            </select-goods-type-list>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="面值">
                            <abc-form-item required>
                                <abc-input
                                    v-model="postData.coupon.discountedPrice"
                                    v-abc-focus-selected
                                    data-cy="abc-input-coupon-value"
                                    :width="60"
                                    class="small-input"
                                    type="money"
                                    :config="{
                                        max: 9999999,
                                        formatLength: 2,
                                    }"
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                >
                                    <span slot="append">元</span>
                                </abc-input>
                            </abc-form-item>
                        </biz-setting-form-item>
                        <biz-setting-form-item label="使用门槛" has-divider>
                            <abc-form-item required :validate-event="validateOrderThreshold">
                                <abc-radio-group v-model="orderThresholdStatus" @change="changeOrderThresholdStatus">
                                    <abc-radio :disabled="isInvalid || isTakeOff || isActiveReceive" :label="0">
                                        无门槛
                                    </abc-radio>

                                    <abc-radio :disabled="isInvalid || isTakeOff || isActiveReceive" :label="1">
                                        <abc-text>满</abc-text>
                                        <abc-input
                                            v-model="postData.coupon.orderThresholdPrice"
                                            v-abc-focus-selected
                                            type="money"
                                            style="margin: 0 4px;"
                                            :width="80"
                                            :config="{
                                                max: 100000,
                                                formatLength: 2,
                                            }"
                                            :disabled="!orderThresholdStatus || isInvalid || isActiveReceive"
                                        >
                                            <span slot="append">元</span>
                                        </abc-input>
                                        <abc-text>可用</abc-text>
                                    </abc-radio>
                                </abc-radio-group>
                            </abc-form-item>
                            <abc-form-item>
                                <abc-checkbox
                                    v-model="postData.onlyOriginalPrice"
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                    type="number"
                                >
                                    仅购买原价商品时可用
                                </abc-checkbox>
                            </abc-form-item>
                        </biz-setting-form-item>
                        <biz-setting-form-item label="使用数量">
                            <abc-radio-group v-model="postData.coupon.maxUseCountPerOrder">
                                <abc-radio
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                    :label="1"
                                >
                                    单次消费仅限使用 1 张
                                </abc-radio>
                                <abc-radio
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                    :label="-1"
                                >
                                    单次消费不限使用数量
                                </abc-radio>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item label="使用期限" has-divider :use-inner-layout="false">
                            <abc-form-item :validate-event="validateValidDays">
                                <abc-radio-group
                                    v-model="postData.coupon.validType"
                                    @change="changeValidType"
                                >
                                    <abc-flex vertical gap="12">
                                        <abc-radio :label="0" :disabled="isInvalid || isActiveReceive">
                                            <abc-text>领券后</abc-text>
                                            <abc-input
                                                v-model="postData.coupon.validDays"
                                                v-abc-focus-selected
                                                data-cy="abc-input-validity-period"
                                                type="number"
                                                :width="50"
                                                style="margin: 0 4px;"
                                                :config="{
                                                    max: 2000,
                                                }"
                                                :input-custom-style="{
                                                    textAlign: 'center',
                                                }"
                                                :disabled="isInvalid || isTakeOff || isActiveReceive || !!postData.coupon.validType"
                                            >
                                                <span slot="append">天</span>
                                            </abc-input>
                                            <abc-text>内</abc-text>
                                            <abc-popover
                                                trigger="hover"
                                                placement="top"
                                                style="display: inline-flex;"
                                                theme="yellow"
                                            >
                                                <abc-icon
                                                    slot="reference"
                                                    icon="info_bold"
                                                    size="14"
                                                    :color="$store.state.theme.style.P3"
                                                >
                                                </abc-icon>
                                                <div>
                                                    <p>有效期按自然天计算。</p>
                                                    <p>举例：如设置领券当日起2天内可用，用户在1月21日14:00时领取优惠券，</p>
                                                    <p>则该优惠券的可用时间为1月21日的14:00:00至1月22日的23:59:59。</p>
                                                </div>
                                            </abc-popover>
                                        </abc-radio>
                                        <abc-flex gap="8">
                                            <abc-radio :label="1" :disabled="isInvalid || isTakeOff || isActiveReceive">
                                                指定日期
                                            </abc-radio>
                                            <abc-form-item v-if="postData.coupon.validType" required>
                                                <abc-date-picker
                                                    v-model="validDate"
                                                    type="daterange"
                                                    clearable
                                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                                    :picker-options="pickerOptions"
                                                    @change="handleValidDateChange"
                                                >
                                                </abc-date-picker>
                                            </abc-form-item>
                                        </abc-flex>
                                    </abc-flex>
                                </abc-radio-group>
                            </abc-form-item>
                        </biz-setting-form-item>
                        <biz-setting-form-item label="发行总量">
                            <abc-form-item required>
                                <abc-input
                                    v-model="postData.coupon.totalCount"
                                    v-abc-focus-selected
                                    data-cy="abc-input-total-number-of-issues"
                                    :disabled="isInvalid || isTakeOff || isActiveReceive"
                                    placeholder="最多1000000张，发行后只能增加不能减少"
                                    type="number"
                                    :width="300"
                                    :config="{
                                        max: 1000000,
                                    }"
                                ></abc-input>
                                <abc-button
                                    v-if="!isInvalid && !isTakeOff"
                                    variant="text"
                                    theme="primary"
                                    size="small"
                                    style="margin-left: 8px;"
                                    @click="handleAddIssuance"
                                >
                                    增加
                                </abc-button>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <marketing-select-card-item
                            label="领取范围"
                            edit-btn-text="选择会员"
                            :tag-width="148"
                            :tag-data.sync="postData.memberTypes"
                            :get-icon-function="() => 's-role-color'"
                            :is-show-card="postData.isAllPatients === 0"
                            :item-display-name="(item) => item.organ ? item.organ?.name : item.name"
                            :disabled="isTakeOff || isInvalid"
                            @openDialog="showMemberDialog = true"
                        >
                            <template
                                v-if="!postData.isAllPatients && !postData.memberTypes.length && memberError"
                                #tips
                            >
                                未选择指定会员
                            </template>
                            <template #radio-group>
                                <abc-radio-group
                                    v-model="postData.isAllPatients"
                                    :disabled="isTakeOff || isInvalid"
                                >
                                    <abc-radio
                                        :label="1"
                                        :disabled="isTakeOff || isInvalid"
                                    >
                                        所有患者
                                    </abc-radio>
                                    <abc-radio
                                        :label="0"
                                        :disabled="isTakeOff || isInvalid"
                                    >
                                        指定会员
                                    </abc-radio>
                                </abc-radio-group>
                            </template>
                        </marketing-select-card-item>

                        <biz-setting-form-item label="领取数量" has-divider>
                            <abc-form-item :validate-event="validateLimitCount" required>
                                <abc-radio-group
                                    v-model="postData.coupon.isLimitObtainCount"
                                    @change="changeLimitObtainCount"
                                >
                                    <abc-radio :label="0" :disabled="isInvalid || isTakeOff">
                                        不限数量
                                    </abc-radio>

                                    <abc-radio :label="1" :disabled="isInvalid || isTakeOff">
                                        每人
                                        <abc-input
                                            v-model="postData.coupon.obtainCountPerUser"
                                            v-abc-focus-selected
                                            :width="80"
                                            type="number"
                                            :input-custom-style="{ textAlign: 'center' }"
                                            :config="{ max: 10000 }"
                                            max-length="10000"
                                            :disabled="!postData.coupon.isLimitObtainCount || isInvalid || isTakeOff"
                                        >
                                            <span slot="append">张</span>
                                        </abc-input>
                                    </abc-radio>
                                </abc-radio-group>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="免费领取" has-divider>
                            <biz-setting-form-item-tip :tip="isOpenMp ? `开启后，用户可在微${$app.institutionTypeWording}【我的-优惠券】中可免费领取` : ''">
                                <abc-checkbox
                                    v-model="postData.coupon.isFreeObtain"
                                    :disabled="isInvalid || isTakeOff || !isOpenMp"
                                    :class="{ 'abc-tipsy--n': !isOpenMp }"
                                    :data-tipsy="`需要开通微${$app.institutionTypeWording}`"
                                    type="number"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                            <abc-form-item v-if="postData.coupon.isFreeObtain" required>
                                <abc-date-picker
                                    v-model="obtainDate"
                                    type="daterange"
                                    clearable
                                    :disabled="isTakeOff || isInvalid"
                                    :picker-options="pickerOptions"
                                    @change="handleObtainDateChange"
                                >
                                </abc-date-picker>
                            </abc-form-item>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>

                <div class="supplement">
                    <div class="coupon-box">
                        <abc-tag-v2
                            class="preview-tag"
                            variant="dark"
                            theme="warning"
                            size="tiny"
                        >
                            预览
                        </abc-tag-v2>
                        <img class="preview-img" src="../../../assets/images/marketing-coupon.png" alt="" />
                        <abc-flex justify="space-between" align="center" style="height: 26px;">
                            <abc-text style="flex-shrink: 0;" size="large" bold>
                                {{ postData.name }}
                            </abc-text>
                            <div class="money">
                                <span style="font-size: 14px;">¥</span>
                                <span style="font-size: 26px; font-weight: 500;">{{ postData.coupon.discountedPrice }}</span>
                            </div>
                        </abc-flex>

                        <abc-flex justify="space-between" align="flex-end">
                            <abc-text v-if="postData.coupon.validType" style="flex-shrink: 0;" size="mini">
                                {{ postData.coupon.validBegin }}-{{ postData.coupon.validEnd }}
                            </abc-text>
                        </abc-flex>

                        <abc-divider variant="dashed"></abc-divider>

                        <abc-flex vertical>
                            <abc-text
                                v-if="postData.coupon.orderThresholdPrice"
                                size="mini"
                                theme="gray-light"
                            >
                                满{{ postData.coupon.orderThresholdPrice }}元可用
                            </abc-text>
                            <abc-text
                                v-if="!postData.coupon.validType"
                                size="mini"
                                theme="gray-light"
                                class="show-max-line-two"
                            >
                                有效期：领券后 {{ postData.coupon.validDays || '' }} 天内
                            </abc-text>
                            <abc-text
                                v-if="useProductTips"
                                size="mini"
                                theme="gray-light"
                                class="show-max-line-two"
                            >
                                适用商品：{{ useProductTips }}
                            </abc-text>
                            <abc-text size="mini" theme="gray-light" class="show-max-line-two">
                                {{ limitTips }}
                            </abc-text>
                            <abc-text
                                v-if="postData.coupon.remark"
                                size="mini"
                                theme="gray-light"
                                class="show-max-line-two"
                            >
                                补充说明：{{ postData.coupon.remark }}
                            </abc-text>
                        </abc-flex>
                    </div>
                    <abc-textarea
                        v-model="postData.coupon.remark"
                        :width="341"
                        :height="200"
                        :disabled="isInvalid"
                        placeholder="补充说明"
                        :maxlength="maxLength"
                        show-max-length-tips
                    >
                    </abc-textarea>
                </div>
            </abc-form>

            <div slot="footer" class="dialog-footer">
                <div class="finish">
                    <template v-if="postData.id">
                        <abc-button
                            v-if="!isTakeOff && !isInvalid"
                            variant="ghost"
                            theme="danger"
                            @click="stopConfirm"
                        >
                            停止发券
                        </abc-button>
                        <abc-button
                            v-if="!isInvalid"
                            variant="ghost"
                            theme="danger"
                            @click="cancelConfirm"
                        >
                            作废
                        </abc-button>
                    </template>
                </div>
                <abc-button v-if="!isInvalid" :disabled="btnLoading" @click="confirm">
                    确定
                </abc-button>
                <abc-button variant="ghost" @click="showDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
        <increase-issuance-dialog
            v-if="showAddIssuance"
            v-model="showAddIssuance"
            :count="postData.coupon.totalCount"
            @cancel="handleHide"
            @save="handleUpdateCoupon"
        ></increase-issuance-dialog>
        <clinic-transfer
            v-if="showClinicsDialog"
            v-model="showClinicsDialog"
            :clinics="postData.clinics"
            title="指定门店"
            :access-key="accessKey"
            @confirm="(list) => postData.clinics = list"
        ></clinic-transfer>
        <member-type-transfer
            v-if="showMemberDialog"
            v-model="showMemberDialog"
            :selecteds="postData.memberTypes"
            @confirm="(list) => postData.memberTypes = list"
        >
        </member-type-transfer>
    </div>
</template>

<script>
    import {
        level, PromotionStatus,
    } from '../data/containts';
    import { parseTime } from 'utils/index';
    import EmojiFilter from 'utils/emoji-filter';
    import { mapGetters } from 'vuex';

    import MarketingAPI from 'api/marketing';

    import IncreaseIssuanceDialog from './increase-issuance-dialog';

    import ClinicAPI from 'api/clinic';
    import AbcAccess from '@/access/utils.js';
    import { GoodsTypeEnum } from '@abc/constants';
    import {
        isChainSubClinic,
    } from 'src/views/common/clinic.js';

    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
    } from '@/components-composite/setting-form/index.js';
    import MarketingSelectCardItem from '@/views/marketing/components/marketing-select-card-item.vue';
    import SelectGoodsTypeList from 'views/marketing/components/select-goods-type-list/select-goods-type-list.vue';
    import SelectGoodsTypeTable from 'views/marketing/components/select-goods-type-list/select-goods-type-table.vue';
    import { debounce } from 'utils/lodash';
    import GoodsV3API from 'api/goods/index-v3';
    import usePaginationSerial from 'views/marketing/hooks/use-pagination-serial';
    import useDataOperationTracker, { OPT_TYPES } from 'views/marketing/hooks/use-data-operation-tracker';


    const oneDayTimestamp = 24 * 60 * 60 * 1000;
    const ClinicTransfer = () => import('components/clinic-transfer/index.vue');
    const MemberTypeTransfer = () => import('components/member-type-transfer/index.vue');

    export default {
        components: {
            IncreaseIssuanceDialog,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            MarketingSelectCardItem,
            SelectGoodsTypeList,
            SelectGoodsTypeTable,
            ClinicTransfer,
            MemberTypeTransfer,
        },
        props: {
            id: {
                required: true,
            },
            value: Boolean,
            isCopy: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            // 初始化数据跟踪器
            const goodsTracker = useDataOperationTracker();
            return {
                goodsTracker,
            };
        },
        data() {
            return {
                accessKey: AbcAccess.accessMap.MARKET_COUPON,
                memberError: false,
                clinicsError: false,
                goodsError: false,
                selectError: false,
                giftRuleIndex: 0,
                showClinicsDialog: false,
                showMemberDialog: false,
                showSelectCoupon: false,
                showReverseType: [GoodsTypeEnum.MEDICINE,
                                  GoodsTypeEnum.MATERIAL,
                                  GoodsTypeEnum.EXAMINATION,
                                  GoodsTypeEnum.OTHER,
                                  GoodsTypeEnum.GOODS,
                                  GoodsTypeEnum.TREATMENT],
                selectReverseList: [],
                allMemberCardTypeTotal: 0,
                selectCouponList: [],
                showAddIssuance: false,
                selectGoodsType: [GoodsTypeEnum.MEDICINE,
                                  GoodsTypeEnum.MATERIAL,
                                  GoodsTypeEnum.EXAMINATION,
                                  GoodsTypeEnum.OTHER,
                                  GoodsTypeEnum.GOODS,
                                  GoodsTypeEnum.TREATMENT,
                                  GoodsTypeEnum.SURGERY],
                maxLength: 200,
                pickerOptions: {
                    disabledDate: (date) => {
                        return date < new Date(new Date().getTime() - 60 * 60 * 1000 * 24);
                    },
                },
                validDate: [],
                obtainDate: [],

                loading: false,
                btnLoading: false,
                postData: {
                    name: '',
                    type: '',
                    onlyOriginalPrice: 1,
                    isAllClinics: 1,
                    isAllPatients: 1,
                    clinics: [],
                    memberTypes: [],
                    goodsList: [],
                    coupon: {
                        totalCount: '',
                        leftCount: 0,
                        usedCount: 0,
                        maxUseCountPerOrder: 1,
                        orderThresholdPrice: '',
                        validBegin: null,
                        validEnd: null,
                        obtainBeginDate: null,
                        obtainEndDate: null,
                        validDays: '',
                        obtainCountPerUser: '',
                        isFreeObtain: 0,
                        discountedPrice: '',
                        isLimitObtainCount: 0,
                        validType: 0,
                        remark: '',
                    },
                },

                orderThresholdStatus: 0,
                totalCount: 0,
                leftCount: 0,

                chainSubClinicList: [], // 连锁下子店列表

                keyword: '',
                filterParams: {
                    id: '',
                },
            };
        },
        computed: {
            ...mapGetters(['isChain', 'isOpenMp']),
            title() {
                return this.id && !this.isCopy ? '编辑优惠券' : '新增优惠券';
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },

            validDays: {
                get() {
                    return this.postData.coupon.validDays ? 0 : 1;
                },
                set(val) {
                    if (!val) {
                        this.postData.coupon.validDays = 7;
                    } else {
                        this.postData.coupon.validDays = 0;
                    }
                },
            },
            obtainCountPerUser: {
                get() {
                    return this.postData.coupon.obtainCountPerUser ? 1 : 0;
                },
                set(val) {
                    this.postData.coupon.obtainCountPerUser = val;
                },
            },
            useProductTips() {
                return this.postData.goodsList
                    .map((item) => {
                        return item.name || item.goods?.medicineCadn || item.goods?.name;
                    })
                    .join('、');
            },
            limitOrganTips() {
                let str = '* 适用门店：';
                if (this.postData.isAllClinics) {
                    str += '所有门店';
                } else {
                    str += `${this.participateOrganTips(this.postData.clinics)}可用`;
                }
                return str;
            },
            limitTips() {
                let str = '领取限制：';

                if (this.participatePatientTips && this.participatePatientTips !== '全部会员') {
                    str += `仅${this.participatePatientTips}可领取，`;
                }

                if (this.postData.coupon.isLimitObtainCount) {
                    str += `每人限领${this.postData.coupon.obtainCountPerUser || ''}张`;
                } else {
                    str += '不限领取数量';
                }
                return str;
            },
            participatePatientTips() {
                const { memberTypes } = this.postData;
                const { allMemberCardTypeTotal } = this;
                if (memberTypes.length === 0 || this.postData.isAllPatients) {
                    return '';
                }
                if (memberTypes.length && memberTypes.length === allMemberCardTypeTotal) {
                    return '全部会员';
                }
                if (memberTypes.length && memberTypes.length !== allMemberCardTypeTotal) {
                    const showApplictors = memberTypes.slice(0, 1);
                    const showApplictorsNames = showApplictors.map((item) => item.name);
                    const names = showApplictorsNames.map((item) =>
                        (item && item.length > 10 ? `${item.substring(0, 10)}...` : item),
                    );
                    if (memberTypes.length > 1) {
                        return `${names.join('、')}等${memberTypes.length}个会员卡`;
                    }
                    return `${names.join('、')}`;

                }
                return '';
            },
            selectGoodsList() {
                return this.postData.goodsList.map((item) => {
                    return {
                        ...item,
                        productId: item.goodsId,
                        goods: item.goods,
                        productSubType: item.goodsType,
                        productType: item.goodsSubType,
                    };
                });
            },

            /**
             * @desc 可发券的状态，除优惠内容（见面金额）外，都可以编辑
             * <AUTHOR>
             * @date 2020/04/17 09:33:21
             */
            isActive() {
                return this.postData.status === PromotionStatus.ACTIVE;
            },

            /**
             * @desc 可发券状态，优惠券领取数>0(只可以编辑 名称，增发数量，领取范围，领取数量，免费领取开关，补充说明)
             * <AUTHOR>
             * @date 2020/04/21 18:48:37
             */
            isActiveReceive() {
                return this.isActive && this.totalCount > this.leftCount;
            },

            /**
             * @desc 下架，停止发券，除优惠内容、领取范围、限领数量、免费领取开关&时间外，都可以编辑
             * <AUTHOR>
             * @date 2020/04/17 09:52:21
             */
            isTakeOff() {
                return this.postData.status === PromotionStatus.TAKE_OFF;
            },

            /**
             * @desc 作废，已经失效 都不可以编辑
             * <AUTHOR>
             * @date 2020/04/17 09:49:43
             */
            isInvalid() {
                return this.postData.status === PromotionStatus.INVALID;
            },

            discountPriceTips() {
                let str = '* ';
                if (this.orderThresholdStatus) {
                    str += `满 ${this.postData.coupon.orderThresholdPrice} 减免 ${this.postData.coupon.discountedPrice} 元`;
                } else {
                    str += `减免 ${this.postData.coupon.discountedPrice} 元`;
                }
                return str;
            },
            validDaysTips() {
                let str = '';
                if (this.postData.coupon.validType) {
                    str += `${this.dateFormat(this.postData.coupon.validBegin)}至${this.dateFormat(
                        this.postData.coupon.validEnd,
                    )}`;
                } else {
                    str += `领券后 ${this.postData.coupon.validDays || ''} 天内`;
                }
                return str;
            },

            // 可选的门店 - 没有到期
            availableSubClinics() {
                return this.chainSubClinicList.filter((item) => AbcAccess.checkAvailableByEdition(this.accessKey, item.edition));
            },
            // 是否禁用选择全部门店
            disabledAllSubClinic() {
                return (
                    this.chainSubClinicList.length === 0 ||
                    this.chainSubClinicList.length !== this.availableSubClinics.length
                );
            },
        },
        watch: {
            disabledAllSubClinic: {
                handler(newValue) {
                    if (newValue && this.isChain) {
                        // 当禁用全部门店时，默认选中指定门店
                        this.postData.isAllClinics = 0;
                    }
                },
                immediate: true,
            },
        },

        async created() {
            if (this.id) {
                this.fetchDetail();
            }

            try {
                // 获取连锁下各门店
                await this.fetchChainClinics();
                const { total = 0 } = await MarketingAPI.getMemberCardTypeDetailList();
                this.allMemberCardTypeTotal = total;
            } catch (error) {
                this.isLoading = false;
            }
            this._handleChangeFilterParams = debounce(this.handleChangeFilterParams, 300, true);
        },
        methods: {
            // 获取详情
            async fetchDetail() {
                this.loading = true;
                const couponInitData = await MarketingAPI.getCouponDetail(this.id);
                await this.fetchPageCouponDataDetails(couponInitData);

                // const types = data.goodsList.filter( item => item.type === 1 )
                // const reverseGoods = data.goodsList.filter( item => item.isReverse === 1 && item.type === 2 )
                // const goods = data.goodsList.filter( item => !item.isReverse && item.type === 2 )
                //
                // types.forEach( item => {
                //     console.log( item )
                //     item.exceptItems = reverseGoods.filter( it => item.id === it.id )
                // } )
                // data.goodsList = [ ...types, ...goods ]
                this.loading = false;
            },
            async fetchPageCouponDataDetails(initData) {
                const handlePageData = (pageData) => {
                    // 这里可以处理每一页的数据
                    this.initCouponData({
                        ...initData,
                        goodsList: pageData.rows ?? [],
                    });
                    // 如果需要，可以在这里执行其他操作
                };
                const {
                    run,
                } = usePaginationSerial(
                    async (params) => {
                        const pageData = await MarketingAPI.getCouponGoodsPageListDetail({
                            ...params, promotionId: this.id,
                        });
                        // 每次请求完成后立即处理数据
                        handlePageData(pageData);
                        return pageData;
                    },
                    {
                        limit: 1000,
                    },
                );
                try {
                    await run();
                } catch (e) {
                    console.log(e);
                }
            },
            initCouponData(data) {
                if (this.isCopy) {
                    data = this.copyHandler(data);
                } else {
                    // 默认数据，不能为0，后台提交后null或者'' 会转成0，需要转化
                    data.coupon.obtainCountPerUser = data.coupon.obtainCountPerUser || '';
                    data.coupon.orderThresholdPrice = data.coupon.orderThresholdPrice || '';
                    this.leftCount = data.coupon.leftCount;
                    this.totalCount = data.coupon.totalCount;
                }

                // 处理指定时间
                if (data.coupon.validBegin && data.coupon.validEnd) {
                    this.validDate = [data.coupon.validBegin, data.coupon.validEnd];
                }

                if (data.coupon.obtainBeginDate && data.coupon.obtainEndDate) {
                    this.obtainDate = [data.coupon.obtainBeginDate, data.coupon.obtainEndDate];
                }

                // 给每一项 goods 添加 isOriginData 作为源数据标识
                data.goodsList.forEach((item) => {
                    item.isOriginData = true;
                });
                this.postData = {
                    ...data,
                    goodsList: [...this.postData.goodsList, ...data.goodsList],
                };
                if (this.postData.coupon.orderThresholdPrice > 0) {
                    this.orderThresholdStatus = 1;
                }
            },
            /**
             * @desc 清空对应的id name 时间等信息
             * <AUTHOR>
             * @date 2020-07-30 20:46:31
             * @params
             * @return
             */
            copyHandler(data) {
                data.name = '';
                delete data.status;
                delete data.id;
                if (!data.isForever) {
                    data.beginDate = '';
                    data.endDate = '';
                }
                data.goodsList = data.goodsList.map((item) => {
                    delete item.id;
                    if (item.exceptItems && item.exceptItems.length) {
                        item.exceptItems = item.exceptItems.map((exceptItem) => {
                            delete exceptItem.id;
                            return exceptItem;
                        });
                    }
                    return item;
                }).filter((item) => {
                    // 复制时过滤已删除的分类
                    if (item.type === 1) {
                        return !!item.name;
                    }
                    return true;
                });
                console.log(data);
                return data;
            },
            filterEmoji(val) {
                this.postData.name = EmojiFilter(val);
            },
            changeOrderThresholdStatus() {
                if (!this.orderThresholdStatus) {
                    this.postData.coupon.orderThresholdPrice = '';
                }
            },

            validateOrderThreshold(coupon, callback) {
                const { orderThresholdPrice } = this.postData.coupon;
                if (this.orderThresholdStatus === 1 && !orderThresholdPrice) {
                    callback({
                        validate: false,
                        message: '使用门槛不能为空',
                    });
                    return false;
                }
                callback({ validate: true });
            },
            /**
             * @desc 每人领取数量不超过发行总量
             * <AUTHOR>
             * @date 2020/4/20
             * @params
             * @return
             */
            validateLimitCount(coupon, callback) {
                const {
                    isLimitObtainCount, obtainCountPerUser, totalCount,
                } = this.postData.coupon;

                const flag = +obtainCountPerUser <= +totalCount;
                if (isLimitObtainCount === 1 && obtainCountPerUser === '') {
                    callback({
                        validate: false,
                        message: '领取数量不能为空',
                    });
                    return false;
                } if (isLimitObtainCount && !flag) {
                    callback({
                        validate: false,
                        message: '每人领取数量不能超过发行总量',
                    });
                }
                callback({ validate: true });
            },
            validateValidDays(coupon, callback) {
                const {
                    validType, validDays,
                } = this.postData.coupon;
                if (validType === 0) {
                    if (validDays === '') {
                        callback({
                            validate: false,
                            message: '使用期限不能为空' ,
                        });
                        return false;
                    }
                    if (+validDays === 0) {
                        callback({
                            validate: false,
                            message: '使用期限必须大于0',
                        });
                        return false;
                    }
                }
                callback({ validate: true });
            },
            validateDate(endDate, callback) {
                const { validEnd } = this.postData.coupon;
                if (!validEnd || !endDate) {
                    callback({ validate: true });
                    return false;
                }

                const endDateTimes = new Date(endDate.replace(/-/g, '/')).getTime();
                const validEndTimes = new Date(validEnd.replace(/-/g, '/')).getTime();
                if (endDateTimes > validEndTimes) {
                    callback({
                        validate: false,
                        message: '领取日期结束时间不能大于使用期限结束时间',
                    });
                    return false;
                }
                callback({ validate: true });
            },

            disabledValidBegin(time) {
                let flag = false;
                if (this.postData.coupon.validEnd) {
                    flag = time.getTime() >= new Date(this.postData.coupon.validEnd).getTime() - oneDayTimestamp;
                }
                const isEndTime = new Date(2038, 0, 18).getTime();
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            disabledValidEnd(time) {
                let flag = false;
                if (this.postData.coupon.validBegin) {
                    flag = time.getTime() < new Date(this.postData.coupon.validBegin).getTime() - oneDayTimestamp;
                }
                const isEndTime = new Date(2038, 0, 18).getTime();
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            disabledBeginDate(time) {
                let flag = false;
                if (this.postData.coupon.obtainEndDate) {
                    flag = time.getTime() >= new Date(this.postData.coupon.obtainEndDate).getTime() - oneDayTimestamp;
                }
                const isEndTime = new Date(2038, 0, 18).getTime();
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            disabledEndDate(time) {
                let flag = false;
                if (this.postData.coupon.obtainBeginDate) {
                    flag = time.getTime() <= new Date(this.postData.coupon.obtainBeginDate).getTime() - oneDayTimestamp ;
                }
                const isEndTime = new Date(2038, 0, 18).getTime();
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },

            changeLimitObtainCount() {
                if (!this.postData.coupon.isLimitObtainCount) {
                    this.postData.coupon.obtainCountPerUser = null;
                }
            },

            changeValidType() {
                if (this.postData.coupon.validType) {
                    this.postData.coupon.validDays = null;
                }
            },

            participateOrganTips(clinics) {
                if (clinics.length === 0) {
                    return '';
                }
                const names = [];
                clinics.forEach((item) => {
                    let name = item.name || item.shortName;
                    name = name && name.length > 8 ? `${name.substring(0, 8)}...` : name;
                    names.push(name);
                });
                const { length } = names;
                if (names.length > 2) {
                    names.length = 2;
                }
                if (names.length > 1) {
                    return `${names.join('、')}等${length}个门店`;
                }
                return `${names.join('、')}`;

            },

            confirm() {
                this.$refs.discountForm.validate(async (valid) => {
                    if (valid) {
                        if (!this.postData.isAllPatients && !this.postData.memberTypes.length) {
                            this.memberError = true;
                            return false;
                        }
                        if (!this.postData.isAllClinics && !this.postData.clinics.length) {
                            this.clinicsError = true;
                            return false;
                        }
                        if (!this.postData.goodsList.length) {
                            this.goodsError = true;
                            const dom = document.querySelector('.abc-dialog-body');
                            this.$nextTick(() => {
                                dom.scrollTop = document.querySelector('.error').scrollTop;
                            });
                            return false;
                        }
                        this.submit();
                    }
                });
            },
            async submit() {
                try {
                    this.btnLoading = true;
                    const params = Object.assign(
                        {},
                        {
                            clinicIds: this.postData.clinics.map((item) => item.clinicId || item.id),
                            memberTypeIds: this.postData.memberTypes.map((item) => item.id),
                        },
                        this.postData,
                    );
                    params.goodsList = (this.isCopy ? this.postData.goodsList : this.goodsTracker.operationData.value || []).map((item) => {
                        const exceptItems = (this.isCopy ? item.exceptItems || [] : item.exceptOperationTrackerItems || []).map((exceptItem) => {
                            if (exceptItem.type === 3) {
                                return {
                                    type: exceptItem.type || '',
                                    clinicId: exceptItem?.clinicId || '',
                                    employeeId: exceptItem?.employeeId || '',
                                    employeeName: exceptItem?.employeeName || '',
                                    departmentId: exceptItem?.departmentId || '',
                                    clinicName: exceptItem?.clinicName || '',
                                    departmentName: exceptItem?.departmentName || '',
                                    optType: this.isCopy ? OPT_TYPES.ADD : exceptItem.optType,
                                };
                            }
                            return {
                                id: exceptItem.id || '',
                                type: exceptItem.type || '',
                                goodsId: exceptItem.goodsId || '',
                                goodsType: exceptItem.goodsType || '',
                                goodsSubType: exceptItem.goodsSubType || '',
                                goodsCMSpec: exceptItem.goodsCMSpec || '',
                                pharmacyType: exceptItem?.pharmacyType || exceptItem?.isAirPharmacy || 0,
                                isAirPharmacy: exceptItem?.pharmacyType || exceptItem?.isAirPharmacy || 0,
                                optType: this.isCopy ? OPT_TYPES.ADD : exceptItem.optType,
                            };
                        });
                        return {
                            id: item.id,
                            goodsId: item.goodsId,
                            type: item.type,
                            goodsType: item.goodsType,
                            goodsSubType: item.goodsSubType,
                            goodsCMSpec: item.goodsCMSpec,
                            exceptItems,
                            pharmacyType: item?.pharmacyType || item?.isAirPharmacy || 0,
                            isAirPharmacy: item?.pharmacyType || item?.isAirPharmacy || 0,
                            customTypeId: item.customTypeId,
                            optType: this.isCopy ? OPT_TYPES.ADD : item.optType,
                        };
                    });

                    if (!params.coupon.orderThresholdPrice) {
                        params.coupon.orderThresholdPrice = 0;
                    }

                    // /**
                    //  * @desc 当免费领取开关开启后，isForever 必须传0， 否则传1
                    //  * <AUTHOR>
                    //  * @date 2020/04/28 16:58:29
                    //  */
                    // params.isForever = +!params.coupon.isFreeObtain

                    if (this.id && !this.isCopy) {
                        await MarketingAPI.editCoupon(params);
                    } else {
                        await MarketingAPI.addCoupon(params);
                    }
                    this.btnLoading = false;
                    this.showDialog = false;
                    this.$emit('refresh', this.id ? 'edit' : 'add');
                } catch (e) {
                    this.btnLoading = false;
                    console.error(e);
                    this.$Toast({
                        type: 'error',
                        message: e.message || '操作失败',
                    });
                }
            },

            stopConfirm() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content:
                        '停止发券后，顾客将无法再领取优惠券。已领取的优惠券，在有效期内还能继续使用，但无法再编辑优惠券内容。确定停止发券？',
                    onConfirm: () => {
                        this.stopSubmit();
                    },
                });
            },
            async stopSubmit() {
                if (!this.id) return false;
                try {
                    await MarketingAPI.stopCoupon(this.id);
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.showDialog = false;
                    this.$emit('refresh', this.id ? 'edit' : 'add');
                } catch (e) {
                    console.error(e);
                }
            },

            cancelConfirm() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '优惠券作废后，顾客将无法再领取优惠券，已领取的优惠券也将作废，无法使用。是否确定作废？',
                    onConfirm: () => {
                        this.cancelSubmit();
                    },
                });
            },
            async cancelSubmit() {
                if (!this.id) return false;
                try {
                    await MarketingAPI.invalidCoupon(this.id);
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.showDialog = false;
                    this.$emit('refresh', this.id ? 'edit' : 'add');
                } catch (e) {
                    console.error(e);
                }
            },

            deleteConfirm() {
                if (!this.id) return false;
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除？',
                    onConfirm: async () => {
                        this.deleteSubmit();
                    },
                });
            },
            async deleteSubmit() {
                try {
                    await MarketingAPI.deleteCoupon(this.id);
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.showDialog = false;
                    this.$emit('refresh', 'add');
                } catch (e) {
                    console.error(e);
                }
            },

            checked(item) {
                return item.selects.map((item) => item.name);
            },

            // 时间显示格式化
            dateFormat(dateString) {
                if (!dateString) {
                    return '';
                }
                return parseTime(new Date(dateString), 'y-m-d', true);
            },
            // 计算几级优惠，最多三级根据数组的长度计算并翻译成大写
            computeLevel(index) {
                const levelItem = level.find((item) => item.level === index);
                return levelItem ? levelItem.alias : '一';
            },
            // 删除优惠券
            handleDeleteCoupon(index) {
                this.postData.giftRules = this.postData.giftRules.filter((item, i) => i !== index);
                this.$emit('change', this.postData);
            },

            async handleFinish() {
                await this.$emit('finished', this.postData);
            },

            async handleDeleteActive() {
                await this.$emit('delete', this.postData);
            },
            handleAddIssuance() {
                this.showAddIssuance = true;
            },
            handleHide() {
                this.showAddIssuance = false;
            },
            handleUpdateCoupon(val) {
                this.showAddIssuance = false;
                this.postData.coupon.totalCount = Number(this.postData.coupon.totalCount) + Number(val);
                this.$emit('change', this.postData);
            },

            /**
             * 拉取连锁下门店列表
             * <AUTHOR>
             * @date 2020-09-25
             */
            async fetchChainClinics() {
                try {
                    const { data } = await ClinicAPI.chainClinicV3();
                    this.chainSubClinicList = (data.rows || []).filter((item) => isChainSubClinic(item));
                } catch (error) {
                    console.log('fetchChainClinics error', error);
                }
            },
            handleValidDateChange(Date) {
                this.postData.coupon.validBegin = Date[0] || '';
                this.postData.coupon.validEnd = Date[1] || '';
            },
            handleObtainDateChange(Date) {
                this.postData.coupon.obtainBeginDate = Date[0] || '';
                this.postData.coupon.obtainEndDate = Date[1] || '';
            },
            filterGoodsList(goodsList, filterParams) {
                const list = goodsList;
                if (filterParams.id) {
                    return list.filter((item) => item.goodsId === filterParams.id || item.id === filterParams.id);
                }
                return list;
            },
            async searchGoodsInfo(keyword, callback) {
                keyword = keyword.trim();
                let dataList = [];
                if (keyword) {
                    try {
                        const { data } = await GoodsV3API.searchGoods({
                            keyword,
                            offset: 0,
                            limit: 50,
                        });
                        dataList = data.list || [];

                        // 分类查询
                        const goodsList = this.postData.goodsList.filter((item) => item.name?.includes(keyword));
                        dataList.push(...goodsList);
                    } catch (error) {
                        console.log('querySearchAsync error', error);
                    }
                }
                return callback(dataList);
            },
            selectGoods(item) {
                this.keyword = item.medicineCadn || item.name;
                this.filterParams.id = item.id || item.goodsId;
            },
            clearKeyword() {
                this.keyword = '';
                this.filterParams.id = '';
            },
            handleBlur() {
                if (!this.keyword.trim()) {
                    this.clearKeyword();
                }
            },
            handleOptTracker(goodsItem,optType) {
                if (!goodsItem) return;
                if (optType === OPT_TYPES.DELETE) {
                    this.goodsTracker.saveDeleteItem(goodsItem);
                }
                if (optType === OPT_TYPES.ADD) {
                    this.goodsTracker.saveAddItem(goodsItem);
                }
                if (optType === OPT_TYPES.UPDATE) {
                    this.goodsTracker.saveUpdateItem(goodsItem);
                }
            },
        },
    };
</script>
