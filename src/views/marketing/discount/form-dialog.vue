<template>
    <div class="discount-form">
        <abc-form ref="discountForm">
            <abc-dialog
                v-if="visible"
                ref="discountFormDialog"
                :title="title"
                :value="visible"
                content-styles="height: 600px;"
                size="xlarge"
                @input="(val) => $emit('visible', val)"
            >
                <abc-flex vertical :gap="16">
                    <abc-flex v-if="showConflictTips || isMemberActivity" :gap="4" vertical>
                        <abc-tips-card-v2 v-if="showConflictTips" theme="warning">
                            部分产品折扣设置与其它活动冲突，导致失效
                            <template #operate>
                                <abc-button class="abc-button-small" type="text" @click="showDiscountConflictView">
                                    详情
                                </abc-button>
                            </template>
                        </abc-tips-card-v2>
                        <abc-tips-card-v2 v-if="isMemberActivity" theme="warning">
                            该折扣为会员折扣，请前往会员管理处修改折扣内容
                        </abc-tips-card-v2>
                    </abc-flex>
                    <div v-abc-loading="isLoading">
                        <biz-setting-form :no-limit-width="true">
                            <biz-setting-form-group>
                                <biz-setting-form-item label-line-height-size="medium" label="活动名称">
                                    <div>
                                        <abc-input
                                            v-model="postData.activityData.name"
                                            :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                            :max-length="20"
                                            :width="328"
                                            show-max-length-tips
                                            type="text"
                                        ></abc-input>
                                    </div>
                                </biz-setting-form-item>
                                <biz-setting-form-item label="活动时间" use-inner-layout>
                                    <abc-radio-group
                                        v-model="postData.activityData.isForever"
                                        :item-block="true"
                                    >
                                        <abc-radio
                                            :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                            :label="1"
                                            class="span-bold"
                                        >
                                            永久有效
                                        </abc-radio>
                                        <abc-radio
                                            :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                            :label="0"
                                            class="span-bold"
                                            style="height: 32px;"
                                        >
                                            <abc-flex :gap="8" align="center">
                                                指定时间
                                                <div
                                                    v-if="postData.activityData.isForever === 0"
                                                    :disabled="postData.activityData.isForever === 1 || isOnlyShow"
                                                >
                                                    <abc-date-picker
                                                        v-model="postData.activityData.dateRange"
                                                        :disabled="postData.activityData.isForever === 1 || isOnlyShow"
                                                        :picker-options="pickerOptions"
                                                        :placeholder="'全部时间'"
                                                        clearable
                                                        width="240"
                                                        type="daterange"
                                                        value-format="YYYY-MM-DD"
                                                        @change="changeDate"
                                                    >
                                                    </abc-date-picker>
                                                </div>
                                            </abc-flex>
                                        </abc-radio>
                                    </abc-radio-group>
                                </biz-setting-form-item>
                                <marketing-select-card-item
                                    label="活动对象"
                                    :tag-data.sync="postData.activityData.applicators"
                                    :get-icon-function="() => 's-role-color'"
                                    :is-show-card="postData.patientType === 1"
                                    :tag-width="143"
                                    :item-display-name="(item) => item.memberType ? item.memberType?.memberTypeName : item.name"
                                    :disabled="postData.patientType === 0 || (isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                    :edit-btn-text="'选择会员'"
                                    @openDialog="showSelectMemberDialog = true"
                                >
                                    <template
                                        v-if="postData.patientType !== 0 &&
                                            isShowErrorNoChoiceMember &&
                                            postData.activityData.applicators.length === 0"
                                        #tips
                                    >
                                        <abc-text theme="danger">
                                            未选择指定会员
                                        </abc-text>
                                    </template>
                                    <template #radio-group>
                                        <abc-radio-group
                                            v-model="postData.patientType"
                                            :item-block="true"
                                            :disabled="(isMemberTypeGrant && !isFromCopy)"
                                        >
                                            <abc-radio
                                                :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                                :label="0"
                                            >
                                                所有{{ viewDistributeConfig?.roleLabel || '患者' }}
                                            </abc-radio>
                                            <abc-radio
                                                :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                                :label="1"
                                            >
                                                指定会员
                                            </abc-radio>
                                        </abc-radio-group>
                                    </template>
                                </marketing-select-card-item>
                                <marketing-select-card-item
                                    v-if="isChain"
                                    has-divider
                                    label="活动门店"
                                    :tag-data.sync="postData.activityData.organs"
                                    :get-icon-function="() => 's-organization-color'"
                                    :is-show-card="postData.organType === 1"
                                    :item-display-name="(item) => item.organ ? item.organ?.name : item.name"
                                    :disabled="postData.organType === 0 || (isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                    :edit-btn-text="'选择门店'"
                                    :tag-width="188"
                                    @openDialog="showSelectClinicsTransfer = true"
                                >
                                    <template #radio-group>
                                        <abc-radio-group
                                            v-model="postData.organType"
                                            :item-block="true"
                                            :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                        >
                                            <abc-radio
                                                :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow || disabledAllSubClinic"
                                                :label="0"
                                                class="span-bold"
                                            >
                                                所有门店
                                            </abc-radio>
                                            <abc-radio
                                                :disabled="(isMemberTypeGrant && !isFromCopy) || isOnlyShow"
                                                :label="1"
                                                class="span-bold"
                                            >
                                                指定门店
                                            </abc-radio>
                                        </abc-radio-group>
                                    </template>
                                </marketing-select-card-item>
                                <biz-setting-form-item label-line-height-size="small" label="活动范围" :use-inner-layout="false">
                                    <select-goods-type-list
                                        :goods-type-list.sync="postData.activityData.goodsList"
                                        :disabled="isMemberActivity"
                                        @update:goodsTypeList="handleUpdateGoodsTypeList"
                                        @handleOptTracker="handleOptTracker"
                                    >
                                        <template #search>
                                            <abc-autocomplete
                                                v-if="postData.activityData.goodsList.length"
                                                v-model.trim="keyword"
                                                :width="220"
                                                :inner-width="560"
                                                placeholder="商品名/首字母"
                                                clearable
                                                :fetch-suggestions.sync="searchGoodsInfo"
                                                search-goods-info
                                                :async-fetch="true"
                                                @enterEvent="selectGoods"
                                                @clear="clearKeyword"
                                                @blur="handleBlur"
                                            >
                                                <abc-icon slot="prepend" icon="search"></abc-icon>
                                                <template slot="suggestions" slot-scope="{ suggestion }">
                                                    <dt
                                                        class="suggestions-item"
                                                        @click="selectGoods(suggestion)"
                                                    >
                                                        <div
                                                            style="width: 240px; min-width: 240px; max-width: 240px; padding-right: 10px;"
                                                            class="ellipsis"
                                                            :title="suggestion | goodsFullName"
                                                        >
                                                            {{ suggestion | goodsFullName }}
                                                        </div>
                                                        <div style="width: 140px; padding-right: 10px;" class="ellipsis" :title="suggestion | goodsDisplaySpec">
                                                            {{ suggestion | goodsDisplaySpec }}
                                                        </div>
                                                        <div style="flex: 1; padding-right: 10px;" class="ellipsis" :title="suggestion.manufacturer">
                                                            {{ suggestion.manufacturer }}
                                                        </div>
                                                    </dt>
                                                </template>
                                            </abc-autocomplete>
                                        </template>
                                        <template #table>
                                            <select-goods-type-table
                                                :goods-list.sync="postData.activityData.goodsList"
                                                :show-discount-radio="true"
                                                :show-error="false"
                                                :show-labe-input="true"
                                                is-member-or-discount
                                                show-except-items
                                                :is-marketing-card="false"
                                                :is-member-type-in-discount="isMemberActivity"
                                                :tips="'选择参与活动的项目范围'"
                                                :show-empty="true"
                                                :filter-params="filterParams"
                                                :filter-func="filterGoodsList"
                                                @handleOptTracker="handleOptTracker"
                                            >
                                            </select-goods-type-table>
                                        </template>
                                    </select-goods-type-list>
                                </biz-setting-form-item>
                            </biz-setting-form-group>
                        </biz-setting-form>
                    </div>
                </abc-flex>
                <abc-flex slot="footer" justify="space-between">
                    <div>
                        <abc-button
                            v-if="activityId && !isFromCopy && postData.activityData.type !== 0 && !isLoading"
                            theme="danger"
                            variant="ghost"
                            @click="stopCheck"
                        >
                            {{ !activityIsFinish ? '终止' : '删除' }}
                        </abc-button>
                    </div>
                    <abc-space>
                        <abc-button v-if="!isOnlyShow" :disabled="isMemberActivity" @click="save">
                            保存
                        </abc-button>
                        <abc-button variant="ghost" @click="cancel">
                            取消
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </abc-dialog>
            <member-type-transfer
                v-if="showSelectMemberDialog"
                v-model="showSelectMemberDialog"
                :selecteds="postData.activityData.applicators"
                @confirm="changeMember"
            >
            </member-type-transfer>
            <clinic-transfer
                v-if="showSelectClinicsTransfer"
                v-model="showSelectClinicsTransfer"
                :access-key="accessKey"
                :clinics="postData.activityData.organs"
                title="指定门店"
                @confirm="changeClinics"
            ></clinic-transfer>
            <discount-conflict-view
                v-if="isShowDiscountConflictView"
                :all-member-card-type-total="allMemberCardTypeTotal"
                :conflict-list="conflictData"
                :visible="isShowDiscountConflictView"
                @visible="(val) => (isShowDiscountConflictView = val)"
            ></discount-conflict-view>
            <select-goods-view
                v-if="isShowSelectGoodsView"
                :no-discount-goods-list="noDiscountGoodsList"
                :product-types="selectGoodsType"
                :select-goods-list="selectGoodsList"
                :value="isShowSelectGoodsView"
                :with-cost-price="1"
                @change="updateSelectedSingleGoods"
                @input="(val) => (isShowSelectGoodsView = val)"
            ></select-goods-view>
        </abc-form>
    </div>
</template>

<script>
    import DiscountTypeData from '../data/discount-type-data';
    const MemberTypeTransfer = () => import('components/member-type-transfer/index.vue');
    import DiscountConflictView from './discount-conflict-view';
    import SelectGoodsView from '../../statistics/commission/setting/select-goods.vue';
    const ClinicTransfer = () => import('components/clinic-transfer/index.vue');
    import clone from 'utils/clone';
    import ClinicAPI from 'api/clinic';
    import MarketingAPI from 'api/marketing';
    import { parseTime } from 'utils/index';
    import {
        emojiExits, filterStrSpace,
    } from 'utils/validate';
    import { mapGetters } from 'vuex';
    import { isChainSubClinic } from 'views/common/clinic.js';
    import discountApi from 'src/api/discount-api.js';
    import { GoodsTypeEnum } from '@abc/constants';
    import {
        enlargeDiscount, reduceDiscount,
    } from 'views/marketing/util.js';
    import AbcAccess from '@/access/utils.js';
    import BizSettingForm from '@/components-composite/setting-form/src/views/index.vue';
    import BizSettingFormItem from '@/components-composite/setting-form/src/views/item.vue';
    import BizSettingFormGroup from '@/components-composite/setting-form/src/views/group.vue';
    import SelectGoodsTypeList from 'views/marketing/components/select-goods-type-list/select-goods-type-list.vue';
    import SelectGoodsTypeTable from 'views/marketing/components/select-goods-type-list/select-goods-type-table.vue';
    import MarketingSelectCardItem from 'views/marketing/components/marketing-select-card-item.vue';
    import GoodsV3API from 'api/goods/index-v3';
    import usePaginationSerial from 'views/marketing/hooks/use-pagination-serial';
    import useDataOperationTracker, { OPT_TYPES } from 'views/marketing/hooks/use-data-operation-tracker';


    export default {
        components: {
            MarketingSelectCardItem,
            SelectGoodsTypeTable,
            SelectGoodsTypeList,
            BizSettingFormItem,
            BizSettingFormGroup,
            BizSettingForm,
            ClinicTransfer,
            MemberTypeTransfer,
            DiscountConflictView,
            SelectGoodsView,
        },
        props: {
            activityId: String,
            visible: Boolean,
            isFromCopy: Boolean,
            isOnlyShow: Boolean,
            noDiscountGoodsList: Array,
        },
        setup() {
            // 初始化数据跟踪器
            const goodsTracker = useDataOperationTracker();
            return {
                goodsTracker,
            };
        },
        data() {
            return {
                accessKey: AbcAccess.accessMap.MARKET_DISCOUNT,
                showSelectClinicsTransfer: false,
                postData: {
                    timeType: 0,
                    patientType: 0,
                    organType: 0,
                    onlyOriginalPrice: 1,
                    activityData: {
                        name: '',
                        dateRange: [],
                        beginDate: null,
                        endDate: null,
                        isForever: 1,
                        organs: [],
                        applicators: [],
                        goodsList: [],
                        goodsCount: 0,
                        type: 1,
                        status: 1,
                    },
                },
                pickerOptions: {
                    disabledDate: this.secoundDate,
                    yearRange: {
                        end: 2038,
                    },
                },
                allMemberCardTypeTotal: 0,
                discountTypeList: clone(DiscountTypeData),
                isShowAddSingleGoodView: false,
                showSelectMemberDialog: false,
                isShowDiscountConflictView: false,
                isShowSelectGoodsView: false,
                isShowErrorNoChoiceMember: false,
                conflictData: [],
                isInit: false,
                isLoading: false,
                selectGoodsType: [GoodsTypeEnum.MEDICINE,
                                  GoodsTypeEnum.MATERIAL,
                                  GoodsTypeEnum.EXAMINATION,
                                  GoodsTypeEnum.OTHER,
                                  GoodsTypeEnum.GOODS,
                                  GoodsTypeEnum.EYEGLASSES,
                                  GoodsTypeEnum.COMPOSE],

                chainSubClinicList: [], // 连锁下子店列表

                originalGoodsList: [], // 原商品列表

                keyword: '',
                filterParams: {
                    id: '',
                },
            };
        },
        computed: {
            ...mapGetters(['isChain']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            title () {
                if (this.isFromCopy || !this.activityId) {
                    return '新建折扣活动';
                }
                return '编辑折扣活动';

            },
            selectGoodsList() {
                const filterData = this.postData.activityData.goodsList.filter((item) => {
                    return !!item.goods;
                });
                return filterData.map((item) => {
                    return {
                        productId: item.goodsId,
                        productInfo: item.goods,
                        productSubType: item.goodsType,
                        productType: item.goodsSubType,
                    };
                });
            },
            isMemberTypeGrant() {
                return this.postData.activityData.type === 0;
            },
            activityIsFinish() {
                return this.postData.activityData.status === 9;
            },
            showConflictTips() {
                return this.conflictData && this.conflictData.length > 0;
            },
            isMemberActivity() {
                return this.postData.activityData.type === 0;
            },
            // 可选的门店 - 没有到期
            availableSubClinics() {
                return this.chainSubClinicList.filter((item) => AbcAccess.checkAvailableByEdition(this.accessKey, item.edition));
            },
            // 是否禁用选择全部门店
            disabledAllSubClinic() {
                return (
                    this.chainSubClinicList.length === 0 ||
                    this.chainSubClinicList.length !== this.availableSubClinics.length
                );
            },
            goodsListWording() {
                return this.postData.activityData.goodsList.filter((item) => !!item.goods);
            },
        },
        watch: {
            disabledAllSubClinic: {
                handler(newValue) {
                    if (newValue && this.isChain) {
                        // 当禁用全部门店时，默认选中指定门店
                        this.postData.organType = 1;
                    }
                },
                immediate: true,
            },
        },
        async created() {
            try {
                this.isLoading = true;

                await Promise.all([
                    this.fetchChainClinics(),
                    this.fetchMemberCardTypeCount(),
                    this.fetchActivityData(),
                    this.checkConflictData(),
                ]);
            } catch (e) {
                //
            }
            this.isLoading = false;
            this.isInit = true;
        },
        methods: {
            changeMember(list) {
                this.postData.activityData.applicators = list;
            },
            changeClinics(list) {
                this.postData.activityData.organs = list;
            },
            update(index, value) {
                this.discountTypeList[index].discount = value;
            },
            secoundDate(date) {
                const isEndTime = new Date(2038, 0, 18).getTime();
                return (
                    date <
                    (this.postData.activityData.beginDate ?
                        this.postData.activityData.beginDate :
                        new Date(new Date().getTime() - 60 * 60 * 1000 * 24)) || date.getTime() > isEndTime
                );
            },
            dateFormat(dateString) {
                if (!dateString) {
                    return '';
                }
                return parseTime(new Date(dateString), 'y-m-d', true);
            },
            // 处理删除项
            handleDeleteGoods(data) {
                const { goodsList } = data;
                // 找出删除项
                const deletedGoods = this.originalGoodsList
                    .filter((oldItem) => !goodsList.some((item) => item.id === oldItem.id))
                    .map((item) => {
                        const formatDiscount = reduceDiscount(item.discount, 10, 3);
                        return {
                            ...item,
                            discount: item.discountType ? item.discount : formatDiscount,
                            discountType: item.discountType || 0,
                            optType: 2,
                        };
                    });

                // 合并当前列表和删除项
                data.goodsList = [...goodsList, ...deletedGoods];
                return data;
            },
            async save() {
                this.loading = true;
                const goodsList = this.isFromCopy ? this.postData.activityData.goodsList : this.goodsTracker.operationData.value;
                const data = {
                    name: this.postData.activityData.name,
                    isForever: this.postData.activityData.isForever,
                    memberTypeIds: this.postData.activityData.applicators.map((item) => {
                        return item.id;
                    }),
                    clinicIds: this.postData.activityData.organs.map((item) => {
                        return item.id;
                    }),
                    goodsList: goodsList?.map((item) => {
                        const formatDiscount = reduceDiscount(item.discount, 10, 3);
                        if (this.isFromCopy) {
                            delete item.exceptOperationTrackerItems;
                            return {
                                ...item,
                                exceptItems: item?.exceptItems?.map(($item) => {
                                    return {
                                        ...$item,
                                        optType: OPT_TYPES.ADD,
                                    };
                                }),
                                optType: OPT_TYPES.ADD,
                                discount: item.discountType ? item.discount : formatDiscount,
                                discountType: item.discountType || 0,
                            };
                        }
                        return {
                            ...item,
                            exceptItems: item.exceptOperationTrackerItems,
                            discount: item.discountType ? item.discount : formatDiscount,
                            discountType: item.discountType || 0,
                        };
                    }),
                    type: 1,
                };

                if (!filterStrSpace(this.postData.activityData.name)) {
                    this.$Toast({
                        message: '未填写活动名称',
                        type: 'error',
                    });
                    return;
                }
                if (emojiExits(this.postData.activityData.name)) {
                    this.$Toast({
                        message: '活动名称中存在非法字符',
                        type: 'error',
                    });
                    return;
                }

                const _operationData = this.postData.activityData.goodsList;
                if (!_operationData.length) {
                    this.$Toast({
                        message: '折扣商品不能为空',
                        type: 'error',
                    });
                    return;
                }

                if (this.postData.activityData.isForever === 0) {
                    if (!this.postData.activityData.beginDate || !this.postData.activityData.endDate) {
                        this.$Toast({
                            message: '未设置活动时间',
                            type: 'error',
                        });
                        return;
                    }
                    if (
                        new Date(this.postData.activityData.beginDate).getTime() >
                        new Date(this.postData.activityData.endDate).getTime()
                    ) {
                        this.$Toast({
                            message: '设置活动开始时间大于结束时间',
                            type: 'error',
                        });
                        return;
                    }
                    data.beginDate = parseTime(new Date(this.postData.activityData.beginDate), 'y-m-d', true);
                    data.endDate = parseTime(new Date(this.postData.activityData.endDate), 'y-m-d', true);
                }
                if (this.postData.patientType === 1 && this.postData.activityData.applicators.length === 0) {
                    this.isShowErrorNoChoiceMember = true;
                    return;
                } if (this.postData.patientType !== 1) {
                    data.memberTypeIds = [];
                }
                if (this.postData.organType === 1 && this.postData.activityData.organs.length === 0) {
                    this.$Toast({
                        message: '未选择指定门店',
                        type: 'error',
                    });
                    return;
                } if (this.postData.organType !== 1) {
                    data.clinicIds = [];
                }

                this.discountTypeList.forEach((item) => {
                    const obj = JSON.parse(JSON.stringify(item));
                    obj.discount = parseFloat((parseFloat(obj.discount) / 10).toFixed(2));
                    // data.goodsList.push(obj);
                });
                try {
                    // this.postData.activityData.goodsList.forEach((item) => {
                    //     if (!item.goods) {
                    //         return;
                    //     }
                    //     const noDiscount = this.noDiscountGoodsList.filter((goods) => goods.goodsId === item.goodsId);
                    //     if (!noDiscount.length) {
                    //         if ((!item.discount && item.discount !== 0) || item.discount > 10 || item.discount < 0) {
                    //             this.$Toast({
                    //                 message: '有未设置折扣的单品，无法保存',
                    //                 type: 'error',
                    //             });
                    //             throw new Error(123);
                    //         }
                    //     }
                    //     const singleGoods = clone(item);
                    //     delete singleGoods.goods;
                    //     singleGoods.discount = singleGoods.discount / 10;
                    //     data.goodsList.push(singleGoods);
                    // });
                    // if (data.goodsList.length === 0) {
                    //     this.$Toast({
                    //         message: '未设置任何项目折扣，无法保存',
                    //         type: 'error',
                    //     });
                    //     return;
                    // }

                    if (this.activityId && !this.isFromCopy) {
                        // 处理删除项
                        // this.handleDeleteGoods(data);
                        await discountApi.updateDiscountPromotionUsingPUT(data, this.activityId);
                        this.$Toast({
                            message: '更新活动成功',
                            type: 'success',
                        });
                    } else {
                        await discountApi.createDiscountPromotionUsingPOST(data);
                        this.$Toast({
                            message: '创建活动成功',
                            type: 'success',
                        });
                    }
                    this.$emit('refresh');
                    this.loading = false;
                } catch (e) {
                    console.log(e);
                    this.loading = false;
                    return;
                }
                this.$emit('visible', false);
            },
            cancel() {
                this.$emit('visible', false);
            },
            stopCheck() {
                if (this.activityIsFinish) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '删除活动后无法恢复，是否确认？',
                        onConfirm: () => {
                            this.stop();
                        },
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '终止活动后无法再次开启，是否确认？',
                        onConfirm: () => {
                            this.stop();
                        },
                    });
                }
            },
            async stop() {
                try {
                    this.loading = true;
                    if (this.activityIsFinish) {
                        await discountApi.deleteDiscountPromotionUsingDELETE(this.activityId);
                        this.$Toast({
                            message: '删除活动成功',
                            type: 'success',
                        });
                    } else {
                        await discountApi.endDiscountPromotionUsingPUT(this.activityId);
                        this.$Toast({
                            message: '终止活动成功',
                            type: 'success',
                        });
                    }
                    this.$emit('refresh');
                } catch (e) {
                    this.loading = false;
                    return;
                }
                this.loading = false;
                this.$emit('visible', false);
            },
            removeGood(index) {
                if (index < this.postData.activityData.goodsList.length) {
                    this.postData.activityData.goodsList.splice(index, 1);
                }
            },
            showAddSingleGoodView() {
                this.isShowSelectGoodsView = true;
            },
            showDiscountConflictView() {
                this.isShowDiscountConflictView = true;
            },
            updateSelectedSingleGoods(data) {
                console.log(data);
                const transformData = data.map((item) => {
                    let isContainObj = null;
                    for (let i = 0; i < this.postData.activityData.goodsList.length; i++) {
                        if (this.postData.activityData.goodsList[i].goodsId === item.productId) {
                            isContainObj = this.postData.activityData.goodsList[i];
                        }
                    }
                    return {
                        type: 2,
                        goodsId: item.productId,
                        goodsType: item.productType,
                        goodsSubType: item.productSubType,
                        discount: isContainObj ? isContainObj.discount : '',
                        goods: item.productInfo,
                    };
                });
                this.postData.activityData.goodsList = this.postData.activityData.goodsList.filter((item) => {
                    return !item.goods;
                });
                this.postData.activityData.goodsList = this.postData.activityData.goodsList.concat(transformData);
            },
            /**
             * 拉取连锁下门店列表
             * <AUTHOR>
             * @date 2020-09-25
             */
            async fetchChainClinics() {
                try {
                    const { data } = await ClinicAPI.chainClinicV3();
                    this.chainSubClinicList = (data.rows || []).filter((item) => isChainSubClinic(item));
                } catch (error) {
                    console.log('fetchChainClinics error', error);
                }
            },

            async fetchMemberCardTypeCount() {
                try {
                    const { total = 0 } = await MarketingAPI.getMemberCardTypeDetailList();
                    this.allMemberCardTypeTotal = total;
                } catch (error) {
                    console.log('fetchMemberCardType error', error);
                }
            },
            async fetchActivityData() {
                if (this.activityId) {
                    const activityInitData = await discountApi.getDiscountPromotionUsingGET(this.activityId);
                    await this.fetchPageActivityDataDetails(activityInitData);
                }
            },
            async fetchPageActivityDataDetails(initData) {
                const handlePageData = (pageData) => {
                    // 这里可以处理每一页的数据
                    this.initActivityData({
                        ...initData,
                        goodsList: pageData.rows ?? [],
                    });
                    // 如果需要，可以在这里执行其他操作
                };
                const {
                    run,
                } = usePaginationSerial(
                    async (params) => {
                        const pageData = await MarketingAPI.getDiscountGoodsPageListDetail({
                            ...params, promotionId: this.activityId,
                        });
                        // 每次请求完成后立即处理数据
                        handlePageData(pageData);
                        return pageData;
                    },
                    {
                        limit: 1000,
                    },
                );
                try {
                    await run();
                } catch (e) {
                    console.log(e);
                }
            },
            initActivityData(data) {
                // 给每一项 goods 添加 isOriginData 作为源数据标识
                data.goodsList.forEach((item) => {
                    item.isOriginData = true;
                });
                this.postData.activityData = {
                    ...data,
                    goodsList: [...this.postData.activityData.goodsList, ...data.goodsList],
                };
                if (this.isFromCopy) {
                    this.postData.activityData.name = '';
                }
                if (this.postData.activityData.isForever === 0) {
                    this.$set(this.postData.activityData, 'dateRange', [
                        new Date(this.postData.activityData.beginDate),
                        new Date(this.postData.activityData.endDate),
                    ]);
                } else {
                    this.$set(this.postData.activityData, 'dateRange', []);
                }
                this.postData.activityData.goodsList = this.postData.activityData.goodsList.map((item) => {
                    if (item.isAlreadyInitialized) {
                        return item;
                    }
                    const formatDiscount = enlargeDiscount(item.discount, 10, 2);
                    return {
                        ...item,
                        discount: item.discountType ? item.discount : formatDiscount,
                        discountType: item.discountType || 0,
                        isAlreadyInitialized: true,
                    };
                }).filter((item) => {
                    // 复制的时候过滤掉已删除的分类
                    if (item.type === 1) {
                        return !!item.name;
                    }
                    return true;
                }) || [];
                this.originalGoodsList = clone(this.postData.activityData.goodsList || []);
                this.postData.patientType = this.postData.activityData.applicators.length === 0 ? 0 : 1;
                this.postData.organType = this.postData.activityData.organs.length === 0 ? 0 : 1;
                this.postData.activityData.applicators = this.postData.activityData.applicators.map((item) => {
                    return {
                        ...item,
                        id: item.memberTypeId,
                        name: item.memberType.memberTypeName,
                    };
                });
                this.postData.activityData.organs = this.postData.activityData.organs.map((item) => {
                    return {
                        ...item,
                        id: item.clinicId,
                        name: item.organ.name || '',
                    };
                });
            },
            async checkConflictData() {
                if (this.activityId && !this.isFromCopy && !this.isOnlyShow) {
                    this.conflictData = await discountApi.queryPromotionDiscountConflictsUsingGET(this.activityId);
                }
            },
            changeDate(date) {
                this.postData.activityData.beginDate = date[0];
                this.postData.activityData.endDate = date[1];
            },
            handleUpdateGoodsTypeList() {
                this.$nextTick(() => {
                    const el = this.$refs.discountFormDialog.$el;
                    el.querySelector('.abc-dialog-body').scrollTop = el.scrollHeight;
                });
            },
            filterGoodsList(goodsList, filterParams) {
                const list = goodsList;
                if (filterParams.id) {
                    return list.filter((item) => item.goodsId === filterParams.id || item.id === filterParams.id);
                }
                return list;
            },
            async searchGoodsInfo(keyword, callback) {
                keyword = keyword.trim();
                let dataList = [];
                if (keyword) {
                    try {
                        const { data } = await GoodsV3API.searchGoods({
                            keyword,
                            offset: 0,
                            limit: 50,
                        });
                        dataList = data.list || [];

                        // 分类查询
                        const goodsList = this.postData.activityData.goodsList.filter((item) => item.name?.includes(keyword));
                        goodsList.forEach((item) => {
                            const target = dataList.find((it) => it.goodsId === item.goodsId);
                            if (!target) dataList.push(item);
                        });
                    } catch (error) {
                        console.log('querySearchAsync error', error);
                    }
                }
                return callback(dataList);
            },
            selectGoods(item) {
                this.keyword = item.medicineCadn || item.name;
                this.filterParams.id = item.id || item.goodsId;
            },
            clearKeyword() {
                this.keyword = '';
                this.filterParams.id = '';
            },
            handleBlur() {
                if (!this.keyword.trim()) {
                    this.clearKeyword();
                }
            },
            handleOptTracker(goodsItem,optType) {
                if (!goodsItem) return;
                if (optType === OPT_TYPES.DELETE) {
                    this.goodsTracker.saveDeleteItem(goodsItem);
                }
                if (optType === OPT_TYPES.ADD) {
                    this.goodsTracker.saveAddItem(goodsItem);
                }
                if (optType === OPT_TYPES.UPDATE) {
                    this.goodsTracker.saveUpdateItem(goodsItem);
                }
            },
        },
    };
</script>

<style lang="scss" rel="stylesheet/scss">
    @import '../../../styles/theme';

    $defaultSpace: 24px;

    .discount-form {
        .discount-item-spacing {
            margin-right: 4px;
            margin-bottom: 4px;
        }

        span {
            font-size: 14px;
        }

        .discount-form .data-item span {
            color: $T1;
        }

        .span-gray {
            color: $T3;
        }

        .span-bold {
            color: $T1;
        }

        .line-space {
            margin-bottom: $defaultSpace;
        }

        .line-sub-row {
            display: flex;
            flex-direction: row;
            align-items: center;

            label {
                font-size: 14px;
            }

            .radio-group {
                .abc-radio {
                    height: 48px;
                }
            }
        }

        .activity-range-wrapper {
            display: flex;
            flex-direction: row;
            align-items: flex-start;

            .activity-range-label {
                margin-right: 23px;
                line-height: 24px;
            }
        }

        .line-sub-row + .line-sub-row {
            margin-top: 8px;
        }

        .line-space-padding {
            padding-bottom: $defaultSpace;
        }

        .separator-line {
            border-bottom: 1px dashed $P6;
        }

        .date-selector-wrapper {
            display: inline-block;

            .date-selector-place-holder {
                position: absolute;
                left: -144px;
                z-index: 1000;
                display: inline-block;
                width: 141px;
                height: 32px;
                line-height: 32px;
                text-align: right;
                vertical-align: middle;
                pointer-events: none;
                background-color: $S2;
                border: 1px solid $P1;
                border-radius: var(--abc-border-radius-small);
            }
        }

        .radio-group {
            display: flex;
            flex-direction: column;

            .abc-radio-label {
                color: $T1;
            }
        }

        .close-img {
            width: 14px;
            height: 14px;
            vertical-align: middle;
            cursor: pointer;
            transform: translateX(26px);
        }

        .error {
            color: $R2;
        }

        .single-good-wrapper {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            width: 96%;
            height: 40px;
            padding-right: 6px;
            padding-left: 10px;
            border: $P1 1px solid;
            border-radius: var(--abc-border-radius-small);

            .discount {
                margin-left: 8px;
                color: #ff3366;
            }

            label {
                color: $T1;
            }

            .good-discount-item {
                display: inline-block;
                width: 72px;
                height: 28px;
                font-size: 0;
                color: $T1;
                border: 1px solid $P1;
                transform: translateX(14px);

                .abc-input__inner {
                    height: 26px;
                    border: 1px solid transparent;
                    border-radius: 0;
                }

                .left {
                    width: 45px;
                    font-size: 0;
                    border-right: solid 1px $P1;
                }

                .right {
                    width: 25px;
                    background-color: $P3;
                }

                div {
                    display: inline-block;
                    height: 100%;
                    font-size: 14px;
                    line-height: 26px;
                    text-align: center;
                    vertical-align: middle;
                }
            }
        }
    }
</style>
