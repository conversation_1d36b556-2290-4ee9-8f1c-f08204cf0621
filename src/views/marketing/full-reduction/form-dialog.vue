<template>
    <div class="full-reduction-coupon-wrapper">
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            :auto-focus="!id"
            :title="title"
            content-styles="height:604px;"
            data-cy="edit-full-reduction-coupon-dialog"
            size="xlarge"
        >
            <abc-form
                ref="discountForm"
                v-abc-loading="loading"
                class="full-reduction-coupon-form"
                item-block
                item-no-margin
                label-position="left"
            >
                <biz-setting-form :label-width="56" no-limit-width>
                    <biz-setting-form-group>
                        <biz-setting-form-item label="活动名称">
                            <abc-form-item
                                :validate-event="validateName"
                                required
                            >
                                <abc-input
                                    v-model="postData.name"
                                    v-abc-focus-selected
                                    :disabled="isTakeOff"
                                    :max-length="20"
                                    :width="240"
                                    show-max-length-tips
                                    type="text"
                                    data-cy="full-reduction-coupon-name-input"
                                    @input="filterEmoji"
                                >
                                </abc-input>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="活动时间">
                            <abc-radio-group v-model="postData.isForever">
                                <abc-radio :disabled="isTakeOff" :label="1" data-cy="full-reduction-coupon-forever-radio">
                                    永久有效
                                </abc-radio>
                                <abc-flex gap="8">
                                    <abc-radio
                                        :disabled="isTakeOff"
                                        :label="0"
                                        data-cy="full-reduction-coupon-assign-date-radio"
                                    >
                                        指定时间
                                    </abc-radio>
                                    <abc-form-item v-if="postData.isForever === 0" required>
                                        <abc-date-picker
                                            v-model="validDate"
                                            type="daterange"
                                            clearable
                                            :disabled="isTakeOff"
                                            :picker-options="pickerOptions"
                                            data-cy="full-reduction-coupon-assign-date-picker"
                                            @change="handleValidDateChange"
                                        >
                                        </abc-date-picker>
                                    </abc-form-item>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <marketing-select-card-item
                            label="活动对象"
                            edit-btn-text="选择会员"
                            :tag-width="148"
                            :tag-data.sync="postData.memberTypes"
                            :get-icon-function="() => 's-role-color'"
                            :is-show-card="postData.isAllPatients === 0"
                            :disabled="isTakeOff"
                            @openDialog="showMemberDialog = true"
                        >
                            <abc-text
                                v-if="!postData.isAllPatients && !postData.memberTypes.length && memberError"
                                slot="tips"
                                size="mini"
                                theme="warning-light"
                            >
                                未选择指定会员
                            </abc-text>
                            <template #radio-group>
                                <abc-radio-group
                                    v-model="postData.isAllPatients"
                                    :disabled="isTakeOff"
                                >
                                    <abc-radio
                                        :label="1"
                                        :disabled="isTakeOff"
                                        data-cy="full-reduction-coupon-all-patients"
                                    >
                                        所有患者
                                    </abc-radio>
                                    <abc-radio
                                        :label="0"
                                        :disabled="isTakeOff"
                                        data-cy="full-reduction-coupon-assign-patients"
                                    >
                                        指定会员
                                    </abc-radio>
                                </abc-radio-group>
                            </template>
                        </marketing-select-card-item>

                        <marketing-select-card-item
                            v-if="isChain"
                            has-divider
                            label="活动门店"
                            edit-btn-text="选择门店"
                            :tag-width="148"
                            :tag-data.sync="postData.clinics"
                            :get-icon-function="() => 's-organization-color'"
                            :is-show-card="postData.isAllClinics === 0"
                            :disabled="isTakeOff"
                            @openDialog="showClinicsDialog = true"
                        >
                            <abc-text
                                v-if="!postData.isAllClinics && !postData.clinics.length && clinicsError"
                                slot="tips"
                                size="mini"
                                theme="warning-light"
                            >
                                未选择指定门店
                            </abc-text>
                            <template #radio-group>
                                <abc-radio-group
                                    v-model="postData.isAllClinics"
                                    :item-block="true"
                                    :disabled="isTakeOff"
                                >
                                    <abc-radio
                                        :disabled="isTakeOff || disabledAllSubClinic"
                                        :label="1"
                                        class="span-bold"
                                    >
                                        所有门店
                                    </abc-radio>
                                    <abc-radio
                                        :disabled="isTakeOff"
                                        :label="0"
                                        class="span-bold"
                                    >
                                        指定门店
                                    </abc-radio>
                                </abc-radio-group>
                            </template>
                        </marketing-select-card-item>

                        <biz-setting-form-item label="参与范围">
                            <select-goods-type-list
                                :goods-type-list.sync="postData.goodsList"
                                @change="handleGoodsListChange"
                                @handleOptTracker="handleOptTracker"
                            >
                                <template #check-box>
                                    <abc-checkbox
                                        v-model="postData.onlyOriginalPrice"
                                        :disabled="isTakeOff"
                                        style="margin-right: 0;"
                                        type="number"
                                        data-cy="full-reduction-coupon-only-original-price-checkbox"
                                    >
                                        仅购买原价商品时可用
                                    </abc-checkbox>
                                </template>
                                <abc-text
                                    v-if="goodsError"
                                    slot="tips"
                                    size="mini"
                                    theme="warning-light"
                                >
                                    未选择参与活动商品
                                </abc-text>
                                <template #search>
                                    <abc-autocomplete
                                        v-if="postData.goodsList.length"
                                        v-model.trim="keyword"
                                        :width="220"
                                        :inner-width="560"
                                        placeholder="商品名/首字母"
                                        clearable
                                        :fetch-suggestions.sync="searchGoodsInfo"
                                        search-goods-info
                                        :async-fetch="true"
                                        @enterEvent="selectGoods"
                                        @clear="clearKeyword"
                                        @blur="handleBlur"
                                    >
                                        <abc-icon slot="prepend" icon="search"></abc-icon>
                                        <template slot="suggestions" slot-scope="{ suggestion }">
                                            <dt
                                                class="suggestions-item"
                                                @click="selectGoods(suggestion)"
                                            >
                                                <div
                                                    style="width: 240px; min-width: 240px; max-width: 240px; padding-right: 10px;"
                                                    class="ellipsis"
                                                    :title="suggestion | goodsFullName"
                                                >
                                                    {{ suggestion | goodsFullName }}
                                                </div>
                                                <div style="width: 140px; padding-right: 10px;" class="ellipsis" :title="suggestion | goodsDisplaySpec">
                                                    {{ suggestion | goodsDisplaySpec }}
                                                </div>
                                                <div style="flex: 1; padding-right: 10px;" class="ellipsis" :title="suggestion.manufacturer">
                                                    {{ suggestion.manufacturer }}
                                                </div>
                                            </dt>
                                        </template>
                                    </abc-autocomplete>
                                </template>
                                <template #table>
                                    <select-goods-type-table
                                        :goods-list.sync="postData.goodsList"
                                        :disabled="isTakeOff"
                                        show-except-items
                                        is-member-or-discount
                                        only-show-exception
                                        :show-empty="true"
                                        :filter-params="filterParams"
                                        :filter-func="filterGoodsList"
                                        @handleOptTracker="handleOptTracker"
                                    >
                                    </select-goods-type-table>
                                </template>
                            </select-goods-type-list>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="优惠设置">
                            <abc-descriptions
                                v-for="(item, index) in postData.giftRules"
                                :key="`id${index}`"
                                :column="1"
                                :label-width="88"
                                grid
                                :need-input-style="false"
                                size="large"
                                label-vertical-align="center"
                            >
                                <template #title>
                                    <abc-flex justify="space-between" align="center" style="width: 100%;">
                                        <div>{{ `${computeLevel(index + 1)}` }}级优惠</div>
                                        <abc-delete-icon
                                            v-if="!isTakeOff && postData.giftRules && postData.giftRules.length > 1"
                                            theme="dark"
                                            @delete="deleteDiscount(index)"
                                        ></abc-delete-icon>
                                    </abc-flex>
                                </template>
                                <abc-descriptions-item label="优惠类型">
                                    <abc-flex justify="space-between">
                                        <abc-form-item
                                            :validate-event="orderThresholdValidate(index, item.orderThresholdPrice)"
                                            required
                                        >
                                            <abc-space size="middle">
                                                <div>
                                                    <abc-text style="margin-right: 8px;">
                                                        {{ item.isCycle ? '每' : '' }}满
                                                    </abc-text>
                                                    <abc-input
                                                        v-model="item.orderThresholdPrice"
                                                        v-abc-focus-selected
                                                        :config="{
                                                            max: 1000000,
                                                            formatLength: 2,
                                                        }"
                                                        :disabled="isTakeOff"
                                                        :width="88"
                                                        data-cy="order-threshold-price-input"
                                                        type="money"
                                                    >
                                                        <span slot="append">元</span>
                                                    </abc-input>
                                                </div>
                                                <div>
                                                    <abc-checkbox
                                                        v-model="item.isDiscounted"
                                                        :disabled="isTakeOff"
                                                        type="number"
                                                        data-cy="is-discounted-checkbox"
                                                    >
                                                        满减
                                                    </abc-checkbox>
                                                    <abc-checkbox
                                                        v-model="item.isGiftCoupon"
                                                        :disabled="isTakeOff"
                                                        type="number"
                                                        data-cy="is-gift-coupon-checkbox"
                                                    >
                                                        满返
                                                    </abc-checkbox>
                                                    <abc-checkbox
                                                        v-model="item.isGiftGoods"
                                                        :disabled="isTakeOff"
                                                        type="number"
                                                        data-cy="is-gift-goods-checkbox"
                                                    >
                                                        满赠
                                                    </abc-checkbox>
                                                </div>
                                            </abc-space>
                                        </abc-form-item>
                                        <abc-flex align="center">
                                            <abc-tooltip
                                                placement="top-end"
                                                theme="black"
                                                size="small"
                                                :disabled="!disabledCircle(index)"
                                                content="只可将最高级优惠设置成循环累计"
                                            >
                                                <abc-checkbox
                                                    v-model="item.isCycle"
                                                    :disabled="disabledCircle(index)"
                                                    style="margin-right: 0;"
                                                    type="number"
                                                    data-cy="is-cycle-checkbox"
                                                >
                                                    循环累计
                                                </abc-checkbox>
                                            </abc-tooltip>

                                            <abc-tooltip-info placement="bottom-end">
                                                <div>
                                                    <p>* 循环累计：可设置每满X元，进行满减/满返/满赠</p>
                                                    <p style="font-size: 12px; color: #96a4b3;">
                                                        例：若设置每满100元，立减10元。消费200元时，将立减20元/满返/满赠
                                                    </p>
                                                    <br />
                                                    <p>* 设置多级优惠时，循环累计只可在最高级优惠上设置应用</p>
                                                    <div style="font-size: 12px; color: #96a4b3;">
                                                        <p>
                                                            例：若设置一级优惠满100元，立减10元，二级优惠每满200元，立减20元
                                                        </p>
                                                        <p>若消费110元，将立减10元；</p>
                                                        <p>若消费210元，将立减20元；</p>
                                                        <p>若消费310元，将立减20元；</p>
                                                        <p>若消费410元，将立减40元</p>
                                                    </div>
                                                </div>
                                            </abc-tooltip-info>
                                            <abc-text
                                                v-if="item.noDiscount"
                                                theme="warning-light"
                                                size="mini"
                                            >
                                                未选择优惠项
                                            </abc-text>
                                        </abc-flex>
                                    </abc-flex>
                                </abc-descriptions-item>
                                <abc-descriptions-item content-padding="0" label="优惠内容">
                                    <template v-if="item.isDiscounted">
                                        <abc-form-item
                                            :validate-event="giftRuleValidate(index, item.discountedPrice)"
                                            required
                                        >
                                            <abc-space size="middle" style="padding: 8px 10px;">
                                                <abc-tag-v2
                                                    shape="round"
                                                    theme="danger"
                                                    size="mini"
                                                    variant="outline"
                                                >
                                                    满减
                                                </abc-tag-v2>
                                                <abc-space>
                                                    <span>立减</span>
                                                    <abc-input
                                                        v-model="item.discountedPrice"
                                                        v-abc-focus-selected
                                                        :config="{
                                                            max: 1000000,
                                                            formatLength: 2,
                                                        }"
                                                        :disabled="isTakeOff"
                                                        :width="88"
                                                        type="money"
                                                        data-cy="discounted-price-input"
                                                    >
                                                        <span slot="append">元</span>
                                                    </abc-input>
                                                </abc-space>
                                            </abc-space>
                                        </abc-form-item>
                                    </template>

                                    <abc-divider v-if="item.isDiscounted && (item.isGiftCoupon || item.isGiftGoods)" margin="none" variant="dashed"></abc-divider>

                                    <template v-if="item.isGiftCoupon">
                                        <abc-space :size="12" align="baseline" style="min-height: 40px; padding: 8px 10px;">
                                            <abc-tag-v2
                                                shape="round"
                                                theme="danger"
                                                size="mini"
                                                variant="outline"
                                            >
                                                满返
                                            </abc-tag-v2>
                                            <abc-flex :gap="12" align="baseline">
                                                <abc-button
                                                    v-if="!isTakeOff"
                                                    class="add-product"
                                                    type="text"
                                                    data-cy="gift-coupon-add-button"
                                                    @click="openGiftCouponDialog(item.giftCoupon, index)"
                                                >
                                                    添加
                                                </abc-button>
                                                <abc-flex
                                                    v-if="item.giftCoupon?.length"
                                                    wrap="wrap"
                                                    gap="middle"
                                                >
                                                    <label-input-v2
                                                        v-for="(coupon, couponIndex) in item.giftCoupon"
                                                        :key="coupon.promotionId"
                                                        v-model="coupon.count"
                                                        :config="{
                                                            max: 100,
                                                        }"
                                                        :disabled="isTakeOff"
                                                        :is-take-off="coupon.status === 9 || coupon.status === 19"
                                                        :name="coupon.name"
                                                        :status-name="getStatusName(coupon.status)"
                                                        icon="s-commodity-color"
                                                        unit="张"
                                                        data-cy="gift-coupon"
                                                        @delete="deleteGiftCoupon(item.giftCoupon, couponIndex)"
                                                    ></label-input-v2>
                                                </abc-flex>

                                                <abc-text
                                                    v-if="showGiftCouponError(item)"
                                                    theme="warning-light"
                                                    size="mini"
                                                >
                                                    未添加优惠券
                                                </abc-text>
                                            </abc-flex>
                                        </abc-space>
                                    </template>

                                    <abc-divider v-if="item.isGiftCoupon && item.isGiftGoods" margin="none" variant="dashed"></abc-divider>

                                    <template v-if="item.isGiftGoods">
                                        <abc-space :size="12" style=" min-height: 40px; padding: 8px 10px;">
                                            <abc-tag-v2
                                                shape="round"
                                                theme="danger"
                                                size="mini"
                                                variant="outline"
                                            >
                                                满赠
                                            </abc-tag-v2>
                                            <abc-flex :gap="12" align="baseline">
                                                <abc-button
                                                    v-if="!isTakeOff"
                                                    class="add-product"
                                                    type="text"
                                                    data-cy="gift-goods-add-button"
                                                    @click="addGiftGoods(item.giftGoods, index)"
                                                >
                                                    添加
                                                </abc-button>
                                                <abc-flex
                                                    v-if="item.giftGoods?.length"
                                                    wrap="wrap"
                                                    gap="middle"
                                                >
                                                    <label-input-v2
                                                        v-for="(goods, giftIndex) in item.giftGoods"
                                                        :key="goods.goodsId"
                                                        v-model="goods.count"
                                                        :disabled="isTakeOff"
                                                        :name="goods.goods ? goods.goods.medicineCadn || goods.goods.name : goods.name"
                                                        :unit="formatUnit(goods)"
                                                        icon="s-commodity-color"
                                                        data-cy="gift-goods"
                                                        @delete="deleteGiftGoods(item.giftGoods, giftIndex)"
                                                    ></label-input-v2>
                                                </abc-flex>
                                                <abc-text
                                                    v-if="showGiftGoodsError(item)"
                                                    theme="warning-light"
                                                    size="mini"
                                                >
                                                    未添加赠品
                                                </abc-text>
                                            </abc-flex>
                                        </abc-space>
                                    </template>
                                </abc-descriptions-item>
                            </abc-descriptions>
                            <div v-if="postData.giftRules.length < 3 && !isTakeOff" class="add-preferential-level">
                                <abc-tooltip
                                    placement="top-start"
                                    theme="black"
                                    size="small"
                                    :disabled="!hasCircle"
                                    :content="addDiscountTips"
                                >
                                    <abc-button
                                        type="text"
                                        @click="addDiscount"
                                    >
                                        添加{{ computeLevel(postData.giftRules.length + 1) }}级优惠
                                    </abc-button>
                                </abc-tooltip>

                                <abc-text theme="gray" size="mini">
                                    每级优惠不叠加，如：满足二级优惠条件后则不再享有一级优惠。最多支持三级优惠。
                                </abc-text>
                            </div>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>
            <div slot="footer" class="dialog-footer">
                <abc-button
                    v-if="isActive || isNoActive"
                    style="margin-right: auto;"
                    type="danger"
                    data-cy="full-reduction-coupon-dialog-stop"
                    @click="stopPromotion"
                >
                    终止
                </abc-button>
                <abc-button
                    v-if="isTakeOff"
                    style="margin-right: auto;"
                    type="danger"
                    data-cy="full-reduction-coupon-dialog-delete"
                    @click="handleDeleteActive"
                >
                    删除
                </abc-button>
                <abc-button
                    v-if="!isTakeOff"
                    :loading="btnLoading"
                    data-cy="full-reduction-coupon-dialog-confirm"
                    @click="submit"
                >
                    确定
                </abc-button>
                <abc-button type="blank" data-cy="full-reduction-coupon-dialog-cancel" @click="showDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
        <!--选择赠品-->
        <select-goods-item-list
            v-if="showGiftGoodsDialog"
            v-model="showGiftGoodsDialog"
            :need-goods-count="true"
            :product-types="giftGoodsType"
            :select-goods-list="selectedGiftGoods"
            @change="changeGiftGoods"
        >
        </select-goods-item-list>

        <!--选择优惠券-->
        <select-coupon-dialog
            v-if="showGiftCouponDialog"
            v-model="showGiftCouponDialog"
            :selected="selectedGiftCoupon"
            @change="changeGiftCoupon"
        >
        </select-coupon-dialog>

        <clinic-transfer
            v-if="showClinicsDialog"
            v-model="showClinicsDialog"
            :clinics="postData.clinics"
            title="指定门店"
            :access-key="accessKey"
            @confirm="(list) => postData.clinics = list"
        ></clinic-transfer>

        <member-type-transfer
            v-if="showMemberDialog"
            v-model="showMemberDialog"
            :selecteds="postData.memberTypes"
            @confirm="(list) => postData.memberTypes = list"
        >
        </member-type-transfer>
    </div>
</template>

<script>
    import LabelInputV2 from '../components/label-input-v2/index';
    import SelectCouponDialog from '../components/select-coupon-dialog/index';

    import {
        level, PromotionStatus,
    } from '../data/containts';

    import ClinicAPI from 'api/clinic';
    import MarketingAPI from 'api/marketing';

    import { mapGetters } from 'vuex';
    import EmojiFilter from 'utils/emoji-filter';

    import { isChainSubClinic } from 'views/common/clinic.js';
    import { GoodsTypeEnum } from '@abc/constants';
    import AbcAccess from '@/access/utils.js';

    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';
    import MarketingSelectCardItem from '@/views/marketing/components/marketing-select-card-item.vue';
    import SelectGoodsTypeList from 'views/marketing/components/select-goods-type-list/select-goods-type-list.vue';
    import SelectGoodsTypeTable from 'views/marketing/components/select-goods-type-list/select-goods-type-table.vue';
    import SelectGoodsItemList from 'views/marketing/components/select-goods-type-list/select-goods-item-list.vue';
    import GoodsV3API from 'api/goods/index-v3';
    import usePaginationSerial from 'views/marketing/hooks/use-pagination-serial';
    import useDataOperationTracker, { OPT_TYPES } from 'views/marketing/hooks/use-data-operation-tracker';


    const ClinicTransfer = () => import('components/clinic-transfer/index.vue');
    const MemberTypeTransfer = () => import('components/member-type-transfer/index.vue');

    export default {
        components: {
            LabelInputV2,
            SelectCouponDialog,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            MarketingSelectCardItem,
            SelectGoodsTypeList,
            SelectGoodsTypeTable,
            MemberTypeTransfer,
            ClinicTransfer,
            SelectGoodsItemList,
        },
        props: {
            visible: {
                type: Boolean,
                default: false,
            },
            id: {
                type: String,
                default: '',
            },
            isCopy: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            // 初始化数据跟踪器
            const goodsTracker = useDataOperationTracker();
            return {
                goodsTracker,
            };
        },
        data() {
            return {
                accessKey: AbcAccess.accessMap.MARKET_PROMOTION,
                postData: {
                    name: '',
                    isForever: 1,
                    beginDate: '',
                    endDate: '',
                    onlyOriginalPrice: 1,
                    isAllPatients: 1,
                    isAllClinics: 1,
                    clinics: [],
                    memberTypes: [],
                    goodsList: [],
                    giftRules: [
                        {
                            noDiscount: false,
                            orderThresholdPrice: '',
                            discountedPrice: '',
                            isDiscounted: 0, // 是否有满减
                            isGiftCoupon: 0, // 是否有满减
                            noGiftCoupon: false,
                            isGiftGoods: 0, // 是否有满减
                            noGiftGoods: false,
                            isCycle: 0, // 是否循环立减
                            giftCoupon: [],
                            giftGoods: [],
                        },
                    ],
                },
                pickerOptions: {
                    disabledDate: this.disabledBeginDate,
                    endDisabledDate: this.disabledEndDate,
                },
                showMemberDialog: false,
                showClinicsDialog: false,
                validDate: [],
                loading: false,
                btnLoading: false,
                memberError: false,

                // 优惠设置 满减 参数
                showGiftGoodsDialog: false,
                giftGoodsIndex: -1,
                selectedGiftGoods: [],
                // 优惠设置 满返 参数
                showGiftCouponDialog: false,
                giftCouponIndex: -1,
                selectedGiftCoupon: [],

                clinicsError: false,
                goodsError: false,
                selectError: false,

                showReverseType: [1, 2, 3, 4, 7],
                allMemberCardTypeTotal: 0,
                showGoodsListDialog: false,

                chainSubClinicList: [], // 连锁下子店列表
                giftGoodsType: [GoodsTypeEnum.MEDICINE,
                                GoodsTypeEnum.MATERIAL,
                                GoodsTypeEnum.TREATMENT,
                                GoodsTypeEnum.EXAMINATION,
                                GoodsTypeEnum.OTHER,
                                GoodsTypeEnum.GOODS,
                                GoodsTypeEnum.EYEGLASSES,
                                GoodsTypeEnum.COMPOSE,
                                GoodsTypeEnum.SURGERY],

                keyword: '',
                filterParams: {
                    id: '',
                },
            };
        },
        computed: {
            ...mapGetters(['isChain']),
            showDialog: {
                get() {
                    return this.visible;
                },
                set(v) {
                    this.$emit('update:visible', v);
                },
            },
            title() {
                return this.id && !this.isCopy ? '编辑满减返活动' : '新增满减返活动';
            },
            /**
             * @desc 已经结束活动 已经终止活动 不可编辑，可删除
             * <AUTHOR>
             */
            isTakeOff() {
                return this.postData.status === PromotionStatus.TAKE_OFF;
            },
            /**
             * @desc 未开始活动 可编辑 可终止
             * <AUTHOR>
             */
            isNoActive() {
                return this.postData.status === PromotionStatus.NO_ACTIVE;
            },
            /**
             * @desc 进行中活动 可编辑 可终止
             * <AUTHOR>
             */
            isActive() {
                return this.postData.status === PromotionStatus.ACTIVE;
            },
            participatePatientTips() {
                const { memberTypes } = this.postData;
                const { allMemberCardTypeTotal } = this;
                if (memberTypes.length === 0) {
                    return '';
                }
                if (memberTypes.length && memberTypes.length === allMemberCardTypeTotal) {
                    return '已选择全部会员';
                }
                if (memberTypes.length && memberTypes.length !== allMemberCardTypeTotal) {
                    const showApplictors = memberTypes.slice(0, 1);
                    const showApplictorsNames = showApplictors.map((item) =>
                        (item.memberType ? item.memberType.name : item.name),
                    );
                    const names = showApplictorsNames.map((item) =>
                        (item && item.length > 10 ? `${item.substring(0, 10)}...` : item),
                    );
                    if (memberTypes.length > 1) {
                        return `已选${names.join('、')}等${memberTypes.length}个会员卡`;
                    }
                    return `已选${names.join('、')}`;

                }
                return '';
            },
            /**
             * @desc 判断最高级优惠是否选中了循环累计
             * <AUTHOR>
             * @date 2020/4/18
             */
            hasCircle() {
                if (!this.postData.giftRules || !this.postData.giftRules.length) return false;
                const lastGiftRules = this.postData.giftRules[this.postData.giftRules.length - 1];
                return !!lastGiftRules.isCycle;
            },
            addDiscountTips() {
                if (this.postData.giftRules) {
                    return `无法添加。若需添加${
                        this._capitalNumber[this.postData.giftRules.length + 1]
                    }级优惠，需要取消${
                        this._capitalNumber[this.postData.giftRules.length]
                    }级优惠的循环累计设置（只可将最高级优惠设置成循环累计）`;
                }
                return '';
            },
            // 可选的门店 - 没有到期
            availableSubClinics() {
                return this.chainSubClinicList.filter((item) => AbcAccess.checkAvailableByEdition(this.accessKey, item.edition));
            },
            // 是否禁用选择全部门店
            disabledAllSubClinic() {
                return (
                    this.chainSubClinicList.length === 0 ||
                    this.chainSubClinicList.length !== this.availableSubClinics.length
                );
            },
        },
        watch: {
            disabledAllSubClinic: {
                handler(newValue) {
                    if (newValue && this.isChain) {
                        // 当禁用全部门店时，默认选中指定门店
                        this.postData.isAllClinics = 0;
                    }
                },
                immediate: true,
            },
        },
        async created() {
            this._capitalNumber = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
            if (this.id) {
                this.fetchData();
            }
            try {
                // 获取连锁下各门店
                await this.fetchChainClinics();

                const { total = 0 } = await MarketingAPI.getMemberCardTypeDetailList();
                this.allMemberCardTypeTotal = total;
            } catch (error) {
                this.isLoading = false;
            }
        },
        methods: {
            // 选择满减的单位展示
            formatUnit(goods) {
                if (goods && goods.goods) {
                    return goods.goods.packageUnit || goods.goods.pieceUnit || '次';
                }
                return '';
            },
            filterEmoji(val) {
                this.postData.name = EmojiFilter(val);
            },
            getStatusName(status) {
                let name = '';
                switch (status) {
                    case 1:
                        name = '';
                        break;
                    case 9:
                        name = '停止发券';
                        break;
                    case 19:
                        name = '已失效';
                        break;
                    default:
                        name = '已失效';
                        break;
                }
                return name;
            },
            disabledBeginDate(time) {
                const isEndTime = new Date(2038, 0, 18).getTime();
                let flag = false;
                const oneDayTimestamp = 24 * 60 * 60 * 1000;
                if (this.postData.endDate) {
                    flag = time.getTime() >= new Date(this.postData.endDate).getTime() - oneDayTimestamp;
                }
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            disabledEndDate(time) {
                const isEndTime = new Date(2038, 0, 18).getTime();
                let flag = false;
                const oneDayTimestamp = 24 * 60 * 60 * 1000;
                if (this.postData.beginDate) {
                    flag = time.getTime() <= new Date(this.postData.beginDate).getTime() - oneDayTimestamp;
                }
                return flag || time.getTime() < Date.now() - oneDayTimestamp || time.getTime() > isEndTime;
            },
            async fetchData() {
                this.loading = true;
                try {
                    const { data: fullReductionInitData } = await MarketingAPI.getPromotionDetail(this.id);
                    await this.fetchPageFullReductionDataDetails(fullReductionInitData);
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },
            async fetchPageFullReductionDataDetails(initData) {
                const handlePageData = (pageData) => {
                    // 这里可以处理每一页的数据
                    this.initFullReductionData({
                        ...initData,
                        goodsList: pageData.rows ?? [],
                    });
                    // 如果需要，可以在这里执行其他操作
                };
                const {
                    run,
                } = usePaginationSerial(
                    async (params) => {
                        const pageData = await MarketingAPI.getGiftGoodsPageListDetail({
                            ...params, promotionId: this.id,
                        });
                        // 每次请求完成后立即处理数据
                        handlePageData(pageData);
                        return pageData;
                    },
                    {
                        limit: 1000,
                    },
                );
                try {
                    await run();
                } catch (e) {
                    console.log(e);
                }
            },
            initFullReductionData(data) {
                // 给每一项 goods 添加 isOriginData 作为源数据标识
                data.goodsList.forEach((item) => {
                    item.isOriginData = true;
                });
                if (this.isCopy) {
                    data = this.copyHandler(data);
                } else {
                    data.giftRules = data.giftRules.map((item) => {
                        item.noDiscount = false;
                        item.noGiftGoods = false;
                        item.noGiftCoupon = false;
                        return item;
                    });
                }
                // 处理时间
                if (data.beginDate && data.endDate) {
                    this.validDate = [data.beginDate, data.endDate];
                }
                this.postData = {
                    ...data,
                    goodsList: [...this.postData.goodsList, ...data.goodsList],
                };
            },
            /**
             * @desc 复制的满减反活动需要清除ID，清空名称，如果选择了活动有效时间也清空
             * <AUTHOR>
             * @date 2020-07-30 20:09:49
             * @params
             * @return
             */
            copyHandler(data) {
                data.name = '';
                delete data.status;
                delete data.id;
                if (!data.isForever) {
                    data.beginDate = '';
                    data.endDate = '';
                }
                data.giftRules = data.giftRules.map((item) => {
                    item.noDiscount = false;
                    item.noGiftGoods = false;
                    item.noGiftCoupon = false;
                    delete item.id;
                    return item;
                });
                data.goodsList = data.goodsList.map((item) => {
                    delete item.id;
                    if (item.exceptItems && item.exceptItems.length) {
                        item.exceptItems = item.exceptItems.map((exceptItem) => {
                            delete exceptItem.id;
                            return exceptItem;
                        });
                    }
                    return item;
                }).filter((item) => {
                    // 复制的时候过滤掉已删除的分类
                    if (item.type === 1) {
                        return !!item.name;
                    }
                    return true;
                });
                return data;
            },

            /**
             * @desc 循环累计是否可选
             * <AUTHOR>
             * @date 2020/4/18
             * @params
             * @return
             */
            disabledCircle(index) {
                return this.isTakeOff || index !== this.postData.giftRules.length - 1;
            },
            validateName(val, callback) {
                if (!val.trim()) {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
                callback({
                    validate: true,
                });
            },
            orderThresholdValidate(index, val) {
                if (Number(val) <= 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能填0',
                        });
                    };
                }
                if (index === 1) {
                    if (
                        this.postData.giftRules[0] &&
                        Number(this.postData.giftRules[0].orderThresholdPrice) >= Number(val)
                    ) {
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: '必须比一级优惠大',
                            });
                        };
                    }
                }
                if (index === 2) {
                    if (
                        this.postData.giftRules[1] &&
                        Number(this.postData.giftRules[1].orderThresholdPrice) >= Number(val)
                    ) {
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: '必须比二级优惠大',
                            });
                        };
                    }
                }
            },
            giftRuleValidate(index, val) {
                if (Number(val) <= 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能填0',
                        });
                    };
                }
                if (
                    this.postData.giftRules[index] &&
                    Number(val) > this.postData.giftRules[index].orderThresholdPrice
                ) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能填大于满减金额',
                        });
                    };
                }
                if (index === 1) {
                    if (
                        this.postData.giftRules[0] &&
                        Number(this.postData.giftRules[0].discountedPrice) >= Number(val)
                    ) {
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: '必须比一级优惠大',
                            });
                        };
                    }
                }
                if (index === 2) {
                    if (
                        this.postData.giftRules[1] &&
                        Number(this.postData.giftRules[1].discountedPrice) >= Number(val)
                    ) {
                        return (_, callback) => {
                            callback({
                                validate: false,
                                message: '必须比二级优惠大',
                            });
                        };
                    }
                }
            },

            checked(item) {
                return item.selects.map((item) => item.name);
            },

            validate(val) {
                if (Number(val) <= 0) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '优惠门槛不能为0',
                        });
                    };
                }
            },

            // 计算几级优惠，最多三级根据数组的长度计算并翻译成大写
            computeLevel(index) {
                const levelItem = level.find((item) => item.level === index);
                return levelItem ? levelItem.alias : '一';
            },
            // 添加x级优惠
            addDiscount() {
                if (this.hasCircle) return false;
                const newLevel = {
                    noDiscount: false,
                    orderThresholdPrice: '',
                    discountedPrice: '',
                    isDiscounted: 0, // 是否有满减
                    isGiftCoupon: 0, // 是否有满返
                    noGiftCoupon: false,
                    isGiftGoods: 0, // 是否有满减
                    noGiftGoods: false,
                    isCycle: 0, // 是否循环立减
                    giftCoupon: [],
                    giftGoods: [],
                };
                this.postData.giftRules.push(newLevel);
            },
            deleteDiscount(index) {
                this.postData.giftRules.splice(index, 1);
            },
            /**
             * @desc 添加满赠商品
             * 保留 具体
             * <AUTHOR>
             * @date 2020/4/18
             */
            addGiftGoods(giftGoods, index) {
                this.showReverseType = [1, 2, 3, 4, 7, 11];
                this.giftGoodsIndex = index;
                this.selectedGiftGoods = giftGoods;
                this.showGiftGoodsDialog = true;
            },
            /**
             * @desc 选择满赠的商品
             * <AUTHOR>
             * @date 2020/4/18
             */
            changeGiftGoods(giftGoods) {
                this.postData.giftRules[this.giftGoodsIndex].giftGoods = giftGoods;
            },
            /**
             * @desc 删除某项满赠
             * <AUTHOR>
             * @date 2020/4/18
             */
            deleteGiftGoods(giftGoods, index) {
                giftGoods.splice(index, 1);
            },

            /**
             * @desc 选择满返优惠券
             * <AUTHOR>
             * @date 2020/4/18
             */
            openGiftCouponDialog(giftCoupon, index) {
                this.giftCouponIndex = index;
                this.selectedGiftCoupon = giftCoupon || [];
                this.showGiftCouponDialog = true;
            },
            /**
             * @desc 添加满返的优惠券
             * <AUTHOR>
             * @date 2020/4/19
             */
            changeGiftCoupon(list) {
                this.postData.giftRules[this.giftCouponIndex].giftCoupon = list;
            },
            /**
             * @desc 删除添加的优惠券
             * <AUTHOR>
             * @date 2020/4/19
             */
            deleteGiftCoupon(giftCoupon, index) {
                giftCoupon.splice(index, 1);
            },
            hasSelectedClinics() {
                return this.postData.isAllClinics || (this.postData.clinics && this.postData.clinics.length);
            },
            hasSelectMemberTypes() {
                return this.postData.isAllPatients || (this.postData.memberTypes && this.postData.memberTypes.length);
            },
            /**
             * @desc 未选择优惠券提示
             * <AUTHOR>
             * @date 2020/4/20
             */
            showGiftCouponError(item) {
                return item.noGiftCoupon && (!item.giftCoupon || !item.giftCoupon.length);
            },
            /**
             * @desc 勾选满赠，没有选择赠品提示
             * <AUTHOR>
             * @date 2020/4/20
             */
            showGiftGoodsError(item) {
                return item.noGiftGoods && (!item.giftGoods || !item.giftGoods.length);
            },
            hasGoods() {
                return this.postData.goodsList && this.postData.goodsList.length;
            },
            hasDiscount(item) {
                return item.isDiscounted || item.isGiftCoupon || item.isGiftGoods;
            },
            submit() {
                this.goodsError = false;
                this.$refs.discountForm.validate(async (valid) => {
                    if (valid) {
                        if (!this.hasSelectMemberTypes()) {
                            this.memberError = true;
                            return false;
                        }
                        if (!this.hasSelectedClinics()) {
                            this.clinicsError = true;
                            return false;
                        }
                        if (!this.hasGoods()) {
                            this.goodsError = true;
                            return false;
                        }

                        let errFlag = false;
                        for (let i = 0, len = this.postData.giftRules.length; i < len; i++) {
                            const giftRule = this.postData.giftRules[i];
                            giftRule.noDiscount = false;
                            if (!this.hasDiscount(giftRule)) {
                                giftRule.noDiscount = true;
                                errFlag = true;
                            }
                            giftRule.noGiftCoupon = false;
                            if (giftRule.isGiftCoupon && !giftRule.giftCoupon.length) {
                                giftRule.noGiftCoupon = true;
                                errFlag = true;
                            }
                            giftRule.noGiftGoods = false;
                            if (giftRule.isGiftGoods && !giftRule.giftGoods.length) {
                                giftRule.noGiftGoods = true;
                                errFlag = true;
                            }
                        }
                        if (errFlag) return false;
                        const data = this.transPostData();
                        this.btnLoading = true;
                        try {
                            const isConflict = await this.checkPromotion(data);
                            if (isConflict) {
                                this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: `活动时间、门店、对象相同时，同一分类/单品的满减返活动，只能存在一个。
                                            冲突商品已用 <i class="iconfont cis-icon-Attention conflict-icon"></i> 标识。`,
                                    onClose: () => {
                                        this.btnLoading = false;
                                    },
                                });
                            } else {
                                if (!this.id || this.isCopy) {
                                    await this.createPromotion(data);
                                } else {
                                    await this.updatePromotion(data);
                                }
                            }
                            this.btnLoading = false;
                        } catch (error) {
                            console.error(error);
                            this.btnLoading = false;
                        }
                    }
                });
            },
            transPostData() {
                let {
                    giftRules,
                } = this.postData;
                let memberTypeIds = [];
                let clinicIds = [];
                let data = null;
                const goodsList = (this.isCopy ? this.postData.goodsList : this.goodsTracker.operationData.value || []).map((item) => {
                    const exceptItems = ((this.isCopy ? item.exceptItems : item.exceptOperationTrackerItems) || [])?.map((exceptItem) => {
                        if (exceptItem.type === 3) {
                            return {
                                type: exceptItem.type || '',
                                clinicId: exceptItem?.clinicId || '',
                                employeeId: exceptItem?.employeeId || '',
                                employeeName: exceptItem?.employeeName || '',
                                departmentId: exceptItem?.departmentId || '',
                                clinicName: exceptItem?.clinicName || '',
                                departmentName: exceptItem?.departmentName || '',
                                optType: this.isCopy ? OPT_TYPES.ADD : exceptItem.optType,
                            };
                        }
                        return {
                            id: exceptItem.id || '',
                            type: exceptItem.type || '',
                            goodsId: exceptItem.goodsId || '',
                            goodsType: exceptItem.goodsType || '',
                            goodsSubType: exceptItem.goodsSubType || '',
                            goodsCMSpec: exceptItem.goodsCMSpec || '',
                            pharmacyType: exceptItem?.pharmacyType || exceptItem?.isAirPharmacy || 0,
                            isAirPharmacy: exceptItem?.pharmacyType || exceptItem?.isAirPharmacy || 0,
                            optType: this.isCopy ? OPT_TYPES.ADD : exceptItem.optType,
                        };
                    });
                    return {
                        id: item.id,
                        goodsId: item.goodsId,
                        type: item.type,
                        goodsType: item.goodsType,
                        goodsSubType: item.goodsSubType,
                        goodsCMSpec: item.goodsCMSpec,
                        exceptItems,
                        pharmacyType: item?.pharmacyType || item?.isAirPharmacy || 0,
                        isAirPharmacy: item?.pharmacyType || item?.isAirPharmacy || 0,
                        customTypeId: item.customTypeId,
                        optType: this.isCopy ? OPT_TYPES.ADD : item.optType,
                    };
                });
                giftRules = giftRules.map((item) => {
                    let {
                        giftCoupon, giftGoods,
                    } = item;
                    giftCoupon = giftCoupon.map((item) => {
                        return {
                            promotionId: item.promotionId || item.id,
                            count: item.count,
                        };
                    });
                    giftGoods = giftGoods.map((item) => {
                        return {
                            goodsId: item.goodsId,
                            count: item.count,
                        };
                    });
                    return {
                        discountedPrice: item.discountedPrice,
                        orderThresholdPrice: item.orderThresholdPrice,
                        isCycle: item.isCycle,
                        isDiscounted: item.isDiscounted,
                        isGiftGoods: item.isGiftGoods,
                        isGiftCoupon: item.isGiftCoupon,
                        giftCoupon,
                        giftGoods,
                    };
                });

                memberTypeIds = this.postData.memberTypes.map((item) => {
                    return item.id;
                });
                clinicIds = this.postData.clinics.map((item) => {
                    return item.id;
                });

                data = Object.assign({}, this.postData, {
                    giftRules, goodsList, memberTypeIds, clinicIds,
                });
                delete data.memberTypes;
                delete data.clinics;
                return data;
            },
            /**
             * @desc 创建满减活动
             * <AUTHOR>
             * @date 2020/4/17
             */
            async createPromotion(data) {
                try {
                    await MarketingAPI.addPromotion(data);
                    this.btnLoading = false;
                    this.$emit('update-list', true);
                    this.$Toast({
                        type: 'success',
                        message: '创建成功',
                    });
                    this.showDialog = false;
                } catch (e) {
                    this.btnLoading = false;
                    if (e.code === 28001) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${e.message}`,
                        });
                    }
                }
            },
            /**
             * @desc 更新满减活动
             * <AUTHOR>
             * @date 2020/4/18
             */
            async updatePromotion(data) {
                try {
                    await MarketingAPI.editPromotion(data);
                    this.$emit('update-list');
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.btnLoading = false;
                    this.showDialog = false;
                } catch (e) {
                    this.btnLoading = false;
                    if (e.code === 28001) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: `${e.message}`,
                        });
                    }
                }
            },
            /**
             * @desc 活动冲突检查
             * <AUTHOR>
             * @date 2020/4/20
             */
            async checkPromotion(postData) {
                try {
                    const { data } = await MarketingAPI.checkPromotion(postData);
                    const { conflicts } = data;
                    if (conflicts && conflicts.length) {
                        conflicts.forEach((item) => {
                            this.postData.goodsList.forEach((goods) => {
                                if (`${goods.goodsId}` === item.goodsId) {
                                    goods.isConflict = true;
                                    goods.conflictMessage = item.message;
                                }
                            });
                        });
                        this.postData.goodsList.sort((first, second) => {
                            return (+second.isConflict || 0) - (+first.isConflict || 0);
                        });
                        return true;
                    }
                    return false;
                } catch (e) {
                    //
                }
            },

            async stopPromotion() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '结束活动后不能恢复，是否确定结束？',
                    onConfirm: async () => {
                        await MarketingAPI.finished(this.id);
                        this.$Toast({
                            type: 'success',
                            message: '操作成功',
                        });
                        this.$emit('update-list');
                        this.showDialog = false;
                    },
                });
            },
            async handleDeleteActive() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除？',
                    onConfirm: async () => {
                        await MarketingAPI.delete(this.id);
                        this.$Toast({
                            type: 'success',
                            message: '删除成功',
                        });
                        this.$emit('update-list', true);
                        this.showDialog = false;
                    },
                });
            },
            /**
             * 拉取连锁下门店列表
             * <AUTHOR>
             * @date 2020-09-25
             */
            async fetchChainClinics() {
                try {
                    const { data } = await ClinicAPI.chainClinicV3();
                    this.chainSubClinicList = (data.rows || []).filter((item) => isChainSubClinic(item));
                } catch (error) {
                    console.log('fetchChainClinics error', error);
                }
            },
            handleValidDateChange(Date) {
                this.postData.beginDate = Date[0] || '';
                this.postData.endDate = Date[1] || '';
            },
            handleGoodsListChange() {
                this.goodsError = false;
            },
            filterGoodsList(goodsList, filterParams) {
                const list = goodsList;
                if (filterParams.id) {
                    return list.filter((item) => item.goodsId === filterParams.id || item.id === filterParams.id);
                }
                return list;
            },
            async searchGoodsInfo(keyword, callback) {
                keyword = keyword.trim();
                let dataList = [];
                if (keyword) {
                    try {
                        const { data } = await GoodsV3API.searchGoods({
                            keyword,
                            offset: 0,
                            limit: 50,
                        });
                        dataList = data.list || [];

                        // 分类查询
                        const goodsList = this.postData.goodsList.filter((item) => item.name?.includes(keyword));
                        goodsList.forEach((item) => {
                            const target = dataList.find((it) => it.goodsId === item.goodsId);
                            if (!target) dataList.push(item);
                        });
                    } catch (error) {
                        console.log('querySearchAsync error', error);
                    }
                }
                return callback(dataList);
            },
            selectGoods(item) {
                this.keyword = item.medicineCadn || item.name;
                this.filterParams.id = item.id || item.goodsId;
            },
            clearKeyword() {
                this.keyword = '';
                this.filterParams.id = '';
            },
            handleBlur() {
                if (!this.keyword.trim()) {
                    this.clearKeyword();
                }
            },
            handleOptTracker(goodsItem,optType) {
                if (!goodsItem) return;
                if (optType === OPT_TYPES.DELETE) {
                    this.goodsTracker.saveDeleteItem(goodsItem);
                }
                if (optType === OPT_TYPES.ADD) {
                    this.goodsTracker.saveAddItem(goodsItem);
                }
                if (optType === OPT_TYPES.UPDATE) {
                    this.goodsTracker.saveUpdateItem(goodsItem);
                }
            },
        },
    };
</script>
