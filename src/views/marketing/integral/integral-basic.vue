<template>
    <biz-setting-layout
        v-abc-loading="loadingFetch"
    >
        <biz-setting-content>
            <biz-setting-form :content-width="828" :label-width="112">
                <abc-form v-if="!!postData" ref="postData" item-no-margin>
                    <biz-setting-form-group>
                        <biz-setting-form-item vertical label="积分活动">
                            <biz-setting-form-item-tip tip="开启后，可设置积分自动获取/抵扣/清零规则">
                                <abc-checkbox v-model="postData.enable" @change="changeEnable">
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <biz-setting-form-group v-if="postData.enable" title="适用范围">
                        <marketing-select-card-item
                            edit-btn-text="选择患者"
                            :tag-width="140"
                            :label="`可使用积分的${viewDistributeConfig?.customerLabel || '患者'}`"
                            :tag-data.sync="postData.usageMemberTypes"
                            :get-icon-function="(item) => item.id !== noMemberId ? 's-vip-color' : ''"
                            @openDialog="visibleSelectTarget = true"
                        >
                            <abc-text v-if="showUsageErrorTips" slot="tips" theme="danger-light">
                                未选择使用{{ viewDistributeConfig?.customerLabel || '患者' }}范围
                            </abc-text>
                        </marketing-select-card-item>
                    </biz-setting-form-group>
                    <biz-setting-form-group v-if="postData.enable" title="获取规则">
                        <biz-setting-form-item vertical label="获取规则">
                            <biz-setting-form-item-indent>
                                <abc-checkbox
                                    v-model="postData.pointsAccumulateEnable"
                                    type="number"
                                    style="margin-right: 56px;"
                                    @change="handleChangePointsAccumulateEnable"
                                >
                                    成功消费得积分
                                </abc-checkbox>
                            </biz-setting-form-item-indent>
                            <biz-setting-form-item-indent v-if="visibleSetAccumulateRat && !allowIntegralRulesSettingByProfit">
                                <span>每消费</span>
                                <abc-form-item required>
                                    <abc-input
                                        v-model="postData.amountAccumulateRat"
                                        type="money"
                                        :max-length="5"
                                        style="margin: 0 4px;"
                                        :config="{
                                            max: 99999, formatLength: 2
                                        }"
                                        :input-custom-style="{ 'text-align': 'center' }"
                                        :width="62"
                                        @blur="handleBlurAmountAccumulateRat"
                                    >
                                        <span slot="append">元</span>
                                    </abc-input>
                                </abc-form-item>
                                <span>，积 </span>
                                <abc-form-item required>
                                    <abc-input
                                        v-model="postData.pointsAccumulateRat"
                                        type="number"
                                        :max-length="10"
                                        :config="{ max: 99999 }"
                                        :input-custom-style="{ 'text-align': 'center' }"
                                        :width="62"
                                        style="margin: 0 4px;"
                                        @blur="handleBlurAmountAccumulateRat"
                                    >
                                        <span slot="append">分</span>
                                    </abc-input>
                                </abc-form-item>
                            </biz-setting-form-item-indent>
                            <biz-setting-form-item-indent v-else-if="usageMemberTypeConfigListVisible">
                                <abc-table
                                    :data-list="usageMemberTypesConfigList"
                                    :render-config="memberRateTableConfig"
                                    :fill-height="false"
                                    :show-hover-tr-bg="false"
                                    :fixed-tr-height="false"
                                >
                                    <template
                                        #rule="{
                                            trData
                                        }"
                                    >
                                        <abc-table-cell v-if="!trData.pointsRule">
                                            <abc-flex
                                                class="ellipsis"
                                                style="width: 100%;"
                                                :title="getRuleText(trData, trData.pointsRule)"
                                            >
                                                {{ getRuleText(trData, trData.pointsRule) }}
                                            </abc-flex>
                                        </abc-table-cell>
                                        <abc-table-cell v-else style="display: block; padding: 8px 10px;">
                                            <div
                                                v-for="(it,index) in trData.ruleDetails"
                                                :key="index"
                                                class="ellipsis"
                                                :title="getRuleText(it, trData.pointsRule, index === trData.ruleDetails.length - 1)"
                                                style="width: 100%; line-height: 22px;"
                                            >
                                                {{ getRuleText(it, trData.pointsRule, index === trData.ruleDetails.length - 1) }}
                                            </div>
                                        </abc-table-cell>
                                    </template>
                                    <template
                                        #operation="{
                                            trData, index
                                        }"
                                    >
                                        <abc-table-cell>
                                            <abc-space align="center" :size="4">
                                                <abc-button
                                                    variant="text"
                                                    :min-width="32"
                                                    size="small"
                                                    @click="openRuleSettings(trData)"
                                                >
                                                    设置
                                                </abc-button>
                                                <abc-button
                                                    v-if="index !== 0"
                                                    size="small"
                                                    variant="text"
                                                    @click="copyRules(index)"
                                                >
                                                    复制上条
                                                </abc-button>
                                            </abc-space>
                                        </abc-table-cell>
                                    </template>
                                </abc-table>
                            </biz-setting-form-item-indent>
                        </biz-setting-form-item>

                        <biz-setting-form-item vertical label="参与范围">
                            <select-goods-type-list
                                :goods-type-list.sync="postData.applicationGoodsList"
                                :show-goods-select="postData.isApplicationSpecGoods === 1"
                                :show-type-select="postData.isApplicationSpecGoods === 1"
                            >
                                <abc-radio-group slot="radio-group" v-model="postData.isApplicationSpecGoods" @change="handleApplicationTypeChange">
                                    <biz-setting-form-item-indent>
                                        <abc-radio :label="0">
                                            全部项目
                                        </abc-radio>
                                    </biz-setting-form-item-indent>
                                    <biz-setting-form-item-indent>
                                        <abc-radio :label="1">
                                            指定项目
                                        </abc-radio>
                                    </biz-setting-form-item-indent>
                                </abc-radio-group>
                                <template #search>
                                    <abc-autocomplete
                                        v-if="postData.applicationGoodsList.length"
                                        v-model.trim="keyword"
                                        :width="220"
                                        :inner-width="560"
                                        placeholder="商品名/首字母"
                                        clearable
                                        :fetch-suggestions.sync="searchGoodsInfo"
                                        search-goods-info
                                        :async-fetch="true"
                                        @enterEvent="selectGoods"
                                        @clear="clearKeyword"
                                        @blur="handleBlur"
                                    >
                                        <abc-icon slot="prepend" icon="search"></abc-icon>
                                        <template slot="suggestions" slot-scope="{ suggestion }">
                                            <dt
                                                class="suggestions-item"
                                                @click="selectGoods(suggestion)"
                                            >
                                                <div
                                                    style="width: 240px; min-width: 240px; max-width: 240px; padding-right: 10px;"
                                                    class="ellipsis"
                                                    :title="suggestion | goodsFullName"
                                                >
                                                    {{ suggestion | goodsFullName }}
                                                </div>
                                                <div style="width: 140px; padding-right: 10px;" class="ellipsis" :title="suggestion | goodsDisplaySpec">
                                                    {{ suggestion | goodsDisplaySpec }}
                                                </div>
                                                <div style="flex: 1; padding-right: 10px;" class="ellipsis" :title="suggestion.manufacturer">
                                                    {{ suggestion.manufacturer }}
                                                </div>
                                            </dt>
                                        </template>
                                    </abc-autocomplete>
                                </template>

                                <template #table>
                                    <select-goods-type-table
                                        :goods-list.sync="postData.applicationGoodsList"
                                        :filter-params="filterParams"
                                        :filter-func="filterGoodsList"
                                        :show-empty="!!postData.applicationGoodsList.length"
                                        is-member-or-discount
                                        show-except-items
                                        only-show-exception
                                        :tips="'选择参与活动的项目范围'"
                                        @except-goods-change="changeExceptGoodsList"
                                    >
                                    </select-goods-type-table>
                                </template>
                            </select-goods-type-list>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <biz-setting-form-group v-if="postData.enable" title="抵扣规则">
                        <biz-setting-form-item vertical label="现金抵扣">
                            <biz-setting-form-item-indent>
                                <abc-checkbox
                                    v-model="postData.pointsDeductionEnable"
                                    type="number"
                                    @change="onChangeDeductionEnable"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-indent>
                            <biz-setting-form-item-indent v-if="visibleSetDeductionRat">
                                <span>收费时可使用积分抵扣现金</span>
                                <abc-form-item required style="margin-bottom: 0;">
                                    <abc-input
                                        v-model="postData.pointsDeductionRat"
                                        type="number"
                                        :max-length="5"
                                        :config="{
                                            min: 1, max: 99999
                                        }"
                                        :input-custom-style="{ 'text-align': 'center' }"
                                        :width="63"
                                        style="margin: 0 8px 0 4px;"
                                    >
                                        <span slot="append">积分</span>
                                    </abc-input>
                                    <span>= {{ postData.amountDeductionRat }} 元</span>
                                </abc-form-item>
                            </biz-setting-form-item-indent>
                        </biz-setting-form-item>
                        <biz-setting-form-item v-if="isShowPointsGoodsDeduction" vertical label="抵扣商品">
                            <select-goods-type-list
                                :goods-type-list.sync="postData.deductGoodsList"
                                :show-goods-select="!!postData.pointsGoodsDeductionEnable"
                                :show-type-select="false"
                            >
                                <abc-checkbox
                                    slot="radio-group"
                                    v-model="postData.pointsGoodsDeductionEnable"
                                    class="deduct-switch"
                                    @change="changeDeductionEnable"
                                >
                                    开启
                                </abc-checkbox>
                                <abc-text v-if="showDeductErrorTips" slot="tips" theme="danger-light">
                                    请选择抵扣项目
                                </abc-text>
                                <template #table>
                                    <select-goods-type-table
                                        ref="deductGoodsListTableRef"
                                        :goods-list.sync="postData.deductGoodsList"
                                        is-member-or-discount
                                        show-points
                                    >
                                    </select-goods-type-table>
                                </template>
                            </select-goods-type-list>
                        </biz-setting-form-item>
                        <biz-setting-form-item vertical label="退款后返还积分">
                            <biz-setting-form-item-tip :tip="`开启后，${viewDistributeConfig?.customerLabel || '患者'}使用积分抵扣现金付款，若全额退款，将返还积分`">
                                <abc-checkbox v-model="postData.pointsRefundEnable" type="number">
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <biz-setting-form-group v-if="postData.enable" title="积分有效期">
                        <biz-setting-form-item vertical label="自动清零">
                            <biz-setting-form-item-indent>
                                <abc-checkbox
                                    v-model="postData.pointsClearEnable"
                                    type="number"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-indent>
                            <biz-setting-form-item-tip
                                v-if="postData.pointsClearEnable"
                                :tip="`开启后，${viewDistributeConfig?.customerLabel || '患者'}的全部积分会在指定日期的 23:59:59 过期`"
                            >
                                <abc-text>自动清零日期：</abc-text>
                                <abc-date-picker
                                    v-model="selectDate"
                                    :clearable="false"
                                    :show-weeks="false"
                                    :show-year-seletor="false"
                                    :width="80"
                                    placeholder=""
                                    format="MM-DD"
                                    type="datequick"
                                    data-cy="e2e-date-picker"
                                >
                                </abc-date-picker>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </abc-form>
            </biz-setting-form>

            <member-type-transfer
                v-if="visibleSelectTarget"
                v-model="visibleSelectTarget"
                :selecteds="postData.usageMemberTypes"
                :show-non-member="isShowNonMember"
                :non-member-id="noMemberId"
                @confirm="onClickConfirm"
            >
            </member-type-transfer>

            <integral-basic-dialog
                v-if="visibleRuleDialog"
                v-model="visibleRuleDialog"
                :member-data="currentMemberData"
                @handleRuleConfirm="handleRuleConfirm"
            ></integral-basic-dialog>

            <template #footer>
                <biz-setting-footer>
                    <abc-button
                        v-if="!!postData"
                        :loading="loadingSave"
                        :disabled="disabledSave"
                        @click="onClickSave"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <select-goods-item-list
            v-if="showGoodsListDialog"
            v-model="showGoodsListDialog"
            from-referrer
            show-types
            :product-types="selectGoodsType"
            :select-goods-list="postData.applicationGoodsList"
            @change="changeGoodsList"
        ></select-goods-item-list>

        <!--选择例外的商品-->
        <select-goods-item-list
            v-if="showExceptGoodsListDialog"
            v-model="showExceptGoodsListDialog"
            from-referrer
            :is-expect="showExceptGoodsListDialog"
            :product-types="exceptGoodsType"
            :sub-type="exceptGoodsSubType"
            :c-m-spec="exceptGoodsCMSpec"
            :custom-type-id-list="exceptGoodsCustomTypeIdList"
            :select-goods-list="selectedExceptGoodsList"
            @change="changeExceptGoodsList"
        ></select-goods-item-list>
    </biz-setting-layout>
</template>

<script>
    import { isEqual } from 'utils/lodash';
    import * as utils from 'utils/index';
    import MarketingAPI from 'api/marketing';
    import IntegralApi from 'api/marketing/integral.js';
    const MemberTypeTransfer = () => import('components/member-type-transfer/index.vue');
    import { GoodsTypeEnum } from '@abc/constants';
    import clone from 'utils/clone';
    import { mapGetters } from 'vuex';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemIndent,
        BizSettingFormItemTip,
    } from '@/components-composite/setting-form/index.js';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import MarketingSelectCardItem from '@/views/marketing/components/marketing-select-card-item.vue';
    import SelectGoodsItemList from 'views/marketing/components/select-goods-type-list/select-goods-item-list.vue';
    import SelectGoodsTypeList from 'views/marketing/components/select-goods-type-list/select-goods-type-list.vue';
    import SelectGoodsTypeTable from 'views/marketing/components/select-goods-type-list/select-goods-type-table.vue';
    import IntegralBasicDialog from './integral-basic-dialog';
    import { createGUID } from 'utils/index';
    import GoodsV3API from 'api/goods/index-v3';

    export default {
        name: 'Integral',
        components: {
            SelectGoodsItemList,
            MemberTypeTransfer,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingFormItemIndent,
            MarketingSelectCardItem,
            SelectGoodsTypeTable,
            SelectGoodsTypeList,
            IntegralBasicDialog,
        },
        data() {
            return {
                noMemberId: '-1',
                visibleSelectTarget: false,
                GoodsTypeEnum,
                loadingFetch: false,
                loadingSave: false,
                memberTypes: [],
                pointsConfig: null,
                usageMemberTypesConfigList: [],
                usageMemberTypesConfigListCache: [],
                postData: null,
                showUsageErrorTips: false,
                showDeductErrorTips: false,
                showApplicationSpecErrorTips: false,
                selectGoodsType: [
                    GoodsTypeEnum.MEDICINE,
                    GoodsTypeEnum.MATERIAL,
                    GoodsTypeEnum.EXAMINATION,
                    GoodsTypeEnum.TREATMENT,
                    GoodsTypeEnum.OTHER,
                    GoodsTypeEnum.GOODS,
                    GoodsTypeEnum.EYEGLASSES,
                    GoodsTypeEnum.COMPOSE,
                    GoodsTypeEnum.SURGERY,
                ],
                cachePostData: null,
                timer: null,
                pageSize: 10,
                pageIndex: 0,
                exceptIndex: 0,
                exceptGoodsType: [],
                exceptGoodsSubType: '',
                exceptGoodsCMSpec: '',
                exceptGoodsCustomTypeIdList: [],
                selectedExceptGoodsList: [],
                showGoodsListDialog: false,
                showExceptGoodsListDialog: false,
                memberRateTableConfig: {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'name',
                            label: '会员等级',
                            style: {
                                width: '108px',
                                maxWidth: '108px',
                            },
                        },
                        {
                            key: 'rule',
                            label: '积分规则',
                            style: {
                                flex: 1,
                            },
                        },
                        {
                            key: 'operation',
                            label: '操作',
                            style: {
                                width: '132px',
                                maxWidth: '132px',
                            },
                        },
                    ],
                },
                visibleRuleDialog: false,
                currentMemberData: {},

                keyword: '',
                filterParams: {
                    id: '',
                },
            };
        },
        watch: {
            'postData.usageMemberTypes': {
                handler(val) {
                    if (val && this.usageMemberTypeConfigListVisible) {
                        this.validateUsageMemberTypesConfigList();
                    }
                },
                deep: true,
            },
            usageMemberTypeConfigListVisible: {
                handler(val) {
                    if (val) {
                        this.validateUsageMemberTypesConfigList();
                    }
                },
                deep: true,
            },
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            usageMemberTypeConfigListVisible() {
                return this.visibleSetAccumulateRat && this.allowIntegralRulesSettingByProfit;
            },
            // 允许积分毛利率同步
            allowIntegralRulesSettingByProfit() {
                return this.viewDistributeConfig.Marketing.allowIntegralRulesSettingByProfit;
            },
            isShowNonMember() {
                return this.viewDistributeConfig.Marketing.isShowNonMember;
            },
            isShowPointsGoodsDeduction() {
                return this.viewDistributeConfig?.Marketing.integralShowPointsGoodsDeduction;
            },
            maxPage() {
                return Math.ceil(this.postData.applicationGoodsList.length / this.pageSize);
            },
            currentPageData() {
                let startIndex = this.pageIndex * this.pageSize;
                let endIndex = startIndex + this.pageSize;
                const len =
                    (this.postData.applicationGoodsList && this.postData.applicationGoodsList.length) || 0;
                if (endIndex > len) {
                    endIndex = len;
                }
                if (startIndex < 0) {
                    startIndex = 0;
                }
                return this.postData.applicationGoodsList.slice(startIndex, endIndex);
            },
            // 是否显示积分获取规则比例
            visibleSetAccumulateRat() {
                const { pointsAccumulateEnable } = this.postData || {};
                return pointsAccumulateEnable === 1;
            },
            // 是否显示积分兑换现金比例
            visibleSetDeductionRat() {
                const { pointsDeductionEnable } = this.postData || {};
                return pointsDeductionEnable === 1;
            },
            // 是否有修改
            isUpdated() {
                return !isEqual(this.postData, this.cachePostData) || !isEqual(this.usageMemberTypesConfigList, this.usageMemberTypesConfigListCache);
            },
            // 禁用保存按钮
            disabledSave() {
                if (!this.pointsConfig || !this.postData || !this.isUpdated) {
                    return true;
                }
                return false;
            },
            // 最大日期
            maxDay() {
                let maxDay = 31;
                const { pointsClearMonth } = this.postData;
                if (pointsClearMonth) {
                    switch (parseInt(pointsClearMonth)) {
                        case 1:
                        case 3:
                        case 5:
                        case 7:
                        case 8:
                        case 10:
                        case 12:
                            maxDay = 31;
                            break;
                        case 2:
                            maxDay = 28;
                            break;
                        case 4:
                        case 6:
                        case 9:
                        case 11:
                            maxDay = 30;
                            break;
                        default:
                            maxDay = 31;
                    }
                }
                return maxDay;
            },
            selectDate: {
                get() {
                    const year = new Date().getFullYear();
                    return new Date(year, this.postData.pointsClearMonth - 1 || 0, this.postData.pointsClearDay || 1);
                },
                set(v) {
                    const selectDate = new Date(v);
                    const month = selectDate.getMonth() + 1;
                    const day = selectDate.getDate();
                    this.postData.pointsClearMonth = month;
                    this.postData.pointsClearDay = day;
                },
            },
            // 是否选中全部人员
            isSelectAll() {
                if (this.postData) {
                    const { usageMemberTypes } = this.postData;
                    return usageMemberTypes.length === this.memberTypes.length + 1;
                }
                return false;
            },
            isPostData() {
                if (this.postData) {
                    return 'border-top: 1px solid #e6eaee;';
                }
                return '';
            },
        },
        beforeDestroy() {
            this.timer && clearTimeout(this.timer);
        },
        async created() {
            this.loadingFetch = true;
            await this.fetchPointsConfig();
            await this.fetchMemberTypes();
            this.loadingFetch = false;
            this.postData = this.createPostData();
            this.cachePostData = clone(this.postData);
        },
        methods: {
            validateUsageMemberTypesConfigList() {
                this.usageMemberTypesConfigList = this.postData.usageMemberTypes?.map((item) => {
                    const currentItem = this.usageMemberTypesConfigList?.find((it) => {
                        return it.id === item.id;
                    });
                    if (currentItem) {
                        return {
                            ...currentItem,
                        };
                    }
                    return {
                        id: item.id,
                        keyId: createGUID(),
                        name: item.name,
                        pointsRule: 0,
                        pointsAccumulateRat: null,
                        amountAccumulateRat: null,
                    };
                });
            },
            // 处方数，接诊数，咨询费，空中药房，加工费，快递费，加工费不展示例外
            noExceptItems(item) {
                return [-1, -2, -4, 8, 12, 13, 14].includes(+item.goodsType);
            },
            openGoodsListDialog() {
                this.showGoodsListDialog = true;
            },
            changePageHandler(pageIndex) {
                this.pageIndex = pageIndex;
            },
            openExceptGoodsListDialog(item) {
                this.showExceptGoodsListDialog = true;
                this.exceptGoodsSubType = item.goodsSubType;
                this.exceptGoodsCMSpec = item.goodsCMSpec;
                this.exceptGoodsType = [ item.goodsType ];
                this.exceptGoodsCustomTypeIdList = item.customTypeId ? [ item.customTypeId ] : [];
                this.exceptIndex = this.postData.applicationGoodsList.findIndex((it) => it.goodsId === item.goodsId && it.name === item.name);
                if (item.exceptInfo?.goodsItems?.length) {
                    this.selectedExceptGoodsList = item.exceptInfo?.goodsItems?.map((goodsItem) => {
                        return {
                            ...goodsItem,
                        };
                    });
                } else {
                    this.selectedExceptGoodsList = [];
                }
            },
            getExceptInfoNumber(item) {
                return item.exceptInfo?.goodsItems?.length || 0;
            },
            changeExceptGoodsList(list, exceptIndex) {
                if (!list) {
                    this.$set(this.postData.applicationGoodsList[exceptIndex], 'exceptInfo', null);
                    return;
                }
                this.$set(this.postData.applicationGoodsList[exceptIndex], 'exceptInfo', {
                    goodsItems: list.map((goodsItem) => {
                        return {
                            goodsId: goodsItem.goodsId,
                            name: goodsItem.name || goodsItem.displayName || goodsItem.goods.medicineCadn || goodsItem.goods.name,
                        };
                    }),
                });
                this.preCheckHandler(2);
            },

            changeGoodsList(list = []) {
                const newList = list.reverse();
                this.postData.applicationGoodsList = newList.map((item) => {
                    return {
                        ...item,
                        showExceptItems: item.type === 1 ? true : false,
                    };
                });
                this.pageIndex = 0;

                this.preCheckHandler(2);
            },
            handleFormInput(list) {
                this.postData.deductGoodsList = list;
            },
            deleteItem(index) {
                this.postData.applicationGoodsList.splice(
                    this.pageIndex * this.pageSize + index,
                    1,
                );

                if (this.currentPageData.length === 0 && this.pageIndex > 0) {
                    this.pageIndex--;
                }
                this.preCheckHandler(2);
            },
            handleApplicationGoodListChange(list) {
                const arr = [];
                list.forEach((item) => {
                    if (item?.exceptItems?.length) {
                        item.exceptItems = item.exceptItems.map((exceptItem) => {
                            return {
                                goodsId: exceptItem.goodsId,
                            };
                        });
                    } else {
                        arr.push(item);
                    }
                });
                this.postData.applicationGoodsList = list;
            },
            /**
             * 拉取会员卡类型
             * <AUTHOR>
             * @date 2021-03-08
             * @returns {Promise}
             */
            async fetchMemberTypes() {
                try {
                    const { rows } = await MarketingAPI.getAllMemberCardByChainId(0, 1000);
                    const dataList = rows || [];
                    this.memberTypes = dataList;
                } catch (error) {
                    console.log('fetchMemberTypes error', error);
                }
            },
            /**
             * 查看积分规则设置
             * <AUTHOR>
             * @date 2021-03-09
             */
            async fetchPointsConfig() {
                try {
                    const { data } = await IntegralApi.fetchPointsConfig();
                    this.pointsConfig = data;
                    this.usageMemberTypesConfigList = this.pointsConfig.usageMemberTypes?.map((item) => {
                        return {
                            ...item,
                            keyId: createGUID(),
                        };
                    }) || [];
                    this.usageMemberTypesConfigListCache = clone(this.usageMemberTypesConfigList);
                } catch (error) {
                    console.log('fetchPointsConfig error', error);
                }
            },
            /**
             * 创建提交数据结构
             * <AUTHOR>
             * @date 2021-03-09
             * @returns {Object}
             */
            createPostData() {
                const postData = {
                    enable: 1,
                    usageMemberTypes: [], // 可使用积分的用户
                    pointsAccumulateEnable: 0, // 消费累计得积分开关
                    amountAccumulateRat: '', // 积分累计比例，金额值
                    pointsClearEnable: 0, // 积分有效期开关
                    pointsAccumulateRat: 1, // 积分累计比例，积分值
                    pointsClearDay: '', // 自动清理-日
                    pointsClearMonth: '', // 自动清理-月
                    pointsDeductionEnable: 0, // 现金抵扣开关
                    amountDeductionRat: 1, // 积分抵扣比例：金额值
                    pointsDeductionRat: '', // 积分抵扣比例：积分值
                    pointsRefundEnable: 0, // 退还
                    pointsGoodsDeductionEnable: 0, // 积分商品抵扣开关
                    isApplicationSpecGoods: 0, // 是否指定项目
                    deductGoodsList: [],
                    applicationGoodsList: [],
                };

                if (this.pointsConfig) {
                    const {
                        usageMemberTypes, // 可使用积分的用户
                        usageForNoMember, // 是否选中非会员
                        pointsAccumulateEnable, // 消费累计得积分开关
                        amountAccumulateRat, // 积分累计比例，金额值
                        pointsClearEnable, // 积分有效期开关
                        pointsClearDay, // 自动清理-日
                        pointsClearMonth, // 自动清理-月
                        pointsDeductionEnable, // 现金抵扣开关
                        pointsDeductionRat, // 现金抵扣积分值
                        pointsRefundEnable, // 退还
                        pointsGoodsDeductionEnable,
                        isApplicationSpecGoods,
                        enable,
                        deductGoodsList,
                        applicationGoodsList,
                        pointsAccumulateRat,
                    } = this.pointsConfig;
                    if (usageMemberTypes && usageMemberTypes.length !== 0) {
                        postData.usageMemberTypes = usageMemberTypes.slice();
                    }
                    if (usageForNoMember === 1) {
                        // 选中非会员
                        postData.usageMemberTypes.unshift({
                            id: this.noMemberId,
                            name: '非会员',
                        });
                    }
                    if (utils.isNotNull(enable)) {
                        postData.enable = enable;
                    }
                    if (utils.isNotNull(pointsAccumulateEnable)) {
                        postData.pointsAccumulateEnable = pointsAccumulateEnable;
                    }
                    if (utils.isNotNull(amountAccumulateRat)) {
                        postData.amountAccumulateRat = amountAccumulateRat;
                    }
                    if (utils.isNotNull(pointsClearDay)) {
                        postData.pointsClearDay = pointsClearDay;
                    }
                    if (utils.isNotNull(pointsClearMonth)) {
                        postData.pointsClearMonth = pointsClearMonth;
                    }
                    if (utils.isNotNull(pointsClearEnable)) {
                        postData.pointsClearEnable = pointsClearEnable;
                    }
                    if (utils.isNotNull(pointsDeductionEnable)) {
                        postData.pointsDeductionEnable = pointsDeductionEnable;
                    }
                    if (utils.isNotNull(pointsDeductionRat)) {
                        postData.pointsDeductionRat = pointsDeductionRat;
                    }
                    if (utils.isNotNull(pointsRefundEnable)) {
                        postData.pointsRefundEnable = pointsRefundEnable;
                    }
                    if (utils.isNotNull(pointsGoodsDeductionEnable)) {
                        postData.pointsGoodsDeductionEnable = this.isShowPointsGoodsDeduction ? pointsGoodsDeductionEnable : 0;
                    }
                    if (utils.isNotNull(isApplicationSpecGoods)) {
                        postData.isApplicationSpecGoods = isApplicationSpecGoods;
                    }
                    if (deductGoodsList && deductGoodsList.length) {
                        postData.deductGoodsList = deductGoodsList.map((item) => {
                            return {
                                ...item,
                                type: 2,
                            };
                        });
                    }
                    if (applicationGoodsList && applicationGoodsList.length) {
                        applicationGoodsList.forEach((item) => {
                            if (item.exceptInfo) {
                                item.exceptItems = item.exceptInfo?.goodsItems?.map((goodsItem) => {
                                    return {
                                        goods: goodsItem?.goods || {},
                                        goodsId: goodsItem.goodsId,
                                        name: goodsItem.name,
                                        displaySpec: goodsItem?.goods?.displaySpec || '',
                                    };
                                });
                            }
                        });
                        postData.applicationGoodsList = applicationGoodsList;
                    }
                    if (utils.isNotNull(pointsAccumulateRat)) {
                        postData.pointsAccumulateRat = pointsAccumulateRat;
                    }
                }
                return postData;
            },
            /**
             * 当点击确认选择适用范围时
             * <AUTHOR>
             * @date 2021-03-09
             * @param {Array} usageMemberTypes 可以会员类型范围
             */
            onClickConfirm(usageMemberTypes) {
                if (usageMemberTypes?.length) {
                    this.showUsageErrorTips = false;
                }
                this.postData.usageMemberTypes = usageMemberTypes;
                this.visibleSelectTarget = false;
            },
            /**
             * 当切换现金抵扣开关时
             * <AUTHOR>
             * @date 2021-03-09
             */
            onChangeDeductionEnable() {
                if (this.postData) {
                    this.postData.pointsDeductionRat = '';
                }
            },

            changeDeductionEnable() {
                if (this.postData) {
                    this.postData.deductGoodsList = [];
                }
            },

            handleApplicationTypeChange(val) {
                if (val === 0) {
                    this.postData.applicationGoodsList = [];
                    this.showApplicationSpecErrorTips = false;
                }
            },

            /**
             * 当修改月份时
             * <AUTHOR>
             * @date 2021-03-09
             */
            onInputMonth() {
                if (this.postData.pointsClearDay > this.maxDay) {
                    this.postData.pointsClearDay = this.maxDay;
                }
            },
            /**
             * 当修改日时
             * <AUTHOR>
             * @date 2021-03-09
             */
            onInputDay() {
                if (this.maxDay < this.postData.pointsClearDay) {
                    this.postData.pointsClearDay = this.maxDay;
                }
            },
            handleChangePointsAccumulateEnable(val) {
                if (!val) {
                    this.preCheckHandler(0);
                }
            },
            handleBlurAmountAccumulateRat() {
                this.preCheckHandler(1);
            },
            getRuleText(item, pointsRule, visibleRules = false) {
                // 0 商品规则 1 毛利率规则
                if (!pointsRule) {
                    return `所有商品每消费 ${item?.amountAccumulateRat || 0} 元，积 ${item?.pointsAccumulateRat || 0} 分`;
                }
                if (visibleRules) {
                    return `毛利率在${item.startGrossMargin}%以下的商品，每单消费 ${item.amountAccumulateRat} 元，积 ${item.pointsAccumulateRat} 分`;
                }
                return `毛利率在${item.endGrossMargin}%到${item.startGrossMargin}%之间的商品，每单消费 ${item.amountAccumulateRat} 元，积 ${item.pointsAccumulateRat} 分；`;
            },
            changeEnable() {
                if (!this.postData.enable) {
                    this.preCheckHandler(0);
                }
            },
            /**
             * @desc 校验
             * type = 0 关闭积分获取规则
             * type = 1 变更积分获取规则
             * type = 2 修改积分获取范围
             * <AUTHOR> Yang
             * @date 2024-08-09 10:21:58
            */
            async preCheckHandler(type = 0) {
                try {
                    const params = this.getParams();
                    const { data } = await IntegralApi.preCheckPointsConfig(params);
                    const {
                        closePromotions,
                        pointRateUpdatePromotions,
                        pointScopeUpdatePromotions,
                    } = data;
                    if (type === 0 && closePromotions && closePromotions.length) {
                        const content = ['关闭积分获取规则，以下进行中的活动多倍积分规则也将同步失效'];
                        closePromotions.forEach((it) => {
                            content.push(`<span style="color: #7a8794">${it.promotionName}</span>`);
                        });
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content,
                        });
                    }
                    if (type === 1 && pointRateUpdatePromotions && pointRateUpdatePromotions.length) {
                        const content = ['修改积分获取规则，以下进行中的活动多倍积分规则也将同步变更'];
                        pointRateUpdatePromotions.forEach((it) => {
                            content.push(
                                `<span style="color: #7a8794">${it.promotionName}（变更后：每消费${it.amountExchangeRat || ''}元，积${it.pointsExchangeRat || ''}分）</span>`,
                            );
                        });
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content,
                        });
                    }
                    if (type === 2 && pointScopeUpdatePromotions && pointScopeUpdatePromotions.length) {
                        const content = ['积分获取参与范围变更后，以下进行中的活动多倍积分范围也会变更'];
                        let flag = false;
                        pointScopeUpdatePromotions.forEach((item) => {
                            const {
                                promotionName,
                                deletePromotionGoodsList,
                            } = item;
                            const list = deletePromotionGoodsList || [];
                            let str = '';
                            if (list.length) {
                                //有删除的范围就需要提示
                                flag = true;
                                let typeStr = '';
                                let singleStr = '';
                                const typeList = list.filter((it) => it.type === 1);
                                const singleList = list.filter((it) => it.type === 2);

                                for (const [index, it] of typeList.entries()) {
                                    if (index > 1) {
                                        typeStr += `等${typeList.length}个分类`;
                                        break;
                                    }
                                    typeStr += (typeStr ? '、' : '') + it.name;
                                }
                                for (const [index, it] of singleList.entries()) {
                                    if (index > 1) {
                                        singleStr += `等${singleList.length}个单品`;
                                        break;
                                    }
                                    const name = it.name || it.goods?.displayName || '';
                                    singleStr += (singleStr ? '、' : '') + name;
                                }
                                str = `(减少${typeStr}${typeStr ? '，以及' : ''}${singleStr})`;
                            }

                            content.push(
                                `<span style="color: #7a8794">${promotionName}${str}</span>`,
                            );
                        });
                        flag && this.$alert({
                            type: 'warn',
                            title: '提示',
                            content,
                        });
                    }
                } catch (e) {
                    console.error(e);
                }
            },
            /**
             * 打开规则设置弹窗
             * @param {Object} memberData 会员数据
             */
            openRuleSettings(memberData) {
                console.log('打开规则设置', memberData);
                this.currentMemberData = clone(memberData);
                this.visibleRuleDialog = true;
            },
            copyRules(index) {
                this.$Toast.warning('复制上条规则成功');
                this.$set(this.usageMemberTypesConfigList, index, {
                    ...this.usageMemberTypesConfigList[index],
                    keyId: createGUID(),
                    amountAccumulateRat: this.usageMemberTypesConfigList[index - 1].amountAccumulateRat,
                    pointsAccumulateRat: this.usageMemberTypesConfigList[index - 1].pointsAccumulateRat,
                    pointsRule: this.usageMemberTypesConfigList[index - 1].pointsRule,
                    ruleDetails: this.usageMemberTypesConfigList[index - 1].pointsRule === 1 ? this.usageMemberTypesConfigList[index - 1].ruleDetails : [],
                });
            },

            /**
             * 处理积分规则设置确认
             * @param {Object} data 会员积分规则数据
             */
            handleRuleConfirm(data) {
                console.log('积分规则设置确认', data);
                // 更新会员积分规则列表
                const currentMemberConfigIndex = this.usageMemberTypesConfigList.findIndex((item) => item.id === data.id);
                if (currentMemberConfigIndex > -1) {
                    this.$set(this.usageMemberTypesConfigList, currentMemberConfigIndex, {
                        ...this.usageMemberTypesConfigList[currentMemberConfigIndex],
                        id: data.id,
                        keyId: createGUID(),
                        amountAccumulateRat: data.amountAccumulateRat,
                        pointsAccumulateRat: data.pointsAccumulateRat,
                        pointsRule: data.pointsRule,
                        // 规则1 不需要列表
                        ruleDetails: data.pointsRule === 1 ? data.list?.map((item, index) => {
                            return {
                                ...item,
                                startGrossMargin: index === 0 ? 100 : (data.list[index - 1].endGrossMargin || 0) ,
                            };
                        }) : [],
                    });
                }
            },
            /**
             * 查看规则详情
             * @param {Object} memberData 会员数据
             */
            viewDetail(memberData) {
                console.log('查看规则详情', memberData);
                this.$abc.modal.info({
                    title: `${memberData.tag}积分规则详情`,
                    content: '会员积分上线规则详情',
                });
            },

            findErrorItemIndex() {
                const list = this.postData.deductGoodsList || [];
                return list?.findIndex((item) => {
                    return !item.deductionUnitCount;
                });
            },

            /**
             * 当点击保存时
             * <AUTHOR>
             * @date 2021-03-08
             */
            onClickSave() {
                if (!this.postData.usageMemberTypes?.length) {
                    this.showUsageErrorTips = true;
                    return;
                }
                if (this.postData.pointsGoodsDeductionEnable && !this.postData.deductGoodsList?.length) {
                    this.showDeductErrorTips = true;
                    return;
                }
                if (this.postData.isApplicationSpecGoods && !this.postData.applicationGoodsList?.length) {
                    this.showApplicationSpecErrorTips = true;
                    return;
                }
                if (!this.postData.pointsClearEnable) {
                    this.postData.pointsClearMonth = '';
                    this.postData.pointsClearDay = '';
                }
                const errorIndex = this.findErrorItemIndex();
                if (errorIndex > -1 && this.$refs.deductGoodsListTableRef) {
                    const pageIndex = Math.ceil((errorIndex + 1) / this.$refs?.deductGoodsListTableRef?.pageParams?.pageSize);
                    if (pageIndex) {
                        this.$refs.deductGoodsListTableRef?.changePageHandler(pageIndex);
                    }
                    this.$nextTick(() => {
                        this.$refs.postData.validate();
                    });
                    return;
                }
                this.$refs.postData.validate(async (valid) => {
                    if (valid) {
                        this.loadingSave = true;
                        try {
                            const params = this.getParams();
                            const { data } = await IntegralApi.updatePointsConfig(params);
                            this.pointsConfig = data;
                            this.cachePostData = clone(this.postData);
                            this.usageMemberTypesConfigListCache = clone(this.usageMemberTypesConfigList);
                            this.$Toast({
                                type: 'success',
                                message: '保存成功',
                            });
                        } catch (error) {
                            console.log('onClickSave error', error);
                        } finally {
                            this.loadingSave = false;
                        }
                    }
                });
            },
            filterGoodsList(goodsList, filterParams) {
                const list = goodsList;
                if (filterParams.id) {
                    return list.filter((item) => item.goodsId === filterParams.id || item.id === filterParams.id);
                }
                return list;
            },
            async searchGoodsInfo(keyword, callback) {
                keyword = keyword.trim();
                let dataList = [];
                if (keyword) {
                    try {
                        const { data } = await GoodsV3API.searchGoods({
                            keyword,
                            offset: 0,
                            limit: 50,
                        });
                        dataList = data.list || [];

                        // 分类查询
                        const goodsList = this.postData.applicationGoodsList.filter((item) => item.name?.includes(keyword));
                        goodsList.forEach((item) => {
                            const target = dataList.find((it) => it.goodsId === item.goodsId);
                            if (!target) dataList.push(item);
                        });
                    } catch (error) {
                        console.log('querySearchAsync error', error);
                    }
                }
                return callback(dataList);
            },
            selectGoods(item) {
                this.keyword = item.medicineCadn || item.name;
                this.filterParams.id = item.id || item.goodsId;
            },
            clearKeyword() {
                this.keyword = '';
                this.filterParams.id = '';
            },
            handleBlur() {
                if (!this.keyword.trim()) {
                    this.clearKeyword();
                }
            },
            /**
             * 获取提交数据
             * <AUTHOR>
             * @date 2021-03-09
             * @returns {Object}
             */
            getParams() {
                const {
                    enable,
                    usageMemberTypes, // 可使用积分的用户
                    pointsAccumulateEnable, // 消费累计得积分开关
                    amountAccumulateRat, // 积分累计比例，金额值
                    pointsAccumulateRat, // 积分累计比例，积分值
                    pointsClearEnable, // 积分有效期开关
                    pointsClearDay, // 自动清理-日
                    pointsClearMonth, // 自动清理-月
                    pointsDeductionEnable, // 现金抵扣开关
                    amountDeductionRat, // 积分抵扣比例：金额值
                    pointsDeductionRat, // 积分抵扣比例：积分值
                    pointsRefundEnable, // 退还
                    deductGoodsList,
                    pointsGoodsDeductionEnable,
                    isApplicationSpecGoods,
                    applicationGoodsList,

                } = this.postData;
                const params = {
                    enable: enable ? 1 : 0,
                    usageMemberTypes: usageMemberTypes
                        .filter((item) => item.id !== this.noMemberId)
                        .map((item) => item.id), // 可使用积分的用户
                    usageForNoMember: 0, // 是否选择非会员
                    pointsAccumulateEnable, // 消费累计得积分开关
                    amountAccumulateRat, // 积分累计比例，金额值
                    pointsAccumulateRat, // 积分累计比例，积分值
                    pointsClearEnable, // 积分有效期开关
                    pointsClearDay, // 自动清理-日
                    pointsClearMonth, // 自动清理-月
                    pointsDeductionEnable, // 现金抵扣开关
                    amountDeductionRat, // 积分抵扣比例：金额值
                    pointsDeductionRat, // 积分抵扣比例：积分值
                    pointsRefundEnable, // 退还
                    deductGoodsList: deductGoodsList.map((item) => {
                        if (item.goodsId === item.id) { // 代表新增
                            item.id = '';
                        }
                        return {
                            ...item,
                            goodsCMSpec: item.goodsCMSpec || item?.goods?.cMSpec,
                        };
                    }) ,
                    applicationGoodsList: applicationGoodsList.map((item) => {
                        const goodsItems = item?.exceptItems?.map((goodsItem) => {
                            return {
                                goodsId: goodsItem.goodsId,
                                name: goodsItem.name || goodsItem.displayName || goodsItem?.goods?.medicineCadn || goodsItem?.goods?.name,
                            };
                        });
                        if (goodsItems && goodsItems.length) {
                            item.exceptInfo = {
                                goodsItems,
                            };
                        }
                        if (item.goodsId === item.id) { // 代表新增
                            item.id = '';
                        }
                        return {
                            ...item,
                        };
                    }) ,
                    pointsGoodsDeductionEnable: pointsGoodsDeductionEnable ? 1 : 0,
                    isApplicationSpecGoods: isApplicationSpecGoods ? 1 : 0,
                    usageMemberConfigList: this.usageMemberTypesConfigList?.map((item) => {
                        return {
                            ...item,
                            memberTypeId: item.id,
                        };
                    }),
                };

                // 非会员处理
                const usageForNoMember = usageMemberTypes.find((item) => item.id === this.noMemberId);
                if (usageForNoMember) {
                    params.usageForNoMember = 1;
                }
                return params;
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme.scss';
    @import 'src/styles/abc-common.scss';

    .marketing-module__integral {
        background: $S2;

        .ellipsis {
            @include ellipsis;
        }

        .section-title {
            padding-bottom: 12px;
            margin-bottom: 24px;
            font-size: 14px;
            font-weight: bold;
            line-height: 14px;
            border-bottom: 1px solid $P6;
        }

        .abc-form section {
            padding: 24px 0 16px;

            .line {
                margin: 20px 0;
                border-bottom: 1px solid $P6;
            }

            .abc-form-item {
                margin-right: 0;
            }

            .setting-item {
                display: flex;
                margin-bottom: 16px;

                &:last-child {
                    margin-bottom: 0;
                }

                > label {
                    width: 128px;
                    line-height: 32px;
                    color: $T2;
                }

                .content {
                    flex: 1;
                    width: 0;

                    .box-wrapper {
                        flex-wrap: wrap;
                        min-height: 38px;

                        @include flex(row, flex-start, center);
                    }

                    .target-item {
                        display: inline-flex;
                        height: 32px;
                        margin: 4px 8px 4px 0;

                        @include flex(row, center, center);

                        &.usage-member-types {
                            display: inline-block;
                        }
                    }

                    .abc-checkbox-wrapper {
                        height: 32px;
                        color: $T1;
                    }

                    .exchange-wrapper {
                        margin: 8px 0 0 24px;
                    }
                }
            }
        }

        .point__application-goods-list {
            margin-top: 8px;
            margin-bottom: 40px;
            margin-left: 130px;
            border: 1px solid $P6;
            border-radius: var(--abc-border-radius-small);

            li {
                display: flex;
                border-bottom: 1px dashed $P6;

                &:last-child {
                    border-bottom: 0;
                }
            }

            .items-page-wrapper {
                display: flex;
                align-self: center;
                height: 36px;
                padding: 8px 12px;

                .product-total-count {
                    display: flex;
                    align-items: center;
                    margin-left: auto;
                    font-size: 14px;

                    > span {
                        color: $T2;
                    }

                    .product-count {
                        font-weight: bold;
                        color: $T1;
                    }
                }
            }

            li.item {
                align-items: center;
                justify-content: space-between;
                height: 40px;
                padding: 0 8px;
            }

            .item-name {
                width: 200px;
                overflow: hidden;
                font-weight: bold;
                color: #000000;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .item-rule {
                display: flex;
                flex: 1 auto;
                justify-content: flex-end;
                margin-left: 8px;
            }
        }

        .marketing-module-point__deduct-goods {
            margin-bottom: 24px;
            color: $T2;

            .deduct-switch {
                margin-left: 69px;
            }

            .deduct-goods-wrapper {
                margin-left: 130px;
            }
        }
    }
</style>
