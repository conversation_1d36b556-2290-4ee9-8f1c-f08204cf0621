<template>
    <biz-fill-remain-height>
        <template #header>
            <abc-manage-tabs :option="tabsOption" @change="handleTabsChange"></abc-manage-tabs>
        </template>
        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import { CHAIN_NOT_SUPPORT_PRICE_MAKE_UP_MODE } from '@/views-pharmacy/inventory/constant';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    const tabsOption = [
        // {
        //     label: '药品物资定价', value: 'price-setting',
        // },
        {
            label: '药品物资税率', value: 'taxrat-setting',
        },
    ];
    export default {
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        computed: {
            ...mapGetters(['isChainAdmin', 'isSingleStore', 'goodsConfig']),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            isSupportCostPriceMakeUp() {
                const isOpenCostPriceMakeUp = !(this.goodsConfig.chainReview.chainExternalFlag & CHAIN_NOT_SUPPORT_PRICE_MAKE_UP_MODE);
                return isOpenCostPriceMakeUp && this.viewDistributeConfig.Inventory.isSupportCostPriceMakeUp;
            },

            tabsOption() {
                return tabsOption;
            },
        },
        methods: {
            handleTabsChange(value) {
                this.$router.push({
                    name: value,
                });
            },
        },
    };
</script>
