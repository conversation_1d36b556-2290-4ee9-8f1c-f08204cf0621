import {
    dosageFormTypeEnum, DosageFormTypeStr,
    MaintainType, OtcType,
} from 'views/common/inventory/constants';

export const barcodeAspectRatio = 0.4;

export const SYSTEM_PRICE_TAG = [
    {
        label: '70mm*38mm',
        width: 70,
        height: 38,
        id: 1,
    },
    {
        label: '50mm*30mm',
        width: 50,
        height: 30,
        id: 2,
    },
    {
        label: '60mm*40mm',
        width: 60,
        height: 40,
        id: 3,
    },
    {
        label: '80mm*38mm',
        width: 80,
        height: 38,
        id: 4,
    },
    {
        label: '80mm*40mm',
        width: 80,
        height: 40,
        id: 5,
    },
];

export const OtcTypeStr = Object.freeze({
    [OtcType.NON_OTC]: '处方药',
    [OtcType.LEVAL_A_OTC]: '甲类非处方',
    [OtcType.LEVAL_B_OTC]: '乙类非处方',
});

export const MaintainTypeStr = Object.freeze({
    [MaintainType.NORMAL]: '一般养护',
    [MaintainType.VIP]: '重点养护',
    [MaintainType.NO]: '无需养护',
});

export const FONT_SIZE_OPTIONS = Object.freeze([
    // {
    //     label: '初号',
    //     value: 56,
    // },
    // {
    //     label: '小初',
    //     value: 48,
    // },
    {
        label: '一号',
        value: 35,
    },
    {
        label: '小一',
        value: 32,
    },
    {
        label: '二号',
        value: 29,
    },
    {
        label: '小二',
        value: 24,
    },
    {
        label: '三号',
        value: 21,
    },
    {
        label: '小三',
        value: 20,
    },
    {
        label: '四号',
        value: 19,
    },
    {
        label: '小四',
        value: 16,
    },
    {
        label: '五号',
        value: 14,
    },
    {
        label: '小五',
        value: 12,
    },
    {
        label: '六号',
        value: 10,
    },
    {
        label: '小六',
        value: 9,
    },
    {
        label: '七号',
        value: 7,
    },
    {
        label: '八号',
        value: 6,
    },
    // {
    //     label: '6',
    //     value: 6,
    // },
    // {
    //     label: '7',
    //     value: 7,
    // },
    // {
    //     label: '9',
    //     value: 9,
    // },
    // {
    //     label: '10',
    //     value: 10,
    // },
    // {
    //     label: '11',
    //     value: 11,
    // },
    // {
    //     label: '12',
    //     value: 12,
    // },
    // {
    //     label: '13',
    //     value: 13,
    // },
    // {
    //     label: '14',
    //     value: 14,
    // },
    // {
    //     label: '15',
    //     value: 15,
    // },
    // {
    //     label: '16',
    //     value: 16,
    // },
    // {
    //     label: '19',
    //     value: 19,
    // },
    // {
    //     label: '21',
    //     value: 21,
    // },
    // {
    //     label: '24',
    //     value: 24,
    // },
    // {
    //     label: '27',
    //     value: 27,
    // },
    // {
    //     label: '30',
    //     value: 30,
    // },
    // {
    //     label: '35',
    //     value: 35,
    // },
    // {
    //     label: '39',
    //     value: 39,
    // },
    // {
    //     label: '48',
    //     value: 48,
    // },
    // {
    //     label: '64',
    //     value: 64,
    // },
    // {
    //     label: '77',
    //     value: 77,
    // },
    // {
    //     label: '96',
    //     value: 96,
    // },
]);

export const OPERATE_RECORD_KEY = Object.freeze({
    Add: 'add',
    Drag: 'drag',
    Delete: 'delete',
    ChangeWidth: 'changeWidth',
    ChangeFontSize: 'changeFontSize',
    ChangeBold: 'changeBold',
    ChangeThroughLine: 'changeThroughLine',
    ChangeMoneyIcon: 'changeMoneyIcon',
    ChangeContent: 'changeContent',
    ChangeMemberLevel: 'changeMemberLevel',
});

export const PRICE_TAG_TYPE_NAME = Object.freeze({
    DisplayName: 'displayName',
    DisplaySpec: 'displaySpec',
    Manufacturer: 'manufacturerFull',
    Unit: 'unit',
    RetailPrice: 'retailPrice',
    MemberPrice: 'memberPrice',
    ShortId: 'shortId',
    BarCode: 'barCode',
    MedicineNmpn: 'medicineNmpn',
    OtcType: 'otcType',
    MaintainType: 'maintainType',
    MedicalFeeGrade: 'medicalFeeGrade',
    Custom: 'custom',
    ProfitCategoryTypeName: 'profitCategoryTypeName', // 利润分类
    Position: 'position', // 柜号
    DosageFormType: 'dosageFormType', // 剂型
    GoodsTag: 'goodsTag', // 商品标签
});

export const PRICE_TAG_TYPE_OPTIONS = Object.freeze([
    {
        title: '常用',
        list: [
            {
                name: PRICE_TAG_TYPE_NAME.DisplayName,
                type: 'text',
                fontSize: 14,
                defaultFontCount: 12,
                minWidth: 12,
                minFontCount: 2,
                title: '商品名称',
                content: '[999皮炎平]复方醋酸地塞米松乳膏',
            },
            {
                name: PRICE_TAG_TYPE_NAME.DisplaySpec,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 6,
                minWidth: 12,
                minFontCount: 2,
                title: '规格',
                content: '15mg*20g/支',
            },
            {
                name: PRICE_TAG_TYPE_NAME.Manufacturer,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 6,
                minWidth: 12,
                minFontCount: 2,
                title: '生产厂家',
                content: '广东华润顺丰',
            },
            {
                name: PRICE_TAG_TYPE_NAME.Unit,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 3,
                minWidth: 12,
                minFontCount: 2,
                title: '单位',
                content: '盒',
            },
            {
                name: PRICE_TAG_TYPE_NAME.RetailPrice,
                type: 'text',
                fontSize: 20,
                defaultFontCount: 4,
                minWidth: 12,
                minFontCount: 2,
                title: '零售价',
                content: '15.50',
                showMoneyIconOperate: true,
            },
            {
                name: PRICE_TAG_TYPE_NAME.MemberPrice,
                type: 'text',
                fontSize: 20,
                defaultFontCount: 3,
                minFontCount: 2,
                title: '会员价',
                content: '12.50',
                memberId: '',
                isMemberValid: true,
                showMoneyIconOperate: true,
                showMemberLevelSelector: true,
            },
        ],
    },
    {
        title: '扩展',
        list: [
            {
                name: PRICE_TAG_TYPE_NAME.ShortId,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 6,
                minWidth: 12,
                minFontCount: 2,
                title: '商品编码',
                content: '4287997620',
            },
            {
                name: PRICE_TAG_TYPE_NAME.BarCode,
                type: 'barcode',
                fontSize: 7,
                defaultWidth: 60,
                defaultFontCount: 7,
                minWidth: 12,
                minFontCount: 2,
                title: '条码',
                content: '6901339905216',
            },
            {
                name: PRICE_TAG_TYPE_NAME.MedicineNmpn,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 10,
                minWidth: 12,
                minFontCount: 2,
                title: '批准文号',
                content: '国药准字Z44024170',
            },
            {
                name: PRICE_TAG_TYPE_NAME.DosageFormType,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 3,
                minFontCount: 2,
                title: '剂型',
                content: DosageFormTypeStr[dosageFormTypeEnum.ointment],
            },
            {
                name: PRICE_TAG_TYPE_NAME.OtcType,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 3,
                minWidth: 12,
                minFontCount: 2,
                title: '处方药/OTC',
                content: OtcTypeStr[OtcType.NON_OTC],
            },
            {
                name: PRICE_TAG_TYPE_NAME.MaintainType,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 4,
                minWidth: 12,
                minFontCount: 2,
                title: '养护分类',
                content: MaintainTypeStr[MaintainType.NORMAL],
            },
            {
                name: PRICE_TAG_TYPE_NAME.MedicalFeeGrade,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 1,
                minWidth: 12,
                minFontCount: 1,
                title: '医保类别',
                content: '乙',
                tips: '医保目录中的类别，如：甲类',
            },
            {
                name: PRICE_TAG_TYPE_NAME.ProfitCategoryTypeName,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 3,
                minFontCount: 2,
                title: '利润分类',
                content: 'C类',
            },
            {
                name: PRICE_TAG_TYPE_NAME.Position,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 3,
                minFontCount: 2,
                title: '柜号',
                content: '5-2-3',
            },
            {
                name: PRICE_TAG_TYPE_NAME.GoodsTag,
                type: 'text',
                fontSize: 10,
                defaultFontCount: 10,
                minWidth: 12,
                minFontCount: 2,
                title: '商品标签',
                content: '此商品不参与优惠',
            },
            {
                name: PRICE_TAG_TYPE_NAME.Custom,
                type: 'input',
                fontSize: 10,
                defaultFontCount: 6,
                minWidth: 12,
                minFontCount: 2,
                title: '自定义文本',
                content: '',
            },
        ],
    },
]);
