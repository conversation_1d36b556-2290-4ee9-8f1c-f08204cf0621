<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        responsive
        :auto-focus="false"
        disabled-keyboard
        append-to-body
        :show-close="false"
        content-styles="padding: 0; display: flex; flex-direction: column; overflow: hidden;"
        custom-class="price-tag-setting-dialog-wrapper"
        @open="onOpen"
    >
        <!-- 头部 -->
        <abc-flex align="center" class="price-tag-setting-dialog-header">
            <!-- 标题 -->
            <abc-flex
                align="center"
                :gap="8"
                class="price-tag-setting-dialog-header-side"
                style="padding-left: 16px;"
            >
                <abc-text theme="black" size="normal" bold>
                    设置价签
                </abc-text>
            </abc-flex>

            <!-- 操作记录 -->
            <abc-flex
                align="center"
                justify="center"
                :gap="8"
                style="flex: 1;"
            >
                <abc-button
                    variant="text"
                    theme="default"
                    icon="s-undo-line-medium"
                    :icon-color="!previousList.length ? 'var(--abc-color-P10)' : 'var(--abc-color-T1)'"
                    size="large"
                    :width="32"
                    class="price-tag-operate-record-icon"
                    :disabled="!previousList.length"
                    @click="onUndo"
                >
                </abc-button>

                <abc-button
                    variant="text"
                    theme="default"
                    icon="s-redo-line-medium"
                    :icon-color="!nextList.length ? 'var(--abc-color-P10)' : 'var(--abc-color-T1)'"
                    size="large"
                    :width="32"
                    class="price-tag-operate-record-icon"
                    :disabled="!nextList.length"
                    @click="onRedo"
                >
                </abc-button>
            </abc-flex>

            <!-- 弹窗操作栏 -->
            <abc-flex
                align="center"
                justify="flex-end"
                :gap="8"
                class="price-tag-setting-dialog-header-side"
                style="padding-right: 16px;"
            >
                <abc-button variant="ghost" :disabled="!hasDataList" @click="onTestPrint">
                    打印测试
                </abc-button>

                <div class="price-tag-dialog-header-btn-divider"></div>

                <abc-button :disabled="!isSaveBtnValid" @click="onSubmit">
                    保存
                </abc-button>

                <abc-button variant="ghost" style="margin-left: 0;" @click="onClose">
                    关闭
                </abc-button>
            </abc-flex>
        </abc-flex>

        <!-- 内容区 -->
        <abc-layout has-sidebar class="price-tag-setting-layout-wrapper">
            <!-- 左侧边栏 -->
            <abc-layout-sidebar
                :width="320"
                class="price-tag-setting-layout-side-wrapper"
                style="border-right: 1px solid var(--abc-color-P8);"
            >
                <abc-flex vertical :gap="16" class="price-tag-setting-layout-side-options">
                    <abc-text bold>
                        价签纸字段
                    </abc-text>

                    <abc-flex
                        v-for="(group, groupIndex) in filteredPriceTagTypeOptions"
                        :key="`price-tag-options-group-${groupIndex}`"
                        :gap="8"
                        vertical
                    >
                        <abc-text size="mini" theme="gray">
                            {{ group.title }}
                        </abc-text>

                        <abc-flex wrap="wrap" :gap="8" class="price-tag-setting-layout-side-card-wrapper">
                            <template v-for="(item, index) in group.list">
                                <!-- 只有药店管家的价签支持会员价 -->
                                <abc-option-card
                                    v-if="!(item.name === PRICE_TAG_TYPE_NAME.MemberPrice && !isSupportPrintPriceTagMemberPrice)"
                                    :key="`price-tag-options-card-${index}`"
                                    :width="139"
                                    :height="32"
                                    :show-icon="false"
                                    :selectable="false"
                                    draggable="true"
                                    style="padding: 0 8px;"
                                    @dragstart.native="(event) => onDragStart(event, item)"
                                    @click="onCardClick(item)"
                                >
                                    <template #title>
                                        <abc-flex justify="space-between" align="center">
                                            <abc-flex justify="space-between" align="center" :gap="4">
                                                <abc-icon :icon="item.name === PRICE_TAG_TYPE_NAME.Custom ? 's-textinput-line-medium' : 's-placeholder-line-medium'" size="14" color="var(--abc-color-P10)"></abc-icon>
                                                <abc-text>
                                                    {{ item.title }}
                                                </abc-text>
                                            </abc-flex>

                                            <abc-tooltip-info v-if="item.tips" :content="item.tips"></abc-tooltip-info>
                                        </abc-flex>
                                    </template>
                                </abc-option-card>
                            </template>
                        </abc-flex>
                    </abc-flex>
                </abc-flex>

                <abc-flex vertical class="price-tag-setting-layout-side-page">
                    <abc-flex align="center" justify="space-between" style="margin-bottom: 16px;">
                        <abc-text bold theme="black">
                            价签纸设置
                        </abc-text>

                        <abc-space>
                            <abc-button
                                icon="s-b-cut-line"
                                variant="text"
                                theme="primary"
                                size="small"
                                @click="onCropping"
                            >
                                裁剪
                            </abc-button>
                            <abc-button
                                icon="s-b-upload-line"
                                variant="text"
                                theme="primary"
                                size="small"
                                @click="onReUpload"
                            >
                                重传
                            </abc-button>
                        </abc-space>
                    </abc-flex>

                    <abc-flex :gap="16" align="center" style="margin-bottom: 8px;">
                        <abc-text theme="gray">
                            尺寸大小
                        </abc-text>
                        <abc-text theme="black">
                            {{ pagerInfo.width }}mm * {{ pagerInfo.height }}mm
                        </abc-text>
                    </abc-flex>

                    <abc-flex :gap="16" align="flex-start">
                        <abc-text theme="gray">
                            背景图片
                        </abc-text>
                        <abc-flex class="price-tag-left-side-preview-image-wrapper">
                            <img :src="pagerBackgroundUrl" alt="failed" />
                        </abc-flex>
                    </abc-flex>
                </abc-flex>
            </abc-layout-sidebar>

            <!-- 编辑区 -->
            <abc-layout-content class="price-tag-setting-layout-content-wrapper">
                <abc-flex class="price-tag-setting-layout-content-edit-wrapper">
                    <abc-flex
                        id="price-tag-parent"
                        align="center"
                        justify="center"
                        class="price-tag-setting-canvas-wrapper"
                    >
                        <div
                            id="price-tag-children"
                            class="price-tag-setting-content"
                            :style="{
                                transform: `scale(${scaleRadio})`,
                                transformOrigin: 'center',
                                width: `${pagerInfo.width}mm`,
                                height: `${pagerInfo.height}mm`,
                                backgroundImage: `url('${pagerBackgroundUrl}')`,
                                backgroundPosition: 'center',
                                backgroundSize: 'cover',
                                overflow: overflowControl,
                            }"
                            @dragover.prevent
                            @drop="onDrop"
                        >
                            <!--<abc-text-->
                            <!--    theme="gray"-->
                            <!--    style="position: absolute; top: 0; left: 0;"-->
                            <!--    :style="{-->
                            <!--        transform: `translateY(-${20 / scaleRadio}px) scale(${1 / scaleRadio})`,-->
                            <!--        transformOrigin: 'top left'-->
                            <!--    }"-->
                            <!--&gt;-->
                            <!--    价签编辑区域-->
                            <!--</abc-text>-->
                            <template v-for="(item, index) in dataList">
                                <drag-resize-item
                                    :key="item.id || item.keyId"
                                    :scale-radio="scaleRadio"
                                    :parent-w="mm2px(pagerInfo.width)"
                                    :parent-h="mm2px(pagerInfo.height)"
                                    :item="item"
                                    :data-list="dataList"
                                    :is-active.sync="item.isActive"
                                    :type="item.type"
                                    :width.sync="item.width"
                                    :min-width="item.minWidth"
                                    :top.sync="item.top"
                                    :left.sync="item.left"
                                    :font-size.sync="item.fontSize"
                                    :font-size-type.sync="item.fontSizeType"
                                    :is-bold.sync="item.isBold"
                                    :show-line-through.sync="item.showLineThrough"
                                    :show-money-icon.sync="item.showMoneyIcon"
                                    :content.sync="item.content"
                                    :show-money-icon-operate="item.showMoneyIconOperate"
                                    @onDeleteClick="onDeleteClick(item, index)"
                                    @onDragStop="onDragStop"
                                    @onResizeStop="onResizeStop"
                                    @onFontSizeChange="(val) => onFontSizeChange(item, val)"
                                    @onMemberLevelChange="(val) => onMemberLevelChange(item, val)"
                                    @onBoldChange="(val) => onBoldChange(item, val)"
                                    @onLineThroughChange="(val) => onLineThroughChange(item, val)"
                                    @onMoneyIconChange="(val) => onMoneyIconChange(item, val)"
                                    @onContentChangeEnd="(val) => onContentChangeEnd(item, val)"
                                    @showEditPanel="onShowEditPanel"
                                    @hideEditPanel="onHideEditPanel"
                                ></drag-resize-item>
                            </template>
                        </div>
                    </abc-flex>
                </abc-flex>

                <price-tag-edit-panel
                    v-if="!!editPanelInfo.item"
                    v-show="!!editPanelInfo.show"
                    :style="{
                        top: `${editPanelInfo.top}px`, left: `${editPanelInfo.left}px`
                    }"
                    :item="editPanelInfo.item"
                    :show-edit-panel="!!(editPanelInfo.show && editPanelInfo.item)"
                    :scale-radio="scaleRadio"
                    :font-size.sync="editPanelInfo.item.fontSize"
                    :font-size-type.sync="editPanelInfo.item.fontSizeType"
                    :is-bold.sync="editPanelInfo.item.isBold"
                    :show-line-through.sync="editPanelInfo.item.showLineThrough"
                    :show-money-icon.sync="editPanelInfo.item.showMoneyIcon"
                    :width.sync="editPanelInfo.item.width"
                    :min-width="editPanelInfo.item.minWidth"
                    :paper-width="pagerInfo.width"
                    :show-money-icon-operate="!!editPanelInfo.item.showMoneyIconOperate"
                    :show-member-level-selector="!!editPanelInfo.item.showMemberLevelSelector"
                    :member-id.sync="editPanelInfo.item.memberId"
                    :is-member-valid.sync="editPanelInfo.item.isMemberValid"
                    :show-bold-operate="!(editPanelInfo.item.type === 'barcode')"
                    :show-line-through-operate="!(editPanelInfo.item.type === 'barcode')"
                    @onFontSizeClick="(type) => onEditPanelFontSizeClick(type, editPanelInfo.item, editPanelInfo.item.type !== 'barcode')"
                    @onFontSizeChange="(val) => onFontSizeChange(editPanelInfo.item, val)"
                    @onMemberLevelChange="(val) => onMemberLevelChange(editPanelInfo.item, val)"
                    @onInputOutside="onInputOutside"
                    @onDeleteClick="onDeleteClick(editPanelInfo.item)"
                    @onBoldChange="(val) => onBoldChange(editPanelInfo.item, val)"
                    @onLineThroughChange="(val) => onLineThroughChange(editPanelInfo.item, val)"
                    @onMoneyIconChange="(val) => onMoneyIconChange(editPanelInfo.item, val)"
                >
                </price-tag-edit-panel>
            </abc-layout-content>

            <!-- 右侧边栏 -->
            <abc-layout-sidebar
                :width="320"
                class="price-tag-setting-layout-side-wrapper side-right"
            >
                <abc-text bold theme="black">
                    打印预览
                </abc-text>

                <div
                    class="price-tag-side-right-preview-wrapper"
                    :style="{
                        width: `${pagerInfo.width}mm`, height: `${pagerInfo.height}mm`, transform: `scale(${previewScale})`
                    }"
                >
                    <price-tag-preview
                        :pager-background-url="pagerBackgroundUrl"
                        :html-data-list="dataList"
                        :is-test-print="true"
                    >
                    </price-tag-preview>
                </div>
            </abc-layout-sidebar>
        </abc-layout>
    </abc-dialog>
</template>

<script>
    import DragResizeItem
        from 'views/settings/print-config/price-tag/components/price-tag-drag-resize/drag-resize-item.vue';
    import {
        FONT_SIZE_OPTIONS,
        OPERATE_RECORD_KEY, PRICE_TAG_TYPE_NAME,
        PRICE_TAG_TYPE_OPTIONS,
    } from 'views/settings/print-config/price-tag/constants';
    import { clone } from '@/common/utils';
    import { createGUID } from '@/utils';
    import { mm2px } from 'views/settings/print-config/price-tag/utils';
    import PriceTagPreview from 'views/settings/print-config/price-tag/components/price-tag-preview/index.vue';
    import { PriceTagPrintApi } from '@/printer/print-api/price-tag';
    import PriceTagBgUploadDialog from 'views/settings/print-config/price-tag/components/price-tag-bg-upload-dialog';
    import { PriceTagTemplateManager } from 'views/settings/print-config/price-tag/utils/price-tag-template-manager';
    import PriceTagRecropDialog from 'views/settings/print-config/price-tag/components/price-tag-recrop-dialog';
    import PriceTagEditPanel from 'views/settings/print-config/price-tag/components/price-tag-edit-panel/index.vue';
    import useMemberLevelList from '@/hooks/business/use-member-levels-list';
    import MarketingAPI from 'api/marketing.js';
    import { mapGetters } from 'vuex';

    export default {
        name: 'PriceTagSettingDialog',
        components: {
            PriceTagEditPanel,
            PriceTagPreview,
            DragResizeItem,
        },
        props: {
            dataListProp: {
                type: Array,
                default: () => ([]),
            },
            pagerInfoProp: {
                type: Object,
                default: () => ({}),
            },
            pagerBackgroundUrlProp: {
                type: String,
                default: '',
            },
            originPagerBackgroundUrlProp: {
                type: String,
                default: '',
            },
            cropInfoProp: {
                type: Array,
                default: () => ([]),
            },
            templateId: {
                type: String,
                default: '',
            },
            onSaveSuccess: {
                type: Function,
                default: null,
            },
        },
        setup() {
            const { memberLevelList } = useMemberLevelList({ MarketingAPI });

            return {
                memberLevelList,
            };
        },
        data() {
            return {
                FONT_SIZE_OPTIONS,
                PRICE_TAG_TYPE_OPTIONS,
                PRICE_TAG_TYPE_NAME,
                visible: false,
                scaleRadio: 1,
                dataList: clone(this.dataListProp),
                draggingOption: null,
                isMoving: false,
                dragOffsetX: 0,
                dragOffsetY: 0,
                overflowControl: 'hidden',
                previousList: [],
                nextList: [],
                isEditPriceTag: false,
                editPanelInfo: {
                    show: false,
                    top: 0,
                    left: 0,
                    item: null,
                },
                pagerInfo: clone(this.pagerInfoProp),
                pagerBackgroundUrl: this.pagerBackgroundUrlProp,
                originPagerBackgroundUrl: this.originPagerBackgroundUrlProp,
                cropInfo: clone(this.cropInfoProp),
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            hasOperate() {
                return this.previousList.length || this.nextList.length || this.isEditPriceTag;
            },
            hasDataList() {
                return this.dataList.length;
            },
            isSaveBtnValid() {
                if (!this.hasDataList) return false;
                return this.hasOperate;
            },
            previewScale() {
                const widthWithPx = mm2px(this.pagerInfo.width);
                return parseFloat((288 / widthWithPx).toFixed(2));
            },
            isSupportPrintPriceTagMemberPrice() {
                return this.viewDistributeConfig.Inventory.isSupportPrintPriceTagMemberPrice;
            },
            isSupportPrintPriceTagProfitClassify() {
                return this.viewDistributeConfig.Inventory.isSupportPrintPriceTagProfitClassify;
            },
            filteredPriceTagTypeOptions() {
                return PRICE_TAG_TYPE_OPTIONS.map((group) => ({
                    ...group,
                    list: group.list.filter((item) => {
                        // 过滤利润分类字段
                        if (item.name === PRICE_TAG_TYPE_NAME.ProfitCategoryTypeName && !this.isSupportPrintPriceTagProfitClassify) {
                            return false;
                        }
                        // 过滤会员价字段
                        if (item.name === PRICE_TAG_TYPE_NAME.MemberPrice && !this.isSupportPrintPriceTagMemberPrice) {
                            return false;
                        }
                        return true;
                    }),
                })).filter((group) => group.list.length > 0); // 移除空组
            },
        },
        mounted() {
            this.$abcEventBus.$on('price-tag-custom-is-inputting', (isInputting) => {
                this.overflowControl = isInputting ? '' : 'hidden';
            }, this);

            window.addEventListener('keyup', this.onKeyUp);
            window.addEventListener('keydown', this.onKeyDown);
        },
        beforeDestroy() {
            window.removeEventListener('keyup', this.onKeyUp);
            window.removeEventListener('keydown', this.onKeyDown);
        },
        methods: {
            mm2px,
            onOpen() {
                this.calcScaleRadio();
            },
            onKeyDown(event) {
                const isActiveItem = this.dataList.find((it) => it.isActive);
                if (!isActiveItem) return;

                let {
                    left, top,
                } = isActiveItem;
                switch (event.key) {
                    case 'ArrowUp':
                        top -= 1;
                        break;
                    case 'ArrowDown':
                        top += 1;
                        break;
                    case 'ArrowLeft':
                        left -= 1;
                        break;
                    case 'ArrowRight':
                        left += 1;
                        break;
                    default:
                        break;
                }
                this.$set(isActiveItem, 'left', left);
                this.$set(isActiveItem, 'top', top);
            },
            onKeyUp(event) {
                const isActiveItem = this.dataList.find((it) => it.isActive);
                if (!isActiveItem) return;

                if (['Delete', 'Backspace'].includes(event.key)) {
                    this.onDeleteClick(isActiveItem);
                }
            },
            onClose() {
                if (this.hasOperate) {
                    this.message = this.$modal({
                        type: 'warn',
                        preset: 'alert',
                        content: () => {
                            return (
                                <div>
                                    <abc-delete-icon
                                        style="position: absolute; top: -20px; right: -20px;"
                                        size={'hugely'}
                                        theme={'dark'}
                                        variant={'outline-square'}
                                        onDelete={() => {
                                            !!this.message && this.message.close();
                                        }}
                                    ></abc-delete-icon>
                                    <h4 style="color: var(--abc-color-T1); margin-bottom: 8px; font-size: 16px; font-weight: 700; line-height: 24px;">
                                        存在未保存的设置
                                    </h4>
                                    <div>
                                        您的修改还未保存，是否保存内容？
                                    </div>
                                </div>
                            );
                        },
                        size: 'small',
                        showClose: false,
                        confirmText: '保存',
                        cancelText: '不保存',
                        onConfirm: async () => {
                            await this.onSubmit();
                        },
                        onCancel: () => {
                            this.visible = false;
                        },
                    });
                } else {
                    this.visible = false;
                }
            },
            async onSubmit() {
                try {
                    const validMemberIsEmpty = () => {
                        let flag = true;
                        this.dataList.forEach((it) => {
                            if (it.name === 'memberPrice') {
                                if (!it.memberId) {
                                    flag = false;
                                    this.$set(it, 'isMemberValid', false);
                                } else {
                                    const findItem = this.memberLevelList.find((memberLevel) => memberLevel.id === it.memberId);
                                    if (!findItem) {
                                        flag = false;
                                        this.$set(it, 'isMemberValid', false);
                                    }
                                }
                            }
                        });
                        return flag;
                    };
                    if (!validMemberIsEmpty()) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '请选择会员价等级',
                        });
                        return;
                    }

                    const params = {
                        sizeTemplateId: this.pagerInfo.id,
                        originalBackgroundImageUrl: this.originPagerBackgroundUrl,
                        clippedBackgroundImageUrl: this.pagerBackgroundUrl,
                        clippedPosition: this.cropInfo,
                        businessPropertyFields: this.dataList.map((it) => {
                            return {
                                ...it,
                                isActive: undefined,
                            };
                        }),
                    };
                    if (!this.templateId) {
                        await PriceTagTemplateManager.getInstance().addPriceTagTemplate(params);
                    } else {
                        await PriceTagTemplateManager.getInstance().updatePriceTagTemplate(this.templateId, params);
                    }
                    this.$Toast.success('保存成功');
                    this.onSaveSuccess && this.onSaveSuccess();
                    this.visible = false;
                } catch (e) {
                    this.$Toast.error('保存失败，请重试');
                    console.error('价签模板保存失败\n', e);
                }
            },
            onTestPrint() {
                PriceTagPrintApi.multiPrintPriceTag({
                    width: this.pagerInfo.width,
                    height: this.pagerInfo.height,
                    backgroundImgUrl: this.pagerBackgroundUrl,
                    htmlDataList: this.dataList,
                    goodsList: [undefined],
                    isPreview: true,
                    isTestPrint: true,
                });
            },
            calcScaleRadio(parentClass = 'price-tag-parent', childrenClass = 'price-tag-children') {
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    const parent = document.getElementById(parentClass);
                    const children = document.getElementById(childrenClass);
                    const parentRect = parent.getBoundingClientRect();
                    const childrenRect = children.getBoundingClientRect();
                    const {
                        left: parentLeft, top: parentTop, width: parentWidth, height: parentHeight,
                    } = parentRect;
                    const {
                        left: childrenLeft, top: childrenTop, width: childrenWidth, height: childrenHeight,
                    } = childrenRect;
                    const leftDifference = Math.ceil(childrenLeft - parentLeft);
                    const topDifference = Math.ceil(childrenTop - parentTop);
                    let scaleRadio = 1;

                    // 计算宽度和高度的最大可能缩放比例
                    const maxWidthScale = parentWidth / childrenWidth;
                    const maxHeightScale = parentHeight / childrenHeight;

                    if (leftDifference < 0 && topDifference < 0) {
                        // 两个方向都溢出，选择较小的缩放比例确保不会超出任何边界
                        scaleRadio = Math.min(maxWidthScale, maxHeightScale);
                    } else if (leftDifference < 0) {
                        // 仅宽度溢出，按宽度缩放
                        scaleRadio = maxWidthScale;
                    } else if (topDifference < 0) {
                        // 仅高度溢出，按高度缩放
                        scaleRadio = maxHeightScale;
                    } else if (leftDifference > 0 && topDifference > 0) {
                        // 两个方向都有空余，选择较小的缩放比例确保不会超出任何边界
                        scaleRadio = Math.min(maxWidthScale, maxHeightScale);
                    }
                    scaleRadio = parseFloat(scaleRadio.toFixed(2));
                    if (!isNaN(scaleRadio)) {
                        this.scaleRadio = scaleRadio;
                    }
                    console.log('%c calc scale radio finish:\n', 'background: green; padding: 0 5px', this.scaleRadio);
                }, 50);
            },
            pushQueue({
                type, data,
            }) {
                this.nextList = [];
                this.previousList.push({
                    type,
                    data,
                });
            },
            getFontSizeTypeByOption(fontSize) {
                return FONT_SIZE_OPTIONS.find((it) => it.value === fontSize)?.label || `${fontSize}`;
            },
            onDragStart(event, option) {
                this.isMoving = true;
                this.draggingOption = clone(option);
                const rect = event.target.getBoundingClientRect();
                this.dragOffsetX = event.clientX - rect.left;
                this.dragOffsetY = event.clientY - rect.top;

                // ---- 修改拖拽时的元素 ----
                // const fontSizeType = this.getFontSizeTypeByOption(option.fontSize);
                // const {
                //     defaultFontCount, minFontCount, content, type, name, showMoneyIconOperate,
                // } = option;
                // const data = {
                //     isActive: true,
                //     name,
                //     type,
                //     top: 0,
                //     left: 0,
                //     fontSizeType,
                //     fontSize,
                //     width: fontSize * defaultFontCount,
                //     minWidth: fontSize * minFontCount,
                //     isBold: false,
                //     showLineThrough: false,
                //     showMoneyIcon: false,
                //     content,
                //     showMoneyIconOperate,
                // };
                // const VmConstructor = Vue.extend(DragResizeItem);
                // const instance = new VmConstructor({
                //     propsData: {
                //         parentW: mm2px(this.pagerInfo.width),
                //         parentH: mm2px(this.pagerInfo.height),
                //         item: data,
                //         isActive: data.isActive,
                //         type: data.type,
                //         width: data.width,
                //         minWidth: data.minWidth,
                //         top: data.top,
                //         left: data.left,
                //         fontSize: data.fontSize,
                //         fontSizeType: data.fontSizeType,
                //         isBold: data.isBold,
                //         showLineThrough: data.showLineThrough,
                //         showMoneyIcon: data.showMoneyIcon,
                //         content: data.content,
                //         showMoneyIconOperate: data.showMoneyIconOperate,
                //     },
                //     parent: this,
                // });
                // const vm = instance.$mount('.price-tag-page-wrapper');
                // html2canvas(vm.$el).then((canvas) => {
                //     vm.$destroy();
                //     const dataURL = canvas.toDataURL('image/png');
                //     const img = document.createElement('img');
                //     img.crossorigin = 'anonymous';
                //     img.onload = function() {
                //         event.dataTransfer.setDragImage(img, this.dragOffsetX, this.dragOffsetY);
                //     };
                //     img.width = data.width;
                //     img.src = dataURL;
                // });
            },
            onDrop(event) {
                if (this.isMoving && this.draggingOption) {
                    this.isMoving = false;

                    const containerDom = document.getElementsByClassName('price-tag-setting-content')[0];
                    const rect = containerDom.getBoundingClientRect();
                    const x = event.clientX - rect.left;
                    const y = event.clientY - rect.top;
                    const adjustedX = (x / this.scaleRadio) - (this.dragOffsetX / this.scaleRadio);
                    const adjustedY = (y / this.scaleRadio) - (this.dragOffsetY / this.scaleRadio);

                    const fontSizeType = this.getFontSizeTypeByOption(this.draggingOption.fontSize);
                    const {
                        defaultFontCount,
                        minFontCount,
                        content,
                        type,
                        name,
                        fontSize,
                        showMoneyIconOperate,
                        defaultWidth,
                        minWidth,
                        showMemberLevelSelector,
                        isMemberValid,
                    } = this.draggingOption;
                    let { memberId } = this.draggingOption;

                    // ------------ 处理会员价 start ------------
                    if (this.memberLevelList && this.memberLevelList.length === 1) {
                        memberId = this.memberLevelList[0].id;
                    }
                    // ------------ 处理会员价 end ------------

                    const data = {
                        keyId: createGUID(),
                        isActive: true,
                        name,
                        type,
                        top: adjustedY,
                        left: adjustedX,
                        fontSizeType,
                        fontSize,
                        width: defaultWidth ? defaultWidth : fontSize * defaultFontCount,
                        minWidth: minWidth ? minWidth : fontSize * minFontCount,
                        isBold: false,
                        showLineThrough: false,
                        showMoneyIcon: false,
                        content,
                        showMoneyIconOperate,
                        showMemberLevelSelector,
                        memberId,
                        isMemberValid,
                    };
                    this.dataList.push(data);

                    this.pushQueue({
                        type: OPERATE_RECORD_KEY.Add,
                        data: { ...data },
                    });

                    // 操作完成后, 重置
                    this.draggingOption = null;
                }
            },
            onCardClick(item) {
                const contentEl = document.getElementById('price-tag-children');
                const contentRect = contentEl.getBoundingClientRect();
                const {
                    width: contentWidth, height: contentHeight,
                } = contentRect;
                const adjustedY = Math.round(contentHeight / this.scaleRadio / 2);
                const adjustedX = Math.round(contentWidth / this.scaleRadio / 2);

                const {
                    name,
                    type,
                    fontSize,
                    defaultFontCount,
                    minFontCount,
                    content,
                    showMoneyIconOperate,
                    defaultWidth,
                    minWidth,
                    showMemberLevelSelector,
                    isMemberValid,
                } = item;
                let { memberId } = item;

                // ------------ 处理会员价 start ------------
                if (this.memberLevelList && this.memberLevelList.length === 1) {
                    memberId = this.memberLevelList[0].id;
                }
                // ------------ 处理会员价 end ------------

                const fontSizeType = this.getFontSizeTypeByOption(fontSize);
                const data = {
                    keyId: createGUID(),
                    isActive: true,
                    name,
                    type,
                    top: adjustedY,
                    left: adjustedX,
                    fontSizeType,
                    fontSize,
                    width: defaultWidth ? defaultWidth : fontSize * defaultFontCount,
                    minWidth: minWidth ? minWidth : fontSize * minFontCount,
                    isBold: false,
                    showLineThrough: false,
                    showMoneyIcon: false,
                    content,
                    showMoneyIconOperate,
                    showMemberLevelSelector,
                    memberId,
                    isMemberValid,
                };
                this.dataList.push(data);

                this.pushQueue({
                    type: OPERATE_RECORD_KEY.Add,
                    data: { ...data },
                });
            },
            onDeleteClick(item) {
                this.$nextTick(() => {
                    const cacheDataList = clone(this.dataList);
                    const index = cacheDataList.findIndex((it) => it.keyId === item.keyId);
                    console.log('%c onDeleteClick\n', 'background: green; padding: 0 5px', item, index);
                    if (index > -1) {
                        this.pushQueue({
                            type: OPERATE_RECORD_KEY.Delete,
                            data: { ...item },
                        });

                        cacheDataList.splice(index, 1);
                        cacheDataList.forEach((it) => {
                            it.isActive = false;
                        });
                        this.dataList = cacheDataList;
                    }

                    this.onHideEditPanel({ item });
                });
            },
            onDragStop({
                keyId, start, end,
            }) {
                this.pushQueue({
                    type: OPERATE_RECORD_KEY.Drag,
                    data: {
                        keyId,
                        start: { ...start },
                        end: { ...end },
                    },
                });
            },
            onResizeStop({
                keyId, start, end,
            }) {
                this.pushQueue({
                    type: OPERATE_RECORD_KEY.ChangeWidth,
                    data: {
                        keyId,
                        start: { ...start },
                        end: { ...end },
                    },
                });
            },
            onFontSizeChange(item, val) {
                this.pushQueue({
                    type: OPERATE_RECORD_KEY.ChangeFontSize,
                    data: {
                        keyId: item.keyId,
                        ...val,
                    },
                });
            },
            onMemberLevelChange(item, val) {
                this.pushQueue({
                    type: OPERATE_RECORD_KEY.ChangeMemberLevel,
                    data: {
                        keyId: item.keyId,
                        ...val,
                    },
                });
            },
            onBoldChange(item, val) {
                this.pushQueue({
                    type: OPERATE_RECORD_KEY.ChangeBold,
                    data: {
                        keyId: item.keyId,
                        ...val,
                    },
                });
            },
            onLineThroughChange(item, val) {
                this.pushQueue({
                    type: OPERATE_RECORD_KEY.ChangeThroughLine,
                    data: {
                        keyId: item.keyId,
                        ...val,
                    },
                });
            },
            onMoneyIconChange(item, val) {
                this.pushQueue({
                    type: OPERATE_RECORD_KEY.ChangeMoneyIcon,
                    data: {
                        keyId: item.keyId,
                        ...val,
                    },
                });
            },
            onContentChangeEnd(item, val) {
                this.pushQueue({
                    type: OPERATE_RECORD_KEY.ChangeContent,
                    data: {
                        keyId: item.keyId,
                        ...val,
                    },
                });
            },
            onEditPanelFontSizeClick(type = 'add', item, isLimit = true) {
                const params = {
                    start: {
                        fontSize: item.fontSize, fontSizeType: item.fontSizeType,
                    },
                };

                let endFontSize, endFontSizeType;

                if (type === 'add') {
                    if (isLimit) {
                        if (item.fontSize >= FONT_SIZE_OPTIONS[0].value) return;
                        const endFontSizeIndex = FONT_SIZE_OPTIONS.findIndex((it) => it.value === item.fontSize);
                        if (endFontSizeIndex > -1 && endFontSizeIndex - 1 >= 0) {
                            endFontSize = FONT_SIZE_OPTIONS[endFontSizeIndex - 1].value;
                            endFontSizeType = FONT_SIZE_OPTIONS[endFontSizeIndex - 1].label;
                        }
                    } else {
                        endFontSize = item.fontSize + 1;
                        endFontSizeType = FONT_SIZE_OPTIONS.find((it) => it.value === endFontSize)?.label ?? `${endFontSize}`;
                    }
                } else {
                    if (isLimit) {
                        if (item.fontSize <= FONT_SIZE_OPTIONS[FONT_SIZE_OPTIONS.length - 1].value) return;
                        const endFontSizeIndex = FONT_SIZE_OPTIONS.findIndex((it) => it.value === item.fontSize);
                        if (endFontSizeIndex > -1 && endFontSizeIndex + 1 < FONT_SIZE_OPTIONS.length) {
                            endFontSize = FONT_SIZE_OPTIONS[endFontSizeIndex + 1].value;
                            endFontSizeType = FONT_SIZE_OPTIONS[endFontSizeIndex + 1].label;
                        }
                    } else {
                        if (item.fontSize <= 1) return;
                        endFontSize = item.fontSize - 1;
                        endFontSizeType = FONT_SIZE_OPTIONS.find((it) => it.value === endFontSize)?.label ?? `${endFontSize}`;
                    }
                }

                if (!endFontSize || !endFontSizeType) return;

                item.fontSize = endFontSize;
                item.fontSizeType = endFontSizeType;
                params.end = {
                    fontSize: endFontSize, fontSizeType: endFontSizeType,
                };
                this.onFontSizeChange(item, params);
            },
            onInputOutside(event) {
                if (event.target?.tagName !== 'INPUT') {
                    const inputEl = document.querySelector('.price-tag-width-input-wrapper .abc-input__inner');
                    if (inputEl) {
                        inputEl.blur?.();
                    }
                }
            },
            onShowEditPanel(params) {
                console.log('%c onShowEditPanel\n', 'background: green; padding: 0 5px', clone(params));
                const {
                    item, top, left,
                } = params;
                this.editPanelInfo.top = top;
                this.editPanelInfo.left = left;
                this.editPanelInfo.item = item;
                this.editPanelInfo.show = true;
            },
            onHideEditPanel(params) {
                console.log('%c onHideEditPanel\n', 'background: green; padding: 0 5px', clone(params));
                const { item } = params;
                if (item.keyId === this.editPanelInfo.item?.keyId && this.editPanelInfo.show) {
                    this.editPanelInfo.top = 0;
                    this.editPanelInfo.left = 0;
                    this.editPanelInfo.show = false;
                }
            },
            onUndo() {
                if (!this.previousList || !this.previousList.length) return;
                const cachePreviousList = clone(this.previousList);
                const operateItem = cachePreviousList.pop();
                this.nextList.push(clone(operateItem));
                this.previousList = cachePreviousList;
                const {
                    type, data,
                } = operateItem;
                const {
                    keyId, start,
                } = data;
                if (type === OPERATE_RECORD_KEY.Add) {
                    const cacheDataList = clone(this.dataList);
                    const index = cacheDataList.findIndex((it) => it.keyId === keyId);
                    if (index > -1) {
                        cacheDataList.splice(index, 1);
                        this.dataList = cacheDataList.map((it) => {
                            it.isActive = false;
                            return it;
                        });
                    }
                } else if (type === OPERATE_RECORD_KEY.Delete) {
                    if (data.name === PRICE_TAG_TYPE_NAME.MemberPrice) {
                        data.isMemberValid = true;
                    }
                    this.dataList.push({
                        ...data,
                        isActive: true,
                    });
                } else if (type === OPERATE_RECORD_KEY.Drag) {
                    const index = this.dataList.findIndex((it) => it.keyId === keyId);
                    if (index > -1) {
                        const cacheDataList = clone(this.dataList);
                        const cacheItem = cacheDataList.splice(index, 1)[0];

                        const newKeyId = createGUID();
                        this.previousList.forEach((it) => {
                            if (it.data.keyId === keyId) {
                                it.data.keyId = newKeyId;
                            }
                        });
                        this.nextList.forEach((it) => {
                            if (it.data.keyId === keyId) {
                                it.data.keyId = newKeyId;
                            }
                        });

                        cacheItem.left = start.left;
                        cacheItem.top = start.top;
                        cacheDataList.push({
                            ...cacheItem,
                            isActive: true,
                            keyId: newKeyId,
                        });
                        this.dataList = cacheDataList;
                    }
                } else if (type === OPERATE_RECORD_KEY.ChangeWidth) {
                    const index = this.dataList.findIndex((it) => it.keyId === keyId);
                    if (index > -1) {
                        const cacheDataList = clone(this.dataList);
                        const cacheItem = cacheDataList.splice(index, 1)[0];

                        const newKeyId = createGUID();
                        this.previousList.forEach((it) => {
                            if (it.data.keyId === keyId) {
                                it.data.keyId = newKeyId;
                            }
                        });
                        this.nextList.forEach((it) => {
                            if (it.data.keyId === keyId) {
                                it.data.keyId = newKeyId;
                            }
                        });

                        cacheItem.width = start.width;
                        cacheDataList.push({
                            ...cacheItem,
                            isActive: true,
                            keyId: newKeyId,
                        });
                        this.dataList = cacheDataList;
                    }
                } else if (type === OPERATE_RECORD_KEY.ChangeFontSize) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.fontSize = start.fontSize;
                    findItem.fontSizeType = start.fontSizeType;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeBold) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.isBold = start;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeThroughLine) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.showLineThrough = start;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeMoneyIcon) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.showMoneyIcon = start;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeContent) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.content = start;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeMemberLevel) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.memberId = start.memberId;
                    findItem.isMemberValid = true;
                    findItem.isActive = true;
                }
            },
            onRedo() {
                if (!this.nextList || !this.nextList.length) return;
                const cacheNextList = clone(this.nextList);
                const operateItem = cacheNextList.pop();
                this.previousList.push(operateItem);
                this.nextList = cacheNextList;
                const {
                    type, data,
                } = operateItem;
                const {
                    keyId, end,
                } = data;
                if (type === OPERATE_RECORD_KEY.Add) {
                    if (data.name === PRICE_TAG_TYPE_NAME.MemberPrice) {
                        data.isMemberValid = true;
                    }
                    this.dataList.push({
                        ...data,
                        isActive: true,
                    });
                } else if (type === OPERATE_RECORD_KEY.Delete) {
                    const cacheDataList = clone(this.dataList) || [];
                    const index = cacheDataList.findIndex((it) => it.keyId === keyId);
                    if (index > -1) {
                        cacheDataList.splice(index, 1);
                        this.dataList = cacheDataList.map((it) => {
                            it.isActive = false;
                            return it;
                        });
                    }
                } else if (type === OPERATE_RECORD_KEY.Drag) {
                    const index = this.dataList.findIndex((it) => it.keyId === keyId);
                    if (index > -1) {
                        const cacheDataList = clone(this.dataList);
                        const cacheItem = cacheDataList.splice(index, 1)[0];

                        const newKeyId = createGUID();
                        this.previousList.forEach((it) => {
                            if (it.data.keyId === keyId) {
                                it.data.keyId = newKeyId;
                            }
                        });
                        this.nextList.forEach((it) => {
                            if (it.data.keyId === keyId) {
                                it.data.keyId = newKeyId;
                            }
                        });

                        cacheItem.left = end.left;
                        cacheItem.top = end.top;
                        cacheDataList.push({
                            ...cacheItem,
                            isActive: true,
                            keyId: newKeyId,
                        });
                        this.dataList = cacheDataList;
                    }
                } else if (type === OPERATE_RECORD_KEY.ChangeWidth) {
                    const index = this.dataList.findIndex((it) => it.keyId === keyId);
                    if (index > -1) {
                        const cacheDataList = clone(this.dataList);
                        const cacheItem = cacheDataList.splice(index, 1)[0];

                        const newKeyId = createGUID();
                        this.previousList.forEach((it) => {
                            if (it.data.keyId === keyId) {
                                it.data.keyId = newKeyId;
                            }
                        });
                        this.nextList.forEach((it) => {
                            if (it.data.keyId === keyId) {
                                it.data.keyId = newKeyId;
                            }
                        });

                        cacheItem.width = end.width;
                        cacheDataList.push({
                            ...cacheItem,
                            isActive: true,
                            keyId: newKeyId,
                        });
                        this.dataList = cacheDataList;
                    }
                } else if (type === OPERATE_RECORD_KEY.ChangeFontSize) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.fontSize = end.fontSize;
                    findItem.fontSizeType = end.fontSizeType;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeBold) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.isBold = end;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeThroughLine) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.showLineThrough = end;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeMoneyIcon) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.showMoneyIcon = end;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeContent) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.content = end;
                    findItem.isActive = true;
                } else if (type === OPERATE_RECORD_KEY.ChangeMemberLevel) {
                    const findItem = this.dataList.find((it) => it.keyId === keyId);
                    findItem.memberId = end.memberId;
                    findItem.isMemberValid = true;
                    findItem.isActive = true;
                }
            },
            async onCreatePager(data) {
                this.isEditPriceTag = true;
                this.scaleRadio = 1;
                await this.$nextTick();
                this.pagerInfo = data.size;
                this.pagerBackgroundUrl = data.cropImage;
                this.originPagerBackgroundUrl = data.originImage;
                this.cropInfo = data.crop;
                this.calcScaleRadio();
            },
            onCropping() {
                new PriceTagRecropDialog({
                    origin: this.originPagerBackgroundUrl,
                    crop: this.cropInfo?.[0],
                    pagerInfo: this.pagerInfo,
                    onCreate: ({
                        cropImage, crop,
                    }) => {
                        this.onCreatePager({
                            size: this.pagerInfo,
                            originImage: this.originPagerBackgroundUrl,
                            cropImage,
                            crop,
                        });
                    },
                }).generateDialogAsync({ parent: this });
            },
            onReUpload() {
                new PriceTagBgUploadDialog({
                    onCreate: this.onCreatePager,
                    defaultSize: this.pagerInfo.id,
                    confirmText: '完成',
                }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>

<style lang="scss">
.price-tag-setting-dialog-wrapper {
    .abc-dialog-body {
        overflow: hidden !important;
    }

    .price-tag-setting-dialog-header {
        width: 100%;
        height: 56px;
        border-bottom: 1px solid var(--abc-color-P8);
    }

    .price-tag-setting-dialog-header-side {
        width: 320px;
    }

    .price-tag-operate-record-icon {
        width: 32px !important;
        min-width: 32px !important;
        max-width: 32px !important;
        height: 32px !important;
        min-height: 32px !important;
        max-height: 32px !important;
    }

    .price-tag-dialog-header-btn-divider {
        width: 0;
        height: 20px;
        border-right: 1px solid var(--abc-color-P6);
    }

    .price-tag-setting-layout-wrapper {
        flex: 1;
        height: 0;
    }

    .price-tag-setting-layout-side-wrapper {
        display: flex;
        flex-direction: column;

        &.side-right {
            gap: 16px;
            padding: 20px 16px;
            border-left: 1px solid var(--abc-color-P8);
        }
    }

    .price-tag-side-right-preview-wrapper {
        width: 100%;
        transform-origin: left top;
    }

    .price-tag-setting-layout-side-options {
        flex: 1;
        width: 100%;
        height: 0;
        padding: 20px 16px;
        border-bottom: 1px solid var(--abc-color-P8);
    }

    .price-tag-setting-layout-side-card-wrapper {
        width: 100%;
    }

    .price-tag-setting-layout-side-page {
        width: 100%;
        padding: 20px 16px;
    }

    .price-tag-left-side-preview-image-wrapper {
        width: 150px;
        height: 86px;
        object-fit: cover;

        img {
            width: 100%;
            height: 100%;
        }
    }

    .price-tag-setting-layout-content-wrapper {
        position: relative;
        z-index: 9999;
        padding: 0 !important;
        background-color: var(--abc-color-AbcDivGrey);
    }

    .price-tag-setting-layout-content-edit-wrapper {
        width: 100%;
        height: 100%;
        padding: 50px !important;
        overflow: hidden;
    }

    .price-tag-setting-canvas-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .price-tag-setting-content {
        position: relative;
    }
}
</style>
