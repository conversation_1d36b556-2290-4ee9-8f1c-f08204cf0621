import i18n from '@/i18n';

export const EXAMPLE_DATA = {
    'id': 'ffffffff0000000034bd639e46dfc004',
    'patient': {
        'id': 'ffffffff00000000349092cea0238000',
        'chainId': 'ffffffff00000000146808c695534000',
        'name': 'juliet',
        'namePy': 'juliet',
        'namePyFirst': 'juliet',
        'mobile': '15068571846',
        'countryCode': '86',
        'sex': '女',
        'birthday': '1981-09-30',
        'idCard': '37010119810930684X',
        'isMember': 0,
        'age': {
            'year': 42,
            'month': 6,
            'day': 24,
        },
        'address': {
            'addressCityId': null,
            'addressCityName': null,
            'addressDetail': '',
            'addressDistrictId': null,
            'addressDistrictName': null,
            'addressGeo': null,
            'addressProvinceId': null,
            'addressProvinceName': null,
            'addressPostcode': null,
        },
        'sn': '001012',
        'remark': '',
        'profession': '职员',
        'company': '',
        'memberInfo': null,
        'patientSource': {
            'id': '11413126450807626787',
            'chainId': null,
            'name': '转诊医生',
            'sourceFrom': '0eba233945864c23b25016b2d2126dd1',
            'sourceFromName': '莫沙',
            'relatedType': 1,
            'relatedId': null,
            'clueId': null,
        },
        'tags': null,
        'marital': 1,
        'weight': null,
        'ethnicity': '',
        'wxBindStatus': 0,
        'isAttention': null,
        'appFlag': 0,
        'arrearsFlag': 0,
        'externalCodeId': '',
        'externalCodeRemark': null,
        'shebaoCardInfo': null,
        'chronicArchivesInfo': null,
        'childCareInfo': null,
        'patientPoints': null,
    },
    'memberInfo': {
        'cardBalance': 289.00,
        'principal': 279.00,
        'present': 10,
        'points': 239,
        'pointsTotal': 239,
        'patient': {
            'id': 'ffffffff00000000349092cea0238000',
            'name': 'juliet',
            'mobile': '15068571846',
            'isMember': 1,
        },
        'memberType': {
            'id': 'ffffffff0000000034cae7c721228000',
            'name': '铂金会员',
        },
    },
    'organ': {
        'id': 'ffffffff00000000146808c695534004',
        'name': 'ABC医院',
        'shortName': 'ABC医院',
        'addressDetail': '',
        'contactPhone': '40008009',
        'logo': 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000146808c695534004/basic/logo_h_square_%E5%8F%A3%E8%85%94%E7%AE%A1%E5%AE%B6_6pctmlBmtqmq_ElI982vHz7Nn.jpg',
        'category': '综合医院',
        'hisType': 100,
        'medicalDocumentsTitle': null,
    },
    'chargeForms': [
        {
            'id': 'ffffffff0000000034bd93f8a6e0c000',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc002',
                    'name': '诊费',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 10,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 10,
                    'composeType': 0,
                    'goodsTypeId': null,
                    'feeComposeType': 20,
                    'feeTypeId': 5,
                    'feeTypeName': i18n.t('registrationFeeName'),
                    'goodsFeeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff0000000034bd63a0c6dfc000',
                            'name': `lxl${i18n.t('registrationFeeName')}用项`,
                            'unit': '',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 10,
                            'composeType': 0,
                            'goodsTypeId': null,
                            'feeComposeType': 0,
                            'feeTypeId': 1003,
                            'feeTypeName': '一般诊疗费',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'cmSpec': null,
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '',
                            'socialName': `lxl${i18n.t('registrationFeeName')}用项`,
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'cmSpec': null,
                    'sourceItemType': 0,
                    'socialCode': '001101000010000-11010000100',
                    'hisCode': '**********',
                    'socialUnit': '次',
                    'socialName': '诊费',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 5,
                    'productSubType': 0,
                },
            ],
            'sourceFormType': 1,
            'printFormType': 1,
            'totalPrice': 10,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd93f8a6e0c005',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc006',
                    'name': '打印检验单项医嘱',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 77,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 77,
                    'composeType': 0,
                    'goodsTypeId': 20,
                    'feeComposeType': 20,
                    'feeTypeId': 20,
                    'feeTypeName': '检验费',
                    'goodsFeeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff0000000034bd639e46dfc008',
                            'name': '打印仪器费',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 55,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 55,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 3784950433603584000,
                            'feeTypeName': '仪器费',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '打印仪器费',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                        {
                            'id': 'ffffffff0000000034bd639e46dfc007',
                            'name': '打印检验费',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 22,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 22,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 3784160481196753000,
                            'feeTypeName': '检验费',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '打印检验费',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                    ],
                    'position': null,
                    'displaySpec': '次',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '打印检验单项医嘱',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 3,
                    'productSubType': 1,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc009',
                    'name': '打印检验组合医嘱',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 141,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 141,
                    'composeType': 0,
                    'goodsTypeId': 20,
                    'feeComposeType': 20,
                    'feeTypeId': 20,
                    'feeTypeName': '检验费',
                    'goodsFeeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff0000000034bd639e46dfc00b',
                            'name': '打印床位费',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 110,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 110,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 3784160481196753000,
                            'feeTypeName': '床位费',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '打印床位费',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                        {
                            'id': 'ffffffff0000000034bd639e46dfc00a',
                            'name': '打印一般诊疗费',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 31,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 31,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 1003,
                            'feeTypeName': '一般诊疗费',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '打印一般诊疗费',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                    ],
                    'position': null,
                    'displaySpec': '次',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '打印检验组合医嘱',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 3,
                    'productSubType': 1,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc00c',
                    'name': 'B超',
                    'unit': '项',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 100,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 100,
                    'composeType': 0,
                    'goodsTypeId': 21,
                    'feeComposeType': 0,
                    'feeTypeId': 21,
                    'feeTypeName': '检查费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '项',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '项',
                    'socialName': 'B超',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 3,
                    'productSubType': 2,
                },
            ],
            'sourceFormType': 2,
            'printFormType': 2,
            'totalPrice': 318,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd93f8a6e0c006',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc00e',
                    'name': '测试治疗医嘱3',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 25,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 25,
                    'composeType': 0,
                    'goodsTypeId': 22,
                    'feeComposeType': 20,
                    'feeTypeId': 22,
                    'feeTypeName': '治疗费',
                    'goodsFeeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff0000000034bd639e46dfc00f',
                            'name': 'lxl治疗费用项',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 25,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 25,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 1003,
                            'feeTypeName': '一般诊疗费',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': 'lxl治疗费用项',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                    ],
                    'position': null,
                    'displaySpec': '次',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '测试治疗医嘱3',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 4,
                    'productSubType': 1,
                },
            ],
            'sourceFormType': 3,
            'printFormType': 3,
            'totalPrice': 25,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd93f8a6e0c008',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc016',
                    'name': '测试医用材料1',
                    'unit': '盒',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 20,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 20,
                    'composeType': 0,
                    'goodsTypeId': 17,
                    'feeComposeType': 0,
                    'feeTypeId': 17,
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '3g*20粒/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '盒',
                    'socialName': '测试医用材料1',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 2,
                    'productSubType': 1,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc019',
                    'name': '打印保健食品',
                    'unit': '包',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 100,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 100,
                    'composeType': 0,
                    'goodsTypeId': 27,
                    'feeComposeType': 0,
                    'feeTypeId': 3789510080980730000,
                    'feeTypeName': '仪器使用费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '200g*5份/包',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '包',
                    'socialName': '打印保健食品',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 7,
                    'productSubType': 3,
                },
            ],
            'sourceFormType': 9,
            'printFormType': 9,
            'totalPrice': 120,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd93f8a6e0c009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc01c',
                    'name': '检验检查套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 157,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 157,
                    'composeType': 1,
                    'goodsTypeId': 8,
                    'feeComposeType': 10,
                    'feeTypeId': 8,
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': [
                        {
                            'id': 'ffffffff0000000034bd639e46dfc01f',
                            'name': '主视眼',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 11,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 11,
                            'composeType': 2,
                            'goodsTypeId': 21,
                            'feeComposeType': 0,
                            'feeTypeId': 21,
                            'feeTypeName': '检查费',
                            'goodsFeeType': 0,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '主视眼',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 3,
                            'productSubType': 2,
                        },
                        {
                            'id': 'ffffffff0000000034bd639e46dfc01e',
                            'name': '猪头',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 14,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 14,
                            'composeType': 2,
                            'goodsTypeId': 20,
                            'feeComposeType': 0,
                            'feeTypeId': 20,
                            'feeTypeName': '检验费',
                            'goodsFeeType': 0,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '猪头',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 3,
                            'productSubType': 1,
                        },
                        {
                            'id': 'ffffffff0000000034bd639e46dfc01d',
                            'name': '组合项目-xzf',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 10,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 10,
                            'composeType': 2,
                            'goodsTypeId': 20,
                            'feeComposeType': 0,
                            'feeTypeId': 20,
                            'feeTypeName': '检验费',
                            'goodsFeeType': 0,
                            'composeChildren': [
                                {
                                    'id': 'a7596897b177435f896f4144730b478a',
                                    'name': '血常规',
                                    'unit': '次',
                                    'count': 1,
                                    'unitCount': 1,
                                    'doseCount': 1,
                                    'totalPrice': 0,
                                    'discountedPrice': 0,
                                    'discountedUnitPrice': 0,
                                    'unitPrice': 0,
                                    'composeType': 2,
                                    'goodsTypeId': null,
                                    'feeComposeType': 0,
                                    'feeTypeId': null,
                                    'feeTypeName': null,
                                    'goodsFeeType': 0,
                                    'composeChildren': null,
                                    'position': null,
                                    'displaySpec': null,
                                    'cmSpec': null,
                                    'sourceItemType': 0,
                                    'socialCode': null,
                                    'hisCode': null,
                                    'socialUnit': '次',
                                    'socialName': '血常规',
                                    'medicalFeeGrade': null,
                                    'ownExpenseRatio': null,
                                    'ownExpenseFee': null,
                                    'inscpScpAmt': null,
                                    'overlmtAmt': null,
                                    'productType': 3,
                                    'productSubType': 1,
                                },
                                {
                                    'id': '159572b5c6d74e55afd3da55a92507eb',
                                    'name': '新建模板未知设备',
                                    'unit': '次',
                                    'count': 1,
                                    'unitCount': 1,
                                    'doseCount': 1,
                                    'totalPrice': 0,
                                    'discountedPrice': 0,
                                    'discountedUnitPrice': 0,
                                    'unitPrice': 0,
                                    'composeType': 2,
                                    'goodsTypeId': null,
                                    'feeComposeType': 0,
                                    'feeTypeId': null,
                                    'feeTypeName': null,
                                    'goodsFeeType': 0,
                                    'composeChildren': null,
                                    'position': null,
                                    'displaySpec': null,
                                    'cmSpec': null,
                                    'sourceItemType': 0,
                                    'socialCode': null,
                                    'hisCode': null,
                                    'socialUnit': '次',
                                    'socialName': '新建模板未知设备',
                                    'medicalFeeGrade': null,
                                    'ownExpenseRatio': null,
                                    'ownExpenseFee': null,
                                    'inscpScpAmt': null,
                                    'overlmtAmt': null,
                                    'productType': 3,
                                    'productSubType': 1,
                                },
                                {
                                    'id': '53fd9e135690485798be77db8fdf6e2a',
                                    'name': '测试项目01',
                                    'unit': '次',
                                    'count': 1,
                                    'unitCount': 1,
                                    'doseCount': 1,
                                    'totalPrice': 0,
                                    'discountedPrice': 0,
                                    'discountedUnitPrice': 0,
                                    'unitPrice': 0,
                                    'composeType': 2,
                                    'goodsTypeId': null,
                                    'feeComposeType': 0,
                                    'feeTypeId': null,
                                    'feeTypeName': null,
                                    'goodsFeeType': 0,
                                    'composeChildren': null,
                                    'position': null,
                                    'displaySpec': null,
                                    'cmSpec': null,
                                    'sourceItemType': 0,
                                    'socialCode': null,
                                    'hisCode': null,
                                    'socialUnit': '次',
                                    'socialName': '测试项目01',
                                    'medicalFeeGrade': null,
                                    'ownExpenseRatio': null,
                                    'ownExpenseFee': null,
                                    'inscpScpAmt': null,
                                    'overlmtAmt': null,
                                    'productType': 3,
                                    'productSubType': 1,
                                },
                                {
                                    'id': 'e543776aa60b497081b45c6a5857168c',
                                    'name': '测试项目02',
                                    'unit': '次',
                                    'count': 1,
                                    'unitCount': 1,
                                    'doseCount': 1,
                                    'totalPrice': 0,
                                    'discountedPrice': 0,
                                    'discountedUnitPrice': 0,
                                    'unitPrice': 0,
                                    'composeType': 2,
                                    'goodsTypeId': null,
                                    'feeComposeType': 0,
                                    'feeTypeId': null,
                                    'feeTypeName': null,
                                    'goodsFeeType': 0,
                                    'composeChildren': null,
                                    'position': null,
                                    'displaySpec': null,
                                    'cmSpec': null,
                                    'sourceItemType': 0,
                                    'socialCode': null,
                                    'hisCode': null,
                                    'socialUnit': '次',
                                    'socialName': '测试项目02',
                                    'medicalFeeGrade': null,
                                    'ownExpenseRatio': null,
                                    'ownExpenseFee': null,
                                    'inscpScpAmt': null,
                                    'overlmtAmt': null,
                                    'productType': 3,
                                    'productSubType': 1,
                                },
                                {
                                    'id': '83f376a9677746bd9fbecc9b8c747a66',
                                    'name': '测试项目03',
                                    'unit': '次',
                                    'count': 1,
                                    'unitCount': 1,
                                    'doseCount': 1,
                                    'totalPrice': 0,
                                    'discountedPrice': 0,
                                    'discountedUnitPrice': 0,
                                    'unitPrice': 0,
                                    'composeType': 2,
                                    'goodsTypeId': null,
                                    'feeComposeType': 0,
                                    'feeTypeId': null,
                                    'feeTypeName': null,
                                    'goodsFeeType': 0,
                                    'composeChildren': null,
                                    'position': null,
                                    'displaySpec': null,
                                    'cmSpec': null,
                                    'sourceItemType': 0,
                                    'socialCode': null,
                                    'hisCode': null,
                                    'socialUnit': '次',
                                    'socialName': '测试项目03',
                                    'medicalFeeGrade': null,
                                    'ownExpenseRatio': null,
                                    'ownExpenseFee': null,
                                    'inscpScpAmt': null,
                                    'overlmtAmt': null,
                                    'productType': 3,
                                    'productSubType': 1,
                                },
                                {
                                    'id': '872377bb6c5840d09bbd2f0795fb3fae',
                                    'name': '测试项目04',
                                    'unit': '次',
                                    'count': 1,
                                    'unitCount': 1,
                                    'doseCount': 1,
                                    'totalPrice': 0,
                                    'discountedPrice': 0,
                                    'discountedUnitPrice': 0,
                                    'unitPrice': 0,
                                    'composeType': 2,
                                    'goodsTypeId': null,
                                    'feeComposeType': 0,
                                    'feeTypeId': null,
                                    'feeTypeName': null,
                                    'goodsFeeType': 0,
                                    'composeChildren': null,
                                    'position': null,
                                    'displaySpec': null,
                                    'cmSpec': null,
                                    'sourceItemType': 0,
                                    'socialCode': null,
                                    'hisCode': null,
                                    'socialUnit': '次',
                                    'socialName': '测试项目04',
                                    'medicalFeeGrade': null,
                                    'ownExpenseRatio': null,
                                    'ownExpenseFee': null,
                                    'inscpScpAmt': null,
                                    'overlmtAmt': null,
                                    'productType': 3,
                                    'productSubType': 1,
                                },
                                {
                                    'id': '0f2e65bee742414599ee19c246a05e15',
                                    'name': '测试项目05',
                                    'unit': '次',
                                    'count': 1,
                                    'unitCount': 1,
                                    'doseCount': 1,
                                    'totalPrice': 0,
                                    'discountedPrice': 0,
                                    'discountedUnitPrice': 0,
                                    'unitPrice': 0,
                                    'composeType': 2,
                                    'goodsTypeId': null,
                                    'feeComposeType': 0,
                                    'feeTypeId': null,
                                    'feeTypeName': null,
                                    'goodsFeeType': 0,
                                    'composeChildren': null,
                                    'position': null,
                                    'displaySpec': null,
                                    'cmSpec': null,
                                    'sourceItemType': 0,
                                    'socialCode': null,
                                    'hisCode': null,
                                    'socialUnit': '次',
                                    'socialName': '测试项目05',
                                    'medicalFeeGrade': null,
                                    'ownExpenseRatio': null,
                                    'ownExpenseFee': null,
                                    'inscpScpAmt': null,
                                    'overlmtAmt': null,
                                    'productType': 3,
                                    'productSubType': 1,
                                },
                                {
                                    'id': 'b03fca3ca8504c7f8003dde2133a519b',
                                    'name': '新建模板单项1202',
                                    'unit': '次',
                                    'count': 1,
                                    'unitCount': 1,
                                    'doseCount': 1,
                                    'totalPrice': 0,
                                    'discountedPrice': 0,
                                    'discountedUnitPrice': 0,
                                    'unitPrice': 0,
                                    'composeType': 2,
                                    'goodsTypeId': null,
                                    'feeComposeType': 0,
                                    'feeTypeId': null,
                                    'feeTypeName': null,
                                    'goodsFeeType': 0,
                                    'composeChildren': null,
                                    'position': null,
                                    'displaySpec': null,
                                    'cmSpec': null,
                                    'sourceItemType': 0,
                                    'socialCode': null,
                                    'hisCode': null,
                                    'socialUnit': '次',
                                    'socialName': '新建模板单项1202',
                                    'medicalFeeGrade': null,
                                    'ownExpenseRatio': null,
                                    'ownExpenseFee': null,
                                    'inscpScpAmt': null,
                                    'overlmtAmt': null,
                                    'productType': 3,
                                    'productSubType': 1,
                                },
                            ],
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '组合项目-xzf',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 3,
                            'productSubType': 1,
                        },
                        {
                            'id': 'ffffffff0000000034bd639e46dfc020',
                            'name': '无散光验光',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 11,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 11,
                            'composeType': 2,
                            'goodsTypeId': 21,
                            'feeComposeType': 0,
                            'feeTypeId': 21,
                            'feeTypeName': '检查费',
                            'goodsFeeType': 0,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '无散光验光',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 3,
                            'productSubType': 2,
                        },
                        {
                            'id': 'ffffffff0000000034bd639e46dfc021',
                            'name': '彩超项目',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 111,
                            'discountedPrice': 0,
                            'discountedUnitPrice': 0,
                            'unitPrice': 111,
                            'composeType': 2,
                            'goodsTypeId': 21,
                            'feeComposeType': 0,
                            'feeTypeId': 21,
                            'feeTypeName': '检查费',
                            'goodsFeeType': 0,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '彩超项目',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 3,
                            'productSubType': 2,
                        },
                    ],
                    'position': null,
                    'displaySpec': '次',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '检验检查套餐',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 11,
                    'productSubType': 1,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'totalPrice': 157,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd93f8a6e0c00a',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc023',
                    'name': '测试软性亲水镜',
                    'unit': '盒',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 800,
                    'discountedPrice': 800,
                    'discountedUnitPrice': 800,
                    'unitPrice': 800,
                    'composeType': 0,
                    'goodsTypeId': 67,
                    'feeComposeType': 0,
                    'feeTypeId': 0,
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '2.00 无色 10片/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '盒',
                    'socialName': '测试软性亲水镜',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 24,
                    'productSubType': 4,
                },
            ],
            'sourceFormType': 22,
            'printFormType': 22,
            'totalPrice': 800,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd93f8a6e0c00b',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc011',
                    'name': '护理-0522',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 149,
                    'discountedPrice': 149,
                    'discountedUnitPrice': 149,
                    'unitPrice': 149,
                    'composeType': 0,
                    'goodsTypeId': 56,
                    'feeComposeType': 20,
                    'feeTypeId': 56,
                    'feeTypeName': '护理费',
                    'goodsFeeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff0000000034bd639e46dfc014',
                            'name': 'bubble 推拿费',
                            'unit': '床',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 20,
                            'discountedPrice': 20,
                            'discountedUnitPrice': 20,
                            'unitPrice': 20,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 1003,
                            'feeTypeName': '一般诊疗费',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '床',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '床',
                            'socialName': 'bubble 推拿费',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                        {
                            'id': 'ffffffff0000000034bd639e46dfc013',
                            'name': 'Ⅱ测试加工配送',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 18,
                            'discountedPrice': 18,
                            'discountedUnitPrice': 18,
                            'unitPrice': 18,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 3784392885637382000,
                            'feeTypeName': '项目加工配送费',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': 'Ⅱ测试加工配送',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                        {
                            'id': 'ffffffff0000000034bd639e46dfc012',
                            'name': '今天超贵费',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 111,
                            'discountedPrice': 111,
                            'discountedUnitPrice': 111,
                            'unitPrice': 111,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 3784125800409153500,
                            'feeTypeName': '加班费1234',
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '今天超贵费',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': null,
                            'ownExpenseFee': null,
                            'inscpScpAmt': null,
                            'overlmtAmt': null,
                            'productType': 19,
                            'productSubType': 0,
                        },
                    ],
                    'position': null,
                    'displaySpec': '次',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '护理-0522',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 21,
                    'productSubType': 1,
                },
            ],
            'sourceFormType': 23,
            'printFormType': 23,
            'totalPrice': 149,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd639e46dfc032',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc033',
                    'name': '双丹膏',
                    'unit': '粒',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 1,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 1,
                    'composeType': 0,
                    'goodsTypeId': 16,
                    'feeComposeType': 0,
                    'feeTypeId': 16,
                    'feeTypeName': '中成药费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '2粒/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '粒',
                    'socialName': '双丹膏',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 1,
                    'productSubType': 3,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc035',
                    'name': '天佛参口服液',
                    'unit': '支',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 6,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 6,
                    'composeType': 0,
                    'goodsTypeId': 16,
                    'feeComposeType': 0,
                    'feeTypeId': 3784160481196753000,
                    'feeTypeName': '中成药费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '2支/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '支',
                    'socialName': '天佛参口服液',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 1,
                    'productSubType': 3,
                },
            ],
            'sourceFormType': 4,
            'printFormType': 4,
            'totalPrice': 7,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd639e46dfc037',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc038',
                    'name': '阿奇霉素注射液',
                    'unit': '瓶',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 1,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 1,
                    'composeType': 0,
                    'goodsTypeId': 12,
                    'feeComposeType': 0,
                    'feeTypeId': 12,
                    'feeTypeName': '西药费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '3ul:2ml*10瓶/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '瓶',
                    'socialName': '阿奇霉素注射液',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 1,
                    'productSubType': 1,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc03a',
                    'name': '瓶贴注射(瓶贴注射)',
                    'unit': '瓶',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 22.4,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 22.4,
                    'composeType': 0,
                    'goodsTypeId': 12,
                    'feeComposeType': 0,
                    'feeTypeId': 12,
                    'feeTypeName': '西药费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '40IU:100mg*5瓶/箱',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '瓶',
                    'socialName': '瓶贴注射(瓶贴注射)',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 1,
                    'productSubType': 1,
                },
            ],
            'sourceFormType': 4,
            'printFormType': 4,
            'totalPrice': 23.4,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034bd639e46dfc025',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034bd639e46dfc026',
                    'name': '淡竹叶',
                    'unit': 'g',
                    'count': 4,
                    'unitCount': 1,
                    'doseCount': 4,
                    'totalPrice': 88,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 22,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': 14,
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T000200178',
                    'hisCode': '000900',
                    'socialUnit': 'g',
                    'socialName': '淡竹叶',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': null,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc028',
                    'name': '艾司奥美拉唑',
                    'unit': 'g',
                    'count': 4,
                    'unitCount': 1,
                    'doseCount': 4,
                    'totalPrice': 8,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 2,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': 14,
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': 'g',
                    'socialName': '艾司奥美拉唑',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 1,
                    'productSubType': 2,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc02a',
                    'name': '瓶贴饮片',
                    'unit': 'g',
                    'count': 4,
                    'unitCount': 1,
                    'doseCount': 4,
                    'totalPrice': 2.66,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 0.665,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': 13,
                    'feeTypeName': '中药费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片 1',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': 'g',
                    'socialName': '瓶贴饮片',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 1,
                    'productSubType': 2,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc02c',
                    'name': '佛手',
                    'unit': 'g',
                    'count': 4,
                    'unitCount': 1,
                    'doseCount': 4,
                    'totalPrice': 12,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 3,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': 13,
                    'feeTypeName': '中药费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T000800255',
                    'hisCode': '000904',
                    'socialUnit': 'g',
                    'socialName': '佛手',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': null,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc02e',
                    'name': '生地',
                    'unit': 'g',
                    'count': 4,
                    'unitCount': 1,
                    'doseCount': 4,
                    'totalPrice': 4,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 1,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': 13,
                    'feeTypeName': '中药费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': 'g',
                    'socialName': '生地',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': null,
                    'ownExpenseFee': null,
                    'inscpScpAmt': null,
                    'overlmtAmt': null,
                    'productType': 1,
                    'productSubType': 2,
                },
                {
                    'id': 'ffffffff0000000034bd639e46dfc030',
                    'name': '柿蒂',
                    'unit': 'g',
                    'count': 4,
                    'unitCount': 1,
                    'doseCount': 4,
                    'totalPrice': 4,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 1,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': 13,
                    'feeTypeName': '中药费',
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T000800696',
                    'hisCode': '000155',
                    'socialUnit': 'g',
                    'socialName': '柿蒂',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': null,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                },
            ],
            'sourceFormType': 6,
            'printFormType': 6,
            'totalPrice': 118.66,
            'specification': '中药饮片',
            'doseCount': 4,
            'dailyDosage': '1日2剂',
            'usage': '煎服',
            'freq': '1日3次',
            'usageLevel': '每次150ml',
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': '',
            'takeMedicationTime': '2024-07-31 17:00',
        },
    ],
    'chargeTransactions': [
        {
            'payMode': 3,
            'paySubMode': 0,
            'payModeName': '支付宝',
            'paySubModeName': null,
            'payModeDisplayName': '支付宝',
            'amount': 949,
            'thirdPartyPayCardId': null,
        },
    ],
    'totalFee': 1728.06,
    'singlePromotionFee': null,
    'packagePromotionFee': null,
    'discountFee': -779.06,
    'receivableFee': 949,
    'netIncomeFee': 949,
    'refundFee': null,
    'oweFee': 0,
    'diagnosedDate': '2024-04-24T02:18:25Z',
    'diagnosis': '胃炎',
    'diagnosisInfos': [
        {
            'code': 'K29.700',
            'name': '胃炎',
            'diseaseType': null,
        },
    ],
    'extendDiagnosisInfos': [
        {
            'toothNos': null,
            'value': [
                {
                    'code': 'K29.700',
                    'name': '胃炎',
                    'diseaseType': null,
                },
            ],
        },
    ],
    'healthCardId': null,
    'healthCardAccountPaymentFee': null,
    'healthCardFundPaymentFee': null,
    'healthCardOtherPaymentFee': null,
    'healthCardCardOwnerType': null,
    'healthCardSelfConceitFee': null,
    'healthCardSelfPayFee': null,
    'personalPaymentFee': 949,
    'chargedByName': '田长书',
    'chargedTime': '2024-04-24T02:18:47Z',
    'sellerName': '',
    'doctorName': '田长书',
    'nationalDoctorCode': 'D440118027143',
    'patientOrderNo': '********',
    'departmentName': '妇产科',
    'departmentCaty': '0502',
    'hospitalCode': 'H3301020062211',
    'doctorWorkNo': null,
    'refundByName': null,
    'refundTime': null,
    'latestOwePaidTime': null,
    'shebaoPayment': {
        cardId: '********', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: '父女', // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 45.00, // 个人帐户支付金额
        personalPaymentFee: 800.00, // 个人现金支付
        fundPaymentFee: 34.72, // 基金支付金额
        otherPaymentFee: 0.00, // 其它支付金额
        // region: 'hangzhou',
        medType: '普通门诊',
        extraInfo: {
            hifpPay: 29.72, // 统筹支付金额
            cvlservPay: 0, // 公务员补助
            hifmiPay: 0, // 大病保险
            mafPay: 0, // 医疗救助
            hifesPay: 1, // 企业补充医疗保险
            othPay: 1, // 其他支出
            wltpayAmt: 1, // 医保钱包支出
            // curYearBalance: 8, // 当年账户余额
            // allYearBalance: 10, // 历年账户余额
            // curYearAccountPaymentFee: 7, // 本年账户支付
            // allYearAccountPaymentFee: 9, // 历年账户支付
            // cashPaymentFee: 0, // 医保现金支付
            // selfConceitFee: 1, // 自负金额
            // allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            // personalHandledAmount: 3, // 自理金额
            // allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            // personalPaymentAmount: 5, // 自费金额
            // allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            // curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            gongjiAccountPaymentFee: 20,
            gongjiFundPaymentFee: 0,
            gongjiOtherPaymentFee: 0,
            gongjiPsnCashPay: 0,
            gongjiBalc: 1200,
            gongjiAuthorName: '任盈盈',
            gongjiRelation: '父女',
            generalFundpayBalc: 200, // 统筹余额
        },
    },
    'subTotals': {
        'registrationFee': 10,
        'westernMedicineFee': 30.4,
        'chineseMedicineFee': 118.66,
        'examinationFee': 318,
        'treatmentFee': 25,
        'materialFee': 120,
        'onlineConsultationFee': 0,
        'expressDeliveryFee': 0,
        'decoctionFee': 0,
        'otherFee': 0,
        'composeProductFee': 157,
        'eyeFee': 800,
        'nursingFee': 149,
    },
    'medicalBills': [
        {
            'name': null,
            'totalFee': 800,
            'totalCount': 1,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 0,
            'innerFlag': 1,
            'sort': 0,
        },
        {
            'name': null,
            'totalFee': 0,
            'totalCount': 2,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 14,
            'innerFlag': 1,
            'sort': 0,
        },
        {
            'name': null,
            'totalFee': 0,
            'totalCount': 1,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 17,
            'innerFlag': 1,
            'sort': 0,
        },
        {
            'name': '西药费',
            'totalFee': 0,
            'totalCount': 2,
            'unit': '项',
            'printType': 1,
            'feeTypeId': 12,
            'innerFlag': 1,
            'sort': 12,
        },
        {
            'name': '中药费',
            'totalFee': 0,
            'totalCount': 4,
            'unit': '项',
            'printType': 2,
            'feeTypeId': 13,
            'innerFlag': 1,
            'sort': 13,
        },
        {
            'name': '中成药费',
            'totalFee': 0,
            'totalCount': 1,
            'unit': '项',
            'printType': 3,
            'feeTypeId': 16,
            'innerFlag': 1,
            'sort': 16,
        },
        {
            'name': '检验费',
            'totalFee': 0,
            'totalCount': 10,
            'unit': '项',
            'printType': 5,
            'feeTypeId': 20,
            'innerFlag': 1,
            'sort': 20,
        },
        {
            'name': '检查费',
            'totalFee': 0,
            'totalCount': 4,
            'unit': '项',
            'printType': 4,
            'feeTypeId': 21,
            'innerFlag': 1,
            'sort': 21,
        },
        {
            'name': '一般诊疗费',
            'totalFee': 20,
            'totalCount': 4,
            'unit': '项',
            'printType': 15,
            'feeTypeId': 1003,
            'innerFlag': 1,
            'sort': 1003,
        },
        {
            'name': '加班费1234',
            'totalFee': 111,
            'totalCount': 1,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 3784125800409153500,
            'innerFlag': 0,
            'sort': 3784125800409153500,
        },
        {
            'name': '中成药费',
            'totalFee': 0,
            'totalCount': 1,
            'unit': '项',
            'printType': 3,
            'feeTypeId': 3784160481196753000,
            'innerFlag': 0,
            'sort': 3784160481196753000,
        },
        {
            'name': '检验费',
            'totalFee': 0,
            'totalCount': 1,
            'unit': '项',
            'printType': 5,
            'feeTypeId': 3784160481196753000,
            'innerFlag': 0,
            'sort': 3784160481196753000,
        },
        {
            'name': '床位费',
            'totalFee': 0,
            'totalCount': 1,
            'unit': '项',
            'printType': 13,
            'feeTypeId': 3784160481196753000,
            'innerFlag': 0,
            'sort': 3784160481196753000,
        },
        {
            'name': '项目加工配送费',
            'totalFee': 18,
            'totalCount': 1,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 3784392885637382000,
            'innerFlag': 0,
            'sort': 3784392885637382000,
        },
        {
            'name': '仪器费',
            'totalFee': 0,
            'totalCount': 1,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 3784950433603584000,
            'innerFlag': 0,
            'sort': 3784950433603584000,
        },
        {
            'name': '仪器使用费',
            'totalFee': 0,
            'totalCount': 1,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 3789510080980730000,
            'innerFlag': 0,
            'sort': 3789510080980730000,
        },
    ],
    'giftCoupons': [],
    'promotionBalances': [],
    'owedStatus': 0,
    'healthCardPaymentFee': 0,
    'serialNo': '3800303192015618052',
    latestChargeComment: '无',
    traceCodeQrCodeUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAAAklEQVR4AewaftIAAASfSURBVO3BUY7kypEAQXei7n9lXyWgAB4SHKpaPbn6oZlA/CUVQ2WpGCq7iqEyKhaVOxW/obKrGCp/w8XrqIvXUR/+oeKnVO5UfEPljspSMVR2KncqFpVRMSoWlScV/w2V5eJ11MXrqA9/oPKk4m+rGCpPVJ6o7FRGxU+pPKnYXbyO+nCIyjcq7lR8o2KojIpF5X/p4nXUxeuoD4dVDJVRsVMZFU8qfqpiqCwVJ128jrp4HfXhDyr+toqhslSMip3KqNipjIqhslMZFT9V8VMXr6M+/IPKSSqjYlEZFUNlqRgqo+IbFUNlpzIqdiq/cfE66uJ1lP0LB6g8qXiislTcUfmNiv8PF6+jLl5HfVRGxROVUbGofKvip1RGxZOKofINld+ouKOyXLyO+lTcUVkq7qgsFUNlVCwqd1SWijsVO5W/RWWpeKIyKobKonKnYrl4HXXxOuqjMipGxZOKReWOylIxVEbFTuVbFYvKk4o7KovKnYql4knFUNldvI66eB0lEP9WMVSWijsqS8VQeVIxVJaKoTIqFpVR8S2VpWKo7CruqCwV/w2V5eJ11If/QOWJyqjYqdypWFS+pTIqnlR8Q2VU7FSeVAyV3cXrqIvXUfYv3FDZVTxR+amKOypLxR2Vb1TcUVkqhsqoWFRGxVDZVewuXkddvI768AcVO5VRsaiMiqGyqxgqi8qoGBWLyqgYFYvKqBgqi8pJFUNlVCwXr6ME4kbFojIqhspSMVRGxaLypOKOyk9V7FRGxVBZKobKrmKojIpvXLyOungd9akYKk9URsWiMiqGyq5iqOxURsUTlZ3KE5WdyqgYKt9QuVOxXLyOungd9eE/qBgqu4qhMip2KqPiicqTip9S2VUMlVGxqxgqT1SWi9dRH5VRMVR2FUNlVzFUdhU7lTsVi8qoGCpLxR2Vn6oYKkvFk4qhMiqWi9dRF6+jPhVD5acqhspJKjuVb1UMlZ3KqHhSsasYKsvF66iL11EflVExVL6hMiqGylIxVEbFUvGtip3KqBgqS8W3KobKUnFHZal4cvE6SiB+oWKo7CqGyrcqdiqjYqcyKnYqu4o7KruKofKkYrl4HXXxOupT8S2Vncqo2KmMiqGyqxgqS8UdlaXiicq3VEbFTmVU7FR2F6+jLl5HffgDlScVP6UyKhaVb1UMlScqu4qh8g2V37h4HfVRuVOxqDxRuVPxRGWpGCqjYlF5ojIqhso3VO6ofENlVOwuXkddvI76VDypeFJxR2WpuKOyqNxR+UbFnYpF5VsVT1S+pbJcvI66eB0lEH9JxVD5qYqdyqjYqYyKobJUPFEZFUNlqRgqu4qhMiqWi9dRH/6h4qdU7lTsVH5DZVQsFUNlVOxURsWTip+q2F28jrp4HfXhD1SeVDxRWSpGxROV/yWV31AZFcvF66iL11EfDqlYVH6j4onKHZVdxVDZVQyVpWKojIpF5cnF66gPh1UMlScVQ2VRGRW7ijsqS8VvqHyrYnfxOuriddSHP6j4G1RGxROVJyq7ijsVi8rfUjFUloonF6+jLl5HffgHlb9FZacyKp5U7FR2KidVDJVvqSwXr6P+DwJnxhBDG+d2AAAAAElFTkSuQmC',
};

export const EXAMPLE_DATA_A5 = {
    patient: {
        id: '37d7519b6722425ea97c2d87cdab88d2',
        name: '任盈盈',
        mobile: '19999999999',
        sex: '女',
        age: {
            year: 22,
            month: 1,
            day: 13,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: null,
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '成都青羊杏林春堂中医门诊部',
        shortName: '成都青羊杏林春堂中医门诊部',
        addressDetail: '成都市一环路西二段199号',
        contactPhone: '028-87732526',
    },
    'chargeForms': [
        {
            'id': 'ffffffff00000000168591800dc0e000',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca002',
                    'name': '诊费',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'unitPrice': 0.1,
                    'doseCount': 1,
                    'totalPrice': 0.1,
                    'discountedPrice': 0.01,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '诊费',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                },
            ],
            'sourceFormType': 1,
            'printFormType': 1,
            'processUsageInfo': null,
            'totalPrice': 0.1,
        },
        {
            'id': 'ffffffff00000000168591800dc0e006',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca009',
                    'name': '推拿',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 5,
                    'discountedPrice': 0.5,
                    'unitPrice': 5,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '推拿',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                },
            ],
            'sourceFormType': 3,
            'printFormType': 3,
            'processUsageInfo': null,
            'totalPrice': 17,
        },
        {
            'id': 'ffffffff00000000168591800dc0e007',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca00b',
                    'name': 'HPV基因全套',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 24,
                    'unitPrice': 24,
                    'discountedPrice': 12,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': 'HPV基因全套',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                },
            ],
            'sourceFormType': 2,
            'printFormType': 2,
            'processUsageInfo': null,
            'totalPrice': 66,
        },
        {
            'id': 'ffffffff00000000168591800dc0e008',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca00f',
                    'name': '热敏灸盒',
                    'unit': '支',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 0.5,
                    'unitPrice': 0.5,
                    'discountedPrice': 0.2,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': 'a-12-34',
                    'displaySpec': 'KC0.55号*号/支',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '支',
                    'socialName': '一次性使用静脉输液针',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'goodsStockInfos': [
                        {
                            'stockId': '100004783',
                            'batchNo': null,
                            'expiryDate': '',
                            'manufacturer': '江西洪达医疗器械',
                            'manufacturerFull': '江西洪达医疗器械集团有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },
            ],
            'sourceFormType': 9,
            'printFormType': 9,
            'processUsageInfo': null,
            'totalPrice': 274,
        },
        {
            'id': 'ffffffff00000000168591800dc0e008',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca00f',
                    'name': '输氧费',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 0.5,
                    'discountedPrice': 0.2,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '',
                    'displaySpec': 'KC0.55号*号/支',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '支',
                    'socialName': '输氧费',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'goodsStockInfos': [],
                },
            ],
            'sourceFormType': 999,
            'printFormType': 999,
            'processUsageInfo': null,
            'totalPrice': 99,
        },
        {
            'id': 'ffffffff00000000168591800dc0e009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca013',
                    'name': '埋线减脂套餐',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 15,
                    'unitPrice': 15,
                    'discountedPrice': 7.5,
                    'composeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff00000000167b12480dbca015',
                            'name': '埋线',
                            'unit': '盒',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'unitPrice': 10,
                            'totalPrice': 10,
                            'discountedPrice': 5,
                            'composeType': 2,
                            'composeChildren': null,
                            'position': '',
                            'displaySpec': '1ml*10支/盒',
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '盒',
                            'socialName': '卡介菌多糖核酸注射液(斯奇康)',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': 1,
                        },
                        {
                            'id': 'ffffffff00000000167b12480dbca014',
                            'name': '减脂',
                            'unit': '次',
                            'count': 2,
                            'unitCount': 2,
                            'doseCount': 1,
                            'totalPrice': 2,
                            'unitPrice': 1,
                            'discountedPrice': 1,
                            'composeType': 2,
                            productType: 3,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': null,
                            'socialCode': null,
                            'hisCode': null,
                            'socialUnit': '次',
                            'socialName': '尿常规',
                            'medicalFeeGrade': null,
                            'ownExpenseRatio': 1,
                        },
                    ],
                    'position': null,
                    'displaySpec': null,
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '次',
                    'socialName': '有检查检验',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                },
            ],
            'sourceFormType': 11,
            'printFormType': 11,
            'processUsageInfo': null,
            'totalPrice': 2289,
        },
        {
            'id': 'ffffffff00000000167b12480dbca022',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca024',
                    'name': '法莫替丁片（迪诺洛克）',
                    'unit': '片',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 9.52,
                    'unitPrice': 4.75,
                    'discountedPrice': 0.96,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': 'a-45-67',
                    'displaySpec': '1*21片/盒',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': '片',
                    'socialName': 'robins测试发药1',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'goodsStockInfos': [
                        {
                            'stockId': '100009993',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '云南白药',
                            'manufacturerFull': '李龙彬有限公司',
                            'supplierName': '长长长长长长长长长长长长长长长长',
                        },
                    ],
                },
            ],
            'sourceFormType': 4,
            'printFormType': 4,
            'processUsageInfo': null,
            'totalPrice': 9.65,
        },
        {
            'id': 'ffffffff00000000167b12480dbca01c',
            'chargeFormItems': [
                {
                    'id': 'ffffffff00000000167b12480dbca01d',
                    'name': '白花蛇舌草颗粒',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 0.22,
                    'unitPrice': 0.11,
                    'discountedPrice': 0.03,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': 'd-3-99',
                    'displaySpec': '05：10',
                    'socialCode': 'YP10785716',
                    'hisCode': '**********',
                    'socialUnit': 'g',
                    'socialName': '白花蛇舌草颗粒1/15（4-9）',
                    'medicalFeeGrade': 1,
                    specialRequirement: '先煎',
                    'ownExpenseRatio': 1,
                    'goodsStockInfos': [
                        {
                            'stockId': '159815',
                            'batchNo': '180603',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '四川省恒世康医药有限责任公司',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca01e',
                    'name': '盐知母',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 0.5,
                    'unitPrice': 0.25,
                    'discountedPrice': 0.05,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': 'B-9-10',
                    'displaySpec': '',
                    'socialCode': null,
                    'hisCode': null,
                    'socialUnit': 'g',
                    'socialName': '盐知母',
                    specialRequirement: '先煎',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'goodsStockInfos': [
                        {
                            'stockId': '175568',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '四川省恒世康医药有限责任公司',
                        },
                    ],
                },
                {
                    'id': 'ffffffff00000000167b12480dbca01f',
                    'name': '盐黄柏',
                    'unit': 'g',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 6,
                    'unitPrice': 3,
                    'discountedPrice': 0.6,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': 'c-19-8',
                    'displaySpec': '规格',
                    'socialCode': 'YP10788727',
                    'hisCode': '**********',
                    'socialUnit': 'g',
                    specialRequirement: '先煎',
                    'socialName': '盐黄柏',
                    'medicalFeeGrade': 1,
                    'ownExpenseRatio': 1,
                    'goodsStockInfos': [
                        {
                            'stockId': '100000740',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '江西天之海',
                            'manufacturerFull': '江西天之海药业股份有限公司',
                            'supplierName': '123123123131313',
                        },
                    ],
                },
            ],
            'sourceFormType': 6,
            'printFormType': 6,
            'specification': '中药饮片',
            'doseCount': 1,
            'dailyDosage': '1日1剂',
            'usage': '煎服',
            'freq': '1日3次',
            'usageLevel': '每次150ml',
            'processUsageInfo': null,
            'totalPrice': 10.8,
        },
        {
            'id': 'ffffffff00000000167b12480dbca01c',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000016712480dbca021',
                    'name': '镜架',
                    'unit': '副',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 22,
                    'unitPrice': 22,
                    'discountedPrice': 0.01,
                    'composeType': 0,
                    'composeChildren': null,
                    'position': '10-8',
                    'displaySpec': 'S:19.75 / C:-2.00',
                    'socialCode': 'YP10789472',
                    'hisCode': '**********',
                    'socialUnit': 'g',
                    'socialName': '',
                    'medicalFeeGrade': 1,
                    specialRequirement: '先煎',
                    'ownExpenseRatio': 1,
                    'goodsStockInfos': [
                        {
                            'stockId': '140643',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                },
            ],
            'sourceFormType': 22,
            'printFormType': 22,
            'processUsageInfo': null,
            'totalPrice': 998,
        },
        {
            'id': 'ffffffff00000000349557fa253a000c',
            'chargeFormItems': [],
            'sourceFormType': 24,
            'printFormType': 24,
            'totalPrice': 0,
            'doseCount': 0,
            'usage': '远用',
            'requirement': '这是一个备注',
            'optometristId': '6e45706922a74966ab51e4ed1e604641',
            'optometristName': '刘喜',
            'glassesType': 0,
            'glassesParams': {
                'items': [
                    {
                        'key': 'frameSpherical',
                        'name': '球镜',
                        'rightEyeValue': '-0.25',
                        'leftEyeValue': '-1.75',
                    },
                    {
                        'key': 'frameLenticular',
                        'name': '柱镜',
                        'rightEyeValue': '-0.50',
                        'leftEyeValue': '-1.25',
                    },
                    {
                        'key': 'frameAxial',
                        'name': '轴位',
                        'rightEyeValue': '11',
                        'leftEyeValue': '12',
                    },
                    {
                        'key': 'framePrism',
                        'name': '棱镜',
                        'rightEyeValue': '13',
                        'leftEyeValue': '14',
                    },
                    {
                        'key': 'frameBase',
                        'name': '基底',
                        'rightEyeValue': '15',
                        'leftEyeValue': '16',
                    },
                    {
                        'key': 'frameCva',
                        'name': '矫正视力',
                        'rightEyeValue': '17',
                        'leftEyeValue': '18',
                    },
                    {
                        'key': 'frameAdd',
                        'name': '下加光',
                        'rightEyeValue': '19',
                        'leftEyeValue': '20',
                    },
                    {
                        'key': 'framePupilDistance',
                        'name': '瞳距',
                        'rightEyeValue': '21',
                        'leftEyeValue': '22',
                    },
                    {
                        'key': 'framePupilHeight',
                        'name': '瞳高',
                        'rightEyeValue': '23',
                        'leftEyeValue': '24',
                    },
                    {
                        'key': 'contactFocalLength',
                        'name': '后顶焦度',
                        'rightEyeValue': '-0.25',
                        'leftEyeValue': '-0.5',
                    },
                    {
                        'key': 'contactBozr',
                        'name': '基弧',
                        'rightEyeValue': '11',
                        'leftEyeValue': '12',
                    },
                    {
                        'key': 'contactDiameter',
                        'name': '直径',
                        'rightEyeValue': '13',
                        'leftEyeValue': '14',
                    },
                    {
                        'key': 'contactLenticular',
                        'name': '柱镜',
                        'rightEyeValue': '-1.25',
                        'leftEyeValue': '-1.5',
                    },
                    {
                        'key': 'contactAxial',
                        'name': '轴位',
                        'rightEyeValue': '15',
                        'leftEyeValue': '16',
                    },
                ],
            },
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000034a6378fc5ea8007',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000034a6378aa5ea800c',
                    'name': '其他项目',
                    'unit': '次',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 13,
                    'discountedPrice': 13,
                    'discountedUnitPrice': 13,
                    'unitPrice': 13,
                    'composeType': 0,
                    'goodsTypeId': 33,
                    'feeComposeType': 20,
                    'feeTypeId': 1003,
                    'goodsFeeType': 1,
                    'composeChildren': [
                        {
                            'id': 'ffffffff0000000034a6378aa5ea800e',
                            'name': 'ABO红细胞定型',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 4.87,
                            'discountedPrice': 4.87,
                            'discountedUnitPrice': 4.87,
                            'unitPrice': 4.87,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 1003,
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': '002600000010000-260000001',
                            'hisCode': '300000905086163',
                            'socialUnit': '次',
                            'socialName': 'ABO红细胞定型',
                            'medicalFeeGrade': 2,
                            'ownExpenseRatio': 1,
                            'ownExpenseFee': null,
                            'inscpScpAmt': 0,
                            'overlmtAmt': 0,
                            'productType': 19,
                            'productSubType': 0,
                        },
                        {
                            'id': 'ffffffff0000000034a6378aa5ea800f',
                            'name': '阿斯匹林耐量试验（ATT）',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 4.88,
                            'discountedPrice': 4.88,
                            'discountedUnitPrice': 4.88,
                            'unitPrice': 4.88,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 1003,
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': '002502030070000-250203007',
                            'hisCode': '300000905086164',
                            'socialUnit': '次',
                            'socialName': '阿斯匹林耐量试验（ATT）',
                            'medicalFeeGrade': 2,
                            'ownExpenseRatio': 1,
                            'ownExpenseFee': null,
                            'inscpScpAmt': 0,
                            'overlmtAmt': 0,
                            'productType': 19,
                            'productSubType': 0,
                        },
                        {
                            'id': 'ffffffff0000000034a6378aa5ea800d',
                            'name': 'A型超声检查',
                            'unit': '次',
                            'count': 1,
                            'unitCount': 1,
                            'doseCount': 1,
                            'totalPrice': 3.25,
                            'discountedPrice': 3.25,
                            'discountedUnitPrice': 3.25,
                            'unitPrice': 3.25,
                            'composeType': 0,
                            'goodsTypeId': 33,
                            'feeComposeType': 0,
                            'feeTypeId': 0,
                            'goodsFeeType': 2,
                            'composeChildren': null,
                            'position': null,
                            'displaySpec': '次',
                            'cmSpec': '',
                            'sourceItemType': 0,
                            'socialCode': '002201000010000-220100001',
                            'hisCode': '300000905085989',
                            'socialUnit': '次',
                            'socialName': 'A型超声检查',
                            'medicalFeeGrade': 2,
                            'ownExpenseRatio': 1,
                            'ownExpenseFee': null,
                            'inscpScpAmt': 0,
                            'overlmtAmt': 0,
                            'productType': 19,
                            'productSubType': 0,
                        },
                    ],
                    'position': null,
                    'displaySpec': '次',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': '002201000010000-220100001',
                    'hisCode': '300000905086162',
                    'socialUnit': '次',
                    'socialName': '其他费用20231128',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': null,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 19,
                    'productSubType': 0,
                },
            ],
            'sourceFormType': 21,
            'printFormType': 21,
            'totalPrice': 13,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
    ],
    chargeTransactions: [
        {
            payMode: 2,
            payModeName: '现金',
            payModeDisplayName: '现金',
            amount: 800.0,
        },
        {
            payMode: 3,
            payModeName: '医保',
            payModeDisplayName: '医保',
            amount: 79.72,
        },
    ],
    totalFee: 899.72,
    discountFee: -20.0,
    receivableFee: 879.72,
    netIncomeFee: 281.3,
    chargedByName: '令狐冲',
    chargedTime: '2019-12-27T07:22:18Z',
    sellerName: '胡红牛',
    doctorName: '胡青牛',
    patientOrderNo: '09210548',
    memberCardBalance: null,
    memberCardMobile: '',
    memberCardBeginningBalance: '', // 会员卡原有余额
    healthCardBeginningBalance: '567.68', // 社保卡原有余额
    healthCardOwnerRelationToPatient: '父女', // 持卡人关系
    healthCardBalance: '0.00', // 社保卡余额
    healthCardNo: '01000520', // 社保卡卡号
    healthCardOwner: '任我行', // 持卡人姓名"
    generalFundpayBalc: 200, // 统筹余额

    subTotals: {
        'registrationFee': 0.1000,
        'westernMedicineFee': 66.3500,
        'chineseMedicineFee': 10.8000,
        'examinationFee': 57.0000,
        'treatmentFee': 2291.0000,
        'composeProductFee': 298.0000,
        'materialFee': 274.0000,
        'onlineConsultationFee': 0,
        'expressDeliveryFee': 0,
        'decoctionFee': 0,
        'otherFee': 66.0000,
        'eyeFee': 66.00,
    },
    medicalBill: {
        registrationFee: 0,
        westernMedicineFee: 1.9,
        chineseMedicineFee: 0,
        chineseComposeMedicineFee: 0,
        examinationFee: 0,
        treatmentFee: 0,
        materialFee: 0,
        otherFee: 0,
    },
    medicalBills: [
        {
            'name': '西药费',
            'totalFee': 9,
            'totalCount': 3,
            'unit': '项',
            'printType': 1,
            'feeTypeId': null,
            'innerFlag': 0,
            'sort': 0,
        },
        {
            'name': '中药饮片',
            'totalFee': 24,
            'totalCount': 3,
            'unit': '项',
            'printType': 2,
            'feeTypeId': null,
            'innerFlag': 0,
            'sort': 0,
        },
        {
            'name': '检查费',
            'totalFee': 20,
            'totalCount': 1,
            'unit': '项',
            'printType': 4,
            'feeTypeId': null,
            'innerFlag': 0,
            'sort': 0,
        },
        {
            'name': '化验费',
            'totalFee': 149,
            'totalCount': 1,
            'unit': '项',
            'printType': 5,
            'feeTypeId': null,
            'innerFlag': 0,
            'sort': 0,
        },
        {
            'name': '治疗费',
            'totalFee': 2,
            'totalCount': 1,
            'unit': '项',
            'printType': 6,
            'feeTypeId': null,
            'innerFlag': 0,
            'sort': 0,
        },
        {
            'name': i18n.t('registrationFeeName'),
            'totalFee': 10,
            'totalCount': 1,
            'unit': '项',
            'printType': 7,
            'feeTypeId': null,
            'innerFlag': 0,
            'sort': 0,
        },
        {
            'name': '护理费',
            'totalFee': 125,
            'totalCount': 1,
            'unit': '项',
            'printType': 11,
            'feeTypeId': null,
            'innerFlag': 0,
            'sort': 0,
        },
    ],
    healthCardPaymentFee: 0.0,
    personalPaymentFee: 1.99,

    shebaoPayment: {
        cardId: '********', // 卡号
        cardOwner: '任我行', // 卡持有者
        cardOwnerType: '职工', // 持卡人类型 职工 居民 离休干部 等
        idCardNum: '880118198001015233', // 卡持有者身份证号
        beforeCardBalance: 2000, // 刷卡前余额
        cardBalance: 2000.0, // 卡余额
        relationToPatient: '父女', // 社保支付的持卡人和患者的关系
        // 帐户支付金额 + 统筹支付金额 + 其它支付金额 = 社保支付金额
        receivedFee: 34.46,
        accountPaymentFee: 45.00, // 个人帐户支付金额
        personalPaymentFee: 800.00, // 个人现金支付
        fundPaymentFee: 34.72, // 基金支付金额
        otherPaymentFee: 0.00, // 其它支付金额
        // region: 'hangzhou',
        medType: '普通门诊',
        extraInfo: {
            hifpPay: 29.72, // 统筹支付金额
            cvlservPay: 0, // 公务员补助
            hifmiPay: 0, // 大病保险
            mafPay: 0, // 医疗救助
            hifesPay: 1, // 企业补充医疗保险
            othPay: 1, // 其他支出
            wltpayAmt: 1, // 医保钱包支出
            // curYearBalance: 8, // 当年账户余额
            // allYearBalance: 10, // 历年账户余额
            // curYearAccountPaymentFee: 7, // 本年账户支付
            // allYearAccountPaymentFee: 9, // 历年账户支付
            // cashPaymentFee: 0, // 医保现金支付
            // selfConceitFee: 1, // 自负金额
            // allYearAccountPaymentSelfConceitFee: 2, // 历年账户支付自负部分 （省医保为空）
            // personalHandledAmount: 3, // 自理金额
            // allYearAccountPaymentPersonalHandled: 4, // 历年账户支付自理 （省医保为空）
            // personalPaymentAmount: 5, // 自费金额
            // allYearAccountPaymentPersonalPayment: 6, // 历年账户支付自费 （省医保为空）
            // curYearOutpatientStartingPointStandardAmount: 11, // 本年门诊起付标准支付累计 （省医保为空）
            gongjiAccountPaymentFee: 20,
            gongjiFundPaymentFee: 0,
            gongjiOtherPaymentFee: 0,
            gongjiPsnCashPay: 0,
            gongjiBalc: 1200,
            gongjiAuthorName: '任盈盈',
            gongjiRelation: '父女',
        },
    },
};

export const EXAMPLE_RECHARGE_DATA = {
    patient: {
        id: '622c7a3a31e14b72b67e7c717f65ab79',
        name: 'wxd1109',
        namePy: null,
        namePyFirst: null,
        birthday: null,
        mobile: '***********',
        sex: '男',
        age: {
            year: 1,
            month: 2,
            day: 16,
        },
        wxOpenId: null,
        isMember: 1,
        address: null,
        idCard: null,
        patientSource: null,
        tags: null,
    },
    organ: {
        id: 'fff730ccc5ee45d783d82a85b8a0e52d',
        name: '一二三四五六七八九十一二三四五六七八九十一二三四五六七八九',
        shortName: '高新大源店',
        addressDetail: '大源北支路28号大源北支路28号',
        contactPhone: '**********',
        logo: null,
    },
    memberType: '挂号7折卡',
    memberCardMobile: '***********',
    principal: 12.0,
    present: 12.0,
    payMode: 1,
    netIncomeFee: 12.0,
    memberCardBalance: 24.0,
    chargedByName: '刘喜喜喜',
    chargedTime: '2020-02-03T11:15:10Z',
    qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAAAklEQVR4AewaftIAAASOSURBVO3BUW7kRhYAwUyi73/l3CnADxAKHLpbEr0/jBCIX1IxVJaKoTIqFpUrFWdUlop3qYyKRWVUDJXfcPC41cHjVi++qPiUyhWVMyqfUhkVi8qZil3Fpyq+Q2U5eNzq4HGrF3+hcqXiUxVDZakYKjuVUbGrOKPyjop3qVyp2B08bvXiJhWLyhWVUTFUdiqjYlG5UjFU/msHj1sdPG714iYqS8WZiisVi8oZlaXiXRVD5b9w8LjVweNWL/6i4icqFpVRMVSWijMq71A5U7GojIqfqPjUweNWL75Q+S0qS8VQGRWLyqjYVQyVUbGojIqhslQMlVGxqIyKncpPHDxudfC4lf3Bf0Tlt1T8hMqu4rcdPG518LiVQPyjYqfyExXfobJUDJUrFZ9SOVOxUxkVi8qVg8et7A/+ofKuiisqu4orKlcqhsqnKobKUjFURsWi8q6K3cHjVgePW734omKoLBVDZajsKnYVQ2VXcaZiURkqo+JTKp+qGCpXVHYHj1sdPG5lf3BBZVRcURkVi8qo2KmcqXiHyqgYKr+tYqcyKnYHj1sJxIWKMyq7ip3KmYpF5UzFojIqhspScUVlVOxURsVQuVKxUxkVy8HjVgePW734omKncqViqIyKT1UMlaXiisqVijMqS8VQuVIxVHYVu4PHrQ4et3pVnFFZKq6onFHZVbyrYlEZFbuKobJTOVOxq9ip/MTB41YCcaJiURkVV1R2Fe9SGRU7lVFxRWWpeJfKqFhU3lUxVJaDx60OHrd6VQyVofIOlTMVi8qo+JTKqNipjIorKu9SuVKxqJypWA4etzp43OqlMiqGylIxVEbFUjFUdhVnVHYVu4ozKkvFULlS8S6VpWKofOrgcasXX6iMikVlVAyVd6iMilGxU7lSsVM5U7Go/JaKoXJFZTl43OrgcSv7gxMqS8UZlaXiXSpXKobKUjFURsWnVEbFojIqhsqnKnYHj1sdPG5lf3BB5TsqFpVRsVP5jopFZVTsVK5UfIfKUnFGZTl43EogTlTsVHYVZ1TeUXFGZam4g8pSMVR2FUPlSsXu4HGrg8etBOIfFTuVMxWLyqjYqYyKncqoeJfKpyqGyjsqzqgsFUNld/C41cHjVi/+RcVQGSpLxZWKobKrGCqjYlEZFZ+q+I6KncqoWFTOVCwHj1sJxImKd6iMiqGyVHyHylIxVEbFojIqhspSMVRGxaLyExVXDh63Onjcyv7gBipXKnYqo2Knsqu4ojIqhsqu4orKqFhURsVQWQ4etzp43Oql8lsqdhVDZahcUfmUyq7iO1SWiu+oWA4et3rxRcWnVL6j4lMqO5UrKt9R8RsOHrc6eNzqxV+oXKn4VMVQ2VV8quKKyrtUPlUxVEbFcvC41cHjVi9upjIqRsVvUBkV76pYVM5UXFFZKkbFUFkOHrd6cZOKd6iMip3KqNipDJVRsVScUVkqhspO5UzFTmV38LjVweNWL/6i4v9JZan4DpWl4l0VQ2WpGCo7lSsHj1sdPG714guV36KyVAyVT6mcqVgqhsqoeIfKqNipjIqhslRcOXjc6n+kBcvFPSUNAQAAAABJRU5ErkJggg==',
};
export const QR_CODE =
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAAAklEQVR4AewaftIAAASOSURBVO3BUW7kRhYAwUyi73/l3CnADxAKHLpbEr0/jBCIX1IxVJaKoTIqFpUrFWdUlop3qYyKRWVUDJXfcPC41cHjVi++qPiUyhWVMyqfUhkVi8qZil3Fpyq+Q2U5eNzq4HGrF3+hcqXiUxVDZakYKjuVUbGrOKPyjop3qVyp2B08bvXiJhWLyhWVUTFUdiqjYlG5UjFU/msHj1sdPG714iYqS8WZiisVi8oZlaXiXRVD5b9w8LjVweNWL/6i4icqFpVRMVSWijMq71A5U7GojIqfqPjUweNWL75Q+S0qS8VQGRWLyqjYVQyVUbGojIqhslQMlVGxqIyKncpPHDxudfC4lf3Bf0Tlt1T8hMqu4rcdPG518LiVQPyjYqfyExXfobJUDJUrFZ9SOVOxUxkVi8qVg8et7A/+ofKuiisqu4orKlcqhsqnKobKUjFURsWi8q6K3cHjVgePW734omKoLBVDZajsKnYVQ2VXcaZiURkqo+JTKp+qGCpXVHYHj1sdPG5lf3BBZVRcURkVi8qo2KmcqXiHyqgYKr+tYqcyKnYHj1sJxIWKMyq7ip3KmYpF5UzFojIqhspScUVlVOxURsVQuVKxUxkVy8HjVgePW734omKncqViqIyKT1UMlaXiisqVijMqS8VQuVIxVHYVu4PHrQ4et3pVnFFZKq6onFHZVbyrYlEZFbuKobJTOVOxq9ip/MTB41YCcaJiURkVV1R2Fe9SGRU7lVFxRWWpeJfKqFhU3lUxVJaDx60OHrd6VQyVofIOlTMVi8qo+JTKqNipjIorKu9SuVKxqJypWA4etzp43OqlMiqGylIxVEbFUjFUdhVnVHYVu4ozKkvFULlS8S6VpWKofOrgcasXX6iMikVlVAyVd6iMilGxU7lSsVM5U7Go/JaKoXJFZTl43OrgcSv7gxMqS8UZlaXiXSpXKobKUjFURsWnVEbFojIqhsqnKnYHj1sdPG5lf3BB5TsqFpVRsVP5jopFZVTsVK5UfIfKUnFGZTl43EogTlTsVHYVZ1TeUXFGZam4g8pSMVR2FUPlSsXu4HGrg8etBOIfFTuVMxWLyqjYqYyKncqoeJfKpyqGyjsqzqgsFUNld/C41cHjVi/+RcVQGSpLxZWKobKrGCqjYlEZFZ+q+I6KncqoWFTOVCwHj1sJxImKd6iMiqGyVHyHylIxVEbFojIqhspSMVRGxaLyExVXDh63Onjcyv7gBipXKnYqo2Knsqu4ojIqhsqu4orKqFhURsVQWQ4etzp43Oql8lsqdhVDZahcUfmUyq7iO1SWiu+oWA4et3rxRcWnVL6j4lMqO5UrKt9R8RsOHrc6eNzqxV+oXKn4VMVQ2VV8quKKyrtUPlUxVEbFcvC41cHjVi9upjIqRsVvUBkV76pYVM5UXFFZKkbFUFkOHrd6cZOKd6iMip3KqNipDJVRsVScUVkqhspO5UzFTmV38LjVweNWL/6i4v9JZan4DpWl4l0VQ2WpGCo7lSsHj1sdPG714guV36KyVAyVT6mcqVgqhsqoeIfKqNipjIqhslRcOXjc6n+kBcvFPSUNAQAAAABJRU5ErkJggg==';

export const BILL_CONFIG = {
    format: 'guangdong',
    guangdong: {
        chargeInstitution: 1,
        flowNumber: 1,
        institutionName: '123',
        medicalRecordNumber: 1,
        socialSecurityNumber: 1,
    },
};

export const PHARMACY_EXAMPLE_DATA = {
    'id': 'ffffffff0000000035027a37b2c24000',
    'patient': {
        'id': 'ffffffff0000000034d9924c2e410000',
        'chainId': 'ffffffff0000000034b430a947024000',
        'name': '林黛玉',
        'namePy': 'lindaiyu',
        'namePyFirst': 'LDY',
        'mobile': '18200353783',
        'countryCode': '86',
        'sex': '女',
        'birthday': null,
        'idCardType': null,
        'idCard': '',
        'isMember': 1,
        'age': {
            'year': 18,
            'month': 6,
            'day': 24,
        },
        'address': {
            'addressCityId': '',
            'addressCityName': '',
            'addressDetail': '',
            'addressDistrictId': '',
            'addressDistrictName': '',
            'addressGeo': null,
            'addressProvinceId': '',
            'addressProvinceName': '',
            'addressPostcode': null,
        },
        'sn': '000002',
        'remark': '',
        'profession': '',
        'company': '',
        'memberInfo': null,
        'patientSource': null,
        'tags': null,
        'marital': null,
        'weight': null,
        'ethnicity': '',
        'wxBindStatus': 0,
        'isAttention': null,
        'appFlag': 0,
        'arrearsFlag': 0,
        'externalCodeId': null,
        'externalCodeRemark': null,
        'pastHistory': '',
        'allergicHistory': '',
        'shebaoCardInfo': null,
        'chronicArchivesInfo': null,
        'childCareInfo': null,
        'patientPoints': null,
    },
    'organ': {
        'id': 'ffffffff0000000034b430a947024002',
        'name': 'A药店',
        'shortName': 'A药店',
        'addressProvinceName': null,
        'addressCityName': null,
        'addressDistrictName': null,
        addressDetail: '大源北支路28号大源北支路28号',
        'contactPhone': '18634677934',
        'logo': null,
        'category': '',
        'addressProvinceId': null,
        'addressCityId': null,
        'addressDistrictId': null,
        'hisType': 10,
        'medicalDocumentsTitle': null,
    },
    'memberInfo': {
        'principal': 0,
        'present': 0,
        'points': 246,
        'pointsTotal': 246,
        'patient': {
            'id': 'ffffffff0000000034d9924c2e410000',
            'name': '林黛玉',
            'mobile': '18200353783',
            'isMember': 1,
        },
        'memberType': {
            'id': 'ffffffff0000000034b430afad134000',
            'name': '普通会员',
        },
        'cardBalance': 0,
        'changePoints': 228,
    },
    'chargeForms': [
        {
            'id': 'ffffffff0000000035027a8232c24008',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000035027a37b2c2400d',
                    'name': '伊洁士牌84消毒液(450ml)',
                    'unit': '瓶',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 30,
                    'discountedPrice': 27,
                    'discountedUnitPrice': 27,
                    'unitPrice': 30,
                    'composeType': 0,
                    'goodsTypeId': 91,
                    'feeComposeType': 0,
                    'feeTypeId': '1004',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'C-4-88',
                    'displaySpec': '450ml/瓶',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': '000047',
                    'socialUnit': '瓶',
                    'socialName': '伊洁士牌84消毒液(450ml)',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': 27,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 2,
                    'productSubType': 4,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 7,
                        'id': 'ffffffff0000000034f060ea1c51c000',
                        'goodsId': 'ffffffff0000000034f060ea1c51c000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3815937043048202000,
                            'status': 30,
                            'gspOrderNo': 'SS20250327000002',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-03-27 11:17:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 30,
                        },
                        'name': '伊洁士牌84消毒液(450ml)',
                        'displayName': '伊洁士牌84消毒液(450ml)',
                        'displaySpec': '450ml/瓶',
                        'checkSpec': '450ml//',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 91,
                        'type': 2,
                        'subType': 4,
                        'barCode': '6958895488073',
                        'manufacturer': '四川省伊洁士医疗科技有限公司',
                        'pieceNum': 450,
                        'pieceUnit': 'ml',
                        'packageUnit': '瓶',
                        'dismounting': 0,
                        'medicineCadn': '',
                        'medicineNmpn': '川(德阳)卫消证字(2015)第0001号',
                        'position': 'C-4-88',
                        'chainPackagePrice': 30,
                        'chainPiecePrice': 0.0667,
                        'piecePrice': 0.0667,
                        'packagePrice': 30,
                        'packageCostPrice': 18,
                        'minPackagePrice': 30,
                        'maxPackagePrice': 30,
                        'totalSalePrice': 30,
                        'priceType': 1,
                        'inTaxRat': 0,
                        'outTaxRat': 0,
                        'pieceCount': 0,
                        'packageCount': 495,
                        'dispGoodsCount': '495瓶',
                        'stockPieceCount': 0,
                        'stockPackageCount': 495,
                        'dispStockGoodsCount': '495瓶',
                        'availablePackageCount': 495,
                        'availablePieceCount': 0,
                        'outPieceCount': 0,
                        'outPackageCount': 495,
                        'dispOutGoodsCount': '495瓶',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0瓶',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0瓶',
                        'lastPackageCostPrice': 18,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000047',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000226dfd500863a000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-05-14T05:47:00Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '四川省伊洁士医疗科技有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034f060ea1c51c000',
                            'goodsType': 2,
                            'isDummy': 0,
                            'medicineNum': '000047',
                            'medicalFeeGrade': 0,
                        },
                        'recentAvgSell': 0.1,
                        'turnoverDays': 4950,
                        'profitRat': 40,
                        'lastStockInId': 50287046,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 18,
                                'stockPieceCount': 0,
                                'stockPackageCount': 495,
                                'availablePackageCount': 495,
                                'availablePieceCount': 0,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 18,
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '1004',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818136588630818817',
                                'goodsId': 'ffffffff0000000034f060ea1c51c000',
                                'parentGoodsId': 'ffffffff0000000034fbe50add2b8000',
                                'composeType': 40,
                            },
                            {
                                'composeId': '3818222878819008519',
                                'goodsId': 'ffffffff0000000034f060ea1c51c000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7a7d46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'minExpiryDate': '2027-03-18',
                        'expiredWarnMonths': 1,
                        'maintainType': 2,
                        'maintainTypeName': '普通养护',
                        'mha': '四川省伊洁士医疗科技有限公司',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c2400d',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285886,
                                'pieceNum': 450,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2027-03-18',
                                'productionDate': '2025-03-10',
                                'inDate': '2025-05-19T05:55:24Z',
                                'supplierId': 'ffffffff0000000034bc465d382b4000',
                                'supplierName': 'feiy供应商',
                                'batchNo': '345243',
                                'packageCostPrice': 18,
                                'packagePrice': 30,
                                'piecePrice': 0.0667,
                                'pieceCount': 0,
                                'packageCount': 495,
                                'dispGoodsCount': '495瓶',
                                'stockPieceCount': 0,
                                'stockPackageCount': 495,
                                'dispStockGoodsCount': '495瓶',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0瓶',
                                'cutTotalPieceCount': 450,
                                'cutPackageCount': 1,
                                'totalSalePrice': 30,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 3,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 25,
                                'parentId': null,
                                'displayName': '消毒产品',
                                'name': '消毒产品',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 2,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -3,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 1,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -3,
                    'totalPromotionFee': -3,
                    'unitAdjustmentFee': 0,
                },
            ],
            'sourceFormType': 9,
            'printFormType': 9,
            'totalPrice': 30,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000035027a8232c24009',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000035027a37b2c2400f',
                    'name': '燕窝',
                    'unit': '盒',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 200,
                    'discountedPrice': 200,
                    'discountedUnitPrice': 100,
                    'unitPrice': 100,
                    'composeType': 0,
                    'goodsTypeId': 27,
                    'feeComposeType': 0,
                    'feeTypeId': '27',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'C-3-4',
                    'displaySpec': '100g/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': '000030',
                    'socialUnit': '盒',
                    'socialName': '燕窝',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': 200,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 7,
                    'productSubType': 3,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 4,
                        'id': 'ffffffff0000000034e0338f3ad3c000',
                        'goodsId': 'ffffffff0000000034e0338f3ad3c000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3810101977789202400,
                            'status': 30,
                            'gspOrderNo': 'SS20241121000001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2024-11-21 16:12:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 100,
                        },
                        'name': '燕窝',
                        'displayName': '燕窝',
                        'displaySpec': '100g/盒',
                        'checkSpec': '100g//',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 27,
                        'type': 7,
                        'subType': 3,
                        'manufacturer': '四川省仁德',
                        'pieceNum': 100,
                        'pieceUnit': 'g',
                        'packageUnit': '盒',
                        'dismounting': 1,
                        'medicineCadn': '',
                        'medicineNmpn': 'Z78623488455',
                        'position': 'C-3-4',
                        'chainPackagePrice': 100,
                        'chainPiecePrice': 1,
                        'piecePrice': 1,
                        'packagePrice': 100,
                        'packageCostPrice': 120,
                        'minPackagePrice': 100,
                        'maxPackagePrice': 100,
                        'totalSalePrice': 200,
                        'priceType': 1,
                        'inTaxRat': 0,
                        'outTaxRat': 0,
                        'pieceCount': 94.1,
                        'packageCount': 1076,
                        'dispGoodsCount': '1076盒94.1g',
                        'stockPieceCount': 94.1,
                        'stockPackageCount': 1076,
                        'dispStockGoodsCount': '1076盒94.1g',
                        'availablePackageCount': 1076,
                        'availablePieceCount': 94.1,
                        'outPieceCount': 94.1,
                        'outPackageCount': 1076,
                        'dispOutGoodsCount': '1076盒94.1g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0盒',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0盒',
                        'lastPackageCostPrice': 120,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000030',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-05-14T05:46:49Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '四川省仁德制药有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034e0338f3ad3c000',
                            'goodsType': 7,
                            'isDummy': 0,
                            'medicineNum': '000030',
                            'medicalFeeGrade': 0,
                        },
                        'recentAvgSell': 0.17,
                        'turnoverDays': 6335,
                        'profitRat': -16.29,
                        'lastStockInId': 50275532,
                        'lastStockInOrderSupplier': '瑞城供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 120,
                                'stockPieceCount': 94.1,
                                'stockPackageCount': 1076,
                                'availablePackageCount': 1076,
                                'availablePieceCount': 94.1,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 116.28808,
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '27',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818136588630818819',
                                'goodsId': 'ffffffff0000000034e0338f3ad3c000',
                                'parentGoodsId': 'ffffffff0000000034fbe50add2b8000',
                                'composeType': 40,
                            },
                            {
                                'composeId': '3818222892777652234',
                                'goodsId': 'ffffffff0000000034e0338f3ad3c000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7dbd46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 1,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'minExpiryDate': '2026-12-31',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c2400f',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50275384,
                                'pieceNum': 100,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2026-12-31',
                                'productionDate': '2024-12-31',
                                'inDate': '2024-11-28T03:46:10Z',
                                'supplierId': 'ffffffff0000000034bd19cc5831c000',
                                'supplierName': '瑞城供应商',
                                'batchNo': '1',
                                'packageCostPrice': 120,
                                'packagePrice': 100,
                                'piecePrice': 1,
                                'pieceCount': 0,
                                'packageCount': 977,
                                'dispGoodsCount': '977盒',
                                'stockPieceCount': 0,
                                'stockPackageCount': 977,
                                'dispStockGoodsCount': '977盒',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0盒',
                                'cutTotalPieceCount': 200,
                                'cutPackageCount': 2,
                                'totalSalePrice': 200,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 5,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 16010,
                                'parentId': 16,
                                'displayName': '食品/保健食品',
                                'name': '保健食品',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 1,
                                'type': 2,
                                'discountWay': 2,
                                'ruleType': 3,
                                'giftBySingleGoodsRuleDetails': {
                                    'thresholdCount': 2,
                                    'discountValue': 1,
                                    'level': 1,
                                    'isCycle': 0,
                                    'giftGoodsId': 'ffffffff0000000034e0338f3ad3c000',
                                    'giftGoodsName': '燕窝',
                                    'giftGoodsUnit': '盒',
                                },
                                'goodsTypeName': '',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': 0,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 2,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': 0,
                    'totalPromotionFee': 0,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24010',
                    'name': '虫草',
                    'unit': '包',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 500,
                    'discountedPrice': 400,
                    'discountedUnitPrice': 400,
                    'unitPrice': 500,
                    'composeType': 0,
                    'goodsTypeId': 26,
                    'feeComposeType': 0,
                    'feeTypeId': '26',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'C-3-6',
                    'displaySpec': '50g/包',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': '000032',
                    'socialUnit': '包',
                    'socialName': '虫草',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': 400,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 7,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 2,
                        'id': 'ffffffff0000000034e033995ad3c000',
                        'goodsId': 'ffffffff0000000034e033995ad3c000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3810102022886359000,
                            'status': 30,
                            'gspOrderNo': 'SS20241121000003',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2024-11-21 16:14:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 500,
                        },
                        'name': '虫草',
                        'displayName': '虫草',
                        'displaySpec': '50g/包',
                        'checkSpec': '50g//',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 26,
                        'type': 7,
                        'subType': 2,
                        'manufacturer': '西藏昌都藏药',
                        'pieceNum': 50,
                        'pieceUnit': 'g',
                        'packageUnit': '包',
                        'dismounting': 1,
                        'medicineCadn': '',
                        'medicineNmpn': 'Z97800456345',
                        'position': 'C-3-6',
                        'chainPackagePrice': 500,
                        'chainPiecePrice': 10,
                        'piecePrice': 10,
                        'packagePrice': 500,
                        'packageCostPrice': 200,
                        'minPackagePrice': 500,
                        'maxPackagePrice': 500,
                        'totalSalePrice': 500,
                        'priceType': 1,
                        'inTaxRat': 0,
                        'outTaxRat': 0,
                        'pieceCount': 30.15,
                        'packageCount': 82,
                        'dispGoodsCount': '82包30.15g',
                        'stockPieceCount': 30.15,
                        'stockPackageCount': 82,
                        'dispStockGoodsCount': '82包30.15g',
                        'availablePackageCount': 82,
                        'availablePieceCount': 30.15,
                        'outPieceCount': 30.15,
                        'outPackageCount': 82,
                        'dispOutGoodsCount': '82包30.15g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0包',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0包',
                        'lastPackageCostPrice': 200,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000032',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-03-27T03:12:45Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '西藏昌都藏药厂',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034e033995ad3c000',
                            'goodsType': 7,
                            'isDummy': 0,
                            'medicineNum': '000032',
                            'medicalFeeGrade': 0,
                        },
                        'recentAvgSell': 0.11,
                        'turnoverDays': 751,
                        'profitRat': 60,
                        'lastStockInId': 50275457,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 200,
                                'stockPieceCount': 30.15,
                                'stockPackageCount': 82,
                                'availablePackageCount': 82,
                                'availablePieceCount': 30.15,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 200.00727,
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '26',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3817896943949676554',
                                'goodsId': 'ffffffff0000000034e033995ad3c000',
                                'parentGoodsId': 'ffffffff0000000034fbe50add2b8000',
                                'composeType': 40,
                            },
                            {
                                'composeId': '3818222899756974081',
                                'goodsId': 'ffffffff0000000034e033995ad3c000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7f5d46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 1,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'minExpiryDate': '2029-06-17',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24010',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50275325,
                                'pieceNum': 50,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2029-06-17',
                                'productionDate': '2024-06-18',
                                'inDate': '2024-11-21T08:17:14Z',
                                'supplierId': 'ffffffff0000000034bc465d382b4000',
                                'supplierName': 'feiy供应商',
                                'batchNo': '1234123',
                                'packageCostPrice': 200,
                                'packagePrice': 500,
                                'piecePrice': 10,
                                'pieceCount': 30.15,
                                'packageCount': 82,
                                'dispGoodsCount': '82包30.15g',
                                'stockPieceCount': 30.15,
                                'stockPackageCount': 82,
                                'dispStockGoodsCount': '82包30.15g',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0包',
                                'cutTotalPieceCount': 50,
                                'cutPackageCount': 1,
                                'totalSalePrice': 500,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 3.04,
                        'isPreciousDevice': 0,
                        'cMSpec': '',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 400,
                                'type': 2,
                                'discountWay': 1,
                                'ruleType': 0,
                                'goodsTypeName': '',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -100,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 1,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -100,
                    'totalPromotionFee': -100,
                    'unitAdjustmentFee': 0,
                },
            ],
            'sourceFormType': 8,
            'printFormType': 8,
            'totalPrice': 700,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000035027a37b2c24011',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000035027a37b2c24012',
                    'name': '奥美拉唑肠溶胶囊(立卫克)',
                    'unit': '盒',
                    'count': 2,
                    'unitCount': 2,
                    'doseCount': 1,
                    'totalPrice': 4.24,
                    'discountedPrice': 3.4,
                    'discountedUnitPrice': 1.7,
                    'unitPrice': 2.12,
                    'composeType': 0,
                    'goodsTypeId': 12,
                    'feeComposeType': 0,
                    'feeTypeId': '12',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-1-2',
                    'displaySpec': '20mg*14粒/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': 'XA02BCA211E005020200239',
                    'hisCode': '000037',
                    'socialUnit': '盒',
                    'socialName': '奥美拉唑肠溶胶囊(立卫克)',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 0.34,
                    'inscpScpAmt': 3.4,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 1,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 5,
                        'id': 'ffffffff0000000034e746001bc38000',
                        'goodsId': 'ffffffff0000000034e746001bc38000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3814830456535548000,
                            'status': 30,
                            'gspOrderNo': 'SS20250303000003',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-03-03 14:44:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 2.12,
                        },
                        'name': '立卫克',
                        'displayName': '奥美拉唑肠溶胶囊(立卫克)',
                        'displaySpec': '20mg*14粒/盒',
                        'checkSpec': '14粒//',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 12,
                        'type': 1,
                        'subType': 1,
                        'barCode': '6938230300017',
                        'manufacturer': '悦康药业',
                        'pieceNum': 14,
                        'pieceUnit': '粒',
                        'packageUnit': '盒',
                        'dismounting': 0,
                        'medicineCadn': '奥美拉唑肠溶胶囊',
                        'medicineNmpn': '国药准字H20056577',
                        'medicineDosageNum': 20,
                        'medicineDosageUnit': 'mg',
                        'specType': 0,
                        'position': null,
                        'chainPackagePrice': 2.12,
                        'chainPiecePrice': 0.1515,
                        'piecePrice': 0.1515,
                        'packagePrice': 2.12,
                        'packageCostPrice': 2,
                        'minPackagePrice': 2.12,
                        'maxPackagePrice': 2.12,
                        'totalSalePrice': 4.24,
                        'priceType': 1,
                        'inTaxRat': 10,
                        'outTaxRat': 13,
                        'pieceCount': 0,
                        'packageCount': 204,
                        'dispGoodsCount': '204盒',
                        'stockPieceCount': 0,
                        'stockPackageCount': 204,
                        'dispStockGoodsCount': '204盒',
                        'availablePackageCount': 204,
                        'availablePieceCount': 0,
                        'outPieceCount': 0,
                        'outPackageCount': 204,
                        'dispOutGoodsCount': '204盒',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0盒',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0盒',
                        'lastPackageCostPrice': 2,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000037',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff000000002217b51008582000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-05-15T07:21:36Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 1,
                        'shebaoNationalCode': 'XA02BCA211E005020200239',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'customTypeName': '胶囊剂',
                        'isSell': 1,
                        'remark': '单次＜3盒',
                        'customTypeId': 59421,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '悦康药业集团有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034e746001bc38000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000037',
                            'medicalFeeGrade': 1,
                            'nationalCode': 'XA02BCA211E005020200239',
                            'nationalCodeId': '3796104995187965995',
                            'shebaoPieceNum': 14,
                            'shebaoPieceUnit': '粒',
                            'shebaoPackageUnit': '瓶',
                            'shebaoMedicineNmpn': '国药准字H20056577',
                            'socialName': '奥美拉唑肠溶胶囊',
                            'standardCode': '86900239000027',
                            'matchMode': 1,
                            'limitUnitType': 0,
                            'limitUnit': '瓶',
                        },
                        'recentAvgSell': 0.04,
                        'turnoverDays': 5100,
                        'profitRat': -412.17,
                        'lastStockInId': 50281173,
                        'lastStockInOrderSupplier': '神奇康正医药管理有限公司',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 2,
                                'stockPieceCount': 0,
                                'stockPackageCount': 204,
                                'availablePackageCount': 204,
                                'availablePieceCount': 0,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 10.85785,
                        'shebaoPayMode': 0,
                        'listingPrice': 13.86,
                        'listingPriceNotTransFlag': 1,
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '12',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818222892777652230',
                                'goodsId': 'ffffffff0000000034e746001bc38000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7dbd46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'XA02BCA211E005020200239',
                        'nationalCodeId': '3796104995187965995',
                        'minExpiryDate': '2025-12-31',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'B类',
                        'otcType': 2,
                        'otcTypeName': '甲类非处方',
                        'dosageFormType': 600,
                        'dosageFormTypeName': '胶囊剂',
                        'shelfLife': 24,
                        'baseMedicineType': 1,
                        'baseMedicineTypeName': '国家基药',
                        'mha': '悦康药业集团股份有限公司',
                        'dangerIngredient': 0,
                        'pharmacologicId': 'ad4fc9614b8a4a4f85a8966e7c44d55e',
                        'pharmacologicName': '抗感染药/青霉素类',
                        'keyId': 'ffffffff0000000035027a37b2c24012',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50279284,
                                'pieceNum': 14,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2025-12-31',
                                'productionDate': '2024-10-30',
                                'inDate': '2025-01-10T02:02:35Z',
                                'supplierId': 'ffffffff0000000034bd19cc5831c000',
                                'supplierName': '瑞城供应商',
                                'batchNo': '1',
                                'packageCostPrice': 12,
                                'packagePrice': 2.12,
                                'piecePrice': 0.1515,
                                'pieceCount': 0,
                                'packageCount': 75,
                                'dispGoodsCount': '75盒',
                                'stockPieceCount': 0,
                                'stockPackageCount': 75,
                                'dispStockGoodsCount': '75盒',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0盒',
                                'cutTotalPieceCount': 28,
                                'cutPackageCount': 2,
                                'totalSalePrice': 4.24,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 1,
                        'gspModifyStatus': 30,
                        'isPreciousDevice': 0,
                        'traceableCodeNoInfoList': [
                            {
                                'no': '8400339',
                                'drugIdentificationCode': '8400339',
                                'traceableCodeType': 1,
                                'type': 0,
                            },
                            {
                                'no': '8373950',
                                'drugIdentificationCode': '8373950',
                                'traceableCodeType': 1,
                                'type': 0,
                            },
                            {
                                'no': '8356928',
                                'drugIdentificationCode': '8356928',
                                'traceableCodeType': 1,
                                'type': 0,
                            },
                        ],
                        'specificationMatchStatus': 0,
                        'cMSpec': '',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.8,
                                'type': 2,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -0.84,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 2,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -0.84,
                    'totalPromotionFee': -0.84,
                    'unitAdjustmentFee': 0,
                    'goodsStockInfos': [
                        {
                            'stockId': '140643',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                    'traceableCodeList': [
                        {
                            'no': '98765432101011121314',
                            'traceableCodeNoInfo': {
                                'no': '98765432101011121314',
                                'drugIdentificationCode': '9876543',
                                'serialNumber': '210101112',
                                'traceableCodeType': 1,
                                'type': 0,
                            },
                            'used': 1,
                            'hisPackageCount': 1,
                        },
                    ],
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24013',
                    'name': '华法林钠片(信谊)',
                    'unit': '瓶',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 23,
                    'discountedPrice': 9,
                    'discountedUnitPrice': 9,
                    'unitPrice': 23,
                    'composeType': 0,
                    'goodsTypeId': 12,
                    'feeComposeType': 0,
                    'feeTypeId': '12',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'B-23',
                    'displaySpec': '2.5mg*60片/瓶',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': 'XB01AAH029A017010200818',
                    'hisCode': '000027',
                    'socialUnit': '瓶',
                    'socialName': '华法林钠片(信谊)',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 0.9,
                    'inscpScpAmt': 9,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 1,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 17,
                        'id': 'ffffffff0000000034d5f29d5a2ac000',
                        'goodsId': 'ffffffff0000000034d5f29d5a2ac000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3809996521846571000,
                            'status': 30,
                            'gspOrderNo': 'SS20241119000001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2024-11-19 09:38:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 22,
                        },
                        'name': '信谊',
                        'displayName': '华法林钠片(信谊)',
                        'displaySpec': '2.5mg*60片/瓶',
                        'checkSpec': '60片//',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 12,
                        'type': 1,
                        'subType': 1,
                        'manufacturer': '上药信谊',
                        'pieceNum': 60,
                        'pieceUnit': '片',
                        'packageUnit': '瓶',
                        'dismounting': 1,
                        'medicineCadn': '华法林钠片',
                        'medicineNmpn': '国药准字H31022123',
                        'medicineDosageNum': 2.5,
                        'medicineDosageUnit': 'mg',
                        'specType': 0,
                        'position': 'B-23',
                        'chainPackagePrice': 23,
                        'chainPiecePrice': 0.39,
                        'piecePrice': 0.39,
                        'packagePrice': 23,
                        'packageCostPrice': 8,
                        'minPackagePrice': 23,
                        'maxPackagePrice': 23,
                        'totalSalePrice': 23,
                        'priceType': 1,
                        'inTaxRat': 10,
                        'outTaxRat': 13,
                        'pieceCount': 0,
                        'packageCount': 59,
                        'dispGoodsCount': '59瓶',
                        'stockPieceCount': 0,
                        'stockPackageCount': 59,
                        'dispStockGoodsCount': '59瓶',
                        'availablePackageCount': 59,
                        'availablePieceCount': 0,
                        'outPieceCount': 0,
                        'outPackageCount': 59,
                        'dispOutGoodsCount': '59瓶',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0瓶',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0瓶',
                        'lastPackageCostPrice': 8,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000027',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff000000002217b51008582000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-06-04T09:33:31Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'priceLimit': 18,
                        'medicalFeeGrade': 1,
                        'shebaoNationalCode': 'XB01AAH029A017010200818',
                        'ownExpenseRatio': 0,
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'customTypeName': '片剂',
                        'isSell': 1,
                        'customTypeId': 60150,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '上海上药信谊药厂有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034d5f29d5a2ac000',
                            'goodsType': 1,
                            'isDummy': 0,
                            'medicineNum': '000027',
                            'priceLimit': 18,
                            'medicalFeeGrade': 1,
                            'nationalCode': 'XB01AAH029A017010200818',
                            'nationalCodeId': '15107000018886',
                            'shebaoPieceNum': 60,
                            'shebaoPieceUnit': '片',
                            'shebaoPackageUnit': '瓶',
                            'shebaoMedicineNmpn': '国药准字H31022123',
                            'socialName': '华法林钠片',
                            'standardCode': '86900818002152',
                            'limitUnitType': 0,
                            'limitUnit': '瓶',
                        },
                        'recentAvgSell': 0.17,
                        'turnoverDays': 348,
                        'profitRat': 66.25,
                        'lastStockInId': 50242687,
                        'lastStockInOrderSupplier': '天江制药',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 8,
                                'stockPieceCount': 0,
                                'stockPackageCount': 59,
                                'availablePackageCount': 59,
                                'availablePieceCount': 0,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 7.76272,
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '12',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818178013053632512',
                                'goodsId': 'ffffffff0000000034d5f29d5a2ac000',
                                'parentGoodsId': 'ffffffff0000000034fcdebffd448000',
                                'composeType': 40,
                            },
                            {
                                'composeId': '3818222899756974084',
                                'goodsId': 'ffffffff0000000034d5f29d5a2ac000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7f5d46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'XB01AAH029A017010200818',
                        'nationalCodeId': '15107000018886',
                        'minExpiryDate': '2026-12-30',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'B类',
                        'otcType': 1,
                        'otcTypeName': '处方药',
                        'dosageFormType': 100,
                        'dosageFormTypeName': '片剂',
                        'shelfLife': 24,
                        'mha': '上海上药信谊药厂有限公司',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24013',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50242521,
                                'pieceNum': 60,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2026-12-30',
                                'productionDate': '2024-05-31',
                                'inDate': '2024-09-20T02:55:11Z',
                                'supplierId': 'ffffffff0000000034bd1dd55833c000',
                                'supplierName': '天江制药',
                                'batchNo': '1',
                                'packageCostPrice': 8,
                                'packagePrice': 23,
                                'piecePrice': 0.39,
                                'pieceCount': 0,
                                'packageCount': 57,
                                'dispGoodsCount': '57瓶',
                                'stockPieceCount': 0,
                                'stockPackageCount': 57,
                                'dispStockGoodsCount': '57瓶',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0瓶',
                                'cutTotalPieceCount': 60,
                                'cutPackageCount': 1,
                                'totalSalePrice': 23,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 5,
                        'gspModifyStatus': 30,
                        'isPreciousDevice': 0,
                        'goodsTagList': [
                            {
                                'tagId': 3809825206785032000,
                                'name': '复购高',
                                'sort': 0,
                            },
                        ],
                        'specificationMatchStatus': 0,
                        'cMSpec': '',
                    },
                    'singlePromotions': [
                        {
                            'parentId': null,
                            'id': '3811402595485040640',
                            'name': '普通会员',
                            'hitRuleDetail': {
                                'discount': 9,
                                'type': 2,
                                'discountWay': 1,
                                'ruleType': 0,
                                'goodsTypeName': '',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -14,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 1,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 0,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -14,
                    'totalPromotionFee': -14,
                    'unitAdjustmentFee': 0,
                    'goodsStockInfos': [
                        {
                            'stockId': '140643',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                    'traceableCodeList': [
                        {
                            'no': '98765432101011121314',
                            'traceableCodeNoInfo': {
                                'no': '98765432101011121314',
                                'drugIdentificationCode': '9876543',
                                'serialNumber': '210101112',
                                'traceableCodeType': 1,
                                'type': 0,
                            },
                            'used': 1,
                            'hisPackageCount': 1,
                        },
                    ],
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24014',
                    'name': '氯雷他定片(顺他欣/石药)',
                    'unit': '盒',
                    'count': 3,
                    'unitCount': 3,
                    'doseCount': 1,
                    'totalPrice': 45,
                    'discountedPrice': 39.6,
                    'discountedUnitPrice': 13.2,
                    'unitPrice': 15,
                    'composeType': 0,
                    'goodsTypeId': 12,
                    'feeComposeType': 0,
                    'feeTypeId': '12',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-1-3',
                    'displaySpec': '10mg*12片/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': 'XB01AAH029A017010200819',
                    'hisCode': '000041',
                    'socialUnit': '盒',
                    'socialName': '氯雷他定片(顺他欣/石药)',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': 39.6,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 1,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 16,
                        'id': 'ffffffff0000000034edb59d5c14c000',
                        'goodsId': 'ffffffff0000000034edb59d5c14c000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3814829965835534300,
                            'status': 30,
                            'gspOrderNo': 'SS20250303000001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-03-03 14:28:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 5.8,
                        },
                        'name': '顺他欣/石药',
                        'displayName': '氯雷他定片(顺他欣/石药)',
                        'displaySpec': '10mg*12片/盒',
                        'checkSpec': '12片//',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 12,
                        'type': 1,
                        'subType': 1,
                        'barCode': '6921780130050',
                        'manufacturer': '山东天顺',
                        'pieceNum': 12,
                        'pieceUnit': '片',
                        'packageUnit': '盒',
                        'dismounting': 0,
                        'medicineCadn': '氯雷他定片',
                        'medicineNmpn': '国药准字H20051688',
                        'medicineDosageNum': 10,
                        'medicineDosageUnit': 'mg',
                        'specType': 0,
                        'position': null,
                        'chainPackagePrice': 15,
                        'chainPiecePrice': 1.25,
                        'piecePrice': 1.25,
                        'packagePrice': 15,
                        'packageCostPrice': 2,
                        'minPackagePrice': 15,
                        'maxPackagePrice': 15,
                        'totalSalePrice': 45,
                        'priceType': 1,
                        'inTaxRat': 10,
                        'outTaxRat': 13,
                        'pieceCount': 0,
                        'packageCount': 219,
                        'dispGoodsCount': '219盒',
                        'stockPieceCount': 0,
                        'stockPackageCount': 219,
                        'dispStockGoodsCount': '219盒',
                        'availablePackageCount': 219,
                        'availablePieceCount': 0,
                        'outPieceCount': 0,
                        'outPackageCount': 219,
                        'dispOutGoodsCount': '219盒',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0盒',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0盒',
                        'lastPackageCostPrice': 2,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000041',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000226dfd500863a000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-05-14T07:25:20Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'customTypeName': '片剂',
                        'isSell': 1,
                        'customTypeId': 60150,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '山东天顺药业股份有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034edb59d5c14c000',
                            'goodsType': 1,
                            'isDummy': 0,
                            'medicineNum': '000041',
                            'medicalFeeGrade': 0,
                        },
                        'profitRat': 86.67,
                        'lastStockInId': 50284874,
                        'lastStockInOrderSupplier': '瑞城供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 2,
                                'stockPieceCount': 0,
                                'stockPackageCount': 219,
                                'availablePackageCount': 219,
                                'availablePieceCount': 0,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 2,
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '12',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818171500272582657',
                                'goodsId': 'ffffffff0000000034edb59d5c14c000',
                                'parentGoodsId': 'ffffffff0000000034fcdebffd448000',
                                'composeType': 40,
                            },
                            {
                                'composeId': '3818222885261459466',
                                'goodsId': 'ffffffff0000000034edb59d5c14c000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7bfd46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'minExpiryDate': '2027-12-31',
                        'expiredWarnMonths': 1,
                        'otcType': 2,
                        'otcTypeName': '甲类非处方',
                        'dosageFormType': 100,
                        'dosageFormTypeName': '片剂',
                        'shelfLife': 36,
                        'baseMedicineType': 1,
                        'baseMedicineTypeName': '国家基药',
                        'mha': '山东天顺药业股份有限公司',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24014',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50283878,
                                'pieceNum': 12,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2027-12-31',
                                'productionDate': '2025-05-31',
                                'inDate': '2025-02-25T07:56:29Z',
                                'supplierId': 'ffffffff0000000034bd19cc5831c000',
                                'supplierName': '瑞城供应商',
                                'batchNo': '2',
                                'packageCostPrice': 2,
                                'packagePrice': 15,
                                'piecePrice': 1.25,
                                'pieceCount': 0,
                                'packageCount': 219,
                                'dispGoodsCount': '219盒',
                                'stockPieceCount': 0,
                                'stockPackageCount': 219,
                                'dispStockGoodsCount': '219盒',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0盒',
                                'cutTotalPieceCount': 36,
                                'cutPackageCount': 3,
                                'totalSalePrice': 45,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 0,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4000,
                                'parentId': 4,
                                'displayName': '中药饮片/含配方',
                                'name': '含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.88,
                                'type': 2,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -5.4,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 3,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -5.4,
                    'totalPromotionFee': -5.4,
                    'unitAdjustmentFee': 0,
                    'goodsStockInfos': [
                        {
                            'stockId': '140643',
                            'batchNo': '20210701',
                            'expiryDate': '2022-07-01',
                            'manufacturer': '四川中庸药业有限公司',
                            'manufacturerFull': '四川中庸药业有限公司',
                            'supplierName': '盘点入库',
                        },
                    ],
                    'traceableCodeList': [
                        {
                            'no': '98765432101011121314',
                            'traceableCodeNoInfo': {
                                'no': '98765432101011121314',
                                'drugIdentificationCode': '9876543',
                                'serialNumber': '210101112',
                                'traceableCodeType': 1,
                                'type': 0,
                            },
                            'used': 1,
                            'hisPackageCount': 1,
                        },
                    ],
                },
            ],
            'sourceFormType': 4,
            'printFormType': 4,
            'totalPrice': 72.24,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000035027a37b2c24001',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000035027a37b2c24002',
                    'name': '白术',
                    'unit': 'g',
                    'count': 60,
                    'unitCount': 15,
                    'doseCount': 4,
                    'totalPrice': 18,
                    'discountedPrice': 16.2,
                    'discountedUnitPrice': 0.27,
                    'unitPrice': 0.3,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-2-3',
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T001200108',
                    'hisCode': '000115',
                    'socialUnit': 'g',
                    'socialName': '白术',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 1.62,
                    'inscpScpAmt': 16.2,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 1,
                        'id': 'ffffffff00000000350271a67eb3c000',
                        'goodsId': 'ffffffff00000000350271a67eb3c000',
                        'status': 1,
                        'gspStatus': 10,
                        'gsp': {
                            'gspInstId': 3819740393355231000,
                            'gspOrderNo': 'SS2025061700001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-06-17 11:08:35',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                        },
                        'name': null,
                        'displayName': '白术',
                        'displaySpec': '饮片',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '白术',
                        'materialSpec': '中药饮片',
                        'position': 'Z-2-3',
                        'chainPackagePrice': 0.3,
                        'chainPiecePrice': 0.3,
                        'piecePrice': 0.3,
                        'packagePrice': 0.3,
                        'packageCostPrice': 0.06,
                        'totalSalePrice': 18,
                        'priceType': 1,
                        'inTaxRat': 9,
                        'outTaxRat': 9,
                        'pieceCount': 890,
                        'packageCount': 0,
                        'dispGoodsCount': '890g',
                        'stockPieceCount': 890,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '890g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 890,
                        'outPieceCount': 890,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '890g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0g',
                        'lastPackageCostPrice': 0.06,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000115',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ad8c53515472485fbe1e7cccc2507b0e',
                        'lastModifiedDate': '2025-06-17T07:52:53Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T001200108',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff00000000350271a67eb3c000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000115',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T001200108',
                            'nationalCodeId': '3795757555955187858',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '5～10g',
                            'socialName': '炒王不留行',
                            'matchMode': 2,
                        },
                        'recentAvgSell': 0,
                        'profitRat': 80,
                        'lastStockInId': 50287980,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.06,
                                'stockPieceCount': 890,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 890,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 0.06,
                        'shebaoPayMode': 0,
                        'restriction': '5～10g',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T001200108',
                        'nationalCodeId': '3795757555955187858',
                        'expiredWarnMonths': 1,
                        'otcType': 1,
                        'otcTypeName': '处方药',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24002',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50286632,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'productionDate': '2025-06-09',
                                'inDate': '2025-06-17T03:09:44Z',
                                'supplierId': 'ffffffff0000000034bc465d382b4000',
                                'supplierName': 'feiy供应商',
                                'batchNo': '56456',
                                'packageCostPrice': 0.06,
                                'packagePrice': 0.3,
                                'piecePrice': 0.3,
                                'pieceCount': 890,
                                'packageCount': 0,
                                'dispGoodsCount': '890g',
                                'stockPieceCount': 890,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '890g',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0g',
                                'cutTotalPieceCount': 60,
                                'cutPieceCount': 60,
                                'totalSalePrice': 18,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 0,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4000,
                                'parentId': 4,
                                'displayName': '中药饮片/含配方',
                                'name': '含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -1.8,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 60,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -1.8,
                    'totalPromotionFee': -1.8,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24003',
                    'name': '白扁豆',
                    'unit': 'g',
                    'count': 24,
                    'unitCount': 6,
                    'doseCount': 4,
                    'totalPrice': 9.6,
                    'discountedPrice': 8.64,
                    'discountedUnitPrice': 0.36,
                    'unitPrice': 0.4,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-2-2',
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T000900092',
                    'hisCode': '000114',
                    'socialUnit': 'g',
                    'socialName': '白扁豆',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 0.86,
                    'inscpScpAmt': 8.64,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 2,
                        'id': 'ffffffff00000000350271a27eb3c000',
                        'goodsId': 'ffffffff00000000350271a27eb3c000',
                        'status': 1,
                        'gspStatus': 10,
                        'gsp': {
                            'gspInstId': 3819740376175362000,
                            'gspOrderNo': 'SS2025061700001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-06-17 11:08:03',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                        },
                        'name': null,
                        'displayName': '白扁豆',
                        'displaySpec': '饮片',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '白扁豆',
                        'materialSpec': '中药饮片',
                        'position': 'Z-2-2',
                        'chainPackagePrice': 0.4,
                        'chainPiecePrice': 0.4,
                        'piecePrice': 0.4,
                        'packagePrice': 0.4,
                        'packageCostPrice': 0.08,
                        'totalSalePrice': 9.6,
                        'priceType': 1,
                        'inTaxRat': 9,
                        'outTaxRat': 9,
                        'pieceCount': 926,
                        'packageCount': 0,
                        'dispGoodsCount': '926g',
                        'stockPieceCount': 926,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '926g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 926,
                        'outPieceCount': 926,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '926g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0g',
                        'lastPackageCostPrice': 0.08,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000114',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ad8c53515472485fbe1e7cccc2507b0e',
                        'lastModifiedDate': '2025-06-17T07:53:27Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T000900092',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff00000000350271a27eb3c000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000114',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T000900092',
                            'nationalCodeId': '3795757555955187745',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '3～10g',
                            'socialName': '炒鸡内金',
                            'matchMode': 2,
                        },
                        'recentAvgSell': 0,
                        'profitRat': 80,
                        'lastStockInId': 50287979,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.08,
                                'stockPieceCount': 926,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 926,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 0.08,
                        'shebaoPayMode': 0,
                        'restriction': '3～10g',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T000900092',
                        'nationalCodeId': '3795757555955187745',
                        'expiredWarnMonths': 1,
                        'otcType': 1,
                        'otcTypeName': '处方药',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24003',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50286631,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'productionDate': '2025-03-09',
                                'inDate': '2025-06-17T03:09:44Z',
                                'supplierId': 'ffffffff0000000034bc465d382b4000',
                                'supplierName': 'feiy供应商',
                                'batchNo': '34563',
                                'packageCostPrice': 0.08,
                                'packagePrice': 0.4,
                                'piecePrice': 0.4,
                                'pieceCount': 926,
                                'packageCount': 0,
                                'dispGoodsCount': '926g',
                                'stockPieceCount': 926,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '926g',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0g',
                                'cutTotalPieceCount': 24,
                                'cutPieceCount': 24,
                                'totalSalePrice': 9.6,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 0,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4000,
                                'parentId': 4,
                                'displayName': '中药饮片/含配方',
                                'name': '含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -0.96,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 24,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -0.96,
                    'totalPromotionFee': -0.96,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24004',
                    'name': '干姜',
                    'unit': 'g',
                    'count': 36,
                    'unitCount': 9,
                    'doseCount': 4,
                    'totalPrice': 36,
                    'discountedPrice': 32.4,
                    'discountedUnitPrice': 0.9,
                    'unitPrice': 1,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-01-02',
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T000700278',
                    'hisCode': '000113',
                    'socialUnit': 'g',
                    'socialName': '干姜',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 3.24,
                    'inscpScpAmt': 32.4,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 1,
                        'id': 'ffffffff000000003501c9553eb04000',
                        'goodsId': 'ffffffff000000003501c9553eb04000',
                        'status': 1,
                        'gspStatus': 10,
                        'gsp': {
                            'gspInstId': 3819555326972543000,
                            'gspOrderNo': 'SS2025061300001',
                            'applyUserId': 'ffffffff0000000034bd15564c7ac000',
                            'applyTime': '2025-06-13 11:23:21',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                        },
                        'name': null,
                        'displayName': '干姜',
                        'displaySpec': '饮片',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '干姜',
                        'materialSpec': '中药饮片',
                        'position': 'Z-01-02',
                        'chainPackagePrice': 1,
                        'chainPiecePrice': 1,
                        'piecePrice': 1,
                        'packagePrice': 1,
                        'packageCostPrice': 0.2,
                        'minPackagePrice': 1,
                        'maxPackagePrice': 1,
                        'totalSalePrice': 36,
                        'priceType': 1,
                        'inTaxRat': 9,
                        'outTaxRat': 9,
                        'pieceCount': 1913,
                        'packageCount': 0,
                        'dispGoodsCount': '1913g',
                        'stockPieceCount': 1913,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '1913g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 1913,
                        'outPieceCount': 1913,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '1913g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0g',
                        'lastPackageCostPrice': 0.2,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000113',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff0000000034bd15564c7ac000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-06-17T03:04:33Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T000700278',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff000000003501c9553eb04000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000113',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T000700278',
                            'nationalCodeId': '3795757554344576314',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '3～10g；单独使用时不予支付',
                            'socialName': '干姜',
                            'matchMode': 2,
                        },
                        'recentAvgSell': 0.04,
                        'turnoverDays': 47825,
                        'profitRat': 84.78,
                        'lastStockInId': 50287945,
                        'lastStockInOrderSupplier': '盘点入库',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.2,
                                'stockPieceCount': 1913,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 1913,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 0.15228,
                        'shebaoPayMode': 0,
                        'restriction': '3～10g；单独使用时不予支付',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T000700278',
                        'nationalCodeId': '3795757554344576314',
                        'expiredWarnMonths': 1,
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24004',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50286604,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'inDate': '2025-06-13T07:09:57Z',
                                'supplierId': 'ffffffff0000000034baf280b80dc000',
                                'supplierName': '神奇康正医药管理有限公司',
                                'batchNo': '001',
                                'packageCostPrice': 0.1,
                                'packagePrice': 1,
                                'piecePrice': 1,
                                'pieceCount': 913,
                                'packageCount': 0,
                                'dispGoodsCount': '913g',
                                'stockPieceCount': 913,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '913g',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0g',
                                'cutTotalPieceCount': 36,
                                'cutPieceCount': 36,
                                'totalSalePrice': 36,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 1,
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -3.6,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 36,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -3.6,
                    'totalPromotionFee': -3.6,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24005',
                    'name': '山楂',
                    'unit': 'g',
                    'count': 16,
                    'unitCount': 4,
                    'doseCount': 4,
                    'totalPrice': 32,
                    'discountedPrice': 28.8,
                    'discountedUnitPrice': 1.8,
                    'unitPrice': 2,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-1-3',
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T330902042',
                    'hisCode': '000081',
                    'socialUnit': 'g',
                    'socialName': '山楂',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 2.88,
                    'inscpScpAmt': 28.8,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 9,
                        'id': 'ffffffff0000000034fc87b0bd358000',
                        'goodsId': 'ffffffff0000000034fc87b0bd358000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3818075776774144000,
                            'status': 30,
                            'gspOrderNo': 'SS20250512000001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-05-12 13:52:05',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                        },
                        'name': null,
                        'displayName': '山楂',
                        'displaySpec': '饮片',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '山楂',
                        'materialSpec': '中药饮片',
                        'position': 'Z-1-3',
                        'chainPackagePrice': 2,
                        'chainPiecePrice': 2,
                        'piecePrice': 2,
                        'packagePrice': 2,
                        'packageCostPrice': 0.3,
                        'minPackagePrice': 2,
                        'maxPackagePrice': 2,
                        'totalSalePrice': 18,
                        'priceType': 1,
                        'inTaxRat': 1,
                        'outTaxRat': 3,
                        'pieceCount': 9,
                        'packageCount': 0,
                        'dispGoodsCount': '9g',
                        'stockPieceCount': 9,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '9g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 9,
                        'outPieceCount': 9,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '9g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0g',
                        'lastPackageCostPrice': 0.3,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000081',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-06-17T03:04:46Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T330902042',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034fc87b0bd358000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000081',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T330902042',
                            'nationalCodeId': '3795757561860768793',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '9～12g',
                            'socialName': '山楂',
                            'matchMode': 2,
                        },
                        'profitRat': 85,
                        'lastStockInId': 50286949,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.3,
                                'stockPieceCount': 9,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 9,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 0,
                        'dispenseAveragePackageCostPrice': 0.3,
                        'shebaoPayMode': 0,
                        'restriction': '9～12g',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818222870229073921',
                                'goodsId': 'ffffffff0000000034fc87b0bd358000',
                                'parentGoodsId': 'ffffffff0000000034fd0d787d46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T330902042',
                        'nationalCodeId': '3795757561860768793',
                        'minExpiryDate': '2027-07-18',
                        'expiredWarnMonths': 1,
                        'otcType': 1,
                        'otcTypeName': '处方药',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24005',
                        'shortagePieceCount': 7,
                        'shortagePackageCount': 0,
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285794,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2027-07-18',
                                'inDate': '2025-05-12T07:11:20Z',
                                'supplierId': 'ffffffff0000000034bc465d382b4000',
                                'supplierName': 'feiy供应商',
                                'batchNo': '234523',
                                'packageCostPrice': 0.3,
                                'packagePrice': 2,
                                'piecePrice': 2,
                                'pieceCount': 9,
                                'packageCount': 0,
                                'dispGoodsCount': '9g',
                                'stockPieceCount': 9,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '9g',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0g',
                                'cutTotalPieceCount': 9,
                                'cutPieceCount': 9,
                                'totalSalePrice': 18,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 0,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4000,
                                'parentId': 4,
                                'displayName': '中药饮片/含配方',
                                'name': '含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -3.2,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 16,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -3.2,
                    'totalPromotionFee': -3.2,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24006',
                    'name': '桑椹',
                    'unit': 'g',
                    'count': 48,
                    'unitCount': 12,
                    'doseCount': 4,
                    'totalPrice': 12.42,
                    'discountedPrice': 11.18,
                    'discountedUnitPrice': 0.2329,
                    'unitPrice': 0.2588,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-1-7',
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T001700654',
                    'hisCode': '000035',
                    'socialUnit': 'g',
                    'socialName': '桑椹',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 1.12,
                    'inscpScpAmt': 11.18,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 3,
                        'id': 'ffffffff0000000034e3ca7b5b250000',
                        'goodsId': 'ffffffff0000000034e3ca7b5b250000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3814830461367386000,
                            'status': 30,
                            'gspOrderNo': 'SS20250303000005',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-03-03 14:44:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 1.5678,
                        },
                        'name': null,
                        'displayName': '桑椹',
                        'displaySpec': '饮片',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'manufacturer': '四川省泓圃药业有限公司',
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '桑椹',
                        'materialSpec': '中药饮片',
                        'position': 'Z-1-7',
                        'chainPackagePrice': 0.2586,
                        'chainPiecePrice': 0.2586,
                        'piecePrice': 0.2586,
                        'packagePrice': 0.2586,
                        'packageCostPrice': 0.1255,
                        'minPackagePrice': 0.2586,
                        'maxPackagePrice': 0.2586,
                        'totalSalePrice': 12.42,
                        'priceType': 1,
                        'inTaxRat': 9,
                        'outTaxRat': 9,
                        'pieceCount': 755,
                        'packageCount': 0,
                        'dispGoodsCount': '755g',
                        'stockPieceCount': 707,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '707g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 755,
                        'outPieceCount': 707,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '707g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 48,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '48g',
                        'lastPackageCostPrice': 0.1255,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000035',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000249ce94008836000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-06-17T03:06:34Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T001700654',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '四川省泓圃药业有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034e3ca7b5b250000',
                            'goodsType': 1,
                            'isDummy': 0,
                            'medicineNum': '000035',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T001700654',
                            'nationalCodeId': '25107000000950',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '9～15g；单独使用时不予支付',
                            'socialName': '桑椹',
                        },
                        'recentAvgSell': 6.77,
                        'turnoverDays': 105,
                        'profitRat': -2.31,
                        'lastStockInId': 50286380,
                        'lastStockInOrderSupplier': '供应商一',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.1255,
                                'stockPieceCount': 707,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 755,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 0.26456,
                        'restriction': '9～15g；单独使用时不予支付',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818222885261459462',
                                'goodsId': 'ffffffff0000000034e3ca7b5b250000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7bfd46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T001700654',
                        'nationalCodeId': '25107000000950',
                        'minExpiryDate': '2025-12-31',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24006',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285234,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2099-04-12',
                                'productionDate': '2024-04-11',
                                'inDate': '2025-04-11T08:53:53Z',
                                'supplierId': 'ffffffff0000000034b434b0b786c000',
                                'supplierName': '供应商一',
                                'batchNo': '243542',
                                'packageCostPrice': 0.1255,
                                'packagePrice': 0.2586,
                                'piecePrice': 0.2586,
                                'pieceCount': 731,
                                'packageCount': 0,
                                'dispGoodsCount': '731g',
                                'stockPieceCount': 697,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '697g',
                                'lockingPieceCount': 34,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '34g',
                                'cutTotalPieceCount': 48,
                                'cutPieceCount': 48,
                                'totalSalePrice': 12.42,
                                'status': 0,
                            },
                        ],
                        'waitingEffectUpdatePriceItemId': 3815208860810920000,
                        'lastMonthSellCount': 203,
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -1.24,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 48,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -1.24,
                    'totalPromotionFee': -1.24,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24007',
                    'name': '马齿苋',
                    'unit': 'g',
                    'count': 24,
                    'unitCount': 6,
                    'doseCount': 4,
                    'totalPrice': 15.6,
                    'discountedPrice': 14.04,
                    'discountedUnitPrice': 0.585,
                    'unitPrice': 0.65,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-1-8',
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T000200531',
                    'hisCode': '000025',
                    'socialUnit': 'g',
                    'socialName': '马齿苋',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 1.4,
                    'inscpScpAmt': 14.04,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 15,
                        'id': 'ffffffff0000000034d0ac3a5984c000',
                        'goodsId': 'ffffffff0000000034d0ac3a5984c000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3814830454388064000,
                            'status': 30,
                            'gspOrderNo': 'SS20250303000002',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-03-03 14:44:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 0.5,
                        },
                        'name': null,
                        'displayName': '马齿苋',
                        'displaySpec': '饮片',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'manufacturer': '四川固康中药饮片有限责任公司',
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '马齿苋',
                        'materialSpec': '中药饮片',
                        'position': 'Z-1-8',
                        'chainPackagePrice': 0.65,
                        'chainPiecePrice': 0.65,
                        'piecePrice': 0.65,
                        'packagePrice': 0.65,
                        'packageCostPrice': 0.165,
                        'minPackagePrice': 0.65,
                        'maxPackagePrice': 0.65,
                        'totalSalePrice': 15.6,
                        'priceType': 1,
                        'inTaxRat': 9,
                        'outTaxRat': 9,
                        'pieceCount': 759,
                        'packageCount': 0,
                        'dispGoodsCount': '759g',
                        'stockPieceCount': 759,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '759g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 759,
                        'outPieceCount': 759,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '759g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0g',
                        'lastPackageCostPrice': 0.165,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000025',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-06-17T03:06:42Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T000200531',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '四川固康中药饮片有限责任公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034d0ac3a5984c000',
                            'goodsType': 1,
                            'isDummy': 0,
                            'medicineNum': '000025',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T000200531',
                            'nationalCodeId': '3795757554344576008',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '9～15g。外用适量捣敷患处',
                            'socialName': '马齿苋',
                        },
                        'recentAvgSell': 6.67,
                        'turnoverDays': 114,
                        'profitRat': 67.07,
                        'lastStockInId': 50286849,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.165,
                                'stockPieceCount': 759,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 759,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 0.21411,
                        'restriction': '9～15g。外用适量捣敷患处',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818222885261459464',
                                'goodsId': 'ffffffff0000000034d0ac3a5984c000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7bfd46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T000200531',
                        'nationalCodeId': '3795757554344576008',
                        'minExpiryDate': '2027-08-17',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'otcType': 1,
                        'otcTypeName': '处方药',
                        'shelfLife': 60,
                        'maintainType': 4,
                        'maintainTypeName': '重点养护',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24007',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285239,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2099-04-12',
                                'productionDate': '2024-04-11',
                                'inDate': '2025-04-11T08:53:53Z',
                                'supplierId': 'ffffffff0000000034b434b0b786c000',
                                'supplierName': '供应商一',
                                'batchNo': '243542',
                                'packageCostPrice': 0.2145,
                                'packagePrice': 0.65,
                                'piecePrice': 0.65,
                                'pieceCount': 753,
                                'packageCount': 0,
                                'dispGoodsCount': '753g',
                                'stockPieceCount': 753,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '753g',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0g',
                                'cutTotalPieceCount': 24,
                                'cutPieceCount': 24,
                                'totalSalePrice': 15.6,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 200,
                        'storage': '常温',
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4001,
                                'parentId': 4,
                                'displayName': '中药饮片/不含配方',
                                'name': '不含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -1.56,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 24,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -1.56,
                    'totalPromotionFee': -1.56,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24008',
                    'name': '丹参',
                    'unit': 'g',
                    'count': 60,
                    'unitCount': 15,
                    'doseCount': 4,
                    'totalPrice': 100.8,
                    'discountedPrice': 90.72,
                    'discountedUnitPrice': 1.512,
                    'unitPrice': 1.68,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-1-9',
                    'displaySpec': '饮片',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T000100057',
                    'hisCode': '000023',
                    'socialUnit': 'g',
                    'socialName': '丹参',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 9.07,
                    'inscpScpAmt': 90.72,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 6,
                        'id': 'ffffffff0000000034c94bd1b91bc000',
                        'goodsId': 'ffffffff0000000034c94bd1b91bc000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3815155541536440300,
                            'status': 30,
                            'gspOrderNo': 'SS20250310000002',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-03-10 14:55:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                        },
                        'name': null,
                        'displayName': '丹参',
                        'displaySpec': '饮片',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'manufacturer': '通化和平',
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '丹参',
                        'materialSpec': '中药饮片',
                        'position': 'Z-1-9',
                        'chainPackagePrice': 1.68,
                        'chainPiecePrice': 1.68,
                        'piecePrice': 1.68,
                        'packagePrice': 1.68,
                        'packageCostPrice': 0.1365,
                        'minPackagePrice': 1.68,
                        'maxPackagePrice': 1.68,
                        'totalSalePrice': 100.8,
                        'priceType': 1,
                        'inTaxRat': 9,
                        'outTaxRat': 9,
                        'pieceCount': 547,
                        'packageCount': 0,
                        'dispGoodsCount': '547g',
                        'stockPieceCount': 545,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '545g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 547,
                        'outPieceCount': 545,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '545g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 2,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '2g',
                        'lastPackageCostPrice': 0.1365,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000023',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-06-17T03:06:57Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T000100057',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'customTypeName': '特等',
                        'isSell': 1,
                        'customTypeId': 59422,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '通化和平药业有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034c94bd1b91bc000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000023',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T000100057',
                            'nationalCodeId': '3795757554344575864',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '3～6g,后下；单独使用时不予支付',
                            'socialName': '薄荷',
                            'matchMode': 1,
                            'cleanMatchCodeFlag': 1,
                        },
                        'recentAvgSell': 7.1,
                        'turnoverDays': 77,
                        'profitRat': 91.88,
                        'lastStockInId': 50286384,
                        'lastStockInOrderSupplier': '供应商一',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.1365,
                                'stockPieceCount': 545,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 547,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 0.1365,
                        'shebaoPayMode': 0,
                        'restriction': '3～6g,后下；单独使用时不予支付',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3817896943949676558',
                                'goodsId': 'ffffffff0000000034c94bd1b91bc000',
                                'parentGoodsId': 'ffffffff0000000034fbe50add2b8000',
                                'composeType': 40,
                            },
                            {
                                'composeId': '3818222899756974085',
                                'goodsId': 'ffffffff0000000034c94bd1b91bc000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7f5d46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T000100057',
                        'nationalCodeId': '3795757554344575864',
                        'minExpiryDate': '2099-04-12',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'otcType': 2,
                        'otcTypeName': '甲类非处方',
                        'baseMedicineType': 1,
                        'baseMedicineTypeName': '国家基药',
                        'maintainType': 2,
                        'maintainTypeName': '普通养护',
                        'mha': '通化和平药业有限公司',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24008',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285238,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2099-04-12',
                                'productionDate': '2024-04-11',
                                'inDate': '2025-04-11T08:53:53Z',
                                'supplierId': 'ffffffff0000000034b434b0b786c000',
                                'supplierName': '供应商一',
                                'batchNo': '243542',
                                'packageCostPrice': 0.1365,
                                'packagePrice': 1.68,
                                'piecePrice': 1.68,
                                'pieceCount': 547,
                                'packageCount': 0,
                                'dispGoodsCount': '547g',
                                'stockPieceCount': 545,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '545g',
                                'lockingPieceCount': 2,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '2g',
                                'cutTotalPieceCount': 60,
                                'cutPieceCount': 60,
                                'totalSalePrice': 100.8,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 213,
                        'storage': '常温，阴凉',
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4001,
                                'parentId': 4,
                                'displayName': '中药饮片/不含配方',
                                'name': '不含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -10.08,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 60,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -10.08,
                    'totalPromotionFee': -10.08,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24009',
                    'name': '神曲',
                    'unit': 'g',
                    'count': 48,
                    'unitCount': 12,
                    'doseCount': 4,
                    'totalPrice': 1.13,
                    'discountedPrice': 1.02,
                    'discountedUnitPrice': 0.0213,
                    'unitPrice': 0.0235,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'B-1-17',
                    'displaySpec': '饮片 净制，一级',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T520901627',
                    'hisCode': '000007',
                    'socialUnit': 'g',
                    'socialName': '神曲',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 0.1,
                    'inscpScpAmt': 1.02,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 5,
                        'id': 'ffffffff0000000034b6caadb7c90000',
                        'goodsId': 'ffffffff0000000034b6caadb7c90000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3798446189239353300,
                            'status': 30,
                            'gspOrderNo': 'SS20240315000001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2024-03-15 09:29:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'AAAAAAA',
                            'referencePrice': 0.3444,
                        },
                        'name': null,
                        'displayName': '神曲',
                        'displaySpec': '饮片 净制，一级',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'manufacturer': '四川',
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '神曲',
                        'materialSpec': '中药饮片',
                        'position': 'B-1-17',
                        'chainPackagePrice': 0.0234,
                        'chainPiecePrice': 0.0234,
                        'piecePrice': 0.0234,
                        'packagePrice': 0.0234,
                        'packageCostPrice': 0.1275,
                        'minPackagePrice': 0.0234,
                        'maxPackagePrice': 0.0234,
                        'totalSalePrice': 1.13,
                        'priceType': 1,
                        'inTaxRat': 9,
                        'outTaxRat': 9,
                        'pieceCount': 1576,
                        'packageCount': 0,
                        'dispGoodsCount': '1576g',
                        'stockPieceCount': 1556,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '1556g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 1576,
                        'outPieceCount': 1556,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '1556g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 20,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '20g',
                        'lastPackageCostPrice': 0.1275,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000007',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-03-27T03:10:52Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '净制，一级',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T520901627',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '四川',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034b6caadb7c90000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000007',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T520901627',
                            'nationalCodeId': '3782518870488268950',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '6～12g。或粉碎后入茶、丸、散等制剂用',
                            'socialName': '六神曲',
                            'matchMode': 2,
                        },
                        'recentAvgSell': 6.7,
                        'turnoverDays': 233,
                        'profitRat': -197.81,
                        'lastStockInId': 50286382,
                        'lastStockInOrderSupplier': '供应商一',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.1275,
                                'stockPieceCount': 1556,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 1576,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 0.06969,
                        'shebaoPayMode': 0,
                        'restriction': '6～12g。或粉碎后入茶、丸、散等制剂用',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818222910494392327',
                                'goodsId': 'ffffffff0000000034b6caadb7c90000',
                                'parentGoodsId': 'ffffffff0000000034fd0d81dd46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T520901627',
                        'nationalCodeId': '3782518870488268950',
                        'minExpiryDate': '2099-04-12',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'dangerIngredient': 0,
                        'pharmacologicId': 'ed1cf3854ea24f8ebf3865cdf4b4b59d',
                        'pharmacologicName': '清热药/清热燥湿药',
                        'keyId': 'ffffffff0000000035027a37b2c24009',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285236,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2099-04-12',
                                'productionDate': '2024-04-11',
                                'inDate': '2025-04-11T08:53:53Z',
                                'supplierId': 'ffffffff0000000034b434b0b786c000',
                                'supplierName': '供应商一',
                                'batchNo': '243542',
                                'packageCostPrice': 0.1275,
                                'packagePrice': 0.0234,
                                'piecePrice': 0.0234,
                                'pieceCount': 591,
                                'packageCount': 0,
                                'dispGoodsCount': '591g',
                                'stockPieceCount': 571,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '571g',
                                'lockingPieceCount': 20,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '20g',
                                'cutTotalPieceCount': 48,
                                'cutPieceCount': 48,
                                'totalSalePrice': 1.13,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 201,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4001,
                                'parentId': 4,
                                'displayName': '中药饮片/不含配方',
                                'name': '不含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -0.11,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 48,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -0.11,
                    'totalPromotionFee': -0.11,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c2400a',
                    'name': '红曲',
                    'unit': 'g',
                    'count': 32,
                    'unitCount': 8,
                    'doseCount': 4,
                    'totalPrice': 50.18,
                    'discountedPrice': 45.17,
                    'discountedUnitPrice': 1.4116,
                    'unitPrice': 1.5681,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-2-1',
                    'displaySpec': '饮片 3g/袋*30袋',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T611201625',
                    'hisCode': '000022',
                    'socialUnit': 'g',
                    'socialName': '红曲',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 4.52,
                    'inscpScpAmt': 45.17,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 10,
                        'id': 'ffffffff0000000034c92499b915c000',
                        'goodsId': 'ffffffff0000000034c92499b915c000',
                        'status': 1,
                        'name': null,
                        'displayName': '红曲',
                        'displaySpec': '饮片 3g/袋*30袋',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'manufacturer': '云南正年轻药业有限公司',
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '红曲',
                        'materialSpec': '中药饮片',
                        'position': 'Z-2-1',
                        'chainPackagePrice': 1.568,
                        'chainPiecePrice': 1.568,
                        'piecePrice': 1.568,
                        'packagePrice': 1.568,
                        'packageCostPrice': 0.1245,
                        'minPackagePrice': 1.568,
                        'maxPackagePrice': 1.568,
                        'totalSalePrice': 50.18,
                        'priceType': 1,
                        'inTaxRat': 9,
                        'outTaxRat': 9,
                        'pieceCount': 336,
                        'packageCount': 0,
                        'dispGoodsCount': '336g',
                        'stockPieceCount': 316,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '316g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 336,
                        'outPieceCount': 316,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '316g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 20,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '20g',
                        'lastPackageCostPrice': 0.1245,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000022',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': '******************************00',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-06-17T03:07:10Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '3g/袋*30袋',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T611201625',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '云南正年轻药业有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034c92499b915c000',
                            'goodsType': 1,
                            'isDummy': 0,
                            'medicineNum': '000022',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T611201625',
                            'nationalCodeId': '25107000009725',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '4.5～9g;外用捣敷',
                            'socialName': '红曲',
                        },
                        'recentAvgSell': 7.04,
                        'turnoverDays': 45,
                        'profitRat': 92.06,
                        'lastStockInId': 50286379,
                        'lastStockInOrderSupplier': '供应商一',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.1245,
                                'stockPieceCount': 316,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 336,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 0.1245,
                        'restriction': '4.5～9g;外用捣敷',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T611201625',
                        'nationalCodeId': '25107000009725',
                        'minExpiryDate': '2099-04-12',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'shelfLife': 60,
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c2400a',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285233,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2099-04-12',
                                'productionDate': '2024-04-11',
                                'inDate': '2025-04-11T08:53:53Z',
                                'supplierId': 'ffffffff0000000034b434b0b786c000',
                                'supplierName': '供应商一',
                                'batchNo': '243542',
                                'packageCostPrice': 0.1245,
                                'packagePrice': 1.568,
                                'piecePrice': 1.568,
                                'pieceCount': 336,
                                'packageCount': 0,
                                'dispGoodsCount': '336g',
                                'stockPieceCount': 302,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '302g',
                                'lockingPieceCount': 34,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '34g',
                                'cutTotalPieceCount': 32,
                                'cutPieceCount': 32,
                                'totalSalePrice': 50.18,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 211,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4000,
                                'parentId': 4,
                                'displayName': '中药饮片/含配方',
                                'name': '含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -5.01,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 32,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -5.01,
                    'totalPromotionFee': -5.01,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c2400b',
                    'name': '三七',
                    'unit': 'g',
                    'count': 40,
                    'unitCount': 10,
                    'doseCount': 4,
                    'totalPrice': 38.12,
                    'discountedPrice': 34.31,
                    'discountedUnitPrice': 0.8578,
                    'unitPrice': 0.953,
                    'composeType': 0,
                    'goodsTypeId': 14,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'Z-01-2',
                    'displaySpec': '饮片 切段',
                    'cmSpec': '中药饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T611102082',
                    'hisCode': '000014',
                    'socialUnit': 'g',
                    'socialName': '三七',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 3.43,
                    'inscpScpAmt': 34.31,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 1,
                        'id': 'ffffffff0000000034bd1dd55833c002',
                        'goodsId': 'ffffffff0000000034bd1dd55833c002',
                        'status': 1,
                        'name': null,
                        'displayName': '三七',
                        'displaySpec': '饮片 切段',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 14,
                        'type': 1,
                        'subType': 2,
                        'barCode': '11100123',
                        'manufacturer': '浙江',
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '三七',
                        'materialSpec': '中药饮片',
                        'position': 'Z-01-2',
                        'chainPackagePrice': 0.953,
                        'chainPiecePrice': 0.953,
                        'piecePrice': 0.953,
                        'packagePrice': 0.953,
                        'packageCostPrice': 0.0125,
                        'minPackagePrice': 0.953,
                        'maxPackagePrice': 0.953,
                        'totalSalePrice': 38.12,
                        'priceType': 1,
                        'inTaxRat': 20,
                        'outTaxRat': 30,
                        'pieceCount': 516,
                        'packageCount': 0,
                        'dispGoodsCount': '516g',
                        'stockPieceCount': 476,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '476g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 516,
                        'outPieceCount': 476,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '476g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 40,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '40g',
                        'lastPackageCostPrice': 0.0125,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000014',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-06-17T02:54:31Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '切段',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T611102082',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'customTypeName': '特等',
                        'isSell': 1,
                        'customTypeId': 59422,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '浙江',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034bd1dd55833c002',
                            'goodsType': 1,
                            'isDummy': 0,
                            'medicineNum': '000014',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T611102082',
                            'nationalCodeId': '3795757569376960688',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '3～9g。用时捣碎',
                            'socialName': '三七',
                        },
                        'recentAvgSell': 8.74,
                        'turnoverDays': 55,
                        'profitRat': 98.69,
                        'lastStockInId': 50286383,
                        'lastStockInOrderSupplier': '供应商一',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 0.0125,
                                'stockPieceCount': 476,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 516,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 0,
                        'dispenseAveragePackageCostPrice': 0.0125,
                        'restriction': '3～9g。用时捣碎',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T611102082',
                        'nationalCodeId': '3795757569376960688',
                        'minExpiryDate': '2099-04-12',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'otcType': 1,
                        'otcTypeName': '处方药',
                        'shelfLife': 36,
                        'baseMedicineType': 1,
                        'baseMedicineTypeName': '国家基药',
                        'maintainType': 2,
                        'maintainTypeName': '普通养护',
                        'storageType': 2,
                        'storageTypeName': '阴凉',
                        'mha': '几点几分',
                        'dangerIngredient': 0,
                        'pharmacologicId': 'a0c2606c34dd4be9a9f3a6fde58a75c8',
                        'pharmacologicName': '解表药/发散风寒药',
                        'keyId': 'ffffffff0000000035027a37b2c2400b',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285237,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2099-04-12',
                                'productionDate': '2024-04-11',
                                'inDate': '2025-04-11T08:53:53Z',
                                'supplierId': 'ffffffff0000000034b434b0b786c000',
                                'supplierName': '供应商一',
                                'batchNo': '243542',
                                'packageCostPrice': 0.0125,
                                'packagePrice': 0.953,
                                'piecePrice': 0.953,
                                'pieceCount': 516,
                                'packageCount': 0,
                                'dispGoodsCount': '516g',
                                'stockPieceCount': 476,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '476g',
                                'lockingPieceCount': 40,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '40g',
                                'cutTotalPieceCount': 40,
                                'cutPieceCount': 40,
                                'totalSalePrice': 38.12,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 262,
                        'isPreciousDevice': 0,
                        'cMSpec': '中药饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3819749679742271488',
                            'id': '3819749679742271490',
                            'name': '造数据的优惠',
                            'hitRuleDetail': {
                                'discount': 0.9,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '中药',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -3.81,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 40,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -3.81,
                    'totalPromotionFee': -3.81,
                    'unitAdjustmentFee': 0,
                },
            ],
            'sourceFormType': 6,
            'printFormType': 6,
            'totalPrice': 313.85,
            'specification': '中药饮片',
            'doseCount': 4,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000035027a8232c2400e',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000035027a37b2c2401a',
                    'name': '燕窝',
                    'unit': '盒',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 100,
                    'discountedPrice': 0,
                    'discountedUnitPrice': 0,
                    'unitPrice': 100,
                    'composeType': 0,
                    'goodsTypeId': 27,
                    'feeComposeType': 0,
                    'feeTypeId': '27',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': 'C-3-4',
                    'displaySpec': '100g/盒',
                    'cmSpec': '',
                    'sourceItemType': 0,
                    'socialCode': null,
                    'hisCode': '000030',
                    'socialUnit': '盒',
                    'socialName': '燕窝',
                    'medicalFeeGrade': null,
                    'ownExpenseRatio': 1,
                    'ownExpenseFee': 0,
                    'inscpScpAmt': 0,
                    'overlmtAmt': 0,
                    'productType': 7,
                    'productSubType': 3,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 4,
                        'id': 'ffffffff0000000034e0338f3ad3c000',
                        'goodsId': 'ffffffff0000000034e0338f3ad3c000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3810101977789202400,
                            'status': 30,
                            'gspOrderNo': 'SS20241121000001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2024-11-21 16:12:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 100,
                        },
                        'name': '燕窝',
                        'displayName': '燕窝',
                        'displaySpec': '100g/盒',
                        'checkSpec': '100g//',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 27,
                        'type': 7,
                        'subType': 3,
                        'manufacturer': '四川省仁德',
                        'pieceNum': 100,
                        'pieceUnit': 'g',
                        'packageUnit': '盒',
                        'dismounting': 1,
                        'medicineCadn': '',
                        'medicineNmpn': 'Z78623488455',
                        'position': 'C-3-4',
                        'chainPackagePrice': 100,
                        'chainPiecePrice': 1,
                        'piecePrice': 1,
                        'packagePrice': 100,
                        'packageCostPrice': 120,
                        'minPackagePrice': 100,
                        'maxPackagePrice': 100,
                        'totalSalePrice': 100,
                        'priceType': 1,
                        'inTaxRat': 0,
                        'outTaxRat': 0,
                        'pieceCount': 94.1,
                        'packageCount': 1074,
                        'dispGoodsCount': '1074盒94.1g',
                        'stockPieceCount': 94.1,
                        'stockPackageCount': 1074,
                        'dispStockGoodsCount': '1074盒94.1g',
                        'availablePackageCount': 1074,
                        'availablePieceCount': 94.1,
                        'outPieceCount': 94.1,
                        'outPackageCount': 1074,
                        'dispOutGoodsCount': '1074盒94.1g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0盒',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0盒',
                        'lastPackageCostPrice': 120,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000030',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-05-14T05:46:49Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'manufacturerFull': '四川省仁德制药有限公司',
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034e0338f3ad3c000',
                            'goodsType': 7,
                            'isDummy': 0,
                            'medicineNum': '000030',
                            'medicalFeeGrade': 0,
                        },
                        'recentAvgSell': 0.17,
                        'turnoverDays': 6335,
                        'profitRat': -16.29,
                        'lastStockInId': 50275532,
                        'lastStockInOrderSupplier': '瑞城供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 120,
                                'stockPieceCount': 94.1,
                                'stockPackageCount': 1074,
                                'availablePackageCount': 1074,
                                'availablePieceCount': 94.1,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 116.28117,
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '27',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818136588630818819',
                                'goodsId': 'ffffffff0000000034e0338f3ad3c000',
                                'parentGoodsId': 'ffffffff0000000034fbe50add2b8000',
                                'composeType': 40,
                            },
                            {
                                'composeId': '3818222892777652234',
                                'goodsId': 'ffffffff0000000034e0338f3ad3c000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7dbd46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 1,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'minExpiryDate': '2026-12-31',
                        'expiredWarnMonths': 1,
                        'profitCategoryType': 3804530788227055600,
                        'profitCategoryTypeName': 'C类',
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c2401a',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50275384,
                                'pieceNum': 100,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2026-12-31',
                                'productionDate': '2024-12-31',
                                'inDate': '2024-11-28T03:46:10Z',
                                'supplierId': 'ffffffff0000000034bd19cc5831c000',
                                'supplierName': '瑞城供应商',
                                'batchNo': '1',
                                'packageCostPrice': 120,
                                'packagePrice': 100,
                                'piecePrice': 1,
                                'pieceCount': 0,
                                'packageCount': 975,
                                'dispGoodsCount': '975盒',
                                'stockPieceCount': 0,
                                'stockPackageCount': 975,
                                'dispStockGoodsCount': '975盒',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0盒',
                                'cutTotalPieceCount': 100,
                                'cutPackageCount': 1,
                                'totalSalePrice': 100,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 5,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 16010,
                                'parentId': 16,
                                'displayName': '食品/保健食品',
                                'name': '保健食品',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'cMSpec': '',
                    },
                    'singlePromotionFee': -100,
                    'totalPromotionFee': -100,
                    'unitAdjustmentFee': 0,
                },
            ],
            'sourceFormType': 10,
            'printFormType': 10,
            'totalPrice': 100,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
        {
            'id': 'ffffffff0000000035027a37b2c24015',
            'chargeFormItems': [
                {
                    'id': 'ffffffff0000000035027a37b2c24017',
                    'name': '党参',
                    'unit': 'g',
                    'count': 40,
                    'unitCount': 40,
                    'doseCount': 1,
                    'totalPrice': 240,
                    'discountedPrice': 120,
                    'discountedUnitPrice': 3,
                    'unitPrice': 6,
                    'composeType': 0,
                    'goodsTypeId': 93,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片',
                    'cmSpec': '非配方饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T501704683',
                    'hisCode': '000068',
                    'socialUnit': 'g',
                    'socialName': '党参',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 12,
                    'inscpScpAmt': 120,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 1,
                        'id': 'ffffffff0000000034fa64067d0fc000',
                        'goodsId': 'ffffffff0000000034fa64067d0fc000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3817473612695568400,
                            'status': 30,
                            'gspOrderNo': 'SS20250429000002',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-04-29 14:18:27',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                        },
                        'name': null,
                        'displayName': '党参',
                        'displaySpec': '饮片',
                        'checkSpec': '1g/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 93,
                        'type': 1,
                        'subType': 2,
                        'pieceNum': 1,
                        'pieceUnit': 'g',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '党参',
                        'materialSpec': '非配方饮片',
                        'position': null,
                        'chainPackagePrice': 6,
                        'chainPiecePrice': 6,
                        'piecePrice': 6,
                        'packagePrice': 6,
                        'packageCostPrice': 1.5,
                        'minPackagePrice': 6,
                        'maxPackagePrice': 6,
                        'totalSalePrice': 168,
                        'priceType': 1,
                        'inTaxRat': 0,
                        'outTaxRat': 0,
                        'pieceCount': 28,
                        'packageCount': 0,
                        'dispGoodsCount': '28g',
                        'stockPieceCount': 28,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '28g',
                        'availablePackageCount': 0,
                        'availablePieceCount': 28,
                        'outPieceCount': 28,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '28g',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0g',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0g',
                        'lastPackageCostPrice': 1.5,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000068',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-05-12T07:05:58Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T501704683',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034fa64067d0fc000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000068',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T501704683',
                            'nationalCodeId': '3795758301668884617',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '9~30g',
                            'socialName': '党参段',
                            'matchMode': 2,
                        },
                        'recentAvgSell': 0.04,
                        'turnoverDays': 700,
                        'profitRat': 75,
                        'lastStockInId': 50286848,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 1.5,
                                'stockPieceCount': 28,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 28,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 1.5,
                        'shebaoPayMode': 0,
                        'restriction': '9~30g',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3817896943949676547',
                                'goodsId': 'ffffffff0000000034fa64067d0fc000',
                                'parentGoodsId': 'ffffffff0000000034fbe50add2b8000',
                                'composeType': 40,
                            },
                            {
                                'composeId': '3818222870229073929',
                                'goodsId': 'ffffffff0000000034fa64067d0fc000',
                                'parentGoodsId': 'ffffffff0000000034fd0d787d46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T501704683',
                        'nationalCodeId': '3795758301668884617',
                        'minExpiryDate': '2027-10-18',
                        'expiredWarnMonths': 1,
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24017',
                        'shortagePieceCount': 12,
                        'shortagePackageCount': 0,
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285703,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2027-10-18',
                                'productionDate': '2025-03-05',
                                'inDate': '2025-05-07T11:41:23Z',
                                'supplierId': 'ffffffff0000000034bc465d382b4000',
                                'supplierName': 'feiy供应商',
                                'batchNo': '123414',
                                'packageCostPrice': 1.5,
                                'packagePrice': 6,
                                'piecePrice': 6,
                                'pieceCount': 28,
                                'packageCount': 0,
                                'dispGoodsCount': '28g',
                                'stockPieceCount': 28,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '28g',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0g',
                                'cutTotalPieceCount': 28,
                                'cutPieceCount': 28,
                                'totalSalePrice': 168,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 1,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4001,
                                'parentId': 4,
                                'displayName': '中药饮片/不含配方',
                                'name': '不含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'specificationMatchStatus': 0,
                        'cMSpec': '非配方饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3809405924781572096',
                            'id': '3809405924781572098',
                            'name': '买赠',
                            'hitRuleDetail': {
                                'discount': 0.5,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '非配方饮片',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -120,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 40,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -120,
                    'totalPromotionFee': -120,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24016',
                    'name': '当归',
                    'unit': '瓶',
                    'count': 1,
                    'unitCount': 1,
                    'doseCount': 1,
                    'totalPrice': 20,
                    'discountedPrice': 10,
                    'discountedUnitPrice': 10,
                    'unitPrice': 20,
                    'composeType': 0,
                    'goodsTypeId': 93,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片',
                    'cmSpec': '非配方饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T001700173',
                    'hisCode': '000080',
                    'socialUnit': '瓶',
                    'socialName': '当归',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 1,
                    'inscpScpAmt': 10,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 5,
                        'id': 'ffffffff0000000034fc02f53d32c000',
                        'goodsId': 'ffffffff0000000034fc02f53d32c000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3817929835932909600,
                            'status': 30,
                            'gspOrderNo': 'SS20250509000001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-05-09 10:21:30',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                        },
                        'name': null,
                        'displayName': '当归',
                        'displaySpec': '饮片',
                        'checkSpec': '1瓶/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 93,
                        'type': 1,
                        'subType': 2,
                        'pieceNum': 1,
                        'pieceUnit': '瓶',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '当归',
                        'materialSpec': '非配方饮片',
                        'position': null,
                        'chainPackagePrice': 20,
                        'chainPiecePrice': 20,
                        'piecePrice': 20,
                        'packagePrice': 20,
                        'packageCostPrice': 4,
                        'minPackagePrice': 20,
                        'maxPackagePrice': 20,
                        'totalSalePrice': 20,
                        'priceType': 1,
                        'inTaxRat': 10,
                        'outTaxRat': 13,
                        'pieceCount': 9,
                        'packageCount': 0,
                        'dispGoodsCount': '9瓶',
                        'stockPieceCount': 9,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '9瓶',
                        'availablePackageCount': 0,
                        'availablePieceCount': 9,
                        'outPieceCount': 9,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '9瓶',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0瓶',
                        'lockingPieceCount': 0,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '0瓶',
                        'lastPackageCostPrice': 4,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000080',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-05-12T11:27:55Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T001700173',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'isSell': 1,
                        'customTypeId': 0,
                        'chainPackageCostPrice': 0,
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034fc02f53d32c000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000080',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T001700173',
                            'nationalCodeId': '3795757555955188140',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '6～12g；单独使用时不予支付',
                            'socialName': '当归',
                            'matchMode': 2,
                        },
                        'profitRat': 80,
                        'lastStockInId': 50286966,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 4,
                                'stockPieceCount': 9,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 9,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 4,
                        'shebaoPayMode': 0,
                        'restriction': '6～12g；单独使用时不予支付',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818222870229073922',
                                'goodsId': 'ffffffff0000000034fc02f53d32c000',
                                'parentGoodsId': 'ffffffff0000000034fd0d787d46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T001700173',
                        'nationalCodeId': '3795757555955188140',
                        'minExpiryDate': '2027-05-15',
                        'expiredWarnMonths': 1,
                        'dangerIngredient': 0,
                        'keyId': 'ffffffff0000000035027a37b2c24016',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285808,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2027-05-15',
                                'productionDate': '2025-03-10',
                                'inDate': '2025-05-12T11:20:52Z',
                                'supplierId': 'ffffffff0000000034bc465d382b4000',
                                'supplierName': 'feiy供应商',
                                'batchNo': '132412',
                                'packageCostPrice': 4,
                                'packagePrice': 20,
                                'piecePrice': 20,
                                'pieceCount': 9,
                                'packageCount': 0,
                                'dispGoodsCount': '9瓶',
                                'stockPieceCount': 9,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '9瓶',
                                'lockingPieceCount': 0,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '0瓶',
                                'cutTotalPieceCount': 1,
                                'cutPieceCount': 1,
                                'totalSalePrice': 20,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 0,
                        'gspModifyStatus': 30,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4001,
                                'parentId': 4,
                                'displayName': '中药饮片/不含配方',
                                'name': '不含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'specificationMatchStatus': 1,
                        'cMSpec': '非配方饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3809405924781572096',
                            'id': '3809405924781572098',
                            'name': '买赠',
                            'hitRuleDetail': {
                                'discount': 0.5,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '非配方饮片',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -10,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 1,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -10,
                    'totalPromotionFee': -10,
                    'unitAdjustmentFee': 0,
                },
                {
                    'id': 'ffffffff0000000035027a37b2c24018',
                    'name': '金银花',
                    'unit': '袋',
                    'count': 10,
                    'unitCount': 10,
                    'doseCount': 1,
                    'totalPrice': 120,
                    'discountedPrice': 60,
                    'discountedUnitPrice': 6,
                    'unitPrice': 12,
                    'composeType': 0,
                    'goodsTypeId': 93,
                    'feeComposeType': 0,
                    'feeTypeId': '13',
                    'feeTypeName': null,
                    'goodsFeeType': 0,
                    'composeChildren': null,
                    'position': null,
                    'displaySpec': '饮片 250g',
                    'cmSpec': '非配方饮片',
                    'sourceItemType': 0,
                    'socialCode': 'T000200433',
                    'hisCode': '000066',
                    'socialUnit': '袋',
                    'socialName': '金银花',
                    'medicalFeeGrade': 2,
                    'ownExpenseRatio': 0.1,
                    'ownExpenseFee': 6,
                    'inscpScpAmt': 60,
                    'overlmtAmt': 0,
                    'productType': 1,
                    'productSubType': 2,
                    'productInfo': {
                        'selfPayProp': {
                            '310': 20,
                            '330': 0,
                            '390': 0,
                        },
                        'goodsVersion': 24,
                        'id': 'ffffffff0000000034fa32357d0b0000',
                        'goodsId': 'ffffffff0000000034fa32357d0b0000',
                        'status': 1,
                        'gspStatus': 30,
                        'gsp': {
                            'gspInstId': 3817418999502045000,
                            'status': 30,
                            'gspOrderNo': 'SS20250428000001',
                            'applyUserId': 'ffffffff00000000168e001004706000',
                            'applyTime': '2025-04-28 10:03:00',
                            'applyOrganId': 'ffffffff0000000034b430a947024002',
                            'applyOrganName': 'A药店',
                            'referencePrice': 15,
                        },
                        'name': null,
                        'displayName': '金银花',
                        'displaySpec': '饮片 250g',
                        'checkSpec': '1袋/',
                        'organId': 'ffffffff0000000034b430a947024000',
                        'typeId': 93,
                        'type': 1,
                        'subType': 2,
                        'pieceNum': 1,
                        'pieceUnit': '袋',
                        'packageUnit': '',
                        'dismounting': 1,
                        'medicineCadn': '金银花',
                        'materialSpec': '非配方饮片',
                        'position': null,
                        'chainPackagePrice': 12,
                        'chainPiecePrice': 12,
                        'piecePrice': 12,
                        'packagePrice': 12,
                        'packageCostPrice': 8,
                        'minPackagePrice': 12,
                        'maxPackagePrice': 12,
                        'totalSalePrice': 120,
                        'priceType': 1,
                        'inTaxRat': 0,
                        'outTaxRat': 0,
                        'pieceCount': 35,
                        'packageCount': 0,
                        'dispGoodsCount': '35袋',
                        'stockPieceCount': 27,
                        'stockPackageCount': 0,
                        'dispStockGoodsCount': '27袋',
                        'availablePackageCount': 0,
                        'availablePieceCount': 35,
                        'outPieceCount': 27,
                        'outPackageCount': 0,
                        'dispOutGoodsCount': '27袋',
                        'prohibitPieceCount': 0,
                        'prohibitPackageCount': 0,
                        'dispProhibitGoodsCount': '0袋',
                        'lockingPieceCount': 8,
                        'lockingPackageCount': 0,
                        'dispLockingGoodsCount': '8袋',
                        'lastPackageCostPrice': 8,
                        'needExecutive': 0,
                        'hospitalNeedExecutive': 0,
                        'shortId': '000066',
                        'composeUseDismounting': 0,
                        'composeSort': 0,
                        'disableComposePrint': 0,
                        'createdUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedUserId': 'ffffffff00000000168e001004706000',
                        'lastModifiedDate': '2025-05-12T08:49:21Z',
                        'combineType': 0,
                        'bizRelevantId': null,
                        'extendSpec': '250g',
                        'medicalFeeGrade': 0,
                        'shebaoNationalCode': 'T000200433',
                        'disable': 0,
                        'chainDisable': 0,
                        'v2DisableStatus': 0,
                        'chainV2DisableStatus': 0,
                        'disableSell': 0,
                        'customTypeName': '代茶饮',
                        'isSell': 1,
                        'customTypeId': 60148,
                        'chainPackageCostPrice': 0,
                        'chainId': 'ffffffff0000000034b430a947024000',
                        'shebao': {
                            'goodsId': 'ffffffff0000000034fa32357d0b0000',
                            'goodsType': 1,
                            'payMode': 0,
                            'isDummy': 0,
                            'medicineNum': '000066',
                            'medicalFeeGrade': 0,
                            'nationalCode': 'T000200433',
                            'nationalCodeId': '3795757554344575988',
                            'shebaoPieceNum': 1,
                            'shebaoPieceUnit': 'g',
                            'shebaoPackageUnit': 'g',
                            'restriction': '6～15g；单独使用时不予支付',
                            'socialName': '金银花',
                        },
                        'profitRat': 33.34,
                        'lastStockInId': 50286699,
                        'lastStockInOrderSupplier': 'feiy供应商',
                        'pharmacyType': 0,
                        'pharmacyNo': 0,
                        'pharmacyName': '本地药房',
                        'pharmacyGoodsStockList': [
                            {
                                'pharmacyName': '本地药房',
                                'pharmacyNo': 0,
                                'lastPackageCostPrice': 8,
                                'stockPieceCount': 27,
                                'stockPackageCount': 0,
                                'availablePackageCount': 0,
                                'availablePieceCount': 35,
                                'esInorder': 1,
                            },
                        ],
                        'defaultInOutTax': 1,
                        'dispenseAveragePackageCostPrice': 8,
                        'shebaoPayMode': 0,
                        'restriction': '6～15g；单独使用时不予支付',
                        'innerFlag': 0,
                        'deviceInnerFlag': 1,
                        'feeComposeType': 0,
                        'feeTypeId': '13',
                        'compositeGoodsComposeList': [
                            {
                                'composeId': '3818222878819008513',
                                'goodsId': 'ffffffff0000000034fa32357d0b0000',
                                'parentGoodsId': 'ffffffff0000000034fd0d7a7d46c000',
                                'composeType': 40,
                            },
                        ],
                        'usePieceUnitFlag': 0,
                        'copiedFlag': 0,
                        'coopFlag': 0,
                        'cloudSupplierFlag': 0,
                        'nationalCode': 'T000200433',
                        'nationalCodeId': '3795757554344575988',
                        'minExpiryDate': '2027-07-27',
                        'expiredWarnMonths': 1,
                        'dangerIngredient': 0,
                        'pharmacologicId': '790d20527ca24fa69712478ef5ad351c',
                        'pharmacologicName': '清热药/清热泻火药',
                        'keyId': 'ffffffff0000000035027a37b2c24018',
                        'goodsBatchInfoList': [
                            {
                                'batchId': 50285562,
                                'pieceNum': 1,
                                'pharmacyType': 0,
                                'pharmacyNo': 0,
                                'expiryDate': '2027-07-27',
                                'productionDate': '2025-01-16',
                                'inDate': '2025-04-28T02:01:32Z',
                                'supplierId': 'ffffffff0000000034bc465d382b4000',
                                'supplierName': 'feiy供应商',
                                'batchNo': '476457456',
                                'packageCostPrice': 8,
                                'packagePrice': 12,
                                'piecePrice': 12,
                                'pieceCount': 35,
                                'packageCount': 0,
                                'dispGoodsCount': '35袋',
                                'stockPieceCount': 27,
                                'stockPackageCount': 0,
                                'dispStockGoodsCount': '27袋',
                                'lockingPieceCount': 8,
                                'lockingPackageCount': 0,
                                'dispLockingGoodsCount': '8袋',
                                'cutTotalPieceCount': 10,
                                'cutPieceCount': 10,
                                'totalSalePrice': 120,
                                'status': 0,
                            },
                        ],
                        'lastMonthSellCount': 0,
                        'gspModifyStatus': 30,
                        'businessScopeList': [
                            {
                                'scene': 0,
                                'id': 4000,
                                'parentId': 4,
                                'displayName': '中药饮片/含配方',
                                'name': '含配方',
                            },
                        ],
                        'isPreciousDevice': 0,
                        'specificationMatchStatus': 1,
                        'cMSpec': '非配方饮片',
                    },
                    'singlePromotions': [
                        {
                            'parentId': '3809405924781572096',
                            'id': '3809405924781572098',
                            'name': '买赠',
                            'hitRuleDetail': {
                                'discount': 0.5,
                                'type': 1,
                                'discountWay': 0,
                                'ruleType': 0,
                                'goodsTypeName': '非配方饮片',
                            },
                            'checked': true,
                            'expectedChecked': null,
                            'discountPrice': -60,
                            'displayDiscountPrice': null,
                            'participationDiscountCount': 10,
                            'leftSaleCount': null,
                            'promotionGoods': null,
                            'type': 1,
                            'parentType': 0,
                        },
                    ],
                    'singlePromotionFee': -60,
                    'totalPromotionFee': -60,
                    'unitAdjustmentFee': 0,
                },
            ],
            'sourceFormType': 28,
            'printFormType': 28,
            'totalPrice': null,
            'optometristId': null,
            'optometristName': null,
            'glassesType': null,
            'glassesParams': null,
            'processUsageInfo': null,
        },
    ],
    'chargeTransactions': [
        {
            'payMode': 3,
            'paySubMode': 0,
            'payModeName': '支付宝',
            'paySubModeName': null,
            'payModeDisplayName': '支付宝',
            'amount': 666.6,
            'thirdPartyPayCardId': null,
        },
        {
            'payMode': 5,
            'paySubMode': 0,
            'payModeName': '医保',
            'paySubModeName': null,
            'payModeDisplayName': '医保',
            'amount': 484.88,
            'thirdPartyPayCardId': '',
        },
    ],
    'totalFee': 1596.09,
    'singlePromotionFee': -444.61,
    'packagePromotionFee': 0,
    'discountFee': null,
    'receivableFee': 1151.48,
    'netIncomeFee': 1151.48,
    'refundFee': null,
    'singlePromotionedTotalFee': 1151.48,
    'oweFee': 0,
    'diagnosedDate': null,
    'diagnosis': '',
    'diagnosisInfos': [],
    'extendDiagnosisInfos': [],
    'healthCardBeginningBalance': 1684.88,
    'healthCardBalance': 1200,
    'healthCardOwner': '任我行',
    'healthCardOwnerRelationToPatient': '-',
    'healthCardId': null,
    'healthCardAccountPaymentFee': 484.88,
    'healthCardFundPaymentFee': 0,
    'healthCardOtherPaymentFee': 0,
    'healthCardCardOwnerType': null,
    'healthCardSelfConceitFee': 0,
    'healthCardSelfPayFee': 0,
    'personalPaymentFee': 666.6,
    'chargedByName': '冯杨',
    'chargedTime': '2025-06-17T08:05:53Z',
    'sellerName': '哑铃儿',
    'doctorName': '',
    'nationalDoctorCode': '',
    'patientOrderNo': '********',
    'departmentName': '',
    'departmentCaty': '',
    'hospitalCode': 'H50010500762',
    'doctorWorkNo': null,
    'refundByName': null,
    'refundTime': null,
    'latestOwePaidTime': null,
    'shebaoPayment': {
        'acctPayInvoiceFee': 484.88,
        'acctMulaidPayInvoiceFee': 0,
        'cardId': '********',
        'cardOwner': '任我行',
        'cardOwnerType': '退休人员',
        'idCardNum': '880118198001015233',
        'beforeCardBalance': 1684.88,
        'cardBalance': 1200,
        'relationToPatient': '-',
        'receivedFee': 484.88,
        'accountPaymentFee': 484.88,
        'fundPaymentFee': 0,
        'otherPaymentFee': 0,
        'personalPaymentFee': 666.6,
        'medType': '定点药店购药',
        'selfPaymentFee': null,
        'selfHandledPaymentFee': null,
        'chargeNumber': '**************',
        'extraInfo': {
            'insutype': '职工基本医疗保险',
            'insutypeCode': '310',
            'cvlservPay': 0,
            'hifesPay': 0,
            'hifmiPay': 0,
            'hifobPay': 0,
            'mafPay': 0,
            'psnCashPay': 666.6,
            'hifpPay': 0,
            'actPayDedc': 0,
            'othPay': 0,
            'fulamtOwnpayAmt': 666.6,
            'shebaoFulamtOwnpayAmt': 0,
            'overlmtSelfpay': 0,
            'preselfpayAmt': 0,
            'inscpScpAmt': 484.88,
            'acctMulaidPay': 0,
            'hospPartAmt': 0,
            'psnNo': '33010099**********76653787',
            'iptOtpNo': 'abc-5027a37adcf00000',
            'medfeeSumamt': 484.88,
            'psnPartAmt': 484.88,
            'rawPsnCashPay': 0,
            'selfPaymentFeeYi': 48.4879936,
            'selfPaymentFeeBing': 0,
            'insuplcAdmdvs': '500105',
            'insuplcAdmdvsName': '重庆市江北区',
            'poolPropSelfpay': 0,
            'empName': '浙江诚信人才资源交流服务有限公司建德分公司310',
            'setlId': '*************',
            'mdtrtareaAdmvs': '500105',
            'hospitalCode': 'H12345678910',
            'hospitalName': '测试定点机构',
            'diseaseCode': null,
            'diseaseName': null,
            'wltpayAmt': 0,
            'gongjiAccountPaymentFee': null,
            'gongjiFundPaymentFee': null,
            'gongjiOtherPaymentFee': null,
            'gongjiPsnCashPay': null,
            'gongjiBalc': null,
            'gongjiAuthorName': null,
            'gongjiRelation': null,
        },
        'region': 'sichuan_chengdu',
    },
    'subTotals': {
        'registrationFee': 0,
        'westernMedicineFee': 72.24,
        'chineseMedicineFee': 693.85,
        'examinationFee': 0,
        'treatmentFee': 0,
        'materialFee': 830,
        'onlineConsultationFee': 0,
        'expressDeliveryFee': 0,
        'decoctionFee': 0,
        'otherFee': 0,
        'composeProductFee': 0,
        'eyeFee': 0,
        'nursingFee': 0,
        'surgeryFee': 0,
    },
    'medicalBills': [
        {
            'name': '西药费',
            'totalFee': 52,
            'totalCount': 3,
            'unit': '项',
            'printType': 1,
            'feeTypeId': null,
            'innerFlag': 1,
            'sort': 0,
        },
        {
            'name': '中药饮片',
            'totalFee': 472.48,
            'totalCount': 13,
            'unit': '项',
            'printType': 2,
            'feeTypeId': null,
            'innerFlag': 1,
            'sort': 0,
        },
        {
            'name': '材料商品费',
            'totalFee': 627,
            'totalCount': 4,
            'unit': '项',
            'printType': 8,
            'feeTypeId': null,
            'innerFlag': 1,
            'sort': 0,
        },
    ],
    'wholeMedicalBills': [
        {
            'name': null,
            'totalFee': 400,
            'totalCount': 1,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 26,
            'innerFlag': 1,
            'sort': 0,
        },
        {
            'name': null,
            'totalFee': 200,
            'totalCount': 2,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 27,
            'innerFlag': 1,
            'sort': 0,
        },
        {
            'name': '西药费',
            'totalFee': 52,
            'totalCount': 3,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 12,
            'innerFlag': 1,
            'sort': 2,
        },
        {
            'name': '中药费',
            'totalFee': 472.48,
            'totalCount': 13,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 13,
            'innerFlag': 1,
            'sort': 4,
        },
        {
            'name': '卫生材料费',
            'totalFee': 27,
            'totalCount': 1,
            'unit': '项',
            'printType': 0,
            'feeTypeId': 1004,
            'innerFlag': 1,
            'sort': 14,
        },
    ],
    'giftCoupons': [],
    'promotionBalances': [],
    'owedStatus': 0,
    'pharmacistName': '冯杨',
    'pharmacistNationalDoctorCode': 'Y12345678910',
    'remarks': '零售备注',
    'latestChargeComment': '收费备注',
    'serialNo': '3819749813604466688',
    'healthCardPaymentFee': 484.88,
    traceCodeQrCodeUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAYAAABxlTA0AAAAAklEQVR4AewaftIAAASfSURBVO3BUY7kypEAQXei7n9lXyWgAB4SHKpaPbn6oZlA/CUVQ2WpGCq7iqEyKhaVOxW/obKrGCp/w8XrqIvXUR/+oeKnVO5UfEPljspSMVR2KncqFpVRMSoWlScV/w2V5eJ11MXrqA9/oPKk4m+rGCpPVJ6o7FRGxU+pPKnYXbyO+nCIyjcq7lR8o2KojIpF5X/p4nXUxeuoD4dVDJVRsVMZFU8qfqpiqCwVJ128jrp4HfXhDyr+toqhslSMip3KqNipjIqhslMZFT9V8VMXr6M+/IPKSSqjYlEZFUNlqRgqo+IbFUNlpzIqdiq/cfE66uJ1lP0LB6g8qXiislTcUfmNiv8PF6+jLl5HfVRGxROVUbGofKvip1RGxZOKofINld+ouKOyXLyO+lTcUVkq7qgsFUNlVCwqd1SWijsVO5W/RWWpeKIyKobKonKnYrl4HXXxOuqjMipGxZOKReWOylIxVEbFTuVbFYvKk4o7KovKnYql4knFUNldvI66eB0lEP9WMVSWijsqS8VQeVIxVJaKoTIqFpVR8S2VpWKo7CruqCwV/w2V5eJ11If/QOWJyqjYqdypWFS+pTIqnlR8Q2VU7FSeVAyV3cXrqIvXUfYv3FDZVTxR+amKOypLxR2Vb1TcUVkqhsqoWFRGxVDZVewuXkddvI768AcVO5VRsaiMiqGyqxgqi8qoGBWLyqgYFYvKqBgqi8pJFUNlVCwXr6ME4kbFojIqhspSMVRGxaLypOKOyk9V7FRGxVBZKobKrmKojIpvXLyOungd9akYKk9URsWiMiqGyq5iqOxURsUTlZ3KE5WdyqgYKt9QuVOxXLyOungd9eE/qBgqu4qhMip2KqPiicqTip9S2VUMlVGxqxgqT1SWi9dRH5VRMVR2FUNlVzFUdhU7lTsVi8qoGCpLxR2Vn6oYKkvFk4qhMiqWi9dRF6+jPhVD5acqhspJKjuVb1UMlZ3KqHhSsasYKsvF66iL11EflVExVL6hMiqGylIxVEbFUvGtip3KqBgqS8W3KobKUnFHZal4cvE6SiB+oWKo7CqGyrcqdiqjYqcyKnYqu4o7KruKofKkYrl4HXXxOupT8S2Vncqo2KmMiqGyqxgqS8UdlaXiicq3VEbFTmVU7FR2F6+jLl5HffgDlScVP6UyKhaVb1UMlScqu4qh8g2V37h4HfVRuVOxqDxRuVPxRGWpGCqjYlF5ojIqhso3VO6ofENlVOwuXkddvI76VDypeFJxR2WpuKOyqNxR+UbFnYpF5VsVT1S+pbJcvI66eB0lEH9JxVD5qYqdyqjYqYyKobJUPFEZFUNlqRgqu4qhMiqWi9dRH/6h4qdU7lTsVH5DZVQsFUNlVOxURsWTip+q2F28jrp4HfXhD1SeVDxRWSpGxROV/yWV31AZFcvF66iL11EfDqlYVH6j4onKHZVdxVDZVQyVpWKojIpF5cnF66gPh1UMlScVQ2VRGRW7ijsqS8VvqHyrYnfxOuriddSHP6j4G1RGxROVJyq7ijsVi8rfUjFUloonF6+jLl5HffgHlb9FZacyKp5U7FR2KidVDJVvqSwXr6P+DwJnxhBDG+d2AAAAAElFTkSuQmC',
};
