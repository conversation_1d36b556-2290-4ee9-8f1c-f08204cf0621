<template>
    <biz-setting-layout class="print-manage-page">
        <biz-setting-content v-abc-loading="loading">
            <abc-form ref="printForm" item-no-margin>
                <biz-setting-form :label-width="84" divider-full-screen>
                    <biz-setting-form-header>
                        <tag-tab></tag-tab>
                    </biz-setting-form-header>
                    <biz-setting-form-group>
                        <biz-setting-form-item v-if="isShowTemplateType" label="模版选择">
                            <abc-radio-group v-model="postData.selectedOption" @change="handleChangeTemplate">
                                <abc-flex gap="12">
                                    <abc-radio :label="postData.options[0]" class="checkbox-no-margin">
                                        通用模版
                                    </abc-radio>
                                    <abc-radio :label="postData.options[1]">
                                        定制模版
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <template v-if="postData.selectedOption === postData.options[1]">
                            <biz-setting-form-item label="抬头名称" label-line-height-size="medium">
                                <abc-form-item required class="print-form-item" :validate-event="validateName">
                                    <title-setting
                                        v-model="postData[postData.selectedOption].title"
                                        :max-length="12"
                                        :distinguish-half-angle-length="false"
                                    ></title-setting>
                                </abc-form-item>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="手机隐私保护">
                                <abc-radio-group v-model="postData[postData.selectedOption].mobileType">
                                    <abc-flex gap="12">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            完整展示
                                        </abc-radio>
                                        <abc-radio :label="1">
                                            隐藏中间4位
                                        </abc-radio>
                                    </abc-flex>
                                </abc-radio-group>
                            </biz-setting-form-item>
                        </template>

                        <template v-else>
                            <biz-setting-form-item label="患者信息">
                                <abc-checkbox v-model="postData[postData.selectedOption].mobile" type="number">
                                    手机
                                </abc-checkbox>
                            </biz-setting-form-item>

                            <biz-setting-form-item v-if="postData[postData.selectedOption].mobile" label="手机隐私保护">
                                <abc-radio-group v-model="postData[postData.selectedOption].mobileType">
                                    <abc-flex gap="12">
                                        <abc-radio :label="2" class="checkbox-no-margin">
                                            完整展示
                                        </abc-radio>
                                        <abc-radio :label="1">
                                            隐藏中间4位
                                        </abc-radio>
                                    </abc-flex>
                                </abc-radio-group>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="就诊信息">
                                <abc-flex v-if="postData.selectedOption === 'common'" :gap="12" wrap="wrap">
                                    <abc-checkbox
                                        v-model="postData[postData.selectedOption].billDepartment"
                                        type="number"
                                        class="checkbox-no-margin"
                                    >
                                        开单科室
                                    </abc-checkbox>
                                </abc-flex>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="成药明细">
                                <abc-flex :gap="12" wrap="wrap">
                                    <abc-checkbox
                                        v-model="postData[postData.selectedOption].westernTotalAmount"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleChangeTab(0)"
                                    >
                                        总量
                                    </abc-checkbox>

                                    <abc-checkbox
                                        v-model="postData[postData.selectedOption].westernDays"
                                        type="number"
                                        class="checkbox-no-margin"
                                        @change="handleChangeTab(0)"
                                    >
                                        天数
                                    </abc-checkbox>

                                    <abc-checkbox v-model="postData[postData.selectedOption].westernSkinTest" type="number" @change="handleChangeTab(0)">
                                        皮试
                                    </abc-checkbox>
                                </abc-flex>
                            </biz-setting-form-item>

                            <biz-setting-form-item v-if="showTagChineseRemark" label="中药备注" label-line-height-size="medium">
                                <abc-textarea
                                    v-model="postData[postData.selectedOption].chineseRemark"
                                    :height="52"
                                    :width="486"
                                    placeholder="输入备注内容"
                                    maxlength="20"
                                    @focus="handleChangeTab(1)"
                                >
                                </abc-textarea>
                            </biz-setting-form-item>

                            <biz-setting-form-item label="页尾信息">
                                <abc-checkbox v-model="postData[postData.selectedOption].printDate" type="number">
                                    打印时间
                                </abc-checkbox>
                            </biz-setting-form-item>
                        </template>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-space>
                        <abc-button :disabled="disabled" :loading="btnLoading" @click="handleSave">
                            保存
                        </abc-button>

                        <abc-button variant="ghost" @click="handleReset">
                            恢复默认
                        </abc-button>
                    </abc-space>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            <preview-layout
                is-tag
                :custom-style="{
                    height: isSelectCustom ? '50mm' : '40mm'
                }"
            >
                <template slot="previewTab">
                    <abc-tabs-v2
                        v-model="curPreviewTab"
                        :option="previewTabs"
                        size="middle"
                        disable-indicator
                        :border="false"
                        @change="handleChangePreviewTab"
                    ></abc-tabs-v2>
                </template>
                <div slot="previewHtml" ref="previewMountPoint"></div>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import PreviewLayout from '../../components/preview-layout.vue';
    import clone from 'utils/clone.js';

    import {
        EXAMPLE_DATA, WS_EXAMPLE_DATA,
    } from 'views/settings/print-config/tags/medicine/exmaple.js';
    import TagMixins from '../mixins/index.js';
    import MixinPrint from 'views/settings/print-config/medical-documents/mixin-print.js';
    import PrintAPI from 'api/print.js';
    import { isEqual } from 'utils/lodash.js';
    import { mapGetters } from 'vuex';
    import TitleSetting from 'views/settings/print-config/components/title-setting.vue';
    import { validateName } from 'views/inventory/goods/utils.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormHeader,
    } from '@/components-composite/setting-form/index.js';

    import {
        BizSettingLayout,
        BizSettingSidebar,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import TagTab from 'views/settings/print-config/components/tag-tab/index.vue';

    const COMMON_INIT_DATA = {
        chineseRemark: '', // 中药备注
        mobile: 0, // 是否展示手机号
        mobileType: 0, // 展示手机号 1 部分展示  2 完整展示
        printDate: 0, // 是否打印日期
        title: '', // 抬头
        westernDays: 0, // 西药天数展示
        westernTotalAmount: 0, // 西药总量展示
        westernSkinTest: 0, // 西药/输注皮试展示
    };
    const CUSTOM_INIT_DATA = {
        mobileType: 0, // 展示手机号 1 部分展示  2 完整展示
        title: '', // 抬头
    };

    export default {
        name: 'MedicineTag',
        components: {
            TagTab,
            BizSettingFormHeader,
            TitleSetting,
            PreviewLayout,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingLayout,
            BizSettingSidebar,
            BizSettingContent,
            BizSettingFooter,
        },
        mixins: [TagMixins, MixinPrint],
        beforeRouteLeave(to, from, next) {
            if (!this.disabled) {
                this.$confirm({
                    type: 'warn',
                    title: '你的修改内容还未保存，确定离开？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                loading: false,
                btnLoading: false,
                postData: {
                    common: COMMON_INIT_DATA,
                    options: ['common'],
                    selectedOption: 'common',
                },
                curPreviewTab: '',
                postDataCache: null,
            };
        },
        computed: {
            ...mapGetters(['currentClinic']),
            ...mapGetters('viewDistribute',[ 'viewDistributeConfig' ]),

            showTagChineseRemark() {
                return this.viewDistributeConfig.Settings.print.showTagChineseRemark;
            },
            disabled() {
                if (!this.postDataCache) return true;
                return isEqual(this.postData, this.postDataCache);
            },
            previewTabs() {
                return [{
                    label: '成药标签',
                    value: '成药标签',
                    exampleData: WS_EXAMPLE_DATA,
                },{
                    label: '中药标签',
                    value: '中药标签',
                    exampleData: EXAMPLE_DATA,
                }];
            },
            previewPage() {
                return this.isSelectCustom ?
                    {
                        size: '80mm×50mm',
                        orientation: 1,
                    } : {
                        size: '60mm×40mm',
                        orientation: 1,
                    };
            },
            isShowTemplateType() {
                return this.postData.options.length > 1;
            },
            isSelectCustom() {
                return this.postData.selectedOption === this.postData.options[1];
            },
        },
        async created() {
            this._medicineTag = true;
            await this.fetchData();
            this.curPreviewTab = this.previewTabs[0].value;
            this.currentExampleData = WS_EXAMPLE_DATA;
        },
        methods: {
            validateName,
            getCurrentTemplate() {
                return this.isSelectCustom ?
                    window.AbcPackages.AbcTemplates.medicineTagCustomized :
                    window.AbcPackages.AbcTemplates.medicineTag;
            },

            instanceGlobalConfigHandler(newValue) {
                const newInstanceGlobalConfig = clone(this.$store.getters.printGlobalConfig);
                newInstanceGlobalConfig.medicineTag = newValue;
                return newInstanceGlobalConfig;
            },
            async fetchData() {
                this.loading = true;
                try {
                    const data = await this.$store.dispatch('fetchPrintMedicineTagConfig');
                    this.postData = data;
                    this.postDataCache = clone(this.postData);
                } catch (e) {
                    console.error('用药标签配置获取失败', e);
                } finally {
                    this.loading = false;
                }
            },
            createPostData(postData) {
                const resetData = {
                    title: '',
                    mobile: 1,
                    mobileType: 1, // 0 不展示 1 部分展示  2 完整展示
                    chineseRemark: '', // 中药备注
                };
                if (!postData) return resetData;
                return Object.assign(resetData, postData);
            },
            handleSave() {
                this.$refs.printForm.validate((val) => {
                    if (val) {
                        this.updatePrintConfig();
                    }
                });
            },
            async updatePrintConfig() {
                try {
                    this.btnLoading = true;
                    const { data } = await PrintAPI.updatePrintConfig('clinic', 'print.medicineTag', {
                        medicineTag: this.postData,
                    });
                    this.postData = data.medicineTag;
                    this.$store.commit('SET_PRINT_MEDICINE_TAG_CONFIG', this.postData);
                    this.postDataCache = clone(this.postData);
                    this.btnLoading = false;
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            handleReset() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '是否确认将当前全部设置恢复为系统默认设置？',
                    onConfirm: () => {
                        this.postData[this.postData.selectedOption] = this.isSelectCustom ? CUSTOM_INIT_DATA : COMMON_INIT_DATA;
                        this.postData[this.postData.selectedOption].title = this.currentClinic.clinicName;
                    },
                });
            },
            setCurrentExampleData() {
                const item = this.previewTabs.find((item) => {
                    return item.value === this.curPreviewTab;
                });
                this.currentExampleData = item.exampleData;
            },
            handleChangePreviewTab(value) {
                this.curPreviewTab = value;
                this.$nextTick(() => {
                    this.setCurrentExampleData();
                    this.printInstance = null;
                    this.updatePreview(this.postData);
                });
            },
            // 编辑中药备注选中中药标签
            handleChangeTab(index) {
                this.curPreviewTab = this.previewTabs[index].label;

                this.$nextTick(() => {
                    this.setCurrentExampleData();
                    this.printInstance = null;
                    this.updatePreview(this.postData);
                });
            },
            async handleChangeTemplate() {
                this.printInstance = null;
                await this.mountPrintInstance();
                await this.updateInstanceGlobalConfig(this.postData);
            },
        },
    };
</script>
