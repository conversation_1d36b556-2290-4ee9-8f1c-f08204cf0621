<template>
    <biz-setting-layout class="data-permission-setting-wrapper">
        <biz-setting-content>
            <abc-form>
                <biz-setting-form :label-width="160" :content-width="1000">
                    <!--工作台-->
                    <biz-setting-form-group
                        title="工作台"
                    >
                        <biz-setting-form-item
                            label="收费员查看账目"
                            label-tip="查看工作台-今日工作汇总的账目信息，以及收费-收费看板弹窗里的账目信息"
                        >
                            <abc-radio-group v-model="postDataPermission.dashboard.chargerPermission">
                                <abc-flex gap="16">
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        允许查看门店全部账目
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        只允许查看个人经手账目
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看账目
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="医生查看挂号费用"
                            label-tip="查看工作台-今日工作汇总弹窗里的“挂号费用”表格，以及门诊-门诊看板里的挂号费用"
                        >
                            <abc-radio-group v-model="postDataPermission.dashboard.doctorRegistrationFee">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看个人挂号收入
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看个人挂号收入
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="医生查看诊疗费用"
                            label-tip="查看工作台-今日工作汇总弹窗里的“诊疗费用”表格，以及门诊-门诊看板里的诊疗费用"
                        >
                            <abc-radio-group v-model="postDataPermission.dashboard.doctorOutpatientFee">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看个人诊疗收入
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看个人诊疗收入
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="医生查看执行金额"
                        >
                            <abc-radio-group v-model="postDataPermission.dashboard.doctorExecuteFee">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看个人执行金额
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看个人执行金额
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="医生查看今日看板"
                        >
                            <abc-radio-group v-model="postDataPermission.dashboard.kanbanPermission">
                                <abc-flex gap="16">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        只允许查看自己的患者看板
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看当天所有患者看板
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--挂号预约-->
                    <biz-setting-form-group
                        title="挂号预约"
                    >
                        <biz-setting-form-item
                            label="查看患者就诊历史"
                        >
                            <abc-radio-group v-model="postDataPermission.registration.medicalHistory">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="查看患者手机号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.registration.patientMobile.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有挂号预约权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="registrationPatientMobileSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('registrationPatientMobile')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="修改支付方式"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.registration.modifyPayMode.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有挂号预约权限可修改
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可修改
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可修改
                                        <abc-button
                                            v-show="registrationPayModeSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('registrationPayMode')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="新增、修改挂号预约"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.registration.modifyRegistration.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有挂号预约权限可修改
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可修改
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可修改
                                        <abc-button
                                            v-show="registrationModifySelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('registrationModify')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--执行站-->
                    <biz-setting-form-group
                        title="执行站"
                    >
                        <biz-setting-form-item label="查看执行单">
                            <abc-radio-group v-model="postDataPermission.nurse.historySheet">
                                <abc-flex gap="16">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        允许查看所有
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅允许查看自己开出的
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="查看执行单的执行历史">
                            <abc-radio-group v-model="postDataPermission.nurse.executedSheetDetail">
                                <abc-flex gap="16">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        允许所有人查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        只允许参与人查看
                                        <abc-tooltip-info content="包括开单人及执行人"></abc-tooltip-info>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看患者就诊历史"
                        >
                            <abc-radio-group v-model="postDataPermission.nurse.medicalHistory">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看患者手机号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.nurse.patientMobile.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有执行站权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="nursePatientMobileSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('nursePatientMobile')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--门诊-->
                    <biz-setting-form-group
                        title="门诊"
                    >
                        <biz-setting-form-item
                            label="查看药品价格"
                        >
                            <abc-radio-group v-model="postDataPermission.outpatient.goodsPrice">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看药品明细，总价
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        只允许查看药品总价
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        不允许查看药品明细，总价
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看历史处方"
                        >
                            <abc-radio-group v-model="postDataPermission.outpatient.historyPrescription">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看患者所有的历史处方
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        只允许查看自己开出的历史处方
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看药品进价"
                        >
                            <abc-radio-group v-model="postDataPermission.outpatient.goodsCostPrice">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看药品进价
                                        <abc-tooltip-info content="议价时便于参考"></abc-tooltip-info>
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看药品进价
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看患者手机号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.outpatient.patientMobile.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有门诊权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="outpatientPatientMobileSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('outpatientPatientMobile')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="新增、修改挂号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.outpatient.modifyRegistration.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有门诊权限可修改
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可修改
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可修改
                                        <abc-button
                                            v-show="outpatientModifyRegistrationSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('outpatientModifyRegistration')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--收费-->
                    <biz-setting-form-group
                        title="收费"
                    >
                        <biz-setting-form-item
                            label="查看药品进价"
                        >
                            <abc-radio-group v-model="postDataPermission.cashier.goodsCostPrice">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看药品进价
                                        <abc-tooltip-info content="议价时便于参考"></abc-tooltip-info>
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看药品进价
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看就诊历史"
                        >
                            <abc-radio-group v-model="postDataPermission.cashier.patientHistory">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看患者手机号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.cashier.patientMobile.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有收费权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="cashierPatientMobileSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('cashierPatientMobile')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改支付方式"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.cashier.modifyPayMode.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有收费权限可修改
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可修改
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可修改
                                        <abc-button
                                            v-show="cashierPayModeSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('cashierPayMode')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--药房-->
                    <biz-setting-form-group
                        title="药房"
                    >
                        <biz-setting-form-item
                            label="查看就诊历史"
                        >
                            <abc-radio-group v-model="postDataPermission.pharmacy.patientHistory">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        允许查看
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        不允许查看
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看患者手机号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.pharmacy.patientMobile.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有药房权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="pharmacyPatientMobileSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('pharmacyPatientMobile')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--库存-->
                    <biz-setting-form-group
                        title="库存"
                    >
                        <biz-setting-form-item
                            label="查看药品物资成本"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.inventory.goodsCostConfig.goodsCost">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有库存权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="inventoryCostSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('inventoryCost')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看药品物资毛利"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.inventory.goodsProfitConfig.goodsProfit">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有库存权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="inventoryProfitSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('inventoryProfit')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="库存单据查看药品成本"
                            label-line-height-size="small"
                        >
                            <abc-radio-group
                                v-model="postDataPermission.inventory.checkGoodsPrice.value"
                                @change="handleInventoryViewGoodsPriceChange"
                            >
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        <abc-space>
                                            <span>
                                                有库存出入库业务权限可查
                                            </span>
                                            <abc-tooltip-info placement="bottom-start" :content="inventoryBusinessDesc">
                                            </abc-tooltip-info>
                                        </abc-space>
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看

                                        <abc-button
                                            v-show="inventoryCheckGoodsPriceSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('inventoryCheckGoodsPrice')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--患者-->
                    <biz-setting-form-group
                        title="患者"
                    >
                        <biz-setting-form-item
                            label="医生查看患者信息"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.doctorPatients">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        查看全部患者
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        仅查看自己接诊过的患者
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="执行人查看患者信息"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.executorPatients">
                                <abc-flex gap="16">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        查看全部患者
                                    </abc-radio>
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        仅查看自己参与过的患者
                                        <abc-tooltip-info content="包括开单，执行"></abc-tooltip-info>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看患者手机号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.patientMobile.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有患者权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="crmPatientMobileSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('crmPatientMobile')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改首诊来源"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.modifyFirstFromAway.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有患者权限可编辑
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可编辑
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可编辑
                                        <abc-button
                                            v-show="crmModifyFirstFromAwaySelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('crmModifyFirstFromAway')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改患者姓名"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.modifyName.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        全部成员可编辑
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可编辑
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可编辑
                                        <abc-button
                                            v-show="crmModifyNameSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('crmModifyName')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改患者证件号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.modifyIdCard.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        全部成员可编辑
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可编辑
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可编辑
                                        <abc-button
                                            v-show="crmModifyIdCardSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('crmModifyIdCard')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改患者档案号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.modifySn.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        全部成员可编辑
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可编辑
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可编辑
                                        <abc-button
                                            v-show="crmModifySnSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('crmModifySn')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="查看患者累计消费"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.patientPayAmount.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有患者权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="crmPatientPayAmountSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('crmPatientPayAmount')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改患者会员等级，余额"
                        >
                            <abc-flex>
                                <abc-text theme="gray">
                                    {{ dataPermissionInfo.crmMemberLevelText }}
                                </abc-text>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改患者积分"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.modifyPoint.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="4" class="data-permission-setting-item">
                                        全部成员可编辑
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可编辑
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可编辑
                                        <abc-button
                                            v-show="crmPointsEditSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('crmPointsEdit')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改患者优惠券"
                        >
                            <abc-radio-group v-model="postDataPermission.crm.modifyCoupon.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="4" class="data-permission-setting-item">
                                        全部成员可编辑
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可编辑
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可编辑
                                        <abc-button
                                            v-show="crmCouponEditSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('crmCouponEdit')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="修改患者卡项"
                        >
                            <abc-flex>
                                <abc-text theme="gray">
                                    {{ dataPermissionInfo.crmCardText }}
                                </abc-text>
                            </abc-flex>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--微诊所-->
                    <biz-setting-form-group
                        v-if="supportSetupHomePageStatistics"
                        :title="institutionTypeWording"
                    >
                        <biz-setting-form-item
                            label="查看首页数据"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.microClinic.homePageConfig.value">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="microHomePageSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('microHomePage')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--统计-->
                    <biz-setting-form-group
                        title="统计"
                    >
                        <biz-setting-form-item
                            label="查看药品物资成本"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.statistics.goodsCostConfig.goodsCost">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有统计权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="statisticsCostSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('statisticsCost')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="查看药品物资毛利"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.statistics.goodsProfitConfig.goodsProfit">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有统计权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="statisticsProfitSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('statisticsProfit')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="查看患者手机号"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.statistics.patientMobileConfig.patientMobile">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有统计权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="statisticsPatientMobileSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('statisticsPatientMobile')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--商城-->
                    <biz-setting-form-group
                        v-if="b2bMallIsPurchased"
                        title="直采商城"
                    >
                        <biz-setting-form-item
                            label="查看商品价格"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.mall.goodsPriceConfig.goodsPrice">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有采购权限可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="lookGoodsPriceSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('mallPrice')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item
                            label="采购商品"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.mall.goodsPurchaseConfig.goodsPurchase">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有采购权限可采购
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可采购
                                        <abc-button
                                            v-show="goodsPurchaseSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('mallPurchase')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--医保-->
                    <biz-setting-form-group
                        title="医保"
                    >
                        <biz-setting-form-item
                            label="查看医保主页"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.medicalInsurance.viewHomePageConfig.viewHome">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有医保权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="socialSurveySelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('socialSurvey')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="查看医保账目数据"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.medicalInsurance.viewAccountConfig.viewAccount">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有医保权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="socialAccountSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('socialAccount')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="查看业务登记数据"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.medicalInsurance.viewBusinessRegistrationRecordConfig.viewBusinessRegistrationRecord">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有医保权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="socialRegisterSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('socialRegister')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="查看机构资料数据"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.medicalInsurance.viewProfileDataConfig.viewProfileData">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有医保权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="socialMaintainSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('socialMaintain')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                        <biz-setting-form-item
                            label="查看医保设置信息"
                            label-line-height-size="small"
                        >
                            <abc-radio-group v-model="postDataPermission.medicalInsurance.viewSetupInformationConfig.viewSetupInformation">
                                <abc-flex gap="16" align="center" style="height: 26px;">
                                    <abc-radio :label="0" class="data-permission-setting-item">
                                        有医保权限可查看
                                    </abc-radio>
                                    <abc-radio :label="1" class="data-permission-setting-item">
                                        仅管理员可查看
                                    </abc-radio>
                                    <abc-radio :label="2" class="data-permission-setting-item">
                                        指定成员可查看
                                        <abc-button
                                            v-show="socialSettingSelected"
                                            variant="text"
                                            size="small"
                                            @click="openMemberSettingDialog('socialSetting')"
                                        >
                                            查看
                                        </abc-button>
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button
                        :loading="loading"
                        :disabled="!isUpdated"
                        @click="submit"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <members-setting-dialog
            v-if="showMembersSettingDialog"
            :visible.sync="showMembersSettingDialog"
            :members="currentMembers"
            :dialog-title="currentMembersDialogTitle"
            :content-text="currentMembersSettingText"
            :text-before-text="textBeforeText"
            :content-text-tips="contentTextTips"
            @cancel="onCancel"
            @confirm="onConfirm"
        ></members-setting-dialog>
    </biz-setting-layout>
</template>

<script type="text/ecmascript-6">
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';

    import MembersSettingDialog from './members-setting-dialog.vue';
    import AbcAccess from '@/access/utils';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';

    export default {
        name: 'DataPermissionConfig',
        components: {
            MembersSettingDialog,

            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
        },
        data() {
            return {
                loading: false,
                showMembersSettingDialog: false,// 是否显示成员设置弹窗
                membersSettingType: '',// 6种成员类型弹窗
                postDataPermission: {
                    dashboard: {
                        chargerPermission: 2,
                        doctorRegistrationFee: 1,
                        doctorOutpatientFee: 1,
                        kanbanPermission: 0,
                        doctorExecuteFee: 1,
                    },// 工作台
                    outpatient: { // 门诊
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                        modifyRegistration: {
                            value: 0,
                            employees: [],
                        },
                    },
                    cashier: { // 收费
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                        modifyPayMode: { //支付方式
                            value: 0,
                            employees: [],
                        },
                    },
                    pharmacy: { // 药房
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                    },
                    inventory: { // 库存
                        goodsCostConfig: {
                            goodsCost: 0,
                            employees: [],
                        },
                        goodsProfitConfig: {
                            goodsProfit: 0,
                            employees: [],
                        },
                        checkGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                        damageGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                        obtainGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                        transGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                        productionGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                    },
                    statistics: { // 统计
                        goodsCostConfig: {
                            goodsCost: 0,
                            employees: [],
                        },
                        goodsProfitConfig: {
                            goodsProfit: 0,
                            employees: [],
                        },
                        patientMobileConfig: {
                            patientMobile: 0,
                            employees: [],
                        },
                    },
                    mall: {
                        goodsPriceConfig: {
                            goodsPrice: 0,
                            employees: [],
                        },
                        goodsPurchaseConfig: {
                            goodsPurchase: 0,
                            employees: [],
                        },
                    },
                    medicalInsurance: {
                        viewHomePageConfig: {
                            viewHome: 0,
                            employees: [],
                        },
                        viewAccountConfig: {
                            viewAccount: 0,
                            employees: [],
                        },
                        viewBusinessRegistrationRecordConfig: {
                            viewBusinessRegistrationRecord: 0,
                            employees: [],
                        },
                        viewProfileDataConfig: {
                            viewProfileData: 0,
                            employees: [],
                        },
                        viewSetupInformationConfig: {
                            viewSetupInformation: 0,
                            employees: [],
                        },
                    },
                    crm: { // 患者
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                        modifyFirstFromAway: {
                            value: 0,
                            employees: [],
                        },
                        patientPayAmount: {
                            value: 0,
                            employees: [],
                        },
                        modifyName: {
                            value: 0,
                            employees: [],
                        },
                        levelEdit: { // 等级编辑权限
                            value: 0, // 0全部成员可编辑 1仅管理员可编辑 2指定成员可编辑
                            employees: [], // 成员ids，仅在value=2时有用
                        },
                        balanceEdit: { // 余额编辑权限
                            value: 0, // 0全部成员可编辑 1仅管理员可编辑 2指定成员可编辑
                            employees: [], // 成员ids，仅在value=2时有用
                        },
                        modifyPoint: { // 积分编辑权限
                            value: 4, // 4全部成员可编辑 1仅管理员可编辑 2指定成员可编辑
                            employees: [], // 成员ids，仅在value=2时有用
                        },
                        modifyCoupon: { // 优惠券编辑权限
                            value: 4, // 4全部成员可编辑 1仅管理员可编辑 2指定成员可编辑
                            employees: [], // 成员ids，仅在value=2时有用
                        },
                        modifyIdCard: {
                            value: 0,
                            employees: [],
                        },
                        modifySn: {
                            value: 0,
                            employees: [],
                        },
                    },
                    nurse: {
                        historySheet: 0,
                        executedSheetDetail: 0,
                        medicalHistory: 1, // 查看患者就诊历史: 1允许 0不允许
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                    },
                    registration: { // 挂号预约
                        medicalHistory: 1, // 查看患者就诊历史: 1允许 0不允许
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                        modifyPayMode: {
                            value: 0,
                            employees: [],
                        },
                        modifyRegistration: {
                            value: 0,
                            employees: [],
                        },
                    },
                    settings: {
                        modifyEmployeeName: 0,
                    },
                    microClinic: {
                        homePageConfig: {
                            value: 0,
                            employees: [],
                        },
                    },
                },
            };
        },
        computed: {
            ...mapGetters(['clinicConfig', 'currentClinic', 'isChainAdmin', 'dataPermission']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            dataPermissionInfo() {
                return this.viewDistributeConfig.CRM.dataPermission;
            },
            supportSetupHomePageStatistics() {
                return this.viewDistributeConfig.WeClinic.supportSetupHomePageStatistics;
            },
            institutionTypeWording() {
                return `微${this.$app.institutionTypeWording}`;
            },
            isSupportModifyEmployeeNameInPermission() {
                return this.viewDistributeConfig.Settings.employees.isSupportModifyEmployeeNameInPermission;
            },
            // 是否展示有查看商品价格的人
            lookGoodsPriceSelected() {
                return this.postDataPermission.mall.goodsPriceConfig.goodsPrice === 2;
            },
            // 是否展示有采购权限的人
            goodsPurchaseSelected() {
                return this.postDataPermission.mall.goodsPurchaseConfig.goodsPurchase === 2;
            },
            // 选中了库存成本radio
            inventoryCostSelected() {
                return this.postDataPermission.inventory.goodsCostConfig.goodsCost === 2;
            },
            // 选中了库存利润radio
            inventoryProfitSelected() {
                return this.postDataPermission.inventory.goodsProfitConfig.goodsProfit === 2;
            },
            // 选中了库存盘点金额
            inventoryCheckGoodsPriceSelected() {
                return this.postDataPermission.inventory.checkGoodsPrice.value === 2;
            },
            // 选中了统计成本radio
            statisticsCostSelected() {
                return this.postDataPermission.statistics.goodsCostConfig.goodsCost === 2;
            },
            // 选中了统计利润radio
            statisticsProfitSelected() {
                return this.postDataPermission.statistics.goodsProfitConfig.goodsProfit === 2;
            },
            // 选中了统计指定成员radio
            statisticsPatientMobileSelected() {
                return this.postDataPermission.statistics.patientMobileConfig.patientMobile === 2;
            },
            // 选中了医保主页radio
            socialSurveySelected() {
                return this.postDataPermission.medicalInsurance.viewHomePageConfig.viewHome === 2;
            },
            // 选中了医保账目radio
            socialAccountSelected() {
                return this.postDataPermission.medicalInsurance.viewAccountConfig.viewAccount === 2;
            },
            // 选中了医保登记信息radio
            socialRegisterSelected() {
                return this.postDataPermission.medicalInsurance.viewBusinessRegistrationRecordConfig.viewBusinessRegistrationRecord === 2;
            },
            // 选中了医保机构资料数据radio
            socialMaintainSelected() {
                return this.postDataPermission.medicalInsurance.viewProfileDataConfig.viewProfileData === 2;
            },
            // 选中了医保设置radio
            socialSettingSelected() {
                return this.postDataPermission.medicalInsurance.viewSetupInformationConfig.viewSetupInformation === 2;
            },
            // 选中了挂号预约指定成员radio
            registrationPatientMobileSelected() {
                return this.postDataPermission.registration.patientMobile.value === 2;
            },
            // 选中了挂号预约修改支付方式指定成员radio
            registrationPayModeSelected() {
                return this.postDataPermission.registration.modifyPayMode.value === 2;
            },
            // 选中了挂号预约修改方式指定成员radio
            registrationModifySelected() {
                return this.postDataPermission.registration.modifyRegistration.value === 2;
            },
            // 选中了执行站指定成员radio
            nursePatientMobileSelected() {
                return this.postDataPermission.nurse.patientMobile.value === 2;
            },
            // 选中了门诊指定成员radio
            outpatientPatientMobileSelected() {
                return this.postDataPermission.outpatient.patientMobile.value === 2;
            },
            // 选中了门诊修改预约挂号方式指定成员radio
            outpatientModifyRegistrationSelected() {
                return this.postDataPermission.outpatient.modifyRegistration.value === 2;
            },
            // 选中了收费指定成员radio
            cashierPatientMobileSelected() {
                return this.postDataPermission.cashier.patientMobile.value === 2;
            },
            // 选中了收费指定支付方式radio
            cashierPayModeSelected() {
                return this.postDataPermission.cashier.modifyPayMode.value === 2;
            },
            // 选中了药房指定成员radio
            pharmacyPatientMobileSelected() {
                return this.postDataPermission.pharmacy.patientMobile.value === 2;
            },
            // 选中了患者指定成员radio
            crmPatientMobileSelected() {
                return this.postDataPermission.crm.patientMobile.value === 2;
            },
            // 首诊来源指定患者群体
            crmModifyFirstFromAwaySelected() {
                return this.postDataPermission.crm.modifyFirstFromAway.value === 2;
            },
            // 收费金额指定患者群体
            crmPatientPayAmountSelected() {
                return this.postDataPermission.crm.patientPayAmount.value === 2;
            },

            // 积分编辑指定成员
            crmPointsEditSelected() {
                return this.postDataPermission.crm.modifyPoint.value === 2;
            },

            // 优惠券编辑指定成员
            crmCouponEditSelected() {
                return this.postDataPermission.crm.modifyCoupon.value === 2;
            },
            // 修改患者姓名指定患者群体
            crmModifyNameSelected() {
                return this.postDataPermission.crm.modifyName.value === 2;
            },
            // 修改患者身份证号指定患者群体
            crmModifyIdCardSelected() {
                return this.postDataPermission.crm.modifyIdCard.value === 2;
            },
            // 修改患者档案号指定患者群体
            crmModifySnSelected() {
                return this.postDataPermission.crm.modifySn.value === 2;
            },
            microHomePageSelected() {
                return this.postDataPermission.microClinic.homePageConfig.value === 2;
            },
            currentMembers() {
                switch (this.membersSettingType) {
                    case 'inventoryCost':
                        return this.postDataPermission.inventory.goodsCostConfig.employees;
                    case 'inventoryProfit':
                        return this.postDataPermission.inventory.goodsProfitConfig.employees;
                    case 'inventoryCheckGoodsPrice':
                        return this.postDataPermission.inventory.checkGoodsPrice.employees;
                    case 'statisticsCost':
                        return this.postDataPermission.statistics.goodsCostConfig.employees;
                    case 'statisticsProfit':
                        return this.postDataPermission.statistics.goodsProfitConfig.employees;
                    case 'statisticsPatientMobile':
                        return this.postDataPermission.statistics.patientMobileConfig.employees;
                    case 'mallPrice':
                        return this.postDataPermission.mall.goodsPriceConfig.employees;
                    case 'mallPurchase':
                        return this.postDataPermission.mall.goodsPurchaseConfig.employees;
                    case 'socialSurvey':
                        return this.postDataPermission.medicalInsurance.viewHomePageConfig.employees;
                    case 'socialAccount':
                        return this.postDataPermission.medicalInsurance.viewAccountConfig.employees;
                    case 'socialRegister':
                        return this.postDataPermission.medicalInsurance.viewBusinessRegistrationRecordConfig.employees;
                    case 'socialMaintain':
                        return this.postDataPermission.medicalInsurance.viewProfileDataConfig.employees;
                    case 'socialSetting':
                        return this.postDataPermission.medicalInsurance.viewSetupInformationConfig.employees;
                    case 'registrationPatientMobile':
                        return this.postDataPermission.registration.patientMobile.employees;
                    case 'registrationPayMode':
                        return this.postDataPermission.registration.modifyPayMode.employees;
                    case 'registrationModify':
                        return this.postDataPermission.registration.modifyRegistration.employees;
                    case 'nursePatientMobile':
                        return this.postDataPermission.nurse.patientMobile.employees;
                    case 'outpatientPatientMobile':
                        return this.postDataPermission.outpatient.patientMobile.employees;
                    case 'outpatientModifyRegistration':
                        return this.postDataPermission.outpatient.modifyRegistration.employees;
                    case 'cashierPatientMobile':
                        return this.postDataPermission.cashier.patientMobile.employees;
                    case 'cashierPayMode':
                        return this.postDataPermission.cashier.modifyPayMode.employees;
                    case 'pharmacyPatientMobile':
                        return this.postDataPermission.pharmacy.patientMobile.employees;
                    case 'crmPatientMobile':
                        return this.postDataPermission.crm.patientMobile.employees;
                    case 'crmModifyFirstFromAway':
                        return this.postDataPermission.crm.modifyFirstFromAway.employees;
                    case 'crmModifyName':
                        return this.postDataPermission.crm.modifyName.employees;
                    case 'crmModifyIdCard':
                        return this.postDataPermission.crm.modifyIdCard.employees;
                    case 'crmModifySn':
                        return this.postDataPermission.crm.modifySn.employees;
                    case 'crmPatientPayAmount':
                        return this.postDataPermission.crm.patientPayAmount.employees;
                    case 'crmLevelEdit':
                        return this.postDataPermission.crm.levelEdit.employees;
                    case 'crmBalanceEdit':
                        return this.postDataPermission.crm.balanceEdit.employees;
                    case 'crmPointsEdit':
                        return this.postDataPermission.crm.modifyPoint.employees;
                    case 'crmCouponEdit':
                        return this.postDataPermission.crm.modifyCoupon.employees;
                    case 'microHomePage':
                        return this.postDataPermission.microClinic.homePageConfig.employees;
                    default:
                        return [];
                }
            },
            currentMembersDialogTitle() {
                const title = {
                    inventoryCost: '库存权限设置',
                    inventoryProfit: '库存权限设置',
                    inventoryCheckGoodsPrice: '库存权限设置',
                    statisticsCost: '统计权限设置',
                    statisticsProfit: '统计权限设置',
                    statisticsPatientMobile: '统计权限设置',
                    mallPrice: '商城权限设置',
                    mallPurchase: '商城权限设置',
                    socialSurvey: '医保权限设置',
                    socialAccount: '医保权限设置',
                    socialRegister: '医保权限设置',
                    socialMaintain: '医保权限设置',
                    socialSetting: '医保权限设置',
                    registrationPatientMobile: '挂号预约权限设置',
                    nursePatientMobile: '执行站权限设置',
                    outpatientPatientMobile: '门诊权限设置',
                    cashierPatientMobile: '收费权限设置',
                    pharmacyPatientMobile: '药房权限设置',
                    crmPatientMobile: '患者权限设置',
                    crmModifyFirstFromAway: '首诊来源设置',
                    crmModifyName: '患者姓名设置',
                    crmModifyIdCard: '患者身份证号设置',
                    crmModifySn: '患者档案号设置',
                    crmPatientPayAmount: '患者收费设置',
                    crmLevelEdit: '等级编辑权限设置',
                    crmBalanceEdit: '余额编辑权限设置',
                    crmPointsEdit: '积分编辑权限设置',
                    crmCouponEdit: '优惠券编辑权限设置',
                    microHomePage: '查看首页数据权限设置',
                };
                return title[this.membersSettingType];
            },
            textBeforeText() {
                if ([
                    'crmModifyFirstFromAway',
                    'crmModifyName',
                    'crmModifyIdCard',
                    'crmModifySn',
                    'registrationPayMode',
                    'registrationModify',
                    'outpatientModifyRegistration',
                    'cashierPayMode',
                ].includes(this.membersSettingType)) {
                    return '可修改';
                }
                return this.membersSettingType === 'mallPurchase' ? '可采购' : '可查看';
            },
            contentTextTips() {
                const text = {
                    mallPrice: '管理员和有采购权限的人员始终可查看',
                    mallPurchase: '管理员和有采购权限的人员始终可采购',
                };
                return text[this.membersSettingType] ?? '';
            },
            currentMembersSettingText() {
                const text = {
                    inventoryCost: '药品物资成本',
                    inventoryProfit: '药品物资毛利',
                    inventoryCheckGoodsPrice: '库存单据药品成本',
                    statisticsCost: '药品物资成本',
                    statisticsProfit: '药品物资毛利',
                    statisticsPatientMobile: '患者手机号',
                    mallPrice: '商品价格',
                    mallPurchase: '商品',
                    socialSurvey: '医保主页',
                    socialAccount: '医保账目',
                    socialRegister: '业务登记信息',
                    socialMaintain: '机构资料数据',
                    socialSetting: '医保设置信息',
                    crmModifyFirstFromAway: '首诊来源设置',
                    crmModifyName: '患者姓名设置',
                    crmModifyIdCard: '患者身份证号设置',
                    crmModifySn: '患者档案号设置',
                    crmPatientPayAmount: '患者收费金额',
                    crmLevelEdit: '等级编辑权限',
                    crmBalanceEdit: '余额编辑权限',
                    crmPointsEdit: '积分编辑权限',
                    crmCouponEdit: '优惠券编辑权限',
                    cashierPayMode: '支付方式',
                    registrationPayMode: '支付方式',
                    registrationModify: '挂号预约',
                    outpatientModifyRegistration: '门诊挂号预约',
                };
                if (['registrationPatientMobile', 'nursePatientMobile', 'outpatientPatientMobile', 'cashierPatientMobile',
                     'pharmacyPatientMobile', 'crmPatientMobile'].includes(this.membersSettingType)) {
                         return '患者手机号';
                     }
                return text?.[this.membersSettingType] ?? '';
            },
            // 页面数据是否发生变化
            isUpdated() {
                return !isEqual(this.dataPermission, this.postDataPermission);
            },

            // 是否已经购买
            b2bMallIsPurchased() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.B2B_MALL);
            },

            inventoryBusinessDesc() {
                return this.viewDistributeConfig?.Inventory?.isSupportProductionOut ? '出入库权限：领用、调拨、报损、盘点、生产出库' : '出入库权限：领用、调拨、报损、盘点';
            },
        },
        watch: {
            dataPermission: {
                handler(value) {
                    console.log('watcher', value);
                    this.postDataPermission = Clone(value);
                },
                immediate: true,
                deep: true,
            },
        },
        created() {
            this.fetchPermissionConfig();
        },
        methods: {
            ...mapActions([
                'fetchDataPermission',
                'updateDataPermission',
            ]),

            async fetchPermissionConfig() {
                this.loading = true;
                await this.fetchDataPermission().catch((e) => {
                    console.log(e);
                });
                this.loading = false;
            },

            async submit() {
                // 库存和统计的如果指定成员不能一个人都不选择

                if (this.inventoryCostSelected && this.postDataPermission.inventory.goodsCostConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看库存药品物资成本成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.inventoryProfitSelected && this.postDataPermission.inventory.goodsProfitConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看库存药品物资毛利成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.inventoryCheckGoodsPriceSelected && this.postDataPermission.inventory.checkGoodsPrice.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看领用、调拨、保存、盘点金额成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.statisticsCostSelected && this.postDataPermission.statistics.goodsCostConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看统计药品物资成本成员',
                        type: 'info',
                    });
                    return false;
                }


                if (this.statisticsProfitSelected && this.postDataPermission.statistics.goodsProfitConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看统计药品物资毛利成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.statisticsPatientMobileSelected && this.postDataPermission.statistics.patientMobileConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看统计查看患者手机号成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.lookGoodsPriceSelected && this.postDataPermission.mall.goodsPriceConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看商品价格成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.goodsPurchaseSelected && this.postDataPermission.mall.goodsPurchaseConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择采购商品成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.socialSurveySelected && this.postDataPermission.medicalInsurance.viewHomePageConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择医保主页成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.socialAccountSelected && this.postDataPermission.medicalInsurance.viewAccountConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择医保账目成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.socialRegisterSelected && this.postDataPermission.medicalInsurance.viewBusinessRegistrationRecordConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择医保登记信息成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.socialMaintainSelected && this.postDataPermission.medicalInsurance.viewProfileDataConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择医保机构资料成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.socialSettingSelected && this.postDataPermission.medicalInsurance.viewSetupInformationConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择医保设置成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmModifyFirstFromAwaySelected && this.postDataPermission.crm.modifyFirstFromAway.employees.length === 0) {
                    this.$Toast({
                        message: '请选择首诊来源可编辑成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmModifyNameSelected && this.postDataPermission.crm.modifyName.employees.length === 0) {
                    this.$Toast({
                        message: '请选择患者姓名可编辑成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmModifyIdCardSelected && this.postDataPermission.crm.modifyIdCard.employees.length === 0) {
                    this.$Toast({
                        message: '请选择患者身份证号可编辑成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmModifySnSelected && this.postDataPermission.crm.modifySn.employees.length === 0) {
                    this.$Toast({
                        message: '请选择患者档案号可编辑成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmPatientPayAmountSelected && this.postDataPermission.crm.patientPayAmount.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看患者收费金额成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmPointsEditSelected && this.postDataPermission.crm.modifyPoint.employees.length === 0) {
                    this.$Toast({
                        message: '请选择积分编辑权限成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmCouponEditSelected && this.postDataPermission.crm.modifyCoupon.employees.length === 0) {
                    this.$Toast({
                        message: '请选择优惠券编辑权限成员',
                        type: 'info',
                    });
                    return false;
                }

                try {
                    this.loading = true;
                    await this.updateDataPermission(this.postDataPermission);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (e) {
                    this.$Toast({
                        message: '保存失败',
                        type: 'error',
                    });
                } finally {
                    this.loading = false;
                }
            },
            /*
             * @param {number} type
             * 1: 库存-查看药品物资成本-指定成员可查看
             * 2: 库存-查看药品物资毛利-指定成员可查看
             * 3: 统计-查看药品物资成本-指定成员可查看
             * 4: 统计-查看药品物资毛利-指定成员可查看
             * */
            openMemberSettingDialog(type) {
                this.showMembersSettingDialog = true;
                this.membersSettingType = type;
            },
            onCancel() {
                this.showMembersSettingDialog = false;
                this.membersSettingType = '';
            },
            onConfirm(members) {
                switch (this.membersSettingType) {
                    case 'inventoryCost':
                        // this.inventoryCostMembers = members;
                        this.postDataPermission.inventory.goodsCostConfig.employees = members;
                        break;
                    case 'inventoryProfit':
                        // this.inventoryProfitMembers = members;
                        this.postDataPermission.inventory.goodsProfitConfig.employees = members;
                        break;
                    case 'statisticsCost':
                        // this.statisticsCostMembers = members;
                        this.postDataPermission.statistics.goodsCostConfig.employees = members;
                        break;
                    case 'statisticsProfit':
                        // this.statisticsProfitMembers = members;
                        this.postDataPermission.statistics.goodsProfitConfig.employees = members;
                        break;
                    case 'statisticsPatientMobile':
                        // this.statisticsProfitMembers = members;
                        this.postDataPermission.statistics.patientMobileConfig.employees = members;
                        break;
                    case 'mallPrice':
                        this.postDataPermission.mall.goodsPriceConfig.employees = members;
                        break;
                    case 'mallPurchase':
                        this.postDataPermission.mall.goodsPurchaseConfig.employees = members;
                        break;
                    case 'socialSurvey':
                        this.postDataPermission.medicalInsurance.viewHomePageConfig.employees = members;
                        break;
                    case 'socialAccount':
                        this.postDataPermission.medicalInsurance.viewAccountConfig.employees = members;
                        break;
                    case 'socialRegister':
                        this.postDataPermission.medicalInsurance.viewBusinessRegistrationRecordConfig.employees = members;
                        break;
                    case 'socialMaintain':
                        this.postDataPermission.medicalInsurance.viewProfileDataConfig.employees = members;
                        break;
                    case 'socialSetting':
                        this.postDataPermission.medicalInsurance.viewSetupInformationConfig.employees = members;
                        break;
                    case 'registrationPatientMobile':
                        this.postDataPermission.registration.patientMobile.employees = members;
                        break;
                    case 'registrationPayMode':
                        this.postDataPermission.registration.modifyPayMode.employees = members;
                        break;
                    case 'registrationModify':
                        this.postDataPermission.registration.modifyRegistration.employees = members;
                        break;
                    case 'nursePatientMobile':
                        this.postDataPermission.nurse.patientMobile.employees = members;
                        break;
                    case 'outpatientPatientMobile':
                        this.postDataPermission.outpatient.patientMobile.employees = members;
                        break;
                    case 'outpatientModifyRegistration':
                        this.postDataPermission.outpatient.modifyRegistration.employees = members;
                        break;
                    case 'cashierPatientMobile':
                        this.postDataPermission.cashier.patientMobile.employees = members;
                        break;
                    case 'cashierPayMode':
                        this.postDataPermission.cashier.modifyPayMode.employees = members;
                        break;
                    case 'pharmacyPatientMobile':
                        this.postDataPermission.pharmacy.patientMobile.employees = members;
                        break;
                    case 'crmPatientMobile':
                        this.postDataPermission.crm.patientMobile.employees = members;
                        break;
                    case 'crmModifyFirstFromAway':
                        this.postDataPermission.crm.modifyFirstFromAway.employees = members;
                        break;
                    case 'crmPatientPayAmount':
                        this.postDataPermission.crm.patientPayAmount.employees = members;
                        break;
                    case 'crmLevelEdit':
                        this.postDataPermission.crm.levelEdit.employees = members;
                        break;
                    case 'crmBalanceEdit':
                        this.postDataPermission.crm.balanceEdit.employees = members;
                        break;
                    case 'crmPointsEdit':
                        this.postDataPermission.crm.modifyPoint.employees = members;
                        break;
                    case 'crmCouponEdit':
                        this.postDataPermission.crm.modifyCoupon.employees = members;
                        break;
                    case 'crmModifyName':
                        this.postDataPermission.crm.modifyName.employees = members;
                        break;
                    case 'crmModifyIdCard':
                        this.postDataPermission.crm.modifyIdCard.employees = members;
                        break;
                    case 'crmModifySn':
                        this.postDataPermission.crm.modifySn.employees = members;
                        break;
                    case 'inventoryCheckGoodsPrice':
                        this.postDataPermission.inventory.checkGoodsPrice.employees = members;
                        this.postDataPermission.inventory.damageGoodsPrice.employees = members;
                        this.postDataPermission.inventory.obtainGoodsPrice.employees = members;
                        this.postDataPermission.inventory.transGoodsPrice.employees = members;
                        this.postDataPermission.inventory.productionGoodsPrice.employees = members;
                        break;
                    case 'microHomePage':
                        this.postDataPermission.microClinic.homePageConfig.employees = members;
                        break;
                    default:
                        break;
                }
                this.onCancel();
            },

            handleInventoryViewGoodsPriceChange(v) {
                // 后台的权限根据模块分开，在这里选代表全选，所有领用、调拨、盘点、报损都要赋值
                this.postDataPermission.inventory.checkGoodsPrice.value = v;
                this.postDataPermission.inventory.damageGoodsPrice.value = v;
                this.postDataPermission.inventory.obtainGoodsPrice.value = v;
                this.postDataPermission.inventory.transGoodsPrice.value = v;
                this.postDataPermission.inventory.productionGoodsPrice.value = v;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import "src/styles/theme.scss";
@import "./_index.scss";

.data-permission-setting-wrapper {
    .data-permission-setting-item {
        width: 220px;
    }
}
</style>
