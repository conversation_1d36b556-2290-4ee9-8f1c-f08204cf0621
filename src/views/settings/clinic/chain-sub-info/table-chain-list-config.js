import BaseProTable from '@/views/layout/tables/base-pro-table.js';

export default class ChainListConfigTable extends BaseProTable {
    name = 'ChainListConfigTable';

    // 由产品.设计提供的静态配置, 开发只能修改key
    static staticConfig = {
        hasInnerBorder: true,
        list: [
            {
                key: 'name',
                label: '门店名称',
                pinned: true,
                style: {
                    width: '300px',
                    minWidth: '300px',
                    textAlign: 'left',
                },

            }, {
                key: 'busMode',
                label: '类型',
                style: {
                    width: '80px',
                    minWidth: '80px',
                    textAlign: 'left',
                },

            }, {
                key: 'addressDetail',
                label: '门店地址',
                style: {
                    width: '300px',
                    minWidth: '300px',
                    textAlign: 'left',
                },
            }, {
                key: 'contactPhone',
                label: '联系电话',
                style: {
                    width: '120px',
                    minWidth: '120px',
                    textAlign: 'left',
                },
            }, {
                key: 'businessScope',
                label: '经营范围',
                style: {
                    width: '330px',
                    minWidth: '330px',
                    textAlign: 'left',
                },
            },
            {
                key: 'manageChainGoods',
                label: '管理连锁商品',
                style: {
                    width: '204px',
                    minWidth: '204px',
                    textAlign: 'center',
                },
                children: [{
                    key: 'add',
                    label: '新建',
                    style: {
                        width: '68px',
                        minWidth: '68px',
                        maxWidth: '68px',
                        textAlign: 'center',
                    },
                }, {
                    key: 'modify',
                    label: '修改',
                    style: {
                        width: '68px',
                        minWidth: '68px',
                        maxWidth: '68px',
                        textAlign: 'center',
                    },
                }, {
                    key: 'delete',
                    label: '删除',
                    style: {
                        width: '68px',
                        minWidth: '68px',
                        maxWidth: '68px',
                        textAlign: 'center',
                    },
                }],
            },
            {
                key: 'subClinicPrice',
                label: '门店定价',
                style: {
                    width: '88px',
                    minWidth: '88px',
                    maxWidth: '88px',
                    textAlign: 'center',
                },
            },
        ],
    };
}
