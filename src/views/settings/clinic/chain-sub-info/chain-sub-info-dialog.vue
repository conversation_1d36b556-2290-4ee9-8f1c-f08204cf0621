<template>
    <abc-dialog
        v-if="showChainInfoDialog"
        v-model="showChainInfoDialog"
        :title="postData.name"
        size="huge"
        append-to-body
        :auto-focus="false"
        disabled-keyboard
    >
        <abc-form ref="form" item-no-margin style="height: 100%;">
            <abc-layout>
                <abc-flex vertical :gap="24">
                    <abc-flex :gap="16">
                        <abc-option-card
                            :key="1"
                            :value="postData.busMode === 1"
                            selectable="icon-inside"
                            :show-icon="false"
                            title="直营店"
                            :width="150"
                            style="overflow: hidden; text-align: center;"
                            @change="handleModeCardClick(1)"
                        ></abc-option-card>

                        <abc-option-card
                            :key="2"
                            :value="postData.busMode === 2"
                            selectable="icon-inside"
                            :show-icon="false"
                            title="加盟店"
                            :width="150"
                            style="overflow: hidden; text-align: center;"
                            @change="handleModeCardClick(2)"
                        ></abc-option-card>
                    </abc-flex>

                    <abc-flex vertical :gap="8">
                        <abc-text bold>
                            基本信息
                        </abc-text>
                        <abc-descriptions
                            :column="3"
                            :label-width="56"
                            size="small"
                            disabled
                            stretch-last-item
                        >
                            <abc-descriptions-item label="门店名称">
                                {{ postData.name }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="门店简称">
                                {{ postData.shortName || '-' }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="信用代码">
                                {{ postData.creditCode || '-' }}
                            </abc-descriptions-item>

                            <abc-descriptions-item label="联系电话">
                                {{ contactPhoneWording || '-' }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="门店地址" :span="2">
                                {{ chainAddress || '-' }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="经营范围" :span="3">
                                {{ chainSubInfoBusModeName || '-' }}
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-flex>
                    <abc-flex v-if="certificationInfos?.length" vertical :gap="8">
                        <abc-text bold>
                            证照资质
                        </abc-text>
                        <abc-row :gutter="[8, 8]" :wrap="'wrap'" class="supplier-archives-info">
                            <abc-col v-for="(cert, index) in certificationInfos" :key="cert.id" :span="6">
                                <cert-item
                                    :width="222"
                                    :disabled="true"
                                    :item.sync="certificationInfos[index]"
                                    style="margin: 0; background: var(--abc-color-cp-white);"
                                ></cert-item>
                            </abc-col>
                        </abc-row>
                    </abc-flex>
                    <abc-flex vertical :gap="8">
                        <abc-text bold>
                            门店权限设置
                        </abc-text>
                        <abc-table
                            type="excel"
                            empty-size="small"
                            :show-hover-tr-bg="false"
                            :show-checked="false"
                            child-key="list"
                            :data-list="postData.permissions"
                            :render-config="renderTableHeader"
                        >
                            <template
                                #auth="{
                                    trData: row, parentData, cellRowSpan
                                }"
                            >
                                <template v-if="row.key === 'goodsAdjustPriceConfig'">
                                    <abc-table-cell>
                                        {{ row.auth }}
                                    </abc-table-cell>
                                </template>
                                <template v-else>
                                    <abc-table-cell
                                        v-if="!parentData"
                                        class="ellipsis"
                                        :cell-row-span="cellRowSpan"
                                        align="center"
                                        style="gap: 4px;"
                                    >
                                        <span>{{ row.auth }}</span>
                                        <abc-tooltip-info placement="top-end">
                                            <span>新建、修改、删除连锁商品档案对全部门店生效，请谨慎授权</span>
                                        </abc-tooltip-info>
                                    </abc-table-cell>
                                </template>
                            </template>

                            <template #isAllow="{ trData: row }">
                                <abc-table-cell
                                    align="center"
                                >
                                    <abc-switch
                                        v-model="postData[row.key].isAllow"
                                        type="number"
                                        @change="handleSwitchChange(row)"
                                    ></abc-switch>
                                </abc-table-cell>
                            </template>

                            <template #employees="{ trData: row }">
                                <abc-form-item :validate-event="(_, callback)=>validateEmployees(row.key, callback)">
                                    <template v-if="postData[row.key].isAllow && row.selectedValue === 2">
                                        <abc-input
                                            v-if="row.employees.length"
                                            readonly
                                            :input-custom-style="{ textAlign: 'center' }"
                                            :value="formatEmployees(row.employees)"
                                            @click="handleOpenAddMembersDialog(row)"
                                        >
                                        </abc-input>
                                        <abc-input
                                            v-else
                                            placeholder="设置操作人"
                                            readonly
                                            :input-custom-style="{ textAlign: 'center' }"
                                            @click="handleOpenAddMembersDialog(row)"
                                        >
                                        </abc-input>
                                    </template>
                                    <template v-else-if="postData[row.key].isAllow && row.selectedValue === 3">
                                        <abc-input
                                            readonly
                                            :input-custom-style="{ textAlign: 'center' }"
                                            value="管理员/店长"
                                            @click="handleOpenAddMembersDialog(row)"
                                        >
                                        </abc-input>
                                    </template>
                                    <template v-else>
                                        <abc-input
                                            v-if="postData[row.key].isAllow"
                                            placeholder="设置操作人"
                                            readonly
                                            :input-custom-style="{ textAlign: 'center' }"
                                            @click="handleOpenAddMembersDialog(row)"
                                        >
                                        </abc-input>
                                    </template>
                                </abc-form-item>
                            </template>
                            <template #rule="{ trData: row }">
                                <abc-form-item style="width: 100%;">
                                    <abc-input
                                        v-if="row.key === 'goodsAdjustPriceConfig'"
                                        readonly
                                        adaptive-width
                                        :value="formatRule(row)"
                                        :input-custom-style="{ textAlign: 'center' }"
                                        @click="showPriceRangeDialog = true"
                                    ></abc-input>
                                </abc-form-item>
                            </template>
                        </abc-table>
                    </abc-flex>
                </abc-flex>
            </abc-layout>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button :loading="btnLoading" :disabled="disabledButton" @click="submit">
                确定
            </abc-button>
            <abc-button type="blank" @click="closeDialog">
                取消
            </abc-button>
        </div>
        <!--    图片预览    -->
        <abc-preview
            v-if="showPreview"
            v-model="showPreview"
            :index="0"
            :lists="imgList"
        ></abc-preview>

        <price-range-dialog
            v-model="showPriceRangeDialog"
        ></price-range-dialog>

        <!--    添加成员弹窗    -->
        <abc-dialog
            v-if="showAddMembersDialog"
            v-model="showAddMembersDialog"
            :title="addMemberDialogTitle"
            append-to-body
            size="medium"
            disabled-keyboard
            @open="openAddMembersDialog"
            @close="closeAddMembersDialog"
        >
            <abc-radio-group
                v-model="selectedValue"
                data-cy="storybook-test-default"
                :item-block="true"
                style="margin-bottom: 4px;"
                gap="12px"
            >
                <abc-radio
                    :label="3"
                >
                    管理员/店长
                </abc-radio>
                <abc-radio
                    :label="2"
                >
                    指定成员
                </abc-radio>
            </abc-radio-group>

            <biz-employee-panel-selector
                v-if="selectedValue === 2"
                v-model="selectedEmployees"
                style="padding-left: 24px;"
                :employees="employeeList"
                :is-deleting="isDeleteEmployee"
                :default-checked-keys="defaultCheckedKeys"
                @update:is-deleting="isDeleteEmployee = $event"
            ></biz-employee-panel-selector>
            <div slot="footer" class="dialog-footer">
                <abc-button :disabled="!selectedValue" @click="confirmSelect">
                    确定
                </abc-button>
                <abc-button type="blank" @click="showAddMembersDialog = false">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
        <!--    门店自主定价确认弹窗    -->
        <abc-dialog
            v-if="showConfirmDialog"
            v-model="showConfirmDialog"
            :auto-focus="false"
            :show-close="false"
            disabled-keyboard
            size="medium"
            title="门店自主定价确认"
        >
            <div>
                <abc-text>
                    {{ postData.name }}将不允许自主定价，请确认门店已自主定价商品的价格：
                </abc-text>
            </div>
            <abc-radio-group
                v-model="currentClearFlag"
                gap="12px"
                style="flex-direction: column; margin-top: 12px;"
            >
                <biz-setting-form-item-tip tip="商品价格由总部定，始终跟随总部价">
                    <abc-radio :label="1">
                        调整为总部价
                    </abc-radio>
                </biz-setting-form-item-tip>
                <biz-setting-form-item-tip tip="商品价格由总部定，总部单独设置门店价">
                    <abc-radio :label="0">
                        维持原价
                    </abc-radio>
                </biz-setting-form-item-tip>
            </abc-radio-group>
            <abc-flex slot="footer" justify="flex-end">
                <abc-button
                    @click="showConfirmDialog = false"
                >
                    确定
                </abc-button>
                <abc-button type="blank" @click="handleCancel">
                    取消
                </abc-button>
            </abc-flex>
        </abc-dialog>
    </abc-dialog>
</template>
<script>
    import { mapGetters } from 'vuex';
    import ClinicAPI from 'api/clinic.js';
    import SettingAPI from 'api/settings';
    import * as tools from '@/views-pharmacy/common/tools';
    import Clone from 'utils/clone';
    import { createGUID } from 'utils/guid';
    import { isEqual } from '@abc/utils';
    import CertItem from '@/views-pharmacy/gsp/frames/first-battalion/supplier/dialog-supplier-detail/cert-item.vue';
    import BizEmployeePanelSelector from '@/components-composite/biz-employee-panel-selector';
    import PriceRangeDialog from 'views/settings/clinic/chain-sub-info/price-range-dialog.vue';
    import {
        BizSettingFormItemTip,
    } from '@/components-composite/setting-form';
    export default {
        name: 'ChainSubInfoDialog',
        components: {
            PriceRangeDialog,
            CertItem,
            BizSettingFormItemTip,
            BizEmployeePanelSelector,
        },
        props: {
            value: Boolean,
            // eslint-disable-next-line vue/require-default-prop
            chainSubInfo: {
                require: true,
                type: Object,
            },
        },
        data() {
            return {
                showAddMembersDialog: false,
                showPriceRangeDialog: false,
                showConfirmDialog: false,// 门店自主定价确认弹窗
                showPreview: false, // 预览图片
                btnLoading: false,
                busModeOptions: [
                    {
                        label: '直营',
                        value: 1,
                    },
                    {
                        label: '加盟',
                        value: 2,
                    },
                ],
                minPricePercent: '',
                maxPricePercent: '',
                imgList: [],
                postData: {},
                catchPostData: {},
                currentClearFlag: 0,// 0 维持原价 1 调整为总部价
                isDeleteEmployee: false,
                // 选中的行数据
                currentRow: null,
                // 选中的radio
                selectedValue: 0,
                // 选中的成员
                selectedEmployees: [],
                // 远程获取的全部成员
                employeeList: [],
            };
        },
        computed: {
            ...mapGetters(['goodsConfig']),
            certificationInfos() {
                const list = [];
                const certMap = {
                    businessLicense: {
                        name: '营业执照',
                        sort: 1,
                    },
                    drugCert: {
                        name: '药品经营许可证',
                        sort: 2,
                    },
                    idCardFront: {
                        name: '法人身份证人像面',
                        sort: 3,
                    },
                    idCardBackend: {
                        name: '法人身份证国徽面',
                        sort: 4,
                    },
                    otherCerts: {
                        name: '其他证照',
                        sort: 5,
                    },
                };
                Object.entries(this.postData.certs || {}).forEach(([key, value]) => {
                    if (value?.url) {
                        const {
                            name, sort,
                        } = certMap[key];

                        list.push({
                            name,
                            sort,
                            no: value.code,
                            validTo: value.expiredAt,
                            pictureUrls: [{
                                url: value.url,
                            }],
                        });
                    }

                });
                return list.sort((a, b) => a.sort - b.sort);
            },
            renderTableHeader() {
                return {
                    hasInnerBorder: true,
                    list: [
                        {
                            key: 'auth',
                            label: '权限',
                            style: {
                                width: '150px',
                                minWidth: '150px',
                                maxWidth: '150px',
                            },
                            rowSpan: (trData) => {
                                if (trData.list?.length) {
                                    return trData.list.length + 1;
                                }
                                return 1;
                            },
                        },
                        {
                            key: 'subAuth',
                            label: '二级权限',
                            style: {
                                width: '150px',
                                minWidth: '150px',
                                maxWidth: '150px',
                            },
                        },
                        {
                            key: 'isAllow',
                            label: '允许门店操作',
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                textAlign: 'center',
                            },
                        },
                        {
                            key: 'employees',
                            label: '操作人',
                            style: {
                                width: '150px',
                                minWidth: '150px',
                                maxWidth: '150px',
                                textAlign: 'center',
                            },
                        },
                        {
                            key: 'rule',
                            label: '规则描述',
                            style: {
                                width: '350px',
                                minWidth: '350px',
                                textAlign: 'center',
                            },
                        },
                    ],
                };
            },
            labelStyle() {
                return {
                    display: 'flex',
                    alignItems: 'center',
                };
            },
            showChainInfoDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            chainAddress() {
                const {
                    addressProvinceName, addressCityName, addressDistrictName, addressDetail,
                } = this.chainSubInfo;
                return `${addressProvinceName ?? ''}${addressCityName ?? ''}${addressDistrictName ?? ''}${addressDetail ?? ''}`;
            },
            chainSubInfoBusModeName() {
                return tools.getBusinessScopeName(this.chainSubInfo.businessScope);
            },
            contactPhoneWording() {
                return tools.getContactPhoneWording(
                    this.chainSubInfo.contactPhone,
                    this.chainSubInfo.backupContactPhone,
                );
            },
            certs() {
                return this.chainSubInfo.certs || {};
            },
            drugCert() {
                return this.certs.drugCert || {};
            },
            idCardBackend() {
                return this.certs.idCardBackend || {};
            },
            idCardFront() {
                return this.certs.idCardFront || {};
            },
            businessLicense() {
                return this.certs.businessLicense || {};
            },
            otherCerts() {
                return this.certs.otherCerts || [];
            },
            disabledButton() {
                return isEqual(this.postData, this.catchPostData);
            },
            addMemberDialogTitle() {
                const obj = {
                    goodsCreateArchivesConfig: '设置新建档案操作人',
                    goodsModifyArchivesConfig: '设置修改档案操作人',
                    goodsDeleteArchivesConfig: '设置删除档案操作人',
                    goodsAdjustPriceConfig: '设置自主定价操作人',
                };
                return obj[this.currentRow?.key] ?? '设置操作人';
            },
            defaultCheckedKeys() {
                return this.selectedEmployees.map((item) => item.id);
            },
        },
        watch: {
            goodsConfig: {
                handler(val) {
                    this.init(val);
                },
                deep: true,
                immediate: true,
            },
            chainSubInfo: {
                handler(val) {
                    const goodsCreateArchivesConfig = val.clinicGoodsPermission?.goodsCreateArchivesConfig ?? {
                        goodsArchives: 0,
                        employees: [],
                        roles: [],
                    };
                    const goodsModifyArchivesConfig = val.clinicGoodsPermission?.goodsModifyArchivesConfig ?? {
                        goodsArchives: 0,
                        employees: [],
                        roles: [],
                    };
                    const goodsDeleteArchivesConfig = val.clinicGoodsPermission?.goodsDeleteArchivesConfig ?? {
                        goodsArchives: 0,
                        employees: [],
                        roles: [],
                    };
                    const goodsAdjustPriceConfig = val.clinicGoodsPermission?.goodsAdjustPriceConfig ?? {
                        goodsAdjustPrice: 0,
                        employees: [],
                        roles: [],
                    };

                    goodsCreateArchivesConfig.isAllow = Number(!!val.clinicGoodsPermission?.goodsCreateArchivesConfig);
                    goodsModifyArchivesConfig.isAllow = Number(!!val.clinicGoodsPermission?.goodsModifyArchivesConfig);
                    goodsDeleteArchivesConfig.isAllow = Number(!!val.clinicGoodsPermission?.goodsDeleteArchivesConfig);
                    goodsAdjustPriceConfig.isAllow = Number(!!val.clinicGoodsPermission?.goodsAdjustPriceConfig);

                    this.postData = Clone({
                        ...val,
                        goodsCreateArchivesConfig,
                        goodsModifyArchivesConfig,
                        goodsDeleteArchivesConfig,
                        goodsAdjustPriceConfig,
                        permissions: [
                            {
                                key: 'goodsCreateArchivesConfig',
                                keyId: createGUID(),
                                auth: '管理连锁商品',
                                subAuth: '新建商品档案',
                                selectedValue: goodsCreateArchivesConfig?.goodsArchives,
                                employees: goodsCreateArchivesConfig?.employees,
                                roles: goodsCreateArchivesConfig?.roles,
                                list: [
                                    {
                                        key: 'goodsModifyArchivesConfig',
                                        keyId: createGUID(),
                                        auth: '管理连锁商品',
                                        subAuth: '修改商品档案',
                                        selectedValue: goodsModifyArchivesConfig?.goodsArchives,
                                        employees: goodsModifyArchivesConfig?.employees,
                                        roles: goodsModifyArchivesConfig?.roles,
                                    },
                                    {
                                        key: 'goodsDeleteArchivesConfig',
                                        keyId: createGUID(),
                                        auth: '管理连锁商品',
                                        subAuth: '删除商品档案',
                                        selectedValue: goodsDeleteArchivesConfig?.goodsArchives,
                                        employees: goodsDeleteArchivesConfig?.employees,
                                        roles: goodsDeleteArchivesConfig?.roles,
                                    },
                                ],
                            },
                            {
                                key: 'goodsAdjustPriceConfig',
                                keyId: createGUID(),
                                auth: '定价',
                                subAuth: '自主定价',
                                selectedValue: goodsAdjustPriceConfig?.goodsAdjustPrice,
                                employees: goodsAdjustPriceConfig?.employees,
                                roles: goodsAdjustPriceConfig?.roles,
                            },

                        ],
                    });
                    this.catchPostData = Clone(this.postData);
                },
                deep: true,
                immediate: true,
            },
        },
        created() {
        },
        methods: {
            async init(data) {
                this.minPricePercent = data.subClinicPrice?.minPricePercent ?? '';
                this.maxPricePercent = data?.subClinicPrice?.maxPricePercent ?? '';
            },
            imageClick(item) {
                this.imgList = [{
                    url: item,
                }];
                this.showPreview = true;
            },
            closeDialog() {
                this.showChainInfoDialog = false;
            },
            closeAddMembersDialog() {
                this.currentRow = null;
                this.selectedEmployees = [];
            },
            async openAddMembersDialog() {
                try {
                    this.selectedValue = this.currentRow?.selectedValue;
                    this.selectedEmployees = this.currentRow?.employees?.slice?.() ?? [];

                    // 获取当前操作门店全部成员
                    // clinicid全小写，后端参数写错了不敢改了
                    const { data } = await ClinicAPI.fetchChainEmployeesByOutPatient({ clinicid: this.postData.id });
                    // 勾选列表
                    this.employeeList = (data?.rows ?? []);
                } catch (e) {
                    console.error(e);
                }
            },
            handleOpenAddMembersDialog(row) {
                this.showAddMembersDialog = true;
                this.currentRow = row;
            },
            formatEmployees(employees) {
                if (!employees.length) {
                    return '';
                }
                if (employees.length === 1) {
                    return `${employees[0].name}`;
                }
                if (employees.length === 2) {
                    return `${employees[0].name}、${employees[1].name}`;
                }
                if (employees.length > 2) {
                    return `${employees[0].name} 等${employees.length}人`;
                }
                return '';
            },
            validateEmployees(key, callback) {
                if (key === 'goodsCreateArchivesConfig' && this.postData.goodsCreateArchivesConfig.isAllow && !this.postData.goodsCreateArchivesConfig.goodsArchives) {
                    callback({
                        message: '请设置新建商品档案操作人',
                        validate: false,
                    });
                    return false;
                }
                if (key === 'goodsModifyArchivesConfig' && this.postData.goodsModifyArchivesConfig.isAllow && !this.postData.goodsModifyArchivesConfig.goodsArchives) {
                    callback({
                        message: '请设置修改商品档案操作人',
                        validate: false,
                    });
                    return false;
                }
                if (key === 'goodsDeleteArchivesConfig' && this.postData.goodsDeleteArchivesConfig.isAllow && !this.postData.goodsDeleteArchivesConfig.goodsArchives) {
                    callback({
                        message: '请设置删除商品档案操作人',
                        validate: false,
                    });
                    return false;
                }
                if (key === 'goodsAdjustPriceConfig' && this.postData.goodsAdjustPriceConfig.isAllow && !this.postData.goodsAdjustPriceConfig.goodsAdjustPrice) {
                    callback({
                        message: '请设置自主定价操作人',
                        validate: false,
                    });
                    return false;
                }

                callback({
                    validate: true,
                });
            },
            confirmSelect() {
                console.log('confirmSelect',this.currentRow, this.selectedEmployees);
                if (this.selectedValue === 2) {
                    if (!this.selectedEmployees.length) {
                        this.$Toast({
                            message: '请选择成员',
                            type: 'info',
                        });
                        return;
                    }
                    this.currentRow.employees = this.selectedEmployees.slice();
                    this.postData[this.currentRow.key].employees = this.currentRow.employees;
                }

                // radio值保存
                this.currentRow.selectedValue = this.selectedValue;
                if (this.currentRow.key === 'goodsAdjustPriceConfig') {
                    this.postData[this.currentRow.key].goodsAdjustPrice = this.selectedValue;
                } else {
                    this.postData[this.currentRow.key].goodsArchives = this.selectedValue;
                }
                this.showAddMembersDialog = false;
            },
            handleModeCardClick(key) {
                if (this.postData.busMode === key) return;
                this.postData.busMode = key;
                this.postData.goodsCreateArchivesConfig.isAllow = +(key === 2);
                this.postData.goodsModifyArchivesConfig.isAllow = +(key === 2);
                this.postData.goodsDeleteArchivesConfig.isAllow = +(key === 2);
                this.postData.goodsAdjustPriceConfig.isAllow = +(key === 2);
            },
            handleSwitchChange(row) {
                if (row.key === 'goodsAdjustPriceConfig' && !this.postData[row.key].isAllow) {
                    this.showConfirmDialog = true;
                }
            },
            formatRule(row) {
                if (row.key === 'goodsAdjustPriceConfig') {
                    return `自主定价范围：总部定价的${this.minPricePercent}%-${this.maxPricePercent}%`;
                }
                return '';
            },
            handleCancel() {
                this.showConfirmDialog = false;
                this.currentClearFlag = 0;
                this.postData.goodsAdjustPriceConfig.isAllow = 1;
            },
            // 提交修改
            async submit() {
                if (!this.chainSubInfo?.id) return;
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        this.btnLoading = true;
                        try {
                            await SettingAPI.chainInfo.updateChainInfo(this.postData.id, this.postData.busMode, {
                                goodsCreateArchivesConfig: this.postData.goodsCreateArchivesConfig.isAllow ? this.postData.goodsCreateArchivesConfig : null,
                                goodsModifyArchivesConfig: this.postData.goodsModifyArchivesConfig.isAllow ? this.postData.goodsModifyArchivesConfig : null,
                                goodsDeleteArchivesConfig: this.postData.goodsDeleteArchivesConfig.isAllow ? this.postData.goodsDeleteArchivesConfig : null,
                                goodsAdjustPriceConfig: this.postData.goodsAdjustPriceConfig.isAllow ? this.postData.goodsAdjustPriceConfig : null,
                                clearFlag: this.postData.goodsAdjustPriceConfig.isAllow ? undefined : this.currentClearFlag,
                            });
                            this.$Toast({
                                message: '保存成功',
                                type: 'success',
                            });
                            this.showChainInfoDialog = false;
                            this.$emit('refresh');
                        } catch (err) {
                            console.log('updateChainInfo', err);
                        } finally {
                            this.btnLoading = false;
                        }
                    }
                });
            },
        },
    };
</script>
