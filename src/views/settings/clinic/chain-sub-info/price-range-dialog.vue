<template>
    <abc-dialog
        v-if="showPriceRangeDialog"
        v-model="showPriceRangeDialog"
        :auto-focus="false"
        size="medium"
        content-styles="max-height: 400px;"
        title="设置门店自主定价范围"
    >
        <abc-form
            ref="priceTaxRatForm"
            label-position="left"
            item-no-margin
        >
            <abc-flex vertical :gap="8">
                <abc-text theme="gray">
                    按「固定售价」定价的商品，自主定价范围
                </abc-text>

                <abc-flex :gap="8" align="center">
                    <abc-text>
                        总部定价的
                    </abc-text>

                    <abc-form-item
                        :validate-event="validateMinPricePercent"
                    >
                        <abc-input
                            v-model.number="postData.minPricePercent"
                            v-abc-focus-selected
                            :config="{
                                maxLength: 2,
                                supportZero: true
                            }"
                            :input-custom-style="{
                                textAlign: 'center'
                            }"
                            :width="78"
                            max-length="99"
                            type="number"
                            placeholder="最低"
                            @enter="enterEvent"
                            @keyup="convertType('minPricePercent', postData.minPricePercent)"
                        >
                            <span slot="append">%</span>
                        </abc-input>
                    </abc-form-item>
                    <abc-text theme="black">
                        -
                    </abc-text>
                    <abc-form-item
                        :validate-event="validateMaxPricePercent"
                        placement="top"
                    >
                        <abc-input
                            v-model.number="postData.maxPricePercent"
                            v-abc-focus-selected
                            :config="{
                                maxLength: 2,
                                supportZero: true
                            }"
                            :input-custom-style="{
                                textAlign: 'center'
                            }"
                            :width="78"
                            max-length="99"
                            type="number"
                            placeholder="最高"
                            @enter="enterEvent"
                            @keyup="convertType('maxPricePercent', postData.maxPricePercent)"
                        >
                            <span slot="append">%</span>
                        </abc-input>
                    </abc-form-item>
                </abc-flex>
            </abc-flex>
        </abc-form>

        <abc-flex slot="footer" justify="flex-end">
            <abc-button
                :disabled="disabledPriceRangeDialogBtn"
                :loading="loading"
                @click="handlePriceRangeSubmit"
            >
                确定
            </abc-button>
            <abc-button type="blank" @click="showPriceRangeDialog = false">
                取消
            </abc-button>
        </abc-flex>
    </abc-dialog>
</template>

<script>
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import Clone from 'utils/clone';
    import EnterEvent from 'views/common/enter-event';
    import { isEqual } from '@/common/utils';
    export default {
        mixins: [EnterEvent],
        props: {
            value: Boolean,
        },
        data() {
            return {
                loading: false,
                postData: {
                    maxPricePercent: '',
                    minPricePercent: '',
                },
                postDataCache: {},
            };
        },
        computed: {
            ...mapGetters(['goodsConfig', 'subClinics', 'isSingleStore']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            showPriceRangeDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            disabledPriceRangeDialogBtn() {
                return isEqual(this.postData, this.postDataCache);
            },
        },
        watch: {
            goodsConfig: {
                handler(val) {
                    this.init(val);
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            ...mapActions(['setGoodsConfig', 'getGoodsConfig']),
            async init(data) {
                this.postData.minPricePercent = data.subClinicPrice?.minPricePercent ?? '';
                this.postData.maxPricePercent = data?.subClinicPrice?.maxPricePercent ?? '';
                this.postDataCache = Clone(this.postData);
            },
            /**
             * @desc 验证最低指导价
             * <AUTHOR>
             * @date 2018/06/06 12:12:07
             */
            validateMinPricePercent(value, callback) {
                if (+value > 100) {
                    callback({
                        validate: false,
                        message: '最低价不能高于总部定价100%',
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
            validateMaxPricePercent(value, callback) {
                if (+value < 100) {
                    callback({
                        validate: false,
                        message: '最高价不能低于总部定价100%',
                    });
                } else if (+value > 9999) {
                    callback({
                        validate: false,
                        message: '最高价不能高于总部定价9999%',
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
            /**
             * @desc 定价、税率类型转换
             * @date 2021/09/30 10:45:26
             */
            convertType(type, data, index) {
                switch (type) {
                    case 'minPricePercent' :
                        if (data === '') {
                            this.postData.minPricePercent = '';
                        } else {
                            this.postData.minPricePercent = Number(data);
                        }
                        break;

                    case 'maxPricePercent' :
                        if (data === '') {
                            this.postData.maxPricePercent = '';
                        } else {
                            this.postData.maxPricePercent = Number(data);
                        }
                        break;
                    default:
                        console.log(`out of ${type}${index}.`);
                }
            },
            handlePriceRangeSubmit() {
                this.$refs.priceTaxRatForm.validate(async (val) => {
                    if (val) {
                        try {
                            this.loading = true;
                            await GoodsAPIV3.updateStorePricePercent(this.postData);
                            this.$Toast({
                                message: '修改成功',
                                type: 'success',
                            });
                            this.showPriceRangeDialog = false;
                            // 拉取最新数据
                            this.getGoodsConfig();
                        } catch (e) {
                            console.error(e);
                        } finally {
                            this.loading = false;
                        }

                    }
                });
            },
        },
    };
</script>
