<template>
    <abc-layout preset="setting-table" class="chain-sub-info-wrapper">
        <abc-layout-header>
            <abc-flex align="center" justify="space-between">
                <abc-space>
                    <abc-input
                        v-model="pageParams.keyword"
                        :width="240"
                        placeholder="门店名称/简称"
                        @input="onInputSearch"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                    </abc-input>
                    <abc-select
                        v-model="pageParams.busMode"
                        placeholder="类型"
                        width="100px"
                        clearable
                        @change="initOffset"
                    >
                        <abc-option
                            v-for="item in busModeOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                </abc-space>

                <abc-button
                    variant="ghost"
                    theme="primary"
                    @click="handleOpenSubClinicPriceDialog"
                >
                    设置门店自主定价范围
                </abc-button>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleLayoutMounted">
            <abc-table
                class="chain-sub-info-table"
                type="pro"
                :loading="loading"
                :custom-td-class="customTdClass"
                :render-config="tableRenderConfig"
                :data-list="list"
                :custom-tr-key="itemKey"
                @handleClickTr="handleClickTr"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="pageParams.count"
                :pager-count="5"
                @current-change="handlePageChange"
            >
            </abc-pagination>
        </abc-layout-footer>

        <chain-sub-info-dialog
            v-model="showChainInfoDialog"
            :chain-sub-info="chainSubInfo"
            @refresh="_debounceGetChainList"
        ></chain-sub-info-dialog>

        <price-range-dialog
            v-model="showPriceRangeDialog"
        ></price-range-dialog>
    </abc-layout>
</template>
<script>
    import TableConfig from './table-chain-list-config';
    import ChainSubInfoDialog from './chain-sub-info-dialog.vue';
    import SettingAPI from '@/api/settings';
    import { debounce } from 'utils/lodash';
    import { busModeType } from '@/views/settings/clinic/chain-sub-info/constant';
    import clone from 'utils/clone';
    import * as tools from '@/views-pharmacy/common/tools';
    import PriceRangeDialog from 'views/settings/clinic/chain-sub-info/price-range-dialog.vue';

    export default {
        name: 'ChainSubInfoIndex',
        components: {
            PriceRangeDialog,
            ChainSubInfoDialog,
        },
        data() {
            return {
                list: [],
                showChainInfoDialog: false, // 展示详情dialog
                showPriceRangeDialog: false,
                chainSubInfo: {}, // 门店信息
                loading: true,
                pageParams: {
                    keyword: '',
                    busMode: '',
                    pageIndex: 0,
                    offset: 0,
                    limit: 10,
                    count: 0,
                },
                busModeOptions: [
                    {
                        label: '直营',
                        value: 1,
                    },
                    {
                        label: '加盟',
                        value: 2,
                    },
                ],
            };
        },
        computed: {
            tableRenderConfig() {
                const isOpen = (val) => {
                    return val ?
                        <abc-icon icon="s-check-medium" size="16" color="var(--abc-color-G2)"></abc-icon> :
                        <abc-icon icon="s-noaccess-medium" size="16" color="var(--abc-color-P10)"></abc-icon>;
                };
                return TableConfig.extendConfig({
                    'businessScope': {
                        dataFormatter: (_, row) => {
                            return tools.getBusinessScopeName(row.businessScope);
                        },
                    },
                    'name': {
                        dataFormatter: (_, row) => {
                            return `${row.name} ${row.shortName ? `（${row.shortName}）` : ''}`;
                        },
                    },
                    'busMode': {
                        dataFormatter: (_, row) => {
                            return busModeType[row.busMode];
                        },
                    },
                    'addressDetail': {
                        dataFormatter: (_, row) => {
                            return `${row.addressProvinceName ?? ''}${row.addressCityName ?? ''}${row.addressDistrictName ?? ''}${row.addressDetail ?? ''}`;
                        },
                    },
                    'contactPhone': {
                        dataFormatter: (_, row) => {
                            return tools.getContactPhoneWording(
                                row.contactPhone,
                                row.backupContactPhone,
                            );
                        },
                    },
                    'add': {
                        // eslint-disable-next-line no-unused-vars
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {
                                        isOpen(row.clinicGoodsPermission?.goodsCreateArchivesConfig)
                                    }
                                </abc-table-cell>
                            );
                        },
                    },
                    'modify': {
                        // eslint-disable-next-line no-unused-vars
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {
                                        isOpen(row.clinicGoodsPermission?.goodsModifyArchivesConfig)
                                    }
                                </abc-table-cell>
                            );
                        },
                    },
                    'delete': {
                        // eslint-disable-next-line no-unused-vars
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {
                                        isOpen(row.clinicGoodsPermission?.goodsDeleteArchivesConfig)
                                    }
                                </abc-table-cell>
                            );
                        },
                    },
                    'subClinicPrice': {
                        // eslint-disable-next-line no-unused-vars
                        customRender: (h, row) => {
                            return (
                                <abc-table-cell>
                                    {
                                        isOpen(row.clinicGoodsPermission?.goodsAdjustPriceConfig)
                                    }
                                </abc-table-cell>
                            );
                        },
                    },
                });
            },
        },
        created() {
            this._debounceGetChainList = debounce(this.getChainList, 250, true);
        },
        methods: {
            customTdClass(config) {
                const classArr = [];
                if (config.key === 'name') {
                    classArr.push('blue-text');
                }

                return classArr;
            },
            itemKey(item) {
                if (!item) return '';
                return item.id;
            },
            handleClickTr(item) {
                this.chainSubInfo = clone(item);
                this.showChainInfoDialog = true;
            },
            handlePageChange(val) {
                this.pageParams.offset = (val - 1) * this.pageParams.limit;
                this.getChainList();
            },
            handleLayoutMounted(data) {
                console.log('handleLayoutMounted', data);
                this.pageParams.limit = data.paginationLimit;
                this._debounceGetChainList();
            },
            onInputSearch() {
                this.pageParams.pageIndex = 0;
                this.pageParams.offset = 0;
                this._debounceGetChainList();
            },
            initOffset() {
                this.pageParams.offset = 0;
                this._debounceGetChainList();
            },
            // 获取门店列表
            async getChainList() {
                try {
                    this.loading = true;
                    const { data } = await SettingAPI.chainInfo.getChainList(this.pageParams);
                    this.pageParams.count = data.total;
                    this.list = data?.rows || [];
                } catch (err) {
                    console.log('getChainList error', err);
                } finally {
                    this.loading = false;
                }
            },
            handleOpenSubClinicPriceDialog() {
                this.showPriceRangeDialog = true;
            },
        },
    };
</script>

<style lang="scss">
.chain-sub-info-wrapper {
    .chain-sub-info-table {
        .blue-text {
            color: var(--abc-color-theme1);
        }
    }
}
</style>
