<template>
    <div style="height: 100%;">
        <instrument v-if="isInstrumentTab"></instrument>

        <examination
            v-else
            :key="selectTab"
            ref="projectListRef"
            :combine-type="combineType"
            :sub-type="1"
            :device-list="deviceArr"
            :is-template-clinic="isTemplateClinic"
            @changeCustomTypes="fetchCustomTypes(primaryClassification.id)"
        ></examination>
    </div>
</template>

<script>
    import Examination from 'views/settings/diagnosis-treatment/examinations/examinations';
    import Instrument from 'src/views/settings/diagnosis-treatment/examinations/instrument/index.vue';
    import { mapGetters } from 'vuex';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import { useExaminationStore } from 'views/settings/diagnosis-treatment/examinations/hooks/useExaminationStore';

    export default {
        name: 'DiagnosisExaminations',
        components: {
            Examination,
            Instrument,
        },
        beforeRouteEnter(to, from, next) {
            next((vm) => {
                // 医保终端跳转过来带上 type,默认选中组合项目
                if (to.query.type === '12') {
                    vm.selectTab = 1;
                } else if (Number.isInteger(+to.query.tab)) {
                    vm.selectTab = +to.query.tab;
                }
            });
        },
        setup() {
            const {
                shareState,
                clearShareState,
                fetchClinicExaminationDeviceList,
                fetchExaminationDeviceModelList,
                fetchCustomTypes,
                fetchClinicDepartments,
                fetchExaminationSamplePipe,
                fetchExaminationUnit,
                fetchSupplierList,
            } = useExaminationStore();

            return {
                shareState,
                clearShareState,
                fetchClinicExaminationDeviceList,
                fetchExaminationDeviceModelList,
                fetchCustomTypes,
                fetchClinicDepartments,
                fetchExaminationSamplePipe,
                fetchExaminationUnit,
                fetchSupplierList,
            };
        },
        data() {
            return {
                selectTab: 1,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'isAdmin',
                'isTemplateClinic',
                'goodsPrimaryClassification',
            ]),
            deviceList() {
                return this.shareState.deviceList || [];
            },
            deviceModelList() {
                return this.shareState.deviceModelList || [];
            },
            // 是否仪器
            isInstrumentTab() {
                return this.selectTab === 0;
            },
            combineType() {
                return this.selectTab === 1 ? 1 : 0;
            },
            chainId() {
                return this.isAdmin && this.currentClinic && this.currentClinic.chainId;
            },
            deviceArr() {
                if (this.isTemplateClinic) {
                    return this.deviceModelList;
                }
                return this.deviceList;
            },
            /**
             * @desc 一级分类
             */
            primaryClassification() {
                return (
                    this.goodsPrimaryClassification.find((item) => {
                        return item.goodsType === GoodsTypeEnum.EXAMINATION &&
                            item.goodsSubType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect;
                    }) || { id: '' }
                );
            },
        },
        watch: {
            '$route': {
                handler() {
                    this.selectTab = this.$route.query.tab !== undefined ? +this.$route.query.tab : 1;
                },
            },
        },
        async created() {
            this.handleRouteInit();

            await Promise.all([
                // 拉取门店绑定的仪器型号列表
                this.fetchClinicExaminationDeviceList(),
                // 拉取所有仪器型号列表
                this.fetchExaminationDeviceModelList(),
                this.fetchCustomTypes(this.primaryClassification.id),
                // 拉取执行科室
                this.fetchClinicDepartments(),
                // 拉取采样组
                this.fetchExaminationSamplePipe(),
                // 拉取单位
                this.fetchExaminationUnit(),
                // 拉取检验机构
                this.fetchSupplierList(),
            ]);
        },
        beforeDestroy() {
            this.$abcEventBus.$offVmEvent(this._uid);
            this.clearShareState();
        },
        methods: {
            // 路由初始化操作
            handleRouteInit() {
                const { tab } = this.$route.query;

                if (tab !== undefined) {
                    this.$router.replace({
                        name: 'examinations',
                        query: {
                            tab: tab * 1,
                        },
                    });

                    this.selectTab = tab * 1;
                }
            },
        },
    };
</script>
