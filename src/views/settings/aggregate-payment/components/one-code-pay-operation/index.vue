<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        class="one-code-pay-operation-modal"
        scrollbar-padding-size="none"
        size="hugely"
        responsive
        :show-confirm="false"
        :show-cancel="false"
        content-styles="padding: 0;"
        :on-close="handleClose"
    >
        <abc-scrollbar padding-size="none" class="one-code-pay-operation-bg">
            <abc-flex vertical>
                <img
                    src="@/assets/images/aggregate-payment/one-code-pay-1.png"
                    alt=""
                    style="width: 100%;"
                    class="one-code-pay-operation-img"
                />
                <img
                    src="@/assets/images/aggregate-payment/one-code-pay-2.png"
                    alt=""
                    style="width: 100%;"
                    class="one-code-pay-operation-img"
                />
                <img
                    src="@/assets/images/aggregate-payment/one-code-pay-3.png"
                    alt=""
                    style="width: 100%;"
                    class="one-code-pay-operation-img"
                />
            </abc-flex>
        </abc-scrollbar>
        <template slot="footerPrepend">
            <abc-flex align="center" justify="center" class="one-code-pay-operation-footer">
                <biz-marketing-button width="380px" button-text="申请开通 ABC 聚合支付" @click="handleClick"></biz-marketing-button>
            </abc-flex>
        </template>
    </abc-modal>
</template>

<script>
    import BizMarketingButton from '@/components-composite/biz-marketing-button/src/views/index.vue';
    import { BusinessKeyMap } from '@/assets/configure/buried-point';
    import { mapGetters } from 'vuex';

    export default {
        components: {
            BizMarketingButton,
        },
        props: {
            onConfirm: {
                type: Function,
                required: true,
            },
            onClose: {
                type: Function,
            },
        },
        computed: {
            ...mapGetters(['userInfo']),
        },
        data() {
            return {
                visible: false,
                startTime: null,
            };
        },
        created() {
            this.startTime = Date.now();
        },
        beforeDestroy() {
            // 组件销毁前上报停留时长（防止用户直接关闭页面）
            this.reportStayDuration();
        },
        methods: {
            reportStayDuration() {
                if (this.startTime) {
                    const stayDuration = Math.floor((Date.now() - this.startTime) / 1000);
                    console.log('用户在窗口停留时长：', stayDuration, '秒');

                    this.$abcPlatform.service.track.report({
                        key: BusinessKeyMap.EXP_ONE_CODE_PAY_OPERATION.key,
                        extendData: {
                            id: this.userInfo.id,
                            name: this.userInfo.name,
                            roleIds: this.userInfo.roleIds,
                            roleNames: this.userInfo.roleNames,
                            mobile: this.userInfo.mobile,
                            isAdmin: this.userInfo.isAdmin,
                            stayDuration,
                        },
                    });
                    this.startTime = null;
                }
            },
            handleClick() {
                if (typeof this.onConfirm === 'function') {
                    this.onConfirm();
                }
                this.visible = false;
            },
            handleClose() {
                if (typeof this.onClose === 'function') {
                    this.onClose();
                }
                this.visible = false;
            },
        },
    };
</script>

<style lang="scss">
.one-code-pay-operation-modal {
    .one-code-pay-operation-img {
        width: 1190px;
        border-radius: var(--abc-dialog-border-radius);
    }

    .modal-content-wrapper .close-button-wrapper {
        position: sticky;
        z-index: 1;
        justify-content: end;
    }

    .abc-dialog-footer {
        background: rgba(255, 255, 255, 0.9);
        border-bottom-left-radius: var(--abc-dialog-border-radius);
        box-shadow: 0 -2px 24px 0 rgba(0, 0, 0, 0.08);
        backdrop-filter: blur(10px);
    }

    .one-code-pay-operation-footer {
        width: 100%;
    }
}
</style>
