<template>
    <abc-space class="aggregate-payment-file-upload" :class="{ 'is-disabled': disabled }">
        <external-file
            ref="externalFileRef"
            v-model="attachments"
            :hidden-left="true"
            :business-type="BusinessTypeEnum.AGGREGATE_PAYMENT"
            oss-filepath="public-transfer-box"
            :max-upload-count="1"
            :business-desc="businessDesc"
            is-limit-max-count
            :file-text="businessDesc"
            :preview-url="exampleLink"
            :accept="accept"
            :disabled="disabled"
            :disabled-click="disabled"
            :business-id="uploadId"
            is-only-preview-img
            width="384px"
            height="40px"
            :upload-description="uploadDescription"
            :enable-compress="enableCompress"
            :compress-quality="compressQuality"
            :compress-max-width="compressMaxWidth"
            :compress-max-height="compressMaxHeight"
            @file-change="handleFileChange"
        >
            <template
                #previewOperate="{
                    item, index
                }"
            >
                <abc-flex justify="space-between" gap="small">
                    <abc-link
                        style="padding: 0 8px;"
                        theme="primary"
                        :disabled="disabled"
                        @click.stop="handleReuploadClick(item, index)"
                    >
                        重传
                    </abc-link>
                    <abc-link
                        style="padding: 0 8px;"
                        theme="danger"
                        :disabled="disabled"
                        @click.stop="handleDeletedClick(item, index)"
                    >
                        删除
                    </abc-link>
                </abc-flex>
            </template>
        </external-file>
        <abc-link v-if="!!exampleLink" theme="primary" @click="handlePreviewClick">
            示例
        </abc-link>

        <abc-preview
            v-if="isShowPreview"
            v-model="isShowPreview"
            :lists="previewImagesList"
            :index="0"
            :enable-compress="false"
        ></abc-preview>
    </abc-space>
</template>
<script type="text/ecmascript-6">
    import {
        accessImagePdf,
    } from '@/assets/configure/access-file';
    import { BusinessTypeEnum } from 'views/layout/mobile-upload-dialog/config';
    import ExternalFile from 'views/layout/external-file/index.vue';
    import AbcSocket from 'views/common/single-socket';
    export default {
        name: 'FileUpload',
        components: { ExternalFile },
        props: {
            value: {
                type: Array,
                default() {
                    return [];
                },
            },
            businessDesc: {
                type: String,
                default: '上传营业执照',
            },
            /**
             * @description: 上传底部提示
             * @date: 2024-12-26 10:46:17
             * @author: Horace
            */
            uploadDescription: {
                type: String,
                default: '支持图片、PDF格式',
            },
            /**
             * @description: 示例图片
             * @date: 2024-12-26 11:31:14
             * @author: Horace
            */
            exampleLink: {
                type: String,
                default: '',
            },
            /**
             * @description: 允许上传的文件类型
             * @date: 2024-12-26 11:37:15
             * @author: Horace
            */
            accept: {
                type: Array,
                default() {
                    return accessImagePdf();
                },
            },
            /**
             * @description: 用于区分不同的文件上传组件
             * @date: 2025-02-11 14:09:31
             * @author: Horace
            */
            uploadId: {
                type: String,
                required: true,
            },
            minSize: {
                type: Number,
                default: 30 * 1024,
            },
            maxSize: {
                type: Number,
                default: 3 * 1024 * 1024,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            /**
             * @description: 是否启用图片压缩
             * @date: 2025-06-16
             * @author: AI Assistant
             */
            enableCompress: {
                type: Boolean,
                default: true,
            },
            /**
             * @description: 压缩质量 (0-1)
             * @date: 2025-06-16
             * @author: AI Assistant
             */
            compressQuality: {
                type: Number,
                default: 0.92,
            },
            /**
             * @description: 压缩后最大宽度
             * @date: 2025-06-16
             * @author: AI Assistant
             */
            compressMaxWidth: {
                type: Number,
                default: 2880,
            },
            /**
             * @description: 压缩后最大高度
             * @date: 2025-06-16
             * @author: AI Assistant
             */
            compressMaxHeight: {
                type: Number,
                default: 2880,
            },
        },
        data() {
            return {
                BusinessTypeEnum,
                isShowPreview: false,
                previewImagesList: [],
            };
        },
        computed: {
            attachments: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        created() {
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('short-url.upload_attachment', this.handleImages);
        },
        beforeDestroy() {
            this._socket?.off('short-url.upload_attachment', this.handleImages);
        },
        methods: {
            handleReuploadClick(item, index) {
                this.attachments.splice(index, 1);
                this.$refs.externalFileRef?.triggerFileInput(item, index);
            },
            handleDeletedClick(item, index) {
                this.$refs.externalFileRef?.deleteItem(item, index);
            },
            handlePreviewClick() {
                this.isShowPreview = true;
                this.previewImagesList = [{ url: this.exampleLink }];
            },
            handleFileChange(newAttachments) {
                const attachments = newAttachments || this.attachments;
                if (attachments.find((item) => {
                    return item.fileSize < this.minSize || item.fileSize > this.maxSize;
                })) {
                    this.$Toast.error(`上传文件大小应该在${this.minSize / 1024}KB到${this.maxSize / 1024 / 1024}MB之间`);
                    this.attachments = [];
                    return;
                }
                this.$emit('file-change', newAttachments || this.attachments);
            },

            handleImages(data) {
                const {
                    attachments = [],
                    businessType,
                    businessId,
                } = data;
                if (businessType === BusinessTypeEnum.AGGREGATE_PAYMENT &&
                    attachments &&
                    attachments.length &&
                    businessId === this.uploadId
                ) {

                    const newAttachments = attachments.map((item) => {
                        return {
                            fileName: item.fileName,
                            fileSize: item.fileSize,
                            sort: item.sort,
                            url: item.url,
                        };
                    });
                    this.attachments.push(...newAttachments);
                    this.handleFileChange(newAttachments);
                }
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import "src/styles/theme.scss";

.aggregate-payment-file-upload {
    &.is-disabled {
        .abc-space-item:first-child {
            color: var(--abc-color-T1);
            cursor: not-allowed;
            background: var(--abc-color-bg-disabled);
        }
    }

    .external-files-wrapper {
        margin: 0;

        .file-add {
            justify-content: flex-start;
            padding: 0 12px;
            margin: 0;
            border: 1px solid var(--abc-color-P1);
            border-radius: var(--abc-border-radius-small);

            & > .add-icon {
                display: flex;
                flex: 1;
                align-items: center;
                min-width: 100%;

                .abc-icon {
                    width: 50px;
                    margin-right: 4px;
                }

                p {
                    font-size: 14px;
                    line-height: 22px;
                    color: var(--abc-color-T2);
                }
            }
        }

        .item {
            margin: 0;
        }

        .external-files-preview-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 40px;
            padding: 0 12px;
            border: 1px solid $abcCardBorderColor;
            border-radius: var(--abc-border-radius-small);

            .abc-file-viewer {
                width: 50px !important;
                height: 28px !important;

                .view-box.image-box {
                    border: none;
                }
            }
        }

        .delete-bar {
            display: none;
        }
    }
}
</style>
