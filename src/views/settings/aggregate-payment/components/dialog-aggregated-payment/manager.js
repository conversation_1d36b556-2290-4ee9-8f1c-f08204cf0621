import SettingsApi from 'api/settings';

import { ToastFunc as Toast } from '@abc/ui-pc';

import {
    listAllBanks,
    listAllinSubPayBankInfo,
    recognizeIdcardFace,
    recognizeIdcardBack,
    recognizeBusinessLicense,
    recognizeBankCardByCode,
    applyOpenAllinPayMerchant,
    getApplyOpenAllinPayMerchant,
    saveAllinPayMerchantDraft,
} from '@/api/settings/aggregate-payment';
import {
    isNull,
} from '@/utils';
import {
    MerchantTypeOptions,
    AccountTypeOptions,
    SettlementMethodOptions,
    SettlementTypeOptions,
    IndustryOptions,
    MerchantTypeEnum,
    IndustryEnum,
    AccountTypeEnum,
    SettlementMethodEnum,
    ActiveStepEnum,
    FormStatusEnum,
    SettlementTypeEnum,
    OperatorTypeOptions,
    OperatorTypeEnum,
} from '../../constants/index';
import { isEqual } from 'utils/lodash';
import cloneDeep from 'lodash.clonedeep';
import ClinicAPI from 'api/clinic.js';
import {
    ApprovalStatus, ComplianceResult, FormFieldAuditMap,
} from '../../constants/audit-result';
import { ModalFunc as AbcModal } from '@abc/ui-pc';
import { getGlobalHost } from 'utils/host';

export default class AggregatedPaymentDialogManager {
    static instance = null;
    constructor() {
        this.disabled = false; // 表单禁用
        this.visible = false; // 是否显示model
        this.showAuthDialog = false; // 是否显示上传签字授权书弹窗
        this.activeStep = ActiveStepEnum.SUBMIT_MATERIAL; // 当前步骤
        this.sourceFormData = {}; // 初始化表单数据标志
        this.currentClinic = {}; // 当前门诊
        this.isHandleState = false; // 是否合规审核不通过
        this.isAutoHeight = false;
        this.statusEnum = {
            status: undefined,
            complianceResult: undefined,
            handleState: undefined,
            handleDetail: undefined,
            approvalStatus: undefined,
            approvalFailReason: undefined,
        };
        this.contentLoading = false; // 内容加载状态
        this.formData = {
            // 基础信息 (basicInfo)
            merchantType: MerchantTypeEnum.ENTERPRISE , //商户性质，默认为企业
            merchantShortName: '' , //商户简称
            businessAddress: {
                addressCityId: '',
                addressCityName: '',
                addressProvinceId: '',
                addressProvinceName: '',
                addressDistrictId: '',
                addressDistrictName: '',
            } , //省市区
            businessAddressDetail: '' , //详细地址
            industry: '' , //所属行业
            settlementType: SettlementTypeEnum.RESPECTIVELY , //连锁下各门店资金如何结算

            // 营业执照 (businessLicense)
            businessLicenseImageUrl: [] , //营业执照图片
            businessLicenseName: '' , //营业执照名称
            unifiedSocialCreditCode: '' , //统一社会信用代码
            businessLicenseValidity: '长期' , //有效期
            businessLicenseValidityForever: true, //营业执照长期有效

            // 法人信息 (legalPerson)
            idCardFrontFile: [] , //身份证正面
            idCardBackFile: [] , //身份证反面
            idCardHoldingFile: [] , //手持身份证照片
            legalPersonName: '' , //法人姓名
            idCardNumber: '' , //身份证号码
            idCardValidity: '' , //证件有效期
            idCardValidityForever: false, //身份证长期有效
            idCardAddress: {
                addressCityId: '',
                addressCityName: '',
                addressProvinceId: '',
                addressProvinceName: '',
                addressDistrictId: '',
                addressDistrictName: '',
            } , //省市区
            idCardAddressDetail: '' , //详细地址
            operatorType: OperatorTypeEnum.LEGAL_PERSON, // 当前操作人身份：legal-法人，agent-经办人
            legalPersonPhone: '' , //法人手机号
            authorizationLetterUrl: [], // 签字授权书
            agentName: '', // 经办人姓名
            agentPhone: '', // 经办人手机号

            // 收款账户 (bankAccount)
            accountType: AccountTypeEnum.CORPORATE , //账户类型，默认为对公账户
            accountName: '' , //账户名称
            accountNumber: '' , //账户号
            bankCode: '' , //开户银行
            bankBranch: '', //开户支行名称
            cnapsNo: '' , //开户支行
            accountProofImageUrl: [] , //收款账户证明
            settlementMethod: SettlementMethodEnum.BANK_CARD , //结算方式，默认为自动提现到银行卡
            adminName: '' , //管理员
            adminPhone: '' , //管理员电话

            // 经营场所 (businessPlace)
            storeFrontImageUrl: [] , //门头照片
            leaseContractImageUrl: [] , // 经营场所证件
            interiorImageUrl: [] , //经营内景照

        }; // 表单数据
        this.formSchema = []; // 表单schema
        this.isDirty = false; // 表单是否被修改
        this.qrCodeSrc = 'https://global-dev.abczs.cn/m/aggregate-payment?clinicId=b0aef05be62b4ef186bafb694c5bae3a&chainId=6a869c22abee4ffbaef3e527bbb70aeb&employeeId=6e45706922a74966ab51e4ed1e604641'; // 二维码图片地址
        this.loading = false; // 加载状态
        this.allBankOptions = []; // 存储所有银行选项
        this.isChainAdmin = false; // 是否是连锁总部
        this.functionIntroductionVisible = false; // 功能介绍弹窗是否显示
    }

    static getInstance() {
        if (!AggregatedPaymentDialogManager.instance) {
            AggregatedPaymentDialogManager.instance = new AggregatedPaymentDialogManager();
        }
        return AggregatedPaymentDialogManager.instance;
    }

    // 埋点上报
    report(keyObj, userInfo) {
        if (!keyObj?.key) {
            return ;
        }
        window.$platform.service.track.report({
            key: keyObj.key,
            extendData: {
                id: userInfo.id,
                name: userInfo.name,
                roleIds: userInfo.roleIds,
                roleNames: userInfo.roleNames,
                mobile: userInfo.mobile,
                isAdmin: userInfo.isAdmin,
                countryCode: userInfo.countryCode,
            },
        });
    }

    // 保存草稿
    async saveDraft() {
        try {
            const params = this.getParams();
            // 覆盖status为0，表示草稿状态
            params.status = 0;

            await saveAllinPayMerchantDraft(params);
            Toast.success('草稿保存成功');
            return true;
        } catch (error) {
            console.error('保存草稿失败:', error);
            return false;
        }
    }

    // 使用 sendBeacon 保存草稿，用于页面关闭时调用
    saveDraftNotCancel() {
        try {
            const params = this.getParams();
            // 覆盖status为0，表示草稿状态
            params.status = 0;

            const apiUrl = `/api/v2/wechatpay/apply/allin-pay/merchant/draft?${+new Date()}`;
            const blob = new Blob([JSON.stringify(params)], { type: 'application/json' });
            navigator.sendBeacon(apiUrl, blob);
            console.log('草稿保存成功');
        } catch (error) {
            console.error('保存草稿失败:', error);
        }
    }

    // 加载草稿
    async loadDraft() {
        try {
            let businessAddress = {
                addressCityId: '',
                addressCityName: '',
                addressProvinceId: '',
                addressProvinceName: '',
                addressDistrictId: '',
                addressDistrictName: '',
            };
            let addressDetail = '';
            const [response, adressRes] = await Promise.all([getApplyOpenAllinPayMerchant(),ClinicAPI.fetchClinicInformation(this.currentClinic?.clinicId || '')]);

            if (adressRes?.data) {
                businessAddress = {
                    addressCityId: adressRes.data.addressCityId || '',
                    addressCityName: adressRes.data.addressCityName || '',
                    addressProvinceId: adressRes.data.addressProvinceId || '',
                    addressProvinceName: adressRes.data.addressProvinceName || '',
                    addressDistrictId: adressRes.data.addressDistrictId || '',
                    addressDistrictName: adressRes.data.addressDistrictName || '',
                };
                this.currentClinic.chainId = this.currentClinic.chainId || adressRes.data.parentId || '';

                addressDetail = adressRes.data.addressDetail || '';
            }
            if (response?.data) {
                const draftData = response.data;
                this.statusEnum.status = draftData.status;
                this.statusEnum.complianceResult = draftData.complianceResult;
                this.statusEnum.handleState = draftData.handleState;
                this.statusEnum.handleDetail = draftData.handleDetail;
                this.statusEnum.approvalStatus = draftData.approvalStatus;
                this.statusEnum.approvalFailReason = draftData.approvalFailReason;


                // 将图片url转换为数组格式
                const convertUrlToFileList = (url) => {
                    return url ? [{ url }] : [];
                };

                // 将地址信息转换为对象格式
                const convertBusinessAddress = (data) => {
                    return {
                        addressProvinceId: data?.addressProvinceId || '',
                        addressProvinceName: data?.addressProvinceName || '',
                        addressCityId: data?.addressCityId || '',
                        addressCityName: data?.addressCityName || '',
                        addressDistrictId: data?.addressDistrictId || '',
                        addressDistrictName: data?.addressDistrictName || '',
                    };
                };

                // 解析身份证地址字符串
                const parseIdCardAddress = (address = '') => {
                    // 假设地址格式为：省名-市名-区名-详细地址
                    const parts = address.split('-');
                    return {
                        addressProvinceName: parts[0] || '',
                        addressCityName: parts[1] || '',
                        addressDistrictName: parts[2] || '',
                        addressDetail: parts.slice(3).join('-') || '',
                    };
                };


                // 根据getParams格式处理特殊字段
                const idCardAddressInfo = parseIdCardAddress(draftData.idCardAddress);
                const addressData = convertBusinessAddress(draftData);
                const getParamsData = (key, defaultValue) => {
                    if (!isNull(draftData[key])) {
                        return draftData[key];
                    } if (!isNull(this.formData[key])) {
                        return this.formData[key];
                    }
                    return defaultValue;

                };
                this.formData = {
                    ...this.formData,
                    ...draftData,
                    agentPhone: draftData.legalPersonPhone,
                    agentName: draftData.legalPersonName,
                    merchantType: getParamsData('merchantType', MerchantTypeEnum.ENTERPRISE),
                    accountType: getParamsData('accountType', AccountTypeEnum.CORPORATE),
                    settlementMethod: getParamsData('settlementMethod', SettlementMethodEnum.BANK_CARD),
                    // 处理地址信息
                    businessAddress: addressData?.addressProvinceName ? addressData : businessAddress,
                    businessAddressDetail: draftData.addressDetail || addressDetail || '',
                    businessLicenseValidityForever: draftData.businessLicenseValidity === '长期',
                    // 处理图片信息
                    businessLicenseImageUrl: convertUrlToFileList(draftData.businessLicenseImageUrl),
                    idCardFrontFile: convertUrlToFileList(draftData.idCardFrontUrl),
                    idCardBackFile: convertUrlToFileList(draftData.idCardBackUrl),
                    idCardHoldingFile: convertUrlToFileList(draftData.idCardHandheldUrl),
                    accountProofImageUrl: convertUrlToFileList(draftData.accountProofImageUrl),
                    storeFrontImageUrl: convertUrlToFileList(draftData.storeFrontImageUrl),
                    leaseContractImageUrl: convertUrlToFileList(draftData.leaseContractImageUrl),
                    interiorImageUrl: convertUrlToFileList(draftData.interiorImageUrl),
                    authorizationLetterUrl: convertUrlToFileList(draftData.authorizationLetterUrl),

                    // 处理身份证地址信息
                    idCardAddress: {
                        addressCityId: idCardAddressInfo.addressCityId || '',
                        addressCityName: idCardAddressInfo.addressCityName || '',
                        addressDistrictId: idCardAddressInfo.addressDistrictId || '',
                        addressDistrictName: idCardAddressInfo.addressDistrictName || '',
                        addressProvinceId: idCardAddressInfo.addressProvinceId || '',
                        addressProvinceName: idCardAddressInfo.addressProvinceName || '',
                    },
                    idCardAddressDetail: idCardAddressInfo.addressDetail || '',
                    idCardValidityForever: draftData.idCardValidity === '长期',
                };
                return true;
            }
            this.formData = {
                ...this.formData,
                businessAddress,
                businessAddressDetail: addressDetail,
            };
            return true;

        } catch (error) {
            console.error('加载草稿失败:', error);
        }
        return false;
    }

    // 清除草稿
    async clearDraft() {
        try {
            // 清除草稿就是提交一个空的草稿
            await applyOpenAllinPayMerchant({
                status: 0,
            });
        } catch (error) {
            console.error('清除草稿失败:', error);
        }
    }

    // 设置表单数据
    setFormData(data) {
        // 几个地址和文件需要判断有无值，如果没有需要给默认值
        this.formData = {
            ...this.formData,
            ...data,
        };
    }

    // 获取表单数据
    getFormData() {
        return this.formData;
    }

    getFormSchemaItem(prop) {
        return this.formSchema.find((item) => item.prop === prop) || {};
    }

    getFormSchemaGroup(prop, itemProp) {
        const schemaItem = this.getFormSchemaItem(prop);
        return schemaItem.schema?.find((item) => item.prop === itemProp) || {};
    }

    getFormSchemaGroupItem(prop, gourpProp, itemProp) {
        const schemaGroup = this.getFormSchemaGroup(prop, gourpProp);
        return schemaGroup.schema?.find((item) => item.prop === itemProp) || {};
    }

    handleMerchantTypeChange(value) {
        const isPersonal = value === MerchantTypeEnum.PERSONAL;
        // 获取行业选择器
        const industryItem = this.getFormSchemaGroup('basicInfo', 'industry') || {};
        // 个人商户时，行业默认为药店且禁用, 否则账户类型默认为对公
        if (isPersonal) {
            this.formData.industry = IndustryEnum.PHARMACY;
            this.formData.businessLicenseImageUrl = [];
            this.formData.businessLicenseName = '';
            this.formData.unifiedSocialCreditCode = '';
            this.formData.businessLicenseValidity = '';
            this.formData.businessLicenseValidityForever = false;
            industryItem.disabled = true;
        } else {
            this.formData.accountType = AccountTypeEnum.CORPORATE;
            this.formData.idCardHoldingFile = [];
            this.formData.leaseContractImageUrl = [];
            industryItem.disabled = false;
        }

        // 获取营业执照组
        const businessLicenseSchema = this.getFormSchemaItem('businessLicense') || {};
        const personalInfo = this.getFormSchemaItem('personalInfo') || {};
        const leaseContractImageGroup = this.getFormSchemaGroup('businessPlace','leaseContractImageGroup') || {};
        // 个人商户时，隐藏营业执照信息，显示个人信息
        businessLicenseSchema.hidden = isPersonal;
        personalInfo.hidden = !isPersonal;
        leaseContractImageGroup.hidden = !isPersonal;

    }

    async getBankBranchOptions(queryString) {
        // 获取当前选中的银行编码
        const { bankCode } = this.formData;
        if (!bankCode) {
            return;
        }

        try {
            const response = await listAllinSubPayBankInfo(bankCode,{
                keyword: queryString || this.formData.bankBranch || '', // 搜索关键字
                limit: 200, // 最大返回数量
                offset: 0, // 从第一条开始
            });

            if (response?.data) {
                const options = response.data.rows?.map((branch) => ({
                    label: branch.netBankName, // 使用netBankName作为显示名称
                    value: branch.netBankCode, // 使用netBankCode作为值
                    cnapsNo: branch.netBankCode, // 使用netBankCode作为cnapsNo
                }));

                // 更新支行选项
                const bankBranchItem = this.getFormSchemaGroup('bankAccount', 'cnapsNo');
                if (bankBranchItem) {
                    bankBranchItem.options = options;
                }

            }
        } catch (error) {
            console.error('搜索支行失败:', error);
        }
    }

    // 初始化表单架构
    async initFormSchema(isChainAdmin) {
        const _this = this;
        // 获取所有银行码表
        const banksResponse = await listAllBanks();

        this.allBankOptions = banksResponse?.data?.rows?.map((bank) => ({
            label: bank.bankName,
            value: bank.bankCode,
        })) || [];
        const isPersonal = this.formData.merchantType === MerchantTypeEnum.PERSONAL;
        const isEnterprise = this.formData.merchantType === MerchantTypeEnum.ENTERPRISE;

        this.formSchema = [
            {
                sort: 1,
                title: '基础信息',
                prop: 'basicInfo',
                schema: [
                    {
                        label: '商户性质',
                        prop: 'merchantType',
                        type: 'select',
                        options: MerchantTypeOptions,
                        required: true,
                        hiddenRedDot: true,
                        onChange: (value) => {
                            _this.handleMerchantTypeChange(value);
                        },
                    },
                    {
                        label: '商户名称',
                        prop: 'merchantShortName',
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        placeholder: '建议填写机构的短名',
                        maxLength: 40,
                    },
                    {
                        label: '经营地址',
                        prop: 'addressGroup',
                        type: 'group',
                        required: true,
                        hiddenRedDot: true,
                        schema: [
                            {
                                label: '省市区',
                                prop: 'businessAddress',
                                type: 'address',
                                required: true,
                                hiddenRedDot: true,
                                validateEvent: (value, callback) => {
                                    const { businessAddress } = _this.formData;
                                    if (!businessAddress?.addressProvinceId) {
                                        callback({
                                            validate: false, message: '请选择省市区',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                            {
                                label: '详细地址',
                                prop: 'businessAddressDetail',
                                type: 'input',
                                required: true,
                                hiddenRedDot: true,
                                maxLength: 30,
                            },
                        ],
                    },
                    {
                        label: '所属行业',
                        prop: 'industry',
                        type: 'select',
                        options: IndustryOptions,
                        required: true,
                        disabled: isPersonal,
                        hiddenRedDot: true,
                    },
                    {
                        label: '连锁下各门店资金如何结算',
                        prop: 'settlementType',
                        type: 'select',
                        labelAlign: 'left',
                        required: true,
                        labelWrap: true,
                        hiddenRedDot: true,
                        options: SettlementTypeOptions,
                        hidden: !isChainAdmin, // 只有连锁总部才显示
                    },
                ],
            },
            {
                sort: 2,
                title: '营业执照',
                prop: 'businessLicense',
                hidden: isPersonal,
                schema: [
                    {
                        label: '营业执照',
                        prop: 'businessLicenseGroup',
                        type: 'group',
                        hiddenRedDot: true,
                        schema: [
                            {
                                label: `需与小程序主体《${this.currentClinic?.name || ''}》一致，否则患者无法在微诊所完成支付`,
                                text: `需与小程序主体《${this.currentClinic?.name || ''}》一致，否则患者无法在微诊所完成支付`,
                                prop: 'businessLicenseText',
                                type: 'text',
                            },
                            {
                                label: '营业执照',
                                prop: 'businessLicenseImageUrl',
                                businessDesc: '营业执照',
                                type: 'file',
                                required: true,
                                hiddenRedDot: true,
                                exampleLink: 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/yingyezhizhao.jpg',

                                onChange: async (files) => {
                                    await this.parseBusinessLicense(files);

                                },
                                validateEvent(value, callback) {
                                    const { businessLicenseImageUrl } = _this.formData;
                                    if (!businessLicenseImageUrl?.length) {
                                        callback({
                                            validate: false, message: '请上传营业执照',
                                        });
                                        return;
                                    }
                                    const file = businessLicenseImageUrl[0];
                                    if (!file?.url) {
                                        callback({
                                            validate: false, message: '营业执照上传失败，请重新上传',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                        ],
                    },
                    {
                        label: '营业执照名称',
                        prop: 'businessLicenseName',
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        maxLength: 30,
                        placeholder: '请输入营业执照名称',
                        onChange: (value) => {
                            // 如果是企业，账户名称需要与营业执照名称保持一致
                            if (isEnterprise) {
                                this.formData.accountName = value;
                            }
                        },
                    },
                    {
                        label: '统一社会信用代码',
                        prop: 'unifiedSocialCreditCode',
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        maxLength: 20,
                    },
                    {
                        label: '有效期',
                        prop: 'businessLicenseValidity',
                        type: 'validityPeriod',
                        foreverProp: 'businessLicenseValidityForever',
                        required: !_this.formData.businessLicenseValidityForever,
                        hiddenRedDot: true,
                        validateEvent(value, callback) {
                            if (_this.formData.businessLicenseValidityForever) {
                                callback({ validate: true });
                            } else {
                                callback({
                                    validate: !!_this.formData.businessLicenseValidity,
                                    message: '有效期不能为空',
                                });
                            }
                        },
                    },
                ],
            },
            {
                sort: 2,
                title: '个人信息',
                prop: 'personalInfo',
                hiddenRedDot: true,
                hidden: !isPersonal,
                schema: [
                    {
                        label: '手持身份证照片',
                        prop: 'idCardHoldingFileGroup',
                        type: 'group',
                        schema: [
                            {
                                label: '完整清晰显示持证人头部正面、肩部与身份证全部信息，白色或浅色的纯色背景。',
                                text: '完整清晰显示持证人头部正面、肩部与身份证全部信息，白色或浅色的纯色背景。',
                                prop: 'idCardHoldingText',
                                type: 'text',
                            },
                            {
                                label: '手持身份证照片',
                                prop: 'idCardHoldingFile',
                                type: 'file',
                                required: true,
                                hiddenRedDot: true,
                                businessDesc: '手持身份证照片',
                                exampleLink: 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/shouchi.jpg',

                                validateEvent(value, callback) {
                                    const { idCardHoldingFile } = _this.formData;
                                    if (!idCardHoldingFile?.length) {
                                        callback({
                                            validate: false, message: '请上传手持身份证照片',
                                        });
                                        return;
                                    }
                                    const file = idCardHoldingFile[0];
                                    if (!file?.url) {
                                        callback({
                                            validate: false, message: '手持身份证照片上传失败，请重新上传',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                        ],
                    },
                ],
            },
            {
                sort: 3,
                title: '法人信息',
                prop: 'legalPerson',
                schema: [
                    {
                        label: '身份证',
                        prop: 'idCardGroup',
                        type: 'group',
                        hiddenRedDot: true,
                        schema: [
                            {
                                label: '务必上传法人代表身份证，若上传法人复印件需加盖公章',
                                text: '务必上传法人代表身份证，若上传法人复印件需加盖公章',
                                prop: 'idCardText',
                                type: 'text',
                            },
                            {
                                label: '身份证人像面',
                                prop: 'idCardFrontFile',
                                type: 'file',
                                required: true,
                                hiddenRedDot: true,
                                businessDesc: '身份证人像面',
                                exampleLink: 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/shenfenzheng.jpg',
                                onChange: async (files) => {
                                    await this.parseIdCardFace(files);
                                },
                                validateEvent(value, callback) {
                                    const { idCardFrontFile } = _this.formData;
                                    if (!idCardFrontFile?.length) {
                                        callback({
                                            validate: false, message: '请上传身份证人像面照片',
                                        });
                                        return;
                                    }
                                    const file = idCardFrontFile[0];
                                    if (!file?.url) {
                                        callback({
                                            validate: false, message: '身份证人像面照片上传失败，请重新上传',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                            {
                                label: '身份证国徽面',
                                prop: 'idCardBackFile',
                                type: 'file',
                                required: true,
                                hiddenRedDot: true,
                                businessDesc: '身份证国徽面',
                                exampleLink: 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/shenfenzheng.jpg',
                                onChange: async (files) => {
                                    await this.parseIdCardBack(files);
                                },
                                validateEvent(value, callback) {
                                    const { idCardBackFile } = _this.formData;
                                    if (!idCardBackFile?.length) {
                                        callback({
                                            validate: false, message: '请上传身份证国徽面照片',
                                        });
                                        return;
                                    }
                                    const file = idCardBackFile[0];
                                    if (!file?.url) {
                                        callback({
                                            validate: false, message: '身份证国徽面照片上传失败，请重新上传',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                        ],
                    },
                    {
                        label: '法人姓名',
                        prop: 'legalPersonName',
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        maxLength: 20,
                    },
                    {
                        label: '身份证号码',
                        prop: 'idCardNumber',
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        maxLength: 18,
                    },
                    {
                        label: '证件有效期',
                        prop: 'idCardValidity',
                        type: 'validityPeriod',
                        foreverProp: 'idCardValidityForever',
                        required: !_this.formData.idCardValidityForever,
                        hiddenRedDot: true,
                        validateEvent(value, callback) {
                            if (_this.formData.idCardValidityForever) {
                                callback({ validate: true });
                            } else {
                                callback({
                                    validate: !!_this.formData.idCardValidity,
                                    message: '证件有效期不能为空',
                                });
                            }
                        },
                    },
                    {
                        label: '证件地址',
                        prop: 'idCardAddressGroup',
                        type: 'group',
                        hiddenRedDot: true,
                        required: true,
                        validateEvent(value, callback) {
                            const {
                                idCardAddress, idCardAddressDetail,
                            } = value || {};
                            if (!idCardAddress) {
                                callback({
                                    validate: false, message: '请选择省市区',
                                });
                                return;
                            }
                            if (!idCardAddressDetail) {
                                callback({
                                    validate: false, message: '请输入详细地址',
                                });
                                return;
                            }
                            callback({ validate: true });
                        },
                        schema: [
                            {
                                label: '省市区',
                                prop: 'idCardAddress',
                                type: 'address',
                                required: true,
                                hiddenRedDot: true,
                            },
                            {
                                label: '详细地址',
                                prop: 'idCardAddressDetail',
                                type: 'input',
                                required: true,
                                hiddenRedDot: true,
                                maxLength: 30,
                            },
                        ],
                    },
                    {
                        label: '当前操作人身份',
                        prop: 'operatorType',
                        type: 'select',
                        options: OperatorTypeOptions,
                        required: true,
                        hiddenRedDot: true,
                        onChange: (value) => {
                            // 获取法人手机号组
                            const phoneGroup = this.getFormSchemaGroup('legalPerson', 'phoneGroup');
                            // 获取法人授权书组
                            const authorizationLetterGroup = this.getFormSchemaGroup('legalPerson', 'authorizationLetterGroup');
                            // 获取经办人姓名
                            const agentNameItem = this.getFormSchemaGroup('legalPerson', 'agentName');
                            // 获取经办人手机号
                            const agentPhoneItem = this.getFormSchemaGroup('legalPerson', 'agentPhone');
                            // 获取管理员
                            const adminNameItem = this.getFormSchemaGroup('bankAccount', 'adminName');
                            // 获取管理员电话
                            const adminPhoneItem = this.getFormSchemaGroup('bankAccount', 'adminPhone');
                            // 根据操作人身份设置字段显示隐藏
                            phoneGroup.hidden = value === OperatorTypeEnum.OPERATOR;
                            authorizationLetterGroup.hidden = value === OperatorTypeEnum.LEGAL_PERSON;
                            agentNameItem.hidden = value === OperatorTypeEnum.LEGAL_PERSON;
                            agentPhoneItem.hidden = value === OperatorTypeEnum.LEGAL_PERSON;
                            adminNameItem.hidden = value === OperatorTypeEnum.OPERATOR;
                            adminPhoneItem.hidden = value === OperatorTypeEnum.OPERATOR;
                        },
                    },
                    {
                        label: '法人手机号',
                        prop: 'phoneGroup',
                        type: 'group',
                        hiddenRedDot: true,
                        hidden: this.formData.operatorType !== OperatorTypeEnum.LEGAL_PERSON,
                        schema: [
                            {
                                label: '用于收签约的短信，请确保手机号正常使用',
                                text: '用于收签约的短信，请确保手机号正常使用',
                                prop: 'phoneText',
                                type: 'text',
                            },
                            {
                                label: '法人手机号',
                                prop: 'legalPersonPhone',
                                type: 'input',
                                required: true,
                                maxLength: 11,
                            },
                        ],
                    },
                    {
                        label: '法人授权委托书',
                        text: '非法人操作，需上传签字授权委托书',
                        prop: 'authorizationLetterGroup',
                        type: 'text',
                        button: this.formData.authorizationLetterUrl?.length ? '已上传' : '查看指引',
                        onClick: () => {
                            _this.showAuthDialog = true;
                        },
                        hiddenRedDot: true,
                        hidden: this.formData.operatorType === OperatorTypeEnum.LEGAL_PERSON,
                    },
                    {
                        label: '经办人姓名',
                        prop: 'agentName',
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        maxLength: 20,
                        hidden: this.formData.operatorType === OperatorTypeEnum.LEGAL_PERSON,
                    },
                    {
                        label: '经办人手机号',
                        prop: 'agentPhone',
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        maxLength: 11,
                        hidden: this.formData.operatorType === OperatorTypeEnum.LEGAL_PERSON,
                    },
                ],
            },
            {
                sort: 4,
                title: '收款账户',
                prop: 'bankAccount',
                schema: [
                    {
                        label: '账户类型',
                        prop: 'accountType',
                        type: 'select',
                        options: AccountTypeOptions,
                        required: true,
                        disabled: false,
                        hiddenRedDot: true,
                        onChange: (value) => {
                            // 更新收款账户证明的 label 和提示文案
                            const accountProofGroup = this.getFormSchemaGroup('bankAccount', 'accountProofGroup');
                            const accountProofImageItem = this.getFormSchemaGroupItem('bankAccount', 'accountProofGroup', 'accountProofImageUrl');

                            this.formData.accountProofImageUrl = [];
                            if (accountProofGroup && accountProofImageItem) {
                                const item = this.getFormSchemaGroupItem('bankAccount', 'accountProofGroup', 'accountProofImageText');
                                if (value === AccountTypeEnum.CORPORATE) {
                                    accountProofGroup.label = '收款账户证明';
                                    accountProofImageItem.businessDesc = '账户材料';
                                    item.text = '以下任选一张图片上传：开户许可证、印鉴卡电子回单、基本存款信息。注意：原件带银行电子章；扫描/复印件需加盖公章（红章）';
                                    accountProofImageItem.exampleLink = 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/shoukuanzhengming.jpg';
                                } else {
                                    accountProofGroup.label = '银行卡正面照片';
                                    item.text = '正面，包含卡片四角，图片清晰';
                                    accountProofImageItem.businessDesc = '上传银行卡';
                                    accountProofImageItem.exampleLink = 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/yinhangka.jpg';
                                }
                            }
                        },
                    },
                    {
                        label: '账户名称',
                        prop: 'accountName',
                        type: 'input',
                        required: true,
                        disabled: false,
                        hiddenRedDot: true,
                        maxLength: 50,
                    },
                    {
                        label: '银行卡号',
                        prop: 'accountNumber',
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        maxLength: 30,
                        onChange: async (value) => {
                            let res = {};
                            // 去除所有空格，包括中间的
                            this.formData.accountNumber = value.replace(/\s/g, '');
                            try {
                                res = await recognizeBankCardByCode({
                                    bankCardNo: this.formData.accountNumber,
                                });
                            } catch (e) {
                                return;
                            }

                            const bankData = res?.data || {};
                            // 填充开户银行信息
                            this.formData.bankCode = bankData.bankCode;
                            this.formData.bankName = bankData.bankName;
                        },
                    },
                    {
                        label: '开户银行',
                        prop: 'bankCode',
                        type: 'select',
                        required: true,
                        withSearch: true,
                        fetchSuggestions: (queryString) => {
                            const results = queryString ?
                                this.allBankOptions.filter((item) =>
                                    item.label.toLowerCase().includes(queryString.toLowerCase()),
                                ) :
                                this.allBankOptions;

                            // 更新当前选项
                            const bankCodeItem = this.getFormSchemaGroup('bankAccount', 'bankCode');
                            if (bankCodeItem) {
                                bankCodeItem.options = results;
                            }
                        },
                        hiddenRedDot: true,
                        options: this.allBankOptions,
                        onChange: async () => {
                            // 清空支行选择
                            this.formData.cnapsNo = '';
                            this.formData.bankBranch = '';

                            await this.getBankBranchOptions();
                        },
                    },
                    {
                        label: '开户支行',
                        prop: 'cnapsNo',
                        type: 'select',
                        required: true,
                        hiddenRedDot: true,
                        withSearch: true,
                        fetchSuggestions: async (queryString) => {
                            this.getBankBranchOptions(queryString);
                        },
                        placeholder: '请选择开户支行',
                        options: [],
                        onChange: (value) => {
                            const bankBranchItem = this.getFormSchemaGroup('bankAccount', 'cnapsNo');
                            const options = bankBranchItem?.options || [];
                            const selectedOption = options.find((item) => item.value === value);
                            if (selectedOption) {
                                this.formData.bankBranch = selectedOption.label;
                            }
                        },
                    },
                    {
                        label: this.formData.accountType === AccountTypeEnum.CORPORATE ? '收款账户证明' : '银行卡正面照片',
                        prop: 'accountProofGroup',
                        type: 'group',
                        required: true,
                        hiddenRedDot: true,
                        schema: [
                            {
                                label: '以下任选一张图片上传：开户许可证、印鉴卡电子回单、基本存款信息。注意：原件带银行电子章；扫描/复印件需加盖公章（红章）',
                                text: this.formData.accountType === AccountTypeEnum.CORPORATE ? '以下任选一张图片上传：开户许可证、印鉴卡电子回单、基本存款信息。注意：原件带银行电子章；扫描/复印件需加盖公章（红章）' : '正面，包含卡片四角，图片清晰',
                                prop: 'accountProofImageText',
                                type: 'text',
                            },
                            {
                                label: this.formData.accountType === AccountTypeEnum.CORPORATE ? '收款账户证明' : '银行卡正面照片',
                                prop: 'accountProofImageUrl',
                                type: 'file',
                                required: true,
                                hiddenRedDot: true,
                                exampleLink: this.formData.accountType === AccountTypeEnum.CORPORATE ? 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/shoukuanzhengming.jpg' : 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/yinhangka.jpg',
                                businessDesc: this.formData.accountType === AccountTypeEnum.CORPORATE ? '账户材料' : '上传银行卡',
                                validateEvent(value, callback) {
                                    const { accountProofImageUrl } = _this.formData;
                                    if (!accountProofImageUrl?.length) {
                                        callback({
                                            validate: false,
                                            message: `请上传${_this.formData.accountType === AccountTypeEnum.CORPORATE ? '收款账户证明' : '银行卡正面照片'}`,
                                        });
                                        return;
                                    }
                                    const file = accountProofImageUrl[0];
                                    if (!file?.url) {
                                        callback({
                                            validate: false,
                                            message: '收款账户证明上传失败，请重新上传',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                        ],

                    },
                    {
                        label: '结算方式',
                        prop: 'settlementMethod',
                        type: 'select',
                        options: SettlementMethodOptions,
                        required: true,
                        hiddenRedDot: true,
                    },
                    {
                        label: '管理员',
                        prop: 'adminName',
                        hidden: this.formData.operatorType === OperatorTypeEnum.OPERATOR,
                        type: 'input',
                        required: true,
                        hiddenRedDot: true,
                        placeholder: '请输入管理员姓名',
                        maxLength: 6,
                    },
                    {
                        label: '管理员电话',
                        prop: 'adminPhone',
                        type: 'input',
                        required: true,
                        hidden: this.formData.operatorType === OperatorTypeEnum.OPERATOR,
                        hiddenRedDot: true,
                        maxLength: 11,
                        placeholder: '用于登录对账平台，请确保手机号正常使用',
                        validateEvent(value, callback) {
                            if (!value) {
                                callback({
                                    validate: false,
                                    message: '请输入管理员电话',
                                });
                                return;
                            }
                            const phoneReg = /^1[3-9]\d{9}$/;
                            if (!phoneReg.test(value)) {
                                callback({
                                    validate: false,
                                    message: '请输入正确的手机号',
                                });
                                return;
                            }
                            callback({ validate: true });
                        },
                    },
                ],
            },
            {
                sort: 5,
                title: '经营场所',
                prop: 'businessPlace',
                hideGroupDivider: true,
                schema: [
                    {
                        label: '门头图片',
                        prop: 'shopFrontGroup',
                        type: 'group',
                        hiddenRedDot: true,
                        schema: [
                            {
                                label: '门头需完整、清晰',
                                text: '门头需完整、清晰',
                                prop: 'shopFrontText',
                                type: 'text',
                            },
                            {
                                label: '门头照片',
                                prop: 'storeFrontImageUrl',
                                businessDesc: '门头照片',
                                type: 'file',
                                required: true,
                                hiddenRedDot: true,
                                exampleLink: 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/mentou.jpg',

                                validateEvent(value, callback) {
                                    const { storeFrontImageUrl } = _this.formData;
                                    if (!storeFrontImageUrl?.length) {
                                        callback({
                                            validate: false,
                                            message: '请上传门头照片',
                                        });
                                        return;
                                    }
                                    const file = storeFrontImageUrl[0];
                                    if (!file?.url) {
                                        callback({
                                            validate: false,
                                            message: '门头照片上传失败，请重新上传',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                        ],
                    },
                    {
                        label: '经营内景照',
                        prop: 'businessInteriorGroup',
                        type: 'group',
                        hiddenRedDot: true,
                        schema: [
                            {
                                label: '需体现经营内容，图片清晰',
                                text: '需体现经营内容，图片清晰',
                                prop: 'businessInteriorText',
                                type: 'text',
                            },
                            {
                                label: '经营照片',
                                prop: 'interiorImageUrl',
                                businessDesc: '经营照片',
                                type: 'file',
                                required: true,
                                hiddenRedDot: true,
                                exampleLink: 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/neijing.jpg',

                                validateEvent(value, callback) {
                                    const { interiorImageUrl } = _this.formData;
                                    if (!interiorImageUrl?.length) {
                                        callback({
                                            validate: false,
                                            message: '请上传经营内景照片',
                                        });
                                        return;
                                    }
                                    const file = interiorImageUrl[0];
                                    if (!file?.url) {
                                        callback({
                                            validate: false,
                                            message: '经营内景照片上传失败，请重新上传',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                        ],
                    },
                    {
                        label: '经营场所证件',
                        prop: 'leaseContractImageGroup',
                        type: 'group',
                        hidden: !isPersonal,
                        schema: [
                            {
                                label: '以下任选一张图片上传：经营场所租赁合同、产权证。',
                                text: '以下任选一张图片上传：经营场所租赁合同、产权证。',
                                prop: 'leaseContractText',
                                type: 'text',
                            },
                            {
                                label: '经营场所证件',
                                prop: 'leaseContractImageUrl',
                                businessDesc: '经营场所证件',
                                type: 'file',
                                required: true,
                                hiddenRedDot: true,
                                exampleLink: 'https://static-common-cdn.abcyun.cn/img/abc-payment/ABCpayment-kaitongsucai/shilitu/chanquan.jpg',

                                validateEvent(value, callback) {
                                    const { interiorImageUrl } = _this.formData;
                                    if (!interiorImageUrl?.length) {
                                        callback({
                                            validate: false,
                                            message: '请上传经营场所证件照片',
                                        });
                                        return;
                                    }
                                    const file = interiorImageUrl[0];
                                    if (!file?.url) {
                                        callback({
                                            validate: false,
                                            message: '经营场所证件照片上传失败，请重新上传',
                                        });
                                        return;
                                    }
                                    callback({ validate: true });
                                },
                            },
                        ],
                    },
                ],
            },
        ];
        await this.getBankBranchOptions();
    }

    checkStatus(isSkipCompliance = false) {
        // 检查进件审核状态
        const {
            approvalStatus, status,handleState,
        } = this.statusEnum;
        this.isHandleState = false;
        this.isAutoHeight = false;
        this.setActiveStep(ActiveStepEnum.SUBMIT_MATERIAL);
        if (approvalStatus === ApprovalStatus.REJECTED ||
            approvalStatus === ApprovalStatus.SUBMIT_FAILED) {
            this.contentLoading = false;
        } else if (approvalStatus === ApprovalStatus.PENDING) {
            this.contentLoading = false;
            AbcModal.alert({
                title: '提示',
                content: '您的申请正在审核中，请耐心等待',
            });
            return;
        }
        if (approvalStatus === ApprovalStatus.REJECTED || approvalStatus === ApprovalStatus.SUBMIT_FAILED) {
            this.contentLoading = false;
            const isSkip = this.handleComplianceErrors();
            if (!isSkip) {
                AbcModal.alert({
                    title: '提示',
                    content: '递交材料有误，请联系ABC客服协助您处理。',
                });
            }
            return;
        }
        if (!isSkipCompliance && handleState && handleState !== ComplianceResult.NORMAL) {
            this.contentLoading = false;
            this.handleApiErrors(this.statusEnum);
            this.isHandleState = true;
            this.isAutoHeight = true;
            return;
        }
        if (approvalStatus === ApprovalStatus.APPROVED) {
            this.setActiveStep(ActiveStepEnum.SIGNING_CERTIFICATION);
            this.contentLoading = false;
            return;
        }

        // 根据审核状态判断当前步骤
        if (this.formData.id && ![
            FormStatusEnum.DRAFT,
            FormStatusEnum.APPROVAL_ING,
            FormStatusEnum.FROZEN,
        ].includes(status)) {
            this.setActiveStep(ActiveStepEnum.SIGNING_CERTIFICATION);
        }
    }

    createErrorMessage(error, message) {
        return {
            schema: error.schema,
            group: error.group,
            field: error.field,
            onMessageClick: error.onMessageClick,
            errorlinkText: '',
            message,
        };
    }

    handleComplianceErrors() {
        const { approvalFailReason } = this.statusEnum;
        const errorMappings = [
            {
                condition: approvalFailReason.includes('身份证已过期'),
                error: FormFieldAuditMap.legalidresult,
                message: '驳回原因：身份证已过期',
            },
            {
                condition: approvalFailReason.includes('断卡行动'),
                error: FormFieldAuditMap.accountNumber,
                message: '驳回原因：收款账户银行卡黑名单',
            },
            {
                condition: approvalFailReason.includes('名称已存在'),
                error: {
                    ...FormFieldAuditMap.creditresult, group: 'businessLicenseName', field: '',
                },
                message: '驳回原因：该营业执照名称已注册过商户',
            },
            {
                condition: approvalFailReason.includes('账户三要素') && approvalFailReason.includes('不一致'),
                errors: [FormFieldAuditMap.accountName ,FormFieldAuditMap.accountNumber],
                message: '驳回原因：收款账户的账户名称和银行卡号不匹配',
            },
            {
                condition: approvalFailReason.includes('营业执照与企业信息校验不一致'),
                errors: [FormFieldAuditMap.creditresult, FormFieldAuditMap.legalidresult],
                message: '驳回原因：营业执照名称、统一社会信用代码、法人与工商网信息不一致',
            },
            {
                condition: approvalFailReason.includes('身份证正面OCR识别失败'),
                errors: [FormFieldAuditMap.legalidresult],
                message: '驳回原因：身份证人像面与国徽面顺序上传错误，请修改后重试',
            },
        ];

        for (const {
            condition, error, errors, message,
        } of errorMappings) {
            if (condition) {
                const errorMessages = Array.isArray(errors) ?
                    errors.map((err) => this.createErrorMessage(err, message)) :
                    [this.createErrorMessage(error, message)];
                this.setErrorMessages(errorMessages);
                this.isAutoHeight = true;
                return true;
            }
        }

        if (approvalFailReason.includes('超过最大') || approvalFailReason.includes('超过最小')) {
            const itemMap = [
                {
                    key: 'headresult', label: '门头照', errorLabel: '门头照片',
                },
                {
                    key: 'creditresult', label: '营业执照', errorLabel: '营业执照',
                },
                {
                    key: 'legalidresult', label: '身份证正反面', errorLabel: '身份证',
                },
                {
                    key: 'legalidresult', label: '法人证件', errorLabel: '身份证',
                },
                {
                    key: 'handpickresult', label: '手持', errorLabel: '个人手持身份证',
                },
                {
                    key: 'clearparamresult', label: '结算', errorLabel: '收款账户证明',
                },
                {
                    key: 'innerresult', label: '经营内景', errorLabel: '经营内景照片',
                },
                {
                    key: 'clearparamresult', label: '开户许可证', errorLabel: '收款账户证明',
                },
            ];
            const item = itemMap.find((i) => approvalFailReason.includes(i.label));
            if (item) {
                const error = FormFieldAuditMap[item.key] || {};
                const errorMessages = [this.createErrorMessage(error, `驳回原因：${item.errorLabel}文件大小超过最${approvalFailReason.includes('超过最大') ? '大(2MB)' : '小'}限制`)];
                this.setErrorMessages(errorMessages);
                this.isAutoHeight = true;
                return true;
            }
        }

        return false;
    }

    async openModal(isChainAdmin, isSkipCompliance = false) {
        this.formData.merchantShortName = this.currentClinic?.shortName;
        await this.loadDraft();
        await this.initFormSchema(isChainAdmin);
        this.qrCodeSrc = `${getGlobalHost()}/m/aggregate-payment?clinicId=${this.currentClinic.clinicId}&chainId=${this.currentClinic.chainId}&employeeId=${this.currentClinic.userId}&isSkipCompliance=${isSkipCompliance ? 1 : 0}`;
        this.checkStatus(isSkipCompliance);

        /* 初始化营业执照图片 法人身份证*/
        await this.initClinicCerts();

        this.sourceFormData = cloneDeep(this.formData);
        this.visible = true;
    }

    /**
     * @desc: 获取门店的资质证照数据
     * @author: ff
     * @time: 2025/5/13
     */
    async initClinicCerts() {
        try {
            const res = await SettingsApi.clinic.fetchClinicInfoById();
            const { data } = res.data;

            await this.initBusinessLicenseImage(data.certs);
        } catch (e) {
            // 出错信息
            console.error(e);
        }
    }
    /**
     * @desc: 解析资质证照图片的数据
     * @author: ff
     * @time: 2025/5/13
     */
    async parseBusinessLicense(files) {
        const isEnterprise = this.formData.merchantType === MerchantTypeEnum.ENTERPRISE;
        const file = files[0];
        if (!file) return;
        let res = {};
        try {
            res = await recognizeBusinessLicense({
                url: file.url,
            });
        } catch (e) {
            return;
        }

        const data = res?.data?.data || {};
        data.companyName = data.companyName?.replace(/\(/g, '（').replace(/\)/g, '）');
        this.formData.businessLicenseName = data.companyName;
        if (isEnterprise) {
            this.formData.accountName = data.companyName;
        }

        this.formData.unifiedSocialCreditCode = data.creditCode;
        this.formData.businessLicenseValidity = data.validToDate || this.formData.businessLicenseValidity;
        this.formData.businessLicenseValidityForever = this.formData.businessLicenseValidity === '长期';
        if (data.type !== -1) {
            this.formData.merchantType = data.type;
            this.handleMerchantTypeChange(data.type);
        }

    }

    async parseIdCardFace(files) {
        const file = files[0];
        if (!file) return;
        let res = {};
        try {
            res = await recognizeIdcardFace({
                url: file.url,
            });
        } catch (e) {
            return;
        }

        const data = res?.data?.data || {};
        const idCardData = data.face?.data;

        // 处理地址信息
        if (idCardData) {

            // 填充身份证信息
            this.formData.legalPersonName = idCardData.name;
            this.formData.idCardNumber = idCardData.idNumber;
            this.formData.idCardAddress = {
                addressCityId: idCardData.addressCityId,
                addressCityName: idCardData.addressCityName,
                addressDistrictId: idCardData.addressDistrictId,
                addressDistrictName: idCardData.addressDistrictName,
                addressProvinceId: idCardData.addressProvinceId,
                addressProvinceName: idCardData.addressProvinceName,
            };
            this.formData.idCardAddressDetail = idCardData.address || '';
        }
    }
    async parseIdCardBack(files) {
        const file = files[0];
        if (!file) return;
        let res = {};
        try {
            res = await recognizeIdcardBack({
                url: file.url,
            });
        } catch (e) {
            return;
        }

        const data = res?.data?.data || {};
        const idCardData = data.back?.data || {};
        // 处理身份证反面信息，从有效期中提取结束日期
        if (idCardData.validPeriod) {
            const endDate = idCardData.validDate.replace(/\./g, '-');
            this.formData.idCardValidity = endDate?.trim() || '';
        }
        this.formData.idCardValidityForever = idCardData.validDate === '长期';
    }


    /**
     * @desc: 获取资质的图片，如果填写的资料默认没有营业执照等图片，则取资质接口中的数据
     * @author: ff
     * @time: 2025/5/13
     */
    async initBusinessLicenseImage(certs) {
        if (!certs) return;
        const {
            businessLicense, idCardFront, idCardBackend,
        } = certs;
        if (!this.formData.businessLicenseImageUrl?.length && businessLicense) {
            this.formData.businessLicenseImageUrl = [{
                url: businessLicense,
            }];
            await this.parseBusinessLicense([{
                url: businessLicense,
            }]);
        }
        if (!this.formData.idCardBackFile?.length && idCardBackend) {
            this.formData.idCardBackFile = [{
                url: idCardBackend,
            }];
            await this.parseIdCardBack([{
                url: idCardBackend,
            }]);

        }
        if (!this.formData.idCardFrontFile?.length && idCardFront) {
            this.formData.idCardFrontFile = [{
                url: idCardFront,
            }];
            await this.parseIdCardFace([{
                url: idCardFront,
            }]);
        }
    }

    setActiveStep(step) {
        this.activeStep = step;
    }

    // 关闭弹窗前的确认
    async beforeClose() {
        this.isDirty = !isEqual?.(this.formData, this.sourceFormData);
        if (this.isDirty && this.activeStep === ActiveStepEnum.SUBMIT_MATERIAL && !this.isHandleState) {
            if (await this.saveDraft()) {
                this.closeModal();
            }
            return;
        }

        this.closeModal();
    }

    closeModal() {
        // this.formData = {
        //     // 基础信息 (basicInfo)
        //     merchantType: MerchantTypeEnum.ENTERPRISE , //商户性质，默认为企业
        //     merchantShortName: '' , //商户简称
        //     businessAddress: {
        //         addressCityId: '',
        //         addressCityName: '',
        //         addressProvinceId: '',
        //         addressProvinceName: '',
        //         addressDistrictId: '',
        //         addressDistrictName: '',
        //     } , //省市区
        //     businessAddressDetail: '' , //详细地址
        //     industry: '' , //所属行业
        //     settlementType: SettlementTypeEnum.RESPECTIVELY , //连锁下各门店资金如何结算

        //     // 营业执照 (businessLicense)
        //     businessLicenseImageUrl: [] , //营业执照图片
        //     businessLicenseName: '' , //营业执照名称
        //     unifiedSocialCreditCode: '' , //统一社会信用代码
        //     businessLicenseValidity: '长期' , //有效期
        //     businessLicenseValidityForever: true, //营业执照长期有效

        //     // 法人信息 (legalPerson)
        //     idCardFrontFile: [] , //身份证正面
        //     idCardBackFile: [] , //身份证反面
        //     idCardHoldingFile: [] , //手持身份证照片
        //     legalPersonName: '' , //法人姓名
        //     idCardNumber: '' , //身份证号码
        //     idCardValidity: '' , //证件有效期
        //     idCardValidityForever: false, //身份证长期有效
        //     idCardAddress: {
        //         addressCityId: '',
        //         addressCityName: '',
        //         addressProvinceId: '',
        //         addressProvinceName: '',
        //         addressDistrictId: '',
        //         addressDistrictName: '',
        //     } , //省市区
        //     idCardAddressDetail: '' , //详细地址
        //     legalPersonPhone: '' , //法人手机号
        //     authorizationLetterUrl: [], // 法人授权书

        //     // 收款账户 (bankAccount)
        //     accountType: AccountTypeEnum.CORPORATE , //账户类型，默认为对公账户
        //     accountName: '' , //账户名称
        //     accountNumber: '' , //账户号
        //     bankCode: '' , //开户银行
        //     bankBranch: '', //开户支行名称
        //     cnapsNo: '' , //开户支行
        //     accountProofImageUrl: [] , //收款账户证明
        //     settlementMethod: SettlementMethodEnum.BANK_CARD , //结算方式，默认为自动提现到银行卡
        //     adminName: '' , //管理员
        //     adminPhone: '' , //管理员电话

        //     // 经营场所 (businessPlace)
        //     storeFrontImageUrl: [] , //门头照片
        //     leaseContractImageUrl: [] , // 经营场所证件
        //     interiorImageUrl: [] , //经营内景照

        // }; // 表单数据
        this.loadDraft();
        this.visible = false;
    }

    // 设置错误信息
    setErrorMessages(errors = []) {

        errors.forEach((error) => {

            const {
                schema,
                group,
                field,
                message,
                onMessageClick,
                errorlinkText,
            } = error;

            // 遍历formSchema查找对应的item
            const errorItem = field ? this.getFormSchemaGroupItem(schema, group, field) : this.getFormSchemaGroup(schema, group);
            if (errorItem) {
                errorItem.errorMessage = message;
                errorItem.onMessageClick = onMessageClick?.bind(this);
                errorItem.errorlinkText = errorlinkText;
            }
        });
        this.getErrorItems();
    }

    // 获取带有错误信息的表单项
    getErrorItems() {

        this.formSchema.forEach((formGroup) => {
            if (formGroup.schema) {
                const errorSchemas = [];

                formGroup.schema.forEach((item) => {
                    if (['businessLicense', 'legalPerson', 'bankAccount'].includes(formGroup.prop)) {
                        const errorItem = formGroup.schema.find((group) => {
                            return group.errorMessage || group.schema?.some((groupItem) => groupItem.errorMessage);
                        });
                        if (errorItem && ['idCardGroup','businessLicenseGroup', 'businessLicenseName', 'accountName', 'accountNumber', 'bankCode', 'cnapsNo'].includes(errorItem.prop)) {
                            item.hidden = !errorItem;
                            // 如果是身份证： 法人手机号不显示
                            if (errorItem.prop === 'idCardGroup') {
                                const phoneGroup = this.getFormSchemaGroup('legalPerson', 'phoneGroup');
                                phoneGroup.hidden = true;
                            }
                            if (!errorSchemas.find((schema) => schema.prop === errorItem.prop)) {
                                errorSchemas.push(errorItem);
                            }
                            // 跳出本次循环
                            return;
                        }
                        if (errorItem && errorItem.prop === 'authorizationLetterGroup') {
                            item.hidden = !errorItem;
                            const phoneGroup = this.getFormSchemaGroup('legalPerson', 'phoneGroup');
                            phoneGroup.hidden = false;
                            if (!errorSchemas.find((schema) => schema.prop === errorItem.prop)) {
                                errorSchemas.push(errorItem);
                            }
                        }
                    }
                    if (item.type === 'group' && item.schema) {
                        // 处理group类型
                        const hasError = item.schema.some((groupItem) => groupItem.errorMessage);
                        // 如果为门头照，带出商户简称
                        if (hasError && item.prop === 'shopFrontGroup') {
                            const basicInfoItem = this.getFormSchemaItem('basicInfo');
                            const merchantShortNameItem = this.getFormSchemaGroup('basicInfo', 'merchantShortName');

                            merchantShortNameItem.hidden = false;
                            basicInfoItem.hidden = false;
                        }
                        item.hidden = !hasError;
                        if (hasError) {
                            errorSchemas.push(item);
                        }
                    } else {
                        item.hidden = !item.errorMessage;
                        // 处理普通item
                        item.errorMessage && errorSchemas.push(item);
                    }
                });

                formGroup.hidden = errorSchemas.length === 0;
            }
        });
    }

    // 处理接口返回的错误信息
    handleApiErrors(response) {

        const {
            handleState, handleDetail,
        } = response;
        if (handleState === ComplianceResult.NORMAL) {
            return;
        }

        const errorMessages = [];
        // 处理审核详情
        if (typeof handleDetail === 'object' && handleDetail) {
            Object.entries(handleDetail).forEach(([key, value]) => {
                const error = FormFieldAuditMap[key];

                if (value && value !== '1' && error) {
                    errorMessages.push({
                        schema: error.schema,
                        group: error.group,
                        field: error.field,
                        onMessageClick: error.onMessageClick,
                        errorlinkText: error.errorlinkText,
                        message: `驳回原因：${error.errorMessageEnum[value]}`,
                    });
                }
            });
        }
        this.setErrorMessages(errorMessages);
        return errorMessages;
    }

    // 设置loading状态
    setLoading(status) {
        this.loading = status;
    }

    //  获取参数
    getParams() {
        const {
            // 基础信息 (basicInfo)
            merchantType , //商户性质，默认为企业
            merchantShortName , //商户简称
            businessAddress , //省市区
            businessAddressDetail , //详细地址
            industry , //所属行业
            settlementType , //连锁下各门店资金如何结算

            // 营业执照 (businessLicense)
            businessLicenseImageUrl , //营业执照图片
            businessLicenseName , //营业执照名称
            unifiedSocialCreditCode , //统一社会信用代码
            businessLicenseValidity , //有效期

            // 法人信息 (legalPerson)
            idCardFrontFile , //身份证正面
            idCardBackFile , //身份证反面
            idCardHoldingFile , //手持身份证照片
            legalPersonName , //法人姓名
            idCardNumber , //身份证号码
            idCardValidity , //证件有效期
            idCardAddress , //省市区
            idCardAddressDetail , //详细地址
            operatorType, // 当前操作人身份
            legalPersonPhone , //法人手机号
            authorizationLetterUrl, //法人授权书
            agentName, // 经办人姓名
            agentPhone, // 经办人手机号

            // 收款账户 (bankAccount)
            accountType , //账户类型
            accountName , //账户名称
            accountNumber , //账户号
            bankCode , //开户银行
            bankName , //开户银行名称
            bankBranch, //开户支行名称
            cnapsNo , //开户支行
            accountProofImageUrl , //收款账户证明
            settlementMethod , //结算方式，默认为自动提现到银行卡

            // 经营场所 (businessPlace)
            storeFrontImageUrl , //门头照片
            leaseContractImageUrl , // 经营场所证件
            interiorImageUrl , //经营内景照

        } = this.formData;

        // 处理地址信息
        const businessAddressInfo = businessAddress || {};
        const idCardAddressInfo = idCardAddress || {};
        const idCardAddressStr = [idCardAddressInfo.addressProvinceName, idCardAddressInfo.addressCityName, idCardAddressInfo.addressDistrictName, idCardAddressDetail].filter((item) => item).join('-');

        // 如果是法人操作，管理员信息使用法人信息
        const adminName = operatorType === OperatorTypeEnum.LEGAL_PERSON ? this.formData.adminName : agentName;
        const adminPhone = operatorType === OperatorTypeEnum.LEGAL_PERSON ? this.formData.adminPhone : agentPhone;

        return {
            // 基础信息 (basicInfo)
            merchantType , //商户性质，默认为企业
            merchantShortName , //商户简称
            industry , //所属行业
            settlementType , //连锁下各门店资金如何结算
            // 经营地址
            addressProvinceId: businessAddressInfo.addressProvinceId,
            addressProvinceName: businessAddressInfo.addressProvinceName,
            addressCityId: businessAddressInfo.addressCityId,
            addressCityName: businessAddressInfo.addressCityName,
            addressDistrictId: businessAddressInfo.addressDistrictId,
            addressDistrictName: businessAddressInfo.addressDistrictName,
            addressDetail: businessAddressDetail,

            // 营业执照 (businessLicense)
            businessLicenseImageUrl: businessLicenseImageUrl?.[0]?.url , //营业执照图片
            businessLicenseName , //营业执照名称
            unifiedSocialCreditCode , //统一社会信用代码
            businessLicenseValidity , //有效期
            idCardHandheldUrl: idCardHoldingFile?.[0]?.url,

            // 法人信息 (legalPerson)
            idCardFrontUrl: idCardFrontFile?.[0]?.url , //身份证正面
            idCardBackUrl: idCardBackFile?.[0]?.url , //身份证反面
            legalPersonName , //法人姓名
            idCardNumber , //身份证号码
            idCardValidity , //证件有效期
            idCardAddress: idCardAddressStr,
            legalPersonPhone: legalPersonPhone || agentPhone, //法人手机号
            authorizationLetterUrl: authorizationLetterUrl?.[0]?.url, // 法人授权书
            operatorType, // 当前操作人身份
            agentName, // 经办人姓名
            agentPhone, // 经办人手机号
            adminName, // 管理员
            adminPhone, // 管理员电话

            // 收款账户 (bankAccount)
            accountType , //账户类型
            accountName , //账户名称
            accountNumber , //账户号
            bankCode , //开户银行
            bankName,
            bankBranch, //开户支行名称
            cnapsNo , //开户支行
            accountProofImageUrl: accountProofImageUrl?.[0]?.url , //收款账户证明
            settlementMethod , //结算方式，默认为自动提现到银行卡

            // 经营场所 (businessPlace)
            storeFrontImageUrl: storeFrontImageUrl?.[0]?.url,
            leaseContractImageUrl: leaseContractImageUrl?.[0]?.url,
            interiorImageUrl: interiorImageUrl?.[0]?.url,

            // 其他信息
            // status: 0: 草稿；10: 提交
            status: 10,
            id: this.formData.id,
        };
    }

    // 打开功能介绍弹窗
    openFunctionIntroduction() {
        this.functionIntroductionVisible = true;
    }

    // 关闭功能介绍弹窗
    closeFunctionIntroduction() {
        this.functionIntroductionVisible = false;
    }
}
