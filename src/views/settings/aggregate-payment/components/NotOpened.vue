<template>
    <div class="not-opened">
        <div class="not-opened-wrapper">
            <div class="pay-img">
                <img src="~assets/images/aggregate-payment/<EMAIL>" />
                <abc-space class="open-status" :size="12">
                    <abc-button
                        v-if="!isIntranetUser"
                        size="large"
                        class="actions-submit"
                        theme="warning"
                        @click="handleActive"
                    >
                        立即开通
                    </abc-button>
                    <abc-button
                        size="large"
                        class="actions-info"
                        variant="ghost"
                        @click="showFunctionIntroduction"
                    >
                        功能介绍
                    </abc-button>
                </abc-space>
            </div>
            <div class="img-box">
                <div v-if="showOneCodePay" @click="openOneCodePayOperation">
                    <img style="cursor: pointer;" src="~assets/images/aggregate-payment/img-yimafu-new.png" />
                </div>
                <div><img src="~assets/images/aggregate-payment/<EMAIL>" /></div>
                <div><img src="~assets/images/aggregate-payment/<EMAIL>" /></div>
                <div><img src="~assets/images/aggregate-payment/<EMAIL>" /></div>
            </div>
        </div>
        <dialog-detail :value="showDialog" @input="handleInput"></dialog-detail>
    </div>
</template>

<script>
    import DialogDetail from '../dialog-detail';
    import AggregatedPaymentDialogManager
        from 'views/settings/aggregate-payment/components/dialog-aggregated-payment/manager';
    import { BusinessKeyMap } from 'assets/configure/buried-point';
    import { mapGetters } from 'vuex';
    import { checkCanApplyAllinPay } from '@/api/settings/aggregate-payment';
    import OneCodePayOperationModal from 'views/settings/aggregate-payment/components/one-code-pay-operation';
    import OneCodePayOpenModal from 'views/settings/aggregate-payment/components/one-code-pay-open';

    export default {
        components: {
            DialogDetail,
        },
        data() {
            return {
                showDialog: false,
                aggregatePaymentManager: AggregatedPaymentDialogManager.getInstance(),
            };
        },
        computed: {
            ...mapGetters(['userInfo', 'isChainAdmin', 'isChainSubStore', 'isIntranetUser']),
            showOneCodePay() {
                return this.$abcSocialSecurity.isShowOneCodePay;
            },
        },
        beforeDestroy() {
            this._oneCodePayOperationModal = null;
            this._oneCodePayOpen = null;
        },
        methods: {
            async handleActive() {
                if (this.isChainSubStore) {
                    const canApply = await this.checkApplyAllinPay();
                    if (canApply?.code !== 0) {
                        return;
                    }
                }
                this.aggregatePaymentManager.report(BusinessKeyMap.CLK_AGGREGATE_PAYMENT_OPEN_NOT_OPENED, this.userInfo);
                this.aggregatePaymentManager.openModal(this.isChainAdmin);
            },
            async checkApplyAllinPay() {
                try {
                    const { data: canApply } = await checkCanApplyAllinPay();
                    if (canApply?.code !== 0) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: canApply?.code === -1 ? '请先联系总部在此页面申请开通，总部开通成功后子店再进行申请' : '总部开通统一结算，子店无需开通',
                        });
                    }
                    return canApply;
                } catch (error) {
                    console.error('检查是否可以申请失败:', error);
                }
            },
            showDetail() {
                this.showDialog = true;
            },

            handleInput(val) {
                this.showDialog = val;
            },
            showFunctionIntroduction() {
                this.aggregatePaymentManager.openFunctionIntroduction();
            },
            openOneCodePayOperation() {
                this.aggregatePaymentManager.report(BusinessKeyMap.CLK_ONE_CODE_PAY_IN_AGGREGATE_PAYMENT, this.userInfo);

                this._oneCodePayOperationModal = new OneCodePayOperationModal({
                    onConfirm: () => {
                        this._oneCodePayOpen = new OneCodePayOpenModal({
                        }).generateDialogAsync({ parent: this });
                    },
                }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/styles/theme.scss';
@import 'styles/abc-common.scss';

.not-opened {
    position: relative;

    .not-opened-wrapper {
        .pay-img {
            position: relative;
            width: 100%;
            //height: 100px;

            img {
                width: 100%;
                height: 100%;
                border-radius: var(--abc-border-radius-small);
            }

            .open-status {
                position: absolute;
                top: 50%;
                right: 60px;
                width: 190px;
                height: 40px;
                transform: translateY(-50%);

                .actions-submit {
                    color: var(--abc-color-T1);
                    background: linear-gradient(90.29deg, #e2b776 0.29%, #efcd9c 99.81%);
                    border: var(--abc-border-1, 1px) solid linear-gradient(259.09deg, rgba(255, 255, 255, 0.3) 19.43%, rgba(255, 255, 255, 0.1) 81.18%);
                }

                .actions-info {
                    color: var(--abc-color-Y6);
                    border: 1px solid rgba(0, 0, 0, 0.08);
                }

                .tips {
                    position: absolute;
                    top: 50%;
                    right: 38px;
                    height: 24px;
                    font-size: 20px;
                    font-weight: 400;
                    line-height: 24px;
                    color: #ff5100;
                    transform: translateY(-12px);
                }
            }
        }

        .img-box {
            @include flex(row, space-between, center);

            flex-wrap: wrap;
            gap: 24px;
            width: 100%;
            //height: 644px;
            margin-top: 24px;

            div {
                width: calc((100% - 24px) / 2);

                img {
                    width: 100%;
                    height: 100%;
                    border-radius: var(--abc-border-radius-small);
                }
            }
        }

        .abc-pay-tips {
            display: flex;
            width: 100%;
            height: 66px;
            padding: 12px;
            margin-top: 24px;
            line-height: 66px;
            background: #fff9f2;
            border-radius: var(--abc-border-radius-small);

            .left > img {
                width: 42px;
                height: 42px;
            }

            .right {
                margin-left: 12px;

                .discount-info {
                    margin-bottom: 4px;
                    font-size: 14px;
                    font-weight: 500;
                    line-height: 20px;

                    .discount-name {
                        color: #000000;
                    }

                    .discount-rate {
                        margin: 0 16px 0 4px;
                        color: #ff9933;
                    }

                    .to-detail {
                        font-weight: 400;
                    }
                }

                .tips {
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 18px;
                    color: #7a8794;
                }
            }
        }

        .wechat-pay-info {
            padding-top: 24px;

            .name {
                padding-bottom: 8px;
                font-size: 14px;
                font-weight: 500;
                color: $T1;
                border-bottom: 1px dashed $P3;
            }

            .wechat-content {
                .info-detail {
                    > li {
                        display: flex;
                        align-items: center;
                        margin-top: 24px;
                        font-size: 14px;

                        &:first-child {
                            margin-top: 12px;
                        }

                        .item-key {
                            width: 84px;
                            color: $T2;
                        }

                        .item-value {
                            margin-left: 16px;
                            color: $T1;

                            .item-error {
                                color: $Y2;
                            }

                            .error-tip {
                                margin-left: 8px;
                                color: $T2;
                            }
                        }
                    }
                }
            }
        }
    }
}

.application-dialog {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 312px;
    color: $T3;

    p {
        height: 20px;
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;

        &:first-child {
            color: #000000;
        }
    }
}
</style>
