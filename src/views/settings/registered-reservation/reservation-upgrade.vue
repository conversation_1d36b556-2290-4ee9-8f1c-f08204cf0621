<template>
    <biz-setting-layout
        v-abc-loading="loading || propertyLoading"
        class="reservation-setting"
    >
        <biz-setting-content>
            <abc-form ref="form" item-no-margin>
                <biz-setting-form :key="`setting-form-${postData.modeType}`" :label-width="98" style="position: relative;">
                    <biz-setting-form-group v-if="isTreatmentReservation">
                        <biz-setting-form-item label="治疗理疗预约">
                            <biz-setting-form-item-tip tip="开启后，患者可预约理疗师或理疗项目">
                                <abc-checkbox
                                    v-model="postData.isOpen"
                                    data-type="label-align"
                                    type="number"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <!--预览图-->
                    <div v-if="postData.isOpen" class="display-wrapper">
                        <span>{{ displayStr }}</span>
                        <img :src="tipImageUrl" />
                    </div>

                    <template v-if="postData.isOpen">
                        <!-- 固定号源 -->
                        <template v-if="isFixedNumberSource">
                            <!--预约模式-->
                            <biz-setting-form-group
                                title="预约模式"
                            >
                                <div class="setting-item" style="margin-bottom: 24px;">
                                    <div class="content c-content">
                                        <abc-space :size="16">
                                            <abc-option-card
                                                v-for="(o, key) in reservationModeOpts"
                                                :key="key"
                                                :width="260"
                                                :show-icon="false"
                                                :value="postData.modeType === key"
                                                theme="success"
                                                selectable="icon-inside"
                                                :title="o.title"
                                                :title-config="{
                                                    size: 'normal'
                                                }"
                                                :description="o.intro"
                                                :description-config="{
                                                    size: 'mini',
                                                    theme: 'gray'
                                                }"
                                                @change="handleModeCardClick(key)"
                                            >
                                            </abc-option-card>
                                        </abc-space>
                                    </div>
                                </div>

                                <!--选号方式-->
                                <biz-setting-form-item label="选号方式">
                                    <abc-radio-group
                                        v-model="postData.fixedOrderDisplayServiceType"
                                        @change="onToggleFixedOrderDisplayServiceType"
                                    >
                                        <biz-setting-form-item-tip tip="在预约页面选择指定的预约号数">
                                            <abc-radio :label="1" data-type="label-align">
                                                按号数选择
                                            </abc-radio>
                                        </biz-setting-form-item-tip>

                                        <biz-setting-form-item-tip tip="在预约页面选择指定的预约时间段">
                                            <abc-radio :label="0" data-type="label-align">
                                                按时间段选择
                                            </abc-radio>
                                        </biz-setting-form-item-tip>
                                    </abc-radio-group>
                                </biz-setting-form-item>

                                <!--预约时间段-->
                                <biz-setting-form-item
                                    v-if="postData.fixedOrderDisplayServiceType === RESERVATION_TIME_TYPE.OTHER"
                                    label="预约时间段"
                                    :has-divider="!isTreatmentReservation"
                                >
                                    <abc-radio-group v-model="postData.serviceType" @change="handleChangeServiceType">
                                        <biz-setting-form-item-indent>
                                            <abc-radio :label="SERVICE_TYPE_ENUM.MORNING_AFTERNOON_EVENING">
                                                固定时间段
                                            </abc-radio>

                                            <template #content>
                                                按上午、下午、晚上
                                            </template>
                                        </biz-setting-form-item-indent>

                                        <biz-setting-form-item-indent>
                                            <abc-radio :label="SERVICE_TYPE_ENUM.CUSTOM_TIME">
                                                自定义时间段
                                            </abc-radio>

                                            <template v-if="postData.serviceType === SERVICE_TYPE_ENUM.CUSTOM_TIME" #content>
                                                <span>
                                                    从排班开始时间，每
                                                </span>

                                                <abc-select v-model="customPeriod" :width="142" style="margin: 0 8px;">
                                                    <abc-option
                                                        v-for="item in modeOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    ></abc-option>
                                                </abc-select>

                                                <span>
                                                    为一个时间段
                                                </span>
                                            </template>
                                        </biz-setting-form-item-indent>
                                    </abc-radio-group>
                                </biz-setting-form-item>

                                <!--分配号数时机-->
                                <biz-setting-form-item v-if="!isTreatmentReservation" label="分配号数时机">
                                    <abc-radio-group v-model="postData.generateOrderNoTime">
                                        <biz-setting-form-item-tip tip="同一号段，按预约先后顺序从小到大依次分配号数">
                                            <abc-radio
                                                data-type="label-align"
                                                :label="GENERATE_ORDER_NO_TIME_ENUM.REGISTRATION"
                                                :disabled="isAccurateTime"
                                            >
                                                预约成功时分配号数
                                            </abc-radio>
                                        </biz-setting-form-item-tip>

                                        <biz-setting-form-item-tip tip="同一号段，按签到先后顺序从小到大依次分配号数">
                                            <abc-popover
                                                placement="top-start"
                                                trigger="hover"
                                                theme="yellow"
                                                :offset="10"
                                                :arrow-offset="3"
                                                :disabled="isSegmentationAndSign"
                                            >
                                                <abc-radio slot="reference" :label="GENERATE_ORDER_NO_TIME_ENUM.SIGN_IN" :disabled="!isSegmentationAndSign">
                                                    签到完成时分配号数
                                                </abc-radio>
                                                <div>
                                                    <template v-if="isAccurateTime">
                                                        按号源精确时间预约时，不支持签到时取号
                                                    </template>
                                                    <template v-else-if="!postData.needSignIn">
                                                        请先在【预约流程】中开启到店签到
                                                    </template>
                                                </div>
                                            </abc-popover>
                                        </biz-setting-form-item-tip>
                                    </abc-radio-group>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--放号规则-->
                            <biz-setting-form-group
                                title="放号规则"
                            >
                                <!--线上放号时间-->
                                <biz-setting-form-item
                                    label="线上放号时间"
                                    has-divider
                                >
                                    <biz-setting-form-item-indent>
                                        <abc-radio-group v-model="postData.isDiffForRevisitedReservationTime" @change="resetReserveTimeRange">
                                            <biz-setting-form-item-indent>
                                                <abc-radio :label="0">
                                                    初复诊患者在同一时间放号
                                                </abc-radio>

                                                <template v-if="postData.isDiffForRevisitedReservationTime === 0" #content>
                                                    <abc-space>
                                                        <span>提前</span>
                                                        <abc-select
                                                            v-model="advanceTime"
                                                            :max-height="350"
                                                            :width="84"
                                                        >
                                                            <abc-option
                                                                v-for="item in _daysOptions"
                                                                :key="item.value"
                                                                :value="item.value"
                                                                :label="item.label"
                                                            ></abc-option>
                                                        </abc-select>

                                                        <span>放号，放号时间：每天</span>

                                                        <abc-select
                                                            v-model="postData.reservationTimeRange.registerStartTime"
                                                            :width="80"
                                                            :max-height="350"
                                                        >
                                                            <abc-option
                                                                v-for="item in _startTimeOptions"
                                                                :key="item.value"
                                                                :value="item.value"
                                                                :label="item.label"
                                                            ></abc-option>
                                                        </abc-select>
                                                    </abc-space>
                                                </template>
                                            </biz-setting-form-item-indent>

                                            <biz-setting-form-item-indent>
                                                <abc-radio :label="1">
                                                    初复诊患者在不同时间放号
                                                </abc-radio>

                                                <template v-if="postData.isDiffForRevisitedReservationTime === 1" #content>
                                                    <abc-flex vertical :gap="8">
                                                        <abc-space>
                                                            <span>初诊患者，提前</span>
                                                            <abc-select
                                                                v-model="advanceTime"
                                                                :max-height="350"
                                                                :width="84"
                                                            >
                                                                <abc-option
                                                                    v-for="item in _daysOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>

                                                            <span>放号，放号时间：每天</span>

                                                            <abc-select
                                                                v-model="postData.reservationTimeRange.registerStartTime"
                                                                :width="80"
                                                                :max-height="350"
                                                            >
                                                                <abc-option
                                                                    v-for="item in _startTimeOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>
                                                        </abc-space>

                                                        <abc-space>
                                                            <span>复诊患者，提前</span>
                                                            <abc-select
                                                                v-model="revisitedAdvanceTime"
                                                                :max-height="350"
                                                                :width="84"
                                                            >
                                                                <abc-option
                                                                    v-for="item in _daysOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>

                                                            <span>放号，放号时间：每天</span>

                                                            <abc-select
                                                                v-model="postData.revisitedReservationTimeRange.registerStartTime"
                                                                :width="80"
                                                                :max-height="350"
                                                            >
                                                                <abc-option
                                                                    v-for="item in _startTimeOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>
                                                        </abc-space>
                                                    </abc-flex>
                                                </template>
                                            </biz-setting-form-item-indent>
                                        </abc-radio-group>

                                        <template #content>
                                            <abc-button
                                                variant="text"
                                                size="small"
                                                @click="handleShowDoctorRegisterDialog"
                                            >
                                                单独设置医生放号时间
                                            </abc-button>
                                        </template>
                                    </biz-setting-form-item-indent>
                                </biz-setting-form-item>

                                <!--线下预留号-->
                                <biz-setting-form-item label="线下预留号">
                                    <biz-setting-form-item-tip tip="开启后，可在排班处设置线下预留号，当线上约满后仍有额外预留号源可用">
                                        <abc-checkbox
                                            v-model="postData.enableLeaveForPC"
                                            type="number"
                                            data-type="label-align"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <!--会员预留号-->
                                <biz-setting-form-item label="会员预留号">
                                    <biz-setting-form-item-tip tip="开启后，可在排班处设置会员预留号，仅供会员使用">
                                        <abc-checkbox
                                            v-model="postData.enableLeaveForMember"
                                            type="number"
                                            data-type="label-align"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--预约签到-->
                            <biz-setting-form-group title="预约签到">
                                <!--预约签到-->
                                <biz-setting-form-item label="预约签到">
                                    <abc-radio-group v-model="postData.needSignIn" @change="onToggleNeedSignIn">
                                        <abc-radio :label="0" data-type="label-align">
                                            预约患者无需现场签到，预约完成后进入就诊当天的待诊列表
                                        </abc-radio>

                                        <abc-radio :label="1">
                                            预约患者需要现场签到，签到完成后进入就诊当天的待诊列表
                                        </abc-radio>
                                    </abc-radio-group>
                                </biz-setting-form-item>

                                <!--签到方式-->
                                <biz-setting-form-item v-if="postData.needSignIn" label="签到方式">
                                    <abc-checkbox :value="true" disabled>
                                        预约/挂号处人工签到
                                    </abc-checkbox>

                                    <setting-version-tip
                                        :scene="SettingVersionTipType.microClinic"
                                        :show-popover="!isOpenMp"
                                        :with-btn="visibleOpenWeClinic"
                                    >
                                        <abc-checkbox
                                            v-model="postData.needScanSignIn"
                                            :disabled="!isOpenMp"
                                            type="number"
                                        >
                                            微诊所/自助服务机自助签到
                                        </abc-checkbox>
                                    </setting-version-tip>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--预约/挂号规则-->
                            <biz-setting-form-group title="预约/挂号规则">
                                <!--预约对象-->
                                <biz-setting-form-item label="预约对象">
                                    <abc-radio-group v-model="postData.notMustReserveEmployee" class="reservation-person">
                                        <abc-radio :label="0">
                                            预约时必须指定{{ isTreatmentReservation ? '理疗师' : '医生' }}
                                        </abc-radio>
                                        <abc-radio :label="1">
                                            预约时可不指定{{ isTreatmentReservation ? '理疗师' : '医生' }}
                                        </abc-radio>
                                    </abc-radio-group>
                                </biz-setting-form-item>

                                <!--排班后挂号-->
                                <biz-setting-form-item v-if="!isTreatmentReservation" has-divider label="排班后挂号">
                                    <abc-radio-group v-model="postData.noneScheduleDisableRegistration" class="reservation-person">
                                        <abc-radio :label="0">
                                            未排班可挂号
                                        </abc-radio>

                                        <biz-setting-form-item-indent>
                                            <abc-radio :label="1">
                                                未排班不可挂号
                                            </abc-radio>

                                            <template v-if="showRemindAdditionalOrderNo" #content>
                                                <abc-radio-group v-model="postData.remindAdditionalOrderNo">
                                                    <abc-radio :label="1">
                                                        号源约满后提示加号
                                                    </abc-radio>
                                                    <abc-radio :label="0">
                                                        号源约满后不提示加号
                                                    </abc-radio>
                                                </abc-radio-group>
                                            </template>
                                        </biz-setting-form-item-indent>
                                    </abc-radio-group>
                                </biz-setting-form-item>

                                <!--号源过期时间-->
                                <biz-setting-form-item
                                    label="号源过期时间"
                                >
                                    <biz-setting-form-item-indent>
                                        <abc-checkbox v-model="postData.enableAheadCloseReserve" type="number" data-type="label-align">
                                            开启
                                        </abc-checkbox>

                                        <template v-if="postData.enableAheadCloseReserve" #content>
                                            <abc-space>
                                                <span>号源将在就诊结束前</span>

                                                <abc-select
                                                    v-model="aheadCloseReserveTimeStr"
                                                    :width="90"
                                                    @change="selectAdvanceTimeRange"
                                                >
                                                    <abc-option
                                                        v-for="item in _CloseReserveTimeOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    ></abc-option>
                                                </abc-select>

                                                <span>关闭预约</span>
                                            </abc-space>
                                        </template>
                                    </biz-setting-form-item-indent>
                                </biz-setting-form-item>

                                <!--退号抢号-->
                                <biz-setting-form-item label="退号抢号">
                                    <biz-setting-form-item-tip>
                                        <setting-version-tip
                                            :scene="SettingVersionTipType.microClinic"
                                            :show-popover="!isOpenMp"
                                            :with-btn="visibleOpenWeClinic"
                                        >
                                            <abc-checkbox
                                                v-model="postData.retreatNotice"
                                                type="number"
                                                :disabled="!isOpenMp"
                                                data-type="label-align"
                                            >
                                                开启
                                            </abc-checkbox>
                                        </setting-version-tip>

                                        <template #tip>
                                            <abc-flex wrap="wrap">
                                                <template v-if="isOpenMp">
                                                    <abc-text theme="gray" size="mini">
                                                        开启后，当号源约满时患者可在微诊所开启抢号提醒，有退号后第一时间通知患者抢号
                                                    </abc-text>
                                                </template>
                                            </abc-flex>
                                        </template>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <!--推荐来源-->
                                <biz-setting-form-item label="推荐来源">
                                    <biz-setting-form-item-tip tip="开启后，对患者进行挂号或者预约时，自动回填上一次的推荐人信息">
                                        <abc-checkbox
                                            v-model="propertyData.isAutoFillReferrer"
                                            type="number"
                                            data-type="label-align"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--限号/爽约设置-->
                            <biz-setting-form-group>
                                <template #title>
                                    <setting-version-tip
                                        :scene="SettingVersionTipType.microClinic"
                                        :show-popover="!isOpenMp"
                                        :with-btn="visibleOpenWeClinic"
                                        :offset="8"
                                    >
                                        <abc-text
                                            tag="div"
                                            bold
                                            size="normal"
                                        >
                                            限号/爽约设置
                                        </abc-text>
                                    </setting-version-tip>
                                </template>

                                <!--微信预约限号-->
                                <biz-setting-form-item label="微信预约限号">
                                    <biz-setting-form-item-indent>
                                        <abc-checkbox
                                            v-model="postData.enableWechatReserveLimitCount"
                                            :disabled="!isOpenMp"
                                            data-type="label-align"
                                        >
                                            防恶意抢号占号
                                        </abc-checkbox>

                                        <template v-if="postData.enableWechatReserveLimitCount && isOpenMp" #content>
                                            <abc-space class="wechat-reserve-limit-count">
                                                <span>同一微信号或手机号</span>
                                                <abc-select
                                                    v-model="wechatReserveLimitCountPeriod"
                                                    :width="80"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatPeriodOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>
                                                <span>内，在单个门店最多可预约</span>
                                                <abc-select
                                                    v-model="postData.wechatReserveLimitCountRule.limitCount"
                                                    :width="80"
                                                >
                                                    <abc-option
                                                        v-for="item in _limitCountOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>
                                            </abc-space>
                                        </template>
                                    </biz-setting-form-item-indent>

                                    <biz-setting-form-item-indent>
                                        <abc-checkbox
                                            v-model="postData.enableWechatPatientReserveLimitCount"
                                            :disabled="!isOpenMp"
                                        >
                                            防患者重复预约
                                        </abc-checkbox>

                                        <template v-if="postData.enableWechatPatientReserveLimitCount && isOpenMp" #content>
                                            <abc-space class="wechat-patient-reserve-limit-count">
                                                <span>同一患者最多可预约同一医生</span>
                                                <abc-select
                                                    v-model="wechatPatientReserveLimitCountPeriod"
                                                    :width="80"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatPeriodOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>
                                                <span>内的</span>
                                                <abc-select
                                                    v-model="postData.wechatPatientReserveLimitCountRule.limitCount"
                                                    :width="80"
                                                >
                                                    <abc-option
                                                        v-for="item in _limitNumberOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>
                                                <span>号源</span>
                                            </abc-space>
                                        </template>
                                    </biz-setting-form-item-indent>
                                </biz-setting-form-item>

                                <!--爽约惩罚-->
                                <biz-setting-form-item label="爽约惩罚">
                                    <biz-setting-form-item-indent>
                                        <biz-setting-form-item-tip tip="开启后，当患者多次预约未就诊，可设置惩罚规则，减少号源浪费">
                                            <abc-checkbox
                                                v-model="postData.enableWechatBreakAppointmentPunish"
                                                type="number"
                                                :disabled="!isOpenMp"
                                                data-type="label-align"
                                            >
                                                开启
                                            </abc-checkbox>
                                        </biz-setting-form-item-tip>

                                        <template v-if="postData.enableWechatBreakAppointmentPunish && isOpenMp" #content>
                                            <abc-flex :gap="8" wrap="wrap" align="center">
                                                <span>每个微信号</span>

                                                <abc-select
                                                    v-model="wechatBreakAppointmentLimitTimeRange"
                                                    :width="92"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatBreakAppointmentPeriodOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    ></abc-option>
                                                </abc-select>

                                                <span>内累计爽约</span>

                                                <abc-select
                                                    v-model="postData.wechatBreakAppointmentPunishRule.breakAppointmentNum"
                                                    :width="92"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatBreakAppointmentLimitCountOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>

                                                <span>将禁止</span>

                                                <abc-select
                                                    v-model="wechatBreakAppointmentLockTimeRange"
                                                    :width="92"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatBreakAppointmentLockTimeOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>

                                                <span>
                                                    不可预约。
                                                </span>

                                                <abc-button
                                                    variant="text"
                                                    size="small"
                                                    @click="showBlockWechatDialog = true"
                                                >
                                                    查看已禁止微信号
                                                </abc-button>
                                            </abc-flex>
                                        </template>
                                    </biz-setting-form-item-indent>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--线上预约收费/退费-->
                            <biz-setting-form-group v-if="!isTreatmentReservation">
                                <template #title>
                                    <setting-version-tip
                                        :scene="SettingVersionTipType.microClinic"
                                        :show-popover="!isOpenMp"
                                        :with-btn="visibleOpenWeClinic"
                                        :offset="8"
                                    >
                                        <abc-text
                                            tag="div"
                                            bold
                                            size="normal"
                                        >
                                            线上预约收费/退费
                                        </abc-text>
                                    </setting-version-tip>
                                </template>

                                <!--线上预约支付-->
                                <biz-setting-form-item label="线上预约支付">
                                    <biz-setting-form-item-tip>
                                        <setting-version-tip
                                            :scene="SettingVersionTipType.abcPay"
                                            :show-popover="!canOption"
                                        >
                                            <abc-checkbox
                                                v-model="postData.wechatReserveMustPay"
                                                :disabled="enableSelectOption"
                                                type="number"
                                                data-type="label-align"
                                            >
                                                开启
                                            </abc-checkbox>
                                        </setting-version-tip>

                                        <template slot="tip">
                                            <abc-text theme="gray" size="mini">
                                                开启后，患者在微诊所预约时需支付{{ $t('registrationFeeName') }}
                                            </abc-text>
                                        </template>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <!--展示挂号费-->
                                <biz-setting-form-item :label="`展示${$t('registrationFeeName')}`">
                                    <biz-setting-form-item-tip tip="开启后，患者可在微诊所查看医生挂号费">
                                        <abc-checkbox
                                            v-model="showRegisFee"
                                            type="number"
                                            :disabled="!!postData.wechatReserveMustPay || !isOpenMp"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <!--支付方式-->
                                <biz-setting-form-item
                                    v-if="postData.wechatReserveMustPay"
                                    label="支付方式"
                                    has-divider
                                >
                                    <template v-if="canOption">
                                        <abc-checkbox v-if="isOpenAbcPay" v-model="canOption" disabled>
                                            ABC支付
                                        </abc-checkbox>
                                        <abc-checkbox v-else v-model="canOption" disabled>
                                            微信
                                        </abc-checkbox>
                                    </template>

                                    <abc-checkbox
                                        v-if="yiBaoPayVisible"
                                        v-model="postData.wechatEnableMobileShebaoPay"
                                        type="number"
                                    >
                                        医保移动支付
                                    </abc-checkbox>

                                    <abc-space>
                                        <abc-checkbox
                                            v-model="postData.wechatEnableMemberCardPay"
                                            :disabled="!enableMemberPassword"
                                            data-type="label-align"
                                        >
                                            会员余额
                                        </abc-checkbox>

                                        <abc-tooltip-info
                                            v-if="!enableMemberPassword"
                                            :content="isSingleStore ? '请先在“营销-会员卡”中开启会员密码支付' : '请先联系总部开启会员卡密码支付'"
                                        ></abc-tooltip-info>
                                    </abc-space>

                                    <template v-if="!enableMemberCard">
                                        <abc-space>
                                            <abc-text theme="warning-light">
                                                {{ isAdmin ? '未设置会员卡' : '未设置会员卡，请前去总部设置' }}
                                            </abc-text>

                                            <abc-button
                                                v-if="isAdmin"
                                                size="small"
                                                variant="text"
                                                @click="toPayModeSet"
                                            >
                                                去设置
                                            </abc-button>
                                        </abc-space>
                                    </template>
                                </biz-setting-form-item>

                                <!--线上退号退费-->
                                <biz-setting-form-item
                                    label="线上退号退费"
                                >
                                    <biz-setting-form-item-tip tip="开启后，患者可在微诊所退号退费，退费金额将原路退回">
                                        <abc-checkbox
                                            v-model="postData.enableWechatReserveRefund"
                                            :disabled="!isOpenMp"
                                            type="number"
                                            data-type="label-align"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <template v-if="postData.enableWechatReserveRefund">
                                    <!-- 当预约不需要收费时，退号规则只能选择时间 -->
                                    <biz-setting-form-item
                                        v-if="!postData.wechatReserveMustPay"
                                        label="退费退号规则"
                                    >
                                        <biz-setting-form-item-tip>
                                            <abc-space data-type="label-align">
                                                <span>患者主动退号，需提前</span>

                                                <abc-select
                                                    v-model="cancelTime"
                                                    :width="92"
                                                    :disabled="!isOpenMp"
                                                    @change="changeCancelTime"
                                                >
                                                    <abc-option
                                                        v-for="item in _timeOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    ></abc-option>
                                                </abc-select>
                                            </abc-space>
                                        </biz-setting-form-item-tip>
                                    </biz-setting-form-item>

                                    <!-- 当预约需要收费时，退号规则可选固定退费和阶段退费 -->
                                    <biz-setting-form-item
                                        v-else
                                        label="退费退号规则"
                                        class="manage-section-item-label"
                                    >
                                        <abc-radio-group v-model="postData.wechatReserveRefundRule.type">
                                            <biz-setting-form-item-indent>
                                                <!--固定退费-->
                                                <abc-radio
                                                    :label="0"
                                                    :disabled="enableSelectOption"
                                                    :class="{ 'enable-select-option': postData.wechatReserveRefundRule.type === 1 }"
                                                    data-type="label-align"
                                                >
                                                    固定退费
                                                </abc-radio>

                                                <template #content>
                                                    <abc-space v-if="postData.wechatReserveRefundRule.type === 0" style="color: #000000;">
                                                        <span>
                                                            患者主动{{ isFixedNumberSource ? '退号' : '取消预约' }}，需提前
                                                        </span>

                                                        <abc-select
                                                            v-model="cancelTime"
                                                            :width="92"
                                                            :disabled="enableSelectOption"
                                                            @change="changeCancelTime"
                                                        >
                                                            <abc-option
                                                                v-for="item in _timeOptions"
                                                                :key="item.value"
                                                                :value="item.value"
                                                                :label="item.label"
                                                            ></abc-option>
                                                        </abc-select>

                                                        <span>
                                                            ，{{ isFixedNumberSource ? '退号' : '取消' }}时将退还患者
                                                        </span>

                                                        <abc-select
                                                            v-model="postData.wechatReserveRefundRule.refundRates[0].refundRate"
                                                            :width="92"
                                                            :disabled="enableSelectOption"
                                                        >
                                                            <abc-option
                                                                v-for="item in _wechatReserveRefundRateOptions"
                                                                :key="item.value"
                                                                :value="item.value"
                                                                :label="item.label"
                                                            ></abc-option>
                                                        </abc-select>

                                                        <span>的{{ $t('registrationFeeName') }}</span>

                                                        <abc-text v-if="yiBaoPayVisible" theme="warning-light" class="warn-tips">
                                                            医保支付{{ $t('registrationFeeName') }}时，将100%全款退回
                                                        </abc-text>
                                                    </abc-space>
                                                </template>
                                            </biz-setting-form-item-indent>

                                            <biz-setting-form-item-indent>
                                                <!--阶梯退费-->
                                                <abc-radio
                                                    :label="1"
                                                    :disabled="enableSelectOption"
                                                    :class="{ 'enable-select-option': postData.wechatReserveRefundRule.type === 1 }"
                                                >
                                                    阶梯退费
                                                </abc-radio>

                                                <template #content>
                                                    <abc-flex
                                                        v-if="postData.wechatReserveRefundRule.type === 1"
                                                        class="stepped-refund-wrapper"
                                                        vertical
                                                        :gap="8"
                                                    >
                                                        <abc-space>
                                                            患者主动退号，需提前
                                                            <abc-select
                                                                v-model="cancelTime"
                                                                :width="92"
                                                                :disabled="enableSelectOption"
                                                                @change="changeCancelTime"
                                                            >
                                                                <abc-option
                                                                    v-for="item in stepTimeOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>
                                                        </abc-space>

                                                        <abc-space>
                                                            <span>
                                                                距离开始就诊 {{ cancelTime }} 以上，不足
                                                            </span>

                                                            <abc-form-item :validate-event="validateSteppedTime">
                                                                <abc-select
                                                                    v-model="steppedRefundCancelTime[1]"
                                                                    :width="92"
                                                                    :disabled="enableSelectOption"
                                                                    @change="changeSteppedCancelTime"
                                                                >
                                                                    <abc-option
                                                                        v-for="item in stepTimeOptions"
                                                                        :key="item.value"
                                                                        :value="item.value"
                                                                        :label="item.label"
                                                                    ></abc-option>
                                                                </abc-select>
                                                            </abc-form-item>

                                                            <span>
                                                                ，退号时将退还
                                                            </span>

                                                            <abc-select
                                                                v-model="postData.wechatReserveRefundRule.refundRates[0].refundRate"
                                                                :width="92"
                                                                :disabled="enableSelectOption"
                                                            >
                                                                <abc-option
                                                                    v-for="item in _wechatReserveRefundRateOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>

                                                            <span>
                                                                的{{ $t('registrationFeeName') }}
                                                            </span>
                                                        </abc-space>

                                                        <abc-space>
                                                            <span>
                                                                距离开始就诊 {{ steppedRefundCancelTime[1] }} 以上，退号时将退还
                                                            </span>

                                                            <abc-select
                                                                v-model="postData.wechatReserveRefundRule.refundRates[1].refundRate"
                                                                :width="92"
                                                                :disabled="enableSelectOption"
                                                            >
                                                                <abc-option
                                                                    v-for="item in _wechatReserveRefundRateOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>

                                                            <span>
                                                                的{{ $t('registrationFeeName') }}
                                                            </span>
                                                        </abc-space>

                                                        <abc-text v-if="yiBaoPayVisible" theme="warning-light" style="padding: 0;">
                                                            医保支付{{ $t('registrationFeeName') }}时，将100%全款退回
                                                        </abc-text>
                                                    </abc-flex>
                                                </template>
                                            </biz-setting-form-item-indent>
                                        </abc-radio-group>

                                        <template v-if="!canOption">
                                            <span class="warn-tips">
                                                {{ isAdmin ? '需要开通ABC支付' : '需要总部开通ABC支付' }}
                                            </span>
                                        </template>
                                    </biz-setting-form-item>
                                </template>
                            </biz-setting-form-group>

                            <!--消息通知-->
                            <biz-setting-form-group title="消息通知" tip="可在总部【营销 - 消息推送】中设置短信/微信通知">
                                <message-notification-item
                                    :show-title="false"
                                    :message-key="'appointment.grab'"
                                    :message-name="'退号抢号提醒'"
                                    class="manage-notification-item"
                                ></message-notification-item>

                                <message-notification-item
                                    :message-key="'appointment.outpatient'"
                                    :show-title="false"
                                    :message-name="'诊前提醒'"
                                    class="manage-notification-item"
                                ></message-notification-item>
                            </biz-setting-form-group>
                        </template>

                        <!-- 灵活模式 -->
                        <template v-else>
                            <!--预约模式-->
                            <biz-setting-form-group
                                title="预约模式"
                                class="mode-section"
                            >
                                <div class="setting-item" style="margin-bottom: 24px;">
                                    <div class="content c-content">
                                        <abc-space :size="16">
                                            <abc-option-card
                                                v-for="(o, key) in reservationModeOpts"
                                                :key="key"
                                                :width="260"
                                                :show-icon="false"
                                                :value="postData.modeType === key"
                                                theme="success"
                                                selectable="icon-inside"
                                                :title="o.title"
                                                :title-config="{
                                                    size: 'normal'
                                                }"
                                                :description="o.intro"
                                                :description-config="{
                                                    size: 'mini',
                                                    theme: 'gray'
                                                }"
                                                @change="handleModeCardClick(key)"
                                            >
                                            </abc-option-card>
                                        </abc-space>
                                    </div>
                                </div>
                            </biz-setting-form-group>

                            <!--预约方式-->
                            <biz-setting-form-group title="预约方式">
                                <biz-setting-form-item label="预约方式">
                                    <abc-checkbox
                                        :value="true"
                                        disabled
                                    >
                                        {{ doctorLabel }}
                                    </abc-checkbox>

                                    <abc-space style="height: 26px;">
                                        <abc-checkbox
                                            v-model="postData.showReserveProduct"
                                            type="number"
                                        >
                                            项目
                                        </abc-checkbox>

                                        <abc-button
                                            v-if="postData.showReserveProduct === 1"
                                            variant="text"
                                            size="small"
                                            @click="showReservationItemDialog = true"
                                        >
                                            设置预约项目 ({{ productsCount }})
                                        </abc-button>
                                    </abc-space>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--预约时间设置-->
                            <biz-setting-form-group title="预约时间设置">
                                <!--预约时间-->
                                <biz-setting-form-item label="预约时间" label-line-height-size="medium">
                                    <p class="flexible-mode" data-type="label-align">
                                        <span>患者可预约</span>
                                        <abc-form-item required>
                                            <abc-select v-model="flexibleStartTime" :width="100" style="margin: 0 8px;">
                                                <abc-option
                                                    v-for="item in _flexibleReservationStartTimeOptions"
                                                    :key="item.value"
                                                    :value="item.value"
                                                    :label="item.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <span>到</span>
                                        <abc-form-item required :validate-event="validateReservationTimeRange">
                                            <abc-select v-model="flexibleEndTime" :width="84" style="margin: 0 8px;">
                                                <abc-option
                                                    v-for="item in _daysOptions"
                                                    :key="item.value"
                                                    :value="item.value"
                                                    :label="item.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <span>内的时间</span>
                                        <abc-button
                                            variant="text"
                                            size="small"
                                            @click="handleShowDoctorRegisterDialog"
                                        >
                                            单独设置医生预约时间
                                        </abc-button>
                                    </p>
                                </biz-setting-form-item>

                                <!--预约时长-->
                                <biz-setting-form-item
                                    label="预约时长"
                                >
                                    <div>
                                        患者已确定预约项目时： 预约时长为预约项目的服务时长
                                    </div>

                                    <div>
                                        <span>患者未确定预约项目时： </span>
                                        <template v-if="isTreatmentReservation">
                                            <span>根据理疗师排班的最小/最大可预约时长</span>
                                        </template>

                                        <template v-else>
                                            <span>初诊占用时长</span>
                                            <abc-select v-model="visitServiceDuration" :width="100" style="margin: 0 8px;">
                                                <abc-option
                                                    v-for="item in _visitOptions"
                                                    :key="item.value"
                                                    :value="item.value"
                                                    :label="item.label"
                                                ></abc-option>
                                            </abc-select>
                                            <span>复诊占用时长</span>
                                            <abc-select v-model="revisitedServiceDuration" :width="100" style="margin: 0 8px;">
                                                <abc-option
                                                    v-for="item in _visitOptions"
                                                    :key="item.value"
                                                    :value="item.value"
                                                    :label="item.label"
                                                ></abc-option>
                                            </abc-select>
                                        </template>
                                    </div>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--预约签到-->
                            <biz-setting-form-group title="预约签到">
                                <!--预约签到-->
                                <biz-setting-form-item label="预约签到">
                                    <abc-radio-group v-model="postData.needSignIn" @change="onToggleNeedSignIn">
                                        <abc-radio :label="0">
                                            预约患者无需现场签到，预约完成后进入就诊当天的待诊列表
                                        </abc-radio>

                                        <abc-radio :label="1">
                                            预约患者需要现场签到，签到完成后进入就诊当天的待诊列表
                                        </abc-radio>
                                    </abc-radio-group>
                                </biz-setting-form-item>

                                <!--签到方式-->
                                <biz-setting-form-item v-if="postData.needSignIn" label="签到方式">
                                    <abc-checkbox :value="true" disabled>
                                        预约/挂号处人工签到
                                    </abc-checkbox>

                                    <setting-version-tip
                                        :scene="SettingVersionTipType.microClinic"
                                        :show-popover="!isOpenMp"
                                        :with-btn="visibleOpenWeClinic"
                                    >
                                        <abc-checkbox
                                            v-model="postData.needScanSignIn"
                                            :disabled="!isOpenMp"
                                            type="number"
                                        >
                                            微诊所/自助服务机自助签到
                                        </abc-checkbox>
                                    </setting-version-tip>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--预约/挂号规则-->
                            <biz-setting-form-group title="预约/挂号规则">
                                <biz-setting-form-item has-divider label="预约对象">
                                    <abc-radio-group v-model="postData.notMustReserveEmployee" class="reservation-person">
                                        <abc-radio :label="0">
                                            预约时必须指定{{ isTreatmentReservation ? '理疗师' : '医生' }}
                                        </abc-radio>
                                        <abc-radio :label="1">
                                            预约时可不指定{{ isTreatmentReservation ? '理疗师' : '医生' }}
                                        </abc-radio>
                                    </abc-radio-group>
                                </biz-setting-form-item>

                                <!--排班后挂号-->
                                <biz-setting-form-item v-if="!isTreatmentReservation" has-divider label="排班后挂号">
                                    <abc-radio-group v-model="postData.noneScheduleDisableRegistration" class="reservation-person">
                                        <abc-radio :label="0">
                                            未排班可挂号
                                        </abc-radio>

                                        <biz-setting-form-item-indent>
                                            <abc-radio :label="1">
                                                未排班不可挂号
                                            </abc-radio>

                                            <template v-if="showRemindAdditionalOrderNo" #content>
                                                <abc-radio-group v-model="postData.remindAdditionalOrderNo">
                                                    <abc-radio :label="1">
                                                        号源约满后提示加号
                                                    </abc-radio>
                                                    <abc-radio :label="0">
                                                        号源约满后不提示加号
                                                    </abc-radio>
                                                </abc-radio-group>
                                            </template>
                                        </biz-setting-form-item-indent>
                                    </abc-radio-group>
                                </biz-setting-form-item>

                                <!--线上预约确认-->
                                <biz-setting-form-item label="线上预约确认">
                                    <biz-setting-form-item-tip tip="开启后，患者在微诊所提交预约后，工作人员联系患者沟通确认后预约生效">
                                        <setting-version-tip
                                            :scene="SettingVersionTipType.microClinic"
                                            :show-popover="!isOpenMp"
                                            :with-btn="visibleOpenWeClinic"
                                        >
                                            <abc-checkbox v-model="postData.wechatReserveNeedApply" :disabled="!isOpenMp" type="number">
                                                开启
                                            </abc-checkbox>
                                        </setting-version-tip>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <!--推荐来源-->
                                <biz-setting-form-item label="推荐来源">
                                    <biz-setting-form-item-tip tip="开启后，对患者进行挂号或者预约时，自动回填上一次的推荐人信息">
                                        <abc-checkbox
                                            v-model="propertyData.isAutoFillReferrer"
                                            type="number"
                                            data-type="label-align"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--限号/爽约设置-->
                            <biz-setting-form-group>
                                <template #title>
                                    <setting-version-tip
                                        :scene="SettingVersionTipType.microClinic"
                                        :show-popover="!isOpenMp"
                                        :with-btn="visibleOpenWeClinic"
                                        :offset="8"
                                    >
                                        <abc-text
                                            tag="div"
                                            bold
                                            size="normal"
                                        >
                                            限号/爽约设置
                                        </abc-text>
                                    </setting-version-tip>
                                </template>

                                <!--微信预约限号-->
                                <biz-setting-form-item label="微信预约限号">
                                    <biz-setting-form-item-indent>
                                        <abc-checkbox
                                            v-model="postData.enableWechatReserveLimitCount"
                                            :disabled="!isOpenMp"
                                            data-type="label-align"
                                        >
                                            防恶意抢号占号
                                        </abc-checkbox>

                                        <template v-if="postData.enableWechatReserveLimitCount && isOpenMp" #content>
                                            <abc-space class="wechat-reserve-limit-count">
                                                <span>同一微信号或手机号</span>
                                                <abc-select
                                                    v-model="wechatReserveLimitCountPeriod"
                                                    :width="80"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatPeriodOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>
                                                <span>内，在单个门店最多可预约</span>
                                                <abc-select
                                                    v-model="postData.wechatReserveLimitCountRule.limitCount"
                                                    :width="80"
                                                >
                                                    <abc-option
                                                        v-for="item in _limitCountOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>
                                            </abc-space>
                                        </template>
                                    </biz-setting-form-item-indent>

                                    <biz-setting-form-item-indent>
                                        <abc-checkbox
                                            v-model="postData.enableWechatPatientReserveLimitCount"
                                            :disabled="!isOpenMp"
                                        >
                                            防患者重复预约
                                        </abc-checkbox>

                                        <template v-if="postData.enableWechatPatientReserveLimitCount && isOpenMp" #content>
                                            <abc-space class="wechat-patient-reserve-limit-count">
                                                <span>同一患者最多可预约同一医生</span>
                                                <abc-select
                                                    v-model="wechatPatientReserveLimitCountPeriod"
                                                    :width="80"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatPeriodOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>
                                                <span>内的</span>
                                                <abc-select
                                                    v-model="postData.wechatPatientReserveLimitCountRule.limitCount"
                                                    :width="80"
                                                >
                                                    <abc-option
                                                        v-for="item in _limitNumberOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>
                                                <span>号源</span>
                                            </abc-space>
                                        </template>
                                    </biz-setting-form-item-indent>
                                </biz-setting-form-item>

                                <!--爽约惩罚-->
                                <biz-setting-form-item label="爽约惩罚">
                                    <biz-setting-form-item-indent>
                                        <biz-setting-form-item-tip tip="开启后，当患者多次预约未就诊，可设置惩罚规则，减少号源浪费">
                                            <abc-checkbox
                                                v-model="postData.enableWechatBreakAppointmentPunish"
                                                type="number"
                                                :disabled="!isOpenMp"
                                                data-type="label-align"
                                            >
                                                开启
                                            </abc-checkbox>
                                        </biz-setting-form-item-tip>

                                        <template v-if="postData.enableWechatBreakAppointmentPunish && isOpenMp" #content>
                                            <abc-flex :gap="8" wrap="wrap" align="center">
                                                <span>每个微信号</span>

                                                <abc-select
                                                    v-model="wechatBreakAppointmentLimitTimeRange"
                                                    :width="92"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatBreakAppointmentPeriodOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    ></abc-option>
                                                </abc-select>

                                                <span>内累计爽约</span>

                                                <abc-select
                                                    v-model="postData.wechatBreakAppointmentPunishRule.breakAppointmentNum"
                                                    :width="92"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatBreakAppointmentLimitCountOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>

                                                <span>将禁止</span>

                                                <abc-select
                                                    v-model="wechatBreakAppointmentLockTimeRange"
                                                    :width="92"
                                                >
                                                    <abc-option
                                                        v-for="item in _wechatBreakAppointmentLockTimeOptions"
                                                        :key="item.value"
                                                        :value="item.value"
                                                        :label="item.label"
                                                    >
                                                    </abc-option>
                                                </abc-select>

                                                <span>
                                                    不可预约。
                                                </span>

                                                <abc-button
                                                    variant="text"
                                                    size="small"
                                                    @click="showBlockWechatDialog = true"
                                                >
                                                    查看已禁止微信号
                                                </abc-button>
                                            </abc-flex>
                                        </template>
                                    </biz-setting-form-item-indent>
                                </biz-setting-form-item>
                            </biz-setting-form-group>

                            <!--线上预约收费/退费-->
                            <biz-setting-form-group v-if="!isTreatmentReservation">
                                <template #title>
                                    <setting-version-tip
                                        :scene="SettingVersionTipType.microClinic"
                                        :show-popover="!isOpenMp"
                                        :with-btn="visibleOpenWeClinic"
                                        :offset="8"
                                    >
                                        <abc-text
                                            tag="div"
                                            bold
                                            size="normal"
                                        >
                                            线上预约收费/退费
                                        </abc-text>
                                    </setting-version-tip>
                                </template>

                                <!--预约支付-->
                                <biz-setting-form-item label="线上预约支付">
                                    <biz-setting-form-item-tip :tip="`开启后，患者在微诊所预约时需支付${ $t('registrationFeeName') }`">
                                        <setting-version-tip
                                            :scene="SettingVersionTipType.abcPay"
                                            :show-popover="!canOption"
                                        >
                                            <abc-checkbox
                                                v-model="postData.wechatReserveMustPay"
                                                :disabled="enableSelectOption"
                                                type="number"
                                                data-type="label-align"
                                            >
                                                开启
                                            </abc-checkbox>
                                        </setting-version-tip>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <!--展示挂号费-->
                                <biz-setting-form-item :label="`展示${$t('registrationFeeName')}`">
                                    <biz-setting-form-item-tip tip="开启后，患者可在微诊所查看医生挂号费">
                                        <abc-checkbox
                                            v-model="showRegisFee"
                                            type="number"
                                            :disabled="!!postData.wechatReserveMustPay || enableSelectOption"
                                        ></abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <!--线上退号退费-->
                                <biz-setting-form-item
                                    label="线上退号退费"
                                >
                                    <biz-setting-form-item-tip tip="开启后，患者可在微诊所退号退费，退费金额将原路退回">
                                        <abc-checkbox
                                            v-model="postData.enableWechatReserveRefund"
                                            :disabled="!isOpenMp"
                                            type="number"
                                            data-type="label-align"
                                        >
                                            开启
                                        </abc-checkbox>
                                    </biz-setting-form-item-tip>
                                </biz-setting-form-item>

                                <template v-if="postData.enableWechatReserveRefund">
                                    <!-- 当预约不需要收费时，退号规则只能选择时间 -->
                                    <biz-setting-form-item
                                        v-if="!postData.wechatReserveMustPay"
                                        label="退费退号规则"
                                    >
                                        <biz-setting-form-item-tip>
                                            <setting-version-tip
                                                :scene="SettingVersionTipType.microClinic"
                                                :show-popover="!isOpenMp"
                                                :with-btn="visibleOpenWeClinic"
                                                :offset="8"
                                            >
                                                <abc-space data-type="label-align">
                                                    <span>患者主动退号，需提前</span>

                                                    <abc-select
                                                        v-model="cancelTime"
                                                        :width="92"
                                                        :disabled="!isOpenMp"
                                                        @change="changeCancelTime"
                                                    >
                                                        <abc-option
                                                            v-for="item in _timeOptions"
                                                            :key="item.value"
                                                            :value="item.value"
                                                            :label="item.label"
                                                        ></abc-option>
                                                    </abc-select>
                                                </abc-space>
                                            </setting-version-tip>
                                        </biz-setting-form-item-tip>
                                    </biz-setting-form-item>

                                    <!-- 当预约需要收费时，退号规则可选固定退费和阶段退费 -->
                                    <biz-setting-form-item
                                        v-else
                                        label="退费退号规则"
                                        class="manage-section-item-label"
                                    >
                                        <abc-radio-group v-model="postData.wechatReserveRefundRule.type">
                                            <biz-setting-form-item-indent>
                                                <!--固定退费-->
                                                <abc-radio
                                                    :label="0"
                                                    :disabled="enableSelectOption"
                                                    :class="{ 'enable-select-option': postData.wechatReserveRefundRule.type === 1 }"
                                                    data-type="label-align"
                                                >
                                                    固定退费
                                                </abc-radio>

                                                <template #content>
                                                    <abc-space v-if="postData.wechatReserveRefundRule.type === 0" style="color: #000000;">
                                                        <span>
                                                            患者主动{{ isFixedNumberSource ? '退号' : '取消预约' }}，需提前
                                                        </span>

                                                        <abc-select
                                                            v-model="cancelTime"
                                                            :width="92"
                                                            :disabled="enableSelectOption"
                                                            @change="changeCancelTime"
                                                        >
                                                            <abc-option
                                                                v-for="item in _timeOptions"
                                                                :key="item.value"
                                                                :value="item.value"
                                                                :label="item.label"
                                                            ></abc-option>
                                                        </abc-select>

                                                        <span>
                                                            ，{{ isFixedNumberSource ? '退号' : '取消' }}时将退还患者
                                                        </span>

                                                        <abc-select
                                                            v-model="postData.wechatReserveRefundRule.refundRates[0].refundRate"
                                                            :width="92"
                                                            :disabled="enableSelectOption"
                                                        >
                                                            <abc-option
                                                                v-for="item in _wechatReserveRefundRateOptions"
                                                                :key="item.value"
                                                                :value="item.value"
                                                                :label="item.label"
                                                            ></abc-option>
                                                        </abc-select>

                                                        <span>的{{ $t('registrationFeeName') }}</span>

                                                        <abc-text v-if="yiBaoPayVisible" theme="warning-light" class="warn-tips">
                                                            医保支付{{ $t('registrationFeeName') }}时，将100%全款退回
                                                        </abc-text>
                                                    </abc-space>
                                                </template>
                                            </biz-setting-form-item-indent>

                                            <biz-setting-form-item-indent>
                                                <!--阶梯退费-->
                                                <abc-radio
                                                    :label="1"
                                                    :disabled="enableSelectOption"
                                                    :class="{ 'enable-select-option': postData.wechatReserveRefundRule.type === 1 }"
                                                >
                                                    阶梯退费
                                                </abc-radio>

                                                <template #content>
                                                    <abc-flex
                                                        v-if="postData.wechatReserveRefundRule.type === 1"
                                                        class="stepped-refund-wrapper"
                                                        vertical
                                                        :gap="8"
                                                    >
                                                        <abc-space>
                                                            患者主动退号，需提前
                                                            <abc-select
                                                                v-model="cancelTime"
                                                                :width="92"
                                                                :disabled="enableSelectOption"
                                                                @change="changeCancelTime"
                                                            >
                                                                <abc-option
                                                                    v-for="item in stepTimeOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>
                                                        </abc-space>

                                                        <abc-space>
                                                            <span>
                                                                距离开始就诊 {{ cancelTime }} 以上，不足
                                                            </span>

                                                            <abc-form-item :validate-event="validateSteppedTime">
                                                                <abc-select
                                                                    v-model="steppedRefundCancelTime[1]"
                                                                    :width="92"
                                                                    :disabled="enableSelectOption"
                                                                    @change="changeSteppedCancelTime"
                                                                >
                                                                    <abc-option
                                                                        v-for="item in stepTimeOptions"
                                                                        :key="item.value"
                                                                        :value="item.value"
                                                                        :label="item.label"
                                                                    ></abc-option>
                                                                </abc-select>
                                                            </abc-form-item>

                                                            <span>
                                                                ，退号时将退还
                                                            </span>

                                                            <abc-select
                                                                v-model="postData.wechatReserveRefundRule.refundRates[0].refundRate"
                                                                :width="92"
                                                                :disabled="enableSelectOption"
                                                            >
                                                                <abc-option
                                                                    v-for="item in _wechatReserveRefundRateOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>

                                                            <span>
                                                                的{{ $t('registrationFeeName') }}
                                                            </span>
                                                        </abc-space>

                                                        <abc-space>
                                                            <span>
                                                                距离开始就诊 {{ steppedRefundCancelTime[1] }} 以上，退号时将退还
                                                            </span>

                                                            <abc-select
                                                                v-model="postData.wechatReserveRefundRule.refundRates[1].refundRate"
                                                                :width="92"
                                                                :disabled="enableSelectOption"
                                                            >
                                                                <abc-option
                                                                    v-for="item in _wechatReserveRefundRateOptions"
                                                                    :key="item.value"
                                                                    :value="item.value"
                                                                    :label="item.label"
                                                                ></abc-option>
                                                            </abc-select>

                                                            <span>
                                                                的{{ $t('registrationFeeName') }}
                                                            </span>
                                                        </abc-space>

                                                        <abc-text v-if="yiBaoPayVisible" theme="warning-light" style="padding: 0;">
                                                            医保支付{{ $t('registrationFeeName') }}时，将100%全款退回
                                                        </abc-text>
                                                    </abc-flex>
                                                </template>
                                            </biz-setting-form-item-indent>
                                        </abc-radio-group>

                                        <template v-if="!canOption">
                                            <span class="warn-tips">
                                                {{ isAdmin ? '需要开通ABC支付' : '需要总部开通ABC支付' }}
                                            </span>
                                        </template>
                                    </biz-setting-form-item>
                                </template>
                            </biz-setting-form-group>

                            <!--消息通知-->
                            <biz-setting-form-group title="消息通知" tip="可在总部【营销 - 消息推送】中设置短信/微信通知">
                                <message-notification-item
                                    :message-key="'appointment.outpatient'"
                                    :show-title="false"
                                    :message-name="'诊前提醒'"
                                    class="manage-notification-item"
                                ></message-notification-item>
                            </biz-setting-form-group>
                        </template>

                        <dialog-setting
                            v-if="visabledSet"
                            v-model="visabledSet"
                            :min-options="_minOptions"
                            :post-data="postData"
                        ></dialog-setting>

                        <doctor-register-setting-dialog
                            v-if="showDoctorRegisterDialog"
                            v-model="showDoctorRegisterDialog"
                            :start-time="postData.reservationTimeRange.startTime"
                            :register-start-time="postData.reservationTimeRange.registerStartTime"
                            :reservation-time="postData.reservationTimeRange.endTime"
                            :revisited-reservation-time-range="postData.revisitedReservationTimeRange"
                            :reservation-time-range="postData.reservationTimeRange"
                            :is-diff-for-revisited-reservation-time="postData.isDiffForRevisitedReservationTime"
                            :registration-type="fetchProductParams.registrationType"
                            :mode-type="postData.modeType"
                        ></doctor-register-setting-dialog>

                        <block-wechat-dialog v-if="showBlockWechatDialog" v-model="showBlockWechatDialog">
                        </block-wechat-dialog>

                        <reservation-item-setting-dialog
                            v-if="showReservationItemDialog"
                            v-model="showReservationItemDialog"
                            :registration-type="postData.type"
                            :available-role-ids="postData.availableRoleIds"
                            :visit-service-duration="visitServiceDuration"
                            :revisited-service-duration="revisitedServiceDuration"
                            @products-count="fetchRegistrationsProductCount"
                        ></reservation-item-setting-dialog>
                    </template>
                </biz-setting-form>
            </abc-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button :loading="buttonLoading || buttonPropertyLoading" :disabled="(!isUpdate && !isUpdatePropertyData) || loading" @click="submit">
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>
    </biz-setting-layout>
</template>

<script>
    import ReservationAPI from 'api/registrations/reservation';
    import DialogSetting from './dialog-setting.vue';

    import {
        mapActions, mapGetters, mapState,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import Reservation from './mixins/reservation';
    import { PayModeEnum } from '@/service/charge/constants.js';
    import {
        GENERATE_ORDER_NO_TIME_ENUM,
        GENERATE_ORDER_NO_TIME_TYPE,
        RESERVATION_MODE_TYPE,
        RESERVATION_TIME_TYPE,
        RESERVATION_TYPE,
        SERVICE_TYPE_ENUM,
    } from './constant.js';
    import MessageNotificationItem from 'src/views/settings/message-notification-item/message-notification-item.vue';

    import MORNINGAFTERNOON from '@/assets/images/settings/reservation-3.png';
    import FIXACCURATE from '@/assets/images/settings/reservation-4.png';
    import FIXONEHOURS from '@/assets/images/settings/reservation-1.png';
    import FLEXIBLETIME from '@/assets/images/settings/reservation-2.png';
    import {
        ROLE_CLERK_ID,
        ROLE_DOCTOR_ASSIST_ID,
        ROLE_DOCTOR_ID,
        ROLE_NURSE_ID, ROLE_OPTOMETRY_ID,
        ROLE_PHYSIOTHERAPIST_ID,
        ROLE_SURVEYOR_ID,
    } from 'utils/constants.js';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
        BizSettingFormItemIndent,
    } from '@/components-composite/setting-form/index';
    import SettingVersionTip from 'views/settings/components/setting-version-tip/index.vue';
    import { SettingVersionTipType } from 'views/settings/components/setting-version-tip/constant';
    import PropertyAPI from 'api/property';

    const DoctorRegisterSettingDialog = () => import('./doctor-register-setting-dialog');
    const ReservationItemSettingDialog = () => import('./reservation-item-setting-dialog.vue');
    const BlockWechatDialog = () => import('views/settings/registered-reservation/block-wechat-dialog');

    const wechatPeriodOptions = [
        {
            value: '1 天',
            label: '1 天',
        },
        {
            value: '1 周',
            label: '1 周',
        },
        {
            value: '1 个月',
            label: '1 个月',
        },
    ];
    const expireOptions = [
        {
            value: '24 小时',
            label: '24 小时后',
        },
        {
            value: '48 小时',
            label: '48 小时后',
        },
        {
            value: '2 天',
            label: '第二天',
        },
        {
            value: '3 天',
            label: '第三天',
        },
    ];
    const CloseReserveTimeOptions = [
        {
            value: '10 分钟',
            label: '10分钟',
        },
        {
            value: '15 分钟',
            label: '15分钟',
        },
        {
            value: '20 分钟',
            label: '20分钟',
        },
        {
            value: '60 分钟',
            label: '1小时',
        },
        {
            value: '120 分钟',
            label: '2小时',
        },
        {
            value: '180 分钟',
            label: '3小时',
        },
        {
            value: '360 分钟',
            label: '6小时',
        },
        {
            value: '720 分钟',
            label: '12小时',
        },
        {
            value: '1080 分钟',
            label: '18小时',
        },
    ];
    const minOptions = [
        {
            value: 4,
            label: '4 分钟',
        },
        {
            value: 5,
            label: '5 分钟',
        },
        {
            value: 10,
            label: '10 分钟',
        },
        {
            value: 15,
            label: '15 分钟',
        },
        {
            value: 20,
            label: '20 分钟',
        },
        {
            value: 30,
            label: '30 分钟',
        },
        {
            value: 40,
            label: '40 分钟',
        },
        {
            value: 50,
            label: '50 分钟',
        },
        {
            value: 60,
            label: '60 分钟',
        },
    ];
    const timeOptions = [
        {
            value: '15 分钟',
            label: '15 分钟',
        },
        {
            value: '30 分钟',
            label: '30 分钟',
        },
        {
            value: '1 小时',
            label: '1 小时',
        },
        {
            value: '2 小时',
            label: '2 小时',
        },
        {
            value: '3 小时',
            label: '3 小时',
        },
        {
            value: '6 小时',
            label: '6 小时',
        },
        {
            value: '12 小时',
            label: '12 小时',
        },
        {
            value: '24 小时',
            label: '24 小时',
        },
        {
            value: '48 小时',
            label: '48 小时',
        },
        {
            value: '72 小时',
            label: '72 小时',
        },
        {
            value: '4 天',
            label: '4 天',
        },
        {
            value: '5 天',
            label: '5 天',
        },
        {
            value: '7 天',
            label: '7 天',
        },
        {
            value: '10 天',
            label: '10 天',
        },
        {
            value: '15 天',
            label: '15 天',
        },
        {
            value: '20 天',
            label: '20 天',
        },
        {
            value: '30 天',
            label: '30 天',
        },
        {
            value: '60 天',
            label: '60 天',
        },
    ];

    const wechatReserveRefundRateOptions = [
        {
            value: 100,
            label: '100%',
        },
        {
            value: 90,
            label: '90%',
        },
        {
            value: 80,
            label: '80%',
        },
        {
            value: 70,
            label: '70%',
        },
        {
            value: 60,
            label: '60%',
        },
        {
            value: 50,
            label: '50%',
        },
        {
            value: 40,
            label: '40%',
        },
        {
            value: 30,
            label: '30%',
        },
        {
            value: 20,
            label: '20%',
        },
        {
            value: 10,
            label: '10%',
        },
        {
            value: 0,
            label: '0%',
        },
    ];

    const limitCountOptions = [
        {
            value: 1,
            label: '1 次',
        },
        {
            value: 2,
            label: '2 次',
        },
        {
            value: 3,
            label: '3 次',
        },
        {
            value: 4,
            label: '4 次',
        },
        {
            value: 5,
            label: '5 次',
        },
        {
            value: 6,
            label: '6 次',
        },
        {
            value: 7,
            label: '7 次',
        },
        {
            value: 8,
            label: '8 次',
        },
        {
            value: 9,
            label: '9 次',
        },
        {
            value: 10,
            label: '10 次',
        },
    ];

    // 阶段退费规则
    const defaultSteppedRefundRates = [
        {
            minAheadTime: {
                day: 0,
                hour: 0,
                min: 15,
            },
            refundRate: 0,
        },
        {
            minAheadTime: {
                day: 0,
                hour: 0,
                min: 15,
            },
            refundRate: 0,
        },
    ];

    // 爽约时间选项
    const wechatBreakAppointmentPeriodOptions = [
        {
            value: '7 天',
            label: '7 天',
        },
        {
            value: '15 天',
            label: '15 天',
        },
        {
            value: '1 个月',
            label: '1 个月',
        },
        {
            value: '2 个月',
            label: '2 个月',
        },
        {
            value: '3 个月',
            label: '3 个月',
        },
        {
            value: '6 个月',
            label: '6 个月',
        },
    ];

    // 爽约触发次数选项
    const wechatBreakAppointmentLimitCountOptions = [
        {
            value: 1,
            label: '1 次',
        },
        {
            value: 2,
            label: '2 次',
        },
        {
            value: 3,
            label: '3 次',
        },
        {
            value: 4,
            label: '4 次',
        },
        {
            value: 5,
            label: '5 次',
        },
        {
            value: 6,
            label: '6 次',
        },
    ];

    // 爽约时间选项
    const wechatBreakAppointmentLockTimeOptions = [
        {
            value: '1 周',
            label: '1 周',
        },
        {
            value: '2 周',
            label: '2 周',
        },
        {
            value: '1 个月',
            label: '1 个月',
        },
        {
            value: '2 个月',
            label: '2 个月',
        },
        {
            value: '3 个月',
            label: '3 个月',
        },
        {
            value: '6 个月',
            label: '6 个月',
        },
    ];

    const modeOptions = [
        {
            label: '半小时',
            value: 30,
        },
        {
            label: '1小时',
            value: 60,
        },
        {
            label: '1.5小时',
            value: 90,
        },
        {
            label: '2小时',
            value: 120,
        },
    ];

    // 灵活预约-预约时长
    const visitOptions = [
        {
            label: '15分钟',
            value: 15,
        },
        {
            label: '30分钟',
            value: 30,
        },
        {
            label: '45分钟',
            value: 45,
        },
        {
            label: '1小时',
            value: 60,
        },
        {
            label: '1.5小时',
            value: 90,
        },
        {
            label: '2小时',
            value: 120,
        },
        {
            label: '2.5小时',
            value: 150,
        },
        {
            label: '3小时',
            value: 180,
        },
        {
            label: '3.5时',
            value: 210,
        },
        {
            label: '4小时',
            value: 240,
        },
    ];

    export default {
        components: {
            SettingVersionTip,
            BlockWechatDialog,
            DialogSetting,
            DoctorRegisterSettingDialog,
            MessageNotificationItem,
            ReservationItemSettingDialog,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
            BizSettingFormItemIndent,
        },
        mixins: [Reservation],
        props: {
            registrationType: {
                type: Number,
                default: 0, // 0:门诊 , 1: 治疗理疗
            },
        },
        data() {
            return {
                RESERVATION_TYPE,
                SERVICE_TYPE_ENUM,
                RESERVATION_MODE_TYPE,
                GENERATE_ORDER_NO_TIME_TYPE,
                RESERVATION_TIME_TYPE,
                GENERATE_ORDER_NO_TIME_ENUM,
                loading: false,
                isUpdate: false,
                buttonLoading: false,

                cancelTime: '24 小时',
                steppedRefundCancelTime: ['15 分钟', '15 分钟'], // 阶梯退费提前时间
                cachePostData: null,
                postData: {
                    isOpen: 1, // 否开启；0:未开启; 1:已开启;
                    modeType: undefined, // 预约模式; 0:固定号源; 1:灵活时间 RESERVATION_MODE_TYPE.FIXED_NUMBER
                    serviceType: SERVICE_TYPE_ENUM.MORNING_AFTERNOON_EVENING, // 预约时间段类型 固定号源预约模式服务类型；0：按上午/下午/晚上; 1:按精确时间段预约; 2:按自定义时段
                    fixedOrderDisplayServiceType: undefined, // 精确预约: 1； 其它: 0 RESERVATION_TIME_TYPE.ACCURATE
                    customPeriod: {
                        hour: 0,
                        min: 30,
                    },
                    notMustReserveEmployee: undefined, // 预约人员  1：可不指定；0：必须指定
                    showReserveProduct: 0 , // 预约项目 是否展示可预约项目；0:否; 1:是;
                    isDiffForRevisitedReservationTime: 0, // 初复诊非同时间放号 0：同时放号 1：非同时放号
                    reservationTimeRange: { // 初诊放号时间或初复诊同时放号时间
                        registerStartTime: '08:00',
                        startTime: {
                            hour: 0,
                            min: 60,
                        },
                        endTime: {
                            month: 3,
                            week: 0,
                            day: 0,
                        },
                    },
                    revisitedReservationTimeRange: { // 复诊放号时间
                        registerStartTime: '08:00',
                        startTime: {
                            hour: 0,
                            min: 60,
                        },
                        endTime: {
                            month: 3,
                            week: 0,
                            day: 0,
                        },
                    },
                    enableAheadCloseReserve: 0, // 号源过期时间 开关
                    aheadCloseReserveTime: { // 号源过期时间设置
                        min: 0,
                        hour: 1,
                    },
                    enableLeaveForPC: 0, // 现场预留号
                    enableLeaveForMember: 0, // 会员预留号
                    disableEpidemiologicalRegistration: 0, // 对于流行病史异常，是否允许挂号
                    wechatReserveNeedApply: 0, // 微信预约
                    generateOrderNoTime: undefined, // 号数确定时机 0: 预约取号，10: 签到取号 GENERATE_ORDER_NO_TIME_TYPE.REGISTRATION
                    needSignIn: undefined, // 预约签到 0，不需要，1，需要
                    needScanSignIn: 0, // 扫码签到 0，不需要，1，需要
                    noneScheduleDisableRegistration: undefined, // 未排班禁止挂号预约 0: 可挂 1: 不可挂
                    remindAdditionalOrderNo: 0, // 算号是追加号是否提示;0:不提示; 1：提示
                    payMode: 0, //挂号收费 0:暂不收费 1:收费
                    enableWechatReserveLimitCount: 0, // 是否开启微诊所预约微信用户号数限制; 0:否; 1:是
                    enableWechatPatientReserveLimitCount: 0, // 是否开启微诊所预约患者号数限制; 0:否; 1:是
                    wechatReserveLimitCountRule: {
                        limitCountPeriod: {
                            month: 0,
                            week: 0,
                            day: 1,
                        }, //微信预约数量期限
                        limitCount: 3, // 限制微信预约号数数量
                    },
                    wechatPatientReserveLimitCountRule: {
                        limitCountPeriod: {
                            month: 0,
                            week: 0,
                            day: 1,
                        }, //微信预约数量期限
                        limitCount: 2, // 限制微信预约号数数量
                    },
                    enableWechatBreakAppointmentPunish: 0, // 是否开启微信预约爽约惩罚，0：否；1：是
                    wechatBreakAppointmentPunishRule: {
                        limitTimeRange: {
                            // 限制时间范围
                            month: 3,
                            week: 0,
                            day: 0,
                        },
                        breakAppointmentNum:
                            wechatBreakAppointmentLimitCountOptions[0].value, // 累计爽约次数
                        lockTime: {
                            // 锁定时间
                            month: 1,
                            week: 0,
                            day: 0,
                        },
                    },
                    retreatNotice: 0, // 退号提醒通知，0，不开启，1，开启
                    wechatReserveMustPay: 0, // 预约是否需要支付
                    wechatEnableMemberCardPay: 0, // 微信预约是否支持会员卡支付挂号费；0:否；1：是
                    hideFeeWeClinicBeforePaid: 0, // 0 - 展示 1 - 不展示 	挂号费设置
                    enableWechatReserveRefund: 0, // 允许退号
                    wechatEnableMobileShebaoPay: 0, //移动医保支付

                    wechatReserveRefundRule: {
                        type: 0, // 退号规则类型，0: 固定费率，1: 阶梯退费
                        refundRates: Clone(
                            defaultSteppedRefundRates,
                        ),
                    },

                    type: 0, // 预约类型; 0:门诊; 1:治疗理疗
                    availableRoleIds: [
                        ROLE_DOCTOR_ID,
                        ROLE_NURSE_ID,
                        ROLE_SURVEYOR_ID,
                        ROLE_PHYSIOTHERAPIST_ID,
                        ROLE_DOCTOR_ASSIST_ID,
                        ROLE_CLERK_ID,
                        ROLE_OPTOMETRY_ID,
                    ], // 可预约角色id集合;1:医生;2:护士;3:检验师;4:理疗师;5:医助; 6:其它; 9:视光师
                    displayName: '展示名称', // 展示名称

                    // reservationNoticeByShortMsg: 0, // 短信通知
                    // reservationNoticeByWx: 0, // 微信通知
                    aheadOfNoticeTime: {
                        hour: 0,
                        min: 0,
                    },
                    aheadOfCancelTime: {
                        hour: 0,
                        min: 15,
                    }, // 退号退费

                    serviceNumPerhour: 4, // 预约时间段
                    serviceOneNeedMins: 10, // 按精确时间段预约

                    serviceDuration: {
                        visitServiceDuration: {
                            hour: 0,
                            min: 30,
                        },
                        revisitedServiceDuration: {
                            hour: 0,
                            min: 30,
                        },
                    },
                    // wechatReserveRefundRate: 100,
                    // wechatReserveSteppedRefundRates: Clone(
                    //     defaultSteppedRefundRates,
                    // ),
                },
                aheadCloseReserveTimeStr: '60 分钟',

                visabledSet: false, // 高级设置弹窗

                showDoctorRegisterDialog: false, // 单独设置医生放号时间弹窗

                showBlockWechatDialog: false, // 爽约锁定微信号弹框

                showReservationItemDialog: false , // 设置预约项目

                modeOptions,
                customPeriod: 30,

                fetchProductParams: {
                    displayName: '',
                    registrationProductTypeId: '',
                    clinicId: '',
                    offset: 0,
                    limit: 200,
                    registrationType: 0,
                },
                productsCount: 0,
                showRegisFee: 0,

                propertyLoading: false,
                buttonPropertyLoading: false,
                isUpdatePropertyData: false,
                propertyDataCache: null,
                propertyData: {
                    isAutoFillReferrer: 0, // 推荐来源 0:不开启 1:开启
                },
            };
        },
        computed: {
            SettingVersionTipType() {
                return SettingVersionTipType;
            },
            ...mapGetters([
                'currentClinic',
                'clinicConfig',
                'userInfo',
                'isAdmin',
                'isSingleStore',
                'isOpenMp',
                'isOpenCall',
                'isOpenAbcPay',
                'weChatPayConfig',
                'chargeConfig', // 收费设置
                'crmPermission',
                'modulePermission',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            ...mapState('socialPc', [
                'mobilePaySwitch',
                'basicInfo',
            ]),

            visibleOpenWeClinic() {
                return this.isSingleStore && this.modulePermission.hasWeClinicModule;
            },

            yiBaoPayVisible() {
                return !!this.mobilePaySwitch && !!this.basicInfo && !!+this.basicInfo.isOpenMobilePay;
            },

            reservationModeOpts() {
                return [
                    {
                        title: '固定号源预约模式',
                        intro: '按号数预约，适合以门诊为主，服务时长较为固定的医疗机构',
                    },{
                        title: '灵活时间预约模式',
                        intro: '按项目所需时长，灵活预约医生时间，适合口腔、理疗、康复等以治疗为主的医疗机构',
                    },
                ];
            },

            isTreatmentReservation() {
                return this.registrationType === RESERVATION_TYPE.TREATMENT_RESERVATION;
            },

            isEnablePayModeSetting() {
                return this.viewDistributeConfig.Settings.reservation.isEnablePayModeSetting;
            },

            enableMemberPassword() {
                return !!this.crmPermission.patient.enableMemberPassword;
            },

            pathRoute() {
                const { path } = this.$route.matched[0];
                return path || '';
            },

            enableMemberCard() {
                return !!this.chargeConfig.chargePayModeConfigs.find(
                    (item) => item.payModeId === PayModeEnum.MEMBER_CARD,
                );
            },

            enableSelectOption() {
                return !this.isOpenMp || !this.canOption ;
            },

            canOption() {
                return this.weChatPayConfig.weChatPaySwitch === 2;
            },

            stepTimeOptions() {
                return timeOptions;
            },

            wechatBreakAppointmentLimitTimeRange: {
                get() {
                    return this.convertPostData2Value(
                        this.postData.wechatBreakAppointmentPunishRule
                            .limitTimeRange,
                    );
                },
                set(val) {
                    this.postData.wechatBreakAppointmentPunishRule.limitTimeRange = this.convertValue2PostData(
                        val,
                    );
                },
            },

            wechatBreakAppointmentLockTimeRange: {
                get() {
                    return this.convertPostData2Value(
                        this.postData.wechatBreakAppointmentPunishRule.lockTime,
                    );
                },
                set(val) {
                    this.postData.wechatBreakAppointmentPunishRule.lockTime = this.convertValue2PostData(
                        val,
                    );
                },
            },

            wechatReserveLimitCountPeriod: {
                get() {
                    return this.convertPostData2Value(
                        this.postData.wechatReserveLimitCountRule.limitCountPeriod,
                    );
                },
                set(val) {
                    this.postData.wechatReserveLimitCountRule.limitCountPeriod = this.convertValue2PostData(
                        val,
                    );
                },
            },

            wechatPatientReserveLimitCountPeriod: {
                get() {
                    return this.convertPostData2Value(
                        this.postData.wechatPatientReserveLimitCountRule.limitCountPeriod,
                    );
                },
                set(val) {
                    this.postData.wechatPatientReserveLimitCountRule.limitCountPeriod = this.convertValue2PostData(
                        val,
                    );
                },
            },

            advanceTime: {
                get() {
                    return this.convertPostData2Value(
                        this.postData.reservationTimeRange.endTime,
                    );
                },
                set(val) {
                    this.postData.reservationTimeRange.endTime = this.convertValue2PostData(val);
                },
            },

            revisitedAdvanceTime: {
                get() {
                    return this.convertPostData2Value(
                        this.postData.revisitedReservationTimeRange.endTime,
                    );
                },
                set(val) {
                    this.postData.revisitedReservationTimeRange.endTime = this.convertValue2PostData(val);
                },
            },


            flexibleStartTime: {
                get() {
                    return this.postData.reservationTimeRange.startTime?.hour * 60 + this.postData.reservationTimeRange.startTime?.min;
                },
                set(val) {
                    this.postData.reservationTimeRange.startTime = this.formatPostDataPeriod(val);
                },
            },
            flexibleEndTime: {
                get() {
                    return this.convertPostData2Value(
                        this.postData.reservationTimeRange.endTime,
                    );
                },
                set(val) {
                    this.postData.reservationTimeRange.endTime = this.convertValue2PostData(val);
                },
            },
            visitServiceDuration: {
                get() {
                    return this.postData.serviceDuration.visitServiceDuration.hour * 60 + this.postData.serviceDuration.visitServiceDuration.min;
                },
                set(val) {
                    this.postData.serviceDuration.visitServiceDuration = this.formatPostDataPeriod(val);
                },
            },
            revisitedServiceDuration: {
                get() {
                    return this.postData.serviceDuration.revisitedServiceDuration.hour * 60 + this.postData.serviceDuration.revisitedServiceDuration.min;
                },
                set(val) {
                    this.postData.serviceDuration.revisitedServiceDuration = this.formatPostDataPeriod(val);
                },
            },

            displayStr() {
                if (this.isFlexibleTime) {
                    return '按灵活时间预约的患者端展示';
                }
                if (this.postData.serviceType === SERVICE_TYPE_ENUM.MORNING_AFTERNOON_EVENING) {
                    return '按上午、下午预约的患者端展示';
                }
                if (this.postData.serviceType === SERVICE_TYPE_ENUM.ACCURATE_TIME) {
                    return '按号源精确时间预约的患者端展示';
                }
                const item = this.modeOptions.find((item) => item.value === this.customPeriod);
                return `按${item?.label}预约的患者端展示`;
            },
            tipImageUrl() {
                // 灵活时间模式
                if (this.isFlexibleTime) {
                    return FLEXIBLETIME;
                }
                // 上下午
                if (this.postData.serviceType === SERVICE_TYPE_ENUM.MORNING_AFTERNOON_EVENING) {
                    return MORNINGAFTERNOON;
                }
                // 固定精确
                if (this.postData.serviceType === SERVICE_TYPE_ENUM.ACCURATE_TIME) {
                    return FIXACCURATE;
                }
                // 固定-分段 1小时
                return FIXONEHOURS;
            },
            // 是否是固定号源预约模式
            isFixedNumberSource() {
                return this.postData.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            // 灵活时间模式
            isFlexibleTime() {
                return this.postData.modeType === RESERVATION_MODE_TYPE.FLEXIBLE_TIME;
            },
            // 分段时间且开启签到
            isSegmentationAndSign() {
                return !!(this.postData.fixedOrderDisplayServiceType === RESERVATION_TIME_TYPE.OTHER && this.postData.needSignIn);
            },
            // 是否是按号源精确时间预约
            isAccurateTime() {
                return this.postData.fixedOrderDisplayServiceType === RESERVATION_TIME_TYPE.ACCURATE;
            },
            showRemindAdditionalOrderNo() {
                const {
                    generateOrderNoTime,
                    noneScheduleDisableRegistration,
                } = this.postData || {};

                return !!(this.isFixedNumberSource && generateOrderNoTime === GENERATE_ORDER_NO_TIME_ENUM.REGISTRATION && noneScheduleDisableRegistration);
            },
            doctorLabel() {
                return this.isTreatmentReservation ? '理疗师' : '医生';
            },
        },
        watch: {
            postData: {
                handler() {
                    if (this.cachePostData && this.postData) {
                        this.isUpdate = !isEqual(this.cachePostData, this.postData) || this.periodFlag !== this.customPeriod;
                    }
                },
                deep: true,
            },
            propertyData: {
                handler() {
                    if (this.propertyDataCache && this.propertyData) {
                        this.isUpdatePropertyData = !isEqual(this.propertyDataCache, this.propertyData);
                    }
                },
                deep: true,
            },
            'postData.fixedOrderDisplayServiceType': {
                handler(val) {
                    if (this.isFixedNumberSource) {
                        if (val) {
                            this.postData.serviceType = SERVICE_TYPE_ENUM.ACCURATE_TIME;
                        } else {
                            this.postData.serviceType = !this.customPeriod ? SERVICE_TYPE_ENUM.MORNING_AFTERNOON_EVENING : SERVICE_TYPE_ENUM.CUSTOM_TIME;
                        }
                    }
                },
                immediate: true,
            },
            customPeriod: {
                handler(val) {
                    if (this.isFixedNumberSource && this.postData.fixedOrderDisplayServiceType !== RESERVATION_TIME_TYPE.ACCURATE) {
                        this.postData.serviceType = !val ? SERVICE_TYPE_ENUM.MORNING_AFTERNOON_EVENING : SERVICE_TYPE_ENUM.CUSTOM_TIME;
                    }
                    this.isUpdate = !isEqual(this.cachePostData, this.postData) || this.periodFlag !== this.customPeriod;
                },
                immediate: true,
            },
            'postData.wechatReserveMustPay': {
                handler(val) {
                    if (!!val && !!this.postData.hideFeeWeClinicBeforePaid) {
                        this.postData.hideFeeWeClinicBeforePaid = 0;
                    }
                },
                immediate: true,
            },
            'postData.wechatReserveRefundRule.type': {
                handler(val) {
                    if (!val) {
                        this.postData.wechatReserveRefundRule.refundRates = [this.cachePostData?.wechatReserveRefundRule?.refundRates[0]];
                    } else {
                        if (this.postData.hideFeeWeClinicBeforePaid) {
                            this.postData.hideFeeWeClinicBeforePaid = 0;
                        }
                        this.postData.wechatReserveRefundRule.refundRates.splice(0, 1, Clone(this.cachePostData?.wechatReserveRefundRule?.refundRates?.[0]) || {
                            minAheadTime: {
                                day: 0,
                                hour: 0,
                                min: 15,
                            },
                            refundRate: 0,
                        });
                        if (!this.cachePostData?.wechatReserveRefundRule?.refundRates?.[1]) {
                            this.postData.wechatReserveRefundRule.refundRates[1] = {};
                        }
                        this.postData.wechatReserveRefundRule.refundRates.splice(1, 1, Clone(this.cachePostData?.wechatReserveRefundRule?.refundRates?.[1]) || {
                            minAheadTime: {
                                day: 0,
                                hour: 0,
                                min: 15,
                            },
                            refundRate: 0,
                        });
                    }
                    const {
                        day,hour,min,
                    } = this.postData?.wechatReserveRefundRule?.refundRates?.[0]?.minAheadTime || {};
                    if (day) {
                        this.cancelTime = `${day} 天`;
                    } else if (hour) {
                        this.cancelTime = `${hour} 小时`;
                    } else {
                        this.cancelTime = `${min} 分钟`;
                    }
                },
                immediate: true,
            },

            isFixedNumberSource: {
                handler(val) {
                    console.log(val, 'isFixedNumberSource');
                },
                immediate: true,
            },

            showRegisFee(v) {
                this.postData.hideFeeWeClinicBeforePaid = v ? 0 : 1;
            },
        },
        async created() {
            console.log('registrationType:',this.registrationType);
            this.fetchProductParams.registrationType = this.registrationType;
            this._minOptions = minOptions;
            this._timeOptions = timeOptions;
            this._expireOptions = expireOptions;
            this._wechatReserveRefundRateOptions = wechatReserveRefundRateOptions;
            this._limitCountOptions = limitCountOptions;
            this._wechatPeriodOptions = wechatPeriodOptions;
            this._CloseReserveTimeOptions = CloseReserveTimeOptions;
            this._wechatBreakAppointmentPeriodOptions = wechatBreakAppointmentPeriodOptions;
            this._wechatBreakAppointmentLimitCountOptions = wechatBreakAppointmentLimitCountOptions;
            this._wechatBreakAppointmentLockTimeOptions = wechatBreakAppointmentLockTimeOptions;
            this._visitOptions = visitOptions;

            await this.$store.dispatch('initChargeConfig');
            await this.$store.dispatch('fetchGoodsPrimaryClassificationIfNeed');
            await this.fetchReservation();
            await this.fetchCrmPermission();
            await this.fetchRegistrationProperty();
        },
        methods: {
            ...mapActions(['fetchCrmPermission','initWeChatPayConfig']),

            toWechatPay() {
                this.$router.push({
                    name: 'wechatpay',
                });
            },
            toOpenMicroClinic() {
                this.$router.push({
                    name: '@WeClinicHome',
                });
            },

            toPayModeSet() {
                this.$router.push({
                    name: 'chargeset',
                });
            },

            async fetchReservation() {
                this.loading = true;
                // 拉取一下消息推送，预约提醒配置
                this.initWeChatPayConfig();
                // 拉取启用项目
                this.fetchRegistrationsProductCount();
                try {
                    // 默认门诊：0
                    const { data } = await ReservationAPI.fetchReservationConfig({ registrationType: this.registrationType });
                    const config = Clone(data) || {};
                    this.isTreatmentReservation ? await this.$store.commit('SET_THERAPY_REGISTRATIONS_CONFIG', config) : await this.$store.commit('SET_OUTPATIENT_REGISTRATIONS_CONFIG', config);
                    console.log(data);

                    if (!data.reservationTimeRange) {
                        data.reservationTimeRange = {};
                    }
                    data.reservationTimeRange.registerStartTime = data.reservationTimeRange?.registerStartTime || '08:00';
                    data.reservationTimeRange.startTime = data.reservationTimeRange?.startTime || {
                        hour: 0,
                        min: 60,
                    };
                    data.reservationTimeRange.endTime = data.reservationTimeRange?.endTime || {
                        month: 0,
                        week: 0,
                        day: 0,
                    };
                    data.aheadCloseReserveTime = data.aheadCloseReserveTime || {
                        min: 0,
                        hour: 1,
                    };
                    data.serviceDuration = data.serviceDuration || {
                        visitServiceDuration: {
                            hour: 0,
                            min: 30,
                        },
                        revisitedServiceDuration: {
                            hour: 0,
                            min: 30,
                        },
                    };

                    if (!data.wechatReserveLimitCountRule) {
                        data.wechatReserveLimitCountRule = {};
                    }
                    data.wechatReserveLimitCountRule.limitCountPeriod = data.wechatReserveLimitCountRule?.limitCountPeriod || {
                        month: 0,
                        week: 0,
                        day: 1,
                    };
                    data.wechatReserveLimitCountRule.limitCount = data.wechatReserveLimitCountRule.limitCount || 3;

                    if (!data.wechatPatientReserveLimitCountRule) {
                        data.wechatPatientReserveLimitCountRule = {};
                    }
                    data.wechatPatientReserveLimitCountRule.limitCountPeriod = data.wechatPatientReserveLimitCountRule?.limitCountPeriod || {
                        month: 0,
                        week: 0,
                        day: 1,
                    };
                    data.wechatPatientReserveLimitCountRule.limitCount = data.wechatPatientReserveLimitCountRule.limitCount || 2;

                    if (!data.wechatReserveRefundRule) {
                        data.wechatReserveRefundRule = {};
                    }
                    data.wechatReserveRefundRule.refundRates =
                        data.wechatReserveRefundRule?.refundRates ||
                        Clone(defaultSteppedRefundRates);

                    data.wechatBreakAppointmentPunishRule = data.wechatBreakAppointmentPunishRule || {
                        limitTimeRange: {
                            // 限制时间范围
                            month: 3,
                            week: 0,
                            day: 0,
                        },
                        breakAppointmentNum:
                            wechatBreakAppointmentLimitCountOptions[0].value, // 累计爽约次数
                        lockTime: {
                            // 锁定时间
                            month: 1,
                            week: 0,
                            day: 0,
                        },
                    };
                    Object.assign(this.postData, data);
                    this._cacheModeType = data.modeType;
                    this.initPeroid(data.customPeriod);
                    this.showRegisFee = this.postData.hideFeeWeClinicBeforePaid ? 0 : 1;

                    this.handleSteppedRefundCancelTime(this.postData);


                    if (this.postData.enableAheadCloseReserve) {
                        this.aheadCloseReserveTimeStr = `${
                            this.postData.aheadCloseReserveTime.hour * 60 +
                            this.postData.aheadCloseReserveTime.min
                        } 分钟`;
                    }
                    this.cachePostData = Clone(this.postData);
                    this.isUpdate = false;
                    this.loading = false;
                } catch (err) {
                    this.cachePostData = Clone(this.postData);
                    this.isUpdate = false;
                    this.loading = false;
                    console.log(err);
                }
            },

            async fetchRegistrationsProductCount () {
                const { data } = await ReservationAPI.fetchRegistrationsProductList(this.fetchProductParams);
                this.productsCount = data?.rows?.filter((item) => !item.disable)?.length || 0;
            },

            async fetchRegistrationProperty() {
                this.propertyLoading = true;
                try {
                    const { data } = await PropertyAPI.get('registration.config.outpatient', 'clinic');
                    this.propertyData = data.outpatient;
                    this.propertyDataCache = Clone(this.propertyData);
                    this.isUpdatePropertyData = false;
                } catch (err) {
                    console.log(err);
                } finally {
                    this.propertyLoading = false;
                }
            },

            handleSteppedRefundCancelTime(postData) {
                if (postData?.wechatReserveRefundRule?.refundRates[0]?.minAheadTime?.day) {
                    this.cancelTime = `${postData.wechatReserveRefundRule.refundRates[0].minAheadTime.day} 天`;
                } else if (postData?.wechatReserveRefundRule?.refundRates[0]?.minAheadTime?.hour) {
                    this.cancelTime = `${postData.wechatReserveRefundRule.refundRates[0].minAheadTime.hour} 小时`;
                } else if (postData?.wechatReserveRefundRule?.refundRates[0]?.minAheadTime?.min) {
                    this.cancelTime = `${postData.wechatReserveRefundRule.refundRates[0].minAheadTime.min} 分钟`;
                }
                this.steppedRefundCancelTime[0] = this.cancelTime;
                if (postData?.wechatReserveRefundRule?.refundRates[1]?.minAheadTime?.day) {
                    this.steppedRefundCancelTime[1] = `${postData.wechatReserveRefundRule.refundRates[1]
                        .minAheadTime.day} 天`;
                } else if (postData?.wechatReserveRefundRule?.refundRates[1]?.minAheadTime?.hour) {
                    this.steppedRefundCancelTime[1] = `${postData.wechatReserveRefundRule.refundRates[1]
                        .minAheadTime.hour} 小时`;
                } else if (postData?.wechatReserveRefundRule?.refundRates[1]?.minAheadTime?.min) {
                    this.steppedRefundCancelTime[1] = `${postData.wechatReserveRefundRule.refundRates[1]
                        .minAheadTime.min} 分钟`;
                } else {
                    this.steppedRefundCancelTime[1] = '15 分钟';
                }
            },

            selectAdvanceTimeRange(val) {
                const minutes = val.split(' ');
                if (!val || minutes.length < 2) {
                    this.postData.aheadCloseReserveTime.min = 0;
                    this.postData.aheadCloseReserveTime.hour = 0;
                }
                const min = minutes[0] % 60;
                const hour = Math.floor(minutes[0] / 60);
                this.postData.aheadCloseReserveTime.min = min;
                this.postData.aheadCloseReserveTime.hour = hour;
            },

            changeCancelTime() {
                this.postData.wechatReserveRefundRule.refundRates[0].minAheadTime.day = 0;
                this.postData.wechatReserveRefundRule.refundRates[0].minAheadTime.hour = 0;
                this.postData.wechatReserveRefundRule.refundRates[0].minAheadTime.min = 0;
                const cancelArr = this.cancelTime.split(' ');
                this.steppedRefundCancelTime[0] = this.cancelTime;
                switch (cancelArr[1]) {
                    case '天':
                        this.postData.wechatReserveRefundRule.refundRates[0].minAheadTime.day = +cancelArr[0];
                        break;
                    case '小时':
                        this.postData.wechatReserveRefundRule.refundRates[0].minAheadTime.hour = +cancelArr[0];
                        break;
                    case '分钟':
                        this.postData.wechatReserveRefundRule.refundRates[0].minAheadTime.min = +cancelArr[0];
                        break;
                    default:
                }

                this.$nextTick(() => {
                    this.$refs.form.validate();
                });
            },

            changeSteppedCancelTime() {
                this.postData.wechatReserveRefundRule.refundRates[1].minAheadTime.day = 0;
                this.postData.wechatReserveRefundRule.refundRates[1].minAheadTime.hour = 0;
                this.postData.wechatReserveRefundRule.refundRates[1].minAheadTime.min = 0;
                const cancelArr = this.steppedRefundCancelTime[1].split(' ');
                switch (cancelArr[1]) {
                    case '天':
                        this.postData.wechatReserveRefundRule.refundRates[1].minAheadTime.day =
                            +cancelArr[0];
                        break;
                    case '小时':
                        this.postData.wechatReserveRefundRule.refundRates[1].minAheadTime.hour =
                            +cancelArr[0];
                        break;
                    case '分钟':
                        this.postData.wechatReserveRefundRule.refundRates[1].minAheadTime.min =
                            +cancelArr[0];
                        break;
                    default:
                }

                this.$nextTick(() => {
                    this.$refs.form.validate();
                });
            },

            formatPostDataPeriod(num) {
                return {
                    hour: Math.floor(num / 60),
                    min: num % 60,
                };
            },

            initPeroid(p) {
                if (!p) {
                    this.periodFlag = null;
                    this.customPeriod = null;
                    return;
                }

                const {
                    hour,
                    min,
                } = p;
                this.customPeriod = hour * 60 + min;

                this.periodFlag = this.customPeriod;
            },

            async submit() {
                if (this.buttonLoading) return false;

                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        try {
                            const promises = [];
                
                            if (this.isUpdate) {
                                promises.push(this.handleSaveConfirm());
                            }
                
                            if (this.isUpdatePropertyData) {
                                promises.push(this.handleSavePropertyConfirm());
                            }
                
                            if (promises.length > 0) {
                                await Promise.all(promises);
                                this.$Toast({
                                    message: '保存成功',
                                    type: 'success',
                                });
                            }
                        } catch (err) {
                            console.error(err);
                        }
                    }
                });
            },
            async handleSaveConfirm() {
                this.buttonLoading = true;
                this.postData.needSignIn = +this.postData.needSignIn;
                this.postData.needScanSignIn = +this.postData.needScanSignIn;
                this.postData.wechatReserveMustPay = Number(this.postData.wechatReserveMustPay);
                this.postData.retreatNotice = +this.postData.retreatNotice;
                this.postData.enableWechatReserveRefund = +this.postData.enableWechatReserveRefund;
                this.postData.enableWechatReserveLimitCount = +this.postData.enableWechatReserveLimitCount;
                this.postData.enableWechatPatientReserveLimitCount = +this.postData.enableWechatPatientReserveLimitCount;
                this.postData.enableLeaveForPC = +this.postData.enableLeaveForPC;
                this.postData.enableLeaveForMember = +this.postData.enableLeaveForMember;
                this.postData.wechatEnableMemberCardPay = +this.postData.wechatEnableMemberCardPay;
                this.postData.customPeriod = this.customPeriod ? this.formatPostDataPeriod(this.customPeriod) : null;
                try {
                    await ReservationAPI.updateReservationConfig(this.postData);
                    await this.fetchReservation();
                    this.buttonLoading = false;
                } catch (err) {
                    this.buttonLoading = false;
                }
            },
            async handleSavePropertyConfirm() {
                this.buttonPropertyLoading = true;
                try {
                    await PropertyAPI.update('registration.config.outpatient', 'clinic', {
                        outpatient: this.propertyData,
                    });
                    await this.fetchRegistrationProperty();
                    this.buttonPropertyLoading = false;
                } catch (err) {
                    this.buttonPropertyLoading = false;
                }
            },
            /**
             * desc [当切换预约签到勾选时]
             */
            onToggleNeedSignIn(val) {
                if (!this.isTreatmentReservation && !val) {
                    if (this.isOpenCall) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content:
                                '已启用排队叫号功能，必须开启预约签到才可正常使用。',
                            onClose: () => {
                                this.postData.needSignIn = 1;
                            },
                        });
                    } else if (this.postData.generateOrderNoTime === GENERATE_ORDER_NO_TIME_TYPE.SIGN_IN) {
                        this.$confirm({
                            type: 'warn',
                            title: '是否确认无需签到？',
                            content: '若选择无需签到，号数确定时机仅支持预约成功时确定就诊号数，是否确认修改？',
                            onConfirm: () => {
                                this.postData.generateOrderNoTime = GENERATE_ORDER_NO_TIME_TYPE.REGISTRATION;
                            },
                            onCancel: () => {
                                this.postData.needSignIn = 1;
                            },
                        });
                    }
                }
            },
            onToggleFixedOrderDisplayServiceType(val) {
                if (this.postData.generateOrderNoTime === GENERATE_ORDER_NO_TIME_TYPE.SIGN_IN && val === RESERVATION_TIME_TYPE.ACCURATE) {
                    this.$confirm({
                        type: 'warn',
                        title: '是否确认切换到按号源精确时间预约？',
                        content: '若选择按号源精确时间预约，号数确定时机仅支持预约成功时确定就诊号数，是否确认修改？',
                        onConfirm: () => {
                            this.postData.generateOrderNoTime = GENERATE_ORDER_NO_TIME_TYPE.REGISTRATION;
                        },
                        onCancel: () => {
                            this.postData.fixedOrderDisplayServiceType = RESERVATION_TIME_TYPE.OTHER;
                        },
                    });
                }
            },
            validateReservationTimeRange(value, callback) {
                let endTime = '';
                if (this.flexibleEndTime === '1 天') {
                    endTime = 1440;
                    if (this.flexibleStartTime >= endTime) {
                        return callback({
                            validate: false,
                            message: '不可小于最小预约时间',
                        });
                    }
                } else if (this.flexibleEndTime === '2 天') {
                    endTime = 2880;
                    if (this.flexibleStartTime >= endTime) {
                        return callback({
                            validate: false,
                            message: '不可小于最小预约时间',
                        });
                    }
                } else {
                    return callback({
                        validate: true,
                    });
                }
            },

            validateSteppedTime(value, callback) {
                const step2Index = this.stepTimeOptions.findIndex(
                    (item) => item.value === value,
                );
                const step1Index = timeOptions.findIndex(
                    (item) => item.value === this.steppedRefundCancelTime[0],
                );
                console.log(step2Index, step1Index);
                if (step2Index <= step1Index) {
                    callback({
                        validate: false,
                        message: '不可小于最小可退号时间',
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },

            convertValue2PostData(value) {
                const range = {
                    day: 0,
                    week: 0,
                    month: 0,
                };
                const arr = value.split(' ');
                // eslint-disable-next-line default-case
                switch (arr[1]) {
                    case '天':
                        range.day = +arr[0];
                        break;
                    case '周':
                        range.week = +arr[0];
                        break;
                    case '个月':
                        range.month = +arr[0];
                        break;
                    default:
                    //
                }
                return range;
            },

            convertPostData2Value(postData) {
                let value = '';
                const range = postData;
                if (range.day) {
                    value = `${range.day} 天`;
                } else if (range.week) {
                    value = `${range.week} 周`;
                } else if (range.month) {
                    value = `${range.month} 个月`;
                }
                return value;
            },

            handleModeCardClick(index) {
                if (this.postData.modeType === index) return;
                this.handleTipsConfirm(index);
            },
            handleTipsConfirm(index) {
                this.postData.modeType = index;
                if (index === 0) {
                    this.postData.fixedOrderDisplayServiceType = this.cachePostData.fixedOrderDisplayServiceType ?? RESERVATION_TIME_TYPE.OTHER;
                    if (!this.cachePostData.customPeriod) {
                        this.customPeriod = null;
                    } else {
                        const {
                            hour,
                            min,
                        } = this.cachePostData.customPeriod;
                        this.customPeriod = hour * 60 + min;
                    }

                    this.postData.reservationTimeRange = Clone(this.cachePostData.reservationTimeRange);
                    this.postData.serviceDuration = Clone(this.cachePostData.serviceDuration);
                    this.postData.showReserveProduct = this.cachePostData.showReserveProduct;
                    this.postData.wechatReserveNeedApply = this.cachePostData.wechatReserveNeedApply;
                } else {
                    this.postData.fixedOrderDisplayServiceType = null;
                    this.postData.serviceType = this.cachePostData.serviceType;
                }
            },
            handleChangeServiceType(curServiceType) {
                if (curServiceType === SERVICE_TYPE_ENUM.CUSTOM_TIME && !this.customPeriod) {
                    this.customPeriod = 30;
                }
            },
            // 重置初复诊时间
            resetReserveTimeRange() {
                this.postData.reservationTimeRange = {
                    registerStartTime: '08:00',
                    startTime: {
                        hour: 0,
                        min: 60,
                    },
                    endTime: {
                        month: 3,
                        week: 0,
                        day: 0,
                    },
                };

                this.postData.revisitedReservationTimeRange = {
                    registerStartTime: '08:00',
                    startTime: {
                        hour: 0,
                        min: 60,
                    },
                    endTime: {
                        month: 3,
                        week: 0,
                        day: 0,
                    },
                };
            },

            handleShowDoctorRegisterDialog() {
                if (this._cacheModeType !== this.postData.modeType) {
                    const text = this.postData.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER ? '放号' : '预约';
                    this.$alert({
                        title: '提示',
                        content: `已切换预约模式，请先保存后再设置医生${text}时间`,
                    });

                    return;
                }
                this.showDoctorRegisterDialog = true;
            },
        },
    };
</script>
