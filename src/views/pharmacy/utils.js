import { PriceType } from 'views/common/inventory/constants';
import TraceCode from '@/service/trace-code/service';
import { createGUID } from '@/common/utils';

export const showBatches = (item) => {
    const {
        productInfo,
        dispensingFormItemBatches,
    } = item;
    const { priceType } = productInfo;
    // 固定售价
    if (priceType === PriceType.PRICE) return false;
    // 没有批次信息
    return !!(dispensingFormItemBatches && dispensingFormItemBatches.length);
};

export function isCompatibleHistoryData(item) {
    const isExist = item?._traceableCodeListCache?.some((code) => {
        return code.hisPackageCount > 0 || code.hisPieceCount > 0;
    });
    return !isExist;
}

export function isNoTraceCodeGoods(item) {
    return TraceCode.isNoTraceCodeGoods(item?.productInfo ?? {});
}

export function canSetTraceCode(item) {
    if (!item.traceableCodeList || item.traceableCodeList.length === 0) return false;
    return TraceCode.formItemSupportTraceCode(item);
}

export function handleSearch(key, item) {
    key = key.trim();
    const _arr = [];
    item.traceableCodeList?.forEach((it) => {
        if (it.no?.indexOf(key) > -1 && it.used === 1) {
            _arr.push(it);
        }
    });
    item._traceableCodeListOptions = _arr.map((it) => {
        it.keyId = it.keyId || createGUID();
        it.count = it.count || 1;
        return it;
    });
}
