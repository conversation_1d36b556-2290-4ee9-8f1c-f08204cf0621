<template>
    <abc-popover
        v-model="isShowPopover"
        placement="top"
        trigger="manual"
        theme="yellow"
        popper-class="refund-trace-code-operation"
        :popper-style="{
            color: 'var(--abc-color-T4)',
            background: 'var(--abc-color-Y2)',
        }"
    >
        <abc-flex
            slot="reference"
            align="center"
            :style="operationStyle"
            @mouseenter="isShowPopover = isShowError"
            @mouseleave="isShowPopover = false"
        >
            <abc-text v-if="!code.checked" style="padding-left: 8px;">
            </abc-text>
            <refund-trace-code-popover
                v-else-if="isPartRefund"
                :list="refundTraceCodeList"
                :trace-code-use-info="code"
                @submit="(list)=>handleRefundTraceCodePopoverSubmit(list)"
            >
                <abc-button v-if="!code.selectedTraceCodeList || code.selectedTraceCodeList.length === 0" variant="text">
                    选择退药追溯码
                </abc-button>
                <abc-button v-else variant="text">
                    {{ code.selectedTraceCodeList[0]?.no ?? '' }}
                    <abc-text style="margin-left: 8px;">
                        <template v-if="code.selectedTraceCodeList.length > 1">
                            等{{ code.selectedTraceCodeList.length }}个
                        </template>
                        <template v-else>
                            x {{ displayHisCount(code.selectedTraceCodeList[0],code) }}
                        </template>
                    </abc-text>
                </abc-button>
            </refund-trace-code-popover>
            <abc-popover
                v-else
                trigger="hover"
                placement="bottom-end"
                width="363px"
                theme="white"
                :disabled="code.selectedTraceCodeList.length <= 1"
                :popper-style="{
                    padding: '6px 0 6px 6px',
                    maxHeight: '200px',
                }"
            >
                <abc-text slot="reference" style="padding-left: 8px;">
                    <template v-if="code.selectedTraceCodeList && code.selectedTraceCodeList.length > 0">
                        {{ code.selectedTraceCodeList[0]?.no ?? '' }}
                        <abc-text style="margin-left: 8px;">
                            <template v-if="code.selectedTraceCodeList.length > 1">
                                等{{ code.selectedTraceCodeList.length }}个
                            </template>
                            <template v-else>
                                x {{ displayHisCount(code.selectedTraceCodeList[0],code) }}
                            </template>
                        </abc-text>
                    </template>
                    <template v-else>
                        <abc-text theme="gray">
                            发药时未采集追溯码
                        </abc-text>
                    </template>
                </abc-text>
                <abc-list
                    show-divider
                    :divider-config="{
                        variant: 'dashed',
                        theme: 'light',
                    }"
                    readonly
                    no-close-border
                    height="186px"
                    :custom-padding="[9,10]"
                    :create-key="(it)=>it.keyId || it.id || it.no"
                    :data-list="code.selectedTraceCodeList"
                >
                    <template
                        #default="{
                            item
                        }"
                    >
                        <abc-text>{{ item.no }}</abc-text>
                        <abc-text>
                            x {{ displayHisCount(item,code) }}
                        </abc-text>
                    </template>
                </abc-list>
            </abc-popover>
        </abc-flex>
        <div>
            追溯码数量必须等于退药数量
        </div>
    </abc-popover>
</template>

<script>
    import RefundTraceCodePopover from 'views/pharmacy/refund-trace-code-popover.vue';
    import { clone } from '@abc/utils';
    import Big from 'big.js';
    import { getSafeNumber } from '@/utils';

    export default {
        name: 'RefundTraceCodeOperation' ,
        components: {
            RefundTraceCodePopover,
        },
        props: {
            code: {
                type: Object,
                default: () => ({}),
            },
            customOperationStyle: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                isShowError: false,
                isShowPopover: false,
            };
        },
        computed: {
            operationStyle() {
                let base = {
                    height: '100%',
                    minHeight: '48px',
                };
                if (this.customOperationStyle) {
                    base = Object.assign({}, base, this.customOperationStyle);
                }
                return this.isShowError ? Object.assign(base, {
                    border: '1px solid var(--abc-color-Y2)',
                }) : base;
            },
            refundTraceCodeList() {
                return clone(this.code?.dispensedTraceCodeList ?? []);
            },
            isPartRefund() {
                return !(+this.code?._unitCount === +this.code?.canRefundUnitCount);
            },
        },
        watch: {
            'code.selectedTraceCodeList': {
                handler () {
                    this.isShowPopover = false;
                    this.isShowError = false;
                },
                immediate: true,
            },
        },
        methods: {
            clone,
            displayHisCount(item,traceCodeUseInfo) {
                const { productInfo } = traceCodeUseInfo;
                const {
                    packageUnit,pieceUnit,
                } = productInfo;
                const packageStr = item?.hisPackageCount ? `${item.hisPackageCount}${packageUnit}` : '';
                const pieceStr = item?.hisPieceCount ? `${item.hisPieceCount}${pieceUnit}` : '';
                return packageStr + pieceStr;
            },

            handleRefundTraceCodePopoverSubmit(list) {
                this.$emit('submit',list);
            },

            validateTraceableCodeCount() {
                if (!this.code?.checked || !this.isPartRefund) {
                    return false;
                }
                const {
                    unit,productInfo,_unitCount,
                } = this.code;
                const {
                    packageUnit,pieceNum,
                } = productInfo;
                const {
                    collectBigCount,collectSmallCount,
                } = this.getTraceCodeCollectCountInfo(this.code);
                const safeCollectBigCount = Big(getSafeNumber(collectBigCount));
                const safeCollectSmallCount = Big(getSafeNumber(collectSmallCount));
                const safePieceNum = Big(getSafeNumber(pieceNum || 1));
                const safeRefundCount = Big(getSafeNumber(_unitCount));
                const isBigUnit = packageUnit === unit;
                let validateFlag = false;
                if (isBigUnit) {
                    const count = safeCollectBigCount.plus(safeCollectSmallCount.div(safePieceNum));
                    validateFlag = !safeRefundCount.eq(count);
                } else {
                    validateFlag = !safeRefundCount.eq(safeCollectSmallCount);
                }
                this.isShowPopover = validateFlag;
                this.isShowError = validateFlag;
                return validateFlag;
            },

            getTraceCodeCollectCountInfo(trData) {
                const {
                    selectedTraceCodeList = [],unit,productInfo,
                } = trData;
                const {
                    packageUnit,pieceNum,
                } = productInfo;
                const isBigUnit = packageUnit === unit;
                const safePieceNum = Big(getSafeNumber(pieceNum || 1));
                let collectBigCount = 0;
                let collectSmallCount = 0;
                if (isBigUnit) {
                    const {
                        _collectBigCount, _collectSmallCount,
                    } = selectedTraceCodeList.reduce((result, cur) => {
                        result._collectBigCount += Number(cur.hisPackageCount ?? 0);
                        result._collectSmallCount += Number(cur.hisPieceCount ?? 0);
                        return result;
                    }, {
                        _collectBigCount: 0, _collectSmallCount: 0,
                    });
                    const smallToBig = (Big(getSafeNumber(_collectSmallCount)).div(safePieceNum)).round(0,0);
                    if (smallToBig.gt(Big(0))) {
                        collectBigCount = _collectBigCount + smallToBig.toNumber();
                        collectSmallCount = (Big(getSafeNumber(_collectSmallCount))).minus(smallToBig.times(safePieceNum)).toNumber();
                    } else {
                        collectSmallCount = _collectSmallCount;
                        collectBigCount = _collectBigCount;
                    }
                } else {
                    collectSmallCount = selectedTraceCodeList.reduce((pre, cur) => {
                        if (cur.hisPackageCount > 0) {
                            return pre + Number(cur.hisPackageCount ?? 0) * pieceNum;
                        }
                        return pre + Number(cur.hisPieceCount ?? 0);

                    }, 0);
                }
                return {
                    collectSmallCount,
                    collectBigCount,
                };
            },
        },
    };
</script>

<style  lang="scss">
.refund-trace-code-operation {
    .popper__arrow::after {
        border-top-color: #ff9933 !important;
    }
}
</style>
