<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="完成发药"
        :show-close="false"
        data-cy="pharmacy-dispense-dialog"
        custom-class="preview-fee-dialog"
        :shadow="showQrCodeDialog"
        tabindex="-1"
        append-to-body
        size="medium"
    >
        <template v-if="!!patientName">
            <div class="dialog-content preview-fee-dialog clearfix">
                <abc-tips-card-v2 v-if="hasCountError" theme="warning" style="margin-bottom: 16px;">
                    <abc-flex justify="space-between">
                        <abc-flex flex="1">
                            当前仍有发药项目未按医保中心要求采集追溯码。继续发药将存在医保监管风险。
                        </abc-flex>

                        <abc-button
                            style="margin-left: 18px;"
                            variant="text"
                            size="small"
                            @click="openTraceCodeDialog"
                        >
                            去修改
                        </abc-button>
                    </abc-flex>
                </abc-tips-card-v2>

                <abc-card background="gray" padding-size="small">
                    <template #title>
                        <div class="preview-item-pharmacy">
                            <label>患<span style="visibility: hidden;">丨</span>者</label>
                            <div style="padding-left: 5px;">
                                {{ patientName }}
                            </div>
                        </div>
                        <div class="preview-item-pharmacy">
                            <label>诊<span style="visibility: hidden;">丨</span>断</label>

                            <abc-popover
                                v-if="diagnose"
                                width="320px"
                                placement="top-start"
                                trigger="hover"
                                theme="yellow"
                            >
                                <div slot="reference" class="diagnose-width">
                                    {{ diagnose }}
                                </div>
                                <div>
                                    {{ diagnose }}
                                </div>
                            </abc-popover>
                        </div>
                    </template>
                    <div class="preview-item-pharmacy" style="margin-bottom: 4px;">
                        <label>发药内容</label>

                        <template v-if="canSetTraceCode">
                            <abc-button
                                variant="text"
                                :theme="hasCountError ? 'warning' : 'primary'"
                                size="small"
                                :disabled="traceCodeDispenseItems.length === 0"
                                @click="openTraceCodeDialog"
                            >
                                追溯码 ({{ traceCodeDispenseItems.length === 0 ? '本单项目均无需采集追溯码' : traceCodeCount }})
                            </abc-button>
                        </template>
                    </div>
                    <div class="preview-form-list">
                        <div class="preview-item-pharmacy">
                            <div v-if="westernFormCount" class="preview-list-item">
                                中西成药
                                ({{ westernFormCount }}种)
                            </div>
                            <div v-if="ChineseFormCount" class="preview-list-item">
                                中药处方
                                ({{ ChineseFormCount }}味)
                            </div>

                            <div v-if="infusionFormCount" class="preview-list-item">
                                输液处方
                                ({{ infusionFormCount }}种)
                            </div>
                            <div v-if="externalFormCount" class="preview-list-item">
                                外治处方
                                ({{ externalFormCount }}种)
                            </div>
                            <div v-if="materialFormCount" class="preview-list-item">
                                材料商品
                                ({{ materialFormCount }}种)
                            </div>
                            <div v-if="glassesFormCount" class="preview-list-item">
                                眼镜商品
                                ({{ glassesFormCount }}种)
                            </div>
                            <div v-if="composeFormCount" class="preview-list-item">
                                套餐内项目
                                ({{ composeFormCount }}种)
                            </div>
                            <div v-if="freeFormCount" class="preview-list-item">
                                赠品
                                ({{ freeFormCount }}种)
                            </div>
                        </div>
                    </div>
                </abc-card>
            </div>

            <abc-card
                v-if="bottomExpand"
                slot="bottom-extend"
                radius-size="mini"
                padding-size="large"
                background="gray"
                class="confirm-treatment-dialog-bottom-extend"
            >
                <abc-text
                    size="normal"
                    theme="gray"
                    tag="div"
                    style="margin-bottom: 8px; text-align: left;"
                >
                    设置每次完诊时需要同时自动打印的医疗文书
                </abc-text>

                <abc-checkbox-group v-model="printOpt.finishSelect" gap="6px 0" class="disabled-no-checkbox print-checkbox-group">
                    <abc-checkbox class="print-checkbox-item" :label="printOptions.DISPENSING_ORDER.label">
                        {{ printOptions.DISPENSING_ORDER.label }}
                    </abc-checkbox>
                    <abc-checkbox class="print-checkbox-item" :label="printOptions.PRESCRIPTION.label">
                        {{ printOptions.PRESCRIPTION.label }}
                    </abc-checkbox>

                    <abc-checkbox class="print-checkbox-item" :label="printOptions.INFUSION_EXECUTE.label">
                        {{ printOptions.INFUSION_EXECUTE.label }}
                    </abc-checkbox>
                    <abc-checkbox class="print-checkbox-item" :label="printOptions.MEDICINE_TAG.label">
                        {{ printOptions.MEDICINE_TAG.label }}
                    </abc-checkbox>
                    <abc-checkbox class="print-checkbox-item" :label="printOptions.PATIENT_TAG.label">
                        {{ printOptions.PATIENT_TAG.label }}
                    </abc-checkbox>
                </abc-checkbox-group>
            </abc-card>


            <div slot="footer" class="dialog-footer">
                <div class="print-meanwhile-set">
                    <abc-checkbox v-model="isPharmacyMeanwhilePrint" @change="changeMeanwhilePrintHandler"></abc-checkbox>
                    <span class="label-text" @click="bottomExpand = !bottomExpand">
                        同时打印
                        <i class="iconfont cis-icon-dropdown_triangle"></i>
                    </span>
                </div>

                <abc-button :loading="saveLoading" data-cy="pharmacy-dispense-dialog-confirm-btn" @click="ok">
                    确定
                </abc-button>
                <abc-button :disabled="saveLoading" variant="ghost" @click="no">
                    取消
                </abc-button>
            </div>
        </template>
    </abc-dialog>
</template>

<script type="text/babel">
    import {
        on, off,
    } from 'utils/dom';
    import Printer from 'views/print';
    import clone from 'utils/clone';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import CollectionTraceCodeDialog from '@/service/trace-code/dialog-collection-trace-code/index';
    import { mapGetters } from 'vuex';
    import DispensaryAPI from 'api/dispensary';
    import TraceCode, {
        SceneTypeEnum, TraceableCodeListErrorType,
    } from '@/service/trace-code/service';
    import { dispenseItemStatusEnum } from 'views/pharmacy/constants';

    export default {
        name: 'ConfirmTreatmentDialog',

        props: {
            value: Boolean,
            chainId: String,
            patient: {
                type: Object,
            },
            diagnose: String,
            dispenseId: String,
            medicalRecord: Object,
            chargeStatus: Number,
            canChargeReview: [Boolean,Number],
            outpatientShowTotalPrice: [Boolean,Number],
            outpatientTotalPrice: [Number,String],
            autoSendOrderInfoSwitch: [Boolean,Number],
            isConsultation: [Boolean,Number],
            submit: Function,
            reviewCharge: Function,
            formInfo: Array,
            shebaoChargeTypeDesc: { // 1自费 2 普通门诊 3 慢特病门诊
                type: String,
                default: '自费',
            },
            dispensingForms: Array,
            traceCodeDispenseItems: Array,
            traceCodeDispensedItems: Array,
            traceCodeFilterItemStatus: Array,
            canSetTraceCode: [Boolean, Number],
            patientOrderId: {
                type: String,
                default: '',
            },
            isShebaoPay: {
                type: Boolean,
                default: false,
            },
            isEnableTraceTempSave: {
                type: Boolean,
                default: false,
            },
            isDisabledCollection: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                closed: false,
                saveLoading: false,
                printOpt: {
                    printSelect: '处方',
                    finishSelect: [],
                },
                showQrCodeDialog: false,
                isPharmacyMeanwhilePrint: false, // 是否需要同时打印
                bottomExpand: false,
                traceCodeCount: 0,
            };
        },

        computed: {
            ...mapGetters([
                'traceCodeConfig',
            ]),
            printOptions() {
                return getViewDistributeConfig().Print.printOptions;
            },

            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            patientName() {
                return this.patient.name || '匿名患者';
            },
            diagnosisHtml() {

                return '';
            },
            westernFormCount() {
                return this.filterFormCount(this.formInfo, 4);
            },
            ChineseFormCount() {
                return this.filterFormCount(this.formInfo, 6);
            },
            infusionFormCount() {
                return this.filterFormCount(this.formInfo, 5);
            },
            externalFormCount() {
                return this.filterFormCount(this.formInfo, 16);
            },
            glassesFormCount() {
                return this.filterFormCount(this.formInfo, 22);
            },
            materialFormCount() {
                let totalCount = 0;
                this.formInfo.forEach((formItem) => {
                    if (formItem.sourceFormType === 8 || formItem.sourceFormType === 9) {
                        totalCount = totalCount + formItem.dispensingFormItems.length;
                    }
                });
                return totalCount;
            },
            composeFormCount() {
                let totalCount = 0;

                this.formInfo.forEach((formItem) => {
                    if (formItem.sourceFormType === 11) {
                        formItem.dispensingFormItems.forEach((goodItem) => {
                            totalCount = totalCount + goodItem.composeChildren.length;
                        });
                    }
                });
                return totalCount;
            },
            freeFormCount() {
                return this.filterFormCount(this.formInfo, 10);
            },
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },

            hasCountError() {
                if (!TraceCode.isStrictCount) return false;
                return this.traceCodeDispenseItems.some((item) => {
                    return item.warnInfo && !item.warnInfo.flag || [
                        TraceableCodeListErrorType.INCONFORMITY_COUNT,
                        TraceableCodeListErrorType.NOT_COLLECTED,
                    ].indexOf(item.warnInfo?.warnType) > -1;
                });
            },
        },

        watch: {
            closed(newVal) {
                if (newVal) {
                    this.showDialog = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            // 初始化同时打印的数据, outpatientNeedPrint 判断是否需要同时打印
            const { cache } = Printer;
            const {
                pharmacyMeanwhilePrint, pharmacy = [],
            } = cache.get();
            this.printOpt.finishSelect = clone(pharmacy);

            // 兼容之前勾选打印处方等同时打印的情况
            this.isPharmacyMeanwhilePrint =
                pharmacyMeanwhilePrint === undefined ? pharmacy.length > 0 : pharmacyMeanwhilePrint;

            if (this.isPharmacyMeanwhilePrint && this.printOpt.finishSelect.length === 0) {
                this.bottomExpand = true;
            }
            this.calcTraceCodeCount();
        },
        mounted() {
            on(document, 'keydown', this.keydownHandle);
        },
        beforeDestroy() {
            off(document, 'keydown', this.keydownHandle);
        },
        methods: {
            calcTraceCodeCount() {
                this.traceCodeCount = 0;
                this.traceCodeDispenseItems.forEach((item) => {
                    if (item.composeChildren) {
                        this.traceCodeCount = item.composeChildren.filter((it) => it.checked).reduce((cur,next) => {
                            const codeCount = (next.traceableCodeList || []).filter((it) => {
                                return !it.used;
                            }).length;
                            return cur + codeCount;
                        }, this.traceCodeCount);
                    } else {
                        const codeCount = item.traceableCodeList?.filter((it) => {
                            return !it.used;
                        }).length || 0;
                        this.traceCodeCount += codeCount;
                    }
                });
            },
            changeMeanwhilePrintHandler() {
                if (!this.printOpt.finishSelect || !this.printOpt.finishSelect.length) {
                    this.bottomExpand = true;
                }
            },

            /**
             * @desc 点击完成接诊弹窗中，是否显示总价格
             * <AUTHOR>
             * @date 2020/01/10 14:37:21
             */


            keydownHandle(e) {
                if (this._collectionTraceCodeDialog?.instance.visible) return;
                const KEY_ENTER = 13;
                const KEY_ESC = 27;
                if (e.keyCode === KEY_ENTER) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.ok();
                } else if (e.keyCode === KEY_ESC) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.no();
                }
            },

            async ok() {
                if (typeof this.submit === 'function') {
                    try {
                        this.saveLoading = true;
                        await this.submit();
                        // 打印
                        const { cache } = Printer;
                        cache.set({
                            pharmacy: this.printOpt.finishSelect,
                            pharmacyMeanwhilePrint: this.isPharmacyMeanwhilePrint,
                        });
                        if (this.isPharmacyMeanwhilePrint) {
                            let finishSelect = clone(this.printOpt.finishSelect);
                            if (finishSelect.includes(this.printOptions.MEDICINE_TAG.label)) {
                                finishSelect = finishSelect.filter((select) => select !== this.printOptions.MEDICINE_TAG.label);
                                this.$emit('printMedicineTag');
                            }
                            this.$emit('print', finishSelect);
                            console.log(this.printOpt.finishSelect);

                        }

                        this.saveLoading = false;
                    } catch (e) {
                        this.saveLoading = false;
                    }
                }
                this.close();
            },

            async no() {
                if (typeof this.cancel === 'function') {
                    this.cancel();
                }
                this.close();
            },

            close() {
                this.closed = true;
                if (typeof this.onClose === 'function') {
                    this.onClose(this);
                }
            },
            destroyElement() {
                off(document, 'keydown', this.keydownHandle);
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            filterFormCount(formInfo, sourceFormType) {
                let totalCount = 0;
                formInfo.forEach((formItem) => {
                    if (formItem.sourceFormType === sourceFormType) {
                        totalCount = totalCount + formItem.dispensingFormItems.length;
                    }
                });
                return totalCount;
            },
            async openTraceCodeDialog() {
                if (this._collectionTraceCodeDialog) return;
                this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                    formItems: this.traceCodeDispenseItems,
                    dispensedFormItems: this.traceCodeDispensedItems,
                    filterItemStatus: this.traceCodeFilterItemStatus,
                    patientOrderId: this.patientOrderId,
                    sceneType: SceneTypeEnum.PHARMACY,
                    isSelectedSocialPay: this.isShebaoPay,
                    isEnableTraceTempSave: this.isEnableTraceTempSave,
                    disabled: this.isDisabledCollection,
                    isDisabledActualCount: true,
                    onConfirm: async (_,action) => {
                        await this.$nextTick();
                        this.calcTraceCodeCount();
                        this.saveTraceCodeHandler();
                        if (action === 'tempSave') {
                            this.close();
                        }
                    },
                    onClose: () => {
                        this._collectionTraceCodeDialog = null;
                    },
                });
                this._collectionTraceCodeDialog.generateDialogAsync();
            },
            saveTraceCodeHandler() {
                try {
                    DispensaryAPI.saveTraceCode(this.dispenseId, {
                        list: this.traceCodeDispenseItems.filter((item) => item.status !== dispenseItemStatusEnum.DISPENSED && item.status !== dispenseItemStatusEnum.RETURN).map((item) => {
                            return {
                                id: item.id,
                                shebaoDismountingFlag: item.shebaoDismountingFlag,
                                traceableCodeRule: item.traceableCodeRule,
                                traceableCodeList: item.traceableCodeList ? item.traceableCodeList.map((code) => {
                                    const {
                                        count, ...restCode
                                    } = code;
                                    return restCode;
                                }) : [],
                                composeChildren: item.composeChildren && item.composeChildren.filter((it) => it.status !== dispenseItemStatusEnum.DISPENSED && it.status !== dispenseItemStatusEnum.RETURN).map((it) => {
                                    return {
                                        id: it.id,
                                        shebaoDismountingFlag: it.shebaoDismountingFlag,
                                        traceableCodeRule: item.traceableCodeRule,
                                        traceableCodeList: it.traceableCodeList ? it.traceableCodeList.map((code) => {
                                            const {
                                                count, ...restCode
                                            } = code;
                                            return restCode;
                                        }) : [],
                                    };
                                }),
                            };
                        }),
                    });
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/theme';

.preview-fee-dialog {
    text-align: center;

    .abc-dialog-header {
        display: flex;
        align-items: center;
    }

    h5 {
        margin-bottom: 24px;
        line-height: 16px;
    }

    .preview-item-pharmacy {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 8px;
        font-size: 14px;

        &:last-child {
            margin-bottom: 0;
        }

        .preview-list-item {
            width: 50%;
            margin-bottom: 8px;
        }

        label {
            min-width: 56px;
            margin-right: 8px;
            color: $T2;
            text-align: left;
        }

        > div {
            display: flex;
            flex-direction: column;
            text-align: left;

            > p {
                display: flex;
                align-items: center;
                font-size: 18px;
                color: $Y2;
            }

            .tips {
                margin-top: 4px;
                font-size: 12px;
                line-height: 28px;
                color: $T2;
            }

            button {
                font-size: 12px;
            }
        }

        .diagnosis-html {
            display: block;

            > div {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
            }

            .global-tooth-selected-quadrant > div {
                color: $T1;
            }
        }
    }

    .preview-form-list {
        padding-left: 69px;
    }

    .print-meanwhile-set {
        margin-right: auto;
        font-size: 14px;

        .abc-checkbox-wrapper {
            margin-right: 0;
        }

        .iconfont {
            color: $T2;
            cursor: pointer;
        }

        .cis-icon-dropdown_triangle {
            margin-left: -4px;
            font-size: 14px;
        }

        > .label-text {
            margin-left: 4px;
            cursor: pointer;
        }

        .label-tips {
            font-size: 12px;
            color: #ff9933;
        }
    }

    .split-line-preview {
        width: 450px;
        height: 1px;
        margin-bottom: 12px;
        border-bottom: 1px dashed #e6eaee;
    }

    .diagnose-width {
        width: 322px;
        height: 20px;
        padding-left: 5px;
        overflow: hidden;
    }

    .confirm-treatment-dialog-bottom-extend {
        position: absolute;
        left: 0;
        width: 100%;
        transform: translateY(54px);

        .tip-text {
            margin-bottom: 6px;
            font-size: 12px;
            color: $T2;
            text-align: left;
        }

        .print-checkbox-group {
            display: flex;
            flex-wrap: wrap;

            .print-checkbox-item {
                width: 25%;
            }
        }

        .abc-checkbox-wrapper {
            margin: 0;

            .abc-checkbox__label {
                color: $T1;
            }
        }
    }
}
</style>
