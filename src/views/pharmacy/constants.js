import { SourceFormTypeEnum } from '@/service/charge/constants';
import Clone from 'utils/clone';
import { dispenseOrderOperationTypeEnum } from '@/views-hospital/pharmacy/utils/constant';
import TraceCode from '@/service/trace-code/service';
import { OutpatientChargeTypeEnum } from 'views/outpatient/constants';
import { GoodsTypeEnum } from '@abc/constants';

export const dispenseStatusEnum = Object.freeze({
    // 待发药 0
    WAITING: 0,
    // 已发药 1
    DISPENSED: 1,
    // 已退药 2
    RETURN: 2,
    // 已关闭 3
    CLOSE: 3,
});

export const payStatusEnum = Object.freeze({
    PAY: 10, // 已支付
    REFUND: 20, // 已退费
});


export const QuickListTabStatusEnum = {
    // 待发药 3, 待加工 4, 待快递 5
    MEDICINE_TO_BE_RELEASED: 3,
    TO_BE_PROCESSED: 4,
    TO_BE_DELIVERED: 5,
};
export const dispenseItemStatusEnum = Object.freeze({
    // 待发药 0
    WAITING: 0,
    // 已发药 1
    DISPENSED: 1,
    // 已退药 2
    RETURN: 2,
    // 已退费 3
    CANCELED: 3,
    // 部分发药
    PART: 4,
});

// operateType
export const PROCESSED = 0;
export const WITHDRAWAL_MEDICINE = 1;
export const SEND_MEDICINE = 2;
export const AUDIT = 3;
export const DELIVERED = 4;
export const UPDATE_DELIVERY = 5;
export const CREATED_DISPENSING_SHEET = 6;
export const CANCEL_DELIVERY = 7;
export const CANCEL_PROCESS = 8;
export const COMPOUND = 9;

export const DISPENSE_WAY = {
    all: 0, // 全发
    byDay: 1, // 按天发
    byUnit: 2, // 按次数发
};

export const CM_VALUE_TO_DAY = {
    'qd': 1,
    'bid': 2,
    'tid': 3,
    'qid': 4,
};

export const cmValueToDay = (value) => {
    const res = CM_VALUE_TO_DAY[value];

    if (res) return res;

    const reg = /^q[0-9]{1,2}h$/;
    if (reg.test(value)) {
        return Math.ceil(24 / value.replace(/[^0-9]/ig, ''));
    }

    return 1;
};

export const EXTERNAL_VALUE_TO_DAY = {
    '1日1次': 1,
    '1日2次': 2,
};

export const externalValueToDay = (value) => {
    const res = EXTERNAL_VALUE_TO_DAY[value];

    if (res) return res;

    return 1;
};

// 材料，商品，眼镜不需要计算规则
// 中药暂时不做规则计算

export const calcDispenseNumberByUnit = (type, formItem) => {
    const {
        usageInfo, productInfo, useDismounting, unit, unitCount, remainingDoseCount,
    } = formItem;
    const {
        medicineDosageNum, pieceNum, pieceUnit, medicineDosageUnit, componentContentNum, componentContentUnit,
    } = productInfo || {};
    const {
        dosage, dosageUnit,
    } = usageInfo;

    // 套餐
    if (type === SourceFormTypeEnum.COMPOSE) {
        return 1;
    }

    // 外治处方
    if (type === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL) {
        return Math.ceil(unitCount / dosage);
    }

    // 中西成药，输注
    if (type === SourceFormTypeEnum.PRESCRIPTION_WESTERN || type === SourceFormTypeEnum.PRESCRIPTION_INFUSION) {
        if (dosageUnit === unit) {
            return +dosage;
        }

        // 成分
        if (dosageUnit === componentContentUnit && componentContentNum) {
            const num = Math.ceil(+dosage / componentContentNum);

            if (useDismounting) {
                return num;
            }

            return Math.ceil(num / pieceNum);
        }

        // medicineDosageUnit
        if (dosageUnit === medicineDosageUnit && medicineDosageNum) {
            const num = Math.ceil(+dosage / medicineDosageNum);

            if (useDismounting) { // 是否拆零
                return num; // 返回小单位
            }

            return Math.ceil(num / pieceNum); // 返回大单位
        }


        // pieceUnit
        if (dosageUnit === pieceUnit && pieceNum) {
            if (useDismounting) { // 是否拆零
                return +dosage; // 返回小单位
            }

            return Math.ceil(+dosage / pieceNum);// 返回大单位
        }

        return remainingDoseCount; // 兜底返回剩余数量
    }
};

export const calcDispenseNumberByDay = (type, formItem, composeItem) => {
    const {
        usageInfo, productInfo, useDismounting, unit, unitCount, remainingDoseCount,
    } = formItem;
    const {
        medicineDosageNum, pieceNum, pieceUnit, medicineDosageUnit, componentContentNum, componentContentUnit,
    } = productInfo;

    const {
        dosage, dosageUnit, freq,
    } = usageInfo;

    if (type === SourceFormTypeEnum.COMPOSE) {
        return composeItem.usageInfo && composeItem.usageInfo.dailyDosage ? +composeItem.usageInfo.dailyDosage : 1;
    }

    if (type === SourceFormTypeEnum.PRESCRIPTION_EXTERNAL) {
        const _day = externalValueToDay(freq); // 频率计算 一天多少次
        return Math.ceil(unitCount / dosage) * _day;
    }

    if (type === SourceFormTypeEnum.PRESCRIPTION_WESTERN || type === SourceFormTypeEnum.PRESCRIPTION_INFUSION) {
        const _day = cmValueToDay(freq); // 频率计算 一天多少次

        if (dosageUnit === unit) {
            return +dosage * _day;
        }

        if (dosageUnit === componentContentUnit && componentContentNum) {
            const num = Math.ceil(+dosage * _day / componentContentNum);

            if (useDismounting) {
                return num;
            }

            return Math.ceil(num / pieceNum);
        }

        if (dosageUnit === medicineDosageUnit && medicineDosageNum) {
            const num = Math.ceil(+dosage * _day / medicineDosageNum);

            if (useDismounting) {
                return num;
            }

            return Math.ceil(num / pieceNum);
        }

        if (dosageUnit === pieceUnit && pieceNum) {
            if (useDismounting) {
                return +dosage * _day;
            }

            return Math.ceil(+dosage * _day / pieceNum);
        }

        return remainingDoseCount;
    }
};

export function flatArray(arr) {
    const result = [];
    arr.forEach((item) => {
        result.push(item);

        Object.keys(item).forEach((key) => {
            const value = item[key];
            if (Array.isArray(value)) {
                result.push(...flatArray(value));
            }
        });
    });
    return result;
}

export function filterItemTraceCodeList(item) {
    item._traceableCodeListCache = item._traceableCodeListCache || item.traceableCodeList;
    if (item.status === dispenseItemStatusEnum.RETURN) {
        item.traceableCodeList = item._traceableCodeListCache?.filter((it) => it.used === 2);
    } else if (item.status === dispenseItemStatusEnum.DISPENSED) {
        item.traceableCodeList = item._traceableCodeListCache?.filter((it) => it.used === 1);
    } else {
        item.traceableCodeList = item.traceableCodeList?.filter((it) => !it.used);
    }
}

/**
 * @desc 获取可以填写追溯码的 dispensingItem 待发药的
 * 目前可以上传追溯码的：
 * 药品 - 西药、中成药
 * 物资 - 医疗器械
 * <AUTHOR> Yang
 * @date 2024-06-17 15:11:35
 * @param {Array} dispensingForms
 */
export function getTraceCodeDispensingItems(dispensingForms) {
    const formItems = [];
    dispensingForms.forEach((form) => {
        form.dispensingFormItems.forEach((item) => {
            // 旧：采集强控模式下部分发药必须全部采集
            // 新：所有模式下部分发药必须全部采集
            // const res = form.checkedList.find((it) => it.id === item.id);
            // const childStatus = item.composeChildren?.some((child) => {
            //     return child.status > 0;
            // });
            if (
                TraceCode.formItemSupportTraceCode(item) &&
                item.sourceItemType !== OutpatientChargeTypeEnum.NO_CHARGE
            ) {

                filterItemTraceCodeList(item);
                item.composeChildren?.forEach((child) => {
                    filterItemTraceCodeList(child);
                });
                formItems.push(item);
            }
        });
    });
    return formItems;
}
function filterDispensedItemTraceCodeList(filterItems) {
    const formItems = [];
    const returnedFormItems = [];
    filterItems.forEach((item) => {
        if (
            item.sourceItemType !== OutpatientChargeTypeEnum.NO_CHARGE &&
            TraceCode.formItemSupportTraceCode(item)
        ) {
            if (item.productType === GoodsTypeEnum.COMPOSE && item.composeChildren && item.composeChildren.length > 0) {
                const {
                    formItems: childFormItems,returnedFormItems: childReturnedItems,
                } = filterDispensedItemTraceCodeList(item.composeChildren);

                if (childFormItems && childFormItems.length > 0) {
                    formItems.push({
                        ...item,
                        composeChildren: childFormItems,
                    });
                }
                if (childReturnedItems && childReturnedItems.length > 0) {
                    returnedFormItems.push({
                        ...item,
                        composeChildren: childReturnedItems,
                    });
                }
            } else {
                if (item.traceableCodeList && item.traceableCodeList?.length > 0) {
                    // 创建退药项目和已发药项目
                    const returnItem = {
                        ...item, status: dispenseItemStatusEnum.RETURN,traceableCodeList: [],
                    };
                    const dispensedItem = {
                        ...item, traceableCodeList: [],
                    };
                    // 处理追溯码列表
                    item.traceableCodeList?.forEach((code) => {
                        // 退药的追溯码
                        if (code.used === 2) {
                            returnItem.traceableCodeList.push(code);
                        }

                        // 已发药的追溯码
                        if (code.used === 1) {
                            dispensedItem.traceableCodeList.push(code);
                        }
                    });

                    // 添加到结果列表
                    if (dispensedItem.traceableCodeList && dispensedItem.traceableCodeList?.length > 0) {
                        formItems.push(dispensedItem);
                    }
                    if (returnItem.traceableCodeList && returnItem.traceableCodeList?.length > 0) {
                        returnedFormItems.push(returnItem);
                    }
                } else {
                    formItems.push(item);
                }
            }
        }
    });
    return {
        formItems,
        returnedFormItems,
    };
}
/**
 * @desc 获取可填写追溯码的 dispensedItem 发药后的
 * 目前可以上传追溯码的：
 * 药品 - 西药、中成药
 * 物资 - 医疗器械
 * <AUTHOR> Yang
 * @date 2024-06-17 15:11:35
 * @param {Array} dispensingForms
 */
export function getTraceCodeDispensedItems(dispensingForms, filterStatus = []) {
    const formItems = [];
    const returnedFormItems = [];
    Clone(dispensingForms).forEach((form) => {
        const filterItems = form.dispensingFormItems.filter((item) => {
            item.composeChildren = item.composeChildren?.filter((it) => {
                return filterStatus.indexOf(it.status) > -1 && (it.isMainItem === 1 || it.status === dispenseItemStatusEnum.RETURN);
            }) || [];
            return (filterStatus.indexOf(item.status) > -1 && (item.isMainItem === 1 || item.status === dispenseItemStatusEnum.RETURN)) || item.composeChildren?.length;
        });
        const {
            formItems: filterFormItems,returnedFormItems: filterReturnedFormItems,
        } = filterDispensedItemTraceCodeList(filterItems);
        formItems.push(...filterFormItems);
        returnedFormItems.push(...filterReturnedFormItems);
    });
    return {
        formItems,
        returnedFormItems,
    };
}

export function getTraceCodeDispensedItemsWithCollCheckStrictMode(dispensingForms, dispensedForms) {
    const newDispensedForms = [];
    const edMap = new Map(dispensedForms.map((item) => [item.id, item]));
    dispensingForms.forEach((form) => {
        if (edMap.has(form.id)) {
            const find = newDispensedForms.find((it) => it.id === form.id);
            if (!find) {
                newDispensedForms.push(edMap.get(form.id));
            }
        } else {
            newDispensedForms.push(form);
        }
    });
    return newDispensedForms;
}

/**
 * @desc 住院 - 获取可以填写追溯码的 dispensingItem 待发药的
 * 目前可以上传追溯码的：
 * 药品 - 西药、中成药
 * 物资 - 医疗器械
 * <AUTHOR>
 * @date 2024/8/30 16:03:32
 * @param {Array} dispensingList
 */
export function getHospitalPharmacyTraceCodeDispensingItems(dispensingList) {
    const formItems = [];
    const filterDispensingList = dispensingList.filter((item) => !item.isChineseForm).filter((item) => [dispenseOrderOperationTypeEnum.DISPENSE, dispenseOrderOperationTypeEnum.DEDUCT].includes(item.dispenseType));
    filterDispensingList.forEach((item) => {
        (item._path || []).forEach((it) => {
            const formItem = it._formItem;
            if (TraceCode.formItemSupportTraceCode(formItem)) {
                formItems.push(formItem);
            }
        });
    });
    return formItems;
}

/**
 * 住院 - 根据操作类型获取已采集追溯码的 dispensingItem
 * @param dispensingList
 * @param dispensingStatusList
 * @return {*[]}
 */
export function getHospitalPharmacyTraceCodeItemsByDispensingStatus(dispensingList, dispensingStatusList) {
    const formItems = [];
    const filterDispensingList = dispensingList.filter((item) => !item.isChineseForm).filter((item) => (dispensingStatusList ? dispensingStatusList.includes(item.status) : true));
    filterDispensingList.forEach((item) => {
        (item._path || []).forEach((it) => {
            const formItem = it._formItem;
            if (TraceCode.formItemSupportTraceCode(formItem)) {
                formItems.push(formItem);
            }
        });
    });
    return formItems;
}
