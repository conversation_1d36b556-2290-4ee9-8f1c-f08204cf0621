<template>
    <div class="statistics-module_promotion-situation">
        <abc-space>
            <abc-date-picker-bar
                v-model="params.currentBasicInfoFilter"
                :options="dateOptions"
                :picker-options="pickerOptions"
                value-format="YYYY-MM-DD"
                :clearable="false"
                @change="handleDateChange"
            ></abc-date-picker-bar>
            <clinic-select
                v-if="isChainAdmin"
                v-model="params.clinicId"
                width="150"
                :is-custom-clinic="false"
                @change="handleClinicChange"
            >
            </clinic-select>
            <clinic-select
                v-model="params.memberCreateClinicId"
                width="150"
                :is-custom-clinic="false"
                placeholder="开卡门店"
                @change="handleClinicChange"
            >
            </clinic-select>
        </abc-space>
        <overview-section
            v-for="(section, index) in renderOverviewData "
            :key="index"
            :overview-item="section"
        ></overview-section>
    </div>
</template>

<script>
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import { dateRangeFormat } from 'views/statistics/common/util';
    import ConsumptionAmountModel from 'views/statistics/promotion/situation/adapter/consumption-amount.js';
    import ConsumptionPeopleModel from 'views/statistics/promotion/situation/adapter/consumption-people.js';
    import ChargeAmountModel from 'views/statistics/promotion/situation/adapter/charge-amount.js';
    import ChargePeopleModel from 'views/statistics/promotion/situation/adapter/charge-people.js';
    import CouponTotalAmountModel from 'views/statistics/promotion/situation/adapter/coupon-total-amount';
    import GiftTotalAmountModel from 'views/statistics/promotion/situation/adapter/gift-total-amount';
    import OverviewSection from 'views/statistics/promotion/situation/overview-section.vue';
    import PromotionApi from 'views/statistics/core/api/promotion.js';
    import ClinicSelect from 'views/statistics/common/clinic-select/clinic-select';
    import { mapGetters } from 'vuex';
    import { AbcDatePickerBar } from '@abc/ui-pc'; const { DatePickerBarOptions } = AbcDatePickerBar;
    import { formatDate } from '@abc/utils-date';
    import {
        formatMoney, toMoney,
    } from 'src/filters/index';


    export default {
        components: {
            OverviewSection,
            ClinicSelect,
        },

        mixins: [ ClinicTypeJudger ],

        data() {
            return {
                filteredServicePackOptions: [],
                params: {
                    filterStart: '',
                    filterEnd: '',
                    clinicId: '',
                    currentBasicInfoFilter: DatePickerBarOptions.DAY.label,
                },
                memberLoading: false,
                cardLoading: false,
                discountLoading: false,
                giftLoading: false,
                couponLoading: false,
                dateOptions: [
                    DatePickerBarOptions.DAY,
                    DatePickerBarOptions.MONTH,
                    DatePickerBarOptions.LATEST_THREE_MONTH,
                    DatePickerBarOptions.LATEST_HALF_YEAR,
                    DatePickerBarOptions.YEAR,
                ],
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                memberData: {},
                cardItemData: {},
                discountData: {},
                specialData: {},
                giftData: {},
                couponData: {},
                cardsSeriesData: [],
                cardsCategory: [],

                paysSeriesData: [],
                paysLegend: [],
                paysCategory: [],
                specialPriceLoading: false,
                // 消费金额
                consumptionAmountData: ConsumptionAmountModel.defaultData,
                // 消费人次
                consumptionPeopleData: ConsumptionPeopleModel.defaultData,
                // 折扣活动 - 收费总金额
                chargeAmountData: ChargeAmountModel.defaultData,
                // 折扣活动 - 消费人次
                chargePeopleData: ChargePeopleModel.defaultData,
                // 特价活动 - 收费总金额
                specialAmountData: ChargeAmountModel.defaultData,
                // 特价活动 - 消费人次
                specialPeopleData: ChargePeopleModel.defaultData,
                // 优惠券-使用分析
                couponAnalysisSeries: [],
                couponAnalysisCategory: [],
                // 优惠券-用券总成交额
                couponTotalAmountData: CouponTotalAmountModel.defaultData,
                // 满减返
                giftTotalAmountData: GiftTotalAmountModel.defaultData,
                giftCustomerPriceSeries: [],
                giftCustomerPriceLegend: [],
                giftCustomerPriceCategory: [],
                // 买赠
                pharmacyGiftTotalAmountData: GiftTotalAmountModel.defaultData,
                pharmacyGiftCustomerPriceSeries: [],
                pharmacyGiftCustomerPriceLegend: [],
                pharmacyGiftCustomerPriceCategory: [],
                pharmacyGiftLoading: false,
                pharmacyGiftData: {},

            };
        },
        computed: {
            ...mapGetters(['isChainAdmin', 'currentClinic']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            showCardOverview() {
                return this.viewDistributeConfig.Statistics.promotionStat.showCardOverview;
            },
            showCouponOverview() {
                return this.viewDistributeConfig.Statistics.promotionStat.showCouponOverview;
            },
            discountStatSectionTitle() {
                return this.viewDistributeConfig.Statistics.promotionStat.discountStatSectionTitle;
            },
            giftStatSectionTitle() {
                return this.viewDistributeConfig.Statistics.promotionStat.giftStatSectionTitle;
            },
            showPharmacyGiftSection() {
                return this.viewDistributeConfig.Statistics.promotionStat.showPharmacyGiftSection;
            },
            showSpecialStatSection() {
                return this.viewDistributeConfig.Statistics.promotionStat.showSpecialStatSection;
            },
            showDiscountStatSection() {
                return this.viewDistributeConfig.Statistics.promotionStat.showDiscountStatSection;
            },
            showGiftStatSection() {
                return this.viewDistributeConfig.Statistics.promotionStat.showGiftStatSection;
            },
            renderOverviewData() {
                const overviews = [
                    {
                        title: '会员',
                        keyData: this.memberKeyData,
                        loading: this.memberLoading,
                        visible: true,
                        visualData: [
                            {
                                label: '消费金额对比',
                                description: '各类会员对应的收费单金额占比',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.consumptionAmountData && this.consumptionAmountData.data || [],
                                    legend: this.consumptionAmountData && this.consumptionAmountData.legend || {},
                                },
                                type: 'pie',
                            },
                            {
                                label: '消费人次对比',
                                description: '',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.consumptionPeopleData.data || [],
                                    legend: this.consumptionPeopleData.legend || {},
                                },
                                type: 'pie',
                            },
                        ],
                    },
                    {
                        title: '卡项',
                        keyData: this.cardItemKeyData,
                        loading: this.cardLoading,
                        visualData: [
                            {
                                label: '开卡人数/购卡金额',
                                description: '',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.cardsSeriesData,
                                    legend: {
                                        data: ['开卡人数', '购卡金额'],
                                    },
                                    category: this.cardsCategory,
                                    colors: ['#fd8351', '#546fc6'],
                                    grid: {
                                        left: '1%',
                                        right: '0',
                                        top: '10%',
                                        bottom: '4%',
                                        containLabel: true,
                                    },
                                },
                                type: 'group-bar',
                            },
                            {
                                label: '余额支付',
                                description: '',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.paysSeriesData,
                                    grid: {
                                        left: '1%',
                                        right: '0',
                                        top: '10%',
                                        bottom: '4%',
                                        containLabel: true,
                                    },
                                    legend: {
                                        data: this.paysLegend,
                                    },
                                    category: this.paysCategory,
                                },
                                type: 'vertical-bar',
                            },
                        ],
                        visible: this.showCardOverview,
                    },
                    {
                        title: '优惠券',
                        keyData: this.couponKeyData,
                        loading: this.couponLoading,
                        visible: this.showCouponOverview ? true : (this.isChainAdmin || this.isChainSubStore),
                        visualData: [
                            {
                                label: '使用分析',
                                description: '',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.couponAnalysisSeries,
                                    legend: {
                                        data: ['领券总数', '用券总数'],
                                    },
                                    category: this.couponAnalysisCategory,
                                    colors: ['#5470c6', '#91cc75'],
                                    grid: {
                                        left: '1%',
                                        right: '0',
                                        top: '10%',
                                        bottom: '4%',
                                        containLabel: true,
                                    },
                                },
                                type: 'group-bar',
                            },
                            {
                                label: '用券总成交额',
                                description: '使用各个优惠券的收费单总金额占比',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.couponTotalAmountData.data,
                                    legend: this.couponTotalAmountData.legend,
                                },
                                type: 'pie',
                            },
                        ],
                    },
                    {
                        title: '特价',
                        keyData: this.specialPriceKeyData,
                        loading: this.specialPriceLoading,
                        visible: this.showSpecialStatSection,
                        visualData: [
                            {
                                label: '消费金额占比',
                                description: '各类折扣活动对应的收费单金额占比',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.specialAmountData.data,
                                    legend: this.specialAmountData.legend,
                                },
                                type: 'pie',
                            },
                            {
                                label: '消费人次',
                                description: '使用折扣的收费人次',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.specialPeopleData.data,
                                    legend: this.specialPeopleData.legend,
                                },
                                type: 'pie',
                            },
                        ],
                    },
                    {
                        title: this.discountStatSectionTitle,
                        keyData: this.discountKeyData,
                        loading: this.discountLoading,
                        visible: true,
                        visualData: [
                            {
                                label: '消费金额占比',
                                description: '各类折扣活动对应的收费单金额占比',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.chargeAmountData.data,
                                    legend: this.chargeAmountData.legend,
                                },
                                type: 'pie',
                            },
                            {
                                label: '消费人次',
                                description: '使用折扣的收费人次',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.chargePeopleData.data,
                                    legend: this.chargePeopleData.legend,
                                },
                                type: 'pie',
                            },
                        ],
                    },
                    {
                        title: '买赠',
                        keyData: this.pharmacyGiftKeyData,
                        loading: this.pharmacyGiftLoading,
                        visible: this.showPharmacyGiftSection,
                        visualData: [
                            {
                                label: '收费总金额',
                                description: '',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.pharmacyGiftTotalAmountData.data,
                                    legend: this.pharmacyGiftTotalAmountData.legend,
                                },
                                type: 'pie',
                            },

                            {
                                label: '客单价',
                                description: '',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    grid: {
                                        left: '1%',
                                        right: '0',
                                        top: '10%',
                                        bottom: '4%',
                                        containLabel: true,
                                    },
                                    data: this.pharmacyGiftCustomerPriceSeries || [],
                                    legend: {
                                        data: this.pharmacyGiftCustomerPriceLegend || [],
                                    },
                                    category: this.pharmacyGiftCustomerPriceCategory || [],
                                },
                                type: 'vertical-bar',
                            },
                        ],
                    },
                    {
                        title: this.giftStatSectionTitle,
                        keyData: this.giftKeyData,
                        loading: this.giftLoading,
                        visible: true,
                        visualData: [
                            {
                                label: '收费总金额',
                                description: '',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    data: this.giftTotalAmountData.data,
                                    legend: this.giftTotalAmountData.legend,
                                },
                                type: 'pie',
                            },

                            {
                                label: '客单价',
                                description: '',
                                dateRange: this.displayDateRange,
                                chartInfo: {
                                    grid: {
                                        left: '1%',
                                        right: '0',
                                        top: '10%',
                                        bottom: '4%',
                                        containLabel: true,
                                    },
                                    data: this.giftCustomerPriceSeries || [],
                                    legend: {
                                        data: this.giftCustomerPriceLegend || [],
                                    },
                                    category: this.giftCustomerPriceCategory || [],
                                },
                                type: 'vertical-bar',
                            },
                        ],
                    },
                ];
                return overviews
                    .filter((item) => item.visible)
                    .map((item) => {
                        if (item.keyData?.length) {
                            item.keyData.forEach((one) => {
                                one.slotName = 'statisticCustom';
                            });
                        }
                        if (item.visualData?.length) {
                            item.visualData.forEach((one) => {
                                one.slotName = 'chartCustom';
                            });
                        }
                        return item;
                    });
            },
            // 会员
            memberKeyData() {
                const {
                    newMemberCount , totalPrice, rechargePrice, consumePrice,rechargePrincipalPrice,rechargePresentPrice,
                } = this.memberData;
                return [
                    {
                        text: '新增会员人数',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: newMemberCount || 0,
                    },
                    {
                        text: '会员消费金额',
                        tips: '包括储值及非储值支付',
                        dateRange: this.displayDateRange,
                        amount: toMoney(totalPrice) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '充值金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(rechargePrice) || '0.00',
                        type: 'money',
                        bottomContent: {
                            principal: toMoney(rechargePrincipalPrice) || '0.00',
                            bonus: toMoney(rechargePresentPrice) || '0.00',
                        },
                    },
                    {
                        text: '余额消费',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(consumePrice) || '0.00',
                        type: 'money',
                    },
                ];
            },
            // 卡项
            cardItemKeyData() {
                const {
                    numberOfNewCard, priceOfNewCard, rechargePrice, totalPrice,rechargePrincipalPrice,rechargePresentPrice,
                } = this.cardItemData;
                return [
                    {
                        text: '开卡人数',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: numberOfNewCard || 0,
                    },
                    {
                        text: '开卡总金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(priceOfNewCard) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '充值金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(rechargePrice) || '0.00',
                        type: 'money',
                        bottomContent: {
                            principal: toMoney(rechargePrincipalPrice) || '0.00',
                            bonus: toMoney(rechargePresentPrice) || '0.00',
                        },
                    },
                    {
                        text: '余额消费',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(totalPrice) || '0.00',
                        type: 'money',
                    },
                ];
            },
            // 折扣活动
            discountKeyData() {
                const {
                    patientOrderCount, totalAmount, discountAmount, rate,
                } = this.discountData;
                return [
                    {
                        text: '消费人数',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: patientOrderCount || 0,
                    },
                    {
                        text: '消费金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(totalAmount) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '折扣金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(discountAmount) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '费效比',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: `${parseFloat(((rate || 0) * 100)).toFixed(2)}%` || '0.00%',
                        type: 'rate',
                    },
                ];
            },
            // 特价活动
            specialPriceKeyData() {
                const {
                    patientOrderCount, totalAmount, discountAmount, rate,
                } = this.specialData;
                return [
                    {
                        text: '消费人数',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: patientOrderCount || 0,
                    },
                    {
                        text: '消费金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(totalAmount) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '折扣金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(discountAmount) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '费效比',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: `${parseFloat(((rate || 0) * 100)).toFixed(2)}%` || '0.00%',
                        type: 'rate',
                    },
                ];
            },
            // 优惠券
            couponKeyData() {
                const {
                    gotCouponCount, usedCouponCount, totalAmount, discountAmount, rate,
                } = this.couponData;
                return [
                    {
                        text: '领券/用券总数',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: `${gotCouponCount || 0}/${usedCouponCount || 0}` || 0,
                    },
                    {
                        text: '用券总成交额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: totalAmount || '0.00',
                        type: 'money',
                    },
                    {
                        text: '折扣总金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(discountAmount) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '费效比',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: `${parseFloat(((rate || 0) * 100)).toFixed(2)}%` || '0.00%',
                        type: 'rate',
                    },
                ];
            },
            // 满减返
            giftKeyData() {
                const {
                    patientOrderCount, totalAmount, avgAmount, giftAmount,
                } = this.giftData;
                return [
                    {
                        text: '消费人数',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: patientOrderCount || 0,
                    },
                    {
                        text: '消费金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(totalAmount) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '客单价',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: avgAmount || '0.00',
                        type: 'money',
                    },
                    {
                        text: '优惠金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(giftAmount) || '0.00',
                        type: 'money',
                    },
                ];
            },

            // 买赠
            pharmacyGiftKeyData() {
                const {
                    patientOrderCount, totalAmount, avgAmount, giftAmount,
                } = this.pharmacyGiftData;
                return [
                    {
                        text: '消费人数',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: patientOrderCount || 0,
                    },
                    {
                        text: '消费金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(totalAmount) || '0.00',
                        type: 'money',
                    },
                    {
                        text: '客单价',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: avgAmount || '0.00',
                        type: 'money',
                    },
                    {
                        text: '优惠金额',
                        tips: '',
                        dateRange: this.displayDateRange,
                        amount: toMoney(giftAmount) || '0.00',
                        type: 'money',
                    },
                ];
            },

            displayDateRange() {
                const {
                    filterStart,filterEnd,
                } = this.params;
                if (filterStart && filterEnd) {
                    return dateRangeFormat(this.params.filterStart, this.params.filterEnd);
                }
                return formatDate(new Date());
            },
        },

        mounted() {
            this.params.filterStart = formatDate(new Date());
            this.params.filterEnd = formatDate(new Date());

            this.getMemberData();
            this.getCardData();
            this.getDisCountData();
            if (this.showSpecialStatSection) {
                this.getSpecialData();
            }
            if (this.showPharmacyGiftSection) {
                this.getPharmacyGiftData();
            }
            this.getGiftData();
            if (this.isChainAdmin || this.isChainSubStore) {
                this.getCouponData();
            }
        },

        methods: {
            formatMoney,
            handleDateChange(date) {
                if (Array.isArray(date)) {
                    this.params.filterStart = date[ 0 ];
                    this.params.filterEnd = date[ 1 ];
                } else {
                    this.params.filterStart = date;
                    this.params.filterEnd = date;
                }
                this.getMemberData();
                this.getCardData();
                this.getDisCountData();
                if (this.showSpecialStatSection) {
                    this.getSpecialData();
                }
                this.getGiftData();
                if (this.showPharmacyGiftSection) {
                    this.getPharmacyGiftData();
                }
                if (this.isChainAdmin || this.isChainSubStore) {
                    this.getCouponData();
                }
            },
            handleClinicChange() {
                this.getMemberData();
                this.getCardData();
                this.getDisCountData();
                if (this.showSpecialStatSection) {
                    this.getSpecialData();
                }
                this.getGiftData();
                if (this.showPharmacyGiftSection) {
                    this.getPharmacyGiftData();
                }
                if (this.isChainAdmin || this.isChainSubStore) {
                    this.getCouponData();
                }
            },

            getParams() {
                const {
                    filterStart: beginDate, filterEnd: endDate, clinicId, memberCreateClinicId,
                } = this.params;
                return {
                    clinicId,
                    beginDate,
                    endDate,
                    memberCreateClinicId,
                };
            },

            async getMemberData() {
                this.memberLoading = true;
                const params = this.getParams();
                try {
                    const { data } = await PromotionApi.situation.member(params);
                    this.memberData = data;
                    const consumptionAmountData = new ConsumptionAmountModel(this.memberData && this.memberData.amounts);
                    this.consumptionAmountData = consumptionAmountData.data;
                    const consumptionPeopleData = new ConsumptionPeopleModel(this.memberData && this.memberData.counts);
                    this.consumptionPeopleData = consumptionPeopleData.data;
                } catch (error) {
                    console.log(error);
                    this.memberData = [];
                } finally {
                    this.memberLoading = false;
                }
            },

            async getCardData() {
                if (!this.showCardOverview) return false;
                this.cardLoading = true;
                const params = this.getParams();
                try {
                    const { data } = await PromotionApi.situation.cardItem(params);

                    this.cardItemData = data;
                    const effectiveNewCards = this.cardItemData?.newCards?.filter((item) => item.numberOfNewCard || item.priceOfNewCard);
                    this.cardsCategory = effectiveNewCards.map((item) => item.name);
                    this.cardsSeriesData = [effectiveNewCards.map((item) => item.numberOfNewCard)];
                    this.cardsSeriesData.splice(1, 0, effectiveNewCards.map((item) => item.priceOfNewCard));


                    const effectivePays = this.cardItemData && this.cardItemData.pays.filter((item) => item.totalPrice);
                    this.paysSeriesData = effectivePays.map((item) => item.totalPrice);
                    this.paysLegend = effectivePays.map((item) => item.name);
                    this.paysCategory = effectivePays.map((item) => item.name);
                } catch (error) {
                    console.log(error);
                    this.cardItemData = [];

                } finally {
                    this.cardLoading = false;
                }
            },

            async getDisCountData() {
                this.discountLoading = true;
                const params = this.getParams();
                try {
                    const { data } = this.showDiscountStatSection ?
                        await PromotionApi.situation.pharmacyDiscount(params) :
                        await PromotionApi.situation.discount(params);
                    this.discountData = data;
                    const chargeAmountData = new ChargeAmountModel(this.discountData.chargeList && this.discountData.chargeList);
                    this.chargeAmountData = chargeAmountData.data;
                    const chargePeopleData = new ChargePeopleModel(this.discountData.chargeList && this.discountData.chargeList);
                    this.chargePeopleData = chargePeopleData.data;
                } catch (error) {
                    console.log(error);
                    this.discountData = [];
                } finally {
                    this.discountLoading = false;
                }
            },

            async getSpecialData() {
                this.specialPriceLoading = true;
                const params = this.getParams();
                try {
                    const { data } = await PromotionApi.situation.pharmacySpecial(params);
                    this.specialData = data;
                    const chargeAmountData = new ChargeAmountModel(this.specialData.chargeList && this.specialData.chargeList);
                    this.specialAmountData = chargeAmountData.data;
                    const chargePeopleData = new ChargePeopleModel(this.specialData.chargeList && this.specialData.chargeList);
                    this.specialPeopleData = chargePeopleData.data;
                } catch (error) {
                    console.log(error);
                    this.specialData = [];
                } finally {
                    this.specialPriceLoading = false;
                }
            },
            async getGiftData() {
                this.giftLoading = true;
                const params = this.getParams();
                try {
                    const { data } = await PromotionApi.situation.gift(params);
                    this.giftData = data || [];
                    const giftTotalAmountData = new GiftTotalAmountModel(this.giftData.chargeList && this.giftData.chargeList.map((item) => {
                        return {
                            id: item.id,
                            name: item.name,
                            amount: item.amount,
                        };
                    }));
                    this.giftTotalAmountData = giftTotalAmountData.data;
                    const effectiveGiftChargeList = this.giftData?.chargeList?.filter((item) => item.avgAmount) || [];
                    this.giftCustomerPriceSeries = effectiveGiftChargeList.map((item) => item.avgAmount);
                    this.giftCustomerPriceLegend = effectiveGiftChargeList.map((item) => item.name);
                    this.giftCustomerPriceCategory = effectiveGiftChargeList.map((item) => item.name);
                } catch (error) {
                    console.log(error);
                    this.giftTotalAmountData = [];
                } finally {
                    this.giftLoading = false;
                }
            },
            async getPharmacyGiftData() {
                this.pharmacyGiftLoading = true;
                const params = this.getParams();
                try {
                    const { data } = await PromotionApi.situation.pharmacyGift(params);
                    this.pharmacyGiftData = data || [];
                    const giftTotalAmountData = new GiftTotalAmountModel(this.pharmacyGiftData.chargeList && this.pharmacyGiftData.chargeList.map((item) => {
                        return {
                            id: item.id,
                            name: item.name,
                            amount: item.amount,
                        };
                    }));
                    this.pharmacyGiftTotalAmountData = giftTotalAmountData.data;
                    const effectiveGiftChargeList = this.pharmacyGiftData?.chargeList?.filter((item) => item.avgAmount) || [];
                    this.pharmacyGiftCustomerPriceSeries = effectiveGiftChargeList.map((item) => item.avgAmount);
                    this.pharmacyGiftCustomerPriceLegend = effectiveGiftChargeList.map((item) => item.name);
                    this.pharmacyGiftCustomerPriceCategory = effectiveGiftChargeList.map((item) => item.name);
                } catch (error) {
                    console.log(error);
                    this.pharmacyGiftTotalAmountData = [];
                } finally {
                    this.pharmacyGiftLoading = false;
                }
            },
            async getCouponData() {
                this.couponLoading = true;
                const params = this.getParams();
                try {
                    const { data } = await PromotionApi.situation.coupon(params);
                    this.couponData = data || [];

                    const effectiveGotCouponAnalyseList = this.couponData?.gotCouponAnalyseList?.filter((item) => item.value);
                    const effectiveUsedCouponAnalyseList = this.couponData.usedCouponAnalyseList?.filter((item) => item.value);
                    this.couponAnalysisCategory = effectiveGotCouponAnalyseList.map((item) => item.name);
                    this.couponAnalysisCategory = this.couponAnalysisCategory && this.couponAnalysisCategory.concat(effectiveUsedCouponAnalyseList.map((item) => item.name));

                    this.couponAnalysisSeries = [effectiveGotCouponAnalyseList.map((item) => item.value)];
                    this.couponAnalysisSeries.splice(1, 0, effectiveUsedCouponAnalyseList.map((item) => item.value));


                    const couponTotalAmountData = new CouponTotalAmountModel(this.couponData.totalAmountList);
                    this.couponTotalAmountData = couponTotalAmountData.data;
                } catch (error) {
                    console.log(error);
                    this.couponData = [];

                } finally {
                    this.couponLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";

.statistics-module_promotion-situation {
    padding: 20px 20px 4px;
}
</style>
