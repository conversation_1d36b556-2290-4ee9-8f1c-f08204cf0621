<template>
    <abc-layout preset="page-table" class="common-padding-container">
        <abc-layout-header>
            <stat-toolbar
                ref="statToolbarRef"
                :clinic-id-filter.sync="params.clinicId"
                :patient-id-filter.sync="params.patientId"
                :enable-features="toolbarFeatures"
                :product-id-filter.sync="params.goodsId"
                :date-filter.sync="params.dateFilter$"
                :patient-width="125"
                is-card-stat
                :product-width="110"
                support-keyword-search
                :patient-placeholder="'全部持卡人'"
                :handle-export="handleExport"
                :export-task-type="exportTaskType"
                @change-date="handleDateChange"
                @change-clinic="handleClinicChange"
                @change-patient="handlePatientChange"
                @change-product="getTableData"
                @searchGoods="onSearchGoods"
            >
                <filter-select
                    slot="patient"
                    v-model="params.cardId"
                    :width="110"
                    placeholder="卡项"
                    :options="cardOptions"
                    :normalize-id="'card_id'"
                    :normalize-key="'card_name'"
                    @change="handleCardChange"
                >
                </filter-select>

                <filter-select
                    v-if="params.dimension === 'list' && !isSingleStore"
                    slot="patient"
                    key="source"
                    v-model="params.sourceId"
                    :width="100"
                    :options="sourceOptions"
                    :normalize-id="'source_id'"
                    :normalize-key="'source_name'"
                    placeholder="来源"
                    @change="handSourceChange"
                >
                </filter-select>
                <filter-select
                    v-if="showListTab"
                    slot="patient"
                    key="type"
                    v-model="params.cardDudectIsComplete"
                    :width="100"
                    :options="statusOptions"
                    placeholder="状态"
                    @change="getTableData"
                >
                </filter-select>
                <filter-select
                    v-if="showServiceTab"
                    slot="patient"
                    key="service"
                    v-model="params.sellerId"
                    :width="100"
                    :options="sellerOptions"
                    placeholder="操作人"
                    id-with-name
                    @change="getTableData"
                >
                </filter-select>
                <filter-select
                    v-if="showNotServiceTab"
                    slot="patient"
                    key="reveneue"
                    v-model="params.cardCreatedId"
                    :width="100"
                    :options="employeeOptions"
                    placeholder="开卡人"
                    @change="getTableData"
                >
                </filter-select>
                <filter-select
                    v-if="params.dimension === 'balance'"
                    slot="patient"
                    key="type"
                    v-model="params.chargeType"
                    :width="100"
                    :options="chargeTypeOptions"
                    :normalize-id="'charge_no'"
                    :normalize-key="'charge_type'"
                    placeholder="类型"
                    @change="handleChargeTypeChange"
                >
                </filter-select>
                <filter-select
                    v-if="params.dimension === 'service'"
                    slot="patient"
                    key="type"
                    v-model="params.chargeType"
                    :width="100"
                    :options="reductionChargeTypeOptions"
                    :normalize-id="'charge_no'"
                    :normalize-key="'charge_type'"
                    placeholder="类型"
                    @change="handleReductionChargeTypeChange"
                >
                </filter-select>
                <stat-dimension-picker
                    slot="right"
                    v-model="params.dimension"
                    :options="dimensionOptions"
                    @change="handleDimensionChange"
                ></stat-dimension-picker>
            </stat-toolbar>
            <div v-if="params.dimension === 'list'" style="margin-top: 16px;">
                <biz-data-statistics-card :list="tableKeyData">
                </biz-data-statistics-card>
            </div>
        </abc-layout-header>

        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :loading="loading"
                :data-list="tableData"
                :render-config="tableRenderHeader"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="pageParams"
                :count="totalCount"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li v-if="showServiceTab" v-html="totalInfo"></li>
                    <li v-else>
                        共 <span>{{ totalCount }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import StatDimensionPicker from 'views/statistics/common/stat-dimension-picker/stat-dimension-picker';
    import { mapGetters } from 'vuex';
    import PromotionAPI from 'views/statistics/core/api/promotion.js';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import TableUtilsMixin from 'views/statistics/mixins/table-utils-mixin';
    import { isEqual } from 'utils/lodash';
    import { resolveHeaderV2 } from 'views/statistics/utils';
    import { resolveHeader } from 'utils/table';
    import BizDataStatisticsCard from '@/components-composite/biz-data-statistics-card/src/views/index.vue';

    export default {
        components: {
            BizDataStatisticsCard,
            StatToolbar,
            StatDimensionPicker,
            FilterSelect,
        },
        mixins: [ClinicTypeJudger, PickerOptions, DateParamsMixins, TableUtilsMixin],
        data() {
            return {
                params: {
                    dimension: 'list',
                    cardId: '',
                    clinicId: '',
                    sourceId: '',
                    patientId: '',
                    chargeType: '',
                    cardDudectIsComplete: '',
                    sellerId: '',
                    cardCreatedId: '',
                    keyword: '',
                    goodsId: '',
                    pageIndex: 0,
                    pageSize: 12,
                },
                dimensionOptions: [
                    {
                        label: 'list', name: '持卡人',
                    },
                    {
                        label: 'balance', name: '余额流水',
                    },
                    {
                        label: 'service', name: '已抵扣项目',
                    },
                    {
                        label: 'not-service', name: '未抵扣项目',
                    },
                ],
                statusOptions: [
                    {
                        name: '未完结',
                        id: '0',
                    },
                    {
                        name: '已完结',
                        id: '1',
                    },
                ],
                loading: false,
                cardList: [],
                cardHeader: [],
                balanceList: [],
                balanceHeader: [],
                serviceList: [],
                serviceHeader: [],
                tableKeyData: [],
                tableHeader: [],
                tableData: [],
                totalCount: 0,
                cardOptions: [],
                sourceOptions: [],
                chargeTypeOptions: [],
                reductionChargeTypeOptions: [],
                exportTaskType: 'promotion-card',
                totalInfo: '',
                employeeOptions: [],
                sellerOptions: [], // 开单人选项

                ListTableConfig: {
                    'hasInnerBorder': true,
                    list: [
                        {
                            'label': '持卡人姓名',
                            'key': 'patientName',
                            'pinned': false,
                            'position': 1,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '120px','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                            },
                        },
                        {
                            'label': '开卡费',
                            'key': 'cardPatientCardFee',
                            'pinned': false,
                            'position': 2,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '100px','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                            },
                        },
                        {
                            'label': '手机号',
                            'key': 'patientMobile',
                            'pinned': false,
                            'position': 3,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '120px','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                        },
                        {
                            'label': '卡名称',
                            'key': 'cardName',
                            'pinned': false,
                            'position': 4,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '140px','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                            },
                        },
                        {
                            'label': '来源',
                            'key': 'source',
                            'pinned': false,
                            'position': 5,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '100px','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                            },
                        },
                        {
                            'label': '开卡人',
                            'key': 'cardCreatedName',
                            'pinned': false,
                            'position': 6,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '86px','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                            },
                        },
                        {
                            'label': '开卡时间',
                            'key': 'cardPatientCreated',
                            'pinned': false,
                            'position': 7,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '148px','maxWidth': '','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                        },
                        {
                            'label': '生效时间',
                            'key': 'cardPatientBeginDate',
                            'pinned': false,
                            'position': 8,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '148px','maxWidth': '','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                        },
                        {
                            'label': '有效期',
                            'key': 'cardValidityPeriod',
                            'pinned': false,
                            'position': 9,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '148px','maxWidth': '','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                        },
                        {
                            'label': '到期时间',
                            'key': 'cardPatientEndDate',
                            'pinned': false,
                            'position': 10,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '148px','maxWidth': '','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                        },
                        {
                            'label': '状态',
                            'key': 'cardDudectIsCompleteStr',
                            'pinned': false,
                            'position': 11,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '100px','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                        },
                        {
                            'label': '卡内剩余服务项目',
                            'key': 'cardSurplusService',
                            'pinned': false,
                            'position': 12,
                            'sortable': false,
                            'style': {
                                'flex': 'none','width': '248px','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                            },
                        },
                        {
                            'label': '期初余额',
                            'key': 'beginBalance',
                            'pinned': false,
                            'position': 13,
                            'sortable': false,
                            'style': {
                                'minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                            'children': [
                                {
                                    'label': '合计',
                                    'key': 'beginBalanceTotal',
                                    'pinned': false,
                                    'position': 13,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '本金',
                                    'key': 'beginBalancePrincipal',
                                    'pinned': false,
                                    'position': 14,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '赠金',
                                    'key': 'beginBalancePresent',
                                    'pinned': false,
                                    'position': 15,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                            ],
                        },
                        {
                            'label': '充值',
                            'key': 'recharge',
                            'pinned': false,
                            'position': 14,
                            'sortable': false,
                            'style': {
                                'minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                            'children': [
                                {
                                    'label': '合计',
                                    'key': 'rechargeTotal',
                                    'pinned': false,
                                    'position': 13,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '本金',
                                    'key': 'rechargePrincipal',
                                    'pinned': false,
                                    'position': 14,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '赠金',
                                    'key': 'rechargePresent',
                                    'pinned': false,
                                    'position': 15,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                            ],
                        },
                        {
                            'label': '消费',
                            'key': 'consume',
                            'pinned': false,
                            'position': 15,
                            'sortable': false,
                            'style': {
                                'minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                            'children': [
                                {
                                    'label': '合计',
                                    'key': 'consumeTotal',
                                    'pinned': false,
                                    'position': 16,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '本金',
                                    'key': 'consumePrincipal',
                                    'pinned': false,
                                    'position': 17,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '赠金',
                                    'key': 'consumePresent',
                                    'pinned': false,
                                    'position': 18,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                            ],
                        },
                        {
                            'label': '期末余额',
                            'key': 'endBalance',
                            'pinned': false,
                            'position': 16,
                            'sortable': false,
                            'style': {
                                'minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                            'children': [
                                {
                                    'label': '合计',
                                    'key': 'endBalanceTotal',
                                    'pinned': false,
                                    'position': 13,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '本金',
                                    'key': 'endBalancePrincipal',
                                    'pinned': false,
                                    'position': 14,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '赠金',
                                    'key': 'beginBalancePresent',
                                    'pinned': false,
                                    'position': 15,
                                    'sortable': false,
                                    'style': {
                                        'flex': 'none','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                            ],
                        },
                    ],
                },
            };
        },

        computed: {
            ...mapGetters(['isSingleStore', 'fullEmployeeList','enablePatientMobileInStatistics']),
            toolbarFeatures() {
                const features = [
                    StatToolbar.Feature.DATE,
                    StatToolbar.Feature.EXPORT,
                    StatToolbar.Feature.PATIENT];

                if (this.showServiceTab || this.showNotServiceTab) {
                    features.push(StatToolbar.Feature.PRODUCT);
                }

                if (!this.showListTab) {
                    features.push(StatToolbar.Feature.CLINIC);
                }
                return features;
            },
            listTabClinicId() {
                return this.isChainAdmin || this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },
            tableRenderHeader() {
                if (this.params.dimension === 'list') {
                    const config = resolveHeaderV2({
                        header: this.tableHeader,
                        staticConfig: this.ListTableConfig,
                    });
                    return {
                        hasHeaderBorder: true,
                        hasInnerBorder: true,
                        ...config,
                    };
                }
                const list = resolveHeader(
                    this.tableHeader,
                    this.renderTypeList,
                    true,
                );
                list.forEach((item) => {
                    item.children.forEach((child) => {
                        child.autoSort = child.sortable;
                    });
                    item.autoSort = item.sortable;
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list,
                };
            },
            renderTypeList() {
                return {
                    cardSurplusServiceRender: (h, row, col) => {
                        const showValue = row.goodsEntityList?.length;
                        return showValue ?
                            (
                            <AbcPopover
                                trigger="click"
                                z-index="10000"
                                popperClass="remind-card-number-popper"
                                placement="left-start"
                                theme="yellow"
                        >
                            <div class="remind-present-header">
                                <span class="remind-present-header-left">
                                    <span>项目名称</span>
                                </span>
                                <span class="remind-present-header-right">
                                    剩余数量
                                </span>
                            </div>
                            <div class="remind-present-content">
                                {row.goodsEntityList?.map((item) => (
                                    <div class="remind-present-item">
                                        <span class="remind-present-item-left">
                                            <span> {item.goodsName}</span>
                                        </span>
                                        <span class="remind-present-item-right">
                                            {item.remindNumber}
                                        </span>
                                    </div>
                                ))}
                            </div>
                            <div slot="reference">
                                <div class="cell">
                                    <span
                                        style="margin-left: 4px;color:#005ED9; cursor: pointer"
                                    >
                                        {row[col.prop]}
                                    </span>
                                </div>
                            </div>
                        </AbcPopover>) :
                            (<div class="cell">
                                    <span
                                        onClick={(e) => {
                                            e.stopPropagation();
                                        }}
                                        style="margin-left: 4px;"
                                    >
                                        {row[col.prop]}
                                    </span>
                                </div>);
                    },
                    equityTotalPricePopover: () => {
                        return (
                            <div>
                                <p>权益实扣金额=抵扣数量*卡内折后价</p>
                                <p>卡内折后价=（权益总价/ 卡内项目价值总和 ）*原价</p>
                                <abc-text theme="gray-light">*若卡项未设置权益总价，则不计算权益实扣金额</abc-text>
                            </div>);
                    },
                };
            },

            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },

            showListTab() {
                return this.params.dimension === 'list';
            },
            showBalanceTab() {
                return this.params.dimension === 'balance';
            },
            showServiceTab() {
                return this.params.dimension === 'service';
            },
            showNotServiceTab() {
                return this.params.dimension === 'not-service';
            },
            pageParams() {
                const {
                    pageIndex,
                    pageSize,
                } = this.params;
                return {
                    pageIndex,
                    pageSize: this.params.dimension === 'list' ? pageSize - 2 : pageSize + 1,
                };
            },
        },

        created() {
            this.exportService = new ExportService();
            this.fetchEmployeeList();
        },
        mounted() {
            this.getSelectOptions();
        },

        methods: {
            handleMounted(data) {
                this.params.pageSize = (data.paginationLimit) || 10;
                this.getTableData();
            },
            normalizeOptions(list = [], key = '') {
                const newList = list;
                return newList?.map((item) => {
                    return {
                        ...item,
                        name: item[key],
                    };
                });
            },
            onSearchGoods(keyword) {
                this.params.keyword = keyword;
                this.getTableData();
            },

            getTableParams() {
                const {
                    patientId,
                    pageIndex,
                    pageSize,
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                    cardId,
                    sellerId,
                    chargeType,
                    cardDudectIsComplete,
                    sourceId,
                    goodsId,
                    keyword,
                    cardCreatedId,
                } = this.params;

                const size = this.params.dimension === 'list' ? pageSize - 2 : pageSize + 1;
                const offset = pageIndex * size;

                const baseParams = {
                    cardId,
                    clinicId: this.queryClinicId,
                    sourceId,
                    patientId,
                    offset,
                    size,
                    beginDate,
                    endDate,
                    chargeType,
                    cardDudectIsComplete,
                    enablePatientMobile: this.enablePatientMobileInStatistics,
                };

                if (this.showListTab) {
                    return {
                        ...baseParams,
                        cardDudectIsComplete,
                        clinicId: this.listTabClinicId,
                    };
                }
                if (this.showBalanceTab) {
                    return baseParams;
                }
                if (this.showServiceTab) {
                    const employees = sellerId ? [
                        {
                            id: sellerId.split('-idWithName-')[0],
                            name: sellerId.split('-idWithName-')[1],
                        },
                    ] : [];
                    return {
                        ...baseParams,
                        goodsId,
                        keyword,
                        employees: JSON.stringify(employees),
                    };
                }
                if (this.showNotServiceTab) {
                    return {
                        ...baseParams,
                        goodsId,
                        keyword,
                        cardCreatedId,
                    };
                }
            },

            fetchEmployeeList() {
                const employeeList = this.fullEmployeeList;
                this.employeeOptions = employeeList.map((item) => ({
                    id: item.employeeId,
                    name: item.employeeName,
                }));
            },

            async getSelectOptions() {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                } = this.params;
                const {
                    data: {
                        card, source, chargeType, dudectChargeType, employees,
                    },
                } = await PromotionAPI.card.selectCondition({
                    clinicId: this.showListTab ? this.listTabClinicId : this.queryClinicId,
                    beginDate,
                    endDate,
                });
                this.cardOptions = this.normalizeOptions(card, 'card_name') || [];
                this.sourceOptions = this.normalizeOptions(source, 'source_name') || [];
                this.chargeTypeOptions = this.normalizeOptions(chargeType, 'charge_type') || [];
                this.reductionChargeTypeOptions = this.normalizeOptions(dudectChargeType, 'charge_type') || [];
                this.sellerOptions = employees || [];
            },
            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            handleDateChange() {
                this.getSelectOptions();
                this.getTableData();
            },

            handleClinicChange() {
                this.getSelectOptions();
                this.getTableData();
            },

            async handleExport() {
                const params = this.getTableParams();
                delete params.limit;
                delete params.offset;

                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        ...params,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            handleDimensionChange() {
                this.getSelectOptions();
                this.getTableData();
            },

            handleCardChange() {
                this.getTableData();
            },

            handSourceChange() {
                this.getTableData();
            },

            handleChargeTypeChange() {
                this.getTableData();
            },

            handleReductionChargeTypeChange() {
                this.getTableData();
            },

            handlePatientChange() {
                this.getTableData();
            },

            async getTableData(resetPageParams = true) {
                await this.$nextTick();
                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }

                const params = this.getTableParams();
                this.loading = true;
                if (this.showListTab) {
                    try {
                        const {
                            data,
                        } = await PromotionAPI.card.list({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.loading = false;
                        console.log(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }
                } else if (this.showBalanceTab) {
                    try {
                        const {
                            data,
                        } = await PromotionAPI.card.balancePayList({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.loading = false;
                        console.log(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }
                } else if (this.showServiceTab) {
                    try {
                        const {
                            data,
                        } = await PromotionAPI.card.serviceDeductionList({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.setTableData(true);
                        console.log(err);
                    } finally {
                        this.loading = false;
                    }
                } else {
                    try {
                        const {
                            data,
                        } = await PromotionAPI.card.notServiceDeductionList({
                            ...params,
                        });
                        if (isEqual(params, this.getTableParams())) {
                            this.setTableData(false, data, resetPageParams);
                        }
                    } catch (err) {
                        this.setTableData(true);
                        console.log(err);
                    } finally {
                        this.loading = false;
                    }
                }
            },
        },

    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.remind-card-number-popper {
    width: 350px;
    min-height: 160px;
    max-height: 180px;
    padding: 12px;
    overflow-x: hidden;
    overflow-y: auto;
    overflow-y: overlay;

    @include scrollBar;

    .remind-present-header {
        display: flex;
        justify-content: space-between;
        padding-bottom: 8px;
        color: $S1;
        border-bottom: 1px dashed $P6;

        .remind-present-header-left {
            span {
                margin-right: 24px;
            }
        }
    }

    .remind-present-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 8px;
        padding-bottom: 3px;
        color: $T2;

        .remind-present-item-left {
            display: flex;
            align-items: center;

            span {
                display: inline-block;
                width: 240px;
                margin-right: 12px;

                @include ellipsis;
            }
        }
    }
}
</style>
