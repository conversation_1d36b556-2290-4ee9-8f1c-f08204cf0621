<template>
    <abc-layout preset="page-table" class="common-padding-container">
        <abc-layout-header>
            <stat-toolbar
                ref="statToolbarRef"
                :enable-features="toolbarFeatures"
                :date-filter.sync="params.dateFilter$"
                :patient-id-filter.sync="params.patientId"
                :patient-placeholder="'搜索会员'"
                :clinic-id-filter.sync="params.clinicId"
                :clinic-list="clinicList"
                custom-clinic-employee
                :handle-export="handleExport"
                :export-task-type="exportTaskType"
                custom-clinic-placeholder="消费门店"
                @change-date="handleDateChange"
                @change-clinic="handleClinicChange"
                @change-patient="handlePatientChange"
            >
                <template v-if="params.dimension === 'list'">
                    <filter-select
                        slot="patient"
                        key="member-type"
                        v-model="params.memberId"
                        :width="130"
                        placeholder="等级"
                        :options="memberOptions"
                        :normalize-id="'memberTypeId'"
                        :normalize-key="'memberType'"
                        @change="handleMemberChange"
                    >
                    </filter-select>
                </template>
                <clinic-select
                    v-if="!isSingleStore"
                    slot="patient"
                    key="source"
                    v-model="params.sourceId"
                    width="150"
                    :is-custom-clinic="false"
                    placeholder="开卡门店"
                    @change="handSourceChange"
                >
                </clinic-select>
                <template v-if="params.dimension === 'record'">
                    <filter-select
                        v-if="
                            (isChainAdmin && params.clinicId) ||
                                isChainStore ||

                                isSingleStore
                        "
                        key="seller"
                        slot="customEmployee"
                        v-model="params.employee"
                        :width="130"
                        placeholder="销售员"
                        :options="employeeList"
                        @change="handleEmployeeChange"
                    >
                    </filter-select>
                    <filter-select
                        slot="patient"
                        key="member-type"
                        v-model="params.memberTypeId"
                        :width="130"
                        placeholder="会员等级"
                        :options="memberTypeList"
                        :normalize-id="'memberTypeId'"
                        :normalize-key="'memberType'"
                        @change="handleMemberTypeChange"
                    >
                    </filter-select>
                    <abc-cascader
                        slot="patient"
                        v-model="params.actionList"
                        :options="actionOptions"
                        :multiple="true"
                        placeholder="类型"
                        :width="130"
                        no-icon
                        clearable
                        @change="handleActionChange"
                    >
                    </abc-cascader>
                </template>
                <stat-dimension-picker
                    slot="right"
                    v-model="params.dimension"
                    :options="dimensionOptions"
                    @change="handleDimensionChange"
                ></stat-dimension-picker>
            </stat-toolbar>

            <div v-if="params.dimension === 'list' " style="margin-top: 16px;">
                <biz-data-statistics-card :list="tableBannerData">
                </biz-data-statistics-card>
            </div>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="tableFixed2Ref"
                :loading="loading"
                :data-list="tableData"
                :min-height="300"
                :render-config="renderTableHeader"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="params"
                :count="totalCount"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li v-if="totalInfo" v-html="totalInfo"></li>
                    <li v-else>
                        共 <span>{{ totalCount }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import StatDimensionPicker from 'views/statistics/common/stat-dimension-picker/stat-dimension-picker';
    import { mapGetters } from 'vuex';
    import PromotionAPI from 'views/statistics/core/api/promotion.js';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import TableUtilsMixin from 'views/statistics/mixins/table-utils-mixin';
    import { formatMoney } from 'src/filters/index';
    import { resolveHeaderV2 } from 'views/statistics/utils';
    import { resolveHeader } from 'utils/table';
    import BizDataStatisticsCard from '@/components-composite/biz-data-statistics-card/src/views/index.vue';
    import ClinicSelect from 'views/statistics/common/clinic-select/clinic-select';

    export default {
        components: {
            BizDataStatisticsCard,
            StatToolbar,
            StatDimensionPicker,
            FilterSelect,
            ClinicSelect,
        },
        mixins: [ClinicTypeJudger, PickerOptions, DateParamsMixins, TableUtilsMixin],
        data() {
            return {
                params: {
                    dimension: 'list',
                    memberId: '',
                    actionList: [],
                    employee: '',
                    memberTypeId: '',
                    patientId: '',
                    couponId: '',
                    sourceId: '',
                    pageIndex: 0,
                    pageSize: 12,
                },
                dimensionOptions: [
                    {
                        label: 'list', name: '会员列表',
                    },
                    {
                        label: 'record', name: '交易流水',
                    },
                ],
                loading: false,
                list: [],
                recordList: [],
                totalCount: 0,
                memberOptions: [],
                typeOptions: [],
                sourceOptions: [],
                actionOptions: [{
                    label: '充值',
                    value: '充值',
                    children: [
                        {
                            label: '充值消费',
                            value: '充值消费',
                        },
                        {
                            label: '退储蓄金',
                            value: '退储蓄金',
                        },
                    ],
                },{
                    label: '消费',
                    value: '消费',
                    children: [
                        {
                            label: '消费',
                            value: '消费',
                        },
                        {
                            label: '退费',
                            value: '退费',
                        },
                    ],
                }],
                memberClinicOptions: [],
                memberSummaryData: {},
                tableHeader: [],
                tableData: [],
                memberTypeList: [],
                ListTableConfig: {
                    'hasInnerBorder': true,
                    list: [
                        {
                            'label': '会员姓名',
                            'key': 'patientName',
                            'pinned': false,
                            'position': 1,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                            },
                        },
                        {
                            'label': '手机号',
                            'key': 'patientMobile',
                            'pinned': false,
                            'position': 2,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                        },
                        {
                            'label': '会员等级',
                            'key': 'memberType',
                            'pinned': false,
                            'position': 3,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                            },
                        },
                        {
                            'label': '开卡门店',
                            'key': 'clinicSource',
                            'pinned': false,
                            'position': 4,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '','maxWidth': '','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                            },
                        },
                        {
                            'label': '开卡日期',
                            'key': 'memberRegDate',
                            'pinned': false,
                            'position': 5,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '','maxWidth': '','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                        },
                        {
                            'label': '期初',
                            'key': 'begin',
                            'pinned': false,
                            'position': 6,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '300px','maxWidth': '300px','minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                            'children': [
                                {
                                    'label': '合计',
                                    'key': 'beginTotal',
                                    'pinned': false,
                                    'position': 6,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '本金',
                                    'key': 'beginPrincipal',
                                    'pinned': false,
                                    'position': 7,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '赠金',
                                    'key': 'beginPresent',
                                    'pinned': false,
                                    'position': 8,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                            ],
                        },
                        {
                            'label': '充值',
                            'key': 'memberCharge',
                            'pinned': false,
                            'position': 9,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '300px','maxWidth': '300px','minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                            'children': [
                                {
                                    'label': '合计',
                                    'key': 'memberChargeTotal',
                                    'pinned': false,
                                    'position': 9,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '本金',
                                    'key': 'memberChargePrincipal',
                                    'pinned': false,
                                    'position': 10,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '赠金',
                                    'key': 'memberChargePresent',
                                    'pinned': false,
                                    'position': 11,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                            ],
                        },
                        {
                            'label': '消费',
                            'key': 'charge',
                            'pinned': false,
                            'position': 12,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '300px','maxWidth': '300px','minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                            'children': [
                                {
                                    'label': '合计',
                                    'key': 'chargeTotal',
                                    'pinned': false,
                                    'position': 12,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '本金',
                                    'key': 'chargePrincipal',
                                    'pinned': false,
                                    'position': 13,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '赠金',
                                    'key': 'chargePresent',
                                    'pinned': false,
                                    'position': 14,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                            ],
                        },
                        {
                            'label': '期末',
                            'key': 'end',
                            'pinned': false,
                            'position': 15,
                            'sortable': false,
                            'style': {
                                'flex': 1,'width': '300px','maxWidth': '300px','minWidth': '300px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                            },
                            'children': [
                                {
                                    'label': '合计',
                                    'key': 'endTotal',
                                    'pinned': false,
                                    'position': 15,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '本金',
                                    'key': 'endPrincipal',
                                    'pinned': false,
                                    'position': 16,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                                {
                                    'label': '赠金',
                                    'key': 'endPresent',
                                    'pinned': false,
                                    'position': 17,
                                    'sortable': false,
                                    'style': {
                                        'flex': 1,'width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                                    },
                                },
                            ],
                        },
                    ],
                },
            };
        },

        computed: {
            ...mapGetters(['isSingleStore', 'currentClinic', 'isChainSubStore','enablePatientMobileInStatistics']),
            renderTableHeader() {
                if (this.params.dimension === 'list') {
                    const config = resolveHeaderV2({
                        header: this.tableHeader,
                        staticConfig: this.ListTableConfig,
                    });
                    return {
                        hasHeaderBorder: true,
                        hasInnerBorder: true,
                        ...config,
                    };
                }
                const list = resolveHeader(
                    this.tableHeader,
                    {},
                    true,
                );
                list.forEach((item) => {
                    item.children.forEach((child) => {
                        child.autoSort = child.sortable;
                    });
                    item.autoSort = item.sortable;
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list,
                };
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },

            tableBannerData() {
                const {
                    newMemberCount, totalReceivedFee, consumptionPersonTimeCount, consumptionPerPersonTimeCount, totalMemberChargePrincipalAndPresent, totalEndPrincipalAndPresent,
                } = this.memberSummaryData;

                const options = [
                    {
                        text: '新增会员',
                        value: newMemberCount || 0,
                        tips: '',
                    },
                    {
                        text: '消费总金额',
                        value: formatMoney(totalReceivedFee || 0),
                        tips: '会员余额消费的总金额',
                        width: 210,
                    },
                    {
                        text: '消费人次',
                        value: consumptionPersonTimeCount || 0,
                        tips: '',
                    },
                    {
                        text: '客单价',
                        value: consumptionPerPersonTimeCount || '0.00',
                        tips: '',
                    },
                    {
                        text: '充值金额',
                        value: formatMoney(totalMemberChargePrincipalAndPresent || 0),
                        tips: '',
                    },
                    {
                        text: '会员余额',
                        value: formatMoney(totalEndPrincipalAndPresent || 0),
                        tips: '',
                        hidden: this.isChainSubStore,
                    },
                ];

                return options.filter((item) => !item.hidden);
            },
            toolbarFeatures() {
                const features = [StatToolbar.Feature.DATE, StatToolbar.Feature.EXPORT, StatToolbar.Feature.PATIENT];
                if (this.params.dimension === 'record') {
                    features.push(StatToolbar.Feature.CLINIC);
                }
                return features;
            },

            clinicList() {
                const list = this.memberClinicOptions
                    .map((clinic) => {
                        return {
                            ...clinic,
                            id: clinic.clinicId,
                            name: clinic.clinicName,
                            shortName: this.chainId === clinic.clinicId ? '总部' : clinic.shortName,
                        };
                    })
                    .filter((clinic) => clinic.clinicName !== '总部');
                return list;
            },
            employeeList() {
                let list = [];
                let currentClinicId = this.params.clinicId;
                if (this.isSingleStore || this.isChainSubStore) {
                    currentClinicId = this.clinicId;
                }
                if (!currentClinicId) {
                    list = [];
                }
                const clinicEmployee = this.clinicList.find((clinic) => clinic.id === currentClinicId);
                if (clinicEmployee) {
                    list = clinicEmployee.employees?.map((item) => {
                        return {
                            ...item,
                            name: item?.sellerUserName,
                            id: item?.sellerUserId,
                        };
                    });
                }

                return list;
            },


            exportTaskType() {
                const { dimension } = this.params;
                return dimension === 'list' ? 'promotion-member-list' : 'promotion-member-fee-list';
            },
        },

        mounted() {
            const { dimension } = this.params;
            if (dimension === 'list') {
                this.getListSelectOptions();
                this.getMemberKeyData();
            } else {
                this.getMemberClinicsOptions();
            }
            this.exportService = new ExportService();
        },
        beforeDestroy() {
            this.exportService.destroy();
        },
        methods: {
            handleMounted(data) {
                this.params.pageSize = data.paginationLimit - 2;
                this.getTableData();
            },
            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },
            handleEmployeeChange() {
                this.getTableData();
                this.getMemberKeyData();
            },
            handleMemberTypeChange() {
                this.getTableData();
            },

            handleDateChange() {
                const { dimension } = this.params;
                if (dimension === 'list') {
                    this.getListSelectOptions();
                    this.getMemberKeyData();
                } else {
                    this.getMemberClinicsOptions();
                }
                this.getTableData();
            },

            handleClinicChange() {
                this.getTableData();
                const { dimension } = this.params;
                if (dimension === 'list') {
                    this.getListSelectOptions();
                } else {
                    this.getMemberClinicsOptions();
                }
            },

            handleTypeChange() {
                this.getTableData();
                this.getMemberKeyData();
            },

            handleMemberChange() {
                this.getTableData();
                this.getMemberKeyData();
            },

            handSourceChange() {
                this.getTableData();
                this.getMemberKeyData();
            },

            handleActionChange() {
                this.getTableData();
            },

            async handleExport() {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    }, sourceId, memberId, actionList, employee, dimension, patientId, memberTypeId,
                } = this.params;

                const params = {
                    beginDate: `${beginDate} 00:00:00`,
                    endDate: `${endDate} 23:59:59`,
                    pageindex: 0,
                    pagesize: 0,

                    memberTypeId: memberId,
                    patientId,
                    enablePatientMobile: this.enablePatientMobileInStatistics,
                    memberCreateClinicId: sourceId,
                };

                if (dimension === 'record') {
                    const actionsSet = new Set();
                    actionList.forEach((item) => {
                        // 只取最后一个action
                        const itemAction = item.slice(-1)?.[0]?.value;
                        actionsSet.add(itemAction);
                    });
                    params.actionList = [...actionsSet];
                    params.seller = employee;
                    params.clinicId = this.queryClinicId;
                    params.memberTypeId = memberTypeId;
                }
                try {
                    await this.exportService.startExport(this.exportTaskType, params);
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },
            handleDimensionChange() {
                const { dimension } = this.params;
                this.params.memberTypeId = '';
                if (dimension === 'list') {
                    this.params.pageSize -= 2;
                    this.getMemberKeyData();
                    this.getListSelectOptions();
                } else {
                    this.params.pageSize += 2;
                    this.getMemberClinicsOptions();
                }
                this.getTableData();
            },

            handlePatientChange() {
                this.getTableData();
            },
            // 会员列表下拉选项
            async getListSelectOptions() {
                try {
                    const {
                        dateFilter$: {
                            begin: beginDate, end: endDate,
                        },
                    } = this.params;
                    const {
                        data: {
                            memberType = [],
                        },
                    } = await PromotionAPI.member.listSelect({
                        begindate: `${beginDate} 00:00:00`,
                        enddate: `${endDate} 23:59:59`,
                        viewType: 'memberList',
                    });
                    this.memberOptions = memberType?.map((item) => {
                        return {
                            ...item,
                            name: item?.memberType,
                        };
                    });
                } catch (err) {
                    console.error(err);
                    this.memberOptions = [];
                }
            },
            async getMemberClinicsOptions() {
                try {
                    const {
                        dateFilter$: {
                            begin: beginDate, end: endDate,
                        },
                    } = this.params;
                    const {
                        data: {
                            memberClinics = [], memberTypeList = [],
                        },
                    } = await PromotionAPI.member.recordSelect({
                        begindate: `${beginDate} 00:00:00`,
                        enddate: `${endDate} 23:59:59`,
                        viewType: 'memberFlow',
                    });

                    this.memberClinicOptions = memberClinics;
                    this.memberTypeList = memberTypeList || [];
                } catch (err) {
                    console.error(err);
                    this.memberClinicOptions = [];
                    this.memberTypeList = [];
                }
            },

            async getMemberKeyData() {
                const {
                    patientId, dateFilter$: {
                        begin: beginDate, end: endDate,
                    }, sourceId, memberId,
                } = this.params;

                const res = await PromotionAPI.member.summary({
                    begindate: `${beginDate} 00:00:00`,
                    enddate: `${endDate} 23:59:59`,
                    patientId,
                    memberTypeId: memberId,
                    memberCreateClinicId: sourceId,
                });

                this.memberSummaryData = res.data || {};

            },
            async getTableData(resetPageParams = true) {
                await this.$nextTick();
                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }
                const {
                    patientId, pageIndex, pageSize, dimension, dateFilter$: {
                        begin: beginDate, end: endDate,
                    }, sourceId, memberId, actionList, employee,memberTypeId,
                } = this.params;
                const actionsSet = new Set();
                actionList.forEach((item) => {
                    // 只取最后一个action
                    const itemAction = item.slice(-1)?.[0]?.value;
                    actionsSet.add(itemAction);
                });
                const _actionList = [...actionsSet];
                try {
                    this.loading = true;
                    if (dimension === 'list') {
                        const {
                            data,
                        } = await PromotionAPI.member.list({
                            patientId,
                            pageindex: pageIndex,
                            pagesize: pageSize,
                            begindate: `${beginDate} 00:00:00`,
                            enddate: `${endDate} 23:59:59`,
                            memberTypeId: memberId,
                            clinicId: this.queryClinicId,
                            enablePatientMobile: this.enablePatientMobileInStatistics,
                            memberCreateClinicId: sourceId,
                        });
                        this.setTableData(false, data, resetPageParams);
                    } else {
                        const {
                            data,
                        } = await PromotionAPI.member.record({
                            pageindex: pageIndex,
                            pagesize: pageSize,
                            begindate: `${beginDate} 00:00:00`,
                            enddate: `${endDate} 23:59:59`,
                            actionList: _actionList,
                            seller: employee,
                            clinicId: this.queryClinicId,
                            patientId,
                            memberTypeId,
                            enablePatientMobile: this.enablePatientMobileInStatistics,
                            memberCreateClinicId: sourceId,
                        });
                        this.setTableData(false, data, resetPageParams);
                    }
                } catch (err) {
                    console.log(err);
                    this.setTableData(true);
                } finally {
                    this.loading = false;
                }

            },
        },

    };
</script>

