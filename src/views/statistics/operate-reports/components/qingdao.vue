<template>
    <div>
        <table class="report-table" style="margin-top: 20px;">
            <tbody>
                <tr>
                    <td colspan="6" class="title-td">
                        收费方式
                    </td>
                </tr>
                <tr v-for="(tr, trIndex) in payModes" :key="trIndex">
                    <template v-for="(td, tdIndex) in tr">
                        <td :key="tdIndex + td.name" colspan="1">
                            {{ td.name }}
                        </td>
                        <td
                            :key="tdIndex + td.name + td.value"
                            :colspan="tdIndex === tr.length - 1 && trIndex === payModes.length - 1 ? lastTdColdSpan : 1"
                            :title="td.value || 0"
                        >
                            {{ td.value || 0 }}
                        </td>
                    </template>
                </tr>
            </tbody>
        </table>

        <table class="report-table" style="margin-top: 20px;">
            <tbody>
                <tr>
                    <td colspan="6" class="title-td">
                        医保
                    </td>
                </tr>
                <tr>
                    <td colspan="1" rowspan="2">
                        普通门诊
                    </td>
                    <td colspan="1">
                        医保支付
                    </td>
                    <td colspan="1" :title="extraInfo.normalOutpatient.receivedFee || 0">
                        {{ extraInfo.normalOutpatient.receivedFee || 0 }}
                    </td>
                    <td colspan="1" rowspan="2">
                        大病门诊
                    </td>
                    <td colspan="1">
                        医保支付
                    </td>
                    <td colspan="1" :title="extraInfo.seriousIllnessOutpatient.receivedFee || 0">
                        {{ extraInfo.seriousIllnessOutpatient.receivedFee || 0 }}
                    </td>
                </tr>
                <tr>
                    <td colspan="1">
                        个人负担
                    </td>
                    <td colspan="1" :title="extraInfo.normalOutpatient.personalBurden || 0">
                        {{ extraInfo.normalOutpatient.personalBurden || 0 }}
                    </td>
                    <td colspan="1">
                        个人负担
                    </td>
                    <td colspan="1" :title="extraInfo.seriousIllnessOutpatient.personalBurden || 0">
                        {{ extraInfo.seriousIllnessOutpatient.personalBurden || 0 }}
                    </td>
                </tr>
                <tr>
                    <td colspan="1" rowspan="2">
                        长期护理
                    </td>
                    <td colspan="1">
                        医保支付
                    </td>
                    <td colspan="1" :title="extraInfo.longCare.receivedFee || 0">
                        {{ extraInfo.longCare.receivedFee || 0 }}
                    </td>
                    <td colspan="1"></td>
                    <td colspan="1"></td>
                    <td colspan="1"></td>
                </tr>
                <tr>
                    <td colspan="1">
                        个人负担
                    </td>
                    <td colspan="1" :title="extraInfo.longCare.personalBurden || 0">
                        {{ extraInfo.longCare.personalBurden || 0 }}
                    </td>
                    <td colspan="1"></td>
                    <td colspan="1"></td>
                    <td colspan="1"></td>
                </tr>
            </tbody>
        </table>
    </div>
</template>

<script>
    export default {
        name: 'Qingdao',
        props: {
            payMode: {
                type: Array,
                default: () => [],
            },
            extraInfo: {
                type: Object,
                default: () => {
                    return {};
                },
            },
        },
        data() {
            return {
                payModes: [],
                lastColSpan: 1,
                lastTdColdSpan: 1,
            };
        },
        watch: {
            payMode: {
                handler(val) {
                    this.payModes = this.divideBy(val, 3);
                    this.lastColSpan = val.length % 3;
                    if (this.lastColSpan === 1) {
                        this.lastTdColdSpan = 5;
                    } else if (this.lastColSpan === 2) {
                        this.lastTdColdSpan = 3;
                    } else {
                        this.lastTdColdSpan = 1;
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        methods: {
            divideBy(arr, count) {
                const result = [];
                let tmp = [];
                for (let i = 0, ii = arr.length; i < ii; i++) {
                    if (tmp.length === count) {
                        result.push(tmp);
                        tmp = [];
                    }
                    tmp.push(arr[i]);

                    if (i === ii - 1 && tmp.length > 0) {
                        result.push(tmp);
                    }
                }
                return result;
            },
        },
    };
</script>
