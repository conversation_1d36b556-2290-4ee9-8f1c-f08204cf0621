<template>
    <div style="padding: 16px;">
        <stat-toolbar
            :enable-features="toolbarFeatures"
            :export-task-type="exportTaskType"
            :handle-export="handleExport"
        >
            <abc-date-time-range-picker
                v-model="fetchParams.dateRange"
                :picker-options="pickerOptionsWithTime"
                :clearable="false"
                @change="handleDateChange"
            >
            </abc-date-time-range-picker>

            <clinic-select
                v-if="isChainAdmin"
                slot="custom-clinic"
                v-model="fetchParams.clinicId"
                placeholder="门店"
                :clearable="false"
                :clinic-list="clinicList"
                @change="handleClinicChange"
            >
            </clinic-select>
            <filter-select
                v-model="fetchParams.sellerId"
                clearable
                :width="75"
                placeholder="收费员"
                :options="cashierList"
                @change="handleEmployeeChange"
            >
            </filter-select>

            <template #right>
                <abc-button
                    variant="ghost"
                    :loading="loading"
                    @click="print"
                >
                    打印
                </abc-button>
            </template>
        </stat-toolbar>

        <div class="operate-cashier-report-wrapper">
            <cashier-table
                v-abc-loading="loading"
                :daily-data="dailyData"
                :pay-modes="payModes"
                :fee-type="feeTypes"
                :daily-report-time="fetchParams"
                :national-medical-insurance="nationalMedicalInsurance"
                :report-title="reportTitle"
            >
            </cashier-table>
        </div>
    </div>
</template>

<script>
    import operateReportsApi from 'views/statistics/core/api/operate-reports.js';

    import fecha from 'utils/fecha.js';
    import PickerOptions from 'views/common/pickerOptions.js';
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';

    import CashierTable from './components/cashier-table.vue';
    import { ABCPrintConfigKeyMap } from '@/printer/constants.js';
    import AbcPrinter from '@/printer/index.js';
    import FilterSelect from 'views/layout/filter-select/index.vue';
    import ClinicSelect from 'views/statistics/common/clinic-select/clinic-select';
    import { mapGetters } from 'vuex';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import RevenueAPI from '@/views/statistics/core/api/revenue.js';
    import { isEqual } from 'utils/lodash';


    const summaryInfo = Object.freeze({
        chainId: '',
        clinicId: '',
        totalAmount: 0,
        totalMedicalInsurance: 0,
        totalPayCash: 0,
    });

    const invoiceInfo = Object.freeze({
        chainId: '',
        clinicId: '',
        outpatientCount: 0,
        outpatientFirstNumber: 0,
        outpatientInvalidAmount: 0,
        outpatientInvalidCount: 0,
        outpatientInvalidNumber: 0,
        outpatientLastNumber: 0,
        registrationCount: 0,
        registrationFirstNumber: 0,
        registrationInvalidAmount: 0,
        registrationInvalidCount: 0,
        registrationInvalidNumber: 0,
        registrationLastNumber: 0,
    });

    export default {
        name: 'CashierReport',
        components: {
            CashierTable,
            FilterSelect,
            ClinicSelect,
            StatToolbar,
        },
        mixins: [PickerOptions, ClinicTypeJudger],
        data() {
            const start = fecha.format(new Date().setHours(0,0), 'YYYY-MM-DD HH:mm');
            const end = fecha.format(new Date().setHours(23,59), 'YYYY-MM-DD HH:mm');
            return {
                currentClinicName: '',
                clinicList: [],
                fetchParams: {
                    dateRange: [start,end],
                    clinicId: '',
                    sellerId: '',
                },
                //收费方式
                payModes: [],
                // 费用分类
                feeTypes: [],
                // 国标地区医保
                nationalMedicalInsurance: [],
                dailyData: {
                    //表头汇总金额
                    summary: {
                        ...summaryInfo,
                    },
                    //发票统计
                    invoice: {
                        ...invoiceInfo,
                    },

                    medicalInsurance: {
                        generalClinic: {},
                        specifiedDiseaseClinic: {},
                        provinceMedicalInsuranceClinic: {},
                        selfFundPatients: 0,
                        provinceOutMedicalInsuranceClinic: {},
                        provinceInnerMedicalInsuranceClinic: {},
                    }, // 就诊类型

                    //预算指标
                    budgetIndex: {
                        //诊所整体指标
                        revenueChargedDailyChineseDoseEntity: {
                            chineseDoseCount: 0,
                            chineseDosePriceAvg: 0,
                        },
                        //医保
                        shebaoStatSummaryReportRsp: {
                            extraInfo: {
                                // 预算指标
                                budgetIndicator: {
                                    chineseDoseCount: 0,
                                    chineseDosePriceAvg: 0,
                                    personCount: 0,
                                    personTimes: 0,
                                    personTimesCountRate: 0,
                                    socialPaymentFee: 0,
                                },

                                // 青岛医保
                                longCare: {}, // 长期护理
                                normalOutpatient: {}, // 普通门诊
                                seriousIllnessOutpatient: {}, // 大病门诊
                            },
                        },
                    },

                    tabulationTime: fecha.format(new Date(), 'YYYY-MM-DD hh:mm:ss'), //制表时间
                    cashierName: '全部', // 收费员
                },
                exportTaskType: 'daily-monthly-reports-export',
                cashierList: [],

                loading: true,
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'subClinics']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            cashierDailyReportTableName() {
                return this.viewDistributeConfig.Hospital.cashierDailyReportTableName;
            },
            reportTitle() {
                let title = this.currentClinicName;
                title += `${this.cashierDailyReportTableName}`;
                return title;
            },
            toolbarFeatures() {
                return [StatToolbar.Feature.EXPORT];
            },
            calcParams() {
                const {
                    dateRange, clinicId, sellerId,
                } = this.fetchParams;

                const params = {
                    clinicId,
                    sellerId,
                    beginDate: dateRange[0],
                    endDate: dateRange[1],
                };

                return params;
            },
        },
        async created() {
            await this.$store.dispatch('fetchChainSubClinics');
            this.clinicList = this.subClinics.filter((item) => !item.chainAdmin);
            this.fetchParams.clinicId = this.isChainAdmin ? this.clinicList?.[0].id : this.clinicId;
            this.currentClinicName = this.isChainAdmin ? this.clinicList[0]?.shortName || this.clinicList[0].name : this.currentClinic.clinicName;
            this.fetchCashierOptions();
            this.fetchData();
            this.exportService = new ExportService();
            this.$abcEventBus.$on('amount-setting-success', () => this.fetchData(), this);
        },
        beforeDestroy() {
            this.exportService?.destroy();
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            async handleExport() {
                const params = this.calcParams;
                const {
                    isEnableReimbursement, // 外诊报销
                    isEnableOperateReportMedicalInsurance, // 就诊类型
                    isEnableOperateReportBudgetTarget,// 预算指标
                } = this.$abcSocialSecurity.config || {};
                params.clinicName = this.currentClinicName;
                params.isShowOutpatient = isEnableReimbursement ? 1 : 0;
                const commonInterfaceSequence = [
                    {
                        name: 'summary',
                        sort: 1,
                    },
                    {
                        name: 'feeTypeList',
                        sort: 2,
                    },
                    {
                        name: 'payModeList',
                        sort: 3,
                    },
                    {
                        name: 'medicalInsuranceChifeng',
                        sort: 4,
                    },
                    {
                        name: 'invoice',
                        sort: 5,
                    },
                ];

                if (isEnableOperateReportMedicalInsurance) {
                    commonInterfaceSequence[2].name = 'medicalInsuranceHangzhou';
                }
                if (isEnableOperateReportBudgetTarget) {
                    commonInterfaceSequence[3].name = 'budgetIndexList';
                }

                params.interfaceSequence = commonInterfaceSequence;

                let region = '';
                if (this.$abcSocialSecurity.config?.isNeimenggu) {
                    region = 'neimenggu';
                }
                if (this.$abcSocialSecurity.config?.isShandongQingdao) {
                    region = 'qingdao';
                }
                if (this.$abcSocialSecurity.config?.isZhejiangHangzhou) {
                    region = 'hangzhou';
                }

                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        ...params,
                        region,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            handleEmployeeChange() {
                this.dailyData.cashierName = this.cashierList.find((item) => item.id === this.fetchParams.sellerId)?.name || '全部';
                this.fetchData();
            },
            async fetchCashierOptions() {
                const {
                    beginDate,
                    endDate,
                    clinicId,
                } = this.calcParams;
                try {
                    const { data } = await RevenueAPI.getCashierSelection({
                        beginDate,
                        endDate,
                        clinicId,
                    });
                    this.cashierList = data || [];
                } catch (e) {
                    console.log(e);
                    this.cashierList = [];
                }
            },
            handleDateChange() {
                this.fetchCashierOptions();
                this.fetchData();
            },

            handleClinicChange() {
                const clinic = this.clinicList.find((item) => item.id === this.fetchParams.clinicId);
                if (clinic) {
                    this.currentClinicName = clinic?.shortName || clinic.name;
                }
                this.fetchCashierOptions();
                this.fetchData();
            },

            async fetchData() {
                try {
                    this.loading = true;

                    await Promise.all([
                        // 表头汇总金额
                        this.getDailySummary(),
                        // 发票统计日报
                        this.getDailyInvoice(),
                        // 费用分类日报
                        this.getDailyFeeType(),
                        // 预算指标日报、青岛医保相关（普通门诊、大病门诊、长期护理）
                        this.getDailyBudgetIndex(),
                    ]);

                    // 山东青岛 | 内蒙古赤峰 | 呼和浩特
                    const {
                        isEnableOperateReportMedicalInsurance = false,
                        isShandongQingdao,
                        isZhejiangHangzhou,
                    } = this.$abcSocialSecurity.config || {};

                    // 杭州
                    if (isEnableOperateReportMedicalInsurance) {
                        // 就诊类型日报
                        this.getDailyMedicalInsurance();
                    }

                    // 赤峰 || 呼和浩特 医保模块 国标地区都可以展示
                    if (!isShandongQingdao && !isZhejiangHangzhou) {
                        this.getNationalMedicalInsurance();
                    }
                    // 收费方式日报
                    this.getDailyPayMode();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            async getDailySummary() {
                const tableParams = this.calcParams;
                const { data = {} } = await operateReportsApi.getDailySummary(tableParams);
                if (isEqual(tableParams, this.calcParams)) {
                    this.dailyData.summary = Object.assign(this.dailyData.summary, data);
                }
            },

            async getDailyInvoice() {
                const tableParams = this.calcParams;
                const { data = {} } = await operateReportsApi.getDailyInvoice(tableParams);
                if (isEqual(tableParams, this.calcParams)) {
                    this.dailyData.invoice = Object.assign(this.dailyData.invoice, data);
                }
            },

            async getDailyFeeType() {
                const tableParams = this.calcParams;
                const { data = {} } = await operateReportsApi.getDailyFeeType(tableParams);
                if (isEqual(tableParams, this.calcParams)) {
                    this.feeTypes = data;
                }
            },

            getDailyPayModeParams() {
                const { isEnableReimbursement = false } = this.$abcSocialSecurity.config || {};
                return {
                    ...this.calcParams,
                    isShowOutpatient: isEnableReimbursement ? 1 : 0,
                };
            },

            async getDailyPayMode() {
                const tableParams = this.getDailyPayModeParams();
                const { data } = await operateReportsApi.getDailyPayMode(tableParams);
                if (isEqual(tableParams, this.getDailyPayModeParams())) {
                    this.payModes = data;
                }
            },


            async getDailyMedicalInsurance() {
                const tableParams = this.calcParams;
                const { data = {} } = await operateReportsApi.getDailyMedicalInsurance(tableParams);

                if (isEqual(tableParams, this.calcParams)) {
                    this.dailyData.medicalInsurance.generalClinic = data.generalClinic || {};
                    this.dailyData.medicalInsurance.specifiedDiseaseClinic = data.specifiedDiseaseClinic || {};
                    this.dailyData.medicalInsurance.provinceMedicalInsuranceClinic = data.provinceMedicalInsuranceClinic || {};
                    this.dailyData.medicalInsurance.selfFundPatients = data.selfFundPatients || 0;
                    this.dailyData.medicalInsurance.provinceOutMedicalInsuranceClinic = data.provinceOutMedicalInsuranceClinic || {};
                    this.dailyData.medicalInsurance.provinceInnerMedicalInsuranceClinic = data.provinceInnerMedicalInsuranceClinic || {};
                }
            },

            getNationalMedicalInsuranceParams() {
                return {
                    ...this.calcParams,
                    region: this.$abcSocialSecurity.config?.isNeimenggu ? 'neimenggu' : '',
                };
            },

            async getNationalMedicalInsurance() {
                try {
                    const tableParams = this.getNationalMedicalInsuranceParams();
                    const { data } = await operateReportsApi.getNationalStandardMedicalInsurance(tableParams);
                    if (isEqual(tableParams, this.getNationalMedicalInsuranceParams())) {
                        if (data) {
                            const {
                                revenueVoList = [], revenueSheBaoVoList = [],
                            } = data;
                            this.nationalMedicalInsurance = this.$abcSocialSecurity.config?.isNeimenggu ? revenueVoList : revenueSheBaoVoList;
                        } else {
                            this.nationalMedicalInsurance = [];
                        }
                    }
                } catch (error) {
                    console.error(error);
                }
            },

            async getDailyBudgetIndex() {
                const tableParams = this.calcParams;
                const { data = {} } = await operateReportsApi.getDailyBudgetIndex(tableParams);
                if (isEqual(tableParams, this.calcParams)) {
                    const {
                        revenueChargedDailyChineseDoseEntity = {},
                        shebaoStatSummaryReportRsp = {},
                    } = data;
                    // 诊所整体指标
                    this.dailyData.budgetIndex.revenueChargedDailyChineseDoseEntity.chineseDoseCount = revenueChargedDailyChineseDoseEntity.chineseDoseCount || 0;
                    this.dailyData.budgetIndex.revenueChargedDailyChineseDoseEntity.chineseDosePriceAvg = revenueChargedDailyChineseDoseEntity.chineseDosePriceAvg || 0;

                    // 市医保  预算指标
                    const budgetIndicator = shebaoStatSummaryReportRsp?.extraInfo?.budgetIndicator || {};
                    if (budgetIndicator && this.$abcSocialSecurity.config.isZhejiangHangzhou) {
                        this.dailyData.budgetIndex.shebaoStatSummaryReportRsp.extraInfo.budgetIndicator =
                            Object.assign(this.dailyData.budgetIndex.shebaoStatSummaryReportRsp.extraInfo.budgetIndicator, budgetIndicator);
                    }

                    if (this.$abcSocialSecurity.config.isShandongQingdao) {
                        const {
                            normalOutpatient = {},
                            seriousIllnessOutpatient = {},
                            longCare = {},
                        } = shebaoStatSummaryReportRsp.extraInfo || {};

                        this.dailyData.budgetIndex.shebaoStatSummaryReportRsp.extraInfo.normalOutpatient = normalOutpatient;
                        this.dailyData.budgetIndex.shebaoStatSummaryReportRsp.extraInfo.seriousIllnessOutpatient = seriousIllnessOutpatient;
                        this.dailyData.budgetIndex.shebaoStatSummaryReportRsp.extraInfo.longCare = longCare;
                    }
                }
            },

            print() {
                const data = {
                    clinicName: this.currentClinicName,
                    dailyData: this.dailyData,
                    fetchParams: this.fetchParams,
                    payModes: this.payModes,
                    feeTypes: this.feeTypes,
                    isShandongQingdao: this.$abcSocialSecurity.config.isShandongQingdao,
                    isZhejiangHangzhou: this.$abcSocialSecurity.config.isZhejiangHangzhou,
                    isNeimenggu: this.$abcSocialSecurity.config.isNeimenggu,
                    nationalMedicalInsurance: this.nationalMedicalInsurance,
                };
                AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates.statOperateCashierReport,
                    printConfigKey: ABCPrintConfigKeyMap.statistics,
                    data,
                    extra: {
                        pdfOptions: {
                            filename: '收费日/月报',
                        },
                    },
                    isPreview: true,
                });
            },


        },
    };
</script>

<style lang="scss" scoped>
.operate-cashier-report-wrapper {
    position: relative;
    margin-top: 24px;
}
</style>
