<template>
    <abc-layout preset="page-table" class="common-padding-container">
        <abc-layout-header>
            <stat-toolbar
                :clinic-id-filter.sync="params.clinicId"
                :enable-features="toolbarFeatures"
                :date-filter.sync="params.dateFilter$"
                :handle-export="handleExport"
                :patient-id-filter.sync="params.patientId"
                :show-all-clinic="false"
                :fee-type-placeholder="supportAddNursing && params.dimension === 'project' ? '医嘱类型' : '费用分类'"
                :fee-type-filter.sync="params.feeTypeFilter$"
                :fee-type-selector-sticky="feeTypeSticky$"
                :fee-type-options="feeTypeFilterOptions"
                :export-task-type="exportTaskType"
                @change-patient="handlePatientChange"
                @change-date="handleDateChange"
                @change-clinic="handleClinicChange"
                @change-fee-type="handleFeeTypeChange"
            >
                <abc-cascader
                    ref="visitSourceRef"
                    v-model="params.visitSourceIdList"
                    :props="{
                        children: 'children',
                        label: 'name',
                        value: 'newId',
                    }"
                    :placeholder="params.dimension === 'record' ? '来源' : '本次推荐'"
                    multiple
                    mutually-exclusive
                    :width="120"
                    :options="visitSourceFilterOptions"
                    @change="handleVisitSourceChange"
                >
                </abc-cascader>
                <abc-select
                    v-if="supportAddNursing && params.dimension !== 'project' && params.dimension !== 'record'"
                    :key="feeTypeKey"
                    v-model="feeTypeIdList"
                    placeholder="费用分类"
                    :width="120"
                    clearable
                    multiple
                    multi-label-mode="text"
                    :max-tag="1"
                    style="margin-left: 2px;"
                    @change="handleAdviceFeeTypeChange"
                >
                    <abc-option
                        v-for="(item, index) in adviceFeeTypeOptions"
                        :key="index"
                        :value="item.id"
                        :label="item.name"
                    ></abc-option>
                </abc-select>
                <abc-checkbox
                    v-if="params.dimension !== 'record'"
                    v-model="isIncludingRegistration"
                    style="height: 32px; margin-left: 8px;"
                    @change="handleIncludingRegistrationChange"
                >
                    包含{{ $t('registrationFeeName') }}
                </abc-checkbox>
                <stat-dimension-picker
                    slot="right"
                    v-model="params.dimension"
                    :options="dimensionOptions"
                    @change="handleDimensionChange"
                ></stat-dimension-picker>
            </stat-toolbar>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <pro-statistics-table
                :loading="loading"
                :data="tableData"
                :render-config="tableRenderHeader"
            ></pro-statistics-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :pagination-params="tablePagination"
                :count="totalCount"
                :class="{ 'show-total': true }"
                @current-change="handlePageIndexChange"
            >
                <ul slot="tipsContent">
                    <li v-if="totalInfo" v-html="totalInfo"></li>
                    <li v-else>
                        共 <span>{{ totalCount }}</span> 条数据
                    </li>
                </ul>
            </abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import ClinicTypeJudger from 'views/statistics/statUtils/clinicTypeJudger';
    import PickerOptions from 'views/common/pickerOptions';
    import StatToolbar from 'views/statistics/common/stat-toolbar/stat-toolbar';
    import DateParamsMixins from 'views/statistics/mixins/date-params-mixin';
    import StatDimensionPicker from 'views/statistics/common/stat-dimension-picker/stat-dimension-picker';
    import { mapGetters } from 'vuex';
    import RecommendVisitApi from 'views/statistics/core/api/recommend-visit';
    import { RecommendService } from '@/service/recommend';
    import FeeTypeMixins from 'views/statistics/mixins/fee-type-mixin';
    import { AmountDialog } from 'views/statistics/common/amount-dialog/index.js';
    import ExportService from 'views/statistics/core/services/export/export-service';
    import {
        resolveHeaderV2,
    } from 'src/views/statistics/utils.js';
    import { resolveHeader } from 'utils/table';
    import ChargeAPI from 'views/statistics/core/api/charge.js';
    import {
        createGUID, formatMoney,
    } from '@/utils';
    import TableUtilsMixin from 'views/statistics/mixins/table-utils-mixin';
    import ProStatisticsTable from 'views/statistics/common/pro-statistics-table/index.vue';
    import {
        channelStaticConfig, orderStaticConfig,
    } from 'views/statistics/performance/recommend-visit/constants';


    export default {
        components: {
            ProStatisticsTable,
            StatToolbar,
            StatDimensionPicker,
        },
        mixins: [ClinicTypeJudger, PickerOptions, DateParamsMixins, FeeTypeMixins, TableUtilsMixin],
        data() {
            return {
                params: {
                    dimension: 'record',
                    clinicId: '',
                    pageIndex: 0,
                    pageSize: 12,
                    patientId: '',
                    visitSourceIdList: [],
                    visitSourceLevel1Ids: '',
                    visitSourceLevel2Ids: '',
                    visitSourceLevel3Ids: '',
                    feeTypeIds: '',
                },
                feeTypeIdList: [],
                adviceFeeTypeOptions: [],

                list: [],
                projectList: [],
                channelList: [],
                tableHeader: [],
                tableData: [],
                totalCount: 0,
                feeTypeFilterOptions: [],
                dimensionOptions: [
                    {
                        label: 'record', name: '推荐记录',
                    },
                    {
                        label: 'channel', name: '推荐渠道',
                    },
                    {
                        label: 'project', name: '项目',
                    },
                    {
                        label: 'sheet', name: '单据',
                    },
                ],
                loading: false,
                visitSourceFilterOptions: [],
                calcStatTableInfo: {},
                visitSourceOption: [],
                exportTaskType: 'achievement-recommend',
                feeTypeKey: createGUID(),
            };
        },

        computed: {
            ...mapGetters([
                'statisticsIsRevenueKpiIncludingRegistration',
                'statisticsIsRevenueKpiIncludingWriter',
                'currentClinic',
                'currentStatSettingRule',
                'isSingleStore',
                'isChainAdmin',
                'enablePatientMobileInStatistics',
                'enableGrossInStatistics',
                'enableCostInStatistics',
                'isChainAdmin',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),

            supportAddNursing() {
                return this.viewDistributeConfig?.Outpatient?.supportAddNursing;
            },
            queryClinicId() {
                if (this.isChainAdmin) {
                    return this.params.clinicId;
                }
                return this.isSingleStore ? '' : this.currentClinic?.clinicId;
            },

            tableRenderHeader() {
                if (this.params.dimension === 'sheet') {
                    const config = resolveHeaderV2({
                        header: this.tableHeader,
                        staticConfig: orderStaticConfig,
                    });
                    return {
                        hasHeaderBorder: true,
                        hasInnerBorder: true,
                        ...config,
                    };
                }
                if (this.params.dimension === 'channel') {
                    const config = resolveHeaderV2({
                        header: this.tableHeader,
                        staticConfig: channelStaticConfig,
                    });
                    config?.list?.forEach((item) => {
                        item?.children?.forEach((child) => {
                            child.autoSort = child.sortable;
                        });
                        item.autoSort = item.sortable;
                    });
                    return {
                        hasHeaderBorder: true,
                        hasInnerBorder: true,
                        ...config,
                    };
                }
                const list = resolveHeader(
                    this.tableHeader,
                    {},
                    true,
                );
                list.forEach((item) => {
                    item.children.forEach((child) => {
                        child.autoSort = child.sortable;
                    });
                    item.autoSort = item.sortable;
                });
                return {
                    hasHeaderBorder: true,
                    hasInnerBorder: true,
                    list,
                };
            },
            tablePagination() {
                return {
                    showTotalPage: false,
                    pageIndex: this.params.pageIndex,
                    pageSize: this.params.dimension !== 'channel' ? this.params.pageSize : 0,
                    count: this.totalCount,
                };
            },
            renderTypeList() {
                return {
                    'commissionAmountPopover': () => {
                        return (
                            <div>
                                <div>计提金额：用来评估员工业绩，为计算提成的基础。提成金额 = 计提金额 * 提成比例</div>
                                <div style="margin-top: 8px">统计口径：</div>
                                <div>
                                    {
                                        this.currentStatSettingRule.map((item, index) => (
                                            <div style="display: flex; align-items: center">
                                                <span>{index + 1}、 {item}</span>
                                            </div>
                                        ))
                                    }
                                </div>

                                <div style="color: #005ED9; cursor: pointer;margin-top: 8px" onClick={this.handleClick}>去设置</div>
                            </div>
                        );
                    },
                    costRender: (h, row, col) => {
                        const displayValue = row[col.prop];
                        const showCostText = row.hoverCode & 1;
                        const showAmounText = row.hoverCode & 2;
                        return <abc-flex justify="flex-end" align="center" style="padding:0 10px;">
                            <span class="ellipsis" title={formatMoney(displayValue)}>{formatMoney(displayValue) || '-'}</span>
                            {
                                row.hoverCode ? <abc-tooltip-info style="margin-left:4px">
                                    <p>成本特殊情况说明：</p>
                                    {showAmounText ? <p>包含在筛选时间外收费的药品，因此仅有成本没有收费金额。</p> : ''}
                                    {showCostText ? <p>包含因修改入库单导致的成本修正</p> : ''}
                                </abc-tooltip-info> : ''
                            }

                        </abc-flex>;
                    },
                };
            },
            toolbarFeatures() {
                const features = [
                    StatToolbar.Feature.DATE,
                    StatToolbar.Feature.EXPORT,
                    StatToolbar.Feature.CLINIC,
                ];

                if (this.params.dimension === 'record') {
                    features.push(StatToolbar.Feature.PATIENT);
                }
                if (this.params.dimension !== 'record') {
                    features.push(StatToolbar.Feature.FEE_TYPE);
                }

                if (this.params.dimension !== 'project' && this.supportAddNursing) {
                    const index = features.findIndex((item) => item === StatToolbar.Feature.FEE_TYPE);
                    if (index !== -1) {
                        features.splice(index, 1);
                    }
                }
                return features;
            },
            isIncludingRegistration: {
                get() {
                    return this.statisticsIsRevenueKpiIncludingRegistration;
                },

                set(val) {
                    this.$store.dispatch(
                        'setIsRevenueKpiIncludingRegistration',
                        val,
                    );
                },
            },
        },

        created() {
            this.exportService = new ExportService();
            if (this.supportAddNursing && this.params.dimension !== 'project' && this.params.dimension !== 'record') {
                this.getAdviceTypeOptions();
            }
        },
        beforeDestroy() {
            this.exportService.destroy();
            this.$parent.$off('amount-setting-success', this.getTableData);
            this.$abcEventBus.$offVmEvent(this._uid);

        },
        mounted() {
            this.feeTypeSticky$ = false;
            this.getFeeTypeSelectOptions();
            this.getListSource();
            this.$abcEventBus.$on('amount-setting-success', () => this.getTableData(), this);
        },

        methods: {
            async handleMounted(data) {
                this.params.pageSize = (data.paginationLimit - 1) || 10;
                await this.getTableData();
            },
            handleAdviceFeeTypeChange(list = []) {
                this.params.feeTypeIds = list?.length ? list?.join(',') : '';
                this.getTableData();
            },

            async getAdviceTypeOptions() {
                try {
                    const {
                        begin: beginDate, end: endDate,
                    } = this.params.dateFilter$;
                    const res = await ChargeAPI.getAdviceTypeSelect({
                        beginDate,
                        endDate,
                        clinicId: this.currentClinic.clinicId,
                    });
                    this.adviceFeeTypeOptions = res?.data?.data || [];
                    this.feeTypeKey = createGUID();
                } catch (err) {
                    console.log(err);
                }
            },

            getTableParams() {
                const {
                    pageIndex,
                    pageSize,
                    dimension,
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                    visitSourceLevel1Ids,
                    visitSourceLevel2Ids,
                    visitSourceLevel3Ids,
                    patientId,
                    feeTypeIds,
                } = this.params;
                const permissionParams = this.createPermissionParams();

                const {
                    isIncludingRegistration, queryClinicId: clinicId,
                } = this;
                const offset = pageIndex * pageSize;
                const baseParams = {
                    clinicId,
                    beginDate,
                    endDate,
                    visitSourceLevel1Ids,
                    visitSourceLevel2Ids,
                    visitSourceLevel3Ids,
                    ...permissionParams,
                };

                if (dimension === 'project') {
                    return {
                        ...baseParams,
                        offset,
                        size: pageSize,
                        isChainAdmin: +this.isChainAdmin,
                        includeReg: +isIncludingRegistration,
                        feeType1: this.feeType1FilterForRequest$,
                        feeType2: this.feeType2FilterForRequest$,
                    };
                } if (dimension === 'channel') {
                    return {
                        ...baseParams,
                        isChainAdmin: +this.isChainAdmin,
                        includeReg: +isIncludingRegistration,
                        feeType1: this.feeType1FilterForRequest$,
                        feeType2: this.feeType2FilterForRequest$,
                        feeTypeIds,
                    };
                } if (dimension === 'sheet') {
                    return {
                        ...baseParams,
                        offset,
                        size: pageSize,
                        isChainAdmin: +this.isChainAdmin,
                        includeReg: +isIncludingRegistration,
                        feeType1: this.feeType1FilterForRequest$,
                        feeType2: this.feeType2FilterForRequest$,
                        feeTypeIds,
                    };
                } return {
                    ...baseParams,
                    offset,
                    size: pageSize,
                    patientId,
                    enablePatientMobile: this.enablePatientMobileInStatistics,
                };
            },
            handleClick() {
                new AmountDialog(
                    {
                        successHandle: () => {
                            this.getTableData();
                        },
                        clinicId: this.currentClinic.clinicId,
                    },
                    'amount-dialog',
                ).generateDialog({ parent: this });
            },
            async getFeeTypeSelectOptions() {
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                } = this.params;
                try {
                    const res = await RecommendVisitApi.getFeeTypeOptions({
                        beginDate,
                        endDate,
                        clinicId: this.queryClinicId,
                        includeReg: +this.isIncludingRegistration,
                    });
                    this.feeTypeFilterOptions = res?.feeTypeList;
                } catch (e) {
                    console.log(e);
                    this.feeTypeFilterOptions = [];
                }
            },

            handleFeeTypeChange() {
                this.getTableData();
            },
            handlePatientChange() {
                this.getTableData();
            },
            handleIncludingRegistrationChange() {
                this.getFeeTypeSelectOptions();
                this.getTableData();
            },

            /**
             * 拉取就诊来源数据
             * <AUTHOR>
             * @date 2020-06-29
             */
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();
                }
                this.visitSourceFilterOptions = RecommendService.getInstance().cascaderOptions;
                this.visitSourceFilterOptions.unshift({
                    id: '00000000000000000000000000000000',
                    name: '未指定',
                });
                this.visitSourceFilterOptions = RecommendService.getInstance().processIds(this.visitSourceFilterOptions);
            },

            generateVisitSource2Params(list) {
                // 第一级是1， 2，下面要拼 null
                // 第一级是0，下面是3， 4要拼 null
                // 其余该怎么处理，就怎么处理
                const newList = list.map((item) => item || []);
                const medicalRecordType1 = newList.length >= 1 && newList[0]?.length ? [...new Set(newList[0].map((item) => item.id))] : [];
                const medicalRecordType2 = [];
                let medicalRecordType3 = [];
                if (newList.length >= 2 && newList[1]?.length) {
                    const arr = [...new Set(newList[1].map((item) => item.newId))];
                    arr.forEach((item) => {
                        if (item.split('/').length === 3) {
                            medicalRecordType3.push(item);
                        } else {
                            medicalRecordType2.push(item);
                        }
                    });
                }

                if (newList.length >= 3 && newList[2]?.length) {
                    medicalRecordType3 = [...medicalRecordType3, ...new Set(newList[2].map((item) => item.newId))];
                }
                this.params.visitSourceLevel1Ids = medicalRecordType1.join(',');
                this.params.visitSourceLevel2Ids = medicalRecordType2.join(',');
                this.params.visitSourceLevel3Ids = medicalRecordType3.join(',');

                this.getTableData();
            },

            handlePageIndexChange(index) {
                this.params.pageIndex = index - 1;
                this.getTableData(false);
            },

            handleDateChange() {
                this.params.feeTypeFilter$ = [];
                if (this.supportAddNursing && this.params.dimension !== 'project' && this.params.dimension !== 'record') {
                    this.getAdviceTypeOptions();
                }
                this.getFeeTypeSelectOptions();
                this.getTableData();
            },

            handleVisitSourceChange() {
                this.generateVisitSource2Params(this.$refs.visitSourceRef.getOriValue(this.params.visitSourceIdList));
            },

            handleClinicChange() {
                if (this.supportAddNursing && this.params.dimension !== 'project' && this.params.dimension !== 'record') {
                    this.getAdviceTypeOptions();
                }
                this.getFeeTypeSelectOptions();
                this.getTableData();
            },
            createPermissionParams() {
                return {
                    enableCost: this.enableCostInStatistics,
                    enableGross: this.enableGrossInStatistics,
                    enablePatientMobile: this.enablePatientMobileInStatistics,
                };
            },
            async handleExport(_, keys) {
                const permissionParams = this.createPermissionParams();
                const {
                    dateFilter$: {
                        begin: beginDate, end: endDate,
                    },
                    visitSourceLevel1Ids,
                    visitSourceLevel2Ids,
                    visitSourceLevel3Ids,
                    patientId,
                } = this.params;
                const {
                    isIncludingRegistration,
                    isChainAdmin,
                    queryClinicId: clinicId,
                } = this;

                try {
                    await this.exportService.startExport(this.exportTaskType, {
                        clinicId,
                        beginDate,
                        endDate,
                        feeType1: this.feeType1FilterForRequest$,
                        feeType2: this.feeType2FilterForRequest$,
                        visitSourceLevel1Ids,
                        visitSourceLevel2Ids,
                        visitSourceLevel3Ids,
                        includeReg: +isIncludingRegistration,
                        isChainAdmin: +isChainAdmin,
                        patientId,
                        keyWordList: keys,
                        ...permissionParams,
                    });
                } catch (e) {
                    console.error(e);
                    return false;
                }
                return true;
            },

            handleDimensionChange() {
                if (this.params.dimension === 'project') {
                    this.feeTypeSticky$ = true;
                } else {
                    this.feeTypeSticky$ = false;
                }
                if (this.supportAddNursing && this.params.dimension !== 'project' && this.params.dimension !== 'record') {
                    this.getAdviceTypeOptions();
                }
                this.tableHeader = [];
                this.projectList = [];
                this.list = [];
                this.channelList = [];
                this.getTableData();
            },

            async getTableData(resetPageParams = true) {
                await this.$nextTick();

                if (resetPageParams) {
                    this.params.pageIndex = 0;
                }

                const params = this.getTableParams();
                const { dimension } = this.params;

                this.loading = true;
                if (dimension === 'channel') {
                    try {
                        const { data } = await RecommendVisitApi.channel({
                            ...params,
                        });
                        this.setTableData(false, data, resetPageParams);
                    } catch (err) {
                        console.error(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }

                } else if (dimension === 'project') {
                    try {
                        const { data } = await RecommendVisitApi.project({
                            ...params,
                        });
                        this.setTableData(false, data, resetPageParams);
                    } catch (err) {
                        console.error(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }
                } else if (dimension === 'sheet') {
                    try {
                        const { data } = await RecommendVisitApi.sheet({
                            ...params,
                        });
                        this.setTableData(false, data, resetPageParams);
                    } catch (err) {
                        console.error(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }
                } else {
                    try {
                        const { data } = await RecommendVisitApi.record({
                            ...params,
                        });
                        this.setTableData(false, data, resetPageParams);
                    } catch (err) {
                        console.error(err);
                        this.setTableData(true);
                    } finally {
                        this.loading = false;
                    }
                }
            },
        },
    };
</script>

<style lang="scss">
.sign-info-wrapper {
    display: flex;
    padding: 16px;
    margin-top: 8px;
    margin-bottom: -16px;
    background-color: #e8f3ff;
}
</style>
