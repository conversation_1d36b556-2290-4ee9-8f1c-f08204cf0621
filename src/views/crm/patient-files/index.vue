<template>
    <div class="patient-files">
        <abc-container-left :is-support-drawer="true">
            <patient-list
                ref="quick-list"
                :patient-id="patientId"
                @change-patient-id="onChangePatientId"
                @fetch-patient-info="fetchPatientInfo"
                @add-visit-group="handleAddVisit"
            ></patient-list>
        </abc-container-left>
        <abc-container-center class="content-container">
            <abc-container-center-top-head custom-class="crm-abc-container-center__top-head" class="crm-header-tabs">
                <template v-if="useAbcUIV2">
                    <abc-tabs-v2
                        v-model="curTab"
                        :option="options"
                        class="crm_header--tabs"
                        :disable-indicator="options.length < 2"
                        :border="false"
                        size="huge"
                        @change="onChangeTab"
                    ></abc-tabs-v2>
                </template>
                <template v-else>
                    <div style="width: 100%;">
                        <abc-tabs-v2
                            v-model="curTab"
                            :option="options"
                            class="crm_header--tabs"
                            :disable-indicator="options.length < 2"
                            style="border-bottom: 0;"
                            size="huge"
                            :adaptation="true"
                            @change="onChangeTab"
                        ></abc-tabs-v2>
                    </div>
                </template>
                <abc-button
                    v-if="isChainAdmin || isSingleStore"
                    icon="n-settings-line"
                    variant="text"
                    style="margin-left: auto;"
                    :width="76"
                    @click="isShowCrmPatientMemberPowerSettingDialog = true"
                >
                    设置
                </abc-button>
            </abc-container-center-top-head>
            <div v-if="isBaseInfoTab" class="overview_info--box">
                <overview-info
                    ref="overview-info"
                    :loading="loadingPatientInfo"
                    :patient-id="patientId"
                    :hidden-operator="hiddenOperator"
                    :patient-info="patientInfo"
                    :patient-base-info="patientBaseInfo"
                    :cumulative-amount="cumulativeAmount"
                    :patient-family-member="patientFamilyMember"
                    @fetch-track="fetchTrack"
                    @fetch-patient-info="fetchPatientInfo"
                    @fetch-family-member="fetchPatientFamilyMember"
                    @update-info="handlePatientInfo"
                ></overview-info>
            </div>
            <div v-else-if="isChildrenHealth" class="overview_info--box-child">
                <children-health :patient-info="patientInfo" style="max-width: 1104px; margin: 0 auto;"></children-health>
            </div>

            <!-- 慢病康复 -->
            <div v-else-if="isChronicCareTab" class="chronic-care_record--box">
                <chronic-care-record
                    style="max-width: 1200px; margin: 0 auto;"
                    :patient="patientInfo"
                    @change="fetchPatientInfo"
                ></chronic-care-record>
            </div>
            <!-- 家庭医生 -->
            <div v-else-if="isFamilyDoctor" class="family_doctor--box">
                <div class="family_doctor--box-left">
                    <family-doctor
                        :patient-id="patientId"
                        :patient-info="patientInfo"
                    ></family-doctor>
                </div>
                <div class="family_doctor--box-right">
                    <patient-track
                        ref="sidebar-right"
                        :patient-id="patientId"
                    ></patient-track>
                </div>
            </div>
            <div v-else class="crm_record--box crm_record--box-record">
                <!--影像报告-->
                <crm-package-img
                    v-if="isExamImg"
                    :patient-info="patientInfo"
                    :patient-id="patientId"
                    :loading-patient-info="loadingPatientInfo"
                ></crm-package-img>
                <!--影像报告-->
                <!--收费记录-->
                <card-record-list
                    v-else-if="isRecord"
                    v-model="recordCurrent"
                    :current-employee-list="currentEmployeeList"
                    :patient-id="patientId"
                ></card-record-list>
                <!--收费记录-->
                <!--随访记录-->
                <crm-visit-table v-else-if="isFollowVisit" :patient-info="patientInfo" :patient-id="patientId">
                </crm-visit-table>
                <!--随访记录-->
                <card-pe-info v-else-if="isPhysicalExamination" :patient-id="patientId"></card-pe-info>
            </div>
            <!--随访记录-->
        </abc-container-center>
        <view-create-visit
            v-if="visibleAddVisit"
            v-model="visibleAddVisit"
            :patient-list="selectedPatientList"
        ></view-create-visit>
        <crm-patient-member-power-setting-dialog
            v-if="isShowCrmPatientMemberPowerSettingDialog"
            v-model="isShowCrmPatientMemberPowerSettingDialog"
        ></crm-patient-member-power-setting-dialog>
    </div>
</template>

<script>
    import CrmAPI from 'api/crm';
    import PatientList from './patient-list.vue';
    import OverviewInfo from './overview-info.vue';
    import PatientTrack from '../common/package-track/index.vue';
    const ChronicCareRecord = () => import('src/views/chronic-care/record/index');
    import FamilyDoctor from './family-doctor/index.vue';
    import {
        mapGetters, mapActions,
    } from 'vuex';
    import AbcAccess from '@/access/utils.js';
    import CrmPatientMemberPowerSettingDialog from './crm-patient-member-power-setting-dialog';
    import CrmPackageImg from 'views/crm/common/package-img/index.vue';
    import CardRecordList from './card-record-list.vue';
    import MixinModulePermission from 'views/permission/module-permission';
    import CrmVisitTable from 'views/crm/common/package-visit/crm-visit-table';
    import CardPeInfo from 'views/crm/patient-files/card-pe-info.vue';
    import { CrmTableTabsTypeEnum } from 'views/crm/constants';
    const ChildrenHealth = () => import('views/crm/patient-files/children-health.vue');
    export default {
        name: 'PatientFiles',
        components: {
            PatientList,
            OverviewInfo,
            PatientTrack,
            ViewCreateVisit: () => import('../common/package-visit/view-create-visit.vue'),
            ChronicCareRecord,
            ChildrenHealth,
            FamilyDoctor,
            CrmPatientMemberPowerSettingDialog,
            CrmPackageImg,
            CardRecordList,
            CrmVisitTable,
            CardPeInfo,
        },
        mixins: [
            MixinModulePermission,
        ],
        data() {
            return {
                curTab: 0,
                isShowCrmPatientMemberPowerSettingDialog: false,
                patientId: '',
                patientInfo: null,
                patientFamilyMember: null,
                patientBaseInfo: {
                    outpatientCount: '',
                    retailCount: '',
                    cumulativeAmount: '',
                    hospitalVisitNum: 0,
                    physicalVisitNum: 0,
                    profit: '',
                },
                cumulativeAmount: 0,
                loadingPatientInfo: false,
                recordCurrent: 'chargeRecord',
                recordList: {
                    [CrmTableTabsTypeEnum.OUTPATIENT]: 'outpatientRecord',
                    [CrmTableTabsTypeEnum.CHARGE]: 'chargeRecord',
                    [CrmTableTabsTypeEnum.TREATMENT]: 'treatmentRecord',
                    [CrmTableTabsTypeEnum.INSPECTION]: 'onlyInspectRecord',
                    [CrmTableTabsTypeEnum.REGISTER]: 'registrationRecord',
                    [CrmTableTabsTypeEnum.EXAM]: 'onlyExaminationRecord',
                },
                selectedPatientList: null,
                visibleAddVisit: false,
                currentEmployeeList: [],
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'isSingleStore','isCanSeePatientPayAmountInCrm']),
            ...mapGetters([
                'isChainAdmin',
            ]),
            ...mapGetters('viewDistribute', [
                'featureFamilyDoctor',
                'featureChronicRecovery',
                'viewDistributeConfig',
            ]),
            ...mapGetters('chronicCare', ['clinicChronicCareProjects']),
            useAbcUIV2() {
                return this.viewDistributeConfig.useAbcUIV2;
            },
            hiddenOperator() {
                return this.viewDistributeConfig.CRM.hiddenOperator;
            },
            memberShareInfos() {
                return this.patientFamilyMember?.familyPatients || [];
            },
            // 是否基础档案tab
            isBaseInfoTab() {
                return this.curTab === CrmTableTabsTypeEnum.BASIC;
            },
            // 是否慢病康复tab
            isChronicCareTab() {
                return this.curTab === CrmTableTabsTypeEnum.CHRONIC_CARE;
            },
            // 是否是儿保
            isChildrenHealth() {
                return this.curTab === CrmTableTabsTypeEnum.CHILDREN_HEALTH;
            },
            // 是否家庭医生tab
            isFamilyDoctor() {
                return this.curTab === CrmTableTabsTypeEnum.FAMILY_DOCTOR;
            },
            // 是否影像检查
            isExamImg() {
                return this.curTab === CrmTableTabsTypeEnum.EXAM_IMG;
            },
            isFollowVisit() {
                return this.curTab === CrmTableTabsTypeEnum.FOLLOW_VISIT;
            },
            isRecord() {
                return [CrmTableTabsTypeEnum.OUTPATIENT,
                        CrmTableTabsTypeEnum.CHARGE,
                        CrmTableTabsTypeEnum.TREATMENT,
                        CrmTableTabsTypeEnum.INSPECTION,
                        CrmTableTabsTypeEnum.EXAM,
                        CrmTableTabsTypeEnum.REGISTER,
                ].includes(this.curTab);
            },
            isPhysicalExamination() {
                return this.curTab === CrmTableTabsTypeEnum.PHYSICAL_EXAM;
            },
            // 慢病康复信息
            chronicArchivesInfo() {
                const { chronicArchivesInfo } = this.patientInfo || {};
                return chronicArchivesInfo || {
                    archivesCount: 0,
                    archivesList: [],
                };
            },
            crmBusinessCount() {
                return this.viewDistributeConfig.CRM.cardPatientInfo.crmBusinessCount;
            },
            isOpenPhysicalExamination() {
                //开通体检
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.PHYSICAL_EXAMINATION);
            },
            // 可选项
            options() {
                const tabOptions = [{
                                        value: CrmTableTabsTypeEnum.BASIC,
                                        label: this.viewDistributeConfig.CRM.baseInfoTabLabel || '档案详情',
                                        visible: true,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.REGISTER,
                                        label: '预约记录',
                                        visible: this.viewDistributeConfig.CRM.showAppointmentRecord,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.OUTPATIENT,
                                        label: '病历记录',
                                        visible: this.viewDistributeConfig.CRM.showOutpatientHistory,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.CHARGE,
                                        label: this.viewDistributeConfig.CRM?.feeRecordTabLabel || '收费记录',
                                        visible: this.isCanSeePatientPayAmountInCrm,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.EXAM_IMG,
                                        label: '影像报告',
                                        visible: this.viewDistributeConfig.CRM.showExamImg,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.FOLLOW_VISIT,
                                        label: '随访记录',
                                        visible: this.viewDistributeConfig.CRM.showVisit,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.PHYSICAL_EXAM,
                                        label: '体检记录',
                                        visible: this.crmBusinessCount.physicalExamination || this.isOpenPhysicalExamination,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.INSPECTION,
                                        label: '检查记录',
                                        visible: this.viewDistributeConfig.CRM.showInspection,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.EXAM,
                                        label: '检验记录',
                                        visible: this.viewDistributeConfig.CRM.showInspection,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.TREATMENT,
                                        label: '治疗/理疗记录',
                                        visible: this.viewDistributeConfig.CRM.showTreatment,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.FAMILY_DOCTOR,
                                        label: '家庭医生',
                                        visible: this.featureFamilyDoctor &&
                                            AbcAccess.getPurchasedByKey(AbcAccess.accessMap.FAMILY_DOCTOR) &&
                                            !this.isChainAdmin,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.CHILDREN_HEALTH,
                                        label: '儿保档案',
                                        visible: this.hasChildHealthModule,
                                    },
                                    {
                                        value: CrmTableTabsTypeEnum.CHRONIC_CARE,
                                        label: '慢病管理',
                                        statisticsNumber: this.chronicArchivesInfo?.archivesCount || 0,
                                        visible: this.featureChronicRecovery &&
                                            this.$store.state.property.chainBasic.isEnableChronicRecovery &&
                                            this.clinicChronicCareProjects.length !== 0 ||
                                            this.chronicArchivesInfo?.archivesCount > 0,
                                    },
                ];
                return tabOptions.filter((item) => item.visible);
            },
        },
        watch: {
            patientFamilyMember: {
                handler (val) {
                    if (val) {
                        this.fetchFamilyBaseInfo();
                    }
                },
                immediate: false,
            },
        },
        async created() {
            if (this.$store.state.property.chainBasic.isEnableChronicRecovery) {
                this.$store.dispatch('chronicCare/initProjects');
            }
            if (!this.isChainAdmin) {
                this.$store.dispatch('fetchAllDoctorsRegsFee', {
                    allEmployee: 1,
                });
            }
            this.$store.dispatch('crm/fetchPointsClearConfig');
            this.fetchRegistrationFee();
            this.fetchEmployeeList();
        },

        methods: {
            ...mapActions(['fetchRegistrationFee']),
            async fetchEmployeeList() {
                try {
                    const { data } = await CrmAPI.fetchRevisitEmployeeList();
                    this.currentEmployeeList = data.rows;
                } catch (error) {
                    console.log('fetchEmployeeList error', error);
                }
            },
            // 统计的接口 操作谨慎
            async fetchFamilyBaseInfo() {
                const patientIds = this.memberShareInfos?.map((item) => {
                    return item?.patientId;
                });
                let clinicId = '';
                if (this.isChainSubStore || this.isSingleStore) {
                    clinicId = this.currentClinic.clinicId;
                }
                const params = {
                    patientIds,
                    clinicId,
                    chainId: this.isSingleStore ? null : this.currentClinic.chainId,
                };
                try {
                    const { data } = await CrmAPI.fetchFamilyBaseInfo(params);
                    this.cumulativeAmount = data?.rows?.map((item) => {
                        return item?.cumulativeAmount || 0;
                    })?.reduce((pre, cur) => {
                        return Number(pre) + Number(cur);
                    }, 0) || 0;
                    const {
                        outpatientCount,
                        cumulativeAmount,
                        retailCount,
                        hospitalVisitNum,
                        physicalVisitNum,
                        profit,
                    } = data?.rows?.find((item) => {return item.patientId === this.patientId;}) || {};
                    this.patientBaseInfo.outpatientCount = outpatientCount;
                    this.patientBaseInfo.retailCount = retailCount;
                    this.patientBaseInfo.cumulativeAmount = cumulativeAmount;
                    this.patientBaseInfo.physicalVisitNum = physicalVisitNum;
                    this.patientBaseInfo.hospitalVisitNum = hospitalVisitNum;
                    this.patientBaseInfo.profit = profit;
                } catch (e) {
                    console.log(e);
                }
            },
            /**
             * 当切换tab时
             * <AUTHOR>
             * @date 2021-01-19
             * @param {Number} index 当前tab索引
             * @param {Object} tabItem 当前tab元素
             */
            onChangeTab(index, tabItem) {
                this.curTab = tabItem.value;
                if (this.isRecord) {
                    this.recordCurrent = this.recordList[this.curTab];
                }
            },
            /**
             * 当改变当前所选患者时
             * <AUTHOR>
             * @date 2021-01-19
             * @param {String} patientId 选择患者id
             */
            async onChangePatientId(patientId) {
                this.curTab = 0;
                this.patientId = patientId;
                this.loadingPatientInfo = true;
                await Promise.all([this.fetchPatientInfo(), this.fetchPatientFamilyMember()]).finally(() => {
                    this.loadingPatientInfo = false;
                });
            },

            async fetchPatientBaseInfo(patientId) {
                try {
                    const { data: res } = await CrmAPI.fetchPatientBaseInfo(patientId);
                    const {
                        outpatientCount,
                        cumulativeAmount,
                        retailCount,
                    } = res.rows[0] || {};
                    this.patientBaseInfo.outpatientCount = outpatientCount;
                    this.patientBaseInfo.retailCount = retailCount;
                    this.patientBaseInfo.cumulativeAmount = cumulativeAmount;
                } catch (e) {
                    console.log(e);
                }
            },

            async fetchPatientFamilyMember() {
                const { patientId } = this;
                try {
                    const { data } = await CrmAPI.fetchFamilyMember(patientId, { needWxBindStatus: true }) || {};
                    this.patientFamilyMember = data;
                } catch (e) {
                    console.log(e);
                }
            },

            /**
             * 拉取患者详情
             * <AUTHOR>
             * @date 2021-01-19
             */
            async fetchPatientInfo() {
                this.loadingPatientInfo = true;
                try {
                    const params = {
                        wx: 1,
                        chronicArchives: 1,
                        promotionCardList: 1,
                    };
                    const { data } = await CrmAPI.fetchPatientOverviewV2(this.patientId, params);
                    if (data.id === this.patientId) {
                        this.patientInfo = data;
                        this.updateSelectedPatient(this.patientInfo);
                    }
                } catch (error) {
                    console.log('fetchPatientInfo error', error);
                }
                this.loadingPatientInfo = false;
            },

            handlePatientInfo(newPatientInfo) {
                this.$set(this.patientInfo, 'wxBindStatus', newPatientInfo.wxBindStatus);
                this.$set(this.patientInfo, 'wxStatus', newPatientInfo.wxBindStatus);
                this.fetchPatientInfo();
                this.updateSelectedPatient(this.patientInfo);
            },
            /**
             * 刷新患者轨迹信息
             * <AUTHOR>
             * @date 2021-01-19
             */
            fetchTrack() {
                const refNode = this.$refs['sidebar-right'];
                refNode && refNode.fetch && refNode.fetch();
            },
            /**
             * 处理新增随访任务
             * <AUTHOR>
             * @date 2021-01-19
             * @param {Array} selectedPatientList 所选患者列表
             */
            handleAddVisit(selectedPatientList) {
                this.visibleAddVisit = true;
                this.selectedPatientList = selectedPatientList;
            },
            /**
             * 更新患者列表，被选中患者的信息
             * <AUTHOR>
             * @date 2021-01-19
             * @param {Object} patientInfo 患者信息
             */
            updateSelectedPatient(patientInfo) {
                const quickList = this.$refs['quick-list'];
                quickList && quickList.onUpdateSelectedPatient && quickList.onUpdateSelectedPatient(patientInfo);
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .crm-module .patient-files {
        display: flex;
        width: 100%;

        .head-wrapper > div {
            padding: 0 24px;

            @include flex(row, flex-start, center);
        }

        .app-base-layout {
            .cent-wrapper > div > div {
                border-radius: 0 0 4px 0;
            }
        }

        .left-wrapper {
            width: 300px;
        }

        .crm-abc-container-center__top-head {
            .header-content {
                padding: 0 4px 0 10px !important;
            }
        }
    }

    .overview_info--box {
        width: 100%;
        height: 100%;
        padding: 20px 10px 20px 20px;

        &-child {
            padding: 20px 10px 20px 20px;
        }
    }

    .chronic-care_record--box {
        width: 100%;
        padding: 24px 14px 24px 24px;

        .chronic-care-record-wrapper {
            min-width: 1150px;
        }
    }

    .family_doctor--box {
        display: flex;
        height: 100%;

        &-left {
            position: relative;
            flex: 1;
            overflow-x: hidden;
            overflow-y: overlay;
        }

        &-right {
            flex-shrink: 0;
            width: 320px;
            height: 100%;
            border-left: 1px solid $P6;
        }
    }

    @media screen and (max-width: 1024px) {
        .overview_info--box {
            padding: 20px 10px 20px 20px !important;
        }
    }

    .crm_record--box-record {
        flex: 1;
        height: 100%;
    }
</style>
