<template>
    <div
        :class="['crm-patient-list_item','item-patient', {
            active: active,
            'crm-patient-list_item--can-select': canSelect,
        }]"
        data-cy="crm-patient-item"
        @click="$emit('click-patient-item')"
    >
        <div
            class="crm-basic"
            :class="{
                'crm-basic-no-wechat': !isShowWxIcon
            }"
        >
            <abc-checkbox
                v-if="canSelect"
                :value="selected"
                style="margin-right: 8px;"
                @change="(selected) => $emit('toggle-select', selected)"
            ></abc-checkbox>
            <abc-avatar
                class="crm-icon"
                :is-male="patient.sex === '男'"
                :border-visible="!active"
                :is-vip="patient.isMember === 1 && isShowMemberIcon"
            ></abc-avatar>
            <span
                class="name"
            >{{ patient.name }}</span>
            <span v-if="isShowWxIcon" class="crm-logo">
                <span
                    v-if="patient.wxBindStatus === 3"
                    class="icon-wechat binded"
                >
                    <img src="~assets/images/crm/icon-wechat.png" alt="wechat" />
                </span>
            </span>
        </div>
        <span class="age"><abc-text size="mini">{{ patientAge }}</abc-text></span>
        <span
            class="vip-level"
            :class="{
                'vip-level-no-wechat': !isShowWxIcon
            }"
            :style="{ width: canSelect ? '32px' : '48px' }"
        >
            <abc-popover
                v-if="patient.memberTypeName && patient.memberTypeName.length > 4"
                placement="top"
                trigger="hover"
                :open-delay="1000"
                theme="yellow"
            >
                <span slot="reference" class="past-history">{{ patient.memberTypeName }}</span>
                <div>{{ patient.memberTypeName }}</div>
            </abc-popover>
            <span v-else>{{ patient.memberTypeName }}</span>
        </span>
        <span class="date"><abc-text size="mini" :theme="active ? 'white' : 'gray'">{{ lastDate }}</abc-text></span>
    </div>
</template>

<script>
    import {
        formatAge, formatCacheTime,
    } from '@/utils';
    import { mapGetters } from 'vuex';
    import AbcAvatar from 'views/layout/abc-avatar/index.vue';


    export default {
        name: 'ItemPatient',
        components: {
            AbcAvatar,
        },
        props: {
            canSelect: {
                type: Boolean,
                default: false,
            },
            selected: {
                type: Boolean,
                default: false,
            },
            active: {
                type: Boolean,
                default: false,
            },
            patient: {
                type: Object,
                required: true,
            },
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            patientAge() {
                // 岁月天只显示其一
                const ageStr = formatAge(this.patient.age,{
                    monthYear: 150, dayYear: 1,
                }).toString();
                const arr = ageStr.split(/\D/);
                if (ageStr.includes('岁')) {
                    return arr[0] ? `${arr[0]}岁` : '';
                } if (ageStr.includes('月')) {
                    return arr[0] ? `${arr[0]}月` : '';
                }
                return arr[0] ? `${arr[0]}天` : '';
            },
            isShowMemberIcon() {
                return this.viewDistributeConfig.CRM.isShowMemberIcon;
            },
            isShowWxIcon() {
                return this.viewDistributeConfig.CRM.isShowWxIcon;
            },
            lastOutpatientInfoLabel() {
                return this.viewDistributeConfig.CRM?.lastOutpatientInfoLabel || '最近就诊';
            },
            lastDate() {
                if (this.lastOutpatientInfoLabel === '最近就诊') {
                    return this.patient.lastOutpatientDate ? formatCacheTime(this.patient.lastOutpatientDate) : '--';
                }
                if (this.lastOutpatientInfoLabel === '最近消费') {
                    return this.patient.lastChargeDate ? formatCacheTime(this.patient.lastChargeDate) : '--';
                }
                return this.patient.lastOutpatientDate ? formatCacheTime(this.patient.lastOutpatientDate) : '--';
            },
        },
        methods: {
            formatAge,
            formatCacheTime,
        },
    };
</script>

<style lang="scss">
@import '~styles/abc-common.scss';

.crm-patient-list_item {
    height: 44px;
    padding: 0 10px;
    font-size: 12px;
    color: $T1;
    cursor: pointer;
    border-radius: var(--abc-border-radius-small);

    @include flex(row, flex-start, center);

    & + .crm-patient-list_item {
        margin-top: 4px;
    }

    .crm-basic {
        max-width: 136px;
        height: 100%;

        @include flex(row, flex-start, center);

        .crm-icon {
            min-width: 20px;
        }

        .crm-logo {
            gap: var(--abc-space-xs);
            width: 34px;

            @include flex(row, flex-end, center);
        }

        .name {
            display: inline-block;
            width: 56px;
            margin: 0 4px;
            font-size: 14px;
            text-overflow: clip;

            @include ellipsis(1);
        }

        .vip {
            display: inline-block;
            flex-shrink: 0;
            width: 14px;
            height: 14px;
            background-image: url('~assets/images/crm/<EMAIL>');
            background-repeat: no-repeat;
            background-position: center center;
            background-size: cover;
        }
    }

    .age {
        display: inline-block;
        flex-shrink: 0;
        width: 33px;
        margin: 0 var(--abc-space-m);
        text-align: right;
    }

    .vip-level {
        display: inline-block;
        min-width: var(--abc-size-m);
        margin-right: 3px;
        overflow: hidden;
        text-align: left;
        white-space: nowrap;

        @include ellipsis(1);
    }

    .vip-level-no-wechat {
        min-width: var(--abc-size-l);
    }

    .crm-basic-no-wechat {
        max-width: 166px;

        .name {
            width: 78px !important;
        }
    }

    .past-history {
        @include ellipsis();
    }

    .date {
        display: inline-block;
        flex-shrink: 0;
        min-width: 64px;
        color: $T2;
        text-align: right;
    }

    &.active {
        color: $S2;
        background-color: $B3;
        border-bottom: 1px solid $B3;

        .vip {
            background-image: url('~assets/images/crm/<EMAIL>');
        }

        .date {
            color: $S2;
        }
    }

    &:not(.active):hover {
        background-color: #eff3f6;
    }

    .icon-wechat {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 14px;
        min-width: 14px;
        height: 14px;
        margin-left: var(--abc-space-xs);
        cursor: pointer;
        border-radius: 2px;

        &.unbind {
            background: $P1;
        }

        &.binded {
            background: $G2;
        }

        > img {
            width: 10px;
            height: 10px;
            margin-left: 0;
        }
    }
}

@media screen and (max-width: 1024px) {
    .crm-module .patient-list {
        .crm-patient-list_item {
            .vip-level {
                display: none;
            }

            .date {
                min-width: 70px;
            }
        }
    }
}

@media screen and (max-width: 1096px) {
    .crm-module .patient-list {
        .crm-patient-list_item {
            .vip-level {
                display: none;
            }
        }

        .crm-patient-list_item--can-select {
            .crm-basic {
                min-width: 78px !important;

                .crm-logo {
                    .icon-wechat {
                        display: none !important;
                    }
                }
            }

            .crm-basic-no-wechat {
                min-width: 100px !important;
            }
        }
    }
}

@media screen and (min-width: 1097px) and (max-width: 1365px) {
    .crm-module .patient-list {
        .crm-patient-list_item {
            .crm-basic {
                min-width: 64px;
            }

            .crm-basic-no-wechat {
                width: 86px !important;
            }

            .crm-logo {
                display: none;
            }

            .vip-level {
                display: none;
            }

            .age {
                width: 33px;
            }

            .date {
                min-width: 70px;
            }
        }

        .crm-patient-list_item--can-select {
            .crm-basic {
                min-width: 56px !important;
            }

            .crm-basic-no-wechat {
                min-width: 78px !important;
            }

            .date {
                min-width: 64px;
            }
        }
    }
}

@media screen and (min-width: 1366px) and (max-width: 1439px) {
    .crm-module .patient-list {
        .crm-patient-list_item {
            .vip-level {
                display: none;
            }
        }
    }
}

@media screen and (min-width: 1440px) and (max-width: 1519px) {
    .crm-module .patient-list {
        .crm-patient-list_item--can-select {
            .vip-level {
                display: none;
            }

            .crm-basic {
                .name {
                    width: 48px;
                    margin: 0 8px 0 4px;
                }
            }

            .crm-basic-no-wechat {
                .name {
                    width: 70px !important;
                    margin: 0 4px !important;
                }
            }
        }
    }
}

@media screen and (min-width: 1660px) and (max-width: 1919px) {
    .crm-patient-list_item {
        .crm-basic {
            .name {
                width: 54px;
                margin: 0 12px 0 6px;
            }
        }

        .crm-basic-no-wechat {
            .name {
                width: 76px !important;
            }
        }

        .age {
            margin: 0 var(--abc-space-s);
        }
    }
}

@media screen and (min-width: 1440px) and (max-width: 1659px) {
    .crm-patient-list_item {
        .crm-basic {
            .name {
                width: 48px;
                margin: 0 8px 0 4px;
            }

            .crm-logo {
                gap: 1px;
            }
        }

        .crm-basic-no-wechat {
            .name {
                width: 70px !important;
                margin: 0 4px !important;
            }
        }

        .age {
            margin: 0 var(--abc-space-s);
        }

        .vip-level {
            min-width: var(--abc-size-l) !important;
        }

        .vip-level-no-wechat {
            min-width: var(--abc-size-xl);
        }

        .date {
            min-width: var(--abc-size-xxl);
        }
    }
}
</style>
