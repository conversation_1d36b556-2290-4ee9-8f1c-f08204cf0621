

<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        size="xlarge"
        title="患者信息对比"
        content-class="merge-crm-dialog"
        append-to-body
        content-styles="height: 456px;"
    >
        <abc-layout v-abc-loading="loading">
            <abc-section>
                <abc-table
                    :render-config="renderConfig"
                    :data-list="dataList"
                    type="excel"
                >
                    <template #checkbox="{ trData: row }">
                        <abc-table-cell>
                            <abc-checkbox
                                v-model="row.checked"
                                :disabled="row.disabled"
                            ></abc-checkbox>
                        </abc-table-cell>
                    </template>
                    <template #excelCrm="{ trData: row }">
                        <abc-table-cell>
                            <abc-flex>
                                <abc-space :size="4" align="center">
                                    <abc-text v-abc-title.ellipsis="row.excelCrm" style="display: block; max-width: 260px;" :theme="row.excelCrm === row.crm ? 'black' : 'warning-light'">
                                    </abc-text>
                                    <abc-popover
                                        v-if="row.excelCrmErrText"
                                        width="auto"
                                        theme="yellow"
                                        placement="top-start"
                                        trigger="hover"
                                        :offset="100"
                                    >
                                        <div>
                                            <abc-text>
                                                {{ row.excelCrmErrText === '未匹配到二级来源，是否确认' ? '未匹配到二级来源' : row.excelCrmErrText === '未匹配到一级来源，是否确认' ? '未匹配到一级来源' : row.excelCrmErrText }}
                                            </abc-text>
                                        </div>
                                        <abc-icon
                                            slot="reference"
                                            icon="Attention"
                                            size="14"
                                            style="color: #ff9933;"
                                        ></abc-icon>
                                    </abc-popover>
                                </abc-space>
                            </abc-flex>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-section>
        </abc-layout>
        <template slot="footer">
            <abc-flex justify="flex-end">
                <abc-space>
                    <!--                    <abc-button :disabled="!dataList.some(item => item.checked)" @click="mergeCrm">-->
                    <!--                        更新-->
                    <!--                    </abc-button>-->
                    <abc-button variant="ghost" @click="showDialog = false">
                        关闭
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    import {
        validateIdCard, validateMobile,
    } from 'utils/validate';
    import CrmAPI from 'api/crm';
    import { defaultCountryCode } from 'utils/country-codes';
    import { age2birthday } from '@/utils';
    import { RecommendService } from '@/service/recommend';
    import { mapGetters } from 'vuex';
    import handleReferrerPromotion from 'views/crm/mixin/handle-referrer-promotion';
    import { DEFAULT_CERT_TYPE } from 'views/crm/constants';

    export default {
        name: 'MergeCrmDialog',
        mixins: [handleReferrerPromotion],
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            mergeCrmObject: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            // eslint-disable-next-line vue/require-default-prop
            conflictPatientId: String,
            formSource: {
                type: String,
                default: 'importList',
            },
        },
        data() {
            return {
                patientInfo: {},
                loading: false,
                dataList: [{
                    keyName: '姓名',
                    key: 'name',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0001',
                },{
                    keyName: '年龄',
                    key: 'age',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0002',
                },{
                    keyName: '性别',
                    key: 'sex',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0003',
                },{
                    keyName: '手机号',
                    key: 'mobile',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0004',
                },{
                    keyName: '证件',
                    key: 'idCard',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0005',
                },{
                    keyName: '住址',
                    key: 'address',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0006',
                },{
                    keyName: '一级来源',
                    key: 'source',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0007',
                },{
                    keyName: '二级来源',
                    key: 'sourceFrom',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0008',
                },{
                    keyName: '备注',
                    key: 'remark',
                    excelCrm: '',
                    crm: '',
                    // checked: false,
                    disabled: true,
                    excelCrmErrText: '',
                    id: '0009',
                }],
                sourceChildList: [],
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isFromErrList() {
                return this.formSource === 'importErrList';
            },
            renderConfig() {
                return {
                    list: [
                        {
                            'key': 'keyName',
                            'label': '字段',
                            'style': {
                                'max-width': '140px',
                                'min-width': '140px',
                            },
                        },
                        {
                            'key': 'excelCrm',
                            'label': 'Excel表格',
                            'style': {
                                flex: 1,
                                'min-width': '149px',
                            },
                        },
                        {
                            'key': 'crm',
                            'label': ' 系统已存在患者',
                            'style': {
                                flex: 1,
                                'min-width': '149px',
                            },
                        },
                    ],
                };
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        async created() {
            await this.getListSource();
            if (this.conflictPatientId) {
                await this.init();
            }
        },
        methods: {
            validateHalfAngleLength(value) {
                if (!value) {
                    return true;
                }
                // 区分全半角长度时，单独处理
                let len = 0;
                for (let i = 0; i < value.length; i++) {
                    len += value.charCodeAt(i) > 127 ? 2 : 1;
                }
                return len <= 40;
            },
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();
                }
                const sourceList = RecommendService.getInstance().cascaderOptions;
                if (!this.viewDistributeConfig.CRM.patientMember.isShowReferralDoctorSource) {
                    const targetIndex = sourceList.findIndex((item) => item.name === '转诊医生');
                    if (targetIndex !== -1) {
                        sourceList.splice(targetIndex, 1);
                    }
                }
                if (!this.viewDistributeConfig.CRM.patientMember.isShowDoctorPutSource) {
                    const targetIndex = sourceList.findIndex((item) => item.name === '医生推荐');
                    if (targetIndex !== -1) {
                        sourceList.splice(targetIndex, 1);
                    }
                }
                this.sourceChildList = sourceList;
            },
            async init() {
                this.loading = true;
                try {
                    const { data = {} } = await CrmAPI.fetchPatientOverviewV2(this.conflictPatientId);
                    this.patientInfo = data;
                    if (data) {
                        this.dataList.map((item) => {
                            if (item.key === 'address') {
                                item.crm = `${data?.[item.key]?.addressProvinceName || ''}${data?.[item.key]?.addressCityName || ''}${data?.[item.key]?.addressDistrictName || ''}${data?.[item.key]?.addressDetail || ''}`.trim();
                                if (this.isFromErrList) {
                                    item.excelCrm = `${this.mergeCrmObject.addressProvinceName || ''}${this.mergeCrmObject.addressCityName || ''}${this.mergeCrmObject.addressDistrictName || ''}${this.mergeCrmObject.addressDetail || ''}`.trim();
                                } else {
                                    item.excelCrm = `${this.mergeCrmObject[item.key].addressProvinceName || ''}${this.mergeCrmObject[item.key].addressCityName || ''}${this.mergeCrmObject[item.key].addressDistrictName || ''}${this.mergeCrmObject.addressDetail || ''}`.trim();
                                }
                                if (this.mergeCrmObject.addressDetail?.length > 300) {
                                    item.excelCrmErrText = '详细地址不能超过300个字符';
                                }
                            } else if (item.key === 'age') {
                                item.crm = data.birthday || '';
                                if (this.isFromErrList) {
                                    item.excelCrmErrText = this.convertToStandardDate(this.mergeCrmObject.birthday) ? '' : '年龄格式不正确,不允许导入';
                                    item.excelCrm = this.convertToStandardDate(this.mergeCrmObject.birthday) || '';
                                } else {
                                    item.excelCrmErrText = this.convertToStandardDate(this.mergeCrmObject.age) ? '' : '年龄格式不正确,不允许导入';
                                    item.excelCrm = this.convertToStandardDate(this.mergeCrmObject.age) || this.mergeCrmObject.age || '';
                                }
                            } else if (item.key === 'source') {
                                if (['医生推荐', '员工推荐', '顾客推荐', '转诊医生'].includes(data.patientSource?.name)) {
                                    // 这几类是固定的
                                    item.crm = data.patientSource?.name || '';
                                } else {
                                    // 有parentName一级来源就是 parentName，二级来源是 name
                                    item.crm = data.patientSource?.parentName || data.patientSource?.name || '';
                                }
                                if (this.isFromErrList) {
                                    item.excelCrm = this.mergeCrmObject.sourceName || '';
                                    item.excelCrmErrText = '';
                                } else {
                                    item.excelCrm = this.mergeCrmObject[item.key] || '';
                                    item.excelCrmErrText = this.mergeCrmObject.sourceText || item.excelCrm?.length > 10 ? this.mergeCrmObject.sourceText || '一级来源不能超过10个字符' : '';
                                }
                            } else if (item.key === 'sourceFrom') {
                                if (['医生推荐', '员工推荐', '顾客推荐', '转诊医生'].includes(data.patientSource?.name)) {
                                    // 这几类是固定的
                                    item.crm = data.patientSource?.sourceFromName || '';
                                } else {
                                    // 有parentName一级来源就是 parentName，二级来源是 name
                                    if (data.patientSource?.parentName) {
                                        item.crm = data.patientSource?.name || '';
                                    } else {
                                        item.crm = '';
                                    }
                                }
                                if (this.isFromErrList) {
                                    item.excelCrm = this.mergeCrmObject.sourceFromName || '';
                                    item.excelCrmErrText = '';
                                } else {
                                    item.excelCrm = this.mergeCrmObject[item.key] || '';
                                    item.excelCrmErrText = this.mergeCrmObject.sourceFromText || item.excelCrm?.length > 10 ? this.mergeCrmObject.sourceFromText || '二级来源不能超过10个字符' : '';
                                }
                            } else if (item.key === 'idCard') {
                                validateIdCard(this.mergeCrmObject[item.key], (res) => {
                                    const { validate } = res;
                                    if (!validate) {
                                        item.excelCrmErrText = '身份证格式不正确,不允许导入';
                                    }
                                });
                                item.crm = data[item.key] ? `${data.idCardType || DEFAULT_CERT_TYPE}-${data[item.key]}` : '';
                                item.excelCrm = this.mergeCrmObject[item.key] ? `${this.mergeCrmObject.idCardType || DEFAULT_CERT_TYPE}-${this.mergeCrmObject[item.key]}` : '';
                            } else {
                                if (item.key === 'name' && !this.validateHalfAngleLength(this.mergeCrmObject[item.key])) {
                                    item.excelCrmErrText = '姓名不能超过20个字符';
                                }
                                if (item.key === 'remark' && this.mergeCrmObject[item.key]?.length > 300) {
                                    item.excelCrmErrText = '备注不能超过300个字符';
                                }
                                if (item.key === 'remark') {
                                    this.validateHalfAngleLength(this.mergeCrmObject[item.key], (res) => {
                                        const { validate } = res;
                                        if (!validate) {
                                            item.excelCrmErrText = '姓名不能超过20个字符';
                                        }
                                    });
                                }
                                if (item.key === 'mobile') {
                                    validateMobile(this.mergeCrmObject[item.key], (res) => {
                                        const { validate } = res;
                                        if (!validate) {
                                            item.excelCrmErrText = '手机号格式不正确,不允许导入';
                                        }
                                    }, this.mergeCrmObject.countryCode);
                                }
                                item.crm = data[item.key] || '';
                                item.excelCrm = this.mergeCrmObject[item.key] || '';
                            }
                            // 二级来源要勾选一级来源
                            if (item.key === 'sourceFrom') {
                                // item.checked = item.crm !== item.excelCrm && !!item.excelCrm && !item.excelCrmErrText && !this.mergeCrmObject.sourceText;
                                item.disabled = !item.excelCrm || item.crm === item.excelCrm || !!item.excelCrmErrText || !!this.mergeCrmObject.sourceText;
                            } else {
                                // item.checked = item.crm !== item.excelCrm && !!item.excelCrm && !item.excelCrmErrText;
                                item.disabled = !item.excelCrm || item.crm === item.excelCrm || !!item.excelCrmErrText;
                            }
                        });
                        if (this.dataList.find((it) => {
                            return it.key === 'sourceFrom';
                        }).disabled) {
                            this.dataList = this.dataList.map((item) => {
                                if (item.key === 'source') {
                                    return {
                                        ...item,
                                        disabled: true,
                                        // checked: false,
                                    };
                                }
                                return {
                                    ...item,
                                };
                            });
                        }

                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            async mergeCrm() {
                const sourceFromItem = this.dataList.find((item) => {
                    return item.key === 'sourceFrom';
                });
                const sourceItem = this.dataList.find((item) => {
                    return item.key === 'source';
                });


                if (!sourceFromItem.checked && sourceItem.checked) {
                    this.$Toast({
                        message: '不能够仅修改一级来源',
                        type: 'error',
                    });
                    return;
                }
                if (sourceFromItem.checked && sourceItem.excelCrm !== sourceItem.crm && !sourceItem.checked) {
                    this.$Toast({
                        message: '一级来源不相同，不能够仅修改二级来源',
                        type: 'error',
                    });
                    return;
                }
                let postData = this.getPostData();
                postData = {
                    ...postData,
                    ...postData.address,
                };
                this.dataList.map((item) => {
                    if (item.checked && !item.disabled) {
                        const {
                            address = {}, addressDetail = '', sourceFrom = '', source = '', sourceName = '', sourceId = '', sourceFromId = '', countryCode = defaultCountryCode,
                        } = this.mergeCrmObject;
                        if (['name', 'mobile','sex','remark','idCard'].includes(item.key)) {
                            postData[item.key] = item.excelCrm;
                        }
                        if (['address'].includes(item.key)) {
                            if (this.isFromErrList) {
                                postData.addressCityId = this.mergeCrmObject.addressCityId || '';
                                postData.addressCityName = this.mergeCrmObject.addressCityName || '';
                                postData.addressProvinceId = this.mergeCrmObject.addressProvinceId || '';
                                postData.addressProvinceName = this.mergeCrmObject.addressProvinceName || '';
                                postData.addressDistrictId = this.mergeCrmObject.addressDistrictId || '';
                                postData.addressDistrictName = this.mergeCrmObject.addressDistrictName || '';
                            } else {
                                postData.addressCityId = address.addressCityId || '';
                                postData.addressCityName = address.addressCityName || '';
                                postData.addressProvinceId = address.addressProvinceId || '';
                                postData.addressProvinceName = address.addressProvinceName || '';
                                postData.addressDistrictId = address.addressDistrictId || '';
                                postData.addressDistrictName = address.addressDistrictName || '';
                            }
                            postData.addressDetail = addressDetail;
                            delete postData.address;
                        }
                        if (item.key === 'age') {
                            postData.birthday = item.excelCrm;
                        }

                        if (item.key === 'mobile') {
                            postData.mobile = item.excelCrm;
                            postData.countryCode = countryCode;
                        }

                        if (item.key === 'source') {
                            if (this.isFromErrList) {
                                if (['医生推荐', '员工推荐', '顾客推荐', '转诊医生'].includes(sourceName)) {
                                    postData.sourceId = sourceId || '';
                                } else {
                                    postData.sourceId = sourceFrom || sourceId || '';
                                }
                            } else {
                                if (['医生推荐', '员工推荐', '顾客推荐', '转诊医生'].includes(source)) {
                                    postData.sourceId = sourceId || '';
                                } else {
                                    postData.sourceId = sourceFromId || sourceId || '';
                                }
                            }
                        }
                        if (item.key === 'sourceFrom') {
                            if (this.isFromErrList) {
                                if (['医生推荐', '员工推荐', '顾客推荐', '转诊医生'].includes(sourceName)) {
                                    postData.sourceFrom = sourceFrom;
                                } else {
                                    postData.sourceFrom = null;
                                }
                            } else {
                                if (['医生推荐', '员工推荐', '顾客推荐', '转诊医生'].includes(source)) {
                                    postData.sourceFrom = sourceFromId;
                                } else {
                                    postData.sourceFrom = null;
                                }
                            }
                        }
                    }
                });
                // 原推荐人是符合老带新规则的推荐人
                if (this.referrerList.includes(this.patientInfo.patientSource?.name) && (this.dataList.find((it) => {
                    return it.key === 'source';
                }).checked || this.dataList.find((it) => {
                    return it.key === 'sourceFrom';
                }).checked)) {
                    const cancelFun = () => {
                        console.log('取消不更新');
                        this.showDialog = false;
                    };
                    const confirmFun = () => {
                        this.submit(postData);
                    };
                    const value = [
                        {
                            label: this.mergeCrmObject.source,
                            value: postData.sourceId,
                        },
                        {
                            label: this.mergeCrmObject.sourceFrom,
                            value: postData.sourceFrom,
                        },
                    ];

                    this.handleReferrer(value, this.patientInfo, confirmFun, cancelFun);
                    return;
                }
                await this.submit(postData);
            },
            async submit(postData) {
                try {
                    const { data = {} } = await CrmAPI.updatePatientInfo(this.patientInfo.id, postData);
                    if (data) {
                        this.$Toast({
                            message: '修改患者信息成功',
                            type: 'success',
                        });
                        this.$emit('mergeCrm', this.mergeCrmObject);
                        this.showDialog = false;
                    }
                } catch (error) {
                    this.$Toast({
                        message: '修改患者失败',
                        type: 'error',
                    });
                    console.log(error);
                }
            },
            getPostData() {
                const postData = {
                    name: '',
                    mobile: '',
                    countryCode: defaultCountryCode,
                    sex: '男',
                    birthday: '',
                    idCard: '',
                    company: '',
                    marital: '',
                    weight: '',
                    profession: '',
                    sn: '',
                    visitReason: '',
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    sourceId: null,
                    sourceFrom: null,
                    address: {
                        addressCityId: '',
                        addressCityName: '',
                        addressProvinceId: '',
                        addressProvinceName: '',
                        addressDistrictId: '',
                        addressDistrictName: '',
                    },
                    addressGeo: '',
                    addressDetail: '',
                    medicalRecord: {
                        pastHistory: '',
                        personalHistory: '',
                        physicalExamination: '',
                        allergicHistory: '',
                        familyHistory: '',
                    },
                    remark: '',
                    ethnicity: '',
                    consultantId: '',
                    memberTypeId: '',
                    // 首评治疗师
                    primaryTherapistId: '',
                    primaryTherapistName: '',
                    // 责任治疗师
                    dutyTherapistId: '',
                    dutyTherapistName: '',
                };
                if (this.patientInfo) {
                    // 有原始数据，此时为患者信息修改
                    const {
                        name,
                        mobile,
                        countryCode,
                        sex,
                        idCard,
                        company,
                        marital,
                        weight,
                        profession,
                        sn,
                        age,
                        patientSource,
                        address,
                        pastHistory,
                        allergicHistory,
                        remark,
                        ethnicity,
                        consultantId = '',
                        visitReason,
                        memberInfo = {},
                        primaryTherapistId,
                        primaryTherapistName,
                        dutyTherapistId,
                        dutyTherapistName,
                    } = this.patientInfo;
                    let { birthday } = this.patientInfo;
                    if (!birthday && (age.year || age.month || age.day)) {
                        birthday = age2birthday(age);
                    }
                    postData.name = name || '';
                    postData.mobile = mobile || '';
                    postData.countryCode = countryCode || defaultCountryCode;
                    postData.sex = sex || '男';
                    postData.birthday = birthday || '';
                    postData.idCard = idCard || '';
                    postData.company = company || '';
                    postData.marital = marital || '';
                    postData.weight = weight || '';
                    postData.profession = profession || '';
                    postData.sn = sn || '';
                    postData.ethnicity = ethnicity || '';
                    postData.consultantId = consultantId || '';
                    postData.visitReason = visitReason || '';
                    postData.memberTypeId = memberInfo?.memberTypeInfo?.memberTypeId || '';
                    if (age) {
                        postData.age.year = age.year || '';
                        postData.age.month = age.month || '';
                        if (!age.year && !age.month) {
                            postData.age.day = age.day || 0;
                        } else {
                            postData.age.day = age.day || '';
                        }
                    }
                    if (patientSource) {
                        postData.sourceId = patientSource.id || null;
                        postData.sourceFrom = patientSource.sourceFrom || null;
                    }
                    if (address) {
                        postData.address.addressCityId = address.addressCityId || '';
                        postData.address.addressCityName = address.addressCityName || '';
                        postData.address.addressProvinceId = address.addressProvinceId || '';
                        postData.address.addressProvinceName = address.addressProvinceName || '';
                        postData.address.addressDistrictId = address.addressDistrictId || '';
                        postData.address.addressDistrictName = address.addressDistrictName || '';
                        postData.addressGeo = address.addressGeo || '';
                        postData.addressDetail = address.addressDetail || '';
                    }
                    postData.medicalRecord.pastHistory = pastHistory || '';
                    postData.medicalRecord.allergicHistory = allergicHistory || '';
                    postData.remark = remark || '';
                    postData.primaryTherapistId = primaryTherapistId || '';
                    postData.primaryTherapistName = primaryTherapistName || '';
                    postData.dutyTherapistId = dutyTherapistId || '';
                    postData.dutyTherapistName = dutyTherapistName || '';
                }
                return postData;
            },
            convertToStandardDate(input) {
                if (!input) {
                    return input;
                }
                const today = new Date();
                const currentYear = today.getFullYear();

                if (!isNaN(Number(input))) {
                    const yearDiff = Number(input);
                    if (yearDiff >= 1 && yearDiff <= 124) {
                        const year = currentYear - yearDiff;
                        return `${year}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;
                    }
                }
                // eslint-disable-next-line no-useless-escape
                const regex = /^(\d{4})[-\/]?(0\d|1[0-2])?[-\/]?(0\d|[12]\d|3[01])?$/;
                const match = input?.match(regex);

                if (match) {
                    const year = match[1];
                    const month = match[2] ? match[2].padStart(2, '0') : '01';
                    const day = match[3] ? match[3].padStart(2, '0') : '01';
                    return `${year}-${month}-${day}`;
                }
                // eslint-disable-next-line no-useless-escape
                const regex2 = /^(\d{4})[-\/]?(0?[1-9]|1[0-2])[-\/]?(0?[1-9]|[12]\d|3[01])$/;
                const match2 = input.match(regex2);
                if (match2) {
                    const year = match2[1];
                    const month = match2[2] ? match2[2].padStart(2, '0') : '01';
                    const day = match2[3] ? match2[3].padStart(2, '0') : '01';
                    return `${year}-${month}-${day}`;
                }
                return '';
            },
        },
    };
</script>
