<template>
    <abc-container
        :class="{
            'merge-border': activeTab.merge,
            'crm-module-table': hiddenScroll,
        }"
        has-header-container
        :has-left-container="hasLeftContainer"
        :has-right-container="hasRightContainer"
        class="crm-module"
    >
        <abc-container-header>
            <abc-tabs-v2
                style="padding-left: 8px;"
                size="huge"
                :option="tabOptions"
                :value="activeTab.value"
                @change="changeTab"
            ></abc-tabs-v2>
        </abc-container-header>

        <!--右侧form区域-->
        <router-view></router-view>
    </abc-container>
</template>

<script>
    import { mapGetters } from 'vuex';
    import ModulePermission from 'views/permission/module-permission';
    import { getRouterChildren } from 'router/filter-router';
    import AbcUiThemeMixin from 'views/common/abc-ui-theme-mixin';

    export default {
        name: 'CrmModule',
        mixins: [ModulePermission, AbcUiThemeMixin],
        computed: {
            ...mapGetters('crm', [
                'visibleRevisitAlertDot', // 是否显示患者红点
                'todayTotalCount',
            ]),
            tabOptions() {
                const { routes } = this.$router.options;
                return getRouterChildren(routes, 'crm')
                    .filter((item) => !item.meta.hidden)
                    .map((item) => ({
                        label: item.name,
                        value: item.name,
                        path: item.path,
                        redirectName: item.meta.redirectName,
                        merge: !!item.meta.merge,
                        noticeNumber: item.name.indexOf('患者随访') !== -1 && this.visibleRevisitAlertDot ? this.todayTotalCount : 0,
                    }));
            },
            activeTab() {
                const target = this.tabOptions.find((item) => {
                    return this.$route.name === item.value || this.$route.name === item.redirectName;
                });
                return target || this.tabOptions[0];
            },
            hasLeftContainer() {
                if (this.activeTab?.path.indexOf('patient-visit') > -1) {
                    return true;
                }
                if (this.activeTab?.path.indexOf('patient-files') > -1) {
                    return true;
                }
                return false;
            },
            hasRightContainer() {
                const { tab = 0 } = this.$route.query;
                if (this.activeTab?.path.indexOf('patient-visit') > -1 && tab !== 3) {
                    return true;
                }
                return false;
            },
            hiddenScroll() {
                if (this.activeTab?.path.indexOf('cardholder-manage') > -1) {
                    return true;
                }
                if (this.activeTab?.path.indexOf('member-manage') > -1) {
                    return true;
                }
                return false;
            },
        },
        async created() {
            this.$store.dispatch('fetchChainSubClinics');
        },
        methods: {
            changeTab(index, item) {
                this.$router.replace({
                    name: item.value,
                });
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .crm-module {
        #abc-container-header {
            .abc-tabs {
                width: 100%;
                height: 56px;
                margin: 0 auto;
                line-height: 56px;
                background-color: #ffffff;
            }
        }

        &-table {
            #abc-container-center {
                overflow-y: unset !important;

                .member-manage {
                    margin-top: 8px !important;
                }

                .cardholder-manage {
                    margin-top: 8px !important;
                }
            }
        }
    }
</style>
