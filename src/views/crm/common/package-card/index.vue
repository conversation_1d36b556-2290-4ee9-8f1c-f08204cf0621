<template>
    <div v-if="showCard" ref="view-card" class="package-card">
        <div v-if="$slots.default" ref="card-btn-ref" class="package-card__card-btn">
            <slot></slot>
        </div>
        <div
            v-else
            ref="card-btn-ref"
            class="card-btn package-card__card-btn"
            @click="visiblePatientCard = !visiblePatientCard"
        >
            <abc-icon icon="profile" size="14"></abc-icon>
        </div>
        <div
            v-show="visibleBaseCard"
            ref="view-card-popper"
            v-abc-click-outside="onClickOutside"
            class="card-box"
            :class="{ 'crm-module__package-card__card-box': fixed }"
        >
            <base-card
                v-if="visibleBaseCard"
                ref="baseCard"
                :id-card-linkage-switch="idCardLinkageSwitch"
                :transmit="false"
                :loading-info="loadingInfo"
                :patient-info="patientInfo"
                :source-list="sourceChildList"
                :patient-source-type="patientSourceType"
                :show-tags="showTags"
                :selected-ids="selectedIds"
                :change-tags="changeTags"
                :is-can-see-patient-mobile="isCanSeePatientMobile"
                @switch-tag="onSwitchTag"
                @close="visibleBaseCard = false"
                @fetch-info="resetPatientOverview"
                @cache-base="onCacheBaseInfo"
                @bind-files="visibleBindFiles = true"
                @open-visit-source="isShowVisitSourceDialog = true"
                @open-child-health="visibleChildHealth = true"
                @open-family-doctor="visibleFamilyDoctor = true"
                @update-popover="handleUpdatePopper"
            ></base-card>
        </div>
        <!-- 就诊来源管理弹窗 -->
        <visit-source-dialog
            v-if="isShowVisitSourceDialog"
            :is-show.sync="isShowVisitSourceDialog"
            :patient-source-type="patientSourceType"
            @close="isShowVisitSourceDialog = false"
        ></visit-source-dialog>
        <dialog-bind-files
            v-if="visibleBindFiles"
            v-model="visibleBindFiles"
            :patient-info="patientInfo"
            @success="
                visibleBindFiles = false;
                fetchPatientOverview();
            "
        ></dialog-bind-files>
        <dialog-child-health
            v-if="visibleChildHealth"
            v-model="visibleChildHealth"
            :patient-info="patientInfo"
            @success="
                visibleChildHealth = false;
                fetchPatientOverview();
            "
        ></dialog-child-health>
    </div>
</template>

<script>
    import CrmAPI from 'api/crm';
    import Popper from 'utils/vue-popper';
    import BaseCard from './base-card.vue';
    import DialogBindFiles from './dialog-bind-files2.vue';
    import VisitSourceDialog from 'views/registration/visit-source-dialog';

    import { RecommendService } from '@/service/recommend';


    import {
        mapState, mapMutations, mapGetters,
    } from 'vuex';
    export default {
        components: {
            BaseCard,
            DialogBindFiles,
            DialogChildHealth: () => import('./dialog-child-health.vue'),
            VisitSourceDialog,
        },
        mixins: [Popper],
        provide() {
            return {
                main: this,
            };
        },
        props: {
            // 患者id，对已经存在的患者进行患者信息编辑
            patientId: {
                type: String,
                default: '',
            },
            // 当为true时，zIndex: 2000
            fixed: {
                type: Boolean,
                default: false,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                loadingInfo: false, // 患者信息拉取中
                patientInfo: {}, // 患者信息详情
                sourceChildList: [], // 首诊来源数据
                patientSourceType: [],
                visiblePatientCard: false, // 详情显示，点击展示
                visibleBindFiles: false, // 可选健康档案列表
                visibleChildHealth: false, // 儿保健康档案详情
                visibleFamilyDoctor: false, // 家庭医生详情
                isShowVisitSourceDialog: false,
            };
        },
        computed: {
            ...mapState('crm', [
                'originLabels', // 全部标签
                'sourceList', // 来源列表
                'doctorList', // 医生列表
                'employeeList', // 人员列表
            ]),
            ...mapGetters([
                'isEnableChildHealth', // 诊所是否开通儿保
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            // 是否开启身份证和生日年龄联动 1开启 0不开启
            idCardLinkageSwitch() {
                return this.viewDistributeConfig.Registration.idCardLinkageSwitch;
            },
            // 是否显示详情
            visibleBaseCard: {
                get() {
                    // 明确有详情返回值后才显示卡片
                    const flag = !!this.patientInfo.id;
                    return this.$slots.default ? this.value && flag : this.visiblePatientCard && flag;
                },
                set(value) {
                    if (this.$slots.default) {
                        this.$emit('input', value);
                    } else {
                        this.visiblePatientCard = value;
                    }
                },
            },
            // 是否可以展示患者卡片
            showCard() {
                // 匿名患者不支持患者信息编辑
                return this.patientId && this.patientId !== '00000000000000000000000000000000';

            },
            // 患者标签展示，首先处理标签数据方便查找，再兼容id、tagId这种后端问题，得到有效的患者标签
            showTags() {
                let tags = [];
                this.originLabels.forEach((item) => {
                    if (item.tags) {
                        tags = [...tags, ...item.tags];
                    }
                });
                if (this.patientInfo.tags) {
                    return this.patientInfo.tags
                        .map((item) => {
                            const target = tags.find((one) => one.id === item.tagId);
                            if (target) {
                                return {
                                    ...item,
                                    tagName: target.name,
                                };
                            }
                            return false;

                        })
                        .filter((item) => item !== false);
                }
                return [];

            },
            // 已经选中的标签ids
            selectedIds() {
                return this.showTags.map((item) => item.tagId);
            },
            // 标签是否有更改
            changeTags() {
                return false;
            },
        },
        watch: {
            patientSourceType: {
                handler(val) {
                    if (val) {
                        this.options = val.map((item) => {
                            if (item.name === '顾客推荐') {
                                return {
                                    ...item,
                                    slot: '新增患者档案',
                                };
                            }

                            return { ...item };
                        });
                    }
                },
                immediate: true,

                deep: true,
            },
            value(newValue) {
                if (this.fixed) {
                    this.showPopper = newValue;
                }
            },
            patientId: {
                handler(newValue) {
                    if (newValue && newValue !== '00000000000000000000000000000000') {
                        this.fetchPatientOverview();
                    }
                },
                immediate: true,
            },
            visibleBaseCard: {
                handler(newValue) {
                    if (this.fixed) {
                        this.$emit('input', newValue);
                    }
                    this.$emit('on-visible-base-card-change', newValue);
                },
            },
        },
        created() {
            // 拉取来源相关信息
            this.getListSource();
        },
        mounted() {
            if (this.fixed) {
                this.referenceElm = this.$refs['view-card'];
                this.popperElm = this.$refs['view-card-popper'];
                this.createPopper();
            }
        },
        updated() {
            this.handleUpdatePopper();
        },
        methods: {
            ...mapMutations('crm', ['updateSourceList', 'updateDoctorList', 'updateEmployeeList']),
            handleUpdatePopper() {
                if (this.fixed) {
                    this.updatePopper && this.updatePopper();
                }
            },
            /**
             * 当点击外边时判断是否有效
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} mousedown 按下对象
             * @param {Object} mouseup 弹起对象
             */
            onClickOutside(mousedown, mouseup) {
                const baseCardVm = this.$refs.baseCard;
                const {
                    saveError,noChangeData,editor,
                } = baseCardVm || {};
                if (this.visibleBindFiles || this.visibleChildHealth || !this.visibleBaseCard || this.isShowVisitSourceDialog || saveError || editor && !noChangeData) {
                    if (saveError) {
                        baseCardVm.saveError = false;
                    }
                    // 当打开弹窗时，不关闭
                    return;
                }

                const classNames = [
                    'card-btn',
                    'view-labels',
                    'abc-date-picker-popper',
                    'panel-popper',
                    'date-navigator-dropdown-wrapper',
                    'source-select-ul',
                    'child-box',
                    'profession-options',
                    'address-selector_popover-wrapper',
                    'cascader-options-wrapper',
                    'crm-module__package-social__card-box',
                    'country-code-select__wrapper',
                    'country-code-select__item',
                    'cert-type-select__item',
                    'ethnicity-options',
                ];
                const cardBtnDom = this.$refs['card-btn-ref'];
                let exit = false;
                let isTarget = true;
                const mouseupPath = this.getEventPath(mouseup);
                if (mouseupPath) {
                    mouseupPath.forEach((item) => {
                        if (item.classList) {
                            const classList = Array.from(item.classList);
                            classNames.forEach((one) => {
                                if (Array.from(classList).includes(one)) {
                                    exit = true;
                                }
                            });
                            if (Array.from(classList).includes('package-card__card-btn') && item !== cardBtnDom) {
                                isTarget = false;
                            }
                        }
                    });
                }
                if (!exit || !isTarget) {
                    this.visibleBaseCard = false;
                }
            },
            getEventPath(evt) {
                if (!evt) return '';
                return evt.path || (evt.composedPath && evt.composedPath()) || '';
            },
            /**
             * 拉取旧患者预览信息
             * <AUTHOR>
             * @date 2020-06-29
             */
            async fetchPatientOverview() {
                if (!this.showCard) return;
                this.loadingInfo = true;
                try {
                    const params = {
                        wx: 1,
                        childCareRecords: 1,
                    };
                    if (this.patientId) {
                        const { data } = await CrmAPI.fetchPatientOverviewV2(this.patientId, params);
                        this.patientInfo = data;
                        this.$emit('init-fetch-info', this.patientInfo);
                        this.$emit('change', this.patientInfo, true);
                    }
                } catch (error) {
                    console.log('fetchPatientOverview error', error);
                }
                this.loadingInfo = false;
            },
            /**
             * 当处理数据透传到上层时
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} patientInfo 患者详情信息
             */
            onCacheBaseInfo(patientInfo) {
                this.visibleBaseCard = false;
                const cachePatientInfo = {
                    ...patientInfo,
                    tags: this.patientInfo.tags || [],
                };
                this.$emit('fetch-info', cachePatientInfo);
                this.$emit('change', cachePatientInfo);
            },
            /**
             * 直接赋值患者预览信息
             * <AUTHOR>
             * @date 2020-06-29
             * @param {Object} patientInfo 患者详情信息
             */
            resetPatientOverview(patientInfo) {
                this.patientInfo = patientInfo;
                this.visibleBaseCard = false;
                this.$emit('fetch-info', patientInfo);
                this.$emit('change', patientInfo);
            },
            // resetDentistyPatientOverview(patientInfo) {
            //     this.patientInfo = patientInfo;
            //     this.$emit('fetch-info', patientInfo);
            //     this.$emit('change', patientInfo);
            // },
            /**
             * 当处理tag操作
             * <AUTHOR>
             * @date 2020-06-29
             * @param {any} tag
             */
            async onSwitchTag(tag) {
                try {
                    if (this.selectedIds.includes(tag.id)) {
                        // 存在-此时删除该标签
                        await CrmAPI.deletePatientTag(this.patientInfo.id, tag.id);
                    } else {
                        // 不存在-此时打上标签
                        await CrmAPI.addPatientsTags({
                            patients: [this.patientInfo.id],
                            tags: [
                                {
                                    tagId: tag.id,
                                    tagName: tag.name,
                                },
                            ],
                        });
                    }
                    await this.getPatientTags();
                    this.$emit('change', this.patientInfo);
                } catch (error) {
                    console.log('clickHandleTag error', error);
                }
            },
            /**
             * 拉取患者最新标签
             * <AUTHOR>
             * @date 2020-06-29
             */
            async getPatientTags() {
                try {
                    const { data } = await CrmAPI.fetchPatientTags(this.patientInfo.id);
                    Object.assign(this.patientInfo, {
                        tags: data,
                    });
                } catch (error) {
                    console.log('getPatientTags error', error);
                }
            },
            /**
             * 拉取首诊来源数据
             * <AUTHOR>
             * @date 2020-06-29
             */
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();

                }
                this.patientSourceType = this.sourceChildList = RecommendService.getInstance().cascaderOptions;
            },
            async fetchAllDoctor() {
                await RecommendService.getInstance().structureOriginOptions();
                this.patientSourceType = this.sourceChildList = RecommendService.getInstance().cascaderOptions;
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .package-card {
        position: relative;
        width: 32px;
        height: 32px;

        .card-btn {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border: 1px solid $P1;
            border-radius: var(--abc-border-radius-small);

            .iconfont {
                font-size: 16px;
                color: $T1;
            }
        }

        .card-box {
            position: absolute;
            top: 30px;
            left: 0;
            z-index: 8;
            display: inline-block;
        }
    }

    .crm-module__package-card__card-box {
        z-index: 2000;
    }
</style>
