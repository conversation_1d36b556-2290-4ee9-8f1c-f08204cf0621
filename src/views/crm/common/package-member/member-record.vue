<template>
    <abc-dialog
        title="余额变更记录"
        class="crm-module__package-member__member-record"
        content-styles="height: 602px;"
        size="hugely"
        :value="true"
        :show-header-border-bottom="!useAbcUIV2"
        append-to-body
        @input="(val) => $emit('input', val)"
    >
        <abc-layout preset="dialog-table">
            <abc-layout-content>
                <abc-table
                    :render-config="TableConfig.record"
                    :data-list="showDataList"
                    :loading="loading"
                    :pagination="tablePagination"
                    @pageChange="onChangePage"
                >
                    <template #topHeader>
                        <abc-flex class="header-record">
                            <abc-space :size="24" align="center">
                                <profile :patient="patient" space-direction="horizontal" :is-can-see-patient-mobile="isCanSeePatientMobile"></profile>
                                <abc-space align="center">
                                    <div>
                                        <abc-text theme="gray">
                                            本金金额
                                        </abc-text>
                                        <div class="content">
                                            <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>
                                            <span style="font-size: 14px; font-weight: bold;">{{ patient.principal.toFixed(2) }}</span>
                                        </div>
                                    </div>
                                    <div>
                                        <abc-text theme="gray">
                                            赠金金额
                                        </abc-text>
                                        <div class="content">
                                            <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>
                                            <span style="font-size: 14px; font-weight: bold;">{{ patient.present.toFixed(2) }}</span>
                                        </div>
                                    </div>
                                </abc-space>
                            </abc-space>
                        </abc-flex>
                    </template>
                    <template #created="{ trData }">
                        <abc-table-cell>
                            {{ trData.createdWording }}
                        </abc-table-cell>
                    </template>
                    <template #createdType="{ trData }">
                        <abc-table-cell>
                            {{ trData.actionWording }}
                        </abc-table-cell>
                    </template>
                    <template #amount="{ trData }">
                        <abc-table-cell>
                            {{ trData.amountWording }}
                        </abc-table-cell>
                    </template>
                    <template #principal="{ trData }">
                        <abc-table-cell>
                            {{ trData.principalWording }}
                        </abc-table-cell>
                    </template>
                    <template #bonus="{ trData }">
                        <abc-table-cell>
                            {{ trData.presentWording }}
                        </abc-table-cell>
                    </template>
                    <template #present="{ trData }">
                        <abc-table-cell>
                            {{ trData.presentBalanceWording }}
                        </abc-table-cell>
                    </template>
                    <template #createdUser="{ trData }">
                        <abc-table-cell>
                            <span v-abc-title.ellipsis="trData.patientNameWording"></span>
                        </abc-table-cell>
                    </template>
                    <template #presentBonus="{ trData }">
                        <abc-table-cell>
                            {{ trData.principalBalanceWording }}
                        </abc-table-cell>
                    </template>
                    <template #seller="{ trData }">
                        <abc-table-cell>
                            {{ trData.sellerUserNameWording }}
                        </abc-table-cell>
                    </template>
                    <template #chargeComment="{ trData }">
                        <abc-table-cell>
                            <abc-popover
                                v-if="trData.chargeCommentWording && trData.chargeCommentWording.length > 4"
                                :open-delay="500"
                                placement="top"
                                trigger="hover"
                                style="cursor: pointer;"
                                theme="yellow"
                            >
                                <span
                                    slot="reference"
                                >{{ `${trData.chargeCommentWording.slice(0,4)}...` }}</span>
                                <span class="past-history">{{ trData.chargeCommentWording }}</span>
                            </abc-popover>
                            <span v-else class="past-history">{{ trData.chargeCommentWording }}</span>
                        </abc-table-cell>
                    </template>
                    <template #payMode="{ trData }">
                        <abc-table-cell>
                            <template v-if="trData.thirdPartTransactionId">
                                <pr-popper placement="right-start" :visible-arrow="false" width="auto">
                                    {{ trData.payModeName }}<i class="icon iconfont cis-icon-info_bold" style="margin-left: 5px; color: #d9dbe3;"></i>
                                    <div slot="popper" class="member-pay-mode-popper">
                                        <span>{{ trData.type === 1 ? `微${$app.institutionTypeWording}线上支付` : '微信线上快速退款' }}</span>
                                        <span>微信商户订单号：{{ trData.thirdPartTransactionId }}</span>
                                    </div>
                                </pr-popper>
                            </template>
                            <template v-else>
                                {{ trData.payModeName }}
                            </template>
                        </abc-table-cell>
                    </template>
                    <template #operator="{ trData }">
                        <abc-table-cell>
                            {{ trData.createdByNameWording }}
                        </abc-table-cell>
                    </template>
                    <template #print="{ trData }">
                        <abc-table-cell>
                            <abc-check-access>
                                <abc-button
                                    v-if="trData.type === 1 || trData.type === -1"
                                    theme="text"
                                    size="small"
                                    @click="onClickPrint(trData)"
                                >
                                    打印
                                </abc-button>
                            </abc-check-access>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-layout-content>
        </abc-layout>
    </abc-dialog>
</template>

<script>
    import Profile from './profile';
    import PrPopper from 'views/layout/pr-popper';
    import TableConfig from './table.js';
    import fecha from 'utils/fecha';
    import { add } from 'utils/math';
    import { paddingMoney } from 'utils/index';
    import MemberApi from 'api/member';
    import { getAbcPrintOptions } from '@/printer/print-handler.js';
    import AbcPrinter from '@/printer/index.js';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import { mapGetters } from 'vuex';
    export default {
        name: 'MemberRecord',
        components: {
            Profile,
            PrPopper,
            AbcCurrencySymbolIcon,
        },
        props: {
            value: Boolean,
            patient: {
                type: Object,
                required: true,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                TableConfig,
                loading: false,
                tablePagination: {
                    showTotalPage: true,
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
                dataList: [],

                isFirstPrint: true,
                printLoading: false,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            useAbcUIV2() {
                return this.viewDistributeConfig.useAbcUIV2;
            },
            // 数据格式化展示
            showDataList() {
                return this.dataList.map((item) => {
                    const {
                        patientOrder, sellerUserName, createdByName,chargeComment,
                    } = item;
                    const { patientName } = patientOrder || {};
                    const itemInfo = {
                        ...item,
                        createdWording: fecha.format(item.created, 'YYYY-MM-DD HH:mm:ss'),
                        actionWording: this.getActionWording(item.type, item.action),
                        amountWording: this.getAmountWording(item.type, add(item.principal, item.present)),
                        principalWording: this.getAmountWording(item.type, item.principal),
                        presentWording: this.getAmountWording(item.type, item.present),
                        presentBalanceWording: paddingMoney(item.presentBalance),
                        patientNameWording: patientName || '',
                        principalBalanceWording: paddingMoney(item.principalBalance),
                        sellerUserNameWording: sellerUserName || '不指定',
                        chargeCommentWording: chargeComment || '',
                        createdByNameWording: createdByName || '不指定',
                    };
                    return itemInfo;
                });
            },
        },
        created() {
            this.fetchMemberRecord();
        },
        methods: {
            /**
             * 获取会员交易数据
             * <AUTHOR>
             * @date 2021-03-10
             */
            async fetchMemberRecord() {
                this.loading = true;
                try {
                    const {
                        pageIndex, pageSize,
                    } = this.tablePagination;
                    const { data } = await MemberApi.records(this.patient.patientId, pageIndex, pageSize);
                    if (pageIndex === this.tablePagination.pageIndex && pageSize === this.tablePagination.pageSize) {
                        const {
                            rows, total,
                        } = data.data;
                        if (rows.length === 0 && this.tablePagination.pageIndex !== 0) {
                            this.tablePagination.pageIndex--;
                            this.fetchMemberRecord();
                        } else {
                            this.dataList = rows;
                            this.tablePagination.count = total;
                            this.pageTotal = Math.ceil(total / this.tablePagination.pageSize);
                        }
                    }
                } catch (error) {
                    console.log('fetchMemberRecord error', error);
                }
                this.loading = false;
            },
            /**
             * 当改变页数时
             * <AUTHOR>
             * @date 2021-03-10
             * @param {Number} index 页数索引
             */
            onChangePage(index) {
                this.tablePagination.pageIndex = index - 1;
                this.fetchMemberRecord();
            },
            /**
             * 获取交易类型描述
             * <AUTHOR>
             * @date 2021-03-10
             * @param {String|Number} type 类型
             * @param {String} action 动作
             * @returns {String}
             */
            getActionWording(type, action) {
                let actionWording = '';
                if (parseInt(type) === -1) {
                    actionWording = action === '退储蓄金' ? '退储蓄金' : '消费';
                } else if (parseInt(type) === 1) {
                    actionWording = action === '充值' ? '充值' : '退款';
                } else {
                    //
                }
                return actionWording;
            },
            /**
             * 获取金额描述，可正可负
             * <AUTHOR>
             * @date 2021-03-10
             * @param {String|Number} type 类型
             * @param {Number} amount 金额
             * @returns {String}
             */
            getAmountWording(type, amount) {
                let amountWording = '';
                if (parseInt(type) === -1 && parseFloat(amount) > 0) {
                    amountWording = '-';
                }
                amountWording += paddingMoney(amount);
                return amountWording;
            },
            /**
             * 当点击打印时
             * <AUTHOR>
             * @date 2021-03-10
             * @param {Object} item 打印对象
             */
            async onClickPrint(item) {
                const id = (item && item.id) || '';
                try {
                    const { data } = await MemberApi.rechargePrint(id);
                    const typeName = item.type === 1 ? '充值小票' : '退款凭证';
                    this.$nextTick(async () => {
                        const printOptions = getAbcPrintOptions(typeName, data);
                        await AbcPrinter.abcPrint(printOptions);
                        this.printLoading = false;
                    });
                } catch (error) {
                    console.log('onClickPrint error', error);
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: error.message,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .crm-module__package-member__member-record {
        .header-record {
            display: flex;
            flex-direction: row;
            align-items: flex-end;
            justify-content: flex-start;

            .member-profile {
                padding-bottom: 0;
                border: none;
            }

            .content {
                display: inline-block;
                font-size: 12px;
                color: $Y2;
                vertical-align: baseline;
            }

            .iconfont {
                font-size: 12px;
            }
        }
    }
</style>
