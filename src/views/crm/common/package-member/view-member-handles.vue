<template>
    <div class="view-member-handles">
        <dialog-member-charge
            v-if="visibleMemberCharge"
            v-model="visibleMemberCharge"
            :print-opt="printOpt"
            :item="currentItem"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="onHandleSuccess"
        ></dialog-member-charge>
        <dialog-member-refund
            v-if="visibleMemberRefund"
            v-model="visibleMemberRefund"
            :item="currentItem"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="onHandleSuccess"
        ></dialog-member-refund>
        <dialog-member-record
            v-if="visibleMemberRecord"
            v-model="visibleMemberRecord"
            :patient="currentItem"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="onHandleSuccess"
        ></dialog-member-record>
        <dialog-integral-record
            v-if="visibleIntegralRecord"
            v-model="visibleIntegralRecord"
            :patient="currentItem"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="onHandleSuccess"
        ></dialog-integral-record>
        <dialog-exchange-point
            v-if="visibleExchangePoint"
            v-model="visibleExchangePoint"
            :patient="currentItem"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="
                visibleExchangePoint = false;
                onHandleSuccess();
            "
        ></dialog-exchange-point>
        <dialog-exchange-charge-point
            v-if="visibleExchangeChargePoint"
            v-model="visibleExchangeChargePoint"
            :patient="currentItem"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="
                visibleExchangeChargePoint = false;
                onHandleSuccess();
            "
        ></dialog-exchange-charge-point>
        <dialog-member-type v-if="visibleMemberType" v-model="visibleMemberType"></dialog-member-type>
        <dialog-payment-password
            v-if="visiblePaymentPassword"
            v-model="visiblePaymentPassword"
            :is-new="false"
            :member-info="patientInfo.memberInfo || patientInfo"
            @finish="visiblePaymentPassword = false"
        ></dialog-payment-password>
        <dialog-charge-integral
            v-if="visibleChargeIntegral"
            v-model="visibleChargeIntegral"
            :patient="currentItem"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="
                visibleChargeIntegral = false;
                onHandleSuccess();
            "
        ></dialog-charge-integral>
    </div>
</template>

<script>
    const DialogMemberCharge = () => import('./charge.vue');
    const DialogMemberRefund = () => import('./refund.vue');
    const DialogMemberRecord = () => import('./member-record.vue');
    const DialogIntegralRecord = () => import('./integral-record.vue');
    const DialogExchangePoint = () => import('./dialog-exchange-point.vue');
    const DialogMemberType = () => import('./member-type.vue');
    const DialogPaymentPassword = () => import('./dialog-payment-password-v2.vue');
    const DialogChargeIntegral = () => import('./dialog-charge-integral.vue');
    const DialogExchangeChargePoint = () => import('./dialog-exchange-charge-point.vue');

    import { add } from 'utils/math';
    import Printer from 'views/print';
    import Api from 'api/member';
    import AbcPrinter from '@/printer/index.js';
    import { getAbcPrintOptions } from '@/printer/print-handler.js';

    export default {
        name: 'ViewMemberHandles',
        components: {
            DialogMemberCharge,
            DialogMemberRefund,
            DialogMemberRecord,
            DialogIntegralRecord,
            DialogExchangePoint,
            DialogMemberType,
            DialogPaymentPassword,
            DialogChargeIntegral,
            DialogExchangeChargePoint,
        },
        props: {
            isManage: {
                type: Boolean,
                default: false,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                printOpt: {
                    finishSelect: [],
                },
                isFirstPrint: true,
                printLoading: false,

                visibleMemberCharge: false, // 会员充值
                visibleMemberRefund: false, // 会员退费
                visibleMemberRecord: false, // 消费记录
                visibleIntegralRecord: false, // 积分流水
                visibleExchangePoint: false, // 积分兑换
                visibleExchangeChargePoint: false, // 积分兑换、积分发放
                visibleMemberType: false, // 会员类型
                visiblePaymentPassword: false, // 会员卡密码支付
                visibleChargeIntegral: false, // 手动发放积分

                patientInfo: null,
            };
        },
        computed: {
            memberHolder() {
                return this.visibleMemberCharge ||
                    this.visibleMemberRefund ||
                    this.visibleMemberRecord ||
                    this.visibleIntegralRecord ||
                    this.visibleExchangePoint ||
                    this.visibleExchangeChargePoint ||
                    this.visibleMemberType ||
                    this.visiblePaymentPassword ||
                    this.visibleChargeIntegral;
            },
            currentItem() {
                if (this.isManage) {
                    return this.patientInfo;
                }
                if (this.patientInfo) {
                    const currentItem = {
                        patientId: this.patientInfo.id,
                        patientName: this.patientInfo.name,
                        patientSex: this.patientInfo.sex,
                        patientAge: { ...this.patientInfo.age },
                        patientMobile: this.patientInfo.mobile,
                        patientBirthday: this.patientInfo.birthday,
                        points: this.patientInfo.points || 0,
                        pointsTotal: this.patientInfo.pointsTotal || 0,
                    };
                    // 处理会员信息
                    const { memberInfo } = this.patientInfo;
                    if (memberInfo) {
                        Object.assign(currentItem, {
                            balance: add(memberInfo.present, memberInfo.principal),
                            created: memberInfo.created,
                            fromClinicId: memberInfo.createdClinicId,
                            fromClinicName: memberInfo.createdClinicName,
                            memberTypeId: memberInfo.memberTypeInfo && memberInfo.memberTypeInfo.memberTypeId,
                            memberTypeName: memberInfo.memberTypeInfo && memberInfo.memberTypeInfo.memberTypeName,
                            present: memberInfo.present,
                            principal: memberInfo.principal,
                            memberRemark: memberInfo.remark,
                        });
                    }
                    return currentItem;
                }
                return {};

            },
        },
        updated() {
            this.$emit('changeMemberHandles', this.memberHolder);
        },
        methods: {
            handleMemberCharge(patientInfo) {
                this.isFirstPrint = true;
                this.printLoading = false;
                this.patientInfo = patientInfo;
                this.visibleMemberCharge = true;
            },
            handleMemberRefund(patientInfo) {
                this.patientInfo = patientInfo;
                this.visibleMemberRefund = true;
            },
            handleExchangePoint(patientInfo) {
                this.patientInfo = patientInfo;
                this.visibleExchangePoint = true;
            },
            handleExchangeChargePoint(patientInfo) {
                this.patientInfo = patientInfo;
                this.visibleExchangeChargePoint = true;
            },
            handleMemberRecord(patientInfo) {
                this.patientInfo = patientInfo;
                this.visibleMemberRecord = true;
            },
            handleIntegralRecord(patientInfo) {
                this.patientInfo = patientInfo;
                this.visibleIntegralRecord = true;
            },
            handleMemberType(patientInfo) {
                this.patientInfo = patientInfo;
                this.visibleExchangePoint = true;
            },
            handleMemberPassword(patientInfo) {
                this.patientInfo = patientInfo;
                this.visiblePaymentPassword = true;
            },
            handleChargeIntegral(patientInfo) {
                this.patientInfo = patientInfo;
                this.visibleChargeIntegral = true;
            },
            /**
             * 当操作成功时
             * <AUTHOR>
             * @date 2021-03-10
             * @param {String} type 操作类型
             * @param {String} id
             */
            onHandleSuccess(type, id) {
                const { cache } = Printer;
                if (type === 'charge') {
                    const { rechargeNeedPrint } = cache.get();
                    if (rechargeNeedPrint && id) {
                        this.print(id, type);
                        cache.set({ rechargeNeedPrint: false });
                    }
                } else if (type === 'refund') {
                    const { refundNeedPrint } = cache.get();
                    if (refundNeedPrint && id) {
                        this.print(id, type);
                        cache.set({ refundNeedPrint: false });
                    }
                }
                this.$emit('fetch-patient-info', type, id);
            },
            /**
             * 打印
             * <AUTHOR>
             * @date 2021-03-10
             * @param {String} id
             */
            async print(id, type) {
                try {
                    const { data } = await Api.rechargePrint(id);
                    this.$nextTick(async () => {
                        const printType = type === 'charge' ? '充值小票' : '退款凭证';
                        const printOptions = getAbcPrintOptions(printType, data);
                        await AbcPrinter.abcPrint(printOptions);
                        this.printLoading = false;
                    });
                } catch (error) {
                    const { message } = error;
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: message,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
    @import '~styles/abc-common.scss';

    .view-member-handles {
        .pay-mode {
            span {
                display: inline-block;
                height: 32px;
                margin-right: 4px;
                font-size: 14px;
                font-weight: normal;
                line-height: 30px;
                color: $T2;
                text-align: center;
                cursor: pointer;
                border: 1px solid $P3;
                border-radius: var(--abc-border-radius-small);
            }

            .cash.selected {
                color: $Y2;
                background-color: #fef4e5;
                border-color: #fed395;
            }

            i {
                float: left;
                margin-right: 5px;
                font-size: 16px;
                vertical-align: middle;
            }

            &.disabled {
                span {
                    cursor: not-allowed;
                }
            }
        }
    }
</style>
