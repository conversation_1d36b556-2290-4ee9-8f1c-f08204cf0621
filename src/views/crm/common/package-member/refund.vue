<template>
    <div class="member-refund">
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            title="退款"
            append-to-body
            class="member-refund"
            size="small"
            data-cy="member-refund-dialog"
            @close="reset"
            @open="getPrintOpt"
        >
            <crm-box-item
                style="width: 312px; margin-bottom: 16px;"
                :patient="item"
                :is-can-see-patient-mobile="isCanSeePatientMobile"
            ></crm-box-item>
            <abc-divider
                variant="dashed"
            ></abc-divider>
            <ul class="fee-bar padd-lr">
                <li>
                    <div class="label-text">
                        本金金额:
                    </div>
                    <div class="content-refund">
                        <abc-currency-symbol-icon :size="14"></abc-currency-symbol-icon>
                        <span data-cy="member-refund-principal">{{ item.principal | formatMoney }}</span>
                    </div>
                </li>
                <li>
                    <div class="label-text">
                        赠金金额:
                    </div>
                    <div class="content-refund">
                        <abc-currency-symbol-icon :size="14"></abc-currency-symbol-icon>
                        <span data-cy="member-refund-present">{{ item.present | formatMoney }}</span>
                    </div>
                </li>
            </ul>
            <abc-form
                ref="form"
                item-block
                label-position="left"
                :label-width="68"
            >
                <abc-form-item
                    label="退本金"
                    class="padd-lr"
                    :validate-event="
                        (val, next) => {
                            validatePrincipal(val, next);
                        }
                    "
                >
                    <abc-input
                        v-model="postData.principal"
                        v-abc-capital="{ paddingMoney: false }"
                        :width="244"
                        type="money"
                        data-cy="member-refund-principal-input"
                        :config="{
                            formatLength: 2 ,supportZero: true, max: 100000000,
                        }"
                        @input="changePayMode"
                    >
                        <template slot="appendInner">
                            元
                        </template>
                    </abc-input>
                </abc-form-item>
                <abc-form-item
                    label="退赠金"
                    class="padd-lr"
                    :validate-event="
                        (val, next) => {
                            next({
                                validate: val <= item.present, message: '退赠金不能大于赠金金额'
                            });
                        }
                    "
                >
                    <abc-input
                        v-model="postData.present"
                        v-abc-capital="{ paddingMoney: false }"
                        :width="244"
                        type="money"
                        data-cy="member-refund-present-input"
                        :config="{
                            max: item.present, formatLength: 2,supportZero: true
                        }"
                    >
                        <template slot="appendInner">
                            元
                        </template>
                    </abc-input>
                </abc-form-item>
                <abc-form-item
                    label="销售员"
                    class="padd-lr"
                    data-cy="member-refund-seller-select"
                    style="margin-bottom: 0;"
                >
                    <employee-selector v-model="postData.sellerUserId" :width="244" :employees="employees">
                        <abc-option :value="null" label="销售不指定"></abc-option>
                    </employee-selector>
                </abc-form-item>
            </abc-form>
            <template slot="footer">
                <abc-flex align="center" justify="space-between">
                    <abc-checkbox-group v-model="printOpt.finishSelect" :clear-data="false">
                        <label class="print-label">
                            <abc-checkbox label="退款凭证" style="margin-right: 16px;">打印退款凭证</abc-checkbox>
                        </label>
                    </abc-checkbox-group>
                    <abc-space align="center">
                        <abc-button :disabled="btnDisabled" data-cy="member-refund-confirm-button" @click="submitPrev">
                            确定
                        </abc-button>
                        <abc-button variant="ghost" data-cy="member-refund-cancel-button" @click="showDialog = false">
                            取消
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </template>
        </abc-dialog>
        <abc-dialog v-if="confirm" v-model="confirm">
            <div class="tips" style="font-size: 16px;">
                {{ item.patientName }}退款
                <span class="price">{{ paddingMoney(postData.principal || 0) }}</span> 元，赠金扣减
                <span class="price">{{ paddingMoney(postData.present || 0) }}</span> 元
            </div>
            <template slot="footer">
                <abc-flex justify="flex-end" align="center">
                    <abc-space align="center">
                        <abc-button :loading="btnLoading" @click="refund">
                            确定
                        </abc-button>
                        <abc-button variant="ghost" @click="confirm = false">
                            取消
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </template>
        </abc-dialog>
    </div>
</template>

<script>
    import CrmAPI from 'api/crm';
    import { paddingMoney } from 'utils/index';
    import Printer from 'views/print';
    // import Profile from './profile';
    import Api from 'api/member';
    import { PayModeEnum } from '@/service/charge/constants.js';
    import EmployeeSelector from 'views/layout/employee-selector/employee-selector';
    import {
        mapGetters, mapActions,
    } from 'vuex';
    import payDialog from '../package-charge-refund/index';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import CrmBoxItem from 'views/crm/common/package-member/crm-box-item.vue';


    export default {
        name: 'Refund',
        components: {
            CrmBoxItem,
            // Profile,
            EmployeeSelector,
            AbcCurrencySymbolIcon,
        },
        props: {
            value: Boolean,
            item: {
                type: Object,
                default: () => {},
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                paddingMoney,
                confirm: false,
                employees: [],
                postData: {
                    principal: '', // 本金
                    present: '', // 赠金
                    payMode: 0, // 支付方式
                    sellerUserId: null, // 销售人员ID
                    sellerUserName: '', // 销售人员name
                    lastPresent: '', // 赠金余额
                    lastPrincipal: '', // 本金余额
                },
                payModeError: false, // 选择支付方式的提示
                btnLoading: false,
                printOpt: {
                    finishSelect: [],
                },
            };
        },
        computed: {
            ...mapGetters([
                'chargeConfig', // 收费设置
            ]),
            // 支持的收费方式
            canPayList() {
                return this.chargeConfig.chargePayModeConfigs.filter((item) => {
                    if (this.$abcSocialSecurity.isOpenSocial && item.payModeId === PayModeEnum.SOCIAL_CARD) {
                        // 开启医保支付时，不允许充值会员
                        return false;
                    } if (item.payModeId === PayModeEnum.MEMBER_CARD) {
                        // 不能用会员卡余额充值会员
                        return false;
                    }
                    return true;

                });
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            btnDisabled() {
                return !Number(this.postData.principal) && !Number(this.postData.present);
            },
        },
        watch: {
            'printOpt.finishSelect': function () {
                const { cache } = Printer;
                cache.set({
                    refund: this.printOpt.finishSelect,
                    refundNeedPrint: this.printOpt.finishSelect.includes('退款凭证'),
                });
            },
            'postData.principal': {
                handler () {
                    if (!this.postData.principal) {
                        this.postData.payMode = 0;
                    }
                },
            },
            'postData.payMode': {
                handler () {
                    if (this.postData.payMode > 0) {
                        this.payModeError = false;
                    }
                },
            },
            // 当收费方式改变时，如果当前选中的收费方式已被移除，需要处理
            'chargeConfig.chargePayModeConfigs': function(val) {
                if (this.postData.payMode) {
                    const exit = val.find(({ payModeId }) => payModeId === this.postData.payMode);
                    if (!exit) {
                        this.postData.payMode = 0;
                    }
                }
            },
        },
        async created() {
            this.$store.dispatch('initChargeConfig');
            this.fetchCurrentClinicConfig();
            const { data } = await CrmAPI.fetchEmployeeList();
            this.employees = data.rows;
            this.postData.lastPresent = this.item && this.item.present;
            this.postData.lastPrincipal = this.item && this.item.principal;
        },
        methods: {
            ...mapActions(['fetchCurrentClinicConfig']),
            reset() {
                this.postData = {
                    principal: '', // 本金
                    present: '', // 赠金
                    payMode: 0, // 支付方式
                    sellerUserId: null, // 销售人员ID
                    sellerUserName: '', // 销售人员name
                };
                this.cachePrintOpt();
            },
            getPrintOpt() {
                const { cache } = Printer;
                const { refund } = cache.get();
                this.printOpt.finishSelect = refund;
            },
            cachePrintOpt() {
                const { cache } = Printer;
                this.printOpt.finishSelect = this.printOpt.finishSelect.filter((item) => {
                    return item === '退款凭证';
                });
                cache.set({
                    refund: this.printOpt.finishSelect,
                    refundNeedPrint: this.printOpt.finishSelect.includes('退款凭证'),
                });
            },
            validatePrincipal(val, next) {
                next({
                    validate: val <= this.item.principal,
                    message: '退本金不能大于本金金额',
                });
            },
            selectPayMode(type) {
                if (+this.postData.principal === 0) return false;
                this.postData.payMode = type;
            },
            submitPrev() {
                this.$refs.form.validate((val) => {
                    if (val) {
                        new payDialog({
                            title: '退款',
                            mode: 'member',
                            canPayList: this.canPayList,
                            payModeError: this.payModeError,
                            postData: this.postData,
                            submit: this.refund,
                            refresh: this.refresh,
                            close: this.close,
                            patientId: this.item.patientId,
                            hiddenPayModeList: [
                                PayModeEnum.SOCIAL_CARD,
                                PayModeEnum.MEMBER_CARD,
                                PayModeEnum.PATIENT_CARD,
                                PayModeEnum.ARREARS,
                            ],
                        }).generateDialog({ parent: this });
                    }
                });
            },
            refresh() {
                this.$emit('refresh', 'refund');
            },
            close() {
                // 关闭弹窗
                this.$emit('input', false);
            },
            async refund() {
                this.btnLoading = true;
                try {
                    // 充值时需要转换payMode
                    const { data } = await Api.refundV2(this.item.patientId, this.postData) || {};
                    // 退款成功
                    this.$Toast({
                        type: 'success',
                        message: '退款成功',
                    });
                    this.confirm = false;
                    this.showDialog = false;
                    if (this.printOpt.finishSelect.includes('退款凭证')) {
                        this.$emit('refresh', 'refund', data?.data?.id);
                        return;
                    }
                    this.$emit('refresh', 'refund');
                } catch (e) {
                    console.log('refund error', e);
                    this.$Toast({
                        type: 'error',
                        message: e.message,
                    });
                }
                this.btnLoading = false;
            },
            changePayMode() {
                if (+this.postData.principal === 0) {
                    this.postData.payMode = 0;
                }
            },
        },
    };
</script>

<style lang="scss" rel="stylesheet/scss">
    @mixin justify($width) {
        width: $width;
        text-align: justify;
        text-align-last: justify;
        text-justify: distribute-all-lines;
    }

    @import '~styles/theme';

    .member-refund {
        .abc-form {
            margin-top: 19px;
        }

        .abc-form-item {
            margin-bottom: 16px !important;
        }

        .dialog-footer {
            display: flex;
            align-content: center;
            justify-content: flex-end;

            > button {
                width: 80px;
                min-width: 80px;
                margin-left: 8px;
            }
        }

        .pay-mode {
            padding: 0 24px;
            margin-bottom: 16px;

            .cash {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                float: left;
                width: 103px;
                padding: 0;
                margin: 0 4px 8px;

                img {
                    width: 16px;
                    height: 16px;
                    margin-right: 5px;
                }

                .name {
                    display: inline-block;

                    @include justify(58px);
                }
            }

            .selected {
                color: #0787e2;
                background-color: #e6f3fc;
                border-color: #a2d2f3;
            }

            .charge-error {
                font-size: 12px;
                color: $Y2;
            }

            .customized {
                img {
                    display: none;
                }

                .name {
                    @include justify(86px);

                    text-align: center;
                    text-align-last: initial;
                }
            }
        }

        .pay-mode > p:first-child {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-start;

            > .cash {
                margin: 0 12px 8px 0;
            }

            > .cash:nth-child(5n-1) {
                margin: 0 0 8px 0;
            }
        }

        .fee-bar {
            margin-top: 19px;

            li {
                display: inline-block !important;
                margin-right: 12px;
                vertical-align: top;

                &:last-child {
                    margin-right: 0;
                }

                & > div {
                    display: inline-block;
                    vertical-align: top;
                }

                .label-text {
                    color: $T2;
                }

                .content-refund {
                    color: $Y2;

                    i {
                        font-size: 14px;
                    }

                    span {
                        margin-left: -4px;
                        font-size: 16px;
                    }
                }
            }
        }

        .price {
            color: $Y2;
        }
    }
</style>
