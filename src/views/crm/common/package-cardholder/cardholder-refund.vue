<template>
    <div>
        <abc-dialog
            v-model="showDialog"
            title="退款"
            custom-class="cardholder-record-refund"
            size="small"
            data-cy="card-refund-dialog"
            append-to-body
            @input="(val) => $emit('input', val)"
            @close="cachePrintOpt"
            @open="getPrintOpt"
        >
            <div v-abc-loading="loading">
                <crm-box-item
                    style="width: 312px; margin-bottom: 16px;"
                    :patient="patient"
                    :is-can-see-patient-mobile="isCanSeePatientMobile"
                ></crm-box-item>
                <abc-divider
                    variant="dashed"
                ></abc-divider>
                <ul class="fee-bar">
                    <li>
                        <div class="label-text">
                            本金金额:
                        </div>
                        <div class="content">
                            <abc-currency-symbol-icon :size="14"></abc-currency-symbol-icon>
                            <span data-cy="card-refund-principal">{{ card.principal | formatMoney }}</span>
                        </div>
                    </li>
                    <li>
                        <div class="label-text">
                            赠金金额:
                        </div>
                        <div class="content">
                            <abc-currency-symbol-icon :size="14"></abc-currency-symbol-icon>
                            <span data-cy="card-refund-present">{{ card.present | formatMoney }}</span>
                        </div>
                    </li>
                </ul>
                <abc-form
                    ref="form"
                    class="refund-form"
                    item-block
                    label-position="left"
                    :label-width="68"
                >
                    <abc-form-item
                        label="退本金"
                        :validate-event="
                            (val, next) => {
                                validatePayPrincipal(val, next);
                            }
                        "
                        :show-red-dot="false"
                        :required="btnDisabled"
                    >
                        <abc-input
                            v-model="postData.payPrincipal"
                            v-abc-capital="{ paddingMoney: false }"
                            :width="244"
                            type="money"
                            data-cy="card-refund-principal-input"
                            :config="{
                                formatLength: 2, supportZero: true, max: 100000000,
                            }"
                        >
                            <template slot="appendInner">
                                元
                            </template>
                        </abc-input>
                    </abc-form-item>
                    <abc-form-item
                        label="退赠金"
                        :show-red-dot="false"
                        :required="btnDisabled"
                    >
                        <abc-input
                            v-model="postData.payPresent"
                            :width="244"
                            type="money"
                            data-cy="card-refund-present-input"
                            :config="{
                                max: card.present ,formatLength: 2 ,supportZero: true
                            }"
                        >
                            <template slot="appendInner">
                                元
                            </template>
                        </abc-input>
                    </abc-form-item>
                    <abc-form-item label="销售员" required>
                        <employee-selector
                            v-model="postData.sellerId"
                            :width="244"
                            placeholder="销售员"
                            data-cy="card-refund-seller-select"
                            :employees="employees"
                        >
                            <abc-option value="" label="销售不指定"></abc-option>
                        </employee-selector>
                    </abc-form-item>
                </abc-form>
            </div>

            <template slot="footer">
                <abc-flex justify="space-between" align="center">
                    <abc-checkbox-group v-model="printOpt.finishSelect" :clear-data="false">
                        <label class="print-label">
                            <abc-checkbox label="退款凭证" style="margin-right: 16px;">打印退款凭证</abc-checkbox>
                        </label>
                    </abc-checkbox-group>
                    <abc-space align="center">
                        <abc-button
                            :disabled="btnDisabled"
                            data-cy="card-refund-confirm-button"
                            @click="confirm"
                        >
                            确定
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            data-cy="card-refund-cancel-button"
                            @click="() => $emit('input', false)"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </template>
        </abc-dialog>
    </div>
</template>

<script>
    // import Profile from '../package-member/profile';
    import payList from 'views/crm/common/package-cardholder/pay-list';
    import EmployeeSelector from 'views/layout/employee-selector/employee-selector';
    import CrmAPI from 'api/crm';
    import payDialog from '../package-charge-refund/index';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import CrmBoxItem from 'views/crm/common/package-member/crm-box-item.vue';
    import { PayModeEnum } from '@/service/charge/constants';
    import { getAbcPrintOptions } from '@/printer/print-handler';
    import AbcPrinter from '@/printer';
    import Printer from 'views/print';


    export default {
        name: 'CardholderRefund',
        components: {
            CrmBoxItem,
            // Profile,
            EmployeeSelector,
            AbcCurrencySymbolIcon,
        },
        mixins: [payList],
        props: {
            value: Boolean,
            patient: {
                type: Object,
                required: true,
                default() {
                    return {};
                },
            },
            card: {
                type: Object,
                required: true,
                default() {
                    return {};
                },
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                employees: [],
                printLoading: false,
                printOpt: {
                    finishSelect: ['退款凭证'],
                },
                postData: {
                    chargePatientId: undefined,
                    id: undefined,
                    patientId: undefined,
                    payPrincipal: '',
                    payPresent: '',
                    payType: undefined,
                    payMode: 0,
                    paySource: 1,
                    sellerId: '',
                    sellerName: undefined,
                    chargeComment: '', // 备注：退费原因
                },
                payModeError: false,
                loading: true,
            };
        },
        computed: {
            btnDisabled() {
                return !Number(this.postData.payPrincipal) && !Number(this.postData.payPresent);
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        watch: {
            'postData.payType': {
                handler() {
                    if (this.postData.payType) {
                        this.payModeError = false;
                    }
                },
            },
        },
        methods: {
            getPrintOpt() {
                const { cache } = Printer;
                const { refund } = cache.get();
                this.printOpt.finishSelect = refund;
            },
            cachePrintOpt() {
                const { cache } = Printer;
                this.printOpt.finishSelect = this.printOpt.finishSelect.filter((item) => {
                    return !!item;
                });
                cache.set({
                    refund: this.printOpt.finishSelect,
                    refundNeedPrint: this.printOpt.finishSelect.includes('退款凭证'),
                });
            },
            // 患者卡信息更新完成回调
            async onPatientCardFetchComplete() {
                try {
                    await this.fetchEmployees();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            validatePayPrincipal(val, next) {
                console.log(val,next);
                next({
                    validate: val <= this.card.principal,
                    message: '退本金不能大于本金金额',
                });
            },
            confirm() {
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        new payDialog({
                            title: '退款',
                            mode: 'card',
                            canPayList: this.canPayList,
                            payModeError: this.payModeError,
                            postData: this.postData,
                            submit: this.submit,
                            refresh: this.refresh,
                            close: this.close,
                            cardItemId: this.card.itemId,
                            patientId: this.patient.id,
                            hiddenPayModeList: [
                                PayModeEnum.SOCIAL_CARD,
                                PayModeEnum.MEMBER_CARD,
                                PayModeEnum.PATIENT_CARD,
                                PayModeEnum.ARREARS,
                            ],
                        }).generateDialog({ parent: this });
                    }
                });
            },
            refresh() {
                // 刷新
                this.$emit('refresh');
            },
            close() {
                // 关闭弹窗
                this.$emit('input', false);
            },
            async onClickPrint(id) {
                try {
                    this.printLoading = true;
                    const { data } = await CrmAPI.rechargePrint(id);
                    const printOptions = getAbcPrintOptions('退款凭证', data);
                    await AbcPrinter.abcPrint(printOptions);
                    this.printLoading = false;
                } catch (error) {
                    console.log('onClickPrint error', error);
                }
            },
            async submit() {
                this.postData.chargePatientId = this.postData.patientId = this.patient.id;
                this.postData.id = this.card.itemId;
                if (this.postData.sellerId) {
                    this.postData.sellerName = this.employees.find((employee) => employee.id === this.postData.sellerId).name;
                }

                try {
                    const { data } = await CrmAPI.cardholderRefund(this.postData);
                    this.$Toast({
                        type: 'success',
                        message: '退款成功',
                    });
                    if (this.printOpt.finishSelect.includes('退款凭证') && data?.id) {
                        this.onClickPrint(data.id);
                    }
                    // 关闭弹窗
                    this.$emit('input', false);
                    // 刷新
                    this.$emit('refresh');
                } catch (e) {
                    Object.assign(this.postData, this.$options.data().postData);
                    this.$emit('input', true);
                    this.$Toast({
                        type: 'error',
                        message: e.message,
                    });
                }
            },
            cancelPay() {
                this.postData.payType = '';
            },

            async fetchEmployees() {
                const { data } = await CrmAPI.fetchEmployeeList();
                this.employees = data.rows;
            },
            selectPayMode(type) {
                if (+this.postData.payPrincipal === 0) return false;
                this.postData.payType = type;
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/abc-common.scss';

.cardholder-record-refund {
    .refund-form {
        margin-top: 19px;
    }

    .abc-form-item {
        margin-bottom: 16px;
    }

    .abc-form-item:last-child {
        margin-bottom: 0;
    }

    .fee-bar {
        height: 14px;
        margin-top: 19px;
        line-height: 14px;

        li {
            display: inline-block;
            margin-right: 12px;
            vertical-align: top;

            &:last-child {
                margin-right: 0;
            }

            & > div {
                display: inline-block;
            }

            .label-text {
                font-size: 14px;
                font-weight: 400;
                line-height: 14px;
                color: $T2;
            }

            .content {
                color: $Y2;

                i {
                    font-size: 14px;
                }

                span {
                    margin-left: -4px;
                    font-size: 16px;
                }
            }
        }
    }

    .pay-mode {
        padding: 0;
        margin-bottom: 16px;

        span {
            display: inline-block;
            height: 32px;
            margin-right: 4px;
            font-size: 14px;
            font-weight: normal;
            line-height: 30px;
            color: $T2;
            text-align: center;
            cursor: pointer;
            border: 1px solid $P3;
            border-radius: var(--abc-border-radius-small);
        }

        .cash.selected {
            color: $theme2;
            background-color: $theme4;
            border-color: $theme2;
        }

        i {
            float: left;
            margin-right: 5px;
            font-size: 16px;
            vertical-align: middle;
        }

        &.disabled {
            span {
                cursor: not-allowed;
            }
        }

        > p:first-child {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: flex-start;

            > .cash {
                margin: 0 12px 8px 0;
            }

            > .cash:nth-child(5n-1) {
                margin: 0 0 8px 0;
            }
        }

        .cash {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            float: left;
            width: 103px;
            padding: 0;
            margin: 0 4px 8px;

            img {
                width: 16px;
                height: 16px;
                margin-right: 5px;
            }

            .name {
                display: inline-block;

                @include justify(58px);
            }
        }

        .selected {
            color: $theme2;
            background-color: $theme4;
            border-color: $theme2;
        }

        .charge-error {
            font-size: 12px;
            color: $Y2;
        }

        .customized {
            img {
                display: none;
            }

            .name {
                @include justify(86px);

                text-align: center;
                text-align-last: initial;
            }
        }
    }
}
</style>
