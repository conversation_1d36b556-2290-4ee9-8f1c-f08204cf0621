<template>
    <!--该模块已经被弃用了 新的功能请单独使用弹窗-->
    <div class="view-cardholder-handles">
        <dialog-cardholder-charge
            v-if="visibleCardholderCharge"
            ref="charge"
            v-model="visibleCardholderCharge"
            :patient="patient"
            :card="card"
            :print-opt="printOpt"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="onHandleRefresh"
        ></dialog-cardholder-charge>
        <dialog-cardholder-refund
            v-if="visibleCardholderRefund"
            ref="refund"
            v-model="visibleCardholderRefund"
            :patient="patient"
            :card="card"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="onHandleRefresh"
        ></dialog-cardholder-refund>
        <dialog-cardholder-destroy
            v-if="visibleCardholderDestroy"
            ref="destroy"
            v-model="visibleCardholderDestroy"
            :patient="patient"
            :card="card"
            :open-card-fee="openCardFee"
            :left-deduction-goods-price="leftDeductionGoodsPrice"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="onHandleRefresh"
        ></dialog-cardholder-destroy>
        <dialog-cardholder-record
            v-if="visibleCardholderRecord"
            ref="record"
            v-model="visibleCardholderRecord"
            :patient="patient"
            :card="card"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @refresh="onHandleRefresh"
        ></dialog-cardholder-record>
        <dialog-cardholder-detail
            v-if="visibleCardholderDetail"
            ref="detail"
            v-model="visibleCardholderDetail"
            :patient="patient"
            :card="card"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @destroyCard="handleDestroyCard"
            @refresh="onHandleRefresh"
        ></dialog-cardholder-detail>
        <dialog-cardholder-create
            v-if="visibleCardholderCreate"
            v-model="visibleCardholderCreate"
            :patient="patient"
            :print-opt="printOpt"
            :need-hover-popover="needHoverPopover"
            :is-can-see-patient-mobile="isCanSeePatientMobile"
            @createSuccess="handleCardholderCreateSuccess"
            @refresh="onHandleRefresh"
        ></dialog-cardholder-create>
        <dialog-cardholder-manage-family
            v-if="visibleCardholderManageFamily"
            ref="family"
            v-model="visibleCardholderManageFamily"
            :patient="patient"
            :card="card"
            :list="checkedList"
            @refresh="onHandleRefresh"
            @finish="onHandleFinish"
            @add-paitent="visibleInsertPatient = true"
        >
        </dialog-cardholder-manage-family>
        <dialog-info-patient
            v-if="visibleInsertPatient"
            ref="insert-patient"
            v-model="visibleInsertPatient"
            @success="onSuccessInsert"
        ></dialog-info-patient>
    </div>
</template>

<script>
    import DialogCardholderCharge from './cardholder-charge';
    import DialogCardholderRefund from './cardholder-refund';
    import DialogCardholderDestroy from './cardholder-destory';
    import DialogCardholderRecord from './cardholder-record';
    import DialogCardholderDetail from './cardholder-detail';
    import DialogCardholderCreate from './cardholder-create';
    import DialogCardholderManageFamily from './cardholder-manage-family';
    const DialogInfoPatient = () => import('views/crm/common/package-info/dialog-info-patient.vue');
    import CrmAPI from 'api/crm';
    import Api from 'api/member';
    import { getAbcPrintOptions } from '@/printer/print-handler.js';
    import AbcPrinter from '@/printer/index.js';
    import { mapActions } from 'vuex';
    import { PayModeEnum } from '@/service/charge/constants';

    export default {
        name: 'ViewCardholderHandles',
        components: {
            DialogCardholderCharge,
            DialogCardholderRefund,
            DialogCardholderDestroy,
            DialogCardholderRecord,
            DialogCardholderDetail,
            DialogCardholderCreate,
            DialogCardholderManageFamily,
            DialogInfoPatient,
        },
        props: {
            needHoverPopover: {
                type: Boolean,
                default: true,
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                visibleCardholderCharge: false,
                visibleCardholderRefund: false,
                visibleCardholderDestroy: false,
                visibleCardholderRecord: false,
                visibleCardholderDetail: false,
                visibleCardholderCreate: false,
                visibleCardholderManageFamily: false,
                visibleInsertPatient: false,

                patient: null,
                card: null,
                openCardFee: 0,
                leftDeductionGoodsPrice: 0,
                cardList: null,
                checkedList: null,

                printOpt: {
                    finishSelect: [],
                },
                isFirstPrint: true,
                printLoading: false,
                postData: {
                    chargePatientId: undefined,
                    id: undefined,
                    patientId: undefined,
                    payMode: 0,
                    payType: undefined,
                    sellerId: '',
                    sellerName: undefined,
                    paySource: 1,
                    refundCardFee: 0,
                },
            };
        },
        computed: {
            isNeedOpenDialog() {
                return this.visibleCardholderCharge || this.visibleCardholderRefund || this.visibleCardholderDestroy || this.visibleCardholderRecord || this.visibleCardholderDetail || this.visibleCardholderCreate || this.visibleCardholderManageFamily;
            },
        },
        updated() {
            this.$emit('changeCardHolderHandles', this.isNeedOpenDialog);
        },
        created() {
            this.initChargeConfig();
        },
        methods: {
            ...mapActions([
                'initChargeConfig',
            ]),
            async fetchPatientCardDetail(params) {
                if (this.card.itemId) {
                    const { data } = await CrmAPI.fetchPatientCardDetail(this.card.itemId, params);
                    const {
                        cardInfo,
                        patient,
                        patientPresents,
                        beginDate,
                        cardBalance,
                        created,
                        endDate,
                        present,
                        principal,
                        shareMembers,
                        remark,
                        openCardFee,
                        leftDeductionGoodsTotalPrice,
                        payTypeName,
                        sellerName,
                    } = data;

                    Object.assign(this.patient, patient);
                    Object.assign(this.card, cardInfo);

                    this.card.patientPresents = patientPresents;
                    this.card.crId = this.card.itemId;
                    this.card.beginDate = beginDate;
                    this.card.cardBalance = cardBalance;
                    this.card.created = created;
                    this.card.payTypeName = payTypeName;
                    this.card.sellerName = sellerName;
                    this.card.endDate = endDate;
                    this.card.present = present;
                    this.card.principal = principal;
                    this.card.shareMembers = shareMembers;
                    this.card.remark = remark;
                    this.openCardFee = openCardFee;
                    this.leftDeductionGoodsPrice = leftDeductionGoodsTotalPrice;
                }
            },
            async fetchPatientDetail() {
                if (this.patient.id) {
                    const { data } = await CrmAPI.fetchPatientOverviewV2(this.patient.id);
                    Object.assign(this.patient, data);
                }
            },
            async handleCardholderCharge(patient, card) {
                this.isFirstPrint = true;
                this.printLoading = false;
                this.patient = patient;
                this.card = card;
                this.visibleCardholderCharge = true;
                await this.fetchPatientCardDetail();
                if (typeof this.$refs.charge.onPatientCardFetchComplete === 'function') {
                    await this.$refs.charge.onPatientCardFetchComplete();
                }
            },
            async handleCardholderRefund(patient, card) {
                this.patient = patient;
                this.card = card;
                this.visibleCardholderRefund = true;
                await this.fetchPatientCardDetail();
                if (typeof this.$refs.refund.onPatientCardFetchComplete === 'function') {
                    await this.$refs.refund.onPatientCardFetchComplete();
                }
            },
            async handleCardholderDestroy(patient, card) {
                this.patient = patient;
                this.card = card;
                this.visibleCardholderDestroy = true;
                await this.fetchPatientCardDetail();
                if (typeof this.$refs.destroy.onPatientCardFetchComplete === 'function') {
                    await this.$refs.destroy.onPatientCardFetchComplete();
                }
            },
            async handleCardholderRecord(patient, card) {
                this.patient = patient;
                this.card = card;
                this.visibleCardholderRecord = true;
                await this.fetchPatientCardDetail();
                if (typeof this.$refs.record.onPatientCardFetchComplete === 'function') {
                    await this.$refs.record.onPatientCardFetchComplete();
                }
            },
            async handleCardholderDetail(patient, card) {
                this.patient = patient;
                this.card = card;
                this.visibleCardholderDetail = true;
                await this.fetchPatientCardDetail({ withOpenPayMode: true });
                if (typeof this.$refs.detail.onPatientCardFetchComplete === 'function') {
                    await this.$refs.detail.onPatientCardFetchComplete();
                }
            },
            async handleCardholderManageFamily(patient, card, checkedList) {
                this.patient = patient;
                this.card = card;
                this.checkedList = checkedList;
                await this.fetchPatientDetail();
                this.visibleCardholderManageFamily = true;
            },
            handleCardholderCreate(patient) {
                this.patient = patient;
                this.visibleCardholderCreate = true;
            },
            handleCardholderCreateSuccess(patient, card) {
                this.$confirm({
                    type: 'success',
                    title: '开卡成功',
                    content: `${card.name}已开卡成功，是否充值?`,
                    onConfirm: () => {
                        this.handleCardholderCharge(patient, card);
                    },
                    onCancel: () => {
                        console.log('cancel');
                    },
                });
            },
            handleDestroyCard(patient, card) {
                // 有余额
                if (card.cardBalance) {
                    return this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '卡内仍有余额，请先退款后再销卡',
                    });
                }
                if (Array.isArray(card.patientPresents)) {
                    card.patientPresents.some((item) => {
                        if (item.totalCount === null) {
                            this.flag = true;
                        } else if ((item.totalCount - item.usedCount) > 0) {
                            this.flag = true;
                        } else {
                            this.flag = false;
                        }
                    });
                }
                // 有未折扣的项目
                if (this.flag) {
                    return this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '卡内有未抵扣完的服务项目，销卡后项目将不可抵扣',
                        onConfirm: () => {
                            this.flag = false;
                            this.visibleCardholderDetail = false;
                            this.$nextTick(() => {
                                // 免费卡确认销卡
                                if (this.openCardFee > 0) {
                                    this.handleCardholderDestroy(patient, card);
                                } else {
                                    this.submit(patient, card);
                                }
                            });
                        },
                    });
                }
                // 销卡
                this.visibleCardholderDetail = false;
                if (this.openCardFee > 0) {
                    this.handleCardholderDestroy(patient, card);
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '是否确定注销本张卡',
                        onConfirm: () => {
                            this.visibleCardholderDetail = false;
                            this.$nextTick(() => {
                                // 免费卡确认销卡
                                this.submit(patient, card);
                            });
                        },
                    });
                }

            },
            // 免费卡确认销卡
            async submit(patient, card) {
                this.postData.chargePatientId = this.postData.patientId = patient.id;
                this.postData.id = card.itemId;
                this.postData.payType = PayModeEnum.CASH_PAY; // 免费开卡的情况，开卡费是0元，不会选择支付方式，后台校验了payMode参数，前端默认传 现金
                await CrmAPI.cardholderDestory(this.postData);
                this.$Toast({
                    type: 'success',
                    message: '销卡成功',
                });
                // 关闭弹窗
                this.$emit('input', false);
                // 刷新
                this.onHandleRefresh();
            },
            onHandleRefresh() {
                this.$emit('refresh');
            },
            async print(id) {
                try {
                    const { data } = await Api.rechargePrint(id);
                    this.$nextTick(async () => {
                        const printOptions = getAbcPrintOptions('充值小票', data);
                        await AbcPrinter.abcPrint(printOptions);
                        this.printLoading = false;
                    });
                } catch (error) {
                    const { message } = error;
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: message,
                    });
                }
            },
            onHandleFinish(list) {
                this.$emit('finish', list);
            },
            onSuccessInsert(data) {
                this.visibleInsertPatient = false;
                const ref = this.$refs.family;
                ref && ref.fetchSearchResult && ref.fetchSearchResult(data);
            },
        },
    };
</script>

<style lang="scss">
@import '~styles/abc-common.scss';

.family-member-form-item {
    align-items: flex-start !important;
    margin-top: -9px;
    margin-bottom: 14px;

    .abc-form-item-label {
        height: 32px;
        line-height: 32px;
    }
}

.open-card-success {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0 -20px;
    margin: -24px 0 -10px;

    .success {
        font-size: 32px;
        color: $G2;
    }

    .title {
        margin: 12px 0 6px 0;
        font-size: 14px;
        font-weight: 500;
        color: $S1;
    }

    .msg {
        font-size: 12px;
        font-weight: 400;
        color: $T2;
    }
}
</style>
