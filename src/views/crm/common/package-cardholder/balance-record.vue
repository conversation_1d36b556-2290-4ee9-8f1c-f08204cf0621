<template>
    <abc-layout preset="dialog-table" class="balance-record">
        <abc-layout-content>
            <abc-table
                :render-config="tableConfig"
                :data-list="showDataList"
                :loading="loading"
                :need-selected="false"
                :pagination="pageParams"
                @pageChange="onChangePageIndex"
            >
                <template #topHeader>
                    <abc-flex class="header-record" align="center" :gap="20">
                        <abc-space align="center" :size="24">
                            <profile
                                :patient="patient"
                                :card="card"
                                space-direction="horizontal"
                                :is-can-see-patient-mobile="isCanSeePatientMobile"
                            ></profile>
                            <abc-space align="center">
                                <abc-flex align="center">
                                    <abc-text theme="gray">
                                        本金金额
                                    </abc-text>
                                    <abc-text
                                        class="content"
                                        style="margin-left: 2px;"
                                        bold
                                        theme="warning-light"
                                    >
                                        <abc-money is-show-space :symbol-icon-size="12" :value="card.principal"></abc-money>
                                    </abc-text>
                                </abc-flex>
                                <abc-flex align="center">
                                    <abc-text theme="gray">
                                        赠金金额
                                    </abc-text>
                                    <abc-text
                                        style="margin-left: 2px;"
                                        class="content"
                                        bold
                                        theme="warning-light"
                                    >
                                        <abc-money is-show-space :symbol-icon-size="12" :value="card.present"></abc-money>
                                    </abc-text>
                                </abc-flex>
                            </abc-space>
                        </abc-space>
                    </abc-flex>
                </template>
                <template #payMoney="{ trData: item }">
                    <abc-table-cell>
                        {{ formatMoneyWithFlag(item.isIncome, item.payMoney) }}
                    </abc-table-cell>
                </template>
                <template #payPrincipal="{ trData: item }">
                    <abc-table-cell>
                        {{ formatMoneyWithFlag(item.isIncome, item.payPrincipal) }}
                    </abc-table-cell>
                </template>
                <template #payPresent="{ trData: item }">
                    <abc-table-cell>
                        {{ formatMoneyWithFlag(item.isIncome, item.payPresent) }}
                    </abc-table-cell>
                </template>
                <template #principalBalance="{ trData: item }">
                    <abc-table-cell>
                        {{ item.principalBalance | formatMoney }}
                    </abc-table-cell>
                </template>
                <template #presentBalance="{ trData: item }">
                    <abc-table-cell>
                        {{ item.presentBalance | formatMoney }}
                    </abc-table-cell>
                </template>
                <template #print="{ trData: item }">
                    <abc-table-cell>
                        <abc-check-access v-if="item.chargeType === 4 || item.chargeType === 5">
                            <abc-button
                                variant="text"
                                size="small"
                                @click="onClickPrint(item)"
                            >
                                打印
                            </abc-button>
                        </abc-check-access>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
    </abc-layout>
</template>

<script>
    import Profile from '../package-member/profile';
    import CrmAPI from 'api/crm';
    import { getAbcPrintOptions } from '@/printer/print-handler.js';
    import AbcPrinter from '@/printer/index.js';

    export default {
        name: 'BalanceRecord',
        components: {
            Profile,
        },
        props: {
            patient: {
                type: Object,
                required: true,
                default() {
                    return {};
                },
            },
            card: {
                type: Object,
                required: true,
                default() {
                    return {};
                },
            },
            isCanSeePatientMobile: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                loading: false,
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
                dataList: [],

                isFirstPrint: true,
                printLoading: false,
                tableConfig: {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'created',
                            label: '交易时间',
                            style: {
                                width: '160px',
                            },
                        },
                        {
                            key: 'chargeTypeStr',
                            label: '交易类型',
                            style: {
                                width: '90px',
                            },
                        },
                        {
                            key: 'chargePatientName',
                            label: '交易客户',
                            style: {
                                width: '70px',
                            },
                        },
                        {
                            key: 'payTypeStr',
                            label: '支付方式',
                            style: {
                                width: '120px',
                                textAlign: 'right',
                                paddingLeft: '0',
                            },
                        },
                        {
                            key: 'payMoney',
                            label: '交易金额',
                            style: {
                                width: '80px',
                                textAlign: 'right',
                                paddingLeft: '0',
                            },
                        },
                        {
                            key: 'payPrincipal',
                            label: '交易本金',
                            style: {
                                width: '80px',
                                textAlign: 'right',
                                paddingLeft: '0',
                            },
                        },
                        {
                            key: 'payPresent',
                            label: '交易赠金',
                            style: {
                                width: '80px',
                                textAlign: 'right',
                                paddingLeft: '0',
                            },
                        },
                        {
                            key: 'principalBalance',
                            label: '剩余本金',
                            style: {
                                width: '80px',
                                textAlign: 'right',
                                paddingLeft: '0',
                            },
                        },
                        {
                            key: 'presentBalance',
                            label: '剩余赠金',
                            style: {
                                width: '80px',
                                textAlign: 'right',
                                paddingLeft: '0',
                            },
                        },
                        {
                            key: 'sellerName',
                            label: '医生/销售人',
                            style: {
                                width: '100px',
                                textAlign: 'center',
                                paddingLeft: '0',
                                paddingRight: '0',
                            },
                        },
                        {
                            key: 'createdByName',
                            label: '收费人',
                            style: {
                                width: '70px',
                                textAlign: 'center',
                                paddingLeft: '0',
                                paddingRight: '0',
                            },
                        },
                        {
                            key: 'print',
                            label: '操作',
                            style: {
                                width: '60px',
                                textAlign: 'center',
                                paddingLeft: '0',
                                paddingRight: '0',
                            },
                        },
                    ],
                },
            };
        },
        computed: {
            showDataList() {
                return this.dataList;
            },
        },
        created() {
            this.fetchBalanceRecord();
        },
        methods: {
            formatMoneyWithFlag(isIncome = true, number) {
                if (!isIncome && number !== 0) {
                    return `-${parseFloat(number || 0).toFixed(2)}`;
                }

                return parseFloat(number || 0).toFixed(2);
            },
            async fetchBalanceRecord() {
                this.loading = true;
                try {
                    const {
                        pageIndex, pageSize,
                    } = this.pageParams;
                    const { data } = await CrmAPI.cardholderBalanceRecord(this.card.itemId, pageIndex, pageSize);
                    if (pageIndex === this.pageParams.pageIndex && pageSize === this.pageParams.pageSize) {
                        const {
                            rows, total,
                        } = data;
                        if (rows.length === 0 && this.pageParams.pageIndex !== 0) {
                            this.pageParams.pageIndex--;
                            await this.fetchBalanceRecord();
                        } else {
                            this.dataList = rows;
                            this.pageParams.count = total;
                            this.pageTotal = Math.ceil(total / this.pageParams.pageSize);
                        }
                    }
                } catch (error) {
                    console.log('fetchBalanceRecord error', error);
                }
                this.loading = false;
            },
            /**
             * 当改变页数时
             * @param {Number} index 页数索引
             */
            onChangePageIndex(index) {
                this.pageParams.pageIndex = index - 1;
                this.fetchBalanceRecord();
            },
            /**
             * 当点击打印时
             * @param {Object} item 打印对象
             */
            async onClickPrint(item) {
                const id = (item && item.id) || '';
                try {
                    const { data } = await CrmAPI.rechargePrint(id);
                    const typeName = item.chargeType === 4 ? '充值小票' : '退款凭证';
                    this.$nextTick(async () => {
                        const printOptions = getAbcPrintOptions(typeName, data);
                        await AbcPrinter.abcPrint(printOptions);
                        this.printLoading = false;
                    });
                } catch (error) {
                    console.log('onClickPrint error', error);
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: error.message,
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
.balance-record {
    .header-record {
        .member-profile {
            padding-bottom: 0;
            border: none;
        }
    }
}
</style>
