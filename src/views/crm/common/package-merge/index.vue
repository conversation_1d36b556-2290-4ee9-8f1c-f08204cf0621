<template>
    <abc-modal
        class="crm-module__package-merge__dialog-patient-merge"
        :title="`合并${viewDistributeConfig?.customerLabel || '患者'}`"
        :value="true"
        preset="step"
        size="large"
        :show-footer="false"
        :need-high-level="false"

        :disabled-keyboard="true"
        content-styles="padding: 0px 0px;overflow: hidden;"
        @input="(val) => $emit('input', val)"
    >
        <template #step>
            <abc-steps :active="active" style="cursor: default !important;">
                <abc-step :index="0" style="cursor: default !important;">
                    合并{{ viewDistributeConfig?.customerLabel || '患者' }}基本资料
                </abc-step>
                <abc-step :index="1" style="cursor: default !important;">
                    {{ secondStepText }}
                </abc-step>
            </abc-steps>
        </template>
        <abc-layout style="height: 550px;">
            <div v-abc-loading="loading">
                <template v-if="step === 1">
                    <div class="search-box">
                        <label>要合并的{{ viewDistributeConfig?.customerLabel || '患者' }}</label>
                        <div class="input-box">
                            <abc-autocomplete
                                v-for="(patient, index) in patientList"
                                :key="index"
                                v-model.trim="patient.name"
                                class="patient-autocomplete-wrapper"
                                :custom-class="isShowOutPatientItem ? 'patient-autocomplete-suggestions-popper-custom' : 'patient-autocomplete-suggestions-popper merge-patient'"
                                async-fetch
                                :inner-width="isShowOutPatientItem ? innerWidthAuto : 479"
                                :width="245"
                                :delay-time="0"
                                :disabled="!!patient.patientInfo"
                                :placeholder="patient.placeholder"
                                :fetch-suggestions="querySearchAsync"
                                :max-length="20"
                                clearable
                                @enterEvent="selectPatient(patient, ...arguments)"
                                @clear="clearPatient(patient)"
                            >
                                <template slot="suggestion-header">
                                    <div v-if="isShowOutPatientItem" class="suggestion-title suggestion-title-custom" v-html="patientInfoTitle()">
                                    </div>
                                </template>
                                <template slot="suggestions" slot-scope="props">
                                    <dt
                                        class="patient-suggestions suggestions-item"
                                        :class="{ selected: props.index == props.currentIndex }"
                                        :disabled="props.suggestion.disabled"
                                        @click="selectPatient(patient, props.suggestion)"
                                        v-html="patientInfo(props.suggestion, patientInfoOptions)"
                                    ></dt>
                                </template>
                                <template v-if="isShowOutPatientItem" slot="suggestion-fixed-footer">
                                    <abc-flex
                                        justify="end"
                                        align="center"
                                        class="suggestion-fixed-footer-wrapper"
                                    >
                                        <abc-button
                                            variant="text"
                                            size="small"
                                            theme="default"
                                            icon="n-settings-line"
                                            class="abc-select-panel-bottom-button"
                                            @click="handleSettingClick"
                                        >
                                        </abc-button>
                                    </abc-flex>
                                </template>
                            </abc-autocomplete>
                        </div>
                    </div>
                    <div class="choice-box-card">
                        <div class="choice-box">
                            <div v-for="item in labelList.concat(chronicLabelList)" :key="item.key" :class="`col-${ item.key}`">
                                <div class="label-box">
                                    <span>{{ item.label }}</span>
                                    <span v-if="item.value === -1" class="iconfont cis-icon-Attention"></span>
                                </div>
                                <abc-radio-group v-model="item.value">
                                    <abc-flex
                                        v-for="patient in showPatientList"
                                        :key="patient.index"
                                        align="flex-start"
                                        justify="flex-start"
                                        class="value-box"
                                    >
                                        <template v-if="patient.show">
                                            <template v-if="item.isChronicFiles">
                                                <span v-if="noEnoughtPatient">
                                                    <template v-if="patient[item.key]">
                                                        <abc-flex align="center" style="line-height: 22px;">
                                                            <span class="chronic-value">{{ patient[item.key].name }}</span>
                                                            <span class="chronic-value" chronic-value>{{ patient[item.key].date }}</span>
                                                        </abc-flex>
                                                    </template>
                                                </span>
                                                <abc-radio v-else :label="patient.index" :disabled="disabledList.includes(item.key)">
                                                    <abc-flex align="center">
                                                        <span class="chronic-value">{{ patient[item.key].name }}</span>
                                                        <span class="chronic-value" style="margin-left: 8px;">{{ patient[item.key].date }}</span>
                                                    </abc-flex>
                                                </abc-radio>
                                            </template>
                                            <template v-else>
                                                <span v-if="noEnoughtPatient">{{ patient[item.key] }}</span>
                                                <abc-radio
                                                    v-else
                                                    :label="patient.index"
                                                    :disabled="disabledList.includes(item.key)"
                                                    style="line-height: 20px;"
                                                >
                                                    {{ patient[item.key] }}
                                                </abc-radio>
                                            </template>
                                        </template>
                                    </abc-flex>
                                </abc-radio-group>
                            </div>
                        </div>
                    </div>
                </template>
                <template v-if="step === 2">
                    <div class="result-box">
                        <div v-for="item in showProdList" :key="item.key" class="col">
                            <div class="label">
                                {{ item.label }}
                            </div>

                            <template v-if="item.isTextMode">
                                <div class="total" :title="item.value" style="flex: 1;">
                                    {{ item.value }}
                                </div>
                            </template>

                            <template v-else>
                                <div
                                    v-for="(result, index) in showResultList"
                                    :key="index"
                                    :title="item.isTag ? '' : result[item.key]"
                                    class="data-item"
                                >
                                    <template v-if="item.isTag">
                                        <abc-flex align="center" :gap="8">
                                            <abc-tag-v2
                                                v-if="result.renderTag"
                                                variant="light-outline"
                                                theme="success"
                                                shape="round"
                                                size="mini"
                                                style="max-width: 100px;"
                                                ellipsis
                                            >
                                                {{ result.renderTag.tagName }}
                                            </abc-tag-v2>

                                            <abc-popover
                                                theme="white"
                                                placement="bottom"
                                                trigger="hover"
                                                width="200px"
                                                append-to-body
                                            >
                                                <abc-tag-v2
                                                    v-if="result.tags.length > 1"
                                                    slot="reference"
                                                    variant="light-outline"
                                                    theme="success"
                                                    shape="round"
                                                    size="mini"
                                                    icon="s-more-line-medium"
                                                >
                                                </abc-tag-v2>

                                                <abc-flex align="center" :gap="8" wrap="wrap">
                                                    <abc-tag-v2
                                                        v-for="tag in result.tags"
                                                        :key="tag.id"
                                                        variant="light-outline"
                                                        theme="success"
                                                        shape="round"
                                                        size="mini"
                                                    >
                                                        {{ tag.tagName }}
                                                    </abc-tag-v2>
                                                </abc-flex>
                                            </abc-popover>
                                        </abc-flex>
                                    </template>

                                    <template v-else>
                                        {{ result[item.key] || 0 }}
                                    </template>
                                </div>
                                <div class="total" :title="item.isTag ? '' : item.value">
                                    <template v-if="item.isTag">
                                        <abc-flex align="center" :gap="8">
                                            <abc-tag-v2
                                                v-if="getRenderTag(item.value)"
                                                variant="light-outline"
                                                theme="success"
                                                shape="round"
                                                size="mini"
                                                style="max-width: 100px;"
                                                ellipsis
                                            >
                                                {{ getRenderTag(item.value).tagName }}
                                            </abc-tag-v2>

                                            <abc-popover
                                                theme="white"
                                                placement="bottom"
                                                trigger="hover"
                                                width="200px"
                                                append-to-body
                                            >
                                                <abc-tag-v2
                                                    v-if="item.value.length > 1"
                                                    slot="reference"
                                                    variant="light-outline"
                                                    theme="success"
                                                    shape="round"
                                                    size="mini"
                                                    icon="s-more-line-medium"
                                                >
                                                </abc-tag-v2>

                                                <abc-flex align="center" :gap="8" wrap="wrap">
                                                    <abc-tag-v2
                                                        v-for="tag in item.value"
                                                        :key="tag.id"
                                                        variant="light-outline"
                                                        theme="success"
                                                        shape="round"
                                                        size="mini"
                                                    >
                                                        {{ tag.tagName }}
                                                    </abc-tag-v2>
                                                </abc-flex>
                                            </abc-popover>
                                        </abc-flex>
                                    </template>

                                    <template v-else>
                                        {{ item.value }}
                                    </template>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>
            <abc-flex
                align="center"
                :style="{
                    marginTop: 'auto',
                    width: '100%',
                    height: '32px',
                }"
                justify="space-around"
            >
                <abc-space>
                    <abc-button
                        v-if="step === 1"
                        :width="80"
                        :min-width="80"
                        :disabled="!canNextStep"
                        @click="onClickNextStep"
                    >
                        下一步
                    </abc-button>
                    <template v-else-if="step === 2">
                        <abc-button style="width: 80px; min-width: 80px;" @click="onClickMerge">
                            合并
                        </abc-button>
                        <abc-button
                            variant="ghost"
                            :width="80"
                            :min-width="80"
                            @click="step = 1"
                        >
                            上一步
                        </abc-button>
                    </template>
                    <abc-button
                        variant="ghost"
                        :width="80"
                        :min-width="80"
                        @click="$emit('input', false)"
                    >
                        取消
                    </abc-button>
                </abc-space>
            </abc-flex>
        </abc-layout>
    </abc-modal>
</template>

<script>
    import CrmAPI from 'api/crm';
    import PatientsAPI from 'api/patients';
    import { parseTime } from 'utils/index';
    import {
        encryptMobile, formatMoney,
    } from 'src/filters/index';
    import { add } from 'utils/math';
    import { validatePatientInfoMergeHospitalInfoConflict } from 'utils/validate';
    import {
        mapGetters, mapState,
    } from 'vuex';
    import {
        PATIENT_FIELD_EQUAL_MAP,
        PATIENT_FIELD_FORMAT_MAP,
        PATIENT_MERGE_FIELD_COMMON,
        PATIENT_MERGE_FIELD_IN_HOSPITAL,
    } from 'views/crm/common/package-merge/constant';
    import {
        DEFAULT_CERT_TYPE, MaritalStatusLabel,
    } from 'views/crm/constants';
    import patientAutocompleteMixin from 'views/common/patient-autocomplete-mixin';

    const MERGE_LIMIT = 2;
    export default {
        name: 'DialogPatientMerge',
        mixins: [patientAutocompleteMixin],
        props: {
            patientIds: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                step: 1, // 当前步骤
                name: '',
                loading: false,
                isDestroy: false,
                statusTimer: null,
                labelList: PATIENT_MERGE_FIELD_COMMON,
                chronicLabelList: [], // 追加展示的数据
                patientList: [],
                prodList: [],
                disabledList: [],
            };
        },
        computed: {
            active() {
                return this.step === 1 ? 0 : 1;
            },
            ...mapState('socialPc', [
                'basicInfo', // 配置基本信息
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            ...mapGetters(['isCanSeePatientMobileInCrm']),

            // 是否支持患者住院信息合并
            isSupportInHospitalInfoMerge() {
                return this.viewDistributeConfig.CRM.isSupportInHospitalInfoMerge;
            },

            // 合并患者信息展示
            showPatientList() {
                return this.patientList.map(({
                    index, patientInfo,
                }) => {
                    if (!patientInfo) {
                        return {
                            show: false,
                        };
                    }
                    let mergeFamilyPatientLabel = '-';
                    if (patientInfo.isMemberFamily) {
                        if (patientInfo.parentName) {
                            mergeFamilyPatientLabel = `被${patientInfo.parentName}添加`;
                        }
                    } else {
                        if (patientInfo.memberInfo &&
                            patientInfo.memberInfo.memberShareInfos &&
                            Array.isArray(patientInfo.memberInfo.memberShareInfos)) {
                            const names = patientInfo.memberInfo.memberShareInfos.map((it) => it.sharedPatientName);
                            if (names.length) {
                                mergeFamilyPatientLabel = `添加了${names.join(',')}`;
                            }
                        }
                    }
                    return {
                        show: true,
                        index,
                        id: patientInfo.id,
                        mergeFamilyPatientId: mergeFamilyPatientLabel,
                        name: patientInfo.name || '-',
                        sex: patientInfo.sex || '-',
                        birthday: patientInfo.birthday || '-',
                        mobile: (this.isCanSeePatientMobileInCrm ? patientInfo.mobile : encryptMobile(patientInfo.mobile)) || '-',
                        wxchat: patientInfo.wxNickName || '-',
                        idCard: (patientInfo.idCard ? `[${patientInfo.idCardType || DEFAULT_CERT_TYPE}]\n${patientInfo.idCard}` : '-'),
                        source: this.getSourceName(patientInfo.patientSource, '-'),
                        member: this.getMemberShow(patientInfo, '非会员'),
                        birthAddress: this.getAddress(patientInfo.birthAddress),
                        ancestralAddress: this.getAddress(patientInfo.ancestralAddress),
                        nationality: patientInfo.nationality || '-',
                        ethnicity: patientInfo.ethnicity || '-',
                        marital: MaritalStatusLabel[patientInfo.marital] || '-',
                        profession: patientInfo.profession || '-',
                        shebaoCardNo: patientInfo.shebaoCardInfo?.cardNo || '-',
                        registerAddress: this.getAddress(patientInfo.registerAddress, '-', true),
                        registerAddressDetail: patientInfo.registerAddress?.addressDetail || '-',
                        registerAddressPostCode: patientInfo.registerAddress?.addressPostcode || '-',
                        address: this.getAddress(patientInfo.address, '-', true),
                        addressDetail: patientInfo.address?.addressDetail || '-',
                        addressPostCode: patientInfo.address?.addressPostcode || '-',
                        familyMobile: patientInfo.familyMobile || '-',
                        company: patientInfo.company || '-',
                        companyAddress: this.getAddress(patientInfo.companyAddress, '-', true),
                        companyAddressDetail: patientInfo.companyAddress?.addressDetail || '-',
                        companyAddressPostCode: patientInfo.companyAddress?.addressPostcode || '-',
                        companyMobile: patientInfo.companyMobile || '-',
                        contactName: patientInfo.contactName || '-',
                        contactMobile: patientInfo.contactMobile || '-',
                        contactRelation: patientInfo.contactRelation || '-',
                        contactAddress: this.getAddress(patientInfo.contactAddress, '-', true),
                        contactAddressDetail: patientInfo.contactAddress?.addressDetail || '-',
                        weight: patientInfo.weight || '-',
                        pastHistory: patientInfo.pastHistory || '-',
                        allergicHistory: patientInfo.allergicHistory || '-',
                        visitReason: patientInfo.visitReason || '-',
                        remark: patientInfo.remark || '-',
                        height: patientInfo.height || '-',
                        email: patientInfo.email || '-',
                        crowdCategory: (patientInfo.crowdCategory ? `${patientInfo.crowdCategory}${(patientInfo.crowdCategory === '其他' && patientInfo.crowdCategoryRemark) ? `(${patientInfo.crowdCategoryRemark})` : ''}` : '') || '-',
                        primaryTherapistName: patientInfo.primaryTherapistName || '-',
                        dutyTherapistName: patientInfo.dutyTherapistName || '-',
                        ...this.getChronicLabelInfoObj(patientInfo.chronicArchivesInfo),
                    };
                });
            },

            // 要展示的合并项目
            showProdList() {
                console.log(this.prodList.filter((item) => !item.hide));
                return this.prodList.filter((item) => !item.hide);
            },
            // 合并结果信息展示
            showResultList() {
                return this.patientList.map(({ patientInfo }) => {
                    const item = {
                        patientShow: this.getPatientShow(patientInfo, '无'),
                        principal: '-', // 本金
                        present: '-', // 赠金
                        points: '-', // 积分
                        outPatientTimes: '-', // 门诊记录
                        payTimesTotal: '-', // 收费记录
                    };
                    if (patientInfo) {
                        if (patientInfo.memberFlag === 1 && patientInfo.memberInfo) {
                            const {
                                principal, present,
                            } = patientInfo.memberInfo || {};
                            item.principal = `${this.$t('currencySymbol')} ${formatMoney(principal)}`;
                            item.present = `${this.$t('currencySymbol')} ${formatMoney(present)}`;
                        }
                        // 积分不仅仅针对会员，普通患者也可以有积分
                        item.points = patientInfo.points || 0;
                        item.outPatientTimes = patientInfo.outPatientTimes || 0;
                        item.payTimesTotal = patientInfo.payTimesTotal || 0;
                        item.tags = patientInfo.tags || [];
                        item.renderTag = this.getRenderTag(patientInfo.tags || []);
                    }
                    console.log(item);
                    return item;
                });
            },
            // 有效的患者列表
            validPatientList() {
                return this.patientList.filter(({ patientInfo }) => !!patientInfo);
            },
            // 已经加入的患者ids
            exitPatientIds() {
                return this.validPatientList.map(({ patientInfo }) => patientInfo.id);
            },
            // 选择合并的患者数量不足两个，无法合并  true-患者不足；false-患者够了
            noEnoughtPatient() {
                return !this.patientList.every(({ patientInfo }) => !!patientInfo);
            },
            // 是否可以点击下一步，
            // 1、在都有患者情况下，可以点击下一步
            canNextStep() {
                return !this.noEnoughtPatient && this.labelList.every((item) => item.value !== -1);
            },
            isShowOutPatientItem() {
                return this.viewDistributeConfig.CRM.patientMerge.isShowOutPatientItem;
            },
            patientInfoOptions() {
                return {
                    isCustom: this.isShowOutPatientItem,
                    isCanSeePatientMobile: this.isCanSeePatientMobileInCrm,
                };
            },
            secondStepText() {
                if (this.isSupportInHospitalInfoMerge) {
                    return '合并门诊/住院/体检/医技记录';
                }

                return `合并${this.isShowOutPatientItem ? '门诊/' : ''}收费记录`;
            },
        },
        watch: {
            showPatientList() {
                this.handleDefaultValue();
            },
        },
        created() {
            this._messageInstance = null;
            this.patientList = [
                {
                    index: 0,
                    name: '',
                    patientInfo: null,
                    placeholder: `${this.viewDistributeConfig?.customerLabel || '患者'}一`,
                },
                {
                    index: 1,
                    name: '',
                    patientInfo: null,
                    placeholder: `${this.viewDistributeConfig?.customerLabel || '患者'}二`,
                },
            ];
            this.labelList = this.isSupportInHospitalInfoMerge ? PATIENT_MERGE_FIELD_IN_HOSPITAL : PATIENT_MERGE_FIELD_COMMON;
            if (this.isSupportInHospitalInfoMerge) {
                this.prodList = [
                    {
                        label: '合并项目', key: 'patientShow', hide: false, value: '合并后结果',
                    },
                    {
                        label: '患者标签', key: 'patientTag', hide: false, value: '',isTag: true,
                    },
                    {
                        label: '会员卡本金', key: 'principal', hide: false, value: '',
                    },
                    {
                        label: '会员卡赠金', key: 'present', hide: false, value: '',
                    },
                    {
                        label: '会员卡积分', key: 'points', hide: false, value: '',
                    },
                    {
                        label: '门诊记录', key: 'outPatientTimes', hide: false, value: '病历文书、手术单、执行记录将使用新信息', isTextMode: true,
                    },
                    {
                        label: '住院记录', key: 'inHospitalRecord', hide: false, value: '住院单、医嘱、医疗文书、手术单、会诊单将使用新信息',isTextMode: true,
                    },
                    {
                        label: '体检记录', key: 'phRecord', hide: false, value: '',isTextMode: true,
                    },
                    {
                        label: '医技记录', key: 'examRecord', hide: false, value: '发退药单、检验单、检查单将使用新信息',isTextMode: true,
                    },
                    {
                        label: '收费记录', key: 'payTimesTotal', hide: false, value: '门诊收费单、住院收费单、体检收费单将使用新信息',isTextMode: true,
                    },
                ];
            } else {
                this.prodList = [
                    {
                        label: '合并项目', key: 'patientShow', hide: false, value: '合并后结果',
                    },
                    {
                        label: '患者标签', key: 'patientTag', hide: false, value: '',isTag: true,
                    },
                    {
                        label: '会员卡本金', key: 'principal', hide: false, value: '',
                    },
                    {
                        label: '会员卡赠金', key: 'present', hide: false, value: '',
                    },
                    {
                        label: '会员卡积分', key: 'points', hide: false, value: '',
                    },
                    {
                        label: '门诊记录', key: 'outPatientTimes', hide: false, value: '病历文书、执行记录将使用新信息', isTextMode: true,
                    },
                    {
                        label: '医技记录', key: 'examRecord', hide: false, value: '发退药单、检验单、检查单将使用新信息',isTextMode: true,
                    },
                    {
                        label: '收费记录', key: 'payTimesTotal', hide: false, value: '门诊收费单将使用新信息',isTextMode: true,
                    },
                ];
            }
            this.handleLabelList();
            if (
                this.patientIds.length !== 0 &&
                this.patientIds.length <= MERGE_LIMIT &&
                this.patientIds[0] !== this.patientIds[1]
            ) {
                this.initPatientInfo();
            }
        },
        beforeDestroy() {
            this.isDestroy = true;
            this.clearTimer(this.statusTimer);
        },
        methods: {
            handleLabelList() {
                if (!this.viewDistributeConfig.CRM.patientMerge.isShowWx) {
                    const targetIndex = this.labelList.findIndex((item) => item.key === 'wxchat');
                    if (targetIndex !== -1) {
                        this.labelList.splice(targetIndex, 1);
                    }
                }
                if (this.viewDistributeConfig.CRM.patientMerge.isMobileItemFirst) {
                    const targetIndex = this.labelList.findIndex((item) => item.key === 'mobile');
                    if (targetIndex !== -1) {
                        [this.labelList[0], this.labelList[targetIndex]] = [this.labelList[targetIndex], this.labelList[0]];
                    }
                }
                const targetIndex = this.labelList.findIndex((item) => item.key === 'source');
                if (targetIndex !== -1) {
                    this.labelList[targetIndex].label = this.viewDistributeConfig.CRM.sourceName || '首诊来源';
                }
            },
            // 校验患者合并信息冲突
            validatePatientInfoMergeHospitalInfoConflict,
            /**
             * 当有目标性的合并患者：意思是指定两个patientId进行合并
             * <AUTHOR>
             * @date 2021-01-21
             */
            async initPatientInfo() {
                const promiseList = this.patientIds.map((patientId) =>
                    (patientId ? CrmAPI.fetchMergePatientInfoV2(patientId) : Promise.resolve({})),
                );
                this.loading = true;
                try {
                    const resList = await Promise.all(promiseList);
                    resList.forEach(({ data }, index) => {
                        if (data && this.patientList[index]) {
                            this.patientList[index].name = data.name;
                            this.patientList[index].patientInfo = data;
                        }
                    });
                    this.updateLabelList();
                } catch (error) {
                    console.log('initPatientInfo error', error);
                }
                this.loading = false;
            },
            /**
             * 拉取患者列表
             * <AUTHOR>
             * @date 2021-01-21
             * @param {String} queryString 搜索关键词
             * @param {Function} callback 回调函数
             */
            async querySearchAsync(queryString, callback) {
                queryString = queryString.trim();
                let dataList = [];
                if (queryString) {
                    try {
                        const { data } = await PatientsAPI.fetchPatientsByName(queryString, {
                            extDataFlag: (this.isShowOutPatientItem && this.needExtDataFlag) ? 1 : 0,
                        });
                        if (data.name !== queryString || !data) {
                            dataList = [];
                        } else {
                            dataList = data.list.map((item) => {
                                item.disabled = this.exitPatientIds.includes(item.id);
                                return item;
                            });
                        }
                    } catch (error) {
                        console.log('querySearchAsync error', error);
                    }
                }
                return callback(dataList);
            },

            /**
             * 首诊来源展示：员工推荐
             * <AUTHOR>
             * @date 2021-01-21
             * @param {Object} patientSource
             * @param {String} def 默认值
             * @returns {String}
             */
            getSourceName(patientSource, def = '-') {
                let showName = def;
                if (patientSource) {
                    const {
                        name, sourceFromName,
                    } = patientSource;
                    if (name) showName = name;
                    if (sourceFromName) showName += `-${sourceFromName}`;
                }
                return showName;
            },
            /**
             * 地址展示
             * <AUTHOR>
             * @date 2021-01-21
             * @param {Object} address 地址对象
             * @param {String} def 默认值
             * @param {Boolean} noDetail 是否拼接详细地址
             * @returns {String}
             */
            getAddress(address, def = '-', noDetail = false) {
                if (address) {
                    const {
                        addressProvinceName, addressCityName, addressDistrictName, addressDetail,
                    } = address;
                    const arr = [];
                    if (addressProvinceName) arr.push(addressProvinceName);
                    if (addressCityName) arr.push(addressCityName);
                    if (addressDistrictName) arr.push(addressDistrictName);
                    if (arr.length !== 0 && addressDetail && !noDetail) {
                        return `${arr.join(' / ')}  ${addressDetail}`;
                    } if (arr.length !== 0) {
                        return arr.join(' / ');
                    } if (addressDetail && !noDetail) {
                        return addressDetail;
                    }
                    return def;

                }
                return def;
            },
            /**
             * 获取会员卡类型展示
             * <AUTHOR>
             * @date 2021-01-21
             * @param {Object} memberInfo 会员信息
             * @param {String} def 默认值
             * @returns {String}
             */
            getMemberShow({
                memberFlag, memberInfo,
            }, def = '非会员') {
                if (memberFlag === 1 && memberInfo) {
                    return memberInfo.memberTypeInfo && memberInfo.memberTypeInfo.memberTypeName;
                }
                return def;

            },
            /**
             * 合并结果患者展示："姓名 手机号"
             * <AUTHOR>
             * @date 2021-01-21
             * @param {Object} patientInfo 患者信息
             * @param {String} def 默认值
             * @returns {String}
             */
            getPatientShow(patientInfo, def = '无') {
                let showName = def;
                if (patientInfo) {
                    showName = patientInfo.name;
                    if (patientInfo.mobile) {
                        showName += this.isCanSeePatientMobileInCrm ? `  ${patientInfo.mobile}` : `  ${encryptMobile(patientInfo.mobile)}`;
                    }
                }
                return showName;
            },
            /**
             * 点击清除搜索
             * <AUTHOR>
             * @date 2021-01-21
             * @param {Object} patient 要清除的患者对象
             */
            clearPatient(patient) {
                patient.name = '';
                patient.patientInfo = null;
                this.updateLabelList();
            },
            /**
             * 选中某个患者加入合并列表
             * <AUTHOR>
             * @date 2021-01-21
             * @param {Object} patient 当前患者对象
             * @param {Object} selectPatient 被选中患者对象
             */
            async selectPatient(patient, selectPatient) {
                if (selectPatient.disabled) {
                    return this.$Toast({
                        type: 'error',
                        message: '不能合并相同患者',
                    });
                }
                try {
                    const { data } = await CrmAPI.fetchMergePatientInfoV2(selectPatient.id);
                    patient.name = data.name;
                    patient.patientInfo = data;
                    this.updateLabelList();
                } catch (error) {
                    console.log('selectPatient error', error);
                }
            },
            updateLabelList() {
                console.debug('更新labellist');
                this.updateChronicLabelList();
                this.updateFamilyInfoList();
            },
            updateFamilyInfoList() {
                let hasFamilyInfo = true;
                this.patientList.forEach(({ patientInfo }) => {
                    if (patientInfo && patientInfo.parentId) {
                        patientInfo.mergeFamilyPatientId = patientInfo.parentId;
                        return;
                    }
                    if (patientInfo &&
                        patientInfo.memberInfo &&
                        patientInfo.memberInfo.memberShareInfos &&
                        Array.isArray(patientInfo.memberInfo.memberShareInfos)
                    ) {
                        if (patientInfo.memberInfo.memberShareInfos.length > 0) {
                            patientInfo.mergeFamilyPatientId = patientInfo.id;
                            return;
                        }
                    }
                    hasFamilyInfo = false;
                });

                const finedItemIndex = this.labelList.findIndex((it) => it.key === 'mergeFamilyPatientId');

                // 如果两个 mergeFamilyPatientId存在并且不同
                const parentId1 = this.patientList[0] && this.patientList[0].patientInfo && this.patientList[0].patientInfo.mergeFamilyPatientId;
                const parentId2 = this.patientList[1] && this.patientList[1].patientInfo && this.patientList[1].patientInfo.mergeFamilyPatientId;
                if (parentId1 && parentId2 && parentId1 !== parentId2) {
                    if (hasFamilyInfo && finedItemIndex < 0) {
                        this.labelList.push({
                            label: '家庭成员', key: 'mergeFamilyPatientId', alert: false, value: '',
                        });
                    }
                }
                if (!hasFamilyInfo && finedItemIndex > -1) {
                    this.labelList.splice(finedItemIndex, 1);
                }
            },
            /**
             * 当患者修改时，获取追加的动态labelList
             * <AUTHOR>
             * @date 2021-01-21
             */
            updateChronicLabelList() {
                const chronicLabelTemplateIds = [];
                this.patientList.forEach(({ patientInfo }) => {
                    if (patientInfo) {
                        const {
                            archivesCount = 0, archivesList = [],
                        } = patientInfo.chronicArchivesInfo || {};
                        if (archivesCount !== 0) {
                            archivesList.forEach((item) => {
                                const { templateId } = item || {};
                                if (templateId && !chronicLabelTemplateIds.includes(templateId)) {
                                    chronicLabelTemplateIds.push(templateId);
                                }
                            });
                        }
                    }
                });
                this.chronicLabelList = chronicLabelTemplateIds.map((templateId) => ({
                    label: '慢病基础档案',
                    key: templateId,
                    alert: false,
                    value: '',
                    isChronicFiles: true,
                }));
            },
            /**
             * 获取单个患者展示的基础档案信息
             * <AUTHOR>
             * @date 2021-01-25
             * @param {Object} chronicArchivesInfo 慢病档案信息
             * @returns {Object} 展示的慢病信息
             */
            getChronicLabelInfoObj(chronicArchivesInfo) {
                const { archivesList = [] } = chronicArchivesInfo || {};
                return this.chronicLabelList.reduce((obj, cur) => {
                    obj[cur.key] = {
                        id: '',
                        name: '-',
                        date: '',
                    };
                    const target = archivesList.find((item) => item.templateId === cur.key);
                    if (target) {
                        obj[cur.key] = {
                            ...target,
                            date: `${parseTime(target.created, 'y-m-d', true)} 建档`,
                        };
                    }
                    return obj;
                }, {});
            },
            /**
             * 是否选中的是会员
             * true-选择是会员，false-是非会员
             * <AUTHOR>
             * @date 2021-01-21
             * @returns {Boolean}
             */
            getSelectMember() {
                let selectedMember = false;
                const memberItem = this.labelList.find((item) => item.key === 'member');
                this.patientList.forEach(({
                    index, patientInfo,
                }) => {
                    if (memberItem.value === index && patientInfo.memberFlag === 1 && patientInfo.memberInfo) {
                        selectedMember = true;
                    }
                });
                return selectedMember;
            },
            /**
             * 合并患者列表中，是否有会员
             * <AUTHOR>
             * @date 2021-01-21
             * @returns {Boolean} true-有会员，false-没有会员
             */
            getExitMember() {
                return this.patientList.some(
                    ({ patientInfo }) => patientInfo.memberFlag === 1 && patientInfo.memberInfo,
                );
            },
            /**
             * 出来合并字段默认值
             * 1、优先选择有值得
             * 2、都有值时，选择最近修改的
             * <AUTHOR>
             * @date 2021-01-21
             */
            handleDefaultValue() {
                const validPatientInfoList = this.validPatientList.map((item) => {
                    item.lastTime = new Date(item.patientInfo.lastModified).getTime();
                    return item;
                });
                this.disabledList = [];
                const orderByLastModified = validPatientInfoList.sort((a, b) => b.lastTime - a.lastTime);
                this.labelList.forEach((item) => {
                    if (validPatientInfoList.length === 0) {
                        // 没有时
                        item.value = '';
                    } else if (validPatientInfoList.length === 1) {
                        // 一个时
                        item.value = validPatientInfoList[0].index;
                    } else {
                        // 多个时
                        // 1、先把有值得筛选出来
                        // 2、在对比一个最近修改的
                        const { key } = item;

                        const hasValueList = validPatientInfoList.filter(({ patientInfo }) => {
                            switch (key) {
                                case 'wxchat':
                                    return !!patientInfo.wxNickName;
                                case 'source':
                                    return !!patientInfo.patientSource;
                                case 'birthAddress':
                                case 'ancestralAddress':
                                    return (
                                        !!patientInfo[key] &&
                                        (patientInfo[key].addressCityId ||
                                            patientInfo[key].addressProvinceId ||
                                            patientInfo[key].addressDistrictId ||
                                            patientInfo[key].addressDetail)
                                    );
                                case 'shebaoCardNo':
                                    return !!patientInfo.shebaoCardInfo?.shebaoCardNo;
                                case 'member':
                                    return patientInfo.memberFlag === 1;
                                case 'registerAddress':
                                case 'address':
                                case 'companyAddress':
                                case 'contactAddress':
                                    return (
                                        patientInfo[key]?.addressCityId ||
                                        patientInfo[key]?.addressProvinceId ||
                                        patientInfo[key]?.addressDistrictId
                                    );
                                case 'registerAddressDetail':
                                    return !!patientInfo.registerAddress?.addressDetail;
                                case 'addressDetail':
                                    return !!patientInfo.address?.addressDetail;
                                case 'companyAddressDetail':
                                    return !!patientInfo.companyAddress?.addressDetail;
                                case 'contactAddressDetail':
                                    return !!patientInfo.contactAddress?.addressDetail;
                                case 'registerAddressPostCode':
                                    return !!patientInfo.registerAddress?.addressPostcode;
                                case 'addressPostCode':
                                    return !!patientInfo.address?.addressPostcode;
                                case 'companyAddressPostCode':
                                    return !!patientInfo.companyAddress?.addressPostcode;
                                case 'primaryTherapistName':
                                    return !!patientInfo.primaryTherapistId;
                                case 'dutyTherapistName':
                                    return !!patientInfo.dutyTherapistId;
                                default:
                                    return !!patientInfo[key];
                            }
                        });
                        if (hasValueList.length === 0) {
                            item.value = orderByLastModified[0].index;
                        } else if (hasValueList.length === 1) {
                            item.value = hasValueList[0].index;
                            this.disabledList.push(key);
                        } else {
                            let isAllEqual = true;
                            for (let i = 1; i < hasValueList.length; i++) {
                                const one = hasValueList[i - 1].patientInfo;
                                const two = hasValueList[i].patientInfo;

                                /** @type Function */
                                const compareFn = PATIENT_FIELD_EQUAL_MAP[key] || PATIENT_FIELD_EQUAL_MAP.defaultCompare;
                                isAllEqual = compareFn(one, two, key);
                                if (isAllEqual === false) {
                                    break;
                                }
                            }
                            if (isAllEqual) {
                                // 值全部相等
                                item.value = orderByLastModified[0].index;
                            } else {
                                // 值并不完全相等，此时提示
                                item.value = -1;
                            }
                        }
                    }
                });
                // 处理追加字段的默认值选择，规则：默认选中最近更改的数据
                this.chronicLabelList.forEach((item) => {
                    const list = this.showPatientList
                        .slice()
                        .filter((item) => item.show)
                        .sort((a, b) => {
                            const aCreated = a[item.key].created;
                            const bCreated = b[item.key].created;
                            const aTime = aCreated ? new Date(aCreated).getTime() : 0;
                            const bTime = bCreated ? new Date(bCreated).getTime() : 0;
                            return bTime - aTime;
                        });
                    item.value = list[0].index;
                });
                // 判断内容是否有有其中一个选项没有值
                this.chronicLabelList.forEach((item) => {
                    if (this.showPatientList.filter((i) => {
                        return !i?.[item.key]?.name;
                    }).length === 0) {
                        this.disabledList.push(item.key);
                    }
                });
            },
            /**
             * 合并项目展示信息合计
             * 如何合并的是两个非会员，或者非会员与会员合并时选择的非会员，两种情况下不展示会员合并信息（本金、赠金）
             * <AUTHOR>
             * @date 2021-01-21
             */
            handleMergeTotalInfo() {
                const selectedMember = this.getSelectMember();

                this.prodList.forEach((item) => {
                    const { key } = item;
                    switch (key) {
                        case 'patientShow': // 不对这项进行合计
                            break;
                        case 'principal':
                        case 'present':
                            if (!selectedMember) {
                                item.hide = true;
                                break;
                            }
                            item.hide = false;
                            item.value = this.validPatientList
                                .filter(({ patientInfo }) => patientInfo.memberFlag === 1 && patientInfo.memberInfo)
                                .reduce((total, { patientInfo }) => add(total, patientInfo.memberInfo[key]), 0);
                            break;
                        case 'points':
                            item.value = this.validPatientList.reduce((total, { patientInfo }) => add(total, patientInfo[key]), 0);
                            break;
                        case 'patientTag':
                            // eslint-disable-next-line no-case-declarations
                            const tags = this.validPatientList.reduce((res, { patientInfo }) => {
                                return res.concat(patientInfo.tags || []);
                            }, []);
                            item.value = Array.from(new Map(tags.map((tag) => [tag.tagId, tag])).values());
                            break;
                        default:
                            // item.value = this.validPatientList.reduce(
                            //     (total, { patientInfo }) => add(total, patientInfo[key]),
                            //     0,
                            // );
                            break;
                    }
                    if (!item.hide && (key === 'principal' || key === 'present')) {
                        item.value = `${this.$t('currencySymbol')} ${formatMoney(item.value)}`;
                    }
                });
            },

            /**
             * 当点击下一步
             * 一个会员，一个非会员，合并时选择非会员，若会员卡上还有余额或积分，alert提示：
             * 钻石卡上还有本金￥300.00，赠金￥100.00，积分241。合并后患者为非会员，将清零所有会员卡余额，是否确认？
             * <AUTHOR>
             * @date 2021-01-21
             */
            async onClickNextStep() {
                const exitMember = this.getExitMember();
                const selectedMember = this.getSelectMember();
                let canMergePatient = true;
                // 判断是门店否有长护功能
                if (this.basicInfo?.hospitalZCM) {
                    // 能否合并两个患者 校验长护部分信息是否重合
                    canMergePatient = await validatePatientInfoMergeHospitalInfoConflict(this.patientList[0], this.patientList[1]);
                }
                if (exitMember && !selectedMember) {
                    const memberPatient = this.validPatientList.find(
                        ({ patientInfo }) => patientInfo.memberFlag === 1 && patientInfo.memberInfo,
                    );
                    const {
                        principal,
                        present,
                        memberTypeInfo: { memberTypeName },
                    } = memberPatient.patientInfo.memberInfo;
                    // 积分不仅仅针对会员，普通患者也可以有积分
                    const points = memberPatient.patientInfo.points || 0;

                    this._messageInstance = this.$message({
                        type: 'warn',
                        title: '是否确认？',
                        content: [`${memberTypeName}上还有本金${this.$t('currencySymbol')} ${formatMoney(principal)}，赠金${this.$t('currencySymbol')} ${formatMoney(
                            present,
                        )}，积分${points}。合并后患者为非会员，将清零所有会员卡余额`],
                        dialogType: 'tiny',
                        noDialogAnimation: true,
                        dialogContentStyles: 'width: 240px; min-width: 240px',
                        referenceEl: this.$el.querySelector('.abc-dialog'),
                        confirmText: '确认',
                        onConfirm: async () => {
                            if (!canMergePatient) {
                                this.handlePatientHospitalInfoConflict();
                                return false;
                            }
                            this.validatePassNext();
                        },
                    });
                } else if (!canMergePatient) {
                    this.handlePatientHospitalInfoConflict();
                } else {
                    this.validatePassNext();
                }
            },
            // 验证通过进行下一步
            validatePassNext() {
                this.handleMergeTotalInfo();
                this.step = 2;
            },
            // 处理患者长护冲突 仅提示 处理流程需要自己去长护登陆处处理
            handlePatientHospitalInfoConflict() {
                this._messageInstance = this.$message({
                    type: 'warn',
                    title: '长护住院记录冲突',
                    content: ['两个患者都有长护登记，请先出院或撤销一次登记后，再进行合并'],
                    dialogType: 'tiny',
                    noDialogAnimation: true,
                    dialogContentStyles: 'width: 240px; min-width: 240px',
                    referenceEl: this.$el.querySelector('.abc-dialog'),
                    confirmText: '确认',
                    onConfirm: async () => {
                    },
                });
            },
            getRealPatientId(patientInfo) {
                if (patientInfo && patientInfo.parentId) {
                    console.log('返回子成员id');
                    return patientInfo.id;
                }

                if (patientInfo && patientInfo.mergeFamilyPatientId) {
                    console.log('返回家长id');
                    return patientInfo.mergeFamilyPatientId;
                }

                return null;
            },
            /**
             * 获取需要提交的数据
             * <AUTHOR>
             * @date 2021-01-21
             * @returns {Object}
             */
            getPostData() {
                const postData = {
                    sourcePatientIds: this.exitPatientIds,
                    mergedPatientInfo: {
                        name: '',
                        idCard: '',
                        idCardType: DEFAULT_CERT_TYPE,
                        countryCode: null,
                        birthday: '',
                        address: null,
                        mobile: '',
                        sex: '',
                        wxOpenId: '',
                        age: null,
                        patientSource: null,
                        memberFlag: '',
                        memberPatientId: '',
                        chronicArchives: [], // 慢病档案合并
                        mergeFamilyPatientId: '',
                    },
                };
                let memberMobile = ''; // 选中的会员卡手机号
                let mergeIdCardType = ''; // 选中的身份证类型
                let mergeCountryCode = '';
                const { mergedPatientInfo } = postData;
                this.labelList.forEach(({
                    key, value,
                }) => {
                    const patient = this.validPatientList.find(({ index }) => index === value);
                    const { patientInfo } = patient;

                    /** @type Function */
                    const formatFn = PATIENT_FIELD_FORMAT_MAP[key] || PATIENT_FIELD_FORMAT_MAP.defaultFn;
                    formatFn(mergedPatientInfo, patientInfo, key);

                    if (key === 'member') {
                        if (patientInfo.memberFlag === 1 && patientInfo.memberInfo) {
                            memberMobile = patientInfo.mobile;
                        }
                    }
                    // 身份证类型用户身份证类型
                    if (key === 'idCard') {
                        mergeIdCardType = patientInfo.idCardType;
                    }

                    if (key === 'mobile') {
                        mergeCountryCode = patientInfo.countryCode;
                    }
                });
                // 患者标签
                mergedPatientInfo.tags = this.prodList.find((item) => item.key === 'patientTag')?.value || [];
                /**
                 * 特殊情况：若选择会员卡卡号与选择手机字段不同
                 * 1、若手机号为空：会员卡号自动填充手机号（前端处理）
                 * 2、若手机号不为空，但与会员卡手机不同：会员卡卡号改为选择的手机号（后端处理）
                 */
                if (!mergedPatientInfo.mobile && memberMobile) {
                    mergedPatientInfo.mobile = memberMobile;
                }

                if (mergedPatientInfo.idCard) {
                    mergedPatientInfo.idCardType = mergeIdCardType || DEFAULT_CERT_TYPE;
                }
                if (mergedPatientInfo.mobile) {
                    mergedPatientInfo.countryCode = mergeCountryCode || null;
                }

                const patientInfo1 = this.patientList[0].patientInfo || {};
                const patientInfo2 = this.patientList[1].patientInfo || {};
                const patientMergeFamilyPatientId1 = patientInfo1.mergeFamilyPatientId;
                const patientMergeFamilyPatientId2 = patientInfo2.mergeFamilyPatientId;

                // 没有家庭关系
                if (!patientMergeFamilyPatientId1 && !patientMergeFamilyPatientId2) {
                    console.log('没有家庭关系');
                    postData.mergedPatientInfo.mergeFamilyPatientId = null;
                }

                // 患者1有家庭关系
                if (patientMergeFamilyPatientId1 && !patientMergeFamilyPatientId2) {
                    console.log('患者1有家庭关系');
                    postData.mergedPatientInfo.mergeFamilyPatientId = this.getRealPatientId(patientInfo1);
                }

                // 患者2有家庭关系
                if (!patientMergeFamilyPatientId1 && patientMergeFamilyPatientId2) {
                    console.log('患者2有家庭关系');
                    postData.mergedPatientInfo.mergeFamilyPatientId = this.getRealPatientId(patientInfo2);
                }

                // 都有家庭关系
                if (patientMergeFamilyPatientId1 && patientMergeFamilyPatientId2) {
                    console.log('两个患者均有家庭关系');
                    // 家庭不同
                    if (patientMergeFamilyPatientId1 !== patientMergeFamilyPatientId2) {
                        console.log('家庭不同');
                        if (patientMergeFamilyPatientId1 === postData.mergedPatientInfo.mergeFamilyPatientId) {
                            console.log('选中患者1的家庭');
                            postData.mergedPatientInfo.mergeFamilyPatientId = this.getRealPatientId(patientInfo1);
                        }

                        if (patientMergeFamilyPatientId2 === postData.mergedPatientInfo.mergeFamilyPatientId) {
                            console.log('选中患者2的家庭');
                            postData.mergedPatientInfo.mergeFamilyPatientId = this.getRealPatientId(patientInfo2);
                        }
                    }

                    // 家庭相同
                    if (patientMergeFamilyPatientId1 === patientMergeFamilyPatientId2) {
                        console.log('家庭相同');
                        if (this.getRealPatientId(patientInfo1) !== patientMergeFamilyPatientId1 &&
                            this.getRealPatientId(patientInfo2) !== patientMergeFamilyPatientId1) {
                            console.log('都是子成员，任意选择一个id');
                            postData.mergedPatientInfo.mergeFamilyPatientId = this.getRealPatientId(patientInfo1);
                        } else {
                            console.log('选择家长');
                            postData.mergedPatientInfo.mergeFamilyPatientId = patientMergeFamilyPatientId1;
                        }
                    }
                }

                console.log('合并的id在患者中的位置:', this.exitPatientIds.indexOf(postData.mergedPatientInfo.mergeFamilyPatientId));

                // 慢病档案合并数据
                postData.mergedPatientInfo.chronicArchives = this.chronicLabelList.map((item) => {
                    const templateId = item.key;
                    const target = this.showPatientList.find((one) => one.index === item.value);
                    const chronicInfo = target[templateId];
                    const itemInfo = {
                        id: chronicInfo.id,
                        templateId,
                        patientId: target.id,
                    };
                    return itemInfo;
                });
                return postData;
            },
            /**
             * 点击合并患者
             * <AUTHOR>
             * @date 2021-01-21
             */
            onClickMerge() {
                this._messageInstance = null;
                this._messageInstance = this.$message({
                    type: 'warn',
                    title: '确定合并?',
                    content: [`${this.viewDistributeConfig.customerLabel || '患者'}合并后`,'不可恢复'],
                    showClose: false,
                    dialogType: 'tiny',
                    noDialogAnimation: true,
                    dialogContentStyles: 'width: 240px; min-width: 240px',
                    referenceEl: this.$el.querySelector('.abc-dialog'),
                    confirmText: '确认',
                    closeAfterConfirm: false,
                    onConfirm: async () => {
                        this._messageInstance.type = 'loading';
                        this._messageInstance.title = `正在合并${this.viewDistributeConfig.customerLabel || '患者'}`;
                        this._messageInstance.content = '';
                        this._messageInstance.showFooter = false;

                        try {
                            const postData = this.getPostData();
                            const { data } = await CrmAPI.createMergeTask(postData);
                            this.pollingTaskStatus(data.id);
                        } catch (error) {
                            this._messageInstance.close();
                            console.log('createMergeTask error', error);
                        }
                    },
                });
            },
            /**
             * 轮询查看合并task状态
             * 患者合并查询任务返回的状态 2-代表成功 3-代表失败。现在这两个状态都代表患者合并已完成，不对用户显示失败或者成功，只显示患者合并已完成
             * <AUTHOR>
             * @date 2021-01-21
             * @param {String} taskId 任务id
             * @param {Number} delay 延迟毫秒数
             */
            async pollingTaskStatus(taskId, delay = 2000) {
                try {
                    const { data } = await CrmAPI.selectMergeTaskStatus(taskId);
                    if (data.taskStatus === 2 || data.taskStatus === 3) {
                        if (this._messageInstance) {
                            this._messageInstance.type = 'success';
                            this._messageInstance.title = '合并成功';
                            this._messageInstance.successAnimation = true;
                            const _timer = setTimeout(() => {
                                this._messageInstance.close();
                                this.$emit('success');
                                this.$emit('input', false);
                                clearTimeout(_timer);
                            }, 1000);
                        }
                    } else {
                        this.clearTimer(this.statusTimer);
                        if (!this.isDestroy) {
                            this.statusTimer = setTimeout(() => {
                                this.pollingTaskStatus(taskId, delay);
                            }, delay);
                        }
                    }
                } catch (error) {
                    if (this._messageInstance) {
                        this._messageInstance.type = 'warn';
                        this._messageInstance.title = '合并失败';
                        this._messageInstance.showFooter = true;
                        this._messageInstance.confirmText = '确认';
                        this._messageInstance.showCancel = false;
                        this._messageInstance.closeAfterConfirm = true;
                        this._messageInstance.onConfirm = () => {
                            this.$emit('input');
                        };
                    }
                }
            },
            /**
             * 处理定时任务的移除
             * <AUTHOR>
             * @date 2021-01-21
             * @param {Object} timer 定时器句柄
             */
            clearTimer(timer) {
                timer && clearTimeout(timer);
                timer = null;
            },

            getRenderTag(tags) {
                return tags.reduce((tag, cur) => {
                    return cur.tagName.length < tag.tagName.length ? cur : tag;
                }, tags[0]);
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .crm-module__package-merge__dialog-patient-merge {
        .abc-dialog {
            overflow: hidden;
        }

        .step-box {
            @include flex(row, center, center);

            height: 20px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #000000;

            .iconfont {
                margin: 0 16px;
                font-size: 14px;
                color: $P1;
            }

            span.active {
                color: $G1;
            }
        }

        .search-box {
            @include flex(row, space-between, center);

            margin-bottom: 16px;
            font-size: 14px;
            color: $T2;

            .input-box {
                > div:first-child {
                    input {
                        border-right-color: rgba(0, 0, 0, 0);
                        border-top-left-radius: 4px;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                        border-bottom-left-radius: 4px;
                    }
                }

                > div {
                    input {
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                    }
                }
            }
        }

        .choice-box {
            border: 1px solid $P6;
            border-radius: var(--abc-border-radius-small);

            &-card {
                width: calc(100% + 10px);
                max-height: 450px;
                margin-bottom: 16px;
                overflow-y: scroll;
            }

            > div {
                @include flex(row, flex-start, stretch);

                min-height: 32px;
                border-bottom: 1px solid $P6;

                .label-box {
                    @include flex(row, space-between, flex-start);

                    box-sizing: border-box;
                    flex-shrink: 0;
                    width: 100px;
                    padding: 6px 8px;
                    font-size: 14px;
                    line-height: 26px;
                    color: $T2;

                    .cis-icon-Attention {
                        font-size: 14px;
                        color: $Y2;
                    }
                }

                .abc-radio-group {
                    @include flex(row, flex-start, stretch);

                    flex: 1;

                    .value-box {
                        display: flex;
                        flex: 1;
                        align-items: center;
                        padding: 6px 8px;
                        line-height: 26px;
                        border-left: 1px solid $P6;

                        .abc-radio {
                            height: auto;
                            min-height: 16px;

                            .abc-radio-label {
                                white-space: normal;
                            }
                        }

                        > span {
                            line-height: 16px;
                        }

                        .chronic-value {
                            @include flex(column, flex-start, flex-start);
                        }
                    }
                }
            }

            > div:last-child {
                border-bottom: 0;
            }
        }

        .result-box {
            border: 1px solid $P6;
            border-radius: var(--abc-border-radius-small);

            > div {
                @include flex(row, flex-start, stretch);

                color: $T2;
                border-top: 1px solid $P6;

                > div {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .data-item {
                    box-sizing: border-box;
                    flex: 1;
                    padding: 6px 8px;
                    color: $T1;
                    border-right: 1px solid $P6;
                }

                .label {
                    box-sizing: border-box;
                    flex-shrink: 0;
                    width: 100px;
                    padding: 6px 8px;
                    border-right: 1px solid $P6;
                }

                .total {
                    box-sizing: border-box;
                    flex-shrink: 0;
                    width: 130px;
                    padding: 6px 8px;
                    color: $T1;
                }
            }

            > div:first-child {
                color: $T1;
                background-color: $P5;
                border-top: 0;
            }
        }
    }

    .merge-patient {
        .suggestion-title {
            @include flex(row, flex-start, center);

            padding: 0 16px;

            > span:first-child {
                display: inline-block;
                width: 270px;
            }
        }

        .suggestions-item {
            z-index: 1009;
            display: flex;
            align-items: center;
            min-height: 40px;
            padding: 0 16px;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
            user-select: none;
            border-bottom: 1px solid $P3;
            outline: 0;

            &[disabled] {
                color: #687481;
                cursor: not-allowed;
            }

            .name {
                flex: none !important;
                width: 80px;

                .vip::after {
                    display: inline-block;
                    width: 12px;
                    height: 12px;
                    margin-left: 4px;
                    content: '';
                    background: url('~assets/images/vip.png') no-repeat;
                    background-size: 12px 12px;
                }
            }

            .mobile {
                width: 110px;
                padding-left: 10px;
            }

            .age,
            .sex {
                width: 40px;
                text-align: right;
            }

            .mr {
                flex: 1;
                color: $T2;
            }

            &.selected {
                .mr {
                    color: #ffffff;
                }
            }
        }
    }
</style>
