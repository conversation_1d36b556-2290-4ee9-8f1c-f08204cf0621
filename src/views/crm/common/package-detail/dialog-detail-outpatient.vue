<template>
    <abc-dialog
        class="dialog-detail-outpatient"
        title="门诊详情"
        :value="true"
        append-to-body
        size="huge"
        content-styles="padding: 0px;height: 75vh;"
        @input="(val) => $emit('input', val)"
    >
        <section v-abc-loading="loading">
            <template v-if="postData.id">
                <div class="top">
                    <span>{{ fecha.format(new Date(postData.diagnosedDate || postData.created), 'YYYY-MM-DD HH:mm') }}</span>
                    <span class="clinicn">{{ postData.clinicName }}</span>
                    <span>医生：{{ postData.doctorName }}</span>
                    <span
                        v-if="postData.patientOrderNo"
                        class="patient-order-number"
                    >就诊号：{{ postData.patientOrderNo | formatPatientOrderNo }}</span>
                    <print-popper
                        size="small"
                        :width="64"
                        placement="bottom"
                        :box-style="{
                            width: '124px', right: '0px'
                        }"
                        :options="printOptions"
                        style="margin-left: 20px;"
                        @print="printHandler"
                        @select-print-setting="openPrintConfigSettingDialog"
                    ></print-popper>
                </div>

                <abc-form ref="patientForm">
                    <!--病例模块-->
                    <div class="outpatient-form-item is-disabled">
                        <medical-record-module
                            :post-data="postData"
                            :switch-setting="switchSetting"
                            disabled
                            disabled-update-question-form
                            :status="postData.status"
                            :is-support-medical-document="isSupportMedicalDocument"
                        ></medical-record-module>
                    </div>

                    <!--诊疗项目-->
                    <diagnosis-treatment
                        :forms.sync="postData.productForms"
                        :patient-info="postData.patient"
                        :doctor-info="{ departmentName: postData.departmentName }"
                        :patient-order-no="postData.patientOrderNo"
                        :medical-record="postData.medicalRecord"
                        disabled
                    ></diagnosis-treatment>

                    <prescription-group
                        ref="prescriptionGroup"
                        :post-data="postData"
                        disabled
                        :need-check-stock="false"
                        show-cooperation-pharmacy
                        :switch-setting="{
                            ...prescriptionKeys,
                        }"
                    ></prescription-group>

                    <div v-if="doctorAdviceInForm" class="content-wrapper prescription-wrapper">
                        <div class="doctor-advice is-disabled">
                            <label>医嘱事项</label>
                            <abc-form-item>
                                <doctor-advice
                                    v-model="postData.medicalRecord.doctorAdvice"
                                    disabled
                                    :diagnosis="postData.medicalRecord.diagnosis"
                                ></doctor-advice>
                            </abc-form-item>
                        </div>
                    </div>
                </abc-form>
            </template>
        </section>
        <div slot="footer" class="dialog-footer">
            <abc-button
                :width="80"
                :min-width="80"
                variant="ghost"
                @click="$emit('input', false)"
            >
                关闭
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import fecha from 'utils/fecha';
    import OutpatientAPI from 'api/outpatient';
    import MedicalRecordModule from 'views/outpatient/medical-record-module/medical-record-module';
    import DoctorAdvice from 'views/outpatient/common/medical-record/doctor-advise.vue';
    const DiagnosisTreatment = () => import('views/outpatient/diagnosis-treatment/diagnosis-treatment');
    const PrescriptionGroup = () => import('views/outpatient/prescription-group/prescription-group');
    import {
        getMedicalRecordStruct,
        MedicalRecordTypeEnum,
    } from 'views/outpatient/common/medical-record/utils.js';
    import PrintPopper from 'views/print/popper';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import outpatientCommon from 'views/outpatient/common';

    export default {
        name: 'DialogDetailOutpatient',
        components: {
            MedicalRecordModule,
            DoctorAdvice,
            DiagnosisTreatment,
            PrescriptionGroup,
            PrintPopper,
        },
        mixins: [outpatientCommon],
        provide() {
            return {
                outpatientEditForm: this.postData,
            };
        },
        props: {
            id: {
                type: String,
                required: true,
            },
        },
        data() {
            return {
                fecha,
                loading: false,
                postData: {},
                outpatientSheetId: '',
                printable: {},
            };
        },
        computed: {
            ...mapGetters('outpatientConfig', ['outpatientEmployeeConfig']),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            medicalRecordIsNewOralKey() {
                const { medicalRecordIsNewOralKey } = this.viewDistributeConfig.Outpatient || {};
                return medicalRecordIsNewOralKey;
            },
            isSupportMedicalDocument() {
                return this.viewDistributeConfig.Outpatient.isSupportMedicalDocument;
            },
            // 模板里面不看开关都要有，但是需要这个key存在，比如眼科就没有中药处方,眼科配镜不需要模板
            prescriptionKeys() {
                const config = this.outpatientEmployeeConfig;
                const { prescription } = config;
                const allKeys = {};
                for (const key in prescription) {
                    if (
                        key !== 'defaultOpenPrescription' &&
                        key !== 'glassesSwitch' &&
                        prescription.hasOwnProperty(key)
                    ) {
                        allKeys[key] = 1;
                    }
                }
                return allKeys;
            },
            switchSetting() {
                const { medicalRecord } = this.postData || {};
                const switchSetting = getMedicalRecordStruct(medicalRecord);
                if (!this.doctorAdviceInForm && medicalRecord.doctorAdvice) {
                    switchSetting.doctorAdvice = 1;
                }
                return switchSetting;
            },
            examPrintOptions() {
                return this.viewDistributeConfig.Print.examPrintOptions;
            },
            printOptions () {
                const res = [
                    {
                        value: this._printOptions.MEDICAL.label,
                        disabled: false,
                        printCount: this.postData.printable.printCount,
                    },
                    {
                        value: this._printOptions.PRESCRIPTION.label,
                        disabled: this.disabledPrintPR,
                        tips: `没有${this._printOptions.PRESCRIPTION.label}`,
                    },
                    {
                        value: this._printOptions.TREATMENT_EXECUTE.label,
                        disabled: this.disabledPrintTreatmentSheet,
                        tips: `没有${this._printOptions.TREATMENT_EXECUTE.label}`,
                    },
                    {
                        value: this._printOptions.INFUSION_EXECUTE.label,
                        disabled: this.disabledPrintInfusion,
                        tips: `没有${this._printOptions.INFUSION_EXECUTE.label}`,
                    },
                    {
                        value: this.examPrintOptions.examination.label,
                        disabled: this.disabledPrintExamination,
                        tips: `没有${this.examPrintOptions.examination.label}`,
                    },
                    {
                        value: this.examPrintOptions.inspection.label,
                        disabled: this.disabledPrintInspect,
                        tips: `没有${this.examPrintOptions.inspection.label}`,
                    },
                    {
                        value: this._printOptions.MEDICAL_CERTIFICATE.label,
                        disabled: false,
                    },
                ];

                return res;
            },
        },
        async mounted() {
            this.loading = true;
            this._printOptions = this.viewDistributeConfig.Print.printOptions;
            try {
                const { data } = await OutpatientAPI.fetch(this.id);
                if (data) {
                    this.outpatientSheetId = data.id;
                    this.printable = data.printable;
                    data.medicalRecord = data.medicalRecord || {};
                    if (this.medicalRecordIsNewOralKey) {
                        data.medicalRecord.type = MedicalRecordTypeEnum.ORAL;
                    }
                    Object.assign(this.postData, data);
                } else {
                    this.postData = {};
                }
            } catch (error) {
                console.log('mounted error', error);
            }
            this.loading = false;
        },
        methods: {
            printHandler(selected) {
                this.print(selected);
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'outpatient' }).generateDialogAsync({ parent: this });
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/abc-common.scss';

    .dialog-detail-outpatient {
        section {
            box-sizing: border-box;
            width: 100%;
            height: 100%;
            padding: 24px;
            overflow-x: hidden;

            .top {
                margin-bottom: 16px;
                font-size: 14px;
                line-height: 1;
                color: $T3;

                @include flex(row, flex-start, center);

                > span {
                    flex-shrink: 0;
                    margin-right: 24px;
                }

                .clinicn {
                    flex: 1;
                    flex-shrink: 1;

                    @include ellipsis(1);
                }

                .patient-order-number {
                    float: right;
                    margin-right: 0;
                }
            }

            .card {
                padding: 6px 12px;
                line-height: 1.6;
                background-color: #f7f7f7;
                border: 1px solid $P6;
                border-radius: var(--abc-border-radius-small);

                .item {
                    @include flex(row, flex-start, flex-start);

                    margin-bottom: 16px;

                    label {
                        width: 80px;
                        font-weight: 500;
                        color: $T1;
                    }

                    .cont {
                        flex: 1;
                        color: $T2;
                        word-break: break-all;

                        @include flex(column, flex-start, flex-start);

                        > div {
                            @include flex(row, flex-start, center);
                        }

                        img.return {
                            width: 30px;
                            height: 18px;
                            margin-left: 4px;
                        }
                    }
                }

                :last-child {
                    margin-bottom: 0;
                }
            }

            .examination-wrapper,
            .treatment-wrapper {
                .title {
                    font-weight: bold;
                    color: $T1;
                }
            }

            .western-prescription-wrapper,
            .chinese-prescription-wrapper {
                margin: 8px 0;
            }
        }
    }
</style>
