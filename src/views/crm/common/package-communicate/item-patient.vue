<template>
    <div
        class="crm-module__package-communicate__item-patient"
        :class="{ 'active': active }"
    >
        <div class="head-img-box">
            <abc-avatar
                :url="patientHeadImg"
                size="large"
                :border-visible="!active"
                border-hidden-color="#00ace9"
            ></abc-avatar>

            <span v-if="!!info.unReadCount" class="badge">{{ info.unReadCount }}</span>
        </div>
        <div class="cont">
            <div class="top">
                <div>
                    <span class="name">{{ info.patientName }}</span>
                </div>
                <span class="date">{{ lastMessageDateStr }}</span>
            </div>
            <div class="bot">
                {{ lastMessageContent }}
            </div>
        </div>
    </div>
</template>

<script>
    import { formatCacheTime } from 'utils/index';
    import { MsgTypeStatus } from './constants';
    import AbcAvatar from 'src/views/layout/abc-avatar/index.vue';

    const {
        MessageStatusEnum, FromUserTypeEnum,
    } = require('views/crm/constants');


    export default {
        name: 'ItemPatient',
        components: {
            AbcAvatar,
        },
        props: {
            info: Object,
            active: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                lastMessageContent: '',
            };
        },
        computed: {
            //患者头像
            patientHeadImg() {
                const { patientHeadImgUrl } = this.info;
                return patientHeadImgUrl;
            },
            //最近消息发送时间
            lastMessageDateStr() {
                const { created } = this.info.lastImMessage || {};
                return created ? formatCacheTime(created) : '';
            },
        },
        watch: {
            info: {
                handler(val) {
                    if (val) {
                        this.setLastMessageContent();
                    }
                },
                deep: true,
            },
        },
        mounted() {
            this.setLastMessageContent();
        },
        methods: {
            setLastMessageContent() {
                const {
                    body, msgType, status = 0, fromUserType,
                } = this.info?.lastImMessage || {};
                const isMine = fromUserType !== FromUserTypeEnum.PATIENT_MSG;
                if (status === MessageStatusEnum.REVOKE) {
                    this.lastMessageContent = `${isMine ? '你' : '患者'}撤回了一条消息`;
                    return;
                }
                let content = '';
                switch (msgType) {
                    case MsgTypeStatus.MSG_TEXT://文字
                        content = body?.text || '';
                        break;
                    case MsgTypeStatus.MSG_IMAGE://图片
                        content = '[图片]';
                        break;
                    case MsgTypeStatus.MSG_AUDIO://音频
                        content = '[音频]';
                        break;
                    case MsgTypeStatus.REG_RECORD_MESSAGE:
                        content = '[预约记录]';
                        break;
                    case MsgTypeStatus.REG_MODE_MESSAGE:
                        content = '[选择预约模式]';
                        break;
                    case MsgTypeStatus.REG_DOCTOR_MESSAGE:
                        content = '[项目预约引导]';
                        break;
                    case MsgTypeStatus.REPORT_MESSAGE:
                        content = '[就诊报告]';
                        break;
                    case MsgTypeStatus.REG_MESSAGE:
                        content = '[医生预约引导]';
                        break;
                    case MsgTypeStatus.DOCTOR_TIME_MESSAGE:
                        content = '[医生坐诊表]';
                        break;
                    case MsgTypeStatus.PROJECT_MESSAGE:
                        content = '[项目预约]';
                        break;
                    case MsgTypeStatus.REG_CARD_SUCCESS_MESSAGE:
                        content = '[预约成功]';
                        break;
                    case MsgTypeStatus.REG_CARD_REQUEST_MESSAGE:
                        content = '[预约申请]';
                        break;
                    case MsgTypeStatus.REG_CARD_CANCEL_MESSAGE:
                        content = '[预约取消]';
                        break;
                    default:
                        break;
                }
                this.lastMessageContent = content;
            },
        },
    };
</script>

<style lang="scss">
@import "styles/abc-common.scss";

.crm-module__package-communicate__item-patient {
    //border-bottom: 1px solid $P6;
    width: 100%;
    padding: 12px 16px;
    cursor: pointer;
    background-color: #ffffff;

    @include flex(row, flex-start, center);

    &:hover {
        &:not(.active) {
            background-color: var(--abc-color-cp-grey4) !important;
            border-radius: var(--abc-border-radius-small);
        }
    }

    .head-img-box {
        position: relative;
        display: inline-block;
        flex-shrink: 0;
        width: 40px;
        height: 40px;

        img {
            width: 100%;
            height: 100%;
        }

        .badge {
            position: absolute;
            top: -8px;
            right: -8px;
            min-width: 16px;
            height: 16px;
            padding: 0 4px;
            font-size: 12px;
            line-height: 16px;
            color: #ffffff;
            text-align: center;
            background-color: #ff3333;
            border-radius: 8px;
        }
    }

    .cont {
        flex: 1;
        height: 40px;
        margin-left: 12px;

        @include flex(column, center, flex-start);

        .top {
            width: 100%;
            height: 20px;

            @include flex(row, flex-start, center);

            > div {
                flex: 1;
                height: 100%;

                @include flex(row, flex-start, center);

                .name {
                    font-size: 14px;
                    color: $T1;

                    @include ellipsis(1);
                }
            }

            .date {
                flex-shrink: 0;
                font-size: 12px;
                color: $T3;
            }
        }

        .bot {
            height: 16px;
            margin-top: 4px;
            font-size: 12px;
            color: $T2;

            @include ellipsis(1);

            .bot-no-read {
                margin-right: 4px;
                color: #ff9933;
            }
        }
    }

    &.active {
        background-color: var(--abc-color-theme2);
        border-radius: var(--abc-border-radius-small);

        .cont {
            .name,
            .date,
            .bot {
                color: #ffffff !important;
            }
        }
    }
}
</style>
