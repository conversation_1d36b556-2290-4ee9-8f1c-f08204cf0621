<template>
    <abc-dialog
        title="微信沟通"
        class="crm-module__dialog-wechat-communicate"
        :value="true"
        size="hugely"
        disabled-keyboard
        :content-styles="contentStyles"
        @input="(val) => !val && muClosePatientCommunicate()"
    >
        <div slot="title" class="crm-module__dialog-wechat-communicate--title">
            微信沟通<div v-if="wechatPatientSendMsgTotal" class="crm-module__dialog-wechat-communicate--notice" @click="scrollToByNearWaitReadMsg">
                {{ wechatPatientSendMsgTotal }}
            </div>
            <div v-if="!isOpenScrm" class="crm-module__dialog--scrm">
                <abc-image
                    :src="logo"
                    :height="14"
                    :width="92"
                    style="margin-right: 8px;"
                ></abc-image>
                企业微信与患者直接连通
                <span style="margin-left: auto;" @click="toScrmDetail">
                    查看详情
                </span>
                <abc-image
                    :src="newImg"
                    :width="30"
                    :height="14"
                    class="new-img"
                ></abc-image>
            </div>

            <abc-button
                variant="text"
                theme="default"
                style="z-index: 999; margin-right: 32px; margin-left: auto;"
                size="large"
                icon="s-b-settings-line"
                @click="onClickConfigDialog"
            ></abc-button>
        </div>
        <div class="left-box">
            <div class="header">
                <div class="search-box">
                    <abc-autocomplete
                        class="patient-autocomplete-wrapper"
                        custom-class="patient-autocomplete-suggestions-popper crm-module__dialog-wechat-communicate__search-follow-wechat"
                        :value="keyword"
                        :inner-width="290"
                        :width="148"
                        :delay-time="0"
                        async-fetch
                        placeholder="搜索姓名"
                        :fetch-suggestions="querySearchAsync"
                        :max-length="20"
                        clearable
                        @enterEvent="onClickTargetPatient"
                        @clear="keyword = ''"
                    >
                        <template slot="prepend">
                            <abc-icon icon="search" size="14" color="#D9DBE3"></abc-icon>
                        </template>
                        <template slot="suggestion-header">
                            <div class="suggestion-title">
                                <div>搜索到的患者</div>
                            </div>
                        </template>
                        <template slot="suggestions" slot-scope="props">
                            <dt
                                class="patient-suggestions suggestions-item"
                                :class="{ selected: props.index == props.currentIndex }"
                                @click="onClickTargetPatient(props.suggestion)"
                            >
                                <item-search-res
                                    :active="props.index == props.currentIndex"
                                    :patient-info="props.suggestion.patient"
                                ></item-search-res>
                            </dt>
                        </template>
                        <div v-if="keyword && !loadingSearch && !!keyword" slot="empty">
                            <span>暂无患者</span>
                        </div>
                    </abc-autocomplete>
                    <abc-button
                        type="blank"
                        style="width: 72px; min-width: 72px;"
                        :disabled="!conversationGroups.length"
                        @click="onClickReadAll"
                    >
                        全部已读
                    </abc-button>
                </div>
                <div v-if="isSearchResult" class="search-result">
                    搜索到
                    <span class="num">{{ dataList.length }}</span>
                    条消息
                </div>
            </div>
            <div
                id="patient-communicate-list"
                v-abc-loading="loadingGroup"
                v-abc-scroll-loader="{
                    methods: fetchMorePatientList, isLast: converParams.isLast
                }"
                class="list-wrapper"
            >
                <div class="list-wrapper-layout">
                    <div v-if="conversationGroups.length === 0 && !loadingGroup" key="no-data" class="no-data">
                        <span>暂无数据</span>
                    </div>
                    <abc-list
                        size="large"
                        :create-key="(e)=>{
                            return e.conversationId
                        }"
                        show-divider
                        style="min-height: 100%; overflow-y: auto !important;"
                        :custom-padding="[0, 0]"
                        :data-list="conversationGroups"
                    >
                        <template
                            #default="{
                                item
                            }"
                        >
                            <item-patient
                                :key="item.conversationId"
                                :info="item"
                                :active="item.conversationId === conversationId"
                                @click.native="fetchAboutPatientInfo(item)"
                            ></item-patient>
                        </template>
                    </abc-list>
                </div>


                <!--                <transition-group-->
                <!--                    name="group-list"-->
                <!--                    class="list"-->
                <!--                    tag="div"-->
                <!--                >-->
                <!--                    <div v-if="conversationGroups.length === 0 && !loadingGroup" key="no-data" class="no-data">-->
                <!--                        <span>暂无数据</span>-->
                <!--                    </div>-->
                <!--                    <item-patient-->
                <!--                        v-for="item in conversationGroups"-->
                <!--                        :key="item.conversationId"-->
                <!--                        :info="item"-->
                <!--                        :active="item.conversationId === conversationId"-->
                <!--                        @click.native="fetchAboutPatientInfo(item)"-->
                <!--                    ></item-patient>-->
                <!--                </transition-group>-->
            </div>
        </div>
        <div class="cont-box">
            <div :class="['message-list',{ 'height-580': is1080Screen }]">
                <div v-if="isHiddenRegisterAppointmentCommunicateTips" class="register-appointment_communicate--tips-top-box">
                    预约申请：<span class="register-appointment_communicate--tips-top-box-time">{{ registerAppointmentPatient.appointmentTime.dateStr }} {{ registerAppointmentPatient.appointmentTime.dateTimeSection }}</span>
                    <span class="register-appointment_communicate--tips-top-box-doctor-name">{{ registerAppointmentPatient.doctorName }}</span>
                    <span class="register-appointment_communicate--tips-top-box-project">{{ registerAppointmentPatient.project }}</span>
                    <span class="register-appointment_communicate--tips-top-box-register" @click="openAppointmentCard(SimpleAppointCardRequestTypeEnum.FROM_TIPS)">去预约</span>
                    <span class="register-appointment_communicate--tips-top-box-reject" @click="refuseAppointmentRequest('near',{})">拒绝</span>
                </div>
                <view-message
                    v-if="!!conversationId && !!conversationInfo"
                    ref="view-message"
                    :user-id="userInfo?.id"
                    :clinic-id="currentClinic?.clinicId"
                    :conversation-id="conversationId"
                    :conversation-info="conversationInfo"
                    @editText="editText"
                    @update-last-message="stickTargetConversation"
                    @refresh-conversation-info="fetchConversationInfo"
                    @send-text-again="(body) => onSendText(body, false)"
                    @send-image-again="(body) => onSendImage(body)"
                    @revoke="revokeMessage"
                    @changeLastMessage="changeLastMessage"
                    @revokeUnReadCount="revokeUnReadCount"
                    @handleRegisterAppointment="handleRegisterAppointment"
                ></view-message>
            </div>
            <div
                :class="{
                    'control-box': true,
                    disabled: editDisabled,
                }"
            >
                <div class="hand-box">
                    <div class="left">
                        <view-emoji
                            v-model="visibleEmojiList"
                            placement="top-start"
                            @add-emoji="handleEditHtml('insertEmoji', ...arguments)"
                        >
                            <span class="iconfont cis-icon-emoji" @click="visibleEmojiList = true"></span>
                        </view-emoji>
                        <common-reply
                            v-model="visibleCommonText"
                            :user-id="userInfo?.id"
                            @send-common-reply="(text) => onSendText({ text }, false)"
                        >
                            <span class="iconfont cis-icon-message_common" @click="visibleCommonText = true"></span>
                        </common-reply>
                        <div class="upload-image" style="cursor: pointer;">
                            <span class="iconfont cis-icon-pic"></span>
                            <input
                                v-if="showInputFile"
                                type="file"
                                :title="''"
                                accept="image/*"
                                @change="onChangeFile"
                            />
                        </div>
                    </div>
                    <div class="right">
                        <div v-if="isOpenWechatWindowRegisterAppointment" class="appointment-board" @click="openRegisterAppointmentSimpleDialog">
                            <div class="appointment-board_box--handle">
                                <abc-icon icon="board" size="14"></abc-icon>
                                预约看板
                            </div>
                        </div>
                        <div class="quick-appointment">
                            <div class="quick-appointment-card-box">
                                <appointment-card
                                    v-if="showAppointmentCard"
                                    ref="appointment-card"
                                    v-model="showAppointmentCard"
                                    :is-reserved="1"
                                    appointment-model="waitAppointment"
                                    form-source="wechatCommunication"
                                    :show-edit-btn="false"
                                    :readonly="sourceSimpleType === SimpleAppointCardRequestTypeEnum.FROM_RECORD"
                                    :department-id="waitToAppointmentPatientInfo.departmentId"
                                    :time-range="waitToAppointmentPatientInfo.timeRange"
                                    :business-type="waitToAppointmentPatientInfo.businessType"
                                    :item-patient="waitToAppointmentPatientInfo.itemPatient"
                                    :pay-status-v2="waitToAppointmentPatientInfo.payStatusV2"
                                    :registration-sheet-id="waitToAppointmentPatientInfo.registrationSheetId"
                                    :departments="departments"
                                    :doctor-id="waitToAppointmentPatientInfo.doctorId"
                                    :registration-id="waitToAppointmentPatientInfo.registrationId"
                                    :registration-category="waitToAppointmentPatientInfo.registrationCategory"
                                    :fee="waitToAppointmentPatientInfo.fee"
                                    :has-departments="hasDepartments"
                                    :item-registration-product-ids="waitToAppointmentPatientInfo.itemRegistrationProductIds"
                                    :date="waitToAppointmentPatientInfo.reserveDate || wechatAppointmentCard"
                                    :style="appointmentCardStyle"
                                    :disabled-patient="disabledModifyAppointmentTypes.includes(sourceSimpleType)"
                                    @handleAppointmentCard="handleAppointmentCard"
                                ></appointment-card>
                            </div>

                            <div v-if="isOpenWechatWindowRegisterAppointment" class="appointment-board_box--handle" @click="openAppointmentCard(SimpleAppointCardRequestTypeEnum.DEFAULT)">
                                <abc-icon icon="quick" size="14"></abc-icon>
                                快速预约
                            </div>
                        </div>
                    </div>
                </div>
                <div class="edit-box">
                    <view-edit-html
                        v-if="!editDisabled"
                        ref="edit-html"
                        @send-text="(text) => onSendText({ text })"
                    ></view-edit-html>
                </div>
                <div class="disabled-status"></div>
            </div>
        </div>
        <div
            v-abc-scroll-loader="{
                methods: fetchMoreTrackList, isLast: pageParams.isLast
            }"
            class="right-box"
        >
            <div v-if="patientId" class="right-box-scroll-box">
                <div class="right-box-scroll">
                    <div class="patient-box">
                        <div class="top">
                            <span>患者资料</span>
                        </div>
                        <div
                            class="patient-info"
                            @click="handleShowPatientCard"
                        >
                            <div class="left">
                                <div
                                    v-if="!!patientInfo"
                                    class="base-info"
                                >
                                    <span class="name">
                                        {{
                                            patientInfo.name
                                        }}
                                        <img
                                            v-if="patientInfo.isMember === 1"
                                            src="~assets/images/crm/<EMAIL>"
                                            alt="vip"
                                        />
                                    </span>
                                    <span class="sex">
                                        {{ patientInfo.sex }}
                                    </span>
                                    <span class="age">
                                        {{
                                            patientInfo.age | formatAge({
                                                monthYear: 12,
                                                dayYear: 1,
                                                textGap: ''
                                            })
                                        }}
                                    </span>
                                    <span class="mobile">
                                        {{ patientInfo.mobile }}
                                    </span>
                                </div>
                                <div v-if="patientTags.length" class="patient-info_tags">
                                    <div
                                        v-for="tag in patientTags"
                                        :key="tag.tagId"
                                        class="patient-info_tags--model"
                                    >
                                        {{ tag.tagName }}
                                    </div>
                                </div>
                            </div>

                            <div class="right">
                                <abc-icon icon="Arrow_Rgiht" class="icon iconfont cis-icon-Arrow_Rgiht"></abc-icon>
                            </div>
                        </div>
                    </div>
                    <div v-if="futureList.length !== 0" class="visit-box">
                        <div class="top">
                            <span>待办随访</span>
                        </div>
                        <div class="list">
                            <item-visit-card
                                v-for="item in showFutureList"
                                :key="item.id"
                                :visit-detail="item"
                                @open-detail="onClickOpenDetail(item)"
                            ></item-visit-card>
                            <span v-if="futureList.length > 1" class="more" @click="displayAll = !displayAll">
                                {{ displayAll ? '收起' : '展开全部' }}
                            </span>
                        </div>
                    </div>
                    <div v-else class="margin-top" style="margin-top: 8px;"></div>
                    <div v-abc-loading="loadingInfo" class="track-box">
                        <div class="top">
                            <span>患者轨迹</span>
                        </div>
                        <div v-if="showTrackDataList.length !== 0" class="track-info">
                            <abc-list
                                size="large"
                                :create-key="(e)=>{
                                    return e.id
                                }"
                                show-divider
                                :no-hover-border="false"
                                style="overflow-y: auto !important;"
                                :custom-padding="[0, 0]"
                                :data-list="showTrackDataList"
                            >
                                <template
                                    #default="{
                                        item, index
                                    }"
                                >
                                    <item-outpatient
                                        v-if="item.action === ActionStatus.OUTPATIENT_STATUS"
                                        :key="index"
                                        :info="item"
                                        style="width: 100%;"
                                        @look-detail="handleOpenDetail('handleShowOutpatientInfo', ...arguments)"
                                    ></item-outpatient>
                                    <item-register-appointment-card
                                        v-if="item.action === ActionStatus.REGISTER_APPOINTMENT_STATUS"
                                        :key="index"
                                        :info="item"
                                        style="width: 100%;"
                                        @openAppointmentCardFromTrack="openAppointmentCardFromTrack"
                                    >
                                    </item-register-appointment-card>
                                    <item-retail
                                        v-if="item.action === ActionStatus.RETAIL_STATUS"
                                        :key="index"
                                        :info="item"
                                        style="width: 100%;"
                                        @look-detail="handleOpenDetail('handleShowChargeInfo', ...arguments)"
                                    ></item-retail>
                                    <item-visit
                                        v-if="item.action === ActionStatus.FOLLOW_UP_STATUS"
                                        :key="index"
                                        :info="item"
                                        style="width: 100%;"
                                        @look-detail="handleOpenDetail('handleShowVisitInfo', ...arguments)"
                                    ></item-visit>
                                </template>
                            </abc-list>
                        </div>
                        <div v-if="showTrackDataList.length === 0 && !loadingInfo" class="no-data">
                            <span>暂无数据</span>
                        </div>
                    </div>
                    <view-order-detail ref="order-detail" from-source="wechat" append-to-body></view-order-detail>
                </div>
            </div>
            <div v-if="!patientId && !loadingInfo && !loadingGroup" class="no-data">
                <span>暂无数据</span>
            </div>
        </div>
        <patient-base-card
            v-if="showPatientCard"
            ref="patient-base-card"
            v-abc-click-outside="onClickOutside"
            class="patient-base-card"
            :patient-id="patientId"
            :default-patient="patientInfo"
            @change-patient="updatePatientInfo"
            @close-card="showPatientCard = false"
        ></patient-base-card>
        <!--弹窗遮罩层-->
        <div v-if="showAppointmentCard && !isChainAdmin" class="quick-appointment-cover" @click="handleOutside"></div>
        <refund-dialog
            v-if="showRefund"
            :id="chargeSheet.id"
            ref="refundPro"
            v-model="showRefund"
            :append-to-body="true"
            :member-id="chargeSheet.memberId"
            :forms="chargeSheet.chargeForms"
            :discount-fee="chargeSheet.chargeSheetSummary.discountFee"
            :net-income-fee="chargeSheet.chargeSheetSummary.netIncomeFee"
            :owed-refund-fee="chargeSheet.chargeSheetSummary.owedRefundFee"
            :adjustment-fee="chargeSheet.chargeSheetSummary.adjustmentFee"
            :net-adjustment-fee="chargeSheet.chargeSheetSummary.netAdjustmentFee"
            @confirm="refundConfirm"
        >
            <span slot="tips">&nbsp;</span>
        </refund-dialog>
        <refund-way-dialog
            v-if="showRefundWayList"
            ref="refundWay"
            v-model="showRefundWayList"
            :charge-sheet-id="chargeSheet.id"
            :refund-fee="refundTotalFee"
            :refund-data="refundData"
            :refund-type="curRefundType"
            :payment-summary-infos.sync="chargeSheet.chargeSheetSummary.paymentSummaryInfos"
            :charge-transactions="chargeSheet.chargeTransactions"
            :charge-config="chargeConfig"
            @finish="refundFinish"
        ></refund-way-dialog>
        <package-communicate-config-dialog v-if="showConfigDialog" v-model="showConfigDialog"></package-communicate-config-dialog>
    </abc-dialog>
</template>

<script>
    import ItemPatient from './item-patient.vue';
    const ViewMessage = () => import('./view-message.vue');
    import CommonReply from './common-reply.vue';
    import ItemVisitCard from './item-visit-card.vue';
    import ItemSearchRes from './item-search-res.vue';
    import ViewEditHtml from './view-edit-html';
    const ViewEmoji = () => import('./view-emoji.vue');

    import ItemVisit from '../package-track/item-visit.vue';
    import ItemRetail from '../package-track/item-retail.vue';
    import ItemOutpatient from '../package-track/item-outpatient.vue';
    import ItemRegisterAppointmentCard from '../package-track/item-register-appointment-card.vue';
    import ViewOrderDetail from '../package-detail/view-order-detail.vue';
    import { NavigateHelper } from '@/core/index.js';
    import { PayStatusV2 } from 'views/registration/common/constants';
    import CrmAPI from 'api/crm';
    import ApiIM from 'api/im';
    import fecha from 'utils/fecha';
    import {
        formatAge, parseTime,
    } from 'utils/index';
    import { debounce } from 'utils/lodash';
    import {
        DATE_FORMATE, DAY_TIMES,
    } from 'assets/configure/constants.js';
    import {
        mapGetters, mapMutations, mapActions,
    } from 'vuex';
    import {
        RefundTypeEnum,
    } from '@/service/charge/constants';
    import MixinIm from './mixin-im';
    import MixinSocket from '../mixin-socket';
    import MixinUpload from '../mixin-upload';
    import PatientBaseCard from 'views/layout/patient/base-card-dentistry';
    import AppointmentCard from 'src/views-dentistry/registration/appointment-card.vue';
    import RegistrationsAPI from 'api/registrations/index';
    import {
        getRegisterAppointmentPatient, handleRegisterAppointmentCardModel,
    } from 'utils/register-appointment-patient-model';
    // 图片上传支持类型
    const UPLOAD_IMAGE_TYPES = ['jpg', 'png', 'gif', 'jpeg'];
    // 最大上次文件大小20M
    const UPLOAD_MAX_SIZE = 1024 * 1024 * 20;
    import { ActionStatus } from 'views/crm/constants';
    import {
        WaitAppointMsgStatus,
        SimpleAppointCardRequestTypeEnum,
    } from 'src/views/crm/common/package-communicate/constants';
    import { isMessageIgnored } from 'views/crm/handle-crm';

    import Logo from '@/assets/images/scrm/logo-scrm.png';
    import NewImg from '@/assets/images/scrm/<EMAIL>';
    // 禁止修改的类型
    const disabledModifyAppointmentTypes = [
        SimpleAppointCardRequestTypeEnum.FROM_TIPS,
        SimpleAppointCardRequestTypeEnum.FROM_LIST,
    ];
    import AppointmentCardDialog from '@/views-dentistry/registration/appointment-card-dialog';
    const RefundWayDialog = () => import('views/cashier/refund-way-dialog');
    const RefundDialog = () => import('views/cashier/refund-dialog');
    import PackageCommunicateConfigDialog
        from 'views/crm/common/package-communicate/package-communicate-config-dialog.vue';
    export default {
        name: 'DialogWechatCommunicate',
        components: {
            ItemPatient,
            ViewMessage,
            ViewEmoji,
            CommonReply,
            ItemVisitCard,
            ItemSearchRes,
            ItemRegisterAppointmentCard,
            ItemVisit,
            ItemRetail,
            ItemOutpatient,
            ViewOrderDetail,
            ViewEditHtml,
            PatientBaseCard,
            AppointmentCard,
            RefundWayDialog,
            RefundDialog,
            PackageCommunicateConfigDialog,
        },
        mixins: [MixinIm, MixinSocket, MixinUpload],
        provide() {
            return {
                isOpenWechatWindowRegisterAppointment: this.isOpenWechatWindowRegisterAppointment,
            };
        },
        props: {
            // 置顶患者<conversationId>
            stickConversationId: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                ActionStatus,
                SimpleAppointCardRequestTypeEnum,
                disabledModifyAppointmentTypes,
                WaitAppointMsgStatus,
                loadingSearch: false,
                hasDepartments: true,
                departments: [],
                keyword: '',
                dataList: [],
                isSearchResult: false,
                visibleEmojiList: false,
                visibleCommonText: false,
                wechatAppointmentCard: parseTime(new Date(), 'y-m-d', true),
                // 左侧聊天列表
                loadingGroup: false,
                topConversationId: '', // 置顶会话id
                conversationGroups: [],
                converParams: {
                    limit: 100,
                    isLast: false,
                },
                // 当前选中会话
                currentGroupChat: null,
                // 当前会话详情
                conversationInfo: null,
                // 患者基本信息
                patientInfo: null,
                appointmentPatientInfo: null,
                patientBaseInfo: {
                    outpatientCount: '',
                    retailCount: '',
                    cumulativeAmount: '',
                },
                sourceSimpleType: SimpleAppointCardRequestTypeEnum.DEFAULT, //简单预约来源 0 正常快速预约 1来自预约tips提示发起预约 2患者轨迹查看 3来自聊天列表发起预约
                currentChoiceFutureAppointmentPatient: null,// 当前选中的预约患者
                loadingInfo: false,
                futureList: [], // 近三个月未执行随访任务
                displayAll: false, // 是否展示全部未执行随访
                trackDataList: [], // 患者轨迹
                pageParams: {
                    offset: 0,
                    limit: 30,
                    isLast: false,
                },

                visiblePatientCard: false,
                showPatientCard: false,
                showInputFile: true, // 是否显示图片上传input,主要用户刷新input
                showAppointmentCard: false,
                showConfigDialog: false,
                storageWaitReadMsgList: [],// 存储待阅读消息列表
                registrationSheetId: '', // 预约申请的id
                registrationSheetIdFromList: '',
                appointmentPatientInfoFromList: null,
                patientDetail: {},
                logo: Logo,
                newImg: NewImg,
                chargeSheet: {},
                showRefund: false,
                showRefundWayList: false,
                refundTotalFee: 0, // 需要退的费用
                refundData: {}, // 项目退费时，{chargeForms, refundFee, needRefundFee}
                curRefundType: '', // 当前退费类型
            };
        },
        computed: {
            ...mapGetters(['userInfo', 'currentClinic', 'isChainAdmin', 'lastCommunicatePatient', 'isOpenScrm', 'chargeConfig']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            // 是否开启微信聊天窗口的预约功能
            isOpenWechatWindowRegisterAppointment() {
                return this.viewDistributeConfig.Registration.isOpenWechatWindowRegisterAppointment && !this.isChainAdmin;
            },
            // 是否隐藏挂号预约对话框tips
            isHiddenRegisterAppointmentCommunicateTips() {
                // 暂时屏蔽
                return this.registerAppointmentPatient && !this.isChainAdmin;
            },
            //
            waitToAppointmentPatientInfo() {
                let registrationAppointmentObject = {};
                let patientDetail = {};
                switch (this.sourceSimpleType) {
                    case this.SimpleAppointCardRequestTypeEnum.FROM_TIPS:
                        registrationAppointmentObject = this.appointmentPatientInfo;
                        patientDetail = this.patientInfo;
                        break;
                    case this.SimpleAppointCardRequestTypeEnum.FROM_RECORD:
                        registrationAppointmentObject = this.currentChoiceFutureAppointmentPatient;
                        break;
                    case this.SimpleAppointCardRequestTypeEnum.FROM_LIST:
                        registrationAppointmentObject = this.appointmentPatientInfoFromList;
                        break;
                    case this.SimpleAppointCardRequestTypeEnum.DEFAULT:
                        registrationAppointmentObject = this.patientInfo;
                        break;
                    default:
                        break;
                }
                return handleRegisterAppointmentCardModel(this.sourceSimpleType, registrationAppointmentObject, patientDetail);
            },
            // 挂号预约患者信息
            registerAppointmentPatient() {
                return this.getRegisterAppointmentPatient(this.appointmentPatientInfo);
            },
            // 微信患者发送消息总数
            wechatPatientSendMsgTotal() {
                return this.conversationGroups?.reduce((sum, cur) => {return sum + (cur?.unReadCount || 0); },0) || 0;
            },
            // 消息接受者-患者
            patientId() {
                return this.currentGroupChat ? this.currentGroupChat.patientId : '';
            },
            // 当前会话标识
            conversationId() {
                return this.currentGroupChat ? this.currentGroupChat.conversationId : '';
            },
            // 是否可以编辑输入框
            editDisabled() {
                return !this.conversationId;
            },
            // 患者标签
            patientTags() {
                const { tags = [] } = this.patientInfo || {};
                return tags;
            },
            // 显示待随访任务
            showFutureList() {
                if (this.displayAll) {
                    return this.futureList;
                }
                return this.futureList.slice(0, 1);

            },
            // 显示的轨迹
            showTrackDataList() {
                return this.trackDataList.filter(
                    (item) => item.action === '门诊' || item.action === '零售' || item.action === '随访' || item.action === '预约',
                );
            },
            is1080Screen() {
                return window.screen.height === 1080;
            },
            contentStyles() {
                return this.is1080Screen ?
                    'height: 722px;' :
                    'height: 592px;';
            },
            appointmentCardStyle() {
                return 'position: relative;z-index: 2000;';
            },
        },
        watch: {
            conversationId(newValue) {
                this.muSetCurrentConverrsationId(newValue);
            },
            patientId: {
                handler(val) {
                    val && this.fetchPatientOverview(val);
                },
                immediate: true,
            },
            wechatPatientSendMsgTotal(newValue) {
                // 表示消息有新增或者减少 消息为0入口会消失，此时不必处理数据
                if (newValue) {
                    this.storageWaitReadMsgList = this.getWaitReadMsgList();
                }
            },
        },
        async created() {
            this._acFetchUnreadMessageCount = debounce(this.acFetchUnreadMessageCount, 2000, 200);
            this.initConversationGroup(this.stickConversationId);
            this.fetchDepartmentsDoctors();
            this.fetchRegistrationFee();
            this.$store.dispatch('initChargeConfig');
        },
        methods: {
            formatAge,
            getRegisterAppointmentPatient,
            ...mapActions(['fetchWaitRegisterAppointmentPatientList']),
            ...mapActions(['fetchRegistrationFee']),
            ...mapMutations('crm', ['muClosePatientCommunicate', 'muSetCurrentConverrsationId']),
            ...mapActions('crm', ['acFetchUnreadMessageCount', 'setLastCommunicatePatient']),
            async refundFinish() {
                this.showRefundWayList = false;
                this.$nextTick(() => {
                    this.showRefund = false;
                });
                this.$Toast({
                    type: 'success', message: '退款成功',
                });
                await this.initWaitAppointmentInfo();
            },
            // 获取当前待阅读列表
            async refreshWaitAppointment() {
                this.showAppointmentCard = false;
                window.removeEventListener('keyup', this._escListener, false);
                await this.fetchOwnWaitAppointmentInfo();
            },
            async openAppointmentCardFromTrack(item) {
                const { registrationSheetId } = item.actionAbstract;
                try {
                    const { data } = await RegistrationsAPI.fetch(registrationSheetId);
                    this.currentChoiceFutureAppointmentPatient = data;
                    await this.openAppointmentCard(this.SimpleAppointCardRequestTypeEnum.FROM_RECORD);
                } catch (e) {
                    console.log(e);
                }
            },
            // 打开预约小卡片
            async openAppointmentCard(type) {
                if (this.appointmentPatientInfo?.registrationSheetId && !this.appointmentPatientInfo?.registrationFormItem) {
                    const { data } = await RegistrationsAPI.fetch(this.appointmentPatientInfo.registrationSheetId);
                    if (data) {
                        this.appointmentPatientInfo.registrationFormItem = data.registrationFormItem;
                    }
                }
                this._escListener = (e) => {
                    if (e.keyCode === 27) {
                        this.showAppointmentCard = false;
                    }
                };
                window.addEventListener('keyup', this._escListener, false);
                this.sourceSimpleType = type;
                if (this.sourceSimpleType === this.SimpleAppointCardRequestTypeEnum.FROM_RECORD) {
                    this.showAppointmentCard = true;
                    return;
                }
                // 打开预约弹窗
                this.$nextTick(() => {
                    window.setTimeout(async () => {
                        await this.fetchDepartmentsDoctors(this.waitToAppointmentPatientInfo?.businessType);
                        this.openAppointmentCardDialog();
                    }, 50);
                });
            },
            openAppointmentCardDialog(extendCardProps = {}) {
                this._appointmentCardDialogInstance = new AppointmentCardDialog(Object.assign({
                    value: true,
                    isReserved: 1,
                    appointmentModel: 'waitAppointment',
                    formSource: 'wechatCommunication',
                    departmentId: this.waitToAppointmentPatientInfo.departmentId,
                    timeRange: this.waitToAppointmentPatientInfo.timeRange,
                    businessType: this.waitToAppointmentPatientInfo.businessType,
                    itemPatient: this.waitToAppointmentPatientInfo.itemPatient,
                    payStatusV2: this.waitToAppointmentPatientInfo.payStatusV2,
                    registrationSheetId: this.waitToAppointmentPatientInfo.registrationSheetId,
                    departments: this.departments,
                    doctorId: this.waitToAppointmentPatientInfo.doctorId,
                    registrationId: this.waitToAppointmentPatientInfo.registrationId,
                    registrationCategory: this.waitToAppointmentPatientInfo.registrationCategory,
                    fee: this.waitToAppointmentPatientInfo.fee,
                    hasDepartments: this.hasDepartments,
                    itemRegistrationProductIds: this.waitToAppointmentPatientInfo.itemRegistrationProductIds,
                    date: this.waitToAppointmentPatientInfo.reserveDate || this.wechatAppointmentCard,
                    style: this.appointmentCardStyle,
                    disabledPatient: this.disabledModifyAppointmentTypes.includes(this.sourceSimpleType),
                    handleAppointmentCard: (type) => {
                        this.handleAppointmentCard(type);
                    },
                },extendCardProps)).generateDialog({
                    parent: this,
                });
            },
            // 处理预约小卡片事件
            handleAppointmentCard(type = 'close') {
                if (type === 'close') {
                    this.showAppointmentCard = false;
                    window.removeEventListener('keyup', this._escListener, false);
                } else {
                    this.refreshWaitAppointment();
                }
            },
            // 获取病人详细信息
            async fetchPatientDetail(Id) {
                const patientId = Id || this.appointmentPatientInfo?.patientId;
                if (!this.appointmentPatientInfo) {
                    this.patientDetail = {};
                }
                if (patientId) {
                    const params = {
                        wx: 1,
                        chronicArchives: 1,
                        promotionCardList: 1,
                        showFamilyDoctor: 1,
                    };
                    const { data } = await CrmAPI.fetchPatientOverviewV2(patientId, params);
                    if (data) {
                        this.patientDetail = data;
                    }
                }
            },
            // 获取待预约待阅读信息列表
            getWaitReadMsgList() {
                const { conversationGroups } = this;
                // 获取到列表中有未读消息的数据
                let waitReadList = [];
                const waitReadListObject = document.getElementsByClassName('crm-module__package-communicate__item-patient');
                // 获取到这些列表中的元素到顶部的距离
                waitReadList = conversationGroups?.filter((item) => {return item.unReadCount;})?.map((item,index) => {
                    // 是否滚动到过了此消息
                    const isScrollToTheMsg = false;
                    return {
                        // 获取每个元素到顶部的距离
                        scrollTopHeight: waitReadListObject[index]?.offsetTop,
                        listIndex: index,
                        isScrollToTheMsg,
                        ...item,
                    };
                });
                return waitReadList;
            },
            openRegisterAppointmentSimpleDialog() {
                this.$abcEventBus.$emit('open-register-appointment-simple-dialog');
            },
            // 滚动到最近的等待阅读消息处
            scrollToByNearWaitReadMsg() {
                const nearMsgItem = this.storageWaitReadMsgList.find((item) => {return item?.isScrollToTheMsg === false;});
                // 获取到最近到未被读取的对象
                if (nearMsgItem) {
                    this.scrollToExpectView(this.storageWaitReadMsgList[nearMsgItem.listIndex].scrollTopHeight);
                    this.storageWaitReadMsgList[nearMsgItem.listIndex].isScrollToTheMsg = true;
                } else {
                    this.storageWaitReadMsgList = this.getWaitReadMsgList();
                    this.scrollToByNearWaitReadMsg();
                }
            },
            // 滚动到期望位置
            scrollToExpectView(scrollTopHeight) {
                const crmListDom = document.getElementById('patient-communicate-list');
                if (crmListDom) {
                    crmListDom.scrollTop = scrollTopHeight || 0;
                }
            },
            async fetchPatientOverview(val) {
                try {
                    const params = {
                        wx: 1,
                        childCareRecords: 1,
                        showFamilyDoctor: 1,
                    };
                    const { data } = await CrmAPI.fetchPatientOverviewV2(val, params);
                    this.patientInfo = data;
                } catch (error) {
                    console.log('fetchPatientOverview error', error);
                }
            },
            /**
             * 关键词搜索，拉取患者列表
             * <AUTHOR>
             * @date 2020-05-07
             * @param {any} queryString 搜索关键词
             * @param {any} callback 搜索结果的回调函数
             */
            async querySearchAsync(keyword, callback) {
                this.keyword = keyword.trim();
                let dataList = [];
                if (keyword) {
                    this.loadingSearch = true;
                    try {
                        const { data } = await CrmAPI.fetchChatPatientBasicQuery(keyword);
                        if (data.keyword !== keyword || !data) {
                            dataList = [];
                        } else {
                            dataList = data.rows || [];
                        }
                    } catch (error) {
                        console.log('querySearchAsync error', error);
                    }
                    this.loadingSearch = false;
                }
                return callback(dataList);
            },
            async fetchDepartmentsDoctors(registrationType = 0) {
                if (this.isChainAdmin) {
                    return false;
                }
                const params = {
                    registrationType,
                };
                if (!registrationType) {
                    params.type = 1;
                }
                const { data } = await CrmAPI.fetchDepartmentsDoctors(params);
                this.departments = data.departments;
                const departmentLength = data.departments.filter((d) => {
                    return d.isDefault !== 1;
                }).length;
                this.hasDepartments = !!departmentLength;
            },
            /**
             * 当从搜索结果中选择患者进行聊天
             * <AUTHOR>
             * @date 2020-05-07
             * @param {any} item
             */
            async onClickTargetPatient({
                conversationId,
                patient,
            }) {
                if (!conversationId) {
                    conversationId = await this.createConversationByPatientId(patient.id);
                }
                if (conversationId) {
                    this.keyword = '';
                    this.conversationGroups = [];
                    // 清除上次的聊天对象
                    await this.setLastCommunicatePatient(null);
                    this.initConversationGroup(conversationId);
                }
            },
            /**
             * 创建会话通过患者id
             * <AUTHOR>
             * @date 2020-05-06
             * @param {any} patientId 患者id
             * @returns 创建成功后返回 conversationId
             */
            async createConversationByPatientId(patientId) {
                try {
                    const { data } = await CrmAPI.createConversation(patientId);
                    if (data && data.conversationId) {
                        return data.conversationId;
                    }
                } catch (error) {
                    console.log('createConversation error', error);
                }
            },
            /**
             * 标记全部已读
             * <AUTHOR>
             * @date 2020-05-06
             */
            async onClickReadAll() {
                try {
                    const sceneType = 1; // 患者沟通
                    await CrmAPI.handleReadAllMessage(sceneType);
                    await this._acFetchUnreadMessageCount();
                    if (this.conversationGroups) {
                        this.conversationGroups.forEach((item) => {
                            item.unReadCount = 0;
                        });
                    }
                } catch (error) {
                    console.log('onClickReadAll error', error);
                }
            },
            // 获取待预约卡片信息 默认获取最近的
            async fetchOwnWaitAppointmentInfo(type = 'near') {
                const { currentGroupChat } = this;
                try {
                    const { data } = await CrmAPI.fetchPatientNearestCreatedAppointment(currentGroupChat.patientId);
                    if (data && type === 'near') {
                        this.appointmentPatientInfo = data;
                        this.registrationSheetId = this.appointmentPatientInfo?.registrationSheetId || '';
                    } else {
                        this.appointmentPatientInfo = null;
                        this.registrationSheetId = '';
                    }
                    await this.fetchPatientDetail();
                } catch (e) {
                    console.log(e);
                }
            },
            // 处理会话中预约申请发起的事件
            async handleRegisterAppointment(type, item) {
                const { registrationSheetId } = item.body;
                const { data } = await RegistrationsAPI.fetch(registrationSheetId);
                const ownAppointment = data;
                switch (type) {
                    case 'agree':
                        // eslint-disable-next-line no-case-declarations
                        const { patientId } = ownAppointment;
                        this.registrationSheetIdFromList = registrationSheetId;
                        ownAppointment.registrationSheetId = registrationSheetId;
                        this.appointmentPatientInfoFromList = ownAppointment;
                        await this.fetchPatientDetail(patientId);
                        await this.openAppointmentCard(this.SimpleAppointCardRequestTypeEnum.FROM_LIST);
                        break;
                    case 'refuse':
                        this.refuseAppointmentRequest('now', {
                            registrationSheetId, appointmentObject: ownAppointment,
                        });
                        break;
                    case 'reRefuse':this.refuseAppointmentRequest('now', {
                                        registrationSheetId, appointmentObject: ownAppointment,
                                    });
                                    break;
                    default:
                        break;
                }
            },
            // 设置审核状态
            async setAuditStatus(registrationSheetId, auditStatus) {
                try {
                    const res = await CrmAPI.setAuditStatus(registrationSheetId, auditStatus);
                    if (res) {
                        this.$Toast({
                            type: 'success', message: auditStatus === 3 ? '拒绝成功' : '预约成功',
                        });
                        await this.initWaitAppointmentInfo();
                    }
                } catch (e) {
                    console.log('error', e);
                }
            },
            async initWaitAppointmentInfo() {
                // 继续拉取待预约数据
                await this.fetchOwnWaitAppointmentInfo();
                // 刷新待定预约列表数据
                await this.fetchWaitRegisterAppointmentPatientList();
            },
            refundConfirm(data) {
                this.refundTotalFee = data.refundFee; // 需要退的总费用
                this.refundData = data;
                this.curRefundType = RefundTypeEnum.NORMAL; // 选择项目退费时
                this.showRefundWayList = true;
            },
            // 退钱
            handleRefund() {
                this.showRefund = true;
            },
            // 拒绝预约请求
            refuseAppointmentRequest(type = 'near',item = {}) {
                let appointmentObject = item?.appointmentObject || {};
                if (type === 'near') {
                    appointmentObject = this.appointmentPatientInfo;
                }
                const {
                    registrationFormItem = {}, chargeSheet = {},
                } = appointmentObject;
                const needRePayment = (registrationFormItem?.payStatusV2 === PayStatusV2.PAID ||
                    registrationFormItem?.payStatusV2 === PayStatusV2.PARTED_PAID ||
                    registrationFormItem?.payStatusV2 === PayStatusV2.PARTED_REFUNDED) &&
                    chargeSheet?.chargeSheetSummary?.receivedFee > 0;
                // 如果需要退费就走挂号退费流程
                if (needRePayment) {
                    this.chargeSheet = chargeSheet;
                    this.handleRefund();
                    return;
                }
                const auditStatus = WaitAppointMsgStatus.REFUSE;
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '拒绝后将取消本次预约,是否确定?',
                    confirmText: '确定拒绝',
                    onConfirm: () => {
                        this.$nextTick(() => {
                            // 表示类型是最近的一次预约
                            if (type === 'near') {
                                this.setAuditStatus(this.registrationSheetId, auditStatus);
                                // 不一定是最近的预约 来自列表的数据
                            } else {
                                this.registrationSheetIdFromList = item.registrationSheetId;
                                this.setAuditStatus(this.registrationSheetIdFromList, auditStatus);
                            }
                        });
                    },
                });
            },
            /**
             * 拉取左侧聊天记录，初始化选中
             * <AUTHOR>
             * @date 2020-05-07
             * @param {String} topConversationId 需要置顶会话
             */
            async initConversationGroup(topConversationId = '') {
                this.topConversationId = topConversationId;
                await this.fetchConversationGroups();
                // 有上次聊天的用户,则获取上次聊天的用户
                if (this.lastCommunicatePatient) {
                    let communicatePatient = null;
                    // 表示从待定预约打开 还需要定位面板到指定位置
                    if (this.lastCommunicatePatient?.waitAppointmentFlag) {
                        const patientId = this.lastCommunicatePatient?.wxUserPatientId || this.lastCommunicatePatient.patientId;
                        communicatePatient = this.conversationGroups.find((item) => {return item.patientId === patientId;});
                    } else {
                        communicatePatient = this.lastCommunicatePatient;
                    }
                    this.$nextTick(() => {
                        const conversationGroupsHaveIndex = this.conversationGroups.map((item, index) => {
                            return {
                                index,
                                ...item,
                            };
                        });
                        const lastCommunicatePatientInConversationGroups = conversationGroupsHaveIndex.find((item) => { return item.patientId === this.lastCommunicatePatient.patientId;}) || { index: 0 };
                        const waitReadListObject = document.getElementsByClassName('crm-module__package-communicate__item-patient');
                        const scrollToTop = waitReadListObject[lastCommunicatePatientInConversationGroups.index]?.offsetTop || 0;
                        this.scrollToExpectView(scrollToTop);
                        if (communicatePatient) {
                            this.fetchAboutPatientInfo(communicatePatient);
                        }
                        this.fetchOwnWaitAppointmentInfo();
                    });
                } else {
                    // 没有最近聊天记录获取列表置顶的会话
                    let selectedItem = this.conversationGroups[0];
                    if (this.topConversationId) {
                        selectedItem = this.conversationGroups.find(
                            (item) => item.conversationId === this.topConversationId,
                        );
                    }
                    this.fetchAboutPatientInfo(selectedItem);
                }
            },
            /**
             * 分页拉取更多左侧聊天列表
             * <AUTHOR>
             * @date 2020-05-11
             */
            fetchMorePatientList() {
                this.fetchConversationGroups(false);
            },
            /**
             * 获取左侧聊天列表
             * <AUTHOR>
             * @date 2020-04-28
             * @param {Boolean} isReset 是否重置 默认true
             */
            async fetchConversationGroups(isReset = true) {
                if (isReset) {
                    this.loadingGroup = true;
                    this.conversationGroups = [];
                }
                try {
                    const params = {
                        offset: this.conversationGroups.length,
                        limit: this.converParams.limit,
                        sceneType: 1, // 0-在线咨询，1-患者随访
                        conversationId: this.topConversationId || '',
                    };
                    const { data } = await ApiIM.fetchConversationGroups(params);
                    const list = data.rows || [];
                    this.conversationGroups = [...this.conversationGroups, ...list];
                    this.converParams.isLast = this.conversationGroups.length >= data.total;
                } catch (error) {
                    console.log('fetchConversationGroups error', error);
                }
                this.loadingGroup = false;
            },
            /**
             * 置顶目标会话通过会话id，首先在左侧聊天列表里面找到对应的会话，将改会话提升到第一位
             * <AUTHOR>
             * @date 2020-05-07
             * @param {Object} lastImMessage 最新消息
             * @param {Boolean} isTop 是否置顶 默认false
             */
            editText(text) {
                this.$refs['edit-html'].appendText(text);
            },
            // 患者发送消息
            async stickTargetConversation(lastImMessage) {
                const index = this.conversationGroups.findIndex(
                    (item) => item.conversationId === lastImMessage.conversationId,
                );

                if (index !== -1) {
                    // 存在左侧列表
                    const target = this.conversationGroups[index];
                    const isPatientSend = !isMessageIgnored(lastImMessage);
                    if (isPatientSend) {
                        target.lastImMessage = lastImMessage;
                        if (target.unReadCount !== 0) {
                            // 只提醒患者发的消息
                            target.unReadCount++;
                        } else {
                            if (target.conversationId === this.conversationId) {
                                // 是当前选中的会话，直接放在最上面
                                this.conversationGroups.splice(index, 1);
                                this.conversationGroups.unshift(target);
                            } else {
                                // 只提醒患者发的消息
                                target.unReadCount = 1;
                                this.conversationGroups.splice(index, 1);
                                const lastIndex = this.conversationGroups.findIndex((item) => item.unReadCount === 0);
                                this.conversationGroups.splice(lastIndex, 0, target);
                            }
                        }
                    }
                } else {
                    // 不存在左侧列表-带着conversationId置顶拉取一条会话，然后得到目标会话插入到左侧聊天列表
                    while (this.loadingGroup) {
                        // 当正在拉取会话列表时，等待拉取完成
                        await new Promise((resolve) => setTimeout(() => resolve(), 200));
                    }
                    const target = await this.fetchTargetConversationItem(lastImMessage.conversationId);
                    const exit = this.conversationGroups.find(
                        (item) => item.conversationId === lastImMessage.conversationId,
                    );
                    if (!exit && target) {
                        const lastIndex = this.conversationGroups.findIndex((item) => item.unReadCount === 0);
                        this.conversationGroups.splice(lastIndex, 0, target);
                    }
                }
            },
            /**
             * 拉取指定会话
             * <AUTHOR>
             * @date 2020-05-08
             * @param {String} conversationId 会话id
             * @returns {any}
             */
            async fetchTargetConversationItem(conversationId) {
                try {
                    const params = {
                        offset: 0,
                        limit: 5,
                        sceneType: 1, // 0-在线咨询，1-患者随访
                        conversationId,
                    };
                    const { data } = await ApiIM.fetchConversationGroups(params);
                    const list = data.rows || [];
                    return list.find((item) => item.conversationId === conversationId);
                } catch (error) {
                    console.log('fetchTargetConversationItem error', error);
                }
            },
            /**
             * 选择会话，拉取患者相关信息，包括：患者基本信息、近三个月未执行随访、患者轨迹
             * <AUTHOR>
             * @date 2020-04-29
             * @param {Object} selectedItem 患者标识
             */
            async fetchAboutPatientInfo(selectedItem) {
                this.showPatientCard = false;
                if (selectedItem) {
                    if (selectedItem.conversationId !== this.conversationId) {
                        this.currentGroupChat = selectedItem;
                        // 将消息标记已读
                        this.readAllMessage();
                        this._acFetchUnreadMessageCount();
                        this.currentGroupChat.unReadCount = 0;
                        this.conversationGroups = this.conversationGroups.map((item) => {
                            let unReadCount = item?.unReadCount || 0;
                            if (selectedItem.conversationId === item.conversationId) {
                                unReadCount = 0;
                            }
                            return {
                                ...item,
                                unReadCount,
                            };
                        });
                        this.conversationInfo = null;
                        this.fetchConversationInfo(this.conversationId);
                        if (this.patientId) {
                            this.loadingInfo = true;
                            await Promise.all([
                                this.fetchRevisitFutureThreeMonths(),
                                this.selectPatientTraceList(true),
                            ]);
                            this.loadingInfo = false;
                        }
                        this.handleEditHtml('clearEditContent');
                    } else {
                        this.handleEditHtml('focusTextareaEnd');
                    }
                    // 存储上次会话的人
                    this.setLastCommunicatePatient(selectedItem);
                    await this.fetchOwnWaitAppointmentInfo();
                } else {
                    this.currentGroupChat = null;
                    this.conversationInfo = null;
                }
            },
            async fetchPatientBaseInfo(patientId) {
                try {
                    if (patientId) {
                        const { data: res } = await CrmAPI.fetchPatientBaseInfo(patientId);
                        const {
                            outpatientCount,
                            cumulativeAmount,
                            payCount,
                        } = res && res.rows[0] || {};
                        this.patientBaseInfo.outpatientCount = outpatientCount;
                        this.patientBaseInfo.payCount = payCount;
                        this.patientBaseInfo.cumulativeAmount = cumulativeAmount;
                    }
                } catch (e) {
                    console.log(e);
                }
            },
            /**
             * 拉取会话详情
             * <AUTHOR>
             * @date 2020-05-06
             * @param {any} conversationId
             */
            async fetchConversationInfo(conversationId) {
                try {
                    const { data } = await ApiIM.fetchConversationInfo(conversationId);
                    this.conversationInfo = data;
                } catch (error) {
                    console.log('fetchConversationInfo error', error);
                }
            },
            /**
             * 更新患者信息，同时更新会话列表的数据
             * <AUTHOR>
             * @date 2020-04-27
             * @param {Object} patientInfo 患者信息对象
             */
            updatePatientInfo(patientInfo) {
                this.patientInfo = patientInfo;
                const target = this.conversationGroups.find((item) => item.patientId === patientInfo.id);
                if (target) {
                    target.patientName = patientInfo.name;
                    if (target.conversationId === this.conversationId) {
                        // 当前会话被选中了，更新聊天记录名字
                        this.handleMessageFunc('updateCurPatientInfo', patientInfo);
                    }
                }
            },
            handleShowPatientCard() {
                if (this.patientInfo?.id) {
                    this.showPatientCard = true;
                }
            },
            /**
             * 拉取未来三个月随访任务
             * <AUTHOR>
             * @date 2020-04-28
             */
            async fetchRevisitFutureThreeMonths() {
                try {
                    const params = {
                        offset: 0,
                        limit: 100,
                        beginDate: fecha.format(Date.now() - DAY_TIMES * 60, DATE_FORMATE),
                        endDate: fecha.format(Date.now() + DAY_TIMES * 30, DATE_FORMATE),
                    };
                    const { data } = await CrmAPI.selectRevisitFutureThreeMonths(this.patientId, params);
                    if (data.result) {
                        this.futureList = data.result.filter((item) => item.id !== this.id);
                    }
                } catch (error) {
                    console.log('fetchRevisitFutureThreeMonths error', error);
                }
            },
            /**
             * 拉取更多患者轨迹数据
             * <AUTHOR>
             * @date 2020-05-11
             */
            fetchMoreTrackList() {
                this.selectPatientTraceList();
            },
            /**
             * 分页查看患者轨迹数据
             * <AUTHOR>
             * @date 2020-05-11
             * @param {Boolean} isReset 是否重置 默认false
             */
            async selectPatientTraceList(isReset = false) {
                if (isReset) {
                    this.trackDataList = [];
                    this.loading = true;
                }
                try {
                    const { data } = await CrmAPI.fetchTrace(this.patientId, {
                        offset: this.trackDataList.length,
                        limit: this.pageParams.limit,
                    });
                    this.trackDataList = [...this.trackDataList, ...data.result];
                    this.pageParams.isLast = this.trackDataList.length >= data.totalCount;
                } catch (error) {
                    console.log('selectPatientTraceList error', error);
                }
                this.loading = false;
            },
            /**
             * 打开详情弹窗
             * <AUTHOR>
             * @date 2020-04-27
             * @param {String} handleFunc 调用详情的函数名称
             * @param {String} orderId 订单id
             */
            handleOpenDetail(handleFunc, orderId) {
                const orderDetailNode = this.$refs['order-detail'];
                orderDetailNode && orderDetailNode[handleFunc] && orderDetailNode[handleFunc](orderId);
            },
            /**
             * 操作调用编辑输入框
             * <AUTHOR>
             * @date 2020-04-28
             * @param {any} emojiName 表情名称
             */
            handleEditHtml(handleFunc, ...args) {
                const editHtmlNode = this.$refs['edit-html'];
                editHtmlNode && editHtmlNode[handleFunc] && editHtmlNode[handleFunc](...args);
            },
            /**
             * 调用消息列表内的方法
             * <AUTHOR>
             * @date 2020-04-27
             * @param {String} funcName 方法名称
             * @param {*} args 参数
             */
            handleMessageFunc(funcName, ...args) {
                const viewMessageRef = this.$refs['view-message'];
                viewMessageRef && viewMessageRef[funcName] && viewMessageRef[funcName](...args);
            },
            /**
             * 当发送文字消息时
             * <AUTHOR>
             * @date 2020-04-26
             * @param {Boolean} isClear 是否清除输入框内容 默认false
             */
            revokeMessage(messageId) {
                this.updateTask(messageId);
            },
            revokeUnReadCount(item) {
                const conversationItem = this.conversationGroups.find((it) => {
                    return it.conversationId === item.conversationId;
                });
                if (!conversationItem) {
                    return;
                }
                if (conversationItem.unReadCount !== 0 && item.data?.msgIsRead === 0) {
                    conversationItem.unReadCount -= 1;
                    this._acFetchUnreadMessageCount();
                }
            },
            changeLastMessage(item) {
                const conversationItem = this.conversationGroups.find((it) => {
                    return it.conversationId === item.conversationId;
                });
                if (conversationItem?.lastImMessage?.id === item?.msgId) {
                    conversationItem.lastImMessage.status = 3;
                }

            },
            onSendText(body, isClear = true) {
                this.closePopover();
                if (body && body.text) {
                    if (body.text.length > 500) {
                        // 发送文字长度限制500
                        return this.$Toast({
                            type: 'error',
                            message: '发送消息内容超出，请分条发送',
                        });
                    }
                    const messageBody = this.getTextMessage(body);
                    if (messageBody) {
                        const {
                            sendItem,
                            saveItem,
                        } = messageBody;
                        const res = this.createSendTask(sendItem);
                        res && this.handleMessageFunc('insertMessageItem', saveItem);
                        isClear && this.handleEditHtml('clearEditContent');
                    }
                }
            },
            /**
             * 当发送图片消息时
             * <AUTHOR>
             * @date 2020-04-26
             * @param {Object} body
             */
            async onSendImage(body) {
                this.closePopover();
                const messageBody = this.getImageMessage(body);
                if (messageBody) {
                    const {
                        sendItem,
                        saveItem,
                    } = messageBody;
                    this.handleMessageFunc('insertMessageItem', saveItem);
                    // 这里图片路径用chainId, 晓东负责
                    const { clinicId } = this.currentClinic;
                    const [err, url] = await this.handleImImageUpload(body.file, clinicId);
                    if (!err) {
                        sendItem.body = {
                            imageUrl: url,
                            width: body.width,
                            height: body.height,
                        };
                        saveItem.body.url = url;
                        const res = this.createSendTask(sendItem);
                        if (res) return;
                    } else {
                        this.$Toast({
                            type: 'error',
                            message: err,
                        });
                    }
                    this.handleMessageFunc('handleSendTimeout', saveItem);
                }
            },
            /**
             * 关闭弹出层
             * <AUTHOR>
             * @date 2020-04-28
             */
            closePopover() {
                this.visibleEmojiList = false;
                this.visibleCommonText = false;
            },
            /**
             * 打开随访任务详情
             * <AUTHOR>
             * @date 2020-04-28
             * @param {any} item
             * @returns {any}
             */
            onClickOpenDetail(item) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '是否需要离开当前页面，跳转到随访详情页面 ？',
                    onConfirm: () => {
                        this.muClosePatientCommunicate();
                        this.$router.push({
                            name: '患者随访',
                            params: {
                                id: item.id,
                            },
                        });
                    },
                });
            },
            /**
             * 刷新图片上传input的dom
             * <AUTHOR>
             * @date 2020-04-29
             */
            refreshInputFile() {
                this.showInputFile = false;
                this.$nextTick(() => {
                    this.showInputFile = true;
                });
            },
            /**
             * 当有图片选入上传时
             * <AUTHOR>
             * @date 2020-04-29
             * @param {Event} event 事件
             */
            async onChangeFile(event) {
                const file = event.target.files[0];
                if (!file) {
                    // 没有选择图片时，直接返回
                    return;
                }
                this.refreshInputFile();

                const fileName = file.name;
                const ext = this.getExtByFileName(fileName).toLowerCase();
                if (!UPLOAD_IMAGE_TYPES.includes(ext)) {
                    return this.$Toast({
                        type: 'error',
                        message: '不支持该图片类型',
                    });
                }

                const [err, ff] = await this.compressorImage(file);
                console.log('err', err);
                const f = ff || file;
                // 图片规格超出限制
                if (f.size > UPLOAD_MAX_SIZE) {
                    return this.$Toast({
                        type: 'error',
                        message: '发送图片不能超过20M',
                    });
                }

                const info = await this.handleImageInfo(f);
                this.onSendImage(info);
            },
            getEventPath(evt) {
                if (!evt) return '';
                return evt.path || (evt.composedPath && evt.composedPath()) || '';
            },

            onClickOutside(mousedown, mouseup) {
                const baseCardVM = this.$refs['patient-base-card'] || {};
                const {
                    showCardCover,selectOption,notCloseCard,
                } = baseCardVM;
                if (mouseup.target.classList.contains('abc-dialog-cover') && showCardCover) {
                    baseCardVM?.handleClickCover();
                    return;
                }

                if (!this.showPatientCard || notCloseCard) {
                    return;
                }

                // 当标签弹窗显示，点击卡片外部，应当先关闭标签弹窗
                if (selectOption) {
                    baseCardVM.showTagPopover = false;
                    return;
                }

                const classNames = [
                    'abc-tooltip__popper',
                    'card-btn',
                    'thr',
                    'view-labels',
                    'sex-select',
                    'unit-select',
                    'abc-date-picker-popper',
                    'panel-popper',
                    'date-navigator-dropdown-wrapper',
                    'source-select-ul',
                    'child-box',
                    'profession-options',
                    'address-selector_popover-wrapper',
                    'cascader-options-wrapper',
                    'crm-module__package-social__card-box',
                ];
                const cardBtnDom = this.$refs['card-btn-ref'];
                let exit = false;
                let isTarget = true;
                const mouseupPath = this.getEventPath(mouseup);
                if (mouseupPath) {
                    mouseupPath.forEach((item) => {
                        if (item.classList) {
                            const classList = Array.from(item.classList);
                            classNames.forEach((one) => {
                                if (Array.from(classList).includes(one)) {
                                    exit = true;
                                }
                            });
                            if (Array.from(classList).includes('package-card__card-btn') && item !== cardBtnDom) {
                                isTarget = false;
                            }
                        }
                    });
                }
                if (!exit || !isTarget) {
                    this.showPatientCard = false;
                }
            },
            handleOutside () {
                const AppointmentBaseCardVm = this.$refs['appointment-card']?.$refs['appointment-base-card'];
                if (AppointmentBaseCardVm?.isEditor || AppointmentBaseCardVm?.isAddPatient) return;
                this.showAppointmentCard = false;
                window.removeEventListener('keyup', this._escListener, false);
            },

            toScrmDetail() {
                NavigateHelper.navigateToScrmDesc(this.currentClinic);
            },
            onClickConfigDialog() {
                this.showConfigDialog = true;
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

.crm-module__dialog-wechat-communicate {
    line-height: normal;

    .abc-dialog-body {
        padding: 0 0 !important;
    }

    .crm-module__dialog-wechat-communicate--title {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
        font-size: 16px;
        font-weight: bold;
        line-height: 16px;
        color: $S1;

        .crm-module__dialog-wechat-communicate--notice {
            position: absolute;
            top: 0;
            left: 64px;
            display: flex;
            align-items: center;
            height: 16px;
            padding: 0 4px;
            font-size: 12px;
            font-weight: normal;
            line-height: 16px;
            color: $S2;
            text-align: center;
            background: #ff3333;
            border-radius: 8px;
        }

        .crm-module__dialog--scrm {
            position: relative;
            display: flex;
            align-items: center;
            width: 364px;
            height: 28px;
            padding: 8px 16px;
            margin-left: 26px;
            font-size: 12px;
            font-weight: normal;
            background: rgba(0, 122, 255, 0.15);
            border-radius: 14px;

            > span {
                margin-left: auto;
                font-size: 13px;
                line-height: 18px;
                color: #007aff;
                cursor: pointer;
            }

            .new-img {
                position: absolute;
                top: -6px;
                right: -18px;
            }
        }
    }

    .abc-dialog .abc-dialog-body {
        position: relative;
        overflow: unset;

        @include flex(row, flex-start, stretch);

        .left-box {
            flex-shrink: 0;
            width: 248px;
            border-right: 1px solid $P6;

            @include flex(column, flex-start, stretch);

            .header {
                z-index: 1;
                flex-shrink: 0;
                border-bottom: 1px solid $P4;

                .search-box {
                    height: 56px;
                    padding: 0 10px;

                    @include flex(row, space-between, center);

                    .empty-alert {
                        position: absolute;
                        top: 36px;
                        width: 291px;
                        height: 120px;
                        background-color: $P5;
                        border: 1px solid var(--abc-color-P7);
                        border-radius: var(--abc-border-radius-small);
                        box-shadow: var(--abc-shadow-1);

                        @include flex(column, center, center);

                        span {
                            font-size: 14px;
                            color: $T3;
                        }
                    }

                    button {
                        padding: 0;
                    }
                }

                .search-result {
                    height: 36px;
                    font-size: 12px;
                    line-height: 36px;
                    color: $T2;
                    text-align: center;
                    background-color: $P5;
                    border-top: 1px solid $P1;

                    .num {
                        margin: 0 4px;
                        color: $T1;
                    }
                }
            }

            .list-wrapper {
                position: relative;
                flex: 1;
                padding: 4px 10px 0 10px;
                overflow-x: hidden;
                overflow-y: scroll;
                border-radius: var(--abc-border-radius-small);

                .list {
                    width: 100%;

                    .no-data {
                        height: 80px;

                        @include flex(row, center, center);

                        > span {
                            font-size: 12px;
                            color: $T2;
                        }
                    }

                    .group-list-move {
                        transition: transform 0.35s;
                    }
                }

                &::-webkit-scrollbar {
                    width: 0;
                }

                &::-webkit-scrollbar-thumb {
                    background: rgba(206, 208, 218, 0);

                    /* 滚动条里面小方块 */
                    border-radius: var(--abc-border-radius-small);
                    transform: all 0.15s ease-out;
                }

                &::-webkit-scrollbar-track {
                    background: rgba(240, 242, 245, 0);

                    /* 滚动条里面轨道 */
                    transform: all 0.15s ease-out;
                }
            }

            .list-wrapper:hover {
                padding: 4px 0 0 10px;

                &::-webkit-scrollbar {
                    width: 10px;
                }

                &::-webkit-scrollbar-thumb {
                    background: rgba(206, 208, 218, 1);

                    /* 滚动条里面小方块 */
                    border-radius: var(--abc-border-radius-small);
                }

                &::-webkit-scrollbar-track {
                    /* 滚动条里面轨道 */
                    background: rgba(255, 255, 255, 1);
                }
            }
        }

        .cont-box {
            flex: 1;

            .message-list {
                display: flex;
                flex-direction: column;
                height: 450px;
                overflow: hidden;
                background-color: #f7f8fa;

                .register-appointment_communicate--tips-top-box {
                    width: 100%;
                    height: 32px;
                    padding: 8px 12px;
                    font-size: 12px;
                    line-height: 16px;
                    color: #ff9933;
                    letter-spacing: 0;
                    background: #fff4ea;
                    box-shadow: inset 0 -1px 0 0 #ffead6;

                    &-doctor-name {
                        margin-left: 4px;
                    }

                    &-project {
                        margin-left: 4px;
                    }

                    &-register {
                        margin-left: 4px;
                        color: #005ed9;
                        cursor: pointer;
                    }

                    &-reject {
                        margin-left: 4px;
                        color: #ff3333;
                        cursor: pointer;
                    }
                }

                &.height-580 {
                    height: 580px;
                }
            }

            .control-box {
                position: relative;
                width: 100%;
                height: 140px;
                background-color: $P5;
                border-top: 1px solid $P6;

                .hand-box {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .left {
                        height: 36px;

                        @include flex(row, flex-start, center);

                        span.iconfont {
                            margin-left: 12px;
                            font-size: 14px;
                            color: $T1;
                            cursor: pointer;

                            &:hover {
                                color: $theme1;
                            }
                        }

                        .upload-image {
                            position: relative;
                            display: inline-flex;
                            width: 16px;
                            height: 16px;
                            margin-left: 10px;
                            overflow: hidden;
                            cursor: pointer;

                            @include flex(row, center, center);

                            &:hover {
                                > span {
                                    color: $theme1;
                                }
                            }

                            > span {
                                margin-left: 0;
                            }

                            > input {
                                position: absolute;
                                z-index: 1;
                                width: 100%;
                                min-width: 0;
                                height: 100%;
                                min-height: 0;
                                cursor: pointer;
                                opacity: 0;
                            }
                        }
                    }

                    .right {
                        display: inline-flex;
                        align-items: center;

                        .quick-appointment {
                            position: relative;
                            height: 20px;
                            margin: 0 12px 0 16px;
                            font-size: 14px;
                            font-weight: 400;
                            line-height: 20px;
                            color: $S1;
                            cursor: pointer;

                            .quick-appointment-card-box {
                                position: absolute;
                                bottom: 30px;
                                left: 0;
                                z-index: 2000;
                                display: flex;
                                width: 340px;
                                height: auto;
                                border-radius: 5px;

                                .appoint-base-card-wrapper {
                                    z-index: 2000;
                                }
                            }
                        }
                    }
                }

                .edit-box {
                    height: 105px;
                    padding: 0 10px 8px;
                }

                .disabled-status {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                    z-index: 1;
                    display: none;
                    cursor: not-allowed;
                }

                &.disabled {
                    .hand-box span {
                        color: $P1;
                    }

                    .disabled-status {
                        display: block;
                    }
                }
            }
        }

        .patient-base-card {
            &.crm-module__package-card__base-card-dentistry {
                position: absolute;
                top: 64px;
                right: 11px;
                z-index: 10;
                width: 447px;
                height: auto;
                max-height: 637px;
                background-color: $S2;
                border-radius: var(--abc-border-radius-small);
                box-shadow: var(--abc-shadow-1);

                .patient-card-cover {
                    top: 0;
                }
            }
        }

        .right-box {
            position: relative;
            flex-shrink: 0;
            width: 288px;
            height: 100%;
            overflow: hidden;
            border-left: 1px solid $P6;

            .right-box-scroll-box {
                width: 100%;
                height: 100%;
                padding: 12px 12px 12px 12px;
                overflow-x: hidden;
                overflow-y: scroll;

                .right-box-scroll {
                    width: 264px;
                    height: auto;
                }

                &::-webkit-scrollbar {
                    width: 0;
                }

                &::-webkit-scrollbar-thumb {
                    background: rgba(206, 208, 218, 0);

                    /* 滚动条里面小方块 */
                    border-radius: var(--abc-border-radius-small);
                    transform: all 0.15s ease-out;
                }

                &::-webkit-scrollbar-track {
                    background: rgba(240, 242, 245, 0);

                    /* 滚动条里面轨道 */
                    transform: all 0.15s ease-out;
                }
            }

            .top {
                margin-bottom: 4px;
                font-size: 12px;
                font-weight: bold;
                line-height: 16px;
                color: $S1;
            }

            .patient-box {
                .patient-info {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 8px 4px 8px 8px;
                    cursor: pointer;
                    border: 1px solid $P6;
                    border-radius: var(--abc-border-radius-small);

                    .left {
                        .base-info {
                            max-width: 236px;
                            overflow: hidden;
                            font-size: 14px;
                            font-weight: 400;
                            line-height: 20px;
                            text-overflow: ellipsis;
                            white-space: nowrap;

                            .name {
                                font-weight: bold;
                                color: $S1;

                                img {
                                    width: 12px;
                                    height: 12px;
                                }
                            }

                            .sex {
                                margin-left: 12px;
                            }

                            .age {
                                margin: 0 8px;
                            }
                        }

                        .record-total {
                            padding-left: 10px;
                            margin: 8px 0;
                            font-size: 13px;
                            font-weight: 400;
                            line-height: 1;
                            color: $S1;
                        }

                        .tags-box {
                            .crm-module__package-label__abc-label {
                                height: 16px;
                                padding: 0;
                                margin: 4px 12px 0 0;
                                font-size: 13px;
                                font-weight: bold;
                                color: $G1;
                                background-color: rgba(255, 255, 255, 0);
                                border: none;
                            }
                        }
                    }

                    .right {
                        .cis-icon-Arrow_Rgiht {
                            font-size: 16px;
                            color: $P1;
                        }
                    }

                    &_tags {
                        display: flex;
                        flex-wrap: wrap;
                        align-items: flex-start;
                        justify-content: flex-start;
                        width: 100%;
                        min-height: 18px;
                        margin-top: 4px;
                        margin-bottom: -8px;
                        font-size: 13px;
                        font-weight: 500;
                        color: #08a446;

                        &--model {
                            height: 18px;
                            margin-right: 12px;
                            margin-bottom: 8px;
                            line-height: 18px;
                        }
                    }
                }
            }

            .visit-box {
                padding: 12px 0;

                .top {
                    @include flex(row, space-between, center);
                }

                .list {
                    border: 1px solid $P6;
                    border-radius: var(--abc-border-radius-small);

                    .crm-module__package-communicate__item-visit-card {
                        &:not(:last-child) {
                            border-bottom: 1px solid $P6;
                        }
                    }

                    .more {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        line-height: 24px;
                        color: $theme1;
                        cursor: pointer;
                    }
                }
            }

            .track-box {
                margin-top: 24px;

                > .head {
                    margin-bottom: 8px;
                    color: $T2;
                }

                .track-info {
                    border: 1px solid $P6;
                    border-radius: var(--abc-border-radius-small);

                    .crm-module__package-track__item-outpatient,
                    .crm-module__package-track__item-retail,
                    .crm-module__package-track__item-visit {
                        &:not(:last-child) {
                            border-bottom: 1px solid $P6;
                        }
                    }
                }
            }

            .no-data {
                height: 80px;
                font-size: 12px;
                color: $T2;

                @include flex(row, center, center);
            }
        }

        .right-box:hover {
            .right-box-scroll-box {
                padding: 12px 2px 12px 12px;

                &::-webkit-scrollbar {
                    width: 10px;
                }

                &::-webkit-scrollbar-thumb {
                    background: rgba(206, 208, 218, 1);

                    /* 滚动条里面小方块 */
                    border-radius: var(--abc-border-radius-small);
                }

                &::-webkit-scrollbar-track {
                    /* 滚动条里面轨道 */
                    background: rgba(255, 255, 255, 1);
                }
            }
        }
    }
}

.crm-module__dialog-wechat-communicate__search-follow-wechat {
    .patient-suggestions.suggestions-item {
        padding: 0 !important;
        border-bottom: 0;
    }
}

.quick-appointment-cover {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1999;
    width: 100%;
    height: 100%;
    background-color: #333333;
    opacity: 0.4;
}

.appointment-board_box--handle {
    cursor: pointer;

    &:hover {
        color: #005ed9;
    }
}
</style>
