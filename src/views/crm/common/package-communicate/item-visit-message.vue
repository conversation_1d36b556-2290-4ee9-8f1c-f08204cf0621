<template>
    <message-card class="content-visit_message" :is-mine="isMine">
        <div style="margin-bottom: 6px; line-height: 20px;">
            <abc-text :bold="true">
                随访消息已发送给患者
            </abc-text>
        </div>
        <div>
            <abc-flex style="line-height: 20px;" gap="4">
                <abc-text size="mini" style="display: inline-block; width: 28px;" theme="gray">
                    时间:
                </abc-text>
                <abc-text size="mini" style="display: inline-block; flex: 1;" theme="gray">
                    {{ createdTime }}
                </abc-text>
            </abc-flex>
            <abc-flex style="line-height: 20px;" gap="4">
                <abc-text size="mini" style="display: inline-block; width: 28px;" theme="gray">
                    内容:
                </abc-text>
                <abc-text
                    size="mini"
                    style="display: inline-block; flex: 1;"
                    theme="gray"
                >
                    <span v-html="parseTextToHtml(item.body.text)"></span>
                </abc-text>
            </abc-flex>
        </div>
    </message-card>
</template>

<script>
    import MessageCard from './components/message-card.vue';
    import { parseTextToHtml } from 'assets/configure/emoji-link';
    import {
        MsgTypeStatus, WaitAppointMsgStatus,
    } from './constants';
    import fecha from 'utils/fecha';
    import { DATE_TIME_MM_FORMATE } from '@/assets/configure/constants';
    export default {
        name: 'ItemRegisterAppointment',
        components: {
            MessageCard,
        },
        props: {
            item: {
                type: Object,
                default: () => {
                    return {
                        msgType: 0,
                    };
                },
            },
            isMine: {
                type: Boolean,
                default: true,
            },
        },
        data() {
            return {
                MsgTypeStatus,
                WaitAppointMsgStatus,
            };
        },
        computed: {
            createdTime() {
                if (!this.item?.created) {
                    return '';
                }
                return fecha.format(this.item.created, DATE_TIME_MM_FORMATE);
            },
        },
        methods: {
            parseTextToHtml,
        },
    };
</script>

<style lang="scss">
@import "src/styles/abc-common.scss";

.content-visit_message {
    position: relative;
    width: 244px;
    height: auto;
    padding: 12px 10px;
    background-color: var(--abc-color-S2);
    border-radius: 2px;
}
</style>

