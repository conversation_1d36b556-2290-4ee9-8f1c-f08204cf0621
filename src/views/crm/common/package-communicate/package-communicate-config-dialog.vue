<template>
    <abc-dialog
        v-model="visible"
        title="微信沟通设置"
        size="large"
        :show-header-border-bottom="true"
        :responsive="false"
        append-to-body
    >
        <abc-flex vertical gap="middle">
            <abc-form
                item-no-margin
                class="package-communicate-config__item"
            >
                <biz-setting-form :label-width="112" no-limit-width>
                    <biz-setting-form-group>
                        <biz-setting-form-item label="患者主动发起沟通">
                            <biz-setting-form-item-tip tip="开启后，患者在公众号内发送信息将自动发起微信沟通">
                                <abc-flex align="center">
                                    <abc-tooltip
                                        placement="top-start"
                                        content="请前往总部修改设置"
                                        :disabled="!isChainSubStore"
                                    >
                                        <div>
                                            <abc-checkbox v-model="initiativeEnable" :disabled="isChainSubStore" shape="square">
                                                开启
                                            </abc-checkbox>
                                        </div>
                                    </abc-tooltip>
                                </abc-flex>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="消息接收提示音">
                            <biz-setting-form-item-tip tip="开启后，接收消息时自动响起提示音">
                                <abc-space>
                                    <abc-checkbox
                                        v-model="curWxReplySound"
                                        shape="square"
                                        type="number"
                                    >
                                        开启
                                    </abc-checkbox>

                                    <abc-audio
                                        ref="abc-audio"
                                        :url="weChatMessageVoiceUrl"
                                    ></abc-audio>
                                </abc-space>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="随访任务静默提示">
                            <biz-setting-form-item-tip tip="开启后，系统自动创建随访沟通会以静默数据提醒形式提醒，不弹更新已有消息对话">
                                <abc-checkbox
                                    v-model="curFollowUpTaskSilentTip"
                                    shape="square"
                                    type="number"
                                >
                                    开启
                                </abc-checkbox>
                            </biz-setting-form-item-tip>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>
        </abc-flex>
        <template #footer>
            <abc-flex justify="flex-end" align="center">
                <abc-space>
                    <abc-button
                        shape="square"
                        variant="fill"
                        theme="primary"
                        size="normal"
                        :disabled="disabled"
                        :loading="loading"
                        @click="handleConfirm"
                    >
                        确定
                    </abc-button>
                    <abc-button
                        shape="square"
                        variant="ghost"
                        theme="primary"
                        size="normal"
                        @click="handleCancel"
                    >
                        取消
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
        BizSettingFormItemTip,
    } from '@/components-composite/setting-form/index.js';
    import { WeChatMessageVoiceUrl } from 'views/crm/common/package-communicate/wechat-message-audio-manager';

    export default {
        name: 'PackageCommunicateConfigDialog',

        components: {
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingFormItemTip,
        },

        props: {
            value: {
                type: Boolean,
                default: false,
            },
            config: {
                type: Object,
                default: () => ({}),
            },
        },

        data() {
            return {
                initiativeEnable: false,
                initiativeEnableCache: false,
                curWxReplySound: 0,
                curWxReplySoundCache: 0,
                curFollowUpTaskSilentTip: 0,
                curFollowUpTaskSilentTipCache: 0,
                loading: false,
            };
        },
        computed: {
            ...mapGetters([
                'isDisableWxAutoReply',
                'isChainSubStore',
                'wxReplySound',
                'followUpTaskSilentTip',
            ]),
            visible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            disabled() {
                return this.initiativeEnableCache === this.initiativeEnable &&
                    this.curWxReplySound === this.curWxReplySoundCache &&
                    this.curFollowUpTaskSilentTip === this.curFollowUpTaskSilentTipCache;
            },
            weChatMessageVoiceUrl() {
                return WeChatMessageVoiceUrl;
            },
        },
        watch: {
            isDisableWxAutoReply(val) {
                this.initiativeEnable = !val;
                this.initiativeEnableCache = !val;
            },
        },
        created() {
            this.initiativeEnable = !this.isDisableWxAutoReply;
            this.initiativeEnableCache = !this.isDisableWxAutoReply;
            this.curWxReplySound = this.wxReplySound;
            this.curWxReplySoundCache = this.wxReplySound;
            this.curFollowUpTaskSilentTip = this.followUpTaskSilentTip;
            this.curFollowUpTaskSilentTipCache = this.followUpTaskSilentTip;
        },
        methods: {
            ...mapActions(['updateDisableWxAutoReply','updateWxReplySound','updateFollowUpTaskSilentTip']),
            async handleConfirm() {
                this.loading = true;
                if (this.initiativeEnableCache !== this.initiativeEnable) {
                    await this.updateDisableWxAutoReply(this.initiativeEnable ? 0 : 1);
                }
                if (this.curWxReplySoundCache !== this.curWxReplySound) {
                    await this.updateWxReplySound(this.curWxReplySound);
                }
                if (this.curFollowUpTaskSilentTipCache !== this.curFollowUpTaskSilentTip) {
                    await this.updateFollowUpTaskSilentTip(this.curFollowUpTaskSilentTip);
                }
                this.loading = false;
                this.visible = false;
            },

            handleCancel() {
                this.visible = false;
            },
        },
    };
</script>

<style lang="scss">
.package-communicate-config__item {
    .trumpet-img {
        width: 18px;
        height: 16px;
        cursor: pointer;
        background-image: url("~assets/images/trumpet-grey.png");
        background-repeat: no-repeat;
        background-position: center;
        background-size: cover;

        &:hover {
            background-image: url("~assets/images/trumpet-blue-0.png");
        }

        &.playing-trumpet {
            animation: auto-play 0.8s infinite;
        }
    }
}
</style>
