<template>
    <div ref="message-box" class="crm-module__package-communicate__view-message" @scroll="listenerScrollPage($event)">
        <div
            :class="{
                scroll: true,
                'up-no-data': upPageParams.isLast,
                'down-no-data': downPageParams.isLast,
            }"
        >
            <div
                v-if="
                    upPageParams.isLast &&
                        downPageParams.isLast &&
                        !upPageParams.loading &&
                        !downPageParams.loading &&
                        showMessageList.length === 0
                "
                class="no-data"
            >
                <span>暂无数据</span>
            </div>
            <div v-if="!upPageParams.isLast" class="up-loading-more">
                <span v-show="upPageParams.loading" class="icon-loading"></span>
            </div>
            <div v-if="!downPageParams.isLast" class="down-loading-more">
                <span v-show="downPageParams.loading" class="icon-loading"></span>
            </div>
            <div v-for="item in showMessageList" :key="item.id" class="item">
                <!-- 时间 -->
                <template v-if="item.msgType === MsgTypeStatus.MSG_TIME">
                    <item-time :time="item.time"></item-time>
                </template>
                <template v-else-if="item.status === MessageStatusEnum.REVOKE">
                    <item-remove-msg
                        :item="item"
                        :other-nick-name="userName"
                        :need-edit="item.msgType === MsgTypeStatus.MSG_TEXT"
                        @editText="editText"
                    ></item-remove-msg>
                </template>
                <div
                    v-else
                >
                    <div class="sender-name" :style="{ 'text-align': item.isMine ? 'right' : 'left' }">
                        <span>{{ item.participanInfo.name }}</span>
                    </div>
                    <div :class="['message', item.isMine ? 'mine' : 'other']">
                        <div v-if="!item.isMine" class="head-img left">
                            <abc-avatar :url="item.participanInfo.headImgUrl" size="mid"></abc-avatar>
                        </div>

                        <div class="send-status">
                            <span v-if="item.sendStatus === 0" class="icon-loading"></span>
                            <span
                                v-if="item.sendStatus === 1"
                                class="iconfont cis-icon-Attention"
                                @click="onClickSendAgain(item)"
                            ></span>
                        </div>

                        <!-- 文本消息 -->
                        <item-visit-message v-if="item.msgType === MsgTypeStatus.MSG_TEXT && followUpTaskSilentTip && item.targetType === 2 && item.isMine" :item="item" :is-mine="item.isMine"></item-visit-message>
                        <div v-else-if="item.msgType === MsgTypeStatus.MSG_TEXT" v-abc-context-menu="[item, menuListNeedCopy, item.isNeedRemove]" class="content-text">
                            <span v-if="item.body" v-html="parseTextToHtml(item.body.text)"></span>
                        </div>
                        <!--预约消息-->
                        <item-register-appointment
                            v-if="appointmentTypes.includes(item.msgType)"
                            :item="item"
                            :info="item.body"
                            :is-mine="item.isMine"
                            @handleRegisterAppointment="handleRegisterAppointment"
                        >
                        </item-register-appointment>
                        <!--预约医生-->
                        <reg-doctor-message v-if="item.msgType === MsgTypeStatus.REG_DOCTOR_MESSAGE" :is-mine="item.isMine"></reg-doctor-message>
                        <!--理疗或者门诊预约医生-->
                        <reg-mode-message v-if="item.msgType === MsgTypeStatus.REG_MODE_MESSAGE" :is-mine="item.isMine"></reg-mode-message>
                        <!--预约信息-->
                        <reg-record-message v-if="item.msgType === MsgTypeStatus.REG_RECORD_MESSAGE" :info="item.body" :is-mine="item.isMine"></reg-record-message>
                        <!-- 项目预约消息-->
                        <project-message v-if="item.msgType === MsgTypeStatus.PROJECT_MESSAGE" :info="item.body" :is-mine="item.isMine"></project-message>
                        <!--预约引导 (医生或者项目)-->
                        <reg-message v-if="item.msgType === MsgTypeStatus.REG_MESSAGE" :is-mine="item.isMine"></reg-message>
                        <!-- 就诊报告-->
                        <report-message v-if="item.msgType === MsgTypeStatus.REPORT_MESSAGE" :info="item.body" :is-mine="item.isMine"></report-message>
                        <!-- 坐诊表-->
                        <doctor-time-message v-if="item.msgType === MsgTypeStatus.DOCTOR_TIME_MESSAGE" :is-mine="item.isMine" :info="item.body"></doctor-time-message>
                        <!-- 图片消息 -->
                        <div v-if="item.msgType === MsgTypeStatus.MSG_IMAGE" v-abc-context-menu="[item, menuList, item.isNeedRemove]" class="content-image">
                            <item-imgbox
                                :sending="item.sendStatus === 0"
                                :info="item.body"
                                @click.native="onClickPreviewMsgImages(item)"
                            ></item-imgbox>
                        </div>
                        <!-- 语音消息 -->
                        <item-audio
                            v-else-if="item.msgType === MsgTypeStatus.MSG_AUDIO"
                            :is-mine="item.isMine"
                            :info="item.body"
                            :playing="playingId === item.id"
                            @audio-stop="playingId = null"
                            @audio-play="playingId = item.id"
                        ></item-audio>

                        <div v-if="item.isMine" class="head-img right">
                            <img :src="item.participanInfo.headImgUrl || wxMpHeadImageUrl" alt="" @error="imgOnError" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <abc-preview
            v-if="preview"
            v-model="preview"
            :lists="previewImagesList"
            append-to-body
            :index="currentIndex"
        ></abc-preview>
    </div>
</template>

<script>
    import ItemTime from './item-time.vue';
    import ItemImgbox from './item-imgbox.vue';
    import ItemAudio from './item-audio.vue';
    import ItemRegisterAppointment from './item-register-appointment.vue';
    import RegDoctorMessage from './reg-doctor-message.vue';
    import RegModeMessage from './reg-mode-message.vue';
    import RegRecordMessage from './reg-record-message.vue';
    import ProjectMessage from './project-message.vue';
    import ReportMessage from './report-message.vue';
    import RegMessage from './reg-message.vue';
    import DoctorTimeMessage from './visit-doctor-message/index.vue';
    import ItemRemoveMsg from './item-remove-msg.vue';
    import ItemVisitMessage from './item-visit-message.vue';
    import { formatCacheTime } from 'utils/index';
    import { parseTextToHtml } from 'assets/configure/emoji-link';
    import { DATE_TIME_MS_FORMATE } from 'assets/configure/constants';
    import DEFAULT_CHAT_HEADER from 'assets/images/crm/icon-default-wehead.png';
    import ImApi from 'api/im';
    import fecha from 'utils/fecha';
    // 消息类型
    import {
        MsgTypeStatus, WaitAppointMsgStatus,
    } from './constants';
    import {
        FromUserTypeEnum, MessageStatusEnum,
    } from 'views/crm/constants';
    const TIMES_INTERVAL = 1000 * 60 * 10; // 没十分钟提示一下时间
    import { copy } from 'utils/dom';
    import AbcAvatar from 'src/views/layout/abc-avatar/index.vue';
    import { PayStatusV2 } from 'views/registration/common/constants';
    import { mapGetters } from 'vuex';
    export default {
        name: 'ViewMessage',
        components: {
            ItemTime,
            ItemImgbox,
            ItemAudio,
            ItemRegisterAppointment,
            ItemVisitMessage,
            RegDoctorMessage,
            RegModeMessage,
            RegRecordMessage,
            ProjectMessage,
            RegMessage,
            ReportMessage,
            DoctorTimeMessage,
            AbcAvatar,
            ItemRemoveMsg,
        },
        props: {
            // 用户id，一般为userInfo.id
            userId: {
                type: String,
                required: true,
            },
            // 诊所id
            clinicId: {
                type: String,
                required: true,
            },
            // 会话id
            conversationId: {
                type: String,
                required: true,
            },
            // 会话详情
            conversationInfo: {
                type: Object,
                required: true,
            },
            // 开始时间，当存在此时间时，视为初始化拉取中间时间的消息，然后上下拉取两端消息
            beginDate: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                MsgTypeStatus,
                MessageStatusEnum,
                messageList: [],
                menuList: [
                    {
                        name: '撤回',
                        key: 'remove',
                        method: (item) => {
                            this.revokeMessage(item);
                        },
                    },
                ],
                menuListNeedCopy: [
                    {
                        name: '复制',
                        key: 'copy',
                        method: (item) => {
                            this.copyMessage(item);
                        },
                    },
                    {
                        name: '撤回',
                        key: 'remove',
                        method: (item) => {
                            this.revokeMessage(item);
                        },
                    },
                ],
                // 向上拉取
                upPageParams: {
                    loading: false,
                    isLast: true,
                },
                // 向下拉取
                downPageParams: {
                    loading: false,
                    isLast: true,
                },
                // 是否打开图片预览
                preview: false,
                previewImagesList: [],
                currentIndex: 0,

                playingId: null, // 当前播放
            };
        },
        computed: {
            ...mapGetters([
                'followUpTaskSilentTip',
            ]),
            userName() {
                return this.conversationInfo?.patient?.name ?? '患者';
            },
            // 消息分段的开始时间、截止时间
            dateSeparated() {
                let beginDate = null;
                let endDate = null;
                if (this.messageList.length !== 0) {
                    const firstItem = this.messageList[0];
                    const lastItem = this.messageList[this.messageList.length - 1];
                    endDate = fecha.format(firstItem.created, DATE_TIME_MS_FORMATE);
                    beginDate = fecha.format(lastItem.created, DATE_TIME_MS_FORMATE);
                }
                if (!endDate) {
                    endDate = fecha.format(Date.now() + 1000 * 60 * 60 * 24, DATE_TIME_MS_FORMATE);
                }
                return {
                    beginDate,
                    endDate,
                };
            },
            // 包含发起预约 预约成功 被拒绝 取消三种状态
            appointmentTypes() {
                return [MsgTypeStatus.REG_CARD_SUCCESS_MESSAGE,
                        MsgTypeStatus.REG_CARD_CANCEL_MESSAGE,
                        MsgTypeStatus.REG_CARD_REQUEST_MESSAGE];
            },
            // 带默认头像的会话详情
            showGroupParticipants() {
                const { groupParticipants = [] } = this.conversationInfo || {};
                return groupParticipants.map((item) => {
                    // if (!item.headImgUrl) {
                    //     item.headImgUrl = DEFAULT_HEADIMAGE;
                    // }
                    return item;
                });
            },
            // 处理消息列表：加入时间分隔
            showMessageList() {
                return this.messageList.reduce((list, curItem) => {
                    let needTimeItem = false;
                    if (list.length !== 0) {
                        const preItem = list[list.length - 1];
                        const preTime = new Date(preItem.created).getTime();
                        const curTime = new Date(curItem.created).getTime();
                        if (curTime - preTime > TIMES_INTERVAL) {
                            needTimeItem = true;
                        }
                    } else {
                        needTimeItem = true;
                    }
                    if (needTimeItem) {
                        // 加入时间分隔
                        const { created } = curItem;
                        const timeItem = {
                            id: `${new Date(created).getTime().toString()}-time`,
                            msgType: 'time',
                            time: formatCacheTime(created),
                            created,
                        };
                        list.push(timeItem);
                    }
                    // 只要不是患者发送的消息都是医生发送的
                    curItem.isMine = curItem.fromUserType !== FromUserTypeEnum.PATIENT_MSG;
                    // 消息被撤回了
                    curItem.revoke = curItem.status === MessageStatusEnum.REVOKE;
                    curItem.isNeedRemove = [FromUserTypeEnum.OWN_MSG, FromUserTypeEnum.ADMINISTRATOR, FromUserTypeEnum.ASSISTANT].includes(curItem.fromUserType) && curItem.status !== MessageStatusEnum.REVOKE;
                    // 会话参与者的详情信息
                    curItem.participanInfo =
                        this.showGroupParticipants.find((item) => item.participantId === curItem.fromUserId) || {};

                    list.push(curItem);

                    return list;
                }, []);
            },
            // 诊所公众号头像
            wxMpHeadImageUrl() {
                const { wxMpHeadImageUrl } = this.conversationInfo || {};
                return wxMpHeadImageUrl || DEFAULT_CHAT_HEADER;
            },
        },
        watch: {
            conversationId: {
                async handler(value) {
                    this.messageList = [];
                    if (value) {
                        if (this.beginDate) {
                            this.upPageParams.isLast = false;
                            this.downPageParams.isLast = false;
                            await this.downFetchMessagelist(this.beginDate);

                            // 拉取一部分历史数据，这样看起来完整，而且触发拉取历史数据更自然
                            this.$nextTick(async () => {
                                const messageBox = this.$refs['message-box'];
                                if (messageBox) {
                                    const oldH = messageBox.scrollHeight;
                                    await this.upFetchMessagelist();
                                    this.$nextTick(() => {
                                        const newH = messageBox.scrollHeight;
                                        const curT = messageBox.scrollTop;
                                        messageBox.scrollTop = newH - oldH + curT;
                                    });
                                }
                            });
                        } else {
                            this.upPageParams.isLast = false;
                            await this.upFetchMessagelist();
                            this.handlePageScrollDown();
                        }
                    }
                },
                immediate: true,
            },
        },
        methods: {
            revokeMessageCtr(item) {
                this.$emit('revokeUnReadCount', item);
                this.$emit('changeLastMessage', item);
                const res = this.messageList.find((o) => o.id === item.msgId);
                if (res) {
                    res.status = MessageStatusEnum.REVOKE;
                    this.messageList = [...this.messageList];
                }
            },
            revokeMessage(item) {
                const res = this.messageList.find((o) => o.id === item.id);
                if (res) {
                    res.status = MessageStatusEnum.REVOKE;
                    this.messageList = [...this.messageList];

                    this.$emit('revoke', item?.msgId || item?.id);
                }
            },
            copyMessage(item) {
                if (item?.body?.text) {
                    copy(item.body.text);
                }
                console.log('save');
            },
            handleRightClick(e,item) {
                if (item?.isMine) {
                    console.log(item,'handleRightClick');
                    e.preventDefault();
                }
            },
            parseTextToHtml,
            imgOnError(event) {
                const img = event.srcElement;
                img.src = DEFAULT_CHAT_HEADER;
                img.onerror = null; // 防止闪图
            },
            /**
             * 当监听页面滑动
             * <AUTHOR>
             * @date 2020-04-29
             */
            async listenerScrollPage() {
                const messageBox = this.$refs['message-box'];
                if (messageBox) {
                    const {
                        scrollTop,
                        offsetHeight,
                        scrollHeight,
                    } = messageBox;

                    // 判断是否需要向上拉取更多
                    if (scrollTop <= 20 && !this.upPageParams.isLast && !this.upPageParams.loading) {
                        await this.upFetchMessagelist();
                        const oldH = scrollHeight;
                        this.$nextTick(() => {
                            const newH = messageBox.scrollHeight;
                            const curT = messageBox.scrollTop;
                            messageBox.scrollTop = newH - oldH + curT;
                        });
                    }

                    // 判断是否需要向下拉取更多
                    const scrollDown = scrollHeight - (scrollTop + offsetHeight);
                    if (
                        this.beginDate &&
                        scrollDown <= 20 &&
                        !this.downPageParams.isLast &&
                        !this.downPageParams.loading
                    ) {
                        await this.downFetchMessagelist();
                        this.$nextTick(() => {
                            messageBox.scrollTop = scrollTop;
                        });
                    }
                }
            },
            /**
             * 向上拉取消息记录
             * <AUTHOR>
             * @date 2020-04-29
             */
            async upFetchMessagelist() {
                this.upPageParams.loading = true;
                try {
                    const params = {
                        userId: this.userId,
                        clinicId: this.clinicId,
                        conversationId: this.conversationId,
                        accessType: 1, // 0-医生在线咨询场景，1-医生患者随访场景，2-患者随访场景
                        limit: 30,
                        endDate: this.dateSeparated.endDate,
                    };
                    const { data } = await ImApi.fetchMessageListByDate(params);
                    if (params.conversationId === this.conversationId) {
                        const messageList = data.messageList || [];
                        this.messageList = [...messageList, ...this.messageList];
                        this.upPageParams.isLast = messageList.length < params.limit;
                    }
                } catch (error) {
                    console.log('upFetchMessagelist error', error);
                }
                this.upPageParams.loading = false;
            },
            /**
             * 向下拉取消息记录
             * <AUTHOR>
             * @date 2020-04-29
             */
            async downFetchMessagelist(beginDate) {
                this.downPageParams.loading = true;
                try {
                    const params = {
                        userId: this.userId,
                        clinicId: this.clinicId,
                        conversationId: this.conversationId,
                        accessType: 1, // 0-医生在线咨询场景，1-医生患者随访场景，2-患者随访场景
                        limit: 30,
                        beginDate: beginDate || this.dateSeparated.beginDate,
                    };
                    const { data } = await ImApi.fetchMessageListByDate(params);
                    if (params.conversationId === this.conversationId) {
                        const messageList = data.messageList || [];
                        this.messageList = [...this.messageList, ...messageList];
                        this.downPageParams.isLast = messageList.length < params.limit;
                    }
                } catch (error) {
                    console.log('downFetchMessagelist error', error);
                }
                this.downPageParams.loading = false;
            },
            /**
             * 将页面滑动到底部
             * <AUTHOR>
             * @date 2020-04-29
             */
            handlePageScrollDown() {
                this.$nextTick(() => {
                    const messageBox = this.$refs['message-box'];
                    if (messageBox) {
                        messageBox.scrollTop = messageBox.scrollHeight;
                    }
                });
            },
            /**
             * 底部插入最新消息
             * <AUTHOR>
             * @date 2020-04-29
             * @param {Object} messageItem 消息对象
             */
            async insertMessageItem(messageItem) {
                while (this.upPageParams.loading || this.downPageParams.loading) {
                    // 当正在拉取聊天记录时，等待拉取完成
                    await new Promise((resolve) => setTimeout(() => resolve(), 200));
                }
                const exit = this.messageList.find((item) => item.id === messageItem.id);
                if (!exit) {
                    this.messageList.push(messageItem);
                    this.$emit('update-last-message', messageItem, true);
                    this.handlePageScrollDown();
                    this.checkRefreshConversationInfo(messageItem.fromUserId);
                    if ([MsgTypeStatus.REG_CARD_SUCCESS_MESSAGE,MsgTypeStatus.REG_CARD_CANCEL_MESSAGE].includes(messageItem?.msgType) && messageItem?.body) {
                        const { registrationSheetId } = messageItem.body;
                        if (registrationSheetId) {
                            // 预约id相同并且状态是申请
                            const apply = this.messageList.find((item) => item?.body?.registrationSheetId === registrationSheetId && item?.body?.applyAuditStatus === WaitAppointMsgStatus.PENDING && item.msgType === MsgTypeStatus.REG_CARD_REQUEST_MESSAGE);
                            if (apply) {
                                const { id } = apply;
                                const index = this.messageList.map((item) => item.id).indexOf(id);
                                this.$set(this.messageList, index, {
                                    ...this.messageList[index],
                                    body: {
                                        ...this.messageList[index].body,
                                        payStatusV2: messageItem.body.payStatusV2,
                                        applyAuditStatus: messageItem?.msgType === MsgTypeStatus.REG_CARD_SUCCESS_MESSAGE ? WaitAppointMsgStatus.PROCESSED : WaitAppointMsgStatus.REFUSE,
                                    },
                                });
                            }
                        }
                    }
                    // 已经有的消息 并且为预约取消，并且退款状态为部分退款或者全退
                } else if ([MsgTypeStatus.REG_CARD_REQUEST_MESSAGE].includes(messageItem.msgType) && messageItem.body?.applyAuditStatus === WaitAppointMsgStatus.REFUSE && [PayStatusV2.PARTED_REFUNDED, PayStatusV2.REFUNED].includes(messageItem.body.payStatusV2)) {
                    const { id } = messageItem;
                    const reRefuse = this.messageList.find((item) => item?.id === id && item?.body?.applyAuditStatus === WaitAppointMsgStatus.REFUSE && item.msgType === MsgTypeStatus.REG_CARD_REQUEST_MESSAGE);
                    if (reRefuse) {
                        const { id } = reRefuse;
                        const index = this.messageList.map((item) => item.id).indexOf(id);
                        // 更新支付状态即可
                        this.$set(this.messageList, index, {
                            ...this.messageList[index],
                            body: {
                                ...this.messageList[index].body,
                                payStatusV2: messageItem.body.payStatusV2,
                                applyAuditStatus: messageItem.body.applyAuditStatus,
                            },
                        });
                    }
                }
            },
            /**
             * 判断当前会话里面是否有目标参与者，没有时视为需要刷新一下会话信息
             * <AUTHOR>
             * @date 2020-05-08
             * @param {String} fromUserId 消息发送者
             */
            checkRefreshConversationInfo(participantId) {
                const exit = this.showGroupParticipants.find((item) => item.participantId === participantId);
                if (!exit) {
                    this.$emit('refresh-conversation-info', this.conversationId);
                }
            },
            /**
             * 更新当前聊天记录患者信息
             * <AUTHOR>
             * @date 2020-05-11
             * @param {Object} patientInfo 患者信息
             */
            updateCurPatientInfo(patientInfo) {
                this.messageList.forEach((item) => {
                    if (item.fromUserId === patientInfo.id && item.participanInfo) {
                        item.participanInfo.name = patientInfo.name;
                    }
                });
            },
            /**
             * 发送成功后处理
             * <AUTHOR>
             * @date 2020-04-27
             * @param {String} {id} 当前消息id
             * @param {String} {msgId} 新消息id
             * sendStatus => 0-发送中，1-发送失败，2-发送成功
             */
            handleSendFinish({ sequenceId }, { msgId }) {
                const target = this.messageList.find((one) => one.sequenceId === sequenceId);
                if (target) {
                    target.msgId = msgId;
                    target.sendStatus = 2;
                }
                target && (target.sendStatus = 2);
            },
            handleRegisterAppointment(type,item) {
                this.$emit('handleRegisterAppointment',type,item);
            },
            /**
             * 发送超时后处理
             * <AUTHOR>
             * @date 2020-04-27
             * @param {String} {sequenceId} 当前消息id
             * sendStatus => 0-发送中，1-发送失败，2-发送成功
             */
            handleSendTimeout({ sequenceId }) {
                const target = this.messageList.find((one) => one.sequenceId === sequenceId);
                target && (target.sendStatus = 1);
            },
            editText(item) {
                if (item?.body?.text) {
                    console.log('editText',item);
                    this.$emit('editText',item.body.text);
                }
            },
            /**
             * 当点击再次发送
             * <AUTHOR>
             * @date 2020-04-29
             * @param {Object} messageItem 需要发送的消息项
             */
            onClickSendAgain({
                id,
                msgType,
                body,
            }) {
                const index = this.messageList.findIndex((item) => item.id === id);
                if (index !== -1) {
                    this.messageList.splice(index, 1);
                    switch (msgType) {
                        case 0: // 文字
                            this.$emit('send-text-again', body);
                            break;
                        case 1: // 图片
                            this.$emit('send-image-again', body);
                            break;
                        default:
                            break;
                    }
                }
            },
            /**
             * 当预览聊天图片时
             * <AUTHOR>
             * @date 2020-04-29
             * @param {String} {id} 消息id
             */
            onClickPreviewMsgImages({
                id,
                sendStatus,
            }) {
                if (sendStatus === 0 || sendStatus === 1) {
                    // 发送中、或者发送失败，不允许预览图片
                    return;
                }
                const list = this.messageList
                    .filter((item) => item.msgType === this.MsgTypeStatus.MSG_IMAGE) // 图片
                    .map((item) => ({
                        id: item.id,
                        url: item.body.url || item.body.imageUrl,
                    }));
                if (list.length !== 0) {
                    const index = list.findIndex((item) => item.id === id);
                    this.preview = true;
                    this.currentIndex = index;
                    this.previewImagesList = list;
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/abc-common.scss';

.crm-module__package-communicate__view-message {
    flex: 1;
    width: 100%;
    overflow-x: hidden;
    overflow-y: overlay;
    background-color: #f7f8fa;

    .scroll {
        position: relative;
        width: 100%;
        padding-top: 52px;
        padding-bottom: 40px;

        &.up-no-data {
            padding-top: 12px;
        }

        &.down-no-data {
            padding-bottom: 0;
        }

        .no-data {
            position: absolute;
            top: 0;
            width: 100%;
            height: 80px;

            @include flex(row, center, center);

            > span {
                font-size: 12px;
                color: $T2;
            }
        }

        .up-loading-more {
            position: absolute;
            top: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 40px;
            margin-bottom: 10px;

            .text {
                font-size: 12px;
                color: #a0b1c4;
            }

            .icon-loading {
                display: inline-block;
                width: 20px;
                height: 20px;
                margin-bottom: 4px;
                background-image: url('~assets/images/<EMAIL>');
                background-repeat: no-repeat;
                background-size: 100% 100%;
                animation: loading-rotate 0.6s linear infinite;
            }
        }

        .down-loading-more {
            @extend .up-loading-more;

            top: auto;
            bottom: 0;
        }

        .item {
            padding-bottom: 12px;

            .sender-name {
                padding: 0 12px;
                margin-bottom: 4px;
                font-size: 12px;
                color: $T3;
            }

            .message {
                display: flex;
                align-items: flex-start;

                .head-img {
                    flex-shrink: 0;

                    img {
                        width: 36px;
                        height: 36px;
                        border-radius: var(--abc-border-radius-small);
                    }
                }

                .send-status {
                    flex-shrink: 0;
                    width: 38px;
                    height: 36px;

                    @include flex(row, center, center);

                    .icon-loading {
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        margin-bottom: 4px;
                        background-image: url('~assets/images/<EMAIL>');
                        background-repeat: no-repeat;
                        background-size: 100% 100%;
                        animation: loading-rotate 0.6s linear infinite;
                    }

                    .iconfont.cis-icon-Attention {
                        font-size: 16px;
                        color: #e52d5b;
                        cursor: pointer;
                    }
                }

                .content-text {
                    position: relative;
                    box-sizing: border-box;
                    min-width: 44px;
                    max-width: 244px;
                    min-height: 36px;
                    padding: 8px 10px;
                    font-size: 14px;
                    font-weight: 400;
                    line-height: 20px;
                    vertical-align: middle;
                    border-radius: var(--abc-border-radius-small);

                    span,
                    div {
                        word-break: break-word;
                    }

                    .emoji {
                        width: 22px;
                        height: 20px;
                        padding: 0 1px;
                        vertical-align: -26%;
                    }
                }

                .content-image {
                    position: relative;
                    box-sizing: border-box;
                    cursor: pointer;
                    border-radius: 2px;
                }
            }

            .message.other {
                justify-content: flex-start;

                .head-img {
                    margin: 0 12px;

                    &.right {
                        display: none;
                    }
                }

                .send-status {
                    display: none;
                }

                .content-text {
                    color: $T1;
                    background-color: #ffffff;
                    border: 1px solid $P6;

                    &::after {
                        position: absolute;
                        top: 14px;
                        left: -5px;
                        width: 8px;
                        height: 8px;
                        content: '';
                        background-color: #ffffff;
                        border-bottom: 1px solid rgba(218, 219, 224, 1);
                        border-left: 1px solid rgba(218, 219, 224, 1);
                        border-bottom-left-radius: 2px;
                        transform: rotate(45deg);
                    }
                }
            }

            .message.mine {
                justify-content: flex-end;

                .head-img {
                    margin: 0 12px;

                    &.left {
                        display: none;
                    }
                }

                .content-text {
                    color: #ffffff;
                    background-color: $G2;

                    &::after {
                        position: absolute;
                        top: 14px;
                        right: -4px;
                        width: 8px;
                        height: 8px;
                        content: '';
                        background-color: #1ec761;
                        border-top-right-radius: 2px;
                        transform: rotate(45deg);
                    }
                }
            }
        }
    }

    &::-webkit-scrollbar-thumb {
        /* 滚动条里面小方块 */
        background: rgba(206, 208, 218, 0);
        border-radius: var(--abc-border-radius-small);
        transform: all 0.15s ease-out;
    }

    &::-webkit-scrollbar-track {
        /* 滚动条里面轨道 */
        background: rgba(240, 242, 245, 0);
        transform: all 0.15s ease-out;
    }

    &:hover {
        &::-webkit-scrollbar-thumb {
            /* 滚动条里面小方块 */
            background: rgba(206, 208, 218, 1);
            border-radius: var(--abc-border-radius-small);
        }

        &::-webkit-scrollbar-track {
            /* 滚动条里面轨道 */
            background: #f7f8fa;
        }
    }
}
</style>
