<template>
    <span class="wechat-push">
        <slot
            :is-push="isPush"
            :wx-status="wxStatus"
            :wx-bind-status="wxBindStatus"
            :patient="patient"
        ></slot>
    </span>
</template>

<script>
    import {
        WxBindStatusEnum, ANONYMOUS_ID,
    } from '@abc/constants';
    import CrmAPI from 'api/crm';

    export default {
        name: 'WechatPush',
        props: {
            patient: {
                type: Object,
                required: true,
                default: () => {
                },
            },
            chainId: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                isPush: false,
                wxBindStatus: WxBindStatusEnum.NO_SUBSCRIBE,
                wxStatus: WxBindStatusEnum.NO_SUBSCRIBE,
            };
        },
        watch: {
            'patient.wxBindStatus': function (val) {
                if (val !== this.wxStatus) {
                    this.wxStatus = val;
                }
            },
        },
        created() {
            this.updateBindStatus(this.patient);
        },
        methods: {
            updateBindStatus(newPatient) {
                if (!newPatient) {
                    newPatient = this.patient;
                }

                if (newPatient && newPatient.id && newPatient.id !== ANONYMOUS_ID) {
                    if (!this.chainId) {
                        console.warn('未提供chainId');
                    }
                    this.fetchPatientWechatPushStatus(newPatient.id, this.chainId);
                    this.fetchPatientWechatBindStatus(newPatient.id);
                }
            },
            async fetchPatientWechatPushStatus(patientId, chainId) {
                const { data } = await CrmAPI.fetchPatientFamilyWechatBindStatus(patientId, chainId);
                console.warn('拉取微信绑定状态',data);

                const {
                    isPush,
                } = data && data.list && data.list[0] || {};
                this.isPush = !!isPush;
            },

            async fetchPatientWechatBindStatus(patientId) {
                const { data } = await CrmAPI.fetchPatientOverviewV2(patientId, {
                    wx: 1,
                    chronicArchives: 1,
                });
                const { wxBindStatus } = data;
                this.wxBindStatus = wxBindStatus;
                this.wxStatus = wxBindStatus;
                const newPatient = Object.assign({},this.patient,data);
                this.$emit('update-patient',newPatient);
            },
        },
    };
</script>
