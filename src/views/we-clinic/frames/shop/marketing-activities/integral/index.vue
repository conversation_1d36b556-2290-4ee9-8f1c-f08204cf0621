<template>
    <biz-setting-layout
        v-abc-loading="loadingFetch"
    >
        <biz-setting-content>
            <biz-setting-form :label-width="112">
                <abc-form v-if="!!postData" ref="postData" item-no-margin>
                    <biz-setting-form-group>
                        <biz-setting-form-item vertical label="微商城获取积分">
                            <abc-checkbox v-model="weShopPostData.mallEnable" type="number">
                                开启
                            </abc-checkbox>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                    <!--适用范围-->
                    <biz-setting-form-group
                        title="适用范围"
                        content-styles="padding-top: 0;"
                    >
                        <marketing-select-card-item
                            :disabled-hover="false"
                            :disabled-hover-text="'请前往营销-积分修改'"
                            :disabled="true"
                            edit-btn-text="选择患者"
                            :tag-width="140"
                            :label="`可使用积分的${viewDistributeConfig?.customerLabel || '患者'}`"
                            :tag-data.sync="postData.usageMemberTypes"
                            :get-icon-function="(item) => item.id !== noMemberId ? 's-vip-color' : ''"
                            @openDialog="visibleSelectTarget = true"
                        >
                            <abc-text v-if="showUsageErrorTips" slot="tips" theme="danger-light">
                                未选择使用{{ viewDistributeConfig?.customerLabel || '患者' }}范围
                            </abc-text>
                        </marketing-select-card-item>
                    </biz-setting-form-group>
                    <!--获取规则-->
                    <biz-setting-form-group v-if="postData.enable || weShopPostData.mallEnable" title="获取规则">
                        <template v-if="postData.enable">
                            <!-- HIS-->
                            <biz-setting-form-item vertical label="获取规则(HIS)">
                                <biz-setting-form-item-indent>
                                    <abc-checkbox
                                        v-model="postData.pointsAccumulateEnable"
                                        type="number"
                                        style="margin-right: 56px;"
                                        :disabled="true"
                                    >
                                        成功消费得积分
                                    </abc-checkbox>
                                </biz-setting-form-item-indent>
                                <biz-setting-form-item-indent v-if="visibleSetAccumulateRat">
                                    <span>每消费</span>
                                    <abc-form-item required>
                                        <abc-input
                                            v-model="postData.amountAccumulateRat"
                                            type="money"
                                            disabled
                                            :max-length="5"
                                            style="margin: 0 4px;"
                                            :config="{
                                                max: 99999, formatLength: 2
                                            }"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                            :width="62"
                                        >
                                            <span slot="append">元</span>
                                        </abc-input>
                                    </abc-form-item>
                                    <span>，积 </span>
                                    <abc-form-item required>
                                        <abc-input
                                            v-model="postData.pointsAccumulateRat"
                                            type="number"
                                            disabled
                                            :max-length="10"
                                            :config="{ max: 99999 }"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                            :width="62"
                                            style="margin: 0 4px;"
                                        >
                                            <span slot="append">分</span>
                                        </abc-input>
                                    </abc-form-item>
                                </biz-setting-form-item-indent>
                            </biz-setting-form-item>
                            <biz-setting-form-item :has-divider="!!weShopPostData.mallEnable" vertical label="参与范围(HIS)">
                                <select-goods-type-list
                                    :disabled="true"
                                    :goods-type-list.sync="postData.applicationGoodsList"
                                    :show-goods-select="postData.isApplicationSpecGoods === 1"
                                    :show-type-select="postData.isApplicationSpecGoods === 1"
                                >
                                    <abc-radio-group
                                        slot="radio-group"
                                        v-model="postData.isApplicationSpecGoods"
                                    >
                                        <biz-setting-form-item-indent>
                                            <abc-radio disabled :label="0">
                                                全部项目
                                            </abc-radio>
                                        </biz-setting-form-item-indent>
                                        <biz-setting-form-item-indent>
                                            <abc-radio disabled :label="1">
                                                指定项目
                                            </abc-radio>
                                        </biz-setting-form-item-indent>
                                    </abc-radio-group>
                                    <template #table>
                                        <select-goods-type-table
                                            :goods-list.sync="postData.applicationGoodsList"
                                            is-member-or-discount
                                            show-except-items
                                            only-show-exception
                                            disabled
                                            :tips="'选择参与活动的项目范围'"
                                        >
                                        </select-goods-type-table>
                                    </template>
                                </select-goods-type-list>
                            </biz-setting-form-item>
                        </template>
                        <template v-if="weShopPostData.mallEnable">
                            <!-- 微商城-->
                            <biz-setting-form-item
                                vertical
                                label="获取规则(微商城)"
                            >
                                <biz-setting-form-item-indent>
                                    <abc-checkbox
                                        v-model="weShopPostData.mallPointsAccumulateEnable"
                                        type="number"
                                        style="margin-right: 56px;"
                                    >
                                        成功消费得积分
                                    </abc-checkbox>
                                </biz-setting-form-item-indent>

                                <biz-setting-form-item-indent v-if="weShopPostData.mallPointsAccumulateEnable">
                                    <span>每消费</span>
                                    <abc-form-item required>
                                        <abc-input
                                            v-model="weShopPostData.mallAmountAccumulateRat"
                                            type="money"
                                            :max-length="5"
                                            style="margin: 0 4px;"
                                            :config="{
                                                max: 99999,
                                                formatLength: 2
                                            }"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                            :width="62"
                                        >
                                            <span slot="append">元</span>
                                        </abc-input>
                                    </abc-form-item>
                                    <span>，积 </span>
                                    <abc-form-item required>
                                        <abc-input
                                            v-model="weShopPostData.mallPointsAccumulateRat"
                                            type="number"
                                            disabled
                                            :max-length="10"
                                            :config="{ max: 99999 }"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                            :width="62"
                                            style="margin: 0 4px;"
                                        >
                                            <span slot="append">分</span>
                                        </abc-input>
                                    </abc-form-item>
                                </biz-setting-form-item-indent>
                            </biz-setting-form-item>
                            <biz-setting-form-item
                                vertical
                                label="参与范围(微商城)"
                            >
                                <select-we-shop-goods-type
                                    :type-list="activityTypeList"
                                    :goods-list="activityGoodsList"
                                    :show-type-tips="false"
                                    :show-goods-tips="false"
                                    :show-select="!!weShopPostData.mallAccumulatePointsGoodsScope"
                                    @change-goods-list="handleChangeGoodsList"
                                    @change-type-list="handleChangeTypeList"
                                >
                                    <template #select-option>
                                        <abc-flex align="center">
                                            <abc-radio-group v-model="weShopPostData.mallAccumulatePointsGoodsScope">
                                                <biz-setting-form-item-indent>
                                                    <abc-radio :label="0">
                                                        全部项目
                                                    </abc-radio>
                                                </biz-setting-form-item-indent>
                                                <abc-space>
                                                    <biz-setting-form-item-indent>
                                                        <abc-radio :label="1">
                                                            指定项目
                                                        </abc-radio>
                                                    </biz-setting-form-item-indent>
                                                    <abc-text v-if="showApplicationSpecErrorTips" theme="danger">
                                                        请选择指定项目
                                                    </abc-text>
                                                </abc-space>
                                            </abc-radio-group>
                                        </abc-flex>
                                    </template>
                                    <template #table>
                                        <select-we-shop-promotion-goods-list
                                            :table-data-list.sync="weShopPostData.applicationMallGoodsList"
                                            :tips="'选择参与活动的项目范围'"
                                            show-except-items
                                        ></select-we-shop-promotion-goods-list>
                                    </template>
                                </select-we-shop-goods-type>
                            </biz-setting-form-item>
                        </template>
                    </biz-setting-form-group>
                    <!--抵扣规则-->
                    <biz-setting-form-group
                        v-if="postData.enable || weShopPostData.mallEnable"
                        title="抵扣规则"
                    >
                        <template v-if="postData.enable">
                            <biz-setting-form-item vertical label="现金抵扣(HIS)">
                                <biz-setting-form-item-indent>
                                    <abc-checkbox
                                        v-model="postData.pointsDeductionEnable"
                                        type="number"
                                        disabled
                                    >
                                        开启
                                    </abc-checkbox>
                                </biz-setting-form-item-indent>
                                <biz-setting-form-item-indent v-if="visibleSetDeductionRat">
                                    <span>收费时可使用积分抵扣现金</span>
                                    <abc-form-item required style="margin-bottom: 0;">
                                        <abc-input
                                            v-model="postData.pointsDeductionRat"
                                            type="number"
                                            :max-length="5"
                                            :config="{
                                                min: 1, max: 99999
                                            }"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                            :width="63"
                                            disabled
                                            style="margin: 0 8px 0 4px;"
                                        >
                                            <span slot="append">积分</span>
                                        </abc-input>
                                        <span>= {{ postData.amountDeductionRat }} 元</span>
                                    </abc-form-item>
                                </biz-setting-form-item-indent>
                            </biz-setting-form-item>
                            <biz-setting-form-item
                                v-if="isShowPointsGoodsDeduction"
                                has-divider
                                vertical
                                label="抵扣商品(HIS)"
                            >
                                <select-goods-type-list
                                    :disabled="true"
                                    :goods-type-list.sync="postData.deductGoodsList"
                                    :show-goods-select="!!postData.pointsGoodsDeductionEnable"
                                    :show-type-select="false"
                                >
                                    <abc-checkbox
                                        slot="radio-group"
                                        v-model="postData.pointsGoodsDeductionEnable"
                                        disabled
                                        class="deduct-switch"
                                    >
                                        开启
                                    </abc-checkbox>
                                    <template #table>
                                        <select-goods-type-table
                                            :disabled="true"
                                            :goods-list.sync="postData.deductGoodsList"
                                            is-member-or-discount
                                            show-points
                                        >
                                        </select-goods-type-table>
                                    </template>
                                </select-goods-type-list>
                            </biz-setting-form-item>
                            <!--                        <manage-section-item-->
                            <!--                            label="退款后返还积分"-->
                            <!--                            class="setting-item"-->
                            <!--                            label-styles="padding-top: 6px;"-->
                            <!--                            content-styles="display: flex;"-->
                            <!--                        >-->
                            <!--                            <div class="content">-->
                            <!--                                <abc-checkbox v-model="postData.pointsRefundEnable" type="number">-->
                            <!--                                    开启-->
                            <!--                                </abc-checkbox>-->
                            <!--                                <abc-text theme="gray-light" size="small">-->
                            <!--                                    开启后，{{ viewDistributeConfig?.customerLabel || '患者' }}使用积分抵扣现金付款，若全额退款，将返还积分-->
                            <!--                                </abc-text>-->
                            <!--                            </div>-->
                            <!--                        </manage-section-item>-->
                        </template>
                        <template v-if="weShopPostData.mallEnable">
                            <biz-setting-form-item
                                label="现金抵扣(微商城)"
                            >
                                <biz-setting-form-item-indent>
                                    <abc-checkbox
                                        v-model="weShopPostData.mallPointsDeductionEnable"
                                        type="number"
                                    >
                                        开启
                                    </abc-checkbox>
                                </biz-setting-form-item-indent>
                                <biz-setting-form-item-indent v-if="weShopPostData.mallPointsDeductionEnable">
                                    <span>开启后，收费时可使用积分抵扣现金 </span>
                                    <abc-form-item required style="margin-bottom: 0;">
                                        <abc-input
                                            v-model="weShopPostData.mallPointsDeductionRat"
                                            type="number"
                                            :max-length="5"
                                            :config="{
                                                min: 1, max: 99999
                                            }"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                            :width="63"
                                            style="margin: 0 8px;"
                                        ></abc-input>
                                        <span>积分 = {{ weShopPostData.mallAmountDeductionRat }} 元</span>
                                    </abc-form-item>
                                </biz-setting-form-item-indent>
                            </biz-setting-form-item>
                            <biz-setting-form-item vertical label="抵扣商品(微商城)">
                                <select-we-shop-goods-type
                                    :goods-list="activityGoodsDeductList"
                                    :show-goods-tips="false"
                                    :show-type-select="false"
                                    :show-select="!!weShopPostData.mallPointsGoodsDeductionEnable"
                                    @change-goods-list="handleChangeGoodsDeductList"
                                >
                                    <template #select-option>
                                        <abc-space>
                                            <abc-checkbox
                                                v-model="weShopPostData.mallPointsGoodsDeductionEnable"
                                                type="number"
                                            >
                                                开启
                                            </abc-checkbox>
                                            <abc-text v-if="showDeductErrorTips" theme="danger">
                                                选择参与活动的项目范围
                                            </abc-text>
                                        </abc-space>
                                    </template>
                                    <template #table>
                                        <select-we-shop-promotion-goods-list
                                            :table-data-list.sync="weShopPostData.deductMallGoodsList"
                                            is-member-or-discount
                                            show-points
                                        ></select-we-shop-promotion-goods-list>
                                    </template>
                                </select-we-shop-goods-type>
                            </biz-setting-form-item>
                        </template>
                    </biz-setting-form-group>
                    <!--积分有效期-->
                    <biz-setting-form-group
                        title="积分有效期"
                    >
                        <biz-setting-form-item
                            label="自动清零"
                        >
                            <biz-setting-form-item-indent>
                                <abc-checkbox
                                    v-model="postData.pointsClearEnable"
                                    disabled
                                    type="number"
                                >
                                    开启
                                </abc-checkbox>
                                <template v-if="postData.pointsClearEnable">
                                    <abc-text>每年清零时间：</abc-text>
                                    <abc-form-item :required="!!postData.pointsClearEnable">
                                        <abc-input
                                            v-model="postData.pointsClearMonth"
                                            disabled
                                            type="number"
                                            :width="88"
                                            :max-length="10"
                                            :config="{ max: 12 }"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                            style="margin-right: 4px;"
                                            @input="onInputMonth"
                                        >
                                            <span slot="appendInner">月</span>
                                        </abc-input>
                                    </abc-form-item>
                                    <abc-form-item :required="!!postData.pointsClearEnable">
                                        <abc-input
                                            v-model="postData.pointsClearDay"
                                            disabled
                                            type="number"
                                            :width="88"
                                            :input-custom-style="{ 'text-align': 'center' }"
                                            :max-length="10"
                                            :config="{ max: maxDay }"
                                            @input="onInputDay"
                                        >
                                            <span slot="appendInner">日</span>
                                        </abc-input>
                                    </abc-form-item>
                                </template>
                            </biz-setting-form-item-indent>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </abc-form>
            </biz-setting-form>
            <template slot="footer">
                <biz-setting-footer>
                    <abc-button
                        v-if="!!postData"
                        :loading="loadingSave"
                        :disabled="disabledSave"
                        @click="onClickSave"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>
    </biz-setting-layout>
</template>

<script>
    import { isEqual } from 'utils/lodash';
    import * as utils from 'utils/index';
    import IntegralApi from 'api/marketing/integral.js';
    import goods from '@/api/goods';
    import { GoodsTypeEnum } from '@abc/constants';
    import clone from 'utils/clone';
    import { mapGetters } from 'vuex';
    import SelectWeShopGoodsType
        from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-goods-type.vue';
    import WeShopAPI from 'api/we-shop';
    import { GOODS_DISCOUNT_TYPE } from 'views/we-clinic/frames/shop/marketing-activities/constants';
    import SelectWeShopPromotionGoodsList
        from 'views/we-clinic/frames/shop/marketing-activities/components/select-we-shop-promotion-goods-list.vue';
    import Clone from 'utils/clone';
    import { isNotNull } from 'utils/index';
    import {
        BizSettingFooter, BizSettingLayout, BizSettingContent,
    } from '@/components-composite/setting-form-layout';
    import BizSettingForm from '@/components-composite/setting-form/src/views/index.vue';
    import {
        BizSettingFormItemIndent,
    } from '@/components-composite/setting-form';
    import BizSettingFormGroup from '@/components-composite/setting-form/src/views/group.vue';
    import BizSettingFormItem from '@/components-composite/setting-form/src/views/item.vue';
    import MarketingSelectCardItem from 'views/marketing/components/marketing-select-card-item.vue';
    import SelectGoodsTypeTable from 'views/marketing/components/select-goods-type-list/select-goods-type-table.vue';
    import SelectGoodsTypeList from 'views/marketing/components/select-goods-type-list/select-goods-type-list.vue';

    export default {
        name: 'WeShopMarketingIntegral',
        components: {
            BizSettingFooter,
            SelectGoodsTypeList,
            SelectGoodsTypeTable,
            BizSettingFormItemIndent,
            MarketingSelectCardItem,
            BizSettingFormItem,
            BizSettingFormGroup,
            BizSettingForm,
            BizSettingLayout,
            BizSettingContent,
            SelectWeShopPromotionGoodsList,
            SelectWeShopGoodsType,
        },
        data() {
            return {
                noMemberId: '-1',
                GoodsTypeEnum,
                loadingFetch: false,
                loadingSave: false,
                memberTypes: [],
                pointsConfig: null,
                postData: null,
                weShopPostData: {
                    mallEnable: 1, //微商城积分开关
                    mallPointsAccumulateEnable: 0, // 微商城获取积分开关
                    mallPointsAccumulateRat: 1, // 微商城积分累计比例，积分值
                    mallAmountAccumulateRat: '', // 微商城积分累计比例，金额值
                    mallAccumulatePointsGoodsScope: 0, // 微商城获取积分项目范围类型。0：全部项目；1：指定项目
                    mallPointsDeductionEnable: 0, // 微商城抵扣积分开关
                    mallPointsDeductionRat: '', // 微商城积分抵扣比例-积分值
                    mallAmountDeductionRat: 1, // 微商城积分抵扣比例-金额
                    mallPointsGoodsDeductionEnable: 0, //微商城积分项目抵扣开关，0：关，1：开
                    deductMallGoodsList: [], // 积分可抵扣的微商城商品列表
                    applicationMallGoodsList: [], // 积分累计的适用微商城商品列表
                    usageMemberTypes: [],
                },
                showUsageErrorTips: false,
                showDeductErrorTips: false,
                showApplicationSpecErrorTips: false,
                selectGoodsType: [
                    GoodsTypeEnum.MEDICINE,
                    GoodsTypeEnum.MATERIAL,
                    GoodsTypeEnum.EXAMINATION,
                    GoodsTypeEnum.TREATMENT,
                    GoodsTypeEnum.OTHER,
                    GoodsTypeEnum.GOODS,
                    GoodsTypeEnum.EYEGLASSES,
                    GoodsTypeEnum.COMPOSE,
                    GoodsTypeEnum.SURGERY,
                ],
                cachePostData: null,
                timer: null,
                pageSize: 10,
                pageIndex: 0,
                exceptIndex: 0,
                exceptGoodsType: [],
                exceptGoodsSubType: '',
                exceptGoodsCMSpec: '',
                exceptGoodsCustomTypeIdList: [],
                selectedExceptGoodsList: [],
                showExceptGoodsListDialog: false,
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            activityGoodsList() {
                return this.weShopPostData.applicationMallGoodsList.filter((item) => item.type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT);
            },
            activityGoodsDeductList() {
                return this.weShopPostData.deductMallGoodsList.map((item) => {
                    return {
                        ...item,
                        goodsDiscountType: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                    };
                });
            },
            activityTypeList() {
                return this.weShopPostData.applicationMallGoodsList.filter((item) => item.type === GOODS_DISCOUNT_TYPE.CATEGORY);
            },
            isShowPointsGoodsDeduction() {
                return this.viewDistributeConfig?.Marketing.integralShowPointsGoodsDeduction;
            },
            currentPageData() {
                let startIndex = this.pageIndex * this.pageSize;
                let endIndex = startIndex + this.pageSize;
                const len =
                    (this.postData.applicationGoodsList && this.postData.applicationGoodsList.length) || 0;
                if (endIndex > len) {
                    endIndex = len;
                }
                if (startIndex < 0) {
                    startIndex = 0;
                }
                return this.postData.applicationGoodsList.slice(startIndex, endIndex);
            },
            // 是否显示积分获取规则比例
            visibleSetAccumulateRat() {
                const { pointsAccumulateEnable } = this.postData || {};
                return pointsAccumulateEnable === 1;
            },
            // 是否显示积分兑换现金比例
            visibleSetDeductionRat() {
                const { pointsDeductionEnable } = this.postData || {};
                return pointsDeductionEnable === 1;
            },
            // 是否有修改
            isUpdated() {
                return !isEqual(this.weShopPostData, this.cachePostData);
            },
            // 禁用保存按钮
            disabledSave() {
                return !this.weShopPostData || !this.isUpdated;
            },
            // 最大日期
            maxDay() {
                let maxDay = 31;
                const { pointsClearMonth } = this.postData;
                if (pointsClearMonth) {
                    switch (parseInt(pointsClearMonth)) {
                        case 1:
                        case 3:
                        case 5:
                        case 7:
                        case 8:
                        case 10:
                        case 12:
                            maxDay = 31;
                            break;
                        case 2:
                            maxDay = 28;
                            break;
                        case 4:
                        case 6:
                        case 9:
                        case 11:
                            maxDay = 30;
                            break;
                        default:
                            maxDay = 31;
                    }
                }
                return maxDay;
            },
            // 是否选中全部人员
            isSelectAll() {
                if (this.postData) {
                    const { usageMemberTypes } = this.postData;
                    return usageMemberTypes.length === this.memberTypes.length + 1;
                }
                return false;
            },
            isPostData() {
                if (this.postData) {
                    return 'border-top: 1px solid #e6eaee;';
                }
                return '';
            },
        },
        beforeDestroy() {
            this.timer && clearTimeout(this.timer);
        },
        async created() {
            this.loadingFetch = true;
            await this.fetchPointsConfig();
            this.loadingFetch = false;
            this.postData = this.createPostData();
            this.cachePostData = clone(this.weShopPostData);
        },
        methods: {
            // 处方数，接诊数，咨询费，空中药房，加工费，快递费，加工费不展示例外
            noExceptItems(item) {
                return [-1, -2, -4, 8, 12, 13, 14].includes(+item.goodsType);
            },
            changePageHandler(pageIndex) {
                this.pageIndex = pageIndex;
            },
            getExceptInfoNumber(item) {
                return item.exceptInfo?.goodsItems?.length || 0;
            },
            /**
             * 查看积分规则设置
             * <AUTHOR>
             * @date 2021-03-09
             */
            async fetchPointsConfig() {
                try {
                    const { data } = await IntegralApi.fetchPointsConfig();
                    this.pointsConfig = data;
                    Object.keys(this.weShopPostData).forEach((key) => {
                        if (data[key] !== undefined && isNotNull(data[key])) {
                            this.weShopPostData[key] = data[key];
                        }
                    });
                    this.weShopPostData.applicationMallGoodsList = this.weShopPostData.applicationMallGoodsList?.map((item) => {
                        return {
                            ...item,
                            name: item.relatedName || '',
                            ...(item.type === GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT && { goodsId: item.relatedValue || '' }),
                            ...(item.type === GOODS_DISCOUNT_TYPE.CATEGORY && { typeId: item.relatedValue || '' }),
                            goodsDiscountType: item.type,
                        };
                    }) || [];
                    this.weShopPostData.deductMallGoodsList = this.weShopPostData.deductMallGoodsList?.map((item) => {
                        return {
                            ...item,
                            name: item.goodsName || '',
                            goodsId: item.goodsId || '',
                            goodsDiscountType: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                            deductionUnitCount: item.deductionUnitCount || '',
                        };
                    }) || [];
                } catch (error) {
                    console.log('fetchPointsConfig error', error);
                }
            },
            /**
             * 创建提交数据结构
             * <AUTHOR>
             * @date 2021-03-09
             * @returns {Object}
             */
            createPostData() {
                const postData = {
                    enable: 1,
                    usageMemberTypes: [], // 可使用积分的用户
                    pointsAccumulateEnable: 0, // 消费累计得积分开关
                    amountAccumulateRat: '', // 积分累计比例，金额值
                    pointsClearEnable: 0, // 积分有效期开关
                    pointsAccumulateRat: 1, // 积分累计比例，积分值
                    pointsClearDay: '', // 自动清理-日
                    pointsClearMonth: '', // 自动清理-月
                    pointsDeductionEnable: 0, // 现金抵扣开关
                    amountDeductionRat: 1, // 积分抵扣比例：金额值
                    pointsDeductionRat: '', // 积分抵扣比例：积分值
                    pointsRefundEnable: 0, // 退还
                    pointsGoodsDeductionEnable: 0, // 积分商品抵扣开关
                    isApplicationSpecGoods: 0, // 是否指定项目
                    deductGoodsList: [],
                    applicationGoodsList: [],
                };

                if (this.pointsConfig) {
                    const {
                        usageMemberTypes, // 可使用积分的用户
                        usageForNoMember, // 是否选中非会员
                        pointsAccumulateEnable, // 消费累计得积分开关
                        amountAccumulateRat, // 消费累计得积分值
                        pointsClearEnable, // 积分有效期开关
                        pointsClearDay, // 自动清理-日
                        pointsClearMonth, // 自动清理-月
                        pointsDeductionEnable, // 现金抵扣开关
                        pointsDeductionRat, // 现金抵扣积分值
                        pointsRefundEnable, // 退还
                        pointsGoodsDeductionEnable,
                        isApplicationSpecGoods,
                        enable,
                        deductGoodsList,
                        applicationGoodsList,
                        pointsAccumulateRat,
                    } = this.pointsConfig;
                    if (usageMemberTypes && usageMemberTypes.length !== 0) {
                        postData.usageMemberTypes = usageMemberTypes.slice();
                    }
                    if (usageForNoMember === 1) {
                        // 选中非会员
                        postData.usageMemberTypes.unshift({
                            id: this.noMemberId,
                            name: '非会员',
                        });
                    }
                    if (utils.isNotNull(enable)) {
                        postData.enable = enable;
                    }
                    if (utils.isNotNull(pointsAccumulateEnable)) {
                        postData.pointsAccumulateEnable = pointsAccumulateEnable;
                    }
                    if (utils.isNotNull(amountAccumulateRat)) {
                        postData.amountAccumulateRat = amountAccumulateRat;
                    }
                    if (utils.isNotNull(pointsClearDay)) {
                        postData.pointsClearDay = pointsClearDay;
                    }
                    if (utils.isNotNull(pointsClearMonth)) {
                        postData.pointsClearMonth = pointsClearMonth;
                    }
                    if (utils.isNotNull(pointsClearEnable)) {
                        postData.pointsClearEnable = pointsClearEnable;
                    }
                    if (utils.isNotNull(pointsDeductionEnable)) {
                        postData.pointsDeductionEnable = pointsDeductionEnable;
                    }
                    if (utils.isNotNull(pointsDeductionRat)) {
                        postData.pointsDeductionRat = pointsDeductionRat;
                    }
                    if (utils.isNotNull(pointsRefundEnable)) {
                        postData.pointsRefundEnable = pointsRefundEnable;
                    }
                    if (utils.isNotNull(pointsGoodsDeductionEnable)) {
                        postData.pointsGoodsDeductionEnable = this.isShowPointsGoodsDeduction ? pointsGoodsDeductionEnable : 0;
                    }
                    if (utils.isNotNull(isApplicationSpecGoods)) {
                        postData.isApplicationSpecGoods = isApplicationSpecGoods;
                    }
                    if (deductGoodsList && deductGoodsList.length) {
                        postData.deductGoodsList = deductGoodsList;
                    }
                    if (applicationGoodsList && applicationGoodsList.length) {
                        postData.applicationGoodsList = applicationGoodsList;
                    }
                    if (utils.isNotNull(pointsAccumulateRat)) {
                        postData.pointsAccumulateRat = pointsAccumulateRat;
                    }
                }
                return postData;
            },
            /**
             * 当修改月份时
             * <AUTHOR>
             * @date 2021-03-09
             */
            onInputMonth() {
                if (this.postData.pointsClearDay > this.maxDay) {
                    this.postData.pointsClearDay = this.maxDay;
                }
            },
            /**
             * 当修改日时
             * <AUTHOR>
             * @date 2021-03-09
             */
            onInputDay() {
                if (this.maxDay < this.postData.pointsClearDay) {
                    this.postData.pointsClearDay = this.maxDay;
                }
            },
            /**
             * 获取提交数据
             * <AUTHOR>
             * @date 2021-03-09
             * @returns {Object}
             */
            getParams() {
                const {
                    enable,
                    usageMemberTypes, // 可使用积分的用户
                    pointsAccumulateEnable, // 消费累计得积分开关
                    amountAccumulateRat, // 积分累计比例，金额值
                    pointsAccumulateRat, // 积分累计比例，积分值
                    pointsClearEnable, // 积分有效期开关
                    pointsClearDay, // 自动清理-日
                    pointsClearMonth, // 自动清理-月
                    pointsDeductionEnable, // 现金抵扣开关
                    amountDeductionRat, // 积分抵扣比例：金额值
                    pointsDeductionRat, // 积分抵扣比例：积分值
                    pointsRefundEnable, // 退还
                    deductGoodsList,
                    pointsGoodsDeductionEnable,
                    isApplicationSpecGoods,
                    applicationGoodsList,

                } = this.postData;
                const params = {
                    enable: enable ? 1 : 0,
                    usageMemberTypes: usageMemberTypes
                        .filter((item) => item.id !== this.noMemberId)
                        .map((item) => item.id), // 可使用积分的用户
                    usageForNoMember: 0, // 是否选择非会员
                    pointsAccumulateEnable, // 消费累计得积分开关
                    amountAccumulateRat, // 积分累计比例，金额值
                    pointsAccumulateRat, // 积分累计比例，积分值
                    pointsClearEnable, // 积分有效期开关
                    pointsClearDay, // 自动清理-日
                    pointsClearMonth, // 自动清理-月
                    pointsDeductionEnable, // 现金抵扣开关
                    amountDeductionRat, // 积分抵扣比例：金额值
                    pointsDeductionRat, // 积分抵扣比例：积分值
                    pointsRefundEnable, // 退还
                    deductGoodsList: deductGoodsList.map((item) => {
                        return {
                            ...item,
                            goodsCMSpec: goods.name,
                        };
                    }) ,
                    applicationGoodsList: applicationGoodsList.map((item) => {
                        return {
                            ...item,
                            goodsCMSpec: goods.name,
                        };
                    }) ,
                    pointsGoodsDeductionEnable: pointsGoodsDeductionEnable ? 1 : 0,
                    isApplicationSpecGoods: isApplicationSpecGoods ? 1 : 0,
                };

                // 非会员处理
                const usageForNoMember = usageMemberTypes.find((item) => item.id === this.noMemberId);
                if (usageForNoMember) {
                    params.usageForNoMember = 1;
                }
                return params;
            },

            /**
             * 当点击保存时
             * <AUTHOR>
             * @date 2021-03-08
             */
            onClickSave() {
                if (this.weShopPostData.mallPointsGoodsDeductionEnable && !this.weShopPostData.deductMallGoodsList?.length) {
                    this.showDeductErrorTips = true;
                    return;
                }
                if (this.weShopPostData.mallAccumulatePointsGoodsScope && !this.weShopPostData.applicationMallGoodsList?.length) {
                    this.showApplicationSpecErrorTips = true;
                    return;
                }
                this.$refs.postData.validate(async (valid) => {
                    if (valid) {
                        this.loadingSave = true;
                        try {
                            const params = this.weShopPostData;
                            const { data } = await WeShopAPI.marketing.savePointRule(params);
                            this.pointsConfig = data;
                            this.cachePostData = clone(this.weShopPostData);
                            this.$Toast({
                                type: 'success',
                                message: '保存成功',
                            });
                        } catch (error) {
                            console.log('onClickSave error', error);
                        }
                        this.loadingSave = false;
                    }
                });
            },

            handleChangeGoodsList(selectedList) {
                const goodsList = selectedList.map((item) => {
                    return {
                        ...item,
                        type: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                        relatedValue: item.goodsId,
                        discountType: 0,
                    };
                });
                this.weShopPostData.applicationMallGoodsList = [...this.activityTypeList, ...goodsList];
            },
            handleChangeGoodsDeductList(selectedList) {
                const goodsList = selectedList.map((item) => {
                    return {
                        ...item,
                        type: GOODS_DISCOUNT_TYPE.SINGLE_PRODUCT,
                        relatedValue: item.goodsId,
                        discountType: 0,
                        deductionUnitCount: this.weShopPostData.mallPointsDeductionRat,
                    };
                });
                this.weShopPostData.deductMallGoodsList = [...goodsList];
            },
            handleChangeTypeList(selectedList) {
                const typeList = Clone(this.activityTypeList);
                selectedList.forEach((item) => {
                    if (!typeList.find((it) => it.typeId === item.typeId)) {
                        this.$set(item, 'exceptItems', []);
                        this.$set(item, 'type', GOODS_DISCOUNT_TYPE.CATEGORY);
                        this.$set(item, 'relatedValue', item.typeId);
                        this.$set(item, 'discountType', 0);
                        delete item.id;
                        typeList.push(item);
                    }
                });

                typeList.forEach((item, index) => {
                    if (!selectedList.some((it) => it.typeId === item.typeId)) {
                        typeList.splice(index, 1);
                    }
                });
                this.weShopPostData.applicationMallGoodsList = [...typeList, ...this.activityGoodsList];
            },
        },
    };
</script>
