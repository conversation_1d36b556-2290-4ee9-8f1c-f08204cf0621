/**
 * @desc 税率相关更新
 * <AUTHOR>
 * @date 2022-07-12 14:57
 */
import {
    formatDate, nextMonth,
} from '@abc/utils-date';
import { mapGetters } from 'vuex';
import GoodsAPI from 'api/goods';
import { GoodsTypeEnum } from '@abc/constants/src';
import { isEqualAllowNull } from 'utils/lodash';
import GoodsV3API from 'api/goods/index-v3';
import { isEqual } from '@abc/utils';

export default {
    data() {
        return {
            isUpdateTaxRate: false,//是否更新税率,用于submit成功后提示框是否显示
            selectedDate: '',//选中的日期
            currentMonthFirstDay: formatDate(new Date(), 'YYYY年MM月01日'),//当前月一号,
            nextMonthFirstDay: formatDate(nextMonth(new Date()), 'YYYY年MM月01日'),//下个月一号
            // nextMonthFirstDay: formatDate(+new Date() + 24 * 3600 * 1000, 'YYYY年MM月DD日'),// 测试使用当前日期第二天生效
            nextMonthDefaultInOutTax: { //下月默认入库税率
                inTaxRat: '-',
                outTaxRat: '-',
            },
            // 需要在引用此 mixin的地方声明
            // ?药品：medicineInfo, 物资：productInfo, 商品：goodsInfo
            // infoKey: 'medicineInfo',
            // catchInfoKey: 'cacheMedicineInfo',
            btnLoading: false,
        };
    },
    computed: {
        ...mapGetters(['goodsConfig', 'allowPharmacyAdjust']),
        currentMonthDefaultInOutTax() {
            return this.goodsConfig?.inOutTaxList?.reduce((res, item) => {
                if (this[this.infoKey].typeId === item.typeId) {
                    res.outTaxRat = item.outTaxRat;
                    res.inTaxRat = item.inTaxRat;
                }
                return res;
            }, {}) ?? {};
        },
    },
    methods: {
        /**
         * @desc 获取下个月默认税率
         * <AUTHOR>
         * @date 2022/7/11 11:16
         */
        async fetchNextMonthDefaultInOutTax() {
            const { data } = await GoodsAPI.getHistoryTaxRate({
                offset: 0,
                limit: 10,
                goodsId: this[this.infoKey].id,
                goodsTypeId: this[this.infoKey].typeId,
                waitingEffect: 1,// 查询未生效的税率
            });
            const [item] = data?.rows ?? [];
            if (item && item.opType === 2) {// ?robins: 0 自定义 2 默认
                this.nextMonthDefaultInOutTax = {
                    inTaxRat: item.inTaxRat,
                    outTaxRat: item.outTaxRat,
                };
            } else {
                // !没有就取这个月的
                this.nextMonthDefaultInOutTax = {
                    ...this.currentMonthDefaultInOutTax,
                };
            }
        },
        async submitBefore() {
            this.btnLoading = true;
            // @abc-error
            try {
                const {
                    defaultInOutTax,
                    inTaxRat,
                    outTaxRat,
                    packagePrice,
                    piecePrice,
                    dismounting,
                    medicineNmpn,
                    medicineCadn,
                    name,
                    type,
                    subType,
                    manufacturerFull,
                    barCode,
                    pieceNum,
                    pieceUnit,
                    packageUnit,
                    componentContentNum,
                    componentContentUnit,
                    medicineDosageNum,
                    medicineDosageUnit,
                    multiPriceList,
                    shebao: {
                        shebaoPieceNum,
                        shebaoPieceUnit,
                        shebaoPackageUnit,
                        shebaoMedicineNmpn,
                    },
                } = this[this.infoKey];
                const {
                    defaultInOutTax: defaultInOutTaxOld,
                    inTaxRat: inTaxRatOld,
                    outTaxRat: outTaxRatOld,
                    packagePrice: packagePriceOld,
                    piecePrice: piecePriceOld,
                    medicineNmpn: medicineNmpnOld,
                    medicineCadn: medicineCadnOld,
                    name: nameOld,
                    manufacturerFull: manufacturerFullOld,
                    barCode: barCodeOld,
                    pieceNum: pieceNumOld,
                    pieceUnit: pieceUnitOld,
                    packageUnit: packageUnitOld,
                    componentContentNum: componentContentNumOld,
                    componentContentUnit: componentContentUnitOld,
                    medicineDosageNum: medicineDosageNumOld,
                    medicineDosageUnit: medicineDosageUnitOld,
                    multiPriceList: multiPriceListOld,
                } = this[this.catchInfoKey];

                // 判断对码风险
                if (this.isChainAdmin || this.isSingleStore) {
                    const params = {
                        goodsType: type,
                        goodsSubType: subType,
                        medicineNmpn,
                        pieceNum,
                        pieceUnit,
                        packageUnit,
                        shebaoPieceNum,
                        shebaoPieceUnit,
                        shebaoPackageUnit,
                        shebaoMedicineNmpn,
                    };
                    const checkShebaoCodeWarnResult = await this.checkShebaoCodeWarning(params);
                    if (checkShebaoCodeWarnResult) {
                        this.btnLoading = false;
                        return;
                    }
                }

                // 判断是否修改过税率
                if (defaultInOutTax !== defaultInOutTaxOld || inTaxRat !== inTaxRatOld || outTaxRat !== outTaxRatOld) {
                    let msg = '';
                    const currentDate = this.currentMonthFirstDay;
                    const nextDate = this.nextMonthFirstDay;
                    let currentLabel = '';
                    let nextLabel = '';
                    // 判断复选框是否变化了
                    if (defaultInOutTax === defaultInOutTaxOld) {
                        msg = `已将进项税从${inTaxRatOld}%改为${inTaxRat}%，销项税从${outTaxRatOld}%改为${outTaxRat}%`;
                    } else {
                        if (defaultInOutTax) {
                            this.isCustomToDefault = true;
                            msg = '已将税率从自定义税率切换至默认税率';
                        } else {
                            this.isCustomToDefault = false;
                            msg = '已将税率从默认税率切换至自定义税率';
                        }
                        currentLabel = defaultInOutTax ? `  进项税${this.currentMonthDefaultInOutTax.inTaxRat}%； 销项税${this.currentMonthDefaultInOutTax.outTaxRat}%` : '';
                        nextLabel = defaultInOutTax ? `  进项税${this.nextMonthDefaultInOutTax.inTaxRat}%； 销项税${this.nextMonthDefaultInOutTax.outTaxRat}%` : '';
                    }


                    const vm = this.$confirm({
                        title: '税率确认',
                        closeAfterConfirm: false,
                        content: () => (<div style="display:flex;flex-direction: column;">
                            <p style="color:#000;">
                                {msg}
                                <br/>
                                请选择新税率的生效时间
                            </p>
                            <abc-radio-group value={this.selectedDate}>
                                <div style="display:flex;align-items:center;font-size: 14px;">
                                    <abc-radio style="margin: 12px 14px 12px 0;" label={currentDate} onClick={() => {
                                        vm.setState(() => {
                                            this.selectedDate = currentDate;
                                        });
                                    }}/>
                                    <span>{currentLabel}</span>
                                </div>
                                <div style="display:flex;align-items:center;font-size: 14px;">
                                    <abc-radio style="margin: 0 14px 0 0;" label={nextDate} onClick={() => {
                                        vm.setState(() => {
                                            this.selectedDate = nextDate;
                                        });
                                    }}/>
                                    <span>{nextLabel}</span>
                                </div>
                            </abc-radio-group>
                        </div>),
                        onConfirm: this.submit,
                    });
                    this.isUpdateTaxRate = true;
                    this.btnLoading = false;
                    return;
                }
                this.isCustomToDefault = false;
                this.isUpdateTaxRate = false;

                if (this.allowPharmacyAdjust) {
                    // 有价格变化
                    if (Number(packagePrice) !== Number(packagePriceOld) ||
                        (dismounting && Number(piecePrice || 0) !== Number(piecePriceOld || 0)) ||
                        !isEqual(multiPriceList, multiPriceListOld)
                    ) {
                        console.log('有价格变化', packagePrice, packagePriceOld, piecePrice, piecePriceOld, multiPriceList, multiPriceListOld);
                        this.$confirm({
                            type: 'info',
                            title: '已加入调价单草稿',
                            content: '调价需要审批，确认本次所有调价商品后，可将调价单草稿提交审批',
                            onConfirm: async () => {
                                this.submit();
                            },
                        });
                        this.btnLoading = false;
                        return;
                    }

                }

                // 批准文号
                const flag1 = !isEqualAllowNull(medicineNmpn, medicineNmpnOld);
                // 通用名
                const flag2 = type === GoodsTypeEnum.MEDICINE ? !isEqualAllowNull(medicineCadn, medicineCadnOld) : !isEqualAllowNull(name, nameOld);
                // 厂家
                const flag3 = !isEqualAllowNull(manufacturerFull, manufacturerFullOld);
                // 条码
                const flag4 = !isEqualAllowNull(barCode, barCodeOld);
                // 规格
                const flag5 = !isEqualAllowNull(componentContentNum, componentContentNumOld) ||
                    !isEqualAllowNull(componentContentUnit, componentContentUnitOld) ||
                    !isEqualAllowNull(medicineDosageNum, medicineDosageNumOld) ||
                    !isEqualAllowNull(medicineDosageUnit, medicineDosageUnitOld) ||
                    !isEqualAllowNull(pieceNum, pieceNumOld) ||
                    !isEqualAllowNull(pieceUnit, pieceUnitOld) ||
                    !isEqualAllowNull(packageUnit, packageUnitOld);

                let fieldText = '';

                if (flag1) {
                    fieldText += '【批准文号】';
                }

                if (flag2) {
                    fieldText += '【通用名】';
                }

                if (flag3) {
                    fieldText += '【厂家】';
                }

                if (flag4) {
                    fieldText += '【条码】';
                }

                if (flag5) {
                    fieldText += '【规格】';
                }

                if (fieldText) {
                    this.$confirm({
                        title: '修改档案确认',
                        type: 'warn',
                        content: () => (
                            <abc-flex vertical gap={8} style={{ width: '460px' }}>
                                <span>修改了{fieldText}字段，请确认是否同一种药品：</span>
                                <abc-space>
                                    <span>*</span>
                                    <span>若不是同一种药品：建议取消修改，新建该药品档案。否则可能导致进销存记录对不齐，医保检查串换药品销售的风险</span>
                                </abc-space>
                                <abc-space>
                                    <span>*</span>
                                    <span>若是同一种药品：请确定修改</span>
                                </abc-space>
                            </abc-flex>
                        ),
                        onConfirm: async () => {
                            this.submit();
                        },
                    });
                    this.btnLoading = false;
                    return;
                }
                // 真正的提交
                await this.submit();
            } catch (error) {
                this.btnLoading = false;
            }
        },
        getMedicineNmpn(string = '') {
            return string.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        },
        async checkShebaoCodeWarning (params) {
            return new Promise((resolve) => {
                const {
                    pieceNum,
                    pieceUnit,
                    packageUnit,
                    medicineNmpn,
                    shebaoMedicineNmpn = '',
                    shebaoPieceNum,
                    shebaoPieceUnit,
                    shebaoPackageUnit,
                } = params;

                let isDiffMedicineNmpn = false;
                const medicineNmpnStr = this.getMedicineNmpn(medicineNmpn);
                const shebaoMedicineNmpnList = shebaoMedicineNmpn.split('；').map((item) => this.getMedicineNmpn(item)).filter((item) => item);
                if (medicineNmpnStr && shebaoMedicineNmpnList.length && !shebaoMedicineNmpnList.find((item) => item === medicineNmpnStr)) {
                    isDiffMedicineNmpn = true;
                }
                if ((!!pieceNum && !!shebaoPieceNum) && ((!!pieceUnit && !!shebaoPieceUnit) || (!!packageUnit && !!shebaoPackageUnit))) {
                    const queryParams = {
                        shebaoRegion: window.$abcSocialSecurity.region,
                        scene: 10,
                        list: [{
                            ...params,
                            pieceCount: null,
                            packageCount: 1,
                        }],
                    };
                    this.verifyUnitSpec(queryParams).then((data) => {
                        if (data) {
                            const warningItem = ['ABC系统档案与医保目录规格无法对应。请确定是否对码错误，或规格填写错误。'];
                            if (isDiffMedicineNmpn) warningItem.unshift('ABC系统档案与医保目录准字不一致，请确定是否对码错误');
                            this.$confirm({
                                title: '医保对码存在合规风险',
                                type: 'warn',
                                confirmText: '去修改',
                                cancelText: '暂不修改',
                                content: () => (
                                    <abc-flex vertical gap="small" style={{ width: '460px' }}>
                                        <ul style={{
                                            listStyle: warningItem.length > 1 ? 'decimal' : 'none',
                                            paddingLeft: warningItem.length > 1 ? '18px' : '0',
                                        }}>
                                            {
                                                warningItem.map((item) => {
                                                    return <li>{item}</li>;
                                                })
                                            }
                                        </ul>
                                        <abc-text theme="gray">*上述风险可能导致结算超刷超限，进销存上报错误。</abc-text>
                                    </abc-flex>
                                ),
                                onConfirm: () => resolve(true),
                                onCancel: () => resolve(false),
                            });
                        } else {
                            resolve(false);
                        }
                    });
                } else if (isDiffMedicineNmpn) {
                    this.$alert({
                        type: 'warn',
                        title: '医保对码存在合规风险',
                        content: 'ABC 系统档案与医保目录准字，请确定是否对码错误',
                        onClose: () => resolve(false),
                    });
                } else {
                    resolve(false);
                }

            });
        },
        async verifyUnitSpec(params) {
            try {
                const { data } = await GoodsV3API.getCollectCodeCountList(params);
                // transformWarn 是否需要警示
                return data.list[0]?.transformWarn === 1;
            } catch (e) {
                return false;
            }
        },
    },
};
