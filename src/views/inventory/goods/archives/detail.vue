<template>
    <div
        class="detail-goods-archives-wrapper"
    >
        <goods-form
            ref="goods-form"
            v-abc-loading="loading"
            :type-id="goods.typeId"
            :error-name-tip="errorNameTip"
            :error-bar-code-tip="errorBarCodeTip"
            :error-short-id-tip="errorShortIdTip"
            :error-trace-code-tip="errorTraceCodeTip"
            :max-cost-info="maxCostInfo"
            :update-logs="updateLogs"
            :chain-max-piece-price="chainMaxPiecePrice"
            :chain-min-piece-price="chainMinPiecePrice"
            :chain-max-package-price="chainMaxPackagePrice"
            :chain-min-package-price="chainMinPackagePrice"
            :selected-clinic-id="selectedClinicId"
            :real-product-info="realProductInfo"
            @changeModel="onGoodsModelChange"
            @quickFiling="handleQuickFiling"
        >
        </goods-form>

        <div class="goods-info-footer">
            <template v-if="isAdmin">
                <div v-if="showDeleteDropDown">
                    <abc-dropdown
                        v-if="!postData.v2DisableStatus && !disabledBaseInfo"
                        placement="bottom-start"
                        data-cy="abc-dropdown-stop-use"
                        @change="handleDisableStatusChange"
                    >
                        <abc-button slot="reference" type="danger">
                            停用
                        </abc-button>
                        <abc-dropdown-item label="停用但保留商品资料" value="reserve"></abc-dropdown-item>
                        <abc-dropdown-item v-if="!hiddenStop" label="停用并删除商品资料" value="delete"></abc-dropdown-item>
                    </abc-dropdown>
                    <abc-button
                        v-else
                        type="blank"
                        :disabled="disabledBaseInfo"
                        @click="changeDisableHandler"
                    >
                        启用
                    </abc-button>
                </div>

                <div v-else>
                    <abc-tooltip
                        v-if="!realProductInfo.chainV2DisableStatus"
                        placement="top-start"
                        :disabled="!!postData.v2DisableStatus"
                    >
                        <abc-button
                            :icon="postData.v2DisableStatus ? '' : 'n-warning-circle-line'"
                            :type="postData.v2DisableStatus ? 'blank' : 'danger'"
                            :disabled="disabledBaseInfo"
                            @click="changeDisableHandler"
                        >
                            {{ postData.v2DisableStatus ? '启用' : '停用' }}
                        </abc-button>
                        <div slot="content">
                            停用后该药品物资将不可进行入库动作（销售，采购，入库）
                        </div>
                    </abc-tooltip>
                </div>

                <abc-space>
                    <abc-button
                        type="blank"
                        :disabled="btnDisabled"
                        :loading="btnLoading"
                        data-cy="inventory-archives-detail-dialog-save-button"
                        @click="beforeSubmit()"
                    >
                        保存修改
                    </abc-button>
                    <abc-button
                        v-if="isVisibleSubmitGspBtn"
                        type="blank"
                        data-cy="inventory-archives-detail-dialog-gsp-button"
                        @click="onClickSubmitGsp"
                    >
                        提交首营
                    </abc-button>
                    <abc-button
                        v-if="isShowPrintPriceTagButton"
                        type="blank"
                        @click="handleClickPrintPricetag"
                    >
                        打印价签
                    </abc-button>
                    <abc-button
                        type="blank"
                        data-cy="inventory-archives-detail-dialog-cancel-button"
                        @click="closeDialog"
                    >
                        取消
                    </abc-button>
                    <abc-dropdown v-if="isPharmacy && !postData.v2DisableStatus" placement="bottom-end" @change="handleMoreChange">
                        <abc-button
                            slot="reference"
                            icon="n-more-line-medium"
                            variant="ghost"
                        >
                        </abc-button>
                        <abc-dropdown-item
                            label="初始库存入库"
                            :value="1"
                        ></abc-dropdown-item>
                    </abc-dropdown>
                </abc-space>
            </template>

            <template v-else>
                <div v-if="showPharmacyDeleteArchiveItem">
                    <abc-dropdown
                        v-if="!postData.v2DisableStatus && !disabledBaseInfo"
                        placement="bottom-start"
                        data-cy="abc-dropdown-stop-use"
                        @change="handleDisableStatusChange"
                    >
                        <abc-button slot="reference" type="danger">
                            停用
                        </abc-button>
                        <abc-dropdown-item label="本店停用商品资料" value="reserve"></abc-dropdown-item>
                        <abc-dropdown-item v-if="!hiddenStop" label="删除连锁商品资料" value="delete"></abc-dropdown-item>
                    </abc-dropdown>
                    <abc-button
                        v-else
                        type="blank"
                        :disabled="disabledBaseInfo"
                        @click="changeDisableHandler"
                    >
                        启用
                    </abc-button>
                </div>
                <div v-else>
                    <abc-tooltip
                        v-if="!realProductInfo.chainV2DisableStatus"
                        placement="top-start"
                        :disabled="!!postData.v2DisableStatus"
                    >
                        <abc-button
                            :icon="postData.v2DisableStatus ? '' : 'n-warning-circle-line'"
                            :type="postData.v2DisableStatus ? 'blank' : 'danger'"
                            :disabled="disabledBaseInfo"
                            @click="changeDisableHandler"
                        >
                            {{ postData.v2DisableStatus ? '启用' : '停用' }}
                        </abc-button>
                        <div slot="content">
                            停用后该药品物资将不可进行入库动作（销售，采购，入库）
                        </div>
                    </abc-tooltip>
                </div>

                <abc-space>
                    <abc-button
                        type="primary"
                        :loading="btnLoading"
                        :disabled="btnDisabled"
                        data-cy="inventory-archives-detail-dialog-save-button"
                        @click="beforeSubmit()"
                    >
                        保存
                    </abc-button>

                    <abc-button
                        v-if="isShowPrintPriceTagButton"
                        type="blank"
                        @click="handleClickPrintPricetag"
                    >
                        打印价签
                    </abc-button>

                    <abc-button
                        type="blank"
                        data-cy="inventory-archives-detail-dialog-cancel-button"
                        @click="closeDialog"
                    >
                        取消
                    </abc-button>
                    <abc-dropdown v-if="isPharmacy && !postData.v2DisableStatus" placement="bottom-end" @change="handleMoreChange">
                        <abc-button
                            slot="reference"
                            icon="n-more-line-medium"
                            variant="ghost"
                        >
                        </abc-button>
                        <abc-dropdown-item
                            label="初始库存入库"
                            :value="1"
                        ></abc-dropdown-item>
                    </abc-dropdown>
                </abc-space>
            </template>
        </div>

        <update-spec-dialog
            v-if="showTips"
            v-model="showTips"
            :type="1"
            :error-data="errorData"
            @confirm="forceSubmit"
        ></update-spec-dialog>

        <disable-dialog
            v-if="showDisableDialog"
            v-model="showDisableDialog"
            :clinics="distributionClinics"
            :goods="postData"
            title="停用但保留商品资料"
            @change="updateV2DisableStatus"
        ></disable-dialog>
    </div>
</template>

<script>
    import {
        computed,
        provide, ref, watch,
    } from 'vue';

    import { mapGetters } from 'vuex';
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import {
        isEqual, isNull,
    } from 'utils/lodash';
    import Clone from 'utils/clone';
    import GoodsAPI from 'api/goods';
    import { paddingMoney } from '@/utils';
    import GoodsArchivesManagerController from 'views/inventory/goods/archives/controller';
    import {
        GoodsArchivesControllerKey,
        disabledBaseInfoKey,
        hiddenPositionKey,
        usePieceUnitFlagKey,
        isShowHistoryTaxratKey,
        isShowUpdateLogKey,
        adjustPriceInfoKey,
        canAdjustPriceKey,
        canUpdateGoodsInfoKey, modifyGoodsItemKey,
    } from 'views/inventory/goods/archives/provideKeys';
    import AddGoodsArchivesDialog from 'views/inventory/goods/archives/dialog';

    import {
        DisableStatusV2,
    } from 'views/common/inventory/constants';
    import Taxrate from 'views/inventory/goods/mixins/taxrate';
    import DisableDialog from 'views/inventory/goods/components/disable-dialog.vue';
    import { ClearInventoryDialog } from '@/views-pharmacy/inventory/frames/components/dialog-clear-inventory/index.js';
    import UpdateSpecDialog from 'views/inventory/goods/components/update-spec-dialog.vue';

    import DialogGoodsDetail from '@/views-pharmacy/gsp/frames/first-battalion/goods/dialog-goods-detail/index.vue';
    import * as constants from '@/views-pharmacy/common/constants';
    import * as tools from '@/views-pharmacy/common/tools';
    import useAdjustPriceInfo from 'views/inventory/goods/archives/hook/adjust-price-info';
    import { gspStatusConst } from '@/views-pharmacy/common/constants';
    import {
        goodsFullName, goodsSpec,
    } from '@/filters';

    import PrintPriceTagDialog from 'views/inventory/components/print-price-tag-dialog';
    import useSichuanNoCode from 'views/inventory/goods/archives/hook/useSichuanNoCode';
    import GoodsStockInDialog from 'components/goods-stock-in-dialog';

    const GoodsForm = () => import('views/inventory/goods/archives/index.vue');

    export default {
        name: 'GoodsArchivesDetail',
        components: {
            UpdateSpecDialog,
            DisableDialog,
            GoodsForm,
        },
        mixins: [Taxrate],
        provide () {
            /**
             * @type {GoodsArchivesManagerController}
             */
            this.GoodsArchivesController = new GoodsArchivesManagerController(true);
            return {
                [GoodsArchivesControllerKey]: this.GoodsArchivesController,
                [disabledBaseInfoKey]: this.disabledBaseInfo,
                [isShowHistoryTaxratKey]: true,
                [isShowUpdateLogKey]: true,
            };
        },
        props: {
            goodsId: String,
            goods: {
                type: Object,
                required: true,
            },
            // 选中的门店id
            selectedClinicId: String,
            // 需要打印价签门店id
            printClinicId: String,
            // 手动控制部分字段的禁用
            disabledBaseInfo: Boolean,
            // 控制柜号字段是否禁用
            hiddenPosition: Boolean,
            // 控制是否能够快速建档
            hiddenSpecChangeIcon: Boolean,
            hiddenStop: Boolean,
            hasStock: Boolean,
        },
        setup(props) {
            const {
                adjustPriceInfo,
                canAdjustPrice,
                initAdjustPriceInfo,
            } = useAdjustPriceInfo();

            const canUpdateGoodsInfo = ref(true);
            const modifyGoodsItem = ref(null);
            const hiddenPosition = ref(props.hiddenPosition);
            // 后端返回的源数据
            const realProductInfo = ref({});

            const usePieceUnitFlag = computed(() => {
                if (props.hiddenSpecChangeIcon) {
                    return false;
                }
                return !!realProductInfo.value.usePieceUnitFlag;
            });

            // 保持计算属性响应式
            provide(canAdjustPriceKey, canAdjustPrice);
            provide(adjustPriceInfoKey, adjustPriceInfo);
            provide(canUpdateGoodsInfoKey, canUpdateGoodsInfo);
            provide(modifyGoodsItemKey, modifyGoodsItem);
            provide(hiddenPositionKey, hiddenPosition);
            provide(usePieceUnitFlagKey, usePieceUnitFlag);


            watch(() => props.hiddenPosition,(v) => {
                hiddenPosition.value = v;
            },{ immediate: true });

            return {
                adjustPriceInfo,
                canAdjustPrice,
                initAdjustPriceInfo,

                canUpdateGoodsInfo,
                modifyGoodsItem,
                realProductInfo,
                usePieceUnitFlag,
            };

        },
        data() {
            return {
                // 前端表单维护的数据
                postData: {},
                // 缓存的前端表单数据
                cacheGoodsModelData: {},
                errorNameTip: '',
                errorBarCodeTip: '',
                errorShortIdTip: '',
                errorTraceCodeTip: '',
                chainMaxPackagePrice: '',
                chainMinPackagePrice: '',
                chainMaxPiecePrice: '',
                chainMinPiecePrice: '',
                // 商品的最高进价
                maxCostInfo: {
                    packageCostPrice: '',
                    clinicName: '',
                },
                // 档案修改日志
                updateLogs: [],
                loading: false,
                btnLoading: false,
                showTips: false,
                errorData: {},
                showDisableDialog: false,
                distributionClinics: [], // 门店分布列表
                modifyPriceOrder: {},
                // !配合 Taxrate这个 mixin使用
                infoKey: 'postData',
                catchInfoKey: 'cacheGoodsModelData',
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'isChainAdmin',
                'isSingleStore',
                'isChainSubStore',
                'userInfo',
                'currentClinic',
                'clinicConfig',
                'goodsConfig',
                'currentPharmacy',
                'goodsConfigPriceAdjustmentNoPowerClinicsId',
                'isPharmacy',
                'isCanDeleteGoodsArchivesInInventory',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
                'featureFeeCompose',
                'featureSupportFeeCategory',
            ]),
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            currentGoodsName() {
                const {
                    type, medicineCadn, name,
                } = this.postData;
                if (type === GoodsTypeEnum.MEDICINE) {
                    return medicineCadn || '药品';
                }
                if (type === GoodsTypeEnum.MATERIAL) {
                    return name || '物资';
                }
                if (type === GoodsTypeEnum.GOODS) {
                    return name || '商品';
                }
                return medicineCadn || name || '';
            },
            isNeedCheckFeeUsed() {
                return this.viewDistributeConfig.Settings.other.isNeedCheckFeeUsed;
            },
            withMemberTypeInfo() {
                return this.viewDistributeConfig.Inventory.withMemberTypeInfo;
            },
            isSupportClearInventoryOnDelete() {
                return this.viewDistributeConfig.Inventory.isSupportClearInventoryOnDelete;
            },
            isSupportManageGoodsArchivesInSubClinic() {
                return this.viewDistributeConfig.Inventory.isSupportManageGoodsArchivesInSubClinic;
            },
            // 子店判断停启用按钮展示逻辑
            showPharmacyDeleteArchiveItem() {
                return this.isSupportManageGoodsArchivesInSubClinic && this.isCanDeleteGoodsArchivesInInventory && !this.realProductInfo.chainV2DisableStatus;
            },
            showDeleteDropDown() {
                if (this.isAdmin) {
                    if (this.isSupportManageGoodsArchivesInSubClinic) {
                        return this.isCanDeleteGoodsArchivesInInventory;
                    }
                    return true;
                }
                return false;
            },
            btnDisabled() {
                const cacheGoodsModelData = Clone(this.cacheGoodsModelData ?? {});
                const postData = Clone(this.postData ?? {});
                // 社保对象只关心这两个是否变化
                const {
                    nationalCode: cacheCode, nationalCodeId: cacheCodeId,
                } = cacheGoodsModelData.shebao || {};
                const {
                    nationalCode, nationalCodeId,
                } = postData.shebao || {};

                cacheGoodsModelData.shebao = {
                    nationalCode: cacheCode,
                    nationalCodeId: cacheCodeId,
                };
                postData.shebao = {
                    nationalCode,
                    nationalCodeId,
                };
                return isEqual(cacheGoodsModelData, postData);
            },
            dispenseMachineList() {
                return this.goodsConfig.readOnlyConfig && this.goodsConfig.readOnlyConfig.dispenseMachineList;
            },
            // 是否待首营
            isGspAwait() {
                return this.goods?.gspStatus === constants.gspStatusConst.AWAIT;
            },
            // 是否已驳回
            isGspReject() {
                return this.goods?.gspStatus === constants.gspStatusConst.REJECT;
            },
            // 是否显示提交首营按钮
            isVisibleSubmitGspBtn() {
                return this.isGspAwait || this.isGspReject;
            },
            // 药物作用分类
            featureSupportPharmacologic() {
                return this.goods.type === GoodsTypeEnum.MEDICINE;
            },
            featureSupportProfitClassification() {
                return this.viewDistributeConfig.Inventory.showProfitClassification;
            },
            isShowRight() {
                // 物资-固定资产与后勤材料不显示
                if (this.postData.typeId === GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS) return false;
                if (this.postData.typeId === GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL) return false;
                return true;
            },
            isSupportPrintPriceTag() {
                return this.viewDistributeConfig.Inventory.isSupportPrintPriceTag;
            },
            isShowPrintPriceTagButton() {
                return this.isSupportPrintPriceTag && (this.isChainAdmin ? !!this.printClinicId : true);
            },
        },
        watch: {
            'postData.shortId': function() {
                this.errorShortIdTip = '';
            },
            'postData.barCode': function() {
                this.errorBarCodeTip = '';
            },
        },
        created() {
            // 只需要初始化一次
            this.GoodsArchivesController.init({
                props: {
                    typeId: this.goods.typeId,
                    type: this.goods.type,
                },
                userInfo: this.userInfo,
                currentClinic: this.currentClinic,
                clinicConfig: this.clinicConfig,
                goodsConfig: this.goodsConfig,
                featureFeeCompose: this.featureFeeCompose,
                featureSupportFeeCategory: this.featureSupportFeeCategory,
                featureSupportPharmacologic: this.featureSupportPharmacologic,
            }, true);

            this.postData = this.GoodsArchivesController.getGoodsInfo();
            this.cacheGoodsModelData = Clone(this.postData);

            this.fetchGoodsInfo();
            this.fetchMaxCostPrice();
            this.fetchGoodsLog();
        },
        beforeDestroy() {
            this.GoodsArchivesController.destroy();
        },
        methods: {
            // 数据模型变了，要重新建立响应式
            onGoodsModelChange() {
                this.postData = this.GoodsArchivesController.getGoodsInfo();
                console.log('onGoodsModelChange Detail',this.postData);
            },
            async fetchGoodsInfo() {
                try {
                    this.loading = true;
                    const res = await GoodsAPI.fetchGoods(this.goodsId, {
                        throwExceptionIfDel: 1,
                        withShebaoCode: 1,
                        withMemberTypeInfo: this.withMemberTypeInfo,
                        forPurchase: 1,
                        // 拉取药品子店的库存价格信息等，不传就是主店的药品资料信息
                        clinicId: this.selectedClinicId,
                        pharmacyNo: isNull(this.pharmacyNo) ? '' : this.pharmacyNo,
                    });
                    const { data } = res;
                    this.realProductInfo = Clone(data);
                    // 有typeId才更新数据模型
                    if (data?.typeId) {
                        this.setGoodsModel(data);
                    }

                    // 拉取调价信息
                    if (data.waitingEffectUpdatePriceItemId) {
                        await this.initAdjustPriceInfo(data.waitingEffectUpdatePriceItemId);
                    }

                    // 审批中，需要拉取首营审批数据
                    if (data.gspModifyStatus === gspStatusConst.DOING) {
                        this.canUpdateGoodsInfo = false;
                        this.modifyGoodsItem = data.modifyGoodsItem;
                    }

                    // 计算总部给门店限制的定价范围
                    if (this.goodsConfig.subClinicPrice?.subSetPrice && !this.isAdmin) {
                        const maxPercent = this.goodsConfig.subClinicPrice.maxPricePercent;
                        const minPercent = this.goodsConfig.subClinicPrice.minPricePercent;
                        this.chainMaxPackagePrice = paddingMoney((maxPercent / 100) * data.chainPackagePrice);
                        this.chainMinPackagePrice = paddingMoney((minPercent / 100) * data.chainPackagePrice);
                        this.chainMaxPiecePrice = paddingMoney((maxPercent / 100) * data.chainPiecePrice);
                        this.chainMinPiecePrice = paddingMoney((minPercent / 100) * data.chainPiecePrice);
                    }
                } catch (e) {
                    console.error(e);
                    // 商品已经被删除
                    if (e.code === 12015) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                            onClose: () => {
                                // 刷新列表
                                this.$emit('updateList', 3);
                                // 关闭弹窗
                                this.$emit('updateList', 0);
                            },
                        });
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                    }
                } finally {
                    this.loading = false;
                }
            },
            async fetchMaxCostPrice() {
                try {
                    this.maxCostInfo = await GoodsAPI.fetchMaxCostPrice(this.goodsId,{
                        clinicId: this.selectedClinicId,
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            async fetchGoodsLog() {
                try {
                    const { data } = await GoodsAPI.fetchGoodsLog(this.goodsId);
                    this.updateLogs = data.logs || [];
                } catch (e) {
                    console.log(e);
                }
            },
            setGoodsModel(goods) {
                // 返回最新数据
                const data = this.GoodsArchivesController.setGoodsModel(goods);
                if (data.goodsTagList && !data.goodsTagIdList) {
                    data.goodsTagIdList = data.goodsTagList.map((item) => item.tagId);
                }
                this.postData = data;
                this.cacheGoodsModelData = Clone(data);
            },
            closeDialog() {
                this.$emit('updateList', 0);
            },
            handleMoreChange(value) {
                if (value === 1) {
                    this.quickStockGoodsIn();
                }
            },
            quickStockGoodsIn() {
                // 打开入库弹窗
                new GoodsStockInDialog({
                    goodsId: this.goodsId,
                    sourceType: 1,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            handleDisableStatusChange(value) {
                switch (value) {
                    case 'reserve':{
                        this.changeDisableHandler();
                        break;
                    }
                    case 'delete':{
                        this.deleteGoodsPrev();
                        break;
                    }
                    default:{
                        console.log('handleDisableStatusChange',value);
                    }
                }
            },
            async changeDisableHandler() {
                if (this.postData.v2DisableStatus) {
                    await this.enableGoods();
                } else {
                    await this.disableGoodsHandler();
                }
            },
            /**
             * @desc 停用
             * <AUTHOR>
             * @date 2020-10-28 10:51:29
             * @params
             * @return
             */
            async disableGoodsHandler() {
                if (this.isSingleStore || this.isChainSubStore) {
                    await this.disableGoods(DisableStatusV2.ForbidTheSale);
                } else {
                    await this.fetchClinicDis();
                    if (this.distributionClinics?.length) {
                        this.showDisableDialog = true;
                    } else {
                        await this.disableGoods(DisableStatusV2.ForbidTheSale);
                    }
                }
            },
            /**
             * @desc 获取该药品门店分布情况
             */
            async fetchClinicDis() {
                const { data } = await GoodsAPI.distribution(this.goodsId, {
                    offset: 0,
                    limit: 100,
                    onlyStock: 1,
                });
                this.distributionClinics = (data && data.rows) || [];
            },
            async disableGoods(v2DisableStatus) {
                const postData = {
                    v2DisableStatus,
                };
                this.$confirm({
                    type: 'warn',
                    title: '停用确认',
                    content: [`停用后不可进行（${v2DisableStatus === DisableStatusV2.ForbidTheSale ? '销售，' : ''}采购，入库）`],
                    confirmText: '停用',
                    onConfirm: async () => {
                        await this.checkGoodsDisable(postData);
                    },
                });
            },
            /**
             * @desc 检查药品是否在套餐中，套餐中包含改药品则抛出异常，否则直接操作成功
             */
            async checkGoodsDisable(postData) {
                try {
                    postData.throwExpIfComposed = true;
                    await GoodsAPI.goodsDisabled(this.goodsId, postData);
                    this.updateV2DisableStatus(postData.v2DisableStatus);
                } catch (e) {
                    this.errorHandler(e, () => {
                        this.changeDisable(postData);
                    });
                }
            },
            deleteGoodsPrev() {
                if (this.hasStock && this.isSupportClearInventoryOnDelete) {
                    // 函数式调用清空剩余库存弹窗
                    new ClearInventoryDialog({
                        visible: true,
                        goodsId: this.goodsId,
                        goods: this.realProductInfo,
                        onConfirm: () => {
                            this.$emit('updateList', 2, false, false);
                        },
                    }).generateDialogAsync({
                        parent: this,
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: `${this.currentGoodsName}删除后不可恢复，请谨慎操作`,
                        onConfirm: async () => {
                            await this.deleteGoods();
                        },
                    });
                }
            },
            async enableGoods() {
                const postData = {
                    v2DisableStatus: DisableStatusV2.Enabled,
                };
                this.$confirm({
                    type: 'warn',
                    title: '启用确认',
                    content: ['启用可进行入库动作（销售，采购，入库）'],
                    confirmText: '启用',
                    onConfirm: async () => {
                        await this.changeDisable(postData);
                    },
                });
            },
            /**
             * @desc 请求改变用户停用启用状态
             */
            async changeDisable(postData) {
                try {
                    postData.throwExpIfComposed = false;
                    await GoodsAPI.goodsDisabled(this.goodsId, postData);
                    this.updateV2DisableStatus(postData.v2DisableStatus);
                } catch (e) {
                    this.errorHandler(e, null);
                }
            },
            errorHandler(err, callback) {
                const projectName = this.isNeedCheckFeeUsed ? '项目' : '套餐';

                // 关联套餐会被停用
                if (err.code === 12808) {
                    const { detail } = err;
                    detail.composite = detail.composite.map((item) => {
                        return `${item.name} <span style="color: var(--abc-color-T2)">【${projectName}】</span>`;
                    });
                    this.$confirm({
                        type: 'warn',
                        title: `此项目存在于以下${detail.composite.length}个${projectName}中，${
                            this.isChainAdmin ? '当该项目在各门店库存为0后，' : ''
                        }将不可出开以下${projectName}`,
                        content: detail.composite,
                        onConfirm: () => {
                            if (callback && typeof callback === 'function') {
                                callback();
                            }
                        },
                    });
                } else if (err.code === 12804) {// 需要从套餐中移除
                    const { detail } = err;
                    detail.composite = detail.composite.map((item) => {
                        return `${item.name} <span style="color: var(--abc-color-T2)">【${projectName}】</span>`;
                    });
                    this.$alert({
                        type: 'warn',
                        title: `该项目存在于以下${detail.composite.length}个${projectName}中，需从${projectName}中移除后才能停用`,
                        content: detail.composite,
                    });
                } else {
                    // 防止重复提示
                    if (!err.alerted) {
                        this.$Toast({
                            type: 'error',
                            message: err.message,
                        });
                    }
                }
            },

            async deleteGoods() {
                try {
                    const res = await GoodsAPI.deleteGoods(this.goodsId);
                    const { status } = res;
                    if (status === 200) {
                        this.$emit('updateList', 2);
                    }
                } catch (error) {
                    if (error.code === 12004) {
                        if (this.isSupportClearInventoryOnDelete) {
                            // 函数式调用清空剩余库存弹窗
                            new ClearInventoryDialog({
                                visible: true,
                                goodsId: this.goodsId,
                                goods: this.realProductInfo,
                                onConfirm: () => {
                                    this.$emit('updateList', 2, false, false);
                                },
                            }).generateDialogAsync({
                                parent: this,
                            });
                        } else {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: `${this.currentGoodsName}还有剩余库存，不能删除`,
                            });
                        }

                    } else if (error.code === 12804) {
                        const projectName = this.isNeedCheckFeeUsed ? '项目' : '套餐';
                        const { detail } = error;

                        detail.composite = detail.composite.map((item) => {
                            return `${item.name} <span style="color: var(--abc-color-T2)">【${projectName}】</span>`;
                        });
                        this.$alert({
                            type: 'warn',
                            title: `此项目存在于以下${detail.composite.length}个${projectName}中，需从${projectName}中移除后才能删除`,
                            content: detail.composite,
                        });
                    } else {
                        // 防止重复提示
                        if (!error.alerted) {
                            this.$Toast({
                                type: 'error',
                                message: error.message,
                            });
                        }
                    }
                }
            },

            /**
             * @desc  点击提交数据 ，先进行检验
             * <AUTHOR>
             * @date 2018/11/01 21:29:20
             * @params
             * @return
             */
            async submit(modelVm) {
                if (modelVm && !this.selectedDate && this.isUpdateTaxRate) {
                    this.$Toast({
                        message: '请选择生效时间',
                        type: 'error',
                    });
                    return;
                }
                this.modifyPriceOrder = {
                    opType: 0,
                    upPercent: 0,
                    lastSupplierName: this.realProductInfo.lastStockInOrderSupplier,
                };

                if (this.isChainAdmin) {
                    this.modifyPriceOrder.affectedClinicIdList = this.goodsConfigPriceAdjustmentNoPowerClinicsId;
                }
                //   先清除错误信息
                this.errorNameTip = '';
                this.errorBarCodeTip = '';
                this.errorShortIdTip = '';
                this.errorTraceCodeTip = '';

                this.$nextTick(() => {
                    const goodsForm = this.$refs['goods-form'];
                    this.btnLoading = true;
                    // 先校验表单
                    goodsForm.validate(async (val) => {
                        if (val) {
                            const submitData = Clone(this.postData);
                            this.update(submitData, modelVm);
                        } else {
                            this.btnLoading = false;
                        }
                    }, null, true);

                });
            },
            beforeSubmit() {
                //   先清除错误信息
                this.errorNameTip = '';
                this.errorBarCodeTip = '';
                this.errorShortIdTip = '';
                this.errorTraceCodeTip = '';

                this.$nextTick(() => {
                    const goodsForm = this.$refs['goods-form'];
                    // 先校验表单
                    goodsForm.validate((val) => {
                        if (val) {
                            // 调用mixins在中的方法
                            this.submitBefore();
                        }
                    }, null, true);

                });
            },
            /**
             * @desc 已知修改规格会带来的影响，强行提交
             * <AUTHOR>
             * @date 2018/10/13 17:26:01
             */
            forceSubmit() {
                const submitData = Clone(this.postData);
                submitData.force = true;
                this.update(submitData);
            },
            async update(data, modelVm) {
                try {
                    if (modelVm) {
                        modelVm.confirmLoading = true;
                        // 税率变化才做处理
                        if (this.isUpdateTaxRate) {
                            // 税率变化对象
                            const effectedTime = this.selectedDate.match(/\d+/g).join('-');// to 2022/12/31
                            data.updateInOutTaxRate = {
                                inTaxRat: data.inTaxRat,
                                outTaxRat: data.outTaxRat,
                                effectedTime,
                                effectedType: this.selectedDate === this.nextMonthFirstDay ? 1 : 0,
                                opType: data.defaultInOutTax ? 2 : 0,// 管理税率1，库存药品税率自定义0 默认 2
                            };
                            // ?产品原话：自定义转默认的时候如果是下个月开始，药品档案那里确实要展示正在用的，下个月在变成默认
                            if (this.isCustomToDefault && this.selectedDate === this.nextMonthFirstDay) {
                                data.defaultInOutTax = 0;
                                data.inTaxRat = this.cacheGoodsModelData?.inTaxRat ?? 0;
                                data.outTaxRat = this.cacheGoodsModelData?.outTaxRat ?? 0;
                            }
                        }
                    }

                    // 没有选择发药机时，不传smartDispenseMachineNo
                    if (data.smartDispenseMachineNo === 0) {
                        data.smartDispenseMachineNo = null;
                    } else if (data.smartDispenseMachineNo >= 1) {
                        if (!this.dispenseMachineList?.length) {
                            data.smartDispenseMachineNo = null;
                        }
                    }

                    this.btnLoading = true;

                    const res = await GoodsAPI.updateGoods(this.goodsId, {
                        ...data,
                        clinicId: this.selectedClinicId,
                        pharmacyNo: isNull(this.pharmacyNo) ? '' : this.pharmacyNo,
                    });


                    // 如果是四川，判断是否是无码，无码的话触发上报
                    if (this.$abcSocialSecurity.config.isSichuan) {
                        const {
                            reportGoodsListNoCode, checkNeedApplySichuanNoCode,
                        } = useSichuanNoCode();
                        if (checkNeedApplySichuanNoCode(data.shebao)) {
                            await reportGoodsListNoCode([data]);
                        }
                    }

                    // 开启审批时修改已首营供应商
                    if (res?.data?.data?.gspModifyStatus === gspStatusConst.DOING) {
                        this.$Toast({
                            type: 'success',
                            message: '修改申请已提交',
                        });
                        this.showToast = false;
                    }

                    if (this.isUpdateTaxRate) {
                        this.$alert({
                            type: 'info',
                            title: '税率更新',
                            content: `税率将在${this.selectedDate}生效，请在历史税率中查看详情`,
                            onClose: () => {
                                this.showTips = false;
                                this.$emit('updateList', 1, true, this.showToast);
                                this.btnLoading = false;
                                this.isUpdateTaxRate = false;
                            },
                        });
                    } else {
                        this.showTips = false;
                        this.$emit('updateList', 1, true, this.showToast);
                        this.btnLoading = false;
                    }
                } catch (err) {
                    console.error('method update', err);
                    let projectName = '套餐';
                    if (this.isNeedCheckFeeUsed) {
                        // eslint-disable-next-line no-unused-vars
                        projectName = '项目';
                    }
                    this.btnLoading = false;
                    if (err.code === 12005) {
                        this.errorBarCodeTip = '条形码重复，请检查';
                    } else if (err.code === 12006) {
                        this.errorNameTip = '药品信息重复（通用名、厂家、包装规格都相同），请检查';
                    } else if (err.code === 12204) {
                        this.errorShortIdTip = err.message?.length < 48 ? err.message : '编码重复，请检查';
                    } else if (err.code === 12007) {
                        this.showTips = true;
                        this.errorData = err.detail;
                    } else if (err.code === 12805) {
                        const { detail } = err;
                        const message = detail.composite.map((item) => {
                            return `${item} <span style="color: var(--abc-color-T2)">【projectName】</span>`;
                        });
                        this.$alert({
                            type: 'warn',
                            title: detail.title,
                            content: message,
                        });
                    } else if (err.code === 12050) {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            confirmText: '复制原档案建档',
                            content: '当前品种已发生拆零进销存，修改最小包装<br>数量将导致历史记录及当前库存错乱，如需<br>修改请重新建档',
                            onConfirm: () => this.handleQuickFiling(2),
                        });
                    } else if (err.code === 12051) {
                        if (err.detail) {
                            const { manufacturerFull } = err.detail || {};
                            this.errorTraceCodeTip = `无法重复绑定。已绑定：${goodsFullName(err.detail)}  ${goodsSpec(err.detail)}  ${manufacturerFull || ''}`;
                        } else {
                            this.errorTraceCodeTip = err.message;
                        }
                    } else {
                        // 防止重复提示
                        if (!err.alerted) {
                            this.$Toast({
                                type: 'error',
                                message: err.message,
                            });
                        }
                        // 编码重复提示
                        // this.dropCodeError = getErrorMessage(err, '药品');
                    }
                } finally {
                    if (modelVm) {
                        modelVm.confirmLoading = false;
                        modelVm.close();
                    }
                }
            },
            // 快速建档-区分重新建档（药店使用删除+重新创建）与复制建档(诊所使用复制新建档案)
            handleQuickFiling(type = 1) {
                const goodsInfo = Clone(this.postData);
                const _goodsId = goodsInfo.id;
                delete goodsInfo.id;
                this._AddGoodsArchivesDialog = new AddGoodsArchivesDialog({
                    hasStock: this.hasStock,
                    deleteGoodsId: _goodsId,
                    isQuickFiling: true,
                    isRecreate: type === 1,
                    isContinue: false,
                    typeId: this.postData.typeId,
                    goodsInfo,
                    updateListFn: this.handleUpdateList,
                });
                this._AddGoodsArchivesDialog?.generateDialogAsync({
                    parent: this,
                });

                this.$on('hook:beforeDestroy', () => {
                    this._AddGoodsArchivesDialog?.destroyDialog();
                    this._AddGoodsArchivesDialog = null;
                });
            },
            // 新建刷新列表
            handleUpdateList(type = 1, keepOrder = false, showToast = true) {
                this.$emit('updateList', type, keepOrder, showToast);
            },
            updateV2DisableStatus(v2DisableStatus) {
                this.GoodsArchivesController.updateGoodsInfo('v2DisableStatus', v2DisableStatus);
                this.cacheGoodsModelData = Clone(this.postData);
                // 触发一次列表刷新
                this.$emit('updateList', 1);
            },
            /**
             * 当点击提交首营
             * <AUTHOR>
             * @date 2024-01-16
             */
            async onClickSubmitGsp() {
                const openResponse = await tools.openDialog({
                    propsData: {
                        goodsId: this.goodsId, // 商品id
                    },
                    component: DialogGoodsDetail,
                });
                if (openResponse.status === false) {
                    return openResponse;
                }
                this.$emit('updateList', 3);
                this.closeDialog();
            },
            // 打印价签
            handleClickPrintPricetag() {
                new PrintPriceTagDialog({
                    params: {
                        goodsIdList: [this.goodsId],
                        isAllChecked: false,
                        queryParams: {
                            clinicId: this.printClinicId,
                        },
                    },
                }).generateDialogAsync({
                    parent: this,
                });
            },
        },
    };
</script>


