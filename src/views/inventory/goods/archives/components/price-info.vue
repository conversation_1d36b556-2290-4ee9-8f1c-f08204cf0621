<template>
    <div class="goods-archives-info price-info-wrapper">
        <abc-row :gutter="[24, 16]" :wrap="'wrap'">
            <template v-for="item in renderData">
                <abc-col v-bind="item.colProps" :key="item.prop">
                    <abc-form-item v-if="item.prop === 'currentSell'" :label="item.label">
                        <abc-select
                            v-model="currentIsSell"
                            :width="206"
                            :disabled="disabled"
                            data-cy="inventory-archives-price-info-form-is-sell-select"
                            @enter="enterEvent"
                        >
                            <abc-option
                                v-for="it in options.sell"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>

                    <abc-form-item v-if="item.prop === 'currentPriceType'" :label="item.label">
                        <abc-select
                            v-model="currentPriceType"
                            :width="206"
                            :disabled="disabledMakeupPercent"
                            data-cy="inventory-archives-price-info-form-price-type-select"
                            @enter="enterEvent"
                        >
                            <abc-option
                                v-for="it in options.priceMode"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>

                    <div v-else-if="item.prop === 'currentPriceMakeupPercent'">
                        <abc-form-item
                            hidden-red-dot
                            :help="makeupPercentRangeText"
                            :required="item.required"
                            :label="item.label"
                            :validate-event="validateMakeupPercent"
                        >
                            <abc-input
                                v-model="currentPriceMakeupPercent"
                                v-abc-focus-selected
                                :width="206"
                                :max-length="10"
                                :disabled="disabledMakeupPercent"
                                type="money"
                                :config="{
                                    supportZero: true
                                }"
                                data-cy="inventory-archives-price-info-form-price-makeup-percent-input"
                                @enter="enterEvent"
                            >
                                <span slot="appendInner" style="color: var(--abc-color-T3);">%</span>
                            </abc-input>
                        </abc-form-item>
                    </div>

                    <div v-else-if="item.prop === 'currentPackagePrice'">
                        <abc-form-item
                            :required="item.required"
                            :label="item.label"
                            :validate-event="validatePackageUnitPrice"
                            class="goods-package-price"
                        >
                            <template v-if="isChainAdmin || isCanSeeGoodsCostInInventory">
                                <view-cost-price-dialog
                                    v-if="goodsInfo.id"
                                    :id="goodsInfo.id"
                                    :clinic-id="currentClinic?.clinicId"
                                    :pharmacy-no="currentPharmacy?.no"
                                    type="medicine"
                                    :slot-style="{
                                        position: 'absolute',
                                        right: '0',
                                        top: '-24px',
                                    }"
                                ></view-cost-price-dialog>
                            </template>


                            <div
                                v-show="isShowPackagePriceLimitTips && !isSupportShebaoLimitListingPrice"
                                class="price-reminder-wrapper"
                            >
                                <abc-popover
                                    placement="top"
                                    trigger="hover"
                                    theme="yellow"
                                    :open-delay="500"
                                    :popper-style="{ padding: 0 }"
                                >
                                    <abc-icon
                                        slot="reference"
                                        icon="Attention"
                                        size="14"
                                        color="var(--abc-color-Y2)"
                                    ></abc-icon>

                                    <div v-if="isSupportShebaoListingPrice">
                                        <abc-flex style="padding: 8px 12px;" vertical>
                                            <abc-flex v-if="validateShebaoPrice?.isOverPackageLimitPrice" :gap="8">
                                                <abc-text theme="gray">
                                                    限价
                                                </abc-text>
                                                <abc-text bold theme="black">
                                                    {{ validateShebaoPrice?.packageLimitPrice ?? '' }}
                                                </abc-text>
                                            </abc-flex>
                                            <abc-flex v-if="validateShebaoPrice?.isOverPackageListingPrice" :gap="8">
                                                <abc-text theme="gray">
                                                    挂网价
                                                </abc-text>
                                                <abc-text bold theme="black">
                                                    {{ validateShebaoPrice?.packageListingPrice ?? '' }}
                                                </abc-text>
                                            </abc-flex>
                                        </abc-flex>
                                        <abc-divider theme="dark" margin="none"></abc-divider>
                                        <abc-flex :gap="4" align="center" style="padding: 8px 12px; line-height: 16px;">
                                            <div style="line-height: 16px;">
                                                <abc-icon
                                                    icon="Attention"
                                                    size="12"
                                                    color="var(--abc-color-Y2)"
                                                ></abc-icon>
                                            </div>
                                            <abc-text theme="warning-light" size="mini">
                                                {{ isChainAdmin ? '定价超限，请前往门店开启“限价助手”避免医保超刷' : '定价超限，请开启“限价助手”避免超刷罚款' }}
                                            </abc-text>
                                            <abc-link
                                                v-if="hasSocialModule && !isChainAdmin"
                                                size="small"
                                                style="margin-left: 4px; font-size: 12px;"
                                                @click="openShebaoPriceSetting"
                                            >
                                                去开启
                                                <abc-icon
                                                    slot="append"
                                                    size="14"
                                                    icon="n-right-line-medium"
                                                ></abc-icon>
                                            </abc-link>
                                        </abc-flex>
                                    </div>
                                    <abc-flex v-else vertical style="padding: 10px;">
                                        <span v-if="validateShebaoPrice?.isOverPackageLimitPrice">{{ `限价：${validateShebaoPrice?.packageLimitPrice ?? '-'}` }}</span>
                                        <span v-if="validateShebaoPrice?.isOverPackageListingPrice">{{ `挂网价：${validateShebaoPrice?.packageListingPrice ?? '-'}` }}</span>
                                    </abc-flex>
                                </abc-popover>
                            </div>

                            <div
                                v-show="validatePriceText"
                                class="price-reminder-wrapper"
                            >
                                <abc-tooltip placement="top">
                                    <abc-icon
                                        icon="Attention"
                                        size="14"
                                        color="var(--abc-color-Y2)"
                                    ></abc-icon>

                                    <div slot="content">
                                        {{ validatePriceText }}
                                    </div>
                                </abc-tooltip>
                            </div>

                            <abc-popover
                                trigger="hover"
                                style="margin-left: auto;"
                                placement="bottom-end"
                                :open-delay="500"
                                theme="yellow"
                                width="100"
                                :disabled="!tipsText"
                            >
                                <span>{{ tipsText }}</span>
                                <div slot="reference">
                                    <abc-tooltip placement="top" :disabled="priceToolTip === '' || !disabledPackagePrice" :content="priceToolTip">
                                        <div>
                                            <abc-popover
                                                placement="top"
                                                trigger="focus"
                                                theme="yellow"
                                                :open-delay="500"
                                                :popper-style="{ padding: 0 }"
                                                :disabled="!!priceToolTip || !(isShowPackagePriceLimitTips && isSupportShebaoLimitListingPrice) || !(goodsInfo.id)"
                                            >
                                                <div slot="reference">
                                                    <abc-input
                                                        v-model="currentPackagePrice"
                                                        v-abc-focus-selected
                                                        :width="206"
                                                        type="money"
                                                        :disabled="disabledPackagePriceMakeUp"
                                                        :config="getPriceConfig"
                                                        data-cy="inventory-archives-price-info-form-package-price-input"
                                                        @enter="enterEvent"
                                                    >
                                                        <label slot="prepend" class="prepend" :style="`color: ${$store.state.theme.style.T3}`">
                                                            <abc-currency-symbol-icon></abc-currency-symbol-icon>
                                                        </label>
                                                        <div slot="appendInner" :style="`color: ${$store.state.theme.style.T3}`">
                                                            <span v-if="currentPackageUnit">/ {{ currentPackageUnit }}</span>
                                                        </div>
                                                    </abc-input>
                                                </div>
                                                <div v-if="isShowPackagePriceLimitTips && isSupportShebaoLimitListingPrice">
                                                    <abc-flex style="padding: 8px 12px;" vertical>
                                                        <!--<abc-flex v-if="validateShebaoPrice?.isOverPackageLimitPrice" :gap="8">-->
                                                        <!--    <abc-text theme="gray">-->
                                                        <!--        超出限价-->
                                                        <!--    </abc-text>-->
                                                        <!--    <abc-text bold theme="black">-->
                                                        <!--        {{ validateShebaoPrice?.packageLimitPrice ?? '' }}-->
                                                        <!--    </abc-text>-->
                                                        <!--</abc-flex>-->
                                                        <abc-flex v-if="validateShebaoPrice?.isOverPackageListingPrice" :gap="8">
                                                            <abc-text theme="gray">
                                                                挂网价
                                                            </abc-text>
                                                            <abc-text bold theme="black">
                                                                {{ validateShebaoPrice?.packageListingPrice ?? '' }}
                                                            </abc-text>
                                                        </abc-flex>
                                                    </abc-flex>
                                                    <abc-divider theme="dark" margin="none"></abc-divider>
                                                    <abc-flex :gap="4" align="center" style="padding: 8px 12px; line-height: 16px;">
                                                        <div style="line-height: 16px;">
                                                            <abc-icon
                                                                icon="n-check-circle-fill"
                                                                size="12"
                                                                color="var(--abc-color-G2)"
                                                            ></abc-icon>
                                                        </div>
                                                        <abc-text theme="black" size="mini">
                                                            已开启“限价助手”，可避免医保超刷罚款
                                                        </abc-text>
                                                    </abc-flex>
                                                </div>
                                                <div v-if="goodsInfo.id" style="padding: 8px 10px;">
                                                    最近进价: {{ (goodsInfo.lastPackageCostPrice) ? formatMoney(goodsInfo.lastPackageCostPrice,false) : '--' }}
                                                </div>
                                            </abc-popover>
                                        </div>
                                    </abc-tooltip>
                                </div>
                            </abc-popover>

                            <div v-if="isChainSubStore && !isSelectCostPriceAddition" class="price-range">
                                <template v-if="showSubSetPrice">
                                    <span v-if="chainPackagePriceRange">
                                        定价范围：{{ chainPackagePriceRange }}
                                    </span>
                                </template>
                                <span v-else>
                                    不支持门店自主定价
                                </span>
                            </div>
                        </abc-form-item>
                    </div>

                    <div v-else-if="item.prop === 'currentPiecePrice'">
                        <div v-if="!isChineseMedicineType" class="custom-label">
                            <abc-checkbox
                                v-model="currentDismounting"
                                :disabled="!currentIsSell || disabledPackagePrice || disabled"
                                data-cy="abc-checkbox-separate-price"
                            >
                                <span style="color: var(--abc-color-T2);">允许拆零销售</span>
                            </abc-checkbox>
                        </div>
                        <abc-form-item :label="item.label" :required="item.required" :validate-event="validatePieceUnitPrice">
                            <view-cost-price-dialog
                                v-if="goodsInfo.id && isCanSeeGoodsCostInInventory && isChineseMedicineType"
                                :id="goodsInfo.id"
                                :clinic-id="currentClinic?.clinicId"
                                :pharmacy-no="currentPharmacy?.no"
                                type="medicine"
                                :slot-style="{
                                    position: 'absolute',
                                    right: '0',
                                    top: '-24px',
                                }"
                            ></view-cost-price-dialog>

                            <div
                                v-show="isShowPiecePriceLimitTips && !isSupportShebaoLimitListingPrice"
                                class="price-reminder-wrapper"
                            >
                                <abc-popover
                                    placement="top"
                                    trigger="hover"
                                    theme="yellow"
                                    :open-delay="500"
                                    :popper-style="{ padding: 0 }"
                                >
                                    <abc-icon
                                        slot="reference"
                                        icon="Attention"
                                        size="14"
                                        color="var(--abc-color-Y2)"
                                    ></abc-icon>

                                    <div v-if="isSupportShebaoListingPrice">
                                        <abc-flex style="padding: 8px 12px;" vertical>
                                            <abc-flex v-if="validateShebaoPrice?.isOverPieceLimitPrice" :gap="8">
                                                <abc-text theme="gray">
                                                    限价
                                                </abc-text>
                                                <abc-text bold theme="black">
                                                    {{ validateShebaoPrice?.pieceLimitPrice ?? '' }}
                                                </abc-text>
                                            </abc-flex>
                                            <abc-flex v-if="validateShebaoPrice?.isOverPieceListingPrice" :gap="8">
                                                <abc-text theme="gray">
                                                    挂网价
                                                </abc-text>
                                                <abc-text bold theme="black">
                                                    {{ validateShebaoPrice?.pieceListingPrice ?? '' }}
                                                </abc-text>
                                            </abc-flex>
                                        </abc-flex>
                                        <abc-divider theme="dark" margin="none"></abc-divider>
                                        <abc-flex :gap="4" align="center" style="padding: 8px 12px; line-height: 16px;">
                                            <div style="line-height: 16px;">
                                                <abc-icon
                                                    icon="Attention"
                                                    size="12"
                                                    color="var(--abc-color-Y2)"
                                                ></abc-icon>
                                            </div>
                                            <abc-text theme="warning-light" size="mini">
                                                {{ isChainAdmin ? '定价超限，请前往门店开启“限价助手”避免医保超刷' : '定价超限，请开启“限价助手”避免超刷罚款' }}
                                            </abc-text>
                                            <abc-link
                                                v-if="hasSocialModule && !isChainAdmin"
                                                size="small"
                                                style="margin-left: 4px; font-size: 12px;"
                                                @click="openShebaoPriceSetting"
                                            >
                                                去开启
                                                <abc-icon
                                                    slot="append"
                                                    size="14"
                                                    icon="n-right-line-medium"
                                                ></abc-icon>
                                            </abc-link>
                                        </abc-flex>
                                    </div>
                                    <abc-flex v-else vertical style="padding: 10px;">
                                        <span v-if="validateShebaoPrice?.isOverPieceLimitPrice">{{ `限价：${validateShebaoPrice?.pieceLimitPrice ?? '-'}` }}</span>
                                        <span v-if="validateShebaoPrice?.isOverPieceListingPrice">{{ `挂网价：${validateShebaoPrice?.pieceListingPrice ?? '-'}` }}</span>
                                    </abc-flex>
                                </abc-popover>
                            </div>

                            <abc-popover
                                trigger="hover"
                                style="margin-left: auto;"
                                placement="bottom-end"
                                theme="yellow"
                                width="100"
                                :open-delay="500"
                                :disabled="!tipsText"
                            >
                                <span v-if="tipsText">{{ tipsText }}</span>
                                <div slot="reference">
                                    <abc-tooltip placement="top" :disabled="priceToolTip === '' || !disabledPiecePrice" :content="priceToolTip">
                                        <div>
                                            <abc-popover
                                                placement="top"
                                                trigger="hover"
                                                theme="yellow"
                                                :open-delay="500"
                                                :popper-style="{ padding: 0 }"
                                                :disabled="!!priceToolTip || !(isShowPiecePriceLimitTips && isSupportShebaoLimitListingPrice) || !(goodsInfo.id)"
                                            >
                                                <div slot="reference">
                                                    <abc-input
                                                        v-model="currentPiecePrice"
                                                        v-abc-focus-selected
                                                        :width="206"
                                                        type="money"
                                                        :config="getPriceConfig"
                                                        :disabled="disabledPiecePrice"
                                                        data-cy="inventory-archives-price-info-form-piece-price-input"
                                                        @enter="enterEvent"
                                                    >
                                                        <label slot="prepend" class="prepend" :style="`color: ${$store.state.theme.style.T3}`">
                                                            <abc-currency-symbol-icon></abc-currency-symbol-icon>
                                                        </label>
                                                        <div slot="appendInner" :style="`color: ${$store.state.theme.style.T3}`">
                                                            <span v-if="currentPieceUnit">/ {{ currentPieceUnit }}</span>
                                                        </div>
                                                    </abc-input>
                                                </div>
                                                <div v-if="isShowPiecePriceLimitTips && isSupportShebaoLimitListingPrice">
                                                    <abc-flex style="padding: 8px 12px;" vertical>
                                                        <abc-flex v-if="validateShebaoPrice?.isOverPieceListingPrice" :gap="8">
                                                            <abc-text theme="gray">
                                                                挂网价
                                                            </abc-text>
                                                            <abc-text bold theme="black">
                                                                {{ validateShebaoPrice?.pieceListingPrice ?? '' }}
                                                            </abc-text>
                                                        </abc-flex>
                                                    </abc-flex>
                                                    <abc-divider theme="dark" margin="none"></abc-divider>
                                                    <abc-flex :gap="4" align="center" style="padding: 8px 12px; line-height: 16px;">
                                                        <div style="line-height: 16px;">
                                                            <abc-icon
                                                                icon="n-check-circle-fill"
                                                                size="12"
                                                                color="var(--abc-color-G2)"
                                                            ></abc-icon>
                                                        </div>
                                                        <abc-text theme="black" size="mini">
                                                            已开启“限价助手”，可避免医保超刷罚款
                                                        </abc-text>
                                                    </abc-flex>
                                                </div>
                                                <div v-if="goodsInfo.id" style="padding: 8px 10px;">
                                                    最近进价: {{ (goodsInfo.lastPackageCostPrice) ? formatMoney(goodsInfo.lastPackageCostPrice,false) : '--' }}
                                                </div>
                                            </abc-popover>
                                        </div>
                                    </abc-tooltip>
                                </div>
                            </abc-popover>
                            <div v-if="isChainSubStore && !isSelectCostPriceAddition" class="price-range">
                                <span
                                    v-if="showSubSetPrice && chainPiecePriceRange"
                                >
                                    定价范围：{{ chainPiecePriceRange }}
                                </span>
                            </div>
                        </abc-form-item>
                    </div>


                    <div v-else-if="item.prop === 'currentMemberPriceList'">
                        <div v-if="isOnlyOneMemberPrice" class="custom-label">
                            <abc-flex align="center" justify="space-between" flex="1">
                                <span style="color: var(--abc-color-T2);">会员价</span>
                                <abc-text size="mini" style="color: var(--abc-color-T2);">
                                    {{ memberPriceProfitRateText }}
                                </abc-text>
                            </abc-flex>
                        </div>

                        <abc-form-item
                            :label="isOnlyOneMemberPrice ? '' : item.label"
                            :help="item.help"
                            :help-theme="item.helpTheme"
                            :validate-event="isOnlyOneMemberPrice ? validateMemberPrice : null"
                        >
                            <abc-space v-if="isOnlyOneMemberPrice" is-compact>
                                <template
                                    v-if="currentMemberPriceList[0].discountType === DiscountTypes.special"
                                >
                                    <abc-tooltip content="会员价高于原价金额" :disabled="!checkMemberPriceExceed(currentMemberPriceList[0].packagePrice)">
                                        <abc-input
                                            ref="memberPriceInputRef"
                                            v-model="currentMemberPriceList[0].packagePrice"
                                            v-abc-focus-selected
                                            type="money"
                                            :width="132"
                                            :placeholder="memberPricePlaceholder"
                                            :config="getPriceConfig"
                                            :disabled="disabledMemberPrice || disabledPackagePrice"
                                            :input-custom-style="{
                                                'text-align': 'left',
                                                color: checkMemberPriceExceed(currentMemberPriceList[0].packagePrice) ? 'var(--abc-color-Y2)' : (disabledMemberPrice || disabledPackagePrice) ? 'var(--abc-color-T3)' : '',
                                            }"
                                            :title="currentMemberPriceList[0].packagePrice"
                                            @change="handleChangeMemberPrice"
                                        >
                                            <abc-tooltip
                                                v-if="memberPriceToolTip"
                                                slot="appendInner"
                                                placement="top"
                                                :content="memberPriceToolTip"
                                            >
                                                <abc-icon
                                                    icon="Attention"
                                                    size="14"
                                                    color="var(--abc-color-Y2)"
                                                ></abc-icon>
                                            </abc-tooltip>
                                            <abc-text
                                                v-else
                                                slot="appendInner"
                                                size="mini"
                                                theme="gray-light"
                                                style="font-size: 12px;"
                                            >
                                                {{ memberPriceDiscountText }}
                                            </abc-text>
                                        </abc-input>
                                    </abc-tooltip>
                                </template>

                                <template
                                    v-else-if="currentMemberPriceList[0].discountType === DiscountTypes.discount"
                                >
                                    <abc-flex
                                        align="center"
                                        justify="flex-start"
                                        style="height: 100%;"
                                    >
                                        <abc-input
                                            ref="memberPriceInputRef"
                                            :key="currentMemberPriceList[0].discountType"
                                            v-model="discountValueView"
                                            v-abc-focus-selected
                                            type="number"
                                            :width="132"
                                            :title="discountValueView"
                                            :config="{
                                                formatLength: 2,
                                                max: 9.99
                                            }"
                                            :input-custom-style="{
                                                textAlign: 'left',
                                                color: (disabledMemberPrice || disabledPackagePrice) ? 'var(--abc-color-T3)' : ''
                                            }"
                                            style="flex: none;"
                                            :disabled="disabledMemberPrice || disabledPackagePrice"
                                            class="member-price-discount-input"
                                            @blur="handleDiscountBlur"
                                        >
                                            <abc-flex
                                                v-if="appendInnerText()"
                                                slot="appendInner"
                                                inline
                                                align="center"
                                                justify="space-between"
                                                :gap="4"
                                                style="width: 80px;"
                                                class="ellipsis"
                                            >
                                                <span style="color: var(--abc-color-T3);">折</span>
                                                <abc-text
                                                    v-abc-title="appendInnerText()"
                                                    size="mini"
                                                    theme="gray-light"
                                                    class="ellipsis"
                                                    style="font-size: 12px;"
                                                >
                                                </abc-text>
                                            </abc-flex>
                                        </abc-input>
                                    </abc-flex>
                                </template>
                                <template
                                    v-else
                                >
                                    <template v-if="currentMemberPriceList[0].discountGoodsDiscountType === DiscountGoodsDiscountType.special">
                                        <abc-input
                                            v-model="currentMemberPriceList[0].packagePrice"
                                            v-abc-focus-selected
                                            type="money"
                                            :width="132"
                                            :placeholder="memberPricePlaceholder"
                                            :config="getPriceConfig"
                                            :disabled="true"
                                            :input-custom-style="{
                                                'text-align': 'left',
                                            }"
                                            :title="currentMemberPriceList[0].packagePrice"
                                        >
                                            <abc-text
                                                v-if="memberPriceDiscountText"
                                                slot="appendInner"
                                                size="mini"
                                                theme="gray-light"
                                                style="font-size: 12px;"
                                            >
                                                {{ memberPriceDiscountText }}
                                            </abc-text>
                                        </abc-input>
                                    </template>

                                    <abc-flex
                                        v-else
                                        align="center"
                                        justify="flex-start"
                                        style="height: 100%;"
                                    >
                                        <abc-input
                                            v-model="discountValueView"
                                            v-abc-focus-selected
                                            type="number"
                                            :width="132"
                                            :title="discountValueView"
                                            :config="{
                                                formatLength: 2,
                                                max: 9.99
                                            }"
                                            :input-custom-style="{
                                                textAlign: 'left',
                                                color: 'var(--abc-color-T3)'
                                            }"
                                            :placeholder="memberPricePlaceholder"
                                            style="flex: none;"
                                            :disabled="true"
                                            class="member-price-discount-input"
                                        >
                                            <abc-flex
                                                v-if="appendInnerText()"
                                                slot="appendInner"
                                                inline
                                                align="center"
                                                justify="space-between"
                                                :gap="4"
                                                style="width: 80px;"
                                                class="ellipsis"
                                            >
                                                <span style="color: var(--abc-color-T3);">折</span>
                                                <abc-text
                                                    v-abc-title="appendInnerText()"
                                                    size="mini"
                                                    theme="gray-light"
                                                    class="ellipsis"
                                                    style="font-size: 12px;"
                                                >
                                                </abc-text>
                                            </abc-flex>
                                        </abc-input>
                                    </abc-flex>
                                </template>

                                <abc-select
                                    v-model="currentMemberPriceList[0].discountType"
                                    width="74px"
                                    inner-width="96px"
                                    placement="bottom-end"
                                    :disabled="disabledMemberPrice || disabledPackagePrice"
                                    @change="handleChangeDiscountType"
                                >
                                    <abc-option :value="DiscountTypes.special" label="特价">
                                    </abc-option>
                                    <abc-option :value="DiscountTypes.discount" label="折扣">
                                    </abc-option>
                                    <abc-option :value="DiscountTypes.no" :label="getDiscountTypeShortLabel(currentMemberPriceList[0])">
                                    </abc-option>
                                </abc-select>
                            </abc-space>
                            <abc-input
                                v-else
                                v-model="memberPriceRangeText"
                                :width="206"
                                :max-length="100"
                                type="text"
                                readonly
                                :disabled="disabledMemberPrice || disabledPackagePrice"
                                data-cy="inventory-archives-price-info-form-member-prices-input"
                                @click="showMemberPriceRangeDialog"
                            >
                                <abc-tooltip
                                    v-if="memberPriceToolTip"
                                    slot="appendInner"
                                    placement="top"
                                    :content="memberPriceToolTip"
                                >
                                    <abc-icon
                                        icon="Attention"
                                        size="14"
                                        color="var(--abc-color-Y2)"
                                    ></abc-icon>
                                </abc-tooltip>
                            </abc-input>
                        </abc-form-item>
                    </div>
                    <div v-else-if="item.prop === 'currentInOutTaxRat'" class="goods-taxrat">
                        <div class="goods-taxrat-label">
                            <abc-checkbox
                                v-model="currentDefaultInOutTax"
                                :disabled="disabled || !canUpdateGoodsInfo"
                                class="goods-taxrat-checkbox"
                                data-cy="abc-checkbox-purchase-and-sales-tax-rate"
                            >
                                <span style="color: var(--abc-color-T2);">默认进丨销项税率</span>
                            </abc-checkbox>
                            <abc-tooltip-info placement="bottom-start">
                                <div v-if="isAdmin">
                                    请在「管理-定价和税率」设置
                                </div>
                                <div v-else>
                                    请在「总部-管理-定价和税率」设置
                                </div>
                            </abc-tooltip-info>
                            <span v-if="isShowHistoryTaxrat" class="view-history-taxrat" @click="showHistoryTaxratDialog = true;">
                                历史
                            </span>
                        </div>

                        <abc-form-item
                            :help="item.help"
                            :help-theme="item.helpTheme"
                        >
                            <abc-space is-compact>
                                <abc-input
                                    v-model="currentInTaxRat"
                                    :width="104"
                                    type="money"
                                    :config="{ max: 100 }"
                                    :disabled="disabledInOutTax"
                                    data-cy="inventory-archives-price-info-form-in-tax-rat-input"
                                    @enter="enterEvent"
                                >
                                    <label slot="appendInner" :style="`color: ${$store.state.theme.style.T3}`"><abc-icon icon="n-percentage-line"></abc-icon></label>
                                </abc-input>
                                <abc-input
                                    v-model="currentOutTaxRat"
                                    :width="103"
                                    type="money"
                                    :config="{ max: 100 }"
                                    :disabled="disabledInOutTax"
                                    data-cy="inventory-archives-price-info-form-out-tax-rat-input"
                                    @enter="enterEvent"
                                >
                                    <label slot="appendInner" :style="`color: ${$store.state.theme.style.T3}`"><abc-icon icon="n-percentage-line"></abc-icon></label>
                                </abc-input>
                            </abc-space>
                        </abc-form-item>
                    </div>

                    <abc-form-item
                        v-else-if="item.prop === 'currentProfitClassification'"
                        :label="item.label"
                        :help="item.help"
                        :help-theme="item.helpTheme"
                    >
                        <abc-select
                            ref="profitTypeSelect"
                            v-model="currentProfitClassification"
                            :width="206"
                            :disabled="disabled || !canUpdateGoodsInfo"
                            :max-height="210"
                            setting
                            show-empty
                            clearable
                            data-cy="inventory-archives-price-info-form-profit-classification-select"
                            @set="openProfitTypeDialog"
                        >
                            <abc-option
                                v-for="(option, index) in renderProfitClassificationList"
                                :key="`${option.id}-${option.name}`"
                                :value="option.id"
                                :label="option.name"
                            >
                                <abc-space class="ellipsis">
                                    <span :title="option.name">{{ option.name }}</span>
                                    <span v-if="index === renderProfitClassificationList.length - 1" style="color: var(--abc-color-T2);">
                                        低于{{ option.profitMax }}%
                                    </span>
                                    <span
                                        v-else
                                        v-abc-title="`${isNull(option.profitMin) ? '' : option.profitMin}% (含) ~ ${isNull(option.profitMax) ? '' : option.profitMax}%`"
                                        style="color: var(--abc-color-T2);"
                                    ></span>
                                </abc-space>
                            </abc-option>
                        </abc-select>
                    </abc-form-item>

                    <abc-form-item v-else-if="item.prop === 'currentFeeTypeId'" :required="item.required" :label="item.label">
                        <abc-select
                            v-model="currentFeeTypeId"
                            :width="206"
                            placeholder="请选择"
                            :disabled="disabled"
                            :max-height="210"
                            data-cy="inventory-archives-price-info-form-fee-type-select"
                        >
                            <abc-option
                                v-for="option in feeTypesList"
                                :key="option.feeTypeId"
                                :value="option.feeTypeId"
                                :label="option.name"
                            ></abc-option>
                        </abc-select>
                    </abc-form-item>

                    <abc-form-item v-else-if="item.prop === 'currentFeeCategoryId'" :label="item.label">
                        <abc-cascader
                            v-model="currentFeeCategoryId"
                            :options="feeCategoryList"
                            :width="206"
                            :disabled="disabled"
                            :panel-max-height="210"
                            :props="{
                                children: 'children',
                                label: 'name',
                                value: 'feeCategoryId'
                            }"
                            data-cy="inventory-archives-price-info-form-fee-category-select"
                        >
                        </abc-cascader>
                    </abc-form-item>
                </abc-col>
            </template>
        </abc-row>
        <abc-tips
            v-if="adjustPriceTips"
            theme="warning"
            size="small"
            style="margin-top: 16px;"
        >
            {{ adjustPriceTips }}
        </abc-tips>
        <history-taxrate-dialog
            v-if="showHistoryTaxratDialog"
            :dialog-visible.sync="showHistoryTaxratDialog"
            :type-id="currentTypeId"
            :goods-id="currentGoodsId"
        ></history-taxrate-dialog>

        <profit-type-setting
            v-if="showProfitTypeDialog"
            v-model="showProfitTypeDialog"
            :options="profitClassificationList"
            @success="handleSuccess"
        >
        </profit-type-setting>
    </div>
</template>


<script>
    import { mapGetters } from 'vuex';
    import { ref } from 'vue';
    import {
        PriceType, RoundingMode, SubPriceFlag,
    } from 'views/common/inventory/constants';
    import EnterEvent from 'views/common/enter-event';
    import { findFeeCategoryById } from '@/views-hospital/settings-common/frames/fee/fee-item/fee-category';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import HistoryTaxrateDialog from 'views/settings/price-taxrat/components/history-taxrate-dialog.vue';
    const ViewCostPriceDialog = () => import('views/inventory/common/view-cost-price-dialog.vue');
    import {
        isChineseMedicine,
    } from '@/filters';
    import {
        getSafeNumber,
        isNotNull, isNull, isSupportShebaoPay, moneyDigit, paddingMoney,
    } from '@/utils';
    import {
        adjustPriceInfoKey,
        canAdjustPriceKey, canUpdateGoodsInfoKey,
        disabledBaseInfoKey,
        isShowHistoryTaxratKey, modifyGoodsItemKey,
    } from 'views/inventory/goods/archives/provideKeys';
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import ProfitTypeSetting from 'views/inventory/goods/archives/components/profitTypeSetting.vue';
    import { formatDate } from '@abc/utils-date';
    import { PriceAdjustmentItemStatus } from '@/views-pharmacy/inventory/constant';
    import { isEmpty } from '@/views-pharmacy/common/tools';
    import {
        calcPriceRangeByMakeupPercent, getMemberPriceRangeText,
    } from 'views/inventory/goods/archives/utils';
    import useIsOpenSocialChainAdmin from 'views/inventory/hooks/useIsOpenSocialChainAdmin';
    import {
        getMfeBasePathKey, navigate2ModuleKey,
    } from 'utils/provide-keys';
    import { MODULE_ID_MAP } from 'utils/constants';
    import { ShebaoPayMode } from 'views/inventory/goods/archives/components/social-code-autocomplete/constant';
    import BusinessGoods from '@/views-pharmacy/inventory/core/goods';
    import MemberPriceRangeDialog from 'views/inventory/goods/archives/components/member-price-range-dialog';
    import {
        DiscountGoodsDiscountType,
        DiscountGoodsType,
        DiscountTypes, OpType, TargetType,
    } from 'views/inventory/goods/archives/constant';
    import Big from 'big.js';
    import useMemberPrice from 'views/inventory/hooks/useMemberPrice';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import { formatMoney } from '@abc/utils';
    // import { GoodsTypeEnum } from '@abc/constants';

    export default {
        name: 'PriceInfo',
        components: {
            ProfitTypeSetting,
            HistoryTaxrateDialog,
            AbcCurrencySymbolIcon,
            ViewCostPriceDialog,
        },
        mixins: [EnterEvent],
        inject: {
            disabledBaseInfo: {
                from: disabledBaseInfoKey,
                default: false,
            },
            isShowHistoryTaxrat: {
                from: isShowHistoryTaxratKey,
                default: false,
            },
            adjustPriceInfo: {
                from: adjustPriceInfoKey,
                default: () => ({ data: null }),
            },
            canAdjustPrice: {
                from: canAdjustPriceKey,
                default: true,
            },
            canUpdateGoodsInfo: {
                from: canUpdateGoodsInfoKey,
                default: true,
            },
            modifyGoodsItem: {
                from: modifyGoodsItemKey,
                default: null,
            },
            getMfeBasePath: {
                from: getMfeBasePathKey,
                default: null,
            },
            navigate2Module: {
                from: navigate2ModuleKey,
                default: null,
            },
        },
        props: {
            goodsInfo: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            maxCostInfo: Object,
            chainMaxPiecePrice: [String, Number],
            chainMinPiecePrice: [String, Number],
            chainMaxPackagePrice: [String, Number],
            chainMinPackagePrice: [String, Number],
            // 费用类型
            feeTypesList: {
                type: Array,
                default: () => [],
            },
            // 病案首页费目
            feeCategoryList: {
                type: Array,
                default: () => [],
            },
            // 费用类型和费目关联关系
            feeTypeRelateFeeCategory: {
                type: Array,
                default: () => [],
            },
            // 利润分类
            profitClassificationList: {
                type: Array,
                default: () => [],
            },
            selectedClinicId: String,

            isChineseMedicineType: Boolean,
            isChineseWesternPatentType: Boolean,
            shebaoPriceInfo: Object,
        },
        setup() {
            const { isHaveClinicOpen } = useIsOpenSocialChainAdmin();

            const {
                getDiscountValue,
                getDiscountValueView,
                getDiscountTypeShortLabel,
            } = useMemberPrice();

            // 使用 ref 来管理临时折扣值
            const tempDiscountValue = ref(null);

            return {
                isHaveClinicOpen,
                tempDiscountValue,
                getDiscountValue,
                getDiscountValueView,
                getDiscountTypeShortLabel,
            };
        },
        data() {
            return {
                DiscountTypes,
                DiscountGoodsType,
                DiscountGoodsDiscountType,
                showHistoryTaxratDialog: false,
                showProfitTypeDialog: false,
                options: {
                    sell: [
                        {
                            label: '允许', value: true,
                        },
                        {
                            label: '不允许', value: false,
                        },
                    ],
                    priceMode: [
                        {
                            label: '固定售价', value: PriceType.PRICE,
                        },
                        {
                            label: '进价加成', value: PriceType.PKG_PRICE_MAKEUP,
                        },
                    ],
                },
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'showSubSetPrice',
                'clinicConfig',
                'currentClinic',
                'currentPharmacy',
                'isChain',
                'isChainAdmin',
                'isChainSubStore',
                'isSingleStore',
                'isAdmin',
                'goodsConfig',
                'priceAdjustmentTabType',
                'priceAdjustmentRatio',
                'goodsConfigPriceAdjustmentNoPowerClinicsId',
                'isCanSeeGoodsCostInInventory',
                'isCanOperateGoodsAdjustPriceInInventory',
                'isOpenCostPriceAddition',
                'isEnableListingPrice',
                'isEnableLimitListingPriceSwitch',
                'isNeedCheckSellPriceNotZero',
            ]),
            ...mapGetters('viewDistribute', [
                'featureFeeCompose',
                'featureSupportFeeCategory',
                'viewDistributeConfig',
            ]),
            isOpenSocial() {
                return this.isHaveClinicOpen || this.$abcSocialSecurity.isOpenSocial;
            },
            // 系统按产品线配置-是否支持会员价
            isSupportGoodsMemberPrice() {
                return this.viewDistributeConfig.Inventory.isSupportGoodsMemberPrice;
            },
            // 系统按产品线配置-是否支持挂网价
            isSupportShebaoListingPriceConfig() {
                return this.viewDistributeConfig.Inventory.isSupportShebaoListingPrice;
            },
            // 医保后端按地区配置-是否支持挂网价限价提示
            isSupportShebaoListingPrice() {
                return this.isEnableListingPrice;
            },
            // 支持挂网价提示时，再读取挂网价限价开关
            isSupportShebaoLimitListingPrice() {
                return this.isEnableLimitListingPriceSwitch;
            },
            isShowPackagePriceLimitTips() {
                return this.isSupportShebaoListingPriceConfig && this.validateShebaoPrice?.showPackagePrice && !this.noMedicalInsurance;
            },
            isShowPiecePriceLimitTips() {
                return this.isSupportShebaoListingPriceConfig && this.validateShebaoPrice?.showPiecePrice && !this.noMedicalInsurance;
            },
            isSupportCostPriceMakeUp() {
                return this.viewDistributeConfig.Inventory.isSupportCostPriceMakeUp;
            },
            fractionDigits() {
                return this.viewDistributeConfig.Inventory.fractionDigits;
            },
            disabledInOutTax() {
                return this.disabled || this.currentDefaultInOutTax || !this.canUpdateGoodsInfo;
            },
            showProfitClassification() {
                return this.viewDistributeConfig.Inventory.showProfitClassification;
            },
            useIndividualPricingModel() {
                return this.viewDistributeConfig.Inventory.useIndividualPricingModel;
            },
            operateGoodsAdjustPrice() {
                return this.viewDistributeConfig.Inventory.operateGoodsAdjustPrice;
            },
            getPriceConfig() {
                return new BusinessGoods(this.goodsInfo).getPriceInputConfig(this.fractionDigits);
            },
            currentTypeId() {
                return +this.goodsInfo.typeId;
            },
            currentGoodsId() {
                return this.goodsInfo.goodsId || this.goodsInfo.id;
            },
            currentType() {
                return +this.goodsInfo.type;
            },
            currentSubType() {
                return +this.goodsInfo.subType;
            },
            currentSubPriceFlag() {
                return this.goodsInfo.subClinicPriceFlag;
            },
            disableStatus() {
                return !!this.goodsInfo.v2DisableStatus;
            },
            // 结算方式选择不过医保
            noMedicalInsurance() {
                return this.goodsInfo.shebaoPayMode === ShebaoPayMode.NO_USE;
            },
            moduleIds() {
                return (this.userInfo && this.userInfo.moduleIds) || '';
            },
            moduleArr() {
                if (!this.moduleIds) {
                    return [];
                }
                return this.moduleIds.split(',');
            },
            isGlobalModule() {
                if (!this.moduleIds) {
                    return false;
                }
                return this.moduleIds === '0';
            },
            /**
             * 有医保权限
             */
            hasSocialModule() {
                return this.isGlobalModule || this.includesModuleId(MODULE_ID_MAP.social);
            },
            renderProfitClassificationList() {
                return this.profitClassificationList.slice().sort((a,b) => b.profitMax - a.profitMax);
            },
            currentPriceType: {
                get() {
                    return this.goodsInfo.priceType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'priceType', v);
                },
            },
            currentPriceMakeupPercent: {
                get() {
                    return this.goodsInfo.priceMakeupPercent;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'priceMakeupPercent', v);
                },
            },
            currentChainPackagePrice: {
                get() {
                    return this.goodsInfo.chainPackagePrice || '-';
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'chainPackagePrice', v);
                },
            },
            currentChainPiecePrice: {
                get() {
                    return this.goodsInfo.chainPiecePrice || '-';
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'chainPiecePrice', v);
                },
            },
            // 会员价列表
            currentMemberPriceList: {
                get() {
                    return this.goodsInfo.multiPriceList?.filter((priceInfo) => priceInfo.targetType === TargetType.vip) || [];
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'multiPriceList', v);
                },
            },
            discountValueView: {
                get() {
                    const row = this.currentMemberPriceList[0];

                    return this.getDiscountValueView(row?.discountValue);
                },
                set(v) {
                    // 使用 ref 存储临时值
                    this.tempDiscountValue = v;
                },
            },
            isOnlyOneMemberPrice() {
                return this.currentMemberPriceList.length === 1;
            },
            memberPriceRangeText() {
                const fractionDigits = this.getPriceConfig.formatLength;

                return getMemberPriceRangeText(this.currentMemberPriceList, fractionDigits);
            },
            memberPricePlaceholder() {
                const {
                    discountType,
                    discountGoodsType,
                    // discountGoodsDiscountType,
                } = this.currentMemberPriceList[0];

                // if (discountType === DiscountTypes.special) {
                //     return '填写特价';
                // }
                // if (discountType === DiscountTypes.discount) {
                //     return '填写折扣';
                // }
                if (discountType === DiscountTypes.no) {
                    if (discountGoodsType === DiscountGoodsType.goods) {
                        return '';
                        // return discountGoodsDiscountType === DiscountGoodsDiscountType.special ? '暂未设置商品组特价' : '暂未设置商品组折扣';
                    }
                    return '暂未设置分类折扣';
                }
                return '';
            },
            currentPiecePrice: {
                get() {
                    if (this.currentDismounting) {
                        if (this.isSelectCostPriceAddition) {
                            return this.piecePriceRangeText;
                        }
                        return this.goodsInfo.piecePrice;
                    }
                    return '';
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'piecePrice', v);
                    this.refreshMemberPriceList();
                },
            },
            currentPackagePrice: {
                get() {
                    if (this.isSelectCostPriceAddition) {
                        return this.packagePriceRangeText;
                    }
                    // TODO: 未对外销售时是否不显示价格待确定
                    // if (this.notExternalSale) return '';
                    return this.goodsInfo.packagePrice;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'packagePrice', v);
                    this.refreshMemberPriceList();
                },
            },
            currentDefaultInOutTax: {
                get() {
                    return !!this.goodsInfo.defaultInOutTax;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'defaultInOutTax', Number(v));
                },
            },
            currentInTaxRat: {
                get() {
                    return this.goodsInfo.inTaxRat;
                },
                set(v) {
                    const inTaxRat = Number(v);
                    this.$emit('updateGoodsInfo', 'inTaxRat', inTaxRat);
                },
            },
            currentOutTaxRat: {
                get() {
                    return this.goodsInfo.outTaxRat;
                },
                set(v) {
                    const outTaxRat = Number(v);
                    this.$emit('updateGoodsInfo', 'outTaxRat', outTaxRat);
                },
            },
            currentIsSell: {
                get() {
                    return !!this.goodsInfo.isSell;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'isSell', Number(v));
                },
            },
            currentDismounting: {
                get() {
                    return !!this.goodsInfo.dismounting;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'dismounting', Number(v));
                    // if (this.isAdmin) {
                    //     this.calPiecePrice();
                    // }
                },
            },
            currentPieceNum: {
                get() {
                    return +this.goodsInfo.pieceNum || 1;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'pieceNum', v);
                },
            },
            currentPackageUnit: {
                get() {
                    return this.goodsInfo.packageUnit;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'packageUnit', v);
                },
            },
            currentPieceUnit: {
                get() {
                    return this.goodsInfo.pieceUnit;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'pieceUnit', v);
                },
            },
            // 利润分类
            currentProfitClassification: {
                get() {
                    return this.goodsInfo.profitCategoryType;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'profitCategoryType', v);
                },
            },
            // 费用类型
            currentFeeTypeId: {
                get() {
                    return this.goodsInfo.feeTypeId;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'feeTypeId', v);
                },
            },
            // 病案首页费目
            currentFeeCategoryId: {
                get() {
                    return findFeeCategoryById(this.goodsInfo.feeCategoryId, this.feeCategoryList);
                },
                set(v) {
                    const feeCategory = v.slice(-1)[0] || {};
                    this.$emit('updateGoodsInfo', 'feeCategoryId', feeCategory.value);
                },
            },
            // 定价方式
            currentSubClinicPriceFlag() {
                return this.goodsInfo.subClinicPriceFlag;
            },
            // 是否开启进价加成模式
            isOpenCostPriceMakeUp() {
                return this.isSupportCostPriceMakeUp && this.isOpenCostPriceAddition;
            },
            // 定价模式是否选择进价加成
            isSelectCostPriceAddition() {
                return this.currentPriceType === PriceType.PKG_PRICE_MAKEUP;
            },
            // 是否可以对外销售
            isSell() {
                return this.currentType === GoodsTypeEnum.GOODS ||
                    this.currentTypeId === GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL ||
                    this.currentTypeId === GoodsTypeIdEnum.MATERIAL_DISINFECTANT;
            },
            // 子店定价方式为连锁统一定价，并且当前药品是进价加成药品
            isUnifyPriceGoods() {
                return this.isChainSubStore && this.isSupportCostPriceMakeUp && (
                    this.currentSubClinicPriceFlag === SubPriceFlag.UNIFY_FIXED_PRICE ||
                    this.currentSubClinicPriceFlag === SubPriceFlag.UNIFY_FIXED_PRICE_AND_ADMIN_CLINIC
                );
            },
            renderData() {

                return [
                    {
                        prop: 'currentSell',
                        label: '对外销售',
                        order: 10,
                        hidden: !this.isSell,
                    },
                    {
                        prop: 'currentPriceType',
                        label: '定价模式',
                        order: 10,
                        hidden: () => {
                            // 门店配置打开了进价加成
                            if (this.isOpenCostPriceMakeUp) return false;
                            // 定价方式为连锁统一定价，并且当前药品是进价加成药品
                            if (this.isUnifyPriceGoods) return false;
                            return true;
                        },
                        disabled: false,
                    },
                    {
                        prop: 'currentPriceMakeupPercent',
                        label: '加成率',
                        order: 10,
                        required: true,
                        // 门店开关和每个药品强关联
                        hidden: () => {
                            if (this.isOpenCostPriceMakeUp && this.isSelectCostPriceAddition) return false;
                            // 定价方式为连锁统一定价，并且当前药品是进价加成药品
                            if (this.isUnifyPriceGoods && this.isSelectCostPriceAddition) return false;

                            return true;
                        },
                    },
                    {
                        prop: 'currentPackagePrice',
                        label: '零售价格',
                        order: 10,
                        required: (this.isSelectCostPriceAddition || this.operateGoodsAdjustPrice) ? false : (this.isSell ? this.currentIsSell : true),
                        hidden: () => {
                            // 中药隐藏大包装价格
                            if (this.isChineseMedicineType) return true;

                            return false;
                        },
                    },
                    {
                        prop: 'currentPiecePrice',
                        label: this.isChineseMedicineType ? '零售价格' : '',
                        order: 10,
                        required: (this.isSelectCostPriceAddition || this.operateGoodsAdjustPrice) ? false : (this.isChineseMedicineType ? true : this.currentDismounting),
                        hidden: () => {
                            // if (this.isChainAdmin) {
                            //     return this.isSelectCostPriceAddition;
                            // }
                            return false;
                        },
                    },
                    {
                        prop: 'currentMemberPriceList',
                        label: '会员价',
                        order: 10,
                        hidden: () => {
                            return !this.isSupportGoodsMemberPrice;
                        },
                    },
                    {
                        prop: 'currentInOutTaxRat',
                        label: '税率',
                        order: 10,
                        hidden: () => {
                            if (this.isAdmin) return false;
                            if (this.isUnifyPriceGoods) return false;
                            return true;
                        },
                        help: (() => {
                            const inTaxRat = this.getHelpText('inTaxRat',(e) => e);
                            const outTaxRat = this.getHelpText('outTaxRat',(e) => e);
                            if (inTaxRat || outTaxRat) {
                                return `审批中：${inTaxRat ? `进项税${inTaxRat}%` : ''} ${outTaxRat ? `销项税${outTaxRat}%` : ''}`;
                            }
                            return '';
                        })(),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentProfitClassification',
                        label: '利润分类',
                        order: 10,
                        hidden: !this.showProfitClassification,
                        help: this.getHelpText('profitCategoryType', () => {
                            // 后端返回的profitCategoryTypeName
                            const profitCategoryTypeName = this.modifyGoodsItem?.profitCategoryTypeName;
                            return profitCategoryTypeName ? `审批中：${profitCategoryTypeName}` : '审批中：删除“利润分类”';
                        }),
                        helpTheme: 'warning',
                    },
                    {
                        prop: 'currentFeeTypeId',
                        label: '费用类型',
                        order: 10,
                        required: true,
                        hidden: !this.featureFeeCompose,
                    },
                    {
                        prop: 'currentFeeCategoryId',
                        label: '病案首页费目',
                        order: 10,
                        // hidden: false,
                        hidden: !this.featureSupportFeeCategory,
                    },
                ].filter((item) => {
                    if (typeof item.hidden === 'function') {
                        return !item.hidden();
                    }
                    return !item.hidden;
                }).sort((a, b) => a.order - b.order);
            },

            // 医保限价与挂网价比较
            validateShebaoPrice() {
                if (this.shebaoPriceInfo && this.isOpenSocial) {
                    const {
                        packageLimitPrice,
                        pieceLimitPrice,
                        packageListingPrice,
                        pieceListingPrice,
                    } = this.shebaoPriceInfo;
                    const {
                        packageUnit, pieceUnit,
                    } = this.goodsInfo;

                    const currentPackagePrice = this.isSelectCostPriceAddition ? this.priceRange.maxPackagePrice : this.goodsInfo.packagePrice;
                    const currentPiecePrice = this.isSelectCostPriceAddition ? this.priceRange.maxPiecePrice : this.goodsInfo.piecePrice;

                    const isOverPackageLimitPrice = packageLimitPrice && currentPackagePrice > packageLimitPrice;
                    const isOverPieceLimitPrice = pieceLimitPrice && currentPiecePrice > pieceLimitPrice;
                    const isOverPackageListingPrice = packageListingPrice && currentPackagePrice > packageListingPrice;
                    const isOverPieceListingPrice = pieceListingPrice && currentPiecePrice > pieceListingPrice;


                    return {
                        showPackagePrice: isOverPackageLimitPrice || isOverPackageListingPrice,
                        showPiecePrice: this.currentDismounting && (isOverPieceLimitPrice || isOverPieceListingPrice),

                        isOverPackageLimitPrice,
                        isOverPieceLimitPrice,

                        isOverPackageListingPrice,
                        isOverPieceListingPrice,

                        packageLimitPrice: `${this.$t('currencySymbol')}${packageLimitPrice}/${packageUnit}`,
                        pieceLimitPrice: `${this.$t('currencySymbol')}${pieceLimitPrice}/${pieceUnit}`,

                        packageListingPrice: `${this.$t('currencySymbol')}${packageListingPrice}/${packageUnit}`,
                        pieceListingPrice: `${this.$t('currencySymbol')}${pieceListingPrice}/${pieceUnit}`,
                    };
                }

                return {};
            },
            // 销售价低于进价
            validatePriceText() {
                // 优先挂网价与限价提示，避免冲突
                if (this.validateShebaoPrice?.showPackagePrice) return '';

                if (this.isSupportGoodsMemberPrice) {
                    const prices = this.currentMemberPriceList
                        .filter((item) => item.packagePrice)
                        .map((item) => Number(item.packagePrice));

                    if (!prices.length) return '';

                    const maxPrice = Math.max(...prices);
                    if (maxPrice > this.currentPackagePrice) {
                        return '原价金额低于会员价';
                    }
                }

                return this.validatePrice({
                    packagePrice: this.currentPackagePrice,
                    piecePrice: this.currentPiecePrice,
                    priceType: this.currentPriceType,
                    type: this.goodsInfo.type,
                    subType: this.goodsInfo.subType,
                });
            },
            tipsText() {
                if (this.subClinicPriceFlag) {
                    if (this.useIndividualPricingModel) return '';
                    return this.subClinicPriceFlag > 1 ? '该药品已修改过零售价格,将不随总部定价而改变' : '该药品未修改过零售价格,将随总部定价而改变';
                }
                return '';
            },
            memberPriceToolTip() {
                if (this.disabledMemberPrice) {
                    if (this.memberPriceRangeText) {
                        return '会员价基于零售价生效，请先填写零售价';
                    }
                    return '请先设置零售价';

                }
                return '';
            },
            memberPriceDiscountText() {
                if (this.currentMemberPriceList[0].packagePrice && !this.disabledMemberPrice && this.retailPrice > 0) {
                    const { packagePrice } = this.currentMemberPriceList[0];
                    // 会员价大于原价
                    if (Big(getSafeNumber(packagePrice)).gte(this.retailPrice)) {
                        return '';
                    }
                    return `约${this.getDiscountValueView(Big(getSafeNumber(packagePrice)).div(this.retailPrice))}折`;
                }
                return '';
            },
            memberPriceProfitRateText() {
                const { packagePrice } = this.currentMemberPriceList[0] || {};
                const { lastPackageCostPrice } = this.goodsInfo || {};

                if (!lastPackageCostPrice || !packagePrice) {
                    return '毛利率: --';
                }

                try {
                    const cost = Big(getSafeNumber(lastPackageCostPrice));
                    const price = Big(getSafeNumber(packagePrice));

                    if (cost.lte(0) || price.lte(0)) {
                        return '毛利率: --';
                    }

                    // 计算毛利率：(售价 - 成本) / 售价 * 100%
                    const profitRate = price.minus(cost).div(price).times(100).toFixed(1, Big.roundHalfUp);
                    return `毛利率: ${profitRate}%`;
                } catch (e) {
                    console.error('计算毛利率出错:', e);
                    return '毛利率: --';
                }
            },
            adjustPriceTips() {
                const info = this.adjustPriceInfo.data;
                if (!info) {
                    return '';
                }
                const {
                    effected,
                    status,
                    waitingEffectPriceList,
                } = info;
                const priceInfos = [];

                const [normalPrice] = waitingEffectPriceList?.filter((item) => item.targetType === TargetType.normal) ?? [];

                if (normalPrice) {
                    if (!isEmpty(normalPrice.afterPackagePrice)) {
                        priceInfos.push(`售价 → ${moneyDigit(normalPrice.afterPackagePrice, 4)}`);
                    }
                    if (isEmpty(normalPrice.afterPiecePrice)) {
                        priceInfos.push(`拆零售价 → ${moneyDigit(normalPrice.afterPiecePrice, 4)}`);
                    }
                }

                if (waitingEffectPriceList?.length) {
                    const memberPriceList = waitingEffectPriceList.filter((item) => item.targetType === TargetType.vip).map((item) => ({
                        ...item,
                        packagePrice: item.afterPackagePrice,
                    }));
                    const text = getMemberPriceRangeText(memberPriceList, 4, false);
                    if (text) {
                        priceInfos.push(`会员价 → ${text}`);
                    }
                }

                const priceTips = priceInfos.join('，');
                if (status === PriceAdjustmentItemStatus.DRAFT) {
                    return `已加入调价单草稿，新价格通过审核后生效：${priceTips}`;
                }
                if (status === PriceAdjustmentItemStatus.REVIEW) {
                    return `调价审核中，新价格通过审核后生效：${priceTips}`;
                }
                if (status === PriceAdjustmentItemStatus.GOODS_IN) {
                    return `调价 ${formatDate(effected, 'YYYY-MM-DD HH:mm:ss')} 生效：${priceTips}`;
                }
                return '';
            },
            // 物资-器械、商品等，不对外销售
            notExternalSale() {
                return this.isSell && !this.currentIsSell;
            },
            disabledPriceType() {
                if (this.disabledBaseInfo) return true;
                if (this.isAdmin) return false; // 总部可编辑

                return !this.isOpenCostPriceMakeUp;
            },
            disabledMakeupPercent() {
                return this.disabledPriceType || !this.showSubSetPrice;
            },
            disabledDismounting() {
                return this.disabledPackagePrice;
            },
            disabledPackagePriceMakeUp() {
                if (this.isSelectCostPriceAddition) return true;
                return this.disabledPackagePrice;
            },
            // 实际售价，大包装，中药的话是小包装价格
            retailPrice() {
                return isChineseMedicine(this.goodsInfo) ? this.currentPiecePrice : this.currentPackagePrice;
            },
            // 未定价商品-禁用会员价
            disabledMemberPrice() {
                return isNull(this.retailPrice);
            },
            disabledPackagePrice() {
                if (this.notExternalSale) return true;
                if (this.disabledBaseInfo) return true;
                // 不允许调价，禁用编辑
                if (!this.canAdjustPrice) return true;
                // 药店定价判断
                if (this.useIndividualPricingModel) {
                    // 药店定价权限
                    // 第一优先级
                    if (this.operateGoodsAdjustPrice && !this.isCanOperateGoodsAdjustPriceInInventory) {
                        return true;
                    }

                    // 详情才有正确的currentSubPriceFlag，新增的时候是默认值连锁统一定价，新版子店允许建档定价会导致无法编辑价格
                    // 第二优先级
                    if (this.goodsInfo.id) {
                        // 连锁统一定价，子店不允许编辑
                        if (this.currentSubPriceFlag === SubPriceFlag.UNIFY_FIXED_PRICE) {
                            return !this.isAdmin;
                        }
                        // 门店自主定价-总部定价，只有总部能编辑
                        if (this.currentSubPriceFlag === SubPriceFlag.SEPARATE_FIXED_PRICE_AND_ADMIN_CLINIC) {
                            return !this.isChainAdmin;
                        }
                        // 门店自主定价-子店定价，只有子店能编辑
                        if (this.currentSubPriceFlag === SubPriceFlag.SEPARATE_FIXED_PRICE_AND_SUB_CLINIC) {
                            return !this.isChainSubStore;
                        }
                        // 如果不在上面3种情况的都禁用
                        return true;
                    }
                    if (this.isChainSubStore) {
                        // 第三优先级
                        return !this.showSubSetPrice;
                    }
                }
                if (this.isAdmin) return false; // 总部可编辑

                return !this.showSubSetPrice;
            },
            // 药店定价特殊提示
            priceToolTip() {
                if (this.useIndividualPricingModel) {
                    // 第一优先级
                    // 药店定价权限
                    if (this.operateGoodsAdjustPrice && !this.isCanOperateGoodsAdjustPriceInInventory) {
                        return this.isChainSubStore ? '无定价权限，请联系总部开启权限' : '无定价权限，可在 [设置-信息安全] 中设置';
                    }
                    // 第二优先级
                    // 门店自主定价-子店定价，只有子店能编辑
                    if (this.isChainSubStore && this.goodsInfo.id && this.currentSubPriceFlag !== SubPriceFlag.SEPARATE_FIXED_PRICE_AND_SUB_CLINIC) {
                        return '门店未开启单独定价权限';
                    }
                    // 第三优先级
                    // 门店定价开关
                    if (this.isChainSubStore && !this.showSubSetPrice) {
                        return '无定价权限，请联系总部开启权限';
                    }
                }
                return '';
            },
            disabledPiecePrice() {
                if (this.disabledPackagePriceMakeUp) return true;
                // 不允许调价，禁用编辑
                if (!this.canAdjustPrice) return true;
                if (this.isChineseMedicineType) return false;
                return !this.currentDismounting;
            },
            maxPackagePrice() {
                if (this.goodsConfig.subClinicPrice.subSetPrice && this.currentPackagePrice) {
                    return Big(getSafeNumber(this.currentPackagePrice)).times(this.goodsConfig.subClinicPrice.maxPricePercent).div(100);
                }
                return '';
            },
            minPackagePrice() {
                if (this.goodsConfig.subClinicPrice.subSetPrice && this.currentPackagePrice) {
                    return Big(getSafeNumber(this.currentPackagePrice)).times(this.goodsConfig.subClinicPrice.minPricePercent).div(100);
                }
                return '';
            },
            maxPiecePrice() {
                if (
                    this.goodsConfig.subClinicPrice.subSetPrice &&
                    (this.currentDismounting || this.subType === 2) &&
                    this.currentPiecePrice
                ) {
                    return Big(getSafeNumber(this.currentPiecePrice)).times(this.goodsConfig.subClinicPrice.maxPricePercent).div(100);
                }
                return '';
            },
            minPiecePrice() {
                if (
                    this.goodsConfig.subClinicPrice.subSetPrice &&
                    (this.currentDismounting || this.subType === 2) &&
                    this.currentPiecePrice
                ) {
                    return Big(getSafeNumber(this.currentPiecePrice)).times(this.goodsConfig.subClinicPrice.minPricePercent).div(100);
                }
                return '';
            },
            packagePriceLabel() {
                let range = '';
                if (this.maxPackagePrice !== '' && this.minPackagePrice !== '') {
                    range += `${paddingMoney(this.minPackagePrice)} ~ ${paddingMoney(this.maxPackagePrice)}`;
                } else if (this.maxPackagePrice !== '' && this.minPackagePrice === '') {
                    range += `不高于${paddingMoney(this.maxPackagePrice)}`;
                } else if (this.maxPackagePrice === '' && this.minPackagePrice !== '') {
                    range += `不低于${paddingMoney(this.minPackagePrice)}`;
                }
                return `${range}`;
            },
            piecePriceLabel() {
                let range = '';
                if (this.maxPiecePrice !== '' && this.minPiecePrice !== '') {
                    range += `${paddingMoney(this.minPiecePrice)} ~ ${paddingMoney(this.maxPiecePrice)}`;
                } else if (this.maxPiecePrice !== '' && this.minPiecePrice === '') {
                    range += `不高于${paddingMoney(this.maxPiecePrice)}`;
                } else if (this.maxPiecePrice === '' && this.minPiecePrice !== '') {
                    range += `不低于${paddingMoney(this.minPiecePrice)}`;
                }
                return `${range}`;
            },
            chainPackagePriceRange() {
                const {
                    minPricePercent, maxPricePercent,
                } = this.goodsConfig.subClinicPrice;
                // 未设置定价范围
                if ((isNull(minPricePercent) || +minPricePercent === 0) && (isNull(maxPricePercent) || +maxPricePercent === 0)) return '';

                let range = '';
                if (this.chainMaxPackagePrice && this.chainMinPackagePrice) {
                    range += `${paddingMoney(this.chainMinPackagePrice)} ~ ${paddingMoney(
                        this.chainMaxPackagePrice,
                    )}`;
                } else if (this.chainMaxPackagePrice && !this.chainMinPackagePrice) {
                    range += `不高于${paddingMoney(this.chainMaxPackagePrice)}`;
                } else if (!this.chainMaxPackagePrice && this.chainMinPackagePrice) {
                    range += `不低于${paddingMoney(this.chainMinPackagePrice)}`;
                }
                return `${range}`;
            },
            chainPiecePriceRange() {
                const {
                    minPricePercent, maxPricePercent,
                } = this.goodsConfig.subClinicPrice;
                // 未设置定价范围
                if ((isNull(minPricePercent) || +minPricePercent === 0) && (isNull(maxPricePercent) || +maxPricePercent === 0)) return '';

                let range = '';
                if (this.chainMaxPiecePrice && this.chainMinPiecePrice) {
                    range += `${paddingMoney(this.chainMinPiecePrice)} ~ ${paddingMoney(this.chainMaxPiecePrice)}`;
                } else if (this.chainMaxPiecePrice && !this.chainMinPiecePrice) {
                    range += `不高于${paddingMoney(this.chainMaxPiecePrice)}`;
                } else if (!this.chainMaxPiecePrice && this.chainMinPiecePrice) {
                    range += `不低于${paddingMoney(this.chainMinPiecePrice)}`;
                }
                return `${range}`;
            },
            makeupPercentRange() {
                const priceModeMinMaxConfig = this.goodsConfig.subClinicPrice?.priceModeMinMaxConfig ?? [];
                // 有可能是返回undefined
                return priceModeMinMaxConfig.find((e) => String(e.typeId) === String(this.currentTypeId));
            },
            makeupPercentRangeText() {
                if (this.isSingleStore) return '';
                if (this.makeupPercentRange) {
                    if (isNull(this.makeupPercentRange.min) && isNull(this.makeupPercentRange.max)) {
                        return '';
                    }
                    return `总部限价 ${this.makeupPercentRange.min ?? ''}%-${this.makeupPercentRange.max ?? ''}%`;
                }
                return '';
            },
            priceRange() {
                return this.getPriceRange(this.currentPriceMakeupPercent);
            },
            packagePriceRangeText() {
                const {
                    maxPackagePrice,
                    minPackagePrice,
                } = this.priceRange;

                if (isNull(this.currentPriceMakeupPercent)) return '-';
                if (minPackagePrice === maxPackagePrice) return paddingMoney(maxPackagePrice);

                return `${paddingMoney(minPackagePrice)}-${paddingMoney(maxPackagePrice)}`;
            },
            piecePriceRangeText() {
                const {
                    maxPiecePrice,
                    minPiecePrice,
                } = this.priceRange;

                if (isNull(this.currentPriceMakeupPercent)) return '-';
                if (minPiecePrice === maxPiecePrice) return paddingMoney(maxPiecePrice);

                return `${paddingMoney(minPiecePrice)}-${paddingMoney(maxPiecePrice)}`;
            },
            // 零售定价范围
            // memberPriceRangeLabel() {
            //     if (this.isChain && this.showSubSetPrice) {
            //         if (this.isChainAdmin) {
            //             return this.isChineseMedicineType ? this.piecePriceLabel : this.packagePriceLabel;
            //         }
            //         return this.isChineseMedicineType ? this.chainPiecePriceRange : this.chainPackagePriceRange;
            //     }
            //     return '';
            // },
        },
        methods: {
            formatMoney,
            isNull,
            includesModuleId(id) {
                return this.moduleArr.includes(id);
            },
            getHelpText(key, formatFn, label = '') {
                if (!this.canUpdateGoodsInfo && this.modifyGoodsItem && key && this.modifyGoodsItem?.hasOwnProperty(key)) {
                    // 修改
                    const val = this.modifyGoodsItem[key];
                    const originVal = this.goodsInfo[key];
                    // 没变化不提示
                    if (val === originVal) return '';
                    // 变化了自定义提示
                    if (formatFn) return formatFn(val);

                    return isNotNull(val) ? `审批中：${val}` : `审批中：删除“${label}”`;
                }
                return '';
            },
            getPriceRange(val) {
                const {
                    maxPackageCostPrice,
                    minPackageCostPrice,
                    maxPackagePrice,
                    minPackagePrice,
                    pieceNum,
                } = this.goodsInfo;

                return calcPriceRangeByMakeupPercent({
                    maxPackageCostPrice,
                    minPackageCostPrice,
                    maxPackagePrice,
                    minPackagePrice,
                    pieceNum,
                    priceMakeupPercent: val,
                    fractionDigits: isChineseMedicine(this.goodsInfo) ? 4 : 2,
                });
            },
            validateMakeupPercent(value, callback) {
                const max = this.makeupPercentRange?.max ?? 9999999;
                const min = this.makeupPercentRange?.min ?? 0;
                if (value > max) {
                    callback({
                        validate: false,
                        message: `不能高于最高限价${max}%`,
                    });
                } else if (value < min) {
                    callback({
                        validate: false,
                        message: `不能低于最低限价${min}%`,
                    });
                }
                callback({
                    validate: true,
                });
            },
            // !下面的代码都是迁移的老代码——————————————————————————————————————————————————————————————
            validatePrice(item) {
                if (!this.maxCostInfo || !this.maxCostInfo.clinicName || this.notExternalSale) return '';
                const {
                    packagePrice, piecePrice, priceType, type, subType,
                } = item || {};
                // 进价加成的药品一定不会比进价低，就不需要出提示
                if (priceType === PriceType.PKG_PRICE_MAKEUP) return '';

                // 中药
                if (type === 1 && subType === 2) {
                    if (+this.maxCostInfo.packageCostPrice && +this.maxCostInfo.packageCostPrice > +piecePrice) {
                        if (this.maxCostInfo.clinicName && this.isChainAdmin) {
                            return `销售价低于进价（${this.$t('currencySymbol')} ${this.maxCostInfo.packageCostPrice}，${this.maxCostInfo.clinicName}）`;
                        }
                        return `销售价低于进价（${this.$t('currencySymbol')} ${this.maxCostInfo.packageCostPrice}）`;
                    }
                } else {
                    if (+this.maxCostInfo.packageCostPrice && +this.maxCostInfo.packageCostPrice > +packagePrice) {
                        if (this.maxCostInfo.clinicName && this.isChainAdmin) {
                            return `销售价低于进价（${this.$t('currencySymbol')} ${this.maxCostInfo.packageCostPrice}，${this.maxCostInfo.clinicName}）`;
                        }
                        return `销售价低于进价（${this.$t('currencySymbol')} ${this.maxCostInfo.packageCostPrice}）`;
                    }
                }
                return '';
            },
            validatePackageUnitPrice(value, callback) {
                const val = Number(value);
                // 进价加成的药品不走这个逻辑
                if (this.isAdmin || this.isSelectCostPriceAddition) {
                    // 总部和子店都要校验售价是否可以为 0
                    if (this.isAdmin && val === 0 && isSupportShebaoPay(this.goodsInfo) && this.isNeedCheckSellPriceNotZero) {
                        callback({
                            validate: false,
                            message: '医保要求售价必填且不能为 0',
                        });
                    } else {
                        callback({
                            validate: true,
                        });
                    }
                } else {
                    const maxPrice = Number(this.chainMaxPackagePrice);
                    const minPrice = Number(this.chainMinPackagePrice);
                    if (val > 9999999) {
                        callback({
                            validate: false,
                            message: '最多7位数',
                        });
                    } else if (maxPrice && val > maxPrice) {
                        callback({
                            validate: false,
                            message: `不能高于最高售价 (${this.$t('currencySymbol')} ${maxPrice})`,
                        });
                    } else if (minPrice && val < minPrice) {
                        callback({
                            validate: false,
                            message: `不能低于最低售价 (${this.$t('currencySymbol')} ${minPrice})`,
                        });
                    } else if (val === 0 && isSupportShebaoPay(this.goodsInfo) && this.isNeedCheckSellPriceNotZero) {
                        callback({
                            validate: false,
                            message: '医保要求售价必填且不能为 0',
                        });
                    } else {
                        callback({
                            validate: true,
                        });
                    }
                }
            },
            validatePieceUnitPrice(value, callback) {
                const val = +value;
                // 总部或者不是拆零就不校验或者进价加成模式
                if (this.isAdmin || !this.currentDismounting || this.isSelectCostPriceAddition) {
                    // 总部和子店都要校验售价是否可以为 0
                    if (this.isAdmin) {
                        if (isNotNull(value) && val === 0 && isSupportShebaoPay(this.goodsInfo) && this.isNeedCheckSellPriceNotZero) {
                            callback({
                                validate: false,
                                message: '医保要求售价必填且不能为 0',
                            });
                            return;
                        }
                    }
                    callback({
                        validate: true,
                    });
                    return;
                }
                const maxPrice = +this.chainMaxPiecePrice;
                const minPrice = +this.chainMinPiecePrice;
                if (val > 9999999) {
                    callback({
                        validate: false,
                        message: '最多7位数',
                    });
                } else if (maxPrice && val > maxPrice) {
                    callback({
                        validate: false,
                        message: `不能高于最高售价 (${this.$t('currencySymbol')} ${maxPrice})`,
                    });
                } else if (minPrice && val < minPrice) {
                    callback({
                        validate: false,
                        message: `不能低于最低售价 (${this.$t('currencySymbol')} ${minPrice})`,
                    });
                } else if (val === 0 && isSupportShebaoPay(this.goodsInfo) && this.isNeedCheckSellPriceNotZero) {
                    callback({
                        validate: false,
                        message: '医保要求售价必填且不能为 0',
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
            validateMemberPrice(_, callback) {
                const {
                    discountValue,
                    discountType,
                } = this.currentMemberPriceList[0];
                console.log('validateMemberPrice',`【${discountValue}】`,`【${discountType}】`);

                if (!this.disabledPackagePrice && (isNull(discountValue) || Number(discountValue) === 0) && discountType !== DiscountTypes.no) {
                    let message = discountType === DiscountTypes.special ? '请填写特价' : '请填写折扣';
                    if (!isNull(discountValue)) {
                        message = discountType === DiscountTypes.special ? '特价不能为0' : '折扣不能为0';
                    }
                    callback({
                        validate: false,
                        message,
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
            calPiecePrice() {
                if (this.currentDismounting && this.currentPieceNum && this.currentPackagePrice) {
                    this.currentPiecePrice = Big(getSafeNumber(this.currentPackagePrice)).div(this.currentPieceNum).toFixed(this.getPriceConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
                } else {
                    this.currentPiecePrice = '';
                }
            },
            openProfitTypeDialog() {
                this.showProfitTypeDialog = true;
            },
            handleSuccess(data) {
                this.$emit('updateProfitTypes', data);
            },
            openShebaoPriceSetting() {
                const basePath = typeof this.getMfeBasePath === 'function' ? this.getMfeBasePath() : '/';
                const toPath = `${basePath}social/limit-price/index`;
                if (typeof this.navigate2Module === 'function') this.navigate2Module(toPath);
            },
            showMemberPriceRangeDialog() {
                new MemberPriceRangeDialog({
                    goodsInfo: this.goodsInfo,
                    memberPriceList: this.currentMemberPriceList,
                    priceInputConfig: this.getPriceConfig,
                    disabledMemberPrice: this.disabledMemberPrice,
                    disabledPackagePrice: this.disabledPackagePrice,
                    onConfirm: this.handleMemberPriceConfirm,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            handleMemberPriceConfirm(data) {
                console.log('handleMemberPriceConfirm',data);
                this.currentMemberPriceList = data;
            },
            // 价格变化-重新计算会员价
            refreshMemberPriceList() {
                // 如果有设置为折扣的会员价，重新计算价格
                if (this.isSupportGoodsMemberPrice && !this.disabledMemberPrice) {
                    this.currentMemberPriceList = this.currentMemberPriceList.map((item) => {
                        let {
                            packagePrice, piecePrice,
                        } = item;
                        // 计算折扣价格-特价和商品组特价不计算
                        if (item.discountType !== DiscountTypes.special && item.discountGoodsDiscountType !== DiscountGoodsDiscountType.special && this.retailPrice && item.discountValue) {
                            packagePrice = Big(getSafeNumber(this.retailPrice)).times(item.discountValue).toFixed(this.getPriceConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
                            piecePrice = Big(getSafeNumber(packagePrice)).div(this.goodsInfo.pieceNum || 1).toFixed(this.getPriceConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
                        }
                        return {
                            ...item,
                            packagePrice,
                            piecePrice,
                        };
                    });
                }
            },
            async handleChangeDiscountType() {
                const row = this.currentMemberPriceList[0];

                if (row.id) {
                    row.opType = OpType.update;
                } else {
                    row.opType = OpType.add;
                }

                if (row.discountType === DiscountTypes.no) {
                    const res = await GoodsAPIV3.fetchGoodsTypeMemberPriceDiscount({
                        goodsType: this.goodsInfo.type,
                        goodsSubType: this.goodsInfo.subType,
                        goodsCMSpec: this.goodsInfo.goodsCMSpec,
                        customTypeId: this.goodsInfo.customTypeId,
                        memberTypeId: row.memberTypeId,
                        compositeGoodsGroupIds: this.goodsInfo.compositeGoodsComposeList?.filter((item) => item?.composeType === 40)?.map((item) => item?.parentGoodsId),
                    });
                    row.discountValue = res?.data?.discountValue ?? '';
                    row.discountValueView = this.getDiscountValueView(row.discountValue);
                    // 折扣也把价格算出来，但是还是以后端为准
                    // 商品组特价不计算
                    row.packagePrice = row.discountGoodsDiscountType === DiscountGoodsDiscountType.special ? row.discountValue : this.calcDiscountPrice(row.discountValue);
                    if (row.packagePrice) {
                        row.piecePrice = Big(getSafeNumber(row.packagePrice)).div(this.goodsInfo.pieceNum || 1).toFixed(this.priceInputConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
                    }
                } else {
                    row.packagePrice = '';
                    row.piecePrice = '';
                    row.discountValue = '';
                    row.discountValueView = '';

                    // 如果是特价或折扣类型，聚焦到输入框
                    this.$nextTick(() => {
                        if (this.$refs.memberPriceInputRef?.[0]) {
                            this.$refs.memberPriceInputRef[0]?.focus();
                        }
                    });
                }
            },
            checkMemberPriceExceed(price) {
                if (!isNull(this.retailPrice) && price) {
                    return Big(getSafeNumber(price)).gt(this.retailPrice);
                }
                return false;
            },
            handleChangeMemberPrice() {
                const row = this.currentMemberPriceList[0];
                if (row.packagePrice) {
                    row.discountValue = row.packagePrice;
                } else {
                    row.discountValue = '';
                }
                if (row.id) {
                    row.opType = OpType.update;
                } else {
                    row.opType = OpType.add;
                }
            },
            calcDiscountPrice(discountValue) {
                if (isNull(this.retailPrice) || isNull(discountValue)) return '';

                return Big(getSafeNumber(this.retailPrice)).times(discountValue).toFixed(this.getPriceConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
            },
            appendInnerText() {
                const { discountValue } = this.currentMemberPriceList[0];

                if (discountValue && !isNull(this.retailPrice)) {
                    const discountPrice = this.calcDiscountPrice(discountValue);
                    return `约${formatMoney(discountPrice)}`;
                }
                return '';
            },
            handleDiscountBlur() {
                if (this.tempDiscountValue !== null) {
                    const row = this.currentMemberPriceList[0];
                    row.discountValue = this.getDiscountValue(this.tempDiscountValue);
                    // 折扣也把价格算出来，但是还是以后端为准
                    row.packagePrice = this.calcDiscountPrice(row.discountValue);
                    if (row.packagePrice) {
                        row.piecePrice = Big(getSafeNumber(row.packagePrice)).div(this.goodsInfo.pieceNum || 1).toFixed(this.getPriceConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
                    }
                    if (row.id) {
                        row.opType = OpType.update;
                    } else {
                        row.opType = OpType.add;
                    }

                    // 清空临时值
                    this.tempDiscountValue = null;
                }
            },
        },
    };
</script>
