<template>
    <div data-cy="identification-code-wrapper" class="identification-code-wrapper">
        <abc-flex align="center" justify="space-between" style="margin-bottom: 6px;">
            <abc-text theme="gray" size="normal">
                {{ item.label }}
            </abc-text>
            <abc-link @click="isShowMask = true">
                示例
            </abc-link>
        </abc-flex>
        <abc-popover
            ref="popoverRef"
            width="206px"
            placement="bottom"
            trigger="hover"
            theme="white"
            :visible-arrow="false"
            :open-delay="300"
            :popper-style="{ padding: '4px' }"
            :resident-popover="isFocus"
            :disabled="isNoCode || !codeList.length"
        >
            <abc-space slot="reference" is-compact>
                <abc-select
                    v-if="isSupportCodelessArea"
                    :value="traceCodeType"
                    :width="60"
                    :disabled="disabled"
                    data-cy="goods-archives-dialog-trace-code-type"
                    @change="changeHasTraceCode"
                >
                    <abc-option
                        v-for="it in GoodsModelOptions.hasTraceCode"
                        :key="it.value"
                        :label="it.label"
                        :value="it.value"
                    ></abc-option>
                </abc-select>

                <abc-input
                    v-if="isNoCode"
                    :value="`${identificationCode}`"
                    :width="isSupportCodelessArea ? 146 : 205"
                    :title="identificationCode"
                    :input-custom-style="{
                        'text-overflow': 'ellipsis',
                        'pointer-events': 'none'
                    }"
                    data-cy="goods-archives-dialog-identification-code"
                    :placeholder="identificationPlaceholder"
                    :disabled="true"
                >
                    <no-code-apply-popover
                        v-if="checkNeedApplySichuanNoCode(goodsInfo.shebao) && noCodeApplyStatus !== -1"
                        slot="appendInner"
                        scene="archives"
                        :status="noCodeApplyStatus"
                    ></no-code-apply-popover>
                </abc-input>

                <abc-form-item
                    v-else
                    ref="formItemRef"
                    :help="item.help"
                    :help-theme="item.helpTheme"
                    :validate-event="validateCode"
                >
                    <abc-input
                        v-model="identificationCode"
                        v-abc-focus-selected
                        type="number-en-char"
                        :width="isSupportCodelessArea ? 146 : 205"
                        :disabled="disabled"
                        data-cy="goods-archives-dialog-identification-code"
                        :class="['goods-archives-dialog-identification-code', isFocus ? 'is-custom-focus' : '']"
                        v-bind="item.config"
                        @enter="handleBindCode"
                        @input="handleInput"
                        @blur="handleInputBlur"
                        @focus="handleInputFocus"
                    >
                        <template v-if="isFocus" #appendInner>
                            <abc-button
                                v-if="identificationCode"
                                shape="square"
                                variant="fill"
                                theme="primary"
                                size="small"
                                style="margin-right: -5px; border-radius: var(--abc-border-radius-small);!important"
                                @click="handleBindCode"
                            >
                                关联
                            </abc-button>
                        </template>

                        <template v-else #appendInner>
                            <abc-text
                                v-if="codeList.length > 1"
                                theme="success-light"
                                bold
                                :style="{
                                    marginRight: countLeftPx,
                                }"
                                size="mini"
                            >
                                +{{ codeList.length - 1 }}
                            </abc-text>

                            <identification-code-risk-popover
                                v-if="isShowInnerRiskPopper(codeList, goodsInfo)"
                                ref="identificationCodeRiskPopoverRef"
                                :product-info="goodsInfo"
                                :code-list="codeList.map((x) => x.drugIdentificationCode)"
                            ></identification-code-risk-popover>
                        </template>
                    </abc-input>
                </abc-form-item>
            </abc-space>

            <abc-flex
                vertical
                style="max-height: 196px;"
            >
                <abc-flex align="center" :gap="4" style="width: 100%; padding: 4px 8px 0;">
                    <abc-text size="mini" theme="gray-light">
                        已关联
                    </abc-text>
                    <abc-text size="mini" theme="gray-light">
                        ({{ codeList.length }})
                    </abc-text>
                </abc-flex>
                <abc-list
                    :create-key="createKey"
                    :data-list="codeList"
                    :scrollable="codeList.length > 5"
                    :custom-padding="4"
                    :hover-item-func="()=>false"
                    style="flex: 1; padding-top: 0;"
                >
                    <template
                        #default="{
                            item, index
                        }"
                    >
                        <abc-flex
                            justify="space-between"
                            align="center"
                            flex="1"
                            :data-id="item.drugIdentificationCode"
                            style="width: 188px; height: 22px; padding-left: 4px;"
                        >
                            <abc-flex align="center" :gap="4">
                                <abc-text class="ellipsis" :theme="'black'" :title="item.drugIdentificationCode">
                                    {{ item.drugIdentificationCode }}
                                </abc-text>

                                <identification-code-risk-popover
                                    v-if="isShowRiskPopover(item, goodsInfo)"
                                    :product-info="goodsInfo"
                                    :code-list="codeList.map((x) => x.drugIdentificationCode)"
                                ></identification-code-risk-popover>
                            </abc-flex>

                            <abc-delete-icon
                                v-if="!disabled"
                                class="delete-icon"
                                data-cy="abc-delete-icon"
                                theme="dark"
                                @delete="handleDeleteItem(item, index)"
                            ></abc-delete-icon>
                        </abc-flex>
                    </template>
                </abc-list>
            </abc-flex>
        </abc-popover>
        <abc-popover
            v-if="allowReport"
            trigger="hover"
            theme="yellow"
            class="identification-code-wrapper-input-icon"
            placement="top-start"
            :open-delay="200"
        >
            <abc-icon
                slot="reference"
                icon="info_bold"
                :size="14"
                color="#d9d8db"
            ></abc-icon>
            <div style="max-width: 280px;">
                {{ popoverText }}<abc-link v-if="showReportBtn" style="margin-left: 42px;" @click="reportNoIdentificationCode">
                    立即上报
                </abc-link>
            </div>
        </abc-popover>

        <span ref="calcWidthRef" style="position: absolute; visibility: hidden;">{{ identificationCode }}</span>

        <abc-guide-image
            v-model="isShowMask"
            dom-selector=".identification-code-wrapper"
            v-bind="imageConfig"
        ></abc-guide-image>
    </div>
</template>

<script>
    import TraceCodeGuideImg1 from 'assets/images/img-guide-code1.png';// 药品
    import TraceCodeGuideImg2 from 'assets/images/img-guide-code2.png';// 耗材
    import AbcGuideImage from 'components/abc-guide-image/index.vue';
    import NoCodeApplyPopover from '@/service/trace-code/components/no-code-apply-popover.vue';
    import TraceCode, {
        TraceableCodeTypeEnum, TraceableNoCodeApplyStatus,
    } from '@/service/trace-code/service';
    // import {
    //     goodsFullName, goodsSpec,
    // } from '@/filters';
    import { GoodsModelOptions } from 'views/common/inventory/constants';
    import TraceCodeConfirmDialog from '@/service/trace-code/dialog-trace-code-confirm';
    import GoodsAPI from 'api/goods';
    import useSichuanNoCode from 'views/inventory/goods/archives/hook/useSichuanNoCode';
    import {
        DialogTraceCodeStandardMatch,
    } from '@/service/trace-code/dialog-trace-code-standard-match/dialog-trace-code-standard-match';
    import IdentificationCodeRiskPopover
        from '@/service/trace-code/dialog-trace-code-standard-match/identification-code-risk-popover.vue';
    export default {
        name: 'IdentificationCodeSelect',
        components: {
            AbcGuideImage,
            NoCodeApplyPopover,
            IdentificationCodeRiskPopover,
        },
        props: {
            item: {
                type: Object,
                required: true,
            },
            goodsInfo: {
                type: Object,
                required: true,
            },
            traceableCodeNoInfoList: {
                type: Array,
                default: () => [],
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            isMedicineType: {
                type: Boolean,
                default: false,
            },
            errorTraceCodeTip: {
                type: String,
                default: '',
            },
            //允许无码的时候上报
            allowReportNoCode: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                getGoodsListNoCode, checkNeedApplySichuanNoCode,
            } = useSichuanNoCode();
            return {
                getGoodsListNoCode,
                checkNeedApplySichuanNoCode,
            };
        },
        data() {
            return {
                TraceableNoCodeApplyStatus,
                identificationCode: '',// 输入框的值
                lastIdentificationCode: '',// 记录上次没保存的值
                traceCodeType: TraceableCodeTypeEnum.HAS_CODE,// 有码还是无码
                errorIdentificationCodeTip: '',// 校验错误提示-改用弹窗提示了
                isShowMask: false,
                isShowButton: false,
                isFocus: false,
                GoodsModelOptions,
                codeList: [],// 标识码列表
                isPrivateNetMode: false,
                reportItem: null,
                noCodeApplyStatus: 0, // 无码申报状态
            };
        },
        computed: {
            isReport() {
                return this.reportItem?.id;
            },
            allowReport() {
                return this.codeList.length === 0 && this.allowReportNoCode && !this.disabled && !this.isFocus;
            },
            showReportBtn() {
                return this.isPrivateNetMode && !this.isReport;
            },
            popoverText() {
                const text = '如果该药品无追溯码，可上报至西安市医保监管平台，助力无码药品库建设';
                if (this.isReport) {
                    const timeStr = this.reportItem?.uploadTime ? `（上报时间：${this.reportItem.uploadTime}）` : '';
                    return `该药品已上报至西安市医保监管平台无码药品库${timeStr}`;
                }
                if (!this.isPrivateNetMode) {
                    return `${text}（请在装有客户端的医保电脑进行操作）`;
                }
                return text;
            },
            isSupportCodelessArea() {
                return !!this.$abcSocialSecurity.defaultDrugTraceCode?.isSupportDefaultTraceCode;
            },
            identificationPlaceholder() {
                if (this.checkNeedApplySichuanNoCode(this.goodsInfo.shebao)) {
                    if (this.goodsInfo.id) {
                        if (this.noCodeApplyStatus === -1) {
                            return '保存后生效';
                        }
                        if (this.noCodeApplyStatus === TraceableNoCodeApplyStatus.SUCCESS) {
                            return '已申报无需采集';
                        }
                        if (this.noCodeApplyStatus === TraceableNoCodeApplyStatus.APPLYING) {
                            return '申报中';
                        }
                        if (this.noCodeApplyStatus === TraceableNoCodeApplyStatus.FAIL) {
                            return '申报无效';
                        }
                    }
                    return '保存后生效';
                }
                return '无需采集';
            },
            noCodeConfirmTextV2() {
                if (this.checkNeedApplySichuanNoCode(this.goodsInfo.shebao)) {
                    return <div style="width: 288px;line-height: 22px;">
                        &bull; 确定后该商品将申报至医保无码库
                        <br/>
                        &bull; 若商品实际有追溯码但销售时未采集，对应费用医保将
                        <abc-text theme="warning-light">不予支付</abc-text>
                    </div>;
                }
                if (window.$abcSocialSecurity.config.isShanxi || (window.$abcSocialSecurity.config.isFujian && !window.$abcSocialSecurity.config.isFujianXiamen) || window.$abcSocialSecurity.config.isJiangsu || window.$abcSocialSecurity.config.isGuangdong || window.$abcSocialSecurity.config.isJiangxi || window.$abcSocialSecurity.config.isChongqingGb || window.$abcSocialSecurity.config.isXinjiang) {
                    return <div style="width:324px;line-height: 22px;">
                        标记无码后，请立即在医保平台申报无码商品。<br/> 若未申报，销售商品后，医保将<abc-text bold>不予支付</abc-text>对应费用。
                    </div>;
                }
                return <div style="width:388px;line-height: 22px;">
                    如果药品包装上无追溯码，可将该药品标记为“无码商品”，发药、入库时无需扫码采集。医保监管要求应采尽采，商品有追溯码但设置成无码，可能会存在监管处罚风险。
                    <br/>
                    <br/>
                    是否确定商品无追溯码？
                </div>;
            },
            isNoCode() {
                return this.isSupportCodelessArea && this.traceCodeType === TraceableCodeTypeEnum.NO_CODE;
            },
            imageConfig() {
                if (this.isMedicineType) {
                    return {
                        image: TraceCodeGuideImg1,
                        targetPosition: {
                            x: 133,
                            y: 259,
                        },
                        imageHeight: 329,
                        imageWidth: 540,
                    };
                }
                return {
                    image: TraceCodeGuideImg2,
                    targetPosition: {
                        x: 133,
                        y: 441.5,
                    },
                    imageHeight: 509,
                    imageWidth: 540,
                };
            },
            countLeftPx() {
                // 三码合一警告
                const isShowRiskPopper = this.isShowInnerRiskPopper(this.codeList, this.goodsInfo);
                if (isShowRiskPopper) {
                    return '8px';
                }
                return '0px';
            },
        },
        watch: {
            traceableCodeNoInfoList: {
                handler(v) {
                    console.log('watch traceableCodeNoInfoList', v);
                    if (v?.length) {
                        const traceableCodeNoInfo = v?.[0];

                        this.codeList = [...v];
                        this.traceCodeType = traceableCodeNoInfo.type;
                        this.identificationCode = traceableCodeNoInfo.drugIdentificationCode ?? '';// 后端认为空字符串和null一样就没返回，这里先保护下避免展示undefined
                    } else {
                        this.codeList = [];
                        this.traceCodeType = TraceableCodeTypeEnum.HAS_CODE;
                        this.identificationCode = '';
                    }
                },
                immediate: true,
            },
            'goodsInfo.id': {
                handler(v) {
                    if (v && this.allowReportNoCode) {
                        this.fetchNoCodeCreateList(v);
                    }
                },
                deep: true,
            },
            'goodsInfo.shebao': {
                handler(v) {
                    if (v) {
                        this.getSichuanNoCode();
                    }
                },
                deep: true,
            },
        },
        async created() {
            this.isPrivateNetMode = !!window.electron;
            console.log('isPrivateNetMode', window.electron, this.isPrivateNetMode);
        },
        methods: {
            async fetchNoCodeCreateInfo(goodsId) {
                const params = { goodsId };
                const { data } = await GoodsAPI.fetchNoCodeCreateInfo(params);
                if (data) {
                    this.$Toast.success('上报成功');
                } else {
                    this.$Toast.error('上报失败');
                }
            },
            async fetchNoCodeCreateList(goodsId) {
                const params = { goodsId };
                const { data } = await GoodsAPI.fetchNoCodeCreateList(params);
                this.reportItem = data?.stockHaveNoCodeItemList?.[0] || null;
                console.log('fetchNoCodeCreateList', data);
            },
            reportNoIdentificationCode() {
                const { goodsInfo } = this;
                // 无社保对码也无条码
                if (!goodsInfo?.shebao?.nationalCode && !goodsInfo?.barCode) {
                    this.$alert({
                        type: 'warn',
                        title: '无法上报',
                        content: '请先填写商品条码或完成医保对码',
                    });
                    return;
                }
                this.$confirm({
                    type: 'warn',
                    title: '无码商品上报确认',
                    content: '确定后会将商品上报至西安市医保监管平台',
                    onConfirm: async () => {
                        await this.fetchNoCodeCreateInfo(this.goodsInfo.id);
                        await this.fetchNoCodeCreateList(this.goodsInfo.id);
                    },
                    onCancel: () => {
                        console.log('cancel');
                    },
                });
            },
            createKey(e) {
                return e.keyId || e.drugIdentificationCode;
            },
            handleDeleteItem(item, index) {
                this.codeList.splice(index, 1);
                this.$emit('update:traceableCodeNoInfoList', this.codeList);
            },
            handleInput(val) {
                this.lastIdentificationCode = val;
                this.errorIdentificationCodeTip = '';
            },
            handleInputBlur() {
                this.errorIdentificationCodeTip = '';
                this.$nextTick(() => {
                    this.$refs.formItemRef?.validate();
                });
                // 延迟是为了防止点击关联按钮时，输入框失去焦点
                this._timer = setTimeout(() => {
                    this.isFocus = false;
                    this.identificationCode = this.codeList?.[0]?.drugIdentificationCode ?? '';
                    // 等待residentPopover更新为false后关闭popover
                    this.$nextTick(() => {
                        this.$refs.popoverRef?.doClose();
                    });
                }, 300);
            },
            handleInputFocus() {
                clearTimeout(this._timer);
                this.isFocus = true;
                this.identificationCode = this.lastIdentificationCode || '';
                // 等待residentPopover更新为true后打开popover
                this.$nextTick(() => {
                    this.$refs.popoverRef?.doShow();
                });
            },
            async checkTraceCodeNoList(code, goodsInfo) {
                return new Promise((resolve) => {
                    const {
                        sanMaHeYiIdentificationCodeList, shebao, medicineCadn, barCode,
                    } = goodsInfo ?? {};
                    const { nationalCode } = shebao ?? {};
                    if (TraceCode.hasInterceptTraceCodeFormatLength && nationalCode && Array.isArray(sanMaHeYiIdentificationCodeList) && sanMaHeYiIdentificationCodeList.length && !sanMaHeYiIdentificationCodeList.includes(code)) {
                        const onConfirm = () => {
                            resolve(true);
                        };
                        const onCancel = () => {
                            resolve(false);
                        };
                        new DialogTraceCodeStandardMatch({
                            sanMaHeYiIdentificationCodeList,
                            shebao,
                            medicineCadn,
                            barCode,
                            code,
                            codeList: this.codeList.map((x) => x.drugIdentificationCode).concat([code]),
                            onConfirm,
                            onCancel,
                        }).generateDialogAsync({ parent: this });
                    } else {
                        resolve(true);
                    }
                });
            },
            /**
             * @desc 处理回车事件，校验追溯码是否重复，是否有绑定的药品，是否重复录入等逻辑
             * @param {Boolean} isEnter 是否是回车事件
             * @returns {Promise<void>}
             */
            async handleBindCode() {
                const code = this.identificationCode;
                if (!code) return;
                const noCode = TraceCode.getDefaultNoCodeIdentification(this.goodsInfo);
                if (noCode && (code.toLocaleLowerCase()).startsWith(noCode.toLocaleLowerCase())) {
                    this.changeHasTraceCode(TraceableCodeTypeEnum.NO_CODE);
                    return;
                }

                const res = await TraceCode.fetchByCode(code);
                const {
                    goodsInfo,
                    no,
                    traceableCodeNoInfo,
                } = res || {};

                // 保存当前的药品标识码
                this.identificationCode = traceableCodeNoInfo?.drugIdentificationCode || code;
                this.traceCodeType = traceableCodeNoInfo?.type || TraceableCodeTypeEnum.HAS_CODE;

                // 有绑定的药品
                if (goodsInfo && goodsInfo.id !== this.goodsInfo.id) {
                    // 校验新增的标识码是否符合青岛三码合一库
                    const traceCodeNoCheckValid = await this.checkTraceCodeNoList(code, this.goodsInfo);
                    if (!traceCodeNoCheckValid) {
                        return;
                    }

                    // this.errorIdentificationCodeTip = `无法重复绑定。已绑定：${goodsInfo.displayName || goodsFullName(goodsInfo)}  ${goodsSpec(goodsInfo)}  ${goodsInfo.manufacturerFull || ''}`;
                    this._traceCodeConfirmDialog = new TraceCodeConfirmDialog({
                        visible: true,
                        title: '采集追溯码关联的不是本商品',
                        bindGoodsInfo: goodsInfo,
                        traceableCodeNoInfo,
                        traceCode: no,
                        goodsInfo,
                        onConfirm: () => {
                            this.codeList.push({
                                drugIdentificationCode: this.identificationCode,
                                type: TraceableCodeTypeEnum.HAS_CODE,
                                ...traceableCodeNoInfo,// 已后端解析数据为准
                            });
                            this.$emit('update:traceableCodeNoInfoList', this.codeList);
                            this.lastIdentificationCode = '';
                            // this.$nextTick(() => {
                            //     this.identificationCode = '';
                            // });
                            this.$abcEventBus.$emit('fetch-goods-list', false);
                        },
                        onClose: () => {
                            this._traceCodeConfirmDialog = null;
                        },
                    });
                    this._traceCodeConfirmDialog.generateDialogAsync();
                } else {
                    // this.errorIdentificationCodeTip = '';

                    // 检查重复录入
                    if (this.codeList.find((e) => e.drugIdentificationCode === this.identificationCode)) {
                        this.errorIdentificationCodeTip = '重复录入';
                        this.$nextTick(() => {
                            const itemRef = this.$refs.formItemRef;
                            if (itemRef) {
                                itemRef.validate();
                                itemRef.isHover = true;
                            }
                        });
                        return;
                    }

                    // 校验新增的标识码是否符合青岛三码合一库
                    const traceCodeNoCheckValid = await this.checkTraceCodeNoList(code, this.goodsInfo);
                    if (!traceCodeNoCheckValid) {
                        return;
                    }

                    this.codeList.push({
                        drugIdentificationCode: this.identificationCode,
                        type: TraceableCodeTypeEnum.HAS_CODE,
                        ...traceableCodeNoInfo,// 已后端解析数据为准
                    });
                    this.$emit('update:traceableCodeNoInfoList', this.codeList);
                    this.$nextTick(() => {
                        this.identificationCode = '';
                        this.lastIdentificationCode = '';
                        this.$refs.popoverRef?.updatePopper();
                    });
                }

                // this.$nextTick(() => {
                //     this.$refs.formItemRef?.validate();
                // });
            },
            /**
             * @desc 验证追溯码是否重复
             * <AUTHOR>
             * @date 2024/8/19 上午10:20
             */
            async validateCode(_, callback) {
                // 无码标识或者禁用状态不需要校验数据,
                if (this.isNoCode || this.disabled) {
                    callback({
                        validate: true,
                    });
                    return;
                }

                // 有错误提示
                if (this.errorTraceCodeTip || this.errorIdentificationCodeTip) {
                    callback({
                        message: this.errorTraceCodeTip || this.errorIdentificationCodeTip,
                        validate: false,
                    });
                } else {
                    callback({
                        validate: true,
                    });
                }
            },
            changeHasTraceCode(val) {
                console.log('changeHasTraceCode',val);

                // 无追溯码
                if (val === TraceableCodeTypeEnum.NO_CODE) {
                    this.$confirm({
                        type: 'warn',
                        title: '是否确定商品无追溯码',
                        content: () => {
                            return this.noCodeConfirmTextV2;
                        },
                        onConfirm: () => {
                            this.traceCodeType = val;
                            this.codeList = [{
                                drugIdentificationCode: TraceCode.getDefaultNoCodeIdentification(this.goodsInfo),
                                type: TraceableCodeTypeEnum.NO_CODE,
                            }];
                            this.$emit('update:traceableCodeNoInfoList', this.codeList);
                            this.identificationCode = TraceCode.getDefaultNoCodeIdentification(this.goodsInfo);
                            this.noCodeApplyStatus = -1;
                        },
                    });
                } else {
                    this.traceCodeType = val;
                    this.codeList = [];
                    this.$emit('update:traceableCodeNoInfoList', this.codeList);
                    this.identificationCode = '';
                }
            },
            async getSichuanNoCode() {
                if (this.checkNeedApplySichuanNoCode(this.goodsInfo.shebao)) {
                    const data = await this.getGoodsListNoCode([this.goodsInfo]);
                    if (data?.queryItemList) {
                        const list = data.queryItemList;
                        this.noCodeApplyStatus = list[0]?.applyStatus;
                    }
                }
            },
            isShowRiskPopover(codeInfo, goodsInfo) {
                const {
                    sanMaHeYiIdentificationCodeList, shebao,
                } = goodsInfo ?? {};
                const { nationalCode } = shebao ?? {};
                return TraceCode.hasInterceptTraceCodeFormatLength && nationalCode && Array.isArray(sanMaHeYiIdentificationCodeList) && sanMaHeYiIdentificationCodeList.length && !sanMaHeYiIdentificationCodeList.includes(codeInfo.drugIdentificationCode);
            },
            isShowInnerRiskPopper(codeInfoList, goodsInfo) {
                const {
                    sanMaHeYiIdentificationCodeList, shebao,
                } = goodsInfo ?? {};
                const { nationalCode } = shebao ?? {};
                if (
                    TraceCode.hasInterceptTraceCodeFormatLength &&
                    nationalCode &&
                    Array.isArray(sanMaHeYiIdentificationCodeList) &&
                    sanMaHeYiIdentificationCodeList.length &&
                    Array.isArray(codeInfoList) &&
                    codeInfoList.length
                ) {
                    for (let i = 0; i < codeInfoList.length; i++) {
                        const codeInfo = codeInfoList[i];
                        if (!sanMaHeYiIdentificationCodeList.includes(codeInfo.drugIdentificationCode)) {
                            return true;
                        }
                    }
                }
                return false;
            },
        },
    };
</script>

<style lang="scss">
.goods-archives-dialog-identification-code:not(.is-custom-focus) {
    //.abc-input__inner{
    //    cursor: pointer;
    //}
    .append-inner-input {
        pointer-events: none;
    }
}

.identification-code-wrapper {
    position: relative;

    &-input-icon {
        position: absolute;
        top: 36px;
        right: 8px;
        z-index: 99999;
    }
}
</style>
