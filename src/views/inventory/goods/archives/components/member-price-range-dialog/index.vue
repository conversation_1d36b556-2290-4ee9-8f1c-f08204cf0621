<template>
    <abc-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        title="设置商品会员价"
        size="large"
        append-to-body
        class="member-price-range-dialog"
        :disabled-keyboard="disabledKeyboard"
        data-cy="member-price-setting-dialog"
        @close="popDialogName"
        @open="pushDialogName"
    >
        <abc-form
            ref="createForm"
            v-abc-loading="loading"
            item-no-margin
            style="height: 100%;"
        >
            <abc-descriptions
                :column="2"
                :label-width="88"
                grid
                disabled
                size="large"
                stretch-last-item
                style="margin-bottom: 16px;"
            >
                <template
                    #title
                >
                    <abc-flex
                        :gap="12"
                        align="center"
                    >
                        <abc-text
                            v-if="displayName"
                            theme="black"
                            bold
                            size="normal"
                        >
                            {{ displayName }}
                        </abc-text>
                        <abc-text
                            v-else
                            theme="gray-light"
                            size="mini"
                            style="font-weight: normal;"
                        >
                            暂未设置通用名
                        </abc-text>
                        <abc-text theme="gray-light" style="font-weight: normal;" size="mini">
                            {{ displaySpec }}
                        </abc-text>
                    </abc-flex>
                </template>
                <abc-descriptions-item
                    label="零售价"
                >
                    <abc-text v-if="disabledMemberPrice" theme="warning-light" size="normal">
                        未定价，会员价基于零售价生效，请先填写零售价
                    </abc-text>
                    <template v-else>
                        <abc-flex justify="space-between">
                            <abc-text v-if="isChineseMedicine(goodsInfo)" theme="black" size="normal">
                                {{ goodsInfo.piecePrice | formatMoney(false) }}/{{ goodsInfo.pieceUnit }}
                            </abc-text>
                            <abc-text v-else theme="black" size="normal">
                                {{ goodsInfo.packagePrice | formatMoney(false) }}/{{ goodsInfo.packageUnit }}
                            </abc-text>
                            <view-cost-price-dialog
                                v-if="goodsInfo.id"
                                :id="goodsInfo.id"
                                :clinic-id="currentClinic?.clinicId"
                                :pharmacy-no="currentPharmacy?.no"
                                type="medicine"
                                :slot-style="{

                                }"
                            >
                                <abc-link size="small">
                                    进价记录
                                </abc-link>
                            </view-cost-price-dialog>
                        </abc-flex>
                    </template>
                </abc-descriptions-item>

                <abc-descriptions-item
                    v-if="!isChineseMedicine(goodsInfo) && goodsInfo.dismounting"
                    label="拆零价"
                    :span="1"
                >
                    <abc-text v-if="disabledMemberPrice" theme="warning-light" size="normal">
                        未定价，会员价基于零售价生效，请先填写零售价
                    </abc-text>
                    <abc-text v-else theme="black" size="normal">
                        {{ goodsInfo.piecePrice | formatMoney(false) }}/{{ goodsInfo.pieceUnit }}
                    </abc-text>
                </abc-descriptions-item>
            </abc-descriptions>

            <abc-layout preset="dialog-table">
                <abc-table
                    type="excel"
                    :data-list="tableData"
                    :render-config="tableConfig"
                    style="height: 497px;"
                >
                    <template #memberCardName="{ trData }">
                        <abc-table-cell>
                            <span :title="trData.memberCardName">{{ trData.memberCardName }}</span>
                        </abc-table-cell>
                    </template>
                    <template #discountType="{ trData }">
                        <abc-select
                            v-model="trData.discountType"
                            adaptive-width
                            data-cy="member-price-type-select"
                            :disabled="disabled"
                            :no-icon="disabled"
                            :input-style="{ color: disabled ? 'var(--abc-color-T3)' : '' }"
                            @change="handleChangeDiscountType(trData)"
                        >
                            <abc-option :value="DiscountTypes.special" label="单独设置特价">
                            </abc-option>
                            <abc-option :value="DiscountTypes.discount" label="单独设置折扣">
                            </abc-option>
                            <abc-option :value="DiscountTypes.no" :label="getDiscountTypeLabel(trData)">
                            </abc-option>
                        </abc-select>
                    </template>
                    <template #memberPrice="{ trData }">
                        <abc-form-item
                            v-if="trData.discountType === DiscountTypes.special"
                            required
                        >
                            <abc-tooltip content="会员价高于原价金额" :disabled="!checkMemberPriceExceed(trData.packagePrice)">
                                <abc-input
                                    v-model="trData.packagePrice"
                                    adaptive-width
                                    type="money"
                                    :config="priceInputConfig"
                                    :input-custom-style="{
                                        color: checkMemberPriceExceed(trData.packagePrice) ? 'var(--abc-color-Y2)' : disabled ? 'var(--abc-color-T3)' : '',
                                    }"
                                    :disabled="disabled"
                                    data-cy="member-price-input"
                                    @change="handleChangeMemberPrice(trData)"
                                >
                                    <abc-currency-symbol-icon slot="prepend" color="var(--abc-color-T3)"></abc-currency-symbol-icon>
                                    <template #appendInner>
                                        <abc-flex :gap="4" align="center">
                                            <span style="color: var(--abc-color-T3);">/ {{ goodsInfo.packageUnit || goodsInfo.pieceUnit }}</span>
                                            <price-type-tabs-popover
                                                is-member-price
                                                :disabled="disabled"
                                                :cost-price-is-null="costPriceIsNull"
                                                :allow-adjust-by-cost-price="allowAdjustByCostPrice"
                                                :allow-adjust-by-profit="allowAdjustByProfit"
                                                @confirm="calcMemberPriceByPopoverConfirm(trData, $event)"
                                            >
                                                <abc-button
                                                    class="button-setting"
                                                    icon="n-setting-line"
                                                    size="small"
                                                    variant="text"
                                                    theme="primary"
                                                ></abc-button>
                                            </price-type-tabs-popover>
                                        </abc-flex>
                                    </template>
                                </abc-input>
                            </abc-tooltip>
                        </abc-form-item>

                        <abc-form-item v-else-if="trData.discountType === DiscountTypes.discount" style="width: 100%;" required>
                            <abc-flex
                                align="center"
                                justify="flex-start"
                                style="height: 100%;"
                            >
                                <abc-input
                                    v-model="trData.discountValueView"
                                    v-abc-focus-selected
                                    type="number"
                                    :width="80"
                                    :config="{
                                        formatLength: 2,
                                        max: 9.99
                                    }"
                                    :input-custom-style="{
                                        textAlign: 'center',
                                        color: disabled ? 'var(--abc-color-T3)' : ''
                                    }"
                                    style="flex: none;"
                                    :disabled="disabled"
                                    data-cy="member-price-discount-input"
                                    @input="handleDiscountValueInput(trData)"
                                >
                                    <span slot="appendInner" style="color: var(--abc-color-T3);">折</span>
                                </abc-input>
                                <abc-text theme="gray-light">
                                    {{ appendInnerText(trData.discountValue) }}
                                </abc-text>
                            </abc-flex>
                        </abc-form-item>

                        <template v-else>
                            <template v-if="trData.discountGoodsDiscountType === DiscountGoodsDiscountType.special">
                                <abc-input
                                    v-model="trData.packagePrice"
                                    v-abc-focus-selected
                                    type="money"
                                    adaptive-width
                                    :placeholder="''"
                                    :config="priceInputConfig"
                                    :disabled="true"
                                    :input-custom-style="{
                                        'text-align': 'left',
                                        color: 'var(--abc-color-T3)'
                                    }"
                                    :title="trData.packagePrice"
                                >
                                    <span
                                        v-if="trData.packagePrice"
                                        slot="prepend"
                                        style="color: var(--abc-color-T3);"
                                    >
                                        <abc-money></abc-money>
                                    </span>
                                </abc-input>
                            </template>

                            <abc-flex
                                v-else-if="trData.discountValueView"
                                align="center"
                                justify="flex-start"
                                style="height: 100%;"
                            >
                                <abc-input
                                    v-model="trData.discountValueView"
                                    :width="80"
                                    type="number"
                                    :config="{
                                        formatLength: 2,
                                        max: 9.99
                                    }"
                                    :input-custom-style="{
                                        textAlign: 'center',
                                        color: 'var(--abc-color-T3)'
                                    }"
                                    disabled
                                    style="flex: none;"
                                >
                                    <span slot="appendInner" style="color: var(--abc-color-T3);">折</span>
                                </abc-input>
                                <abc-text theme="gray-light">
                                    {{ appendInnerText(trData.discountValue) }}
                                </abc-text>
                            </abc-flex>
                            <abc-table-cell v-else>
                                <abc-text theme="gray-light">
                                    {{ trData.discountGoodsType === DiscountGoodsType.type ? '未设置分类折扣' : '' }}
                                </abc-text>
                            </abc-table-cell>
                        </template>
                    </template>
                </abc-table>
                <!--                <abc-layout-footer v-if="memberPriceRangeText">-->
                <!--                    <abc-flex flex="1" justify="flex-end" style="margin-top: 16px;">-->
                <!--                        <abc-text theme="gray" size="normal">-->
                <!--                            {{ `定价范围：${memberPriceRangeText}` }}-->
                <!--                        </abc-text>-->
                <!--                    </abc-flex>-->
                <!--                </abc-layout-footer>-->
            </abc-layout>
        </abc-form>
        <template #footer>
            <abc-flex justify="flex-end">
                <abc-space>
                    <abc-button data-cy="member-price-setting-dialog-confirm" :disabled="disabledConfirmBtn" @click="handleConfirm">
                        确定
                    </abc-button>
                    <abc-button data-cy="member-price-setting-dialog-cancel" variant="ghost" @click="handleCancel">
                        取消
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    // import MarketingAPI from 'api/marketing';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    const ViewCostPriceDialog = () => import('views/inventory/common/view-cost-price-dialog.vue');
    import PriceTypeTabsPopover from '@/views-pharmacy/inventory/frames/price-adjustment/components/priceTypeTabsPopover.vue';

    import {
        goodsFullName,
        goodsSpec,
        isChineseMedicine,
    } from '@/filters';
    import { mapGetters } from 'vuex';
    import {
        getSafeNumber, isNull,
    } from '@/utils';
    import {
        clone, isEqual,
    } from '@abc/utils';
    import Big from 'big.js';
    import {
        OpType, DiscountTypes, DiscountGoodsType, DiscountGoodsDiscountType,
    } from 'views/inventory/goods/archives/constant';
    import GoodsV3API from 'api/goods/index-v3';
    import useMemberPrice from 'views/inventory/hooks/useMemberPrice';
    import { getMemberPriceRangeText } from 'views/inventory/goods/archives/utils';
    import { RoundingMode } from '@/common/constants/inventory';

    export default {
        name: 'MemberPriceRangeDialog',
        components: {
            AbcCurrencySymbolIcon,
            ViewCostPriceDialog,
            PriceTypeTabsPopover,
        },
        props: {
            visible: {
                type: Boolean,
                default: false,
            },
            onClose: {
                type: Function,
                default: () => {},
            },
            onConfirm: {
                type: Function,
                default: () => {},
            },
            // 设置的会员价数据
            memberPriceList: {
                type: Array,
                default: () => [],
            },
            goodsInfo: {
                type: Object,
                default: () => ({}),
            },
            priceInputConfig: {
                type: Object,
                default: () => ({}),
            },
            // 是否禁用价价格-档案价格信息传入
            disabledPackagePrice: {
                type: Boolean,
                default: false,
            },
            // 是否未定价商品-禁用会员价
            disabledMemberPrice: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                disabledKeyboard,
                pushDialogName,
                popDialogName,
            } = useDialogStackManager('设置商品会员价');

            const {
                getDiscountValue,
                getDiscountValueView,
                getDiscountTypeLabel,
            } = useMemberPrice();

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                getDiscountValue,
                getDiscountValueView,
                getDiscountTypeLabel,
            };
        },
        data() {
            return {
                DiscountTypes,
                DiscountGoodsType,
                DiscountGoodsDiscountType,
                dialogVisible: this.visible,
                loading: false,
                tableConfig: {
                    hasInnerBorder: true,
                    list: [
                        {
                            label: '会员等级',
                            key: 'memberCardName',
                            style: {
                                width: '204px',
                                minWidth: '204px',
                                maxWidth: '204px',
                            },
                        },
                        {
                            label: '会员价类型',
                            key: 'discountType',
                            style: {
                                width: '146px',
                                minWidth: '146px',
                                maxWidth: '146px',
                            },
                        },
                        {
                            label: '会员价',
                            key: 'memberPrice',
                            style: {
                                flex: '1',
                                maxWidth: '240px',
                                textAlign: 'left',
                            },
                        },
                    ],
                },
                // 所有会员类型
                allMemberTypes: [],
                // 会员价数据
                tableData: [],
                cachedTableData: [],
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'showSubSetPrice',
                'clinicConfig',
                'currentClinic',
                'currentPharmacy',
            ]),
            // ...mapGetters('viewDistribute', [
            //     'viewDistributeConfig',
            // ]),
            // fractionDigits() {
            //     return this.viewDistributeConfig.Inventory.fractionDigits;
            // },
            disabled() {
                return this.disabledMemberPrice || this.disabledPackagePrice;
            },
            displayName() {
                return this.goodsInfo.displayName ?? goodsFullName(this.goodsInfo);
            },
            displaySpec() {
                const spec = goodsSpec(this.goodsInfo);
                return spec === '1' ? '' : spec;
            },
            disabledConfirmBtn() {
                return isEqual(this.cachedTableData, this.tableData);
            },
            costPriceIsNull() {
                return isNull(this.goodsInfo.lastPackageCostPrice);
            },
            allowAdjustByCostPrice() {
                const {
                    lastPackageCostPrice, profitRat,
                } = this.goodsInfo;
                return !!(lastPackageCostPrice && profitRat);
            },
            allowAdjustByProfit() {
                const {
                    lastPackageCostPrice, stockPackageCount, stockPieceCount,
                } = this.goodsInfo;
                return !!(lastPackageCostPrice && (stockPackageCount || stockPieceCount));
            },
            retailPrice() {
                return isChineseMedicine(this.goodsInfo) ? this.goodsInfo.piecePrice : this.goodsInfo.packagePrice;
            },
            memberPriceRangeText() {
                const fractionDigits = this.priceInputConfig.formatLength;

                return getMemberPriceRangeText(this.tableData, fractionDigits, false);
            },
        },
        watch: {
            dialogVisible(val) {
                this.$emit('update:visible', val);
                if (!val) {
                    this.onClose && this.onClose();
                }
            },
        },
        created() {
            this.fetchMemberTypes();
        },
        methods: {
            isNull,
            isChineseMedicine,
            async fetchMemberTypes() {
                this.loading = true;
                try {
                    let { memberPriceList } = this;
                    // 新建场景下，拉会员数据
                    if (!this.memberPriceList.length) {
                        const {
                            CMSpec,
                            id,
                            type,
                            subType,
                            customTypeId,
                        } = this.goodsInfo;

                        const res = await GoodsAPIV3.fetchGoodsMemberTypePriceList({
                            list: [{
                                CMSpec,
                                goodsId: id,
                                goodsType: type,
                                goodsSubType: subType,
                                customTypeId,
                            }],
                        });

                        memberPriceList = res?.data?.list ?? [];

                        // const res = await MarketingAPI.getMemberCardTypeDetailList();
                        // memberPriceList = res?.rows?.map((item) => ({
                        //     memberTypeId: item.id,
                        //     memberCardName: item.name,
                        // })) ?? [];
                    }

                    this.tableData = memberPriceList.map((item) => {
                        const isSpecial = item.discountType === DiscountTypes.special || (item.discountGoodsDiscountType === DiscountGoodsDiscountType.special);
                        const packagePrice = isSpecial ? item.discountValue : this.calcDiscountPrice(item.discountValue);
                        let piecePrice = item?.piecePrice ?? '';

                        if (packagePrice) {
                            piecePrice = Big(getSafeNumber(packagePrice)).div(this.goodsInfo.pieceNum || 1).toFixed(this.priceInputConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
                        }
                        return {
                            // 保证原始数据不变，后端回传给后端
                            ...item,
                            // 用到的单独写出来
                            memberCardName: item?.memberCardName ?? '',
                            memberTypeId: item?.memberTypeId,
                            discountType: item?.discountType ?? DiscountTypes.no,
                            packagePrice: packagePrice ?? '',
                            piecePrice: piecePrice ?? '',
                            discountValue: item?.discountValue ?? '',
                            discountValueView: this.getDiscountValueView(item.discountValue),
                        };
                    });
                    this.cachedTableData = clone(this.tableData);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            appendInnerText(discountValue) {
                if (discountValue && !isNull(this.retailPrice)) {
                    const discountPrice = this.calcDiscountPrice(discountValue);
                    return `(约 ${this.$t('currencySymbol')}${discountPrice})`;
                }
                return '';
            },
            async calculatePrice(itemList = [], {
                opType,
                upPercent,
                profitRat,
                scaleType,
                roundingMode,
            }) {
                try {
                    const params = {
                        /**
                         * @type CalculatePriceItem[]
                         */
                        list: [],
                    };
                    itemList.forEach((item) => {
                        const constParams = {
                            goodsId: item.id,
                            pieceNum: item.pieceNum,
                            piecePrice: item.piecePrice,
                            packagePrice: item.packagePrice,
                            packageCostPrice: item.lastPackageCostPrice, // 最近进价
                            avgPackageCostPrice: item.avgPackageCostPrice,// 平均进价
                            shebaoLimitPackagePrice: item.shebaoLimitPackagePrice,
                            roundingMode,
                            scaleType,
                            calPackageType: opType,
                            calPackageUpPercent: OpType.PROFIT === opType ? Number(profitRat) : upPercent,
                            calPieceType: opType,
                            calPieceUpPercent: OpType.PROFIT === opType ? Number(profitRat) : upPercent,
                        };


                        params.list.push(constParams);
                    });
                    const res = await GoodsV3API.calculatePrice(params);
                    return res;
                } catch (e) {
                    console.error(e);
                }
            },
            async calcMemberPriceByPopoverConfirm(row, modifyPriceParams) {
                //调接口算费用
                const result = await this.calculatePrice([{
                    ...row,
                    ...this.goodsInfo,
                }], modifyPriceParams);
                const newPackagePrice = result?.list?.[0]?.newPackagePrice;
                const newPiecePrice = result?.list?.[0]?.newPiecePrice;
                const calResult = result?.list?.[0]?.calResult;

                if (calResult === 1) {
                    if (!isNull(newPackagePrice)) {
                        row.packagePrice = newPackagePrice;
                    }
                    if (!isNull(newPiecePrice)) {
                        row.piecePrice = newPiecePrice;
                    }
                    // 避免后端中药没算大单位价格，保证大单位价格有值
                    if (isChineseMedicine(this.goodsInfo)) {
                        row.packagePrice = row.piecePrice;
                    }

                    this.handleChangeMemberPrice(row);
                } else {
                    this.$Toast({
                        type: 'error',
                        message: '价格计算失败',
                    });
                }
            },
            calcDiscountPrice(discountValue) {
                if (isNull(this.retailPrice) || isNull(discountValue)) return '';

                return Big(getSafeNumber(this.retailPrice)).times(discountValue).toFixed(this.priceInputConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
            },
            checkMemberPriceExceed(price) {
                if (!isNull(this.retailPrice) && price) {
                    return Big(getSafeNumber(price)).gt(this.retailPrice);
                }
                return false;
            },
            async handleChangeDiscountType(row) {
                if (row.discountType === DiscountTypes.no) {
                    const res = await GoodsAPIV3.fetchGoodsTypeMemberPriceDiscount({
                        goodsType: this.goodsInfo.type,
                        goodsSubType: this.goodsInfo.subType,
                        goodsCMSpec: this.goodsInfo.goodsCMSpec,
                        customTypeId: this.goodsInfo.customTypeId,
                        memberTypeId: row.memberTypeId,
                        compositeGoodsGroupIds: this.goodsInfo.compositeGoodsComposeList?.filter((item) => item?.composeType === 40)?.map((item) => item?.parentGoodsId),
                    });
                    row.discountValue = res?.data?.discountValue ?? '';
                    row.discountValueView = this.getDiscountValueView(row.discountValue);
                    // 折扣也把价格算出来，但是还是以后端为准
                    // 商品组特价不计算
                    const isSpecial = row.discountType === DiscountTypes.special || (row.discountGoodsDiscountType === DiscountGoodsDiscountType.special);

                    row.packagePrice = isSpecial ? row.discountValue : this.calcDiscountPrice(row.discountValue);
                    if (row.packagePrice) {
                        row.piecePrice = Big(getSafeNumber(row.packagePrice)).div(this.goodsInfo.pieceNum || 1).toFixed(this.priceInputConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
                    }
                } else {
                    row.packagePrice = '';
                    row.piecePrice = '';
                    row.discountValue = '';
                    row.discountValueView = '';
                }
            },
            handleChangeMemberPrice(row) {
                if (row.packagePrice) {
                    row.discountValue = row.packagePrice;
                }
            },
            handleDiscountValueInput(row) {
                if (row.discountValueView) {
                    row.discountValue = this.getDiscountValue(row.discountValueView);
                    // 折扣也把价格算出来，但是还是以后端为准
                    row.packagePrice = this.calcDiscountPrice(row.discountValue);
                    if (row.packagePrice) {
                        row.piecePrice = Big(getSafeNumber(row.packagePrice)).div(this.goodsInfo.pieceNum || 1).toFixed(this.priceInputConfig?.formatLength ?? 2, RoundingMode.ROUND_UP);
                    }
                }
            },
            handleCancel() {
                this.dialogVisible = false;
            },
            handleConfirm() {
                this.$refs.createForm?.validate((valid) => {
                    if (valid) {
                        // 补充数据标记，会员价不能删除
                        this.tableData.forEach((item) => {
                            if (item.id) {
                                item.opType = OpType.update;
                            } else {
                                item.opType = OpType.add;
                            }
                        });
                        this.onConfirm && this.onConfirm(this.tableData);
                        this.dialogVisible = false;
                    }
                });

            },
        },
    };
</script>

<style lang="scss">
// TODO 复写abc-input 下appendInner灰色样式
.member-price-range-dialog {
    .button-setting {
        .abc-icon {
            color: var(--abc-color-theme1) !important;
        }
    }
}
</style>
