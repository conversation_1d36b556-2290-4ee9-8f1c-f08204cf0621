<template>
    <div class="goods-archives-info shebao-info-wrapper">
        <div class="shebao-code-wrapper">
            <div class="title" style="margin-bottom: 8px;">
                <span class="label">医保对码</span>
            </div>
            <shebao-code-card
                v-model="nationalCode"
                :code-id.sync="nationalCodeId"
                :current-social-info="currentSocialInfo"
                :selected-clinic-id="selectedClinicId"
                :product-info.sync="goodsInfo"
                :start-auto-shebao-code.sync="startAutoShebaoCode"
                :allow-auto-shebao-code="allowAutoShebaoCode && !clearAutoShebaoCode"
                :clear-auto-shebao-code="clearAutoShebaoCode"
                :disabled="disableCode"
                :search-loading.sync="searchLoading"
                :shebao-pay-mode.sync="shebaoPayMode"
                :search-table-view-type="searchTableViewType"
                :shebao-code-type="3"
                :need-check-repeat="true"
                :allow-init-shebao="allowInitShebao"
                :wait-handle-national-code-id="waitHandleNationalCodeId"
                :wait-handle-national-code="waitHandleNationalCode"
                :detail-type="detailType"
                @change="changeSocialInfoV2"
                @changeMatchMode="changeMatchMode"
                @clearCurrentSocial="clearCurrentSocialInfo"
                @updateWaitHandleInfo="updateWaitHandleInfo"
                @modifyProductInfo="handleModifyProductInfo"
                @fetchAutoShebaoCode="$emit('fetchAutoShebaoCode')"
            ></shebao-code-card>
        </div>

        <template v-if="!isChainAdmin && nationalCode && nationalCode !== DISABLED">
            <div class="shebao-pay-mode">
                <abc-form-item
                    label="医保结算方式"
                >
                    <template v-if="!!nationalCode && viewDistributeConfig.Inventory.showShebaoPayIcon" slot="labelTips">
                        <abc-flex vertical :gap="8" style="max-width: 400px; font-size: 12px;">
                            <abc-space align="start" :size="8">
                                <p style=" width: 58px; color: var(--abc-color-T2);">
                                    优先统筹
                                </p>
                                <p style="text-align: justify;">
                                    结算时优先使用统筹基金，统筹余额不足或条件不足时转向个账，个账余额不足或条件不足时转向自费
                                </p>
                            </abc-space>
                            <abc-space v-if="visibleShebaoPayModeSelfCost" :size="8" align="start">
                                <p style=" width: 58px; color: var(--abc-color-T2);">
                                    优先个账
                                </p>
                                <p style="text-align: justify;">
                                    结算时不使用统筹基金，优先使用个账，个账余额不足或条件不足时转向自费
                                </p>
                            </abc-space>
                            <abc-space :size="8" align="start">
                                <p style=" width: 58px; color: var(--abc-color-T2);">
                                    不过医保
                                </p>
                                <p style="text-align: justify;">
                                    不提交医保结算
                                </p>
                            </abc-space>
                        </abc-flex>
                    </template>
                    <abc-radio-group v-model="shebaoPayMode" data-cy="inventory-archives-shebao-info-form-pay-mode-radio-group">
                        <abc-radio :disabled="disablePayMode" :label="shebaoPayModeObj.overAll.value" data-cy="inventory-archives-shebao-info-form-over-all-radio">
                            {{ shebaoPayModeObj.overAll.label }}
                        </abc-radio>
                        <abc-radio
                            v-if="(!!nationalCode) && visibleShebaoPayModeSelfCost && shebaoPayModeObj.self"
                            :disabled="disablePayMode"
                            :label="shebaoPayModeObj.self.value"
                            data-cy="inventory-archives-shebao-info-form-self-radio"
                        >
                            {{ shebaoPayModeObj.self.label }}
                        </abc-radio>
                        <abc-radio
                            :disabled="disablePayMode"
                            :label="shebaoPayModeObj.noUse.value"
                            data-cy="inventory-archives-shebao-info-form-no-use-radio"
                        >
                            {{ shebaoPayModeObj.noUse.label }}
                        </abc-radio>
                    </abc-radio-group>
                </abc-form-item>
            </div>
        </template>
    </div>
</template>


<script>
    import { ShebaoPayMode } from 'views/inventory/goods/archives/components/social-code-autocomplete/constant';
    import {
        canUpdateGoodsInfoKey, disabledBaseInfoKey,
    } from 'views/inventory/goods/archives/provideKeys';

    import { mapGetters } from 'vuex';
    import ShebaoCodeCard from 'views/inventory/goods/archives/components/shebao-code-card.vue';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import {
        ShebaoSearchTypeEnum,
    } from 'views/inventory/components/social-code-autocomplete/constant';
    import {
        DISABLED,
    } from 'views/inventory/components/social-code-autocomplete/constant';
    export default {
        name: 'ShebaoInfo',
        components: {
            ShebaoCodeCard,
        },
        inject: {
            // 手动控制部分字段的禁用
            disabledBaseInfo: {
                from: disabledBaseInfoKey,
                default: false,
            },
            canUpdateGoodsInfo: {
                from: canUpdateGoodsInfoKey,
                default: true,
            },
        },
        props: {
            goodsInfo: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            selectedClinicId: {
                type: String,
                default: '',
            },
            disabledEditArchives: {
                type: Boolean,
                default: false,
            },
            allowInitShebao: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                ShebaoPayMode,
                currentSocialInfo: null,
                currentShebaoPayMode: 0,
                DISABLED,
                startAutoShebaoCode: false,
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin', 'currentClinic','isCanOperateGoodsAdjustPriceInInventory','isCanModifyGoodsArchivesInInventory','isAdmin']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            // 详情不可修改就默认不自动对码
            allowAutoShebaoCode() {
                return !this.disabledEditArchives && this.startAutoShebaoCode;
            },
            // 存储清除标记
            clearAutoShebaoCode() {
                // 详情不触发自动对码
                return !!this.goodsInfo.shebao?.cleanMatchCodeFlag;
            },
            searchTableViewType() {
                const {
                    type = 0, subType = 1, typeId = 1,
                } = this.goodsInfo;
                if (type === GoodsTypeEnum.MEDICINE &&
                    [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(subType) &&
                    [GoodsTypeIdEnum.MEDICINE_WESTERN, GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT].includes(typeId)) {
                    return ShebaoSearchTypeEnum.MEDICINE;
                }
                if (type === GoodsTypeEnum.MEDICINE &&
                    subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine &&
                    [GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES, GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE].includes(typeId)) {
                    return ShebaoSearchTypeEnum.MEDICINE_CHINESE;
                }
                if (type === GoodsTypeEnum.MATERIAL &&
                    subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials &&
                    typeId === GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL) {
                    return ShebaoSearchTypeEnum.MATERIAL;
                }
                return ShebaoSearchTypeEnum.PROJECT;
            },
            curChainId() {
                return this.currentClinic.chainId;
            },
            visibleShebaoPayModeSelfCost() {
                return this.$abcSocialSecurity.isNational;
            },
            // 总部查看门店的药品信息不能编辑医保对码
            disableCode() {
                if (this.operateGoodsArchives && this.isAdmin) {
                    const canOperate = this.isCanOperateGoodsAdjustPriceInInventory || this.isCanModifyGoodsArchivesInInventory;
                    if (!canOperate) {
                        return true;
                    }
                }
                // const disabled = this.isChainAdmin ? (!!(this.selectedClinicId && this.selectedClinicId !== this.curChainId)) : false;
                return this.disabledBaseInfo;
            },
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            disablePayMode() {
                return !!this.goodsInfo.disable || this.disabledBaseInfo;
            },
            shebaoPayModeObj() {
                return this.viewDistributeConfig.Inventory.shebaoPayModeObj || {};
            },
            nationalCode: {
                get() {
                    return this.goodsInfo.shebao?.nationalCode;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'shebao', {
                        ...this.goodsInfo.shebao,
                        nationalCode: v,
                    });
                },
            },
            searchLoading: {
                get() {
                    return this.goodsInfo.shebao?.searchLoading;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'shebao', {
                        ...this.goodsInfo.shebao,
                        searchLoading: v,
                    });
                },
            },
            nationalCodeId: {
                get() {
                    return this.goodsInfo.shebao?.nationalCodeId;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'shebao', {
                        ...this.goodsInfo.shebao,
                        nationalCodeId: v,
                    });
                },
            },
            waitHandleNationalCode() {
                return this.goodsInfo.shebao?.waitHandleNationalCode;
            },
            waitHandleNationalCodeId() {
                return this.goodsInfo.shebao?.waitHandleNationalCodeId;
            },
            detailType() {
                return this.goodsInfo.shebao?.detailType;
            },
            shebaoPayMode: {
                get() {
                    return this.goodsInfo.shebaoPayMode || ShebaoPayMode.OVERALL;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'shebaoPayMode', v);
                },
            },
        },
        watch: {
            nationalCode: {
                handler(value) {
                    console.log('当前值=', value);
                    if (!value) {
                        this.currentSocialInfo = null;
                    }
                },
                deep: true,
            },
        },
        methods: {
            clearCurrentSocialInfo() {
                // 需要标记当前对码清理过，并保存
                this.$emit('updateGoodsInfo', 'shebao', {
                    ...this.goodsInfo.shebao,
                    cleanMatchCodeFlag: 1,
                });
            },
            changeMatchMode(matchMode = null) {
                const shebaoInfo = this.goodsInfo.shebao;
                // 需要标记当前对码清理过，并保存
                if (this.goodsInfo.shebao.autoShebaoCode && matchMode === 2) {
                    shebaoInfo.autoShebaoCode = false;
                }
                this.$emit('updateGoodsInfo', 'shebao', {
                    ...shebaoInfo,
                    matchMode,
                });
            },
            updateWaitHandleInfo(item) {
                this.$emit('updateGoodsInfo', 'shebao', {
                    ...this.goodsInfo.shebao,
                    ...item,
                });
            },
            handleModifyProductInfo(val) {
                const {
                    pieceNum,
                    pieceUnit,
                    packageUnit,
                } = val;
                this.$emit('updateGoodsInfo', 'pieceNum', pieceNum);
                this.$emit('updateGoodsInfo', 'pieceUnit', pieceUnit);
                this.$emit('updateGoodsInfo', 'packageUnit', packageUnit);
            },
            changeSocialInfoV2(socialInfo, isClear = false) {
                if (isClear) {
                    this.currentSocialInfo = null;
                } else {
                    this.currentSocialInfo = socialInfo;
                }
                const currentItem = {
                    // 添加医保等级
                    medicalFeeGrade: socialInfo?.medicalFeeGrade !== undefined ? socialInfo?.medicalFeeGrade ?? '' : this.goodsInfo?.shebao?.medicalFeeGrade ?? '',
                    shebaoPieceNum: socialInfo?.pieceNum,
                    shebaoPieceUnit: socialInfo?.pieceUnit,
                    shebaoPackageUnit: socialInfo?.packageUnit,
                    shebaoMedicineNmpn: socialInfo?.approvalCode,
                    shebaoManufacturer: socialInfo?.manufacturer,
                    shebaoMedicineCadn: socialInfo?.medicineCadn,
                };
                if (socialInfo?.standardCode) {
                    currentItem.standardCode = socialInfo?.standardCode;
                }
                this.$emit('updateGoodsInfo', 'shebao', {
                    ...this.goodsInfo.shebao,
                    detailType: 'default',
                    ...currentItem,
                });
                this.$emit('updateShebaoPriceInfo', this.currentSocialInfo);
            },
            changeSocialInfo(socialInfo) {
                this.$emit('updateGoodsInfo', 'shebao', {
                    ...this.goodsInfo.shebao,
                    // 添加医保等级
                    medicalFeeGrade: socialInfo?.medicalFeeGrade ?? '',
                    // 添加本位码
                    standardCode: socialInfo?.standardCode ?? '',
                });
            },
        },

    };
</script>
