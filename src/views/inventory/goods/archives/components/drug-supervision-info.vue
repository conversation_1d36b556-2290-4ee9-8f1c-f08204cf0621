<template>
    <div class="goods-archives-info">
        <abc-flex vertical gap="middle">
            <abc-text theme="gray">
                药监对码
                <abc-tooltip-info>
                    <div style="width: 300px;">
                        河北省药监局规定：药品须先完成药监品种编码对照，并每日上报进销存明细
                    </div>
                </abc-tooltip-info>
            </abc-text>
            <drug-supervision-code-card
                v-model="centerCode"
                :current-drug-supervision-info="currentDrugSupervisionInfo"
                :product-info="goodsInfo"
                :start-auto-center-code.sync="startAutoCenterCode"
                :allow-auto-center-code="allowAutoCenterCode && !clearAutoCenterCode"
                :clear-auto-center-code="clearAutoCenterCode"
                :disabled="disableCode"
                :search-loading.sync="searchLoading"
                :real-product-info="realProductInfo"
                @clearCurrentDrugSupervision="clearCurrentDrugSupervision"
                @change="changeDrugSupervisionInfoV2"
            ></drug-supervision-code-card>
        </abc-flex>
    </div>
</template>

<script>
    import {
        canUpdateGoodsInfoKey, disabledBaseInfoKey,
    } from 'views/inventory/goods/archives/provideKeys';
    import { mapGetters } from 'vuex';
    import DrugSupervisionCodeCard from 'views/inventory/goods/archives/components/drug-supervision-code/drug-supervision-code-card.vue';

    export default {
        name: 'DrugSupervisionInfo',
        components: {
            DrugSupervisionCodeCard,
        },
        inject: {
            // 手动控制部分字段的禁用
            disabledBaseInfo: {
                from: disabledBaseInfoKey,
                default: false,
            },
            canUpdateGoodsInfo: {
                from: canUpdateGoodsInfoKey,
                default: true,
            },
        },
        props: {
            goodsInfo: {
                type: Object,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            selectedClinicId: {
                type: String,
                default: '',
            },
            disabledEditArchives: {
                type: Boolean,
                default: false,
            },
            realProductInfo: Object,
        },
        data() {
            return {
                currentDrugSupervisionInfo: null,
                startAutoCenterCode: false,
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
                'isCanOperateGoodsAdjustPriceInInventory',
                'isCanModifyGoodsArchivesInInventory',
                'isAdmin',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            // 详情不可修改就默认不自动对码
            allowAutoCenterCode() {
                return !this.disabledEditArchives && this.startAutoCenterCode;
            },
            // 存储清除标记
            clearAutoCenterCode() {
                // 详情不触发自动对码
                return !!this.goodsInfo.drugSupervision?.cleanMatchCodeFlag;
            },
            curChainId() {
                return this.currentClinic.chainId;
            },
            // 总部查看门店的药品信息不能编辑医保对码
            disableCode() {
                if (this.operateGoodsArchives && this.isAdmin) {
                    const canOperate = this.isCanOperateGoodsAdjustPriceInInventory || this.isCanModifyGoodsArchivesInInventory;
                    if (!canOperate) {
                        return true;
                    }
                }
                const disabled = this.isChainAdmin ? (!!(this.selectedClinicId && this.selectedClinicId !== this.curChainId)) : false;
                return !!this.goodsInfo.v2DisableStatus || this.disabledBaseInfo || disabled;
            },
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            centerCode: {
                get() {
                    return this.goodsInfo.centerCode;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'centerCode', v);
                },
            },
            searchLoading: {
                get() {
                    return this.goodsInfo.drugSupervision?.searchLoading;
                },
                set(v) {
                    this.$emit('updateGoodsInfo', 'drugSupervision', {
                        ...this.goodsInfo.drugSupervision,
                        searchLoading: v,
                    });
                },
            },
        },
        watch: {
            centerCode: {
                handler(value) {
                    if (!value) {
                        this.currentDrugSupervisionInfo = null;
                    }
                },
                deep: true,
            },
        },
        methods: {
            clearCurrentDrugSupervision() {
                // 需要标记当前对码清理过，并保存
                this.$emit('updateGoodsInfo', 'drugSupervision', {
                    ...this.goodsInfo.drugSupervision,
                    cleanMatchCodeFlag: 1,
                });
            },
            changeDrugSupervisionInfoV2(drugSupervisionInfo, isClear = false) {
                this.timer = setTimeout(() => {
                    // 使用对象展开运算符来确保响应式更新
                    if (isClear) {
                        this.currentDrugSupervisionInfo = null;
                    } else {
                        this.currentDrugSupervisionInfo = drugSupervisionInfo;
                    }
                }, 100);
            },
        },
    };
</script>
