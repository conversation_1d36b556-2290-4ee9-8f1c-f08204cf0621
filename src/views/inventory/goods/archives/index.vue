<template>
    <abc-form ref="form" class="goods-archives-wrapper">
        <div class="left">
            <abc-tips-card-v2 v-if="showPharmacyEditTips" theme="primary" style="margin-bottom: 24px;">
                {{ showPharmacyEditTips }}
            </abc-tips-card-v2>
            <section class="group-item">
                <h3>基础</h3>
                <base-info
                    :goods-info="postData"
                    :disabled="disabledEditArchives"
                    :error-name-tip="errorNameTip"
                    :error-bar-code-tip="errorBarCodeTip"
                    :error-short-id-tip="errorShortIdTip"
                    :primary-classification="primaryClassification"
                    :instructions-category-map="instructionsCategoryMap"
                    :dispense-machine-list="dispenseMachineList"
                    :is-open-smart-dispensing="isOpenSmartDispensing"
                    :is-medicine-material="isMedicineMaterial"
                    :is-medicine-disinfectant="isMedicineDisinfectant"
                    :is-goods-material="isGoodsMaterial"
                    :is-chinese-medicine-type="isChineseMedicineType"
                    :is-chinese-patent-medicine-type="isChinesePatentMedicineType"
                    :is-chinese-western-patent-type="isChineseWesternPatentType"
                    :is-fixed-or-logistics-material="isFixedOrLogisticsMaterial"
                    @updateGoodsInfo="updateGoodsInfo"
                    @blurInput="blurInput"
                    @quickFiling="$emit('quickFiling', $event)"
                ></base-info>
            </section>
            <section v-if="isShowInfo" class="group-item">
                <h3>扩展</h3>
                <extend-info
                    :goods-info="postData"
                    :disabled="disabledEditArchives"
                    :selected-clinic-id="selectedClinicId"
                    :error-trace-code-tip="errorTraceCodeTip"
                    :is-medicine-material="isMedicineMaterial"
                    :is-medicine-disinfectant="isMedicineDisinfectant"
                    :is-goods-material="isGoodsMaterial"
                    :is-chinese-medicine-type="isChineseMedicineType"
                    :is-chinese-western-patent-type="isChineseWesternPatentType"
                    :is-fixed-or-logistics-material="isFixedOrLogisticsMaterial"
                    @updateGoodsInfo="updateGoodsInfo"
                ></extend-info>
            </section>
            <slot></slot>
        </div>
        <div v-if="isShowRight" class="right">
            <section v-if="isShowInfo" class="group-item">
                <h3>{{ priceTitle }}</h3>
                <price-info
                    :goods-info="postData"
                    :disabled="disabledEditFixPrice"
                    :max-cost-info="maxCostInfo"
                    :chain-max-piece-price="chainMaxPiecePrice"
                    :chain-min-piece-price="chainMinPiecePrice"
                    :chain-max-package-price="chainMaxPackagePrice"
                    :chain-min-package-price="chainMinPackagePrice"
                    :is-chinese-medicine-type="isChineseMedicineType"
                    :is-chinese-western-patent-type="isChineseWesternPatentType"
                    :fee-category-list="feeCategoryList"
                    :fee-types-list="feeTypesList"
                    :profit-classification-list="profitClassificationList"
                    :selected-clinic-id="selectedClinicId"
                    :shebao-price-info="shebaoPriceInfo"
                    @updateProfitTypes="handleUpdateProfitTypes"
                    @updateGoodsInfo="updateGoodsInfo"
                ></price-info>
            </section>
            <section v-if="isShowInfo && !isGoodsType" class="group-item">
                <h3>医保</h3>
                <shebao-info
                    :goods-info="postData"
                    :disabled="disabled"
                    :allow-init-shebao="allowInitShebao"
                    :disabled-edit-archives="disabledEditArchives"
                    :selected-clinic-id="selectedClinicId"
                    @updateGoodsInfo="updateGoodsInfo"
                    @updateShebaoPriceInfo="updateShebaoPriceInfo"
                    @fetchAutoShebaoCode="fetchAutoShebaoCode('check')"
                ></shebao-info>
            </section>


            <section v-if="isShowDrugSupervisionInfo" class="group-item">
                <h3>药监</h3>
                <drug-supervision-info
                    :goods-info="postData"
                    :disabled="disabled"
                    :disabled-edit-archives="disabledEditArchives"
                    :selected-clinic-id="selectedClinicId"
                    :real-product-info="realProductInfo"
                    @updateGoodsInfo="updateGoodsInfo"
                    @fetchAutoCenterCode="fetchAutoCenterCode"
                ></drug-supervision-info>
            </section>

            <update-log
                v-if="isShowUpdateLog"
                style="padding-bottom: 36px; margin: 0; border: none;"
                :is-show="!isShowInfo"
                :update-log="updateLogs"
                :goods-type="postData.type"
            ></update-log>
        </div>
    </abc-form>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        GoodsTypeEnum,
        GoodsTypeIdEnum,
        GoodsSubTypeEnum,
        PharmacyTypeEnum,
    } from '@abc/constants';
    import API from 'api/property';

    import {
        isChineseMedicine, isChinesePatentMedicine, isWesternMedicine,
    } from '@/filters';
    import {
        isShowUpdateLogKey,
        disabledBaseInfoKey,
        GoodsArchivesControllerKey,
        canUpdateGoodsInfoKey,
    } from 'views/inventory/goods/archives/provideKeys';

    import BaseInfo from './components/base-info.vue';
    import ExtendInfo from './components/extend-info.vue';
    import PriceInfo from './components/price-info.vue';
    import ShebaoInfo from './components/shebao-info.vue';
    import DrugSupervisionInfo from './components/drug-supervision-info.vue';
    import { SubPriceFlag } from 'views/common/inventory/constants';
    import useIsOpenSocialChainAdmin from 'views/inventory/hooks/useIsOpenSocialChainAdmin';
    const UpdateLog = () => import('views/inventory/goods/components/update-log.vue');

    export default {
        name: 'GoodsArchivesForm',
        components: {
            UpdateLog,
            BaseInfo,
            ExtendInfo,
            PriceInfo,
            ShebaoInfo,
            DrugSupervisionInfo,
        },
        inject: {
            GoodsArchivesController: {
                from: GoodsArchivesControllerKey,
            },
            isShowUpdateLog: {
                from: isShowUpdateLogKey,
                default: false,
            },
            // 手动控制部分字段的禁用
            disabledBaseInfo: {
                from: disabledBaseInfoKey,
                default: false,
            },
            canUpdateGoodsInfo: {
                from: canUpdateGoodsInfoKey,
                default: true,
            },
        },
        props: {
            typeId: Number,
            type: Number,
            // 错误提示
            errorNameTip: String,
            errorBarCodeTip: String,
            errorShortIdTip: String,
            errorTraceCodeTip: String,
            maxCostInfo: Object,
            chainMaxPiecePrice: [String, Number],
            chainMinPiecePrice: [String, Number],
            chainMaxPackagePrice: [String, Number],
            chainMinPackagePrice: [String, Number],
            // 档案修改日志
            updateLogs: {
                type: Array,
                default: () => [],
            },
            isShowRight: {
                type: Boolean,
                default: true,
            },
            isRecreate: {
                type: Boolean,
                default: false,
            },
            selectedClinicId: String,
            allowInitShebao: {
                type: Boolean,
                default: false,
            },
            realProductInfo: Object,
        },
        setup() {
            const { isHaveClinicOpen } = useIsOpenSocialChainAdmin();

            return {
                isHaveClinicOpen,
            };
        },
        data() {
            return {
                postData: {
                    typeId: this.typeId,
                    type: this.type,
                },
                // 是否开启智能发药机
                isOpenSmartDispensing: false,
                // 药品说明书
                instructionsCategoryMap: {},
                // 费用类型
                feeTypesList: [],
                // 病案首页费目
                feeCategoryList: [],
                shebaoPriceInfo: null,
            };
        },
        computed: {
            ...mapGetters([
                'goodsPrimaryClassification',
                'goodsConfig',
                'isAdmin',
                'isSingleStore',
                'isChainSubStore',
                'currentPharmacy',
                'showSubSetPrice',
                'profitClassificationList',
                'isCanCreateGoodsArchivesInInventory',
                'isCanModifyGoodsArchivesInInventory',
                'isCanOperateGoodsAdjustPriceInInventory',
                'clinicBasicConfig',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isCanOperateGoodsArchivesInInventory() {
                return this.postData.id ? this.isCanModifyGoodsArchivesInInventory : this.isCanCreateGoodsArchivesInInventory;
            },
            showPharmacyEditTips() {
                if (this.operateGoodsArchives && !this.isCanOperateGoodsArchivesInInventory) {
                    if (this.isChainSubStore) {
                        return '无修改档案权限，请联系总部开启权限';
                    }
                    return '无编辑档案权限，请在[设置-信息安全]中设置';
                }
                return '';
            },
            disabledEditArchives() {
                // 药品审批中不支持修改
                if (!this.canUpdateGoodsInfo) return true;

                if (this.operateGoodsArchives) {
                    return !this.isCanOperateGoodsArchivesInInventory;
                }
                return this.disabled;
            },
            disabledEditFixPrice() {
                if (this.operateGoodsAdjustPrice) {
                    return !this.isCanOperateGoodsAdjustPriceInInventory;
                }
                return this.disabled;
            },
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            operateGoodsAdjustPrice() {
                return this.viewDistributeConfig.Inventory.operateGoodsAdjustPrice;
            },
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            useIndividualPricingModel() {
                return this.viewDistributeConfig.Inventory.useIndividualPricingModel;
            },
            supportGoodsTypeIdMap() {
                return this.viewDistributeConfig.Inventory.supportGoodsTypeIdMap;
            },
            priceTitle() {
                if (this.useIndividualPricingModel) {
                    if (this.isSingleStore) return '定价';
                    if (this.postData.id) {
                        if (this.postData.subClinicPriceFlag === SubPriceFlag.UNIFY_FIXED_PRICE) return '连锁统一定价';
                        if (this.postData.subClinicPriceFlag === SubPriceFlag.SEPARATE_FIXED_PRICE_AND_ADMIN_CLINIC) return '门店单独定价（总部定）';
                        if (this.postData.subClinicPriceFlag === SubPriceFlag.SEPARATE_FIXED_PRICE_AND_SUB_CLINIC) return '门店单独定价（门店定）';
                    }
                    if (this.isChainSubStore && this.showSubSetPrice) return '门店单独定价（门店定）';

                    return '连锁统一定价';
                }
                return '定价';
            },
            errorInfos() {
                return this.errorNameTip + this.errorBarCodeTip + this.errorShortIdTip + this.errorTraceCodeTip;
            },
            disabled() {
                return !this.isAdmin || this.disabledBaseInfo;
            },
            // 物资-固定资产与后勤材料不显示
            isShowInfo() {
                // 物资-固定资产与后勤材料不显示
                if (this.postData.typeId === GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS) return false;
                if (this.postData.typeId === GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL) return false;
                return true;
            },

            isFixedOrLogisticsMaterial () {
                return !this.isShowInfo;
            },

            // 是代煎代配库房
            isVirtualPharmacy() {
                return this.currentPharmacy?.type === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            // 是商品
            isGoodsType() {
                return this.postData.type === GoodsTypeEnum.GOODS;
            },
            // 是中药
            isChineseMedicineType() {
                return isChineseMedicine(this.postData);
            },
            // 是西药
            isWesternMedicineType() {
                return isWesternMedicine(this.postData);
            },
            // 是中成药
            isChinesePatentMedicineType() {
                return isChinesePatentMedicine(this.postData);
            },
            // 是中西成药
            isChineseWesternPatentType() {
                return this.isWesternMedicineType || this.isChinesePatentMedicineType;
            },
            // 是医疗器械
            isMedicineMaterial() {
                return this.postData.typeId === GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL;
            },
            // 是消毒用品
            isMedicineDisinfectant() {
                return this.postData.typeId === GoodsTypeIdEnum.MATERIAL_DISINFECTANT;
            },
            // 是商品材料
            isGoodsMaterial() {
                return this.postData.typeId === GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT ||
                    this.postData.typeId === GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE ||
                    this.postData.typeId === GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD ||
                    this.postData.typeId === GoodsTypeIdEnum.ADDITIONAL_COSMETIC ||
                    this.postData.typeId === GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT;
            },
            // 药品的一级分类
            primaryClassification() {

                const list = this.goodsPrimaryClassification.map((e) => {
                    return {
                        ...e,
                        name: this.transGoodsClassificationName(e.name),
                        id: +e.id,
                    };
                });
                // 重新建档运行全类型切换
                if (this.isRecreate) return list;

                if (this.isVirtualPharmacy) {
                    return list.filter((item) => {
                        // 虚拟库房只有中药
                        return item.goodsType === GoodsTypeEnum.MEDICINE && item.goodsSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine;
                    });
                }
                return list.filter((item) => {
                    if (this.postData.type) {
                        return item.goodsType === this.postData.type && this.supportGoodsTypeIdMap[item.id];
                    }
                    return true;
                });
            },
            // 颗粒机数据
            dispenseMachineList() {
                const list = this.goodsConfig.readOnlyConfig?.dispenseMachineList ?? [];
                if (list.length) {
                    return [
                        {
                            no: 0, name: '否',
                        },
                    ].concat(list);
                }

                return [
                    {
                        no: 0, name: '否',
                    },
                    {
                        no: 1, name: '是',
                    },
                ];
            },
            isHebeiArea() {
                return this.clinicBasicConfig.addressProvinceId.startsWith('13');
            },
            isShowDrugSupervisionInfo() {
                return this.viewDistributeConfig.Inventory.isSupportDrugAdministrationCenterCode &&
                    [GoodsTypeIdEnum.MEDICINE_WESTERN, GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT].includes(this.postData.typeId) &&
                    this.isHebeiArea;
            },
        },
        watch: {
            errorInfos() {
                this.$nextTick(this.validate);
            },
        },
        async created() {
            this.GoodsArchivesController.setReactiveCallback(this.onGoodsModelChange);

            // 第一次手动建立响应式
            this.reactive();

            // 利润分类数据
            this.$store.dispatch('fetchProfitClassificationList');
            // 一级分类数据
            this.$store.dispatch('fetchGoodsPrimaryClassificationIfNeed');
            // 初始化表单所需下拉数据等
            await this.GoodsArchivesController.initOptions();
            // 仅触发一次
            if (!this.isChainSubStore && !!this.postData.id && !this.postData?.shebao?.nationalCodeId) {
                this.fetchAutoShebaoCode('first');
            } else {
                this.GoodsArchivesController.updateInitShebaoCode();
            }
            if (this.isShowDrugSupervisionInfo && !this.isChainSubStore && !!this.postData.id && !this.postData?.centerCode) {
                this.fetchAutoCenterCode();
            }
            this.handleOptionsFinish();
            // 拉取智能发药机状态
            await this.fetchSmartDispense();
        },
        methods: {
            // 通知数据模型变化
            onGoodsModelChange() {
                this.reactive();
                console.log('onGoodsModelChange Index', this.postData);
                this.$emit('changeModel');
            },
            fetchAutoShebaoCode(type) {
                this.GoodsArchivesController.fetchAutoShebaoCode(type);
            },
            fetchAutoCenterCode(type) {
                this.GoodsArchivesController.fetchAutoCenterCode(type);
            },
            // 更新社保价格信息
            async updateShebaoPriceInfo(data) {
                console.log('updateShebaoPriceInfo', data);
                this._currentSocialInfo = data;
                // 有挂网价或者限价-支持医保就计算
                if ((data?.listingPrice || data?.priceLimit) && (this.isHaveClinicOpen || this.$abcSocialSecurity.isOpenSocial)) {
                    this.shebaoPriceInfo = await this.GoodsArchivesController.transShebaoPrice(data);
                } else {
                    this.shebaoPriceInfo = null;
                }
            },
            // 更新postData的唯一方式
            updateGoodsInfo(key, val) {
                this.GoodsArchivesController.updateGoodsInfo(key, val);
            },
            blurInput(key) {
                this.$nextTick(() => {
                    this.GoodsArchivesController.blurInput(key);
                    this.updateShebaoPriceInfo(this._currentSocialInfo);
                });
            },
            // 为模型数据建立响应式
            reactive() {
                this.postData = this.GoodsArchivesController.getGoodsInfo();
            },
            handleOptionsFinish() {
                this.instructionsCategoryMap = this.GoodsArchivesController.getInstructionsCategoryMap();
                this.feeTypesList = this.GoodsArchivesController.getFeeTypesList();
                this.feeCategoryList = this.GoodsArchivesController.getFeeCategoryList();
            },
            handleUpdateProfitTypes() {
                this.$store.dispatch('fetchProfitClassificationList');
            },
            // 是否开启智能发药机
            async fetchSmartDispense() {
                try {
                    const { data } = await API.getV3('dispensing.smartDispensing', 'clinic') || {};
                    this.isOpenSmartDispensing = !!(data?.isSmartDispensing ?? 0);
                } catch (e) {
                    console.error(e);
                }
            },
            // 校验表单
            validate(...args) {
                this.$refs.form.validate(...args);
            },
        },
    };
</script>
