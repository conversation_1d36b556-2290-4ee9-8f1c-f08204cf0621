
import {
    typeIdMap,
    PriceType,
    SubPriceFlag,
    SubPriceMode,
} from 'views/common/inventory/constants';
import Units from 'src/assets/configure/western-medicine-config';

import { isChineseMedicine } from '@/filters';
import {
    GoodsTypeEnum, GoodsTypeIdEnum,
} from '@abc/constants';

export default class BaseModel {
    constructor(obj = {}) {
        // 表单数据，只能在本服务中修改
        this.goods = {
            id: obj.id,
            disable: obj.disable,
            v2DisableStatus: obj.v2DisableStatus,
            // 发起gsp审批流
            gspReviewStarted: obj.gspReviewStarted,
            // 类型
            type: obj.type,
            // 子类型
            subType: obj.subType,
            // 一级分类ID
            typeId: obj.typeId,
            // 二级分级ID
            customTypeId: obj.customTypeId,
            // 生产厂家
            manufacturerFull: obj.manufacturerFull,
            // 包装单位
            packageUnit: obj.packageUnit,
            // 最小包装数量
            pieceNum: obj.pieceNum,
            // 最小包装单位
            pieceUnit: obj.pieceUnit,
            // 编码
            shortId: obj.shortId,
            // 条码
            barCode: obj.barCode,
            // 柜号
            position: obj.position,

            // *定价相关
            // ?定价设置
            individualPricingType: obj.individualPricingType ?? SubPriceMode.FOLLOW,
            // ?定价方式
            subClinicPriceFlag: obj.subClinicPriceFlag ?? SubPriceFlag.UNIFY_FIXED_PRICE,
            // !定价模式 1固定售价 3进价加成
            priceType: obj.priceType ?? PriceType.PRICE,
            // !加成率
            priceMakeupPercent: obj.priceMakeupPercent,
            // 售价
            packagePrice: obj.packagePrice,
            // 进价
            packageCostPrice: obj.packageCostPrice,
            // 拆零售价
            piecePrice: obj.piecePrice,
            // 总部售价
            chainPackagePrice: obj.chainPackagePrice,
            // 总部拆零售价
            chainPiecePrice: obj.chainPiecePrice,
            // 上次的固定售价
            fixedPackagePrice: obj.fixedPackagePrice,
            fixedPiecePrice: obj.fixedPiecePrice,
            // 最大与最小的批次进价-后端动态算的，前端用于计算售价展示
            maxPackageCostPrice: obj.maxPackageCostPrice,
            minPackageCostPrice: obj.minPackageCostPrice,
            // 最大与最小的大单位售价
            maxPackagePrice: obj.maxPackagePrice,
            minPackagePrice: obj.minPackagePrice,
            // 最近进价
            lastPackageCostPrice: obj.lastPackageCostPrice,
            // 平均进价
            avgPackageCostPrice: obj.avgPackageCostPrice,
            // 是否拆零
            dismounting: obj.dismounting ?? 0,
            // 进项税率
            inTaxRat: obj.inTaxRat,
            // 销项税率
            outTaxRat: obj.outTaxRat,
            // 使用默认税率
            defaultInOutTax: obj.defaultInOutTax ?? 1,
            // !利润分类
            profitCategoryType: obj.profitCategoryType,
            // 费用类型
            feeTypeId: obj.feeTypeId,
            // 病案首页费目
            feeCategoryId: obj.feeCategoryId,
            // *医保相关
            shebao: obj.shebao ?? {
                standardCode: null,
                nationalCode: null,
                nationalCodeId: null,
                waitHandleNationalCode: null,
                waitHandleNationalCodeId: null,
                detailType: 'default',
                searchLoading: false,
                matchMode: null,
                cleanMatchCodeFlag: 0,
                initShebaoCode: false,
            },
            // 医保对码
            shebaoPayMode: obj.shebaoPayMode,

            // 所属经营范围
            businessScopeList: obj.businessScopeList,
            // 备注
            remark: obj.remark,
            // 商品标签
            goodsTagList: obj.goodsTagList,
            goodsTagIdList: obj.goodsTagIdList,
            // 商品多定价-目前有会员价相关的
            multiPriceList: obj.multiPriceList || [],
            // 毛利率
            profitRat: obj.profitRat,
            // 库存量
            stockPackageCount: obj.stockPackageCount,
            stockPieceCount: obj.stockPieceCount,
            // type: 0-套餐和组合项 10-费用项 20-采样组 30-关联项-耗材 40-商品组子项
            compositeGoodsComposeList: obj.compositeGoodsComposeList || [],
            sanMaHeYiIdentificationCodeList: obj.sanMaHeYiIdentificationCodeList ?? [],
        };
    }

    getGoods() {
        return this.goods;
    }

    get isChineseMedicineType() {
        return isChineseMedicine(this.goods);
    }

    setGoods(key, val) {
        // 只更新有的字段
        if (this.goods.hasOwnProperty(key)) {
            this.goods[key] = val;
        }
    }

    updateGoods(key, val) {
        // 纯更新字段
        if (this.goods.hasOwnProperty(key)) {
            this.goods[key] = {
                ...this.goods[key],
                ...val,
            };
        }
    }

    // 仅子类调用，防止加入不合法的key
    assignGoods(object = {}) {
        this.formatGoodsUnit(Object.assign(this.goods, object));
    }
    // 对合并后的goods做单位校验，特殊情况将清除单位数据
    formatGoodsUnit(goods) {
        // 获取一级分类
        const type = typeIdMap[goods.typeId];
        let {
            packageUnit, pieceUnit, medicineDosageUnit, componentContentUnit,
        } = goods;

        // 药品
        if (type === GoodsTypeEnum.MEDICINE) {
            // 中药
            if (goods.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES || goods.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE) {
                // 不符合系统内置单位
                if (!Units.chpUnit.find((item) => item.name === pieceUnit)) {
                    pieceUnit = '';
                }
            } else {
                // 不符合系统内置单位
                if (!Units.dosageFormUnit.find((item) => item.name === packageUnit)) {
                    packageUnit = '';
                }
                if (!Units.dosageFormUnit.find((item) => item.name === pieceUnit)) {
                    pieceUnit = '';
                }
                if (!Units.dosageUnit.find((item) => item.name === medicineDosageUnit)) {
                    medicineDosageUnit = '';
                }
                if (!Units.dosageUnit.find((item) => item.name === componentContentUnit)) {
                    componentContentUnit = '';
                }
            }
        }
        // 物资与商品
        if (type === GoodsTypeEnum.MATERIAL || type === GoodsTypeEnum.GOODS) {
            // 不符合系统内置单位
            if (!Units.materialUnit.find((item) => item.name === packageUnit)) {
                packageUnit = '';
            }
            if (!Units.materialUnit.find((item) => item.name === pieceUnit)) {
                pieceUnit = '';
            }
        }

        this.setGoods('medicineDosageUnit', medicineDosageUnit);// 成分含量单位
        this.setGoods('componentContentUnit', componentContentUnit);// 容量单位
        this.setGoods('packageUnit', packageUnit);// 大包装单位
        this.setGoods('pieceUnit', pieceUnit);// 小包装单位

    }
}
