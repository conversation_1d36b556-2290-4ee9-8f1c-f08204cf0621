import { GoodsTypeIdEnum } from '@abc/constants';
import {
    ChineseMedicineGranuleModel,
    ChineseMedicinePiecesModel,
    NonPrescriptionChineseMedicinePiecesModel,
    ChinesePatentModel,
    CosmeticModel,
    DisinfectantModel,
    FixedAssetsModel,
    HealthFoodModel,
    HealthMedicineModel,
    LogisticalMaterialModel,
    MedicalMaterialModel,
    OtherProductModel,
    SelfProductModel,
    WesternMedicineModel,
} from './model/index';

// 各类型对应数据模型关系
export const modelMap = Object.freeze({
    [GoodsTypeIdEnum.MEDICINE_WESTERN]: WesternMedicineModel,
    [GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES]: NonPrescriptionChineseMedicinePiecesModel,
    [GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES]: ChineseMedicinePiecesModel,
    [GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE]: ChineseMedicineGranuleModel,
    [GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT]: ChinesePatentModel,

    [GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL]: MedicalMaterialModel,
    [GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL]: LogisticalMaterialModel,
    [GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS]: FixedAssetsModel,
    [GoodsTypeIdEnum.MATERIAL_DISINFECTANT]: DisinfectantModel,

    [GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT]: SelfProductModel,
    [GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE]: HealthMedicineModel,
    [GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD]: HealthFoodModel,
    [GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT]: OtherProductModel,
    [GoodsTypeIdEnum.ADDITIONAL_COSMETIC]: CosmeticModel,
});

// 价格体系
export const TargetType = Object.freeze({
    // 普通价格
    normal: 0,
    // 体检合作药房价格
    physicalExamination: 1,
    // 折扣价格-会员价
    vip: 10,
});

// 价格体系
export const OpType = Object.freeze({
    // 新增
    add: 0,
    // 修改
    update: 1,
    // 删除
    delete: 2,
});
// 会员价应用的类型
export const DiscountTypes = Object.freeze({
    // 单独设置特价
    special: 1,
    // 单独设置折扣
    discount: 0,
    // 跟随会员设置
    no: 10,
});

// 跟随会员设置区分，分类/商品组
export const DiscountGoodsType = Object.freeze({
    // 分类
    type: 1,
    // 商品组
    goods: 4,
});
// 跟随商品组类型细分，商品组折扣/商品组特价
export const DiscountGoodsDiscountType = Object.freeze({
    // 商品组特价
    special: 1,
    // 商品组折扣
    discount: 0,
});
