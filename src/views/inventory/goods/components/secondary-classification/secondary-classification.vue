<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        class="secondary-classification-dialog"
        content-styles="width:300px;min-width:300px;padding:0;max-height: 276px;min-height:180px"
        :title="title"
        :auto-focus="false"
        append-to-body
    >
        <abc-form ref="postForm">
            <draggable
                v-model="currentOptions"
                v-bind="{
                    animation: 150,
                    handle: '.cis-icon-move_item',
                }"
                @update="sortChange"
            >
                <transition-group name="classification-moving">
                    <div
                        v-for="(item, index) in currentOptions"
                        :key="`${item.id }${ index }`"
                        class="option-item"
                        :class="{
                            'editing-option-item': index === currentIndex,
                            'hover-option-item': hoverIndex === index && currentIndex === -1,
                            'not-editing-options': index !== currentIndex && currentIndex !== -1,
                        }"
                        @mouseenter="changeHoverIndex(index)"
                    >
                        <div class="icon-wrapper">
                            <i v-if="currentIndex === -1" class="iconfont cis-icon-move_item"></i>
                        </div>
                        <abc-form-item
                            :validate-event="validateName"
                            laebl="''"
                            style="margin: 0;"
                            required
                        >
                            <abc-input
                                v-model.trim="item.name"
                                :readonly="index !== currentIndex"
                                :width="168"
                                class="input-wrapper"
                                :class="{ 'is-editing': index === currentIndex }"
                                :max-length="maxLength"
                                :input-custom-style="{ background: 'transparent' }"
                            >
                            </abc-input>
                        </abc-form-item>
                        <div class="op-btn">
                            <template v-if="index === currentIndex">
                                <abc-button variant="text" size="small" @click.stop="confirmUpdate(item, index)">
                                    确定
                                </abc-button>
                                <abc-button
                                    variant="text"
                                    size="small"
                                    theme="default"
                                    @click="cancelItem(item, index)"
                                >
                                    取消
                                </abc-button>
                            </template>
                            <template v-if="currentIndex === -1 && index === hoverIndex && !disabledTypes.includes(item.id)">
                                <abc-button variant="text" size="small" @click.stop="reviseItem(index, item)">
                                    修改
                                </abc-button>
                                <delete-confirm @confirm="deleteItem(index, item)">
                                    <abc-button variant="text" size="small" theme="danger">
                                        删除
                                    </abc-button>
                                </delete-confirm>
                            </template>
                        </div>
                    </div>
                </transition-group>
            </draggable>
        </abc-form>

        <div slot="footer">
            <abc-button
                variant="text"
                size="small"
                icon="plus_thin"
                :disabled="currentIndex !== -1"
                @click="addItem"
            >
                新增
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import Draggable from 'vuedraggable';
    import clone from 'utils/clone';
    import { mapGetters } from 'vuex';

    import GoodsAPI from 'api/goods/index';
    import { GoodsTypeIdEnum } from 'views/inventory/constant';
    import DeleteConfirm from 'components/index/delete-confirm.vue';
    import TipTemplate from 'views/layout/goods-pre-operate-check/index.vue';

    export default {
        name: 'SecondaryClassification',
        components: {
            DeleteConfirm,
            Draggable,
        },
        props: {
            value: {
                type: Boolean,
                required: true,
            },
            typeId: {
                type: [Number, String],
                required: true,
            },
            options: {
                type: Array,
                required: true,
            },
            onSubmit: {
                type: Function,
            },
            supplierId: {
                type: String,
            },
            maxLength: {
                type: Number,
                default: 10,
            },
        },
        data() {
            return {
                disabledTypes: ['200', '100'],
                addOptions: [],
                currentIndex: -1,
                hoverIndex: -1,
                currentItem: null,
                currentOptions: [],
                delOptions: [],
            };
        },
        computed: {
            ...mapGetters(['primaryClassificationMap']),
            showDialog: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
            typeInfo() {
                return this.primaryClassificationMap.get(this.typeId?.toString()) || {};
            },
            title() {
                return `${this.typeId === GoodsTypeIdEnum.DENTURE_PROCESSING ? this.typeInfo.name : `${this.typeInfo.name}二级`}分类`;
            },
        },
        watch: {
            options: {
                handler () {
                    this.currentOptions = clone(this.options);
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            changeHoverIndex(index) {
                this.hoverIndex = index;
            },
            /**
             * @desc 获取二级分类
             * <AUTHOR>
             * @date 2020-07-13 11:05:45
             */
            fetchCustomTypes() {},
            /**
             * @desc 移开之后，点开的删除要销毁
             * <AUTHOR>
             * @date 2020-07-13 10:52:15
             */
            reviseItem(index, item) {
                this.currentIndex = index;
                this.currentItem = clone(item);
                this.focusInput();
            },
            /**
             * @desc 取消当前正在编辑的内容
             */
            cancelItem(item, index) {
                this.currentIndex = -1;
                if (this.currentItem && this.currentItem.id) {
                    this.currentOptions[index].name = this.currentItem.name;
                } else {
                    // 新增之后点击取消直接删除该项
                    if (!item.id) {
                        this.currentOptions.splice(index, 1);
                    }
                }
                this.currentItem = null;
            },
            /**
             * @desc
             * <AUTHOR>
             * @date 2020-07-13 11:46:07
             * @params
             * @return
             */
            validateName(val, callback) {
                if (val && val.trim()) {
                    callback({
                        validate: true,
                    });
                } else {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
            },
            /**
             * @desc 确定修改内容
             */
            async confirmUpdate(item, index) {
                this.$refs.postForm.validate(async (val) => {
                    if (val) {
                        if (!item.id) {
                            await this.updateHandler(index);
                        } else {
                            const count = await this.fetchCount(item.id);
                            if (count) {
                                this.$confirm({
                                    type: 'warn',
                                    title: '提示',
                                    content: `有${count}个${this.typeInfo.name}属于该二级分类，修改后${this.typeInfo.name}所属分类名称将同步修改。是否确定修改？`,
                                    onConfirm: async () => {
                                        await this.updateHandler(index);
                                    },
                                });
                            } else {
                                await this.updateHandler(index);
                            }
                        }
                    }
                });
            },
            sortChange({ newIndex }) {
                this.saveHandler(newIndex);
                this.currentItem = null;
                this.currentIndex = -1;
                this.hoverIndex = newIndex;
            },
            async updateHandler(index) {
                this.saveHandler(index);
                this.currentIndex = -1;
                this.currentItem = null;
            },
            async deleteItem(index, item) {
                const count = await this.fetchCount(item.id);
                if (count) {
                    if (this.typeId === GoodsTypeIdEnum.DENTURE_PROCESSING) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '分类内有项目不能删除，需先移动或清空后删除分类',
                        });
                    } else {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: `有${count}个${this.typeInfo.name}属于该二级分类，删除后将一同清空${this.typeInfo.name}的分类。是否确定删除？`,
                            onConfirm: async () => {
                                await this.deleteHandler(index);
                            },
                        });
                    }
                } else {
                    await this.deleteHandler(index);
                }
            },
            transPostData() {
                const postData = [];
                this.currentOptions.concat(this.delOptions).forEach((item) => {
                    if (item.name && item.name.trim()) {
                        const obj = {
                            name: item.name,
                            id: item.id || '',
                        };
                        if (item.isDeleted) {
                            obj.isDeleted = 1;
                        }
                        postData.push(obj);
                    }
                });
                return postData;
            },
            async deleteHandler(index) {
                const delItem = this.currentOptions[index];
                // 删除的项存起来，发给后端
                delItem.isDeleted = true;
                this.delOptions.push(delItem);
                await this.saveHandler(
                    index,
                    { customErrorTips: true },
                    () => {
                        this.currentOptions.splice(index, 1);
                        this.currentIndex = -1;
                        this.currentItem = null;
                    },
                );
            },
            /**
             * @desc 新增一个二级分类
             * <AUTHOR>
             * @date 2020-07-13 11:31:21
             */
            addItem() {
                const index = this.currentOptions.findIndex((item) => {
                    return !item.id;
                });
                if (index === -1) {
                    this.currentOptions.push({
                        id: '',
                        name: '',
                    });
                    this.currentItem = {
                        id: '',
                        name: '',
                    };
                }
                this.currentIndex = this.currentOptions.length - 1;
                this.hoverIndex = this.currentOptions.length - 1;
                this.focusInput();
            },
            focusInput() {
                this.$nextTick(() => {
                    const editInput = $('.option-item .is-editing .abc-input__inner');
                    editInput && editInput.focus();
                });
            },
            /**
             * @desc 提交二级分类结果
             * @param {number} index 报错之后需要聚焦到当前的input
             * @param {object} requestConfig 请求配置
             * @param {function} successCallback 请求成功后的回调
             */
            async saveHandler(index, requestConfig = {}, successCallback) {
                const postData = {
                    supplierId: this.supplierId,
                    typeId: this.typeId,
                    customTypes: this.transPostData(),
                };

                try {
                    const { data } = await GoodsAPI.updateSecondaryClassification(postData, requestConfig);
                    this.$emit('change', data.rows || data.list || []);
                    if (this.typeId === GoodsTypeIdEnum.DENTURE_PROCESSING) {
                        this.onSubmit();
                        this.currentOptions = data?.rows || data?.list || [];
                    }
                    // 提交成功后，清空删除的项
                    this.delOptions = [];

                    successCallback && successCallback();
                } catch (e) {
                    const deletedItem = postData.customTypes.find((item) => item.isDeleted);

                    this.handleErrorTip(e, index, deletedItem);
                }
            },
            async fetchCount(id) {
                try {
                    const { data } = await GoodsAPI.fetchCustomTypesCount(id);
                    return data.count;
                } catch (e) {
                    console.log(e);
                }
            },

            handleErrorTip(e, index, deletedItem) {
                if (e.code === 12802) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: e.message,
                        onClose: () => {
                            this.currentIndex = index;
                            this.focusInput();
                        },
                    });
                }

                if (e.code === 12821) {
                    const usingItems = (e.detail || []).reduce((acc, item) => {
                        if (item.isUsed) {
                            acc.push(...(item.usingItems || []));
                        }

                        return acc;
                    }, []);

                    const goodsApplyingList = usingItems.map((item) => ({
                        path: item.typeName,
                        content: item.promotionName,
                    }));

                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        size: 'medium',
                        content: () => {
                            return (
                                <TipTemplate
                                    goodsApplyingList={goodsApplyingList}
                                    goodsName={deletedItem?.name}
                                ></TipTemplate>
                            );
                        },
                    });
                }
            },
        },
    };
</script>
