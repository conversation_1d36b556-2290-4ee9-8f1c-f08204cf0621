<template>
    <abc-dropdown
        ref="downloadRef"
        :style="dropdownStyle"
        placement="bottom-end"
        @change="handleChange"
    >
        <abc-tooltip
            slot="reference"
            :z-index="9999"
            placement="top"
            theme="black"
            size="small"
            :disabled="canAddProduct"
            :content="contentText"
        >
            <abc-flex align="center">
                <abc-text theme="gray">
                    没有找到商品？
                </abc-text>
                <abc-button
                    variant="text"
                    size="small"
                    :disabled="!canAddProduct"
                    v-bind="buttonConfig"
                >
                    新建档案
                </abc-button>
            </abc-flex>
        </abc-tooltip>

        <abc-dropdown-item :value="GoodsTypeEnum.MEDICINE">
            新建药品
        </abc-dropdown-item>
        <abc-dropdown-item :value="GoodsTypeEnum.MATERIAL">
            新建{{ materialText }}
        </abc-dropdown-item>
        <abc-dropdown-item :value="GoodsTypeEnum.GOODS">
            新建商品
        </abc-dropdown-item>
    </abc-dropdown>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { GoodsTypeEnum } from '@abc/constants';
    import AddGoodsArchivesDialog from 'views/inventory/goods/archives/dialog';
    import useClinicStorage, {
        CustomKeys,
    } from '@/hooks/business/use-clinic-storage';

    export default {
        name: 'AddArchiveDropdown',
        props: {
            buttonConfig: {
                type: Object,
                default: () => ({}),
            },
            dropdownStyle: {
                type: Object,
                default: () => ({
                    'width': '164px',
                    'min-width': '164px',
                }),
            },
            isCustomChange: {
                type: Boolean,
                default: false,
            },
            successCallback: {
                type: Function,
            },
            isShowToast: {
                type: Boolean,
                default: true,
            },
            goodsInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        setup() {
            const {
                getStorage,
            } = useClinicStorage();

            return {
                getStorage,
            };
        },
        computed: {
            ...mapGetters([
                'modulePermission',
                'isChainSubStore',
                'isCanCreateGoodsArchivesInInventory',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            GoodsTypeEnum() {
                return GoodsTypeEnum;
            },
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            defaultGoodsTypeMap() {
                return this.viewDistributeConfig.Inventory.defaultGoodsTypeMap;
            },
            canAddArchiveByHasHospitalSupplyCenterGoodsModule() {
                return this.viewDistributeConfig.Inventory.canAddArchiveByHasHospitalSupplyCenterGoodsModule;
            },
            materialText() {
                return this.viewDistributeConfig.Inventory.materialText;
            },
            contentText() {
                // 药店管家逻辑
                if (this.operateGoodsArchives) {
                    if (!this.canAddProduct) {
                        return this.isChainSubStore ? '请联系总部开启权限' : '无建档权限，可在 [设置-信息安全] 中设置';
                    }
                    return '';
                }

                // 诊所管家逻辑
                if (!this.canAddProduct) {
                    return this.isChainSubStore ? '请联系总部创建药品物资档案' : '无创建药品物资档案权限';
                }

                return '';
            },
            canAddProduct() {
                // 不用判断门店类型，子店现在也支持建档
                if (this.operateGoodsArchives) {
                    return this.isCanCreateGoodsArchivesInInventory;
                }
                // 没开启operateGoodsArchives权限控制的，还是走诊所管家的老逻辑判断模块权限与门店类型。
                const hasGoodsModule = this.canAddArchiveByHasHospitalSupplyCenterGoodsModule ?
                    this.modulePermission.hasHospitalSupplyCenterGoodsModule :
                    this.modulePermission.hasGoodsModule;

                return hasGoodsModule && !this.isChainSubStore;
            },
        },
        methods: {
            handleSuccess(goods) {
                if (this.isShowToast) {
                    this.$Toast({
                        type: 'success',
                        message: '新建成功',
                    });
                }
                this._AddGoodsArchivesDialog?.destroyDialog();

                if (this.successCallback) {
                    this.successCallback(goods);
                }
            },
            handleChange(value) {
                this.$emit('change', value);

                if (!this.isCustomChange) {
                    this.openAddArchiveDialog(value);
                }
            },
            openAddDialog() {
                this.$emit('openAddDialog');
            },
            closeAddDialog() {
                this.$emit('closeAddDialog');
            },
            openAddArchiveDialog(type) {
                // 获取用户上次建档类型
                const typeId = this.getStorage(CustomKeys.USER_GOODS_TYPE, type);

                this._AddGoodsArchivesDialog = new AddGoodsArchivesDialog({
                    isContinue: false,
                    typeId: typeId || this.defaultGoodsTypeMap[type],
                    goodsInfo: this.goodsInfo,
                    successFn: this.handleSuccess,
                    openAddDialog: this.openAddDialog,
                    closeAddDialog: this.closeAddDialog,
                });
                this._AddGoodsArchivesDialog?.generateDialogAsync({
                    parent: this,
                });

                this.$on('hook:beforeDestroy',() => {
                    this._AddGoodsArchivesDialog?.destroyDialog();
                    this._AddGoodsArchivesDialog = null;
                });
            },
        },
    };
</script>
