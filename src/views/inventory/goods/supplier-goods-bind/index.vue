<template>
    <abc-layout preset="dialog-table" style="padding: 16px 24px 24px;">
        <abc-layout-header>
            <abc-flex
                flex="1"
                align="center"
                justify="space-between"
                gap="large"
            >
                <abc-tips-card-v2
                    theme="primary"
                    border-radius
                    title="用于供应商ERP档案与ABC系统档案的绑定关系管理，建立绑定关系后可一键收货。"
                >
                </abc-tips-card-v2>


                <abc-tooltip placement="top" :disabled="canEdit" content="请联系总部新增">
                    <abc-button
                        theme="primary"
                        variant="fill"
                        style="min-width: 82px;"
                        :disabled="!canEdit"
                        @click="onClick"
                    >
                        新增绑定
                    </abc-button>
                </abc-tooltip>
            </abc-flex>
        </abc-layout-header>

        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                :loading="loading"
                :render-config="tableConfig"
                :data-list="tableData"
                empty-size="small"
                :custom-tr-key="customTrKey"
                :pagination="tablePagination"
                :need-selected="false"
                :show-hover-tr-bg="false"
                @pageChange="pageTo"
            >
                <template #operate="{ trData: row }">
                    <abc-table-cell>
                        <abc-button
                            variant="text"
                            size="small"
                            :disabled="!canEdit"
                            @click="handleEdit(row)"
                        >
                            编辑
                        </abc-button>
                        <delete-confirm @confirm="handleConfirm(row)">
                            <abc-button
                                variant="text"
                                size="small"
                                :disabled="!canEdit"
                            >
                                删除
                            </abc-button>
                        </delete-confirm>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <bind-goods-dialog
            v-if="showBindGoodsDialog"
            v-model="showBindGoodsDialog"
            :goods="goods"
            :bound-supplier-goods="currentRow"
            @bind-success="fetchData"
        ></bind-goods-dialog>
    </abc-layout>
</template>
<script>
    import GoodsAPIV3 from 'api/goods/index-v3';

    import BindGoodsDialog from 'views/inventory/goods/supplier-goods-bind/dialog.vue';
    import DeleteConfirm from 'components/index/delete-confirm.vue';
    import { mapGetters } from 'vuex';

    export default {
        name: 'Index',
        components: {
            DeleteConfirm, BindGoodsDialog,
        },
        props: {
            goods: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                loading: true,
                showBindGoodsDialog: false,
                currentRow: null,
                fetchParams: {
                    limit: 10,
                    offset: 0,
                },
                panelData: {
                    rows: [],
                    total: 0,
                },
            };
        },
        computed: {
            ...mapGetters(['isAdmin', 'isCanCreateGoodsArchivesInInventory']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            canEdit() {
                if (this.operateGoodsArchives && !this.isCanCreateGoodsArchivesInInventory) {
                    return false;
                }
                return this.isAdmin;
            },
            tableConfig() {
                const list = [
                    {
                        label: '供应商',
                        key: 'supplierName',
                        style: {
                            width: '180px',
                            minWidth: '180px',
                            maxWidth: '180px',
                        },
                    },
                    {
                        label: '商品名称',
                        key: 'name',
                        style: {
                            width: '240px',
                            minWidth: '240px',
                            maxWidth: '240px',
                        },
                    },
                    {
                        label: '规格',
                        key: 'displaySpec',
                        style: {
                            flex: 1,
                            minWidth: '124px',
                        },
                    },
                    {
                        label: '生产厂家',
                        key: 'manufacturerFull',
                        style: {
                            width: '180px',
                            minWidth: '180px',
                            maxWidth: '180px',
                        },
                    },
                    {
                        label: '批准文号',
                        key: 'medicineNmpn',
                        style: {
                            width: '150px',
                            minWidth: '150px',
                            maxWidth: '150px',
                        },
                    },
                    {
                        label: 'ERP商品编码',
                        key: 'supplierGoodsId',
                        style: {
                            width: '108px',
                            minWidth: '108px',
                            maxWidth: '108px',
                        },
                    },
                    {
                        label: '操作',
                        key: 'operate',
                        slot: true ,
                        style: {
                            width: '124px',
                            minWidth: '124px',
                            maxWidth: '124px',
                            textAlign: 'center',
                        },
                    },
                ];
                return {
                    hasInnerBorder: false,
                    list,
                };
            },
            tableData() {
                return this.panelData.rows || [];
            },
            tableTotal() {
                return this.panelData.total | 0;
            },
            tablePagination() {
                return {
                    count: this.tableTotal,
                    limit: this.fetchParams.limit,
                    offset: this.fetchParams.offset,
                    showTotalPage: true,
                };
            },
        },
        methods: {
            handleMounted(data) {
                console.log('handleMounted', data);
                this.fetchParams.limit = data.paginationLimit;
                this.fetchData();
            },
            async fetchData() {
                this.loading = true;
                try {
                    const res = await GoodsAPIV3.getSupplierGoodsList(this.goods.id, this.fetchParams);
                    this.panelData = res?.data || {
                        rows: [],
                        total: 0,
                    };
                } catch (error) {
                    console.error(error);
                } finally {
                    this.loading = false;
                }
            },
            pageTo(page) {
                this.fetchParams.offset = (page - 1) * this.fetchParams.limit;
                this.fetchData();
            },
            onClick() {
                this.showBindGoodsDialog = true;
                this.currentRow = null;
            },
            handleEdit(row) {
                console.log('handleEdit',row);
                this.showBindGoodsDialog = true;
                this.currentRow = row;
            },
            async handleConfirm(row) {
                console.log('handleConfirm', row);
                try {
                    await GoodsAPIV3.bindSupplierGoods({
                        opType: 2,
                        goodsId: this.goods.id,
                        supplierId: row.supplierId,
                        supplierGoodsId: row.supplierGoodsId,
                    });
                    this.$Toast.success('删除成功');
                    this.fetchData();
                } catch (e) {
                    // 防止重复提示
                    if (!e.alerted) {
                        this.$Toast({
                            message: e.message,
                            type: 'error',
                        });
                    }
                }
            },
            customTrKey(row) {
                return row.supplierGoodsId;
            },
        },
    };
</script>
