<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="确认打印价签"
        append-to-body
        size="hugely"
        :auto-focus="false"
    >
        <abc-layout preset="dialog-table">
            <abc-layout-header>
                <!--商品搜索-->
                <goods-auto-complete
                    ref="autoComplete"
                    :auto-focus-first="false"
                    :clear-search-key="false"
                    :clinic-id="clinicId"
                    :pharmacy-no="pharmacyNo"
                    :search.sync="currentKey"
                    :width="180"
                    :with-stock="false"
                    class="abc-autocomplete-search"
                    clearable
                    focus-show
                    placeholder="商品名称/条码"
                    @clear="clearSearch"
                    @selectGoods="selectGoods"
                >
                    <abc-search-icon slot="prepend"></abc-search-icon>
                </goods-auto-complete>
            </abc-layout-header>
            <abc-layout-content>
                <abc-layout>
                    <abc-layout-content>
                        <abc-table
                            type="excel"
                            :loading="loading"
                            :render-config="renderConfig"
                            :data-list="dataList"
                            :pagination="pageParams"
                            style="min-height: 496px;"
                            @pageChange="changePage"
                        >
                            <template #packagePriceType="{ trData: item }">
                                <abc-select
                                    v-if="isEnableSelectPrice(item)"
                                    v-model="item.printPriceType"
                                    style="width: 100%;"
                                    :inner-width="120"
                                    @change="handleChangeItem(item)"
                                >
                                    <abc-option label="整装售价" :value="PrintPriceTypeEnum.PACKAGE"></abc-option>
                                    <abc-option label="拆零售价" :value="PrintPriceTypeEnum.PIECE"></abc-option>
                                </abc-select>
                                <abc-table-cell v-else data-cy="package-price-type-text">
                                    整装售价
                                </abc-table-cell>
                            </template>

                            <template #price="{ trData: item }">
                                <abc-select
                                    v-if="item.modifyPriceTipsView"
                                    :show-value="getPriceAndUnitWithModifyPriceTipsViewText(item)"
                                    :width="120"
                                    :max-width="300"
                                    placeholder="选择售价"
                                    placement="bottom-end"
                                    data-cy="price-select"
                                    @change="v => handleChangeItem(item, v)"
                                >
                                    <abc-option v-if="getBeforePriceText(item)" :value="SalePriceTypeEnum.BEFORE" data-cy="before-price-option">
                                        <abc-flex
                                            :gap="24"
                                            align="center"
                                            justify="space-between"
                                            style="width: 100%;"
                                        >
                                            <abc-text data-cy="before-price-text">
                                                {{ getBeforePriceText(item) }}
                                            </abc-text>
                                            <abc-text size="small" theme="gray" data-cy="before-price-label">
                                                当前售价
                                            </abc-text>
                                        </abc-flex>
                                    </abc-option>
                                    <abc-option :value="SalePriceTypeEnum.AFTER" data-cy="after-price-option">
                                        <abc-flex
                                            :gap="24"
                                            align="center"
                                            justify="space-between"
                                            style="width: 100%;"
                                        >
                                            <abc-text data-cy="after-price-text">
                                                {{ getAfterPriceText(item) }}
                                            </abc-text>
                                            <abc-text size="small" theme="gray" data-cy="after-price-label">
                                                {{
                                                    formatDate(item.modifyPriceTipsView.effected, 'YYYY-MM-DD')
                                                }} 生效售价
                                            </abc-text>
                                        </abc-flex>
                                    </abc-option>
                                </abc-select>

                                <abc-table-cell v-else data-cy="price-text">
                                    {{ getPriceAndUnitWithModifyPriceTipsViewText(item) }}
                                </abc-table-cell>
                            </template>

                            <template v-for="memberLevel in printMemberLevelList" #[`${memberLevel.tableKey}`]="{ trData: item }">
                                <abc-table-cell v-if="getMemberLevelStaticStr(item, memberLevel).flag" :key="`${memberLevel.id}`" data-cy="member-price-text">
                                    {{ getMemberLevelStaticStr(item, memberLevel).price }}
                                </abc-table-cell>

                                <abc-select
                                    v-else
                                    :key="`${memberLevel.id}`"
                                    :show-value="getMemberAfterPrice(item, memberLevel).flag ? getMemberAfterPrice(item, memberLevel).price : ''"
                                    :width="120"
                                    :max-width="300"
                                    placeholder="选择会员价"
                                    placement="bottom-end"
                                    data-cy="member-price-select"
                                    @change="(v) => handleMemberPriceTypeChangeItem(item, v, memberLevel)"
                                >
                                    <abc-option :value="SalePriceTypeEnum.BEFORE" data-cy="member-before-price-option">
                                        <abc-flex
                                            :gap="24"
                                            align="center"
                                            justify="space-between"
                                            style="width: 100%;"
                                        >
                                            <abc-text data-cy="member-before-price-text">
                                                {{ getMemberBeforePriceText(item, memberLevel).price }}
                                            </abc-text>
                                            <abc-text size="small" theme="gray" data-cy="member-before-price-label">
                                                当前售价
                                            </abc-text>
                                        </abc-flex>
                                    </abc-option>
                                    <abc-option :value="SalePriceTypeEnum.AFTER" data-cy="member-after-price-option">
                                        <abc-flex
                                            :gap="24"
                                            align="center"
                                            justify="space-between"
                                            style="width: 100%;"
                                        >
                                            <abc-text data-cy="member-after-price-text">
                                                {{ getMemberAfterPriceText(item, memberLevel).price }}
                                            </abc-text>
                                            <abc-text
                                                v-if="getMemberAfterPriceDate(item, memberLevel)"
                                                size="small"
                                                theme="gray"
                                                data-cy="member-after-price-label"
                                            >
                                                {{
                                                    getMemberAfterPriceDate(item, memberLevel)
                                                }} 生效售价
                                            </abc-text>
                                        </abc-flex>
                                    </abc-option>
                                </abc-select>
                            </template>
                        </abc-table>
                    </abc-layout-content>
                </abc-layout>
            </abc-layout-content>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button :loading="batchLoading" @click="handleClickBatchPrint">
                打印
            </abc-button>
            <abc-button
                variant="ghost"
                @click="handleClickCancel"
            >
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import GoodsAPI from 'api/goods/index';
    import { PriceTagPrintApi } from '@/printer/print-api/price-tag';
    import { isChineseMedicine } from '@/filters';
    import { mapGetters } from 'vuex';
    import localStorage from 'utils/localStorage-handler';
    import PriceTagAPI from 'api/price-tag';
    import { formatMoney } from '@abc/utils';
    import { formatDate } from '@tool/date';
    import { usePriceTagSetupStatus } from 'views/settings/print-config/price-tag/hooks/price-tag-setup-status';
    import { PriceTagTemplateManager } from 'views/settings/print-config/price-tag/utils/price-tag-template-manager';
    import useMemberLevelList from '@/hooks/business/use-member-levels-list';
    import MarketingAPI from 'api/marketing.js';
    import { parseTime } from '@abc/utils-date';
    import { isNull } from '@/common/utils';
    import { isNotNull } from '@/utils';

    const GoodsAutoComplete = () => import('views/inventory/common/goods-auto-complete.vue');

    const PrintPriceTypeEnum = {
        PACKAGE: 1,
        PIECE: 2,
    };

    const SalePriceTypeEnum = {
        BEFORE: 1,
        AFTER: 2,
    };

    const PrintPriceUserHabitCacheKey = '_print_price_tag_user_habit';

    export default {
        name: 'PrintPriceTagDialog',

        components: {
            GoodsAutoComplete,
        },

        props: {
            visible: {
                type: Boolean,
                default: false,
            },
            params: {
                type: Object,
                default: () => ({
                    // isAllChecked为true时 这里为排除的商品
                    // isAllChecked为false时 这里为包含的商品
                    // queryParams为查询条件
                    goodsIdList: [],
                    goodsMemberPriceList: [],
                    isAllChecked: false,
                    queryParams: {},
                }),
            },
            useAfterPrice: {
                type: Boolean,
                default: false,
            },
        },

        setup() {
            const { isSetupPriceTagTemplate } = usePriceTagSetupStatus();
            const { memberLevelList } = useMemberLevelList({ MarketingAPI });

            return {
                isSetupPriceTagTemplate,
                memberLevelList,
            };
        },

        data() {
            const cacheList = this.useAfterPrice ? [] : (localStorage.get(PrintPriceUserHabitCacheKey, true) || []);
            return {
                showDialog: this.visible,
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
                dataList: [],
                loading: true,

                currentKey: '',

                batchLoading: false,

                goodsId: '',

                cacheList,
                priceTagTemplateList: [],
                goodsMemberPriceMap: {},
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'isChainAdmin',
                'subClinics',
                'currentClinic',
                'currentPharmacy',
                'showSubSetPrice',
                'profitClassificationList',
                'allowProfitWarnFlag',
                'isCanOperateGoodsAdjustPriceInInventory',
            ]),
            PrintPriceTypeEnum() {
                return PrintPriceTypeEnum;
            },
            SalePriceTypeEnum() {
                return SalePriceTypeEnum;
            },
            renderConfig() {
                const list = [
                    {
                        label: '商品名称',
                        key: 'displayName',
                        style: {
                            flex: '1',
                            minWidth: '294px',
                        },
                    },
                    {
                        label: '规格',
                        key: 'displaySpec',
                        style: {
                            width: '150px',
                            maxWidth: '150px',
                            minWidth: '150px',
                        },
                    },
                    {
                        label: '生产厂家',
                        key: 'manufacturer',
                        style: {
                            width: '293px',
                            maxWidth: '293px',
                            minWidth: '293px',
                        },
                    },
                    {
                        label: '零售价类型',
                        key: 'packagePriceType',
                        style: {
                            width: '120px',
                            maxWidth: '120px',
                            minWidth: '120px',
                        },
                    },
                    {
                        label: this.useAfterPrice ? '调价后售价' : '售价',
                        key: 'price',
                        style: {
                            width: '120px',
                            maxWidth: '120px',
                            minWidth: '120px',
                        },
                    },
                ];
                this.printMemberLevelList.forEach((it) => {
                    list.push({
                        key: it.tableKey,
                        style: {
                            width: '120px',
                            maxWidth: '120px',
                            minWidth: '120px',
                        },
                        // eslint-disable-next-line no-unused-vars
                        headerRender: (h) => {
                            const memberPriceTitle = this.useAfterPrice ? `调价后${it.name}价` : `${it.name}价`;
                            return (
                                <div title={memberPriceTitle} style="display: inline-block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;">
                                    { memberPriceTitle }
                                </div>
                            );
                        },
                    });
                });
                return {
                    list,
                };
            },

            clinicId() {
                return this.currentClinic.clinicId;
            },

            pharmacyNo() {
                return this.currentPharmacy?.no;
            },

            printMemberLevelList() {
                const cachePrintMemberLevelList = [];
                this.priceTagTemplateList.forEach((it) => {
                    if (it.memberId) {
                        const findMemberLevelItem = (this.memberLevelList || []).find((memberLevel) => memberLevel.id === it.memberId);
                        if (findMemberLevelItem && !cachePrintMemberLevelList.some((memberLevel) => memberLevel.id === findMemberLevelItem.id)) {
                            cachePrintMemberLevelList.push({
                                id: findMemberLevelItem.id,
                                name: findMemberLevelItem.name,
                                tableKey: `member-price-${findMemberLevelItem.id}`,
                            });
                        }
                    }
                });
                return cachePrintMemberLevelList;
            },
        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        async created() {
            await this.fetchPrintPriceTagTemplate();
            this.$nextTick(() => {
                this.fetchGoodsList();
            });
        },
        methods: {
            formatDate,
            isChineseMedicine,

            async fetchPrintPriceTagTemplate() {
                const cachePriceTagTemplate = (await PriceTagTemplateManager.getInstance().fetchPriceTagTemplate())[0];
                if (cachePriceTagTemplate) {
                    this.priceTagTemplateList = cachePriceTagTemplate.templateContent?.businessPropertyFields || [];
                }
            },
            createParams() {
                let {
                    type,
                } = this.params.queryParams;

                const { subType } = this.params.queryParams;

                if (type && typeof type === 'string') {
                    type = type.split(',').map((it) => Number(it));
                } else {
                    type = [];
                }

                return {
                    ...this.params.queryParams,
                    fullSelect: Number(this.params.isAllChecked),
                    batchGoodsIdList: this.params.goodsIdList,
                    type,
                    subType: !subType ? undefined : subType,
                    orderBy: 'unEffectedPriceFirst',
                    filterNotSubmitPrice: 1,
                    onlyStock: (this.params.queryParams?.onlyStock === undefined || this.params.queryParams?.onlyStock === 0) ? [] : `${this.params.queryParams.onlyStock}`?.length === 1 ? [Number(this.params.queryParams.onlyStock)] : (this.params.queryParams.onlyStock?.split(',')?.map((i) => {
                        return Number(i);
                    }) || []),
                    disable: this.params.queryParams?.disable === undefined ? [] : `${this.params.queryParams.disable}`?.length === 1 ? [Number(this.params.queryParams.disable)] : (this.params.queryParams?.disable?.split(',')?.map((i) => {
                        return Number(i);
                    }) || []),
                    // 字符串转数组
                    hasBarCode: this.params.queryParams?.hasBarCode === undefined ? [] : `${this.params.queryParams.hasBarCode}`?.length === 1 ? [Number(this.params.queryParams.hasBarCode)] : (this.params.queryParams.hasBarCode?.split(',')?.map((i) => {
                        return Number(i);
                    }) || []),
                    memberPriceFlag: this.params.queryParams?.memberPriceFlag === undefined ? [] : `${this.params.queryParams?.memberPriceFlag}`?.length === 1 ? [Number(this.params.queryParams.memberPriceFlag)] : (this.params.queryParams.memberPriceFlag?.split(',')?.map((i) => {
                        return Number(i);
                    }) || []),
                };
            },

            async fetchGoodsList() {
                try {
                    this.loading = true;
                    const {
                        data: {
                            rows = [],
                            total = 0,
                        },
                    } = await GoodsAPI.goodsListForPrint({
                        ...this.createParams(),
                        goodsId: this.goodsId,
                        offset: this.pageParams.pageIndex * this.pageParams.pageSize,
                        limit: this.pageParams.pageSize,
                    });

                    if (!rows) {
                        this.dataList = [];
                        return;
                    }

                    // 调价单不支持选择未来售价
                    if (this.useAfterPrice) {
                        rows.forEach((it) => {
                            delete it.modifyPriceTipsView;
                            const {
                                packagePrice, piecePrice,
                            } = this.params.goodsListPrices.find((item) => item.goodsId === it.id);
                            it.packagePrice = packagePrice;
                            it.piecePrice = piecePrice;
                        });
                    }

                    this.pageParams.count = total;
                    this.dataList = rows.map((it) => {
                        const cacheIndex = this.cacheList.findIndex((cacheItem) => cacheItem.id === it.id);
                        if (cacheIndex > -1) {
                            const memberSalePriceTypeObj = {};
                            const cacheSalePriceType = this.cacheList[cacheIndex].salePriceType;
                            if (isNotNull(cacheSalePriceType)) {
                                this.printMemberLevelList.forEach((memberLevel) => {
                                    memberSalePriceTypeObj[`salePriceType${memberLevel.id}`] = cacheSalePriceType;
                                    if (!this.goodsMemberPriceMap[it.goodsId]) {
                                        this.goodsMemberPriceMap[it.goodsId] = {};
                                    }
                                    this.goodsMemberPriceMap[it.goodsId][memberLevel.id] = cacheSalePriceType;
                                });
                            }

                            return {
                                ...it,
                                printPriceType: this.cacheList[cacheIndex].printPriceType,
                                salePriceType: cacheSalePriceType,
                                ...memberSalePriceTypeObj,
                            };
                        }

                        const salePriceType = (this.useAfterPrice && it.modifyPriceTipsView) ? SalePriceTypeEnum.AFTER : undefined;

                        const memberSalePriceTypeObj = {};
                        if (isNotNull(salePriceType)) {
                            this.printMemberLevelList.forEach((memberLevel) => {
                                memberSalePriceTypeObj[`salePriceType${memberLevel.id}`] = salePriceType;
                                if (!this.goodsMemberPriceMap[it.goodsId]) {
                                    this.goodsMemberPriceMap[it.goodsId] = {};
                                }
                                this.goodsMemberPriceMap[it.goodsId][memberLevel.id] = salePriceType;
                            });
                        }
                        return {
                            ...it,
                            printPriceType: PrintPriceTypeEnum.PACKAGE,
                            salePriceType,
                            ...memberSalePriceTypeObj,
                        };
                    });
                } catch (error) {
                    console.error(error);
                } finally {
                    this.loading = false;
                }
            },

            changePage(index) {
                this.pageParams.pageIndex = index - 1;
                this.fetchGoodsList();
            },

            async handleClickBatchPrint() {
                try {
                    this.batchLoading = true;
                    const printList = [];
                    /**
                     * 批量从后台拉取分页数据
                     **/
                    let pageIndex = 0;
                    const pageSize = 1000;
                    // 未设置多个售价的商品数量
                    let unsetSalePriceCount = 0;


                    const template = (await PriceTagTemplateManager.getInstance().fetchPriceTagTemplate())?.[0];

                    if (!template) {
                        return this.$Toast({
                            type: 'error',
                            message: '请先设置价签模板',
                        });
                    }

                    while (pageIndex * pageSize < this.pageParams.count) {
                        const {
                            data: {
                                rows = [],
                            },
                        } = await GoodsAPI.goodsListForPrint({
                            ...this.createParams(),
                            offset: pageIndex * pageSize,
                            limit: pageSize,
                        });

                        if (this.useAfterPrice) {
                            rows.forEach((it) => {
                                delete it.modifyPriceTipsView;
                                const {
                                    packagePrice, piecePrice,
                                } = this.params.goodsListPrices.find((item) => item.goodsId === it.id);
                                it.packagePrice = packagePrice;
                                it.piecePrice = piecePrice;
                            });
                        }

                        for (const it of rows) {
                            const salePriceType = this.cacheList.find((cacheItem) => cacheItem.id === it.id)?.salePriceType;
                            const printPriceType = this.cacheList.find((cacheItem) => cacheItem.id === it.id)?.printPriceType || PrintPriceTypeEnum.PACKAGE;
                            const {
                                price, unit,
                            } = this.getPriceAndUnitWithModifyPriceTipsView({
                                ...it,
                                printPriceType,
                                salePriceType,
                            });

                            const goodsTag = (it.goodsTagList || []).map((item) => item.name).join(',');

                            if (it.modifyPriceTipsView && !salePriceType) {
                                unsetSalePriceCount++;
                            }

                            // -------------- 处理会员价 start -------------------
                            const memberLevelPriceObj = {};
                            if (printPriceType === PrintPriceTypeEnum.PACKAGE) {
                                this.memberLevelList.forEach((memberLevel) => {
                                    const res = this.getMemberLevelStaticStr(it, memberLevel);
                                    if (res.flag) {
                                        // 一个价格
                                        if (res.price !== '-') {
                                            // 唯一价格
                                            memberLevelPriceObj[`memberPrice${memberLevel.id}`] = parseFloat(res.price);
                                        } else {
                                            // 没有价格
                                        }
                                    } else {
                                        // 二选一
                                        const beforeAndAfterRes = this.getMemberAfterPriceResult(it, memberLevel);
                                        if (beforeAndAfterRes.flag) {
                                            // 选了价格
                                            memberLevelPriceObj[`memberPrice${memberLevel.id}`] = parseFloat(beforeAndAfterRes.price);
                                        } else {
                                            // 没选价格
                                        }
                                    }
                                });
                            }
                            // -------------- 处理会员价 end -------------------

                            printList.push({
                                ...it,
                                price,
                                unit,
                                shebao: it.shebaoNationalView,
                                ...memberLevelPriceObj,
                                goodsTag,
                            });
                        }

                        pageIndex++;
                    }

                    const next = () => {
                        const {
                            businessPropertyFields,
                            clippedBackgroundImageUrl,
                            sizeHeightSnap,
                            sizeWidthSnap,
                        } = template.templateContent;

                        PriceTagPrintApi.multiPrintPriceTag({
                            width: sizeWidthSnap,
                            height: sizeHeightSnap,
                            backgroundImgUrl: clippedBackgroundImageUrl,
                            htmlDataList: businessPropertyFields,
                            goodsList: printList,
                            isPreview: true,
                            onPrintSuccess: () => {
                                // 通知后台打印完成更新
                                PriceTagAPI.priceTagPrintFinish({
                                    list: printList.map((it) => ({
                                        id: it.id,
                                        // 价签是否打印标记 null 无意义 10 未打印 20 已打印
                                        printPriceFlag: 20,
                                    })),
                                });
                            },
                        });
                    };

                    if (unsetSalePriceCount > 0) {
                        return this.$confirm({
                            type: 'warn',
                            title: '部分商品未选择售价',
                            content: `${unsetSalePriceCount}个商品存在多个售价，未选择打印售价，确认继续打印吗？`,
                            onConfirm: () => {
                                next();
                            },
                            onCancel: () => {
                                //
                            },
                        });
                    }

                    next();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.batchLoading = false;
                }
            },

            isEnableSelectPrice(item) {
                return item.dismounting && !isChineseMedicine(item);
            },

            handleClickCancel() {
                this.showDialog = false;
            },

            initFetch() {
                this.pageParams.pageIndex = 0;
                this.fetchGoodsList();
            },

            clearSearch() {
                this.currentKey = '';
                this.goodsId = '';
                this.initFetch();
            },

            selectGoods(goods) {
                this.goodsId = goods.id;
                this.initFetch();
            },

            handleChangeItem(item, salePriceType) {
                item.salePriceType = salePriceType;
                const cacheIndex = this.cacheList.findIndex((it) => it.id === item.id);
                if (cacheIndex > -1) {
                    this.cacheList.splice(cacheIndex, 1, {
                        id: item.id,
                        printPriceType: item.printPriceType,
                        salePriceType: item.salePriceType,
                    });
                } else {
                    this.cacheList.push({
                        id: item.id,
                        printPriceType: item.printPriceType,
                        salePriceType: item.salePriceType,
                    });
                }

                if (!this.useAfterPrice) {
                    localStorage.set(PrintPriceUserHabitCacheKey, this.cacheList);
                }

                // 处理会员价变更
                if (isNotNull(salePriceType)) {
                    this.printMemberLevelList.forEach((memberLevel) => {
                        this.$set(item, `salePriceType${memberLevel.id}`, salePriceType);
                        if (!this.goodsMemberPriceMap[item.goodsId]) {
                            this.goodsMemberPriceMap[item.goodsId] = {};
                        }
                        this.goodsMemberPriceMap[item.goodsId][memberLevel.id] = salePriceType;
                    });
                }
            },

            handleMemberPriceTypeChangeItem(item, salePriceType, memberLevel) {
                this.$set(item, `salePriceType${memberLevel.id}`, salePriceType);
                const { goodsId } = item;
                if (!this.goodsMemberPriceMap[goodsId]) {
                    this.goodsMemberPriceMap[goodsId] = {};
                }
                this.goodsMemberPriceMap[goodsId][memberLevel.id] = salePriceType;
            },

            getBeforePriceText(it) {
                if (!it.modifyPriceTipsView) {
                    return '暂无售价';
                }
                const {
                    beforePackagePrice,
                    beforePiecePrice,
                } = it.modifyPriceTipsView;
                const {
                    price,
                    unit,
                } = this.getPriceAndUnit(it, {
                    piecePrice: beforePiecePrice,
                    packagePrice: beforePackagePrice,
                });
                if (price === null) {
                    return '';
                }
                return `${formatMoney(price)}/${unit}`;
            },

            getMemberBeforePriceText(item, memberLevel) {
                if (!item.multiPriceList) {
                    return { flag: false };
                }
                const findItem = (item.multiPriceList || []).find((it) => it.memberTypeId === memberLevel.id);
                if (findItem && isNotNull(findItem.packagePrice)) {
                    return {
                        flag: true,
                        price: `${formatMoney(findItem.packagePrice)}/${item.packageUnit || item.pieceUnit}`,
                    };
                }
                return { flag: false };
            },

            getAfterPriceText(it) {
                if (!it.modifyPriceTipsView) {
                    return '暂无售价';
                }
                const {
                    afterPackagePrice,
                    afterPiecePrice,
                } = it.modifyPriceTipsView;
                const {
                    price,
                    unit,
                } = this.getPriceAndUnit(it, {
                    piecePrice: afterPiecePrice,
                    packagePrice: afterPackagePrice,
                });
                return `${formatMoney(price)}/${unit}`;
            },

            getMemberAfterPriceText(item, memberLevel) {
                if (!item.modifyPriceTipsView) {
                    return { flag: false };
                }
                const {
                    waitingEffectPriceList,
                } = item.modifyPriceTipsView;
                const findItem = (waitingEffectPriceList || []).find((it) => it.memberTypeId === memberLevel.id);
                if (findItem && isNotNull(findItem.afterPackagePrice)) {
                    return {
                        flag: true,
                        price: `${formatMoney(findItem.afterPackagePrice)}/${item.packageUnit || item.pieceUnit}`,
                    };
                }
                return { flag: false };
            },

            getMemberAfterPriceDate(item, memberLevel) {
                if (!item.modifyPriceTipsView) {
                    return '';
                }
                const {
                    waitingEffectPriceList,
                } = item.modifyPriceTipsView;
                const findItem = (waitingEffectPriceList || []).find((it) => it.memberTypeId === memberLevel.id);
                if (findItem && isNotNull(findItem.effected)) {
                    return `${parseTime(findItem.effected, 'y-m-d', true)}`;
                }
                return '';
            },

            getPriceAndUnitWithModifyPriceTipsViewText(it) {
                const {
                    price, unit,
                } = this.getPriceAndUnitWithModifyPriceTipsView(it);
                if (!price && !it.salePriceType) {
                    return '';
                }
                return `${formatMoney(price)}/${unit}`;
            },

            getMemberAfterPrice(item, memberLevel) {
                let res = { flag: false };

                if (isNull(item[`salePriceType${memberLevel.id}`])) return res;

                if (item[`salePriceType${memberLevel.id}`] === SalePriceTypeEnum.AFTER) {
                    res = this.getMemberAfterPriceText(item, memberLevel);
                } else if (item[`salePriceType${memberLevel.id}`] === SalePriceTypeEnum.BEFORE) {
                    res = this.getMemberBeforePriceText(item, memberLevel);
                }
                return res;
            },

            getMemberAfterPriceResult(item, memberLevel) {
                let res = { flag: false };

                const { goodsId } = item;
                const salePriceType = this.goodsMemberPriceMap[goodsId]?.[memberLevel.id];

                if (isNull(salePriceType)) return res;

                if (salePriceType === SalePriceTypeEnum.AFTER) {
                    res = this.getMemberAfterPriceText(item, memberLevel);
                } else if (salePriceType === SalePriceTypeEnum.BEFORE) {
                    res = this.getMemberBeforePriceText(item, memberLevel);
                }
                return res;
            },

            getPriceAndUnitWithModifyPriceTipsView(it) {
                if (!it.modifyPriceTipsView) {
                    return this.getPriceAndUnit(it);
                }

                const {
                    beforePackagePrice,
                    beforePiecePrice,
                    afterPackagePrice,
                    afterPiecePrice,
                } = it.modifyPriceTipsView;

                const { salePriceType } = it;

                if (!salePriceType) {
                    return {
                        price: undefined,
                        unit: undefined,
                    };
                }

                return this.getPriceAndUnit(it, {
                    piecePrice: salePriceType === SalePriceTypeEnum.AFTER ? afterPiecePrice : beforePiecePrice,
                    packagePrice: salePriceType === SalePriceTypeEnum.AFTER ? afterPackagePrice : beforePackagePrice,
                });
            },

            getPriceAndUnit(it, defaultValue) {
                let price = 0;
                let unit = '';

                let {
                    piecePrice,
                    packagePrice,
                } = it;

                const {
                    pieceUnit,
                    packageUnit,
                } = it;

                if (defaultValue) {
                    piecePrice = defaultValue.piecePrice;
                    packagePrice = defaultValue.packagePrice;
                }
                if (isChineseMedicine(it)) {
                    price = piecePrice;
                    unit = pieceUnit;
                } else if (!it.dismounting) {
                    price = packagePrice;
                    unit = packageUnit;
                } else {
                    if (it.printPriceType === PrintPriceTypeEnum.PACKAGE) {
                        price = packagePrice;
                        unit = packageUnit;
                    } else if (it.printPriceType === PrintPriceTypeEnum.PIECE) {
                        price = piecePrice;
                        unit = pieceUnit;
                    }
                }
                return {
                    price,
                    unit,
                };
            },

            showNoneMemberPriceText(item) {
                return this.isEnableSelectPrice(item) && item.printPriceType === PrintPriceTypeEnum.PIECE;
            },

            getMemberAfterPriceStrByUseAfterPrice(item, memberLevel) {
                const res = {
                    flag: true, price: '-',
                };
                if (!this.showNoneMemberPriceText(item)) {
                    const { goodsMemberPriceList } = this.params;
                    const {
                        goodsId, packageUnit, pieceUnit,
                    } = item;
                    const findItem = (goodsMemberPriceList || []).find((it) => it.goodsId === goodsId);
                    if (findItem) {
                        const { memberPriceList } = findItem;
                        const findMemberPrice = (memberPriceList || []).find((member) => member.memberTypeId === memberLevel.id);
                        if (findMemberPrice && isNotNull(findMemberPrice.afterPackagePrice)) {
                            res.price = `${formatMoney(findMemberPrice.afterPackagePrice)}/${packageUnit || pieceUnit}`;
                        }
                    }
                }
                return res;
            },

            getMemberLevelStaticStr(item, memberLevel) {
                let res = { flag: false };

                if (this.useAfterPrice) {
                    res = this.getMemberAfterPriceStrByUseAfterPrice(item, memberLevel);
                } else {
                    const beforeRes = this.getMemberBeforePriceText(item, memberLevel);
                    const afterRes = this.getMemberAfterPriceText(item, memberLevel);
                    if (
                        this.showNoneMemberPriceText(item) ||
                        (!beforeRes.flag && !afterRes.flag)
                    ) {
                        res.flag = true;
                        res.price = '-';
                    } else if (!beforeRes.flag) {
                        res.flag = true;
                        res.price = afterRes.price;
                    } else if (!afterRes.flag) {
                        res.flag = true;
                        res.price = beforeRes.price;
                    }
                }
                return res;
            },
        },
    };
</script>
