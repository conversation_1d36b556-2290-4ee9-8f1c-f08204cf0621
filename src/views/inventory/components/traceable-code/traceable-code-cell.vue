<template>
    <abc-popover
        width="340px"
        placement="bottom-end"
        trigger="manual"
        :value="showPopover"
        theme="white"
        :popper-style="{ padding: 0 }"
        :disabled="disabled || !needCollect"
        :offset="offset"
        :class="['traceable-code-cell-popover', (disabled || !needCollect) ? '' : 'hover-bg']"
        :style="{ 'height': '100%' }"
        data-cy="abc-trace-code-popover"
        :resident-popover="residentPopover"
        @show="onShow"
        @hide="onHide"
    >
        <template slot="reference">
            <abc-form-item
                class="traceable-code-cell-trigger"
                :validate-event="validateEvent"
                data-cy="abc-trace-code-popover-reference"
                placement="top"
                @click.native="showPopover = !showPopover"
            >
                <abc-tooltip
                    v-bind="tooltipConfig"
                >
                    <abc-input
                        :value="textConfig.text"
                        readonly
                        :disabled="disabled || !needCollect"
                        :class="{ 'custom-readonly': readonly }"
                        :data-theme="textConfig.theme"
                        adaptive-width
                        :tabindex="-1"
                        @enter="handleReferenceInputEnter"
                    >
                        <template v-if="isStrictCountWithTraceCodeCollect ? false : showError" #appendInner>
                            <abc-tooltip
                                placement="top"
                                :content="warningTips"
                                :disabled="!warningTips"
                            >
                                <abc-icon :size="16" icon="n-alert-fill" color="var(--abc-color-Y2)"></abc-icon>
                            </abc-tooltip>
                        </template>
                    </abc-input>

                    <template v-if="isStrictCountWithTraceCodeCollect && isSupportTraceCodeForceCheckStock() && goodsCount.isTrans" slot="content">
                        <div v-if="goodsCount" style="width: 296px;">
                            <abc-flex vertical>
                                <abc-text size="mini" style="margin-bottom: 6px;">
                                    医保中心要求，严格按照医保目录规格采集追溯码：
                                </abc-text>
                                <abc-text theme="gray" size="mini">
                                    本次{{ goodsCount.label || goodsCount.countLabel }} = {{ goodsCount.unitCount }} {{ goodsCount.unit }}
                                </abc-text>
                                <abc-text theme="gray" size="mini">
                                    {{ displayShebaoSpec }}
                                </abc-text>
                                <abc-text theme="gray" size="mini">
                                    经系统换算，本次应采集 {{ goodsCount.isTrans ? goodsCount.maxCount : goodsCount.unitCount }} 个追溯码
                                </abc-text>
                            </abc-flex>
                        </div>
                    </template>
                </abc-tooltip>
            </abc-form-item>
        </template>
        <abc-flex
            v-if="traceCodeCollectRender"
            v-abc-click-outside="outside"
            vertical
            :style="{ height: `${contentHeight}px` }"
            data-cy="abc-trace-code-popover-content"
        >
            <div style="padding: 12px 20px; border-bottom: 1px solid var(--abc-color-P8);">
                <abc-input
                    ref="scanRef"
                    v-model.trim="searchKey"
                    adaptive-width
                    placeholder="扫描或输入追溯码"
                    :tabindex="-1"
                    inputmode="none"
                    :loading="loading"
                    loading-position="left"
                    :disabled="readonly"
                    data-cy="abc-trace-code-input"
                    @blur="handleBlur"
                    @focus="handleFocus"
                    @enter="handleEnter"
                >
                    <abc-icon slot="prepend" icon="scan"></abc-icon>
                    <abc-text v-if="searchKey" slot="appendInner" theme="gray-light">
                        敲回车录入
                    </abc-text>
                </abc-input>
            </div>

            <template v-if="isNullCodeGoods">
                <div
                    style="flex: 1; padding-bottom: 49px;"
                >
                    <abc-content-empty style="width: 228px; margin: 0 auto;" size="mini" value="该商品已被标记为无追溯码，如果当前包装有追溯码，请继续扫码或输入"></abc-content-empty>
                </div>
            </template>
            <template v-else>
                <div v-if="warningTips && traceableCodeList.length" style="padding: 10px 10px 0;">
                    <abc-tips-card-v2 theme="warning">
                        {{ warningTips }}
                    </abc-tips-card-v2>
                </div>



                <div v-if="!traceableCodeList.length && sceneType === SceneTypeEnum.GOODS_IN" style="padding: 10px 10px 0;">
                    <abc-tips-card-v2
                        theme="primary"
                        :operate-options="{
                            text: '查看',
                            onClick: ()=>handleViewDoc('ffffffff0000000034d2a7b5c10e0000')
                        }"
                    >
                        如何便捷的采集入库追溯码?
                    </abc-tips-card-v2>
                </div>

                <div v-if="!traceableCodeList.length && sceneType === SceneTypeEnum.GOODS_CHECK" style="padding: 10px 10px 0;">
                    <abc-tips-card-v2
                        theme="primary"
                        :operate-options="{
                            text: '查看',
                            onClick: ()=>handleViewDoc('ffffffff0000000034d2a856210e4000')
                        }"
                    >
                        如何采集盈亏药品的追溯码?
                    </abc-tips-card-v2>
                </div>
                <code-list
                    v-if="renderList.length"
                    ref="codeList"
                    :traceable-code-list="renderList"
                    :goods="goods"
                    :readonly="readonly"
                    :is-no-trace-code-goods="isNoTraceCodeGoods"
                    style="flex: 1; margin-top: 4px;"
                    data-cy="abc-trace-code-list"
                    @deleteItem="handleDeleteItem"
                ></code-list>

                <div
                    v-else
                    style="flex: 1;"
                >
                    <abc-content-empty size="mini" value="暂无已采集追溯码"></abc-content-empty>
                </div>

                <abc-flex
                    v-if="(traceableCodeList.length) || (isSupportCodelessArea && !readonly && !isNoTraceCodeGoods)"
                    align="center"
                    justify="space-between"
                    style="padding: 8px 12px; border-top: 1px solid var(--abc-color-P8);"
                >
                    <div>
                        <abc-tooltip
                            placement="bottom-start"
                            :content="'系统将自动上传无码标识，无法删除'"
                            :disabled="!isNoTraceCodeGoods"
                        >
                            <delete-confirm v-if="traceableCodeList.length" @confirm="handleDelete">
                                <abc-button
                                    :disabled="isNoTraceCodeGoods || readonly"
                                    icon="n-delete-2-line"
                                    variant="text"
                                    theme="default"
                                    data-cy="abc-button-delete-all"
                                >
                                    全部删除
                                </abc-button>
                            </delete-confirm>
                        </abc-tooltip>
                    </div>

                    <abc-button
                        v-if="isSupportCodelessArea && !isNoTraceCodeGoods && !readonly"
                        variant="text"
                        size="normal"
                        @click="handleConfirmNoCode"
                    >
                        商品无追溯码？
                    </abc-button>
                </abc-flex>
            </template>
        </abc-flex>
    </abc-popover>
</template>

<script>
    import GoodsAPIV3 from 'api/goods/index-v3';
    import DeleteConfirm from 'components/index/delete-confirm.vue';
    import CodeList from './traceable-code-list.vue';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';
    import TraceCode, {
        SceneTypeEnum,
        TraceableCodeListItemErrorType,
        TraceableCodeOpTypeEnum,
        TraceableCodeTypeEnum,
    } from '@/service/trace-code/service';
    import TraceCodeConfirmDialog from '@/service/trace-code/dialog-trace-code-confirm';
    import TraceCodeSelectGoodsDialog from '@/service/trace-code/dialog-trace-code-select-goods';
    import TraceCodeRepeatDialog from '@/service/trace-code/dialog-repeat-trace-code';
    import {
        createGUID, isNotNull,
    } from '@/utils';
    import { debounce } from 'utils/lodash';
    import Logger from 'utils/logger';
    import cloneDeep from 'lodash.clonedeep';
    import {
        DialogTraceCodeStandardMatch,
    } from '@/service/trace-code/dialog-trace-code-standard-match/dialog-trace-code-standard-match';
    import { isChineseMedicine } from '@/filters';

    export default {
        name: 'TraceableCodeCell',
        components: {
            DeleteConfirm,
            CodeList,
        },
        props: {
            value: {
                type: Array,
                default: () => [],
            },
            dataItem: Object,
            // eslint-disable-next-line vue/require-default-prop
            goods: Object,
            goodsCount: {
                type: Object,
                default: () => ({
                    label: '采购数量',
                    countLabel: '采购数量',
                    unitCount: 0,
                    maxCount: 0,
                    unit: '',
                    isTrans: false,// 后端是否换算过
                }),
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            offset: {
                type: Number,
                default: 0,
            },
            sceneType: {
                type: String,
            },
            needValidate: {
                type: Boolean,
                default: false,
            },
            isStrictCountWithTraceCodeCollect: {
                type: Boolean,
                default: false,
            },
            popoverRenderControlled: {
                type: Boolean,
                default: false,
            },
            item: {
                type: Object,
                default: () => ({}),
            },
            needValidateDrugIdentificationCode: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                startBarcodeDetect,
                stopBarcodeDetect,
                setDisabledScanBarcode,
            } = useBarcodeScanner();

            return {
                startBarcodeDetect,
                stopBarcodeDetect,
                setDisabledScanBarcode,
            };
        },
        data() {
            return {
                searchKey: '',
                traceableCodeList: [],
                showPopover: false,
                residentPopover: false,
                contentHeight: 456,
                traceCount: 0,
                loading: false,
                isPopoverOpened: false,
                codeQueue: [], // 获取到的追溯码队列
                traceCodeCallbackQueue: [], // 追溯码回调队列
                traceCodeDialogManager: {}, // 追溯码弹窗管理对象
                popoverShowStatus: false, // popover显示状态
            };
        },
        computed: {
            SceneTypeEnum() {
                return SceneTypeEnum;
            },
            isSupportCodelessArea() {
                return !!this.$abcSocialSecurity.defaultDrugTraceCode?.isSupportDefaultTraceCode;
            },
            isNoTraceCodeGoods() {
                return TraceCode.isNoTraceCodeGoods(this.goods);
            },
            // 特殊无码商品-无固定值后端默认是空字符串
            isNullCodeGoods() {
                return TraceCode.isNullCodeGoods(this.goods);
            },
            needCollect() {
                if (!this.goods) return false;
                return TraceCode.isSupportTraceCode(this.goods.typeId);
            },
            displayShebaoSpec() {
                return TraceCode.displayShebaoSpec(this.goods);
            },
            textConfig() {
                let text = '';
                let theme = '';
                if (this.needCollect && !this.isNullCodeGoods) {
                    if (this.noProfitOrLoss) {
                        if (this.traceableCodeList.length) {
                            text = `已采: ${this.traceCount}`;
                            theme = 'primary-light';
                        } else {
                            text = '无需采集';
                            theme = 'gray-light';
                        }
                    } else {
                        if (this.isStrictCountWithTraceCodeCollect && this.isSupportTraceCodeForceCheckStock()) {
                            const totalCount = this.goodsMaxCount || this.goodsCount.unitCount || 0;
                            text = totalCount ? `${this.traceCount}/${totalCount}` : '';
                            theme = this.traceCount === totalCount ? 'black' : 'warning-light';
                        } else {
                            if (!this.traceableCodeList.length) {
                                text = this.needValidate ? '未采集' : '';
                                theme = this.needValidate ? 'warning-light' : '';
                            } else {
                                text = `已采: ${this.traceCount}`;
                                theme = 'primary-light';
                            }
                        }
                    }
                } else {
                    text = '无需采集';
                    theme = 'gray-light';
                }

                return {
                    text,
                    theme,
                };
            },
            // 特殊判断，盘点无盈亏
            noProfitOrLoss() {
                const count = this.isStrictCountWithTraceCodeCollect ? this.goodsCount.maxCount : this.goodsCount.unitCount;
                return this.sceneType === SceneTypeEnum.GOODS_CHECK && this.goodsCount.label === '盈亏数量' && count === 0;
            },
            hoverTips() {
                if (this.isStrictCountWithTraceCodeCollect) {
                    return this.warningTips;
                }

                if (this.noProfitOrLoss) {
                    return '未产生盈亏，无需采集';
                }

                if (this.textConfig.text === '无需采集' && !this.isNullCodeGoods) {
                    return '只需采集中西成药和器械追溯码';
                }

                return '';
            },
            warningTips() {
                if (!this.needCollect || this.disabled || !this.needValidate || this.noProfitOrLoss) return '';
                return this.warningInfo.warnTips;
            },
            errorTips() {
                return this.warningInfo.errorTips || '';
            },
            warningInfo() {
                return TraceCode.validateDataItem({
                    sceneType: this.sceneType,
                    dataItem: this.dataItem || {
                        traceableCodeList: this.traceableCodeList,
                        _maxTraceCodeCount: this.goodsMaxCount,
                        _isTransformable: this.goodsCount.isTrans,
                    },
                    getUnitInfo: () => {
                        return {
                            ...this.goodsCount,
                            countLabel: this.goodsCount.label,
                        };
                    },
                    productInfo: this.goods,
                });
            },
            renderList() {
                return TraceCode.initTraceableCodeList(this.traceableCodeList, this.goods, this.sceneType);
            },
            showWarnIcon() {
                return this.renderList.some((item) => item.status !== TraceableCodeListItemErrorType.NORMAL);
            },
            // 输入框后异常展示
            showError() {
                return (this.warningTips || this.showWarnIcon) && !this.disabled;
            },
            tooltipConfig() {
                let disableHoverCard = false;
                if (this.isStrictCountWithTraceCodeCollect && this.isSupportTraceCodeForceCheckStock() && this.goodsCount.isTrans) {
                    disableHoverCard = false;
                } else {
                    disableHoverCard = !this.hoverTips;
                }
                return {
                    content: this.hoverTips,
                    disabled: disableHoverCard || !!this.errorTips || this.disabled,
                    placement: this.isStrictCountWithTraceCodeCollect ? 'top' : 'bottom-start',
                    maxWidth: this.isStrictCountWithTraceCodeCollect ? 296 : 134,
                    openDelay: 500,
                    // offset: this.isStrictCountWithTraceCodeCollect ? 0 : 26,
                    // arrowOffset: this.isStrictCountWithTraceCodeCollect ? 0 : 54,
                };
            },
            noCodeConfirmTextV2() {
                if (window.$abcSocialSecurity.config.isShanxi || (window.$abcSocialSecurity.config.isFujian && !window.$abcSocialSecurity.config.isFujianXiamen) || window.$abcSocialSecurity.config.isJiangsu || window.$abcSocialSecurity.config.isGuangdong || window.$abcSocialSecurity.config.isJiangxi || window.$abcSocialSecurity.config.isChongqingGb || window.$abcSocialSecurity.config.isXinjiang) {
                    return <div style="width:324px;line-height: 22px;">
                        标记无码后，请立即在医保平台申报无码商品。<br/> 若未申报，销售商品后，医保将<abc-text bold>不予支付</abc-text>对应费用。
                    </div>;
                }
                return <div style="width:388px;line-height: 22px;">
                    如果药品包装上无追溯码，可将该药品标记为“无码商品”，发药、入库时无需扫码采集。医保监管要求应采尽采，商品有追溯码但设置成无码，可能会存在监管处罚风险。
                    <br/>
                    <br/>
                    是否确定商品无追溯码？
                </div>;
            },
            // 重复追溯码弹窗是否需要校验数量，只校验入库、药店收货场景
            isValidateCount() {
                return this.sceneType === SceneTypeEnum.GOODS_IN || this.sceneType === SceneTypeEnum.GOODS_TAKE;
            },
            // 内容渲染时机判断
            traceCodeCollectRender() {
                if (this.popoverRenderControlled) {
                    return this.showPopover;
                }
                return true;
            },
            goodsMaxCount() {
                if (TraceCode.isNoTraceCodeGoods(this.goods) && isChineseMedicine(this.goods)) {
                    return 1;
                }
                return this.goodsCount.maxCount;
            },
        },
        watch: {
            value: {
                handler(val) {
                    this.traceableCodeList = (val || []);
                },
                immediate: true,
            },
            traceableCodeList: {
                handler(val) {
                    this.$emit('input', val);
                    // 更新采集数量
                    this.traceCount = (val || []).reduce((count, item) => {
                        return count + (item.count || 1);
                    }, 0);
                },
                deep: true,
                immediate: true,
            },
        },
        created() {
            this.calculateContentHeight();
            this._openAllDialog = debounce(() => {
                this.traceCodeCallbackQueue.forEach((callback) => {
                    if (typeof callback === 'function')callback();
                });
                this.traceCodeCallbackQueue = [];
            }, 500, true);
        },
        beforeDestroy() {
            // 当前面板打开的状态下，如果组件卸载，先执行失焦后再进行销毁（不然顺序是先执行了onHide,组件卸载后再执行blur导致事件错乱）
            if (this.isPopoverOpened) {
                this.$refs.scanRef?.$refs?.abcinput?.blur();
            }
            this.onHide();
        },
        methods: {
            /**
             * 限制追溯码最大采集数量
             */
            validateTraceCodeMaxCount(cacheTraceableCodeList, isOldTraceableCodeList = false) {
                const traceableCodeList = cacheTraceableCodeList ?? this.traceableCodeList;
                if (isNotNull(this.item._traceableCodeMaxNum) && typeof this.item._traceableCodeMaxNum === 'number') {
                    const maxTraceCodeCount = this.item._traceableCodeMaxNum;
                    // 计算当前已采集的追溯码总数量
                    const currentTotalCount = traceableCodeList.reduce((total, item) => {
                        // 如果有 count 字段使用 count，否则默认为 1
                        const itemCount = item.count ?? 1;
                        return total + itemCount;
                    }, 0);
                    // 判断是否超过最大限制
                    if (isOldTraceableCodeList) {
                        if (currentTotalCount >= maxTraceCodeCount) {
                            return false;
                        }
                    } else {
                        if (currentTotalCount > maxTraceCodeCount) {
                            return false;
                        }
                    }
                }
                return true;
            },
            outside() {
                // console.log('_outside');
                if (this.showPopover && !this.residentPopover) {
                    this.showPopover = false;
                }
            },
            onShow() {
                console.log('traceable-code-cell:show', this.goods?.displayName);
                this.isPopoverOpened = true;
                this.$emit('show');

                // 只读状态下不允许扫码
                if (this.readonly) return;

                this.$nextTick(() => {
                    this.$refs.scanRef?.$refs?.abcinput?.focus();
                });

                // 当两个popover切换时，当前onShow会比上一个popover的onHide先执行。
                this._timer = setTimeout(() => {
                    this.setDisabledScanBarcode(true);
                }, 0);
            },
            // popover销毁时也会执行onHide
            onHide() {
                console.log('traceable-code-cell:hide', this.isPopoverOpened, this.goods?.displayName);

                this.$emit('hide');
                this.stopBarcodeDetect();
                if (this.isPopoverOpened) {
                    this.setDisabledScanBarcode(false);
                    this.isPopoverOpened = false;
                }
                this._timer && clearTimeout(this._timer);
                this._popoverTimer && clearTimeout(this._popoverTimer);
            },
            // 失焦恢复扫码监听
            handleBlur() {
                console.log('traceable-code-cell:blur');
                this.startBarcodeDetect(this.handleScanCode);
            },
            // 聚焦禁止扫码监听
            handleFocus() {
                this.stopBarcodeDetect();
            },
            handleEnter(e) {
                if (!e.target.value) return;
                console.log('handleEnter', e.target.value);
                this.codeQueue.push(e.target.value);
                this.searchKey = '';
                e.target.value = '';
                this.triggerHandler();
            },
            handleScanCode(e, code) {
                if (e.target === this.$refs.scanRef?.$el) {
                    return;
                }
                this.codeQueue.push(code);
                this.triggerHandler();
            },
            async triggerHandler() {
                // 在处理中不再重复执行
                if (this.loading) return;
                const code = this.codeQueue.shift();
                await this.handleBarcode(code);
                if (this.codeQueue.length) {
                    this.triggerHandler();
                } else {
                    // 没有追溯码录入后，打开所有产生的弹窗
                    this._openAllDialog();
                }
            },
            async handleBarcode(code) {
                code = code.replace(/\s+/g, '');
                // 前端校验是否为追溯码
                if (!TraceCode.isTraceableCode(code)) {
                    this.$Toast({
                        type: 'error',
                        message: TraceCode.hasInterceptTraceCodeFormatLength() ? '无法采集追溯码（医保要求药品追溯码必须为20位或30位，且不应存在汉字或其他符号）' : '未识别到有效的追溯码，请检查扫描或输入是否正确！',
                        referenceEl: this.$refs.scanRef?.$el,
                    });
                    this.searchKey = '';
                    return;
                }
                const noCode = TraceCode.getDefaultNoCodeIdentification(this.goods);
                if (noCode && (code.toLocaleLowerCase()).startsWith(noCode.toLocaleLowerCase())) {
                    this.handleConfirmNoCode();
                    return;
                }

                // 追溯码：限制采集数量
                const isValidate = this.validateTraceCodeMaxCount(this.traceableCodeList, true);
                if (!isValidate) {
                    this.$Toast({
                        type: 'error',
                        message: `无法继续采集追溯码（医保要求追溯码最多采集${this.item._traceableCodeMaxNum}条）`,
                        referenceEl: this.$refs.scanRef?.$el,
                    });
                    this.searchKey = '';
                    return;
                }

                this.loading = true;

                try {
                    const traceCodeInfo = await TraceCode.fetchByCode(
                        code,
                        // 入库场景下，需要传入商品id，后端决定是否到总部查询追溯码关联关系
                        this.sceneType === SceneTypeEnum.GOODS_IN ? this.goods?.id : undefined,
                    );

                    const drugIdentificationCode = traceCodeInfo?.traceableCodeNoInfo?.drugIdentificationCode;

                    // 后端未解析出追溯码信息
                    if (!drugIdentificationCode) {
                        this.$Toast({
                            type: 'error',
                            message: '扫描的不是追溯码，无法采集',
                            referenceEl: this.$refs.scanRef?.$el,
                        });
                        return;
                    }

                    // 重复采集
                    const codeItem = this.traceableCodeList.find((e) => e.no === code);
                    if (codeItem) {
                        this.traceCodeCallbackQueue.push(() => {
                            // 如果是聚焦扫的，先触发输入框失焦后，再打开重复采集弹窗（内部有新的事件注册)，避免将重复采集弹窗内的事件清除掉
                            this.$refs.scanRef?.$refs?.abcinput?.blur?.();

                            this.openRepeatTraceCodeDialog({
                                ...traceCodeInfo,
                                count: (codeItem.count || 1),
                            });
                        });
                        return;
                    }

                    // 未匹配商品时，先加入到追溯码列表中，不做校验
                    if (!this.goods) {
                        this.addTraceableCodeListHandler(traceCodeInfo);
                        return;
                    }

                    // const res = TraceCode.validateTraceCode({
                    //     traceCodeObj: traceCodeInfo,
                    //     goodsInfo: this.goods,
                    // });
                    // 是本商品直接添加
                    if (TraceCode.getDrugIdentificationCodeList(this.goods).includes(drugIdentificationCode)) {
                        this.addTraceableCodeListHandler(traceCodeInfo);
                    } else {
                        // 有绑定商品
                        if (traceCodeInfo.goodsInfo) {
                            // 但不是本商品
                            if (traceCodeInfo.goodsInfo.id !== this.goods.id) {
                                this.traceCodeCallbackQueue.push(() => {
                                    this.openConfirmGoodsDialog(traceCodeInfo);
                                });
                            } else {
                                this.addTraceableCodeListHandler(traceCodeInfo);
                            }
                        } else {
                            this.traceCodeCallbackQueue.push(() => {
                                this.openSelectGoodsDialog(traceCodeInfo);
                            });
                        }
                    }

                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            async checkTraceCodeNoList(code, goodsInfo, codeInfoList) {
                return new Promise((resolve) => {
                    const {
                        sanMaHeYiIdentificationCodeList, shebao, medicineCadn, barCode,
                    } = goodsInfo ?? {};
                    const { nationalCode } = shebao ?? {};
                    if (
                        this.needValidateDrugIdentificationCode &&
                        TraceCode.hasInterceptTraceCodeFormatLength &&
                        nationalCode &&
                        Array.isArray(sanMaHeYiIdentificationCodeList) &&
                        sanMaHeYiIdentificationCodeList.length &&
                        !sanMaHeYiIdentificationCodeList.includes(code)
                    ) {
                        const onConfirm = () => {
                            resolve(true);
                        };
                        const onCancel = () => {
                            resolve(false);
                        };
                        new DialogTraceCodeStandardMatch({
                            sanMaHeYiIdentificationCodeList,
                            shebao,
                            medicineCadn,
                            barCode,
                            code,
                            codeList: codeInfoList.concat([code]),
                            onConfirm,
                            onCancel,
                        }).generateDialogAsync({ parent: this });
                    } else {
                        resolve(true);
                    }
                });
            },
            async openSelectGoodsDialog(traceCodeInfo) {
                if (!this.traceCodeDialogManager[traceCodeInfo.no]) {
                    this.residentPopover = true;
                    this.traceCodeDialogManager[traceCodeInfo.no] = true;

                    const codeInfoList = TraceCode.getDrugIdentificationCodeList(this.goods) ?? [];
                    const checkTraceCodeNoListFn = async () => {
                        const { drugIdentificationCode } = traceCodeInfo.traceableCodeNoInfo ?? {};
                        return this.checkTraceCodeNoList(drugIdentificationCode, this.goods, codeInfoList);
                    };

                    this._selectGoodsDialog = new TraceCodeSelectGoodsDialog({
                        value: true,
                        title: '请确认追溯码关联的商品',
                        desc: '追溯码对应的商品',
                        placeholder: '搜索该追溯码对应的商品',
                        keywordTraceableCodeNoInfo: traceCodeInfo,
                        goodsInfo: this.goods,
                        onConfirm: this.handleConfirmBindGoods,
                        checkTraceCodeNoListFn,
                        onClose: () => {
                            this._selectGoodsDialog = null;
                            this.traceCodeDialogManager[traceCodeInfo.no] = false;
                            this.resetResidentPopover();
                        },
                    });

                    this._selectGoodsDialog.generateDialogAsync({
                        parent: this,
                    });
                }
            },
            openConfirmGoodsDialog(traceCodeInfo) {
                if (!this.traceCodeDialogManager[traceCodeInfo.no]) {
                    this.residentPopover = true;
                    this.traceCodeDialogManager[traceCodeInfo.no] = true;

                    this._traceCodeConfirmDialog = new TraceCodeConfirmDialog({
                        visible: true,
                        title: '采集追溯码关联的不是本商品',
                        bindGoodsInfo: traceCodeInfo.goodsInfo,
                        traceableCodeNoInfo: traceCodeInfo.traceableCodeNoInfo,
                        traceCode: traceCodeInfo.traceCode,
                        goodsInfo: traceCodeInfo.goodsInfo,
                        onConfirm: () => {
                            // 第二个参数是药品发生换绑需要清除掉原有的追溯码
                            this.$emit('updateGoodsTraceableCodeNoInfo', null, traceCodeInfo);
                            this.openSelectGoodsDialog(traceCodeInfo);
                        },
                        onClose: () => {
                            this._traceCodeConfirmDialog = null;
                            this.traceCodeDialogManager[traceCodeInfo.no] = false;
                            this.resetResidentPopover();
                        },
                    });
                    this._traceCodeConfirmDialog.generateDialogAsync();
                }
            },
            openRepeatTraceCodeDialog(traceCodeInfo) {
                if (!this.traceCodeDialogManager[traceCodeInfo.no]) {
                    this.residentPopover = true;
                    this.traceCodeDialogManager[traceCodeInfo.no] = true;

                    this._traceCodeRepeatDialog = new TraceCodeRepeatDialog({
                        visible: true,
                        isValidateCount: this.isValidateCount,
                        title: '采集相同追溯码',
                        traceCodeInfo,
                        onConfirm: (data) => {
                            const item = this.traceableCodeList.find((e) => e.no === data.no);
                            if (item) {
                                // 追溯码：限制采集数量
                                const cacheTraceableCodeList = cloneDeep(this.traceableCodeList);
                                const cacheItem = cacheTraceableCodeList.find((e) => e.no === data.no);
                                Object.assign(cacheItem, data);
                                const isValidate = this.validateTraceCodeMaxCount(cacheTraceableCodeList);
                                if (!isValidate) {
                                    this.$Toast({
                                        type: 'error',
                                        message: `无法继续采集追溯码（医保要求追溯码最多采集${this.item._traceableCodeMaxNum}条）`,
                                        referenceEl: this.$refs.scanRef?.$el,
                                    });
                                } else {
                                    // 不改变原数据，只是更新数量
                                    Object.assign(item, data);
                                    this.$refs.codeList?.triggerHighLight(item.no);
                                }

                            } else {
                                Logger.reportAnalytics('goods-business', {
                                    key: 'repeatedTraceCode',
                                    value: '采集相同追溯码',
                                    data,
                                    traceableCodeList: this.traceableCodeList,
                                });
                            }
                        },
                        onClose: () => {
                            this._traceCodeRepeatDialog = null;
                            this.traceCodeDialogManager[traceCodeInfo.no] = false;
                            this.resetResidentPopover();
                        },
                    });
                    this._traceCodeRepeatDialog.generateDialogAsync();
                }
            },
            addTraceableCodeListHandler(traceCodeInfo) {
                this.traceableCodeList.push({
                    ...traceCodeInfo.traceableCodeNoInfo,
                    ...traceCodeInfo,
                });
                this.$refs.codeList?.triggerHighLight(traceCodeInfo.traceCode || traceCodeInfo.no);
            },

            // 药品标识码绑定goods成功后的回调
            handleConfirmBindGoods(goods, keywordTraceableCodeNoInfo) {
                console.log('handleConfirmBindGoods', goods, keywordTraceableCodeNoInfo);
                // 清除掉无码的追溯码
                this.traceableCodeList = this.traceableCodeList.filter((item) => item.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE);
                this.addTraceableCodeListHandler(keywordTraceableCodeNoInfo);

                // 更新追溯码信息到外部goods
                // eslint-disable-next-line vue/no-mutating-props
                if (this.isNoTraceCodeGoods) {
                    this.goods.traceableCodeNoInfoList = [keywordTraceableCodeNoInfo.traceableCodeNoInfo];
                } else {
                    this.goods.traceableCodeNoInfoList = [
                        ...(this.goods.traceableCodeNoInfoList || []),
                        keywordTraceableCodeNoInfo.traceableCodeNoInfo,
                    ];
                }
                this.$emit('updateGoodsTraceableCodeNoInfo', this.goods, keywordTraceableCodeNoInfo);
                this.$emit('update:goods', { ...this.goods });
            },
            handleConfirmNoCode() {
                this.residentPopover = true;

                // 有码商品的标识码，可能为空就是未绑定
                const code = TraceCode.getDrugIdentificationCode(this.goods);

                if (code) {
                    this.$alert({
                        type: 'info',
                        title: '提示',
                        content: () => {
                            return <div style="width:388px;line-height: 22px;">
                                若确定商品无追溯码，可在库存档案中将【追溯码-产品标识码】修改为无码。后续发药、入库时无需再扫码采集，系统将自动上传无码标识。
                            </div>;
                        },
                        onClose: () => {
                            this.resetResidentPopover();
                        },
                    });
                    return;
                }

                this.$confirm({
                    type: 'warn',
                    title: '是否确定商品无追溯码',
                    content: () => {
                        return this.noCodeConfirmTextV2;
                    },
                    onConfirm: async () => {

                        try {
                            const goodsId = this.goods.id || this.goods.goodsId;
                            const code = TraceCode.getDefaultNoCodeIdentification(this.goods);
                            await GoodsAPIV3.boundIdentificationCode(goodsId, {
                                no: code,
                                type: TraceableCodeTypeEnum.NO_CODE,
                                opType: TraceableCodeOpTypeEnum.BIND,
                            });

                            this.$Toast({
                                message: '修改成功',
                                type: 'success',
                            });

                            // const list = [];
                            const traceableCodeNoInfo = {
                                no: code,
                                drugIdentificationCode: code,
                                type: TraceableCodeTypeEnum.NO_CODE,
                            };

                            // 更新追溯码信息到外部goods
                            // eslint-disable-next-line vue/no-mutating-props
                            if (this.isNoTraceCodeGoods) {
                                this.goods.traceableCodeNoInfoList = [traceableCodeNoInfo];
                            } else {
                                this.goods.traceableCodeNoInfoList = [
                                    ...(this.goods.traceableCodeNoInfoList || []),
                                    traceableCodeNoInfo,
                                ];
                            }

                            // 补充无码追溯码-这里按照产品要求清除了原有的追溯码
                            if (TraceCode.isSupplementNoCodeGoods(this.goods)) {
                                const noTraceCodeObj = TraceCode.initNoTraceCodeObj({
                                    goods: this.goods,
                                    ...this.goodsCount,
                                });
                                this.traceableCodeList = [{
                                    ...noTraceCodeObj,
                                    traceableCodeNoInfo: noTraceCodeObj,
                                    keyId: createGUID(),
                                }];
                            } else {
                                this.traceableCodeList = [];
                            }
                            this.$emit('updateGoodsTraceableCodeNoInfo', this.goods);
                            this.$emit('update:goods', { ...this.goods });
                        } catch (e) {
                            console.error(e);
                            if (!e.alerted) {
                                this.$Toast({
                                    message: e.message,
                                    type: 'error',
                                });
                            }
                        }

                    },
                    onClose: () => {
                        this.resetResidentPopover();
                    },
                });
            },
            handleDelete() {
                console.log('全部删除');
                this.traceableCodeList = [];
            },
            handleDeleteItem(item, index) {
                console.log('删除', item, index);
                this.traceableCodeList.splice(index, 1);
            },
            handleViewDoc(id) {
                TraceCode.openTraceCodeGuide(id);
            },
            // 取消popover常驻状态
            resetResidentPopover() {
                // 当弹窗还存在时不取消常驻
                if (this._traceCodeRepeatDialog) return;
                if (this._traceCodeConfirmDialog) return;
                if (this._selectGoodsDialog) return;

                this._popoverTimer = setTimeout(() => {
                    this.residentPopover = false;
                }, 500);
            },
            calculateContentHeight() {
                const { innerHeight } = window;
                if (innerHeight <= 900) {
                    this.contentHeight = 400;
                }
                if (innerHeight <= 800) {
                    this.contentHeight = 380;
                }
                if (innerHeight <= 700) {
                    this.contentHeight = 330;
                }
                if (innerHeight <= 664) {
                    this.contentHeight = 312;
                }
            },
            validateEvent(_, callback) {
                if (this.needValidate && this.showError) {
                    callback({
                        validate: false,
                        message: this.errorTips,
                    });
                    return;
                }
                callback({
                    validate: true,
                });
            },
            isSupportTraceCodeForceCheckStock() {
                return TraceCode.isSupportTraceCodeForceCheckStock();
            },
            handleReferenceInputEnter() {
                if (this.popoverRenderControlled) {
                    this.showPopover = true;
                }
            },
        },
    };
</script>

<style lang="scss">
    .traceable-code-cell-popover {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;

        .traceable-code-cell-trigger {
            height: 100%;

            .abc-input-wrapper.is-readonly {
                .abc-input__inner {
                    cursor: pointer;
                }
            }

            .abc-input-wrapper.custom-readonly {
                .abc-input__inner {
                    border-bottom-color: transparent !important;
                }
            }

            .abc-input-wrapper[data-theme='warning-light'] {
                .abc-input__inner {
                    color: var(--abc-color-Y2);
                }
            }

            .abc-input-wrapper[data-theme='primary-light'] {
                .abc-input__inner {
                    color: var(--abc-color-B1);
                }
            }

            .abc-input-wrapper[data-theme='gray-light'] {
                .abc-input__inner {
                    color: var(--abc-color-T3);
                }
            }

            &.is-show-popover:hover {
                .abc-input-wrapper.is-readonly:not(.custom-readonly) {
                    .abc-input__inner {
                        position: relative;
                        border-color: var(--abc-color-theme3);
                        box-shadow: 0 0 0 2px #c3e0fe;
                    }
                }
            }
        }
    }
</style>
