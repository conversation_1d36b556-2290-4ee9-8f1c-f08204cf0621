<template>
    <ul
        class="shebao-code-detail-wrapper"
        :class="{
            'is-code-detail': isCodeDetailScene,
            'is-hover': isHoverScene,
            'is-search': isSearchScene
        }"
    >
        <li v-if="!isCodeDetailScene" class="code-item-detail ellipsis">
            <abc-flex :gap="8" align="center">
                <abc-text class="goods-name">
                    {{ shebaoCodeInfo.medicineCadn || shebaoCodeInfo.name }}
                </abc-text>

                <abc-text v-if="shebaoCodeInfo.packageUnit">
                    / {{ shebaoCodeInfo.packageUnit || '' }}
                </abc-text>

                <abc-text v-if="shebaoCodeInfo.medicalFeeGrade">
                    [{{ shebaoCodeInfo.medicalFeeGrade | medicalFeeGrade2Str }}]
                </abc-text>
            </abc-flex>
        </li>
        <li v-if="getSpecStr(shebaoCodeInfo)" class="code-item-detail">
            <span v-if="showSpecLabel" class="code-label">规格</span>
            <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ getSpecStr(shebaoCodeInfo) }}</span>
        </li>
        <li v-if="shebaoCodeInfo.packMaterialName" class="code-item-detail">
            <span class="code-label">包装</span>
            <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ shebaoCodeInfo.packMaterialName }}</span>
        </li>
        <li v-if="shebaoCodeInfo.manufacturer" class="code-item-detail">
            <span class="code-label">厂家</span>
            <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ shebaoCodeInfo.manufacturer }}</span>
        </li>

        <li class="code-item-detail">
            <span class="code-label">{{ codeTypeName }}</span>
            <span class="code-value ellipsis">{{ shebaoCodeInfo.shebaoCode }}</span>
        </li>
        <li v-if="!isHoverScene || (shebaoCodeInfo.approvalCode && isHoverScene) || (shebaoCodeInfo.standardCode && !isSearchScene)" class="code-item-detail">
            <div class="code-item-detail code-item-detail-flex" style="flex: 1;">
                <template v-if="!isHoverScene">
                    <span v-if="shebaoCodeInfo.approvalCode" class="code-label">准字</span>
                    <span v-if="shebaoCodeInfo.approvalCode" class="ellipsis">{{ shebaoCodeInfo.approvalCode }}</span>
                    <span v-if="shebaoCodeInfo && shebaoCodeInfo.enablePriceMarkupForChild">
                        儿童：{{ shebaoCodeInfo.enablePriceMarkupForChild }}
                    </span>
                </template>
                <template v-if="shebaoCodeInfo.approvalCode && isHoverScene">
                    <span class="code-label">
                        准字
                    </span>
                    <!--国药准字-->
                    <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ shebaoCodeInfo.approvalCode }}</span>
                </template>
            </div>
            <div v-if="shebaoCodeInfo.standardCode && !isSearchScene" class="code-item-detail code-item-detail-flex" style="width: 150px; max-width: 150px; margin-left: 16px;">
                <span class="code-label">本位码</span>
                {{ shebaoCodeInfo.standardCode }}
            </div>
        </li>


        <!--不限制在搜索中展示扩展字段-->
        <li v-for="item in getExtendInfoList(shebaoCodeInfo)" :key="item.value" class="code-item-detail">
            <span class="code-label">
                {{ item.key }}
            </span>
            <!--国药准字-->
            <span class="code-value ellipsis">{{ item.value }}</span>
        </li>
        <li v-if="isSupportShebaoListingPrice || listingPrice" class="code-item-detail">
            <template v-if="isSupportShebaoListingPrice">
                <span class="code-label">
                    挂网价
                </span>
                <span v-if="listingPrice" class="code-value ellipsis">
                    <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{ listingPrice|formatMoney }}/{{ listingPriceUnit }}
                </span>
                <span v-else class="code-value ellipsis">无</span>
            </template>
            <template v-else-if="listingPrice">
                <span class="code-label">
                    挂网价
                </span>
                <span class="code-value ellipsis">
                    <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{ listingPrice|formatMoney }}/{{ listingPriceUnit }}
                </span>
            </template>
        </li>
        <li class="code-item-detail ">
            <span
                class="ellipsis code-item-detail"
                :class="{ 'warn-price': isExceedPriceLimit }"
                style="min-width: 88px; margin-right: 12px; margin-bottom: 0;"
            >
                <span class="code-label">
                    限价
                </span>

                <template v-if="shebaoCodeInfo.priceLimit2">
                    成人
                </template>

                <template v-if="!isEmpty(shebaoCodeInfo.priceLimit)">
                    <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>
                    {{ shebaoCodeInfo.priceLimit }}/{{
                        shebaoCodeInfo.limitUnit || shebaoCodeInfo.packageUnit || defaultUnit
                    }}
                </template>
                <template v-else>
                    无
                </template>

                <template v-if="shebaoCodeInfo.priceLimit2">
                    / 儿童 <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{
                        `${shebaoCodeInfo.priceLimit2 || ''}${(shebaoCodeInfo.limitUnit || shebaoCodeInfo.packageUnit || defaultUnit)}（6周岁以下）`
                    }}
                </template>

                <span v-if="isExceedPriceLimit" class="warn-price">
                    定价超出限价
                    <template v-if="isGoingExceedPrice">
                        即将在 {{ shebaoDictInfo.shebaoCodeFutureStartDate | parseTime('y-m-d') }} 调整限价 <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>
                        {{ shebaoDictInfo.shebaoCodeFuturePriceLimited || '' }}
                    </template>
                </span>
            </span>
        </li>

        <!--使用限制-->
        <li v-if="restriction" class="code-item-detail">
            <span class="code-label">
                限制
            </span>
            <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ restriction }}</span>
        </li>

        <!--使用限制-->
        <li v-if=" shebaoCodeInfo.enablePriceMarkupForChild && isHoverScene" class="code-item-detail">
            <span class="code-label">
                儿童
            </span>
            <span class="code-value">{{ shebaoCodeInfo.enablePriceMarkupForChild }}</span>
        </li>
        <template v-if="!isCodeDetailScene && (isTreatment || isProject)">
            <!--项目内涵-->
            <li v-if="shebaoCodeInfo && shebaoCodeInfo.content" class="code-item-detail">
                <span class="code-label">
                    内涵
                </span>
                <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ shebaoCodeInfo.content }}</span>
            </li>
            <!--项目说明-->
            <li v-if="shebaoCodeInfo && shebaoCodeInfo.detail" class="code-item-detail">
                <span class="code-label">
                    说明
                </span>
                <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ shebaoCodeInfo.detail }}</span>
            </li>
        </template>
        <!--除外内容-->
        <li v-if="(isTreatment || isProject) && exception" class="code-item-detail">
            <span class="code-label">
                除外
            </span>
            <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ exception }}</span>
        </li>
        <!--  集采分类  -->
        <li v-if="showShebaoPurchaseType" class="code-item-detail">
            <span class="code-label">集采分类</span>
            <span class="code-value" :class="{ 'ellipsis': !isHoverScene }">{{ shebaoCodeInfo.purchaseType ?? '无' }}</span>
        </li>

        <li v-if="isTreatment && isHoverScene && shebaoCodeInfo.isExpired" style="margin-top: 4px;">
            <abc-tips
                icon
                theme="warning"
                size="small"
            >
                该对码项已失效，请更换对码项
            </abc-tips>
        </li>
    </ul>
</template>

<script>
    import {
        mapState, mapGetters,
    } from 'vuex';
    import { isNull } from 'utils/index.js';
    import { isChineseMedicine } from '@/filters/index.js';
    import {
        SocialCodeTypeEnum, ShebaoDetailSceneTypeEnum,
    } from './constant.js';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import useIsOpenSocialChainAdmin from 'views/inventory/hooks/useIsOpenSocialChainAdmin';


    export default {
        name: 'ShebaoCodeInfo',
        components: {
            AbcCurrencySymbolIcon,
        },
        props: {
            shebaoCodeInfo: {
                type: Object,
                required: true,
            },
            showSpecLabel: {
                type: Boolean,
                default: false,
            },
            type: {
                type: String,
            },
            defaultUnit: {
                type: String,
                default: '',
            },
            shebaoCodeType: {
                type: Number,
            },
            productInfo: {
                type: Object,
            },
            // 使用场景，hover时popover中展示，search下拉展示，对码详情展示
            sceneType: {
                type: String,
                default: 'codeDetail',
                validator: (prop) => {
                    return [
                        ShebaoDetailSceneTypeEnum.HOVER,
                        ShebaoDetailSceneTypeEnum.SEARCH,
                        ShebaoDetailSceneTypeEnum.CODE_DETAIL].includes(prop);
                },
            },
        },
        setup() {
            const { isHaveClinicOpen } = useIsOpenSocialChainAdmin();

            return {
                isHaveClinicOpen,
            };
        },
        data() {
            return {
                SocialCodeTypeEnum,
                ShebaoDetailSceneTypeEnum,
            };
        },
        computed: {
            /**
             * warningPriceExceed 限价提醒
             */
            ...mapState('socialPc', [
                'warningPriceExceed',
                'warningPriceExceedProvince',
                'warningPriceExceedNational',
                'isEnableListingPrice',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            isSupportShebaoListingPrice() {
                return !!this.isEnableListingPrice;
            },

            /**
             * @desc 限制在搜索列表中不占位
             * <AUTHOR>
             * @date 2022-07-11 10:27:15
             * @params
             * @return
             */
            restriction() {
                return this.isSearchScene ? this.shebaoCodeInfo?.restriction : (this.shebaoCodeInfo?.restriction || '-');
            },
            // 除外在搜索列表中不占位
            exception() {
                return this.isSearchScene ? this.shebaoCodeInfo?.exception : (this.shebaoCodeInfo?.exception || '-');
            },
            codeTypeName() {
                if (this.shebaoCodeType === SocialCodeTypeEnum.cityCode) {
                    return '市编码';
                }
                if (this.shebaoCodeType === SocialCodeTypeEnum.provinceCode) {
                    return '省编码';
                }
                return '国家码';
            },
            isGoods() {
                return this.type === 'goods';
            },
            isTreatment() {
                return this.type === 'diagnosis-treatment';
            },
            isProject() {
                return this.type === 'project';
            },
            isHoverScene() {
                return this.sceneType === ShebaoDetailSceneTypeEnum.HOVER;
            },
            isSearchScene() {
                return this.sceneType === ShebaoDetailSceneTypeEnum.SEARCH;
            },
            isCodeDetailScene() {
                return this.sceneType === ShebaoDetailSceneTypeEnum.CODE_DETAIL;
            },
            /**
             * @desc 获取对应目录限价的配置
             * <AUTHOR>
             * @date 2021-08-30 20:11:45
             */
            getWarningPriceExceed() {
                if (this.shebaoCodeType === SocialCodeTypeEnum.cityCode) {
                    return this.warningPriceExceed;
                }
                if (this.shebaoCodeType === SocialCodeTypeEnum.provinceCode) {
                    return this.warningPriceExceedProvince;
                }
                if (this.shebaoCodeType === SocialCodeTypeEnum.nationalCode) {
                    return this.warningPriceExceedNational;
                }
                return false;
            },
            isExceedPriceLimit() {
                if (!this.isCodeDetailScene) return false;
                if (!this.getWarningPriceExceed) return false;
                if (this.shebaoCodeInfo) {
                    if (this.shebaoCodeInfo.priceLimit === null) return false;
                    return this.getGoodsPrice > this.shebaoCodeInfo.priceLimit;
                }
                return false;
            },
            // 获取商品的价格
            getGoodsPrice() {
                let price = 0;
                // 需要使用小单位的价格, 同限价比较
                if (this.shebaoCodeInfo.limitUnitType === 1) {
                    if (this.productInfo.dismounting) {
                        price = this.productInfo.piecePrice;
                    } else {
                        price = this.productInfo.packagePrice / this.productInfo.pieceNum;
                    }
                } else {
                    price = isChineseMedicine(this.productInfo) ? this.productInfo.piecePrice : this.productInfo.packagePrice;
                }
                return price;
            },
            shebaoDictInfo() {
                return this.shebaoCodeInfo.shebaoDictInfo || {};
            },
            listingPrice() {
                return this.shebaoCodeInfo.listingPrice || this.shebaoDictInfo.listingPrice || 0;
            },
            // 挂网价单位
            listingPriceUnit() {
                return this.shebaoCodeInfo.listingUnit || this.shebaoCodeInfo.packageUnit || '';
            },
            /**
             * @desc 即将超限价
             */
            isGoingExceedPrice() {
                if (!this.getWarningPriceExceed) return false;
                if (!this.shebaoDictInfo.shebaoCodeFutureStartDate) return false;
                if (this.shebaoCodeInfo) {
                    const goodsPrice = isChineseMedicine(this.productInfo) ?
                        this.productInfo.piecePrice :
                        this.productInfo.packagePrice;
                    return goodsPrice > this.shebaoDictInfo.shebaoCodeFuturePriceLimited;
                }
                return false;
            },
            showShebaoPurchaseType() {
                const {
                    goodsType, goodsSubType,
                } = this.shebaoCodeInfo;
                // 是中西成药
                const isPatentMedicine = goodsType === GoodsTypeEnum.MEDICINE && (
                    GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine === goodsSubType ||
                    GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM === goodsSubType
                );
                return (isPatentMedicine || isChineseMedicine({
                    type: goodsType,
                    subType: goodsSubType,
                })) && this.$abcSocialSecurity.region === 'liaoning_shenyang';
            },
        },
        methods: {
            isEmpty(val) {
                return isNull(val);
            },
            getSpecStr(item) {
                if (this.isGoods) {
                    return item.spec || item.dosageForm || '';
                }
                return '';
            },
            getExtendInfoList(item) {
                return item?.originalDictContent?.extendInfo?.extendInfoUi || [];
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/theme.scss';

.shebao-code-detail-wrapper {
    font-size: 12px;
    line-height: 16px;
    color: #aab4bf;

    &.is-code-detail {
        min-width: 120px;
        max-width: 100%;
        padding: 4px 6px;
        margin-top: 4px;
        border: 1px dashed #dadbe0;
        border-radius: var(--abc-border-radius-small);
    }

    .goods-name {
        font-weight: bold;
        color: $T1;
    }

    .code-item-detail {
        display: flex;
        align-items: flex-start;

        &-flex {
            display: flex;
            align-items: flex-start;
            //width: calc((100% - 24px)/2);
        }

        span {
            font-size: 12px;
        }

        .code-label {
            position: relative;
            display: inline-block;
            min-width: 36px;
            margin-right: 12px;
            text-align: justify;
            text-align-last: justify;

            &::after {
                position: absolute;
                right: -14px;
                content: '\ff1a';
            }
        }

        .code-value {
            flex: 1;
        }

        .warn-price {
            color: #fba300;

            .code-label {
                &::after {
                    color: #fba300;
                }
            }
        }
    }
}
</style>
