<template>
    <div>
        <abc-popover
            width="360px"
            placement="top"
            trigger="hover"
            theme="yellow"
            :open-delay="200"
            popper-class="shebao-code-detail-wrapper-v2-popover"
        >
            <ul
                slot="reference"
                class="shebao-code-detail-wrapper-v2"
            >
                <div>
                    <template v-if="showBasicInfo">
                        <div
                            style="display: flex; flex-wrap: wrap; gap: 8px 16px; line-height: 16px;"
                        >
                            <abc-text
                                v-if="getSpecStr(shebaoCodeInfo) || shebaoCodeInfo.manufacturer"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">规格</span>{{ getSpecStr(shebaoCodeInfo) || shebaoCodeInfo.manufacturer || '无' }}
                            </abc-text>
                            <abc-text
                                v-if="isMaterial || isMedicine"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">包装</span>{{ shebaoCodeInfo.packMaterialName || '无' }}
                            </abc-text>
                            <abc-text
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">厂家</span>{{ shebaoCodeInfo.manufacturer || '无' }}
                            </abc-text>
                            <abc-text
                                :theme="isDiffMedicineNmpn ? 'warning-light' : 'gray'"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">准字</span>{{ shebaoCodeInfo.approvalCode || '无' }}
                            </abc-text>
                            <abc-text
                                v-if="shebaoCodeInfo.standardCode"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 412px; min-width: 412px;"
                            >
                                <span class="code-label">本位码</span>{{ shebaoCodeInfo.standardCode }}
                            </abc-text>
                            <abc-text
                                v-if="isSupportShebaoListingPrice"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <template v-if="listingPrice">
                                    <span class="code-label">挂网价</span><abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{ listingPrice|formatMoney }}/{{ listingPriceUnit }}
                                </template>
                                <template v-else>
                                    <span class="code-label">挂网价</span>无
                                </template>
                            </abc-text>
                            <abc-text
                                v-else-if="listingPrice"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">挂网价</span><abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{ listingPrice|formatMoney }}/{{ listingPriceUnit }}
                            </abc-text>
                            <abc-text
                                v-if="shebaoCodeInfo.priceLimit || shebaoCodeInfo.priceLimit2"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">限价</span><template v-if="shebaoCodeInfo.priceLimit2">
                                    成人
                                </template><template v-if="!isEmpty(shebaoCodeInfo.priceLimit)">
                                    <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{ shebaoCodeInfo.priceLimit }}/{{ shebaoCodeInfo.limitUnit || shebaoCodeInfo.packageUnit || defaultUnit }}
                                </template><template v-else>
                                    无
                                </template>
                                <template v-if="shebaoCodeInfo.priceLimit2">
                                    / 儿童 <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{
                                        `${shebaoCodeInfo.priceLimit2 || ''}${(shebaoCodeInfo.limitUnit || shebaoCodeInfo.packageUnit || '')}（6周岁以下）`
                                    }}
                                </template>
                                <template v-if="isExceedPriceLimit">
                                    定价超出限价
                                    <template v-if="isGoingExceedPrice">
                                        即将在 {{ shebaoDictInfo.shebaoCodeFutureStartDate | parseTime('y-m-d') }} 调整限价 {{ $t('currencySymbol') }} {{
                                            shebaoDictInfo.shebaoCodeFuturePriceLimited || ''
                                        }}
                                    </template>
                                </template>
                            </abc-text>
                            <abc-text
                                v-else
                                :theme="isExceedPriceLimit ? 'warning-light' : 'gray'"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">限价</span>无
                            </abc-text>
                            <abc-text
                                v-if="restriction"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">限制</span>{{ `${ restriction }` }}
                            </abc-text>
                        </div>
                    </template>
                    <template v-else>
                        <div
                            style="display: flex; flex-wrap: wrap; gap: 8px 16px; line-height: 16px;"
                        >
                            <abc-text
                                v-if="shebaoCodeInfo.standardCode"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 412px; min-width: 412px;"
                            >
                                <span class="code-label">本位码</span>{{ shebaoCodeInfo.standardCode }}
                            </abc-text>
                            <abc-text
                                v-if="isSupportShebaoListingPrice"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <template v-if="listingPrice">
                                    <span class="code-label">挂网价</span><abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{ listingPrice|formatMoney }}/{{ listingPriceUnit }}
                                </template>
                                <template v-else>
                                    <span class="code-label">挂网价</span>无
                                </template>
                            </abc-text>
                            <abc-text
                                v-else-if="listingPrice"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">挂网价</span><abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{ listingPrice|formatMoney }}/{{ listingPriceUnit }}
                            </abc-text>
                            <abc-text
                                v-if="shebaoCodeInfo.priceLimit || shebaoCodeInfo.priceLimit2"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">限价</span><template v-if="shebaoCodeInfo.priceLimit2">
                                    成人
                                </template><template v-if="!isEmpty(shebaoCodeInfo.priceLimit)">
                                    <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{ shebaoCodeInfo.priceLimit }}/{{ shebaoCodeInfo.limitUnit || shebaoCodeInfo.packageUnit || defaultUnit }}
                                </template><template v-else>
                                    无
                                </template>
                                <template v-if="shebaoCodeInfo.priceLimit2">
                                    / 儿童 <abc-currency-symbol-icon :size="12"></abc-currency-symbol-icon>{{
                                        `${shebaoCodeInfo.priceLimit2 || ''}${(shebaoCodeInfo.limitUnit || shebaoCodeInfo.packageUnit || defaultUnit)}（6周岁以下）`
                                    }}
                                </template>
                                <template v-if="isExceedPriceLimit">
                                    定价超出限价
                                    <template v-if="isGoingExceedPrice">
                                        即将在 {{ shebaoDictInfo.shebaoCodeFutureStartDate | parseTime('y-m-d') }} 调整限价 {{ $t('currencySymbol') }} {{
                                            shebaoDictInfo.shebaoCodeFuturePriceLimited || ''
                                        }}
                                    </template>
                                </template>
                            </abc-text>
                            <abc-text
                                v-else
                                :theme="isExceedPriceLimit ? 'warning-light' : 'gray'"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">限价</span>无
                            </abc-text>
                            <abc-text
                                v-if="restriction"
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">限制</span>{{ `${ restriction }` }}
                            </abc-text>
                            <abc-text
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">内涵</span>{{ `${ shebaoCodeInfo.content || '无' }` }}
                            </abc-text>
                            <abc-text
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">说明</span>{{ `${ shebaoCodeInfo.detail || '无' }` }}
                            </abc-text>
                            <abc-text
                                theme="gray"
                                size="mini"
                                class="ellipsis"
                                style="display: inline-block; width: 198px; min-width: 198px;"
                            >
                                <span class="code-label">除外</span>{{ `${ shebaoCodeInfo.exception || '无'}` }}
                            </abc-text>
                        </div>
                    </template>

                    <!--不限制在搜索中展示扩展字段-->
                    <li v-for="item in getExtendInfoList(shebaoCodeInfo)" :key="item.value" class="code-item-detail ">
                        <span class="code-label">
                            {{ item.key }}
                        </span>
                        <!--国药准字-->
                        <span class="code-value ellipsis">{{ item.value || '无' }}</span>
                    </li>

                    <li v-if="shebaoCodeInfo && shebaoCodeInfo.enablePriceMarkupForChild" class="code-item-detail">
                        <span>
                            儿童：{{ shebaoCodeInfo.enablePriceMarkupForChild }}
                        </span>
                    </li>

                    <!--除外内容-->
                    <li v-if="isTreatment && exception" class="code-item-detail">
                        <span class="code-label">
                            除外
                        </span>
                        <span class="code-value ellipsis">{{ exception }}</span>
                    </li>
                    <!--  集采分类  -->
                    <li v-if="showShebaoPurchaseType" class="code-item-detail ">
                        <span class="code-label">集采分类</span>
                        <span class="code-value ellipsis">{{ shebaoCodeInfo.purchaseType ?? '无' }}</span>
                    </li>
                </div>

                <li v-if="verifyList.length" class="code-item-detail code-item-verify">
                    <abc-icon size="12" icon="n-alert-fill" :color="$store.state.theme.style.Y2"></abc-icon>
                    <abc-flex vertical gap="small">
                        <abc-text>合规风险：可能导致结算超刷超限，进销存上报错误。</abc-text>
                        <ul>
                            <li v-for="(item, index) in verifyList" :key="index">
                                {{ item.label }}
                                <abc-link
                                    v-if="item.canModify"
                                    size="small"
                                    style="font-size: 12px;"
                                    @click="openVerifyDialog"
                                >
                                    点此解决
                                </abc-link>
                            </li>
                        </ul>
                    </abc-flex>
                </li>
            </ul>
            <shebao-code-detail
                :shebao-code-type="shebaoCodeType"
                :shebao-code-info="shebaoCodeInfo"
                :default-unit="defaultUnit"
                scene-type="hover"
                :show-spec-label="true"
                :type="isProject ? 'project' : type"
            ></shebao-code-detail>
        </abc-popover>
    </div>
</template>

<script>
    import {
        mapGetters, mapState,
    } from 'vuex';
    import { isNull } from 'utils/index.js';
    import { isChineseMedicine } from '@/filters/index.js';
    import {
        SocialCodeTypeEnum, ShebaoDetailSceneTypeEnum, ShebaoSearchTypeEnum,
    } from './constant.js';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import i18n from '@/i18n';
    import ShebaoCodeVerifyDialog from './shebao-code-verify-dialog';
    import AbcShebaoPopver from '@/views/inventory/components/social-code-autocomplete/directive/shebao-popper.js';
    import { disabledBaseInfoKey } from 'views/inventory/goods/archives/provideKeys';
    import GoodsV3API from 'api/goods/index-v3';
    import { isEqual } from 'utils/lodash';
    import useIsOpenSocialChainAdmin from 'views/inventory/hooks/useIsOpenSocialChainAdmin';
    const ShebaoCodeDetail = () => import('views/inventory/components/social-code-autocomplete/shebao-code-detail.vue');

    export default {
        name: 'ShebaoCodeDetailV2',
        components: {
            AbcCurrencySymbolIcon,
            ShebaoCodeDetail,
        },
        directives: { AbcShebaoPopver },
        inject: {
            disabledBaseInfo: {
                from: disabledBaseInfoKey,
                default: false,
            },
        },
        props: {
            shebaoCodeInfo: {
                type: Object,
                required: true,
            },
            isProject: {
                type: Boolean,
            },
            type: {
                type: String,
            },
            shebaoCodeType: {
                type: Number,
            },
            showBasicInfo: {
                type: Boolean,
                default: true,
            },
            productInfo: {
                type: Object,
            },
            // 使用场景，hover时popover中展示，search下拉展示，对码详情展示
            sceneType: {
                type: String,
                default: 'codeDetail',
                validator: (prop) => {
                    return [
                        ShebaoDetailSceneTypeEnum.HOVER,
                        ShebaoDetailSceneTypeEnum.SEARCH,
                        ShebaoDetailSceneTypeEnum.CODE_DETAIL].includes(prop);
                },
            },
            // 0 默认 1 中药 2 材料 3 费用项目
            searchTableViewType: {
                type: Number,
                default: ShebaoSearchTypeEnum.MEDICINE,
            },
        },
        setup() {
            const { isHaveClinicOpen } = useIsOpenSocialChainAdmin();

            return {
                isHaveClinicOpen,
            };
        },
        data() {
            return {
                SocialCodeTypeEnum,
                ShebaoDetailSceneTypeEnum,

                isDiffSpec: false,
            };
        },
        computed: {
            /**
             * warningPriceExceed 限价提醒
             */
            ...mapState('socialPc', [
                'warningPriceExceed',
                'warningPriceExceedProvince',
                'warningPriceExceedNational',
            ]),
            ...mapGetters([
                'isChainAdmin',
                'isSingleStore',
                'isEnableListingPrice',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            defaultUnit() {
                if (this.isChinese) {
                    return 'g';
                }
                return '';
            },
            isMedicine() {
                return this.searchTableViewType === ShebaoSearchTypeEnum.MEDICINE;
            },
            isChinese() {
                return this.searchTableViewType === ShebaoSearchTypeEnum.MEDICINE_CHINESE;
            },
            isMaterial() {
                return this.searchTableViewType === ShebaoSearchTypeEnum.MATERIAL;
            },
            isSupportShebaoListingPrice() {
                return this.isEnableListingPrice;
            },
            /**
             * @desc 限制在搜索列表中不占位
             * <AUTHOR>
             * @date 2022-07-11 10:27:15
             * @params
             * @return
             */
            restriction() {
                return (this.shebaoCodeInfo?.restriction || '无');
            },
            // 除外在搜索列表中不占位
            exception() {
                return (this.shebaoCodeInfo?.exception || '无');
            },
            isGoods() {
                return this.type === 'goods';
            },
            isTreatment() {
                return this.type === 'diagnosis-treatment';
            },
            isCodeDetailScene() {
                return this.sceneType === ShebaoDetailSceneTypeEnum.CODE_DETAIL;
            },
            /**
             * @desc 获取对应目录限价的配置
             * <AUTHOR>
             * @date 2021-08-30 20:11:45
             */
            getWarningPriceExceed() {
                if (this.shebaoCodeType === SocialCodeTypeEnum.cityCode) {
                    return this.warningPriceExceed;
                }
                if (this.shebaoCodeType === SocialCodeTypeEnum.provinceCode) {
                    return this.warningPriceExceedProvince;
                }
                if (this.shebaoCodeType === SocialCodeTypeEnum.nationalCode) {
                    return this.warningPriceExceedNational;
                }
                return false;
            },
            // 获取商品的价格
            getGoodsPrice() {
                let price = 0;
                // 需要使用小单位的价格, 同限价比较
                if (this.shebaoCodeInfo.limitUnitType === 1) {
                    if (this.productInfo.dismounting) {
                        price = this.productInfo.piecePrice;
                    } else {
                        price = this.productInfo.packagePrice / this.productInfo.pieceNum;
                    }
                } else {
                    price = isChineseMedicine(this.productInfo) ? this.productInfo.piecePrice : this.productInfo.packagePrice;
                }
                return price;
            },
            shebaoDictInfo() {
                return this.shebaoCodeInfo.shebaoDictInfo || {};
            },
            listingPrice() {
                return this.shebaoCodeInfo.listingPrice || this.shebaoDictInfo.listingPrice || 0;
            },
            // 挂网价单位
            listingPriceUnit() {
                return this.shebaoCodeInfo.listingUnit || this.shebaoCodeInfo.packageUnit || '';
            },
            isExceedPriceLimit() {
                if (!this.getWarningPriceExceed) return false;
                if (this.shebaoCodeInfo) {
                    if (this.shebaoCodeInfo.priceLimit === null) return false;
                    return this.getGoodsPrice > this.shebaoCodeInfo.priceLimit;
                }
                return false;
            },
            /**
             * @desc 即将超限价
             */
            isGoingExceedPrice() {
                if (!this.getWarningPriceExceed) return false;
                if (!this.shebaoDictInfo.shebaoCodeFutureStartDate) return false;
                if (this.shebaoCodeInfo) {
                    const goodsPrice = isChineseMedicine(this.productInfo) ?
                        this.productInfo.piecePrice :
                        this.productInfo.packagePrice;
                    return goodsPrice > this.shebaoDictInfo.shebaoCodeFuturePriceLimited;
                }
                return false;
            },
            showShebaoPurchaseType() {
                const {
                    goodsType, goodsSubType,
                } = this.shebaoCodeInfo;
                // 是中西成药
                const isPatentMedicine = goodsType === GoodsTypeEnum.MEDICINE && (
                    GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine === goodsSubType ||
                    GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM === goodsSubType
                );
                return (isPatentMedicine || isChineseMedicine({
                    type: goodsType,
                    subType: goodsSubType,
                })) && this.$abcSocialSecurity.region === 'liaoning_shenyang';
            },
            isDiffMedicineNmpn() {
                const approvalCode = this.shebaoCodeInfo.approvalCode || '';
                const approvalCodeList = approvalCode.split('；').map((item) => this.getMedicineNmpn(item)).filter((item) => item);
                const medicineNmpn = this.getMedicineNmpn(this.productInfo.medicineNmpn);
                return medicineNmpn &&
                    approvalCodeList.length &&
                    !approvalCodeList.find((item) => item === medicineNmpn);
            },
            verifyList() {
                const verifyList = [];
                if (this.isDiffMedicineNmpn) {
                    verifyList.push({
                        label: 'ABC 系统档案与医保目录准字不一致，请确定是否对码错误。',
                    });
                }
                if (this.isDiffSpec) {
                    console.log('productInfo', this.productInfo);
                    console.log('shebaoCodeInfo', this.shebaoCodeInfo);
                    verifyList.push({
                        label: 'ABC 系统档案与医保目录规格无法对应。请确定是否对码错误或规格填写错误',
                        canModify: (this.isChainAdmin || this.isSingleStore) &&
                            !this.disabledBaseInfo &&
                            !(this.productInfo.pieceUnit === this.shebaoCodeInfo.pieceUnit && this.productInfo.packageUnit === this.shebaoCodeInfo.packageUnit && this.productInfo.pieceNum !== this.shebaoCodeInfo.pieceNum),
                    });
                }
                return verifyList;
            },
        },
        watch: {
            'productInfo.pieceNum': {
                handler(val, oldVal) {
                    if (!isEqual(val, oldVal)) {
                        this.verifyUnitSpec();
                    }
                },
            },
            'productInfo.pieceUnit': {
                handler(val, oldVal) {
                    if (!isEqual(val, oldVal)) {
                        this.verifyUnitSpec();
                    }
                },
            },
            'productInfo.packageUnit': {
                handler(val, oldVal) {
                    if (!isEqual(val, oldVal)) {
                        this.verifyUnitSpec();
                    }
                },
            },
            shebaoCodeInfo: {
                handler(val, oldVal) {
                    if (!isEqual(val, oldVal)) {
                        this.verifyUnitSpec();
                    }
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            isEmpty(val) {
                return isNull(val);
            },
            getSpecStr(item) {
                if (this.isGoods) {
                    return item.spec || item.dosageForm || '';
                }
                return '规格：无';
            },
            getExtendInfoList(item) {
                return item?.originalDictContent?.extendInfo?.extendInfoUi || [];
            },
            getPriceLimit(row) {
                const {
                    priceLimit2, priceLimit,
                } = row;
                if (!priceLimit && !priceLimit2) {
                    return '限价：无';
                }
                const text = `/ 儿童 ${i18n.t('currencySymbol')}${priceLimit2 || ''}${(row.limitUnit || row.packageUnit || this.defaultUnit)}（6周岁以下）`;
                let warnText = '';
                if (this.isExceedPriceLimit) {
                    warnText += '定价超出限价';
                    if (this.isGoingExceedPrice) {
                        warnText += `即将在 ${this.shebaoDictInfo.shebaoCodeFutureStartDate | this.parseTime('y-m-d')} 调整限价 ${i18n.t('currencySymbol')} ${this.shebaoDictInfo.shebaoCodeFuturePriceLimited || ''}`;
                    }
                }
                return `限价：${priceLimit2 ? '成人' : ''}${i18n.t('currencySymbol')}${!this.isEmpty(priceLimit) ? priceLimit : '-'}${row.limitUnit || row.packageUnit || this.defaultUnit}${priceLimit2 ? text : ''}${warnText}`;
            },
            getMedicineNmpn(string = '') {
                return string.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
            },
            openVerifyDialog() {
                new ShebaoCodeVerifyDialog({
                    productInfo: this.productInfo,
                    shebaoCodeInfo: this.shebaoCodeInfo,
                    modifyProductInfo: this.handleModifyProductInfo,
                }).generateDialog({ parent: this });
            },
            handleModifyProductInfo(val) {
                this.$emit('modify-product-info', val);
            },
            async verifyUnitSpec() {
                try {
                    const {
                        type,
                        subType,
                        pieceNum,
                        pieceUnit,
                        packageUnit,
                        medicineNmpn,
                    } = this.productInfo;
                    const {
                        pieceNum: shebaoPieceNum,
                        pieceUnit: shebaoPieceUnit,
                        packageUnit: shebaoPackageUnit,
                        approvalCode: shebaoMedicineNmpn,
                        specialPriceLimit,
                        name,
                    } = this.shebaoCodeInfo;
                    if ((!!pieceNum && !!shebaoPieceNum) && ((!!pieceUnit && !!shebaoPieceUnit) || (!!packageUnit && !!shebaoPieceUnit))) {
                        const { data } = await GoodsV3API.getCollectCodeCountList({
                            shebaoRegion: window.$abcSocialSecurity.region,
                            shebaoHisType: window.$abcSocialSecurity.basicInfo?.hospitalType || '1',
                            scene: 10,
                            list: [{
                                goodsType: type,
                                goodsSubType: subType,
                                medicineNmpn,
                                pieceNum,
                                pieceUnit,
                                pieceCount: null,
                                packageUnit,
                                packageCount: 1,
                                shebaoPieceNum,
                                shebaoPieceUnit,
                                shebaoPackageUnit,
                                shebaoMedicineNmpn,
                                specialPriceLimit,
                                shebaoMedicineName: name,
                            }],
                        });
                        // transformWarn 是否需要警示
                        this.isDiffSpec = data.list[0]?.transformWarn === 1;
                    }
                } catch (e) {
                    this.isDiffSpec = false;
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/theme.scss';

.shebao-code-detail-wrapper-v2 {
    width: 100%;
    padding: 8px 8px;
    margin-top: 8px;
    font-size: 12px;
    line-height: 16px;
    color: $T2;
    border: 1px dashed $P6;
    border-radius: var(--abc-border-radius-small);

    .code-label {
        position: relative;
        display: inline-block;
        min-width: 36px;
        margin-right: 12px;
        text-align: justify;
        text-align-last: justify;

        &::after {
            position: absolute;
            right: -14px;
            content: '\ff1a';
        }
    }

    .code-item-detail {
        display: flex;
        align-items: flex-start;
        margin-top: 8px;
        line-height: 16px;
        color: $T2;

        span {
            font-size: 12px;
        }

        .code-value {
            flex: 1;
        }

        &.code-item-verify {
            gap: 4px;
            align-items: baseline;
            color: var(--abc-color-Y2);

            ul {
                padding-left: 12px;
                list-style: disc;
            }
        }
    }
}

.shebao-code-detail-wrapper-v2-popover {
    .shebao-code-detail-wrapper {
        color: $T1;

        .code-label {
            color: $T2;
        }

        .code-item-detail {
            min-height: 22px;
            margin-bottom: 0;
            line-height: 22px;
        }
    }
}
</style>
