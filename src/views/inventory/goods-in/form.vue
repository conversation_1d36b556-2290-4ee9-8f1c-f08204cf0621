<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        :title="dialogTitle"
        responsive
        append-to-body
        class="goods-in-wrapper"
        data-cy="inventory-goods-in-add-dialog"
        :before-close="closeDialog"
        :auto-focus="!isImportOrder"
        size="hugely"
        :disabled-keyboard="disabledKeyboard"
        @open="dialogOpen"
        @close="popDialogName"
    >
        <abc-form
            ref="createForm"
            v-abc-loading="loading"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-form-item-group is-excel>
                        <abc-descriptions
                            :column="4"
                            :label-width="88"
                            background
                            grid
                            size="large"
                            stretch-last-item
                        >
                            <abc-descriptions-item
                                :span="1"
                                content-class-name="ellipsis"
                                label="入库人"
                            >
                                <span>{{ userInfo?.name ?? '' }}</span>
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                :span="1"
                                :content-padding="(isChainAdmin && canEdit) ? '0px' : ''"
                                content-class-name="ellipsis"
                                label="入库门店"
                            >
                                <template v-if="isChainAdmin && canEdit">
                                    <abc-form-item required>
                                        <clinic-select
                                            v-model="order.toOrganId"
                                            :show-all-clinic="false"
                                            adaptive-width
                                            @change="changeClinic"
                                        ></clinic-select>
                                    </abc-form-item>
                                </template>
                                <span v-else v-abc-title.ellipsis="currentClinic?.clinicName || '-'"></span>
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                v-if="!!multiPharmacyCanUse"
                                :span="1"
                                content-class-name="ellipsis"
                                :content-padding="isSummary ? '0px' : ''"
                                label="入库库房"
                            >
                                <abc-form-item v-if="isSummary" required>
                                    <abc-select
                                        ref="supplierSelect"
                                        v-model="order.pharmacyNo"
                                        :custom-class="['supplier-select']"
                                        :max-width="220"
                                        @change="onPharmacyNoChange"
                                    >
                                        <abc-option
                                            v-for="it in goodsInPharmacyList"
                                            :key="`${it.no }`"
                                            :label="it.name"
                                            :value="it.no"
                                        >
                                        </abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="order.pharmacy?.name || '-'"></span>
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                :span="1"
                                :content-padding="(!virtualSupplierInfo && canEdit) ? '0px' : ''"
                                content-class-name="ellipsis"
                                label="供应商"
                            >
                                <abc-form-item v-if="!virtualSupplierInfo && canEdit" required>
                                    <abc-select
                                        id="supplierSelect"
                                        ref="supplierSelect"
                                        v-model="order.supplierId"
                                        :custom-class="['supplier-select', 'supplier-select-btn', `${pharmacyType ? 'is-virtual-supplier' : ''}`]"
                                        with-search
                                        :fetch-suggestions="fetchSuggestions"
                                        :max-width="160"
                                        inner-width="280px"
                                        setting
                                        setting-text="新增供应商"
                                        setting-icon="n-add-line-medium"
                                        data-cy="inventory-goods-in-add-supplier-select"
                                        @set="openSupplierDialog"
                                        @open="openSupplierSelectPanel"
                                        @change="changeSupplier"
                                    >
                                        <abc-option
                                            v-for="it in currentSupplierList"
                                            :key="`${it.id }`"
                                            :label="it.name"
                                            :value="it.id"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="order.supplierName || order.supplier || '-'"></span>
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                :span="1"
                                :content-padding="(canEdit) ? '0px' : ''"
                                content-class-name="ellipsis"
                                label="随货单号"
                            >
                                <abc-form-item v-if="canEdit">
                                    <abc-input
                                        v-model="order.outOrderNo"
                                        :max-length="50"
                                        clearable
                                        @change="autoSaveDraftAsync"
                                    ></abc-input>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="order.outOrderNo || '-'"></span>
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                v-if="isShowPurchaseType"
                                :span="1"
                                :content-padding="'0px'"
                                content-class-name="ellipsis"
                                label="采购方式"
                            >
                                <abc-form-item>
                                    <abc-select
                                        v-model="order.purchaseType"
                                        :max-width="160"
                                        @change="handlePurchaseTypeChange"
                                    >
                                        <abc-option
                                            v-for="it in options.purchaseTypeList"
                                            :key="`${it.value }`"
                                            :label="it.label"
                                            :value="it.value"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                v-if="isShebaoCollect"
                                :span="1"
                                :content-padding="'0px'"
                                content-class-name="ellipsis"
                                label="采购平台"
                            >
                                <abc-form-item required>
                                    <abc-select
                                        v-model="order.platformType"
                                        :max-width="160"
                                        @change="autoSaveDraftAsync"
                                    >
                                        <abc-option
                                            v-for="it in options.platformTypeList"
                                            :key="`${it.value }`"
                                            :label="it.label"
                                            :value="it.value"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                :span="1"
                                :content-padding="(canEdit) ? '0px' : ''"
                                content-class-name="ellipsis"
                                label="验收人"
                            >
                                <abc-form-item v-if="canEdit" required>
                                    <employee-select
                                        v-model="order.inspectBy"
                                        with-search
                                        :employee-list="stockEmployeeList"
                                        adaptive-width
                                        data-cy="inventory-goods-in-add-inspect-by-select"
                                        @change="handleEmployeeChange"
                                    >
                                    </employee-select>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="order.inspectUser?.name ?? '-'"></span>
                            </abc-descriptions-item>
                            <abc-descriptions-item
                                :content-padding="(canEdit) ? '0px' : ''"
                                content-class-name="ellipsis"
                                label="备注"
                            >
                                <abc-form-item v-if="canEdit">
                                    <abc-input
                                        v-model="order.comment"
                                        :max-length="200"
                                        @change="autoSaveDraftAsync"
                                    ></abc-input>
                                </abc-form-item>
                                <span v-else v-abc-title.ellipsis="order.comment || '-'"></span>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        :render-config="tableConfigRender"
                        :data-list="order.list"
                        empty-size="small"
                        need-delete-confirm
                        support-delete-tr
                        :show-hover-tr-bg="false"
                        cell-size="large"
                        :virtual-list-config="{
                            bufferSize: 10,
                            // threshold: 60,// 为了不自动开启虚拟列表滚动
                            rowHeight: 49
                        }"
                        :custom-tr-key="customTrKey"
                        :custom-tr-class="customTrClass"
                        @delete-tr="deleteIt"
                    >
                        <template #topHeader>
                            <abc-flex
                                v-if="!mallOrderId"
                                align="center"
                                justify="space-between"
                                flex="1"
                            >
                                <goods-auto-complete-cover-title
                                    ref="goodsAutoCompleteRef"
                                    class="entry-medicine back-focus-to-autocomplete"
                                    data-cy="inventory-goods-in-search-input"
                                    placeholder="扫条形码 / 扫追溯码 / 输入名称或拼音首字母"
                                    :search="searchKey"
                                    :enable-barcode-detector="enableBarcodeDetector"
                                    :width="460"
                                    need-filter-disable
                                    :clinic-id="isChainAdmin ? order.toOrganId : clinicId"
                                    :inorder-config="0"
                                    format-count-key="stock"
                                    size="medium"
                                    focus-show
                                    show-empty
                                    :resident-sugguestions="enableBarcodeDetector"
                                    enable-local-search
                                    is-close-validate
                                    :next-input-auto-focus="false"
                                    :type-id-list="isVirtualPharmacy ? chineseTypeIdList : []"
                                    :format-search-result-fn="formatSearchResultFn"
                                    is-has-no-medicine
                                    @selectGoods="selectGoods"
                                    @hasNoMedicine="addMedicine(GoodsTypeEnum.MEDICINE, $event)"
                                    @traceableCodeEnter="traceableCodeEnter"
                                >
                                    <abc-icon slot="prepend" icon="n-add-line-medium" color="var(--abc-color-T3)"></abc-icon>

                                    <div
                                        v-if="isAdmin"
                                        slot="fixed-footer"
                                        class="inventory__fixed-footer-wrapper"
                                        @click.stop=""
                                    >
                                        <add-archive-dropdown
                                            is-custom-change
                                            :is-show-toast="false"
                                            :success-callback="selectGoods"
                                            @change="addMedicine"
                                        ></add-archive-dropdown>
                                    </div>
                                </goods-auto-complete-cover-title>
                                <abc-button
                                    v-if="!pharmacyType"
                                    variant="ghost"
                                    theme="default"
                                    @click="showImportDialog = true"
                                >
                                    导入随货单
                                </abc-button>
                            </abc-flex>
                        </template>
                        <template #footer>
                            <goods-in-table-footer :list="order.list"></goods-in-table-footer>
                        </template>
                        <!--药品编码-->
                        <template #shortId="{ trData: { goods } }">
                            <abc-table-cell class="ellipsis">
                                <overflow-tooltip v-if="goods?.shortId" :content="goods?.shortId"></overflow-tooltip>
                                <abc-text v-else theme="warning-light">
                                    未匹配
                                </abc-text>
                            </abc-table-cell>
                        </template>
                        <!--药品名称-->
                        <template
                            #cadn="{
                                trData: item,
                                index
                            }"
                        >
                            <display-name-cell
                                v-if="currentEditIndex !== getRealIndex(item) && item.goods"
                                :goods="item.goods"
                                :class="{ 'cadn-hover': canEdit }"
                                :hover-config="{
                                    openDelay: 500,
                                    showPrice: true,
                                    showShebaoCode: true,
                                    showF2,
                                    pharmacyNo: order.pharmacy ? order.pharmacy.no : order.pharmacyNo
                                }"
                                @click.stop.native="editCurrentGoodsItem(item, index)"
                            >
                            </display-name-cell>
                            <div
                                v-else-if="currentEditIndex !== getRealIndex(item) && !item.goods"
                                style="width: 100%; height: 100%;"
                                @click.stop="editCurrentGoodsItem(item, index)"
                            >
                                <abc-form-item :validate-event="handleValidateKey(item)" style="width: 100%;">
                                    <abc-input :value="item.searchGoodsKey " style="width: 100%;" :input-custom-style="{ 'color': `${$store.state.theme.style.Y2}` }"></abc-input>
                                </abc-form-item>
                            </div>
                            <!--                    处理导入的随货同行单，可以搜索替换药品-->
                            <div v-else style="width: 100%; height: 100%;">
                                <abc-form-item :validate-event="handleValidateKey(item)" style="width: 100%;" @click="editCurrentGoodsItem(item, index)">
                                    <goods-auto-complete-cover-title
                                        class="inventory-replace-goods-autocomplete-wrapper"
                                        style="width: 100%; height: 100%;"
                                        :search.sync="item.searchGoodsKey"
                                        :enable-barcode-detector="false"
                                        :clinic-id="isChainAdmin ? order.toOrganId : clinicId"
                                        :inorder-config="0"
                                        :type-id-list="isVirtualPharmacy ? chineseTypeIdList : []"
                                        size="medium"
                                        resident-sugguestions
                                        show-empty
                                        enable-local-search
                                        format-count-key="out"
                                        focus-show
                                        :close-on-click-outside="() => handleResetSearchKey(item)"
                                        @selectGoods="(goods) => selectReplaceGoods(goods, item)"
                                    >
                                        <div
                                            v-if="isAdmin || item.rowMatchInfo"
                                            slot="fixed-footer"
                                            class="inventory__fixed-footer-wrapper"
                                            @click.stop=""
                                        >
                                            <add-archive-dropdown
                                                v-if="isAdmin"
                                                ref="addGoodsDropdownRef"
                                                is-custom-change
                                                @change="(type) => handleAddNewGoods(type, item)"
                                            ></add-archive-dropdown>
                                            <div
                                                v-if="item.rowMatchInfo"
                                                class="ellipsis"
                                                style="display: flex; align-items: center; margin-left: auto; color: var(--abc-color-T2);"
                                            >
                                                随货同行单商品：
                                                <span style="max-width: 182px;" class="ellipsis" :title="item.rowMatchInfo.medicineCadn">{{ item.rowMatchInfo.medicineCadn }}</span>
                                                <span style="margin-left: 2px;" class="ellipsis" :title="item.rowMatchInfo.specification">
                                                    {{ item.rowMatchInfo.specification }}
                                                </span>
                                                <span style="max-width: 126px; margin-left: 8px;" class="ellipsis" :title="item.rowMatchInfo.manufacturerFull">
                                                    {{ item.rowMatchInfo.manufacturerFull }}
                                                </span>
                                            </div>
                                        </div>
                                    </goods-auto-complete-cover-title>
                                </abc-form-item>
                            </div>
                        </template>
                        <!--售价-->
                        <template
                            #sellPrice="{
                                trData: {
                                    goods, useUnitCostPrice, useUnit
                                }
                            }"
                        >
                            <abc-table-cell v-if="goods">
                                <template v-if="isCostPriceAdditionMedicine(goods)">
                                    <span
                                        v-if="isChineseMedicine(goods)"
                                        v-abc-title.ellipsis="isNull(useUnitCostPrice) ? '-' : `${paddingMoney(getPriceRange(goods, useUnitCostPrice, useUnit).minPiecePrice)}/${goods.pieceUnit}`"
                                    >
                                    </span>
                                    <span
                                        v-else
                                        v-abc-title.ellipsis="isNull(useUnitCostPrice) ? '-' : `${paddingMoney(getPriceRange(goods, useUnitCostPrice, useUnit).minPackagePrice)}/${goods.packageUnit}`"
                                    >
                                    </span>
                                </template>

                                <template v-else>
                                    <span
                                        v-if="isChineseMedicine(goods)"
                                        v-abc-title.ellipsis="`${moneyDigit(goods.piecePrice, 5)}/${goods.pieceUnit}`"
                                    >
                                    </span>
                                    <span
                                        v-else
                                        v-abc-title.ellipsis="`${moneyDigit(goods.packagePrice, 5)}/${goods.packageUnit}`"
                                    >
                                    </span>
                                </template>
                            </abc-table-cell>
                        </template>
                        <!--批号-->
                        <template #batchNo="{ trData: row }">
                            <abc-form-item :validate-event="validateBatchNo">
                                <abc-input
                                    v-model="row.batchNo"
                                    v-abc-focus-selected
                                    :max-length="20"
                                    @enter="enterEvent"
                                    @focus="setDisabledScanBarcode(true)"
                                    @blur="setDisabledScanBarcode(false)"
                                    @change="autoSaveDraftAsync"
                                >
                                </abc-input>
                            </abc-form-item>
                        </template>
                        <!--生产日期-->
                        <template
                            #productionDate="{
                                trData: item
                            }"
                        >
                            <abc-form-item
                                :validate-event="validateDate"
                                class="form-item-production_date"
                            >
                                <abc-tooltip :disabled="!getProductDateTooltip(item)" content="生产日期不可选择未来时间">
                                    <abc-date-picker
                                        v-model="item.productionDate"
                                        type="datequick"
                                        :show-icon="false"
                                        :class="{ 'tool-tip-price': getProductDateTooltip(item) }"
                                        :prevent-direction-navigation="false"
                                        editable
                                        placeholder=""
                                        @enter="enterEvent($event, true)"
                                        @change="productionDateChange($event, item)"
                                    >
                                    </abc-date-picker>
                                </abc-tooltip>
                            </abc-form-item>
                        </template>
                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item,
                            }"
                        >
                            <traceable-code-cell
                                :ref="`traceableCodeCellRef${item.keyId}`"
                                v-model="item.traceableCodeList"
                                :item="item"
                                :goods.sync="item.goods"
                                :goods-count="{
                                    isTrans: item._isTransformable,
                                    maxCount: item._maxTraceCodeCount,
                                    unitCount: item.useCount,
                                    unit: item.useUnit,
                                    label: '入库数量',
                                }"
                                :scene-type="SceneTypeEnum.GOODS_IN"
                                :need-validate="validateCell"
                                :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                                need-validate-drug-identification-code
                                @updateGoodsTraceableCodeNoInfo="updateGoodsTraceableCodeNoInfo"
                            ></traceable-code-cell>
                        </template>
                        <!--效期-->
                        <template
                            #expiryDate="{
                                trData: row
                            }"
                        >
                            <abc-form-item
                                :validate-event="validateDate"
                                class="form-item-expiry_date"
                            >
                                <abc-tooltip :disabled="!getExpiryDateTooltip(row)" :content="getExpiryDateTooltip(row)">
                                    <abc-date-picker
                                        v-model="row.expiryDate"
                                        type="datequick"
                                        :show-icon="false"
                                        :class="{ 'tool-tip-price': getExpiryDateTooltip(row) }"
                                        :prevent-direction-navigation="false"
                                        editable
                                        placeholder=""
                                        @enter="enterEvent"
                                        @change="expiryDateChange($event, row)"
                                    >
                                    </abc-date-picker>
                                </abc-tooltip>
                            </abc-form-item>
                        </template>

                        <!--单位-->
                        <template #unit="{ trData: row }">
                            <stock-unit-or-input
                                v-if="row.goods"
                                :key="customTrKey(row)"
                                :value="row"
                                :type="'unit'"
                                :immediate-change="false"
                                :use-unit="row.useUnit"
                                :use-count.sync="row.useCount"
                                data-cy="inventory-goods-in-add-unit-select"
                                :validate-fn="validateNull"
                                @change="unitChange($event, row)"
                                @changeCMUnit="(val) => updateLastUseUnit(val, row)"
                            >
                            </stock-unit-or-input>
                        </template>
                        <!--数量-->
                        <template #count="{ trData: row }">
                            <abc-tooltip
                                :disabled="!isVirtualPharmacy || !getVirtualSaleInfo(row)"
                                :content="getVirtualSaleInfo(row)"
                            >
                                <stock-unit-or-input
                                    :key="customTrKey(row)"
                                    :value="row"
                                    :type="'count'"
                                    :use-unit.sync="row.useUnit"
                                    :use-count="row.useCount"
                                    :input-custom-style="{ textAlign: 'right' }"
                                    :class="{
                                        'tool-tip-price': getVirtualSaleInfo(row),
                                        'focus-input': true
                                    }"
                                    data-cy="inventory-goods-in-add-count-input"
                                    :immediate-change="!isStrictCountWithTraceCodeCollect"
                                    :validate-fn="validateNull"
                                    @change="countChange($event, row)"
                                    @changeCMUnit="(val) => updateLastUseUnit(val, row)"
                                >
                                </stock-unit-or-input>
                            </abc-tooltip>
                        </template>

                        <!--进价-->
                        <template
                            #packageCostPrice="{
                                trData: item, index
                            }"
                        >
                            <abc-form-item
                                :ref="`totalPrice${index}`"
                                :validate-event="(_, callback)=>validateCostPrice(callback, item)"
                            >
                                <!--中西药均支持 5 位小数-->
                                <abc-popover
                                    trigger="focus"
                                    placement="top-end"
                                    :popper-style="{ 'font-size': '12px' }"
                                    width="auto"
                                    :disabled="!!validatePrice(item)"
                                    theme="yellow"
                                    :close-delay="10"
                                    style="height: 100%;"
                                >
                                    <div slot="reference" style="height: 100%;">
                                        <abc-tooltip :disabled="!validatePrice(item)" :content="validatePrice(item)">
                                            <abc-input
                                                v-model="item.useUnitCostPrice"
                                                v-abc-focus-selected
                                                type="money"
                                                :class="{ 'tool-tip-price': validatePrice(item) }"
                                                :input-custom-style="{
                                                    'text-align': 'right',
                                                }"
                                                :config="getCostConfig(item)"
                                                data-cy="inventory-goods-in-add-cost-price-input"
                                                @enter="enterEvent"
                                                @change="unitCostPriceChange($event, item)"
                                            ></abc-input>
                                        </abc-tooltip>
                                    </div>

                                    <div v-show="!validatePrice(item)">
                                        <template v-if="hasLastPackageCostPrice(item)">
                                            最近进价：{{ moneyDigit(item.goods.lastPackageCostPrice, 5) }}/{{
                                                item.goods.packageUnit || item.goods.pieceUnit
                                            }}
                                            <view-cost-price-dialog
                                                :id="item.goodsId"
                                                :triangle="false"
                                                :clinic-id="isChainAdmin ? order.toOrganId : clinicId"
                                                :pharmacy-no="pharmacyNo"
                                                :slot-style="{
                                                    display: 'inline-flex',
                                                    height: '20px',
                                                    lineHeight: '20px',
                                                }"
                                            >
                                                历史进价
                                            </view-cost-price-dialog>
                                        </template>
                                        <template v-else>
                                            无历史进价信息
                                        </template>
                                    </div>
                                </abc-popover>
                            </abc-form-item>
                        </template>

                        <!--金额-->
                        <template
                            #totalPrice="{
                                trData: item, index
                            }"
                        >
                            <abc-form-item
                                :ref="`TotalCostPrice${index}`"
                                :validate-event="(_, callback)=>validateTotalPrice(callback, item)"
                            >
                                <abc-input
                                    v-model="item.useTotalCostPrice"
                                    v-abc-focus-selected
                                    type="money"
                                    :input-custom-style="{
                                        'text-align': 'right',
                                    }"
                                    :config="{
                                        formatLength: 2, max: 10000000, supportZero: true
                                    }"
                                    data-cy="inventory-goods-in-add-total-price-input"
                                    @enter="enterEvent"
                                    @change="totalCostPriceChange($event, item)"
                                ></abc-input>
                            </abc-form-item>
                        </template>

                        <!--  医保上报相关  -->
                        <template #shebaoCode="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <overflow-tooltip :content="row.shebaoCode || ''"></overflow-tooltip>
                            </abc-table-cell>
                        </template>
                        <!--  平台产品编码  -->
                        <template #erpGoodsId="{ trData: row }">
                            <abc-form-item required>
                                <abc-input
                                    v-model="row.erpGoodsId"
                                    v-abc-focus-selected
                                    :max-length="20"
                                    @enter="enterEvent"
                                    @change="autoSaveDraftAsync"
                                >
                                </abc-input>
                            </abc-form-item>
                        </template>

                        <!--  应急采购  -->
                        <template #emergencyFlag="{ trData: row }">
                            <abc-form-item required>
                                <abc-select
                                    v-model="row.emergencyFlag"
                                    adaptive-width
                                    @enter="enterEvent"
                                    @change="autoSaveDraftAsync"
                                >
                                    <abc-option label="是" :value="1">
                                    </abc-option>
                                    <abc-option label="否" :value="0">
                                    </abc-option>
                                </abc-select>
                            </abc-form-item>
                        </template>

                        <!--  订单明细ID  -->
                        <template #erpOrderItemId="{ trData: row }">
                            <abc-form-item required>
                                <abc-input
                                    v-model="row.erpOrderItemId"
                                    v-abc-focus-selected
                                    :max-length="30"
                                    type="number-en-char"
                                    @enter="enterEvent"
                                    @change="autoSaveDraftAsync"
                                >
                                </abc-input>
                            </abc-form-item>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-space style="margin-right: auto;">
                <logs-v3-popover v-if="orderId && !isReInStock" :logs="order.logs"></logs-v3-popover>

                <abc-button
                    v-if="!orderId && draftId"
                    type="danger"
                    :loading="deleteDraftBtnLoading"
                    :disabled="saveDraftBtnLoading"
                    @click="deleteDraftHandler"
                >
                    删除
                </abc-button>
                <abc-button v-if="mallOrderId || order.mallOrderId" variant="ghost" @click="handleOpenMallOrderDetail">
                    查看商城订单
                </abc-button>
            </abc-space>
            <abc-button :loading="buttonLoading" :disabled="submitDisabled" @click="beforeSubmit">
                {{ isChainAdmin ? '确定' : stockInChainReview ? '提交入库审核' : '确定入库' }}
            </abc-button>
            <abc-button
                v-if="!orderId || isReInStock"
                type="blank"
                :loading="saveDraftBtnLoading"
                :disabled="deleteDraftBtnLoading || disableSaveDraftBtn || buttonLoading"
                @click="beforeSaveDraft"
            >
                保存草稿
            </abc-button>
            <abc-button
                v-if="canRevoke"
                type="danger"
                :loading="revokeLoading"
                @click="revokeHandler"
            >
                撤回
            </abc-button>
            <abc-button type="blank" @click="closeDialog">
                关闭
            </abc-button>
        </div>

        <add-goods-archives-dialog
            v-if="addMedicineDialogVisible"
            v-model="addMedicineDialogVisible"
            :type-id="currentTypeId"
            :bar-code="barCodeToAddMedicine"
            :is-continue="false"
            :pharmacy-type="pharmacyType"
            :goods-info="importGoodsInfo"
            @updateList="handleUpdateList"
            @success="handleSuccessAdd"
            @close="closeDialogHandle"
        >
        </add-goods-archives-dialog>

        <supplier-dialog
            v-if="showSupplierDialog"
            v-model="showSupplierDialog"
            type="add"
            :status-disable="true"
            :pharmacy-type="pharmacyType"
            @add="handleAddSupplier"
        ></supplier-dialog>

        <status-dialog
            v-if="showImportLoading"
            :width="240"
            fixed
            class="import-loading"
        >
            <abc-flex slot="status" align="center" justify="center">
                <abc-loading-spinner></abc-loading-spinner>
            </abc-flex>
            <span slot="title">{{ virtualSupplierInfo ? '入库单生成中' : '清单导入中' }}</span>
            <span slot="detail">{{ virtualSupplierInfo ? '正在导入入库药品，请稍后' : '文件大小会影响导入时间，请稍后' }}</span>
        </status-dialog>

        <div>
            <import-excel-dialog
                v-if="showImportDialog"
                v-model="showImportDialog"
                @uploaded="handleLoadedExcel"
                @open-import-order="handleOpenImportOrderDialog"
            ></import-excel-dialog>
        </div>
        <div>
            <import-loading-dialog
                v-if="showLoadingDialog"
                v-model="showLoadingDialog"
                :current-count="currentParseData.currentCount"
                :total-count="currentParseData.totalCount"
                :current-goods="currentParseData.currentGoods"
            ></import-loading-dialog>
        </div>
        <div>
            <import-parse-header-dialog
                v-if="showParseHeader"
                v-model="showParseHeader"
                :matched-data-list="matchedDataList"
                :origin-excel-options="originExcelOptions"
                :order-draft-id="orderDraftId"
                @parsed="handleParsedHeaderAfter"
            ></import-parse-header-dialog>
        </div>


        <div>
            <import-goods-dialog v-if="showImportOrderDialog" v-model="showImportOrderDialog" @finish="importProjectAfter"></import-goods-dialog>
        </div>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        GoodsSubTypeEnum,
        GoodsTypeEnum,
        GoodsTypeIdEnum,
        PharmacyTypeEnum,
    } from '@abc/constants';
    import {
        CHECK_IN_SUPPLIER_ID, GOODS_IN_PRICE_TYPE,
        GOODS_IN_STATUS, GoodsInPurchasePlatformEnum, GoodsInPurchaseTypeEnum,
    } from 'views/inventory/constant.js';
    import GoodsAPI from 'api/goods';
    import StockInAPI from 'api/goods/stock-in';
    import SettingAPI from 'api/settings';

    const SupplierDialog = () => import('../goods-supplier/supplier');
    import StatusDialog from '../common/status-dialog';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';
    const ViewCostPriceDialog = () => import('../common/view-cost-price-dialog');

    const GoodsAutoCompleteCoverTitle = () => import('views/inventory/common/goods-auto-complete-cover-title');
    // import GoodsAutoCompleteCoverTitle from 'views/inventory/common/goods-auto-complete-cover-title';
    import StockUnitOrInput from 'views/inventory/common/stock-unit-or-input';
    import GoodsTableV3Mixins from 'views/inventory/mixins/goods-table-v3.js';

    import EmployeeSelect from '@/views-pharmacy/inventory/frames/components/employee-select.vue';

    import AutoFocus from '../mixins/auto-focus';
    import Draft from './draft';
    import DeleteGoodsHandler from '../mixins/delete-goods-handler';
    import EnterEvent from 'views/common/enter-event';
    import ImportGoods from 'views/inventory/goods-in/mixins/import-goods.js';
    import dialogAutoWidth from 'views/inventory/mixins/dialog-auto-width.js';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import TraceCodeSelectGoodsDialog from '@/service/trace-code/dialog-trace-code-select-goods';
    import TraceCodeWarningDialog from '@/service/trace-code/dialog-trace-code-warning';

    import {
        isEqual,
    } from 'utils/lodash';
    import {
        goodsTotalCostPrice,
        isChineseMedicine,
        isChinesePatentMedicine,
        isCostPriceAdditionMedicine,
        isWesternMedicine,
    } from 'src/filters/goods';
    import {
        calCostPriceSingle,
        calCostPriceTotal,
        checkExpiryDate,
        formatGoodsNameSpec,
        showExpiryDateTip,
        showProductionDateTip,
        validateExpirationTime,
        validatePrice,
        showOverdueTip,
    } from './common';
    import {
        createGUID, paddingMoney, moneyDigit, isNull, parseTime, isNotNull, getSafeNumber,
    } from '@/utils';
    import Clone from '@/utils/clone';
    import { accessExcel } from '@/assets/configure/access-file.js';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';

    const AddGoodsArchivesDialog = () => import('views/inventory/goods/archives/add.vue');
    import {
        formatDate,
    } from '@abc/utils-date';
    import Big from 'big.js';
    import { calcPriceRangeByMakeupPercent } from 'views/inventory/goods/archives/utils';
    import { getUserLastTypeId } from '@/views-pharmacy/inventory/utils';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';
    import InventoryGoodsInTable from 'views/inventory/goods-in/table-config';
    import OverflowTooltip from '@/components/overflow-tooltip.vue';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import TraceCode, {
        SceneTypeEnum,
        TraceableCodeTypeEnum,
        TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import TraceCodeRepeatDialog from '@/service/trace-code/dialog-repeat-trace-code';
    import Logger from 'utils/logger';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';

    export default {
        name: 'OrderForm',
        components: {
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
            AddArchiveDropdown: () => import('views/inventory/goods/components/add-archive-dropdown.vue'),
            AddGoodsArchivesDialog,
            GoodsAutoCompleteCoverTitle,
            StockUnitOrInput,
            SupplierDialog,
            StatusDialog,
            ViewCostPriceDialog,
            ClinicSelect,
            EmployeeSelect,
            OverflowTooltip,
            LogsV3Popover: () => import('@/views-pharmacy/components/logs-v3-popover.vue'),
            GoodsInTableFooter: () => import('views/inventory/goods-in/components/goods-in-form-footer.vue'),
            DisplayNameCell,
            AbcLoadingSpinner,
        },

        mixins: [EnterEvent, Draft, DeleteGoodsHandler, AutoFocus, dialogAutoWidth, GoodsTableV3Mixins,ImportGoods],

        // 通过药品搜索 点开 入库单需要对 入库单进行排序 被搜索的药品需要置顶
        props: {
            value: Boolean,
            orderId: [String, Number],
            draftId: [String, Number],
            goodsId: [String, Number],
            isSummary: Boolean,
            pharmacyNo: {
                type: Number,
                default: PharmacyTypeEnum.LOCAL_PHARMACY,
            },
            pharmacyName: {
                type: String,
                default: '本地库房',
            },
            pharmacyType: {
                type: Number,
                default: PharmacyTypeEnum.LOCAL_PHARMACY,
            },
            // eslint-disable-next-line vue/require-default-prop
            // 代煎代配库房才有，从入库单按供应商入库
            virtualSupplierInfo: {
                type: Object,
            },
            // 是否是重新入库，这个props在函数式调用时才会打开。
            isReInStock: Boolean,
            // eslint-disable-next-line vue/require-default-prop
            refresh: Function,
            // eslint-disable-next-line vue/require-default-prop
            close: Function,
            // 随货单数据
            importOrderData: {
                type: Object,
                default: null,
            },
            importDraftId: {
                type: String,
                default: '',
            },
            mallGoodsList: {
                type: Array,
                default: () => [],
            },
            mallOrderId: {
                type: String,
            },
            stockInType: Number, // 入库单类型
        },

        setup(props) {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('入库单');

            const {
                setDisabledScanBarcode,
                isDisabledScanBarcode,
            } = useBarcodeScanner();

            const {
                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
                initSupplierList,
            } = useSearchSupplier({
                status: 1,
                excludeInitSupplier: true,
                pharmacyType: props.pharmacyType,
            });

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                setDisabledScanBarcode,
                isDisabledScanBarcode,

                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
                initSupplierList,
            };
        },

        data() {
            return {
                GoodsTypeEnum,
                SceneTypeEnum,
                CHECK_IN_SUPPLIER_ID,
                currentEditIndex: -1,
                showDialog: this.value,
                searchKey: '',
                currentTypeId: '',
                barCodeToAddMedicine: '',
                addMedicineDialogVisible: false,
                showTraceableCodeModal: false,
                showTraceableCodePopover: false,
                order: {
                    pharmacyNo: this.pharmacyNo,
                    pharmacyType: this.pharmacyType,
                    pharmacy: {
                        no: this.pharmacyNo,
                        name: this.pharmacyName,
                        type: this.pharmacyType,
                    },
                    toOrganId: '',
                    supplierId: '',
                    inspectBy: '',
                    supplier: null,
                    outOrderNo: '',
                    comment: '',
                    purchaseType: '',
                    platformType: '',
                    list: [
                        // {
                        //     goods: {},
                        //     useCount: '',
                        //     useUnit: '',
                        //     useUnitCostPrice: '',
                        //     useTotalCostPrice: '',
                        //     batchNo: '',
                        //     productionDate: '',
                        //     expiryDate: '',
                        //     traceableCodeList: [],
                        //     shebaoCode: '',
                        //     erpGoodsId: '',
                        //     erpOrderItemId: '',
                        //     emergencyFlag: 0,
                        //     searchGoodsKey: '',
                        //     rowMatchInfo: null,
                        // }
                    ],
                },
                options: {
                    purchaseTypeList: [
                        {
                            label: '自行采购',
                            value: GoodsInPurchaseTypeEnum.SELF,
                        },
                        {
                            label: '医保集采',
                            value: GoodsInPurchaseTypeEnum.COLLECT,
                        },
                    ],
                    platformTypeList: [
                        {
                            label: '市平台',
                            value: GoodsInPurchasePlatformEnum.CITY,
                        },
                        {
                            label: '省平台',
                            value: GoodsInPurchasePlatformEnum.PROVINCE,
                        },
                    ],
                },
                loading: false,
                buttonLoading: false,
                priceReminderVisible: false,
                priceReminderMessage: '',
                lastUseUnit: {
                    keli: '',
                    yinpian: '',
                },
                originOrder: null,
                suppliers: [],
                showSupplierDialog: false,
                showImportLoading: false,
                supplierErr: '',
                revokeLoading: false,
                actionType: 'submit',// 'submit'|'save_draft'
                pharmacyList: [],// 查询每个门店的药房列表
                isShowTraceableDialog: false,//追溯码弹窗flag
                goodsName: '',
                goodsCount: 0,
                goodsIndex: '',
                traceableList: [],

                // 导入的随货单逻辑
                selectGoodsType: '',
                showAddMaterialDialog: false,
                showAddGoodsDialog: false,
                validateCell: false,
            };
        },

        computed: {
            ...mapGetters([
                'subClinics',
                'currentClinic',
                'userInfo',
                'clinicConfig',
                'isChain',
                'isChainSubStore',
                'isChainAdmin',
                'isAdmin',
                'goodsConfig',
                'multiPharmacyCanUse',
                'localPharmacyUserList',
                'stockEmployeeList',
                'traceCodeConfig',
                'isStrictCountWithTraceCodeCollect',
                'isNeedCheckStockInfoNotEmpty',
                'isNeedCheckStockInCostPriceNotZero',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            isShowPurchaseType() {
                // return true;
                return this.$abcSocialSecurity.region === 'jiangsu_nanjing';
            },
            // 医保集采
            isShebaoCollect() {
                return this.order.purchaseType === GoodsInPurchaseTypeEnum.COLLECT;
            },
            defaultGoodsTypeMap() {
                return this.viewDistributeConfig.Inventory.defaultGoodsTypeMap;
            },
            useNewGoodsArchives() {
                return this.viewDistributeConfig.Inventory.useNewGoodsArchives;
            },
            chineseTypeIdList() {
                return [
                    GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                    GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE,
                ];
            },
            goodsInPharmacyList() {
                const pharmacyList = this.isChainAdmin ? this.pharmacyList : this.localPharmacyUserList;
                return pharmacyList.filter((item) => item.status === 1 && item.enablePurchase && item.type === PharmacyTypeEnum.LOCAL_PHARMACY);
            },
            orderMainNameText() {
                return this.viewDistributeConfig.Inventory.orderMainNameText;
            },
            enableBarcodeDetector() {
                return !this.isDisabledScanBarcode && !this.isDialogShowing;
            },
            isDialogShowing() {
                return this.addMedicineDialogVisible || this.showTraceableCodeModal || this.showTraceableCodePopover;
            },
            dialogTitle() {
                const prefix = '采购';

                if (this.orderId && this.order && this.order.orderNo && !this.isReInStock) {
                    const { Y2 } = this.$store.state?.theme?.style ?? {};
                    if (this.order.status === GOODS_IN_STATUS.REVIEW) {
                        return `${prefix}入库单 ${this.order.orderNo}<span class="goods-small-title" style="color: ${Y2}">待总部审核</span>`;
                    }
                    if (this.order.status === GOODS_IN_STATUS.REFUSE) {
                        return `${prefix}入库单 ${this.order.orderNo}<span class="goods-small-title">已驳回</span>`;
                    }
                    if (this.order.status === GOODS_IN_STATUS.WITH_DRAW) {
                        return `${prefix}入库单 ${this.order.orderNo}<span class="goods-small-title">已撤回</span>`;
                    }
                    if (
                        this.order.status === GOODS_IN_STATUS.CONFIRM &&
                        this.order.toOrgan &&
                        (this.order.toOrgan.shortName || this.order.toOrgan.name)
                    ) {
                        return `${prefix}入库单 ${this.order.orderNo}<span class="goods-small-title" style="color: ${Y2}">待${
                            this.order.toOrgan.shortName || this.order.toOrgan.name
                        }确认</span>`;
                    }
                    return `${prefix}入库单 ${this.order.orderNo}`;
                }
                return `${prefix}入库单`;
            },

            currentSubClinicsArray() {
                return this.subClinics || [];
            },

            clinicId() {
                return this.currentClinic && this.currentClinic.clinicId;
            },
            // 总部给子店入库不显示调价
            showF2() {
                if (this.isChainAdmin && (this.order.toOrganId !== this.clinicId)) {
                    return false;
                }
                return true;
            },


            orderChanged() {
                return !(isEqual(this.order, this.originOrder) && isEqual(this.order.comment, this.originComment));
            },
            isImportOrder() {
                return !!this.importOrderData;
            },
            submitDisabled() {
                if (this.isReInStock) {
                    return this.order?.list?.length === 0;
                }
                return (this.order?.list?.length === 0) || (!this.orderChanged);
            },
            stockInChainReview() {
                return this.goodsConfig?.chainReview?.stockInChainReview;
            },
            /**
             * 待审核和待确定可以撤回
             */
            canRevoke() {
                return this.order.status === GOODS_IN_STATUS.REVIEW || this.order.status === GOODS_IN_STATUS.CONFIRM;
            },
            /**
             * @desc 是否可编辑，之前待审核和待确认的入库单会在这个文件打开，现在待审核待确认都不允许编辑了
             * <AUTHOR>
             * @date 2022/6/15 10:56
             */
            canEdit() {
                return !(this.order.status === 0 || this.order.status === 1);
            },
            isVirtualPharmacy() {
                return this.order.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            // 数据没有变化，不需要保存草稿
            disableSaveDraftBtn() {
                return isEqual(this.order, this._cacheOrderDraft) && !this.importOrderData;
            },
            virtualListConfig() {
                const config = {
                    bufferLoad: false,
                    bufferSize: 10,
                    visibleCount: 20,
                    scrollendTime: 300,
                    rowHeight: 45,// 44+1边框
                    phantomStripe: false,
                    phantomHeightOffset: 1,
                    isAutoHeight: false,
                };
                return config;
            },
            tableConfigRender() {
                const TableConfig = new InventoryGoodsInTable();

                return TableConfig.createRenderConfig({
                    orderMainNameText: this.orderMainNameText,
                    isEnableTraceableCode: this.isEnableTraceableCode,
                    isShebaoCollect: this.isShebaoCollect,
                });

            },
            // table滚动后校验或聚焦输入框的回调时间，根据是否开启虚拟列表来决定大小
            timeout() {
                return this.order.list?.length >= 60 ? 1000 : 0;
            },
            indexMap() {
                return this.order.list?.reduce((map, item, index) => {
                    map[item.keyId || item.id] = index;
                    return map;
                }, {}) || {};
            },
        },

        watch: {
            showDialog(val) {
                this.$emit('input', val);
            },
            'order.toOrganId': {
                async handler(clinicId) {
                    if (this.isChainAdmin && clinicId) {
                        const res = await GoodsAPI.getPharmacyListByClinicId({
                            queryClinicId: clinicId,
                        });
                        if (res) {
                            this.pharmacyList = res.data?.list?.[0]?.pharmacyList || [];
                        }
                    }
                },
                immediate: true,
            },
        },

        async created() {
            if (this.isStrictCountWithTraceCodeCollect) {
                this.validateCell = true;
            }
            if (!this.draftId && !this.orderId) {
                this.order.toOrganId = this.clinicId;
                this.order.type = this.stockInType;
                this.order.inspectBy = this.userInfo.id;
            }

            this.$store.dispatch('fetchStockEmployeeListIfNeed');

            if (this.orderId) {
                await this.fetchOrder();
            }
            if (this.virtualSupplierInfo) {
                const data = await this.createVirtualSupplierForm();
                if (data.status === 10) {
                    this.order = data;
                    this.order.list = this.transImportData(this.order.list || []);
                    this.order.virtualSupplierInfo = this.virtualSupplierInfo;
                    this.order.toOrganId = this.virtualSupplierInfo.clinicId;
                    this.order.supplierId = this.order?.supplier?.id;
                    this.order.pharmacy = {
                        name: this.pharmacyName || data.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY ? '代煎代配' : '',
                        no: data.pharmacyNo,
                        type: data.pharmacyType,
                    };
                    this.showImportLoading = false;
                } else {
                    this.startCreateVirtualSupplierForm();
                }
            }

            this._orderClientUniqKey = createGUID();
        },

        methods: {
            isNull,
            accessExcel,
            formatGoodsNameSpec,
            handleEmployeeChange(employeeId) {
                console.log('employeeId', employeeId);
            },
            handlePurchaseTypeChange() {
                this.autoSaveDraftAsync();
            },
            getPriceRange(goods, useUnitCostPrice, useUnit) {
                const _isChineseMedicine = isChineseMedicine(goods);
                // 是否使用拆零
                const useDismounting = useUnit === goods.pieceUnit && goods.packageUnit !== goods.pieceUnit;
                // 大单位成本价
                let packageCostPrice = useDismounting ? Big(getSafeNumber(useUnitCostPrice)).times(goods.pieceNum).toNumber() : useUnitCostPrice;
                if (_isChineseMedicine && useUnit === 'Kg') {
                    packageCostPrice = Big(getSafeNumber(useUnitCostPrice)).div(1000).toNumber();
                }
                return calcPriceRangeByMakeupPercent({
                    maxPackageCostPrice: packageCostPrice,
                    minPackageCostPrice: packageCostPrice,
                    maxPackagePrice: goods.maxPackagePrice,
                    minPackagePrice: goods.minPackagePrice,
                    pieceNum: goods.pieceNum,
                    priceMakeupPercent: goods.priceMakeupPercent,
                    fractionDigits: _isChineseMedicine ? 4 : 2,
                });
            },
            // 校验goodsId一定存在
            handleValidateKey(item) {
                if (!item.goodsId) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能为空',
                        });
                    };
                }
                return (_, callback) => {
                    callback({
                        validate: true,
                    });
                };
            },
            /**
             * @desc 校验时间格式
             * <AUTHOR>
             * @date 2022-03-25 17:10:23
             * @params
             * @return
             */
            validateDate(value, callback) {
                value = value.trim();
                if (!value || checkExpiryDate(value, {
                    validateSimpleDate: false,
                    validateShortDate: false,
                })) {
                    callback({ validate: true });
                } else {
                    callback({
                        validate: false, message: '格式错误',
                    });
                }
            },
            /**
             * @desc 通过时间格式的校验 1. 效期不可小于或等于生产日期 2. 该药品将在一年半之内过期
             * <AUTHOR>
             * @date 2022-04-11 17:38:22
             */
            getExpiryDateTooltip(item) {
                if (!item.expiryDate || !checkExpiryDate(item.expiryDate, {
                    validateSimpleDate: false,
                    validateShortDate: false,
                })) return '';
                let diffTime;
                const expiredWarnMonths = item.goods?.expiredWarnMonths;
                // 诊所、医院总部没有预警设置
                if (expiredWarnMonths && !this.isChainAdmin) {
                    diffTime = expiredWarnMonths * 30 * 24 * 3600 * 1000;
                }
                const today = parseTime((new Date()), 'y-m-d', true);
                if (showOverdueTip(item.expiryDate, today)) {
                    return '该药品已过期';
                }
                if (showExpiryDateTip(item.productionDate, item.expiryDate)) {
                    return '效期不可小于或等于生产日期';
                }
                if (validateExpirationTime(today, item.expiryDate, diffTime)) {
                    if (diffTime) {
                        return `该药品将在${expiredWarnMonths}个月之内过期`;
                    }
                    return '该药品将在一年半之内过期';
                }
                return '';
            },
            /**
             * @desc 通过时间格式的校验,生产日期范围校验通过
             * <AUTHOR>
             * @date 2022-04-11 17:38:22
             */
            getProductDateTooltip(item) {
                if (!item.productionDate || !checkExpiryDate(item.productionDate, {
                    validateSimpleDate: false,
                    validateShortDate: false,
                })) return '';
                return showProductionDateTip(item.productionDate);
            },
            initNoTraceCodeList(item) {
                // 对无码商品初始化追溯码
                if (this.isEnableTraceableCode && TraceCode.isSupplementNoCodeGoods(item.goods)) {
                    item.traceableCodeList = TraceCode.mergeNoTraceCodeList({
                        ...item,
                        isTrans: item._isTransformable,
                        maxCount: item._maxTraceCodeCount,
                        unitCount: item.useCount,
                        unit: item.useUnit,
                    });
                }
            },
            async unitChange(value, item) {
                item.useUnit = value.useUnit;
                this.calTotalPrice(item);

                await this.initCollectCodeCountList([item]);
                this.initNoTraceCodeList(item);
                this.autoSaveDraftAsync();
            },
            async countChange(value, item) {
                item.useCount = value.useCount;
                this.calTotalPrice(item);

                await this.initCollectCodeCountList([item]);
                this.initNoTraceCodeList(item);
                this.autoSaveDraftAsync();
            },
            // 计算药品应采追溯码数量
            async initCollectCodeCountList(list = []) {
                // 开启强校验时才实时计算
                if (this.isEnableTraceableCode && this.isStrictCountWithTraceCodeCollect && TraceCode.isSupportTraceCodeForceCheckStock()) {

                    const resList = await TraceCode.getMaxTraceCountList({
                        scene: TraceCodeScenesEnum.INVENTORY,
                        dataList: list,
                        getGoodsInfo: (item) => item.goods,
                        getUnitInfo: (item) => ({
                            unitCount: item.useCount,
                            unit: item.useUnit,
                        }),
                        createKeyId: this.customTrKey,
                    });

                    (resList || []).forEach((e) => {
                        const item = list.find((i) => this.customTrKey(i) === e.keyId);
                        if (item) {
                            this.$set(item, '_maxTraceCodeCount', e.traceableCodeNum);
                            this.$set(item, '_isTransformable', e.isTransformable);
                            this.$set(item, '_traceableCodeMaxNum', e.traceableCodeMaxNum);
                        }
                    });
                }
            },
            productionDateChange(value, item) {
                // console.log('productionDateChange',value, item);
                if (value?.trim() !== item.productionDate) {
                    item.productionDate = value.trim();
                    this.autoSaveDraftAsync();
                }
            },
            expiryDateChange(value, item) {
                // console.log('expiryDateChange',value, item);
                if (value?.trim() !== item.expiryDate) {
                    item.expiryDate = value.trim();
                    this.autoSaveDraftAsync();
                }
            },
            unitCostPriceChange(value, item) {
                item.opType = GOODS_IN_PRICE_TYPE.UNIT_PRICE;
                this.calTotalPrice(item);
                this.autoSaveDraftAsync();
            },
            totalCostPriceChange(value, item) {
                item.opType = GOODS_IN_PRICE_TYPE.TOTAL_PRICE;
                this.calCostPrice(item);
                this.autoSaveDraftAsync();
            },
            onPharmacyNoChange(no) {
                const pharmacy = this.goodsInPharmacyList.find((item) => item.no === no);
                this.order.pharmacy = pharmacy || {};
                this.autoSaveDraftAsync();
            },
            validateNull(callback, value, type) {
                value = value[type === 'count' ? 'useCount' : 'useUnit'];
                // 新建提交不能为 0
                if (this.actionType === 'submit') {
                    if (type === 'count') {
                        if (isNull(value) || +value === 0) {
                            callback({
                                validate: false, message: '不能为空',
                            });
                        }
                    } else {
                        if (!value) {
                            callback({
                                validate: false, message: '不能为空',
                            });
                        }
                    }
                }
                callback({ validate: true });
            },
            validateBatchNo(value, callback) {
                if (this.isNeedCheckStockInfoNotEmpty) {
                    if (isNull(value)) {
                        callback({
                            validate: false, message: '医保要求生产批号必填',
                        });
                    } else {
                        callback({ validate: true });
                    }
                } else {
                    callback({ validate: true });
                }
            },
            validateCostPrice(callback, item) {
                // 支持 0
                if (this.actionType === 'submit' && isNull(item.useUnitCostPrice)) {
                    callback({
                        validate: false, message: '不能为空',
                    });
                    return;
                }

                // 河北不支持0进价
                if (this.isNeedCheckStockInCostPriceNotZero && +item.useUnitCostPrice === 0) {
                    callback({
                        validate: false, message: '不能为0',
                    });
                    return;
                }
                callback({ validate: true });
            },
            validateTotalPrice(callback, item) {
                if (this.actionType === 'save_draft') {
                    callback({ validate: true });
                }

                if (Number(item.useUnitCostPrice) === 0 && Number(item.useTotalCostPrice) === 0) {
                    callback({
                        validate: true,
                    });
                    return;
                }

                const isError = (Number(item.useCount) === 0 && Number(item.useTotalCostPrice) > 0) ||
                    (Number(item.useTotalCostPrice) === 0 && Number(item.useCount) > 0);
                if (isError) {
                    callback({
                        validate: false,
                        message: `入库数量为${Number(item.useCount)}，请重新检查入库总金额`,
                    });
                    return;

                }
                callback({
                    validate: true,
                });
            },

            hasLastPackageCostPrice(item) {
                if (!item.goods) return false;
                return item.goods.lastPackageCostPrice !== undefined && item.goods.lastPackageCostPrice !== null;
            },
            getGoodIds() {
                const tempSet = new Set();
                this.order.list.forEach((item) => {
                    if (!tempSet.has(item.goodsId)) {
                        tempSet.add(item.goodsId);
                    }
                });
                return Array.from(tempSet);
            },
            async changeClinic() {
                this.order.pharmacyNo = '';
                if (this.order.list && this.order.list.length) {
                    await this.updateCostPrice();
                }
                this.autoSaveDraftAsync();
            },
            changeSupplier() {
                if (this.order.supplierId === this.CHECK_IN_SUPPLIER_ID) {
                    this.order.supplier = {
                        id: this.CHECK_IN_SUPPLIER_ID,
                        name: '初始化入库',
                    };
                } else {
                    this.order.supplier = this.findSupplier(this.order.supplierId);
                }
                this.autoSaveDraftAsync();
            },
            /**
             * @desc 更新药品的进价
             * <AUTHOR>
             * @date 2019/12/10
             * @params
             * @return
             */
            async updateCostPrice() {
                this.loading = true;
                const goodsIds = this.getGoodIds();
                try {
                    const clinicId = this.order.toOrganId;
                    const { data } = await SettingAPI.commonPrescription.fetchPrescriptionTemplateStock({
                        goodsIds,
                        clinicId,
                    });
                    const list = (data && data.list) || [];
                    const tempMap = this.getGoodsIdsMap(list);
                    const newOrderList = [];
                    this.order.list.forEach((item) => {
                        if (tempMap && tempMap.get(item.goodsId)) {
                            item.goods = tempMap.get(item.goodsId);
                        }
                        newOrderList.push(item);
                    });
                    this.order.list = newOrderList;
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                    console.log(e);
                }
            },
            getGoodsIdsMap(goodsIds) {
                if (!goodsIds || !goodsIds.length) return null;
                const tempMap = new Map();
                goodsIds.forEach((item) => {
                    if (!tempMap.has(item.id)) {
                        tempMap.set(item.id, item);
                    }
                });
                return tempMap;
            },
            addMedicine(type = GoodsTypeEnum.MEDICINE, barCode) {
                // 获取用户上次建档类型
                const typeId = getUserLastTypeId(type);
                this.currentTypeId = typeId || this.defaultGoodsTypeMap[type];
                this.barCodeToAddMedicine = barCode || this.searchKey;
                this.currentGoodsIndex = -1;
                this.addMedicineDialogVisible = true;
            },
            traceableCodeEnter(keywordTraceableCodeNoInfo) {
                console.log('traceableCodeEnter', keywordTraceableCodeNoInfo);

                this.showTraceableCodeModal = true;
                new TraceCodeSelectGoodsDialog({
                    value: true,
                    title: '请选择追溯码关联的商品',
                    keywordTraceableCodeNoInfo,
                    onConfirm: this.handleConfirmBindGoods,
                    onClose: () => {
                        this.showTraceableCodeModal = false;
                    },
                }).generateDialogAsync({
                    parent: this,
                });
            },
            traceableCodeWarning(data) {
                this.showTraceableCodeModal = true;
                new TraceCodeWarningDialog({
                    value: true,
                    traceCodeDetails: data,
                    onConfirm: () => {
                        this.isStillSubmit = true;
                        this.showTraceableCodeModal = false;
                        this.submit(true);
                    },
                    onClose: () => {
                        this.buttonLoading = false;
                        this.isStillSubmit = false;
                        this.showTraceableCodeModal = false;
                    },
                }).generateDialogAsync();
            },

            // 药品标识码绑定goods成功后的回调
            handleConfirmBindGoods(goods, keywordTraceableCodeNoInfo) {
                console.log('handleConfirmBindGoods', goods, keywordTraceableCodeNoInfo);
                this.selectGoods(goods, {
                    keywordTraceableCodeNoInfo,
                });
            },
            formatSearchResultFn(goodsList) {
                if (this.isVirtualPharmacy) {
                    return goodsList.map((goods) => {
                        const pharmacyStockInfo = (goods.pharmacyList || []).find((p) => p.pharmacyNo === this.pharmacyNo);
                        return {
                            ...goods,
                            ...(pharmacyStockInfo || {}),
                        };
                    });
                }
                return goodsList;
            },
            isCostPriceAdditionMedicine,
            isChineseMedicine,
            isChinesePatentMedicine,
            isWesternMedicine,
            goodsTotalCostPrice,
            paddingMoney,
            moneyDigit,
            calCostPriceTotal,
            calCostPriceSingle,
            validatePrice,
            /**
             * @desc  关闭弹窗
             * <AUTHOR>
             * @date 2018/11/26 14:54:26
             */
            closeDialogHandle() {
                this.searchKey = '';
                this.$refs.goodsAutoCompleteRef?.handleClear();
                this.addMedicineDialogVisible = false;
                this.importGoodsInfo = {};
            },
            /**
             * @desc 添加或修改药品之后，table发生更新
             * <AUTHOR>
             * @date 2018/11/02 10:17:41
             * @params type = 1,修改药品,增加 type=2 删除药品
             * @return
             */
            handleUpdateList() {
                this.addMedicineDialogVisible = false;
            },
            /**
             * @desc 获取入库单详情
             * <AUTHOR>
             * @date 2018/11/23 19:55:10
             */
            async fetchOrder() {
                this.loading = true;
                this.order = await StockInAPI.getById(this.orderId);
                this.order.comment = '';
                this.originOrder = Clone(this.order);
                this.originComment = Clone(this.order.comment);
                this.order.list.forEach((item) => {
                    this.$set(item,'batchNo',item.batchNo || '');
                    this.$set(item,'expiryDate',item.expiryDate || '');
                    this.$set(item,'productionDate',item.productionDate || '');
                    this.calTotalPrice(item);
                });
                if (this.goodsId && this.order.list.length) {
                    this.sortOrderList();
                }
                // 需要 将搜索的药品 置顶
                this.loading = false;
            },

            /**
             * @desc  查询供应方
             * <AUTHOR>
             * @date 2018/11/06 21:03:42
             * @params
             * @return
             */
            async querySuppliers(key, callback) {
                let suppliers = await StockInAPI.suppliers(this.order.toOrganId, key);

                if (suppliers instanceof Array) {
                    suppliers = suppliers.filter((item) => {
                        return item && item !== '初始化入库';
                    });
                    callback(suppliers instanceof Array ? suppliers.concat(['初始化入库']) : ['初始化入库']);
                } else {
                    callback(['初始化入库']);
                }
            },

            /**
             * @desc  选择一个供应方
             * <AUTHOR>
             * @date 2018/11/06 21:03:58
             * @params
             * @return
             */
            async selectSupplier(suggestion) {
                this.order.supplier = suggestion;
            },
            customTrKey(tr) {
                return tr.keyId || tr.id;
            },
            customTrClass(tr) {
                return `abc-table-tr--${this.customTrKey(tr)}`;
            },
            deleteIt(rowIndex, childIndex) {
                let goods;
                if (isNotNull(rowIndex) && isNotNull(childIndex)) {
                    const arr = this.order.list[rowIndex]?.batchs ?? [];
                    const [item] = arr.splice(childIndex, 1);
                    goods = item;
                } else {
                    const [item] = this.order.list.splice(rowIndex, 1);
                    goods = item;
                }

                if (this.isImportOrder) {
                    Logger.reportAnalytics('goods-business', {
                        key: 'importOrderBehaviorRecord',
                        value: '删除药品',
                        goods,
                    });
                }
            },
            async importProjectAfter(parseData) {
                this.showImportDialog = false;
                this.showImportOrderDialog = false;
                if (parseData) {
                    // 导入的数据处理
                    if (parseData?.list?.length) {
                        this.order.list = this.order.list.concat(this.transImportData(parseData.list));
                        this.order.list = Clone(this.order.list).sort((a, b) => {
                            return a.goodsId > b.goodsId ? 1 : -1;
                        });
                        await this.initCollectCodeCountList(this.order.list);

                        this.order.list.forEach((item) => {
                            // 补充无码标识
                            this.initNoTraceCodeList(item);
                        });
                    }
                    this.order.outOrderNo = parseData.outOrderNo;
                    this.order.purchaseType = parseData.purchaseType || '';
                    if (parseData.supplier) {
                        this.order.supplierId = parseData.supplier.id;
                    }
                }
            },
            // 只有当前选中的中药单位是g，才会去读取上次保存的kg 或者 g（ lastUseUnit ）
            initGoodsUseUnit(goodsObj) {
                if (!goodsObj) return;
                let useUnit;
                // 只有当前选中的中药单位是g，才会去读取上次保存的kg 或者 g（ lastUseUnit ）
                if (isChineseMedicine(goodsObj)) {
                    const {
                        cMSpec,
                        pieceUnit,
                    } = goodsObj;
                    if (pieceUnit === 'g') {
                        if (cMSpec === '中药颗粒') {
                            useUnit = this.lastUseUnit.keli || pieceUnit;
                        } else if (cMSpec === '中药饮片') {
                            useUnit = this.lastUseUnit.yinpian || pieceUnit;
                        } else {
                            useUnit = pieceUnit;
                        }
                    } else {
                        useUnit = goodsObj.pieceUnit;
                    }
                } else {
                    useUnit = goodsObj.packageUnit;
                }
                return useUnit;
            },
            // 找到可以添加追溯码的项目
            findCanAddTraceCodeItem(goods, res) {
                const item = TraceCode.findCanAddTraceCodeItem({
                    traceableCodeNoInfo: res.keywordTraceableCodeNoInfo.traceableCodeNoInfo,
                    dataList: this.order.list,
                    goodsInfo: goods,
                    getUnitInfo(item) {
                        return {
                            unitCount: item.useCount,
                            unit: item.useUnit,
                        };
                    },
                });
                // 有匹配的药品，追加追溯码
                if (item) {
                    // 当前商品是无码商品，说明是无码商品绑定追溯码，需清除之前的无码标识
                    if (TraceCode.isNoTraceCodeGoods(item.goods)) {
                        item.traceableCodeList = item.traceableCodeList.filter((it) => it.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE);
                        item.goods.traceableCodeNoInfoList = [res.keywordTraceableCodeNoInfo.traceableCodeNoInfo];
                    } else {
                        item.traceableCodeList = item.traceableCodeList || [];
                    }

                    /**
                     * 重庆地区
                     * 如果采集的追溯码超上限了，则阻止继续采集并提示
                     */
                    const refKey = `traceableCodeCellRef${item.keyId}`;
                    const traceableCodeCellRefs = this.$refs[refKey];
                    const isValidate = traceableCodeCellRefs?.validateTraceCodeMaxCount?.(null, true) ?? true;
                    if (!isValidate) {
                        this.$confirm({
                            type: 'warn',
                            title: '追溯码采集提示',
                            content: `${item.goods?.displayName ?? ''} 追溯码采集条数不符合医保要求`,
                            confirmText: '去修改',
                            showCancel: false,
                        });
                        return;
                    }

                    const codeItem = item.traceableCodeList.find((e) => e.no === res.keywordTraceableCodeNoInfo.no);
                    if (codeItem) {
                        // 手动失焦
                        this.$refs.goodsAutoCompleteRef?.manualBlur();
                        this._traceCodeRepeatDialog = new TraceCodeRepeatDialog({
                            visible: true,
                            isValidateCount: true,
                            title: '采集相同追溯码',
                            traceCodeInfo: {
                                ...res.keywordTraceableCodeNoInfo,
                                count: (codeItem.count || 1),
                            },
                            onConfirm: (data) => {
                                // 不改变原数据，只是更新数量
                                Object.assign(codeItem, data);
                            },
                        });
                        this._traceCodeRepeatDialog.generateDialogAsync();
                        return;
                    }

                    // 录入当前扫码的追溯码
                    item.traceableCodeList.push({
                        ...res.keywordTraceableCodeNoInfo,
                    });

                    return item;
                }
            },
            async selectGoods(goods, res) {
                let repeatGoodsIndex = this.order.list.findIndex((item) => {
                    return item.goodsId === goods.id;
                });

                // 扫追溯码走下面逻辑
                if (this.isEnableTraceableCode && res?.keywordTraceableCodeNoInfo) {
                    this.findCanAddTraceCodeItem(goods, res);

                    // 扫码重复药品不自动添加了
                    if (repeatGoodsIndex > -1) {
                        this.focusInput(repeatGoodsIndex);
                        return;
                    }

                }
                if (this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    if (goods.subType !== GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '空中库房只支持中药饮片、颗粒',
                        });
                        return false;
                    }
                }

                const clinicId = this.order.toOrganId || this.clinicId;
                let goodsObj = null;

                try {
                    const { data } = await GoodsAPI.goods(goods.id, clinicId, {
                        throwExceptionIfDel: 1,
                        withStock: 1,
                        forPurchase: 1,
                        withShebaoCode: 1,
                        pharmacyNo: this.order?.pharmacy?.no,
                    });
                    goodsObj = data;
                } catch (e) {
                    if (e.code === 12015) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                        });
                    } else {
                        this.$Toast({
                            message: e.message || '查询药品详情失败',
                            type: 'error',
                        });
                    }
                }

                if (!goodsObj) return;

                const tempGoods = {
                    keyId: createGUID(),
                    goods: goodsObj,
                    goodsId: goods.id,
                    useCount: '',
                    useUnit: this.initGoodsUseUnit(goodsObj),
                    useUnitCostPrice: '',
                    batchNo: '',
                    useTotalCostPrice: '',
                    expiryDate: '',
                    productionDate: '',
                };

                if (this.isShebaoCollect) {
                    tempGoods.shebaoCode = goodsObj.shebaoNationalCode || '';
                    tempGoods.erpGoodsId = '';
                    tempGoods.emergencyFlag = 0;
                    tempGoods.erpOrderItemId = '';
                }

                if (this.isEnableTraceableCode) {
                    tempGoods.traceableCodeList = res?.keywordTraceableCodeNoInfo ? [{
                        ...res.keywordTraceableCodeNoInfo,
                    }] : [];
                }

                // 二次检查是否重复药品
                repeatGoodsIndex = this.order.list.findIndex((item) => {
                    return item.goodsId === goods.id;
                });

                if (repeatGoodsIndex > -1) {
                    const repeatGoods = this.order.list[repeatGoodsIndex];
                    tempGoods.expiredWarnMonths = repeatGoods.expiredWarnMonths;
                    this.sortOrderListDesc(repeatGoods.goodsId);

                    // 有追溯码信息,追加后return,因为不需要新加数据，手动加重复药品不拦截
                    if (this.isEnableTraceableCode && res.keywordTraceableCodeNoInfo) {
                        this.findCanAddTraceCodeItem(goods, res);
                        this.focusInput(repeatGoodsIndex);
                        return;
                    }
                }

                tempGoods.searchGoodsKey = goodsObj.medicineCadn || goodsObj.name;
                tempGoods.isMatched = 1;
                this.order.list.push(tempGoods);
                this.autoSaveDraftAsync();

                this.searchKey = '';
                this.$refs.goodsAutoCompleteRef?.handleClear();
                this.focusInput(this.order.list.length - 1, true, true);

                if (this.isImportOrder) {
                    Logger.reportAnalytics('goods-business', {
                        key: 'importOrderBehaviorRecord',
                        value: '添加药品',
                        goods: tempGoods,
                    });
                }
            },
            // 滚动table,聚焦输入框
            focusInput(index, isScroll = true) {
                if (isScroll) {
                    console.log('scrollToElement', index);
                    this.$refs.tableRef?.scrollToElement({
                        index,
                        top: 47,
                        time: 60,
                        behavior: 'instant',
                    });
                }
                if (this.focusTimer) {
                    clearTimeout(this.focusTimer);
                    this.focusTimer = null;
                }
                this.focusTimer = setTimeout(() => {
                    const keyId = this.customTrKey(this.order.list[index]);

                    const $tableTr = this.$refs.tableRef?.$el?.querySelector(`.abc-table-tr--${keyId}`);
                    $tableTr && $tableTr.querySelector('.focus-input input').focus();
                }, this.timeout);
            },
            beforeSaveDraft() {
                this.actionType = 'save_draft';
                this.saveDraftBtnLoading = true;
                this.validateDateVirtualListData();

                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.createForm?.validate?.(
                        (val) => {
                            if (val) {
                                this.saveDraft();
                            } else {
                                this.saveDraftBtnLoading = false;
                            }
                        },
                        (item) => {
                            const { classList } = item.$el;
                            // 校验日期
                            if (classList.contains('form-item-production_date') || classList.contains('form-item-expiry_date')) {
                                return false;
                            }
                            return true;
                        },
                    );
                }, this.timeout);
            },

            beforeSubmit() {
                this.actionType = 'submit';
                this.submit();
            },
            /**
             * @desc 触发提交，先验证 / 判断 create / update
             * <AUTHOR>
             * @date 2018/11/23 20:16:49
             */
            async submit(isFocus = false) {
                this.buttonLoading = true;
                this.validateCell = false;

                this.validateVirtualListData();
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.createForm?.validate?.(async (val) => {
                        try {
                            if (val) {

                                if (this.isEnableTraceableCode && !isFocus) {
                                    let validateTraceCodeMaxCountNum = 0;
                                    this.order.list.forEach((item) => {
                                        const refKey = `traceableCodeCellRef${item.keyId}`;
                                        const traceableCodeCellRefs = this.$refs[refKey];
                                        const isValidate = traceableCodeCellRefs?.validateTraceCodeMaxCount?.() ?? true;
                                        if (!isValidate) validateTraceCodeMaxCountNum++;
                                    });
                                    if (validateTraceCodeMaxCountNum) {
                                        this.$confirm({
                                            type: 'warn',
                                            title: '追溯码采集提示',
                                            content: `${validateTraceCodeMaxCountNum} 个药品追溯码采集条数不符合医保要求`,
                                            confirmText: '去修改',
                                            showCancel: false,
                                        });
                                        this.buttonLoading = false;
                                        this.isStillSubmit = false;
                                        return;
                                    }

                                    const {
                                        flag, firstErrorIndex, errorList,
                                    } = await TraceCode.validate({
                                        scene: TraceCodeScenesEnum.INVENTORY,
                                        sceneType: SceneTypeEnum.GOODS_IN,
                                        dataList: this.order.list,
                                        createKeyId: this.customTrKey,
                                        getGoodsInfo: (e) => e.goods,
                                        getUnitInfo: (e) => {
                                            return {
                                                unitCount: e.useCount,
                                                unit: e.useUnit,
                                                countLabel: '入库数量',
                                            };
                                        },
                                        needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckStock(),
                                    });

                                    if (!flag) {

                                        const scrollError = () => {
                                            this.validateCell = true;
                                            this.buttonLoading = false;

                                            this.$refs.tableRef?.scrollToElement({
                                                index: firstErrorIndex,
                                                top: 47,
                                                time: 60,
                                                behavior: 'instant',
                                            });

                                            // eslint-disable-next-line abc/no-timer-id
                                            setTimeout(() => {
                                                this.$refs.createForm?.validate?.();
                                            }, this.timeout);
                                        };

                                        if (errorList.some((item) => item.isError)) {
                                            scrollError();
                                        } else {
                                            this.$confirm({
                                                type: 'warn',
                                                title: '追溯码采集风险提醒',
                                                content: errorList.map((it) => {
                                                    const {
                                                        count,
                                                        warnTips,
                                                    } = it;
                                                    return `有 ${count} 个商品${warnTips}`;
                                                }),
                                                confirmText: '去修改',
                                                cancelText: '仍要提交',
                                                disabledKeyboard: true,
                                                showClose: false,
                                                onConfirm: scrollError,
                                                onCancel: () => this.submit(true),
                                            });
                                        }
                                        return;
                                    }
                                }

                                // 海南对接药监追溯码查询
                                if (await TraceCode.getPharmaceuticalTraceCodeQueryConfig() && !this.isStillSubmit) {
                                    const data = await TraceCode.fetchPharmaceuticalTraceCodeDetails({
                                        dataList: this.order.list,
                                        beforeHook: () => {
                                            this._loading = this.$Loading({
                                                text: '正在核验追溯码',
                                                customClass: 'print-loading-wrapper',
                                            });
                                        },
                                        afterHook: () => {
                                            this._loading?.close();
                                        },
                                        errorHook: (error) => {
                                            this.$Toast({
                                                type: 'error',
                                                message: error?.message ?? '查询失败',
                                            });
                                        },
                                    });
                                    if (data?.length) {
                                        this.traceableCodeWarning(data);
                                        return;
                                    }
                                }

                                let title = '入库后将立即更新库存，确认入库吗？';

                                // 新增入库单  非总部，入库门店是当前门店
                                if (!this.isChainAdmin && !this.order.toOrganId) {
                                    this.order.toOrganId = this.clinicId;
                                }

                                const subClinic = this.currentSubClinicsArray.find((item) => {
                                    return item.id === this.order.toOrganId;
                                });

                                const name = subClinic && (subClinic.shortName || subClinic.name);

                                if (this.orderId && !this.isReInStock) {
                                    title = '入库单修改后，将会立即更新系统库存';
                                    if (this.order.toOrganId !== this.clinicId) {
                                        title = `修改入库单后，需要 ${name} 确认`;
                                    }
                                } else {
                                    if (this.order.toOrganId !== this.clinicId) {
                                        title = `入库单需要 ${name} 确认`;
                                    }
                                }

                                // 连锁分店，且打开了审核开关，则需要弹框提示需要总部审核
                                if (this.isChainSubStore && this.stockInChainReview) {
                                    title = '确定后将发给总部审核，审核通过后将立即更新库存';
                                }

                                this.$confirm({
                                    type: 'warn',
                                    title: '提示',
                                    content: title,
                                    onConfirm: () => {
                                        (this.orderId && !this.isReInStock) ? this.updateOrder() : this.createOrder();
                                    },
                                    onCancel: () => {
                                        this.buttonLoading = false;
                                        this.isStillSubmit = false;
                                    },
                                });
                            } else {
                                this.buttonLoading = false;
                                this.isStillSubmit = false;
                            }
                        } catch (e) {
                            console.error(e);
                            this.buttonLoading = false;
                            this.isStillSubmit = false;
                        }
                    });
                }, this.timeout);

            },
            validateDateVirtualListData() {
                let valid = true;
                let invalidIndex = -1;
                for (let i = 0; i < this.order.list.length; i++) {
                    const item = this.order.list[i];

                    // eslint-disable-next-line no-loop-func
                    this.validateDate(item.productionDate || '', (res) => {
                        if (res.validate === false) {
                            valid = false;
                            invalidIndex = i;
                        }
                    });

                    if (!valid) {
                        break;
                    }

                    // eslint-disable-next-line no-loop-func
                    this.validateDate(item.expiryDate || '', (res) => {
                        if (res.validate === false) {
                            valid = false;
                            invalidIndex = i;
                        }
                    });

                    if (!valid) {
                        break;
                    }
                }
                if (!valid) {
                    this.$refs.tableRef?.scrollToElement({
                        index: invalidIndex,
                        top: 47,
                        time: 60,
                        behavior: 'instant',
                    });
                }
                return valid;
            },

            validateVirtualListData() {
                let valid = true;
                try {
                    let invalidIndex = -1;
                    for (let i = 0; i < this.order.list.length; i++) {
                        const item = this.order.list[i];

                        // eslint-disable-next-line no-loop-func
                        this.handleValidateKey(item)(item, (res) => {
                            if (res.validate === false) {
                                valid = false;
                                invalidIndex = i;
                            }
                        });

                        if (!valid) {
                            break;
                        }

                        // eslint-disable-next-line no-loop-func
                        this.validateDate(item.productionDate || '', (res) => {
                            if (res.validate === false) {
                                valid = false;
                                invalidIndex = i;
                            }
                        });

                        if (!valid) {
                            break;
                        }

                        // eslint-disable-next-line no-loop-func
                        this.validateDate(item.expiryDate || '', (res) => {
                            if (res.validate === false) {
                                valid = false;
                                invalidIndex = i;
                            }
                        });

                        if (!valid) {
                            break;
                        }

                        // eslint-disable-next-line no-loop-func
                        this.validateNull((res) => {
                            if (res.validate === false) {
                                valid = false;
                                invalidIndex = i;
                            }
                        }, item, 'count');

                        if (!valid) {
                            break;
                        }

                        // eslint-disable-next-line no-loop-func
                        this.validateNull((res) => {
                            if (res.validate === false) {
                                valid = false;
                                invalidIndex = i;
                            }
                        }, item, 'unit');

                        if (!valid) {
                            break;
                        }

                        // eslint-disable-next-line no-loop-func
                        this.validateCostPrice((res) => {
                            if (res.validate === false) {
                                valid = false;
                                invalidIndex = i;
                            }
                        }, item);

                        if (!valid) {
                            break;
                        }

                        // eslint-disable-next-line no-loop-func
                        this.validateTotalPrice((res) => {
                            if (res.validate === false) {
                                valid = false;
                                invalidIndex = i;
                            }
                        }, item);

                        if (!valid) {
                            break;
                        }
                    }
                    if (!valid) {
                        this.$refs.tableRef?.scrollToElement({
                            index: invalidIndex,
                            top: 47,
                            time: 60,
                            behavior: 'instant',
                        });
                    }
                    return valid;
                } catch (e) {
                    console.error(e);
                    return valid;
                }
            },
            getDate(date, mask = 'YYYY-MM-DD') {
                try {
                    return formatDate(date, mask);
                } catch (e) {
                    console.error('getDate', e);
                    return '';
                }
            },
            transPostData(list, isUpdate = false) {
                return list.map((item) => {
                    const tempItem = {
                        goodsId: item.goodsId,
                        useUnit: item.useUnit,
                        useCount: item.useCount || 0,
                        useUnitCostPrice: item.useUnitCostPrice,
                        useTotalCostPrice: item.useTotalCostPrice,
                        batchNo: item.batchNo,
                        externalRelatedKey: item.externalRelatedKey,
                        expiryDate: this.getDate(item.expiryDate),
                        productionDate: this.getDate(item.productionDate),
                        skuGoodsId: item.skuGoodsId,
                        opType: item.opType,
                        traceableCodeList: TraceCode.transCodeList(item.traceableCodeList),
                    };

                    // 医保上报数据
                    if (this.isShebaoCollect) {
                        tempItem.extendData = {
                            shebaoCode: item.shebaoCode,
                            erpGoodsId: item.erpGoodsId,
                            emergencyFlag: item.emergencyFlag,
                            erpOrderItemId: item.erpOrderItemId,
                        };
                    }

                    if (isUpdate) {
                        tempItem.id = item.id;
                    }
                    return tempItem;
                });
            },
            transImportData(list = []) {
                return list.map((item) => {
                    const tempObj = {
                        ...item,
                        expiryDate: this.getDate(item.expiryDate),
                        productionDate: this.getDate(item.productionDate),
                        keyId: createGUID(),
                    };

                    // 补充无码标识
                    // this.initNoTraceCodeList(tempObj);

                    return tempObj;
                });
            },

            async createOrder(isForceSubmit = false) {
                const {
                    toOrganId: clinicId,
                    supplierId,
                    outOrderNo,
                    comment,
                    list,
                    pharmacyType,
                    pharmacyNo,
                    pharmacy,
                    type,
                    inspectBy,
                    purchaseType,
                    platformType,
                } = this.order;
                try {
                    this.buttonLoading = true;
                    const params = {
                        clinicId,
                        supplierId,
                        comment,
                        outOrderNo,
                        type,
                        inspectBy,
                        orderClientUniqKey: this._orderClientUniqKey,
                        pharmacyNo: pharmacyNo || pharmacy?.no,
                        pharmacyType: pharmacyType || pharmacy?.type,
                        list: this.transPostData(list),
                        mallOrderId: this.mallOrderId,
                    };

                    if (this.isShowPurchaseType) {
                        params.extendData = {
                            purchaseType,
                            platformType,
                        };
                    }

                    // 强制提交创建
                    if (isForceSubmit) params.forceSubmit = 1;
                    if (this._isCloudDraft) {
                        // 创建入库单时传入自动删除云草稿
                        params.inOrderDraftId = this.draftId;
                    }

                    await StockInAPI.createOrder(params);
                    // 清除异步草稿任务
                    clearTimeout(this._timer);

                    // 删除本地草稿
                    this.clearDraft('goods-in', this._draftId || this.draftId);

                    if (typeof this.refresh === 'function') {
                        this.refresh(true, 'add');
                    }

                    this.$emit('refresh', true, 'add');
                    this.showDialog = false;
                    this.buttonLoading = false;
                } catch (e) {
                    console.error(e);
                    if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goodsId !== e.detail.goodsId);
                        });
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goodsId !== e.detail.id);
                        });
                    } else if (e.code === 12814) {
                        // 当前草稿已删除、已提交，需要重新提交单据
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: e.message,
                            confirmText: '确认',
                            onConfirm: () => this.createOrder(true),
                        });
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                    }
                    this.buttonLoading = false;
                }
            },

            /** 没机会调这个接口
             * @desc  未确认之前，创建者可以修改整个入库单，类似新增入库，
             *        注意如果确定的时候，入库单状态被修改了，需要给出提示，当前入库单已被（who）确认
             * <AUTHOR>
             * @date 2018/11/23 11:02:39
             * @params
             * @return
             */
            async updateOrder() {
                try {
                    const {
                        supplierId, list, comment, lastModifiedDate, outOrderNo,
                    } = this.order;
                    this.buttonLoading = true;
                    const clinicId = this.order.toOrganId;

                    await StockInAPI.updateOrder(this.orderId, {
                        supplierId,
                        comment,
                        lastModifiedDate,
                        clinicId,
                        outOrderNo,
                        pharmacyNo: this.order.pharmacyNo,
                        pharmacyType: this.order.pharmacyType,
                        list: this.transPostData(list, true),
                    });

                    this.buttonLoading = false;
                    this.$emit('refresh', true);
                    this.showDialog = false;
                } catch (e) {
                    if (e.code === 962) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '当前入库单已被确定',
                            onClose: () => {
                                this.$emit('refresh');
                            },
                        });
                    } else if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail, () => {
                            this.order.list = this.order.list.filter((item) => item.goodsId !== e.detail.goodsId);
                        });
                        if (e.code === 12808) {
                            this.handleGoodsDisable(e.detail, () => {
                                this.order.list = this.order.list.filter((item) => item.goodsId !== e.detail.id);
                            });
                        }
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                        this.$emit('refresh');
                    }
                    this.buttonLoading = false;
                }
            },
            // 点击 x 关闭 提示内容有变化是否保存草稿,
            closeDialog() {
                const confirmTitle = this.draftId ? '草稿信息发生变动，是否保存？' : '是否需要保存为草稿？';
                if (!isEqual(this.order, this._cacheOrderDraft) && !this.orderId || this.importOrderData) {
                    const vm = this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: confirmTitle,
                        showConfirm: false,
                        showCancel: false,
                        footerPrepend: () => {
                            return (
                                <abc-space>
                                    <abc-button
                                        onClick={() => {
                                            this.saveDraft();
                                            vm.close();// 手动关闭
                                        }}
                                    >
                                        保存
                                    </abc-button>
                                    <abc-button
                                        type="blank"
                                        onClick={() => {
                                            this.closeDraftHandler('goods-in');
                                            this.$emit('close');
                                            this.showDialog = false;
                                            vm.close();// 手动关闭
                                        }}
                                    >
                                        不保存
                                    </abc-button>
                                </abc-space>
                            );
                        },
                    });
                } else {
                    this.$emit('close');
                    this.showDialog = false;
                }
            },
            async dialogOpen() {
                this.pushDialogName();

                if (this.importOrderData) {
                    // 导入的数据处理
                    this.importOrderData.list = this.transImportData(this.importOrderData.list || []);
                    Object.assign(this.order, this.importOrderData);
                    await this.initCollectCodeCountList(this.order.list);

                    this.order.list.forEach((item) => {
                        // 补充无码标识
                        this.initNoTraceCodeList(item);
                    });
                }

                this._cacheOrderDraft = Clone(this.order);
            },

            calCostPrice(item) {
                if (item.useCount !== '' && item.useTotalCostPrice !== '') {
                    item.useUnitCostPrice = this.calCostPriceSingle(item);
                }
            },

            /**
             * @desc 清空金额，无数量或进价时，清空金额
             * <AUTHOR>
             * @date 2022-04-26 14:57:42
             */
            calTotalPrice(item) {
                if (item.useCount === '' || item.useUnitCostPrice === '') {
                    item.useTotalCostPrice = '';
                } else {
                    item.useTotalCostPrice = this.calCostPriceTotal(item);
                }
            },

            isFloat(n) {
                return Number(n) !== parseInt(n);
            },

            sortOrderList() {
                const newOrderList = Clone(this.order.list);
                newOrderList.sort((a, b) => {
                    return (b.goodsId === this.goodsId) - (a.goodsId === this.goodsId);
                });
                this.order.list = Clone(newOrderList);
            },
            sortOrderListDesc(id) {
                const newOrderList = Clone(this.order.list);
                newOrderList.sort((a, b) => {
                    return (a.goodsId === id) - (b.goodsId === id);
                });
                this.order.list = Clone(newOrderList);
            },

            updateLastUseUnit(val, item) {
                if (val === 'g' || val === 'Kg') {
                    const cMSpec = item.goods?.cMSpec;// 中药颗粒-中药饮片-‘’
                    if (cMSpec === '中药颗粒') this.lastUseUnit.keli = val;
                    if (cMSpec === '中药饮片') this.lastUseUnit.yinpian = val;
                }
            },
            getCostConfig(item) {
                if (!item.goods) {
                    return {
                        formatLength: 5, max: 10000000, supportZero: true,
                    };
                }
                if (isChineseMedicine(item.goods) && item.useUnit === 'Kg') {
                    return {
                        formatLength: 2, max: 10000000, supportZero: true,
                    };
                }
                return {
                    formatLength: 5, max: 10000000, supportZero: true,
                };
            },
            openSupplierDialog() {
                this.$refs.supplierSelect.showPopper = false;
                this.showSupplierDialog = true;
            },
            openSupplierSelectPanel() {
                if (!this.currentSupplierList.length) {
                    Logger.reportAnalytics('goods-business', {
                        key: 'noSupplierData',
                        value: '无供应商数据',
                    });
                    this.initSupplierList(true);
                }
            },
            /**
             * @desc 新增供应商
             * <AUTHOR>
             * @date 2019/07/10 23:07:07
             * @params
             * @return
             */
            handleAddSupplier(supplier) {
                this.order.supplierId = supplier.id;
            },
            revokeHandler() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '是否确认撤回该申请，撤回后单据将失效',
                    onConfirm: async () => {
                        await this.revoke();
                    },
                });
            },
            async revoke() {
                try {
                    this.revokeLoading = true;
                    await StockInAPI.revokeOrder(this.orderId);
                    this.revokeLoading = false;
                    this.$emit('refresh');
                    this.showDialog = false;
                } catch (e) {
                    this.revokeLoading = false;
                }
            },
            /**
             * @desc 轮询请求虚拟库房入库单创建
             * <AUTHOR>
             * @date 2022-04-29 17:27:00
             * @params
             * @return
             */
            startCreateVirtualSupplierForm() {
                this.showImportLoading = true;
                const _timer = setInterval(async () => {
                    try {
                        const data = await this.createVirtualSupplierForm();
                        if (data.status === 10) {
                            this.order = data;
                            this.order.list = this.transImportData(this.order.list || []);
                            this.order.virtualSupplierInfo = this.virtualSupplierInfo;
                            this.order.toOrganId = this.virtualSupplierInfo.clinicId;
                            this.order.supplierId = this.order?.supplier?.id;
                            this.showImportLoading = false;
                            this.order.pharmacy = {
                                name: this.pharmacyName || data.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY ? '代煎代配' : '',
                                no: data.pharmacyNo,
                                type: data.pharmacyType,
                            };
                            clearInterval(_timer);
                        }
                    } catch (e) {
                        this.showImportLoading = false;
                        clearInterval(_timer);
                    }
                }, 10000);
            },

            /**
             * @desc 创建代煎代配库房的入库单
             * <AUTHOR>
             * @date 2022-04-13 18:06:39
             */
            async createVirtualSupplierForm() {
                this.showImportLoading = true;
                const {
                    clinicId,
                    supplierId,
                    begDate, // 销售时间范围
                    endDate,
                    costPricePercent, // 自定义进价比例
                } = this.virtualSupplierInfo;
                const { data } = await StockInAPI.virtualSupplierInOrder({
                    clinicId,
                    supplierId,
                    begDate, // 销售时间范围
                    endDate,
                    costPricePercent, // 自定义进价比例
                });
                return this.initVirtualFormData(data);
            },
            // 构造一个系统计算的入库数量，同用户修改后的数量进行对比
            initVirtualFormData(data) {
                data.list = data.list.map((item) => {
                    return {
                        lastUseUnit: item.useUnit,
                        lastUseCount: item.useCount,
                        ...item,
                    };
                });
                return data;
            },
            /**
             * @desc 用户填写的数量同系统计算的入库数量不一致时给出提示文案
             * <AUTHOR>
             * @date 2022-05-06 14:27:23
             */
            getVirtualSaleInfo(item) {
                const { virtualSupplierInfo } = this.order;
                if (virtualSupplierInfo) {
                    const {
                        begDate, endDate,
                    } = virtualSupplierInfo;
                    if (begDate && endDate && Number(item.useCount) !== Number(item.lastUseCount) && item.lastUseCount !== undefined) {
                        return `${begDate} ~ ${endDate}，该药品的销量为${item.lastUseCount}${item.lastUseUnit}`;
                    }
                }
                return '';
            },
            //是否能填写追溯码
            isEnterTraceableCode(item) {
                const canEnterTraceableCode =
                    item.useCount && this.isPackageUnitMedicine(item) && (isChinesePatentMedicine(item.goods) || isWesternMedicine(item.goods));
                if (!canEnterTraceableCode) {
                    item.traceableCodeList = null;
                }
                return canEnterTraceableCode;
            },
            //是否是大单位药
            isPackageUnitMedicine(item) {
                return item.useUnit === (item.goods?.packageUnit || '') ;
            },


            /**
             * @desc 替换商品
             * <AUTHOR>
             * @date 2023-09-19 17:16:27
             * @params
             * @return
             */
            async selectReplaceGoods(goods, item) {
                if (goods) {
                    const clinicId = this.order.toOrganId || this.clinicId;
                    let goodsObj = null;

                    try {
                        const { data } = await GoodsAPI.goods(goods.id, clinicId, {
                            throwExceptionIfDel: 1,
                            withStock: 1,
                            forPurchase: 1,
                            withShebaoCode: 1,
                            pharmacyNo: this.order?.pharmacy?.no,
                        });
                        goodsObj = data;
                    } catch (e) {
                        Logger.reportAnalytics('goods-business', {
                            key: 'selectReplaceGoods',
                            value: '查询药品详情失败',
                            goods,
                        });
                        if (e.code === 12015) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: e.message,
                            });
                        } else {
                            this.$Toast({
                                message: e.message || '查询药品详情失败',
                                type: 'error',
                            });
                        }
                    }

                    // 查询失败-不走后续逻辑避免数据异常
                    if (!goodsObj) return;

                    item.isMatched = 1;
                    item.goods = goodsObj;
                    item.searchGoodsKey = goods.medicineCadn || goods.name;
                    item.goodsId = goods.id;
                    item.useUnit = this.initGoodsUseUnit(goods);
                    item.traceableCodeList = [];
                    if (this.isShebaoCollect) {
                        item.shebaoCode = goods.shebaoNationalCode || '';
                        item.erpGoodsId = '';
                        item.emergencyFlag = 0;
                        item.erpOrderItemId = '';
                    }
                    this.currentEditIndex = -1;

                    await this.initCollectCodeCountList([item]);
                    this.initNoTraceCodeList(item);
                    this.autoSaveDraftAsync();

                    if (this.isImportOrder) {
                        Logger.reportAnalytics('goods-business', {
                            key: 'importOrderBehaviorRecord',
                            value: '修改药品',
                            goods,
                        });
                    }
                }
            },
            handleResetSearchKey(item) {
                const index = this.getRealIndex(item);

                if ((this.currentEditIndex !== -1 && !this.$refs?.addGoodsDropdownRef?.$refs?.downloadRef?.showPopper) || this.currentEditIndex !== index) {
                    item.searchGoodsKey = item.goods ?
                        (item.goods.medicineCadn || item.goods.name) : item.rowMatchInfo?.medicineCadn;
                    this.currentEditIndex = -1;
                    return true;
                }
            },
            handleAddNewGoods(type, item) {
                const index = this.getRealIndex(item);

                this.currentEditIndex = -1;
                const { rowMatchInfo } = item;
                const { specificationDict } = rowMatchInfo || {};
                this.importGoodsInfo = {
                    medicineCadn: rowMatchInfo?.medicineCadn,
                    specification: rowMatchInfo?.specification,
                    manufacturerFull: rowMatchInfo?.manufacturerFull,
                    medicineNmpn: rowMatchInfo?.medicineNmpn,
                    barCode: rowMatchInfo?.barCode,

                    pieceNum: specificationDict?.pieceNum,
                    pieceUnit: specificationDict?.pieceUnit,
                    packageUnit: specificationDict?.packageUnit,
                    componentContentNum: specificationDict?.dosageNum ,// 解析那边这个是容量
                    componentContentUnit: specificationDict?.dosageUnit,
                    medicineDosageNum: specificationDict?.componentNum,// 解析那边这个是成分
                    medicineDosageUnit: specificationDict?.componentUnit,
                };
                // import-goods中在使用
                this.currentGoodsIndex = index;

                if (this.useNewGoodsArchives) {
                    if (type !== GoodsTypeEnum.MEDICINE) {
                        this.importGoodsInfo.name = this.importGoodsInfo.medicineCadn;
                        this.importGoodsInfo.medicineCadn = '';
                    }
                    // 获取用户上次建档类型
                    const typeId = getUserLastTypeId(type);
                    this.currentTypeId = typeId || this.defaultGoodsTypeMap[type];
                    this.barCodeToAddMedicine = '';
                    this.addMedicineDialogVisible = true;
                } else {
                    this.addMedicineDialogVisible = false;
                    this.showAddMaterialDialog = false;
                    this.showAddGoodsDialog = false;
                    if (type === 1) {
                        this.addMedicineDialogVisible = true;
                        return;
                    }
                    if (type === 2) {
                        this.importGoodsInfo.name = this.importGoodsInfo.medicineCadn;
                        this.importGoodsInfo.medicineCadn = '';
                        this.showAddMaterialDialog = true;
                        return;
                    }
                    if (type === 3) {
                        this.importGoodsInfo.name = this.importGoodsInfo.medicineCadn;
                        this.importGoodsInfo.medicineCadn = '';
                        this.showAddGoodsDialog = true;
                    }
                }
            },
            /**
             * @desc 获取table中真实index
             * <AUTHOR>
             * @date 2024/12/16 上午10:15
             * table如果开启虚拟列表后index是渲染元素的位置，不是正确的在原始数据中的index
             */
            getRealIndex(currentItem) {
                const key = currentItem.keyId || currentItem.id;
                return this.indexMap[key];
            },
            /**
             * @desc 修改药品组件切换事件
             * <AUTHOR>
             * @date 2024/12/16 上午10:29
             * @param {Object} item 当前药品数据
             * @param {Number} index 当前药品数据在table中渲染的index，不是原数据中的index
             */
            editCurrentGoodsItem(item, index) {
                this.currentEditIndex = this.getRealIndex(item);
                this._timer = setTimeout(() => {
                    this.autoFocus(index + 1, false, 'input', '.abc-table-tr');
                }, 0);
            },
            // 更新商品标识码信息
            updateGoodsTraceableCodeNoInfo(goods, keywordTraceableCodeNoInfo) {
                this.order.list.forEach((item) => {
                    if (item.goods) {
                        // 更新新绑定商品标识码信息
                        if (goods && item.goods.id === goods.id) {
                            item.goods.traceableCodeNoInfoList = goods.traceableCodeNoInfoList;
                        }

                        // 清除已解绑商品的标识码信息、与录入的追溯码
                        if (keywordTraceableCodeNoInfo && item.goods.traceableCodeNoInfoList?.length) {
                            if (item.goods.id === keywordTraceableCodeNoInfo.goodsInfo?.id) {
                                item.goods.traceableCodeNoInfoList = item.goods.traceableCodeNoInfoList.filter((e) => {
                                    return e.drugIdentificationCode !== keywordTraceableCodeNoInfo.traceableCodeNoInfo?.drugIdentificationCode;
                                });

                                item.traceableCodeList = item.traceableCodeList?.filter((e) => {
                                    return e.no !== keywordTraceableCodeNoInfo.no;
                                });
                            }
                        }
                    }
                });
            },
        },
    };
</script>
<style lang="scss">
@import 'src/styles/theme.scss';
@import 'src/styles/mixin.scss';

.handle-traceable-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 14px;
    color: #005ed9;
    cursor: pointer;
}

.inventory-replace-goods-autocomplete-wrapper {
    &.is-not-matched .abc-input__inner {
        color: $Y2;
    }

    .abc-input__inner {
        height: 44px;
        border-color: transparent;
        border-radius: 0;
    }
}
</style>
