import {
    ref, computed,
} from 'vue';
import SupplierAPI from 'api/goods/supplier';
import { PharmacyTypeEnum } from 'views/common/enum';
import { isNull } from '@/utils';
import useInit from '@/hooks/base/use-init';
import {
    CHECK_IN_SUPPLIER_ID, initGoodsInSupplier,
} from 'views/inventory/constant';
import Logger from 'utils/logger';

// 记录是否产生新增或者修改供应商数据
const isUpdatedSupplier = ref(false);
const supplierList = ref([]);
// 上次新增的供应商
const lastAddSupplier = ref(null);
// 获取供应商数据
async function fetchSupplierList() {
    // todo临时测试拉全部数据，后续接口需支持
    const res0 = await SupplierAPI.searchSupplier({ pharmacyType: PharmacyTypeEnum.LOCAL_PHARMACY });
    const res2 = await SupplierAPI.searchSupplier({ pharmacyType: PharmacyTypeEnum.VIRTUAL_PHARMACY });
    const rows0 = res0?.data?.rows ?? [];
    rows0.push(initGoodsInSupplier);
    // 防止新建供应商后，立即拉取不到当前新建的供应商
    if (lastAddSupplier.value && !rows0.find((item) => item.id === lastAddSupplier.value.id)) {
        rows0.push(lastAddSupplier.value);
    }
    const rows2 = res2?.data?.rows ?? [];
    supplierList.value = rows0.concat(rows2);
}

const { init } = useInit(fetchSupplierList);

/**
 * @desc 供应商选择hooks
 * <AUTHOR>
 * @date 2024/12/4 上午11:42
 * @param options 配置参数
 * @param options.isRefreshList 是否刷新供应商数据
 */
export default function useSearchSupplier(options = {}) {
    const {
        immediate = true,
        excludeInitSupplier = false,
        status,
        pharmacyType,
    } = options || {};

    const searchKeyword = ref('');

    // 过滤供应商数据
    const currentSupplierList = computed(() => {
        const key = searchKeyword.value.toLocaleLowerCase();

        return supplierList.value.filter((s) => {

            // 排除初始化供应商
            if (excludeInitSupplier && s.id === CHECK_IN_SUPPLIER_ID) {
                return false;
            }

            let flag1 = true, flag2 = true, flag3 = true;
            // 过滤供应商名称
            if (key) {
                const name = (s.name || '').toLocaleLowerCase();
                const namePy = (s.namePy || '').toLocaleLowerCase();
                const namePyFirst = (s.namePyFirst || '').toLocaleLowerCase();

                if (name.includes(key) || namePy.includes(key) || namePyFirst.includes(key)) {
                    flag1 = true;
                } else {
                    flag1 = false;
                }
            }

            // 过滤供应商状态
            if (!isNull(status)) {
                flag2 = status === s.status;
            }

            // 过滤供应商类型
            if (!isNull(pharmacyType)) {
                flag3 = pharmacyType === s.pharmacyType;
            }

            return flag1 && flag2 && flag3;
        });
    });

    // 供应商搜索
    function fetchSuggestions(keyword = '') {
        searchKeyword.value = keyword.trim();
    }

    // 支持直接新增数据
    function addSupplier(item) {
        lastAddSupplier.value = item;
        supplierList.value.push(item);
        isUpdatedSupplier.value = true;
    }

    function findSupplier(id) {
        const supplier = supplierList.value.find((item) => {
            return item.id === id;
        });
        return supplier;
    }

    // 刷新供应商数据
    function initSupplierList(isRefresh = isUpdatedSupplier.value, params) {
        init(isRefresh, params);
        isUpdatedSupplier.value = false;
    }

    function supplierListRetry() {
        if (!currentSupplierList.value.length) {
            console.log('supplierListRetry reportAnalytics');
            Logger.reportAnalytics('goods-business', {
                key: 'noSupplierData',
                value: '无供应商数据',
            });
            initSupplierList(true);
        }
    }

    if (immediate) {
        init(isUpdatedSupplier.value);
        isUpdatedSupplier.value = false;
    }

    return {
        currentSupplierList,
        supplierList,
        initSupplierList,
        fetchSuggestions,
        addSupplier,
        findSupplier,
        supplierListRetry,
    };
}
