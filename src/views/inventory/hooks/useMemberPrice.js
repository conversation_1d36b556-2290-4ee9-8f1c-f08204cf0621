import Big from 'big.js';
import { getSafeNumber } from '@/utils';
import { RoundingMode } from '@/common/constants/inventory';
import {
    DiscountGoodsDiscountType, DiscountGoodsType,
} from 'views/inventory/goods/archives/constant';

/**
 * @desc 商品会员价相关逻辑
 * <AUTHOR>
 * @date 2024/7/4 下午6:47
 */
export default function useMemberPrice() {
    const getDiscountValue = (value) => {
        if (!value) return '';
        return Big(getSafeNumber(value)).div(10).toFixed(3, RoundingMode.ROUND_DOWN);
    };
    const getDiscountValueView = (value) => {
        if (!value) return '';
        return Big(getSafeNumber(value)).times(10).toFixed(2, RoundingMode.ROUND_DOWN);
    };

    const getDiscountTypeShortLabel = (memberPriceItem) => {
        const {
            discountGoodsType,
            discountGoodsDiscountType,
        } = memberPriceItem || {};

        if (discountGoodsType === DiscountGoodsType.goods) {
            return discountGoodsDiscountType === DiscountGoodsDiscountType.special ? '商品组特价' : '商品组折扣';
        }
        return '分类折扣';
    };

    const getDiscountTypeLabel = (memberPriceItem) => {
        return `跟随${getDiscountTypeShortLabel(memberPriceItem)}`;
    };

    return {
        getDiscountValue,
        getDiscountValueView,
        getDiscountTypeShortLabel,
        getDiscountTypeLabel,
    };
}
