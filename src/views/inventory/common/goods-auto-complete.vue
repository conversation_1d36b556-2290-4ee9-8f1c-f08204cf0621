<template>
    <abc-autocomplete
        ref="autoComplete"
        v-model="searchKey"
        v-abc-focus-selected
        :placeholder="placeholder"
        :width="width"
        :size="size"
        :inner-width="calcInnerWidth"
        :delay-time="0"
        :fetch-suggestions="medicineSearch"
        :async-fetch="true"
        :auto-focus-first="autoFocusFirst"
        :focus-show="focusShow"
        :clearable="clearable"
        :disabled="disabled"
        max-length="80"
        custom-class="inventory-suggestion-wrapper"
        @enterEvent="selectGoods"
        @blur="handleBlur"
        @focus="handleFocus"
        @enter="enter"
        @clear="handleClear"
    >
        <template v-if="showSuggestionHeader" slot="suggestion-header">
            <abc-flex align="center" style="height: 30px; padding-left: 8px;">
                <div
                    style="width: 234px; min-width: 234px; max-width: 234px; padding-right: 10px;"
                >
                    药名
                </div>
                <div style="width: 140px; padding-right: 10px;" class="ellipsis">
                    规格
                </div>
                <div v-if="withStock && showStock" style="width: 94px; padding-right: 10px; text-align: right;">
                    库存
                </div>
                <div style="flex: 1; padding-right: 10px;" class="ellipsis">
                    厂家
                </div>
                <div style="width: 60px;">
                </div>
            </abc-flex>
        </template>
        <template
            slot="suggestions"
            slot-scope="{
                suggestion, index, currentIndex
            }"
        >
            <dt
                class="suggestions-item"
                :class="{
                    selected: index === currentIndex,
                    'is-disabled': suggestion.v2DisableStatus || suggestion.disabled
                }"
                @click="selectGoods(suggestion)"
            >
                <div
                    style="width: 234px; min-width: 234px; max-width: 234px; padding-right: 10px;"
                    class="ellipsis"
                    :title="suggestion | goodsFullName"
                >
                    {{ suggestion | goodsFullName }}
                </div>
                <div style="width: 140px; padding-right: 10px;" class="ellipsis" :title="suggestion | goodsDisplaySpec">
                    {{ suggestion | goodsDisplaySpec }}
                </div>
                <div v-if="withStock && showStock" style="width: 94px; padding-right: 10px; text-align: right;">
                    {{ showAssignGoodsCount(suggestion) }}
                </div>
                <div style="flex: 1; padding-right: 10px;" class="ellipsis" :title="suggestion.manufacturer">
                    {{ suggestion.manufacturer }}
                </div>
                <div style="width: 60px;">
                    <template v-if="suggestion.v2DisableStatus">
                        已停用
                    </template>
                </div>
            </dt>
        </template>
        <slot slot="prepend" name="prepend"></slot>
        <slot slot="append" name="append"></slot>
    </abc-autocomplete>
</template>
<script>
    import { mapGetters } from 'vuex';
    import * as repository from 'MfFeEngine/repository';
    import GoodsAPI from 'api/goods';
    import EnterEvent from 'views/common/enter-event';
    import {
        isBarcode, isNull,
    } from '@/utils';
    import { showAssignGoodsCount } from 'views/inventory/goods-utils';
    import { GoodsTypeEnum } from '@abc/constants';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';
    import Logger from 'utils/logger';
    import TraceCode from '@/service/trace-code/service';

    export default {
        name: 'GoodsAutoComplete',
        mixins: [EnterEvent],
        props: {
            search: String,
            size: String,
            placeholder: String,
            isOut: Boolean,
            formatCountKey: String,// 'out'
            withStock: {
                type: Boolean,
                default: true,
            },
            showStock: {
                type: Boolean,
                default: true,
            },
            needFilterDisable: {
                // 是否需要过滤停用的药品
                type: Boolean,
                default: false,
            },
            onlyStock: {
                type: Boolean,
                default: false,
            },
            clinicId: {
                type: String,
                default: '',
            },
            clearSearchKey: {
                type: Boolean,
                default: true,
            },
            enableBarcodeDetector: {
                type: Boolean,
                default: true,
            },
            filter: {
                type: Function,
                default: null,
            },
            autoFocusFirst: {
                type: Boolean,
                default: true,
            },
            width: {
                type: Number,
                default: 200,
            },
            focusShow: {
                type: Boolean,
                default: false,
            },
            clearable: {
                type: Boolean,
                default: false,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            type: {
                type: [String, Number],
                default: '',
            },
            // 采购模块支持西成药，允许多个subType 和 subType的参数
            isTypeArr: {
                type: Boolean,
                default: false,
            },
            typeArr: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            subType: {
                type: [String, Number],
                default: '',
            },
            subTypeArr: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            cMSpec: {
                type: [String, Number],
                default: '',
            },
            customTypeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            typeIdList: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 支持模糊搜索
            supportKeywordSearch: {
                type: Boolean,
                default: false,
            },
            inorderConfig: {
                type: Number,
            },
            pharmacyNo: {
                type: [String, Number],
            },
            enableLocalSearch: {
                type: Boolean,
                default: false,
            },
            // 是否搜索SPU
            isSpu: {
                type: Boolean,
                default: false,
            },
            // 是否可售
            isSell: {
                type: Number,
            },
            formatSearchResultFn: {
                type: Function,
                default: (res) => res,
            },
            needSearchNoStock: {
                type: Boolean,
                default: true,
            },
            priceType: Number,
            // enter auto focus next input
            nextInputAutoFocus: {
                type: Boolean,
                default: true,
            },
            // 无档案时是否提示
            isHasNoMedicine: {
                type: Boolean,
                default: false,
            },
            customErrorHandler: Function,
            showSuggestionHeader: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                startBarcodeDetect,
                stopBarcodeDetect,
                handleInputFocus,
                handleInputBlur,
            } = useBarcodeScanner();

            return {
                startBarcodeDetect,
                stopBarcodeDetect,
                handleInputFocus,
                handleInputBlur,
            };
        },
        data() {
            return {
                searchKey: '',
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            ...mapGetters(['subClinics', 'currentClinic', 'userInfo', 'isChainAdmin', 'isSingleStore']),
            hasEyeglasses() {
                return this.viewDistributeConfig?.Inventory?.hasEyeglasses;
            },
            calcInnerWidth() {
                let width = 660;
                if (this.withStock && this.showStock) {
                    width += 94;
                }
                return width;
            },
        },
        watch: {
            searchKey(v) {
                this.$emit('update:search', v);
                if (!v) {
                    this.$emit('clear');
                }
            },
            search: {
                handler(v) {
                    this.searchKey = v;
                },
                immediate: true,
            },
            enableBarcodeDetector: {
                handler(v) {
                    // console.log('watch enableBarcodeDetector, v ' + v);
                    if (v) {
                        this.startBarcodeDetect(this.onDetectBarcode);
                    } else {
                        this.stopBarcodeDetect();
                    }
                },
                immediate: true,
            },
        },
        methods: {
            showAssignGoodsCount(goods) {
                if (this.formatCountKey) {
                    return showAssignGoodsCount(goods,this.formatCountKey);
                }
                return showAssignGoodsCount(goods);
            },
            handleClose() {
                this.$refs.autoComplete?.handleClose?.();
            },
            async enter(e, selectItem) {
                // e.preventDefault();
                // e.stopPropagation();
                const { value } = e.currentTarget;

                if (!selectItem && (TraceCode.isTraceableCode(value) || isBarcode(value))) {
                    // 条形码、追溯码搜索
                    this.handleBarcode(value, () => {
                        this.$emit('searchGoods', value);
                    });
                } else if (!selectItem && value.trim()) {
                    // 关键字搜索
                    this.$emit('searchGoods', value);
                } else {
                    this.nextInputAutoFocus && this.enterEvent(e);
                    if (this.supportKeywordSearch) {
                        this.$emit('searchGoods', '');
                    }
                }
                // 敲回车后药品推荐弹窗收起
                this.handleClose();
            },

            async handleBarcode(barcode, callback) {
                if (isNull(barcode)) return;

                try {
                    const { data } = await GoodsAPI.search({
                        key: barcode,
                        withStock: this.withStock,
                        onlyStock: this.onlyStock,
                        clinicId: this.clinicId,
                        needFilterDisable: this.needFilterDisable,
                        inorderConfig: this.inorderConfig,
                        pharmacyNo: this.pharmacyNo,
                        customTypeId: this.customTypeIdList,
                        typeId: this.typeIdList,
                    });
                    // 追溯码或者条码才走下面逻辑
                    if (data.keywordTraceableCode || isBarcode(barcode)) {
                        const ret = (data && data.list) || [];
                        if (ret.length) {
                            this.selectGoods(ret[0]);
                            // eslint-disable-next-line abc/no-timer-id
                            setTimeout(() => {
                                this.handleClose();
                            }, 500);
                            if (this.clearSearchKey) {
                                this.searchKey = '';
                            }
                        } else {
                            callback && callback();
                            // 扫的追溯码,未绑定商品应该出提示弹窗，外部自行处理
                            if (!this.isOut && data.keywordTraceableCode && !isBarcode(barcode)) {
                                this.$emit('traceableCodeEnter', data.keywordTraceableCode);
                                return;
                            }
                            this.errorHandler(barcode);
                        }
                    } else {
                        callback && callback();
                    }

                } catch (err) {
                    console.error(err);
                    Logger.reportAnalytics('goods-business', {
                        key: 'handleBarcodeError',
                        err,
                    });
                    callback && callback();
                    if (err.code === 12015) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: err.message,
                        });
                    }
                }
            },

            async errorHandler(barcode) {
                $(this.$el).find('input').blur();

                // 根据查询到的信息做不同提示
                const res = await GoodsAPI.search({
                    key: barcode,
                    clinicId: this.clinicId,
                    pharmacyNo: this.pharmacyNo,
                });

                const list = res?.data?.list ?? [];

                // 已建过档
                if (list[0]) {
                    if (typeof this.customErrorHandler === 'function') {
                        this.customErrorHandler(list[0]);
                        return;
                    }

                    if (list[0].noStocks) {
                        this.$alert({
                            type: 'warn',
                            title: '商品未入库',
                            content: '此商品未入过库，请先入库再进行操作',
                            onClose: () => {
                                this.searchKey = '';
                                $(this.$el).find('input').focus();
                            },
                        });
                    }

                } else {
                    if (this.isHasNoMedicine && this.isSingleStore) {
                        this.$emit('hasNoMedicine', this.searchKey);
                        return;
                    }

                    this.$alert({
                        type: 'warn',
                        title: '条码不存在',
                        content: '此条码无匹配商品，请联系商品管理员补充条码',
                        onClose: () => {
                            this.searchKey = '';
                            $(this.$el).find('input').focus();
                        },
                    });
                }
            },

            async medicineSearch(key, callback) {
                key = key.trim();
                if (key) {
                    this._goods_repo_instance = repository.GoodsRepositoryService.getInstance();

                    let {
                        pharmacyNo,
                        onlyStock,
                        enableLocalSearch,
                    } = this;
                    let defaultType = this.isTypeArr ? this.typeArr : this.type;
                    // 本地搜索参数处理
                    if (enableLocalSearch && this._goods_repo_instance?.searchConfig?.useLocalSearchGoodsAPI) {
                        // 总部启用本地搜索的情况下，不带药房号进行搜索（总部没入库的会搜不出)
                        if (this.isChainAdmin) {
                            pharmacyNo = '';
                            if (this.needSearchNoStock) {
                                onlyStock = '';
                            }
                            // 总部指定门店搜索时，不走本地搜索
                            if (this.clinicId && this.clinicId !== this.currentClinic.clinicId) {
                                enableLocalSearch = false;
                            }
                        }
                        // 保证本地搜索只搜药品物资商品眼镜
                        if (!defaultType || !defaultType.length) {
                            defaultType = [GoodsTypeEnum.MEDICINE, GoodsTypeEnum.GOODS, GoodsTypeEnum.MATERIAL];
                            if (this.hasEyeglasses) {
                                defaultType.push(GoodsTypeEnum.EYEGLASSES);
                            }
                        }
                    }
                    const res = await this._goods_repo_instance.searchStockGoods({
                        key,
                        isSpu: this.isSpu,
                        isSell: this.isSell,
                        withStock: this.withStock ? 1 : '',
                        onlyStock: onlyStock ? 1 : '',
                        clinicId: this.clinicId,
                        type: defaultType,
                        subType: this.isTypeArr ? this.subTypeArr : this.subType,
                        cMSpec: this.cMSpec,
                        disable: this.needFilterDisable ? 0 : '',
                        inorderConfig: this.inorderConfig,
                        pharmacyNo,
                        priceType: this.priceType,
                        customTypeId: this.customTypeIdList.length ? this.customTypeIdList.map(Number) : '',
                        typeId: this.typeIdList.length ? this.typeIdList.map(Number) : '',
                        limit: 200,// 默认只展示40条，可能要搜索在40条后，导致看不到。
                    }, enableLocalSearch, this.hasEyeglasses ? 2 : 1);

                    let list = this.formatSearchResultFn(res?.list ?? []);
                    if (this.$props.filter) {
                        list = this.$props.filter(list);
                    }
                    callback(list);
                } else {
                    callback([]);
                }
            },
            async selectGoods(goods) {
                if (!goods) {
                    return false;
                }
                if (goods.disabled) {
                    return false;
                }
                this.$emit('selectGoods', goods);
            },
            handleBlur(event) {
                this.$emit('blur', event);
                if (this.enableBarcodeDetector) {
                    // 里面在启动扫码监听
                    this.handleInputBlur();
                }
            },
            handleFocus(event) {
                this.$emit('focus', event);
                this.handleInputFocus();
            },
            handleClear() {
                this.searchKey = '';
                this.$emit('clear');
            },

            onDetectBarcode(e, barcode) {
                this.handleBarcode(barcode);
                // 抛出事件，让业务能够自由处理
                this.$emit('enterBarcode', barcode, e);
            },
            
            manualFocus() {
                this.$refs.autoComplete?.$refs?.abcinput?.focus();
                this.handleFocus();
            },

            manualBlur() {
                this.$refs.autoComplete?.$refs?.abcinput?.blur();
            },
        },
    };
</script>
<style lang="scss">
.abc-autocomplete-wrapper {
    .append-input i {
        color: var(--abc-color-T3) !important;
    }
}

.inventory-suggestion-wrapper {
    .suggestions-item {
        &.is-disabled {
            color: var(--abc-color-T2);
        }
    }
}
</style>
