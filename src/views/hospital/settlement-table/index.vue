<template>
    <abc-table
        style="height: 438px;"
        class="changhu-settlement-table"
        support-child-fold
        child-key="composeChildren"
        :render-config="renderConfig"
        :data-list="renderDataList"
        :custom-tr-class="() => 'changhu-settlement-table-tr'"
        :scroll-load-config="{
            fetchData: scrollLoadConfig.fetchData,
            total: currentTotal,
        }"
    >
    </abc-table>
</template>

<script>
    import Vue from 'vue';
    import ChargeAPI from 'api/charge';
    import SettlementTable from './table';
    import {
        ChargeItemStatusEnum,
        ChargeStatusEnum,
    } from '@/common/constants/charge';
    import { GoodsTypeEnum } from '@abc/constants';
    import Common from 'src/views/cashier/table/common.js';
    import {
        isNumber,
        formatMoney,
    } from '@/utils';
    import {
        selfPayText, medicalFeeGrade2Str, getSpec,
    } from '@/common/filters';
    import { getSelfPayPropTips } from '@/common/social-info';
    import IconReturn from '@/assets/images/icon-return.png';
    import IconUncharged from '@/assets/images/icon-uncharged.png';
    import Logger from 'utils/logger';
    import { isNull } from '@/common/utils.js';

    const TypeEnum = Object.freeze({
        CURRENT: 1,
        OUT: 2,
    }) ;

    export default {
        name: 'ChanghuSettlementTable',
        mixins: [Common],
        props: {
            data: {
                type: Array,
                required: true,
            },
            outDiagnosisData: {
                type: Array,
                default() {
                    return [];
                },
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            canViewCostPrice: {
                type: Boolean,
                default: false,
            },
            showStatusImg: {
                type: Boolean,
                default: true,
            },
            scrollLoadConfig: {
                type: Object,
                default() {
                    return {};
                },
            },
            canEditLimitPrice: {
                type: Boolean,
                default: false,
            },
            hospitalSheetId: String,
        },
        data() {
            return {
                curTab: 1,
                tableHeadList: [],
            };
        },
        computed: {
            renderConfig() {
                return SettlementTable.getRenderConfig({
                    list: this.tableHeadList,
                    nameRender: this.nameRender,
                    unitPriceRender: this.unitPriceRender,
                    unitPriceEditRender: this.unitPriceEditRender,
                    shebaoSelfPayRatio: this.shebaoSelfPayRatio,
                    getUnitCountAndUnit: this.getUnitCountAndUnit,
                });
            },
            renderDataList() {
                let result = [];
                if (this.curTab === TypeEnum.OUT) {
                    result = this.outDiagnosisData;
                } else {
                    this.data.forEach((chargeSheet) => {
                        chargeSheet.chargeForms.forEach((form) => {
                            form.chargeFormItems.forEach((item) => {
                                if (item.composeChildren?.length) {
                                    const composeChildren = [];
                                    item.composeChildren.forEach((compose) => {
                                        if (!compose.composeChildren?.length) {
                                            composeChildren.push({
                                                ...compose,
                                                isSheetCharged: this.isSheetCharged(chargeSheet),
                                                isChild: true,
                                            });
                                        } else {
                                            compose.composeChildren.forEach((child) => {
                                                composeChildren.push({
                                                    ...child,
                                                    isSheetCharged: this.isSheetCharged(chargeSheet),
                                                    name: `${compose.name}-${child.name}`,
                                                    isChild: true,
                                                });
                                            });
                                        }
                                    });
                                    item.composeChildren = composeChildren;
                                }
                                result.push({
                                    ...item,
                                    isSheetCharged: this.isSheetCharged(chargeSheet),
                                });
                            });
                        });
                    });
                }
                return Vue.observable(result);
            },
            currentTotal() {
                const { isLast } = this.scrollLoadConfig;
                if (isLast) {
                    return this.renderDataList.length;
                }
                return this.renderDataList.length + 2;
            },
        },
        created() {
            this.getTableHeadList();
        },
        methods: {
            getTableHeadList() {
                this.tableHeadList = [
                    {
                        'key': 'name',
                        'label': '',
                        'testValue': '',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'flex': 1,
                            'maxWidth': '',
                            'minWidth': '',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'left',
                        },
                        // eslint-disable-next-line no-unused-vars
                        headerAppendRender: (h) => {
                            return (
                            <abc-tabs
                                value={this.curTab}
                                size="middle"
                                style={{
                                    height: '36px',
                                    borderBottom: 'none',
                                }}
                                option={
                                    [{
                                        label: '本院', value: TypeEnum.CURRENT,
                                    },{
                                        label: '外诊', value: TypeEnum.OUT,
                                    }]
                                }
                                onChange={(value) => {
                                    this.curTab = value;
                                }}
                            >
                            </abc-tabs>
                            );
                        },
                    },
                    {
                        'key': 'manufacturer',
                        'label': '厂家',
                        'testValue': '',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'maxWidth': '100px',
                            'minWidth': '100px',
                            'width': '100px',
                            'paddingLeft': '',
                            'paddingRight': '',
                        },
                    },
                    {
                        'key': 'selfPayProp',
                        'label': '医保',
                        'testValue': '',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'maxWidth': '70px',
                            'minWidth': '70px',
                            'width': '70px',
                            'paddingLeft': '',
                            'paddingRight': '',
                        },
                    },
                    {
                        'key': 'created',
                        'label': '时间',
                        'testValue': '',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'maxWidth': '70px',
                            'minWidth': '70px',
                            'width': '70px',
                            'paddingLeft': '',
                            'paddingRight': '',
                        },
                    },
                    {
                        'key': 'unitPrice',
                        'label': '单价',
                        'testValue': '',
                        'colType': 'money',
                        'isCheckbox': false,
                        'style': {
                            'maxWidth': '70px',
                            'minWidth': '70px',
                            'width': '70px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'right',
                        },
                    },
                    {
                        'key': 'unitPriceEdit',
                        'label': '',
                        'testValue': '',
                        'colType': '',
                        'isCheckbox': false,
                        'style': {
                            'maxWidth': '26px',
                            'minWidth': '26px',
                            'width': '26px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'center',
                        },
                    },
                    {
                        'key': 'unitCountAndUnit',
                        'label': '数量',
                        'testValue': '',
                        'colType': 'text',
                        'isCheckbox': false,
                        'style': {
                            'maxWidth': '66px',
                            'minWidth': '66px',
                            'width': '66px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'center',
                        },
                    },
                    {
                        'key': 'totalPrice',
                        'label': '金额',
                        'testValue': '',
                        'colType': 'money',
                        'isCheckbox': false,
                        'style': {
                            'maxWidth': '76px',
                            'minWidth': '76px',
                            'width': '76px',
                            'paddingLeft': '',
                            'paddingRight': '',
                            'textAlign': 'right',
                        },
                    }];
            },

            nameRender(h, item) {
                let medicalFeeGradeString = '';
                let selfPayPropTips = '';
                if (item.usageInfo && item.usageInfo.payType === 10) {
                    medicalFeeGradeString = '自';
                } else if (item.productInfo && item.productInfo.medicalFeeGrade) {
                    medicalFeeGradeString = `${medicalFeeGrade2Str(item.productInfo.medicalFeeGrade)}`;
                    selfPayPropTips = getSelfPayPropTips(item.productInfo, this.shebaoCardInfo);
                }
                const spec = getSpec(item.productInfo);
                return (
                    <abc-flex
                        class="abc-table-cell table-cell table-cell-default table-cell__padding-default table-cell__height-default"
                        gap={4}
                        style={{ paddingLeft: item.isChild ? '50px' : 'calc(var(--abc-table-cell-padding-default) + 4px)' }}
                    >
                        <abc-tooltip
                            placement="top"
                            theme="black"
                            disabled={!selfPayPropTips}
                            content={selfPayPropTips}
                        >
                            <abc-text tag="p" theme="gray">{medicalFeeGradeString}</abc-text>
                        </abc-tooltip>
                       <abc-text
                           v-abc-goods-hover-popper={{
                                   showCostPrice: this.canViewCostPrice,
                                   goods: item,
                                   showF1: item.productType !== GoodsTypeEnum.MATERIAL && item.productType !== GoodsTypeEnum.GOODS,
                               }}>
                           {item.name}
                       </abc-text>
                        <abc-text tag="p" theme="gray" size="mini">{spec}</abc-text>
                        {
                            (this.showStatusImg && item.isSheetCharged) ?
                                (
                                    item.status === ChargeItemStatusEnum.REFUND ? <abc-image height={18} src={IconReturn}></abc-image> : (
                                        item.status === ChargeItemStatusEnum.RETURNED ? <abc-image height={18} src={IconUncharged}></abc-image> : null
                                    )
                                ) : null
                        }
                    </abc-flex>
                );
            },

            unitPriceRender(h, item) {
                return (
                    <abc-table-cell>
                        <abc-flex vertical>
                            <abc-text>
                                {formatMoney(item.unitPrice)}
                            </abc-text>
                            {
                                item.sheBaoReceivableUnitFee && Number(item.sheBaoReceivableUnitFee) !== Number(item.unitPrice) ?
                                    (
                                        <abc-text
                                            size={item.sheBaoReceivableUnitFee ? 'small' : 'normal'}
                                            theme="gray"
                                        >
                                            {formatMoney(item.sheBaoReceivableUnitFee)}
                                        </abc-text>
                                    ) : null
                            }
                        </abc-flex>
                    </abc-table-cell>
                );
            },

            unitPriceEditRender(h, item) {
                return (
                    this.canEditLimitPrice &&
                    this.curTab === TypeEnum.CURRENT &&
                    item.status === ChargeItemStatusEnum.CHARGED &&
                    !item.composeChildren?.length
                ) ?
                    (
                        <abc-table-cell>
                            <abc-tooltip
                                placement="top"
                                content="临时修改医保结算金额，超出部分将收现金"
                                maxWidth="190px"
                                class="edit-limit-price-icon"
                            >
                                <abc-button
                                    icon="s-b-edited-line"
                                    variant="text"
                                    theme="default"
                                    size="small"
                                    onClick={() => {
                                        this.handleEditLimitPrice(item);
                                    }}
                                >
                                </abc-button>
                            </abc-tooltip>
                        </abc-table-cell>
                    ) : null;
            },

            shebaoSelfPayRatio(item) {
                const {
                    shebaoSelfPayRatio, productInfo,
                } = item;

                if (isNumber(shebaoSelfPayRatio)) {
                    return `${shebaoSelfPayRatio * 100}%`;
                }

                return selfPayText(productInfo?.selfPayProp, this.shebaoCardInfo, true);
            },

            getUnitCountAndUnit(item) {
                if (this.isChinesePrescriptionForm) {
                    const unitString = `${(item.productInfo && item.productInfo.pieceUnit) || 'g'} * ${item.doseCount || 1}剂}`;
                    return `${item.unitCount}${unitString}`;
                }
                return `${item.unitCount}${item.unit}`;

            },

            isSheetCharged(chargeSheet) {
                return chargeSheet.status !== ChargeStatusEnum.UN_CHARGE && chargeSheet.status !== ChargeStatusEnum.RETAIL;
            },

            handleEditLimitPrice(item) {
                const h = this.$createElement;
                item.expectedUnitPrice = item.sheBaoReceivableUnitFee ? formatMoney(item.sheBaoReceivableUnitFee) : null;
                const content = h(
                    'abc-flex',
                    {
                        props: {
                            vertical: true,
                            gap: 4,
                        },
                    },
                    [
                        h('abc-text', '将医保结算金额修改为：'),
                        h('abc-input',
                          {
                              props: {
                                  value: item.expectedUnitPrice,
                                  type: 'money',
                                  config: {
                                      formatLength: 2,
                                      max: item.unitPrice,
                                  },
                              },
                              on: {
                                  input: (value) => {
                                      item.expectedUnitPrice = value;
                                  },
                              },

                          },
                          [
                              h('abc-text',
                                {
                                    slot: 'appendInner',
                                },
                                '元',
                              ),
                          ],
                        ),
                        h('abc-text', '超出限价部分将转现金'),
                    ],
                );
                this.$modal({
                    title: ' ',
                    content,
                    closeAfterConfirm: false,
                    onConfirm: async (modelVm) => {
                        if (isNull(item.expectedUnitPrice)) return;
                        await this.updateItemPrice(modelVm, item);
                        this.$emit('refresh-social-fee');
                    },
                });
            },

            async updateItemPrice(modelVm, item) {
                try {
                    modelVm.confirmLoading = true;
                    const postData = {
                        hospitalSheetId: this.hospitalSheetId,
                        items: [
                            {
                                id: item.id,
                                expectedUnitPrice: item.expectedUnitPrice,
                                batches: [],
                            },
                        ],
                    };
                    await ChargeAPI.updateHospitalItemPrice(postData);
                    modelVm.close();
                    item.sheBaoReceivableUnitFee = item.expectedUnitPrice;
                } catch (err) {
                    Logger.error({
                        scene: 'updateHospitalItemPrice',
                        err,
                    });
                } finally {
                    modelVm.confirmLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
    .changhu-settlement-table {
        .changhu-settlement-table-tr {
            .edit-limit-price-icon {
                opacity: 0;
            }

            &:hover {
                .edit-limit-price-icon {
                    opacity: 1;
                }
            }
        }
    }
</style>
