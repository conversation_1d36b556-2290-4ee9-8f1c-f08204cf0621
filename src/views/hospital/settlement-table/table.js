import BaseProTable from '@/views/layout/tables/base-pro-table.js';
import { parseTime } from '@/utils';

export default class SettlementTable extends BaseProTable {
    name = 'SettlementTable';

    static getRenderConfig({
        list,
        nameRender,
        unitPriceRender,
        unitPriceEditRender,
        shebaoSelfPayRatio,
        getUnitCountAndUnit,
    }) {
        return {
            hasInnerBorder: false,
            list: list.map((config) => {
                if (config.key === 'name') {
                    config.customRender = nameRender;
                }
                if (config.key === 'manufacturer') {
                    config.dataFormatter = (val, item) => {
                        return (item.productInfo && item.productInfo.manufacturer) || '';
                    };
                }
                if (config.key === 'selfPayProp') {
                    config.dataFormatter = (val, item) => {
                        return shebaoSelfPayRatio(item);
                    };
                }
                if (config.key === 'created') {
                    config.dataFormatter = (val) => {
                        return parseTime(val, 'm-d');
                    };
                }
                if (config.key === 'unitPrice') {
                    config.customRender = unitPriceRender;
                }
                if (config.key === 'unitPriceEdit') {
                    config.customRender = unitPriceEditRender;
                }
                if (config.key === 'unitCountAndUnit') {
                    config.dataFormatter = (val, item) => {
                        return getUnitCountAndUnit(item);
                    };
                }
                return config;
            }),
        };
    }
}
