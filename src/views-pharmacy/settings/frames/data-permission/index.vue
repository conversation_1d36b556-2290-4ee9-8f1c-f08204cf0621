<template>
    <biz-setting-layout>
        <biz-setting-content>
            <biz-setting-form :label-width="148" no-limit-width>
                <!--零售-->
                <biz-setting-form-group v-if="isSingleStore || isChainSubStore" title="零售">
                    <biz-setting-form-item label="零售查看进价/毛利率" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.retail.goodsCostPrice.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有零售权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="retailViewCostPriceSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('retailViewCostPrice')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                    <biz-setting-form-item label="零售单查看毛利/毛利率" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.retail.profitConfig.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有零售单权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="retailProfitSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('retailProfit')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                    <biz-setting-form-item label="查看会员手机号" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.cashier.patientMobile.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有零售单权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="retailPatientMobileSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('retailPatientMobile')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                    <biz-setting-form-item label="查看会员毛利率" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.retail.patientProfit.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有零售权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="retailMemberProfitRateSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('retailMemberProfitRate')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                    <biz-setting-form-item label="零售余额充值/退款" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.retail.patientBalanceOpt.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有零售单权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="retailPatientBalanceSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('retailPatientBalance')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                    <biz-setting-form-item label="零售积分发放/抵扣" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.retail.patientPointsOpt.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有零售单权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="retailPatientPointsSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('retailPatientPoints')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                    <biz-setting-form-item label="零售优惠券发放/作废" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.retail.patientCouponOpt.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有零售单权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="retailPatientCouponSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('retailPatientCoupon')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                </biz-setting-form-group>

                <!--库存-->
                <biz-setting-form-group title="库存">
                    <biz-setting-form-item v-if="isSingleStore || isChainSubStore" label="查看药品物资成本" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.inventory.goodsCostConfig.goodsCost">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有库存权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="inventoryCostSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('inventoryCost')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        v-if="isSingleStore || isChainSubStore"
                        label="查看药品物资毛利"
                        label-line-height-size="small"
                    >
                        <abc-radio-group v-model="postDataPermission.inventory.goodsProfitConfig.goodsProfit">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有库存权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="inventoryProfitSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('inventoryProfit')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        v-if="isSingleStore || isChainSubStore"
                        label="库存单据查看成本"
                        label-line-height-size="small"
                    >
                        <abc-radio-group
                            v-model="postDataPermission.inventory.checkGoodsPrice.value"
                            @change="handleInventoryViewGoodsPriceChange"
                        >
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有报损，盘点权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="inventoryCheckGoodsPriceSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('inventoryCheckGoodsPrice')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        v-if="showCreateArchiveItem"
                        label="新建商品档案"
                        label-line-height-size="small"
                    >
                        <abc-radio-group
                            v-model="postDataPermission.inventory.goodsCreateArchivesConfig.goodsArchives"
                        >
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>有商品权限可操作</div>
                                    </abc-tooltip>
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="3" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>
                                            <template v-if="isChainAdmin">
                                                管理员/质量/采购人员可操作
                                            </template>
                                            <template v-if="isChainSubStore">
                                                管理员/店长可操作
                                            </template>
                                            <template v-if="isSingleStore">
                                                管理员/店长/质量/采购人员可操作
                                            </template>
                                        </div>
                                    </abc-tooltip>
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>
                                            <span>
                                                指定成员可操作
                                            </span>
                                            <abc-button
                                                v-show="inventoryGoodsCreateArchivesSelected"
                                                variant="text"
                                                size="small"
                                                @click="openMemberSettingDialog('inventoryGoodsCreateArchives')"
                                            >
                                                {{ !isAdmin ? '查看' : '设置' }}
                                            </abc-button>
                                        </div>
                                    </abc-tooltip>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        v-if="showModifyArchiveItem"
                        label="修改商品档案"
                        label-line-height-size="small"
                    >
                        <abc-radio-group
                            v-model="postDataPermission.inventory.goodsModifyArchivesConfig.goodsArchives"
                        >
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>有商品权限可操作</div>
                                    </abc-tooltip>
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="3" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>
                                            <template v-if="isChainAdmin">
                                                管理员/质量/采购人员可操作
                                            </template>
                                            <template v-if="isChainSubStore">
                                                管理员/店长可操作
                                            </template>
                                            <template v-if="isSingleStore">
                                                管理员/店长/质量/采购人员可操作
                                            </template>
                                        </div>
                                    </abc-tooltip>
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>
                                            <span>指定成员可操作</span>
                                            <abc-button
                                                v-show="inventoryGoodsModifyArchivesSelected"
                                                variant="text"
                                                size="small"
                                                @click="openMemberSettingDialog('inventoryGoodsModifyArchives')"
                                            >
                                                {{ !isAdmin ? '查看' : '设置' }}
                                            </abc-button>
                                        </div>
                                    </abc-tooltip>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        v-if="showDeleteArchiveItem"
                        label="删除商品档案"
                        label-line-height-size="small"
                    >
                        <abc-radio-group
                            v-model="postDataPermission.inventory.goodsDeleteArchivesConfig.goodsArchives"
                        >
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>有商品权限可操作</div>
                                    </abc-tooltip>
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="3" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>
                                            <template v-if="isChainAdmin">
                                                管理员/质量/采购人员可操作
                                            </template>
                                            <template v-if="isChainSubStore">
                                                管理员/店长可操作
                                            </template>
                                            <template v-if="isSingleStore">
                                                管理员/店长/质量/采购人员可操作
                                            </template>
                                        </div>
                                    </abc-tooltip>
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>
                                            <span>指定成员可操作</span>
                                            <abc-button
                                                v-show="inventoryGoodsDeleteArchivesSelected"
                                                variant="text"
                                                size="small"
                                                @click="openMemberSettingDialog('inventoryGoodsDeleteArchives')"
                                            >
                                                {{ !isAdmin ? '查看' : '设置' }}
                                            </abc-button>
                                        </div>
                                    </abc-tooltip>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        v-if="showSetPrice"
                        label="定价/调价"
                        label-line-height-size="small"
                    >
                        <abc-radio-group
                            v-model="postDataPermission.inventory.goodsAdjustPriceConfig.goodsAdjustPrice"
                        >
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>有商品权限可操作</div>
                                    </abc-tooltip>
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="3" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>
                                            <template v-if="isChainAdmin">
                                                管理员/运营人员可操作
                                            </template>
                                            <template v-if="isChainSubStore">
                                                管理员/店长可操作
                                            </template>
                                            <template v-if="isSingleStore">
                                                管理员/店长/运营人员可操作
                                            </template>
                                        </div>
                                    </abc-tooltip>
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2" :disabled="!isAdmin">
                                    <abc-tooltip placement="bottom" :disabled="isAdmin" content="无商品档案操作人设置权限，请联系总部设置">
                                        <div>
                                            <span>指定成员可操作</span>
                                            <abc-button
                                                v-show="inventoryGoodsAdjustPriceSelected"
                                                variant="text"
                                                size="small"
                                                @click="openMemberSettingDialog('inventoryGoodsAdjustPrice')"
                                            >
                                                {{ !isAdmin ? '查看' : '设置' }}
                                            </abc-button>
                                        </div>
                                    </abc-tooltip>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                </biz-setting-form-group>

                <!--报表-->
                <biz-setting-form-group v-if="isSingleStore || isChainSubStore" title="报表">
                    <biz-setting-form-item label="查看药品物资成本" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.statistics.goodsCostConfig.goodsCost">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有报表权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="statisticsCostSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('statisticsCost')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        label="查看药品物资毛利"
                        label-line-height-size="small"
                    >
                        <abc-radio-group v-model="postDataPermission.statistics.goodsProfitConfig.goodsProfit">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有报表权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="statisticsProfitSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('statisticsProfit')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                    <biz-setting-form-item
                        label="查看顾客手机号"
                        label-line-height-size="small"
                    >
                        <abc-radio-group v-model="postDataPermission.statistics.patientMobileConfig.patientMobile">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有报表权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="statisticsPatientMobileSelected"
                                        variant="text"
                                        size="small"
                                        @click="openMemberSettingDialog('statisticsPatientMobile')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                </biz-setting-form-group>
                <!--商城-->
                <biz-setting-form-group v-if="b2bMallIsPurchased && (isSingleStore || isChainSubStore)" title="商城">
                    <biz-setting-form-item label="查看商品价格" label-line-height-size="small">
                        <abc-radio-group v-model="postDataPermission.mall.goodsPriceConfig.goodsPrice">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有采购权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="lookGoodsPriceSelected"
                                        type="text"
                                        @click="openMemberSettingDialog('mallPrice')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>

                    <biz-setting-form-item
                        label="采购商品"
                        label-line-height-size="small"
                    >
                        <abc-radio-group v-model="postDataPermission.mall.goodsPurchaseConfig.goodsPurchase">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有采购权限可采购
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可采购
                                    <abc-button
                                        v-show="goodsPurchaseSelected"
                                        type="text"
                                        @click="openMemberSettingDialog('mallPurchase')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                </biz-setting-form-group>

                <!--会员-->
                <biz-setting-form-group
                    v-if="isSingleStore || isChainSubStore"
                    title="会员"
                >
                    <biz-setting-form-item
                        label="查看会员手机号"
                        label-line-height-size="small"
                    >
                        <abc-radio-group v-model="postDataPermission.crm.patientMobile.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有会员权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="crmPatientMobileSelected"
                                        type="text"
                                        @click="openMemberSettingDialog('crmPatientMobile')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                    <biz-setting-form-item
                        label="查看会员毛利率"
                        label-line-height-size="small"
                    >
                        <abc-radio-group v-model="postDataPermission.crm.patientProfit.value">
                            <abc-flex align="center" style="height: 26px;">
                                <abc-radio style="width: 248px;" :label="0">
                                    有会员权限可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="1">
                                    仅管理员可查看
                                </abc-radio>
                                <abc-radio style="width: 248px;" :label="2">
                                    指定成员可查看
                                    <abc-button
                                        v-show="crmMemberProfitRateSelected"
                                        type="text"
                                        @click="openMemberSettingDialog('crmMemberProfitRate')"
                                    >
                                        设置
                                    </abc-button>
                                </abc-radio>
                            </abc-flex>
                        </abc-radio-group>
                    </biz-setting-form-item>
                </biz-setting-form-group>
            </biz-setting-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-button :loading="loading" :disabled="!isUpdated" @click="submit">
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>
        <members-setting-dialog
            v-if="showMembersSettingDialog"
            :visible.sync="showMembersSettingDialog"
            :members="currentMembers"
            :dialog-title="currentMembersDialogTitle"
            :content-text="currentMembersSettingText"
            :text-before-text="textBeforeText"
            :content-text-tips="contentTextTips"
            :readonly="readonlySetting"
            @cancel="onCancel"
            @confirm="onConfirm"
        ></members-setting-dialog>
    </biz-setting-layout>
</template>

<script type="text/ecmascript-6">
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import Clone from 'utils/clone';
    import AbcAccess from '@/access/utils';
    import MembersSettingDialog from '@/views/settings/data-permission/members-setting-dialog.vue';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import {
        BizSettingForm,
        BizSettingFormGroup,
        BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';

    export default {
        name: 'DataPermissionConfig',
        components: {
            MembersSettingDialog,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
        },
        data() {
            return {
                loading: false,
                showMembersSettingDialog: false,// 是否显示成员设置弹窗
                membersSettingType: '',// 6种成员类型弹窗
                postDataPermission: {
                    dashboard: {},// 工作台
                    outpatient: { // 门诊
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                    },
                    pharmacy: { // 药房
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                    },
                    inventory: { // 库存
                        goodsCostConfig: {
                            goodsCost: 0,
                            employees: [],
                        },
                        goodsProfitConfig: {
                            goodsProfit: 0,
                            employees: [],
                        },
                        checkGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                        damageGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                        obtainGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                        transGoodsPrice: {
                            employees: [],
                            goodsProfit: 0,
                        },
                        goodsArchivesConfig: {
                            goodsArchives: 0,
                            employees: [],
                            roles: [],
                        },
                        goodsCreateArchivesConfig: {
                            goodsArchives: 0,
                            employees: [],
                            roles: [],
                        },
                        goodsModifyArchivesConfig: {
                            goodsArchives: 0,
                            employees: [],
                            roles: [],
                        },
                        goodsDeleteArchivesConfig: {
                            goodsArchives: 0,
                            employees: [],
                            roles: [],
                        },
                        goodsAdjustPriceConfig: {
                            goodsAdjustPrice: 0,
                            employees: [],
                            roles: [],
                        },
                    },
                    statistics: { // 统计
                        goodsCostConfig: {
                            goodsCost: 0,
                            employees: [],
                        },
                        goodsProfitConfig: {
                            goodsProfit: 0,
                            employees: [],
                        },
                        patientMobileConfig: {
                            patientMobile: 0,
                            employees: [],
                        },
                    },
                    mall: {
                        goodsPriceConfig: {
                            goodsPrice: 0,
                            employees: [],
                        },
                        goodsPurchaseConfig: {
                            goodsPurchase: 0,
                            employees: [],
                        },
                    },
                    medicalInsurance: {
                        viewHomePageConfig: {
                            viewHome: 0,
                            employees: [],
                        },
                        viewAccountConfig: {
                            viewAccount: 0,
                            employees: [],
                        },
                        viewBusinessRegistrationRecordConfig: {
                            viewBusinessRegistrationRecord: 0,
                            employees: [],
                        },
                        viewProfileDataConfig: {
                            viewProfileData: 0,
                            employees: [],
                        },
                        viewSetupInformationConfig: {
                            viewSetupInformation: 0,
                            employees: [],
                        },
                    },
                    crm: { // 患者
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                        patientProfit: {
                            value: 0,
                            employees: [],
                        },
                        modifyFirstFromAway: {
                            value: 0,
                            employees: [],
                        },
                    },
                    nurse: {
                        medicalHistory: 1, // 查看患者就诊历史: 1允许 0不允许
                    },
                    registration: { // 挂号预约
                        medicalHistory: 1, // 查看患者就诊历史: 1允许 0不允许
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                    },
                    cashier: { // 收费
                        patientMobile: {
                            value: 0,
                            employees: [],
                        },
                    },
                    retail: {
                        goodsCostPrice: {
                            value: 0,
                            employees: [],
                        },
                        profitConfig: {
                            value: 0,
                            employees: [],
                        },
                        patientBalanceOpt: {
                            value: 0,
                            employees: [],
                        },
                        patientPointsOpt: {
                            value: 0,
                            employees: [],
                        },
                        patientCouponOpt: {
                            value: 0,
                            employees: [],
                        },
                        patientProfit: {
                            value: 0,
                            employees: [],
                        },
                    },
                },
            };
        },
        computed: {
            ...mapGetters([
                'clinicConfig',
                'currentClinic',
                'isAdmin',
                'isChainAdmin',
                'isChainSubStore',
                'isSingleStore',
                'dataPermission',
                'goodsConfig',
                'showSubSetPrice',
                'showCreateArchives',
                'showModifyArchives',
                'showDeleteArchives',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            // 是否已经购买
            b2bMallIsPurchased() {
                return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.B2B_MALL);
            },
            // 是否展示有查看商品价格的人
            lookGoodsPriceSelected() {
                return this.postDataPermission.mall.goodsPriceConfig.goodsPrice === 2;
            },
            // 是否展示有采购权限的人
            goodsPurchaseSelected() {
                return this.postDataPermission.mall.goodsPurchaseConfig.goodsPurchase === 2;
            },
            // 选中了库存成本radio
            inventoryCostSelected() {
                return this.postDataPermission.inventory.goodsCostConfig.goodsCost === 2;
            },
            // 选中了库存利润radio
            inventoryProfitSelected() {
                return this.postDataPermission.inventory.goodsProfitConfig.goodsProfit === 2;
            },
            // 选中了库存利润radio
            retailProfitSelected() {
                return this.postDataPermission.retail.profitConfig?.value === 2;
            },
            // 选中了库存盘点金额
            inventoryCheckGoodsPriceSelected() {
                return this.postDataPermission.inventory.checkGoodsPrice.value === 2;
            },
            // 选中了新建/编辑药品档案radio
            inventoryGoodsArchivesSelected() {
                return this.postDataPermission.inventory.goodsArchivesConfig.goodsArchives === 2;
            },
            // 选中了新建药品档案radio
            inventoryGoodsCreateArchivesSelected() {
                return this.postDataPermission.inventory.goodsCreateArchivesConfig.goodsArchives === 2;
            },
            // 选中了编辑药品档案radio
            inventoryGoodsModifyArchivesSelected() {
                return this.postDataPermission.inventory.goodsModifyArchivesConfig.goodsArchives === 2;
            },
            // 选中了删除药品档案radio
            inventoryGoodsDeleteArchivesSelected() {
                return this.postDataPermission.inventory.goodsDeleteArchivesConfig.goodsArchives === 2;
            },
            // 选中了定价/调价radio
            inventoryGoodsAdjustPriceSelected() {
                return this.postDataPermission.inventory.goodsAdjustPriceConfig.goodsAdjustPrice === 2;
            },
            // 选中了统计成本radio
            statisticsCostSelected() {
                return this.postDataPermission.statistics.goodsCostConfig.goodsCost === 2;
            },
            // 选中了统计利润radio
            statisticsProfitSelected() {
                return this.postDataPermission.statistics.goodsProfitConfig.goodsProfit === 2;
            },
            // 选中了统计指定成员radio
            statisticsPatientMobileSelected() {
                return this.postDataPermission.statistics.patientMobileConfig.patientMobile === 2;
            },
            // 选中了零售查看患者手机号radio
            retailPatientMobileSelected() {
                return this.postDataPermission.cashier.patientMobile.value === 2;
            },
            // 选中了crm查看患者手机号radio
            crmPatientMobileSelected() {
                return this.postDataPermission.crm.patientMobile.value === 2;
            },
            // 选中了crm查看会员毛利率radio
            crmMemberProfitRateSelected() {
                return this.postDataPermission.crm.patientProfit?.value === 2;
            },
            // 选中了零售查看进价/毛利率radio
            retailViewCostPriceSelected() {
                return this.postDataPermission.retail.goodsCostPrice?.value === 2;
            },
            // 选中了零售查看患者余额radio
            retailPatientBalanceSelected() {
                return this.postDataPermission.retail.patientBalanceOpt?.value === 2;
            },
            // 选中了零售查看患者积分radio
            retailPatientPointsSelected() {
                return this.postDataPermission.retail.patientPointsOpt?.value === 2;
            },
            // 选中了零售优惠券发放radio
            retailPatientCouponSelected() {
                return this.postDataPermission.retail.patientCouponOpt?.value === 2;
            },
            // 选中了查看会员毛利率radio
            retailMemberProfitRateSelected() {
                return this.postDataPermission.retail.patientProfit?.value === 2;
            },

            currentMembers() {
                switch (this.membersSettingType) {
                    case 'inventoryCost':
                        return this.postDataPermission.inventory.goodsCostConfig.employees;
                    case 'inventoryProfit':
                        return this.postDataPermission.inventory.goodsProfitConfig.employees;
                    case 'inventoryCheckGoodsPrice':
                        return this.postDataPermission.inventory.checkGoodsPrice.employees;
                    case 'inventoryGoodsArchives':
                        return this.postDataPermission.inventory.goodsArchivesConfig.employees;

                    case 'inventoryGoodsCreateArchives':
                        return this.postDataPermission.inventory.goodsCreateArchivesConfig.employees;

                    case 'inventoryGoodsModifyArchives':
                        return this.postDataPermission.inventory.goodsModifyArchivesConfig.employees;

                    case 'inventoryGoodsDeleteArchives':
                        return this.postDataPermission.inventory.goodsDeleteArchivesConfig.employees;

                    case 'inventoryGoodsAdjustPrice':
                        return this.postDataPermission.inventory.goodsAdjustPriceConfig.employees;
                    case 'statisticsCost':
                        return this.postDataPermission.statistics.goodsCostConfig.employees;
                    case 'statisticsProfit':
                        return this.postDataPermission.statistics.goodsProfitConfig.employees;
                    case 'statisticsPatientMobile':
                        return this.postDataPermission.statistics.patientMobileConfig.employees;
                    case 'mallPrice':
                        return this.postDataPermission.mall.goodsPriceConfig.employees;
                    case 'mallPurchase':
                        return this.postDataPermission.mall.goodsPurchaseConfig.employees;
                    case 'retailProfit':
                        return this.postDataPermission.retail.profitConfig.employees;
                    case 'retailPatientMobile':
                        return this.postDataPermission.cashier.patientMobile.employees;
                    case 'retailViewCostPrice':
                        return this.postDataPermission.retail.goodsCostPrice.employees;
                    case 'retailPatientBalance':
                        return this.postDataPermission.retail.patientBalanceOpt.employees;
                    case 'retailPatientPoints':
                        return this.postDataPermission.retail.patientPointsOpt.employees;
                    case 'retailPatientCoupon':
                        return this.postDataPermission.retail.patientCouponOpt.employees;
                    case 'crmPatientMobile':
                        return this.postDataPermission.crm.patientMobile.employees;
                    case 'crmMemberProfitRate':
                        return this.postDataPermission.crm.patientProfit.employees;
                    case 'retailMemberProfitRate':
                        return this.postDataPermission.retail.patientProfit.employees;
                    default:
                        return [];
                }
            },
            currentMembersDialogTitle() {
                const title = {
                    inventoryCost: '库存权限设置',
                    inventoryProfit: '库存权限设置',
                    inventoryCheckGoodsPrice: '库存权限设置',
                    inventoryGoodsArchives: '库存权限设置',
                    inventoryGoodsAdjustPrice: '库存权限设置',
                    statisticsCost: '统计权限设置',
                    statisticsProfit: '统计权限设置',
                    statisticsPatientMobile: '统计权限设置',
                    mallPrice: '商城权限设置',
                    mallPurchase: '商城权限设置',
                    retailProfit: '零售权限设置',
                    retailPatientMobile: '零售权限设置',
                    retailViewCostPrice: '零售权限设置',
                    retailPatientBalance: '零售权限设置',
                    retailPatientPoints: '零售权限设置',
                    retailPatientCoupon: '零售权限设置',
                    retailMemberProfitRate: '零售权限设置',
                    crmPatientMobile: '会员权限设置',
                    crmMemberProfitRate: '会员权限设置',
                };
                return title[this.membersSettingType];
            },
            textBeforeText() {
                const beforeText = {
                    inventoryCost: '可查看',
                    inventoryProfit: '可查看',
                    inventoryCheckGoodsPrice: '可查看',
                    inventoryGoodsArchives: '可操作',
                    inventoryGoodsAdjustPrice: '可操作',
                    statisticsCost: '可查看',
                    statisticsProfit: '可查看',
                    statisticsPatientMobile: '可查看',
                    mallPurchase: '可采购',
                    retailProfit: '可查看',
                    retailPatientMobile: '可查看',
                    retailViewCostPrice: '可查看',
                    retailPatientBalance: '可操作',
                    retailPatientPoints: '可操作',
                    retailPatientCoupon: '可操作',
                    retailMemberProfitRate: '可查看',
                    crmPatientMobile: '可查看',
                    crmMemberProfitRate: '可查看',
                };
                return beforeText[this.membersSettingType];
            },
            contentTextTips() {
                const text = {
                    mallPrice: '管理员和有采购权限的人员始终可查看',
                    mallPurchase: '管理员和有采购权限的人员始终可采购',
                };
                return text[this.membersSettingType] ?? '';
            },
            currentMembersSettingText() {
                const text = {
                    inventoryCost: '药品物资成本',
                    inventoryProfit: '药品物资毛利',
                    inventoryCheckGoodsPrice: '库存单据药品成本',
                    inventoryGoodsArchives: '药品物资档案',
                    inventoryGoodsAdjustPrice: '药品物资价格',
                    statisticsCost: '药品物资成本',
                    statisticsProfit: '药品物资毛利',
                    statisticsPatientMobile: '顾客手机号',
                    mallPrice: '商品价格',
                    mallPurchase: '商品',
                    retailProfit: '零售毛利率',
                    retailPatientMobile: '会员手机号',
                    retailViewCostPrice: '零售进价/毛利率',
                    retailPatientBalance: '会员余额充值/退款',
                    retailPatientPoints: '会员积分发放/抵扣',
                    retailPatientCoupon: '会员优惠券发放/作废',
                    retailMemberProfitRate: '会员毛利率',
                    crmPatientMobile: '会员手机号',
                    crmMemberProfitRate: '会员毛利率',
                };
                return text?.[this.membersSettingType] ?? '';
            },
            // 页面数据是否发生变化
            isUpdated() {
                return !isEqual(this.dataPermission, this.postDataPermission);
            },
            showCreateArchiveItem() {
                if (this.isAdmin) return true;

                return this.showCreateArchives;
            },
            showModifyArchiveItem() {
                if (this.isAdmin) return true;

                return this.showModifyArchives;
            },
            showDeleteArchiveItem() {
                if (this.isAdmin) return true;

                return this.showDeleteArchives;
            },
            showSetPrice() {
                if (this.isAdmin) return true;

                return this.showSubSetPrice;
            },
            readonlySetting() {
                if (['inventoryGoodsCreateArchives', 'inventoryGoodsModifyArchives', 'inventoryGoodsDeleteArchives', 'inventoryGoodsAdjustPrice'].includes(this.membersSettingType)) {
                    return !this.isAdmin;
                }
                return false;
            },
        },
        watch: {
            dataPermission: {
                handler(value) {
                    console.log('watcher', value);
                    this.postDataPermission = Clone(value);
                },
                immediate: true,
                deep: true,
            },
        },
        created() {
            this.fetchPermissionConfig();
        },
        methods: {
            ...mapActions([
                'fetchDataPermission',
                'updateDataPermission',
            ]),

            async fetchPermissionConfig() {
                this.loading = true;
                await this.fetchDataPermission().catch((e) => {
                    console.log(e);
                });
                this.loading = false;
            },

            async submit() {
                // 库存和统计的如果指定成员不能一个人都不选择
                if (this.inventoryCostSelected && this.postDataPermission.inventory.goodsCostConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看库存药品物资成本成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.inventoryProfitSelected && this.postDataPermission.inventory.goodsProfitConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看库存药品物资毛利成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.inventoryCheckGoodsPriceSelected && this.postDataPermission.inventory.checkGoodsPrice.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看领用、调拨成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.statisticsCostSelected && this.postDataPermission.statistics.goodsCostConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看报表药品物资成本成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.statisticsProfitSelected && this.postDataPermission.statistics.goodsProfitConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看报表药品物资毛利成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.statisticsPatientMobileSelected && this.postDataPermission.statistics.patientMobileConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看报表查看患者手机号成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.inventoryGoodsArchivesSelected && this.postDataPermission.inventory.goodsArchivesConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择新建/编辑药品档案成员',
                        type: 'info',
                    });
                    return false;
                }
                if (this.inventoryGoodsCreateArchivesSelected && this.postDataPermission.inventory.goodsCreateArchivesConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择新建商品档案成员',
                        type: 'info',
                    });
                    return false;
                }
                if (this.inventoryGoodsModifyArchivesSelected && this.postDataPermission.inventory.goodsModifyArchivesConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择编辑商品档案成员',
                        type: 'info',
                    });
                    return false;
                }
                if (this.inventoryGoodsDeleteArchivesSelected && this.postDataPermission.inventory.goodsDeleteArchivesConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择删除商品档案成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.inventoryGoodsAdjustPriceSelected && this.postDataPermission.inventory.goodsAdjustPriceConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择定价/调价成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.retailProfitSelected && this.postDataPermission.retail.profitConfig.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看零售毛利率成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.retailPatientMobileSelected && this.postDataPermission.cashier.patientMobile.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看零售会员手机号成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmPatientMobileSelected && this.postDataPermission.crm.patientMobile.employees.length === 0) {
                    this.$Toast({
                        message: '请选择查看会员手机号成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.crmMemberProfitRateSelected && this.postDataPermission.crm.patientProfit?.employees?.length === 0) {
                    this.$Toast({
                        message: '请选择会员模块查看会员毛利率成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.retailViewCostPriceSelected && this.postDataPermission.retail.goodsCostPrice?.employees?.length === 0) {
                    this.$Toast({
                        message: '请选择零售查看进价/毛利率成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.retailPatientBalanceSelected && this.postDataPermission.retail.patientBalanceOpt?.employees?.length === 0) {
                    this.$Toast({
                        message: '请选择零售操作会员余额充值/退款成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.retailPatientPointsSelected && this.postDataPermission.retail.patientPointsOpt?.employees?.length === 0) {
                    this.$Toast({
                        message: '请选择零售操作会员积分发放/抵扣成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.retailPatientCouponSelected && this.postDataPermission.retail.patientCouponOpt?.employees?.length === 0) {
                    this.$Toast({
                        message: '请选择零售操作会员优惠券发放/作废成员',
                        type: 'info',
                    });
                    return false;
                }

                if (this.retailMemberProfitRateSelected && this.postDataPermission.retail.patientProfit?.employees?.length === 0) {
                    this.$Toast({
                        message: '请选择零售模块查看会员毛利率成员',
                        type: 'info',
                    });
                    return false;
                }

                if (!this.postDataPermission.retail.profitConfig) {
                    delete this.postDataPermission.retail;
                }

                try {
                    this.loading = true;
                    await this.updateDataPermission(this.postDataPermission);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                } catch (e) {
                    this.$Toast({
                        message: '保存失败',
                        type: 'error',
                    });
                } finally {
                    this.loading = false;
                }
            },
            /*
             * @param {number} type
             * 1: 库存-查看药品物资成本-指定成员可查看
             * 2: 库存-查看药品物资毛利-指定成员可查看
             * 3: 统计-查看药品物资成本-指定成员可查看
             * 4: 统计-查看药品物资毛利-指定成员可查看
             * */
            openMemberSettingDialog(type) {
                this.showMembersSettingDialog = true;
                this.membersSettingType = type;
            },
            onCancel() {
                this.showMembersSettingDialog = false;
                this.membersSettingType = '';
            },
            onConfirm(members) {
                switch (this.membersSettingType) {
                    case 'inventoryCost':
                        // this.inventoryCostMembers = members;
                        this.postDataPermission.inventory.goodsCostConfig.employees = members;
                        break;
                    case 'inventoryProfit':
                        // this.inventoryProfitMembers = members;
                        this.postDataPermission.inventory.goodsProfitConfig.employees = members;
                        break;
                    case 'statisticsCost':
                        // this.statisticsCostMembers = members;
                        this.postDataPermission.statistics.goodsCostConfig.employees = members;
                        break;
                    case 'statisticsProfit':
                        // this.statisticsProfitMembers = members;
                        this.postDataPermission.statistics.goodsProfitConfig.employees = members;
                        break;
                    case 'statisticsPatientMobile':
                        // this.statisticsProfitMembers = members;
                        this.postDataPermission.statistics.patientMobileConfig.employees = members;
                        break;
                    case 'inventoryCheckGoodsPrice':
                        this.postDataPermission.inventory.checkGoodsPrice.employees = members;
                        this.postDataPermission.inventory.damageGoodsPrice.employees = members;
                        this.postDataPermission.inventory.obtainGoodsPrice.employees = members;
                        this.postDataPermission.inventory.transGoodsPrice.employees = members;
                        break;
                    case 'inventoryGoodsArchives':
                        this.postDataPermission.inventory.goodsArchivesConfig.employees = members;
                        break;
                    case 'inventoryGoodsCreateArchives':
                        this.postDataPermission.inventory.goodsCreateArchivesConfig.employees = members;
                        break;
                    case 'inventoryGoodsModifyArchives':
                        this.postDataPermission.inventory.goodsModifyArchivesConfig.employees = members;
                        break;
                    case 'inventoryGoodsDeleteArchives':
                        this.postDataPermission.inventory.goodsDeleteArchivesConfig.employees = members;
                        break;
                    case 'inventoryGoodsAdjustPrice':
                        this.postDataPermission.inventory.goodsAdjustPriceConfig.employees = members;
                        break;
                    case 'mallPrice':
                        this.postDataPermission.mall.goodsPriceConfig.employees = members;
                        break;
                    case 'mallPurchase':
                        this.postDataPermission.mall.goodsPurchaseConfig.employees = members;
                        break;
                    case 'retailProfit':
                        this.postDataPermission.retail.profitConfig.employees = members;
                        break;
                    case 'retailPatientMobile':
                        this.postDataPermission.cashier.patientMobile.employees = members;
                        break;
                    case 'retailViewCostPrice':
                        this.postDataPermission.retail.goodsCostPrice.employees = members;
                        break;
                    case 'retailPatientBalance':
                        this.postDataPermission.retail.patientBalanceOpt.employees = members;
                        break;
                    case 'retailPatientPoints':
                        this.postDataPermission.retail.patientPointsOpt.employees = members;
                        break;
                    case 'retailPatientCoupon':
                        this.postDataPermission.retail.patientCouponOpt.employees = members;
                        break;
                    case 'retailMemberProfitRate':
                        this.postDataPermission.retail.patientProfit.employees = members;
                        break;
                    case 'crmPatientMobile':
                        this.postDataPermission.crm.patientMobile.employees = members;
                        break;
                    case 'crmMemberProfitRate':
                        this.postDataPermission.crm.patientProfit.employees = members;
                        break;
                    default:
                        break;
                }
                this.onCancel();
            },

            handleInventoryViewGoodsPriceChange(v) {
                // 后台的权限根据模块分开，在这里选代表全选，所有领用、调拨、盘点、报损都要赋值
                this.postDataPermission.inventory.checkGoodsPrice.value = v;
                this.postDataPermission.inventory.damageGoodsPrice.value = v;
                this.postDataPermission.inventory.obtainGoodsPrice.value = v;
                this.postDataPermission.inventory.transGoodsPrice.value = v;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import "src/styles/theme.scss";
@import "./_index.scss";
</style>
