<template>
    <biz-fill-remain-height class="chain-sub-setting">
        <template #header>
            <abc-manage-tabs
                :option="tabsOption"
                @change="handleTabsChange"
            >
            </abc-manage-tabs>
        </template>

        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';
    import { PharmacySettingsRouterNameKeys } from '@/views-pharmacy/settings/core/routes';

    export default {
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        inject: ['$abcPage'],
        computed: {
            ...mapGetters([
                'currentClinic',
                'clinics',
                'userInfo',
                'undoReviewCount',
                'unUploadOrganCertCount',
                'isChainAdmin',
                'isChain',
            ]),

            tabsOption() {
                const options = [
                    {
                        label: '门店管理',
                        value: PharmacySettingsRouterNameKeys.chainSubInfo,
                    },
                ].filter((item) => !item.isHidden);

                return options;
            },
        },
        methods: {
            handleTabsChange(val) {
                this.$router.push({
                    name: val,
                });
            },
        },
    };
</script>
