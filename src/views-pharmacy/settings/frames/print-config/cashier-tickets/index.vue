<template>
    <biz-setting-layout>
        <biz-setting-content v-abc-loading="loading">
            <biz-setting-form :label-width="90" divider-full-screen>
                <biz-setting-form-header>
                    <ticket-tab></ticket-tab>
                </biz-setting-form-header>

                <abc-form ref="printForm" item-no-margin>
                    <biz-setting-form-group title="前记">
                        <biz-setting-form-item label="Logo设置">
                            <abc-radio-group v-model="postData.titleStyle">
                                <abc-flex vertical gap="12">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        不打印
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        打印自定义Logo
                                    </abc-radio>
                                    <setting-set-logo
                                        v-if="postData.titleStyle === 1"
                                        v-model="postData.titleStyleCustomLogo"
                                        file-path="basic"
                                    ></setting-set-logo>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="抬头名称" label-line-height-size="medium">
                            <abc-form-item required :validate-event="validateName">
                                <title-setting v-model="postData.title" :max-length="60"></title-setting>
                            </abc-form-item>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="抬头信息">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox
                                    v-model="postData.clinicInfo.address"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    机构地址
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.clinicInfo.mobile"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    机构电话
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="购药信息">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox v-model="postData.patientInfo.pharmacistName" type="number" class="checkbox-no-margin">
                                    药师姓名
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.patientInfo.pharmacistCode" type="number" class="checkbox-no-margin">
                                    药师编码
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.patientInfo.sellerName" type="number" class="checkbox-no-margin">
                                    销售员
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>
                    </biz-setting-form-group>


                    <biz-setting-form-group title="正文-收费项目">
                        <biz-setting-form-item label="收费项目">
                            <abc-radio-group v-model="postData.feeInfo.chargeItem">
                                <abc-flex gap="12">
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        打印
                                    </abc-radio>
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        不打印
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.feeInfo.chargeItem" label="项目序号">
                            <abc-radio-group v-model="postData.feeInfo.itemNo">
                                <abc-flex gap="12">
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        打印
                                    </abc-radio>
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        不打印
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.feeInfo.chargeItem" label="名称超长处理">
                            <abc-radio-group v-model="postData.feeInfo.autoChangeLine">
                                <abc-flex gap="12">
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        超长后换行
                                    </abc-radio>
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        超长后截断
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.feeInfo.chargeItem && isNewPharmacyCashierVersion" label="配方饮片打印">
                            <abc-radio-group v-model="postData.feeInfo.prescriptionType">
                                <abc-flex gap="12">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        按单味打印明细
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        按处方打印汇总
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.feeInfo.chargeItem" label="项目明细">
                            <abc-table
                                :render-config="printItemInfoTableRenderConfig"
                                :data-list.sync="itemDetail"
                                type="excel"
                                cell-size="small"
                                style="width: 500px;"
                            >
                                <template #itemType="{ trData }">
                                    <abc-table-cell>
                                        {{ itemTypeConstants[trData.type] }}
                                    </abc-table-cell>
                                </template>
                                <template #printInfo="{ trData }">
                                    <abc-select
                                        v-model="trData.selectItems"
                                        multiple
                                        width="100%"
                                        :inner-width="160"
                                        :max-tag="trData.maxTag"
                                        :input-style="{ height: '32px' }"
                                        @change="handlePrintInfoChange(trData)"
                                    >
                                        <abc-option
                                            v-for="option in trData.items"
                                            :key="option.key"
                                            :value="option.key"
                                            :label="printItemConstants[option.key]"
                                        ></abc-option>
                                    </abc-select>
                                </template>
                            </abc-table>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="正文-收银信息">
                        <biz-setting-form-item label="收银信息">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox v-model="postData.cashierInfo.totalFee" type="number" class="checkbox-no-margin">
                                    合计
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.cashierInfo.receivableFee" type="number" class="checkbox-no-margin">
                                    应收
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.cashierInfo.netIncomeFee" type="number" class="checkbox-no-margin">
                                    实收
                                </abc-checkbox>
                                <!--等收费支持后再再试-->
                                <!--<abc-checkbox v-model="postData.cashierInfo.change" type="number" class="checkbox-no-margin">-->
                                <!--    现金找零-->
                                <!--</abc-checkbox>-->
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="优惠/议价">
                            <abc-radio-group v-model="postData.cashierInfo.singlePromotionFee">
                                <abc-flex gap="12">
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        不打印
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        仅打印汇总
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        打印到项目明细
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="会员信息">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox v-model="postData.memberInfo.level" type="number" class="checkbox-no-margin">
                                    会员等级
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.memberInfo.balance" type="number" class="checkbox-no-margin">
                                    会员余额
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.memberInfo.changePoints" type="number" class="checkbox-no-margin">
                                    本次增加积分
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.memberInfo.points" type="number" class="checkbox-no-margin">
                                    剩余积分
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.memberInfo.sex" type="number" class="checkbox-no-margin">
                                    会员性别
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.memberInfo.age" type="number" class="checkbox-no-margin">
                                    会员年龄
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="会员姓名">
                            <abc-radio-group v-model="postData.memberInfo.nameType">
                                <abc-flex gap="12">
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        完整展示
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        隐藏关键字
                                    </abc-radio>
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        不展示
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="会员手机号">
                            <abc-radio-group v-model="postData.memberInfo.mobileType">
                                <abc-flex gap="12">
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        完整展示
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        隐藏中间4位
                                    </abc-radio>
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        不展示
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="医保账户信息">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox v-model="postData.healthCardInfo.hospitalInfo" type="number" class="checkbox-no-margin">
                                    定点机构信息
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.healthCardInfo.cardInfo" type="number" class="checkbox-no-margin">
                                    持卡人信息
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.healthCardInfo.balanceInfo" type="number" class="checkbox-no-margin">
                                    余额信息
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="医保结算信息">
                            <abc-radio-group v-model="postData.healthCardInfo.settlementInfo">
                                <abc-flex gap="12">
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        打印全部
                                    </abc-radio>
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        不打印0元明细
                                    </abc-radio>
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        全部不打印
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="追溯码信息">
                            <abc-radio-group v-model="postData.clinicInfo.traceCodeQrCode">
                                <abc-flex vertical gap="12">
                                    <abc-radio :label="2" class="checkbox-no-margin">
                                        打印追溯码
                                    </abc-radio>
                                    <abc-radio :label="1" class="checkbox-no-margin">
                                        打印追溯码查询二维码
                                    </abc-radio>
                                    <abc-radio :label="0" class="checkbox-no-margin">
                                        不打印追溯码
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <biz-setting-form-group title="页尾">
                        <biz-setting-form-item label="页尾信息">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox v-model="postData.clinicInfo.chargeOperator" type="number" class="checkbox-no-margin">
                                    收费员
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.clinicInfo.chargeDate" type="number" class="checkbox-no-margin">
                                    收费时间
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.clinicInfo.printDate" type="number" class="checkbox-no-margin">
                                    打印时间
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.clinicInfo.retailRemark" type="number" class="checkbox-no-margin">
                                    零售备注
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.clinicInfo.remark" type="number" class="checkbox-no-margin">
                                    收费备注
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.clinicInfo.patientSign" type="number" class="checkbox-no-margin">
                                    顾客签字
                                </abc-checkbox>
                                <abc-checkbox v-model="postData.clinicInfo.replacementReminder" type="number" class="checkbox-no-margin">
                                    退换提醒
                                </abc-checkbox>
                                <abc-popover
                                    placement="top"
                                    :disabled="isOpenElectronicOrMedicalInvoice"
                                    trigger="hover"
                                    theme="yellow"
                                >
                                    <abc-checkbox
                                        slot="reference"
                                        v-model="postData.invoiceCode"
                                        :disabled="!isOpenElectronicOrMedicalInvoice"
                                        type="number"
                                        class="checkbox-no-margin"
                                    >
                                        电子发票二维码
                                    </abc-checkbox>
                                    <abc-flex :gap="6" align="center">
                                        <abc-icon icon="info" size="14" color="var(--abc-color-Y2)"></abc-icon>
                                        <abc-text>开通电子发票后才可设置</abc-text>
                                    </abc-flex>
                                </abc-popover>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="公告提示">
                            <abc-edit-div
                                v-model="postData.remark"
                                style="width: 486px; min-height: 52px; white-space: pre-wrap;"
                                placeholder="可输入公告、提示、说明、备注等信息"
                                :maxlength="100"
                            ></abc-edit-div>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </abc-form>
            </biz-setting-form>

            <template #footer>
                <biz-setting-footer>
                    <abc-space>
                        <abc-button :disabled="disabled" :loading="btnLoading" @click="submit">
                            保存
                        </abc-button>
                        <abc-button type="blank" @click="handleReset">
                            恢复默认
                        </abc-button>
                    </abc-space>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            <preview-layout
                :is-ticket="!isNewPharmacyCashierVersion"
                :is-esc-pos-ticket="!!isNewPharmacyCashierVersion"
                :class="{
                    'is-esc-pos-80-ticket-preview-layout': pageSizeType === 2,
                }"
            >
                <template v-if="isNewPharmacyCashierVersion" slot="previewTab">
                    <abc-flex vertical gap="large" align="center">
                        <abc-tabs-v2
                            :value="pageSizeType"
                            :option="pageSizeOptions"
                            size="middle"
                            disable-indicator
                            :border="false"
                            @change="changeTab"
                        ></abc-tabs-v2>
                        <div class="preview-tab-item preview-tab-item__active">
                            收费小票
                        </div>
                    </abc-flex>
                </template>
                <div slot="previewHtml" ref="previewMountPoint"></div>
            </preview-layout>
        </biz-setting-sidebar>

        <fee-compose-info-dialog
            v-if="showFeeComposeInfoDialog"
            v-model="showFeeComposeInfoDialog"
            :data="postData.feeComposeDetail"
            @change="handleFeeComposeInfoChange"
        ></fee-compose-info-dialog>
    </biz-setting-layout>
</template>

<script>
    import PreviewLayout from 'views/settings/print-config/components/preview-layout.vue';

    import PrintAPI from 'api/print.js';
    import {
        BILL_CONFIG,
        PHARMACY_EXAMPLE_DATA,
    } from 'views/settings/print-config/tickets/cashier/example.js';
    import clone from 'utils/clone';
    import { isEqual } from 'utils/lodash';

    import { mapGetters } from 'vuex';
    import TicketPrint from 'views/settings/print-config/tickets/mixins';
    import MixinPrint from 'views/settings/print-config/medical-documents/mixin-print.js';
    import store from '@/store';
    import TitleSetting from 'views/settings/print-config/components/title-setting.vue';
    import ABCPrinterConfig from '@/printer/config';
    import {
        defaultConfig, itemTypeConstants, printItemConstants, printItemInfoTableRenderConfig,
    } from 'views/settings/print-config/tickets/cashier/constant';
    import FeeComposeInfoDialog from 'views/settings/print-config/tickets/cashier/fee-compose-info-dialog.vue';

    import {
        BizSettingForm,
        BizSettingFormGroup, BizSettingFormHeader,
        BizSettingFormItem,
    } from '@/components-composite/setting-form';

    import {
        BizSettingLayout,
        BizSettingSidebar,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout';
    import TicketTab from 'views/settings/print-config/components/ticket-tab/index.vue';
    import SettingSetLogo from 'views/settings/components/setting-set-logo/index.vue';
    import PrintManager from '@/printer/manager/print-manager';

    export default {
        name: 'PharmacyPrintCashier',
        components: {
            TicketTab,
            BizSettingFormHeader,
            FeeComposeInfoDialog,
            PreviewLayout,
            TitleSetting,
            BizSettingForm,
            BizSettingFormGroup,
            BizSettingFormItem,
            BizSettingLayout,
            BizSettingSidebar,
            BizSettingContent,
            BizSettingFooter,
            SettingSetLogo,
        },
        mixins: [TicketPrint, MixinPrint],
        beforeRouteLeave(to, from, next) {
            if (!this.disabled) {
                this.$confirm({
                    type: 'warn',
                    title: '你的修改内容还未保存，确定离开？',
                    onConfirm: () => {
                        next();
                    },
                });
            } else {
                next();
            }
        },
        data() {
            return {
                itemTypeConstants,
                printItemConstants,
                printItemInfoTableRenderConfig,
                printData: PHARMACY_EXAMPLE_DATA,
                config: BILL_CONFIG,
                postData: {},
                loading: false,
                btnLoading: false,
                isFirstPrint: true,
                printLoading: false,
                ticketConfig: clone(ABCPrinterConfig.ticket),
                showFeeComposeInfoDialog: false,
                cachePrintConfig: null,
                pageSizeType: 1,
                pageSizeOptions: [
                    {
                        value: 1, label: '58小票',
                    },
                    {
                        value: 2, label: '80小票',
                    },
                ],
            };
        },
        computed: {
            ...mapGetters([
                'isElectron',
                'currentClinic',
                'printHeaderConfig',
                'clinicBasicConfig',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
            ...mapGetters('viewDistribute', ['featureFeeCompose']),
            ...mapGetters('invoice', [
                'isOpenInvoice',
                'isOpenMedicalInvoice',
                'isOpenIsvDigitalInvoice',
                'isOpenNuonuoDigitalInvoice',
            ]),
            isOpenElectronicOrMedicalInvoice() {
                return !!(this.isOpenInvoice || this.isOpenMedicalInvoice || this.isOpenIsvDigitalInvoice || this.isOpenNuonuoDigitalInvoice);
            },
            // 默认配置对应的key
            defaultConfigKey() {
                return this.viewDistributeConfig.Settings.print.defaultConfigKey;
            },
            currentExampleData() {
                return PHARMACY_EXAMPLE_DATA;
            },
            disabled() {
                if (!this.cachePrintConfig) return true;
                return isEqual(this.cachePrintConfig, this.postData);
            },
            isNewPharmacyCashierVersion() {
                return PrintManager.getInstance().isNewPharmacyCashierVersion();
            },
            itemDetail() {
                return this.postData.feeInfo.itemDetail
                    .filter((item) => {
                        if (item.type === 'nonFormulatedPrescription') {
                            return this.isNewPharmacyCashierVersion;
                        }
                        return item;
                    })
                    .map((item) => {
                        if (item.type === 'chinese') {
                            return {
                                ...item,
                                items: item.items.filter((one) => (this.postData.feeInfo.prescriptionType ? one.key === 'singleGoodsCountPerPrescription' : one.key !== 'singleGoodsCountPerPrescription')),
                                selectItems: item.selectItems.filter((one) => (this.postData.feeInfo.prescriptionType ? one === 'singleGoodsCountPerPrescription' : one !== 'singleGoodsCountPerPrescription')),
                            };
                        }
                        return item;
                    });
            },
            previewPage() {
                return {
                    size: this.pageSizeType === 1 ? '热敏小票（58mm）' : '热敏小票（80mm）',
                    pageSizeReduce: {
                        'bottom': 4,
                        'left': 4,
                        'right': 4,
                        'top': 4,
                    },
                };
            },
        },
        created() {
            this.$store.dispatch('invoice/initInvoiceConfig');
            this._cashier = true;
            this.postData = this.createPostData();
            this.fetchData();
        },
        methods: {
            createPostData() {
                return clone(defaultConfig[this.defaultConfigKey]);
            },
            instanceGlobalConfigHandler(newValue) {
                const newInstanceGlobalConfig = clone(store.getters.printGlobalConfig);
                newInstanceGlobalConfig.cashier = newValue;
                return newInstanceGlobalConfig;
            },
            getCurrentTemplate() {
                return this.isNewPharmacyCashierVersion ?
                    window.AbcPackages.AbcTemplates.pharmacyCashierV2 :
                    window.AbcPackages.AbcTemplates.pharmacyCashier;
            },
            initItemDetail() {
                this.postData.feeInfo.itemDetail.forEach((item) => {
                    item.selectItems = item.items.reduce((arr, cur) => {
                        if (cur.value) arr.push(cur.key);
                        return arr;
                    }, []);
                    item.maxTag = item.type === 'productGoods' ? 5 : this.calcMaxTagNum(item.selectItems);
                });
            },
            async fetchData() {
                try {
                    this.loading = true;
                    const data = await this.$store.dispatch('fetchPrintCashierConfig');
                    this.postData = data.cashier;
                    this.initItemDetail();
                    if (!this.postData.title) {
                        this.postData.title = this.currentClinic.clinicName;
                    }
                    this.cachePrintConfig = clone(this.postData);
                    this.loading = false;
                } catch (e) {
                    this.loading = false;
                }
            },
            async submit() {
                this.btnLoading = true;
                const postData = {
                    cashier: {
                        ...this.postData,
                    },
                };
                try {
                    const { data } = await PrintAPI.updatePrintConfig('clinic', 'print.cashier', postData);
                    this.postData = data.cashier;
                    this.cachePrintConfig = clone(this.postData);
                    this.$store.commit('SET_PRINT_CASHIER_CONFIG', this.postData);

                    /**
                     * 如果选择了小票格式,边距默认为0
                     * 如果选择了A5格式,边距默认为4
                     */
                    const cacheTicketConfig = clone(this.ticketConfig);
                    const cashierCashierTicketConfig = cacheTicketConfig.find((item) => {
                        return item.key === 'cashier';
                    });
                    cashierCashierTicketConfig.pageSizeReduce = {
                        left: 0,
                        top: 0,
                        bottom: 0,
                        right: 0,
                    };
                    // 保存边距配置
                    ABCPrinterConfig.setPrintConfig({
                        ticket: cacheTicketConfig,
                    });

                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.btnLoading = false;
                } catch (e) {
                    this.btnLoading = false;
                }
            },
            handleReset() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '是否确认将当前全部设置恢复为系统默认设置？',
                    onConfirm: () => {
                        this.postData = this.createPostData();
                        this.initItemDetail();
                        this.postData.title = this.currentClinic.clinicName;
                    },
                });
            },
            handleFeeComposeInfoChange(feeComposeInfo) {
                this.postData.feeComposeDetail = clone(feeComposeInfo);
            },

            handlePrintInfoChange(trData) {
                this.postData.feeInfo.itemDetail.forEach((item) => {
                    if (item.type === trData.type) {
                        if (item.type === 'chinese') {
                            if (this.postData.feeInfo.prescriptionType) {
                                item.items = item.items.map((one) => {
                                    if (one.key === 'singleGoodsCountPerPrescription') {
                                        return {
                                            ...one,
                                            value: trData.selectItems.includes(one.key) ? 1 : 0,
                                        };
                                    }
                                    return one;
                                });
                            } else {
                                item.items = item.items.map((one) => {
                                    if (one.key === 'singleGoodsCountPerPrescription') {
                                        return one;
                                    }
                                    return {
                                        ...one,
                                        value: trData.selectItems.includes(one.key) ? 1 : 0,
                                    };
                                });
                            }

                        } else {
                            item.items = item.items.map((one) => ({
                                ...one,
                                value: trData.selectItems.includes(one.key) ? 1 : 0,
                            }));
                        }
                        item.selectItems = item.items.reduce((arr, cur) => {
                            if (cur.value) arr.push(cur.key);
                            return arr;
                        }, []);
                        if (item.type !== 'productGoods') {
                            item.maxTag = this.calcMaxTagNum(item.selectItems);
                        }
                    }
                });
            },
            calcMaxTagNum(selectItems = []) {
                const len = selectItems.length;
                const specList = ['mha', 'medicalFeeGrade', 'ownExpenseRatio'];
                const specNum = selectItems.filter((item) => specList.includes(item)).length;
                if (specNum === 0) return 5;
                if (specNum === 3 && len <= 5) return 3;
                return 4;
            },
            async changeTab(val) {
                this.pageSizeType = val;
                this.printInstance = null;
                await this.mountPrintInstance();
                await this.updateInstanceGlobalConfig(this.postData);
            },
        },
    };
</script>
