<template>
    <biz-fill-remain-height class="clinic-setting">
        <template #header>
            <abc-manage-tabs
                v-if="showTabs"
                :option="tabsOption"
                @change="handleTabsChange"
            >
            </abc-manage-tabs>
        </template>

        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';

    export default {
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        inject: ['$abcPage'],
        computed: {
            ...mapGetters([
                'currentClinic',
                'clinics',
                'userInfo',
                'undoReviewCount',
                'unUploadOrganCertCount',
                'isChainAdmin',
                'isChain',
            ]),

            tabsOption() {
                const options = [
                    {
                        label: this.isChainAdmin ? '连锁设置' : `${this.$app.institutionTypeWording}设置`,
                        value: 'baseinfo',
                    },
                    {
                        label: this.isChainAdmin ? '部门设置' : '科室设置',
                        value: 'departments',
                        isHidden: !this.isChainAdmin,
                    },
                    {
                        label: '成员管理',
                        value: 'employees',
                        noticeNumber: this.undoReviewCount,
                    },

                    {
                        label: '证照资质',
                        value: 'license',
                        isHidden: this.isChainAdmin,
                        tagOption: this.unUploadOrganCertCount ? {
                            variant: 'outline',
                            theme: 'warning',
                            shape: 'round',
                            size: 'mini',
                            text: `缺少${this.unUploadOrganCertCount}项`,
                        } : undefined,
                    },
                ].filter((item) => !item.isHidden);

                return options;
            },

            showTabs() {
                // 不需要tab的form层级
                const noTabsNames = [ 'departmentForm', 'employeeForm', 'employeeAdd'];
                return noTabsNames.indexOf(this.$route.name) === -1;
            },
        },
        methods: {
            handleTabsChange(val) {
                this.$router.push({
                    name: val,
                });
            },
        },
    };
</script>
