<template>
    <div>
        <abc-flex
            justify="space-between"
            align="center"
            class="file-form-item-wrapper"
        >
            <template v-if="licenseItem.url">
                <abc-flex
                    justify="space-between"
                    align="center"
                    class="license-item-with-content"
                >
                    <abc-flex :gap="12" flex="1" style="overflow: hidden;">
                        <div style="font-size: 0;">
                            <abc-image
                                :src="licenseItem.url"
                                :width="52"
                                :height="40"
                                @click.stop="showPreviewImage = true"
                            ></abc-image>
                        </div>

                        <div class="license-info-item-wrapper">
                            <div class="license-item-name">
                                {{ licenseRenderName }}
                            </div>
                            <div
                                class="license-item-expire-date"
                                :class="{
                                    'is-warn': checkIsWillNotEnough3Month(licenseItem.expiredAt),
                                    'is-expired': checkIsExpired(licenseItem.expiredAt)
                                }"
                            >
                                有效期至：{{ formatDate(licenseItem.expiredAt, 'YYYY-MM-DD') }}
                            </div>
                        </div>
                    </abc-flex>

                    <abc-space :size="0">
                        <abc-button
                            variant="text"
                            icon="n-upload-line"
                            @click="licenseDialogVis = true"
                        ></abc-button>

                        <delete-confirm @confirm="handleClearItem">
                            <abc-button
                                variant="text"
                                icon="n-delete-2-line"
                                theme="danger"
                            ></abc-button>
                        </delete-confirm>
                    </abc-space>
                </abc-flex>
            </template>

            <template v-else>
                <abc-flex align="center" class="license-item-with-empty" @click="handleAdd">
                    <abc-space>
                        <abc-icon icon="n-add-line-medium" class="add-icon"></abc-icon>
                        <span class="the-placeholder">
                            {{ licenseRenderName }}
                        </span>
                    </abc-space>
                </abc-flex>
            </template>
        </abc-flex>

        <abc-preview
            v-if="showPreviewImage"
            v-model="showPreviewImage"
            :lists="[{ url: licenseItem.url }]"
            :index="0"
        ></abc-preview>

        <write-license-info-dialog
            v-if="licenseDialogVis"
            v-model="licenseDialogVis"
            :license-item="licenseItem"
            :is-id-card-front="isIdCardFront"
            :is-id-card-backend="isIdCardBackend"
            v-bind="$attrs"
            v-on="$listeners"
        ></write-license-info-dialog>
    </div>
</template>

<script>
    import WriteLicenseInfoDialog from './write-license-info-dialog.vue';
    import {
        formatDate, nextMonth,
    } from '@abc/utils-date';
    import DeleteConfirm from '@/components/index/delete-confirm.vue';

    export default {
        name: 'FileFormItem',

        components: {
            DeleteConfirm,
            WriteLicenseInfoDialog,
        },

        props: {
            isIdCardFront: Boolean,

            isIdCardBackend: Boolean,

            licenseItem: {
                type: Object,
                default: () => ({}),
            },

            isRemove: Boolean,

            addIsNeedSelectType: Boolean,
        },

        data () {
            this.defaultLicenseItem = {
                code: '',
                expiredAt: '',
                name: '',
                type: '',
                url: '',
            };

            return {
                licenseDialogVis: false,
                showPreviewImage: false,
                curLicenseItem: {},
            };
        },

        computed: {
            licenseRenderName() {
                if (this.isIdCardFront) {
                    return '身份证人像面';
                }

                if (this.isIdCardBackend) {
                    return '身份证国徽面';
                }
                return this.licenseItem.name || '其他证照';
            },
        },

        methods: {
            handleClearItem() {
                if (this.isRemove) {
                    return this.$emit('remove');
                }
                this.$emit('update:licenseItem', {
                    ...this.defaultLicenseItem,
                    name: this.licenseItem.name,
                });
            },

            formatDate,

            handleAdd() {
                if (this.addIsNeedSelectType) {
                    return this.$emit('add');
                }
                this.licenseDialogVis = true;
            },
            getThreeMonthDay() {
                const threeMonthDays = nextMonth(new Date(nextMonth(new Date(nextMonth(new Date())))));

                return formatDate(threeMonthDays, 'YYYY-MM-DD 00:00:00');
            },
            checkIsWillNotEnough3Month(date) {
                if (!date) return false;
                const currentDate = formatDate(new Date(date), 'YYYY-MM-DD 23:59:59');
                const threeMonthDay = this.getThreeMonthDay();

                return new Date(currentDate).getTime() <= new Date(threeMonthDay).getTime();
            },

            checkIsExpired(date) {
                const currentDate = formatDate(new Date(date), 'YYYY-MM-DD 23:59:59');

                return new Date(currentDate).getTime() < Date.now();
            },

            getLicenseRenderName() {

            },
        },
    };
</script>

<style lang='scss'>
.file-form-item-wrapper {
    width: 400px;
    height: 48px;
    cursor: pointer;
    background: var(--abc-color-cp-white);
    border: 1px solid var(--abc-color-P7);
    border-radius: var(--abc-border-radius-small);

    &:hover {
        border-color: var(--abc-color-theme3);
    }

    .license-item-with-content {
        width: 100%;
        padding: 0 var(--abc-paddingLR-m) 0 var(--abc-paddingLR-s);

        .abc-image-wrapper {
            border-radius: var(--abc-border-radius-mini);
        }

        .license-info-item-wrapper {
            flex: 1;

            .license-item-name {
                white-space: nowrap;
            }

            .license-item-expire-date {
                font-size: 12px;
                line-height: 16px;
                color: var(--abc-color-T3);

                &.is-warn {
                    color: var(--abc-color-Y2);
                }

                &.is-expired {
                    color: var(--abc-color-R7);
                }
            }
        }
    }

    .license-item-with-empty {
        width: 100%;
        height: 100%;
        padding: 0 var(--abc-paddingLR-l);
        color: var(--abc-color-T3);
    }
}
</style>
