<template>
    <!--内容区域-->
    <abc-container
        has-left-container
        :left-container-max-width="240"
        :left-container-min-width="240"
        :is-support-center-scroll="false"
        class="abc-hospital-settings-container"
    >
        <!--左侧预约情况列表-->
        <abc-container-left>
            <side-bar :nav-lists="navLists" :icon-size="16"></side-bar>
        </abc-container-left>
        <!--右侧form区域-->
        <abc-container-center class="content-container settings-container">
            <router-view></router-view>
        </abc-container-center>
    </abc-container>
</template>

<script type="text/ecmascript-6">
    // 自定义组件
    import { mapGetters } from 'vuex';
    import * as core from '@/views-hospital/settings-hospital/core';
    import { createAbcPage } from '@/core/page/factory.js';
    import ModulePermission from 'views/permission/module-permission';
    import { PharmacySettingsRouterNameKeys } from '@/views-pharmacy/settings/core/routes.js';
    import {
        MODULE_ID_MAP,
    } from 'utils/constants';

    export default {
        name: 'PharmacySetting',
        components: {
            SideBar: () => import('views/layout/settings-sideBar/sidebar'),
        },
        mixins: [ModulePermission,createAbcPage(core)],
        computed: {
            ...mapGetters([
                'clinicConfig',
                'isChainSubStore',
                'isSingleStore',
                'isChainAdmin',
                'unUploadOrganCertCount',
            ]),
            hasOrganSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting)
                );
            },
            hasPriceAndTaxRateSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.priceAndTaxRateSetting)
                );
            },
            hasChargeSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.chargeSetting)
                );
            },
            hasApproveSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.approveSetting)
                );
            },
            hasWareHouseSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.wareHouseSetting)
                );
            },
            hasMemberSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.memberSetting)
                );
            },
            hasProductCenterSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.productCenterSetting)
                );
            },

            hasScheduleSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.scheduleSetting)
                );
            },

            hasPrintSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.printSetting)
                );
            },

            hasFieldSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.settingSub.fieldLayoutSetting)
                );
            },

            hasAggregatePaymentSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.aggregatePaymentSetting)
                );
            },

            hasDataPermissionSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.dataPermission)
                );
            },
            navLists() {
                const array = [
                    {
                        label: '机构设置',
                        name: PharmacySettingsRouterNameKeys.baseInfo,
                        path: 'clinic',
                        icon: 'n-profile-line',
                        showRedDot: true,
                        visible: this.hasOrganSetting,
                        count: this.isChainAdmin ? 0 : this.unUploadOrganCertCount,
                    },
                    {
                        label: '门店管理',
                        name: PharmacySettingsRouterNameKeys.chainSubInfo,
                        path: 'chain-sub-management',
                        icon: 's-store-line',
                        showRedDot: false,
                        visible: this.hasOrganSetting && (this.isChainAdmin),
                    },
                    {
                        label: '税率',
                        name: PharmacySettingsRouterNameKeys.priceTaxRat,
                        icon: 'n-currency-line',
                        path: 'pricetaxrat',
                        showRedDot: false,
                        visible: this.hasPriceAndTaxRateSetting && (this.isChainAdmin || this.isSingleStore),
                    },
                    {
                        label: '零售设置',
                        name: 'chargeset',
                        icon: 'n-commodity-outline',
                        path: 'chargeset',
                        showRedDot: false,
                        visible: this.hasChargeSetting,
                    },
                    {
                        label: '审批设置',
                        name: PharmacySettingsRouterNameKeys.approve,
                        path: 'approve/base',
                        visible: this.hasApproveSetting,
                        icon: 'n-seal-line',
                    },
                    {
                        label: '库存设置',
                        name: PharmacySettingsRouterNameKeys.wareHouseSet,
                        path: 'ware-house-set',
                        visible: this.hasWareHouseSetting,
                        icon: 'n-inbox-outline',
                    },

                    {
                        label: '会员设置',
                        name: 'patient-tag-setting',
                        icon: 'n-vip-1-line',
                        path: 'patient-tag-setting',
                        showRedDot: false,
                        visible: this.hasMemberSetting && (this.isChainAdmin || this.isSingleStore),
                    },
                    {
                        label: '字段设置',
                        name: 'FieldLayout',
                        path: 'field-layout',
                        icon: 's-layout-line',
                        showRedDot: false,
                        visible: this.hasFieldSetting,
                    },
                    {
                        label: '打印设置',
                        name: 'print',
                        path: 'print',
                        icon: 'n-print-line',
                        showRedDot: false,
                        visible: this.hasPrintSetting,
                    },

                    {
                        label: '监管上报',
                        path: 'regulatory',
                        name: 'regulatory',
                        icon: 's-reporting-line',
                        showRedDot: false,
                        visible: this.hasAdminModule && (this.isChainSubStore || this.isSingleStore) && this.$abcRegulatory?.config?.isSupportRegulatory === true,
                    },

                    {
                        label: '信息安全',
                        name: 'datapermission',
                        icon: 's-safety-line1',
                        path: 'datapermission',
                        showRedDot: false,
                        visible: this.hasDataPermissionSetting,
                    },

                    {
                        label: '聚合支付',
                        path: 'aggregate-payment',
                        name: 'aggregate-payment',
                        icon: 'n-wallet-line',
                        showRedDot: false,
                        visible: this.hasAggregatePaymentSetting,
                    },

                    {
                        label: '药诊互通',
                        name: 'pharmacyLinkClinic',
                        icon: 's-drug-diagnosis-intercommunication-line',
                        path: 'pharmacy-link-clinic',
                        showRedDot: false,
                        visible: this.hasAdminModule,
                    },

                    {
                        label: '产品中心',
                        name: 'product-center',
                        path: 'product-center',
                        icon: 's-logo-a-color',
                        iconActive: 's-logo-a-fill',
                        visible: this.hasProductCenterSetting,
                        visibleOnExpired: true,
                    },
                ];
                let menuItems;
                if (this.$app.isExpired) {
                    menuItems = array.filter((item) => item.visibleOnExpired);
                } else {
                    menuItems = array.filter((item) => item.visible);
                }
                return {
                    basicNavLists: menuItems,
                };
            },
        },
        created() {
            this.$store.dispatch('fetchChainSubClinics');
        },
    };
</script>

<style lang="scss">
    @import './_index.scss';
</style>


