import {
    MODULE_ID_MAP,
} from 'utils/constants';
import { RouterScope } from 'utils/constants.js';
import Index from '../index.vue';
// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

// 外部跳转都应该用该字段
export const PharmacySettingsRouterNameKeys = {
    index: '@PharmacySettings',
    baseInfo: 'baseinfo',
    chainSubInfo: 'chainSubInfo',
    approve: '@settings/approve/base',
    schedule: 'registered',
    priceTaxRat: 'pricetaxrat',
    wareHouseSet: 'ware-house-set',
    cooperationClinic: 'cooperation-clinic',
    pharmacyLinkClinicIntroduce: 'pharmacy-link-clinic-introduce',
};

export default {
    path: 'settings',
    name: PharmacySettingsRouterNameKeys.index,
    component: Index,
    meta: {
        name: '设置',
        needAuth: true,
        moduleId: MODULE_ID_MAP.bizPharmacySetting,
        pageAsyncClass: PageAsync,
        icon: 'n-settings-fill',
        selectedIcon: 'n-settings-fill',
    },
    redirect: {
        name: PharmacySettingsRouterNameKeys.baseInfo,
    },
    children: [
        // 机构设置
        {
            path: 'clinic',
            component: () => import('src/views-pharmacy/settings/frames/clinic/index.vue'),
            name: 'clinic',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySetting,
                needAuth: true,
            },
            redirect: {
                name: PharmacySettingsRouterNameKeys.baseInfo,
            },
            children: [
                {
                    path: 'baseinfo',
                    component: () => import('src/views/settings/clinic/base-info/form.vue'),
                    name: PharmacySettingsRouterNameKeys.baseInfo,
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        name: '基本信息',
                        needAuth: true,
                    },
                },
                {
                    path: 'departments',
                    component: () => import('src/views/settings/clinic/department/table.vue'),
                    name: 'departments',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        name: '科室',
                        needAuth: true,
                    },
                },
                {
                    path: 'departments/:id',
                    component: () => import('src/views/settings/clinic/department/form.vue'),
                    name: 'departmentForm',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        needAuth: true,
                    },
                },
                {
                    path: 'employees',
                    component: () => import('src/views/settings/clinic/employee/table.vue'),
                    name: 'employees',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        needAuth: true,
                    },
                },
                {
                    path: 'employees-add',
                    component: () => import('src/views/settings/clinic/employee/form.vue'),
                    name: 'employeeAdd',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        needAuth: true,
                    },
                },
                {
                    path: 'employees/:id',
                    component: () => import('src/views/settings/clinic/employee/form.vue'),
                    name: 'employeeForm',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        needAuth: true,
                    },
                },
                {
                    path: 'equipments',
                    component: () => import('src/views/settings/clinic/medical-equipment/table.vue'),
                    name: 'medicalEquipments',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        needAuth: true,
                    },
                },
                {
                    path: 'equipments/:id',
                    component: () => import('src/views/settings/clinic/medical-equipment/form.vue'),
                    name: 'medicalEquipmentForm',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        needAuth: true,
                    },
                },
                {
                    path: 'license',
                    component: () => import('src/views-pharmacy/settings/frames/clinic/license-biz-pharmacy/index.vue'),
                    name: 'license',
                    meta: {
                        name: '证照资质',
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.organSetting,
                        needAuth: true,
                        scope:
                            RouterScope.CHAIN_ADMIN |
                            RouterScope.CHAIN_SUB |
                            RouterScope.SINGLE_STORE,
                    },
                },
            ],
        },
        {
            path: 'chain-sub-management',
            component: () => import('src/views-pharmacy/settings/frames/chain-sub-management/index.vue'),
            name: 'chain-sub-management',
            meta: {
                name: '门店管理',
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.storeSetting,
                needAuth: true,
                scope: RouterScope.CHAIN_ADMIN,
            },
            redirect: {
                name: PharmacySettingsRouterNameKeys.chainSubInfo,
            },
            children: [
                {
                    path: 'chain-sub-info',
                    component: () => import('src/views/settings/clinic/chain-sub-info/index.vue'),
                    name: PharmacySettingsRouterNameKeys.chainSubInfo,
                    meta: {
                        name: '门店管理',
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.storeSetting,
                        needAuth: true,
                        scope: RouterScope.CHAIN_ADMIN,
                    },
                },
            ],
        },
        // 定价和税率
        {
            path: 'pricetaxrat',
            component: () => import('src/views/settings/price-taxrat/index.vue'),
            name: 'pricetaxrat',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.priceAndTaxRateSetting,
                name: '定价和税率',
                needAuth: true,
            },
            children: [
                // {
                //     path: 'price',
                //     component: () => import('views/settings/price-taxrat/price.vue'),
                //     name: 'price-setting',
                //     meta: {
                //         moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.priceAndTaxRateSetting,
                //         name: '定价',
                //         needAuth: true,
                //         scope: RouterScope.CHAIN_ADMIN,
                //     },
                // },
                {
                    path: 'taxrat',
                    component: () => import('views/settings/price-taxrat/taxrat.vue'),
                    name: 'taxrat-setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.priceAndTaxRateSetting,
                        name: '税率',
                        needAuth: true,
                        scope: RouterScope.CHAIN_ADMIN | RouterScope.SINGLE_STORE,
                    },
                },
            ],
        },

        // 收费设置
        {
            path: 'chargeset',
            component: () => import('src/views/settings/charge-setting/index.vue'),
            hidden: true,
            name: 'chargeset',
            redirect: {
                name: '@chargeset/basic',
            },
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.chargeSetting,
                name: '零售设置',
                needAuth: true,
            },
            children: [
                {
                    path: 'basic',
                    name: '@chargeset/basic',
                    component: () => import('src/views/settings/charge-setting/charge-setting.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.chargeSetting,
                        scope: RouterScope.CHAIN_ADMIN | RouterScope.SINGLE_STORE | RouterScope.CHAIN_SUB,
                    },
                },
                {
                    path: 'invoice',
                    name: '@chargeset/invoice',
                    component: () => import('src/views/settings/charge-setting/invoice-setting/index.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.chargeSetting,
                        scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
                    },
                },
            ],
        },

        // 会员设置
        {
            path: 'patient-tag-setting',
            component: () => import('views/settings/patient-tag-setting/index'),
            name: 'patient-tag-setting',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.memberSetting,
                name: '会员设置',
                needAuth: true,
            },
            redirect: {
                name: 'tag-setting',
            },
            children: [
                {
                    path: 'tag-setting',
                    name: 'tag-setting',
                    component: () => import('views/settings/patient-tag-setting/tag-setting'),
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.memberSetting,
                        name: '会员标签',
                        needAuth: true,
                        scope: RouterScope.CHAIN_ADMIN | RouterScope.SINGLE_STORE,
                    },
                },
            ],
        },
        {
            path: 'field-layout',
            component: () => import('views/settings/field-layout/index.vue'),
            name: 'FieldLayout',
            meta: {
                name: '字段设置',
                moduleId: MODULE_ID_MAP.settingSub.fieldLayoutSetting,
                needAuth: true,
            },
            redirect: {
                name: 'FieldLayoutPatientCreate',
            },
            children: [
                {
                    path: 'field-layout-patient-create',
                    name: 'FieldLayoutPatientCreate',
                    component: () => import('views/settings/field-layout/patient-create/index.vue'),
                    meta: {
                        name: '会员建档',
                        needAuth: true,
                    },
                },
            ],
        },
        // 打印设置
        {
            path: 'print',
            component: () => import('src/views/settings/print-config/index.vue'),
            name: 'print',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.printSetting,
                name: '打印设置',
                // scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
            },
            // redirect: {
            //     name: 'printTickets',
            // },
            children: [
                {
                    path: 'tickets',
                    name: 'printTickets',
                    component: () => import('src/views/settings/print-config/tickets/index.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.printSetting,
                        name: '小票',
                        scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
                    },
                    redirect: {
                        name: 'printTicketsCashier',
                    },
                    children: [
                        {
                            path: 'cashier',
                            name: 'printTicketsCashier',
                            component: () => import('@/views-pharmacy/settings/frames/print-config/cashier-tickets/index.vue'),
                            meta: {
                                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.printSetting,
                                name: '小票-收费单',
                            },
                        },
                        {
                            path: 'statement',
                            name: 'printMedicalStatement',
                            component: () => import('src/views/settings/print-config/medical-bills/statement/index.vue'),
                            meta: {
                                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.printSetting,
                                name: '医疗票据-医保结算单',
                            },
                        },
                    ],
                },
                {
                    path: 'price-tag',
                    name: 'priceTag',
                    component: () => import('src/views/settings/print-config/price-tag/index.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.printSetting,
                        name: '价签',
                    },
                },
            ],
        },

        // 聚合支付
        {
            path: 'aggregate-payment',
            component: () => import('src/views/settings/aggregate-payment/index'),
            name: 'aggregate-payment',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.aggregatePaymentSetting,
                name: '聚合支付',
                needAuth: true,
            },
            children: [
                {
                    path: 'aggregate-payment-content',
                    component: () => import('views/settings/aggregate-payment/components/aggregatePaymentContent.vue'),
                    name: 'aggregate-payment-content',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.aggregatePaymentSetting,
                        name: '聚合支付',
                        needAuth: true,
                    },
                },
            ],
        },

        //药诊互通
        {
            path: 'pharmacy-link-clinic',
            component: () => import('src/views-pharmacy/settings/frames/pharmacy-link-clinic/index.vue'),
            name: 'pharmacyLinkClinic',
            meta: {
                moduleId: MODULE_ID_MAP.setting,
                name: '药诊互通',
                needAuth: true,
            },
            redirect: {
                name: 'cooperation',
            },
            children: [
                {
                    path: 'cooperation',
                    component: () => import('src/views-pharmacy/settings/frames/pharmacy-link-clinic/cooperation.vue'),
                    name: 'cooperation-clinic',
                    meta: {
                        moduleId: MODULE_ID_MAP.setting,
                        name: '合作诊所',
                        needAuth: true,
                    },
                },
                {
                    path: 'introduce',
                    component: () => import('src/views-pharmacy/settings/frames/pharmacy-link-clinic/introduce.vue'),
                    name: 'pharmacy-link-clinic-introduce',
                    props: {
                        isAlwaysDisplay: true,
                        hideTitle: true,
                    },
                    meta: {
                        name: '服务介绍',
                        needAuth: true,
                    },
                },
            ],
        },

        // 产品中心
        {
            path: 'product-center',
            component: () => import('views/settings/product-center/index.vue'),
            name: 'product-center',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.productCenterSetting,
            },
            children: [
                {
                    path: 'product-center-settings',
                    component: () => import('views/settings/product-center/product-center.vue'),
                    name: 'product-center-settings',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.productCenterSetting,
                        name: '产品中心',
                        needAuth: true,
                        visibleOnExpired: true,
                    },
                },
            ],
        },

        // 审批设置
        {
            path: 'approve',
            component: () => import('src/views/settings/approve-setting/index.vue'),
            name: 'approve',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.approveSetting,
                needAuth: true,
            },
            redirect: {
                name: PharmacySettingsRouterNameKeys.approve,
            },
            children: [
                {
                    path: 'base',
                    component: () => import('src/views/settings/approve-setting/approve.vue'),
                    name: PharmacySettingsRouterNameKeys.approve,
                    meta: {
                        name: '审批管理',
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.approveSetting,
                        needAuth: true,
                    },
                },
            ],
        },

        // 库存设置
        {
            path: 'ware-house-set',
            component: () => import('views/settings/ware-house-setting/index'),
            name: 'ware-house-set',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.wareHouseSetting,
                name: '库存设置',
                needAuth: true,
            },
            children: [
                {
                    path: 'warning-procurement',
                    name: 'warningProcurement',
                    component: () => import('views/settings/ware-house-setting/warning-procurement'),
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.wareHouseSetting,
                        name: '预警与采购设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'tags-management',
                    name: 'tagsManagement',
                    component: () => import('views/settings/goods-tag-setting/tag-setting.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.wareHouseSetting,
                        name: '标签管理',
                        needAuth: true,
                    },
                },
            ],
        },

        // 数据权限
        {
            path: 'datapermission',
            component: () => import('views/settings/data-permission/index.vue'),
            name: 'datapermission',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.dataPermission,
                name: '数据权限',
                needAuth: true,
            },
            children: [
                {
                    path: 'datapermissionsettings',
                    component: () => import('src/views-pharmacy/settings/frames/data-permission/index.vue'),
                    name: 'datapermissionsettings',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.dataPermission,
                        name: '数据权限',
                        needAuth: true,
                    },
                },
                {
                    path: 'safeloginsettings',
                    component: () => import('src/views/settings/data-permission/safe-login.vue'),
                    name: 'safeloginsettings',
                    meta: {
                        moduleId: MODULE_ID_MAP.bizPharmacySettingSubModule.dataPermission,
                        name: '安全登录',
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'product-center-renewal',
            component: () => import('views/settings/product-center/order.vue'),
            name: 'product-center-renewal',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySetting,
                visibleOnExpired: true,
            },
        },

        {
            path: 'product-center-upgrade',
            component: () => import('views/settings/product-center/order.vue'),
            name: 'product-center-upgrade',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySetting,
                visibleOnExpired: true,
            },
        },

        {
            path: 'product-center-invoice',
            component: () => import('views/settings/product-center/invoice.vue'),
            name: 'product-center-invoice',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySetting,
                visibleOnExpired: true,
            },
        },

        {
            path: 'product-center-invoice-detail',
            component: () => import('views/settings/product-center/invoice-detail.vue'),
            name: 'product-center-invoice-detail',
            meta: {
                moduleId: MODULE_ID_MAP.bizPharmacySetting,
                visibleOnExpired: true,
            },
        },

    ],
};
