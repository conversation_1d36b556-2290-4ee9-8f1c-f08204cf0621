<template>
    <abc-checkbox-group v-model="currentSelectIds" style="height: 100%;">
        <abc-list
            :data-list="list"
            size="large"
            :create-key="createKey"
            :enable-virtual-list="enableVirtualList"
            :virtual-list-config="virtualListConfig"
            style="height: 100%;"
        >
            <template #prepend="{ item }">
                <div style="height: 30px;">
                    <abc-checkbox :label="item.goodsId" @change="handleSelectChange($event, item)">
                    </abc-checkbox>
                </div>
            </template>
            <template
                #default="{
                    item
                }"
            >
                <abc-flex
                    flex="1"
                    :vertical="true"
                    style="width: 100%;"
                    class="ellipsis"
                    @click="handleSelect(item)"
                >
                    <abc-flex align="center" justify="start" :gap="4">
                        <abc-text
                            class="ellipsis"
                            size="normal"
                        >
                            {{ item.medicineCadn || item.name || '' }}
                        </abc-text>
                        <abc-tooltip
                            v-if="showTooltip(item)"
                            placement="top"
                            content="不在供应商经营范围"
                        >
                            <abc-icon
                                style="overflow: unset;"
                                :size="16"
                                icon="s-alert-small-fill"
                                color="var(--abc-color-Y3)"
                            ></abc-icon>
                        </abc-tooltip>
                    </abc-flex>
                    <abc-text
                        style="width: 100%;"
                        class="ellipsis"
                        size="mini"
                        theme="gray-light"
                        :title="`${isChineseMedicine(item) ? `${ transGoodsClassificationName(item.cMSpec) } ${ item.extendSpec || '' }` : goodsSpec(item) }  ${ item.manufacturer}`"
                    >
                        <span> {{ isChineseMedicine(item) ? `${ transGoodsClassificationName(item.cMSpec) } ${ item.extendSpec || '' }` : goodsSpec(item) }}</span>
                        <span style="padding-left: 8px;">{{ `${ item.manufacturer}` }}</span>
                    </abc-text>
                </abc-flex>
            </template>
            <template #append="{ item }">
                <abc-flex :vertical="true" justify="flex-end">
                    <abc-title :bold="false" style="text-align: right;">
                        {{
                            item.dispStockGoodsCount
                        }}
                    </abc-title>
                    <abc-popover
                        ref="stockCountPopover"
                        trigger="hover"
                        :offset="10"
                        :arrow-offset="40"
                        placement="bottom-end"
                        theme="yellow"
                        :popper-style="{
                            padding: '0px', 'min-width': '177px', marginTop: '10px'
                        }"
                        @show="()=>{
                            currentHoverId = item.goodsId
                        }"
                    >
                        <abc-flex
                            v-if="showPurchasingCount(item)"
                            slot="reference"
                            align="center"
                            justify="flex-end"
                            :style="{
                                width: '72px',
                                height: '16px',
                            }"
                            :gap="4"
                            @click.prevent.stop=""
                        >
                            <abc-icon icon="n-truck-fill" :size="14" :color="$store.state.theme.style.Y3"></abc-icon>
                            <span style="font-size: 12px; color: var(--abc-color-Y1);">采购中</span>
                        </abc-flex>
                        <popover-stock-card
                            :row="item"
                            row-key="goodsId"
                            :current-hover-id="currentHoverId"
                            :fetch-params="{
                                goodsId: item.goodsId,
                            }"
                        ></popover-stock-card>
                    </abc-popover>
                </abc-flex>
            </template>
        </abc-list>
    </abc-checkbox-group>
</template>

<script>
    import {
        complexCount, goodsSpec, isChineseMedicine,
    } from '@/filters';
    import clone from 'utils/clone';
    import PopoverStockCard from './popover-stock-card.vue';
    import { mapGetters } from 'vuex';

    export default {
        name: 'StockWarnList',
        components: { PopoverStockCard },
        props: {
            list: {
                type: Array,
                default: () => [],
            },
            selectedIds: {
                type: Array,
                default: () => [],
            },
            // 是否需要提示
            needTooltip: {
                type: Boolean,
                default: false,
            },
            // 经营范围
            bizScopeList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                currentHoverId: '',
                virtualListConfig: {
                    rowHeight: 58,
                    bufferSize: 20,
                    bufferLoad: false,
                },
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            currentSelectIds: {
                get() {
                    return this.selectedIds;
                },
                set(ids) {
                    this.$emit('update:selectedIds', ids);
                    this.handleChange(ids);
                },
            },
            warnListMap() {
                return this.list.reduce((map, item) => {
                    map[item.goodsId] = item;
                    return map;
                }, {});
            },
            enableVirtualList() {
                return this.list.length > 100;
            },
        },
        methods: {
            isChineseMedicine,
            goodsSpec,
            createKey(item) {
                return item.goodsId || item.id || item.keyId;
            },
            showPurchasingCount(item) {
                const {
                    purchasingCount, claimingCount,
                } = item;
                if (purchasingCount) {
                    return purchasingCount.packageCount || purchasingCount.pieceCount;
                }
                if (claimingCount) {
                    return claimingCount.packageCount || claimingCount.pieceCount;
                }
                return false;
            },
            formatGoodsStock(goodsInfo, packageCount = 0, pieceCount = 0) {
                if (goodsInfo) {
                    let curGoods = clone(goodsInfo);
                    curGoods = Object.assign(curGoods, {
                        packageCount, pieceCount,
                    });
                    return complexCount(curGoods);
                }
                return '';
            },
            handleSelect(item) {
                const isExist = this.selectedIds.includes(item.goodsId);
                this.$emit('select', !isExist, item);
            },
            handleSelectChange(val,item) {
                this.$emit('select', val, item);
            },
            handleChange(ids) {
                this.$emit('change', ids.map((id) => {
                    return this.warnListMap[id];
                }));
            },
            showTooltip(item) {
                if (this.needTooltip && this.bizScopeList?.length && item?.businessScopeList?.length) {
                    const bizScopeIds = this.bizScopeList.map((scope) => scope.id);
                    const bizScopeParentIds = this.bizScopeList.map((scope) => scope.parentId);
                    return !item.businessScopeList.some((listItem) => bizScopeIds.includes(listItem.id) || bizScopeParentIds.includes(listItem.id));
                }
                return false;
            },
        },
    };
</script>

<style lang="scss" scoped>
@import "src/styles/abc-common.scss";

.stock-item {
    height: 48px;
    padding: 5px 12px 5px 8px;
    cursor: pointer;
    border-radius: var(--abc-border-radius-small);

    &:hover {
        background-color: $B4;
    }

    &:not(:first-child) {
        margin-top: 12px;
    }

    .desc-text {
        font-size: 12px;
        line-height: 16px;
        color: var(--abc-color-T3);

        .manufacturer {
            padding-left: 8px;
        }
    }
}
</style>
