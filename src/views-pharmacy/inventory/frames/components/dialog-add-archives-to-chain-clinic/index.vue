<template>
    <abc-dialog
        v-if="showDialog"
        ref="dialogRef"
        v-model="showDialog"
        v-bind="dialogProps"
        @open="onDialogOpen"
        @close="onDialogClose"
        @close-dialog="handleCancel"
    >
        <abc-form style="height: 100%;">
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-flex justify="space-between">
                        <abc-space>
                            <goods-auto-complete-cover-title
                                ref="goodsAutoCompleteRef"
                                class="entry-medicine back-focus-to-autocomplete"
                                placeholder="扫码/商品名/首字母/条码"
                                :focus-show="true"
                                :search.sync="searchKey"
                                show-last-supplier
                                :auto-focus-first="false"
                                :enable-local-search="false"
                                :enable-barcode-detector="enableBarcodeDetector"
                                :width="240"
                                :inorder-config="0"
                                show-empty
                                clearable
                                is-close-validate
                                :next-input-auto-focus="false"
                                :resident-sugguestions="enableBarcodeDetector"
                                @clear="clearSearch"
                                @enter="initOffset"
                                @searchGoods="searchGoods"
                                @selectGoods="selectGoods"
                            >
                                <abc-search-icon slot="prepend"></abc-search-icon>
                            </goods-auto-complete-cover-title>

                            <!--类型筛选-->
                            <biz-goods-type-cascader
                                v-model="currentSelectedTypes"
                                :goods-type-options="goodsAllTypes"
                                :cascader-config="{
                                    width: 100,
                                    panelMaxHeight: 274,
                                }"
                                @change="handleChangeGoodsType"
                            >
                            </biz-goods-type-cascader>

                            <!--                            <abc-checkbox-button-->
                            <!--                                v-model="fetchParams.isNotAdd"-->
                            <!--                                type="number"-->
                            <!--                                @change="initOffset"-->
                            <!--                            >-->
                            <!--                                未添加-->
                            <!--                            </abc-checkbox-button>-->
                        </abc-space>

                        <abc-dropdown
                            :min-width="100"
                            placement="bottom-end"
                            @change="handleCreateArchive"
                        >
                            <template #reference>
                                <abc-check-access>
                                    <abc-tooltip :disabled="!disabledPharmacyArchives" :content="contentText" placement="top">
                                        <div>
                                            <abc-button
                                                :disabled="disabledPharmacyArchives"
                                                icon="s-b-add-line-medium"
                                                theme="success"
                                                data-cy="inventory-stock-add-archives"
                                            >
                                                新建档案
                                            </abc-button>
                                        </div>
                                    </abc-tooltip>
                                </abc-check-access>
                            </template>

                            <abc-dropdown-item :value="GoodsTypeEnum.MEDICINE" label="新建药品" data-cy="inventory-stock-add-medicine">
                            </abc-dropdown-item>

                            <abc-dropdown-item :label="materialText" :value="GoodsTypeEnum.MATERIAL" data-cy="inventory-stock-add-materials">
                            </abc-dropdown-item>

                            <abc-dropdown-item :value="GoodsTypeEnum.GOODS" label="新建商品" data-cy="inventory-stock-add-goods">
                            </abc-dropdown-item>
                        </abc-dropdown>
                    </abc-flex>
                </abc-layout-header>
                <abc-layout-content @layout-mounted="handleMounted">
                    <abc-table
                        :loading="loading"
                        :data-list="tableData"
                        :render-config="renderTableHeader"
                        :need-selected="false"
                        :show-all-checkbox="false"
                        :tr-click-trigger-checked="true"
                        :disabled-item-func="disabledItemFunc"
                        :custom-tr-key="customTrKey"
                        :pagination="tablePagination"
                        @pageChange="handlePageChange"
                        @changeChecked="onChangeChecked"
                    >
                        <!--                        <ul slot="paginationTipsContent">-->
                        <!--                            <li>-->
                        <!--                                已选<span> {{ selectedCount }} </span>种-->
                        <!--                            </li>-->
                        <!--                        </ul>-->
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <template slot="footer">
            <abc-flex justify="flex-end">
                <abc-button
                    :loading="submitBtnLoading"
                    :disabled="!selectedCount"
                    :count="selectedCount"
                    @click="handleConfirm"
                >
                    {{ confirmText }}
                </abc-button>

                <abc-button
                    variant="ghost"
                    @click="handleCancel"
                >
                    {{ cancelText }}
                </abc-button>
            </abc-flex>
        </template>

        <add-goods-archives-dialog
            v-if="addMedicineDialogVisible"
            v-model="addMedicineDialogVisible"
            :type-id="currentTypeId"
            @successAdd="handleSuccessAdd"
        >
        </add-goods-archives-dialog>
    </abc-dialog>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import goodsApi from '@/api/goods';
    import GoodsAutoCompleteCoverTitle from 'views/inventory/common/goods-auto-complete-cover-title.vue';
    import { getUserLastTypeId } from '@/views-pharmacy/inventory/utils';
    import { GoodsTypeEnum } from '@abc/constants';
    const AddGoodsArchivesDialog = () => import('views/inventory/goods/archives/add.vue');
    import BizGoodsTypeCascader, { useGoodsType } from '@/components-composite/biz-goods-type-cascader';
    import usePagination from '@/hooks/abc-ui/use-table-pagination';
    import { debounce } from 'utils/lodash';
    import { useSelectAllManager } from '@/views-pharmacy/inventory/hooks/useSelectAllManager';
    import GoodsAPI from 'api/goods';

    export default {
        name: 'AddArchivesToChainClinic',
        components: {
            GoodsAutoCompleteCoverTitle,
            AddGoodsArchivesDialog,
            BizGoodsTypeCascader,
        },
        props: {
            visible: Boolean,
            title: String,
            cancelText: {
                type: String,
                default: '取消',
            },
            confirmText: {
                type: String,
                default: '添加',
            },
            onConfirm: Function,
            onCancel: Function,
        },
        setup(props) {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager(props.title);

            const {
                allTypesCoverUnspecified,
                fetchAllGoodsTypes,
            } = useGoodsType();

            const {
                pageParams,
                setPageSize,
                setPageTotal,
                changePageIndex,
            } = usePagination({
                pageSize: 10,
                pageIndex: 0,
                total: 0,
            });

            const {
                isAllChecked,
                submitArr,
                hasChecked,
                changeChecked,
                changeAllChecked,
                resetState,
            } = useSelectAllManager();

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                allTypesCoverUnspecified,
                fetchAllGoodsTypes,

                changePageIndex,
                setPageSize,
                setPageTotal,
                pageParams,

                isAllChecked,
                submitArr,
                hasChecked,
                changeChecked,
                changeAllChecked,
                resetState,
            };
        },
        data() {
            return {
                GoodsTypeEnum,
                showDialog: this.visible,
                currentTypeId: '',
                tableData: [],
                total: 0,
                searchKey: '',
                fetchParams: {
                    goodsId: '',
                    keyword: '',
                    isNotAdd: 0, // 默认只显示未添加的商品
                    type: [],
                    subType: [],
                    typeId: [],
                    customTypeId: [],
                },
                currentSelectedTypes: [],
                selectedGoodsItems: [],
                loading: false,
                submitBtnLoading: false,
                addMedicineDialogVisible: false,
            };
        },
        computed: {
            ...mapGetters(['isAdmin', 'isChainSubStore', 'isCanCreateGoodsArchivesInInventory']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            dialogProps() {
                return {
                    title: this.title || '从连锁商品库添加',
                    size: 'hugely',
                    responsive: true,
                    'append-to-body': true,
                    disabledKeyboard: this.disabledKeyboard,
                };
            },
            enableBarcodeDetector() {
                return !this.isDialogShowing;
            },
            isDialogShowing() {
                return this.addMedicineDialogVisible;
            },
            materialText() {
                return `新建${this.viewDistributeConfig.Inventory.materialText}`;
            },
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            defaultGoodsTypeMap() {
                return this.viewDistributeConfig.Inventory.defaultGoodsTypeMap;
            },
            supportGoodsTypeIdMap() {
                return this.viewDistributeConfig.Inventory.supportGoodsTypeIdMap;
            },
            disabledPharmacyArchives() {
                if (this.operateGoodsArchives) {
                    return !this.isCanCreateGoodsArchivesInInventory;
                }
                return !this.isAdmin;
            },
            contentText() {
                // 药店管家逻辑
                if (this.operateGoodsArchives) {
                    if (this.disabledPharmacyArchives) {
                        return this.isChainSubStore ? '请联系总部开启权限' : '无建档权限，可在 [设置-信息安全] 中设置';
                    }
                    return '';
                }

                // 诊所管家逻辑
                if (this.disabledPharmacyArchives) {
                    return this.isChainSubStore ? '请联系总部创建药品物资档案' : '无创建药品物资档案权限';
                }

                return '';
            },
            goodsAllTypes() {
                return this.allTypesCoverUnspecified.filter((item) => {
                    return this.supportGoodsTypeIdMap[+item.id];
                });
            },
            selectedCount() {
                return this.isAllChecked ? this.pageParams.count - this.submitArr.length : this.submitArr.length;
            },
            goodsIdList() {
                return this.submitArr.map((item) => item.goodsId);
            },
            renderTableHeader() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            label: '',
                            isCheckbox: true,
                            pinned: true,
                            style: {
                                flex: 'none',
                                width: '40px',
                                maxWidth: '',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'shortId',
                            label: '商品编码',
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                            },
                        },
                        {
                            key: 'displayName',
                            label: '商品名称',
                            style: {
                                width: '180px',
                                minWidth: '180px',
                            },
                        },
                        {
                            key: 'displaySpec',
                            label: '规格',
                            style: {
                                width: '150px',
                                minWidth: '150px',
                                maxWidth: '150px',
                            },
                        },
                        {
                            key: 'manufacturer',
                            label: '厂家',
                            style: {
                                width: '150px',
                                minWidth: '150px',
                                maxWidth: '150px',
                            },
                        },
                        {
                            key: 'dosageFormTypeName',
                            label: '剂型',
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                            },
                        },
                        {
                            key: 'goodsTypeName',
                            label: '类型',
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                            },
                        },
                        {
                            key: 'customTypeName',
                            label: '二级分类',
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                            },
                        },
                        {
                            key: 'otcTypeName',
                            label: '处方药/OTC',
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                            },
                        },
                        {
                            key: 'medcineNpmn',
                            label: '批准文号',
                            style: {
                                width: '150px',
                                minWidth: '150px',
                                maxWidth: '150px',
                            },
                        },
                        // {
                        //     key: 'barCode',
                        //     label: '条码',
                        //     style: {
                        //         width: '120px',
                        //         minWidth: '120px',
                        //         maxWidth: '120px',
                        //     },
                        // },
                    ],
                };
            },
            tablePagination() {
                return {
                    showTotalPage: true,
                    limit: this.pageParams.limit,
                    offset: this.pageParams.offset,
                    count: this.pageParams.total,
                };
            },
        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
                if (!val) {
                    this.onCancel && this.onCancel();
                }
            },
            searchKey(val) {
                if (!val) {
                    this.clearSearch();
                }
            },
        },
        created() {
            this._fetchData = debounce(this.fetchData, 500, true);
            this.fetchAllGoodsTypes(() => {
                return GoodsAPI.fetchGoodsClassificationV3({
                    queryType: 1,
                    needCustomType: 1,
                });
            });
        },
        methods: {
            onDialogOpen() {
                this.pushDialogName();
                this.$emit('open');
            },
            onDialogClose() {
                this.popDialogName();
                this.$emit('close');
            },
            async handleConfirm() {
                this.submitBtnLoading = true;
                try {
                    await goodsApi.batchAddGoodsArchiveToClinic({ goodsIdList: this.goodsIdList });

                    this.$Toast.success('商品添加成功');
                    if (typeof this.onConfirm === 'function') this.onConfirm(3);
                    this.showDialog = false;
                } catch (error) {
                    console.error('商品添加失败:', error);
                    this.$Toast.error(error.message || '商品添加失败');
                } finally {
                    this.submitBtnLoading = false;
                }
            },
            handleCancel() {
                // 业务回调
                if (typeof this.onCancel === 'function') this.onCancel();
                this.showDialog = false;
            },
            // 新建档案
            handleCreateArchive(goodsType) {
                // 获取用户上次建档类型
                const typeId = getUserLastTypeId(goodsType);
                this.currentTypeId = typeId || this.defaultGoodsTypeMap[goodsType];
                this.addMedicineDialogVisible = true;
            },
            customTrKey(row) {
                return row.goodsId || row.shortId || row.id;
            },
            disabledItemFunc(item) {
                return !!item.isAdd;
            },
            async fetchData() {
                this.loading = true;
                try {
                    const params = {
                        ...this.fetchParams,
                        // 使用 pageParams 中的分页参数
                        offset: this.pageParams.offset || 0,
                        limit: this.pageParams.limit || 10,
                    };

                    const { data } = await goodsApi.fetchChainStockGoodsList(params);
                    this.tableData = data.rows?.map((item) => {
                        return {
                            ...item,
                            checked: item.isAdd ? true : this.hasChecked(item.goodsId),
                        };
                    }) || [];
                    this.setPageTotal(data.totalCount || 0);
                    return data;
                } catch (error) {
                    console.error('获取总部档案列表失败:', error);
                    this.$message.error('获取总部档案列表失败');
                    this.tableData = [];
                    this.setPageTotal(0);
                    throw error;
                } finally {
                    this.loading = false;
                }
            },
            clearSearch() {
                this.fetchParams.keyword = '';
                this.fetchParams.goodsId = '';
                this.initOffset();
            },
            searchGoods(val = '') {
                this.fetchParams.keyword = val.trim();
                this.fetchParams.goodsId = '';
                this.initOffset();
            },
            selectGoods(goods) {
                if (!goods) return;

                this.searchKey = goods.displayName;
                this.fetchParams.keyword = '';
                this.fetchParams.goodsId = goods.goodsId || goods.id;
                this.initOffset();
            },
            initOffset() {
                this.changePageIndex(0);
                this._fetchData();
            },
            handleMounted(data) {
                const limit = data.paginationLimit || 10;
                this.setPageSize(limit);
                this.initOffset();
            },
            handleChangeGoodsType(typeIdList, customTypeIdList) {
                this.fetchParams.typeId = typeIdList;
                this.fetchParams.customTypeId = customTypeIdList;
                this.initOffset();
            },
            // 分页事件处理
            handlePageChange(page) {
                this.changePageIndex(page - 1);
                this._fetchData();
            },
            onChangeChecked(item) {
                console.log('checked item', item);
                this.changeChecked(item, 'goodsId');
            },
            handleSuccessAdd() {
                this.initOffset();
                this.addMedicineDialogVisible = false;
                this.$Toast.success('保存成功并提交首营');
            },
        },
    };
</script>
