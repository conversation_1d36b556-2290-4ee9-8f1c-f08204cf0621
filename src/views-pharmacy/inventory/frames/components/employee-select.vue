<template>
    <abc-select
        v-model="employeeId"
        :clearable="clearable"
        :with-search="withSearch"
        :width="width"
        :inner-width="innerWidth || width"
        :adaptive-width="adaptiveWidth"
        :max-height="maxHeight"
        :only-bottom-border="onlyBottomBorder"
        :placeholder="placeholder"
        :fetch-suggestions="fetchReceiver"
        :clicked-focus-input="clickedFocusInput"
        :shortcut-mode="shortcutMode"
        show-empty
        @change="$emit('change', $event)"
    >
        <abc-option
            v-for="it in myEmployeeList"
            :key="`${it.employeeId }`"
            :value="it.employeeId"
            :label="it.employeeName"
        ></abc-option>
    </abc-select>
</template>

<script>
    export default {
        name: 'EmployeeSelect',

        props: {
            value: String,
            placeholder: {
                type: String,
                default: '',
            },
            adaptiveWidth: {
                type: Boolean,
                default: false,
            },
            clearable: {
                type: Boolean,
                default: true,
            },
            width: {
                type: Number,
                default: 100,
            },
            innerWidth: {
                type: Number,
                default: 0,
            },
            maxHeight: {
                type: Number,
                default: 210,
            },
            employeeList: {
                type: Array,
                default: () => [],
            },
            withSearch: {
                type: Boolean,
                default: false,
            },
            onlyBottomBorder: {
                type: Boolean,
                default: false,
            },
            clickedFocusInput: {
                type: Boolean,
                default: false,
            },
            shortcutMode: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                myEmployeeList: this.employeeList,
            };
        },
        computed: {
            employeeId: {
                get() {
                    return this.value;
                },
                set(v) {
                    this.$emit('input', v);
                },
            },
        },
        watch: {
            employeeList: {
                handler(val) {
                    this.myEmployeeList = val || [];
                },
                deep: true,
            },
        },
        methods: {
            fetchReceiver(key = '') {
                this.myEmployeeList = this.employeeList?.filter((item) => {
                    return item.employeeName.includes(key.trim());
                });
            },
        },
    };
</script>
