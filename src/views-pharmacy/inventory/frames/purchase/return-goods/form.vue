<template>
    <frame-dialog
        v-if="showDialog"
        ref="frameDialog"
        v-model="showDialog"
        class="pharmacy-return-goods_dialog"
        :title="title"
        :show-title-append="true"
        :right-width="rightWidth"
        :before-close="closeDialog"
        :loading="pageLoading"
        :tag-config="tagConfig"
        :gsp-inst-id="gspInstId"
        @open="handleOpenDialog"
    >
        <abc-form
            ref="form"
            is-excel
            style="height: 100%;"
            item-no-margin
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <!--选择入库单-->
                    <abc-descriptions
                        v-if="isSelectGoodsIn"
                        :column="returnGoodsTable.column"
                        :label-width="100"
                        grid
                        size="large"
                        :need-input-style="false"
                        background
                    >
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_1"
                            content-class-name="ellipsis"
                            label="入库日期"
                            :content-style="{ padding: '0px' }"
                        >
                            <abc-form-item>
                                <abc-date-picker
                                    v-model="dateRange"
                                    type="daterange"
                                    size="medium"
                                    :picker-options="pickerOptions"
                                    :clearable="false"
                                    value-format="YYYY-MM-DD"
                                    @change="changeDate"
                                ></abc-date-picker>
                            </abc-form-item>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_2"
                            content-class-name="ellipsis"
                            label="供应商"
                            :content-style="{ padding: '0px' }"
                        >
                            <abc-form-item>
                                <abc-select
                                    v-model="fetchParams.supplierId"
                                    with-search
                                    :input-style="{ paddingRight: '26px' }"
                                    :fetch-suggestions="fetchSuggestions"
                                    size="large"
                                    @open="supplierListRetry"
                                    @change="changeSupplier"
                                >
                                    <abc-option :value="''" label="全部供应商"></abc-option>
                                    <abc-option
                                        v-for="it in currentSupplierList"
                                        :key="`${it.id }`"
                                        :value="it.id"
                                        :label="it.name"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_3"
                            content-class-name="ellipsis"
                            label="商品名称"
                            :content-style="{
                                padding: '0px',
                            }"
                        >
                            <abc-form-item>
                                <goods-auto-complete-cover-title
                                    placeholder="扫码/商品名称/首字母"
                                    :with-stock="false"
                                    :pharmacy-no="pharmacyNo"
                                    :search.sync="searchKey"
                                    :clear-search-key="false"
                                    size="large"
                                    enable-local-search
                                    has-right-border-radius
                                    :enable-barcode-detector="!showGoodsInitReturnDialog"
                                    @selectGoods="selectGoods"
                                >
                                    <div slot="append" class="search-icon" @click="clearSearch">
                                        <abc-icon v-if="searchKey" icon="cross_small"></abc-icon>
                                    </div>
                                </goods-auto-complete-cover-title>
                            </abc-form-item>
                        </abc-descriptions-item>
                    </abc-descriptions>
                    <abc-descriptions
                        v-else-if="isSelectReturnGoods"
                        :column="returnGoodsTable.column"
                        :label-width="106"
                        grid
                        size="large"
                        background
                        stretch-last-item
                    >
                        <!-- <abc-descriptions-item
                            :span="returnGoodsTable.span_1"
                            content-class-name="ellipsis"
                            label="采购订单"
                        >
                            <span v-abc-title="relationshipOrderNo"></span>
                        </abc-descriptions-item> -->
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_2"
                            content-class-name="ellipsis"
                            label="供应商"
                        >
                            <span v-abc-title="selectTableColumns.supplier"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_3"
                            content-class-name="ellipsis"
                            :label="'供应商销售员'"
                        >
                            <span v-abc-title="selectTableColumns.supplierSellerName"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_3"
                            content-class-name="ellipsis"
                            label="收货门店"
                        >
                            <span v-abc-title="selectTableColumns.clinicName"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_4"
                            content-class-name="ellipsis"
                            label="入库单号"
                        >
                            <span v-abc-title="titleOrderNo"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_5"
                            content-class-name="ellipsis"
                            label="入库人"
                        >
                            <span v-abc-title="selectTableColumns.user"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_6"
                            content-class-name="ellipsis"
                            label="入库日期"
                        >
                            <span v-abc-title="selectTableColumns.createdDate"></span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                    <abc-descriptions
                        v-else-if="isSelectConfirmGoods"
                        :column="returnGoodsTable.column"
                        :label-width="106"
                        grid
                        size="large"
                        background
                        stretch-last-item
                    >
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_1"
                            content-class-name="ellipsis"
                            label="供应商"
                        >
                            <span v-abc-title="createTableColumns.supplier"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_3"
                            content-class-name="ellipsis"
                            :label="'供应商销售员'"
                        >
                            <span v-abc-title="createTableColumns.supplierSellerName"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_2"
                            content-class-name="ellipsis"
                            label="退货机构"
                        >
                            <span v-abc-title="createTableColumns.clinicName"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_3"
                            content-class-name="ellipsis"
                            label="退货人"
                        >
                            <span v-abc-title="createTableColumns.user"></span>
                        </abc-descriptions-item>
                        <!-- <abc-descriptions-item
                            :span="returnGoodsTable.span_4"
                            content-class-name="ellipsis"
                            label="采购订单"
                        >
                            <span v-abc-title="relationshipOrderNo"></span>
                        </abc-descriptions-item> -->
                        <abc-descriptions-item
                            :span="returnGoodsTable.span_5"
                            content-class-name="ellipsis"
                            label="关联入库单"
                        >
                            <span v-abc-title="createTableColumns.orderNo"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            content-class-name="ellipsis"
                            :content-style="{ padding: '0px' }"
                            label="备注"
                        >
                            <abc-form-item>
                                <abc-input v-model="comment"></abc-input>
                            </abc-form-item>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-layout-header>
                <abc-layout-content :class="{ 'select-goods-in_table': isSelectGoodsIn }">
                    <div v-if="isSelectGoodsIn" v-abc-loading="loading" class="in-order-list">
                        <div v-if="!inOrders.length && !loading" class="no-in-order">
                            <abc-content-empty top="120px" size="small" value="没有相关入库单"></abc-content-empty>
                        </div>
                        <div
                            v-for="item in inOrders"
                            v-else
                            :key="item.id"
                            class="in-order-item-box"
                        >
                            <div class="in-order-item">
                                <div class="info">
                                    <h4>
                                        <abc-space :size="12">
                                            <abc-title>{{ item.orderNo }}</abc-title>
                                            <abc-p gray>
                                                {{ item.supplier || '' }}
                                            </abc-p>
                                            <abc-popover
                                                trigger="hover"
                                                class="inventory-popper-wrapper"
                                                placement="bottom-start"
                                                theme="yellow"
                                            >
                                                <abc-link slot="reference">
                                                    查看详情
                                                </abc-link>
                                                <div class="order-table-wrapper">
                                                    <div class="table-header" style="padding-right: 12px;">
                                                        <div
                                                            class="name"
                                                            :style="isGsp ? {
                                                                width: '190px',
                                                            } : {}"
                                                        >
                                                            {{ orderMainNameText }}
                                                        </div>
                                                        <div class="batch-no">
                                                            生产批号
                                                        </div>
                                                        <div class="date">
                                                            效期
                                                        </div>
                                                        <div v-if="!isGsp" class="package-price">
                                                            进价
                                                        </div>
                                                        <div class="count">
                                                            入库数量
                                                        </div>
                                                        <div v-if="!isGsp" class="return-count">
                                                            可退数量
                                                        </div>
                                                    </div>
                                                    <div class="table-body" style="overflow-y: scroll;">
                                                        <div
                                                            v-for="(inOrder, oIndex) in (handleList(item.list) || [])"
                                                            :key="oIndex"
                                                            class="table-tr"
                                                        >
                                                            <div
                                                                class="name ellipsis"
                                                                :style="isGsp ? {
                                                                    width: '190px',
                                                                } : {}"
                                                            >
                                                                {{ inOrder.goods | goodsFullName }}
                                                            </div>
                                                            <div class="batch-no ellipsis">
                                                                {{ inOrder.batchNo }}
                                                            </div>
                                                            <div class="date ellipsis">
                                                                {{ inOrder.expiryDate }}
                                                            </div>
                                                            <div v-if="!isGsp" class="package-price">
                                                                {{ paddingMoney(inOrder.useUnitCostPrice) }}/{{ inOrder.useUnit }}
                                                            </div>
                                                            <div class="count">
                                                                {{ inOrder.useCount }}{{ inOrder.useUnit }}
                                                            </div>
                                                            <div v-if="!isGsp" class="return-count">
                                                                {{ formatReturnLeft(inOrder) }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </abc-popover>
                                        </abc-space>
                                    </h4>
                                    <abc-title :bold="false" class="ellipsis">
                                        {{ transList(item.list || []) }}
                                    </abc-title>
                                </div>

                                <div class="btn">
                                    <abc-button width="64" :disabled="disabledBtn(item.list || [])" @click="isGsp ? selectGspOrder(item.id) : item.sourceType === 'goodsInit' ? handleSelect(item) : selectInOrder(item.id)">
                                        {{ isGsp ? '添加' : '选择' }}
                                    </abc-button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <abc-table
                        v-else-if="isSelectReturnGoods"
                        ref="isSelectReturnGoodsTable"
                        :data-list="order.list"
                        empty-size="small"
                        type="excel"
                        :scroll-load-config="scrollLoadConfig"
                        :disabled-item-func="disabledItemFunc"
                        :render-config="tableRenderConfig"
                        :need-selected="canEdit"
                        cell-size="large"
                        :custom-tr-key="customSelectTrKey"
                        :show-hover-tr-bg="true"
                        :tr-clickable="true"
                        :show-all-checkbox="order.list?.length > 1"
                        :custom-all-checked-func="customAllCheckedFunc"
                        @changeChecked="checkOne"
                        @changeAllChecked="checkAll"
                    >
                        <template #topHeader>
                            <goods-auto-complete-cover-title
                                ref="return-goods-auto-complete-search"
                                class="return-goods-auto-complete-search back-focus-to-autocomplete"
                                placeholder="扫码/商品名称/首字母"
                                :search.sync="returnGoodsSearchKey"
                                :focus-show="true"
                                size="medium"
                                :width="480"
                                enable-local-search
                                :custom-medicine-search="searchInReturnGoods"
                                :custom-error-handler="customErrorHandler"
                                @selectGoods="selectReturnGoods"
                                @input="handleReturnGoodsInput"
                            >
                                <abc-icon slot="prepend" icon="n-search-line-medium" color="var(--abc-color-T3)"></abc-icon>
                                <div slot="append" class="search-icon" @click="clearReturnGoodsSearch">
                                    <abc-icon v-if="returnGoodsSearchKey" icon="cross_small"></abc-icon>
                                </div>
                            </goods-auto-complete-cover-title>
                        </template>
                        <template #shortId="{ trData: row }">
                            <abc-table-cell v-if="row.goods" class="ellipsis">
                                <span>{{ row.goods.shortId }}</span>
                            </abc-table-cell>
                        </template>
                        <template #medicineName="{ trData: row }">
                            <display-name-cell v-if="row.goods" :goods="row.goods"></display-name-cell>
                        </template>
                        <template #count="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                {{ row | complexCount }}
                            </abc-table-cell>
                        </template>
                        <template #returnCount="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title.ellipsis="formatReturnLeft(row)"></span>
                            </abc-table-cell>
                        </template>
                        <!--进价-->
                        <template #costPrice="{ trData: row }">
                            <abc-table-cell
                                v-if="row.inItem"
                                v-abc-title.ellipsis="`${paddingMoney(row.inItem.useUnitCostPrice)}/${row.inItem.useUnit}`"
                            >
                                {{ `${paddingMoney(row.inItem.useUnitCostPrice)}/${row.inItem.useUnit}` }}
                            </abc-table-cell>
                            <abc-table-cell
                                v-else
                                v-abc-title.ellipsis="`${paddingMoney(row.useUnitCostPrice)}/${row.inOrderTransTotalCount ? row.receiveUnit : row.useUnit}`"
                            >
                                {{ `${paddingMoney(row.useUnitCostPrice)}/${row.inOrderTransTotalCount ? row.receiveUnit : row.useUnit}` }}
                            </abc-table-cell>
                        </template>

                        <!--退货供应商-->
                        <template #supplierId="{ trData: row }">
                            <abc-form-item :required="row.checked">
                                <abc-select
                                    v-model="row.supplierId"
                                    custom-class="supplierWrapper"
                                    with-search
                                    clearable
                                    placeholder="供应商"
                                    :width="150"
                                    inner-width="280px"
                                    placement="bottom-end"
                                    :fetch-suggestions="fetchSuggestions"
                                    :tabindex="-1"
                                    @open="supplierListRetry"
                                    @change="(val) => supplierChange(val, row)"
                                >
                                    <abc-option
                                        v-for="it in currentSupplierList"
                                        :key="`${it.id }`"
                                        :value="it.id"
                                        :label="it.name"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </template>

                        <!--金额-->
                        <template #useTotalCostPrice="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title="formatMoney(row.useTotalCostPrice, false)"></span>
                            </abc-table-cell>
                        </template>

                        <!--退货数量-->
                        <template #realReturnCount="{ trData: row }">
                            <abc-form-item
                                v-if="row.checked"
                                :required="!row.returnPackageCount && !row.returnPieceCount"
                                :validate-event="validateStock(row)"
                            >
                                <abc-space is-compact compact-block @click.stop>
                                    <abc-input
                                        v-if="!isChineseMedicine(row.goods)"
                                        v-model.number="row.returnPackageCount"
                                        v-abc-focus-selected
                                        :width="74"
                                        type="number"
                                        :config="getConfig(row.goods)"
                                        @enter="enterEvent"
                                        @click.stop
                                        @change="handleChangeUnitCount(row, row.goods)"
                                    >
                                        <div slot="appendInner">
                                            {{ row.goods.packageUnit }}
                                        </div>
                                    </abc-input>
                                    <abc-input
                                        v-if="!unitEqual(row.goods) || isChineseMedicine(row.goods)"
                                        v-model.number="row.returnPieceCount"
                                        v-abc-focus-selected
                                        :width="74"
                                        type="number"
                                        :config="getConfig(row.goods)"
                                        @enter="enterEvent"
                                        @click.stop
                                        @change="handleChangeUnitCount(row, row.goods)"
                                    >
                                        <div slot="appendInner">
                                            {{ row.goods.pieceUnit }}
                                        </div>
                                    </abc-input>
                                </abc-space>
                            </abc-form-item>
                            <div v-else class="full-flex is-disabled"></div>
                        </template>

                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item,
                            }"
                        >
                            <abc-flex v-if="item.checked" style="height: 100%;" @click.stop>
                                <traceable-code-cell
                                    v-model="item.traceableCodeList"
                                    :goods="item.goods"
                                    :goods-count="getUnitCount({
                                        packageCount: item.returnPackageCount,
                                        pieceCount: item.returnPieceCount
                                    }, item.goods, '退货数量')"
                                    :need-validate="validateCell"
                                    :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                                ></traceable-code-cell>
                            </abc-flex>
                        </template>
                        <template #footer>
                            <abc-space :size="4" style="margin-right: 12px; margin-left: auto;">
                                <abc-text theme="gray">
                                    品种
                                </abc-text>
                                <abc-text theme="black">
                                    {{ returnGoodsFooterKindCount }}
                                </abc-text>

                                <abc-text theme="gray">
                                    ，数量
                                </abc-text>
                                <abc-text theme="black">
                                    {{ returnGoodsFooterTotalCount }}
                                </abc-text>

                                <abc-text theme="gray">
                                    ，金额
                                </abc-text>
                                <abc-text theme="black">
                                    {{ returnGoodsFooterAmount | formatMoney(false) }}
                                </abc-text>
                            </abc-space>
                        </template>
                    </abc-table>
                    <abc-table
                        v-else-if="isSelectConfirmGoods"
                        :data-list="list"
                        empty-size="small"
                        :render-config="confirmTableRenderConfig"
                        :custom-tr-key="customTrKey"
                        cell-size="large"
                        type="excel"
                    >
                        <template #shortId="{ trData: row }">
                            <abc-table-cell v-if="row.goods" class="ellipsis">
                                <span>{{ row.goods.shortId }}</span>
                            </abc-table-cell>
                        </template>
                        <template #medicineName="{ trData: row }">
                            <display-name-cell v-if="row.goods" :goods="row.goods"></display-name-cell>
                        </template>
                        <template #count="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                {{ row | complexCount }}
                            </abc-table-cell>
                        </template>
                        <!--进价-->
                        <template #costPrice="{ trData: row }">
                            <abc-table-cell
                                v-if="row.inItem"
                                v-abc-title.ellipsis="`${paddingMoney(row.inItem.useUnitCostPrice)}/${row.inItem.useUnit}`"
                            >
                                {{ `${paddingMoney(row.inItem.useUnitCostPrice)}/${row.inItem.useUnit}` }}
                            </abc-table-cell>
                            <abc-table-cell
                                v-else
                                v-abc-title.ellipsis="`${paddingMoney(row.useUnitCostPrice)}/${row.inOrderTransTotalCount ? row.receiveUnit : row.useUnit}`"
                            >
                                {{ `${paddingMoney(row.useUnitCostPrice)}/${row.inOrderTransTotalCount ? row.receiveUnit : row.useUnit}` }}
                            </abc-table-cell>
                        </template>

                        <template #supplierId="{ trData: row }">
                            <abc-form-item>
                                <abc-select
                                    v-model="row.supplierId"
                                    custom-class="supplierWrapper"
                                    with-search
                                    clearable
                                    placeholder="供应商"
                                    :width="150"
                                    inner-width="280px"
                                    placement="bottom-end"
                                    :fetch-suggestions="fetchSuggestions"
                                    :tabindex="-1"
                                    @open="supplierListRetry"
                                >
                                    <abc-option
                                        v-for="it in currentSupplierList"
                                        :key="`${it.id }`"
                                        :value="it.id"
                                        :label="it.name"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </template>

                        <!--金额-->
                        <template #amount="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span v-abc-title="formatMoney(goodsTotalCostPrice(row), false)"></span>
                            </abc-table-cell>
                        </template>

                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item,
                            }"
                        >
                            <traceable-code-cell
                                v-model="item.traceableCodeList"
                                :goods="item.goods"
                                :goods-count="getUnitCount(item, item.goods, '出库数量')"
                                :need-validate="validateCell"
                                :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                            ></traceable-code-cell>
                        </template>
                        <template #footer>
                            <abc-space :size="4" style="margin-right: 12px; margin-left: auto;">
                                <abc-text theme="gray">
                                    品种
                                </abc-text>
                                <abc-text theme="black">
                                    {{ kindCount }}
                                </abc-text>

                                <abc-text theme="gray">
                                    ，数量
                                </abc-text>
                                <abc-text theme="black">
                                    {{ totalCount }}
                                </abc-text>

                                <abc-text theme="gray">
                                    ，金额
                                </abc-text>
                                <abc-text theme="black">
                                    {{ amount | formatMoney(false) }}
                                </abc-text>
                            </abc-space>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <template #footer>
            <template v-if="isSelectGoodsIn">
                <abc-pagination
                    style="margin-top: 0 !important;"
                    :show-total-page="true"
                    :pagination-params="pageParams"
                    :count="orderCount"
                    @current-change="changePageIndex"
                ></abc-pagination>
            </template>
            <abc-flex v-else justify="flex-end">
                <template v-if="isSelectReturnGoods">
                    <abc-space style="margin-left: auto;">
                        <abc-button v-if="!quickReturnOrderId" variant="ghost" @click="backGetInOrder">
                            上一步
                        </abc-button>
                        <abc-button
                            :disabled="disabledNextInitOrder"
                            @click="handleSelectGoodsNextClick"
                        >
                            下一步
                        </abc-button>
                        <abc-button variant="ghost" @click="handleCancel">
                            取消
                        </abc-button>
                    </abc-space>
                </template>
                <template v-else-if="isSelectConfirmGoods">
                    <abc-space style="margin-left: auto;">
                        <abc-button v-if="!isSpecificGoods" variant="ghost" @click="cancelOutOrder">
                            上一步
                        </abc-button>
                        <abc-button
                            :loading="buttonLoading"
                            :disabled="!list.length"
                            @click="createOutOrder"
                        >
                            {{ submitText }}
                        </abc-button>
                        <abc-button variant="ghost" @click="handleCancel">
                            取消
                        </abc-button>
                    </abc-space>
                </template>
            </abc-flex>
        </template>

        <return-dialog
            v-if="showGoodsInitReturnDialog"
            v-model="showGoodsInitReturnDialog"
            @refresh="handleRefresh"
            @close="handleCancel"
        ></return-dialog>
    </frame-dialog>
</template>


<script>
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';
    import {
        PurchaseGoodsInOrderStatusName,
        PurchaseGoodsInOrderStatusTagTheme,
        RelatedOrderType,
        ReturnPageType,
        ReturnPageTypeName,
        ReturnSourceType,
    } from '@/views-pharmacy/inventory/constant';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import { useFillHeightInDialog } from '@/views-pharmacy/inventory/hooks/useFillHeightInDialog';
    import {
        paddingMoney, parseTime,
    } from '@/utils';
    import { GoodsTypeEnum } from '@abc/constants';
    import pickerOptions from 'views/common/pickerOptions';
    import { formatDate } from '@abc/utils-date';
    import { mapGetters } from 'vuex';
    import clone from 'utils/clone';
    import StockOutAPI from 'api/goods/stock-out.js';
    import GoodsAutoCompleteCoverTitle from 'views/inventory/common/goods-auto-complete-cover-title.vue';
    import {
        UNSELECTED_SUPPLIER_INFO, GoodsInInitTypeEnum,GoodsTypeIdEnum,
    } from 'views/inventory/constant.js';
    import EnterEvent from 'views/common/enter-event';
    import { unitEqual } from 'views/inventory/goods-utils';
    import {
        _complexCount, clinicName, isChineseMedicine,
    } from 'src/filters/goods';
    import StockInAPI from 'api/goods/stock-in.js';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import DeleteGoodsHandler from 'views/inventory/mixins/delete-goods-handler';
    const { orderMainNameText } = getViewDistributeConfig().Inventory;
    import Big from 'big.js';
    import { formatMoney } from '@/filters';
    import {
        getSourceOrderText,getSourceOrder,
    } from '@/views-pharmacy/inventory/utils';
    // import OrderNoComp from '@/views-pharmacy/inventory/frames/components/orderNoComp.vue';
    const ReturnDialog = () => import('./orderDialog.vue');

    import {
        isNull,
    } from '@/utils';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import TraceCode, { TraceCodeScenesEnum } from '@/service/trace-code/service';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';

    /**
     * 退货单组件，支持三种模式：
     * 1. 新增退货，进入后需要选择退货单
     * 2. 传入指定的入库单（quickReturnOrderId），进入后需要选择退货商品；业务场景：入库单退货
     * 3. 传入指定的商品（specificReturnGoods），直接提供退货的商品信息；业务场景：召回和不合格品点击退货
     */
    export default {
        name: 'ReturnForm',
        components: {
            FrameDialog,
            GoodsAutoCompleteCoverTitle,
            ReturnDialog,
            DisplayNameCell,
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
        },
        mixins: [pickerOptions, EnterEvent, DeleteGoodsHandler],
        props: {
            value: Boolean,
            orderId: String,
            quickReturnOrderId: String,
            /**
             * 指定退货的商品，支持召回和不合格品，通过 source 区分 {@link ReturnSourceType}
             */
            specificReturnGoods: {
                type: Object,
                default: null,
            },
            gspInstId: String,
            // 需要审核
            stockOutChainReview: {
                type: Boolean,
                default: false,
            },
            isGsp: Boolean,
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('采购退货');

            const {
                dialogBody,
                tableRef,
                fillBottomOffset,
                logs,
                setLogs,
                openDialog,
            } = useFillHeightInDialog();

            const {
                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
                supplierListRetry,
            } = useSearchSupplier({
                excludeInitSupplier: true,
            });

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                dialogBody,
                tableRef,
                fillBottomOffset,
                logs,
                setLogs,
                handleOpenDialog () {
                    pushDialogName();
                    openDialog();
                },

                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
                supplierListRetry,
            };
        },
        data() {
            const endDate = new Date();
            const endDay = endDate.getDate(); // 当前日
            const endMonth = endDate.getMonth(); // 当前月
            const endYear = endDate.getFullYear(); // 当前年

            const startDate = new Date(endYear, endMonth, endDay - 29);
            return {
                ReturnPageType,
                orderMainNameText,
                showGoodsInitReturnDialog: false,
                active: ReturnPageType.SELECT_GOODS_IN,
                pageLoading: true,
                tableLoading: false,
                rightLoading: false,
                order: {},
                fromOrganId: '',
                dateRange: [formatDate(startDate), formatDate(endDate)],
                fetchParams: {
                    clinicId: '',
                    offset: 0,
                    limit: 10,
                    begDate: formatDate(startDate),
                    endDate: formatDate(endDate),
                    goodsId: '',
                    listCount: 10,
                    supplierId: '',
                    withReturnLeft: 1,
                    pharmacyNo: '',
                    chineseMedicine: false,
                },
                searchKey: '',
                loading: false,
                inOrders: [],
                orderCount: 0,

                buttonLoading: false,
                list: [],
                outOrder: {},
                comment: '',
                submitText: '提交',
                validateCell: false,

                returnGoodsSearchKey: '', // 选择退货商品支持商品搜索
                returnGoodsFetchParams: {
                    offset: 0,
                    limit: 500,
                },
                returnGoodsPageParams: {
                    showTotalPage: true,
                    pageIndex: 0,
                    pageSize: 500,
                    count: 0,
                },
                returnGoodsFooterParams: {
                    kindCount: 0,
                    totalCount: 0,
                    amount: 0,
                },
                returnGoodsSelectedList: [], // 保存已选中的退货商品列表
                isSearchMode: false, // 是否处于搜索模式
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
                'currentPharmacy',
                'userInfo',
                'traceCodeConfig',
                'isStrictCountWithTraceCodeCollect',
            ]),
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            relationshipOrderNo() {
                const {
                    sourceType,
                } = this.relatedOrder || {};
                if (!isNull(sourceType)) {
                    return getSourceOrderText(this.relatedOrder);
                }
                return '-';

            },
            // 来源单据
            relatedOrder() {
                const { relatedOrders } = this.order;
                const [order] = getSourceOrder(relatedOrders?.list ?? [], RelatedOrderType.PURCHASE);
                return order || {};
            },
            kindCount() {
                if (!this.list.length) return '';
                return this.uniqueCount(this.list);
            },
            totalCount() {
                let count = 0;
                this.list.forEach((item) => {
                    const pieceCount = Number((item.pieceCount / item.pieceNum).toFixed(4));
                    count += (+item.packageCount || 0) + pieceCount;
                });
                return count;
            },
            amount() {
                let total = 0;
                this.list.forEach((item) => {
                    total += +this.goodsTotalCostPrice(item) || 0;
                });
                return total;
            },
            /**
             * 当前选中的商品列表
             * @return {*}
             */
            selectedGoodsList() {
                return this.order?.list?.filter((item) => {
                    return item.checked;
                }) || [];
            },
            /**
             * 未选中商品时，禁用下一步
             * @return {boolean}
             */
            disabledNextInitOrder() {
                return this.returnGoodsSelectedList.length === 0;
            },
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            pharmacyType() {
                return this.currentPharmacy?.type;
            },
            // 选择退货单
            selectTableColumns() {
                const {
                    orderNo, createdDate, createdUser, toOrgan, supplier, kindCount, sum, amount = 0, supplierSeller, supplierSellerName,
                } = this.order || {};
                return {
                    orderNo,
                    createdDate: parseTime(createdDate,'y-m-d h:i:s',true),
                    supplier,
                    user: createdUser?.name || '',
                    clinicName: clinicName(toOrgan),
                    comment: '',
                    kindCount,
                    sum,
                    amount: amount || 0,
                    supplierSellerName: supplierSeller?.name || supplierSellerName || '-',
                };
            },
            // 验收单
            receiveOrders() {
                const { relatedOrders } = this.order;
                const orders = getSourceOrder(relatedOrders?.list ?? [], RelatedOrderType.INSPECT);
                return orders || [];
            },
            // 确认退货单
            createTableColumns() {
                const {
                    toOrgan, supplier, supplierSeller, supplierSellerName,
                } = this.order || {};
                return {
                    user: this.userInfo?.name || '',
                    clinicName: clinicName(toOrgan),
                    supplier,
                    comment: this.selectTableColumns.comment || '',
                    orderNo: this.orderId ? this.order.stockInOrder?.orderNo : this.order.orderNo,
                    supplierSellerName: supplierSeller?.name || supplierSellerName || '-',
                };
            },
            title() {
                const name = ReturnPageTypeName[this.active];
                return name;
            },
            titleOrderNo() {
                return this.order.orderNo;
            },
            statusName() {
                return PurchaseGoodsInOrderStatusName[this.order.status];
            },
            tagConfig() {
                const config = {
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                };
                config.theme = PurchaseGoodsInOrderStatusTagTheme[this.order.status];
                return config;
            },
            rightWidth() {
                return 0;
            },
            isSelectGoodsIn() {
                return this.active === ReturnPageType.SELECT_GOODS_IN;
            },
            isSelectReturnGoods() {
                return this.active === ReturnPageType.SELECT_RETURN_GOODS;
            },
            isSelectConfirmGoods() {
                return this.active === ReturnPageType.CONFIRM_RETURN_GOODS;
            },
            canEdit() {
                return true;
            },
            confirmTableRenderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'shortId',
                            label: '商品编码',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'medicineName',
                            label: '商品名称',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '280px',
                                minWidth: '280px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'batchNo',
                            label: '生产批号',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'expiryDate',
                            label: '效期',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '110px',
                                minWidth: '110px',
                                maxWidth: '110px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'costPrice',
                            label: '进价',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '90px',
                                minWidth: '90px',
                                maxWidth: '90px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'supplierId',
                            label: '供应商',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '150px',
                                minWidth: '150px',
                                maxWidth: '150px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                            hidden: !this.isInitInOrderUnselectedSupplier,
                        },
                        {
                            key: 'count',
                            label: '出库数量',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '90px',
                                minWidth: '90px',
                                maxWidth: '90px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'amount',
                            label: '金额',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '90px',
                                minWidth: '90px',
                                maxWidth: '90px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
            tableRenderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            label: '',
                            isCheckbox: true,
                            pinned: true,
                            style: {
                                flex: 'none',
                                width: '40px',
                                maxWidth: '',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'shortId',
                            label: '商品编码',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'medicineName',
                            label: '商品名称',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '180px',
                                minWidth: '180px',
                                maxWidth: '300px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'batchNo',
                            label: '生产批号',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'expiryDate',
                            label: '效期',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '92px',
                                minWidth: '92px',
                                maxWidth: '92px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'count',
                            label: '入库数量',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'costPrice',
                            label: '进价',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'supplierId',
                            label: '供应商',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '150px',
                                minWidth: '150px',
                                maxWidth: '150px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                            hidden: !this.isInitInOrderUnselectedSupplier,
                        },
                        {
                            key: 'useTotalCostPrice',
                            label: '金额',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'returnCount',
                            label: '可退数量',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '90px',
                                minWidth: '90px',
                                maxWidth: '90px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'realReturnCount',
                            label: '退货数量',
                            slot: true,
                            style: {
                                flex: 1,
                                width: '180px',
                                minWidth: '180px',
                                maxWidth: '180px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
            pageParams() {
                const {
                    limit: pageSize,
                    offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                };
            },
            returnGoodsTable() {
                if (this.isSelectReturnGoods) {
                    return {
                        column: 9,
                        span_1: 3,
                        span_2: 3,
                        span_3: 3,
                        span_4: 3,
                        span_5: 3,
                        span_6: 3,
                        span_7: 3,
                        span_8: 3,
                        span_9: 3,
                    };
                }
                if (this.isSelectConfirmGoods) {
                    return {
                        column: 9,
                        span_1: 3,
                        span_2: 3,
                        span_3: 3,
                        span_4: 3,
                        span_5: 3,
                        span_6: 3,
                    };
                }
                return {
                    column: 9,
                    span_1: 3,
                    span_2: 3,
                    span_3: 3,
                };
            },
            isSpecificGoods() {
                return !!this.specificReturnGoods;
            },
            // 退货选择商品-滚动加载配置
            scrollLoadConfig() {
                // 搜索模式下不启用滚动加载
                if (this.isSearchMode) {
                    return null;
                }
                return {
                    fetchData: this.getInboundOrderList,
                    total: this.returnGoodsPageParams.count,
                };
            },
            returnGoodsFooterKindCount() {
                if (!this.returnGoodsSelectedList.length) return 0;
                return this.uniqueCount(this.returnGoodsSelectedList);
            },
            returnGoodsFooterTotalCount() {
                let count = Big(0);
                this.returnGoodsSelectedList.forEach((item) => {
                    const pieceCount = Big(item.returnPieceCount || 0).div(item.pieceNum || 1).toFixed(4);
                    count = count.plus(Big(item.returnPackageCount || 0)).plus(Big(pieceCount));
                });
                // 如果是整数则展示整数，小数则保留两位
                const result = count.toString();
                if (result.indexOf('.') === -1 || result.endsWith('.00')) {
                    return parseInt(result, 10);
                }
                return count.toFixed(2);
            },
            returnGoodsFooterAmount() {
                let total = 0;
                this.returnGoodsSelectedList.forEach((item) => {
                    const tempItem = { ...item };
                    tempItem.packageCount = tempItem.returnPackageCount || '';
                    tempItem.pieceCount = tempItem.returnPieceCount || '';
                    total += +this.goodsTotalCostPrice(tempItem) || 0;
                });
                return total;
            },
            isInitInOrderUnselectedSupplier() {
                return GoodsInInitTypeEnum.includes(this.order.type) && this.order.supplierId === UNSELECTED_SUPPLIER_INFO.pharmacy.id;
            },
        },
        created() {
            // 指定商品
            if (this.isSpecificGoods) {
                this.fetchInOrderBySpecificGoods(this.specificReturnGoods);
            } else if (this.quickReturnOrderId) {
                this.active = ReturnPageType.SELECT_RETURN_GOODS;
                this.selectInOrder(this.quickReturnOrderId);
            } else {
                this.fetchInOrder();
            }
        },
        methods: {
            paddingMoney,
            formatMoney,
            isChineseMedicine,
            unitEqual,
            isNull,
            handleList(list) {
                if (this.isGsp) {
                    return list.filter((item) => {
                        return [GoodsTypeIdEnum.CHINESE_MEDICINE,
                                GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                                GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES,
                                GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE].includes(item?.goods?.typeId);
                    });
                }
                return list;
            },
            supplierChange(supplierId, row) {
                // 更新已选中项目中对应的数据
                if (this.returnGoodsSelectedList && this.returnGoodsSelectedList.length > 0) {
                    const index = this.returnGoodsSelectedList.findIndex((item) => item.goodsId === row.goodsId && item.batchId === row.batchId);

                    if (index !== -1) {
                        // 更新已选中项目中的供应商信息
                        this.returnGoodsSelectedList[index].supplierId = supplierId;
                    }
                }
            },

            customTrKey(item) {
                return `result_${item.goods.id}`;
            },
            customSelectTrKey(item) {
                return `select_${item.goodsId}_${item.batchId}`;
            },
            goodsTotalCostPrice(item) {
                try {
                    // 全退的场景，直接使用入库总金额，避免价格对不上
                    if (
                        ((item.returnPieceCount || 0) === (item.returnLeftPieceCount || 0)) &&
                        ((item.returnPackageCount || 0) === (item.returnLeftPackageCount || 0))
                    ) {
                        return item.useTotalCostPrice;
                    }

                    // 大单位成本价格
                    const packageCostPrice = item.inItem ? item.inItem.packageCostPrice : item.packageCostPrice;
                    // 小单位转换为大单位
                    const packageCount = Big(item.packageCount || 0).plus(Big(item.pieceCount || 0).div(item.pieceNum || 1));
                    // 计算总价
                    const price = (Big(packageCostPrice).times(packageCount)).toFixed(4, Big.roundHalfUp);
                    return price || '';
                } catch (e) {
                    console.error(e);
                    return '';
                }
            },
            uniqueCount(list) {
                let count = 0;
                const Obj = Object.create(null);

                list.forEach((item) => {
                    if (!Obj[item.goods.id]) {
                        count++;
                        Obj[item.goods.id] = 1;
                    }
                });
                return count;
            },
            getConfig(goods) {
                return {
                    formatLength: (this.isChineseMedicine(goods) || goods.type === GoodsTypeEnum.GOODS) ? 2 : 0,
                    max: 10000000,
                    supportZero: false,
                };
            },
            validateStock({
                returnPackageCount = 0,
                returnPieceCount = 0,
                pieceNum,
                returnLeftPackageCount,
                returnLeftPieceCount,
            }) {
                const flag = +returnPieceCount + returnPackageCount * pieceNum > +returnLeftPieceCount + returnLeftPackageCount * pieceNum;
                if (flag) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '库存不足',
                        });
                    };
                }
                return (_, callback) => {
                    callback({ validate: true });
                };

            },
            closeDialog() {
                this.popDialogName();
                this.handleCancel();
            },
            transList(list) {
                if (this.isGsp) {
                    return list.filter((item) => {
                        return [GoodsTypeIdEnum.CHINESE_MEDICINE,
                                GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                                GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES,
                                GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE].includes(item?.goods?.typeId);
                    }).map((item) => {
                        return `${item.goods.medicineCadn || item.goods.name || ''} ${_complexCount(item) || ''}`;
                    }).join('、');
                }
                return list
                    .map((item) => {
                        return `${item.goods.medicineCadn || item.goods.name || ''} ${_complexCount(item) || ''}`;
                    })
                    .join('、');
            },
            disabledBtn(list = []) {
                if (this.isGsp) {
                    return false;
                }
                return list.every((item) => !item.returnLeft);
            },
            handleSelect(item) {
                console.log(item);
                // 打开初始化入库单退货
                this.showGoodsInitReturnDialog = true;
            },
            async selectGspOrder(id) {
                // GSP使用选择入库单功能
                this.returnGoodsFetchParams.offset = 0;
                const order = await StockInAPI.getOrderById(id, {
                    withStockId: 1,
                    withReturnLeft: 1,
                    offset: this.returnGoodsFetchParams.offset,
                    limit: this.returnGoodsFetchParams.limit,
                    withGoodsId: this.fetchParams.goodsId,
                    withGoodsIdOrderFirst: 1,
                });
                this.$emit('select-order', order);
                this.showDialog = false;
            },
            async selectInOrder(id) {
                this.pageLoading = true;
                try {
                    this.returnGoodsFetchParams.offset = 0;
                    const data = await StockInAPI.getOrderById(id, {
                        withStockId: 1,
                        withReturnLeft: 1,
                        offset: this.returnGoodsFetchParams.offset,
                        limit: this.returnGoodsFetchParams.limit,
                        withGoodsId: this.fetchParams.goodsId,
                        withGoodsIdOrderFirst: 1,
                    });
                    data.list = data.list.map((item) => {
                        // 默认填充可退数量
                        if (isChineseMedicine(item.goods)) {
                            item.returnLeftPieceCount = item.returnLeftPieceCount || 0;
                            // 中药只有pieceUnit
                            item.returnPieceCount = item.returnLeftPieceCount;
                            item.returnPackageCount = '';
                        } else {
                            // 兼容处理保证可退数量存在
                            if (!item.returnLeftPieceCount && !item.returnLeftPackageCount) {
                                item.returnLeftPieceCount = 0;
                                item.returnLeftPackageCount = 0;
                            }

                            item.returnPieceCount = item.returnLeftPieceCount || '';
                            item.returnPackageCount = item.returnLeftPackageCount || '';
                        }
                        const checked = this.fetchParams.goodsId === item.goodsId && item.returnLeft;
                        item.checked = checked;
                        item.traceableCodeList = [];
                        if (item.checked) {
                            this.checkOne(item);
                        }
                        return item;
                    });
                    if (!this.fetchParams.goodsId) {
                        data.list.sort((a, b) => {
                            return b.returnLeft > a.returnLeft ? 1 : -1;
                        });
                    }

                    this.order = data;
                    this.cacheOrderList = clone(this.order.list);
                    this.returnGoodsPageParams = {
                        showTotalPage: true,
                        pageIndex: 0,
                        pageSize: this.returnGoodsFetchParams.limit,
                        count: this.order.totalCount,
                    };
                    this.returnGoodsFetchParams.offset += this.returnGoodsFetchParams.limit;
                    this.active = ReturnPageType.SELECT_RETURN_GOODS;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.pageLoading = false;
                }
            },
            async searchInReturnGoods(params) {
                const res = await StockInAPI.getInboundOrderList(this.order.id, {
                    totalCount: this.order.totalCount,
                    offset: 0,
                    limit: 200,
                    keyword: params.key,
                });
                if (res?.data?.rows) {
                    res.data.list = res.data.rows.map((e) => {
                        // 默认填充可退数量
                        if (isChineseMedicine(e.goods)) {
                            e.returnLeftPieceCount = e.returnLeftPieceCount || 0;
                            // 中药只有pieceUnit
                            e.returnPieceCount = e.returnLeftPieceCount;
                            e.returnPackageCount = '';
                        } else {
                            // 兼容处理保证可退数量存在
                            if (!e.returnLeftPieceCount && !e.returnLeftPackageCount) {
                                e.returnLeftPieceCount = 0;
                                e.returnLeftPackageCount = 0;
                            }

                            e.returnPieceCount = e.returnLeftPieceCount || '';
                            e.returnPackageCount = e.returnLeftPackageCount || '';
                        }

                        return {
                            ...e.goods,
                            ...e,
                            name: e.goods?.displayName || e.goods?.name || '',
                            // 入库数量
                            inPackageCount: e.packageCount,
                            inPieceCount: e.pieceCount,
                            // 可退数量
                            packageCount: e.returnLeftPackageCount,
                            pieceCount: e.returnLeftPieceCount,
                        };
                    });
                }
                return res?.data;
            },
            async selectReturnGoods(goods) {
                // 设置为搜索结果页模式
                this.isSearchMode = true;

                // 在搜索结果中选择商品时，保留已选中的商品状态
                const existingItem = this.returnGoodsSelectedList.find((selected) =>
                    selected.goodsId === goods.goodsId && selected.batchId === goods.batchId,
                );

                if (existingItem) {
                    goods.checked = true;
                    goods.returnPackageCount = existingItem.returnPackageCount;
                    goods.returnPieceCount = existingItem.returnPieceCount;
                    goods.traceableCodeList = existingItem.traceableCodeList || [];
                } else {
                    // 初始化退货数量
                    if (isChineseMedicine(goods.goods)) {
                        goods.returnPieceCount = goods.returnLeftPieceCount || 0;
                        goods.returnPackageCount = '';
                    } else {
                        goods.returnPieceCount = goods.returnLeftPieceCount || '';
                        goods.returnPackageCount = goods.returnLeftPackageCount || '';
                    }
                    goods.checked = goods.returnLeft ? true : false;
                }

                this.order.list = [goods];
                this.checkOne(goods);
            },
            // 补充提示自定义搜索时无数据进入异常后的提示
            customErrorHandler(goods) {
                this.$alert({
                    type: 'warn',
                    title: '提示',
                    content: '当前商品不在入库单中，无法退货',
                    onClose: () => {
                        console.log(goods);
                        this.searchKey = '';
                    },
                });
            },
            handleReturnGoodsInput(val) {
                if (!val) {
                    this.clearReturnGoodsSearch();
                }
            },
            clearReturnGoodsSearch() {
                // 退出搜索模式
                this.isSearchMode = false;

                // 恢复原始列表，并保持已选中项的状态
                const originalList = this.cacheOrderList || [];

                // 更新原始列表中的选中状态和数据
                this.order.list = originalList.map((item) => {
                    const selectedItem = this.returnGoodsSelectedList.find((selected) =>
                        selected.goodsId === item.goodsId && selected.batchId === item.batchId,
                    );

                    if (selectedItem) {
                        return {
                            ...item,
                            checked: true,
                            returnPackageCount: selectedItem.returnPackageCount,
                            returnPieceCount: selectedItem.returnPieceCount,
                            traceableCodeList: selectedItem.traceableCodeList || [],
                        };
                    }

                    return {
                        ...item,
                        checked: false,
                    };
                });

                this.returnGoodsSearchKey = '';
            },

            getUnitCount(item, goods, label) {
                const count = TraceCode.getCollectCount(item, goods);
                let unitCount = '', unit = '', packageCount = '', pieceCount = '';

                // 拆零
                if (Math.abs(count) < goods.pieceNum) {
                    unit = goods.pieceUnit;
                    unitCount = Math.abs(count);
                    pieceCount = unitCount;
                } else {
                    unit = goods.packageUnit;
                    unitCount = Math.ceil(Math.abs(count / goods.pieceNum));
                    packageCount = Math.floor(Math.abs(count / goods.pieceNum));
                    pieceCount = Math.abs(count % goods.pieceNum);
                }

                return {
                    useExternalCount: this.isStrictCountWithTraceCodeCollect,
                    packageCount,
                    pieceCount,
                    unitCount,
                    unit,
                    label,
                    countLabel: label,
                    maxCount: item._maxTraceCodeCount,
                    isTrans: item._isTransformable,
                };
            },
            async initCollectCodeCountList(list = []) {
                const resList = await TraceCode.getMaxTraceCountList({
                    scene: TraceCodeScenesEnum.INVENTORY,
                    dataList: list,
                    createKeyId: this.customSelectTrKey,
                    getGoodsInfo: (item) => item.goods,
                    getUnitInfo: (item) => {
                        return this.getUnitCount({
                            packageCount: item.returnPackageCount,
                            pieceCount: item.returnPieceCount,
                        }, item.goods);
                    },
                });

                resList.forEach((e) => {
                    const item = list.find((i) => i.id === e.keyId);
                    if (item) {
                        this.$set(item, '_maxTraceCodeCount', e.traceableCodeNum);
                        this.$set(item, '_isTransformable', e.isTransformable);
                    }
                });
            },
            initNoTraceCodeList(item) {
                // 对无码商品初始化追溯码
                if (this.isEnableTraceableCode && TraceCode.isSupplementNoCodeGoods(item.goods)) {
                    const traceableCodeList = TraceCode.mergeNoTraceCodeList({
                        ...item,
                        ...this.getUnitCount({
                            ...item,
                            packageCount: item.returnPackageCount,
                            pieceCount: item.returnPieceCount,
                        }, item.goods),
                    });
                    this.$set(item, 'traceableCodeList', traceableCodeList);
                }
            },
            async handleChangeUnitCount(item) {
                // 开启强校验时才实时计算
                if (this.isEnableTraceableCode && this.isStrictCountWithTraceCodeCollect && TraceCode.isSupportTraceCodeForceCheckStock()) {
                    await this.initCollectCodeCountList([item]);
                }

                this.initNoTraceCodeList(item);
            },

            checkOne(changedItem) {
                if (!changedItem) {
                    return;
                }

                if (changedItem?.checked) {
                    // 初始化退货数量（如果未设置）
                    if (isChineseMedicine(changedItem.goods)) {
                        if (!changedItem.returnPieceCount && changedItem.returnLeftPieceCount) {
                            changedItem.returnPieceCount = changedItem.returnLeftPieceCount;
                            changedItem.returnPackageCount = '';
                        }
                    } else {
                        if ((!changedItem.returnPieceCount && !changedItem.returnPackageCount) &&
                            (changedItem.returnLeftPieceCount || changedItem.returnLeftPackageCount)) {
                            changedItem.returnPieceCount = changedItem.returnLeftPieceCount || '';
                            changedItem.returnPackageCount = changedItem.returnLeftPackageCount || '';
                        }
                    }

                    // 添加到已选中列表
                    const existIndex = this.returnGoodsSelectedList.findIndex((selected) =>
                        selected.goodsId === changedItem.goodsId && selected.batchId === changedItem.batchId,
                    );

                    if (existIndex === -1) {
                        this.returnGoodsSelectedList.push(changedItem);
                    } else {
                        // 更新已存在的项
                        this.returnGoodsSelectedList.splice(existIndex, 1, changedItem);
                    }

                    this.handleChangeUnitCount(changedItem, changedItem.goods);

                    // 聚焦到该行第一个可编辑的input
                    this.$nextTick(() => {
                        const tr = document.querySelector(`.abc-table-tr[data-id="${changedItem.id}"]`);
                        if (tr) {
                            // 找到第一个abc-input-wrapper下的input元素并聚焦
                            const firstInputWrapper = tr.querySelector('.abc-input-wrapper');
                            if (firstInputWrapper) {
                                const input = firstInputWrapper.querySelector('input');
                                if (input) {
                                    input.focus();
                                }
                            }
                        }
                    });
                } else if (changedItem) {
                    // 从已选中列表中移除
                    const existIndex = this.returnGoodsSelectedList.findIndex((selected) =>
                        selected.goodsId === changedItem.goodsId && selected.batchId === changedItem.batchId,
                    );

                    if (existIndex !== -1) {
                        this.returnGoodsSelectedList.splice(existIndex, 1);
                    }
                }
            },
            customAllCheckedFunc(dataList) {
                return dataList.filter((item) => item.returnLeft).every((item) => item.checked);
            },
            async checkAll(checked) {
                console.log('checkAll', checked);
                if (checked) {
                    // 判断是否已经加载了所有数据
                    const isAllLoaded = this.order.list.length >= this.order.totalCount;

                    // 如果没有加载完所有数据，先加载全部数据
                    if (!isAllLoaded) {
                        try {
                            // 计算还需要加载的数据数量
                            const remainingCount = this.order.totalCount - this.order.list.length;
                            // 根据剩余数量计算还需要请求的次数
                            const requestsNeeded = Math.ceil(remainingCount / this.returnGoodsFetchParams.limit);

                            for (let i = 0; i < requestsNeeded; i++) {
                                await this.getInboundOrderList(true);
                                // 如果已加载完成，提前退出循环
                                if (this.order.list.length >= this.order.totalCount) {
                                    break;
                                }
                            }
                        } catch (error) {
                            console.error('加载全部数据失败:', error);
                        }
                    }

                    // 处理追溯码相关逻辑
                    if (this.isEnableTraceableCode) {
                        const list = this.order.list.filter((item) => item.checked);
                        if (this.isStrictCountWithTraceCodeCollect) {
                            await this.initCollectCodeCountList(list);
                        }

                        list.forEach((item) => {
                            // 补充无码标识
                            this.initNoTraceCodeList(item);
                        });
                    }

                    this.$nextTick(() => {
                        this.handleGoodsAllSelection();
                    });
                } else {
                    // 取消全选直接清空已选中列表
                    this.returnGoodsSelectedList = [];
                }
            },
            /**
             * 处理商品全选状态变化
             * 由于全选时已经加载了所有数据，只需处理当前列表中选中的项
             * 对于已经存在于选中列表的项，保留其原有数据
             */
            handleGoodsAllSelection() {
                if (!this.order.list.length) return;

                // 创建已选中项目的映射表
                const selectedMap = new Map();
                this.returnGoodsSelectedList.forEach((item) => {
                    const key = `${item.goodsId}-${item.batchId}`;
                    selectedMap.set(key, item);
                });

                const checkedItems = this.order.list.filter((item) => item.checked && item.returnLeft).map((item) => {
                    const key = `${item.goodsId}-${item.batchId}`;

                    if (selectedMap.has(key)) {
                        const selectedItem = selectedMap.get(key);
                        const result = selectedItem;
                        result.checked = true;
                        return result;
                    }

                    return item;
                });

                this.returnGoodsSelectedList = checkedItems;
            },
            disabledItemFunc(item) {
                return !item.returnLeft;
            },
            formatReturnLeft(item) {
                if (this.isChineseMedicine(item.goods)) {
                    return `${item.returnLeft}${item.goods.pieceUnit}`;
                }
                if (item.returnLeftPieceCount) {
                    return `${
                        item.returnLeftPackageCount || 0
                    }${
                        item.goods.packageUnit || ''
                    }${
                        item.returnLeftPieceCount
                    }${
                        item.goods.pieceUnit || ''
                    }`;
                }

                return `${item.returnLeft || 0}${item.useUnit || ''}`;
            },
            changePageIndex(index) {
                this.fetchParams.offset = (index - 1) * this.fetchParams.limit;
                this.fetchInOrder();
            },
            backGetInOrder() {
                if (this.returnGoodsSelectedList.length > 0) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: `返回上一步（选择入库单），已选择的${orderMainNameText}将清空`,
                        onConfirm: async () => {
                            this.order = {};
                            this.active = ReturnPageType.SELECT_GOODS_IN;
                            // 清空已选中列表
                            this.returnGoodsSelectedList = [];
                            this.returnGoodsSearchKey = '';
                            this.fetchInOrder();
                        },
                    });
                    return;
                }
                this.order = {};
                this.active = ReturnPageType.SELECT_GOODS_IN;
                // 清空已选中列表
                this.returnGoodsSelectedList = [];
                this.returnGoodsSearchKey = '';
                this.fetchInOrder();
            },
            /**
             * 点击选择商品下一步，将 this.order.list 中选中的商品列表 转换为 this.list
             */
            handleSelectGoodsNextClick() {
                this.validateCell = false;

                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.form.validate(async (val) => {
                        if (val) {
                            // 使用 returnGoodsSelectedList 代替 selectedGoodsList
                            this.initOutOrder(this.returnGoodsSelectedList);
                            this.active = ReturnPageType.CONFIRM_RETURN_GOODS;
                            if (this.isStrictCountWithTraceCodeCollect) {
                                this.validateCell = true;
                            }
                        }
                    });
                }, 0);

            },
            initOutOrder(selectedGoodsList) {
                this.type = 10;
                const goodsList = clone(selectedGoodsList);
                this.list = goodsList.map((item) => {
                    return {
                        ...item,
                        checked: false,
                        packageCount: item.returnPackageCount || '',
                        pieceCount: item.returnPieceCount || '',
                        traceableCodeList: item.traceableCodeList || [],
                    };
                });
            },
            cancelOutOrder() {
                const helpMap = this.list.reduce((res, item) => {
                    res[`${item.goods.id}-${item.batchId}`] = item;
                    return res;
                }, {});

                this.list = [];
                this.active = ReturnPageType.SELECT_RETURN_GOODS;
                this.order.list = this.order.list.map((item) => {
                    const _item = helpMap[`${item.goods.id}-${item.batchId}`];
                    item.checked = !!_item;
                    if (this.isEnableTraceableCode) {
                        // 还原已保存追溯码
                        item.traceableCodeList = _item?.traceableCodeList || [];
                    }
                    return item;
                });
            },
            createOutOrder() {
                this.validateCell = false;
                this.buttonLoading = true;

                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.form.validate(async (val) => {
                        if (val) {

                            if (this.isEnableTraceableCode) {
                                const {
                                    flag, errorList,
                                } = await TraceCode.validate({
                                    scene: TraceCodeScenesEnum.INVENTORY,
                                    dataList: this.list,
                                    createKeyId: this.customTrKey,
                                    getGoodsInfo: (item) => item.goods,
                                    getUnitInfo: (item) => {
                                        return this.getUnitCount(item, item.goods, '退货数量');
                                    },
                                    needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckStock(),
                                });
                                if (!flag) {
                                    this.$confirm({
                                        type: 'warn',
                                        title: '追溯码采集风险提醒',
                                        content: errorList.map((it) => {
                                            const {
                                                count,
                                                warnTips,
                                            } = it;
                                            return `有 ${count} 个商品${warnTips}`;
                                        }),
                                        confirmText: '去修改',
                                        cancelText: '仍要提交',
                                        disabledKeyboard: true,
                                        showClose: false,
                                        onConfirm: () => {
                                            this.validateCell = true;
                                            this.buttonLoading = false;

                                            // eslint-disable-next-line abc/no-timer-id
                                            setTimeout(() => {
                                                this.$refs.form.validate();
                                            }, 0);
                                        },
                                        onCancel: () => {
                                            this.submitConfirm();
                                        },
                                    });
                                    return;
                                }
                            }

                            this.submitConfirm();
                        } else {
                            this.buttonLoading = false;
                        }
                    });
                }, 0);
            },
            async submitConfirm() {
                // 开启了审核
                if (this.stockOutChainReview) {
                    this.buttonLoading = false;
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '确认提交后将发起审核确认，确认通过立即出库',
                        onConfirm: () => {
                            this.createOrder();
                        },
                    });
                } else {
                    this.buttonLoading = true;
                    await this.createOrder();
                    this.buttonLoading = false;
                }
            },
            async createOrder() {
                this.fromOrganId = this.order.toOrganId;
                const {
                    type,
                    fromOrganId,
                    list,
                } = this;
                try {
                    if (this.isInitInOrderUnselectedSupplier) {
                        await StockOutAPI.createImportGoodsOrder({
                            comment: this.comment,
                            type: this.order.type,
                            list: list.map((item) => {
                                return {
                                    'goodsId': item.goodsId,
                                    'packageCount': item.returnPackageCount,
                                    'pieceCount': item.returnPieceCount,
                                    'returnInOrderId': item.orderId,
                                    'stockInId': item.id,
                                    'supplierId': item.supplierId,
                                    traceableCodeList: TraceCode.transCodeList(item.traceableCodeList || []),
                                };
                            }),
                        });
                    } else {
                        const params = {
                            type,
                            clinicId: fromOrganId,
                            comment: this.comment,
                            list: list.map((item) => {
                                return {
                                    goodsId: item.goods?.id,
                                    stockId: item.stock?.id,
                                    stockInId: item.id, // 详单ID
                                    pieceCount: item.pieceCount || 0,
                                    packageCount: item.packageCount || 0,
                                    traceableCodeList: TraceCode.transCodeList(item.traceableCodeList || []),
                                };
                            }),
                            returnInOrderId: this.order.id, // 入库单ID
                        };
                        if (this.isSpecificGoods) {
                            // 指定商品的模式，需要透传关联的订单
                            params.relatedOrders = this.order.relatedOrders;
                        }
                        await StockInAPI.createOrder(params);
                    }

                    this.showDialog = false;
                    this.$emit('refresh', 1);
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                } catch (e) {
                    console.error(e);
                    if (e.code === 12010) {
                        const shortageMedicineTips = [];
                        const goodsStock = (e && e.detail) || [];
                        const { length } = goodsStock;
                        goodsStock.forEach((item, index) => {
                            let str = '';
                            str = `${item.goods.medicineCadn || item.goods.name || ''}`;
                            if (length - 1 === index) {
                                str += ' 库存不足';
                            }
                            shortageMedicineTips.push(str);
                        });
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: shortageMedicineTips,
                        });
                    } else if (e.code === 12015) {
                        this.handleGoodsDelete(e.detail, () => {
                            this.list = this.list.filter((item) => item.goods.id !== e.detail.goodsId);
                        });
                    } else if (e.code === 12808) {
                        this.handleGoodsDisable(e.detail, () => {
                            this.list = this.list.filter((item) => item.goods.id !== e.detail.goodsId);
                        });
                    } else {
                        if (!e.alerted) {
                            this.$Toast({
                                message: e.message,
                                type: 'error',
                            });
                        }
                    }
                }
            },
            handleRefresh(data) {
                this.$emit('refresh', data);
                this.handleCancel();
            },
            handleCancel() {
                this.showDialog = false;
                this.$emit('close');
            },
            changeDate(picker) {
                if (picker.length === 2) {
                    this.fetchParams.begDate = picker[0];
                    this.fetchParams.endDate = picker[1];
                } else {
                    this.fetchParams.begDate = '';
                    this.fetchParams.endDate = '';
                }
                this.fetchParams.offset = 0;
                this.fetchInOrder();
            },
            changeSupplier() {
                this.fetchParams.offset = 0;
                this.fetchInOrder();
            },
            async selectGoods(goods) {
                this.fetchParams.goodsId = goods.id;
                if (goods.medicineCadn) {
                    this.searchKey = goods.medicineCadn + (goods.name ? `（${goods.name}）` : '');
                } else {
                    this.searchKey = goods.name || '';
                }
                this.fetchParams.offset = 0;
                this.fetchInOrder();
            },
            clearSearch() {
                this.searchKey = '';
                this.fetchParams.goodsId = '';
                this.fetchInOrder();
            },
            async fetchInOrder() {
                this.loading = true;

                if (!this.isChainAdmin) {
                    this.fetchParams.pharmacyNo = this.pharmacyNo;
                } else {
                    this.fetchParams.pharmacyNo = '';
                }
                this.fetchParams.clinicId = this.currentClinic?.clinicId;
                this.fetchParams.chineseMedicine = !!this.isGsp;
                try {
                    const { data } = await StockOutAPI.fetchInOrder(this.fetchParams);
                    // const { data: initGoodsData } = await StockOutAPI.fetchInOrderWithImport(this.fetchParams);
                    const {
                        count,
                        rows,
                    } = data;
                    // https://www.tapd.cn/tapd_fe/67459320/story/detail/1167459320001087980 调整，不需要拉初始化入库数据
                    // // 汇总成一条数据
                    // if (initGoodsData?.rows?.length) {
                    //     rows.unshift({
                    //         sourceType: 'goodsInit',
                    //         orderNo: initGoodsData.orderNo,
                    //         list: initGoodsData.rows.reduce((res, item) => {
                    //             return res.concat(item.list);
                    //         }, []),
                    //     });
                    //     this.orderCount = count + 1;
                    // } else {
                    //     this.orderCount = count;
                    // }
                    this.orderCount = count;
                    this.inOrders = rows;

                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                    this.pageLoading = false;
                }
            },
            /**
             * 通过指定的商品准备提交数据
             * @param specificReturnGoods
             * @return {Promise<void>}
             */
            async fetchInOrderBySpecificGoods(specificReturnGoods) {
                const {
                    sourceType,
                    returnOrderProvider,
                    sourceNo = '',
                    isAdmin = false,
                } = specificReturnGoods;
                this.pageLoading = true;
                try {
                    this.order = await returnOrderProvider();
                    this.initOutOrder(this.order.list);
                    // 不是总部的单子
                    if (!isAdmin) {
                        this.submitText = '发送门店确认';
                    }
                    if (sourceType === ReturnSourceType.RECALL) {
                        this.comment = `召回记录: ${sourceNo}`;
                    } else if (sourceType === ReturnSourceType.UNQUALIFIED) {
                        this.comment = `不合格品: ${sourceNo}`;
                    }
                    this.active = ReturnPageType.CONFIRM_RETURN_GOODS;
                } catch (e) {
                    this.$Toast({
                        message: e.message || '获取退货单失败',
                        type: 'error',
                    });
                } finally {
                    this.pageLoading = false;
                }
            },
            // 滚动加载退货单列表
            async getInboundOrderList(autoChecked = false) {
                if (this.loading) {
                    return;
                }
                this.loading = true;
                try {
                    const params = {
                        keyword: '',
                        withGoodsId: this.fetchParams.goodsId,
                        withGoodsIdOrderFirst: 1,
                        totalCount: this.order.totalCount,
                        offset: this.returnGoodsFetchParams.offset,
                        limit: this.returnGoodsFetchParams.limit,
                    };
                    const res = await StockInAPI.getInboundOrderList(this.order.id, params);
                    const newList = res?.data?.rows?.map((item) => {
                        // 默认填充可退数量
                        if (isChineseMedicine(item.goods)) {
                            item.returnLeftPieceCount = item.returnLeftPieceCount || 0;
                            // 中药只有pieceUnit
                            item.returnPieceCount = item.returnLeftPieceCount;
                            item.returnPackageCount = '';
                        } else {
                            // 兼容处理保证可退数量存在
                            if (!item.returnLeftPieceCount && !item.returnLeftPackageCount) {
                                item.returnLeftPieceCount = 0;
                                item.returnLeftPackageCount = 0;
                            }

                            item.returnPieceCount = item.returnLeftPieceCount || '';
                            item.returnPackageCount = item.returnLeftPackageCount || '';
                        }

                        // 检查是否在已选中列表中
                        const selectedItem = this.returnGoodsSelectedList.find((selected) =>
                            selected.goodsId === item.goodsId && selected.batchId === item.batchId,
                        );

                        let checked = selectedItem ? true : (this.fetchParams.goodsId === item.goodsId && item.returnLeft);

                        // 如果已选中，使用已选中项的数据
                        if (selectedItem) {
                            return {
                                ...item,
                                checked,
                                returnPackageCount: selectedItem.returnPackageCount,
                                returnPieceCount: selectedItem.returnPieceCount,
                                traceableCodeList: selectedItem.traceableCodeList || [],
                            };
                        }

                        // 用于全选时批量拉取剩余数据
                        if (autoChecked && item.returnLeft) {
                            checked = true;
                        }

                        return {
                            ...item,
                            checked,
                            traceableCodeList: [],
                        };
                    });
                    const cacheOrderList = clone(newList);
                    cacheOrderList.sort((a, b) => {
                        return b.returnLeft > a.returnLeft ? 1 : -1;
                    });
                    this.order.list = this.order.list.concat(cacheOrderList);
                    this.cacheOrderList = clone(this.order.list);
                    this.returnGoodsPageParams.count = res.data.total;
                    this.returnGoodsFetchParams.offset += (res?.data?.rows?.length || 0);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
.pharmacy-return-goods_dialog {
    .select-goods-in_table {
        height: calc(100% - 57px);
    }

    .in-order-list {
        height: 100%;
        padding: 10px 0 10px 10px;
        overflow-y: scroll;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);

        .in-order-item {
            display: flex;
            align-items: center;

            &-box {
                padding: 8px 0;
                border-bottom: 1px dashed var(--abc-color-P8);

                &:first-child {
                    padding-top: 0;
                }
            }

            .info {
                flex: 1;
                width: 0;
                padding: 8px 16px;

                h4 {
                    line-height: 22px;
                }

                p {
                    line-height: 22px;
                }
            }

            .detail {
                display: none;
                color: $theme2;
                cursor: pointer;
            }

            .btn {
                display: none;
                width: 90px;
                min-width: 90px;
                max-width: 90px;
                text-align: center;
            }

            &:hover {
                background: var(--abc-color-cp-grey4);
                border-radius: var(--abc-border-radius-small);

                .btn {
                    display: block;
                }

                .info h4 .detail {
                    display: inline-block;
                }
            }
        }

        .no-in-order {
            display: flex;
            align-items: center;
            justify-content: space-around;
            width: 100%;
        }

        .is-disabled {
            cursor: default;
            background-color: #f9fafc;
        }

        .full-flex {
            display: flex;
            align-items: center;
            width: 100%;
            height: 100%;
        }
    }

    .abc-form.abc-form--is-excel {
        .return-goods-auto-complete-search {
            .abc-input__inner {
                border: 1px solid var(--abc-color-P7);
                border-radius: var(--abc-border-radius-small);
            }
        }
    }

    .abc-table--excel {
        .abc-table-tr.show-hover-tr-bg:not(.is-checked):hover {
            .abc-table-td {
                background-color: $tableTrHoverBg;
            }
        }
    }
}
</style>
