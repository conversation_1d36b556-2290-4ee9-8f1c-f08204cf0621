<template>
    <frame-dialog
        v-if="showDialog"
        ref="frameDialog"
        v-model="showDialog"
        :loading="pageLoading"
        :title="title"
        :order-no="order.orderNo"
        :status-name="statusName"
        :before-close="closeDialog"
        show-title-append
        :tag-config="tagConfig"
        responsive-dialog
        custom-class="biz-pharmacy-purchase-take-delivery-dialog"
        :dialog-config="{
            disabledKeyboard: showModal
        }"
    >
        <abc-form
            ref="form"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-form-item-group is-excel>
                        <abc-popover
                            ref="orderInfoPopoverRef"
                            placement="right"
                            trigger="hover"
                            theme="white"
                            :close-delay="0"
                            :disabled="onlyView || !(isFinishOrder && isOrderClinic)"
                            :resident-popover="edit"
                            :offset="20"
                            :popper-style="{
                                display: 'flex',
                                padding: '6px 8px',
                                'margin-left': '2px',
                            }"
                            :control="false"
                        >
                            <abc-descriptions
                                ref="orderInfoDescriptionsRef"
                                slot="reference"
                                :column="5"
                                :label-width="106"
                                size="large"
                                grid
                                background
                                stretch-last-item
                            >
                                <abc-descriptions-item
                                    label="收货门店"
                                    content-class-name="ellipsis"
                                    :content-style="{ padding: isAdd ? '0px' : '' }"
                                >
                                    <abc-form-item v-if="isAdd" required>
                                        <abc-popover
                                            placement="top"
                                            trigger="hover"
                                            theme="yellow"
                                            :popper-style="{
                                                padding: '0 0 0 0',
                                            }"
                                            :disabled="!isChainAdminSelf"
                                        >
                                            <abc-flex style="padding: 12px 16px;">
                                                <abc-text>供应商类型为总部集采（自配），收货门店仅支持总部</abc-text>
                                            </abc-flex>
                                            <div slot="reference">
                                                <clinic-select
                                                    v-model="order.clinicId"
                                                    placeholder="总部/门店"
                                                    :adaptive-width="true"
                                                    size="large"
                                                    custom-style="height: 40px !important;"
                                                    :show-all-clinic="false"
                                                    :disabled="!isChainAdmin || isChainAdminSelf"
                                                    @change="changeClinic"
                                                ></clinic-select>
                                            </div>
                                        </abc-popover>
                                    </abc-form-item>
                                    <span v-else v-abc-title="order.clinicName"></span>
                                </abc-descriptions-item>
                                <abc-descriptions-item
                                    v-if="!isChainAdminDistribution"
                                    content-class-name="ellipsis"
                                    :label="'采购订单'"
                                    :content-style="{ padding: canEditRelationshipOrderNo ? '0px' : '' }"
                                >
                                    <abc-form-item v-if="canEditRelationshipOrderNo" class="relation-form-item">
                                        <abc-popover
                                            ref="purchaseOrderListPopover"
                                            width="1200px"
                                            placement="bottom"
                                            trigger="click"
                                            theme="white"
                                            :popper-style="{
                                                height: '535px',
                                                padding: '24px',
                                                marginTop: '10px'
                                            }"
                                            :control="false"
                                            :resident-popover="showModal"
                                            @show="showPopoverOrderList = true"
                                            @hide="showPopoverOrderList = false"
                                        >
                                            <abc-input
                                                slot="reference"
                                                ref="inputRef"
                                                :value="relationshipOrderNo"
                                                :clearable="isAdd || (canTakeDelivery && isErpDirect)"
                                                :input-custom-style="{
                                                    width: '100%',
                                                    height: '100%',
                                                    pointerEvents: 'none',
                                                    caretColor: 'transparent'

                                                }"
                                                :focus-when-clear="false"
                                                size="large"
                                                style="width: 100%; height: 100%;"
                                                icon="cis-icon-dropdown_triangle"
                                                class="keyboard-navigation-abc-input-ignore enter-key-call-fn-handleOpenPurchaseOrderList"
                                                @clear="handleClear(true)"
                                                @icon-click="handleIconClick"
                                            ></abc-input>
                                            <order-list
                                                ref="orderListRef"
                                                :need-keyboard-navigation="true"
                                                :select-order-no="relatedOrder.orderNo"
                                                :show-clinic-name="isChainAdmin"
                                                :show-clinic-filter="isChainAdmin"
                                                :current-id="order.clinicId"
                                                :order="order"
                                                :is-supplement="isSupplement"
                                                :show-up-layer="showModal"
                                                :show-search="showPopoverOrderListSearch"
                                                :next-input-auto-focus="false"
                                                @select="handleSelectOrderConfirm"
                                                @additional="handleAdditional"
                                                @close="handleClosePopover"
                                            ></order-list>
                                        </abc-popover>
                                    </abc-form-item>
                                    <abc-flex v-else align="center">
                                        <div
                                            v-abc-title:[relationshipOrderNoWidth].ellipsis="relationshipOrderNo || '-'"
                                            style="height: 100%;"
                                            class="show-text-box"
                                        ></div>
                                        <abc-tag-v2 v-if="showPurchaseOrderStatus" v-bind="tagConfig">
                                            {{ statusName }}
                                        </abc-tag-v2>
                                    </abc-flex>
                                    <additional-dialog
                                        v-if="showAdditionalDialog"
                                        v-model="showAdditionalDialog"
                                        :is-erp-direct="isErpDirect"
                                        :supplier-id="order.supplierId"
                                        :source-type="order.additionalPurchaseOrder.purchaseType"
                                        :purchase-by="order.additionalPurchaseOrder.purchaseBy"
                                        :purchase-order-date="order.additionalPurchaseOrder.purchaseOrderDate"
                                        :current-id="order.clinicId"
                                        :claim-clinic-id="order.claimClinicId"
                                        @confirm="handleAdditionalConfirm"
                                    ></additional-dialog>
                                </abc-descriptions-item>

                                <abc-descriptions-item
                                    v-if="!isChainAdminDistribution"
                                    label="供应商"
                                    content-class-name="ellipsis"
                                    :content-style="{ padding: isAdd ? '0px' : '' }"
                                >
                                    <abc-form-item v-if="isAdd" required>
                                        <abc-popover
                                            placement="top"
                                            trigger="hover"
                                            theme="yellow"
                                            :popper-style="{
                                                padding: '0 0 0 0',
                                            }"
                                            :control="false"
                                            :disabled="!(relationshipOrderNo || (!relationshipOrderNo && supplieExpired(order.supplierId)))"
                                        >
                                            <abc-flex v-if="relationshipOrderNo" style="padding: 12px 16px;">
                                                <abc-text v-if="!supplieExpired(order.supplierId)">
                                                    关联订单供应商，不支持修改
                                                </abc-text>
                                                <abc-text v-else>
                                                    供应商资质过期，可在采购订单修改供应商
                                                </abc-text>
                                            </abc-flex>
                                            <abc-divider v-if="relationshipOrderNo && supplieExpired(order.supplierId)" margin="none"></abc-divider>
                                            <div v-if="supplieExpired(order.supplierId)" style="padding: 12px 16px 4px 16px;">
                                                <abc-flex
                                                    v-for="(it, index) in gualificationList(order.supplierId)"
                                                    :key="index"
                                                    style="width: 100%; margin-bottom: 8px;"
                                                    align="center"
                                                    :gap="16"
                                                    justify="space-between"
                                                >
                                                    <abc-text theme="gray">
                                                        {{ it.name }}
                                                    </abc-text>
                                                    <abc-text>已过期</abc-text>
                                                </abc-flex>
                                            </div>
                                            <div slot="reference">
                                                <abc-select
                                                    v-model="order.supplierId"
                                                    custom-class="supplierWrapper"
                                                    with-search
                                                    adaptive-width
                                                    clearable
                                                    :disabled="!!relationshipOrderNo"
                                                    :no-icon="!!relationshipOrderNo"
                                                    placeholder=""
                                                    style="height: 40px !important;"
                                                    inner-width="280px"
                                                    placement="bottom-end"
                                                    size="large"
                                                    :input-style="supplieExpired(order.supplierId) ? {
                                                        color: '#D72E22 !important',
                                                    } : {}"
                                                    :fetch-suggestions="fetchSuggestions"
                                                    clicked-focus-input
                                                    shortcut-mode="controlled"
                                                    @open="supplierListRetry"
                                                    @change="handleSupplierChange"
                                                >
                                                    <abc-option
                                                        v-for="it in supplierOptionsNormal"
                                                        :key="`${it.id }`"
                                                        :value="it.id"
                                                        :label="it.name"
                                                    ></abc-option>
                                                    <abc-option
                                                        v-if="supplierOptionsExpired.length"
                                                        style="align-items: flex-end; height: 24px; min-height: 24px;"
                                                        value="-2"
                                                        disabled
                                                    >
                                                        资质过期
                                                    </abc-option>
                                                    <abc-option
                                                        v-for="it in supplierOptionsExpired"
                                                        :key="`${it.id }`"
                                                        :value="it.id"
                                                        :label="it.name"
                                                    >
                                                        <abc-text theme="danger">
                                                            {{ it.name }}
                                                        </abc-text>
                                                    </abc-option>
                                                </abc-select>
                                            </div>
                                        </abc-popover>
                                    </abc-form-item>
                                    <span v-else v-abc-title="order.supplier?.name || '-'" class="show-text-box"></span>
                                </abc-descriptions-item>

                                <abc-descriptions-item
                                    v-if="!isChainAdminDistribution"
                                    content-class-name="ellipsis"
                                    :content-style="{ padding: isAdd || (canTakeDelivery && !isErpDirect) ? '0px' : '' }"
                                    :label="'供应商销售员'"
                                >
                                    <abc-form-item v-if="isAdd || (canTakeDelivery && !isErpDirect)">
                                        <abc-tooltip :disabled="!!order.supplierId" content="请先选择供应商">
                                            <div>
                                                <seller-select
                                                    :supplier-id="order.supplierId"
                                                    :supplier="order.supplier"
                                                    :seller="order.supplierSeller"
                                                    :only-one-seller-auto-select="true"
                                                    style="height: 100%;"
                                                    clicked-focus-input
                                                    shortcut-mode="controlled"
                                                    close-add-seller-auto-focus
                                                    @change="selectSeller"
                                                ></seller-select>
                                            </div>
                                        </abc-tooltip>
                                    </abc-form-item>
                                    <span v-else v-abc-title="order.supplierSeller?.name || '-'" class="show-text-box"></span>
                                </abc-descriptions-item>

                                <abc-descriptions-item v-if="!isSingleStore && !isChainAdminDistribution" content-class-name="ellipsis" label="类型">
                                    <span v-abc-title="formatSupplierTypeName(order.supplier)" class="show-text-box"></span>
                                </abc-descriptions-item>

                                <template v-if="isChainAdminDistribution">
                                    <abc-descriptions-item content-class-name="ellipsis" :label="'发货门店'">
                                        <span class="show-text-box">总部</span>
                                    </abc-descriptions-item>
                                    <abc-descriptions-item content-class-name="ellipsis" :label="'配货人'">
                                        <span v-abc-title="order?.deliverer?.name" class="show-text-box"></span>
                                    </abc-descriptions-item>
                                </template>

                                <abc-descriptions-item
                                    :label="'随货单号'"
                                    content-class-name="ellipsis"
                                    :content-style="{ padding: isAdd || (canTakeDelivery && !isErpDirect) ? '0px' : '' }"
                                >
                                    <abc-form-item v-if="isAdd || (canTakeDelivery && !isErpDirect)">
                                        <abc-input
                                            v-model="order.outOrderNo"
                                            v-abc-focus-selected
                                            :max-length="50"
                                            size="large"
                                        ></abc-input>
                                    </abc-form-item>
                                    <span v-else v-abc-title="order.outOrderNo || '-'" class="show-text-box"></span>
                                </abc-descriptions-item>

                                <abc-descriptions-item
                                    :label="'收货人'"
                                    content-class-name="ellipsis"
                                    :content-style="{ padding: isAdd || canTakeDelivery ? '0px' : '' }"
                                >
                                    <abc-form-item v-if="isAdd || canTakeDelivery" required>
                                        <employee-select
                                            v-model="order.receiveBy"
                                            :inner-width="160"
                                            :employee-list="currentStockEmployeeList"
                                            clicked-focus-input
                                            shortcut-mode="controlled"
                                            adaptive-width
                                            @change="handleReceiverChange"
                                        >
                                        </employee-select>
                                    </abc-form-item>
                                    <span v-else v-abc-title="order.receiver?.name ?? '-'" class="show-text-box"></span>
                                </abc-descriptions-item>

                                <abc-descriptions-item :label="'收货日期'" content-class-name="ellipsis">
                                    <span v-abc-title="order.receiveTime || '-'" class="show-text-box"></span>
                                </abc-descriptions-item>

                                <abc-descriptions-item
                                    label="备注"
                                    content-class-name="ellipsis"
                                    :content-style="{ padding: isAdd || canTakeDelivery || edit ? '0px' : '' }"
                                >
                                    <abc-form-item v-if="isAdd || canTakeDelivery || edit">
                                        <abc-input
                                            ref="orderRemark"
                                            v-model="order.comment"
                                            v-abc-focus-selected
                                            size="large"
                                            style="height: 40px;"
                                            :max-length="200"
                                        ></abc-input>
                                    </abc-form-item>
                                    <span v-else v-abc-title="order.comment || '-'" class="show-text-box"></span>
                                </abc-descriptions-item>
                            </abc-descriptions>
                            <abc-space direction="vertical">
                                <abc-button v-if="!edit" type="text" @click="handleUpdate">
                                    修改
                                </abc-button>
                                <template v-else>
                                    <abc-button
                                        variant="text"
                                        size="small"
                                        :loading="saveBtnLoading"
                                        @click="updateOrderInfo"
                                    >
                                        保存
                                    </abc-button>

                                    <abc-button
                                        variant="text"
                                        theme="default"
                                        size="small"
                                        :disabled="saveBtnLoading"
                                        @click="cancelUpdate"
                                    >
                                        取消
                                    </abc-button>
                                </template>
                            </abc-space>
                        </abc-popover>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        class="focus-table"
                        :support-delete-tr="!isChainAdminDistribution && (isAdd || canTakeDelivery)"
                        need-delete-confirm
                        :delete-support-shortcut="true"
                        delete-icon-title="删除 Alt + Del"
                        :show-hover-tr-bg="false"
                        :loading="tableLoading"
                        :render-config="renderConfig"
                        :data-list="order.list"
                        empty-size="small"
                        :need-selected="true"
                        :custom-tr-key="createTrKey"
                        :custom-tr-class="createTrClass"
                        :virtual-list-config="{
                            bufferSize: 10,
                            threshold: 30,
                            rowHeight: 41
                        }"
                        @delete-tr="handleDeleteTr"
                    >
                        <template v-if="isAdd && !hasMallOrderId" #topHeader>
                            <goods-auto-complete-cover-title
                                ref="goodsAutoCompleteRef"
                                class="entry-medicine back-focus-to-autocomplete"
                                placeholder="输入商品名称或扫码添加"
                                :search="searchKey"
                                :focus-show="true"
                                show-last-supplier
                                :enable-local-search="false"
                                :enable-barcode-detector="enableBarcodeDetector"
                                :width="460"
                                :inorder-config="0"
                                show-empty
                                is-close-validate
                                :next-input-auto-focus="false"
                                :resident-sugguestions="enableBarcodeDetector"
                                @selectGoods="selectGoods"
                                @clearTraceCode="clearTraceCode"
                                @focus="focusGoodsAutoComplete"
                                @traceableCodeEnter="traceableCodeEnter"
                            >
                                <abc-icon slot="prepend" icon="n-add-line-medium" color="var(--abc-color-T3)"></abc-icon>
                                <template #appendInner>
                                    <abc-text style="color: var(--abc-color-T3);">
                                        F2
                                    </abc-text>
                                </template>
                                <div
                                    slot="fixed-footer"
                                    class="inventory__fixed-footer-wrapper"
                                    @click.stop=""
                                >
                                    <add-archive-dropdown
                                        :is-show-toast="false"
                                        :success-callback="selectGoods"
                                        @openAddDialog="addArchiveDialogVisible = true"
                                        @closeAddDialog="addArchiveDialogVisible = false"
                                    ></add-archive-dropdown>
                                </div>
                            </goods-auto-complete-cover-title>
                        </template>
                        <template #shortId="{ trData: row }">
                            <abc-table-cell v-if="row.notMatched || !row.goods">
                                <abc-flex v-if="isErpDirect" align="baseline" :gap="4">
                                    <abc-text theme="warning-light">
                                        未绑定
                                    </abc-text>
                                    <abc-tooltip placement="top" :content="isSingleStore ? '未与供应商ERP商品绑定，请绑定商品或建档后收货' : '未与供应商ERP商品绑定，请联系总部绑定或建档后收货'">
                                        <abc-icon size="14" icon="Attention"></abc-icon>
                                    </abc-tooltip>
                                </abc-flex>
                                <abc-text v-else theme="warning-light">
                                    未匹配
                                </abc-text>
                            </abc-table-cell>

                            <abc-table-cell v-else class="ellipsis">
                                <overflow-tooltip :content="row.goods?.shortId ?? '-'">
                                </overflow-tooltip>
                            </abc-table-cell>
                        </template>

                        <template
                            #displayName="{
                                trData: item, index
                            }"
                        >
                            <display-name-cell
                                v-if="currentEditIndex !== getRealIndex(item) && item.goods"
                                :goods="item.goods"
                                :hover-config="{
                                    showF3: (isChainAdmin || isSingleStore) && canEdit, openDelay: 500, goodDetailsConfig: goodDetailsConfig
                                }"
                                :class="{ 'cadn-hover': canEdit }"
                                :vertical="false"
                                @updateGoods="updateGoods(item, index)"
                                @click.stop.native="editCurrentGoodsItem(item, index)"
                            ></display-name-cell>

                            <div
                                v-else-if="currentEditIndex !== getRealIndex(item) && !item.goods"
                                style="width: 100%; height: 100%;"
                                @click.stop="editCurrentGoodsItem(item, index)"
                            >
                                <abc-form-item :validate-event="handleValidateKey(item)" style="width: 100%;">
                                    <abc-input
                                        :value="item.searchGoodsKey "
                                        size="medium"
                                        style="width: 100%; height: 100%;"
                                        :disabled="!canEdit"
                                        :input-custom-style="{
                                            'padding': '8px',
                                            'color': `var(--abc-color-Y2)`
                                        }"
                                    ></abc-input>
                                </abc-form-item>
                            </div>
                            <!--                    处理导入的随货同行单，可以搜索替换药品-->
                            <div v-else style="width: 100%; height: 100%;">
                                <abc-form-item :validate-event="handleValidateKey(item)" style="width: 100%;" @click="editCurrentGoodsItem(item, index)">
                                    <goods-auto-complete-cover-title
                                        class="entry-medicine inventory-replace-goods-autocomplete-wrapper"
                                        style="width: 100%; height: 100%;"
                                        :search.sync="item.searchGoodsKey"
                                        :enable-barcode-detector="false"
                                        :inorder-config="0"
                                        size="medium"
                                        show-empty
                                        resident-sugguestions
                                        enable-local-search
                                        format-count-key="out"
                                        focus-show
                                        :close-on-click-outside="() => handleResetSearchKey(item)"
                                        @selectGoods="(goods) => selectReplaceGoods(goods, item)"
                                    >
                                        <div
                                            v-if="isAdmin || item.rowMatchInfo"
                                            slot="fixed-footer"
                                            class="inventory__fixed-footer-wrapper"
                                            @click.stop=""
                                        >
                                            <add-archive-dropdown
                                                ref="addGoodsDropdownRef"
                                                is-custom-change
                                                @change="(type) => handleAddNewGoods(type, item)"
                                            ></add-archive-dropdown>
                                            <abc-flex
                                                v-if="item.rowMatchInfo"
                                                align="center"
                                                inline
                                                class="ellipsis"
                                                style="margin-left: auto; color: var(--abc-color-T2);"
                                            >
                                                <span>{{ item.rowMatchInfo.ERPLabel ? item.rowMatchInfo.ERPLabel : '随货同行单商品：' }}</span>

                                                <abc-flex align="center" :gap="4" inline>
                                                    <span style="max-width: 100px;" class="ellipsis" :title="item.rowMatchInfo.supplierGoodsId">{{ item.rowMatchInfo.supplierGoodsId }}</span>
                                                    <span style="max-width: 140px;" class="ellipsis" :title="item.rowMatchInfo.medicineCadn">{{ item.rowMatchInfo.medicineCadn }}</span>
                                                    <span style="max-width: 100px;" class="ellipsis" :title="item.rowMatchInfo.specification">
                                                        {{ item.rowMatchInfo.specification }}
                                                    </span>
                                                    <span style="max-width: 90px;" class="ellipsis" :title="item.rowMatchInfo.manufacturerFull">
                                                        {{ item.rowMatchInfo.manufacturerFull }}
                                                    </span>
                                                </abc-flex>
                                            </abc-flex>
                                        </div>
                                    </goods-auto-complete-cover-title>
                                </abc-form-item>
                            </div>
                        </template>
                        <template #sellPrice="{ trData: row }">
                            <abc-table-cell v-if="row.goods" :theme="isNull(row.goods.packagePrice) ? 'warning' : 'default'">
                                <div v-if="isNull(row.goods.packagePrice)" key="un-fix-price">
                                    未定价
                                </div>
                                <div
                                    v-else-if="isChineseMedicine(row.goods)"
                                >
                                    <span v-abc-title.ellipsis="`${moneyDigit(row.goods.piecePrice, 5)}/${row.goods.pieceUnit}`"></span>
                                </div>
                                <div
                                    v-else
                                >
                                    <span v-abc-title.ellipsis="`${moneyDigit(row.goods.packagePrice, 5)}/${row.goods.packageUnit}`"></span>
                                </div>
                            </abc-table-cell>
                            <abc-table-cell v-else>
                                -
                            </abc-table-cell>
                        </template>

                        <template #purchaseCount="{ trData: row }">
                            <template v-if="row.goods">
                                <abc-table-cell
                                    v-if="order.additionalFlag"
                                >
                                    <span>{{ row.receiveCount || 0 }}{{ row.receiveUnit }}</span>
                                </abc-table-cell>
                                <abc-table-cell
                                    v-else
                                >
                                    <span
                                        v-abc-title.ellipsis="complexCount({
                                            ...row.goods,
                                            packageCount: row.purchasePackageCount || 0,
                                            pieceCount: row.purchasePieceCount || 0,
                                        })"
                                    ></span>
                                </abc-table-cell>
                            </template>

                            <abc-table-cell v-else>
                                <span>-</span>
                            </abc-table-cell>
                        </template>

                        <template #distributionCount="{ trData: row }">
                            <abc-table-cell
                                v-if="row.goods"
                                v-abc-title.ellipsis="complexCount({
                                    ...row.goods,
                                    packageCount: row.deliveryPackageCount || 0,
                                    pieceCount: row.deliveryPieceCount || 0,
                                })"
                            ></abc-table-cell>
                            <abc-table-cell v-else>
                                <span>-</span>
                            </abc-table-cell>
                        </template>

                        <template #receivePackageCount="{ trData: row }">
                            <abc-form-item v-if="canEdit" required>
                                <abc-input
                                    v-model.number="row.receiveCount"
                                    v-abc-focus-selected
                                    type="number"
                                    :config="{
                                        max: 9999999,
                                        supportZero: false,
                                        formatLength: getFormatLength(row, 'receiveUnit')
                                    }"
                                    class="focus-input"
                                    @change="calTotalPrice(row)"
                                ></abc-input>
                            </abc-form-item>
                            <template v-else>
                                <abc-table-cell v-abc-title.ellipsis="row.receiveCount ?? ''"></abc-table-cell>
                            </template>
                        </template>

                        <template #receivePackageCostPrice="{ trData: row }">
                            <abc-form-item v-if="canEdit && row.goods" required>
                                <price-fluctuation-popover
                                    :use-goods-config="true"
                                    :use-cost-price="row.receivePackageCostPrice"
                                    :last-price="row.goods.lastPackageCostPrice"
                                    :goods="row.goods"
                                    :use-unit="row.receiveUnit"
                                    :clinic-id="order.clinicId"
                                    :update-price-source-type="PriceModifySourceType.QUICK_MODIFY_PURCHASE"
                                    :show-history-cost-price="false"
                                    @changePrice="changePrice($event, row)"
                                >
                                    <template
                                        #default="{
                                            isRise, showIcon
                                        }"
                                    >
                                        <abc-input
                                            v-model="row.receivePackageCostPrice"
                                            v-abc-focus-selected
                                            :disabled="!canEdit"
                                            type="money"
                                            :config="getCostConfig({
                                                ...row.goods,
                                                purchaseUnit: row.receiveUnit
                                            })"
                                            :input-custom-style="{ textAlign: 'right' }"
                                            @change="calTotalPrice(row)"
                                            @focus="receivePackageCostPriceFocus(row, $event)"
                                            @blur="receivePackageCostPriceBlur(row)"
                                        >
                                            <span v-if="showIcon" slot="prepend">
                                                <abc-icon v-if="isRise" icon="arrow_up" color="var(--abc-color-R2)"></abc-icon>
                                                <abc-icon v-else icon="arrow_down" color="var(--abc-color-G2)"></abc-icon>
                                            </span>
                                        </abc-input>
                                    </template>
                                </price-fluctuation-popover>
                            </abc-form-item>
                            <div v-else style="height: 100%;">
                                <price-fluctuation-popover
                                    show-history-cost-price
                                    :use-goods-config="true"
                                    :use-cost-price="row.receivePackageCostPrice"
                                    :last-price="row.goods?.lastPackageCostPrice"
                                    :goods="row.goods"
                                    :use-unit="row.receiveUnit"
                                    :clinic-id="order.clinicId"
                                    :update-price-source-type="PriceModifySourceType.QUICK_MODIFY_PURCHASE"
                                >
                                    <template
                                        #default="{
                                            isRise, showIcon
                                        }"
                                    >
                                        <abc-input
                                            v-model="row.receivePackageCostPrice"
                                            v-abc-focus-selected
                                            readonly
                                            disabled
                                            type="money"
                                            :config="getCostConfig({
                                                ...row.goods,
                                                purchaseUnit: row.receiveUnit
                                            })"
                                            :input-custom-style="{ textAlign: 'right' }"
                                            :title="formatMoney(row.receivePackageCostPrice, false)"
                                        >
                                            <span v-if="showIcon" slot="prepend">
                                                <abc-icon v-if="isRise" icon="arrow_up" color="var(--abc-color-R2)"></abc-icon>
                                                <abc-icon v-else icon="arrow_down" color="var(--abc-color-G2)"></abc-icon>
                                            </span>
                                        </abc-input>
                                    </template>
                                </price-fluctuation-popover>
                            </div>
                        </template>

                        <template #receiveUnit="{ trData: row }">
                            <abc-form-item v-if="canEdit && row.goods" required>
                                <abc-select
                                    :key="row.goodsId"
                                    v-model="row.receiveUnit"
                                    :inner-width="56"
                                    adaptive-width
                                    :input-style="{
                                        'text-align': 'center'
                                    }"
                                    :disabled="getUnits(row.goods)?.length === 1"
                                    clicked-focus-input
                                    shortcut-mode="controlled"
                                    @change="unitChange(row)"
                                >
                                    <abc-option
                                        v-for="unit in getUnits(row.goods)"
                                        :key="unit"
                                        :label="unit"
                                        :value="unit"
                                        center
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-table-cell v-else>
                                <span v-abc-title.ellipsis="row.receiveUnit || '-'"></span>
                            </abc-table-cell>
                        </template>

                        <template #receiveTotalCost="{ trData: row }">
                            <abc-form-item v-if="canEdit" required>
                                <abc-input
                                    v-model="row.receiveTotalCost"
                                    v-abc-focus-selected
                                    type="money"
                                    :config="{
                                        formatLength: 2, max: 10000000, supportZero: true
                                    }"
                                    :input-custom-style="{ textAlign: 'right' }"
                                    @change="calCostPrice(row)"
                                    @focus="setDisabledScanBarcode(true)"
                                    @blur="setDisabledScanBarcode(false)"
                                ></abc-input>
                            </abc-form-item>

                            <abc-table-cell v-else v-abc-title.ellipsis="formatMoney(row.receiveTotalCost, false)"></abc-table-cell>
                        </template>

                        <template #supplierName="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <overflow-tooltip :content="row?.batchInfo?.supplierName || '-'">
                                </overflow-tooltip>
                            </abc-table-cell>
                        </template>

                        <template #batchNo="{ trData: row }">
                            <abc-form-item v-if="singleRowCanEdit(row) || (canEdit && !canChainAdminSave)" :required="!isOtherGoods(row.goods)">
                                <abc-input
                                    v-model="row.batchNo"
                                    v-abc-focus-selected
                                    :max-length="20"
                                    @focus="setDisabledScanBarcode(true)"
                                    @blur="setDisabledScanBarcode(false)"
                                ></abc-input>
                            </abc-form-item>
                            <abc-table-cell v-else v-abc-title.ellipsis="row.batchNo ?? ''"></abc-table-cell>
                        </template>

                        <template #productionDate="{ trData: row }">
                            <abc-form-item
                                v-if="singleRowCanEdit(row) || (canEdit && !canChainAdminSave)"
                                :validate-event="validateProductionDate"
                            >
                                <abc-tooltip style="height: 100%;" :disabled="!getProductDateTooltip(row)" content="生产日期不可选择未来时间">
                                    <abc-date-picker
                                        v-model="row.productionDate"
                                        type="datequick"
                                        :class="{ 'tool-tip-price': getProductDateTooltip(row) }"
                                        :prevent-direction-navigation="false"
                                        editable
                                        placeholder=""
                                        adaptive-width
                                        shortcut-mode="controlled"
                                        :focus-show-options="false"
                                    >
                                    </abc-date-picker>
                                </abc-tooltip>
                            </abc-form-item>
                            <abc-table-cell v-else v-abc-title.ellipsis="row.productionDate ?? ''"></abc-table-cell>
                        </template>

                        <template #expiryDate="{ trData: row }">
                            <abc-form-item
                                v-if="singleRowCanEdit(row) || (canEdit && !canChainAdminSave)"
                                :validate-event="validateExpiryDate"
                                :required="row.goods && !isChineseMedicine(row.goods)"
                            >
                                <abc-tooltip style="height: 100%;" :disabled="!getExpiryDateTooltip(row)" :content="getExpiryDateTooltip(row)">
                                    <abc-date-picker
                                        v-model="row.expiryDate"
                                        type="datequick"
                                        :class="{ 'tool-tip-price': getExpiryDateTooltip(row) }"
                                        :prevent-direction-navigation="false"
                                        editable
                                        placeholder=""
                                        adaptive-width
                                        clicked-focus-input
                                        shortcut-mode="controlled"
                                        :focus-show-options="false"
                                    >
                                    </abc-date-picker>
                                </abc-tooltip>
                            </abc-form-item>
                            <abc-table-cell v-else v-abc-title.ellipsis="row.expiryDate ?? ''"></abc-table-cell>
                        </template>

                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item
                            }"
                        >
                            <traceable-code-cell
                                :ref="`traceableCodeCell-${item.keyId}`"
                                v-model="item.traceableCodeList"
                                :goods.sync="item.goods"
                                :item="item"
                                :goods-count="{
                                    isTrans: item._isTransformable,
                                    maxCount: item._maxTraceCodeCount,
                                    unitCount: item.receiveCount,
                                    unit: item.receiveUnit,
                                    countLabel: '收货数量',
                                    label: '收货数量',
                                }"
                                :scene-type="SceneTypeEnum.GOODS_TAKE"
                                :need-validate="validateCell"
                                :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                                :readonly="!canEdit || canChainAdminSave"
                                need-validate-drug-identification-code
                                @updateGoodsTraceableCodeNoInfo="updateGoodsTraceableCodeNoInfo"
                                @show="updateTraceableCodePopoverShowId(item.keyId, item)"
                                @hide="updateTraceableCodePopoverShowId('', item)"
                            ></traceable-code-cell>
                        </template>
                        <!--操作-->
                        <template
                            #operate="{
                                trData: row, index
                            }"
                        >
                            <abc-table-cell style="padding: 8px 0;">
                                <abc-space v-if="row.id === currentModifyGoodsItemId" :size="2">
                                    <abc-button
                                        variant="text"
                                        size="small"
                                        min-width="38"
                                        @click="saveRow(row, index)"
                                    >
                                        保存
                                    </abc-button>
                                    <abc-button
                                        variant="text"
                                        size="small"
                                        min-width="38"
                                        theme="default"
                                        @click="cancelRow(row, index)"
                                    >
                                        取消
                                    </abc-button>
                                </abc-space>
                                <abc-button
                                    v-else
                                    min-width="38"
                                    size="small"
                                    variant="text"
                                    :disabled="onlyView"
                                    @click="editRow(row)"
                                >
                                    修改
                                </abc-button>
                            </abc-table-cell>
                        </template>

                        <template #footer>
                            <abc-flex
                                flex="1"
                                align="center"
                                justify="flex-end"
                                style="padding-right: 12px;"
                            >
                                <abc-flex flex="1" align="center" justify="flex-end">
                                    <abc-space :size="4">
                                        <abc-text theme="gray">
                                            品种
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ isAdd ? computedOrder.kindCount : order.kindCount }}
                                        </abc-text>
                                        <abc-text theme="gray">
                                            ，数量
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ isAdd ? computedOrder.count : order.count }}
                                        </abc-text>
                                        <abc-text theme="gray">
                                            ，含税金额
                                        </abc-text>
                                        <abc-text theme="black">
                                            {{ isAdd ? formatMoney(computedOrder.amount,false) : formatMoney(order.amount,false) }}
                                        </abc-text>
                                    </abc-space>
                                </abc-flex>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <template v-if="onlyView" #footer>
            <abc-flex align="center" justify="flex-end">
                <abc-button
                    variant="ghost"
                    @click="handleCancel"
                >
                    关闭
                </abc-button>
            </abc-flex>
        </template>

        <template v-else #footer>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-button
                        v-if="isDraft"
                        :loading="deleteDraftBtnLoading"
                        :disabled="pageLoading || submitBtnLoading || confirmBtnLoading || draftBtnLoading"
                        type="danger"
                        @click="deleteDraftOrder"
                    >
                        删除草稿
                    </abc-button>
                    <abc-button
                        v-if="hasMallOrderId"
                        variant="ghost"
                        @click="handleOpenMallOrderDetail"
                    >
                        查看商城订单
                    </abc-button>
                    <abc-tooltip
                        v-if="showRecordBtn"
                        placement="top"
                        :disabled="!isDraft && !isAdd"
                        theme="black"
                    >
                        <template #content>
                            <abc-text style="color: var(--abc-color-T5);">
                                {{ isChainAdminReadChainStore ? '查看运输记录&nbsp;&nbsp;F8' : '运输记录&nbsp;&nbsp;F8' }}
                            </abc-text>
                        </template>
                        <abc-button
                            variant="ghost"
                            :disabled="disableRecordBtn"
                            @click="openOrderDialog"
                        >
                            {{ isChainAdminReadChainStore ? '查看运输记录' : '运输记录' }}
                        </abc-button>
                    </abc-tooltip>
                    <logs-v3-popover v-if="logs.length" style="margin-left: 8px;" :logs="logs"></logs-v3-popover>
                </abc-space>
                <abc-space>
                    <template v-if="isAdd || canTakeDelivery">
                        <abc-tooltip
                            placement="top"
                            theme="black"
                        >
                            <template #content>
                                <abc-text style="color: var(--abc-color-T5);">
                                    一键收货/验收/入库&nbsp;&nbsp;Ctrl + Alt + S
                                </abc-text>
                            </template>
                            <abc-button
                                icon="n-flash-fill"
                                :loading="submitBtnLoading"
                                :disabled="!order.list.length || confirmBtnLoading || draftBtnLoading"
                                @click="handleSubmitBefore(ReceiveActionType.FINISH)"
                            >
                                一键收货/验收/入库
                            </abc-button>
                        </abc-tooltip>

                        <abc-tooltip
                            placement="top"
                            theme="black"
                        >
                            <template #content>
                                <abc-text style="color: var(--abc-color-T5);">
                                    确定收货&nbsp;&nbsp;Alt + S
                                </abc-text>
                            </template>
                            <abc-button
                                :loading="confirmBtnLoading"
                                :disabled="!order.list.length || submitBtnLoading || draftBtnLoading"
                                @click="handleSubmitBefore(ReceiveActionType.CONFIRM)"
                            >
                                确定收货
                            </abc-button>
                        </abc-tooltip>

                        <abc-tooltip
                            v-if="isAdd"
                            placement="top"
                            theme="black"
                        >
                            <template #content>
                                <abc-text style="color: var(--abc-color-T5);">
                                    保存草稿&nbsp;&nbsp;F4
                                </abc-text>
                            </template>
                            <abc-button
                                :loading="draftBtnLoading"
                                :disabled="pageLoading || disabledDraftBtn || submitBtnLoading || confirmBtnLoading || deleteDraftBtnLoading"
                                variant="ghost"
                                @click="handleSubmit(ReceiveActionType.DRAFT)"
                            >
                                保存草稿
                            </abc-button>
                        </abc-tooltip>
                    </template>

                    <abc-button
                        v-if="canChainAdminSave"
                        :loading="buttonLoading"
                        :disabled="disabledDraftBtn || !order.list.length || submitBtnLoading || confirmBtnLoading || draftBtnLoading"
                        variant="ghost"
                        @click="handleSubmitBefore(ReceiveActionType.SAVE)"
                    >
                        保存
                    </abc-button>

                    <abc-button
                        v-if="canExport"
                        variant="ghost"
                        :disabled="pageLoading"
                        @click="handleExport"
                    >
                        导出
                    </abc-button>

                    <abc-button
                        variant="ghost"
                        @click="closeDialog"
                    >
                        {{ closeText }}
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>

        <abc-dialog
            v-if="showReceiveDialog"
            v-model="showReceiveDialog"
            :title="receiveTitle"
            content-styles="width:560px;"
            append-to-body
            @open="handleReceiveDialogOpen"
            @close="handleReceiveDialogClose"
        >
            <abc-layout>
                <abc-section v-if="showTips">
                    <abc-tips-card-v2 theme="primary" border-radius>
                        {{ receiveDialogTips }}
                    </abc-tips-card-v2>
                </abc-section>
                <abc-section>
                    <abc-descriptions
                        :column="1"
                        size="large"
                        :label-width="96"
                        grid
                    >
                        <template v-if="!isChainAdminDistribution">
                            <abc-descriptions-item :label="'采购订单'">
                                <span v-abc-title.ellipsis="relationshipOrderNo || '-'" class="show-text-box"></span>
                            </abc-descriptions-item>
                            <abc-descriptions-item :label="'供应商'">
                                <span v-abc-title.ellipsis="order.supplier?.name ?? '-'" class="show-text-box"></span>
                            </abc-descriptions-item>
                            <abc-descriptions-item v-if="!isSingleStore" :label="'类型'">
                                <span v-abc-title.ellipsis="formatSupplierTypeName(order.supplier)" class="show-text-box"></span>
                            </abc-descriptions-item>
                        </template>
                        <abc-descriptions-item :label="'收货门店'">
                            <span v-abc-title.ellipsis="isAdd ? orderClinicName : order.clinicName" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'收货品种'">
                            <span v-abc-title.ellipsis="computedOrder.kindCount" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'收货金额'">
                            <span v-abc-title.ellipsis="formatMoney(computedOrder.amount, false)" class="show-text-box"></span>
                        </abc-descriptions-item>

                        <abc-descriptions-item v-if="receiveType === ReceiveActionType.CONFIRM" :label="'收货人'">
                            <span v-abc-title.ellipsis="order.receiver?.name ?? '-'" class="show-text-box"></span>
                        </abc-descriptions-item>

                        <abc-descriptions-item v-else :label="'验收结果'">
                            <span v-abc-title.ellipsis="'合格'" class="show-text-box" :style="{ color: $store.state.theme.style.G1 }"></span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-section>
                <abc-section v-if="receiveType === ReceiveActionType.FINISH">
                    <abc-form
                        ref="confirm-form"
                        :label-width="60"
                        label-position="left"
                        item-no-margin
                    >
                        <abc-flex gap="large">
                            <abc-form-item label="收货人" required hidden-red-dot>
                                <employee-select v-model="order.receiveBy" :employee-list="currentStockEmployeeList">
                                </employee-select>
                            </abc-form-item>
                            <abc-form-item label="验收人" required hidden-red-dot>
                                <employee-select v-model="order.inspectBy" :employee-list="currentStockEmployeeList">
                                </employee-select>
                            </abc-form-item>
                            <abc-form-item label="入库人" required hidden-red-dot>
                                <employee-select v-model="order.stockInBy" :employee-list="currentStockEmployeeList">
                                </employee-select>
                            </abc-form-item>
                        </abc-flex>
                    </abc-form>
                </abc-section>
            </abc-layout>

            <template slot="footer">
                <div class="dialog-footer">
                    <abc-space>
                        <abc-button
                            :loading="buttonLoading"
                            @click="handleSubmit(receiveType)"
                        >
                            确定
                        </abc-button>
                        <abc-button
                            type="blank"
                            @click="handleConfirmCancel"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </div>
            </template>
        </abc-dialog>

        <add-goods-archives-dialog
            v-if="addMedicineDialogVisible"
            v-model="addMedicineDialogVisible"
            :type-id="currentTypeId"
            :is-continue="false"
            :pharmacy-type="pharmacyType"
            :supplier-id="bindGoodsParams.supplierId"
            :supplier-goods-id="bindGoodsParams.supplierGoodsId"
            :goods-info="importGoodsInfo"
            @updateList="handleUpdateList"
            @success="handleSuccessAdd"
            @close="closeDialogHandle"
        >
        </add-goods-archives-dialog>
        <transport-record-dialog
            v-if="showTransportRecordDialog"
            v-model="showTransportRecordDialog"
            :transport-record-id="order && order.transportRecordId || ''"
            :order="order"
            :order-id="orderId"
            :order-clinic-name="orderClinicName"
            :clinic-employees="clinicEmployees"
            :computed-order="computedOrder"
            :can-edit="!isChainAdminReadChainStore"
            :is-draft="isDraft"
            :source-type="0"
            @refresh="refresh"
        ></transport-record-dialog>
        <bind-goods-dialog
            v-if="showBindGoodsDialog"
            v-model="showBindGoodsDialog"
            :op-type="1"
            :goods="bindGoodsParams.goods"
            :bound-supplier-goods="bindGoodsParams"
        ></bind-goods-dialog>
    </frame-dialog>
</template>

<script>
    import Big from 'big.js';
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';
    import {
        PurchaseReceiveOrderStatus,
        PurchaseReceiveOrderStatusName,
        ReceiptOrginOrderType,
        SourceTypeToOrderType,
        PurchaseReceiveOrderStatusTagTheme,
        // RelatedOrderType,
        BIT_FLAG_PURCHASE_APPLY_CHAIN_ENTRUST_DELIVERY,
        BIT_FLAG_PURCHASE_APPLY_CLINIC_SELF, RelatedOrderType, EntrustDeliveryType,
    } from '@/views-pharmacy/inventory/constant';
    const GoodsAutoCompleteCoverTitle = () => import('views/inventory/common/goods-auto-complete-cover-title.vue');

    import GoodsBaseAPI from 'api/goods';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import { mapGetters } from 'vuex';
    import {
        CHECK_IN_SUPPLIER_ID,
    } from 'views/inventory/constant';
    import {
        complexCount, formatMoney, isChineseMedicine, clinicName as formatClinicName,
    } from '@/filters';
    import { formatDate } from '@abc/utils-date';
    import {
        createGUID,
        isNotNull,
        isNull, moneyDigit,
    } from '@/utils';
    import {
        checkExpiryDate, showExpiryDateTip, showOverdueTip, showProductionDateTip, validateExpirationTime,
    } from 'views/inventory/goods-in/common';
    import EnterEvent from 'views/common/enter-event';
    import EmployeeSelect from '@/views-pharmacy/inventory/frames/components/employee-select.vue';
    import OrderList from '@/views-pharmacy/inventory/frames/purchase/require-goods/order-list.vue';
    // import GoodsCountInput from '@/views-pharmacy/inventory/frames/components/goods-count-input.vue';
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
    } from '@abc/constants';
    import { unitEqual } from 'views/inventory/goods-utils';
    import {
        autoFocus,
        calCostPriceSingle,
        calCostPriceTotal,
        getCostConfig,
        getFormatLength,
        getSourceOrder,
        getSourceOrderText,
        getUserLastTypeId,
        getUserGoodsUnit,
        setUserGoodsUnit,
        formatSupplierTypeName,
    } from '@/views-pharmacy/inventory/utils';

    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import {
        clone, isEqual,
    } from '@abc/utils';
    import { parseTime } from '@/utils';
    import AdditionalDialog from './additionalDialog.vue';
    const AddGoodsArchivesDialog = () => import('views/inventory/goods/archives/add.vue');
    const BindGoodsDialog = () => import('views/inventory/goods/supplier-goods-bind/dialog.vue');
    // const ViewCostPriceDialog = () => import('views/inventory/common/view-cost-price-dialog.vue');

    import TransportRecordDialog from '@/views-pharmacy/gsp/frames/check-accept/transport-record/order-dialog';
    import GspAPI from '@/api/pharmacy/gsp';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    import BusinessGoods from '@/views-pharmacy/inventory/core/goods';
    import TraceCode, {
        SceneTypeEnum,
        TraceCodeScenesEnum,
        TraceableCodeTypeEnum,
    } from '@/service/trace-code/service';
    import Clone from 'utils/clone';
    import TraceCodeSelectGoodsDialog from '@/service/trace-code/dialog-trace-code-select-goods';
    import TraceCodeRepeatDialog from '@/service/trace-code/dialog-repeat-trace-code';
    import TraceCodeWarningDialog from '@/service/trace-code/dialog-trace-code-warning';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';
    import { PriceModifySourceType } from 'views/common/inventory/constants';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    import ClinicAPI from 'api/clinic';
    import { checkHasAbcDialog } from '@/utils';
    import useKeyboardNavigation from '@/hooks/base/use-keyboard-navigation';
    import Logger from 'utils/logger';

    const ReceiveActionType = Object.freeze({
        DRAFT: 0,
        CONFIRM: 1,
        FINISH: 2,
        SAVE: 3,
    });
    export default {
        name: 'OrderDialog',
        components: {
            ClinicSelect,
            // GoodsCountInput,
            EmployeeSelect,
            GoodsAutoCompleteCoverTitle,
            FrameDialog,
            OrderList,
            AdditionalDialog,
            AddGoodsArchivesDialog,
            TransportRecordDialog,
            DisplayNameCell,
            LogsV3Popover,
            BindGoodsDialog,
            AddArchiveDropdown: () => import('views/inventory/goods/components/add-archive-dropdown.vue'),
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
            OverflowTooltip: () => import('@/components/overflow-tooltip.vue'),
            PriceFluctuationPopover: () => import('@/views-pharmacy/inventory/frames/components/price-fluctuation-popover.vue'),
            SellerSelect: () => import('@/views-pharmacy/inventory/frames/supplier/components/seller-select.vue'),
        },
        mixins: [EnterEvent],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: Boolean,
            orderId: String,
            orderStatus: {
                type: [Number, String],
                default: '',
            },
            // 随货单数据
            importOrderData: {
                type: Object,
                default: null,
            },
            // 采购订单一键导入
            purchaseOrder: {
                type: Object,
                default: null,
            },
            mallOrderId: {
                type: String,
                default: '',
            },
            mallOrderRefresh: Function,
            onlyView: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager();

            const {
                currentSupplierList,
                fetchSuggestions,
                supplierListRetry,
                findSupplier,
                initSupplierList,
            } = useSearchSupplier({
                status: 1,
                excludeInitSupplier: true,
            });

            const {
                setDisabledScanBarcode,
                isDisabledScanBarcode,
            } = useBarcodeScanner();

            const {
                initKeyboardNavigation,
                initKeyboardNavigationCallback,
                handleChangePosition,
                updateCurrentPosition,
                closeNavigationPanel,
                bindKeyboardNavigationCallFn,
            } = useKeyboardNavigation();

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                currentSupplierList,
                initSupplierList,
                fetchSuggestions,
                supplierListRetry,
                findSupplier,

                setDisabledScanBarcode,
                isDisabledScanBarcode,

                initKeyboardNavigation,
                initKeyboardNavigationCallback,
                handleChangePosition,
                updateCurrentPosition,
                closeNavigationPanel,
                bindKeyboardNavigationCallFn,
            };
        },
        data() {
            return {
                SceneTypeEnum,
                ReceiveActionType,
                CHECK_IN_SUPPLIER_ID,
                currentModifyGoodsItemId: '',
                saveRowLoading: false,
                cacheCurrentModifyGoodsItem: null,
                edit: false,
                saveBtnLoading: false,
                showDialog: this.value,
                pageLoading: false,
                tableLoading: false,
                submitBtnLoading: false,
                confirmBtnLoading: false,
                draftBtnLoading: false,
                buttonLoading: false,
                deleteDraftBtnLoading: false,
                showReceiveDialog: false,
                showAdditionalDialog: false,
                receiveType: ReceiveActionType.CONFIRM,
                searchKey: '',
                currentTypeId: '',
                currentEditIndex: -1,
                currentGoodsIndex: -1,
                addMedicineDialogVisible: false,
                addArchiveDialogVisible: false,
                showBindGoodsDialog: false,
                // 绑定商品参数
                bindGoodsParams: {
                    goods: {},
                    boundSupplierGoods: null,
                    supplierId: '',
                    supplierGoodsId: '',
                },
                // 新建档案默认填充的商品信息
                importGoodsInfo: {},
                cacheOrder: null,
                initClinicName: false,
                order: {
                    orderFlag: 0, // 0自收 1总部帮子店收
                    status: this.orderStatus,
                    outOrderNo: '',
                    clinicId: '',
                    // 采购单ID
                    purchaseOrderId: '',
                    // 配货单ID
                    deliveryOrderId: '',
                    receiveTime: formatDate(Date.now(), 'YYYY-MM-DD'),
                    comment: '',
                    // 收货人
                    receiver: {
                        name: '',
                        id: '',
                    },
                    // 收货人id
                    receiveBy: '',
                    // 验收人id
                    inspectBy: '',
                    // 入库人id
                    stockInBy: '',
                    orderNo: '',
                    supplierId: '',
                    supplier: {
                        name: '',
                        id: '',
                    },
                    supplierSellerId: '',
                    supplierSeller: {
                        name: '',
                        id: '',
                    },
                    relatedOrders: {
                        list: [],
                    },
                    list: [],
                    // 补录采购单信息
                    additionalPurchaseOrder: {
                        purchaseBy: '',
                        purchaseType: ReceiptOrginOrderType.SEPARATE,
                        purchaseOrderDate: formatDate(Date.now(),'YYYY-MM-DD'),
                    },
                    // 来自供应商打通
                    fromSupplier: 0,
                    // 是否是补录采购
                    additionalFlag: 0,
                    // erp供应商主动推送
                    isErpDirect: 0,
                    transportRecordId: '',
                    claimClinicId: undefined, // 要货门店
                },
                transportRecord: null,
                showTransportRecordDialog: false,
                showTraceableCodePopover: false,
                showTraceableCodeModal: false,
                validateCell: false,
                showModal: false,
                showPopoverOrderList: false, // 采购订单列表是否展开
                highlightKeyId: '',
                clinicEmployees: [],
                traceableCodePopoverShow: false, // 追溯码采集面板是否展开
                isGoodsSearching: false, // 商品搜索中
            };
        },
        computed: {
            currentStockEmployeeList() {
                if (!!this.order.clinicId && this.order.clinicId !== this.clinicId) {
                    return this.clinicEmployees || [];
                }
                return this.stockEmployeeList || [];
            },
            PriceModifySourceType() {
                return PriceModifySourceType;
            },
            ...mapGetters([
                'goodsConfig',
                'currentClinic',
                'userInfo',
                'supplierList',
                'stockEmployeeList',
                'currentPharmacy',
                'isSingleStore',
                'isChainAdmin',
                'isChainSubStore',
                'modulePermission',
                'isAdmin',
                'traceCodeConfig',
                'isStrictCountWithTraceCodeCollect',
            ]),
            ...mapGetters(['subClinics']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isChainAdminSelf() {
                return this.isChainAdmin &&
                    this.order.supplier?.isEntrustDelivery &&
                    this.order.supplier?.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY;
            },
            goodDetailsConfig() {
                return {
                    hiddenPosition: this.hiddenPosition,
                    initTabContent: '',
                    pharmacyNo: this.pharmacyNo,
                    pharmacyType: this.pharmacyType,
                    selectedClinicId: '',
                };
            },
            hiddenPosition() {
                // 汇总库房不显示柜号
                return isNull(this.pharmacyNo);
            },
            supplierOptions() {
                return this.currentSupplierList.map((item) => {
                    const handleItem = this.getGualificationStatus(item);
                    return {
                        ...item,
                        isExpired: handleItem.isExpired,
                        expiredList: handleItem.expiredList,
                    };
                }) || [];
            },
            supplierOptionsNormal() {
                return this.supplierOptions?.filter((item) => {
                    return !item.isExpired;
                });
            },
            supplierOptionsExpired() {
                return this.supplierOptions?.filter((item) => {
                    return item.isExpired;
                });
            },
            closeText() {
                return (this.pageLoading || [PurchaseReceiveOrderStatus.RECEIVED, PurchaseReceiveOrderStatus.REJECT].includes(this.order?.status)) ? '关闭' : '取消';
            },
            hasMallOrderId() {
                return this.mallOrderId || this.order?.mallOrderId;
            },
            GoodsTypeEnum() {
                return GoodsTypeEnum;
            },
            logs() {
                return this.order?.logs ?? [];
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            showTips() {
                // 补录采购单
                if (this.order.additionalFlag === 1 && !this.isAdmin) {
                    // 根据选择的供应商-判断是否开启审批
                    if (this.order.additionalPurchaseOrder.purchaseType === ReceiptOrginOrderType.CONCENTRATE_SEPARATE) {
                        return !!(this.goodsConfig.chainReview.chainExternalFlag & BIT_FLAG_PURCHASE_APPLY_CHAIN_ENTRUST_DELIVERY);
                    }
                    return !!(this.goodsConfig.chainReview.chainExternalFlag & BIT_FLAG_PURCHASE_APPLY_CLINIC_SELF);
                }
                return false;
            },
            //1 非连锁总部 都可以看到 2 连锁总部 非新增 单子是自己的 3 连锁总部 新增 4 单子已经完成 连锁总部 非新增 非总部自己的单子 并且子店有关联运输单
            // 还要加标志 单子是总部收货的也可以查看
            showRecordBtn() {
                return !this.isChainAdmin ||
                    (this.isChainAdmin && this.orderId && (this.clinicId === this.order.clinicId || this.canModifyOrder)) ||
                    this.isAdd ||
                    this.isChainAdminReadChainStore;
            },
            disableRecordBtn() {
                return this.order.status === PurchaseReceiveOrderStatus.REJECT;
            },
            // 连锁总部 单子是帮子店建的标识
            canModifyOrder() {
                return this.isChainAdmin && !!this.order.orderFlag;
            },
            // TODO
            isChainAdminReadChainStore() {
                return (this.isFinishOrder && this.isChainAdmin && this.orderId && this.clinicId !== this.order.clinicId && this.order.transportRecordId);
            },
            defaultGoodsTypeMap() {
                return this.viewDistributeConfig.Inventory.defaultGoodsTypeMap;
            },
            useNewGoodsArchives() {
                return this.viewDistributeConfig.Inventory.useNewGoodsArchives;
            },
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            pharmacyType() {
                return this.currentPharmacy?.type;
            },
            enableBarcodeDetector() {
                return !this.isDisabledScanBarcode && !this.isDialogShowing;
            },
            isDialogShowing() {
                return this.addMedicineDialogVisible ||
                    this.addArchiveDialogVisible ||
                    this.showAdditionalDialog ||
                    this.showTraceableCodeModal ||
                    this.showTraceableCodePopover ||
                    this.showTransportRecordDialog ||
                    this.showModal;
            },
            isAdd() {
                return !this.orderId || this.isDraft;
            },
            isDraft() {
                return this.order.status === PurchaseReceiveOrderStatus.DRAFT;
            },
            isFromSupplier() {
                return !!this.order.fromSupplier;
            },
            isErpDirect() {
                return !!this.order.isErpDirect;
            },
            isOrderClinic() {
                return this.order.clinicId === this.clinicId || this.canModifyOrder;
            },
            isFinishOrder() {
                return this.order.status === PurchaseReceiveOrderStatus.RECEIVED;
            },
            showPurchaseOrderStatus() {
                return this.order.status === PurchaseReceiveOrderStatus.REVIEW || this.order.status === PurchaseReceiveOrderStatus.REJECT;
            },
            disabledDraftBtn() {
                const order = clone(this.order);
                const orderCache = clone(this.cacheOrder);
                delete order.transportRecordId;
                delete orderCache.transportRecordId;
                return isEqual(order, orderCache);
            },
            // 是否是总部配货
            isChainAdminDistribution() {
                return this.order?.deliveryOrderId;
            },
            title() {
                return '收货单';
            },
            receiveTitle() {
                return this.receiveType === ReceiveActionType.CONFIRM ? '确认收货' : '确认收货/验收/入库';
            },
            receiveDialogTips() {
                return this.receiveType === ReceiveActionType.CONFIRM ? '补录采购订单将发往总部审核，审核通过后将自动完成收货' : '补录采购订单将发往总部审核，审核通过后将自动完成收货/验收/入库';
            },
            statusName() {
                return PurchaseReceiveOrderStatusName[this.order.status];
            },
            tagConfig() {
                const config = {
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                };
                config.theme = PurchaseReceiveOrderStatusTagTheme[this.order.status];
                return config;
            },
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            selectClinicId() {
                // 连锁总部优先取选中的门店
                if (this.isChainAdmin) {
                    return this.order.clinicId || this.clinicId;
                }
                return this.clinicId;
            },
            submitBtnText() {
                return '确定收货';
            },
            isSupplement() {
                return this.order.additionalFlag === 1 && (!this.order.orderNo || this.order.orderNo === '补录');
            },
            orderClinicName() {
                const current = this.subClinics?.find((item) => {
                    return item.id === this.order.clinicId;
                }) || {};
                return current?.shortName || current?.name || this.currentClinic?.clinicName || '';
            },
            relationshipOrderNo() {
                if (this.relatedOrder && !isNull(this.relatedOrder.sourceType)) {
                    return getSourceOrderText({
                        ...this.relatedOrder,
                        additionalFlag: this.isDraft && this.order.additionalFlag,
                    });
                }

                return '';
            },
            relationshipOrderNoWidth() {
                return this.showPurchaseOrderStatus ? '200px' : '100%';
            },
            relatedOrder() {
                const [order] = getSourceOrder(this.order.relatedOrders?.list ?? [], RelatedOrderType.PURCHASE);
                return order || {};
            },
            computedOrder() {
                const goodsIds = new Set();


                const totalPrice = this.order.list.reduce((res, item) => {
                    goodsIds.add(item.goodsId || item.goods?.id);
                    res = Big(res).plus(item.receiveTotalCost || 0);
                    return res;
                }, Big(0));

                const count = this.order.list.reduce((res, item) => {
                    goodsIds.add(item.goodsId || item.goods?.id);
                    res = Big(res).plus(item.receiveCount || 0);
                    return res;
                }, Big(0));

                return {
                    kindCount: goodsIds.size,
                    amount: totalPrice.toNumber(),
                    count,
                };
            },
            // 连锁总部保存按钮
            canChainAdminSave() {
                if (this.isChainAdmin && this.isErpDirect && this.order.status === PurchaseReceiveOrderStatus.TAKE_DELIVERY) {
                    return true;
                }
                return false;
            },
            canEdit() {
                // 5.21神奇康正线上问题，总部也能修改erp推送的待收货单，帮助建档
                if (this.canChainAdminSave) return true;
                // 来自供应链打通的收货，限制不能修改表格数据
                if (this.isFromSupplier && !this.isErpDirect) return false;
                // 若为配送类型则不可编辑，追溯码除外
                if (this.isChainAdminDistribution) return false;
                return this.isAdd || this.canTakeDelivery;
            },
            canEditRelationshipOrderNo() {
                return this.isAdd || (this.canTakeDelivery && this.isErpDirect);
            },
            canTakeDelivery() {
                const {
                    status, clinicId,
                } = this.order;

                return (status === PurchaseReceiveOrderStatus.TAKE_DELIVERY) && (clinicId === this.clinicId || this.canModifyOrder);
            },
            canExport() {
                return !this.isAdd;
            },
            // table滚动后校验或聚焦输入框的回调时间，根据是否开启虚拟列表来决定大小
            timeout() {
                return this.order.list?.length >= 30 ? 1000 : 0;
            },
            indexMap() {
                return this.order.list?.reduce((map, item, index) => {
                    map[item.keyId || item.id] = index;
                    return map;
                }, {}) || {};
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'shortId',
                            label: '商品编码',
                            slot: true,
                            pinned: 'left',
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '120px',
                                'minWidth': '96px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'displayName',
                            label: '商品名称',
                            slot: true,
                            'style': {
                                'flex': '1',
                                'width': '',
                                'maxWidth': '',
                                'minWidth': '316px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                            'pinned': 'left',
                        },

                        {
                            key: 'sellPrice',
                            label: '售价',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '120px',
                                'minWidth': '90px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: this.isChainAdminDistribution ? 'distributionCount' : 'purchaseCount',
                            label: this.isChainAdminDistribution ? '配货数量' : '采购量',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '100px',
                                'minWidth': '80px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: 'receivePackageCount',
                            label: '收货量',
                            slot: true,
                            colType: 'money',
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '80px',
                                'minWidth': '58px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: 'receiveUnit',
                            label: '单位',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '64px',
                                'minWidth': '56px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'receivePackageCostPrice',
                            label: '进价',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '120px',
                                'minWidth': '90px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: 'receiveTotalCost',
                            label: '金额',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '100px',
                                'minWidth': '80px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: 'supplierName',
                            label: '供应商',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '124px',
                                'minWidth': '124px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                            hidden: !this.isChainAdminDistribution,
                        },
                        {
                            key: 'batchNo',
                            label: '生产批号',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '100px',
                                'minWidth': '88px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'productionDate',
                            label: '生产日期',
                            // colType: 'date',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '114px',
                                'maxWidth': '114px',
                                'minWidth': '114px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'expiryDate',
                            label: '有效日期',
                            // colType: 'date',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '114px',
                                'maxWidth': '114px',
                                'minWidth': '114px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            label: '追溯码',
                            key: 'traceableCode',
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '220px',
                                'minWidth': '120px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'operate',
                            label: '操作',
                            pinned: 'right',
                            style: {
                                'flex': 1,
                                'width': '94px',
                                'maxWidth': '94px',
                                'minWidth': '94px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'center',
                            },
                            hidden: !(this.isFinishOrder && this.isOrderClinic),
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
            showPopoverOrderListSearch() {
                return this.showModal || this.showPopoverOrderList;
            },
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
            'order.clinicId': {
                handler() {
                    this.initClinicName = true;
                },
                deep: true,
            },
        },
        async created() {
            if (this.isStrictCountWithTraceCodeCollect && this.isAdd) {
                this.validateCell = true;
            }

            if (this.isAdd) {
                // 新建的时候 并且不是连锁总部才需要把值给到
                if (!this.orderId && !this.isChainAdmin) {
                    this.order.clinicId = this.clinicId;
                }
                // 不是连锁总部，并且是创建阶段 需要自动带入
                if (!this.isChainAdmin && !this.orderId) {
                    this.order.receiveBy = this.userInfo?.id;
                    this.order.inspectBy = this.userInfo?.id;
                    this.order.stockInBy = this.userInfo?.id;
                    this.handleReceiverChange(this.order.receiveBy);
                }
                // 先缓存，保证导入的数据可以保存草稿
                this.cacheOrder = clone(this.order);

                if (this.importOrderData) {
                    this.importOrderDataFormat();
                    await this.initCollectCodeCountList(this.order.list);
                }
                // 采购订单一键导入
                if (this.purchaseOrder) {
                    await this.handleSelectOrder(this.purchaseOrder);
                    if (!this.orderId && !this.order.clinicId) {
                        this.order.clinicId = this.purchaseOrder.applicantOrganId || this.clinicId;
                    }
                }

                this.order.list.forEach((item) => {
                    // 补充无码标识
                    this.initNoTraceCodeList(item);
                });

            }

            if (this.orderId) {
                await this.fetchOrderDetail();
            }
        },
        mounted() {
            if (this.isDraft || this.isAdd) {
                this.registerKeyboardNavigation();
                window.addEventListener('keydown', this.handleShortcutKeys);
            }
        },
        beforeDestroy() {
            if (this.isDraft || this.isAdd) {
                // 移除快捷键监听
                if (this.keyboardNavigationTimer) {
                    clearTimeout(this.keyboardNavigationTimer);
                }
                window.removeEventListener('keydown', this.handleShortcutKeys);
            }
        },
        methods: {
            isNull,
            getFormatLength,
            unitEqual,
            formatMoney,
            isChineseMedicine,
            complexCount,
            moneyDigit,
            autoFocus,
            calCostPriceSingle,
            calCostPriceTotal,
            getCostConfig,
            formatSupplierTypeName,
            formatClinicName,
            openSupplement() {
                if (this.isSupplement) {
                    this.showAdditionalDialog = true;
                }
            },
            async fetchEmployeeByModuleId() {
                try {
                    const { data } = await ClinicAPI.fetchEmployeeByModuleId({
                        queryClinicId: this.order.clinicId,
                    });
                    this.clinicEmployees = data || [];
                } catch (e) {
                    console.log(e);
                }
            },
            async changeClinic() {
                this.clinicEmployees = [];
                this.showModal = false;
                // 已经初始化过了并且有采购订单
                if (this.initClinicName && !!this.relationshipOrderNo) {
                    this.order.supplierId = '';
                    this.order.supplierSellerId = '';
                    if (this.order.additionalFlag === 1 && (!this.order.orderNo || this.order.orderNo === '补录')) {
                        // 订单是补录的话就不清理商品的内容，因为本身就是补录
                    } else {
                        this.$confirm({
                            type: 'warn',
                            title: '清除订单商品',
                            content: '收货的采购订单已清除，是否清除已添加的商品',
                            showClose: false,
                            disabledKeyboard: true,
                            onConfirm: () => {
                                this.order.list = [];
                            },
                        });
                    }
                } else {
                    this.initClinicName = true;
                }
                if (this.order.additionalFlag === 1 && (!this.order.orderNo || this.order.orderNo === '补录')) {
                    // 是补录的话就不清理选中的内容，因为本身就是补录
                } else {
                    this.handleClear();
                }
                if (this.order.clinicId !== this.clinicId) {
                    await this.fetchEmployeeByModuleId();
                    // 拉取新的表后找不到
                    if (this.order.receiveBy && !this.currentStockEmployeeList?.find((it) => {
                        return it.employeeId === this.order.receiveBy;
                    })) {
                        this.order.receiveBy = '';
                        this.order.inspectBy = '';
                        this.order.stockInBy = '';
                    }
                }
            },
            gualificationList(id) {
                return this.supplierOptionsExpired?.find((item) => {
                    return item.id === id;
                })?.expiredList || [];
            },
            supplieExpired(id) {
                return this.supplierOptionsExpired?.find((item) => {
                    return item.id === id;
                });
            },
            // 获取经营范围
            getGualificationStatus(item) {
                const { extendInfo = {} } = item;
                const certificationInfos = extendInfo?.certificationInfos || [];
                if (!certificationInfos?.length) {
                    return {
                        isExpired: false,
                        expiredList: [],
                    };
                }
                const expiredItem = certificationInfos?.find((it) => {
                    const currentTime = this.getCurrentTime(it);
                    return this.isExpired(currentTime);
                });
                if (expiredItem) {
                    return {
                        isExpired: true,
                        expiredList: certificationInfos?.filter((it) => {
                            const currentTime = this.getCurrentTime(it);
                            return this.isExpired(currentTime);
                        }) || [],
                    };
                }
                return {
                    isExpired: false,
                    expiredList: [],
                };
            },
            // 是否已到期
            isExpired(currentTime) {
                const date1 = new Date(currentTime);
                const date2 = new Date();
                return date1.getTime() - date2.getTime() < 0;
            },
            getCurrentTime(item) {
                if (!item?.validTo) {
                    return '';
                }
                const validToDate = new Date(item.validTo);
                // 格式化为 YYYY-MM-DD 23:59:59
                const year = validToDate.getFullYear();
                const month = String(validToDate.getMonth() + 1).padStart(2, '0');
                const day = String(validToDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day} 23:59:59`;
            },
            singleRowCanEdit(row) {
                if (!(this.isFinishOrder && this.isOrderClinic)) {
                    return false;
                }
                return row.id === this.currentModifyGoodsItemId;
            },
            editRow(row) {
                if (this.currentModifyGoodsItemId) {
                    const currentItem = this.order.list?.find((item) => {
                        return item.id === this.currentModifyGoodsItemId;
                    });
                    if (!isEqual(currentItem, this.cacheCurrentModifyGoodsItem)) {
                        const index = this.order.list?.findIndex((item) => {
                            return item.id === this.currentModifyGoodsItemId;
                        });
                        this.$Toast.warning(`第${index + 1}行存在正在编辑的内容`);
                        this.$refs.tableRef.scrollToElement({
                            index,
                            top: 0,
                            time: 60,
                        });
                        return;
                    }
                }
                this.currentModifyGoodsItemId = row.id;
                this.cacheCurrentModifyGoodsItem = clone(row);
            },
            async saveRow(row, index) {
                const {
                    expiryDate = '', productionDate = '', batchNo = '',
                } = row;
                if (!batchNo) {
                    return;
                }
                if (row.goods && !isChineseMedicine(row.goods) && !expiryDate) {
                    return;
                }
                if (expiryDate && !checkExpiryDate(expiryDate)) {
                    return;
                }
                if (productionDate && !checkExpiryDate(productionDate)) {
                    return;
                }
                if (this.saveRowLoading) {
                    return;
                }
                try {
                    this.saveRowLoading = true;
                    const { data = {} } = await GspAPI.updateReceiveOrderItemRecord(this.orderId, row.id, {
                        expiryDate,
                        productDate: productionDate,
                        batchNo,
                    });
                    if (data) {
                        this.$set(this.order.list, index, {
                            ...row,
                            expiryDate,
                            productionDate,
                            batchNo,
                        });
                        if (this.cacheOrder?.list) {
                            this.$set(this.cacheOrder.list, index, {
                                ...row,
                                expiryDate,
                                productionDate,
                                batchNo,
                            });
                        }
                        this.$Toast({
                            type: 'success',
                            message: '修改成功',
                        });
                    }
                    // 失败不处理，让用户自己取消
                    this.currentModifyGoodsItemId = '';
                    this.$nextTick(() => {
                        this.cacheCurrentModifyGoodsItem = null;
                    });
                } catch (e) {
                    console.log(e);
                } finally {
                    this.saveRowLoading = false;
                }
            },
            cancelRow(row, index) {
                const {
                    expiryDate = '', productionDate = '', batchNo = '',
                } = this.cacheCurrentModifyGoodsItem;
                this.$set(this.order.list, index, {
                    ...row,
                    expiryDate,
                    productionDate,
                    batchNo,
                });
                this.currentModifyGoodsItemId = '';
                this.$nextTick(() => {
                    this.cacheCurrentModifyGoodsItem = null;
                });
            },
            async updateOrderInfo() {
                try {
                    this.saveBtnLoading = true;
                    const { data = {} } = await GspAPI.updateReceiveOrderRecord(this.orderId, {
                        comment: this.order.comment,
                    });
                    if (data) {
                        this.cacheOrder.comment = this.order.comment;
                        this.$Toast({
                            type: 'success',
                            message: '修改成功',
                        });
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.saveBtnLoading = false;
                    this.edit = false;
                    this.$nextTick(() => {
                        this.$refs.orderInfoPopoverRef?.doClose();
                    });
                }
            },
            cancelUpdate() {
                this.edit = false;
                this.order.comment = this.cacheOrder.comment;
                this.$nextTick(() => {
                    this.$refs.orderInfoPopoverRef?.doClose();
                });
            },
            handleUpdate() {
                this.edit = true;
                this.$nextTick(() => {
                    this.$refs?.orderRemark?.focus();
                });
            },
            changePrice(priceInfo, row) {
                // 更新价格信息
                Object.assign(row.goods, priceInfo);
            },
            async fetchTransportRecord(transportRecordId) {
                try {
                    const { data = {} } = await GspAPI.fetchTransportRecord(transportRecordId);
                    const detailInfo = data;
                    const params = {
                        id: detailInfo.id,
                        recorderId: detailInfo.recorderId,
                        transportMode: detailInfo.transportMode,
                        transportNo: detailInfo.transportNo,
                        temperatureControlMode: detailInfo.temperatureControlMode,
                        departureTime: detailInfo.departureTime,
                        departureTemperature: detailInfo.departureTemperature,
                        driverName: detailInfo.driverName,
                        departureProvinceId: detailInfo.departureProvinceId,
                        departureProvinceName: detailInfo.departureProvinceName,
                        departureCityId: detailInfo.departureCityId,
                        departureCityName: detailInfo.departureCityName,
                        departureDistrictId: detailInfo.departureCityName,
                        departureDistrictName: detailInfo.departureDistrictName,
                        departureAddressDetail: detailInfo.departureAddressDetail,
                        carrierName: detailInfo.carrierName,
                        arrivalTime: detailInfo.arrivalTime,
                        arrivalTemperature: detailInfo.arrivalTemperature,
                        remark: detailInfo.remark,
                        attachments: detailInfo.attachments,
                    };
                    this.setTransportRecord(params);
                } catch (e) {
                    console.log(e);
                }
            },
            openOrderDialog() {
                this.showTransportRecordDialog = true;
            },
            async handleOpenMallOrderDetail() {
                if (this.mallOrderId || this.order.mallOrderId) {
                    const mall = await this.$abcPlatform.module.mall;
                    mall.service.OrderDetailDialog({
                        orderId: this.mallOrderId || this.order.mallOrderId,
                    });
                }
            },
            setTransportRecord(transportRecord) {
                this.transportRecord = transportRecord;
            },
            refresh(id) {
                console.log('更新草稿单id=', id);
                this.order.transportRecordId = id;
                this.fetchTransportRecord(this.order.transportRecordId);
            },
            createTrKey(item) {
                return item.keyId || item.id || item.goodsId;
            },
            createTrClass(tr) {
                const keyId = this.createTrKey(tr);
                return `abc-table-tr--${keyId} ${this.highlightKeyId === keyId ? 'abc-table-tr--highlight' : ''}`;
            },
            calCostPrice(item) {
                const {
                    receiveTotalCost, receiveCount,
                } = item;
                // const useCount = isChineseMedicine(goods) ? receivePieceCount : receivePackageCount;

                if (isNotNull(receiveCount) && isNotNull(receiveTotalCost)) {
                    const price = this.calCostPriceSingle({
                        useCount: receiveCount,
                        useTotalCostPrice: receiveTotalCost,
                    });

                    this.$set(item, 'receivePackageCostPrice', price);
                }
            },

            async calTotalPrice(item) {
                // const {
                //     receivePackageCount, receivePieceCount, receivePackageCostPrice, goods,
                // } = item;
                // const useCount = isChineseMedicine(goods) ? receivePieceCount : receivePackageCount;
                const {
                    receivePackageCostPrice, receiveCount,
                } = item;

                // 先初始化采集数量
                await this.initCollectCodeCountList([item]);

                // 再初始化无码数量
                this.initNoTraceCodeList(item);

                if (isNull(receiveCount) || isNull(receivePackageCostPrice)) {
                    this.$set(item, 'receiveTotalCost', '');
                } else {
                    const totalCost = this.calCostPriceTotal({
                        useCount: receiveCount,
                        useUnitCostPrice: receivePackageCostPrice,
                    });

                    this.$set(item, 'receiveTotalCost', totalCost);
                }
            },
            async initCollectCodeCountList(list = []) {
                // 开启强校验时才实时计算
                if (this.isEnableTraceableCode && this.isStrictCountWithTraceCodeCollect && TraceCode.isSupportTraceCodeForceCheckStock()) {

                    const resList = await TraceCode.getMaxTraceCountList({
                        scene: TraceCodeScenesEnum.INVENTORY,
                        dataList: list,
                        getGoodsInfo: (item) => item.goods,
                        getUnitInfo: (item) => ({
                            unitCount: item.receiveCount,
                            unit: item.receiveUnit,
                        }),
                        createKeyId: this.createTrKey,
                    });

                    (resList || []).forEach((e) => {
                        const item = list.find((i) => this.createTrKey(i) === e.keyId);
                        if (item) {
                            this.$set(item, '_maxTraceCodeCount', e.traceableCodeNum);
                            this.$set(item, '_isTransformable', e.isTransformable);
                            this.$set(item, '_traceableCodeMaxNum', e.traceableCodeMaxNum);
                        }
                    });
                }
            },
            initNoTraceCodeList(item) {
                // 对无码商品初始化追溯码
                if (this.isEnableTraceableCode && TraceCode.isSupplementNoCodeGoods(item.goods)) {
                    item.traceableCodeList = TraceCode.mergeNoTraceCodeList({
                        ...item,
                        isTrans: item._isTransformable,
                        maxCount: item._maxTraceCodeCount,
                        unitCount: item.receiveCount,
                        unit: item.receiveUnit,
                    });
                }
            },
            getDate(date, mask = 'YYYY-MM-DD') {
                try {
                    return formatDate(date, mask);
                } catch (e) {
                    console.error('getDate', e);
                    return '';
                }
            },
            importOrderDataFormat() {
                if (!this.importOrderData) return;
                const importOrderList = (this.importOrderData?.list || []);
                // 标准模板
                if (this.importOrderData.isStandardTemplate) {
                    const {
                        outOrderNo, supplier, receiveBy, receiveDate,
                    } = this.importOrderData;
                    this.order = {
                        ...this.order,
                        outOrderNo: outOrderNo ?? '',
                        supplierId: supplier?.id ?? '',
                        supplier: supplier ?? this.order.supplier,
                        receiveBy: receiveBy ?? this.order.receiveBy,
                        receiveTime: receiveDate ? formatDate(new Date(receiveDate), 'YYYY-MM-DD') : this.order.receiveTime,
                        list: importOrderList.map((item) => {
                            const {
                                receivePackageCount, receivePieceCount, goods,
                            } = item;

                            const tempObj = {
                                ...item,
                                searchGoodsKey: goods?.medicineCadn || goods?.name || '',
                                purchasePackageCount: receivePackageCount,
                                purchasePieceCount: receivePieceCount,
                                // 采购只会有大单位或者小单位一种情况
                                receiveCount: receivePackageCount || receivePieceCount,
                                expiryDate: this.getDate(item.expiryDate),
                                productionDate: this.getDate(item.productionDate),
                                keyId: createGUID(),
                            };

                            // 补充无码标识
                            // this.initNoTraceCodeList(tempObj);
                            return tempObj;
                        }),
                    };
                } else {


                    this.order = {
                        ...this.order,
                        ...this.importOrderData,
                        list: importOrderList.map((item) => {

                            const {
                                useCount,
                                useUnit,
                                useUnitCostPrice,
                                useTotalCostPrice,
                                goods,
                            } = item;
                            // 使用大包装数量
                            const usePackageCount = useUnit === goods?.packageUnit;
                            const tempObj = {
                                ...item,
                                searchGoodsKey: item.medicineCadn || item.name || item.rowMatchInfo?.medicineCadn || '',
                                purchasePackageCount: usePackageCount ? useCount : 0,
                                purchasePieceCount: usePackageCount ? 0 : useCount,

                                receivePackageCount: usePackageCount ? useCount : 0,
                                receivePieceCount: usePackageCount ? 0 : useCount,

                                receiveUnit: useUnit,
                                receiveCount: useCount,
                                receivePackageCostPrice: useUnitCostPrice,
                                receiveTotalCost: useTotalCostPrice,
                                expiryDate: this.getDate(item.expiryDate),
                                productionDate: this.getDate(item.productionDate),
                                keyId: createGUID(),
                            };

                            // 补充无码标识
                            // this.initNoTraceCodeList(tempObj);

                            return tempObj;
                        }),
                    };
                }

            },
            getUnits(goods) {
                const businessGoods = new BusinessGoods(goods);
                return businessGoods.getAvailableUnits(true);
            },
            async fetchOrderDetail() {
                try {
                    this.pageLoading = true;
                    const res = await GoodsAPIV3.getPurchaseReceiveOrderDetail(this.orderId);
                    if (res.data) {
                        const {
                            additionalFlag, applicantId, purchaseType, purchaseOrderDate,
                        } = res.data?.purchaseOrder ?? {};
                        this.order = {
                            ...this.order,
                            ...res.data,
                            additionalFlag,
                            additionalPurchaseOrder: {
                                purchaseBy: applicantId,
                                purchaseType,
                                purchaseOrderDate,
                            },
                            list: res.data.list?.map((item) => {
                                const {
                                    receiveUnit,
                                    receiveCount,
                                    receivePackageCount,
                                    receivePieceCount,
                                    receivePackageCostPrice,
                                    receiveCostPrice,
                                    receiveTotalCost,
                                    goods,
                                    productionDate,
                                    expiryDate,
                                } = item;

                                item.keyId = createGUID();
                                item.searchGoodsKey = goods?.medicineCadn || goods?.name || '';
                                item.productionDate = productionDate || (item.batchInfo?.productionDate ?? '');
                                item.expiryDate = expiryDate || (item.batchInfo?.expiryDate ?? '');

                                item.receiveUnit = receiveUnit;
                                item.receiveCount = receiveCount || (item.receiveUnit === goods?.packageUnit ? receivePackageCount : receivePieceCount) || '';
                                item.receivePackageCostPrice = receiveCostPrice || receivePackageCostPrice;
                                item.receiveTotalCost = receiveTotalCost;
                                item.traceableCodeList = item.traceableCodeList || [];

                                if (item.extendInfo?.erpData) {
                                    try {
                                        const {
                                            erpData,
                                        } = item.extendInfo;
                                        if (erpData.semantic) {
                                            item.rowMatchInfo = JSON.parse(erpData.semantic) || {};
                                            // 兼容中间库没数据的情况
                                            if (!item.rowMatchInfo.medicineCadn) {
                                                item.rowMatchInfo.medicineCadn = erpData.medicineCadn || erpData.name || '';
                                            }
                                            item.rowMatchInfo.ERPLabel = '供应商ERP商品：';
                                            item.rowMatchInfo.shortId = erpData.shortId;
                                            item.rowMatchInfo.supplierGoodsId = erpData.supplierGoodsId;
                                            item.rowMatchInfo.specification = item.rowMatchInfo.specification || erpData.displaySpec;
                                            item.rowMatchInfo.manufacturerFull = item.rowMatchInfo.manufacturerFull || erpData.manufacturer;
                                            item.searchGoodsKey = item.searchGoodsKey || item.rowMatchInfo?.medicineCadn || '';
                                        } else {
                                            item.rowMatchInfo = {
                                                medicineCadn: erpData.medicineCadn || erpData.name || '',
                                                specification: erpData.displaySpec,
                                                manufacturerFull: erpData.manufacturer,
                                            };
                                            item.rowMatchInfo.ERPLabel = '供应商ERP商品：';
                                            item.rowMatchInfo.shortId = erpData.shortId;
                                            item.rowMatchInfo.supplierGoodsId = erpData.supplierGoodsId;
                                            item.searchGoodsKey = item.searchGoodsKey || item.rowMatchInfo.medicineCadn || '';
                                        }
                                    } catch (e) {
                                        console.error(e);
                                    }
                                }

                                if (item.extendInfo?.excelData) {
                                    try {
                                        const { rowMatchInfo } = JSON.parse(item.extendInfo?.excelData) || {};
                                        item.rowMatchInfo = rowMatchInfo || {};
                                        item.searchGoodsKey = item.searchGoodsKey || item.rowMatchInfo.medicineCadn || '';
                                    } catch (e) {
                                        console.error(e);
                                    }
                                }

                                return item;
                            }),
                            relatedOrders: res.data.relatedOrders || {
                                list: [],
                            },
                            receiveBy: res.data.receiveBy || res.data.receiver?.id || '',
                            inspectBy: res.data.inspectBy || res.data.inspector?.id || '',
                            stockInBy: res.data.stockInBy || res.data.stockInUser?.id || '',
                            // 收货时间都不能改
                            receiveTime: res.data.receiveTime ? formatDate(new Date(res.data.receiveTime), 'YYYY-MM-DD') : formatDate(Date.now(), 'YYYY-MM-DD'),
                        };

                        if (this.order.receiveBy) {
                            this.handleReceiverChange(this.order.receiveBy);
                        }
                        this.handleSupplierChange(this.order.supplierId, false);


                        // 总部配货
                        if (!this.isChainAdminDistribution) {
                            this.order.purchaseOrderId = this.relatedOrder.id;
                        }
                        // 未关联采购单的erp收货单
                        // if (this.isErpDirect && !this.order.relatedOrders.list?.length) {
                        //     let sourceType;
                        //
                        //     if (this.isSingleStore) {
                        //         sourceType = ReceiptOrginOrderType.SEPARATE;
                        //     } else {
                        //         sourceType = this.order.supplier?.isEntrustDelivery ? ReceiptOrginOrderType.CONCENTRATE_SEPARATE : ReceiptOrginOrderType.SEPARATE;
                        //     }
                        //     this.order.relatedOrders.list = [{
                        //         orderNo: '补录',
                        //         sourceType,// 区分来源类型
                        //         type: RelatedOrderType.PURCHASE,// 区分单据类型
                        //     }];
                        //     this.order.additionalPurchaseOrder.purchaseType = sourceType;
                        // }
                        // 草稿单打开需要将收货日期同步到今天
                        if (this.order.status === PurchaseReceiveOrderStatus.DRAFT) {
                            this.order.receiveTime = formatDate(Date.now(), 'YYYY-MM-DD');
                        }
                        if (this.order.transportRecordId) {
                            this.fetchTransportRecord(this.order.transportRecordId);
                        }
                    }
                    if (this.order.clinicId !== this.clinicId) {
                        await this.fetchEmployeeByModuleId();
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    window.setTimeout(() => {
                        this.$refs.tableRef?.getScrollShadow();
                    }, 50);
                    this.cacheOrder = clone(this.order);
                    this.pageLoading = false;
                }
            },
            validateProductionDate(value, callback) {
                value = value.trim();
                if (value) {
                    if (checkExpiryDate(value)) {
                        callback({ validate: true });
                    } else {
                        callback({
                            validate: false, message: '格式错误',
                        });
                    }
                }
            },
            validateExpiryDate(value, callback) {
                value = value.trim();
                if (value) {
                    if (checkExpiryDate(value)) {
                        callback({ validate: true });
                    } else {
                        callback({
                            validate: false, message: '格式错误',
                        });
                    }
                }
            },
            validateOrderNo(value, callback) {
                if (this.order.additionalFlag || this.isErpDirect) {
                    callback({
                        validate: true,
                    });
                    return;
                }
                if (!this.relationshipOrderNo) {
                    callback({
                        validate: false, message: '不能为空',
                    });
                }
            },
            // 校验goodsId一定存在
            handleValidateKey(item) {
                if (!item.goodsId) {
                    return (_, callback) => {
                        callback({
                            validate: false,
                            message: '不能为空',
                        });
                    };
                }
                return (_, callback) => {
                    callback({
                        validate: true,
                    });
                };
            },
            // 保健食品、其他商品生产批号，生产日期非必填
            isOtherGoods(goods) {
                return [GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD, GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT].includes(goods?.typeId);
            },
            validateVirtualListData(virtualListRes) {
                let valid = true;
                let invalidIndex = -1;

                for (let i = 0; i < this.order.list.length; i++) {
                    const item = this.order.list[i];

                    // eslint-disable-next-line no-loop-func
                    this.handleValidateKey(item)(item, (res) => {
                        if (res.validate === false) {
                            valid = false;
                            invalidIndex = i;
                        }
                    });

                    if (!valid) {
                        break;
                    }

                    // eslint-disable-next-line no-loop-func
                    this.validateProductionDate(item.productionDate || '', (res) => {
                        if (res.validate === false) {
                            valid = false;
                            invalidIndex = i;
                        }
                    });

                    if (!valid) {
                        break;
                    }

                    // eslint-disable-next-line no-loop-func
                    this.validateExpiryDate(item.expiryDate || '', (res) => {
                        if (res.validate === false) {
                            valid = false;
                            invalidIndex = i;
                        }
                    });

                    if (!valid) {
                        break;
                    }


                    // 非中药必填有效日期
                    if (item.goods && !isChineseMedicine(item.goods) && !item.expiryDate) {
                        valid = false;
                        invalidIndex = i;
                        break;
                    }

                    // 校验必填项
                    if (
                        isNull(item.receiveCount) ||
                        isNull(item.receivePackageCostPrice) ||
                        isNull(item.receiveTotalCost) ||
                        isNull(item.receiveUnit) ||
                        (isNull(item.batchNo) && !this.isOtherGoods(item.goods))
                    ) {
                        valid = false;
                        invalidIndex = i;
                        break;
                    }

                }

                if (!valid) {
                    virtualListRes.scrollToElement({
                        index: invalidIndex,
                        top: 47,
                        time: 60,
                        behavior: 'instant',
                    });
                }
                return valid;
            },
            getProductDateTooltip(item) {
                if (!item.productionDate || !checkExpiryDate(item.productionDate)) return '';
                return showProductionDateTip(item.productionDate);
            },
            getExpiryDateTooltip(item) {
                if (!item.expiryDate || !checkExpiryDate(item.expiryDate)) return '';
                let diffTime;
                const expiredWarnMonths = item.goods?.expiredWarnMonths;
                if (expiredWarnMonths) {
                    diffTime = expiredWarnMonths * 30 * 24 * 3600 * 1000;
                }

                const today = parseTime((new Date()), 'y-m-d', true);
                if (showOverdueTip(item.expiryDate, today)) {
                    return '该药品已过期';
                }
                if (showExpiryDateTip(item.productionDate, item.expiryDate)) {
                    return '效期不可小于或等于生产日期';
                }
                if (validateExpirationTime(today, item.expiryDate, diffTime)) {
                    if (expiredWarnMonths) {
                        return `该药品将在${expiredWarnMonths}个月之内过期`;
                    }
                    return '该药品将在一年半之内过期';
                }
                return '';
            },
            unitChange(row) {
                setUserGoodsUnit(row.goodsId, row.receiveUnit);
                if (isChineseMedicine(row.goods) && String(row.receiveUnit).toLocaleLowerCase() === 'kg') {
                    row.receivePackageCostPrice = formatMoney(row.receivePackageCostPrice, true);
                    this.calTotalPrice(row);
                }
            },
            // 只有当前选中的中药单位是g，才会去读取上次保存的kg 或者 g（ lastUseUnit ）
            initGoodsUseUnit(goodsObj) {
                if (!goodsObj) return;
                let useUnit;
                // 只有当前选中的中药单位是g，才会去读取上次保存的kg 或者 g（ lastUseUnit ）
                if (isChineseMedicine(goodsObj)) {
                    const {
                        id,
                        pieceUnit,
                    } = goodsObj;

                    if (pieceUnit === 'g') {
                        const lastUseUnit = getUserGoodsUnit(id);
                        useUnit = lastUseUnit || pieceUnit;
                    } else {
                        useUnit = goodsObj.pieceUnit;
                    }
                } else {
                    useUnit = goodsObj.packageUnit;
                }
                return useUnit;
            },
            async selectReplaceGoods(goods, item) {
                if (goods) {
                    let goodsObj = null;
                    try {
                        const { data } = await GoodsBaseAPI.goods(
                            goods.id,
                            this.clinicId,
                            {
                                forPurchase: 1,
                                withStock: 1,
                            },
                        );
                        goodsObj = data;
                    } catch (e) {
                        Logger.reportAnalytics('goods-business', {
                            key: 'selectReplaceGoods',
                            value: '查询药品详情失败',
                            goods,
                        });
                        if (e.code === 12015) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: e.message,
                            });
                        } else {
                            this.$Toast({
                                message: e.message || '查询药品详情失败',
                                type: 'error',
                            });
                        }
                    }

                    // 查询失败-不走后续逻辑避免数据异常
                    if (!goodsObj) return;

                    item.notMatched = 0;
                    item.goods = goodsObj;
                    item.searchGoodsKey = goods.medicineCadn || goods.name;
                    item.goodsId = goods.id;
                    item.receiveUnit = this.initGoodsUseUnit(goods);
                    item.traceableCodeList = [];
                    this.currentEditIndex = -1;

                    // 先初始化采集数量
                    await this.initCollectCodeCountList([item]);

                    // 再初始化无码数量
                    this.initNoTraceCodeList(item);
                }
            },
            handleResetSearchKey(item) {
                const index = this.getRealIndex(item);

                if ((this.currentEditIndex !== -1 && !this.$refs.addGoodsDropdownRef?.$refs?.downloadRef?.showPopper) || this.currentEditIndex !== index) {
                    item.searchGoodsKey = item.goods?.medicineCadn || item.goods?.name || item.rowMatchInfo?.medicineCadn || '';
                    this.currentEditIndex = -1;
                    return true;
                }
            },
            handleAddNewGoods(type, item) {
                const index = this.getRealIndex(item);

                this.currentEditIndex = -1;
                const { rowMatchInfo } = item;
                const {
                    typeId,
                    specification,
                    specificationDict,
                    pieceUnit,
                    packageUnit,
                } = rowMatchInfo || {};
                this.importGoodsInfo = {
                    specification,
                    medicineCadn: rowMatchInfo?.medicineCadn,
                    manufacturerFull: rowMatchInfo?.manufacturerFull,
                    medicineNmpn: rowMatchInfo?.medicineNmpn,
                    // barCode: rowMatchInfo?.barCode,
                    // shortId: rowMatchInfo?.shortId || '',
                    dosageFormType: rowMatchInfo?.dosageFormTypeValue,
                    businessScopeList: rowMatchInfo?.businessScopeListValue,
                    otcType: rowMatchInfo?.otcTypeValue,
                    mha: rowMatchInfo?.mha,
                    maintainType: rowMatchInfo?.maintainTypeValue,
                    storage: rowMatchInfo?.storage,
                    shelfLife: rowMatchInfo?.shelfLife,
                    medicineNmpnEndExpiryDate: rowMatchInfo?.medicineNmpnEndExpiryDate,
                    medicineNmpnStartExpiryDate: rowMatchInfo?.medicineNmpnStartExpiryDate,

                    pieceNum: specificationDict?.pieceNum,
                    pieceUnit: specificationDict?.pieceUnit,
                    packageUnit: specificationDict?.packageUnit,
                    componentContentNum: specificationDict?.dosageNum,// 解析那边这个是容量
                    componentContentUnit: specificationDict?.dosageUnit,
                    medicineDosageNum: specificationDict?.componentNum,// 解析那边这个是成分
                    medicineDosageUnit: specificationDict?.componentUnit,
                };

                // 中药取值调整
                if (typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE || typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES) {
                    this.importGoodsInfo.extendSpec = specification;
                    this.importGoodsInfo.pieceUnit = pieceUnit || packageUnit;// 兼容erp数据只有packageUnit
                    this.importGoodsInfo.packageUnit = '';
                }
                this.currentGoodsIndex = index;

                if (this.useNewGoodsArchives) {
                    if (type !== GoodsTypeEnum.MEDICINE) {
                        this.importGoodsInfo.name = this.importGoodsInfo.medicineCadn;
                        this.importGoodsInfo.medicineCadn = '';
                    }
                    // 获取用户上次建档类型
                    const lastTypeId = getUserLastTypeId(type);
                    this.currentTypeId = typeId || lastTypeId || this.defaultGoodsTypeMap[type];
                    this.addMedicineDialogVisible = true;
                }
            },
            getRealIndex(currentItem) {
                const key = currentItem.keyId || currentItem.id;
                return this.indexMap[key];
            },
            editCurrentGoodsItem(item, index) {
                if (!this.canEdit) return;
                this.currentEditIndex = this.getRealIndex(item);
                this._timer = setTimeout(() => {
                    this.autoFocus(index);
                }, 100);
            },
            // 表格删除的index是正确的
            handleDeleteTr(index) {
                const len = this.order.list.length;
                this.order.list.splice(index, 1);
                if (this.order.list.length === 0) {
                    // 回到搜索
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this.$refs?.goodsAutoCompleteRef?.manualFocus();
                    }, 0);
                    return;
                }
                this.$nextTick(() => {
                    if (index === len - 1) {
                        this.focusInput(this.order.list.length - 1);
                    } else {
                        this.focusInput(index);
                    }
                });
            },
            handleReceiveDialogOpen() {
                this.pushDialogName(this.receiveTitle);
            },
            handleReceiveDialogClose() {
                this.popDialogName(this.receiveTitle);
                this.handleConfirmCancel();
            },
            handleUpdateList() {
                this.addMedicineDialogVisible = false;
            },
            closeDialogHandle() {
                this.searchKey = '';
                this.$refs.goodsAutoCompleteRef?.handleClear();
                this.addMedicineDialogVisible = false;
                this.importGoodsInfo = {};
            },
            handleSuccessAdd(goods) {
                this.importGoodsInfo = {};
                const item = this.order.list[this.currentGoodsIndex];

                if (item) {
                    Object.assign(item, {
                        ...item,
                        notMatched: 0,
                        goods,
                        goodsId: goods.id,
                        receiveUnit: isChineseMedicine(goods) ? goods.pieceUnit : goods.packageUnit,
                        searchGoodsKey: goods.medicineCadn || goods.name || '',
                    });
                }
            },
            // 推荐数据:search-recommend-fn="searchRecommendByPurchase"
            async searchRecommendByPurchase() {
                if (this._waitFocus) return [];

                const res = await GoodsAPIV3.searchRecommendByPurchase({
                    pharmacyNo: this.pharmacyNo,
                    supplierId: this.order.supplierId,
                });
                return res?.data?.list || [];
            },
            clearTraceCode() {
                this._waitFocus = true;
            },
            focusGoodsAutoComplete() {
                this._waitFocus = false;
                if (this.isAdd && !this.hasMallOrderId) {
                    this.updateCurrentPosition({
                        row: 1,
                        col: 0,
                    });
                }
            },
            traceableCodeEnter(keywordTraceableCodeNoInfo) {
                console.log('traceableCodeEnter', keywordTraceableCodeNoInfo);

                const hasDialog = checkHasAbcDialog(['biz-pharmacy-purchase-take-delivery-dialog']);
                if (hasDialog || this.showPopoverOrderList) return;

                this.showTraceableCodeModal = true;
                new TraceCodeSelectGoodsDialog({
                    value: true,
                    title: '请选择追溯码关联的商品',
                    desc: '追溯码关联的商品',
                    placeholder: '搜索该追溯码关联的商品',
                    keywordTraceableCodeNoInfo,
                    onConfirm: this.handleConfirmBindGoods,
                    onClose: () => {
                        console.log('onClose');
                        this.showTraceableCodeModal = false;
                    },
                }).generateDialogAsync({
                    parent: this,
                });
            },
            traceableCodeWarning(data) {
                this.showTraceableCodeModal = true;
                new TraceCodeWarningDialog({
                    value: true,
                    traceCodeDetails: data,
                    onConfirm: () => {
                        this.showTraceableCodeModal = false;
                        this.handleConfirm();
                    },
                    onClose: () => {
                        this.confirmBtnLoading = false;
                        this.submitBtnLoading = false;
                        this.buttonLoading = false;
                        this.showTraceableCodeModal = false;
                    },
                }).generateDialogAsync();
            },
            // 药品标识码绑定goods成功后的回调
            handleConfirmBindGoods(goods, keywordTraceableCodeNoInfo) {
                console.log('handleConfirmBindGoods', goods, keywordTraceableCodeNoInfo);
                this.selectGoods(goods, {
                    keywordTraceableCodeNoInfo,
                });
            },
            async updateGoods(item, index) {
                console.log('需要更新goods', item, index);
                try {
                    const goodsId = item.goodsId || item.goods.id;
                    // 重新获取药品信息
                    const { data = {} } = await GoodsBaseAPI.goods(
                        goodsId,
                        this.selectClinicId,
                        {
                            forPurchase: 1, withStock: 1,
                        },
                    );
                    // eslint-disable-next-line vue/max-len
                    const currentUnit = isChineseMedicine(data) ? data.pieceUnit : (this.order.list[index].receiveUnit === data.packageUnit || this.order.list[index].receiveUnit === data.pieceUnit) ? this.order.list[index].receiveUnit : data.packageUnit;
                    this.$set(this.order.list, index, {
                        ...this.order.list[index],
                        goods: data || {},
                        receiveUnit: currentUnit,
                    });
                } catch (e) {
                    console.log(e);
                }
            },
            findCanAddTraceCodeItem(goods, res) {
                const item = TraceCode.findCanAddTraceCodeItem({
                    traceableCodeNoInfo: res.keywordTraceableCodeNoInfo.traceableCodeNoInfo,
                    dataList: this.order.list,
                    goodsInfo: goods,
                    getUnitInfo(item) {
                        return {
                            unitCount: item.receiveCount,
                            unit: item.receiveUnit,
                        };
                    },
                });
                // 有匹配的药品，追加追溯码
                if (item) {
                    // 当前商品是无码商品，说明是无码商品绑定追溯码，需清除之前的无码标识
                    if (TraceCode.isNoTraceCodeGoods(item.goods)) {
                        item.traceableCodeList = item.traceableCodeList.filter((it) => it.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE);
                        item.goods.traceableCodeNoInfoList = [res.keywordTraceableCodeNoInfo.traceableCodeNoInfo];
                    } else {
                        item.traceableCodeList = item.traceableCodeList || [];
                    }

                    const codeItem = item.traceableCodeList.find((e) => e.no === res.keywordTraceableCodeNoInfo.no);
                    if (codeItem) {
                        // 手动失焦
                        this.$refs.goodsAutoCompleteRef?.manualBlur();
                        this._traceCodeRepeatDialog = new TraceCodeRepeatDialog({
                            visible: true,
                            isValidateCount: true,
                            title: '采集相同追溯码',
                            traceCodeInfo: {
                                ...res.keywordTraceableCodeNoInfo,
                                count: (codeItem.count || 1),
                            },
                            onConfirm: (data) => {
                                // 不改变原数据，只是更新数量
                                Object.assign(codeItem, data);
                            },
                        });
                        this._traceCodeRepeatDialog.generateDialogAsync();
                        return;
                    }

                    item.traceableCodeList.push({
                        ...res.keywordTraceableCodeNoInfo,
                    });

                    return item;
                }
            },
            async selectGoods(goods, res) {
                console.log('selectGoods', goods, res);

                let repeatGoodsIndex = this.order.list.findIndex((item) => {
                    return item.goodsId === goods.id;
                });

                // 扫追溯码走下面逻辑
                if (this.isEnableTraceableCode && res?.keywordTraceableCodeNoInfo) {
                    this.findCanAddTraceCodeItem(goods, res);

                    // 扫码重复药品不自动添加了
                    if (repeatGoodsIndex > -1) {
                        // 高亮行
                        const highlightKeyId = this.createTrKey(this.order.list[repeatGoodsIndex]);
                        if (res?.codeType) {
                            this.scrollToIndex(this.order.list.length - 1, highlightKeyId);
                        } else {
                            this.focusInput(repeatGoodsIndex);
                        }
                        return;
                    }
                }

                try {
                    this.isGoodsSearching = true;
                    const goodsId = goods.goodsId || goods.id;
                    // 重新获取药品信息
                    const { data: tempGoods } = await GoodsBaseAPI.goods(
                        goodsId,
                        this.selectClinicId,
                        {
                            forPurchase: 1, withStock: 1,
                        },
                    );

                    if (tempGoods) {
                        this.searchKey = '';
                        this.$refs.goodsAutoCompleteRef?.handleClear();
                        const businessGoods = new BusinessGoods(tempGoods);
                        const {
                            useUnit,
                        } = businessGoods.getAvailableUnit();
                        // 获取药品所有可用单位
                        const units = businessGoods.getAvailableUnits(true);
                        // 获取用户上次使用的单位
                        const lastUseUnit = getUserGoodsUnit(goodsId);

                        const item = {
                            ...tempGoods,
                            keyId: createGUID(),
                            goods: tempGoods,
                            goodsId: tempGoods.id,
                            searchGoodsKey: tempGoods.medicineCadn || tempGoods.name,
                            receiveUnit: units.includes(lastUseUnit) ? lastUseUnit : useUnit,
                        };

                        if (this.isEnableTraceableCode) {
                            item.traceableCodeList = res?.keywordTraceableCodeNoInfo ? [{
                                ...res.keywordTraceableCodeNoInfo,
                            }] : [];
                        }

                        // 二次检查是否重复药品
                        repeatGoodsIndex = this.order.list.findIndex((item) => {
                            return item.goodsId === goods.id;
                        });

                        if (repeatGoodsIndex > -1) {
                            const repeatGoods = this.order.list[repeatGoodsIndex];
                            tempGoods.expiredWarnMonths = repeatGoods.expiredWarnMonths;
                            this.sortOrderListDesc(repeatGoods.goodsId);

                            // 有追溯码信息,追加后return,因为不需要新加数据，手动加重复药品不拦截
                            if (this.isEnableTraceableCode && res.keywordTraceableCodeNoInfo) {
                                this.findCanAddTraceCodeItem(goods, res);

                                // 高亮行
                                const highlightKeyId = this.createTrKey(this.order.list[repeatGoodsIndex]);
                                if (res?.codeType) {
                                    this.scrollToIndex(this.order.list.length - 1, highlightKeyId);
                                } else {
                                    this.focusInput(repeatGoodsIndex);
                                }
                                return;
                            }
                        }

                        // 判断是扫码添加的药品
                        if (res?.codeType) {
                            const index = this.order.list.length - 1;
                            let highlightKeyId = '';
                            // 不添加新药品，而是将收货量+1
                            if (res.codeType === 2 && repeatGoodsIndex > -1) {
                                // 上面将药品排到最后一个了
                                const repeatGoods = this.order.list[index];
                                if (repeatGoods) {
                                    repeatGoods.receiveCount = (repeatGoods.receiveCount || 0) + 1;
                                }
                                // 高亮行
                                highlightKeyId = this.createTrKey(repeatGoods);
                            } else {
                                // 高亮行
                                highlightKeyId = this.createTrKey(item);
                                // 收货数量默认1
                                item.receiveCount = 1;
                                this.order.list.push(item);
                            }

                            this.scrollToIndex(this.order.list.length - 1, highlightKeyId);

                        } else {
                            this.order.list.push(item);
                            // this.highlightKeyId = this.createTrKey(item);
                            // 添加药品后都要聚集到最后一个
                            this.focusInput(this.order.list.length - 1, true);
                        }
                        // 关联订单改为非必填，不用校验了
                        // if (!this.relationshipOrderNo) {
                        //     this.$nextTick(() => {
                        //         this.$refs.form?.validate(() => {}, (item) => {
                        //             const { classList } = item.$el;
                        //             // 校验关联单号
                        //             if (classList.contains('relation-form-item')) {
                        //                 return false;
                        //             }
                        //             return true;
                        //         });
                        //     });
                        // }
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.isGoodsSearching = false;
                }

            },
            // 滚动table,聚焦输入框
            focusInput(index, isScroll = true) {
                if (isScroll) {
                    this.$refs.tableRef.scrollToElement({
                        index,
                        top: 47,
                        time: 60,
                        behavior: 'instant',
                    });
                }
                if (this.focusTimer) {
                    clearTimeout(this.focusTimer);
                    this.focusTimer = null;
                }

                this.focusTimer = setTimeout(() => {
                    const keyId = this.createTrKey(this.order.list[index]);
                    const $tableTr = this.$refs.tableRef.$el.querySelector(`.abc-table-tr--${keyId}`);
                    $tableTr && $tableTr.querySelector('.focus-input input').focus();
                    this._waitFocus = false;
                    this.$refs.tableRef.handleClickTr(this.order.list[index]);
                }, this.timeout);
            },
            scrollToIndex(index, highlightKeyId) {
                // 只滚动不聚焦
                const { virtualListRes } = this.$refs.tableRef;
                if (virtualListRes?.isVirtualList?.value) {
                    this.$refs.tableRef.scrollToElement({
                        index,
                        top: 47,
                        time: 60,
                        behavior: 'instant',
                    });
                } else {
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this.$refs.tableRef.$refs.tableBody.$el.scrollTop = Math.ceil((index + 1) * 48);
                    }, 60);
                }

                // 清除上次高亮
                this.highlightKeyId = '';
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.highlightKeyId = highlightKeyId;
                }, this.timeout);
            },
            sortOrderListDesc(id) {
                const newOrderList = Clone(this.order.list);
                newOrderList.sort((a, b) => {
                    return (a.goodsId === id) - (b.goodsId === id);
                });
                this.order.list = Clone(newOrderList);
            },
            createPostData(params) {
                const {
                    purchaseOrderId,
                    deliveryOrderId,
                    clinicId,
                    supplierId,
                    supplierSellerId,
                    comment,
                    receiveTime,
                    list,
                    receiveBy,
                    inspectBy,
                    stockInBy,
                    outOrderNo,
                    additionalPurchaseOrder,
                    additionalFlag,
                    isErpDirect,
                    mallOrderId = '',
                    claimClinicId,
                } = this.order;

                if (claimClinicId) {
                    additionalPurchaseOrder.claimClinicId = claimClinicId;
                }
                let { orderFlag = 0 } = this.order;
                // 连锁总部，并且可以添加
                if (this.isChainAdmin && this.isAdd) {
                    orderFlag = (this.currentClinic?.clinicId === this.order.clinicId) ? 0 : 1;
                }

                const {
                    sourceType,
                } = this.relatedOrder;

                const obj = {
                    purchaseOrderId,
                    deliveryOrderId,
                    comment,
                    supplierId,
                    supplierSellerId,
                    clinicId,
                    receiveBy,
                    inspectBy,
                    stockInBy,
                    receiveTime,
                    outOrderNo,
                    opType: params.opType,
                    type: sourceType,
                    additionalFlag,
                    isErpDirect,
                    orderFlag,
                    list: list.map((e) => {
                        const {
                            receiveUnit,
                            receiveCount,
                            receivePackageCostPrice,
                            receiveTotalCost,
                            purchasePackageCount,
                            purchasePieceCount,
                            batchId,
                        } = e;

                        const item = {
                            id: this.isAdd ? null : e.id,
                            goodsId: e.goodsId,

                            useCount: receiveCount,
                            useUnit: receiveUnit,
                            useUnitCostPrice: receivePackageCostPrice,
                            useTotalCostPrice: receiveTotalCost,
                            purchasePackageCount,
                            purchasePieceCount,
                            batchNo: e.batchNo,
                            productionDate: e.productionDate,
                            expiryDate: e.expiryDate,

                            orderDraftId: e.orderDraftId,
                            externalRelatedKey: e.externalRelatedKey,
                            skuGoodsId: e?.skuGoodsId || '',
                        };
                        // 追溯码数据
                        if (this.isEnableTraceableCode) {
                            item.traceableCodeList = TraceCode.transCodeList(e.traceableCodeList || []);
                        }
                        // excel一键导入收货保存草稿
                        if (e.rowMatchInfo && !this.importOrderData?.isStandardTemplate && params.opType === ReceiveActionType.DRAFT) {
                            item.excelData = JSON.stringify({
                                rowMatchInfo: e.rowMatchInfo,
                            });
                        }
                        // 保存把数据带回
                        // if (params.opType === ReceiveActionType.SAVE) {
                        //     item.erpData = item.extendInfo?.erpData;
                        // }
                        if (this.isChainAdminDistribution) {
                            item.batchId = batchId;
                        }
                        return item;
                    }),
                    transportRecord: this.transportRecord,
                    mallOrderId,
                    claimClinicId,
                };
                // 有传入mallOrderId
                if (this.mallOrderId) {
                    obj.mallOrderId = this.mallOrderId;
                }
                if (obj.additionalFlag) {
                    obj.additionalPurchaseOrder = additionalPurchaseOrder;
                } else {
                    if (this.isErpDirect) {
                        // 后端控制是否需要补录的开关，设置为0就不补录采购单
                        obj.erpDirectPush = 0;
                    }
                }


                if (obj.opType !== ReceiveActionType.FINISH) {
                    delete obj.inspectBy;
                    delete obj.stockInBy;
                }

                return obj;
            },
            handleExport() {
                GoodsAPIV3.exportReceiveOrder(this.orderId);
            },
            openSupplierSelectPanel() {
                if (!this.currentSupplierList.length) {
                    this.initSupplierList(true);
                }
            },
            handleSupplierChange(id, clear = true, oldVal = '') {
                if (id === oldVal) return;
                const supplier = this.findSupplier(id);
                this.order.supplier = supplier;
                // 连锁总部，并且有选择门店,并且门店说自配
                if (this.isChainAdmin && supplier?.isEntrustDelivery && supplier.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY) {
                    this.order.clinicId = this.clinicId;
                }
                if (clear) {
                    this.order.supplierSellerId = '';
                    this.order.supplierSeller = {
                        name: '',
                        id: '',
                    };
                }
            },
            selectSeller(seller) {
                if (this.order.supplierSellerId === seller.id) return;
                this.order.supplierSellerId = seller.id;
                this.order.supplierSeller = {
                    name: seller.name,
                    id: seller.id,
                };
            },
            // 采购订单接收 key 方向键
            handleClosePopover(key) {
                this.$refs.purchaseOrderListPopover?.doToggle();
                if (key) {
                    this.$nextTick(() => {
                        this.handleShortcutKeys({ key });
                    });
                }
            },
            // 打开采购订单
            handleOpenPurchaseOrderList() {
                if (!this.$refs.purchaseOrderListPopover?.showPopper) {
                    this.$refs.purchaseOrderListPopover?.doToggle();
                }
            },
            handleClear(needConfirm = false) {
                if (this.order.additionalFlag === 1 && (!this.order.orderNo || this.order.orderNo === '补录')) {
                    this.order.additionalFlag = 0;
                    this.order.purchaseOrderId = '';
                    this.order.relatedOrders = {
                        list: [],
                    };
                } else {
                    this.order.additionalFlag = 0;
                    this.order.purchaseOrderId = '';
                    this.order.relatedOrders = {
                        list: [],
                    };
                    if (needConfirm) {
                        this.$confirm({
                            type: 'warn',
                            title: '清除订单商品',
                            content: '收货的采购订单已清除，是否清除已添加的商品',
                            showClose: false,
                            disabledKeyboard: true,
                            onConfirm: () => {
                                this.order.list = [];
                            },
                        });
                    }
                }
            },
            handleIconClick() {
                console.log('handleIconClick');
                // TODO: 【待组件优化】abc-input内部在nextTick聚焦了，这个场景不能聚焦
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.inputRef.$refs.abcinput?.blur?.();
                }, 0);
            },
            handleSelectOrderConfirm(order) {
                if (!order) return;
                this.showModal = true;
                this.$confirm({
                    type: 'info',
                    title: '订单商品加入收货单',
                    content: '是否将已选采购订单的商品加入收货单？',
                    confirmText: '确定加入',
                    // showClose: false,
                    // disabledKeyboard: true,
                    onConfirm: () => {
                        this.handleConfirmAdd(order);
                    },
                    onCancel: () => {
                        this.showModal = false;
                    },
                });
            },
            initClinicId(order) {
                if (order.sourceType === ReceiptOrginOrderType.CONCENTRATE && this.isChainAdmin) {
                    this.order.clinicId = this.clinicId;
                } else {
                    this.order.clinicId = order?.applicantOrganId ?? this.order.clinicId;
                }
            },
            async handleConfirmAdd(order) {
                if (!this.order.list?.length) {
                    await this.handleSelectOrder(order);
                    this.initClinicId(order);
                } else {
                    this.showModal = true;
                    this.$confirm({
                        type: 'warn',
                        title: '已录入商品处理',
                        content: `收货单内已录入${this.order.list?.length}条商品，请确认是否保留？`,
                        confirmText: '保留',
                        cancelText: '清除',
                        showClose: false,
                        // disabledKeyboard: true,
                        onConfirm: async () => {
                            await this.handleSelectOrder(order, true);
                            this.initClinicId(order);
                        },
                        onCancel: async () => {
                            await this.handleSelectOrder(order);
                            this.initClinicId(order);
                        },
                        onClose: async () => {
                            this.showModal = false;
                        },
                    });
                }
            },

            async handleSelectOrder(order, needMerge = false) {
                try {
                    this.order.additionalFlag = 0;
                    this.order.relatedOrders = {
                        list: [{
                            id: order.id,
                            orderNo: order.orderNo,
                            sourceType: order.sourceType,
                            type: SourceTypeToOrderType[order.sourceType],
                        }],
                    };
                    this.order.purchaseOrderId = order.id;
                    this.order.supplierId = order.supplierId || '';
                    this.handleSupplierChange(this.order.supplierId);
                    if (!this.order.supplier) {
                        this.order.supplier = {
                            name: order.supplierName,
                            id: order.supplierId,
                        };
                    }

                    this.tableLoading = true;
                    this.showModal = false;
                    this.handleClosePopover();

                    const res = await GoodsAPIV3.getPurchaseOrderDetail(order.id);
                    if (!this.hasMallOrderId) {
                        if (res.data.list) {
                            const list = res.data.list.map((item) => {
                                const {
                                    goodsId,
                                    goods,
                                    packageCostPrice,
                                    packageCount = 0,
                                    pieceCount = 0,
                                    purchaseUnit,
                                    totalCost,
                                    receivedPackageCount = 0,
                                    receivedPieceCount = 0,
                                } = item;
                                let receivePackageCount = (packageCount - receivedPackageCount);
                                let receivePieceCount = (pieceCount - receivedPieceCount);

                                if (receivePackageCount <= 0) receivePackageCount = '';
                                if (receivePieceCount <= 0) receivePieceCount = '';

                                const row = {
                                    goodsId,
                                    goods,
                                    purchasePackageCount: packageCount,
                                    purchasePieceCount: pieceCount,
                                    receivePackageCostPrice: packageCostPrice,
                                    receivePackageCount,
                                    receivePieceCount,
                                    receiveUnit: purchaseUnit || '',
                                    receiveTotalCost: totalCost,
                                    batchNo: '',
                                    productionDate: '',
                                    expiryDate: '',
                                    searchGoodsKey: goods.medicineCadn || goods.name || '',
                                    receiveCount: purchaseUnit === goods?.packageUnit ? receivePackageCount : receivePieceCount,
                                    purchaseCount: purchaseUnit === goods?.packageUnit ? packageCount : pieceCount,
                                    keyId: createGUID(),
                                };

                                try {
                                    if (isNull(row.receiveCount) || isNull(row.receivePackageCostPrice)) {
                                        row.receiveTotalCost = '';
                                    } else {
                                        row.receiveTotalCost = this.calCostPriceTotal({
                                            useCount: row.receiveCount,
                                            useUnitCostPrice: row.receivePackageCostPrice,
                                        });
                                    }
                                } catch (e) {
                                    console.error(e);
                                    row.receiveTotalCost = '';
                                }

                                return row;
                                // 已经收完的商品不再添加进列表
                            }).filter((e) => e.receiveCount);

                            const orderListLength = this.order.list.length;
                            if (needMerge) {
                                // 合并收货单，如果商品已存在，使用已有的商品
                                const existingGoodsMap = new Map(this.order.list.map((item) => [item.goodsId, item]));
                                const mergedList = [...this.order.list]; // 先保留原有的所有商品

                                // 遍历新列表，添加原有列表中不存在的商品
                                list.forEach((newItem) => {
                                    if (!existingGoodsMap.has(newItem.goodsId)) {
                                        mergedList.push(newItem);
                                    }
                                });

                                this.order.list = mergedList;
                                // 聚焦到新增数据第一行
                                if (this.order.list.length > orderListLength) {
                                    this.focusInput(orderListLength);
                                }
                            } else {
                                this.order.list = list;
                                // 聚焦到数据第一行
                                this.focusInput(0);
                            }
                            await this.initCollectCodeCountList(this.order.list);
                        } else {
                            this.order.list = [];
                        }
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.tableLoading = false;
                    console.log('this.showModal=', this.showModal);
                }
            },
            handleAdditional(additionalOrder) {
                this.order.clinicId = additionalOrder.receiveClinicId ?? this.order.clinicId;
                this.order.additionalFlag = 1;
                this.order.relatedOrders = {
                    list: [
                        {
                            orderNo: '补录',
                            sourceType: additionalOrder.sourceType,// 区分来源类型
                            type: SourceTypeToOrderType[additionalOrder.sourceType],// 区分单据类型
                        },
                    ],
                };
                this.order.additionalPurchaseOrder = {
                    purchaseBy: additionalOrder.purchaseBy,
                    purchaseType: additionalOrder.sourceType,// 区分来源类型
                    purchaseOrderDate: additionalOrder.purchaseOrderDate,
                };
                this.order.receiveBy = additionalOrder.purchaseBy;
                this.order.inspectBy = additionalOrder.purchaseBy;
                this.order.stockInBy = additionalOrder.purchaseBy;
                this.order.supplierId = additionalOrder.supplierId || '';
                this.order.claimClinicId = additionalOrder.claimClinicId;
                this.handleReceiverChange(this.order.receiveBy);
                this.handleSupplierChange(this.order.supplierId);
            },
            handleAdditionalConfirm(additionalOrder) {
                this.handleAdditional(additionalOrder);
            },
            handleReceiverChange(employeeId) {
                const employee = this.currentStockEmployeeList.find((e) => e.employeeId === employeeId);
                if (employee) {
                    const {
                        employeeId: id, employeeName: name,
                    } = employee;
                    this.order.receiver = {
                        id,
                        name,
                    };
                }
            },
            handleSubmitBefore(type) {
                if (type === ReceiveActionType.CONFIRM) this.confirmBtnLoading = true;
                if (type === ReceiveActionType.FINISH) this.submitBtnLoading = true;
                if (type === ReceiveActionType.SAVE) this.buttonLoading = true;

                this.validateCell = false;

                // 保证先loading
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    const { virtualListRes } = this.$refs.tableRef;
                    if (virtualListRes?.isVirtualList?.value) {
                        this.validateVirtualListData(virtualListRes);
                    }

                    // 如果有错误，等待滚动到错误位置再校验
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this.$refs.form.validate(async (val) => {
                            if (val) {
                                this.receiveType = type;
                                // erp主动推送
                                if (this.isErpDirect) {
                                    // 未绑定商品
                                    const arr = this.order.list.filter((item) => !item.goods?.shortId);
                                    if (arr.length > 0) {
                                        this.$alert({
                                            type: 'warn',
                                            title: '提示',
                                            content: `存在${arr.length}种商品未与供应商商品绑定，无法完成收货，请联系管理员`,
                                        });
                                        this.confirmBtnLoading = false;
                                        this.submitBtnLoading = false;
                                        this.buttonLoading = false;
                                        return;
                                    }
                                }

                                if (this.isEnableTraceableCode) {
                                    const {
                                        flag, errorList, firstErrorIndex,
                                    } = await TraceCode.validate({
                                        scene: TraceCodeScenesEnum.INVENTORY,
                                        sceneType: SceneTypeEnum.GOODS_TAKE,
                                        dataList: this.order.list,
                                        createKeyId: this.createTrKey,
                                        getGoodsInfo: (item) => item.goods,
                                        getUnitInfo: (item) => {
                                            return {
                                                unitCount: item.receiveCount,
                                                unit: item.receiveUnit,
                                                countLabel: '收货数量',
                                                label: '收货数量',
                                            };
                                        },
                                        needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckStock(),
                                    });
                                    if (!flag) {
                                        const scrollError = () => {
                                            this.validateCell = true;
                                            this.confirmBtnLoading = false;
                                            this.submitBtnLoading = false;
                                            this.buttonLoading = false;

                                            this.$refs.tableRef.scrollToElement({
                                                index: firstErrorIndex,
                                                top: 47,
                                                time: 60,
                                                behavior: 'instant',
                                            });

                                            // eslint-disable-next-line abc/no-timer-id
                                            setTimeout(() => {
                                                this.$refs.form.validate();
                                            }, this.timeout);
                                        };

                                        if (errorList.some((item) => item.isError)) {
                                            scrollError();
                                        } else {
                                            this.$confirm({
                                                type: 'warn',
                                                title: '追溯码采集风险提醒',
                                                content: errorList.map((it) => {
                                                    const {
                                                        count,
                                                        warnTips,
                                                    } = it;
                                                    return `有 ${count} 个商品${warnTips}`;
                                                }),
                                                confirmText: '去修改',
                                                cancelText: '仍要提交',
                                                disabledKeyboard: true,
                                                showClose: false,
                                                onConfirm: scrollError,
                                                onCancel: () => {
                                                    this.validatePharmaceutical();
                                                },
                                            });

                                        }

                                        return;
                                    }
                                }

                                this.validatePharmaceutical();
                            } else {
                                console.error('校验失败');
                                this.confirmBtnLoading = false;
                                this.submitBtnLoading = false;
                                this.buttonLoading = false;
                            }
                        });
                    }, this.timeout);
                }, 0);
            },
            async validatePharmaceutical() {
                // 海南对接药监追溯码查询
                if (await TraceCode.getPharmaceuticalTraceCodeQueryConfig()) {
                    const data = await TraceCode.fetchPharmaceuticalTraceCodeDetails({
                        dataList: this.order.list,
                        beforeHook: () => {
                            this._loading = this.$Loading({
                                text: '正在核验追溯码',
                                customClass: 'print-loading-wrapper',
                            });
                        },
                        afterHook: () => {
                            this._loading?.close();
                        },
                        errorHook: (error) => {
                            this.$Toast({
                                type: 'error',
                                message: error?.message ?? '查询失败',
                            });
                        },
                    });
                    if (data?.length) {
                        this.traceableCodeWarning(data);
                        return;
                    }
                }
                this.handleConfirm();
            },
            handleConfirm() {
                if (this.receiveType === ReceiveActionType.SAVE) {
                    this.submitOrder(this.receiveType);
                } else {
                    this.order.receiveBy = this.order.receiveBy || this.currentStockEmployeeList?.[0]?.employeeId || '';
                    this.order.inspectBy = this.order.receiveBy;
                    this.order.stockInBy = this.order.receiveBy;
                    this.showReceiveDialog = true;
                }
            },
            /**
             * @description: 删除草稿
             * @author: ff
             * @date: 2024/2/4
             */
            async deleteDraftOrder() {
                this.$confirm({
                    type: 'warn',
                    title: '删除确认',
                    content: '删除后不能恢复。确定删除该草稿？',
                    onConfirm: async () => {
                        try {
                            this.deleteDraftBtnLoading = true;
                            await GoodsAPIV3.deleteReceiveOrder(this.orderId);
                            this.$emit('refresh');
                            this.handleCancel();
                        } catch (e) {
                            console.error(e);
                        } finally {
                            this.deleteDraftBtnLoading = false;
                        }
                    },
                });
            },
            /**
             * 创建收货单
             * @param opType {number} 0:保存草稿 1:确定收货 2:一键收货
             */
            handleSubmit(opType = ReceiveActionType.DRAFT) {
                if (opType === ReceiveActionType.DRAFT || opType === ReceiveActionType.CONFIRM) {
                    this.submitOrder(opType);
                } else {
                    this.$refs['confirm-form'].validate((val) => {
                        if (val) {
                            this.submitOrder(opType);
                        } else {
                            console.error('校验失败');
                        }
                    });
                }
            },
            async submitOrder(opType) {
                try {
                    if (opType === ReceiveActionType.DRAFT) this.draftBtnLoading = true;
                    if (opType === ReceiveActionType.CONFIRM) this.confirmBtnLoading = true;
                    if (opType === ReceiveActionType.FINISH) this.submitBtnLoading = true;
                    this.buttonLoading = true;

                    const postData = this.createPostData({ opType });
                    if (this.orderId) {
                        await GoodsAPIV3.updatePurchaseReceiveOrder(this.orderId, postData);
                    } else {
                        await GoodsAPIV3.createPurchaseReceiveOrder(postData);
                    }
                    if (this.isAdd) {
                        this.$Toast({
                            message: '提交成功',
                            type: 'success',
                        });
                    }
                    this.showDialog = false;
                    this.$emit('refresh', this.isAdd ? 1 : 2);
                    if (typeof this.mallOrderRefresh === 'function') {
                        this.mallOrderRefresh(true, 'add');
                    }

                } catch (e) {
                    console.error(e);
                } finally {
                    this.submitBtnLoading = false;
                    this.confirmBtnLoading = false;
                    this.draftBtnLoading = false;
                    this.buttonLoading = false;
                }
            },
            handleCancel() {
                this.showDialog = false;
                this.$emit('close');
            },
            handleConfirmCancel() {
                this.showReceiveDialog = false;
                this.draftBtnLoading = false;
                this.confirmBtnLoading = false;
                this.submitBtnLoading = false;
                this.buttonLoading = false;
            },
            // 更新商品标识码信息
            updateGoodsTraceableCodeNoInfo(goods, keywordTraceableCodeNoInfo) {
                this.order.list.forEach((item) => {
                    if (item.goods) {
                        if (goods && item.goods.id === goods.id) {
                            item.goods.traceableCodeNoInfoList = goods.traceableCodeNoInfoList;
                        }

                        // 清除已解绑商品的标识码信息、与录入的追溯码
                        if (keywordTraceableCodeNoInfo && item.goods.traceableCodeNoInfoList?.length) {
                            if (item.goods.id === keywordTraceableCodeNoInfo.goodsInfo?.id) {
                                item.goods.traceableCodeNoInfoList = item.goods.traceableCodeNoInfoList.filter((e) => {
                                    return e.drugIdentificationCode !== keywordTraceableCodeNoInfo.traceableCodeNoInfo?.drugIdentificationCode;
                                });

                                item.traceableCodeList = item.traceableCodeList?.filter((e) => {
                                    return e.no !== keywordTraceableCodeNoInfo.no;
                                });
                            }
                        }
                    }
                });
            },
            updateTraceableCodePopoverShowId(id, item) {
                const index = this.order.list.findIndex((e) => e.keyId === item.keyId);

                console.log('updateTraceableCodePopoverShowId', id, index);
                if (id) {
                    this.traceableCodePopoverShowIndex = index;
                    this.traceableCodePopoverShowId = id;
                } else {
                    if (this.traceableCodePopoverShowIndex === index) {
                        this.traceableCodePopoverShowId = '';
                    }
                }
            },
            // 点击 x 关闭 提示内容有变化是否保存草稿,
            closeDialog() {
                if (this.isAdd) {
                    const confirmTitle = this.isDraft ? '草稿信息发生变动，是否保存？' : '是否需要保存为草稿？';
                    if (!this.disabledDraftBtn) {

                        const vm = this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: confirmTitle,
                            showConfirm: false,
                            showCancel: false,
                            footerPrepend: () => {
                                return (
                                    <abc-space>
                                        <abc-button
                                            onClick={() => {
                                                this.handleSubmit(ReceiveActionType.DRAFT);
                                                vm.close();// 手动关闭
                                            }}
                                        >
                                            保存
                                        </abc-button>
                                        <abc-button
                                            type="blank"
                                            onClick={() => {
                                                this.handleCancel();
                                                vm.close();// 手动关闭
                                            }}
                                        >
                                            不保存
                                        </abc-button>
                                    </abc-space>
                                );
                            },
                        });
                    } else {
                        this.handleCancel();
                    }

                } else {
                    this.handleCancel();
                }
            },

            receivePackageCostPriceFocus(row, event) {
                if (!row.receivePackageCostPrice) {
                    row.receivePackageCostPrice = `${formatMoney(row.goods.lastPackageCostPrice, false)}`;
                    this.calTotalPrice(row);
                    this.$nextTick(() => {
                        event?.target?.select();
                    });
                }
                this.setDisabledScanBarcode(true);
            },
            receivePackageCostPriceBlur(row) {
                if (row.receivePackageCostPrice) {
                    row.receivePackageCostPrice = `${formatMoney(row.receivePackageCostPrice, false)}`;
                }
                this.setDisabledScanBarcode(false);
            },
            // 注册快捷键操作区域
            registerKeyboardNavigation() {
                if (this.onlyView) return;
                let rowNum = 0;
                this.initKeyboardNavigation({
                    component: this.$refs.orderInfoDescriptionsRef, type: 'AbcDescriptions',
                }, rowNum);
                this.bindKeyboardNavigationCallFn('handleOpenPurchaseOrderList', this.handleOpenPurchaseOrderList);
                rowNum++;
                if (this.isAdd && !this.hasMallOrderId) {
                    this.initKeyboardNavigation({
                        type: 'custom',
                        component: {
                            ...this.$refs.goodsAutoCompleteRef,
                            operateInnerPointer: ({
                                col,
                            }, directive) => {
                                console.log('col', col);
                                console.log('directive', directive);
                                return {
                                    row: 0,
                                    col: 0,
                                    maxRow: 0,
                                    maxCol: 0,
                                    curComponent: this.$refs.goodsAutoCompleteRef,
                                };
                            },
                        },
                    }, rowNum);
                    rowNum++;
                }
                this.initKeyboardNavigation({
                    type: 'AbcTable',
                    component: this.$refs.tableRef,
                }, rowNum);
                this.keyboardNavigationTimer = setTimeout(() => {
                    this.initKeyboardNavigationCallback();
                }, 0);
            },

            closeTraceableCodePopover() {
                if (this.traceableCodePopoverShowId) {
                    this.$refs[`traceableCodeCell-${this.traceableCodePopoverShowId}`]?.outside();
                    this.traceableCodePopoverShowId = '';
                }
            },

            // 处理快捷键事件
            handleShortcutKeys(event) {
                // 如果有弹窗显示，不处理快捷键
                if (this.isDialogShowing || this.showPopoverOrderList || this.traceableCodePopoverShow || this.onlyView || this.isGoodsSearching) {
                    return;
                }

                // 采购订单里补录采购订单弹窗是否打开
                if (this.$refs.orderListRef?.showDialog) {
                    return;
                }

                // 是否正在扫追溯码
                if (this.$refs.goodsAutoCompleteRef.isSearching) {
                    return;
                }

                // F2 添加商品
                if (event.key === 'F2') {
                    if (this.isAdd && !this.hasMallOrderId) {
                        this.$refs.goodsAutoCompleteRef.manualFocus();
                        this.closeTraceableCodePopover();
                    }
                    return;
                }

                // F8 查看运输记录
                if (event.key === 'F8') {
                    if (this.showRecordBtn) {
                        this.openOrderDialog();
                        this.closeNavigationPanel();
                        this.closeTraceableCodePopover();
                    }
                    return;
                }

                // Ctrl + Alt + S 一键收货/验收/入库
                if (event.key === 's' && event.altKey && event.ctrlKey) {
                    if (!this.order.list.length || this.confirmBtnLoading || this.draftBtnLoading) return;
                    if (this.isAdd || this.canTakeDelivery) {
                        this.handleSubmitBefore(ReceiveActionType.FINISH);
                    }
                    return;
                }

                // Alt + S 确定收货
                if (event.key === 's' && event.altKey && !event.ctrlKey) {
                    if (!this.order.list.length || this.submitBtnLoading || this.draftBtnLoading) return;
                    if (this.isAdd || this.canTakeDelivery) {
                        this.handleSubmitBefore(ReceiveActionType.CONFIRM);
                    }
                    return;
                }

                // F4 保存草稿
                if (event.key === 'F4') {
                    if (this.isAdd) {
                        this.handleSubmit(ReceiveActionType.DRAFT);
                    }
                    return;
                }

                // Alt + Delete 删除当前行
                if (event.key === 'Delete' && event.altKey) {
                    this.$refs.tableRef?.deleteSelectTr();
                    document?.activeElement?.blur();
                    this.closeTraceableCodePopover();
                    return;
                }

                // 如果有追溯码采集面板显示，不处理相应快捷键
                if (this.traceableCodePopoverShowId) {
                    return;
                }

                this.handleChangePosition(event);
            },
        },
    };
</script>
