<template>
    <abc-layout preset="page-table" style="height: 100%;">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-date-picker
                        v-model="selectDate"
                        type="daterange"
                        placeholder="收货日期"
                        :describe-list="describeList"
                        :picker-options="pickerOptions"
                        clearable
                        @change="initOffset"
                    >
                    </abc-date-picker>

                    <!--商品搜索-->
                    <goods-auto-complete
                        ref="autoComplete"
                        class="abc-autocomplete-search"
                        placeholder="商品名称/单号"
                        :with-stock="false"
                        :width="180"
                        :auto-focus-first="false"
                        :clear-search-key="false"
                        :enable-barcode-detector="!isDialogShowing"
                        :only-stock="false"
                        :search.sync="searchKey"
                        :pharmacy-no="pharmacyNo"
                        focus-show
                        enable-local-search
                        clearable
                        @selectGoods="selectGoods"
                        @searchGoods="searchGoods"
                        @enter="initOffset"
                        @clear="clearSearch"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                        <!--<abc-delete-icon v-if="searchKey" slot="append" @delete="clearSearch"></abc-delete-icon>-->
                    </goods-auto-complete>

                    <abc-select
                        v-model="fetchParams.supplierId"
                        :width="150"
                        custom-class="supplierWrapper"
                        with-search
                        clearable
                        placeholder="供应商"
                        inner-width="280px"
                        :fetch-suggestions="fetchSuggestions"
                        @change="initOffset"
                    >
                        <abc-option
                            v-for="it in currentSupplierList"
                            :key="`${it.id }`"
                            :value="it.id"
                            :label="it.name"
                        ></abc-option>
                    </abc-select>

                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="fetchParams.clinicId"
                        placeholder="总部/门店"
                        :show-all-clinic="false"
                        :clearable="true"
                        :width="150"
                        @change="initOffset"
                    ></clinic-select>

                    <abc-select
                        v-if="!isSingleStore"
                        key="fetchParams.type"
                        v-model="fetchParams.type"
                        :width="120"
                        placeholder="来源单据类型"
                        clearable
                        @change="initOffset"
                    >
                        <abc-option
                            v-for="option in orderTypeOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                        ></abc-option>
                    </abc-select>

                    <abc-select
                        v-model="fetchParams.status"
                        :width="100"
                        placeholder="状态"
                        clearable
                        @change="initOffset"
                    >
                        <abc-option
                            v-for="option in statusOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value"
                            :statistics-number="option.statisticsNumber"
                        ></abc-option>
                    </abc-select>

                    <employee-select
                        v-model="fetchParams.receiveBy"
                        placeholder="收货人"
                        :with-search="true"
                        :employee-list="stockEmployeeList"
                        @change="initOffset"
                    >
                    </employee-select>
                </abc-space>
                <abc-space>
                    <abc-button
                        theme="success"
                        icon="n-add-line-medium"
                        @click="handleAdd"
                    >
                        新增收货
                    </abc-button>

                    <abc-button
                        variant="ghost"
                        :loading="exportBtnLoading"
                        icon="n-upload-line"
                        @click="handleExport"
                    >
                        {{ exportBtnLoading ? '导出中' : '导出' }}
                    </abc-button>
                </abc-space>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="abcTable"
                type="pro"
                :loading="tableLoading"
                :render-config="renderConfig"
                :data-list="panelData.rows"
                :disabled-item-func="disabledItemFunc"
                :custom-tr-class="itemClassStr"
                :need-selected="false"
                @sortChange="sortChange"
                @handleClickTr="handleClickTr"
            >
                <template #orderNo="{ trData: row }">
                    <abc-table-cell class="ellipsis" :style="{ color: $store.state.theme.style.theme1 }">
                        <span
                            v-if="row.status === PurchaseReceiveOrderStatus.DRAFT"
                            v-abc-title="`最后修改：${ row.modifyTime ? formatCacheTime(row.modifyTime) : '-'}`"
                        ></span>
                        <span
                            v-else
                            v-abc-title="row.orderNo"
                        ></span>
                    </abc-table-cell>
                </template>

                <template #comment="{ trData: row }">
                    <abc-table-cell>
                        <abc-tooltip-info
                            v-if="row.comment"
                            placement="top"
                            :content="row.comment"
                        ></abc-tooltip-info>
                    </abc-table-cell>
                </template>

                <template #status="{ trData: row }">
                    <abc-table-cell>
                        <abc-tag-v2
                            v-bind="statusConfig(row)"
                            :min-width="57"
                        >
                            {{ PurchaseReceiveOrderStatusName[row.status] }}
                        </abc-tag-v2>
                    </abc-table-cell>
                </template>

                <template #type="{ trData: row }">
                    <abc-table-cell>
                        {{ ReceiptOrginOrderTypeName[row.type] }}
                    </abc-table-cell>
                </template>

                <template #sourceOrderNo="{ trData: row }">
                    <abc-table-cell>
                        {{ getSourceOrderStr(row) }}
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="tablePagination"
                :count="tablePagination.count"
                @current-change="pageChange"
            >
                <template #tipsContent>
                    <abc-flex flex="1" justify="flex-end">
                        <abc-space :size="4">
                            <abc-text theme="gray">
                                收货金额
                            </abc-text>
                            <abc-text theme="black" bold style="padding-right: 12px;">
                                {{ formatMoney(panelData.totalPrice, false) }}
                            </abc-text>
                        </abc-space>
                    </abc-flex>
                </template>
            </abc-pagination>
        </abc-layout-footer>

        <div>
            <order-dialog
                v-if="showOrderDialog"
                v-model="showOrderDialog"
                :order-id="orderId"
                :order-status="orderStatus"
                :mall-order-id="mallOrderId"
                :import-order-data="parseData"
                :purchase-order="selectPurchaseOrder"
                @refresh="handleRefresh"
            ></order-dialog>
        </div>

        <abc-dialog
            v-if="showTypeDialog"
            v-model="showTypeDialog"
            append-to-body
            custom-class="custom-goods-action-dialog"
            title="选择新建收货单的方式"
            :disabled-keyboard="disabledKeyboard"
            :show-header-border-bottom="false"
            @open="pushDialogName"
            @close="popDialogName"
        >
            <ul>
                <li>
                    <abc-button
                        type="blank"
                        class="in-select-btn"
                        size="large"
                        @click="handleOpenType(openTypeEnum.purchase)"
                    >
                        <abc-icon
                            icon="s-document-fill"
                            size="24"
                            style="margin-right: 8px;"
                            color="#459eff"
                        ></abc-icon>
                        手工录入
                    </abc-button>
                </li>
                <li>
                    <abc-button
                        type="blank"
                        class="in-select-btn"
                        size="large"
                        @click="handleOpenType(openTypeEnum.excel)"
                    >
                        <abc-icon
                            icon="s-xls-fill"
                            size="24"
                            style="margin-right: 8px;"
                            color="#459eff"
                        ></abc-icon>
                        一键导入 - Excel
                    </abc-button>
                </li>
                <li>
                    <abc-button
                        type="blank"
                        class="in-select-btn"
                        :style="isChainAdmin ? {
                            marginBottom: 0
                        } : {}"
                        size="large"
                        @click="handleOpenType(openTypeEnum.order)"
                    >
                        <abc-icon
                            icon="s-order-1-fill"
                            size="24"
                            style="margin-right: 8px;"
                            color="#459eff"
                        ></abc-icon>
                        一键导入 - 采购订单
                    </abc-button>
                </li>
                <li v-if="!isChainAdmin">
                    <abc-button
                        type="blank"
                        style="margin-bottom: 0;"
                        class="in-select-btn"
                        size="large"
                        @click="handleOpenType(openTypeEnum.mall)"
                    >
                        <abc-icon
                            icon="s-s-order-5-fill"
                            size="24"
                            style="margin-right: 8px;"
                            color="#459eff"
                        ></abc-icon>
                        一键导入 - 商城订单
                    </abc-button>
                </li>
            </ul>
        </abc-dialog>
        <div>
            <goods-import-parse-dialog
                v-if="showTemplateDialog"
                v-model="showTemplateDialog"
                :import-draft-id="importDraftId"
                :mall-goods-list="mallGoodsList"
                :mall-order-id="mallOrderId"
                :pharmacy-type="pharmacyType"
                is-pharmacy-take-delivery
                @open-form="importProjectAfter"
            ></goods-import-parse-dialog>
        </div>

        <abc-dialog
            v-if="showPurchaseOrderListDialog"
            v-model="showPurchaseOrderListDialog"
            append-to-body
            content-styles="height: 608px;"
            title="从采购订单一键导入"
            :disabled-keyboard="disabledKeyboard"
            size="hugely"
            @open="pushDialogName"
            @close="popDialogName"
        >
            <order-list
                :is-support-additional="false"
                :show-clinic-name="isChainAdmin"
                :show-clinic-filter="isChainAdmin"
                :show-search="true"
                @select="handleSelectOrder"
            ></order-list>
        </abc-dialog>
        <inbound-order-dialog
            v-if="showInboundOrder"
            v-model="showInboundOrder"
            @createInOrderByMall="handleCreateInOrderByMall"
        ></inbound-order-dialog>
    </abc-layout>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        formatMoney, goodsFullName,
    } from '@/filters';
    import {
        formatDate,
    } from '@abc/utils-date';
    import {
        PurchaseReceiveOrderStatus,
        PurchaseReceiveOrderStatusName,
        ReceiptOrginOrderType,
        ReceiptOrginOrderTypeName,
        RelatedOrderType,
    } from '@/views-pharmacy/inventory/constant';
    import {
        CHECK_IN_SUPPLIER_ID, GoodsInFromTypeEnum,
    } from 'views/inventory/constant';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import {
        isEqual,
    } from '@abc/utils';
    import EmployeeSelect from '@/views-pharmacy/inventory/frames/components/employee-select.vue';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    import {
        getSourceOrder,
    } from '@/views-pharmacy/inventory/utils';
    import {
        formatCacheTime, isNull,
    } from '@/utils';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import GoodsInImg from '@/assets/images/inventory/goods-in-artificial.png';
    import GoodsInMallImg from '@/assets/images/inventory/goods-in-mall.png';
    import GoodsInExcelImg from '@/assets/images/inventory/goods-in-excel.png';
    import GoodsInSupplier from '@/assets/images/inventory/goods-in-supplier.png';
    import GoodsImportParseDialog
        from 'views/inventory/goods-in/components/goods-import-parse-dialog/goods-import-parse-dialog.vue';
    import OrderList from '@/views-pharmacy/inventory/frames/purchase/require-goods/order-list.vue';
    import { TakeDeliveryTableConfig } from '@/views-pharmacy/inventory/frames/purchase/take-delivery/table-config';
    import InboundOrderDialog from 'views/inventory/goods-in/components/inbound-order/inbound-order-dialog.vue';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';

    const OrderDialog = () => import('@/views-pharmacy/inventory/frames/purchase/take-delivery/order-dialog.vue');
    const GoodsAutoComplete = () => import('views/inventory/common/goods-auto-complete.vue');

    export default {
        name: 'PurchaseTakeDelivery',
        components: {
            InboundOrderDialog,
            OrderList,
            GoodsImportParseDialog,
            ClinicSelect,
            EmployeeSelect,
            GoodsAutoComplete,
            OrderDialog,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager();

            const {
                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
            } = useSearchSupplier({
                excludeInitSupplier: true,
            });

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
            };
        },
        data() {
            const now = new Date();
            const nowDayOfWeek = now.getDay();
            const nowDay = now.getDate();
            const nowMonth = now.getMonth();
            const nowYear = now.getFullYear();
            now.setDate(1);
            now.setMonth(now.getMonth() - 1);
            const pickerOptions = {
                disabledDate(date) {
                    return date > new Date() || date < new Date('2001-11-01');
                },
                shortcuts: [{
                    text: '今天',
                    onClick(cb) {
                        const start = new Date();
                        const end = new Date();
                        cb([start, end]);
                    },
                }, {
                    text: '昨天',
                    onClick(cb) {
                        const end = new Date(nowYear, nowMonth, nowDay - 1);
                        const start = new Date(nowYear, nowMonth, nowDay - 1);
                        cb([start, end]);
                    },
                }, {
                    text: '本周',
                    onClick(cb) {
                        let start = new Date(nowYear, nowMonth, nowDay - 6);
                        if (nowDayOfWeek) {
                            start = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek + 1);
                        }
                        const end = new Date();
                        cb([start, end]);
                    },
                }, {
                    text: '本月',
                    onClick(cb) {
                        const start = new Date(nowYear, nowMonth, 1);
                        const end = new Date();
                        cb([start, end]);
                    },
                }],
            };
            return {
                CHECK_IN_SUPPLIER_ID,
                GoodsInImg,
                GoodsInMallImg,
                GoodsInExcelImg,
                GoodsInSupplier,
                describeList: [{
                    date: formatDate(new Date(), 'YYYY-MM-DD'),
                    describe: '今天',
                }],
                pickerOptions,
                showTypeDialog: false,
                showOrderDialog: false,
                showTemplateDialog: false,
                showPurchaseOrderListDialog: false,
                exportBtnLoading: false,
                tableLoading: false,
                searchKey: '',
                selectDate: [],
                orderId: '',
                orderStatus: '',
                parseData: null,
                selectPurchaseOrder: null,
                stockInType: undefined,
                showInboundOrder: false,
                fetchParams: {
                    clinicId: '',
                    goodsId: '',
                    keyword: '',
                    supplierId: '',
                    receiveBy: '',
                    dateStart: '',
                    dateEnd: '',
                    // 0:草稿 10:待收货 20:已收货
                    status: '',
                    // 来源类型 0:总部集采 10:门店自采 20:总部配货 30:门店调拨
                    type: '',
                    offset: 0,
                    limit: 10,
                },
                panelData: {
                    rows: [],
                    todo: {
                        waitReceiveCount: 0,
                        receivedCount: 0,
                    },
                    total: 0,
                    totalPrice: 0,
                },
                mallGoodsList: [],
                mallOrderId: undefined,
                importDraftId: '',
                isImport: false,
            };
        },
        computed: {
            ...mapGetters([
                'stockEmployeeList',
                'isAdmin',
                'isChainAdmin',
                'isSingleStore',
                'currentClinic',
                'currentPharmacy',
            ]),
            openTypeEnum() {
                return {
                    purchase: 1, // 手动添加
                    excel: 2, // 导入Excel模板
                    order: 3, // 选择采购订单
                    mall: 4, // 商城一键入库
                };
            },
            PurchaseReceiveOrderStatus() {
                return PurchaseReceiveOrderStatus;
            },
            tablePagination() {
                const {
                    limit, offset,
                } = this.fetchParams;
                return {
                    showTotalPage: false,
                    pageIndex: (offset / limit),
                    pageSize: limit,
                    count: this.panelData.total || 0,
                };
            },
            ReceiptOrginOrderTypeName() {
                return ReceiptOrginOrderTypeName;
            },
            PurchaseReceiveOrderStatusName() {
                return PurchaseReceiveOrderStatusName;
            },
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            pharmacyType() {
                return this.currentPharmacy?.type;
            },
            orderTypeOptions() {
                return [
                    {
                        label: '总部集采(自配)',
                        value: ReceiptOrginOrderType.CONCENTRATE,
                    },
                    {
                        label: '总部集采(委配)',
                        value: ReceiptOrginOrderType.CONCENTRATE_SEPARATE,
                    },
                    {
                        label: '门店自采',
                        value: ReceiptOrginOrderType.SEPARATE,
                    },
                    // {
                    //     label: '总部配货',
                    //     value: ReceiptOrginOrderType.DELIVERY,
                    // },
                    // {
                    //     label: '门店调拨',
                    //     value: ReceiptOrginOrderType.TRANS,
                    // },
                ];
            },
            statusOptions() {
                const {
                    waitAuditCount = 0,
                    waitReceiveCount = 0,
                } = this.panelData.todo || {};
                return [
                    {
                        label: '待审核',
                        value: PurchaseReceiveOrderStatus.REVIEW,
                        statisticsNumber: waitAuditCount || undefined,
                    },
                    {
                        label: '待收货',
                        value: PurchaseReceiveOrderStatus.TAKE_DELIVERY,
                        statisticsNumber: waitReceiveCount || undefined,
                    },
                    {
                        label: '已收货',
                        value: PurchaseReceiveOrderStatus.RECEIVED,
                        statisticsNumber: undefined,
                    },
                    {
                        label: '已驳回',
                        value: PurchaseReceiveOrderStatus.REJECT,
                        statisticsNumber: undefined,
                    },
                    {
                        label: '已撤销',
                        value: PurchaseReceiveOrderStatus.WITH_DRAW,
                        statisticsNumber: undefined,
                    },
                ];
            },

            renderConfig() {
                const tableConfig = new TakeDeliveryTableConfig(this.currentClinic);
                return tableConfig.extendConfig({
                    amount: {
                        dataFormatter: (amount) => {
                            return isNull(amount) ? '' : formatMoney(amount, false);
                        },
                    },
                });
            },
            isDialogShowing() {
                return this.showTemplateDialog || this.showOrderDialog || this.showTypeDialog || this.showPurchaseOrderListDialog;
            },
        },
        watch: {
            showOrderDialog(val) {
                if (!val) {
                    this.mallOrderId = undefined;
                }
            },
        },
        methods: {
            formatCacheTime,
            formatMoney,
            getSourceOrder,
            handleMounted(data) {
                this.fetchParams.limit = data.paginationLimit || 10;
                this.initOffset();
            },
            async handleCreateInOrderByMall(data) {
                const {
                    draftId,
                    goodsList,
                    mallOrderId,
                } = data;

                this.importDraftId = draftId;
                this.mallGoodsList = goodsList;
                this.mallOrderId = mallOrderId;

                this.isImport = true;
                this.showTemplateDialog = true;
                this.showInboundOrder = false;
            },
            getSourceOrderStr(row) {
                const [order] = this.getSourceOrder((row.relatedOrders?.list ?? []), RelatedOrderType.PURCHASE);
                return order?.orderNo || '-';
            },
            statusConfig(row) {
                const {
                    status, clinicId,
                } = row;

                const canReceive = (status === PurchaseReceiveOrderStatus.TAKE_DELIVERY) && (clinicId === this.currentClinic.clinicId);


                const warningConfig = {
                    theme: 'warning',
                    variant: 'outline',
                };
                const waitConfig = {
                    theme: 'primary',
                    variant: canReceive ? 'light-outline' : 'outline',
                };
                const successConfig = {
                    theme: 'success',
                    variant: 'outline',
                };
                const dangerConfig = {
                    theme: 'danger',
                    variant: 'outline',
                };
                const defaultConfig = {
                    theme: 'default',
                    variant: 'outline',
                };
                const configMap = {
                    [PurchaseReceiveOrderStatus.DRAFT]: warningConfig,
                    [PurchaseReceiveOrderStatus.REVIEW]: waitConfig,
                    [PurchaseReceiveOrderStatus.TAKE_DELIVERY]: waitConfig,
                    [PurchaseReceiveOrderStatus.RECEIVED]: successConfig,
                    [PurchaseReceiveOrderStatus.REJECT]: dangerConfig,
                    [PurchaseReceiveOrderStatus.WITH_DRAW]: defaultConfig,
                };
                return configMap[row.status] || {};

            },
            createParams() {
                const params = {
                    ...this.fetchParams,
                };
                if (params.goodsId) {
                    params.keyword = '';
                } else {
                    params.keyword = this.searchKey;
                }
                if (this.selectDate.length) {
                    params.dateStart = this.selectDate[0];
                    params.dateEnd = this.selectDate[1];
                } else {
                    params.dateStart = '';
                    params.dateEnd = '';
                }
                return params;
            },
            async fetchData() {
                try {
                    this.tableLoading = true;
                    const beforeParams = this.createParams();
                    const { data } = await GoodsAPIV3.getPurchaseReceiveOrderList(beforeParams);
                    const afterParams = this.createParams();
                    if (isEqual(beforeParams, afterParams) && data) {
                        this.panelData = data;
                    }
                } catch (err) {
                    console.error(err);
                } finally {
                    this.tableLoading = false;
                }
            },
            searchGoods(val = '') {
                this.fetchParams.keyword = val.trim();
                this.fetchParams.goodsId = '';

                this.initOffset();
            },
            selectGoods(goods) {
                if (!goods) return;
                this.searchKey = goodsFullName(goods);
                this.fetchParams.goodsId = goods.id;
                this.fetchParams.keyword = '';


                this.initOffset();
            },

            clearSearch() {
                this.searchKey = '';
                this.fetchParams.keyword = '';
                this.fetchParams.goodsId = '';

                this.initOffset();
            },
            initOffset() {
                this.fetchParams.offset = 0;
                this.fetchData();
            },
            disabledItemFunc(item) {
                return !!item.v2DisableStatus;
            },
            itemClassStr() {
                return 'clickable';
            },
            handleClickTr(item) {
                console.log('handleClickTr', item);
                this.showOrderDialog = true;
                this.orderId = item.id;
                this.orderStatus = item.status;
            },
            sortChange(res) {
                console.log('sortChange', res);
            },
            pageChange(page) {
                const offset = (page - 1) * this.fetchParams.limit;
                this.fetchParams.offset = offset;
                this.fetchData();
            },
            handleAdd() {
                this.showTypeDialog = true;
            },
            handleOpenType(type) {
                if (type === this.openTypeEnum.mall) {
                    this.stockInType = GoodsInFromTypeEnum.MALL;
                    this.showInboundOrder = true;
                }
                if (type === this.openTypeEnum.purchase) {
                    this.showOrderDialog = true;
                }
                if (type === this.openTypeEnum.excel) {
                    this.showTemplateDialog = true;
                }
                if (type === this.openTypeEnum.order) {
                    this.showPurchaseOrderListDialog = true;
                }
                this.orderId = '';
                this.orderStatus = '';
                this.mallOrderId = undefined;
                this.parseData = null;
                this.selectPurchaseOrder = null;
                this.$nextTick(() => {
                    this.showTypeDialog = false;
                });
            },
            importProjectAfter(data) {
                this.showTemplateDialog = false;
                // 打开入库弹窗
                this.showOrderDialog = true;

                this.parseData = {
                    ...data,
                };
            },
            handleSelectOrder(order) {
                this.selectPurchaseOrder = order;
                this.showPurchaseOrderListDialog = false;
                this.$nextTick(() => {
                    this.showOrderDialog = true;
                });
            },
            /**
             * 刷新列表
             * @param type {number} 1新增 2修改
             * @returns {unknown}
             */
            handleRefresh(type = 1) {
                if (type === 1) {
                    this.initOffset();
                } else {
                    this.fetchData();
                }
            },
            async handleExport() {
                try {
                    this.exportBtnLoading = true;
                    const exportParams = this.createParams();
                    await GoodsAPIV3.exportReceiveList(exportParams);
                } catch (e) {
                    console.error(e);
                    const { message } = e;
                    this.$Toast({
                        message,
                        type: 'error',
                        duration: 1000,
                    });
                } finally {
                    this.exportBtnLoading = false;
                }
            },
        },
    };
</script>
