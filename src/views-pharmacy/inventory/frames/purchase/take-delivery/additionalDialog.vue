<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        title="补录采购订单"
        size="medium"
        append-to-body
        :disabled-keyboard="submitBtnLoading"
        @open="pushDialogName"
        @close="popDialogName"
    >
        <abc-tips-card-v2 slot="top-extend" theme="primary" border-radius>
            应GSP管理规范要求，需补录采购订单才可进行收货
        </abc-tips-card-v2>
        <abc-layout>
            <abc-section>
                <abc-form
                    ref="form"
                    label-position="left"
                    :item-no-margin="false"
                    :label-width="80"
                >
                    <abc-form-item :label="'采购机构'">
                        <span v-abc-title.ellipsis="clinicName"></span>
                    </abc-form-item>
                    <abc-form-item :label="'供应商'" required>
                        <abc-popover
                            placement="top"
                            trigger="hover"
                            theme="yellow"
                            :disabled="!(!isErpDirect && supplieExpired(purchaseOrder.supplierId))"
                            :popper-style="{
                                padding: '16px 16px 8px 16px',
                            }"
                            data-cy="storybook-test-base1"
                        >
                            <abc-flex
                                v-for="(it, index) in gualificationList(purchaseOrder.supplierId)"
                                :key="index"
                                style="width: 100%; margin-bottom: 8px;"
                                align="center"
                                :gap="16"
                                justify="space-between"
                            >
                                <abc-text theme="gray">
                                    {{ it.name }}
                                </abc-text>
                                <abc-text>已过期</abc-text>
                            </abc-flex>
                            <abc-select
                                slot="reference"
                                v-model="purchaseOrder.supplierId"
                                custom-class="supplierWrapper"
                                with-search
                                clearable
                                :input-style="(!isErpDirect && supplieExpired(purchaseOrder.supplierId)) ? {
                                    color: '#D72E22 !important',
                                } : {}"
                                placeholder="供应商"
                                :disabled="isErpDirect"
                                :fetch-suggestions="fetchSuggestions"
                                @open="supplierListRetry"
                                @change="supplierChange"
                            >
                                <abc-option
                                    v-for="it in supplierOptionsNormal"
                                    :key="`${it.id }`"
                                    :value="it.id"
                                    :label="it.name"
                                ></abc-option>
                                <abc-option
                                    v-if="supplierOptionsExpired.length"
                                    style="align-items: flex-end; height: 24px; min-height: 24px;"
                                    value="-2"
                                    disabled
                                >
                                    资质过期
                                </abc-option>
                                <abc-option
                                    v-for="it in supplierOptionsExpired"
                                    :key="`${it.id }`"
                                    :value="it.id"
                                    :label="it.name"
                                >
                                    <abc-text theme="danger">
                                        {{ it.name }}
                                    </abc-text>
                                </abc-option>
                            </abc-select>
                        </abc-popover>
                    </abc-form-item>
                    <abc-form-item v-if="isChain" label="类型">
                        <span v-abc-title.ellipsis="formatSupplierTypeName(supplier) || '-'"></span>
                    </abc-form-item>
                    <abc-form-item label="收货门店" required>
                        <!--                        <span v-abc-title.ellipsis="clinicName"></span>-->
                        <clinic-select
                            v-model="purchaseOrder.receiveClinicId"
                            placeholder="总部/门店"
                            :disabled="!isChainAdmin || showClaimClinic"
                            :show-all-clinic="false"
                            style="width: 100%;"
                            clearable
                            @change="changeClaimClinicId"
                        ></clinic-select>
                    </abc-form-item>
                    <abc-form-item v-if="showClaimClinic" label="要货门店">
                        <clinic-select
                            v-model="purchaseOrder.claimClinicId"
                            placeholder="总部/门店"
                            :show-all-clinic="false"
                            style="width: 100%;"
                            clearable
                        ></clinic-select>
                    </abc-form-item>
                    <abc-form-item :label="'采购人'" required>
                        <employee-select
                            v-model="purchaseOrder.purchaseBy"
                            adaptive-width
                            :employee-list="stockEmployeeList"
                        >
                        </employee-select>
                    </abc-form-item>
                    <abc-form-item :label="'采购日期'" required>
                        <abc-date-picker
                            v-model="purchaseOrder.purchaseOrderDate"
                            :width="280"
                        >
                        </abc-date-picker>
                    </abc-form-item>

                    <abc-form-item :label="'采购品种'">
                        <span>确认收货后，系统将按照实际收货品种自动填充</span>
                    </abc-form-item>

                    <abc-form-item :label="'采购金额'">
                        <span>确认收货后，系统将按照实际收货金额自动填充</span>
                    </abc-form-item>
                </abc-form>
            </abc-section>
        </abc-layout>

        <template slot="footer">
            <div class="dialog-footer">
                <abc-space>
                    <abc-button
                        :loading="submitBtnLoading"
                        @click="handleSubmit"
                    >
                        确定
                    </abc-button>
                    <abc-button
                        type="blank"
                        @click="showDialog = false"
                    >
                        取消
                    </abc-button>
                </abc-space>
            </div>
        </template>
    </abc-dialog>
</template>

<script>
    import EmployeeSelect from '@/views-pharmacy/inventory/frames/components/employee-select.vue';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import { CHECK_IN_SUPPLIER_ID } from 'views/inventory/constant';
    import { mapGetters } from 'vuex';
    import {
        ReceiptOrginOrderType, EntrustDeliveryType,
    } from '@/views-pharmacy/inventory/constant';
    import {
        formatDate,
    } from '@abc/utils-date';
    import { clinicName } from '@/filters';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    import { formatSupplierTypeName } from '@/views-pharmacy/inventory/utils';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';

    export default {
        name: 'AdditionalDialog',
        components: {
            ClinicSelect, EmployeeSelect,
        },
        props: {
            value: Boolean,
            isErpDirect: Boolean,
            sourceType: Number,
            purchaseBy: String,
            supplierId: String,
            purchaseOrderDate: String,
            currentId: {
                type: String,
                default: '',
            },
            claimClinicId: {
                type: String,
                default: '',
            },
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('补录采购订单弹窗');

            const {
                currentSupplierList,
                fetchSuggestions,
                supplierListRetry,
            } = useSearchSupplier({
                status: 1,
                excludeInitSupplier: true,
            });

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                currentSupplierList,
                fetchSuggestions,
                supplierListRetry,
            };
        },
        data() {
            return {
                CHECK_IN_SUPPLIER_ID,
                showDialog: this.value,
                submitBtnLoading: false,
                purchaseOrder: {
                    supplierId: this.supplierId,
                    sourceType: this.sourceType,
                    purchaseBy: this.purchaseBy,
                    purchaseOrderDate: this.purchaseOrderDate,
                    claimClinicId: undefined, // 要货门店
                    receiveClinicId: undefined, // 收货门店
                },
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'isChain',
                'isChainAdmin',
                'isSingleStore',
                'userInfo',
                'stockEmployeeList',
                'currentClinic',
                'currentPharmacy',
            ]),
            showClaimClinic() {
                if (!this.isChainAdmin) {
                    return false;
                }
                return this.supplier?.isEntrustDelivery && this.supplier.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY;
            },
            supplierOptions() {
                return this.currentSupplierList.map((item) => {
                    const handleItem = this.getGualificationStatus(item);
                    return {
                        ...item,
                        isExpired: handleItem.isExpired,
                        expiredList: handleItem.expiredList,
                    };
                });
            },
            supplierOptionsNormal() {
                return this.supplierOptions?.filter((item) => {
                    if (this.isChain && !this.isChainAdmin) { // 连锁子店需过滤掉集采自配
                        return !item.isExpired && !(item.isEntrustDelivery && item.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY);
                    }
                    return !item.isExpired;
                });
            },
            supplierOptionsExpired() {
                return this.supplierOptions?.filter((item) => {
                    if (this.isChain && !this.isChainAdmin) { // 连锁子店需过滤掉集采自配
                        return item.isExpired && !(item.isEntrustDelivery && item.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY);
                    }
                    return item.isExpired;
                });
            },
            clinicName() {
                return clinicName(this.currentClinic, false);
            },
            supplier() {
                return this.supplierOptions.find((s) => s.id === this.purchaseOrder.supplierId);
            },
            purchaseOptions() {
                return [
                    {
                        label: '总部集采(自配)',
                        value: ReceiptOrginOrderType.CONCENTRATE,
                        isShow: this.isChain && !this.supplier?.isEntrustDelivery,
                    },
                    {
                        label: '总部集采(委配)',
                        value: ReceiptOrginOrderType.CONCENTRATE_SEPARATE,
                        isShow: this.isChain && this.supplier?.isEntrustDelivery,
                    },
                    {
                        label: '门店自采',
                        value: ReceiptOrginOrderType.SEPARATE,
                        isShow: this.isSingleStore ? true : (!this.isChainAdmin && !this.supplier?.isEntrustDelivery),
                    },
                ].filter((e) => e.isShow);
            },

        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
            currentId(val) {
                console.log('收货门店', val);
            },
        },
        created() {
            this.purchaseOrder = {
                supplierId: this.supplierId,
                sourceType: this.sourceType,
                purchaseBy: this.purchaseBy || this.userInfo.id,
                purchaseOrderDate: this.purchaseOrderDate || formatDate(Date.now(),'YYYY-MM-DD'),
                claimClinicId: this.claimClinicId, // 要货门店
                receiveClinicId: this.currentId, // 收货门店
            };
            if (!this.isChainAdmin) {
                this.purchaseOrder.receiveClinicId = this.currentClinic?.clinicId || '';
            }
        },
        methods: {
            formatSupplierTypeName,
            gualificationList(id) {
                return this.supplierOptionsExpired?.find((item) => {
                    return item.id === id;
                })?.expiredList || [];
            },
            supplieExpired(id) {
                return this.supplierOptionsExpired?.find((item) => {
                    return item.id === id;
                });
            },
            // 获取经营范围
            getGualificationStatus(item) {
                const { extendInfo = {} } = item;
                const certificationInfos = extendInfo?.certificationInfos || [];
                if (!certificationInfos?.length) {
                    return {
                        isExpired: false,
                        expiredList: [],
                    };
                }
                const expiredItem = certificationInfos?.find((it) => {
                    const currentTime = this.getCurrentTime(it);
                    return this.isExpired(currentTime);
                });
                if (expiredItem) {
                    return {
                        isExpired: true,
                        expiredList: certificationInfos?.filter((it) => {
                            const currentTime = this.getCurrentTime(it);
                            return this.isExpired(currentTime);
                        }) || [],
                    };
                }
                return {
                    isExpired: false,
                    expiredList: [],
                };
            },
            // 是否已到期
            isExpired(currentTime) {
                const date1 = new Date(currentTime);
                const date2 = new Date();
                return date1.getTime() - date2.getTime() < 0;
            },
            getCurrentTime(item) {
                if (!item?.validTo) {
                    return '';
                }
                const validToDate = new Date(item.validTo);
                // 格式化为 YYYY-MM-DD 23:59:59
                const year = validToDate.getFullYear();
                const month = String(validToDate.getMonth() + 1).padStart(2, '0');
                const day = String(validToDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day} 23:59:59`;
            },
            changeClaimClinicId() {
                // 连锁总部非总部自采
                if (this.isChainAdmin && !(this.supplier?.isEntrustDelivery && this.supplier.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY)) {
                    this.purchaseOrder.claimClinicId = this.purchaseOrder.receiveClinicId;
                }
            },
            supplierChange() {
                if (this.isChain) {
                    if (this.supplier?.isEntrustDelivery) { // 集采
                        this.purchaseOrder.sourceType = ReceiptOrginOrderType.CONCENTRATE_SEPARATE;
                        if (this.supplier.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY) {
                            this.purchaseOrder.sourceType = ReceiptOrginOrderType.CONCENTRATE;
                            this.purchaseOrder.receiveClinicId = this.currentClinic?.clinicId;
                            // 总部带入要货门店
                        } else if (this.isChainAdmin) {
                            this.purchaseOrder.claimClinicId = this.purchaseOrder.receiveClinicId || '';
                        }
                    } else { // 自采
                        this.purchaseOrder.sourceType = ReceiptOrginOrderType.SEPARATE;
                    }
                } else {
                    this.purchaseOrder.sourceType = ReceiptOrginOrderType.SEPARATE;
                }
            },
            handleSubmit() {
                this.$refs.form.validate(async (val) => {
                    if (val) {
                        this.$emit('confirm', this.purchaseOrder);
                        this.showDialog = false;
                    }
                });
            },
        },
    };
</script>


