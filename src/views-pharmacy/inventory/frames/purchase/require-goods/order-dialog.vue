<template>
    <frame-dialog
        v-if="showDialog"
        ref="frameDialog"
        v-model="showDialog"
        :title="title"
        :order-no="titleOrderNo"
        :status-name="statusName"
        :show-title-append="showTitleAppend"
        :right-width="rightWidth"
        :before-close="closeDialog"
        :loading="pageLoading"
        :tag-config="tagConfig"
        :gsp-inst-id="gspInstId"
        responsive-dialog
        :class="[
            {
                'biz-pharmacy-add-purchase-dialog': isAdd && !sendToSupplier,
            }
        ]"
        @review-confirm="handleReviewConfirm"
    >
        <abc-form
            ref="form"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header v-if="!sendToSupplier">
                    <abc-form-item-group is-excel>
                        <!--采购-->
                        <abc-descriptions
                            v-if="isPurchaseOrder"
                            :column="3"
                            :label-width="100"
                            grid
                            size="large"
                            background
                            stretch-last-item
                        >
                            <abc-descriptions-item content-class-name="ellipsis" label="采购机构">
                                <span v-abc-title="organName"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item content-class-name="ellipsis" label="采购人">
                                <span v-abc-title="isAdd ? userInfo.name : order.applicant?.name"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                label="采购日期"
                                :content-style="descriptionsItemContentStyle"
                            >
                                <abc-form-item v-if="canHeaderEdit">
                                    <abc-date-picker
                                        v-model="order.purchaseOrderDate"
                                        :picker-options="pickerOptions"
                                        size="large"
                                    >
                                    </abc-date-picker>
                                </abc-form-item>
                                <span v-else v-abc-title="order.purchaseOrderDate || ''"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="isPurchaseOrder"
                                label="供应商"
                                content-class-name="ellipsis"
                                :content-style="descriptionsItemContentStyle"
                            >
                                <abc-form-item v-if="canHeaderEdit" required>
                                    <abc-popover
                                        placement="top"
                                        trigger="hover"
                                        theme="yellow"
                                        :popper-style="{
                                            padding: '16px 16px 8px 16px',
                                        }"
                                        :disabled="!supplieExpired(order.supplierId)"
                                    >
                                        <abc-flex
                                            v-for="(it, index) in gualificationList(order.supplierId)"
                                            :key="index"
                                            style="width: 100%; margin-bottom: 8px;"
                                            align="center"
                                            :gap="16"
                                            justify="space-between"
                                        >
                                            <abc-text theme="gray">
                                                {{ it.name }}
                                            </abc-text>
                                            <abc-text>已过期</abc-text>
                                        </abc-flex>
                                        <div slot="reference">
                                            <abc-select
                                                v-model="order.supplierId"
                                                custom-class="supplierWrapper"
                                                with-search
                                                clearable
                                                placeholder="供应商"
                                                inner-width="280px"
                                                placement="bottom-end"
                                                :fetch-suggestions="fetchSuggestions"
                                                style="height: 40px !important;"
                                                :input-style="supplieExpired(order.supplierId) ? {
                                                    color: '#D72E22 !important',
                                                } : {}"
                                                size="large"
                                                @change="handleSupplierChange"
                                            >
                                                <abc-option
                                                    v-for="it in supplierOptionsNormal"
                                                    :key="`${it.id }`"
                                                    :value="it.id"
                                                    :label="it.name"
                                                ></abc-option>
                                                <abc-option
                                                    v-if="supplierOptionsExpired.length"
                                                    style="align-items: flex-end; height: 24px; min-height: 24px;"
                                                    value="-2"
                                                    disabled
                                                >
                                                    资质过期
                                                </abc-option>
                                                <abc-option
                                                    v-for="it in supplierOptionsExpired"
                                                    :key="`${it.id }`"
                                                    :value="it.id"
                                                    :label="it.name"
                                                >
                                                    <abc-text theme="danger">
                                                        {{ it.name }}
                                                    </abc-text>
                                                </abc-option>
                                            </abc-select>
                                        </div>
                                    </abc-popover>
                                </abc-form-item>
                                <span v-else v-abc-title="order.supplier?.name || ''"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="isChain"
                                label="类型"
                                content-class-name="ellipsis"
                            >
                                <span v-abc-title="formatSupplierTypeName(curSelectedSupplier)"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="showClaimClinic"
                                label="要货门店"
                                content-class-name="ellipsis"
                                :content-style="descriptionsItemContentStyle"
                            >
                                <abc-form-item v-if="canHeaderEdit">
                                    <clinic-select
                                        v-model="order.claimClinicId"
                                        placeholder="总部/门店"
                                        :show-all-clinic="false"
                                        :filter-current-clinic="filterCurrentClinic"
                                        clearable
                                    ></clinic-select>
                                </abc-form-item>
                                <span v-else v-abc-title="claimClinicName"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="isChainAdmin"
                                label="收货门店"
                                content-class-name="ellipsis"
                                :content-style="descriptionsItemContentStyle"
                            >
                                <abc-form-item v-if="canHeaderEdit" required>
                                    <clinic-select
                                        v-model="order.applicantOrganId"
                                        placeholder="总部/门店"
                                        :show-all-clinic="false"
                                        :filter-current-clinic="filterCurrentClinic"
                                        :disabled="disableApplicantOrgan"
                                        clearable
                                        @change="changeClaimOrderId"
                                    ></clinic-select>
                                </abc-form-item>
                                <span
                                    v-else
                                    v-abc-title="order?.applicantOrgan?.shortNameFirst || formatClinicName(order.applicantOrgan, false) || ''"
                                ></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="receiveOrders.length"
                                content-class-name="ellipsis"
                                label="关联收货单"
                            >
                                <order-no-comp :orders="receiveOrders" :only-view="true"></order-no-comp>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="inspectOrders.length"
                                content-class-name="ellipsis"
                                label="关联验收单"
                            >
                                <order-no-comp :orders="inspectOrders" :only-view="true"></order-no-comp>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="inOrders.length"
                                content-class-name="ellipsis"
                                label="关联入库单"
                            >
                                <order-no-comp :orders="inOrders" :only-view="true"></order-no-comp>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                label="备注"
                                :content-style="descriptionsItemContentStyle"
                            >
                                <abc-form-item v-if="canHeaderEdit">
                                    <abc-input
                                        v-model="order.comment"
                                        :max-length="200"
                                        size="large"
                                        @enter="enterEvent"
                                    ></abc-input>
                                </abc-form-item>
                                <span v-else v-abc-title="order.comment || ''"></span>
                            </abc-descriptions-item>
                        </abc-descriptions>
                        <!--采购-->
                        <!--要货-->
                        <abc-descriptions
                            v-else
                            :column="5"
                            :label-width="(!isPurchaseOrder && purchaseOrders.length) ? 106 : 100"
                            grid
                            size="large"
                            background
                            stretch-last-item
                        >
                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :label="'要货门店'"
                            >
                                <span v-abc-title="organName"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :label="'要货人'"
                            >
                                <span v-abc-title="isAdd ? userInfo.name : order.applicant?.name"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                :label="'要货日期'"
                                :content-style="descriptionsItemContentStyle"
                            >
                                <abc-form-item v-if="canHeaderEdit">
                                    <abc-date-picker
                                        v-model="order.purchaseOrderDate"
                                        :picker-options="pickerOptions"
                                        size="large"
                                    >
                                    </abc-date-picker>
                                </abc-form-item>
                                <span v-else v-abc-title="order.purchaseOrderDate || ''"></span>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="!isPurchaseOrder && purchaseOrders.length"
                                content-class-name="ellipsis"
                                label="关联采购订单"
                                :label-width="120"
                            >
                                <order-no-comp :orders="purchaseOrders" :only-view="true"></order-no-comp>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="!isPurchaseOrder && deliveryOrders.length"
                                content-class-name="ellipsis"
                                label="关联配货单"
                            >
                                <order-no-comp :orders="deliveryOrders" :only-view="true"></order-no-comp>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="receiveOrders.length"
                                content-class-name="ellipsis"
                                label="关联收货单"
                            >
                                <order-no-comp :orders="receiveOrders" :only-view="true"></order-no-comp>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="inspectOrders.length"
                                content-class-name="ellipsis"
                                label="关联验收单"
                            >
                                <order-no-comp :orders="inspectOrders" :only-view="true"></order-no-comp>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                v-if="inOrders.length"
                                content-class-name="ellipsis"
                                label="关联入库单"
                            >
                                <order-no-comp :orders="inOrders" :only-view="true"></order-no-comp>
                            </abc-descriptions-item>

                            <abc-descriptions-item
                                content-class-name="ellipsis"
                                label="备注"
                                :content-style="descriptionsItemContentStyle"
                            >
                                <abc-form-item v-if="canHeaderEdit">
                                    <abc-input
                                        v-model="order.comment"
                                        :max-length="200"
                                        @enter="enterEvent"
                                    ></abc-input>
                                </abc-form-item>
                                <span v-else v-abc-title="order.comment || ''"></span>
                            </abc-descriptions-item>
                        </abc-descriptions>
                        <!--要货-->
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        class="focus-table"
                        :support-delete-tr="supportDeleteTr"
                        :show-hover-tr-bg="false"
                        :loading="tableLoading"
                        :render-config="renderConfig"
                        :data-list="order.list"
                        empty-size="small"
                        :custom-tr-key="createTrKey"
                        :custom-tr-class="(tr)=>`abc-table-tr--${createTrKey(tr)}`"
                        need-delete-confirm
                        :virtual-list-config="{
                            bufferSize: 10,
                            threshold: 30,
                            rowHeight: 41
                        }"
                        @delete-tr="handleDeleteTr"
                        @sortChange="handleSortChange"
                    >
                        <template v-if="supportAddGoods" #topHeader>
                            <goods-auto-complete-cover-title
                                ref="goodsAutoCompleteRef"
                                class="back-focus-to-autocomplete"
                                placeholder="输入商品名称或扫码添加"
                                :search="searchKey"
                                :focus-show="true"
                                :enable-local-search="false"
                                :enable-barcode-detector="!addMedicineDialogVisible"
                                show-last-supplier
                                show-purchasing
                                show-empty
                                show-remark
                                :resident-sugguestions="!addMedicineDialogVisible"
                                :width="460"
                                :inorder-config="0"
                                :clinic-id="goodsSearchClinicId"
                                :need-tooltip="true"
                                :biz-scope-list="curSelectedSupplierBizScopeList"
                                @selectGoods="selectGoods"
                                @clearTraceCode="clearTraceCode"
                                @focus="focusGoodsAutoComplete"
                            >
                                <abc-icon
                                    slot="prepend"
                                    icon="n-add-line-medium"
                                    color="var(--abc-color-T3)"
                                ></abc-icon>
                                <div
                                    slot="fixed-footer"
                                    class="inventory__fixed-footer-wrapper"
                                    @click.stop=""
                                >
                                    <add-archive-dropdown
                                        :is-show-toast="false"
                                        :success-callback="selectGoods"
                                        @openAddDialog="addMedicineDialogVisible = true"
                                        @closeAddDialog="addMedicineDialogVisible = false"
                                    ></add-archive-dropdown>
                                </div>
                            </goods-auto-complete-cover-title>
                        </template>

                        <template #shortId="{ trData: row }">
                            <abc-table-cell v-if="row.goods" class="ellipsis">
                                <overflow-tooltip :content="row.goods.shortId ?? '-'">
                                </overflow-tooltip>
                            </abc-table-cell>
                        </template>

                        <template #displayName="{ trData: row }">
                            <display-name-cell
                                v-if="row.goods"
                                :vertical="false"
                                :goods="row.goods"
                                :show-tooltip="showTooltip(row.goods)"
                            ></display-name-cell>
                        </template>

                        <template #lastPackageCostPrice="{ trData: row }">
                            <abc-table-cell
                                v-if="!isNull(row.hisGoodsStockStatInfo?.lastPackageCostPrice)"
                                class="ellipsis"
                            >
                                <abc-flex align="center" justify="space-between" style="width: 100%;">
                                    <abc-flex :gap="4">
                                        <abc-text theme="black">
                                            {{ row.hisGoodsStockStatInfo.lastPackageCostPrice | formatMoney(false) }}
                                        </abc-text>

                                        <view-cost-price-dialog
                                            :id="row.goodsId || row.goods?.id"
                                            slot-style="position: relative;"
                                            :triangle="false"
                                            :open-delay="250"
                                            :clinic-id="goodsSearchClinicId"
                                        >
                                            <abc-icon icon="n-price-trend-line-medium" color="#2680f7"></abc-icon>
                                        </view-cost-price-dialog>
                                    </abc-flex>
                                    <abc-text
                                        theme="gray"
                                        size="mini"
                                        style="max-width: 60%;"
                                        :title="row.hisGoodsStockStatInfo?.lastStockInOrderSupplier || row.hisGoodsStockStatInfo?.lastSupplierName || ''"
                                        class="ellipsis"
                                    >
                                        {{
                                            row.hisGoodsStockStatInfo?.lastStockInOrderSupplier || row.hisGoodsStockStatInfo?.lastSupplierName || ''
                                        }}
                                    </abc-text>
                                </abc-flex>
                            </abc-table-cell>
                            <abc-table-cell v-else>
                                -
                            </abc-table-cell>
                        </template>

                        <template #purchaseCount="{ trData: row }">
                            <abc-form-item v-if="canEdit" required>
                                <abc-input
                                    v-model="row.purchaseCount"
                                    v-abc-focus-selected
                                    :config="{
                                        max: row.hisGoodsStockStatInfo?.recommendView?.stockCapacityMaxLimit || 9999999,
                                        supportZero: false,
                                        formatLength: getFormatLength(row, 'purchaseUnit')
                                    }"
                                    type="number"
                                    class="focus-input"
                                    @enter="enterEvent"
                                    @change="calTotalPrice(row)"
                                ></abc-input>
                            </abc-form-item>
                            <abc-table-cell v-else>
                                <span v-abc-title="row.purchaseCount || '-'"></span>
                            </abc-table-cell>
                        </template>

                        <template #supplier="{ trData: row }">
                            <abc-form-item required style="width: 100%;">
                                <abc-select
                                    v-model="row.supplierId"
                                    custom-class="supplierWrapper"
                                    with-search
                                    clearable
                                    adaptive-width
                                    placeholder="供应商"
                                    inner-width="280px"
                                    placement="bottom-end"
                                    :fetch-suggestions="fetchSuggestions"
                                >
                                    <abc-option
                                        v-for="it in filterCurrentSupplierList"
                                        :key="`${it.id }`"
                                        :value="it.id"
                                        :label="it.name"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </template>

                        <template #purchaseUnit="{ trData: row }">
                            <abc-form-item v-if="canEdit && row.goods" required>
                                <abc-select
                                    v-model="row.purchaseUnit"
                                    :inner-width="56"
                                    :tabindex="-1"
                                    adaptive-width
                                    :input-style="{
                                        'text-align': 'center'
                                    }"
                                    @enter="enterEvent"
                                    @change="unitChange(row)"
                                >
                                    <!--<abc-option-->
                                    <!--    v-if="isChineseMedicine(row.goods)"-->
                                    <!--    label="kg"-->
                                    <!--    value="kg"-->
                                    <!--    style="justify-content: center;"-->
                                    <!--&gt;</abc-option>-->
                                    <abc-option
                                        v-if="row.goods.packageUnit"
                                        :label="row.goods.packageUnit"
                                        :value="row.goods.packageUnit"
                                        center
                                    ></abc-option>
                                    <abc-option
                                        v-if="row.goods.pieceUnit && !unitEqual(row.goods) && row.goods.dismounting"
                                        :label="row.goods.pieceUnit"
                                        :value="row.goods.pieceUnit"
                                        center
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-table-cell v-else>
                                <span v-abc-title.ellipsis="row.purchaseUnit || '-'"></span>
                            </abc-table-cell>
                        </template>

                        <template #packageCostPrice="{ trData: row }">
                            <abc-form-item v-if="canEdit">
                                <abc-input
                                    v-model="row.packageCostPrice"
                                    v-abc-focus-selected
                                    type="money"
                                    :config="getCostConfig(row.goods)"
                                    :input-custom-style="{ textAlign: 'right' }"
                                    @enter="enterEvent"
                                    @change="calTotalPrice(row)"
                                ></abc-input>
                            </abc-form-item>
                            <abc-table-cell v-else>
                                <span
                                    v-abc-title.ellipsis="isNull(row.packageCostPrice) ? '' : moneyDigit(row.packageCostPrice, 5)"
                                ></span>
                            </abc-table-cell>
                        </template>

                        <template #totalCost="{ trData: row }">
                            <abc-form-item v-if="canEdit">
                                <abc-input
                                    v-model="row.totalCost"
                                    v-abc-focus-selected
                                    type="money"
                                    :config="{
                                        formatLength: 2, max: 10000000, supportZero: true
                                    }"
                                    :input-custom-style="{ textAlign: 'right' }"
                                    @enter="enterEvent"
                                    @change="calCostPrice(row)"
                                ></abc-input>
                            </abc-form-item>
                            <abc-table-cell v-else>
                                <span
                                    v-abc-title.ellipsis="isNull(row.totalCost) ? '' : formatMoney(row.totalCost, false)"
                                ></span>
                            </abc-table-cell>
                        </template>

                        <template #footer>
                            <table-footer v-if="isPurchaseOrder" :list="order.list"></table-footer>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <template v-if="isAdd" #right>
            <div v-if="!sendToSupplier" v-abc-loading="rightLoading" class="purchase-sidebar-wrapper">
                <abc-flex
                    class="purchase-sidebar-tab"
                    align="center"
                    justify="space-between"
                >
                    <abc-tabs-v2
                        v-model="selectedTab"
                        :border="false"
                        :option="tabsOptions"
                        size="huge"
                        @change="changeFilterCondition"
                    ></abc-tabs-v2>
                    <abc-button
                        :disabled="!warnList || !warnList.length"
                        size="small"
                        variant="text"
                        @click="handleCheckAll"
                    >
                        全部加入
                    </abc-button>
                </abc-flex>
                <abc-flex class="purchase-filter-options" vertical="vertical" :gap="10">
                    <abc-tips-card-v2
                        v-if="isShowTurnoverDaysTipsCard && selectedTab === 0"
                        theme="primary"
                        custom-class="purchase-filter-options-tips-card"
                    >
                        门店预警：周转天数&lt;{{ goodsConfig?.stockGoodsConfig?.stockWarnGoodsTurnoverDays ?? '-' }}
                        <template #operate>
                            <abc-flex align="center" style="height: 100%;">
                                <abc-button
                                    v-if="hasWareHouseSetting"
                                    variant="text"
                                    size="small"
                                    @click="handleGoToUpdateTurnoverDays"
                                >
                                    去修改
                                </abc-button>
                                <abc-delete-icon
                                    theme="dark"
                                    @delete="isShowTurnoverDaysTipsCard = false"
                                ></abc-delete-icon>
                            </abc-flex>
                        </template>
                    </abc-tips-card-v2>
                    <abc-flex :gap="6">
                        <abc-cascader
                            v-model="selectedTypes"
                            :collapse="true"
                            :mutually-exclusive="false"
                            :options="goodsTypeOptions"
                            :props="{
                                label: 'name',
                                value: 'id',
                                children: 'children'
                            }"
                            :value-props="{
                                _label: 'name',
                                _value: 'id'
                            }"
                            :width="140"
                            multiple
                            placeholder="全部类型"
                            @change="handleChangeType"
                        >
                        </abc-cascader>

                        <abc-select
                            v-model="stockWarnParams.sbLimitPrice"
                            placeholder="医保限价"
                            :width="118"
                            clearable
                            @change="fetchStockWarnList"
                        >
                            <abc-option :value="1" label="有限价"></abc-option>
                            <abc-option :value="0" label="无限价"></abc-option>
                        </abc-select>
                        <stock-warn-filter-popover
                            :key="selectedTab"
                            :selected-tab="selectedTab"
                            @filter="handleStockWarnFilter"
                        ></stock-warn-filter-popover>
                    </abc-flex>
                </abc-flex>
                <abc-section
                    style="padding-left: 10px; margin-top: 8px;"
                    :style="{
                        height: calcHeight
                    }"
                >
                    <stock-warn-list
                        v-if="warnList && warnList.length"
                        :list="warnList"
                        :selected-ids.sync="selectedWarnIds"
                        :need-tooltip="true"
                        :biz-scope-list="curSelectedSupplierBizScopeList"
                        @select="handleCheck"
                    ></stock-warn-list>
                    <div v-else class="empty-content">
                        <abc-content-empty value="暂无数据" size="small"></abc-content-empty>
                    </div>
                </abc-section>
            </div>
        </template>

        <template v-else #right>
            <abc-layout v-abc-loading="rightLoading">
                <approval-flow v-if="!!instDetail" :inst-detail="instDetail"></approval-flow>
            </abc-layout>
        </template>

        <template v-if="onlyView" #footer>
            <abc-flex align="center" justify="flex-end">
                <abc-button
                    variant="ghost"
                    @click="handleCancel"
                >
                    关闭
                </abc-button>
            </abc-flex>
        </template>

        <template v-else #footer>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-button
                        v-if="isDraft"
                        :loading="deleteDraftBtnLoading"
                        :disabled="pageLoading || submitDraftBtnLoading || submitBtnLoading"
                        type="danger"
                        @click="deleteDraftOrder"
                    >
                        删除草稿
                    </abc-button>
                    <logs-v3-popover v-if="logs.length && !isResubmit" :logs="logs"></logs-v3-popover>
                </abc-space>
                <abc-space>
                    <template v-if="isAdd">
                        <abc-button
                            :loading="submitBtnLoading"
                            :disabled="pageLoading || submitDraftBtnLoading || deleteDraftBtnLoading"
                            :count="order.list.length"
                            @click="handleSubmitOrder(ReceiveActionType.FINISH)"
                        >
                            {{ submitBtnText }}
                        </abc-button>

                        <abc-button
                            v-if="!sendToSupplier"
                            :loading="submitDraftBtnLoading"
                            :disabled="pageLoading || disabledDraftBtn || submitBtnLoading || deleteDraftBtnLoading"
                            type="blank"
                            @click="handleSubmit(ReceiveActionType.DRAFT)"
                        >
                            保存草稿
                        </abc-button>
                    </template>

                    <template v-if="canReview">
                        <abc-button
                            :loading="resolveBtnLoading"
                            :disabled="pageLoading || rejectBtnLoading"
                            @click="handleResolve"
                        >
                            同意
                        </abc-button>
                        <abc-button
                            theme="danger"
                            variant="ghost"
                            :loading="rejectBtnLoading"
                            :disabled="pageLoading || resolveBtnLoading"
                            @click="handleReject"
                        >
                            驳回
                        </abc-button>
                    </template>

                    <abc-button
                        v-if="canRevoke"
                        type="danger"
                        :loading="pageLoading || revokeBtnLoading"
                        @click="handleRevoke"
                    >
                        撤销
                    </abc-button>

                    <template v-if="canProcess">
                        <abc-button
                            type="danger"
                            :loading="pageLoading || revokeBtnLoading"
                            @click="handleTermination"
                        >
                            终止要货
                        </abc-button>

                        <!--<abc-button-->
                        <!--    :disabled="pageLoading"-->
                        <!--    @click="handleDistribution"-->
                        <!--&gt;-->
                        <!--    总部配货-->
                        <!--</abc-button>-->

                        <abc-button
                            :disabled="pageLoading"
                            @click="handlePurchase"
                        >
                            向供应商采购
                        </abc-button>
                    </template>

                    <template v-if="canResubmit">
                        <abc-button @click="handleResubmit">
                            修改并重新发起
                        </abc-button>
                    </template>

                    <abc-button
                        v-if="canExport"
                        variant="ghost"
                        :disabled="pageLoading"
                        @click="handleExport"
                    >
                        导出
                    </abc-button>

                    <abc-button
                        type="blank"
                        @click="closeDialog"
                    >
                        {{ isAdd ? '取消' : '关闭' }}
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>

        <!--<template v-if="isOpenErp" #review-content="{ data }">-->
        <!--    <abc-layout v-if="data.pass">-->
        <!--        <abc-flex vertical>-->
        <!--            <abc-space>-->
        <!--                <abc-p gray>-->
        <!--                    审批结果-->
        <!--                </abc-p>-->
        <!--                <abc-title :style="`color: ${$store.state.theme.style.R2}`">-->
        <!--                    同意-->
        <!--                </abc-title>-->
        <!--            </abc-space>-->
        <!--            <abc-p gray>-->
        <!--                审批同意后，采购订单将发送给供应商-->
        <!--            </abc-p>-->
        <!--        </abc-flex>-->
        <!--    </abc-layout>-->
        <!--</template>-->
        <distribution-dialog
            v-if="showOrderDialog"
            v-model="showOrderDialog"
            order
            :require-order-list="order.list"
            :require-order-no="order.orderNo"
            :require-order-clinic-id="order.applicantOrganId"
            :claim-order-id="orderId"
            @refresh="handleRefresh"
        ></distribution-dialog>

        <require-dialog
            v-if="showPurchaseOrderDialog"
            v-model="showPurchaseOrderDialog"
            send-to-supplier
            :applicant-organ-id="applicantOrganIdProp"
            :claim-order-id="orderId"
            :clinic-name="formatClinicName(order.applicantOrgan, false)"
            :goods-list="order.list"
            @refresh="handleRefresh"
        ></require-dialog>


        <abc-dialog
            v-if="showPreOrderDialog"
            v-model="showPreOrderDialog"
            :title="'创建采购订单'"
            content-styles="width:640px;height:486px"
            append-to-body
            @open="pushDialogName"
            @close="popDialogName"
        >
            <abc-layout>
                <abc-section v-for="(item, index) in preOrders" :key="index">
                    <abc-descriptions
                        :column="1"
                        :label-width="88"
                        grid
                        size="large"
                        background
                        :custom-title-style="{ 'background-color': 'var(--abc-color-LY4)' }"
                    >
                        <template #title>
                            采购订单{{ index + 1 }}
                        </template>
                        <abc-descriptions-item v-if="!isChain" label="配送方式">
                            委托供应商配送到门店，门店收货/验收
                        </abc-descriptions-item>
                        <abc-descriptions-item label="采购机构">
                            {{ formatClinicName(order.purchaseOrgan, false) ?? currentClinic.clinicName }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="供应商">
                            {{ item.supplier?.name }}
                        </abc-descriptions-item>
                        <abc-descriptions-item v-if="isChain" label="类型">
                            {{ formatSupplierTypeName(item.supplier) }}
                        </abc-descriptions-item>
                        <abc-descriptions-item v-if="isChainAdmin" label="要货门店">
                            {{ clinicName }}
                        </abc-descriptions-item>
                        <abc-descriptions-item v-if="isChainAdmin" label="收货门店">
                            {{ formatReceiveClinicName(item.supplier) }}
                        </abc-descriptions-item>
                        <abc-descriptions-item label="采购品种">
                            {{ item.kindCount }}种
                        </abc-descriptions-item>
                        <abc-descriptions-item label="采购金额">
                            {{ formatMoney(item.totalCost, false) }}
                            {{
                                item.undeterminedKindCount ? `(${item.undeterminedKindCount}个品种未确定金额，可到货入库时补充)` : ''
                            }}
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-section>
            </abc-layout>

            <template slot="footer">
                <div class="dialog-footer">
                    <abc-space>
                        <abc-button
                            @click="handleSubmit(ReceiveActionType.FINISH)"
                        >
                            确定
                        </abc-button>
                        <abc-button
                            type="blank"
                            @click="handleCancelPurchase"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </div>
            </template>
        </abc-dialog>

        <abc-dialog
            v-if="showCheckGoodsDialog"
            v-model="showCheckGoodsDialog"
            :title="'提示'"
            content-styles="width:720px;"
            append-to-body
            @open="pushDialogName"
            @close="popDialogName"
        >
            <abc-layout>
                <abc-section>
                    <abc-tips icon theme="warning">
                        以下商品无法采购，请与供应商商品重新对码或删除商品继续下单
                    </abc-tips>
                </abc-section>
                <abc-section>
                    <abc-table
                        type="excel"
                        show-order
                        :show-hover-tr-bg="false"
                        :render-config="tableConfig"
                        :data-list="tableList"
                        empty-size="small"
                        style="height: 352px;"
                    >
                        <template #shortId="{ trData: row }">
                            <abc-table-cell v-if="row.goods" class="ellipsis">
                                <span>{{ row.goods.shortId }}</span>
                            </abc-table-cell>
                        </template>
                        <template #displayName="{ trData: row }">
                            <display-name-cell v-if="row.goods" :goods="row.goods"></display-name-cell>
                        </template>
                        <template #supplierName="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span>{{
                                    supplierName(sendToSupplier ? row.supplierId : order.supplierId) || '-'
                                }}</span>
                            </abc-table-cell>
                        </template>
                        <template #purchaseCount="{ trData: row }">
                            <abc-table-cell class="ellipsis">
                                <span>{{ row.purchaseCount }}{{ row.purchaseUnit }}</span>
                            </abc-table-cell>
                        </template>
                    </abc-table>
                </abc-section>
            </abc-layout>
            <template slot="footer">
                <div class="dialog-footer">
                    <abc-space>
                        <abc-button
                            type="blank"
                            @click="handleFocusSubmit"
                        >
                            删除并继续下单
                        </abc-button>
                        <abc-button
                            type="blank"
                            @click="showCheckGoodsDialog = false"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </div>
            </template>
        </abc-dialog>
    </frame-dialog>
</template>

<script>
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';
    import StockWarnList from '@/views-pharmacy/inventory/frames/components/stockWarnList.vue';
    import useSupplierPinYinSort from '@/hooks/business/use-supplier-pin-yin-sort';
    import {
        PurchaseOrderStatus,
        PurchaseOrderStatusName,
        PurchaseOrderType,
        ReceiptOrginOrderType,
        RelatedOrderType,
        PurchaseOrderStatusTagTheme,
        BIT_FLAG_PURCHASE_APPLY_CHAIN_ENTRUST_DELIVERY, BIT_FLAG_PURCHASE_APPLY_CLINIC_SELF, EntrustDeliveryType,
    } from '@/views-pharmacy/inventory/constant';
    import PurchaseAPI from 'api/purchase';
    import GoodsBaseAPI from 'api/goods';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import { watch } from 'vue';

    import {
        mapActions, mapGetters,
    } from 'vuex';
    import { CHECK_IN_SUPPLIER_ID } from 'views/inventory/constant';
    import ApprovalFlow from '@/views-pharmacy/components/approval-flow/index.vue';
    import ApprovalAPI from 'api/pharmacy/approval';
    import {
        clinicName as formatClinicName,
        complexCount,
        count1,
        formatMoney,
        isChineseMedicine,
    } from '@/filters';
    import {
        cascaderDataAdapter,
        createGUID,
        isNotNull,
        isNull, moneyDigit,
    } from '@/utils';
    import {
        getCostConfig, getFormatLength, getSourceOrder, getUserGoodsUnit, setUserGoodsUnit, formatSupplierTypeName,
    } from '@/views-pharmacy/inventory/utils';

    // import {
    //     PharmacyNoKey, PharmacyTypeKey,
    // } from '@/views-pharmacy/inventory/provideKeys';
    import { formatDate } from '@abc/utils-date';
    import {
        calCostPriceSingle, calCostPriceTotal,
    } from '@/views-pharmacy/inventory/utils';
    import EnterEvent from 'views/common/enter-event';
    // import GoodsCountInput from '@/views-pharmacy/inventory/frames/components/goods-count-input.vue';
    import { GoodsTypeEnum } from '@abc/constants';
    // import Big from 'big.js';
    import {
        clone, isEqual,
    } from '@abc/utils';

    const DistributionDialog = () => import('@/views-pharmacy/inventory/frames/purchase/distribution/order-dialog.vue');
    const RequireDialog = () => import('@/views-pharmacy/inventory/frames/purchase/require-goods/order-dialog.vue');
    const GoodsAutoCompleteCoverTitle = () => import('views/inventory/common/goods-auto-complete-cover-title.vue');
    const ViewCostPriceDialog = () => import('views/inventory/common/view-cost-price-dialog.vue');
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    import OrderNoComp from '@/views-pharmacy/inventory/frames/components/orderNoComp.vue';
    import { unitEqual } from 'views/inventory/goods-utils';
    import BusinessGoods from '@/views-pharmacy/inventory/core/goods';
    import themeStyle from 'src/styles/theme.module.scss';

    const ReceiveActionType = Object.freeze({
        DRAFT: 0,
        CONFIRM: 1,
        FINISH: 2,
    });
    import AutoFocus from '@/views/inventory/mixins/auto-focus';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    import StockWarnFilterPopover from 'views/purchase/stock-warn-sidebar/stock-warn-filter-popover.vue';
    import { navigateToWarningProcurementSetting } from '@/core/navigate-helper';
    import { debounce } from 'utils/lodash';
    import GoodsAPI from 'api/goods';
    import ModulePermission from 'views/permission/module-permission';
    import { MODULE_ID_MAP } from 'utils/constants';
    import GoodsAPIStock from 'api/goods/stock-trans';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';


    export default {
        name: 'OrderDialog',
        components: {
            StockWarnFilterPopover,
            OrderNoComp,
            ClinicSelect,
            // GoodsCountInput,
            ViewCostPriceDialog,
            ApprovalFlow,
            GoodsAutoCompleteCoverTitle,
            FrameDialog,
            StockWarnList,
            DistributionDialog,
            RequireDialog,
            DisplayNameCell,
            LogsV3Popover,
            AddArchiveDropdown: () => import('views/inventory/goods/components/add-archive-dropdown.vue'),
            OverflowTooltip: () => import('@/components/overflow-tooltip.vue'),
            TableFooter: () => import('./table-footer.vue'),
        },
        mixins: [EnterEvent, AutoFocus, ModulePermission],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: Boolean,
            orderId: String,
            gspInstId: String,
            orderType: {
                type: Number,
                default: PurchaseOrderType.PURCHASE,
            },
            clinicName: String,
            sendToSupplier: {
                type: Boolean,
                default: false,
            },
            applicantOrganId: String,
            claimOrderId: String,
            // 从要货单向供应商采购来的
            goodsList: {
                type: Array,
                default: () => ([]),
            },
            orderStatus: {
                type: [Number, String],
                default: '',
            },
            onlyView: {
                type: Boolean,
                default: false,
            },
            isResubmit: Boolean,
        },
        setup() {
            const {
                disabledKeyboard, pushDialogName, popDialogName,
            } = useDialogStackManager('内部弹窗');

            const {
                currentSupplierList,
                supplierList,
                initSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
            } = useSearchSupplier({
                status: 1,
                excludeInitSupplier: true,
            });

            // 使用供应商拼音排序 hooks，传入供应商列表
            const {
                sortGoodsBySupplierPinyin, setSupplierList,
            } = useSupplierPinYinSort();

            watch(supplierList, (list) => {
                console.log('watch supplierList', list);
                if (list.length > 0) {
                    setSupplierList(supplierList.value);
                } else {
                    initSupplierList(true);
                }
            },{ immediate: true });

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,

                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,

                // 供应商拼音排序方法
                sortGoodsBySupplierPinyin,
            };
        },
        data() {
            return {
                themeStyle,
                ReceiveActionType,
                GoodsTypeEnum,
                CHECK_IN_SUPPLIER_ID,
                showDialog: this.value,
                showOrderDialog: false,
                showPurchaseOrderDialog: false,
                pageLoading: false,
                tableLoading: false,
                rightLoading: false,
                submitBtnLoading: false,
                submitDraftBtnLoading: false,
                deleteDraftBtnLoading: false,
                revokeBtnLoading: false,
                resolveBtnLoading: false,
                rejectBtnLoading: false,
                showPreOrderDialog: false,
                showCheckGoodsDialog: false,
                failGoodsList: [],
                preOrders: {},
                searchKey: '',
                order: {
                    status: this.orderStatus,
                    purchaseOrderDate: formatDate(Date.now(), 'YYYY-MM-DD'),
                    comment: '',
                    applicantId: '',
                    applicantOrganId: '',
                    applicant: {
                        id: '',
                        name: '',
                    },
                    purchaseOrgan: {},
                    clinicName: '',
                    orderNo: '',
                    supplierId: '',
                    list: [],
                    orderType: this.orderType,
                    claimClinicId: '', // 要货门店
                },
                instDetail: null,
                warnList: [],
                selectedWarnIds: [],
                pickerOptions: {
                    disabledDate(date) {
                        return date > new Date();
                    },
                },
                addMedicineDialogVisible: false,
                supplierDialogVisible: false,
                supplierValue: '',

                selectedTab: 0,
                isShowTurnoverDaysTipsCard: true,

                stockWarnParams: {
                    typeId: [],
                    customTypeId: [],
                },
                initStockWarnParams: {
                    typeId: [],
                    customTypeId: [],
                },
                goodsAllTypes: [],
                selectedTypes: [],
                warnDataCount: 0,

                filterCurrentClinic: false, // 总部模式下门店选择是否过滤当前门店
                disableApplicantOrgan: false,
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'isChain',
                'isChainAdmin',
                'currentClinic',
                'userInfo',
                'supplierList',
                'currentPharmacy',
                'goodsConfig',
            ]),
            showClaimClinic() {
                if (!this.isChainAdmin) {
                    return false;
                }
                if (this.isAdd) {
                    return this.curSelectedSupplier?.isEntrustDelivery && this.curSelectedSupplier?.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY;
                }
                // 连锁总部
                return this.order.claimClinicId;
            },
            hasWareHouseSetting() {
                return (
                    this.hasAdminModule ||
                    this.includesModuleId(MODULE_ID_MAP.bizPharmacySettingSubModule.wareHouseSetting)
                );
            },
            tabsOptions() {
                return [
                    {
                        label: '库存预警',
                        value: 0,
                        statisticsNumber: this.warnDataCount,
                    },
                    {
                        label: '全部商品',
                        value: 1,
                    },
                ];
            },
            goodsTypeOptions() {
                return this.goodsAllTypes.filter((item) => {
                    return item;
                });
            },
            logs() {
                return this.order?.logs ?? [];
            },
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            pharmacyType() {
                return this.currentPharmacy?.type;
            },
            isAdd() {
                return !this.orderId || this.isDraft || this.isResubmit;
            },
            isOpenErp() {
                return true;
            },
            isDraft() {
                return this.order.status === PurchaseOrderStatus.DRAFT;
            },
            isPurchaseOrder() {
                return this.order.orderType === PurchaseOrderType.PURCHASE;
            },
            organName() {
                if (this.isAdd) {
                    return this.currentClinic.clinicName || this.currentClinic.name;
                }

                return this.isPurchaseOrder ? (formatClinicName(this.order.purchaseOrgan, false) ?? '') : (formatClinicName(this.order.applicantOrgan, false) ?? '');
            },
            title() {
                if (this.sendToSupplier) {
                    return '向供应商采购';
                }
                return this.isPurchaseOrder ? '采购订单' : '要货单';
            },
            titleOrderNo() {
                if (this.sendToSupplier) {
                    return this.clinicName;
                }
                return this.order.orderNo;
            },
            statusName() {
                return PurchaseOrderStatusName[this.order.status];
            },
            tagConfig() {
                const config = {
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                };
                config.theme = PurchaseOrderStatusTagTheme[this.order.status];
                return config;
            },
            rightWidth() {
                if (this.sendToSupplier) return 0;
                if (this.isAdd || this.gspInstId) return 320;
                return 0;
            },
            showTitleAppend() {
                return !!this.orderId || this.sendToSupplier;
            },
            // 执行taskId
            taskId() {
                const target = (this.instDetail?.taskList || []).find((item) => item.execTaskId && item.status === 10);
                return target?.execTaskId || '';
            },
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            goodsSearchClinicId() {
                if (!this.isAdd) {
                    return this.isPurchaseOrder ? this.clinicId : this.order.applicantOrganId;
                }
                return this.clinicId;
            },
            purchaseOrders() {
                const orders = getSourceOrder(this.order.relatedOrders?.list ?? [], RelatedOrderType.PURCHASE);
                return orders || [];
            },
            deliveryOrders() {
                const orders = getSourceOrder(this.order.relatedOrders?.list ?? [], RelatedOrderType.DELIVERY);
                return orders || [];
            },
            receiveOrders() {
                const orders = getSourceOrder(this.order.relatedOrders?.list ?? [], RelatedOrderType.RECEIPT);
                return orders || [];
            },
            inspectOrders() {
                const orders = getSourceOrder(this.order.relatedOrders?.list ?? [], RelatedOrderType.INSPECT);
                return orders || [];
            },
            inOrders() {
                const orders = getSourceOrder(this.order.relatedOrders?.list ?? [], RelatedOrderType.GOODS_IN);
                return orders || [];
            },
            submitBtnText() {
                return this.isPurchaseOrder ? '创建订单' : '提交总部';
            },
            // 根据当前选择供应商判断是否开启采购审批
            isOpenApprove() {
                const supplier = this.currentSupplierList.find((e) => e.id === this.order.supplierId);
                // 根据选择的供应商-判断是否开启审批
                if (supplier?.isEntrustDelivery) {
                    return !!(this.goodsConfig.chainReview.chainExternalFlag & BIT_FLAG_PURCHASE_APPLY_CHAIN_ENTRUST_DELIVERY);
                }
                return !!(this.goodsConfig.chainReview.chainExternalFlag & BIT_FLAG_PURCHASE_APPLY_CLINIC_SELF);
            },
            isShowConfirmModal() {
                // 子店采购
                return this.isPurchaseOrder && !this.isAdmin && this.isOpenApprove;
            },
            canExport() {
                return !this.isAdd;
            },
            canProcess() {
                if (
                    this.isChainAdmin &&
                    this.order.status === PurchaseOrderStatus.ACTION &&
                    this.order.orderType === PurchaseOrderType.REQUIRE
                ) {
                    return true;
                }
                return false;
            },
            canRevoke() {
                // 待审核、待收货的采购订单，创建人支持手动撤销采购订单
                if (this.isPurchaseOrder) {
                    if (this.order.status === PurchaseOrderStatus.REVIEW || this.order.status === PurchaseOrderStatus.TAKE_DELIVERY) {
                        // 提交人是自己
                        return (this.order.applicantId === this.userInfo.id);
                    }
                } else {
                    if (this.order.status === PurchaseOrderStatus.REVIEW) {
                        // 提交人是自己
                        return (this.order.applicantId === this.userInfo.id) && (this.order.applicantOrganId === this.currentClinic.clinicId);
                    }
                }
                return false;
            },
            supplierOptions() {
                return this.currentSupplierList.map((item) => {
                    const handleItem = this.getGualificationStatus(item);
                    return {
                        ...item,
                        isExpired: handleItem.isExpired,
                        expiredList: handleItem.expiredList,
                    };
                }) || [];
            },
            supplierOptionsNormal() {
                return this.supplierOptions?.filter((item) => {
                    if (this.isChain && !this.isChainAdmin) {
                        return !item.isExpired && !(item.isEntrustDelivery && item.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY);
                    }
                    return !item.isExpired;
                });
            },
            supplierOptionsExpired() {
                return this.supplierOptions?.filter((item) => {
                    if (this.isChain && !this.isChainAdmin) {
                        return item.isExpired && !(item.isEntrustDelivery && item.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY);
                    }
                    return item.isExpired;
                });
            },
            canReview() {

                if (this.gspInstId) {
                    return !!this.instDetail?.canApprove;
                }
                return this.isChainAdmin && (this.order.status === PurchaseOrderStatus.REVIEW);
            },
            canResubmit() {
                if (this.order.status === PurchaseOrderStatus.WITH_DRAW || this.order.status === PurchaseOrderStatus.REFUSE) {
                    // 提交人是自己
                    // return (this.order.applicantId === this.userInfo.id) && (this.order.applicantOrganId === this.currentClinic.clinicId);
                    return true;
                }
                return false;
            },
            canEdit() {
                // 待审核采购单暂时不允许修改
                return this.isAdd || (this.canReview && !this.isPurchaseOrder);
            },
            canHeaderEdit() {
                return this.isPurchaseOrder ? this.canEdit : this.isAdd;
            },
            supportDeleteTr() {
                return this.sendToSupplier ? false : this.canEdit;
            },
            supportAddGoods() {
                return this.sendToSupplier ? false : this.canEdit;
            },
            disabledDraftBtn() {
                return isEqual(this.order, this.cacheOrder);
            },
            // 没有关联的goods数据
            tableList() {
                return this.order.list.filter((item) => {
                    const goodsId = item.goodsId || item.goods?.id;
                    return !!this.failGoodsList.find((e) => e.goodsId === goodsId);
                });
            },
            tableConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'shortId',
                            label: '商品编码',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '120px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'displayName',
                            label: '商品名称',
                            slot: true,
                            style: {
                                flex: '1',
                                width: '200px',
                                minWidth: '200px',
                                maxWidth: '240px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'supplierName',
                            label: '采购供应商',
                            slot: true,
                            style: {
                                flex: '1',
                                width: '200px',
                                minWidth: '200px',
                                maxWidth: '240px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'purchaseCount',
                            label: '采购量',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '120px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                        },
                    ],
                };
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'shortId',
                            label: '商品编码',
                            slot: true,
                            pinned: 'left',
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '120px',
                                'minWidth': '96px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'displayName',
                            label: '商品名称',
                            slot: true,
                            pinned: 'left',
                            style: {
                                'flex': '1',
                                'width': '',
                                'maxWidth': '',
                                'minWidth': '316px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'lastPackageCostPrice',
                            label: '最近进价/供应商',
                            sortable: true,
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '280px',
                                'minWidth': '160px',
                                'paddingLeft': '',
                                'paddingRight': '4px',
                                'textAlign': 'left',
                            },
                        },
                        {
                            key: 'requireCount',
                            label: '要货量',
                            slot: true,
                            colType: 'money',
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '100px',
                                'minWidth': '75px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                            hidden: !this.sendToSupplier,
                            dataFormatter: (_, row) => {
                                return complexCount({
                                    ...row.goods,
                                    packageCount: row.requirePackageCount,
                                    pieceCount: row.requirePieceCount,
                                });
                            },
                        },
                        {
                            key: 'currentStock',
                            label: '可售数量',
                            customRender: (h, row) => {
                                const {
                                    stockWarnFlag, currentStock: stock,
                                } = row.hisGoodsStockStatInfo;
                                const str = complexCount({
                                    ...row.goods,
                                    packageCount: stock.stockPackageCount,
                                    pieceCount: stock.stockPieceCount,
                                });

                                return (
                                <abc-table-cell theme={stockWarnFlag ? 'warning' : 'default'} class="ellipsis">
                                    <span>{str}</span>
                                </abc-table-cell>
                                );
                            },

                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '120px',
                                'minWidth': '90px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: 'turnoverDays',
                            label: '周转',
                            customRender: (h, row) => {
                                const {
                                    turnoverDays, turnoverDaysWarnFlag,
                                } = row.hisGoodsStockStatInfo;

                                const str = turnoverDays ? `${turnoverDays}天` : '-';

                                return (
                                <abc-table-cell theme={turnoverDaysWarnFlag ? 'warning' : 'default'} class="ellipsis">
                                    <span>{str}</span>
                                </abc-table-cell>
                                );
                            },
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '80px',
                                'minWidth': '65px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: 'avgSell',
                            label: '30日销量',
                            dataFormatter: (_, row) => {
                                return this.formatLastMonthSellCount(row);
                            },
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '100px',
                                'minWidth': '80px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },

                        {
                            key: 'supplier',
                            label: '供应商',
                            slot: true,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '124px',
                                'minWidth': '124px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'left',
                            },
                            hidden: !this.sendToSupplier,
                            // eslint-disable-next-line no-unused-vars
                            headerAppendRender: (h, config) => {
                                return (
                                <abc-popover
                                    value={this.supplierDialogVisible}
                                    placement="bottom-end"
                                    trigger="manual"
                                    theme="white"
                                    style="margin-left: auto;"
                                    onInput={(val) => {
                                        this.supplierDialogVisible = val;
                                    }}
                                >
                                    <abc-button
                                        variant="text"
                                        slot="reference"
                                        size="small"
                                        onClick={this.handleSupplierClick}
                                    >批量
                                    </abc-button>

                                    <abc-form
                                        ref="supplierForm"
                                        label-position="left"
                                        label-width="80"
                                        item-block
                                    >
                                        <abc-form-item label="供应商" style="margin: 0;">
                                            <abc-select
                                                width="200"
                                                with-search
                                                clearable
                                                inner-width="280px"
                                                placement="bottom-end"
                                                fetch-suggestions={this.fetchSuggestions}
                                                value={this.supplierValue}
                                                onChange={(val) => {
                                                    this.supplierValue = val;
                                                }}
                                            >
                                                {
                                                    this.filterCurrentSupplierList.map((o) => {
                                                        return <abc-option
                                                            key={o.id}
                                                            value={o.id}
                                                            label={o.name}
                                                        ></abc-option>;
                                                    })
                                                }
                                            </abc-select>
                                        </abc-form-item>
                                    </abc-form>

                                    <abc-flex justify="flex-end" style="margin-top: 12px;">
                                        <abc-button variant="fill" onClick={() => {
                                            this.handleBatchConfirm(this.supplierValue);
                                        }} disabled={!this.supplierValue}>
                                            批量填入
                                        </abc-button>

                                        <abc-button variant="ghost" onClick={() => {
                                            this.supplierDialogVisible = false;
                                        }}>
                                            取消
                                        </abc-button>
                                    </abc-flex>
                                </abc-popover>
                                );
                            },
                        },
                        {
                            key: 'purchaseCount',
                            label: this.isPurchaseOrder ? '采购量' : '要货量',
                            slot: true,

                            colType: 'money',
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '100px',
                                'minWidth': '75px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: 'purchaseUnit',
                            label: '单位',
                            slot: true,

                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '64px',
                                'minWidth': '56px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'center',
                            },
                        },
                        {
                            key: 'packageCostPrice',
                            label: '单价',
                            slot: true,
                            hidden: !this.isPurchaseOrder,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '120px',
                                'minWidth': '90px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                        {
                            key: 'totalCost',
                            label: '金额',
                            slot: true,
                            hidden: !this.isPurchaseOrder,
                            style: {
                                'flex': 1,
                                'width': '',
                                'maxWidth': '120px',
                                'minWidth': '90px',
                                'paddingLeft': '',
                                'paddingRight': '',
                                'textAlign': 'right',
                            },
                        },
                    ].filter((item) => !item.hidden),
                };
            },

            descriptionsItemContentStyle() {
                return { padding: this.canHeaderEdit ? '0px' : '' };
            },
            // table滚动后校验或聚焦输入框的回调时间，根据是否开启虚拟列表来决定大小
            timeout() {
                return this.order.list?.length >= 30 ? 1000 : 0;
            },
            // 当前所选供应商经营范围
            curSelectedSupplierBizScopeList() {
                const supplier = this.currentSupplierList.find((s) => s.id === this.order.supplierId);
                return supplier?.businessScope?.businessScopeList || [];
            },
            // 总部供应商经营范围 排除自采供应商
            filterCurrentSupplierList() {
                if (this.isChainAdmin) {
                    return this.currentSupplierList.filter((s) => s.isEntrustDelivery);
                }
                return this.currentSupplierList;
            },
            // 当前所选供应商
            curSelectedSupplier() {
                return this.currentSupplierList.find((s) => s.id === this.order.supplierId);
            },
            // 要货门店名称
            claimClinicName() {
                return this.order?.claimOrgan?.shortName || this.order?.claimOrder?.applicantOrgan?.shortName || this.order?.claimOrgan?.name || this.order?.claimOrder?.applicantOrgan?.name || '';
            },
            // 要货门店ID 以 prop 传入
            applicantOrganIdProp() {
                return this.order?.applicantOrganId || '';
            },
            calcHeight() {
                if (this.selectedTab === 1) {
                    return 'calc(100% - 122px)';
                }
                // 57tab栏+57筛选栏+8底部padding
                let height = 122;
                if (this.isShowTurnoverDaysTipsCard) {
                    // tipsCard高度
                    height += 50;
                }
                return `calc(100% - ${height}px)`;
            },
            warnListMap() {
                return this.warnList.reduce((map, item) => {
                    map[item.goodsId] = item;
                    return map;
                }, {});
            },
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        async created() {
            this._debounceFetchStockWarnList = debounce(this.fetchStockWarnList, 500, true);

            if (this.orderId) {
                await this.fetchOrderDetail();
            }
            if (this.isResubmit) {
                this.initResubmitOrder();
            }

            if (this.isAdd) {
                if (!this.order.applicantOrganId) {
                    this.order.applicantOrganId = this.currentClinic.clinicId;
                }

                if (!this.sendToSupplier) {
                    this._debounceFetchStockWarnList();
                    this.fetchAllGoodsTypes();
                }

                if (this.goodsList.length) {
                    this.order.list = clone(this.goodsList.map((item, index) => {
                        const {
                            lastStockInOrderSupplier, lastSupplierName,
                        } = item.hisGoodsStockStatInfo || {};
                        const supplierName = item.supplierName || lastStockInOrderSupplier || lastSupplierName || item.manufacturerFull;
                        const supplierId = this.filterCurrentSupplierList.find((supplier) => supplier.name === supplierName)?.id ?? '';
                        return {
                            ...item,
                            originalIndex: index,
                            supplierId,
                            requirePackageCount: item.packageCount,
                            requirePieceCount: item.pieceCount,
                        };
                    }));
                }

                this.cacheOrder = clone(this.order);
            }

            if (this.gspInstId) {
                this.fetchTaskDetail();
            }
        },
        methods: {
            ...mapActions([
                'acFetchUserInfo',
            ]),
            unitEqual,
            isNull,
            formatMoney,
            formatClinicName,
            moneyDigit,
            isChineseMedicine,
            complexCount,
            calCostPriceSingle,
            calCostPriceTotal,
            formatSupplierTypeName,
            gualificationList(id) {
                return this.supplierOptionsExpired?.find((item) => {
                    return item.id === id;
                })?.expiredList || [];
            },
            supplieExpired(id) {
                return this.supplierOptionsExpired?.find((item) => {
                    return item.id === id;
                });
            },
            // 获取经营范围
            getGualificationStatus(item) {
                const { extendInfo = {} } = item;
                const certificationInfos = extendInfo?.certificationInfos || [];
                if (!certificationInfos?.length) {
                    return {
                        isExpired: false,
                        expiredList: [],
                    };
                }
                const expiredItem = certificationInfos?.find((it) => {
                    const currentTime = this.getCurrentTime(it);
                    return this.isExpired(currentTime);
                });
                if (expiredItem) {
                    return {
                        isExpired: true,
                        expiredList: certificationInfos?.filter((it) => {
                            const currentTime = this.getCurrentTime(it);
                            return this.isExpired(currentTime);
                        }) || [],
                    };
                }
                return {
                    isExpired: false,
                    expiredList: [],
                };
            },
            // 是否已到期
            isExpired(currentTime) {
                const date1 = new Date(currentTime);
                const date2 = new Date();
                return date1.getTime() - date2.getTime() < 0;
            },
            getCurrentTime(item) {
                if (!item?.validTo) {
                    return '';
                }
                const validToDate = new Date(item.validTo);
                // 格式化为 YYYY-MM-DD 23:59:59
                const year = validToDate.getFullYear();
                const month = String(validToDate.getMonth() + 1).padStart(2, '0');
                const day = String(validToDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day} 23:59:59`;
            },
            createTrKey(item) {
                return item.keyId || item.id || item.goodsId;
            },
            handleBatchConfirm(val) {
                this.order.list.forEach((o) => {
                    o.supplierId = val;
                });

                this.supplierDialogVisible = false;
            },
            formatLastMonthSellCount(item) {
                if (!item) return '';
                const unit = isChineseMedicine(item.goods) ? item.goods.pieceUnit : item.goods.packageUnit;
                // lastMonthSellCount为0也空值展示
                const { lastMonthSellCount } = item.hisGoodsStockStatInfo;

                return isNull(lastMonthSellCount) ? '-' : (count1(lastMonthSellCount) + unit);
            },
            calCostPrice(item) {
                const {
                    purchaseCount, totalCost,
                } = item;
                const useCount = purchaseCount;

                if (isNotNull(useCount) && isNotNull(totalCost)) {
                    const packageCostPrice = this.calCostPriceSingle({
                        useCount,
                        useTotalCostPrice: totalCost,
                    });
                    this.$set(item, 'packageCostPrice', packageCostPrice);
                }
            },

            calTotalPrice(item) {
                const {
                    packageCostPrice, purchaseCount,
                } = item;
                // const useCount = isChineseMedicine(goods) ? pieceCount : packageCount;
                const useCount = purchaseCount;


                if (isNull(useCount) || isNull(packageCostPrice)) {
                    this.$set(item, 'totalCost', '');
                } else {
                    const totalCost = this.calCostPriceTotal({
                        useCount,
                        useUnitCostPrice: packageCostPrice,
                    });
                    this.$set(item, 'totalCost', totalCost);
                }
            },
            getCostConfig,
            getFormatLength,
            unitChange(row) {
                setUserGoodsUnit(row.goodsId, row.purchaseUnit);
            },
            async fetchOrderDetail() {
                try {
                    this.pageLoading = true;
                    const res = await GoodsAPIV3.getPurchaseOrderDetail(this.orderId);
                    if (res.data) {
                        const [comment = {}] = res.data.comment?.slice(-1) ?? [];
                        this.order = {
                            ...res.data,
                            comment: comment?.content ?? '',
                            purchaseOrderDate: formatDate(res.data.purchaseOrderDate, 'YYYY-MM-DD'),
                            list: res.data.list.map((item, index) => {
                                return {
                                    ...item,
                                    keyId: createGUID(),
                                    originalIndex: index,
                                    purchaseCount: item.purchaseUnit === item.goods?.packageUnit ? item.packageCount : item.pieceCount,
                                };
                            }),
                        };
                        this.cacheOrder = clone(this.order);
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.pageLoading = false;
                }
            },
            /**
             * @description: 处理重新提交的数据
             * @author: ff
             * @date: 2024/2/25
             */
            initResubmitOrder() {
                // 保证数据要和order的初始化数据结构一致
                this.order = {
                    status: '',
                    purchaseOrderDate: formatDate(Date.now(), 'YYYY-MM-DD'),
                    comment: this.order.comment,
                    applicantOrganId: this.order.applicantOrganId,
                    claimClinicId: this.order.claimClinicId,
                    applicantId: '',
                    applicant: {
                        id: '',
                        name: '',
                    },
                    purchaseOrgan: {},
                    clinicName: '',
                    orderNo: '',
                    supplierId: this.order.supplierId,
                    list: this.order.list.map((item, index) => {
                        return {
                            goods: item.goods,
                            keyId: item.keyId,
                            goodsId: item.goodsId,
                            hisGoodsStockStatInfo: item.hisGoodsStockStatInfo,
                            purchaseUnit: item.purchaseUnit,
                            purchaseCount: item.purchaseCount,
                            packageCostPrice: item.packageCostPrice,
                            totalCost: item.totalCost,
                            originalIndex: item.originalIndex ?? index,
                        };
                    }),
                    orderType: this.order.orderType,
                };
                this.cacheOrder = clone(this.order);
            },
            async fetchTaskDetail() {
                try {
                    this.rightLoading = true;
                    const res = await ApprovalAPI.fetchInstDetail(this.gspInstId);
                    if (res?.data) {
                        this.instDetail = res.data;
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.rightLoading = false;
                }
            },
            async fetchStockWarnList() {
                try {
                    this.rightLoading = true;
                    if (this.selectedTab === 0) {
                        this.stockWarnParams.stockWarn = 1;
                    }
                    const { data } = await PurchaseAPI.fetchStockWarn(this.stockWarnParams);
                    this.warnList = data?.items ?? [];
                    if (this.selectedTab === 0) {
                        this.warnDataCount = this.warnList.length;
                    }
                    this.order.list.forEach((item) => {
                        const warnItem = this.warnListMap[item.goodsId];
                        if (warnItem && !this.selectedWarnIds.includes(warnItem.goodsId)) {
                            this.selectedWarnIds.push(warnItem.goodsId);
                        }
                    });
                } catch (e) {
                    console.log(e);
                } finally {
                    this.rightLoading = false;
                }
            },
            supplierName(id) {
                return this.supplierList.find((supplier) => supplier.id === id)?.name;
            },
            handleDeleteTr(index) {
                const item = this.order.list[index];
                // 需同步取消预警列表勾选
                if (this.selectedWarnIds.includes(item.goodsId)) {
                    this.selectedWarnIds = this.selectedWarnIds.filter((id) => id !== item.goodsId);
                }
                this.order.list.splice(index, 1);
            },
            handleSortChange({
                orderBy, orderType,
            }) {
                if (orderBy === 'lastPackageCostPrice') {
                    this.sortBySupplierPinyin(orderType);
                }
            },

            /**
             * 根据供应商名称的拼音首字母排序
             * @param {string} orderType - 排序类型 'asc' | 'desc'
             */
            sortBySupplierPinyin(orderType) {
                // 需要排序
                if (orderType) {
                    // 使用 hooks 中的供应商拼音排序方法
                    const sortedList = this.sortGoodsBySupplierPinyin(
                        this.order.list,
                        (item) => item.hisGoodsStockStatInfo?.lastStockInOrderSupplier || item.hisGoodsStockStatInfo?.lastSupplierName || '',
                        orderType,
                    );

                    // 更新列表
                    this.order.list = sortedList;
                } else {
                    // 还原排序
                    this.order.list.sort((a, b) => {
                        return (a.originalIndex || 0) - (b.originalIndex || 0);
                    });
                }
            },
            handleCheck(checked, item) {
                const index = this.order.list.findIndex((it) => it.goodsId === item.goodsId);
                if (checked) {
                    // 当前药品不在列表中，需添加进去
                    if (index === -1) {
                        // 需查询药品详细信息再添加
                        this.selectGoods(item);
                    // this.order.list.push(item);
                    }
                } else {
                    // 当前药品在列表中，需删除
                    if (index !== -1) {
                        this.order.list.splice(index, 1);
                        this.selectedWarnIds = this.selectedWarnIds.filter((id) => id !== item.goodsId);
                    }
                }
            },
            handleCheckAll() {
                this.selectAllGoods(this.warnList);
            },
            changeFilterCondition() {
                this.selectedTypes = [];
                this.stockWarnParams = clone(this.initStockWarnParams);
                this._debounceFetchStockWarnList();
            },
            // 推荐数据:search-recommend-fn="searchRecommendByPurchase"
            async searchRecommendByPurchase() {
                if (this._waitFocus) return [];

                const res = await GoodsAPIV3.searchRecommendByPurchase({
                    pharmacyNo: this.pharmacyNo,
                    supplierId: this.order.supplierId,
                });
                return res?.data?.list || [];
            },
            clearTraceCode() {
                this._waitFocus = true;
            },
            focusGoodsAutoComplete() {
                this._waitFocus = false;
            },
            // 滚动table,聚焦输入框
            focusInput(index, isScroll = true) {
                if (isScroll) {
                    this.$refs.tableRef.scrollToElement({
                        index,
                        top: 47,
                        time: 60,
                        behavior: 'instant',
                    });
                }
                if (this.focusTimer) {
                    clearTimeout(this.focusTimer);
                    this.focusTimer = null;
                }

                this.focusTimer = setTimeout(() => {
                    const keyId = this.createTrKey(this.order.list[index]);
                    const $tableTr = this.$refs.tableRef.$el.querySelector(`.abc-table-tr--${keyId}`);
                    $tableTr && $tableTr.querySelector('.focus-input input').focus();
                    this._waitFocus = false;
                }, this.timeout);
            },
            async selectGoods(goods) {
                try {
                    const goodsId = goods.id || goods.goodsId;
                    const index = this.order.list.findIndex((e) => e.goodsId === goodsId);
                    // 已添加的药品，不再添加
                    if (index !== -1) {
                        this.focusInput(index);
                        return;
                    }
                    // 重新获取药品信息
                    const { data: tempGoods } = await GoodsBaseAPI.goods(
                        goodsId,
                        this.goodsSearchClinicId,
                        {
                            forPurchase: 1, withStock: 1,
                        },
                    );

                    const res = await this.fetchGoodsRecommend([goodsId], this.goodsSearchClinicId);
                    const recommendView = res?.list?.[0] ?? {};


                    if (tempGoods) {
                        this.searchKey = '';
                        this.$refs.goodsAutoCompleteRef?.handleClear();
                        // 产品要求新增药品时重置排序
                        this.$refs.tableRef.resetOrder({
                            orderBy: 'lastPackageCostPrice',
                            orderType: '',
                        });
                        this.order.list.push(this.createGoodsItem(tempGoods, recommendView));
                        this.focusInput(this.order.list.length - 1, true);
                        // 当前药品在预警列表中
                        if (this.warnList.find((item) => item.goodsId === tempGoods.id)) {
                            this.selectedWarnIds = [...new Set(this.selectedWarnIds.concat([tempGoods.id]))];
                        }
                    }
                } catch (e) {
                    console.error(e);
                }

            },
            async selectAllGoods(goodsList) {
                const willPushIdList = [];
                goodsList.forEach((item) => {
                    const isDuplicate = this.order.list.some((orderItem) => {
                        return item.id === orderItem.goodsId || item.goodsId === orderItem.goodsId;
                    });
                    if (!isDuplicate) {
                        willPushIdList.push(item.id || item.goodsId);
                    }
                });
                if (!willPushIdList || willPushIdList.length === 0) return;
                // 批量查询goods信息，构造feeItem
                const goodsRes = await GoodsAPIStock.queryCount(
                    willPushIdList,
                    this.goodsSearchClinicId,
                    undefined,
                    0,
                );
                const goodsMap = (goodsRes.list || []).reduce((map, item) => {
                    map[item.id] = item;
                    return map;
                }, {});
                const goodsListRes = willPushIdList.map((id) => goodsMap[id]).filter(Boolean); //对goodsListRes进行排序根据WillPushIdList
                const res = await this.fetchGoodsRecommend(willPushIdList, this.goodsSearchClinicId);
                const recommendViews = res?.list ?? [];

                if (goodsListRes && goodsListRes.length) {
                    this.searchKey = '';
                    this.$refs.goodsAutoCompleteRef?.handleClear();
                    goodsListRes.forEach((item) => {
                        const findItem = recommendViews.find((recommendView) => recommendView.goodsId === item.goodsId);
                        this.order.list.push(this.createGoodsItem(item, findItem));
                    });
                    this.$nextTick(() => this.autoFocus(this.order.list.length - 1));

                    const temp = goodsListRes.map((item) => item.id);
                    this.selectedWarnIds = [...new Set(temp)];
                }
            },
            createGoodsItem(goods = {}, recommend = {}) {
                const GoodsInstance = new BusinessGoods(goods);
                const lastSelectedUnit = getUserGoodsUnit(goods.id);
                const recommendCount = recommend.recommendCount || '';
                let purchaseCount = GoodsInstance.isChineseGoods ? recommendCount : ~~recommendCount;

                // 产品小于等于零，不填值
                if (purchaseCount <= 0) purchaseCount = '';
                return {
                    goods,
                    keyId: createGUID(),
                    goodsId: goods.id,
                    purchaseUnit: lastSelectedUnit || (GoodsInstance.isChineseGoods ? goods.pieceUnit : goods.packageUnit),
                    packageCount: GoodsInstance.isChineseGoods ? 0 : ~~recommendCount,
                    pieceCount: GoodsInstance.isChineseGoods ? recommendCount : 0,
                    purchaseCount,
                    packageCostPrice: '',
                    totalCost: '',
                    originalIndex: this.order.list.length,
                    hisGoodsStockStatInfo: {
                        // 日均销量
                        avgSell: goods.recentAvgSell,
                        // 30日销量
                        lastMonthSellCount: recommend.lastMonthSellCount,
                        // 周转天数
                        turnoverDays: goods.turnoverDays || '',
                        // 利润率
                        profitRatio: goods.profitRat || '-',
                        // 最近进价
                        lastPackageCostPrice: goods.lastPackageCostPrice || '',
                        // 最近供应商
                        lastStockInOrderSupplier: goods.lastStockInOrderSupplier || '',
                        // 库存量
                        currentStock: goods,
                        // 推荐量
                        recommendView: {
                            // 计算平均销量的天数
                            daysOfDayAvgSell: recommend.daysOfDayAvgSell || '',
                            // 计算周转库存的天数
                            purchaseCycleDays: recommend.purchaseCycleDays || '',
                            // 日均销量
                            recentAvgSell: recommend.recentAvgSell,
                            // 推荐采购库存
                            recommendCount,
                            // 最大采购库存
                            stockCapacityMaxLimit: recommend.stockCapacityMaxLimit || '',
                        },
                    },
                };
            },
            createPostData(opType) {
                const {
                    purchaseOrderDate,
                    supplierId,
                    comment,
                    list,
                    orderType,
                    applicantOrganId,
                    claimClinicId,
                } = this.order;

                // 总部配货
                let purchaseType = ReceiptOrginOrderType.DELIVERY;
                if (this.isPurchaseOrder) {
                    purchaseType = this.isAdmin ? ReceiptOrginOrderType.CONCENTRATE : ReceiptOrginOrderType.SEPARATE;
                }

                const obj = {
                    id: this.orderId,
                    applicantId: this.userInfo.id,
                    applicantOrganId,
                    // 采购单还是要货单
                    orderType,
                    // TODO:需要前端传吗？
                    purchaseType,
                    comment,
                    supplierId,
                    purchaseOrderDate,
                    // 保存草稿
                    isDraft: opType === ReceiveActionType.DRAFT,
                    claimOrderId: this.claimOrderId,
                    claimClinicId,
                    // sendToSupplier: this.sendToSupplier,
                    list: list.map((item) => {
                        // 使用大包装数量
                        const usePackageCount = item.purchaseUnit === item.goods.packageUnit;

                        // 总部要货单，集采自配-要货门店为子店、收货门店为总部，集采委配-要货门店与收货门店一致
                        if (this.sendToSupplier) {
                            const supplier = this.currentSupplierList.find((e) => e.id === item.supplierId);
                            if (supplier?.isEntrustDelivery) {
                                if (supplier.entrustDeliveryType === EntrustDeliveryType.ENTRUST_DELIVERY) {
                                    item.claimClinicId = this.applicantOrganId;
                                    item.applicantOrganId = this.applicantOrganId;
                                }
                                if (supplier.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY) {
                                    item.claimClinicId = this.applicantOrganId;
                                    item.applicantOrganId = this.currentClinic.clinicId;
                                }
                            }
                        }
                        return {
                            id: item.id,
                            goodsId: item.goodsId || item.goods?.id,
                            packageCount: usePackageCount ? item.purchaseCount : 0,
                            pieceCount: usePackageCount ? 0 : item.purchaseCount,
                            purchaseUnit: item.purchaseUnit,
                            packageCostPrice: item.packageCostPrice,
                            totalCost: item.totalCost,
                            supplierId: item.supplierId,
                            applicantOrganId: item.applicantOrganId,
                            claimClinicId: item.claimClinicId,
                        };
                    }),
                };

                return obj;
            },
            /**
             * @desc 获取药品的建议采购量
             * purchaseCycleDays 采购周期
             * recommendCount 建议采购量
             * stockCapacityMaxLimit 采购上限
             * currentCount 当前库存
             * recentAvgSell 日均销量
             */
            async fetchGoodsRecommend(goodsIds, clinicId) {
                try {
                    const { data } = await PurchaseAPI.fetchGoodsRecommend({ goodsIds }, clinicId);
                    return data;
                } catch (e) {
                    console.log(e);
                }
            },
            async handleExport() {
                await GoodsAPIV3.exportPurchaseOrder(this.orderId);
            },
            // 提交前检查供应商关联关系
            async handleSubmitOrder(opType) {
                this.$refs.form.validate(async (val) => {
                    if (val) {
                        // 开启了审核
                        if (this.isShowConfirmModal) {
                            this.$confirm({
                                type: 'warn',
                                title: '提示',
                                content: '采购订单需要总部审核确认，是否确定？',
                                onConfirm: () => {
                                    this.checkValidToSupplierGoods(opType);
                                },
                            });
                        } else {
                            this.checkValidToSupplierGoods(opType);
                        }

                    } else {
                        console.error('表单校验不通过');
                    }
                });

            },
            async checkValidToSupplierGoods(opType) {
                if (this.isPurchaseOrder) {

                    const list = this.order.list.reduce((res, item) => {
                        const id = this.sendToSupplier ? item.supplierId : this.order.supplierId;
                        const isOpenErp = this.supplierList.find((s) => s.id === id)?.abcSupplierId;

                        if (isOpenErp) {
                            res.push({
                                goodsId: item.goodsId || item.goods?.id,
                                supplierId: id,
                            });
                        }

                        return res;

                    }, []);

                    if (list.length) {
                        const res = await GoodsAPIV3.checkValidToSupplierGoods({
                            list,
                        });
                        const goodsList = res?.data ?? [];
                        this.failGoodsList = goodsList.filter((item) => !item.valid);
                        if (this.failGoodsList.length) {
                            this.showCheckGoodsDialog = true;
                            return;
                        }
                    }
                }

                if (this.sendToSupplier) {
                    await this.handleSubmitBefore();
                } else {
                    this.handleSubmit(opType);
                }
            },
            /**
             * @description: 删除草稿
             * @author: ff
             * @date: 2024/1/31
             */
            async deleteDraftOrder() {
                this.$confirm({
                    type: 'warn',
                    title: '删除确认',
                    content: '删除后不能恢复。确定删除该草稿？',
                    onConfirm: async () => {
                        try {
                            this.deleteDraftBtnLoading = true;
                            await GoodsAPIV3.deletePurchaseOrder(this.orderId);
                            this.$emit('refresh');
                            this.handleCancel();
                        } catch (e) {
                            console.error(e);
                        } finally {
                            this.deleteDraftBtnLoading = false;
                        }
                    },
                });
            },
            // 向供应商采购-拆单
            async handleSubmitBefore() {
                try {
                    this.submitBtnLoading = true;
                    const postData = this.createPostData();
                    const { data } = await GoodsAPIV3.preCreatePurchaseOrder(postData);
                    this.showPreOrderDialog = true;
                    this.preOrders = data;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.submitBtnLoading = false;
                }
            },
            handleFocusSubmit() {
                this.showCheckGoodsDialog = false;
                this.order.list = this.order.list.filter((item) => {
                    if (this.failGoodsList.find((e) => e.goodsId === item.goodsId)) {
                        return false;
                    }
                    return true;
                });
                this.handleSubmit(ReceiveActionType.FINISH);
            },
            // 直接提交
            handleSubmit(opType, customFn) {
                if (opType === ReceiveActionType.DRAFT) {
                    this.submitOrder(opType, customFn);
                } else {
                    this.$refs.form.validate((val) => {
                        if (val) {
                            this.submitOrder(opType);
                        } else {
                            console.error('表单校验不通过');
                        }
                    });
                }

            },
            handleCancel() {
                this.showDialog = false;
                this.$emit('close');
            },
            handleTermination() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '将驳回要货单，是否确定？',
                    onConfirm: async () => {
                        await this.stopOrder();
                    },
                });
            },
            // 总部配货成功刷新
            handleRefresh() {
                this.$emit('refresh');
                this.handleCancel();
            },
            handleDistribution() {
                this.showOrderDialog = true;
            },
            handlePurchase() {
                this.showPurchaseOrderDialog = true;
            },
            handleRevoke() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '撤销后单据将作废，确认撤销吗？',
                    onConfirm: async () => {
                        await this.revokeOrder();
                    },
                });
            },
            handleResolve() {
                this.$refs.form.validate(async (val) => {
                    if (val) {
                        await this.resolveOrder(
                            {
                                pass: 1,
                                content: '',
                            },
                        );
                    }
                });
            },
            handleReject() {
                this.$refs.form.validate(async (val) => {
                    if (val) {
                        this.$refs.frameDialog.openReviewDialog('reject');
                    }
                });

            },
            handleReviewConfirm(data, cb) {
                if (data.pass) {
                    this.resolveOrder(data, cb);
                } else {
                    this.rejectOrder(data);
                }
            },

            handleResubmit() {
                this.$emit('resubmit', this.orderId);
                this.showDialog = false;
            },

            async submitOrder(opType, customFn) {
                try {
                    if (opType === ReceiveActionType.FINISH) this.submitBtnLoading = true;
                    if (opType === ReceiveActionType.DRAFT) this.submitDraftBtnLoading = true;
                    const postData = this.createPostData(opType);
                    if (this.sendToSupplier) {
                        await GoodsAPIV3.createPurchaseOrderSendToSupplier(postData);
                    } else {
                        if (this.isPurchaseOrder) {
                            if (this.orderId && !this.isResubmit) {
                                await GoodsAPIV3.updatePurchaseOrder(this.orderId, postData);
                            } else {
                                await GoodsAPIV3.createPurchaseOrder(postData);
                            }
                        } else {
                            if (this.orderId && !this.isResubmit) {
                                await GoodsAPIV3.updateClaimOrder(this.orderId, postData);
                            } else {
                                await GoodsAPIV3.createRequireOrder(postData);
                            }
                        }
                        customFn && customFn();
                    }
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.$emit('refresh', this.isAdd ? 1 : 2);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.submitBtnLoading = false;
                    this.submitDraftBtnLoading = false;
                }
            },
            async revokeOrder(data) {
                console.log('revokeOrder', data);
                try {
                    this.revokeBtnLoading = true;

                    if (this.isPurchaseOrder) {
                        await PurchaseAPI.revokeOrder(this.orderId);
                    } else {
                        await GoodsAPIV3.revokePurchaseOrder(this.orderId);
                    }

                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.$emit('refresh');
                } catch (e) {
                    console.error(e);
                } finally {
                    this.revokeBtnLoading = false;
                }
            },
            async stopOrder(data) {
                console.log(data);
                try {
                    this.revokeBtnLoading = true;
                    await GoodsAPIV3.terminatePurchaseOrder(this.orderId);

                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.$emit('refresh');
                } catch (e) {
                    console.error(e);
                } finally {
                    this.revokeBtnLoading = false;
                }
            },
            async resolveOrder(data, cb) {
                try {
                    this.resolveBtnLoading = true;
                    const postData = this.createPostData();

                    if (this.gspInstId) {
                        const {
                            status, data: res,
                        } = await ApprovalAPI.postApprovalPass(this.taskId, {
                            comments: data.content ?? '',
                            variableUpdated: 0,
                            variables: {
                                [this.orderId]: postData,
                            },
                        });
                        // 上面请求内部try-catch了返回了status为false就是异常了
                        if (status === false) {
                            throw res;
                        }
                    } else {
                        await GoodsAPIV3.resolvePurchaseOrder(this.orderId, {
                            pass: 1,
                            comment: data.content ?? '',
                            ...postData,
                        });
                    }
                    this.$Toast({
                        message: '操作成功',
                        type: 'success',
                    });
                    // 关闭审核弹窗
                    cb && cb();
                    this.showDialog = false;
                    this.$emit('confirm');
                    this.$emit('refresh');
                } catch (e) {
                    console.error(e);
                    if (!e.alerted) {
                        this.$Toast({
                            message: e.message,
                            type: 'error',
                        });
                    }
                } finally {
                    this.resolveBtnLoading = false;
                }
            },
            async rejectOrder(data) {
                try {
                    this.rejectBtnLoading = true;
                    if (this.gspInstId) {

                        await ApprovalAPI.postApprovalNotPass(this.taskId, {
                            comments: data.content ?? '',
                        });
                    } else {
                        await GoodsAPIV3.rejectPurchaseOrder(this.orderId, {
                            pass: 0,
                            comment: data.content ?? '',
                        });
                    }
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.showDialog = false;
                    this.$emit('refresh');
                } catch (e) {
                    console.error(e);
                } finally {
                    this.rejectBtnLoading = false;
                }
            },
            handleConfirmPurchase() {
            },
            handleCancelPurchase() {
                this.showPreOrderDialog = false;
            },
            // 点击 x 关闭 提示内容有变化是否保存草稿,
            closeDialog() {
                if (this.isAdd && !this.sendToSupplier) {
                    const confirmTitle = this.isDraft ? '草稿信息发生变动，是否保存？' : '是否需要保存为草稿？';
                    console.log(this.order, this.cacheOrder);
                    if (!isEqual(this.order, this.cacheOrder)) {

                        const vm = this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: confirmTitle,
                            showConfirm: false,
                            showCancel: false,
                            footerPrepend: () => {
                                return (
                                <abc-space>
                                    <abc-button
                                        onClick={() => {
                                            this.handleSubmit(ReceiveActionType.DRAFT);
                                            vm.close();// 手动关闭
                                        }}
                                    >
                                        保存
                                    </abc-button>
                                    <abc-button
                                        type="blank"
                                        onClick={() => {
                                            this.handleCancel();
                                            vm.close();// 手动关闭
                                        }}
                                    >
                                        不保存
                                    </abc-button>
                                </abc-space>
                                );
                            },
                        });
                    } else {
                        this.handleCancel();
                    }
                } else {
                    this.handleCancel();
                }
            },
            async handleGoToUpdateTurnoverDays() {
                await this.acFetchUserInfo();
                if (this.hasWareHouseSetting && this.isAdd && !this.sendToSupplier) {
                    const confirmTitle = this.isDraft ? '草稿信息发生变动，是否保存？' : '是否需要保存为草稿？';
                    if (!isEqual(this.order, this.cacheOrder)) {
                        this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: confirmTitle,
                            showConfirm: false,
                            showCancel: false,
                            footerPrepend: () => {
                                return (
                                <abc-space>
                                    <abc-button
                                        onClick={async () => {
                                            const customFn = () => navigateToWarningProcurementSetting(this.currentClinic);
                                            this.handleSubmit(ReceiveActionType.DRAFT, customFn);
                                        }}
                                    >
                                        保存
                                    </abc-button>
                                    <abc-button
                                        type="blank"
                                        onClick={() => {
                                            navigateToWarningProcurementSetting(this.currentClinic);
                                        }}
                                    >
                                        不保存
                                    </abc-button>
                                </abc-space>
                                );
                            },
                        });
                    } else {
                        navigateToWarningProcurementSetting(this.currentClinic);
                    }
                } else {
                    this.$alert({
                        type: 'warn',
                        title: '没有权限',
                        content: '你暂时没有访问权限，请联系管理员设置',
                    });
                }
            },
            async fetchAllGoodsTypes() {
                const { data } = await GoodsAPI.fetchGoodsClassificationV3({
                    queryType: 1,
                    needCustomType: 1,
                });
                if (data) {
                    this.goodsAllTypes = data.list?.map((item) => {
                        const children = item.customTypes || [];
                        if (children.length) {
                            children.push({
                                id: -item.id,
                                name: '未指定',
                                sort: 999,
                                typeId: +item.id,
                            });
                        }
                        return {
                            ...item,
                            children,
                        };
                    }) ?? [];
                }
            },
            // 类型筛选
            handleChangeType() {
                const {
                    typeIdList, customTypeIdList,
                } = cascaderDataAdapter(this.goodsTypeOptions, this.selectedTypes);
                this.stockWarnParams.customTypeId = customTypeIdList || [];
                this.stockWarnParams.typeId = typeIdList || [];
                this.fetchStockWarnList();
            },
            handleStockWarnFilter(filter) {
                const {
                    maxProfitRat, minProfitRat, turnoverDays, stockCount, sbListingPrice, tagsId, profitCategoryTypeList,
                } = filter;
                this.stockWarnParams = {
                    ...this.stockWarnParams,
                    maxProfitRat,
                    minProfitRat,
                    turnoverDays,
                    stockCount,
                    sbListingPrice,
                    tagId: tagsId.map((item) => item.tagId).join(','),
                    profitCategoryTypeList: profitCategoryTypeList.join(','),
                };
                this._debounceFetchStockWarnList();
            },
            showTooltip(item) {
                if (this.curSelectedSupplierBizScopeList?.length && item?.businessScopeList?.length) {
                    const bizScopeIds = this.curSelectedSupplierBizScopeList.map((scope) => scope.id);
                    const bizScopeParentIds = this.curSelectedSupplierBizScopeList.map((scope) => scope.parentId);
                    return !item.businessScopeList.some((listItem) => bizScopeIds.includes(listItem.id) || bizScopeParentIds.includes(listItem.id));
                }
                return false;
            },
            changeClaimOrderId() {
                // 连锁总部
                if (this.isChainAdmin) {
                    // 集采委配
                    if (this.curSelectedSupplier?.isEntrustDelivery && this.curSelectedSupplier?.entrustDeliveryType === EntrustDeliveryType.ENTRUST_DELIVERY) {
                        this.order.claimClinicId = this.order.applicantOrganId;
                    }
                }
            },
            handleSupplierChange() {
                if (this.isChainAdmin) {
                    if (this.curSelectedSupplier?.isEntrustDelivery) { // 集采
                        if (this.curSelectedSupplier?.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY) { // 自配
                            this.order.applicantOrganId = this.currentClinic.clinicId;
                            this.order.claimClinicId = '';
                            this.disableApplicantOrgan = true;
                        } else {
                            this.order.claimClinicId = '';
                            this.disableApplicantOrgan = false;
                        }
                    } else { // 自采
                        this.order.applicantOrganId = this.currentClinic.clinicId;
                        this.order.claimClinicId = this.currentClinic.clinicId;
                        this.disableApplicantOrgan = false;
                    }
                }
            },
            formatReceiveClinicName(supplier) {
                if (supplier?.isEntrustDelivery && supplier?.entrustDeliveryType === EntrustDeliveryType.SELF_DELIVERY) {
                    return this.currentClinic.clinicName;
                }
                return this.clinicName;
            },
            handleSupplierClick() {
                this.supplierValue = '';
                this.supplierDialogVisible = true;
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
<style lang="scss">
.biz-pharmacy-add-purchase-dialog {
    .order-layout {
        .order-sidebar {
            padding: 8px 0;
            overflow: hidden;

            .purchase-sidebar-wrapper {
                height: 100%;

                .purchase-sidebar-tab {
                    padding: 0 12px 0 6px;
                    border-bottom: 1px solid var(--abc-color-layout-divider-color);
                }

                .purchase-filter-options {
                    padding: 12px 18px;
                    font-size: 12px;
                    color: $T2;
                    border-bottom: 1px solid $P8;

                    &-tips-card {
                        padding: 6px 8px;
                        font-size: 14px;
                    }

                    .iconfont {
                        margin-left: 2px;
                        font-size: 14px;
                    }

                    .filter-condition {
                        line-height: 14px;
                        cursor: pointer;

                        & + .filter-condition {
                            margin-left: 24px;
                        }
                    }

                    .purchase-type-cascader {
                        input {
                            height: 16px;
                        }

                        .abc-cascader-text-area {
                            font-size: 12px;
                            color: $T2;
                        }
                    }
                }

                .empty-content {
                    padding-top: 70%;
                }
            }
        }
    }
}
</style>
