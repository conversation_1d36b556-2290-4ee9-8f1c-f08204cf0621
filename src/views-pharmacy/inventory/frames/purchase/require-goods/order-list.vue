<template>
    <abc-layout preset="dialog-table">
        <abc-layout-header v-if="showSearch">
            <abc-space>
                <!--商品搜索-->
                <goods-auto-complete
                    ref="autoComplete"
                    class="abc-autocomplete-search"
                    placeholder="商品名称/单号"
                    :with-stock="false"
                    :width="220"
                    :auto-focus-first="false"
                    :clear-search-key="false"
                    :enable-barcode-detector="false"
                    :only-stock="false"
                    :search.sync="searchKey"
                    :pharmacy-no="pharmacyNo"
                    focus-show
                    enable-local-search
                    clearable
                    size="medium"
                    :next-input-auto-focus="nextInputAutoFocus"
                    @selectGoods="selectGoods"
                    @searchGoods="searchGoods"
                    @clear="clearSearch"
                    @focus="handleFocus"
                >
                    <abc-search-icon slot="prepend"></abc-search-icon>
                    <!--<abc-delete-icon v-if="searchKey" slot="append" @delete="clearSearch"></abc-delete-icon>-->
                </goods-auto-complete>
                <abc-select
                    ref="supplierSelectRef"
                    v-model="fetchParams.supplierId"
                    :width="180"
                    custom-class="supplierWrapper"
                    with-search
                    clearable
                    size="medium"
                    placeholder="供应商"
                    inner-width="280px"
                    :fetch-suggestions="fetchSuggestions"
                    @change="initOffset"
                    @focus="handleFocus"
                >
                    <abc-option
                        v-for="it in currentSupplierList"
                        :key="`${it.id }`"
                        :value="it.id"
                        :label="it.name"
                    ></abc-option>
                </abc-select>
                <clinic-select
                    v-show="showClinicFilter"
                    v-model="clinicId"
                    custom-class="supplierWrapper"
                    size="medium"
                    placeholder="总部/门店"
                    :show-all-clinic="false"
                    clearable
                    @change="initOffset"
                ></clinic-select>
            </abc-space>
        </abc-layout-header>
        <abc-layout-content>
            <abc-table
                ref="table"
                type="pro"
                show-hover-tr-bg
                :loading="tableLoading"
                :render-config="renderConfig"
                :data-list="tableData"
                empty-size="small"
                style="width: 1150px;"
                :custom-tr-key="customTrKey"
                :custom-tr-class="needKeyboardNavigation ? itemClassStr : () => ''"
                :scroll-load-config="scrollLoadConfig"
                :disabled-item-func="disabledItemFunc"
                @handleClickTr="handleClickTr"
            >
                <template #orderNo="{ trData }">
                    <abc-table-cell :style="getTdStyle(trData)">
                        {{ getSourceOrderStr(trData) }}
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer v-if="isSupportAdditional">
            <abc-tooltip
                placement="top"
                theme="black"
                :disabled="!needKeyboardNavigation"
            >
                <template #content>
                    <abc-text style="color: var(--abc-color-T5);">
                        补录采购订单&nbsp;&nbsp;F9
                    </abc-text>
                </template>
                <abc-button
                    icon="n-add-line-medium"
                    variant="ghost"
                    @click="handleOpenDialog"
                >
                    补录采购订单
                </abc-button>
            </abc-tooltip>
        </abc-layout-footer>
        <additional-dialog
            v-if="showDialog"
            v-model="showDialog"
            :current-id="currentId"
            :is-erp-direct="isSupplement ? !!order.isErpDirect : false"
            :supplier-id="order.supplierId"
            :source-type="isSupplement ? order.additionalPurchaseOrder.purchaseType : undefined"
            :purchase-by="isSupplement ? order.additionalPurchaseOrder.purchaseBy : ''"
            :purchase-order-date="isSupplement ? order.additionalPurchaseOrder.purchaseOrderDate : ''"
            :claim-clinic-id="isSupplement ? order.claimClinicId : ''"
            @confirm="handleConfirm"
        ></additional-dialog>
    </abc-layout>
</template>

<script>
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';
    import useTableKeyboardNavigation from '@/hooks/base/use-table-keyboard-navigation';

    const GoodsAutoComplete = () => import('views/inventory/common/goods-auto-complete.vue');

    import { goodsFullName } from '@/filters';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import { isEqual } from '@abc/utils';
    import {
        formatMoney, isNull,
    } from '@/utils';
    import {
        off, on,
    } from 'utils/dom';
    import { CHECK_IN_SUPPLIER_ID } from 'views/inventory/constant';
    import { mapGetters } from 'vuex';
    import { formatDate } from '@abc/utils-date';
    import { getCurrentInstance } from 'vue';
    import {
        getSourceOrderText,
    } from '../../../utils';
    import AdditionalDialog from '../take-delivery/additionalDialog.vue';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';

    import { findNearestComponent } from 'utils/dom';

    export default {
        name: 'OrderList',
        components: {
            ClinicSelect,
            GoodsAutoComplete,
            AdditionalDialog,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            selectOrderNo: String,
            isSupportAdditional: {
                type: Boolean,
                default: true,
            },
            // 展示门店名称
            showClinicName: {
                type: Boolean,
                default: false,
            },
            currentId: {
                type: String,
                default: '',
            },
            showClinicFilter: {
                type: Boolean,
                default: false,
            },
            isSupplement: {
                type: Boolean,
                default: false,
            },
            order: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            // 标识是否有弹层打开
            showUpLayer: {
                type: Boolean,
                default: false,
            },
            // 用于键盘导航时，搜索栏不计入行列计算里
            showSearch: {
                type: Boolean,
                default: false,
            },
            // 是否需要键盘导航
            needKeyboardNavigation: {
                type: Boolean,
                default: false,
            },
            nextInputAutoFocus: {
                type: Boolean,
                default: true,
            },
        },
        setup() {
            const instance = getCurrentInstance();

            const {
                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
            } = useSearchSupplier({
                excludeInitSupplier: true,
            });

            const {
                itemClassStr,
                keyupHandle,
                updateCurrentKeyboardActiveRowIndex,
            } = useTableKeyboardNavigation({
                rowHeight: 40,
                getTableData: () => instance.proxy.tableData || [],
                getTableRef: () => instance.proxy.$refs.table?.$el,
                handleRowClick: (row) => {
                    if (row) {
                        instance.proxy.handleClickTr(row);
                    }
                },
                handleUpToTableTopBoundary: () => {
                    instance.proxy.$refs?.autoComplete?.manualFocus();
                },
            });
            return {
                currentSupplierList,
                fetchSuggestions,
                addSupplier,
                findSupplier,
                itemClassStr,
                keyupHandle,
                updateCurrentKeyboardActiveRowIndex,
            };
        },
        data() {
            return {
                CHECK_IN_SUPPLIER_ID,
                searchKey: '',
                fetchParams: {
                    goodsId: '',
                    supplierId: '',
                    keyword: '',
                    queryClinicId: '',
                    offset: 0,
                    limit: 20,
                },
                showDialog: false,
                purchaseOptions: [],
                tableLoading: false,
                submitBtnLoading: false,
                tableData: [],
                tablePagination: {
                    showTotalPage: true,
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
                clinicId: '',
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'userInfo',
                'isChainAdmin',
                'stockEmployeeList',
                'currentClinic',
                'currentPharmacy',
            ]),
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            pharmacyType() {
                return this.currentPharmacy?.type;
            },
            scrollLoadConfig() {
                return {
                    fetchData: this.fetchData,
                    total: this.tablePagination.count,
                };
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'applicantOrganName',
                            label: '门店',
                            style: {
                                flex: '1',
                                width: '180px',
                                textAlign: 'left',
                            },
                            hidden: !this.showClinicName,
                        },
                        {
                            key: 'orderNo',
                            label: '采购订单',
                            style: {
                                flex: '1',
                                width: '260px',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'kindCount',
                            label: '品种',
                            style: {
                                width: '60px',
                                textAlign: 'right',
                            },
                            customRender: (_, row) => {
                                return (
                                    <div class="abc-table-cell table-cell table-cell__padding-default" style={this.getTdStyle(row)}>{row.kindCount}</div>
                                );
                            },
                        },
                        {
                            key: 'totalPrice',
                            label: '采购金额',
                            colType: 'money',

                            style: {
                                width: '80px',
                                minWidth: '80px',
                                paddingRight: '4px',
                                textAlign: 'right',
                            },
                            customRender: (_, row) => {
                                return (
                                    <div class="abc-table-cell table-cell table-cell__padding-default" style={this.getTdStyle(row)}>
                                        {formatMoney(row.totalPrice, false)}
                                    </div>
                                );
                            },
                        },
                        {
                            key: 'supplierName',
                            label: '供应商',
                            style: {
                                width: '180px',
                                textAlign: 'left',
                            },
                            customRender: (_, row) => {
                                return (
                                    <div class="abc-table-cell table-cell table-cell__padding-default" style={this.getTdStyle(row)}>{row.supplierName}</div>
                                );
                            },
                        },
                        {
                            key: 'purchaseOrderDate',
                            label: '采购日期',
                            colType: 'date',
                            style: {
                                width: '120px',
                                textAlign: 'left',
                            },
                            customRender: (_, row) => {
                                return (
                                    <div class="abc-table-cell table-cell table-cell__padding-default" style={this.getTdStyle(row)}>{formatDate(row.purchaseOrderDate, 'YYYY-MM-DD') }</div>
                                );
                            },
                        },
                        {
                            key: 'waitKindCount',
                            label: '待收货品种',
                            style: {
                                width: '240px',
                                textAlign: 'left',
                            },
                            customRender: (_, row) => {
                                return (
                                    <div class="abc-table-cell table-cell table-cell__padding-default ellipsis" style={this.getTdStyle(row)}>
                                            <span style="flex-shrink: 0;">
                                                {(row.kindCount || 0) - (row.receivedKindCount || 0)}
                                            </span>

                                            <div style="margin-left:8px" class="ellipsis">
                                                {
                                                    row.waitRecieveGoodsList?.map((item,index) => {
                                                        return <span style={{ color: item.searchHit ? '#E5892D' : '#7A8794' }}>
                                                            {item.name}
                                                            {index === row.waitRecieveGoodsList.length - 1 ? '' : '，'}
                                                        </span>;
                                                    })
                                                }
                                            </div>
                                    </div>
                                );
                            },
                        },
                    ],
                };

            },
        },
        watch: {
            currentId: {
                async handler() {
                    // 需要展示门店才有此逻辑
                    if (this.showClinicName) {
                        this.clinicId = this.currentId;
                        await this.initOffset();
                    }
                },
                deep: true,
                immediate: true,
            },
            showSearch(val) {
                if (val) {
                    this.clearSearch();
                    if (this.needKeyboardNavigation) {
                        this.keyboardBindTimer = setTimeout(() => {
                            on(document, 'keyup', this.keyupHandleFn);
                        }, 300);
                    }
                } else {
                    if (this.needKeyboardNavigation) {
                        if (this.keyboardBindTimer) {
                            clearTimeout(this.keyboardBindTimer);
                            this.keyboardBindTimer = null;
                        }
                        off(document, 'keyup', this.keyupHandleFn);
                    }
                }
            },
        },
        beforeDestroy() {
            if (this.needKeyboardNavigation) {
                if (this.keyboardBindTimer) {
                    clearTimeout(this.keyboardBindTimer);
                    this.keyboardBindTimer = null;
                }
                off(document, 'keyup', this.keyupHandleFn);
            }
        },
        created() {
            if (!this.showClinicName) {
                this.fetchData();
            }
            if (this.selectOrderNo) {
                // eslint-disable-next-line abc/no-timer-id
                setTimeout(() => {
                    this.$refs.table.handleClickTr({ orderNo: this.selectOrderNo });
                });
            }
        },
        methods: {
            formatDate,
            getTdStyle(row) {
                const {
                    kindCount = 0,
                    receivedKindCount = 0,
                } = row;
                const waitKindCount = kindCount - receivedKindCount;
                // 无待收货品种也展示T3
                const color = waitKindCount === 0 ? this.$store.state.theme.style.T3 : this.$store.state.theme.style.T1;
                return `color: ${color}`;
            },
            getSourceOrderStr(order) {
                if (order && !isNull(order.sourceType)) {
                    return getSourceOrderText(order);
                }
                return '-';
            },
            searchGoods(val = '') {
                this.fetchParams.keyword = val.trim();
                this.fetchParams.goodsId = '';

                this.initOffset();
            },
            selectGoods(goods) {
                if (!goods) return;
                this.searchKey = goodsFullName(goods);
                this.fetchParams.goodsId = goods.id;
                this.fetchParams.keyword = '';

                this.initOffset();
                this.$refs?.autoComplete?.manualBlur();
            },

            clearSearch() {
                this.searchKey = '';
                this.fetchParams.keyword = '';
                this.fetchParams.goodsId = '';

                this.initOffset();
            },
            async initOffset() {
                this.fetchParams.offset = 0;
                await this.fetchData(true);
                if (this.needKeyboardNavigation) {
                    this.updateCurrentKeyboardActiveRowIndex();
                }
            },
            createParams() {
                const params = {
                    ...this.fetchParams,
                    // 连锁总部需要筛选门店
                    queryClinicId: this.isChainAdmin ? (this.showClinicName ? (this.clinicId || '') : '') : (this.currentClinic?.clinicId || ''),
                };
                if (params.goodsId) {
                    params.keyword = '';
                } else {
                    params.keyword = this.searchKey;
                }
                return params;
            },
            async fetchData(focusUpdate) {
                try {
                    this.tableLoading = true;
                    const beforeParams = this.createParams();
                    const { data } = await GoodsAPIV3.associablePurchaseOrder(beforeParams);
                    const afterParams = this.createParams();
                    const rows = data.rows || [];
                    if (isEqual(beforeParams, afterParams)) {
                        if (focusUpdate || (data.total > this.tableData.length)) {
                            this.tableData = focusUpdate ? rows : [...this.tableData, ...rows];
                            this.tablePagination.count = data.total || 0;
                            this.fetchParams.offset += this.fetchParams.limit;
                        }
                    }
                } catch (err) {
                    console.error(err);
                } finally {
                    this.tableLoading = false;
                }
            },
            customTrKey(item) {
                return `${item.id}_${item.orderNo}`;
            },
            disabledItemFunc(item) {
                return !!item.v2DisableStatus;
            },
            pageChange(page) {
                const offset = (page - 1) * this.fetchParams.limit;
                this.fetchParams.offset = offset;
                this.fetchData();
            },
            handleClickTr(item) {
                if (this.selectOrderNo === item.orderNo) return;

                this.$emit('select', item);
                if (this.needKeyboardNavigation) {
                    const findIndex = this.tableData.findIndex((it) => it.id === item.id && it.orderNo === item.orderNo);
                    this.updateCurrentKeyboardActiveRowIndex(findIndex);
                }
            },
            handleOpenDialog() {
                this.showDialog = true;
                this.$emit('close');
            },
            handleConfirm(purchaseOrder) {
                this.$emit('additional', purchaseOrder);
            },
            handleFocus() {
                this.updateCurrentKeyboardActiveRowIndex(-1);
            },
            keyupHandleFn(e) {
                if (this.showUpLayer || !this.showSearch) return;

                // 使用 key 属性检测 F9 键
                if (e.key === 'F9' && this.isSupportAdditional) {
                    // 触发补录采购订单方法
                    this.handleOpenDialog();
                    return;
                }
                // 判断是否有聚焦元素
                const activeElement = this.$el?.querySelector('.is-focus .abc-input__inner');

                // 上下键
                if (e.key === 'ArrowUp' && activeElement) {
                    return;
                }
                if (e.key === 'ArrowDown' && activeElement) {
                    let nearestComponent = findNearestComponent(activeElement, 'AbcSelect');
                    if (nearestComponent && nearestComponent.showPopper) {
                        return;
                    }
                    if (nearestComponent && !nearestComponent.showPopper) {
                        nearestComponent.handleBlur();
                        this.updateCurrentKeyboardActiveRowIndex(0);
                        return;
                    }
                    nearestComponent = findNearestComponent(activeElement, 'AbcAutocomplete');
                    if (nearestComponent && !nearestComponent.showSuggestions) {
                        nearestComponent.$refs?.abcinput?.blur();
                        this.updateCurrentKeyboardActiveRowIndex(0);
                        return;
                    }
                    if (nearestComponent && nearestComponent.showSuggestions) {
                        return;
                    }
                }
                // Enter键
                if (e.key === 'Enter' && activeElement) {
                    const nearestComponent = findNearestComponent(activeElement, 'AbcAutocomplete');
                    if (nearestComponent && !nearestComponent.showSuggestions && !nearestComponent.selectedValue) {
                        this.$refs.supplierSelectRef.$refs.abcinput.focus();
                    }
                    return;
                }

                // 左右键
                if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                    if (activeElement) return;
                    this.$emit('close', e.key);
                    return;
                }

                this.keyupHandle(e);
            },
        },
    };
</script>
