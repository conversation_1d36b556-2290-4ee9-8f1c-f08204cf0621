<template>
    <frame-dialog
        v-if="showDialog"
        ref="frameDialog"
        v-model="showDialog"
        :title="title"
        :order-no="order.orderNo"
        :status-name="statusName"
        show-title-append
        :loading="pageLoading"
        :tag-config="tagConfig"
    >
        <abc-form
            ref="form"
            is-excel
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-descriptions
                        :column="!isInitStockInOrder ? 3 : 2"
                        :label-width="106"
                        size="large"
                        grid
                        background
                        stretch-last-item
                    >
                        <abc-descriptions-item v-if="!isInitStockInOrder" :label="'采购订单'" content-class-name="ellipsis">
                            <div v-abc-title="relationshipOrderNo || '-'" style="height: 100%;" class="show-text-box"></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            label="供应商"
                            content-class-name="ellipsis"
                            :content-style="{ padding: isAdd ? '0px' : '' }"
                        >
                            <abc-form-item v-if="isAdd">
                                <abc-select
                                    v-model="order.supplierId"
                                    custom-class="supplierWrapper"
                                    with-search
                                    clearable
                                    placeholder="供应商"
                                    inner-width="280px"
                                    placement="bottom-end"
                                    :fetch-suggestions="fetchSuppliers"
                                    @change="handleSupplierChange"
                                >
                                    <abc-option
                                        v-for="it in supplierOptions"
                                        :key="`${it.id }`"
                                        :value="it.id"
                                        :label="it.name"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <span v-else v-abc-title="order.supplier || '-'" class="show-text-box"></span>
                        </abc-descriptions-item>

                        <abc-descriptions-item v-if="!isInitStockInOrder" :label="'供应商销售员'">
                            <div v-abc-title="order.supplierSeller?.name || order.supplierSellerName || '-'" class="show-text-box ellipsis"></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item v-if="!isInitStockInOrder && !isSingleStore" label="类型" content-class-name="ellipsis">
                            <div v-abc-title="formatSupplierTypeName(order.supplierObj || order.supplier)" class="show-text-box ellipsis"></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item :label="'收货门店'" content-class-name="ellipsis">
                            <span v-abc-title="isAdd ? currentClinic.clinicName : clinicName(order.toOrgan, false)" class="show-text-box"></span>
                        </abc-descriptions-item>



                        <abc-descriptions-item
                            v-if="!isInitStockInOrder && receiveOrders.length"
                            :label="'关联收货单'"
                            content-class-name="ellipsis"
                        >
                            <order-no-comp :orders="receiveOrders" :only-view="true" :disabled="onlyView"></order-no-comp>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            v-if="!isInitStockInOrder && returnOrders.length"
                            :label="'关联退货单'"
                            content-class-name="ellipsis"
                        >
                            <order-no-comp :orders="returnOrders" :only-view="true" :disabled="onlyView"></order-no-comp>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            v-if="!isInitStockInOrder && inspectOrders.length"
                            :label="'关联验收单'"
                            content-class-name="ellipsis"
                        >
                            <order-no-comp :orders="inspectOrders" :only-view="true" :disabled="onlyView"></order-no-comp>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            :label="'入库人'"
                            content-class-name="ellipsis"
                            :content-style="{ padding: canConfirm ? '0px' : '' }"
                        >
                            <abc-form-item v-if="canConfirm">
                                <employee-select v-model="order.stockInBy" :employee-list="currentStockEmployeeList" @change="handleReceiverChange">
                                </employee-select>
                            </abc-form-item>
                            <span v-else v-abc-title="order.stockInUser?.name ?? '-'" class="show-text-box"></span>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            :label="'入库日期'"
                            content-class-name="ellipsis"
                            :content-style="{ padding: canConfirm ? '0px' : '' }"
                        >
                            <abc-form-item v-if="canConfirm" required>
                                <!--:disabled="!canConfirm"-->
                                <abc-date-picker
                                    v-model="order.inDate"
                                    :show-icon="false"
                                    disabled
                                    :picker-options="{ disabledDate: disabledDate }"
                                >
                                </abc-date-picker>
                            </abc-form-item>
                            <span v-else v-abc-title="formatDate(order.inDate,'YYYY-MM-DD')" class="show-text-box"></span>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            v-if="!isInitStockInOrder"
                            :label="'备注'"
                            content-class-name="ellipsis"
                            :content-style="{ padding: canConfirm ? '0px' : '' }"
                        >
                            <abc-form-item v-if="canConfirm">
                                <abc-input
                                    v-model="order.comment"
                                    size="large"
                                    adaptive-width
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                            <span v-else v-abc-title="order.comment || '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        :show-hover-tr-bg="false"
                        :loading="tableLoading"
                        cell-size="large"
                        :render-config="renderConfig"
                        :data-list="order.list"
                        empty-size="small"
                    >
                        <template #shortId="{ trData: row }">
                            <abc-table-cell class="ellipsis" style="gap: 8px;">
                                <abc-tooltip-info v-if="getSettlementText(row)" :content="getSettlementText(row)"></abc-tooltip-info>
                                <span v-abc-title.ellipsis="row.goods?.shortId ?? ''"></span>
                            </abc-table-cell>
                        </template>

                        <template #displayName="{ trData: row }">
                            <display-name-cell v-if="row.goods" :goods="row.goods"></display-name-cell>
                        </template>

                        <template #sellPrice="{ trData: row }">
                            <abc-table-cell v-if="row.goods" :theme="isNull(row.goods.packagePrice) ? 'warning' : 'default'">
                                <div v-if="isNull(row.goods.packagePrice)" key="un-fix-price">
                                    未定价
                                </div>
                                <div
                                    v-else-if="isChineseMedicine(row.goods)"
                                >
                                    <span v-abc-title.ellipsis="`${moneyDigit(row.goods.piecePrice, 5)}/${row.goods.pieceUnit}`"></span>
                                </div>
                                <div
                                    v-else
                                >
                                    <span v-abc-title.ellipsis="`${moneyDigit(row.goods.packagePrice, 5)}/${row.goods.packageUnit}`"></span>
                                </div>
                            </abc-table-cell>
                        </template>

                        <template #goodsCount="{ trData: row }">
                            <abc-table-cell v-if="row.inOrderTransTotalCount">
                                <span v-abc-title.ellipsis="`${row.inOrderTransTotalCount}${row.receiveUnit}`"></span>
                            </abc-table-cell>
                            <abc-table-cell v-else-if="row.useCount && row.usePieceCount">
                                <span v-abc-title.ellipsis="`${row.useCount}${row.useUnit}${row.usePieceCount}${row.usePieceUnit}`"></span>
                            </abc-table-cell>
                            <abc-table-cell v-else-if="row.useCount">
                                <span v-abc-title.ellipsis="`${row.useCount}${row.useUnit}`"></span>
                            </abc-table-cell>
                            <abc-table-cell v-else-if="row.usePieceCount">
                                <span v-abc-title.ellipsis="`${row.usePieceCount}${row.usePieceUnit}`"></span>
                            </abc-table-cell>

                            <abc-table-cell
                                v-else
                            >
                                <span
                                    v-abc-title.ellipsis="complexCount({
                                        ...row.goods,
                                        packageCount: row.packageCount || 0,
                                        pieceCount: row.pieceCount || 0,
                                    })"
                                ></span>
                            </abc-table-cell>
                        </template>

                        <template #packageCostPrice="{ trData: row }">
                            <abc-table-cell v-abc-title.ellipsis="formatMoney(row.useUnitCostPrice, false)"></abc-table-cell>
                        </template>


                        <template #totalCost="{ trData: row }">
                            <abc-table-cell v-abc-title.ellipsis="isNull(row.useTotalCostPrice) ? '' : formatMoney(row.useTotalCostPrice, false)"></abc-table-cell>
                        </template>

                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item,
                            }"
                        >
                            <traceable-code-cell
                                v-model="item.traceableCodeList"
                                :goods="item.goods"
                                :goods-count="{
                                    label: '入库数量',
                                    unit: item.receiveUnit || item.useUnit,
                                    unitCount: item.inOrderTransTotalCount || item.useCount || 0
                                }"
                                readonly
                            ></traceable-code-cell>
                        </template>

                        <template #footer>
                            <abc-flex flex="1" align="center" justify="flex-end">
                                <abc-space :size="4" style="margin-right: 12px; color: var(--abc-color-T1);">
                                    <abc-p gray>
                                        品种
                                    </abc-p>
                                    <abc-p>{{ order.kindCount }}</abc-p>
                                    <abc-p gray>
                                        ，入库数量
                                    </abc-p>
                                    <abc-p>{{ order.sum || '' }}</abc-p>
                                    <abc-p gray>
                                        ，含税金额
                                    </abc-p>
                                    <abc-p>{{ isNull(order.amount) ? '' : formatMoney(order.amount, false) }}</abc-p>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <template v-if="onlyView" #footer>
            <abc-flex align="center" justify="flex-end">
                <abc-button
                    variant="ghost"
                    @click="handleCancel"
                >
                    关闭
                </abc-button>
            </abc-flex>
        </template>

        <template v-else #footer>
            <abc-flex align="center" justify="space-between">
                <abc-space align="center">
                    <abc-button
                        v-if="order && order.mallOrderId"
                        variant="ghost"
                        @click="handleOpenMallOrderDetail"
                    >
                        查看商城订单
                    </abc-button>
                    <logs-v3-popover v-if="logs.length" :logs="logs"></logs-v3-popover>
                </abc-space>
                <abc-space>
                    <template v-if="canConfirm">
                        <abc-button
                            :loading="confirmBtnLoading"
                            :disabled="!order.list.length"
                            @click="handleSubmitBefore()"
                        >
                            确定入库
                        </abc-button>
                    </template>
                    <abc-button
                        v-if="allowReturnGoods"
                        :disabled="!canReturnGoodsCount"
                        :count="canReturnGoodsCount"
                        theme="danger"
                        variant="outline"
                        @click="handleResubmit"
                    >
                        退货
                    </abc-button>
                    <abc-check-access v-if="isFinished">
                        <print-dropdown
                            @print="print"
                            @select-print-setting="openPrintConfigSettingDialog"
                        ></print-dropdown>
                    </abc-check-access>
                    <abc-button
                        v-if="!isAdd"
                        variant="ghost"
                        @click="exportRK"
                    >
                        导出
                    </abc-button>
                    <abc-button
                        variant="ghost"
                        @click="handleCancel"
                    >
                        {{ closeText }}
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>

        <abc-dialog
            v-if="showReceiveDialog"
            v-model="showReceiveDialog"
            :title="receiveTitle"
            content-styles="width:560px;"
            append-to-body
        >
            <abc-layout>
                <abc-section>
                    <abc-p gray>
                        入库后将立即更新库存
                    </abc-p>
                </abc-section>
                <abc-section style="margin-top: 12px;">
                    <abc-descriptions
                        :column="1"
                        :label-width="88"
                        size="large"
                        grid
                    >
                        <abc-descriptions-item :label="'采购订单'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="relationshipOrderNo || '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'供应商'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.supplier ?? '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'收货门店'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.toOrgan?.name ?? '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'入库品种'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.kindCount" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'入库数量'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.sum" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'入库金额'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="formatMoney(order.amount, false)" class="show-text-box"></span>
                        </abc-descriptions-item>

                        <abc-descriptions-item :label="'入库人'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.stockInUser?.name ?? '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-section>
            </abc-layout>

            <template slot="footer">
                <div class="dialog-footer">
                    <abc-space>
                        <abc-button
                            @click="handleSubmit()"
                        >
                            确定
                        </abc-button>
                        <abc-button
                            type="blank"
                            @click="showReceiveDialog = false"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </div>
            </template>
        </abc-dialog>
    </frame-dialog>
</template>

<script>
    import Big from 'big.js';
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';
    import {
        PurchaseGoodsInOrderStatus,
        PurchaseGoodsInOrderStatusName,
        RelatedOrderType,
        PurchaseGoodsInOrderStatusTagTheme,
    } from '@/views-pharmacy/inventory/constant';
    import GoodsBaseAPI from 'api/goods';
    // import GoodsAPIV3 from 'api/goods/index-v3';
    import { mapGetters } from 'vuex';
    import { GoodsInInitTypeEnum } from 'views/inventory/constant';
    import {
        clinicName,
        complexCount, formatMoney, isChineseMedicine,
    } from '@/filters';
    import {
        getSafeNumber,
        isNull, moneyDigit,
    } from '@/utils';
    import EnterEvent from 'views/common/enter-event';
    import EmployeeSelect from '@/views-pharmacy/inventory/frames/components/employee-select.vue';
    import { formatDate } from '@abc/utils-date';
    import StockInAPI from 'api/goods/stock-in';
    import {
        getSourceOrder, getSourceOrderText, formatSupplierTypeName,
    } from '@/views-pharmacy/inventory/utils';
    const ReceiveActionType = Object.freeze({
        DRAFT: 0,
        CONFIRM: 1,
        FINISH: 2,
    });
    import themeStyle from 'src/styles/theme.module.scss';
    import PrintDropdown from 'views/print/print-dropdown.vue';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import Clone from 'utils/clone';
    import AbcPrinter from '@/printer';
    import { ABCPrintConfigKeyMap } from '@/printer/constants';
    import OrderNoComp from '@/views-pharmacy/inventory/frames/components/orderNoComp.vue';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    export default {
        name: 'OrderDialog',
        components: {
            LogsV3Popover,
            OrderNoComp,
            PrintDropdown,
            EmployeeSelect,
            FrameDialog,
            DisplayNameCell,
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
        },
        mixins: [EnterEvent],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: Boolean,
            orderId: String,
            onlyView: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                themeStyle,
                order: {
                    // orderId
                    id: '',
                    // clinicId
                    clinicId: '',
                    // 单号
                    orderNo: '',
                    // 单据状态
                    status: '',
                    // 单据备注
                    comment: '',
                    // 供应商
                    supplierId: '',
                    // 单据列表
                    list: [],
                    // 品种数
                    kindCount: 0,
                    // 总金额
                    amount: 0,
                    // 入库人
                    stockInBy: '',
                    stockInUser: {
                        id: '',
                        name: '',
                    },
                    // 入库日期
                    inDate: '',
                    relatedOrders: {
                        list: [],
                    },
                    orderFlag: 0,// 用于判断总部帮子店收货，可以操作子店的收货单
                },
                clinicEmployees: [],
                ReceiveActionType,
                showDialog: this.value,
                pageLoading: false,
                tableLoading: false,
                submitBtnLoading: false,
                confirmBtnLoading: false,
                draftBtnLoading: false,
                showReceiveDialog: false,
                receiveType: ReceiveActionType.CONFIRM,
                searchKey: '',
                supplierOptions: [],
                showAcceptanceCheckOrderDialog: false,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'userInfo',
                'currentPharmacy',
                'supplierList',
                'stockEmployeeList',
                'isChainAdmin',
                'traceCodeConfig',
                'isSingleStore',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            currentStockEmployeeList() {
                if (this.canConfirm && this.canModifyOrder) {
                    return this.clinicEmployees || [];
                }
                return this.stockEmployeeList || [];
            },
            needTransGoodsClassificationName() {
                return this.viewDistributeConfig.needTransGoodsClassificationName;
            },
            closeText() {
                return (this.pageLoading || this.order?.status === PurchaseGoodsInOrderStatus.GOODS_IN) ? '关闭' : '取消';
            },
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            pharmacyType() {
                return this.currentPharmacy?.type;
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            isAdd() {
                return !this.orderId || this.isDraft;
            },
            isDraft() {
                return this.order.status === PurchaseGoodsInOrderStatus.DRAFT;
            },
            isFinished() {
                return this.order.status === PurchaseGoodsInOrderStatus.GOODS_IN;
            },
            allowReturnGoods() {
                if (!this.isChainAdmin) {
                    return this.isFinished;
                }
                return this.isFinished && (this.order?.toOrgan?.clinicId === this.clinicId || this.canModifyOrder);
            },
            canReturnGoodsCount() {
                return this.order.list?.filter((i) => {
                    return i.returnLeft;
                })?.length;
            },
            title() {
                return this.isInitStockInOrder ? '初始化入库单' : '采购入库单';
            },
            receiveTitle() {
                return this.receiveType === ReceiveActionType.CONFIRM ? '确认入库' : '确认入库/入库';
            },
            statusName() {
                return PurchaseGoodsInOrderStatusName[this.order.status];
            },
            tagConfig() {
                const config = {
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                };
                config.theme = PurchaseGoodsInOrderStatusTagTheme[this.order.status];
                return config;
            },
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 来源单据
            relatedOrder() {
                const { relatedOrders } = this.order;
                const [order] = getSourceOrder(relatedOrders?.list ?? [], RelatedOrderType.PURCHASE);
                return order || {};
            },
            relationshipOrderNo() {
                const {
                    sourceType,
                } = this.relatedOrder || {};
                if (!isNull(sourceType)) {
                    return getSourceOrderText(this.relatedOrder);
                }
                return '';

            },
            // 收货单
            receiveOrders() {
                const { relatedOrders } = this.order;
                const orders = getSourceOrder(relatedOrders?.list ?? [], RelatedOrderType.RECEIPT);
                return orders || [];
            },
            // 验收单
            inspectOrders() {
                const { relatedOrders } = this.order;
                const orders = getSourceOrder(relatedOrders?.list ?? [], RelatedOrderType.INSPECT);
                return orders || [];
            },
            // 退货单
            returnOrders() {
                const orders = getSourceOrder(this.order.relatedOrders?.list ?? [], RelatedOrderType.RETURN);
                return orders || [];
            },
            computedOrder() {
                const goodsIds = new Set();

                const totalPrice = this.order.list.reduce((res, item) => {
                    goodsIds.add(item.goodsId || item.goods?.id);
                    res = Big(res).plus(item.receiveTotalCost || 0);
                    return res;
                }, Big(0));

                return {
                    kindCount: goodsIds.size,
                    amount: totalPrice.toNumber(),
                };
            },
            canConfirm() {
                const {
                    status, toOrganId,
                } = this.order;
                return (status === PurchaseGoodsInOrderStatus.CONFIRM) && ((toOrganId === this.currentClinic.clinicId) || this.canModifyOrder);
            },
            // 连锁总部 单子是帮子店建的标识
            canModifyOrder() {
                return this.isChainAdmin && !!this.order.orderFlag;
            },
            canEdit() {
                return this.isAdd || this.canConfirm;
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'shortId',
                            label: '商品编码',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'displayName',
                            label: '商品名称',
                            slot: true,
                            style: {
                                flex: '2',
                                width: '180px',
                                minWidth: '180px',
                                maxWidth: '400px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'sellPrice',
                            label: '售价',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'goodsCount',
                            label: '入库数量',
                            slot: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'packageCostPrice',
                            label: '进价',
                            slot: true,

                            style: {
                                width: '110px',
                                minWidth: '110px',
                                maxWidth: '110px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'totalCost',
                            label: '金额',
                            slot: true,

                            style: {
                                width: '110px',
                                minWidth: '110px',
                                maxWidth: '110px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'batchNo',
                            label: '生产批号',
                            style: {
                                width: '110px',
                                minWidth: '110px',
                                maxWidth: '110px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'productionDate',
                            label: '生产日期',
                            // colType: 'date',
                            style: {
                                width: '92px',
                                minWidth: '92px',
                                maxWidth: '92px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'expiryDate',
                            label: '有效日期',
                            // colType: 'date',
                            style: {
                                width: '92px',
                                minWidth: '92px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            label: '追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
            logs() {
                return this.order?.logs ?? [];
            },
            // 是否初始化入库单
            isInitStockInOrder() {
                return GoodsInInitTypeEnum.includes(this.order?.type);
            },
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        async created() {
            this.fetchSuppliers();
            if (this.orderId) {
                try {
                    this.pageLoading = true;
                    await this.fetchOrderDetail(this.orderId);
                    if (this.canConfirm && this.canModifyOrder) {
                        await this.fetchEmployeeByModuleId();
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.pageLoading = false;
                }
            }
        },
        methods: {
            clinicName,
            formatDate,
            isNull,
            formatMoney,
            isChineseMedicine,
            complexCount,
            moneyDigit,
            formatSupplierTypeName,
            getSettlementText(row) {
                if (isNull(row.businessSettleStatus) || row.businessSettleStatus === 0) {
                    return '';
                }
                if (row.businessSettleStatus === 4) {
                    return '已结算';
                }
                const count = Big(getSafeNumber(row.useCount)).minus(getSafeNumber(row.settledCount)).toNumber();
                // if(row.settledCount){
                // }
                // return `未结算${count}${row.useUnit}`;
                return `已结算${row.settledCount || 0}${row.useUnit}，未结算${count || 0}${row.useUnit}`;
            },
            async handleOpenMallOrderDetail() {
                if (this.order.mallOrderId) {
                    const mall = await this.$abcPlatform.module.mall;
                    mall.service.OrderDetailDialog({
                        orderId: this.order.mallOrderId,
                    });
                }
            },
            disabledDate(date) {
                return date < new Date(this.order.inspectTime) || date > new Date();
            },
            async fetchOrderDetail(orderId) {
                try {
                    const order = await StockInAPI.getById(orderId);

                    this.order = {
                        ...order,
                        list: order.list ?? [],
                        stockInBy: order.stockInBy || order.stockInUser?.id || this.userInfo?.id,
                        inDate: order.inDate || formatDate(Date.now(), 'YYYY-MM-DD'),
                        comment: order.comment[0]?.content || '',
                    };
                    this.handleReceiverChange(this.order.stockInBy);
                } catch (e) {
                    console.error(e);
                }
            },


            async confirmOrder(orderId, postData) {
                try {
                    await StockInAPI.confirmOrder(orderId, postData);
                } catch (e) {
                    console.error(e);
                }
            },
            async fetchSuppliers(key = '') {
                this.supplierOptions = this.supplierList.filter((item) => {
                    return item.name.includes(key.trim());
                });
            },
            handleDeleteTr(index) {
                // const item = this.order.list[index];

                this.order.list.splice(index, 1);
            },
            async selectGoods(goods) {
                try {
                    const goodsId = goods.goodsId || goods.id;
                    // 重新获取药品信息
                    const { data: tempGoods } = await GoodsBaseAPI.goods(
                        goodsId,
                        this.clinicId,
                        {
                            forPurchase: 1, withStock: 1,
                        },
                    );

                    if (tempGoods) {
                        this.order.list.push({
                            ...tempGoods,
                            goods: tempGoods,
                        });
                    }
                } catch (e) {
                    console.error(e);
                }

            },
            createPostData() {
                const {
                    supplierId,
                    supplier,
                    list,
                    stockInBy,
                    comment,
                    // inDate,
                } = this.order;

                const obj = {
                    stockInBy,
                    pharmacyNo: this.pharmacyNo,
                    comment,
                    supplier,
                    supplierId,
                    // inDate,
                    list: list.map((e) => {
                        return {
                            id: e.id,
                        };
                    }),
                };

                return obj;
            },
            /**
             * @desc 打印入库单
             * <AUTHOR>
             * @date 2018/11/24 14:03:17
             * @params
             * @return
             */
            print() {
                const data = Clone(this.order);
                data.comment = this.comment || '';
                if (this.multiPharmacyCanUse) {
                    data.multiPharmacyCanUse = true;
                }
                data.needTransGoodsClassificationName = this.needTransGoodsClassificationName;
                AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates.goodsIn,
                    printConfigKey: ABCPrintConfigKeyMap.RK,
                    data,
                });
            },
            /**
             * @desc  导出入库单
             * <AUTHOR>
             * @date 2018/11/28 10:44:05
             */
            async exportRK() {
                await StockInAPI.exportById(this.orderId);
            },
            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'goods-in' }).generateDialogAsync({ parent: this });
            },
            handleSupplierChange(id) {
                this.order.supplier = this.supplierOptions.find((e) => e.id === id) || {};
            },
            handleSelectOrder(order) {
                console.log(order);
            },
            handleReceiverChange(employeeId) {
                const employee = this.stockEmployeeList.find((e) => e.employeeId === employeeId);
                if (employee) {
                    const {
                        employeeId: id, employeeName: name,
                    } = employee;
                    this.order.stockInUser = {
                        id,
                        name,
                    };
                }
            },
            handleSubmitBefore() {
                this.showReceiveDialog = true;
            },
            async handleSubmit(status = ReceiveActionType.CONFIRM) {
                try {
                    if (status === ReceiveActionType.DRAFT) this.draftBtnLoading = true;
                    if (status === ReceiveActionType.CONFIRM) this.confirmBtnLoading = true;
                    if (status === ReceiveActionType.FINISH) this.submitBtnLoading = true;

                    const postData = this.createPostData({ status });

                    await this.confirmOrder(this.orderId, postData);

                    if (this.isAdd) {
                        this.$Toast({
                            message: '提交成功',
                            type: 'success',
                        });
                    }
                    this.showDialog = false;
                    this.$emit('refresh', this.isAdd ? 1 : 2);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.submitBtnLoading = false;
                    this.confirmBtnLoading = false;
                    this.draftBtnLoading = false;
                }
            },
            handleResubmit() {
                this.$emit('handleResubmit', this.orderId);
            },
            handleCancel() {
                this.showDialog = false;
                this.$emit('close');
            },
        },
    };
</script>

