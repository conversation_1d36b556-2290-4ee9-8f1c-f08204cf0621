<template>
    <frame-dialog
        v-if="showDialog"
        ref="frameDialog"
        v-model="showDialog"
        :title="title"
        :order-no="order.orderNo"
        :status-name="statusName"
        :tag-config="tagConfig"
        show-title-append
        :loading="pageLoading"
    >
        <abc-form
            ref="form"
            is-excel
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-descriptions
                        :column="3"
                        :label-width="106"
                        grid
                        size="large"
                        background
                        stretch-last-item
                    >
                        <abc-descriptions-item :label="'采购订单'" content-class-name="ellipsis">
                            <div v-abc-title="relationshipOrderNo || '-'" class="show-text-box"></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            label="供应商"
                            content-class-name="ellipsis"
                            :content-style="{ padding: isAdd ? '0px' : '' }"
                        >
                            <abc-form-item v-if="isAdd">
                                <abc-select
                                    v-model="order.supplierId"
                                    custom-class="supplierWrapper"
                                    size="large"
                                    with-search
                                    clearable
                                    placeholder="供应商"
                                    inner-width="280px"
                                    placement="bottom-end"
                                    :fetch-suggestions="fetchSuppliers"
                                    @change="handleSupplierChange"
                                >
                                    <abc-option
                                        v-for="it in supplierOptions"
                                        :key="`${it.id }`"
                                        :value="it.id"
                                        :label="it.name"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <div v-else v-abc-title="order.supplier?.name || '-'" class="show-text-box"></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item :label="'供应商销售员'">
                            <div v-abc-title="order.supplierSeller?.name || order.supplierSellerName || '-'" class="show-text-box ellipsis"></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item :label="'收货门店'">
                            <div v-abc-title="isAdd ? order.clinicName : order.clinicName" class="show-text-box ellipsis"></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item :label="'关联收货单'">
                            <div
                                v-abc-title="receiveOrder?.orderNo || '-'"
                                style="color: #005ed9; cursor: pointer;"
                                class="show-text-box ellipsis"
                                @click="showTakeDeliveryOrderDialog = true"
                            ></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            :label="'验收人'"
                            content-class-name="ellipsis"
                            :content-style="{ padding: canEdit ? '0px' : '' }"
                        >
                            <abc-form-item v-if="canEdit">
                                <employee-select v-model="order.inspectBy" :employee-list="currentStockEmployeeList" @change="handleReceiverChange">
                                </employee-select>
                            </abc-form-item>
                            <div v-else v-abc-title="order.inspector?.name ?? '-'" class="show-text-box"></div>
                        </abc-descriptions-item>

                        <abc-descriptions-item
                            :label="'验收日期'"
                            content-class-name="ellipsis"
                            :content-style="{
                                padding: canEdit ? '0px' : ''
                            }"
                        >
                            <abc-form-item v-if="canEdit" required>
                                <abc-date-picker
                                    v-model="order.inspectTime"
                                    :picker-options="{ disabledDate: disabledDate }"
                                    size="large"
                                >
                                </abc-date-picker>
                            </abc-form-item>
                            <div v-else v-abc-title="formatDate(order.inspectTime,'YYYY-MM-DD')" class="show-text-box"></div>
                        </abc-descriptions-item>
                        <abc-descriptions-item
                            :label="'备注'"
                            content-class-name="ellipsis"
                            :content-style="{ padding: canConfirm ? '0px' : '' }"
                        >
                            <abc-form-item v-if="canConfirm">
                                <abc-input
                                    v-model="order.comment"
                                    size="large"
                                    adaptive-width
                                    @enter="enterEvent"
                                ></abc-input>
                            </abc-form-item>
                            <span v-else v-abc-title="order.comment || '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="tableRef"
                        type="excel"
                        :show-hover-tr-bg="false"
                        :loading="tableLoading"
                        :render-config="renderConfig"
                        :data-list="order.list"
                        empty-size="small"
                        cell-size="large"
                        @delete-tr="handleDeleteTr"
                    >
                        <template #shortId="{ trData: row }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="row.goods?.shortId ?? ''"></span>
                            </abc-table-cell>
                        </template>

                        <template #displayName="{ trData: row }">
                            <display-name-cell v-if="row.goods" :goods="row.goods"></display-name-cell>
                        </template>

                        <template #receiveNumSum="{ trData: row }">
                            <abc-table-cell v-if="row.receiveCount">
                                <span v-abc-title.ellipsis="`${row.receiveCount}${row.receiveUnit}`"></span>
                            </abc-table-cell>
                            <abc-table-cell
                                v-else
                            >
                                <span
                                    v-abc-title.ellipsis="complexCount({
                                        ...row.goods,
                                        packageCount: row.receivePackageCount || 0,
                                        pieceCount: row.receivePieceCount || 0,
                                    })"
                                ></span>
                            </abc-table-cell>
                        </template>

                        <!--合格数量-->
                        <template #inspectNumSum="{ trData: row }">
                            <abc-form-item v-if="canEdit && !isChainAdminDistribution" required>
                                <goods-count-input
                                    :package-count.sync="row.inspectPackageCount"
                                    :piece-count.sync="row.inspectPieceCount"
                                    :package-unit="row.goods?.packageUnit"
                                    :piece-unit="row.goods?.pieceUnit"
                                    :show-package-count-input="row.showPackageUnit"
                                    :show-piece-count-input="row.showPieceUnit"
                                    :package-count-input-config="{
                                        config: {
                                            max: row.receivePackageCount || 9999999,
                                            supportZero: true,
                                            formatLength: row.goods?.type === GoodsTypeEnum.GOODS ? 2 : 0,
                                        },
                                    }"
                                    :piece-count-input-config="{
                                        config: {
                                            max: row.receivePieceCount || 9999999,
                                            supportZero: true,
                                            formatLength: (isChineseMedicine(row.goods) || row.goods.type === GoodsTypeEnum.GOODS) ? 2 : 0,
                                        }
                                    }"
                                    @packageCountChange="handleChangeUnitCount(row, row.goods)"
                                    @pieceCountChange="handleChangeUnitCount(row, row.goods)"
                                >
                                </goods-count-input>
                            </abc-form-item>
                            <template v-else>
                                <abc-table-cell v-if="showTransCount(row)">
                                    <span v-abc-title.ellipsis="`${row.inspectTransTotalCount}${row.receiveUnit}`"></span>
                                </abc-table-cell>

                                <abc-table-cell
                                    v-else
                                >
                                    <span
                                        v-abc-title.ellipsis="complexCount({
                                            ...row.goods,
                                            packageCount: row.inspectPackageCount || 0,
                                            pieceCount: row.inspectPieceCount || 0,
                                        })"
                                    ></span>
                                </abc-table-cell>
                            </template>
                        </template>

                        <template #unqualifiedNumSum="{ trData: row }">
                            <abc-table-cell v-if="showTransCount(row)">
                                <span v-abc-title.ellipsis="`${row.unqualifiedTransTotalCount}${row.receiveUnit}`"></span>
                            </abc-table-cell>
                            <abc-table-cell v-else>
                                <span v-abc-title.ellipsis="changeCountText(row)" :style="{ color: changeCountColor(row) }"></span>
                            </abc-table-cell>
                        </template>


                        <template #inspectComment="{ trData: row }">
                            <abc-form-item v-if="canEdit && changeCount(row.inspectPieceCount, row.inspectPackageCount, row.goods, row.receivePieceCount, row.receivePackageCount)">
                                <abc-input v-model="row.inspectComment" :max-length="50"></abc-input>
                            </abc-form-item>
                            <abc-table-cell v-else>
                                <span v-abc-title.ellipsis="row.inspectComment || ''"></span>
                            </abc-table-cell>
                        </template>

                        <template #measure="{ trData: row }">
                            <abc-form-item v-if="canEdit && changeCount(row.inspectPieceCount, row.inspectPackageCount, row.goods, row.receivePieceCount, row.receivePackageCount)">
                                <abc-input v-model="row.measure" :max-length="50"></abc-input>
                            </abc-form-item>
                            <abc-table-cell v-else>
                                <span v-abc-title.ellipsis="row.measure || ''"></span>
                            </abc-table-cell>
                        </template>

                        <!--追溯码-->
                        <template
                            #traceableCode="{
                                trData: item,
                            }"
                        >
                            <traceable-code-cell
                                v-if="(orderId && item.unqualifiedTraceableCodeList?.length) || (canEdit && changeCount(item.inspectPieceCount, item.inspectPackageCount, item.goods, item.receivePieceCount, item.receivePackageCount))"
                                v-model="item.unqualifiedTraceableCodeList"
                                :goods="item.goods"
                                :goods-count="getUnitCount(item, item.goods)"
                                :readonly="!canEdit"
                                :need-validate="validateCell"
                                :is-strict-count-with-trace-code-collect="isStrictCountWithTraceCodeCollect"
                            ></traceable-code-cell>
                        </template>

                        <template #footer>
                            <abc-flex flex="1" align="center" justify="flex-end">
                                <abc-space :size="4" style="margin-right: 12px; margin-left: auto; color: var(--abc-color-T1);">
                                    <abc-p gray>
                                        品种
                                    </abc-p>
                                    <abc-p>{{ order.kindCount }}</abc-p>
                                    <abc-p gray>
                                        ，收货数量
                                    </abc-p>
                                    <abc-p>{{ order.receiveNumSum }}</abc-p>
                                </abc-space>
                            </abc-flex>
                        </template>
                    </abc-table>
                </abc-layout-content>
            </abc-layout>
        </abc-form>

        <template v-if="onlyView" #footer>
            <abc-flex align="center" justify="flex-end">
                <abc-button
                    variant="ghost"
                    @click="handleCancel"
                >
                    关闭
                </abc-button>
            </abc-flex>
        </template>
        <template v-else #footer>
            <abc-flex align="center" justify="space-between">
                <abc-space align="center">
                    <abc-button
                        v-if="order?.mallOrderId"
                        variant="ghost"
                        @click="handleOpenMallOrderDetail"
                    >
                        查看商城订单
                    </abc-button>
                    <logs-v3-popover v-if="logs.length" :logs="logs"></logs-v3-popover>
                </abc-space>
                <abc-space>
                    <abc-button
                        v-if="canConfirm"
                        :loading="confirmBtnLoading"
                        :disabled="!order.list.length"
                        @click="handleSubmitBefore(ReceiveActionType.CONFIRM)"
                    >
                        确定验收
                    </abc-button>

                    <abc-button
                        v-if="canExport"
                        variant="ghost"
                        :disabled="pageLoading"
                        @click="handleExport"
                    >
                        导出
                    </abc-button>

                    <abc-button
                        type="blank"
                        @click="handleCancel"
                    >
                        {{ closeText }}
                    </abc-button>
                </abc-space>
            </abc-flex>
        </template>

        <abc-dialog
            v-if="showReceiveDialog"
            v-model="showReceiveDialog"
            :title="receiveTitle"
            content-styles="width:560px;"
            append-to-body
        >
            <abc-layout>
                <abc-section>
                    <abc-descriptions
                        :column="1"
                        :label-width="88"
                        size="large"
                        grid
                    >
                        <abc-descriptions-item :label="'采购订单'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="relationshipOrderNo || '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'供应商'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.supplier?.name ?? '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'收货门店'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.clinicName || '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'收货品种'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.kindCount || '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                        <abc-descriptions-item :label="'验收结果'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="inspectResult.text" :style="{ color: inspectResult.color }" class="show-text-box"></span>
                        </abc-descriptions-item>

                        <!--<abc-descriptions-item :label="'合格数量'">-->
                        <!--    <span v-abc-title.ellipsis="computedOrder.inspectNumSum"></span>-->
                        <!--</abc-descriptions-item>-->

                        <!--<abc-descriptions-item :label="'不合格数量'">-->
                        <!--    <span v-abc-title.ellipsis="computedOrder.unqualifiedNumSum"></span>-->
                        <!--</abc-descriptions-item>-->

                        <abc-descriptions-item :label="'验收人'" :label-style="{ background: themeStyle.P5 }">
                            <span v-abc-title.ellipsis="order.inspector?.name ?? '-'" class="show-text-box"></span>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-section>
            </abc-layout>

            <template slot="footer">
                <div class="dialog-footer">
                    <abc-space>
                        <abc-button
                            :loading="submitBtnLoading"
                            @click="handleSubmit(inspectType)"
                        >
                            确定
                        </abc-button>
                        <abc-button
                            type="blank"
                            @click="showReceiveDialog = false"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </div>
            </template>
        </abc-dialog>

        <take-delivery-order-dialog
            v-if="showTakeDeliveryOrderDialog"
            v-model="showTakeDeliveryOrderDialog"
            :order-id="receiveOrder.id"
            :only-view="true"
        ></take-delivery-order-dialog>
    </frame-dialog>
</template>

<script>
    import Big from 'big.js';
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';
    import {
        PurchaseInspectOrderStatus,
        PurchaseInspectOrderStatusName,
        PurchaseInspectOrderStatusTagTheme,
        RelatedOrderType,
    } from '@/views-pharmacy/inventory/constant';
    import GoodsBaseAPI from 'api/goods';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import { mapGetters } from 'vuex';
    import { CHECK_IN_SUPPLIER_ID } from 'views/inventory/constant';
    import {
        complexCount, formatMoney, isChineseMedicine,
    } from '@/filters';
    import {
        isNull, moneyDigit,
    } from '@/utils';
    import EnterEvent from 'views/common/enter-event';
    import EmployeeSelect from '@/views-pharmacy/inventory/frames/components/employee-select.vue';
    import { formatDate } from '@abc/utils-date';
    import GoodsCountInput from '@/views-pharmacy/inventory/frames/components/goods-count-input.vue';
    import { unitEqual } from 'views/inventory/goods-utils';
    import { GoodsTypeEnum } from '@abc/constants';
    import {
        getSourceOrder, getSourceOrderText,
    } from '@/views-pharmacy/inventory/utils';
    import themeStyle from 'src/styles/theme.module.scss';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import LogsV3Popover from '@/views-pharmacy/components/logs-v3-popover.vue';
    import TraceCode, { TraceCodeScenesEnum } from '@/service/trace-code/service';
    import ClinicAPI from 'api/clinic';
    const takeDeliveryOrderDialog = () => import('@/views-pharmacy/inventory/frames/purchase/take-delivery/order-dialog.vue');
    const ReceiveActionType = Object.freeze({
        // 保存草稿
        DRAFT: 0,
        // 确定验收
        CONFIRM: 1,
        // 确定验收并入库
        FINISH: 2,
    });
    export default {
        name: 'OrderDialog',
        components: {
            GoodsCountInput,
            EmployeeSelect,
            FrameDialog,
            takeDeliveryOrderDialog,
            DisplayNameCell,
            LogsV3Popover,
            TraceableCodeCell: () => import('views/inventory/components/traceable-code/traceable-code-cell.vue'),
        },
        mixins: [EnterEvent],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            value: Boolean,
            orderId: String,
            onlyView: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                themeStyle,
                order: {
                    // orderId
                    id: '',
                    // clinicId
                    clinicId: '',
                    // 单号
                    orderNo: '',
                    // 单据状态
                    status: '',
                    // 单据备注
                    comment: '',
                    // 供应商
                    supplierId: '',
                    // 单据列表
                    list: [],
                    // 品种数
                    kindCount: 0,
                    // 总金额
                    amount: 0,
                    // 验货数量
                    inspectNumSum: 0,
                    // 收货数量
                    receiveNumSum: 0,
                    // 验收人
                    inspector: {
                        id: '',
                        name: '',
                    },
                    // 验收日期
                    inspectTime: '',
                    relatedOrders: {
                        list: [],
                    },
                    orderFlag: 0,// 用于判断总部帮子店收货，可以操作子店的收货单
                },
                clinicEmployees: [],
                ReceiveActionType,
                CHECK_IN_SUPPLIER_ID,
                showDialog: this.value,
                pageLoading: false,
                tableLoading: false,
                submitBtnLoading: false,
                confirmBtnLoading: false,
                draftBtnLoading: false,
                showReceiveDialog: false,
                inspectType: ReceiveActionType.CONFIRM,
                searchKey: '',
                supplierOptions: [],
                showTakeDeliveryOrderDialog: false,
                validateCell: true,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'userInfo',
                'currentPharmacy',
                'supplierList',
                'stockEmployeeList',
                'traceCodeConfig',
                'isChainAdmin',
                'isStrictCountWithTraceCodeCollect',
            ]),
            currentStockEmployeeList() {
                if (this.canConfirm && this.canModifyOrder) {
                    return this.clinicEmployees || [];
                }
                return this.stockEmployeeList || [];
            },
            // 连锁总部 单子是帮子店建的标识
            canModifyOrder() {
                return this.isChainAdmin && !!this.order.orderFlag;
            },
            logs() {
                return this.order?.logs ?? [];
            },
            GoodsTypeEnum() {
                return GoodsTypeEnum;
            },
            isEnableTraceableCode() {
                return !!this.traceCodeConfig.goodsIn;
            },
            isAdd() {
                return !this.orderId || this.isDraft;
            },
            isDraft() {
                return this.order.status === PurchaseInspectOrderStatus.DRAFT;
            },
            canEdit() {
                return (this.isAdd || this.canConfirm);
            },
            title() {
                return '验收单';
            },
            receiveTitle() {
                return this.inspectType === ReceiveActionType.CONFIRM ? '确认验收' : '确认验收/入库';
            },
            statusName() {
                return PurchaseInspectOrderStatusName[this.order.status];
            },
            closeText() {
                return (this.pageLoading || [PurchaseInspectOrderStatus.INSPECT_PASS, PurchaseInspectOrderStatus.PART_PASS].includes(this.order?.status)) ? '关闭' : '取消';
            },
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 来源单据
            relatedOrder() {
                const { relatedOrders } = this.order;
                const [order] = getSourceOrder(relatedOrders?.list ?? [], RelatedOrderType.PURCHASE);
                return order || {};
            },
            relationshipOrderNo() {
                const {
                    sourceType,
                } = this.relatedOrder || {};
                if (!isNull(sourceType)) {
                    return getSourceOrderText(this.relatedOrder);
                }
                return '';

            },
            // 收货单
            receiveOrder() {
                const { relatedOrders } = this.order;
                const [order] = getSourceOrder(relatedOrders?.list ?? [], RelatedOrderType.RECEIPT);
                return order || {};
            },
            // 是否是总部配货
            isChainAdminDistribution() {
                return this.order?.deliveryOrderId;
            },
            inspectResult() {
                const len = this.order.list.length;
                const result = this.order.list.reduce((res, item) => {
                    const {
                        inspectPieceCount, inspectPackageCount, goods, receivePieceCount, receivePackageCount,
                    } = item;
                    const count = this.changeCount(inspectPieceCount, inspectPackageCount, goods, receivePieceCount, receivePackageCount);
                    if (!inspectPieceCount && !inspectPackageCount) {
                        res.unqualifiedCount += 1;
                    } else {
                        if (count) {
                            res.partQualifiedCount += 1;
                        } else {
                            res.qualifiedCount += 1;
                        }
                    }

                    return res;
                },{
                    qualifiedCount: 0,
                    unqualifiedCount: 0,
                    partQualifiedCount: 0,
                });

                if (len === result.qualifiedCount) {
                    return {
                        text: '合格',
                        color: this.$store.state.theme.style.G1,
                    };
                }
                if (len === result.unqualifiedCount) {
                    return {
                        text: '不合格',
                        color: this.$store.state.theme.style.R1,
                    };
                }
                return {
                    text: '部分合格',
                    color: this.$store.state.theme.style.R1,
                };
            },
            canExport() {
                return !this.isAdd;
            },

            canConfirm() {
                const {
                    status, clinicId,
                } = this.order;
                return (status === PurchaseInspectOrderStatus.WAIT_INSPECT) && ((clinicId === this.currentClinic.clinicId) || this.canModifyOrder);
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            key: 'shortId',
                            label: '商品编码',
                            slot: true,
                            style: {
                                width: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'displayName',
                            label: '商品名称',
                            slot: true,
                            style: {
                                flex: '1',
                                width: '160px',
                                minWidth: '160px',
                                maxWidth: '400px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'batchNo',
                            label: '生产批号',
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'productionDate',
                            label: '生产日期',
                            // colType: 'date',
                            style: {
                                width: '92px',
                                minWidth: '92px',
                                maxWidth: '92px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'expiryDate',
                            label: '有效日期',
                            // colType: 'date',
                            style: {
                                width: '92px',
                                minWidth: '92px',
                                maxWidth: '92px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'left',
                            },
                        },
                        {
                            key: 'receiveNumSum',
                            label: '收货数量',
                            slot: true,
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'inspectNumSum',
                            label: '合格数量',
                            slot: true,
                            style: {
                                width: '80px',
                                minWidth: '80px',
                                maxWidth: '80px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'unqualifiedNumSum',
                            label: '不合格数量',
                            slot: true,
                            style: {
                                width: '86px',
                                minWidth: '86px',
                                maxWidth: '86px',
                                paddingLeft: '',
                                paddingRight: '',
                                textAlign: 'right',
                            },
                        },
                        {
                            key: 'inspectComment',
                            label: '不合格原因',
                            slot: true,
                            style: {
                                width: '160px',
                                minWidth: '160px',
                                maxWidth: '160px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                        },
                        {
                            key: 'measure',
                            label: '处理措施',
                            slot: true,
                            style: {
                                width: '120px',
                                minWidth: '120px',
                                maxWidth: '120px',
                                paddingLeft: '',
                                paddingRight: '',
                            },
                        },
                        {
                            label: '不合格品追溯码',
                            key: 'traceableCode',
                            style: {
                                width: '120px',
                            },
                        },
                    ].filter((item) => {
                        if (item.key === 'traceableCode') {
                            return this.isEnableTraceableCode;
                        }
                        return true;
                    }),
                };
            },
            tagConfig() {
                const config = {
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                };
                config.theme = PurchaseInspectOrderStatusTagTheme[this.order.status];
                return config;
            },
            // table滚动后校验或聚焦输入框的回调时间，根据是否开启虚拟列表来决定大小
            timeout() {
                return this.order.list?.length >= 60 ? 1000 : 0;
            },
        },
        watch: {
            value(val) {
                this.showDialog = val;
            },
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        async created() {
            this.fetchSuppliers();
            try {
                if (this.orderId) {
                    this.pageLoading = true;
                    await this.fetchOrderDetail(this.orderId);
                    if (this.canConfirm && this.canModifyOrder) {
                        await this.fetchEmployeeByModuleId();
                    }
                }
            } catch (e) {
                console.error(e);
            } finally {
                this.pageLoading = false;
            }

        },
        methods: {
            formatDate,
            formatMoney,
            isChineseMedicine,
            complexCount,
            moneyDigit,
            unitEqual,
            async fetchEmployeeByModuleId() {
                try {
                    const { data } = await ClinicAPI.fetchEmployeeByModuleId({
                        queryClinicId: this.order.clinicId,
                    });
                    this.clinicEmployees = data || [];
                } catch (e) {
                    console.log(e);
                }
            },
            getUnitCount(item, goods) {
                const count = this.changeCount(item.inspectPieceCount, item.inspectPackageCount, item.goods, item.receivePieceCount, item.receivePackageCount);

                let unitCount = '', unit = '', packageCount = '', pieceCount = '';

                // 拆零
                if (Math.abs(count) < goods.pieceNum) {
                    unit = goods.pieceUnit;
                    unitCount = Math.abs(count);
                    pieceCount = unitCount;
                } else {
                    unit = goods.packageUnit;
                    unitCount = Math.ceil(Math.abs(count / goods.pieceNum));
                    packageCount = Math.floor(Math.abs(count / goods.pieceNum));
                    pieceCount = Math.abs(count % goods.pieceNum);
                }

                return {
                    useExternalCount: this.isStrictCountWithTraceCodeCollect,
                    packageCount,
                    pieceCount,
                    unitCount,
                    unit,
                    label: '不合格数量',
                    countLabel: '不合格数量',
                    maxCount: item._maxTraceCodeCount,
                    isTrans: item._isTransformable,
                };
            },
            async initCollectCodeCountList(list = []) {
                const resList = await TraceCode.getMaxTraceCountList({
                    scene: TraceCodeScenesEnum.INVENTORY,
                    dataList: list,
                    createKeyId: (item) => item.id,
                    getGoodsInfo: (item) => item.goods,
                    getUnitInfo: (item) => {
                        return this.getUnitCount(item, item.goods);
                    },
                });

                resList.forEach((e) => {
                    const item = list.find((i) => i.id === e.keyId);
                    if (item) {
                        this.$set(item, '_maxTraceCodeCount', e.traceableCodeNum);
                        this.$set(item, '_isTransformable', e.isTransformable);
                    }
                });
            },
            async handleChangeUnitCount(item, goods) {
                // 开启强校验时才实时计算
                if (this.isEnableTraceableCode && this.isStrictCountWithTraceCodeCollect && TraceCode.isSupportTraceCodeForceCheckStock()) {
                    await this.initCollectCodeCountList([item]);
                }

                // 对无码商品初始化追溯码
                if (this.isEnableTraceableCode && TraceCode.isSupplementNoCodeGoods(goods)) {
                    const traceableCodeList = TraceCode.mergeNoTraceCodeList({
                        ...item,
                        ...this.getUnitCount(item, goods),
                    });
                    this.$set(item, 'unqualifiedTraceableCodeList', traceableCodeList);
                }
            },
            disabledDate(date) {
                return date < new Date(this.order.receiveTime) || date > new Date();
            },
            showTransCount(row) {
                if (!row.goods || row.receiveUnit !== 'kg') {
                    return false;
                }
                return this.isChineseMedicine(row.goods) && !this.canEdit;
            },
            async fetchOrderDetail(orderId) {
                try {
                    const { data } = await GoodsAPIV3.getPurchaseInspectOrderDetail(orderId);
                    this.order = {
                        ...data,
                        list: data.list.map((item) => {
                            return {
                                ...item,
                                showPackageUnit: !!item.inspectPackageCount,
                                showPieceUnit: !!item.inspectPieceCount,
                            };
                        }),
                        inspectBy: data.inspectBy || data.inspector?.id || this.userInfo?.id,
                        inspectTime: data.inspectTime || formatDate(Date.now(), 'YYYY-MM-DD'),
                    };

                    this.handleReceiverChange(this.order.inspectBy);
                } catch (e) {
                    console.error(e);
                }
            },
            async confirmOrder(postData) {
                try {
                    await GoodsAPIV3.updatePurchaseInspectOrder(postData);
                } catch (e) {
                    console.error(e);
                }
            },
            async fetchSuppliers(key = '') {
                this.supplierOptions = this.supplierList.filter((item) => {
                    return item.name.includes(key.trim());
                });
            },
            handleDeleteTr(index) {
                // const item = this.order.list[index];

                this.order.list.splice(index, 1);
            },
            /**
             * @desc 根据用户输入的实际数量，计算盈亏
             * <AUTHOR>
             * @date 2018/11/24 13:36:25
             */
            changeCount(pieceCount, packageCount, goods, beforePieceCount, beforePackageCount) {
                if (pieceCount === '' && packageCount === '') return '';
                if (!goods) return '无法计算';

                const pieceNum = +goods.pieceNum || 1;
                const goodsPieceCount = +beforePieceCount || 0;
                const goodsPackageCount = +beforePackageCount || 0;

                pieceCount = +pieceCount || 0;
                packageCount = +packageCount || 0;
                const inputCount = Big(pieceCount).plus(Big(packageCount).times(pieceNum));
                const beforeCount = Big(goodsPieceCount).plus(Big(goodsPackageCount).times(pieceNum));

                return Big(inputCount).minus(beforeCount).toNumber();
            },

            /**
             * @desc 根据用计算盈亏结果 判断 + - 符号
             * <AUTHOR>
             * @date 2018/11/24 13:36:25
             */
            changeCountColor(item, goods) {
                const tempGoods = item.goods || goods;
                const change = this.changeCount(item.inspectPieceCount, item.inspectPackageCount, tempGoods, item.receivePieceCount, item.receivePackageCount);
                if (change > 0) {
                    return this.$store.state.theme.style.G1;
                }
                if (change < 0) {
                    return this.$store.state.theme.style.R1;
                }
                return this.$store.state.theme.style.T1;
            },
            /**
             * @desc 根据用计算盈亏结果 展示文案+样式
             * <AUTHOR>
             * @date 2018/11/24 13:45:23
             */
            changeCountText(item, goods, changeCount) {
                const tempGoods = item.goods || goods;
                let change = changeCount || this.changeCount(item.inspectPieceCount, item.inspectPackageCount, tempGoods, item.receivePieceCount, item.receivePackageCount);
                // console.log(change,'~~~~~change');
                if (change === '') return '';

                const sign = '';
                // if (change > 0) {
                //     sign = '+';
                // } else if (change < 0) {
                //     sign = '-';
                // }
                change = Math.abs(change);

                let pieceNum, pieceCount, packageCount;
                // 中药的数量都是保存在pieceCount中
                if (isChineseMedicine(tempGoods)) {
                    // 大单位数据清 0 防止重复拼接（如果packageCount有值，pieceCount无值的中药）
                    packageCount = 0;
                    pieceCount = change;
                } else {
                    pieceNum = +tempGoods.pieceNum || 1;
                    pieceCount = Big(change).mod(pieceNum);
                    packageCount = Math.floor(change / pieceNum);
                }
                return sign + complexCount({
                    pieceCount, packageCount, goods: tempGoods,
                });
            },
            async selectGoods(goods) {
                try {
                    const goodsId = goods.goodsId || goods.id;
                    // 重新获取药品信息
                    const { data: tempGoods } = await GoodsBaseAPI.goods(
                        goodsId,
                        this.clinicId,
                        {
                            forPurchase: 1, withStock: 1,
                        },
                    );

                    if (tempGoods) {
                        this.order.list.push({
                            ...tempGoods,
                            goods: tempGoods,
                        });
                    }
                } catch (e) {
                    console.error(e);
                }

            },
            createPostData() {
                const {
                    clinicId,
                    supplierId,
                    comment,
                    inspectBy,
                    inspectTime,
                    list,
                } = this.order;

                const obj = {
                    comment,
                    supplierId,
                    clinicId,
                    inspectBy,
                    inspectTime,
                    opType: ReceiveActionType.CONFIRM,
                    list: list.map((e) => {
                        return {
                            id: e.id,
                            goodsId: e.goodsId || e.goods?.id,
                            receivePackageCount: e.receivePackageCount,
                            receivePieceCount: e.receivePieceCount,
                            inspectPackageCount: e.inspectPackageCount,
                            inspectPieceCount: e.inspectPieceCount,
                            inspectComment: e.inspectComment,
                            measure: e.measure,
                            unqualifiedTraceableCodeList: e.unqualifiedTraceableCodeList,
                        };
                    }),
                };

                return obj;
            },
            async handleExport() {
                await GoodsAPIV3.exportInspectOrder(this.orderId);
            },
            handleSupplierChange(id) {
                this.order.supplier = this.supplierOptions.find((e) => e.id === id) || {};
            },
            handleSelectOrder(order) {
                console.log(order);
            },
            handleReceiverChange(employeeId) {
                const employee = this.stockEmployeeList.find((e) => e.employeeId === employeeId);
                if (employee) {
                    const {
                        employeeId: id, employeeName: name,
                    } = employee;
                    this.order.inspector = {
                        id,
                        name,
                    };
                }
            },
            async handleOpenMallOrderDetail() {
                if (this.order.mallOrderId) {
                    const mall = await this.$abcPlatform.module.mall;
                    mall.service.OrderDetailDialog({
                        orderId: this.order.mallOrderId,
                    });
                }
            },
            handleSubmitBefore(type) {
                this.validateCell = false;
                // 有不合格数量的情况下才需要校验追溯码
                const hasUnqualified = this.order.list.some((item) => {
                    return this.changeCount(item.inspectPieceCount, item.inspectPackageCount, item.goods, item.receivePieceCount, item.receivePackageCount);
                });

                if (this.isEnableTraceableCode && this.isStrictCountWithTraceCodeCollect && hasUnqualified) {
                    this.confirmBtnLoading = true;

                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this.$refs.form.validate(async (val) => {
                            if (val) {
                                const {
                                    flag, errorList, firstErrorIndex,
                                } = await TraceCode.validate({
                                    scene: TraceCodeScenesEnum.INVENTORY,
                                    dataList: this.order.list,
                                    createKeyId: (item) => item.id,
                                    getGoodsInfo: (item) => item.goods,
                                    getUnitInfo: (item) => {
                                        return this.getUnitCount(item, item.goods);
                                    },
                                    traceableCodeListKey: 'unqualifiedTraceableCodeList',
                                    needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckStock(),
                                });
                                this.confirmBtnLoading = false;

                                if (!flag) {
                                    this.$confirm({
                                        type: 'warn',
                                        title: '追溯码采集风险提醒',
                                        content: errorList.map((it) => {
                                            const {
                                                count,
                                                warnTips,
                                            } = it;
                                            return `有 ${count} 个商品${warnTips}`;
                                        }),
                                        confirmText: '去修改',
                                        cancelText: '仍要提交',
                                        disabledKeyboard: true,
                                        showClose: false,
                                        onConfirm: () => {
                                            this.validateCell = true;
                                            this.confirmBtnLoading = false;

                                            this.$refs.tableRef.scrollToElement({
                                                index: firstErrorIndex,
                                                top: 47,
                                                time: 60,
                                                behavior: 'instant',
                                            });

                                            // eslint-disable-next-line abc/no-timer-id
                                            setTimeout(() => {
                                                this.$refs.form.validate();
                                            }, this.timeout);
                                        },
                                        onCancel: () => {
                                            this.confirmBtnLoading = false;
                                            this.showReceiveDialog = true;
                                            this.inspectType = type;
                                        },
                                    });
                                    return;
                                }

                                this.showReceiveDialog = true;
                                this.inspectType = type;

                            } else {
                                this.confirmBtnLoading = false;
                            }
                        });
                    }, 0);
                } else {
                    this.showReceiveDialog = true;
                    this.inspectType = type;
                }
            },
            /**
             * 确认验收单
             * @param status {number} 0:保存草稿 1:确定验收 2:一键验收/入库
             */
            async handleSubmit(status = ReceiveActionType.DRAFT) {
                try {
                    if (status === ReceiveActionType.DRAFT) this.draftBtnLoading = true;
                    if (status === ReceiveActionType.CONFIRM) this.confirmBtnLoading = true;
                    if (status === ReceiveActionType.FINISH) this.submitBtnLoading = true;

                    const postData = this.createPostData({ status });

                    await GoodsAPIV3.confirmPurchaseInspectOrder(this.orderId, postData);

                    if (this.isAdd) {
                        this.$Toast({
                            message: '提交成功',
                            type: 'success',
                        });
                    }
                    this.showDialog = false;
                    this.$emit('refresh', this.isAdd ? 1 : 2);
                } catch (e) {
                    console.error(e);
                    // 防止重复提示
                    if (!e.alerted) {

                        this.$Toast({
                            message: e.message,
                            type: 'error',
                        });
                    }
                } finally {
                    this.submitBtnLoading = false;
                    this.confirmBtnLoading = false;
                    this.draftBtnLoading = false;
                }
            },
            handleCancel() {
                this.showDialog = false;
                this.$emit('close');
            },
        },
    };
</script>
