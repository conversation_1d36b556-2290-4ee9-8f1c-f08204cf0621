<template>
    <abc-form
        ref="confirmTableForm"
        is-excel
        item-no-margin
        style="height: 100%;"
    >
        <abc-layout preset="dialog-table">
            <abc-layout-header>
                <abc-descriptions
                    :column="3"
                    :label-width="116"
                    size="large"
                    grid
                    background
                    stretch-last-item
                >
                    <abc-descriptions-item label="调价商品数量">
                        <span class="text">{{ tableData.length }}</span>
                    </abc-descriptions-item>
                    <abc-descriptions-item
                        v-if="isChainAdmin"
                        label="调价门店"
                        :content-style="{ padding: '0px' }"
                        content-padding="0"
                    >
                        <abc-form-item :title="clinicName">
                            <abc-input v-model="clinicName" :disabled="true" size="medium"></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item
                        v-if="showAdjustmentTimeAndReason"
                        label="调价生效时间"
                        :content-style="{ padding: status !== 3 ? '0px' : '' }"
                        content-padding="0"
                    >
                        <template v-if="status !== 3">
                            <abc-form-item v-if="effectedType === 0">
                                <abc-select
                                    v-model="effectedType"
                                    inner-width="200"
                                    :disabled="disabled"
                                    size="medium"
                                >
                                    <abc-option :value="0" label="立即生效"></abc-option>
                                    <abc-option :value="1" label="定时生效"></abc-option>
                                </abc-select>
                            </abc-form-item>
                            <abc-space v-if="effectedType === 1" is-compact>
                                <abc-form-item>
                                    <abc-select
                                        v-model="effectedType"
                                        :disabled="disabled"
                                        inner-width="200"
                                        size="medium"
                                    >
                                        <abc-option :value="0" label="立即生效"></abc-option>
                                        <abc-option :value="1" label="定时生效"></abc-option>
                                    </abc-select>
                                </abc-form-item>
                                <abc-form-item v-if="effectedType === 1">
                                    <abc-date-picker
                                        v-model="currentEffected"
                                        size="large"
                                        :disabled="disabled"
                                        :picker-options="pickerOptions"
                                        :placeholder="disabled ? '' : '选择日期'"
                                        clearable
                                    >
                                    </abc-date-picker>
                                </abc-form-item>
                            </abc-space>
                        </template>
                        <template v-else>
                            <span class="text" :title="formatDate(currentEffected,'YYYY-MM-DD HH:mm:ss')">{{
                                formatDate(currentEffected,'YYYY-MM-DD HH:mm:ss')
                            }}</span>
                        </template>
                    </abc-descriptions-item>
                    <abc-descriptions-item label="调价人">
                        <span class="text">{{ createName || userInfo.name }}</span>
                    </abc-descriptions-item>
                    <abc-descriptions-item
                        v-if="showAdjustmentTimeAndReason"
                        label="调价原因"
                        :content-style="{ padding: '0px' }"
                        content-padding="0"
                    >
                        <abc-form-item>
                            <abc-input v-model="currentComment" :disabled="disabled" size="large"></abc-input>
                        </abc-form-item>
                    </abc-descriptions-item>
                    <abc-descriptions-item v-if="!showAdjustmentTimeAndReason" label="备注">
                        <span class="text">来自商品档案快捷调价</span>
                    </abc-descriptions-item>
                </abc-descriptions>
            </abc-layout-header>

            <abc-layout-content @layout-mounted="handleMounted">
                <abc-table
                    ref="confirmTable"
                    type="excel"
                    class="price-adjustment-confirm-table"
                    :render-config="confirmTableRenderConfig"
                    :data-list="tableDataCurPage"
                    empty-size="small"
                    :loading="loading"
                    cell-size="large"
                    :custom-tr-key="customTrKey"
                    child-key="children"
                    :support-delete-tr="supportDeleteTr"
                    :pagination="handleTablePagination"
                    @sortChange="handleConfirmTableSortChange"
                    @pageChange="handleConfirmTablePageChange"
                    @delete-tr="handleConfirmTableDelete"
                >
                    <template
                        #shortId="{
                            trData: item, parentData, cellRowSpan
                        }"
                    >
                        <abc-table-cell
                            v-if="!parentData"
                            class="ellipsis"
                            :cell-row-span="cellRowSpan"
                        >
                            <overflow-tooltip v-if="item.goods?.shortId" :content="item.goods?.shortId" style="height: 48px; line-height: 48px;"></overflow-tooltip>
                        </abc-table-cell>
                        <span v-else></span>
                    </template>

                    <template
                        #displayName="{
                            trData: item, parentData, cellRowSpan
                        }"
                    >
                        <display-name-cell
                            v-if="!parentData"
                            :goods="item.goods"
                            :hover-config="{
                                openDelay: 500,
                                showPrice: true,
                                showShebaoCode: true,
                            }"
                            :cell-config="{
                                'cell-row-span': cellRowSpan,
                            }"
                            :content-style="{
                                width: '100%',
                                height: '48px',
                                'justify-content': 'center',
                            }"
                        >
                        </display-name-cell>
                        <span v-else></span>
                    </template>

                    <template
                        #lastPackageCostPrice="{
                            trData: item, parentData, cellRowSpan
                        }"
                    >
                        <abc-table-cell
                            v-if="!parentData"
                            class="ellipsis"
                            :cell-row-span="cellRowSpan"
                        >
                            <span>{{ formatDecimal(item.goods?.lastPackageCostPrice || item?.packageCostPrice) }}</span>
                        </abc-table-cell>
                        <span v-else></span>
                    </template>

                    <template
                        #avgPackageCostPrice="{
                            trData: item, parentData, cellRowSpan
                        }"
                    >
                        <abc-table-cell
                            v-if="!parentData"
                            class="ellipsis"
                            :cell-row-span="cellRowSpan"
                        >
                            <span>{{ formatDecimal(item.goods?.avgPackageCostPrice || item?.avgPackageCostPrice) }}</span>
                        </abc-table-cell>
                        <span v-else></span>
                    </template>
                </abc-table>
            </abc-layout-content>
        </abc-layout>
    </abc-form>
</template>

<script>
    import { defineComponent } from 'vue';
    import ConfirmTableConfig
        from '@/views-pharmacy/inventory/frames/price-adjustment/table-config/table-confirm-price-adjustment';
    import QuickConfirmTableConfig from '@/views-pharmacy/inventory/frames/price-adjustment/table-config/quick-table-confirm-price-adjustment';
    import {
        PriceType, RoundingMode,
    } from 'views/common/inventory/constants';
    import { mapGetters } from 'vuex';
    import { OpType } from '@/views-pharmacy/inventory/constant';
    import GoodsV3API from 'api/goods/index-v3';
    import {
        getSafeNumber, isNotNull, isNull,
        isSupportShebaoPay,
        moneyDigit,
        paddingMoney,
    } from '@/utils';
    import { formatDate } from '@abc/utils-date';
    import {
        isChineseMedicine,
    } from '@/filters';
    import DisplayNameCell from '@/views-pharmacy/components/display-name-cell.vue';
    import OverflowTooltip from '@/components/overflow-tooltip.vue';
    import {
        DiscountTypes, TargetType,
    } from 'views/inventory/goods/archives/constant';
    import Big from 'big.js';
    import BusinessGoods from '@/views-pharmacy/inventory/core/goods';
    import useMemberPrice from 'views/inventory/hooks/useMemberPrice';
    export default defineComponent({
        name: 'ResultFormTable',
        components: {
            OverflowTooltip,
            DisplayNameCell,
        },
        props: {
            clinicName: {
                type: String,
                default: '',
            },
            formData: {
                type: Object,
                default: () => {},
            },
            tableData: {
                type: Array,
                default: () => [],
            },
            loading: {
                type: Boolean,
                default: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            createName: {
                type: String,
                default: '',
            },
            supportDeleteTr: {
                type: Boolean,
                default: false,
            },
            sourceType: {
                type: Number,
                default: 1,
            },
            status: {
                type: Number,
                default: -1,
            },
            // 用于判断是否需要审核
            gspInstId: {
                type: String,
                default: '',
            },
            needValidate: {
                type: Boolean,
                default: false,
            },
            allowValidate: {
                type: Boolean,
                default: false,
            },
            isDraft: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                getDiscountValue,
                getDiscountValueView,
            } = useMemberPrice();

            return {
                getDiscountValue,
                getDiscountValueView,
            };
        },
        data() {
            const pickerOptions = {
                disabledDate(date) {
                    return date < new Date();
                },
            };
            return {
                pickerOptions,
                tablePagination: {
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'isChainAdmin',
                'isChainSubStore',
                'goodsConfig',
                'isNeedCheckSellPriceNotZero',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            fractionDigits() {
                return this.viewDistributeConfig.Inventory.fractionDigits;
            },
            needValidateTable() {
                return (this.needValidate || this.isChainSubStore) && this.allowValidate;
            },
            maxPricePercent() { //总部设置上限
                return this.goodsConfig?.subClinicPrice?.maxPricePercent || 0;
            },
            minPricePercent() { //总部设置下限
                return this.goodsConfig?.subClinicPrice?.minPricePercent || 0;
            },
            handleTablePagination() {
                return {
                    showTotalPage: true,
                    pageIndex: this.tablePagination.pageIndex,
                    pageSize: this.tablePagination.pageSize,
                    count: this.tablePagination.count,
                };
            },
            showAdjustmentTimeAndReason() {
                // 1 草稿单 2 来自批量调价 3 来自商品档案调价并需要审核
                return this.isDraft || this.sourceType === 1 || (this.sourceType === 0 && !!this.gspInstId);
            },
            currentComment: {
                get() {
                    return this.formData.comment || '';
                },
                set(val) {
                    const formData = {
                        ...this.formData,
                        comment: val,
                    };
                    this.$emit('update:formData', formData);
                },
            },
            currentEffected: {
                get() {
                    return this.formData.effected || '';
                },
                set(val) {
                    const formData = {
                        ...this.formData,
                        effected: val,
                    };
                    this.$emit('update:formData', formData);
                },
            },
            effectedType: {
                get() {
                    return this.formData.effectedType || 0;
                },
                set(val) {
                    const formData = {
                        ...this.formData,
                        effectedType: val,
                    };
                    this.$emit('update:formData', formData);
                },
            },
            selectedClinicName() {
                if (!this.isChainAdmin) {
                    return '';
                }
                const [type, clinic] = this.selectedClinics;
                if (type.value === 1) {
                    return clinic.label;
                }
                return type.label;
            },
            confirmTableRenderConfig() {
                return this.getConfirmRenderConfig();
            },
            tableDataCurPage() {
                const {
                    pageIndex, pageSize,
                } = this.tablePagination;
                return this.tableData.slice(pageSize * pageIndex, pageSize * (pageIndex + 1));
            },
            sortList(list, orderBy, orderType, isCn) {
                if (isCn) {
                    return list.sort((a, b) => {
                        if (orderType === 'asc') {
                            return a[orderBy].localeCompare(b[orderBy]);
                        }
                        return b[orderBy].localeCompare(a[orderBy]);
                    });
                }
                return list.sort((a, b) => {
                    if (orderType === 'asc') {
                        return a[orderBy] - b[orderBy];
                    }
                    return b[orderBy] - a[orderBy];
                });
            },
        },
        watch: {
            tableData: {
                handler(val) {
                    this.tablePagination.count = val?.reduce((count, item) => count + (item.children?.length || 0) + 1, 0) || 0;
                },
                deep: true,
                immediate: true,
            },
        },
        methods: {
            formatDate,
            getPriceConfig(goodsInfo) {
                return new BusinessGoods(goodsInfo).getPriceInputConfig(this.fractionDigits, { supportZero: false });
            },
            handleMounted(data) {
                console.log('handleMounted', data);
                this.tablePagination.pageSize = data.paginationLimit;
            },
            handleErrorList(errorList) {
                errorList.map((item) => {
                    let afterPackagePriceErrorInfo = '';
                    let afterPiecePriceErrorInfo = '';
                    const errStr = item.msgs?.filter((i) => {
                        return i.indexOf('零售') === -1 && i.indexOf('拆零') === -1;
                    })?.join(',');
                    // 只管拆零和零售的提示 范围和为空校验前端会做
                    afterPackagePriceErrorInfo = item.msgs?.find((i) => {
                        return i.indexOf('零售') !== -1;
                    }) || errStr || '';
                    afterPiecePriceErrorInfo = item.msgs?.find((i) => {
                        return i.indexOf('拆零') !== -1;
                    }) || errStr || '';
                    this.tableData[item.rowIndex].afterPackagePriceErrorInfo = afterPackagePriceErrorInfo;
                    this.tableData[item.rowIndex].afterPiecePriceErrorInfo = afterPiecePriceErrorInfo;
                    return {
                        ...item,
                    };
                });
                const index = errorList[0].rowIndex;
                let pageIndex = Math.ceil(index / this.tablePagination.pageSize);
                if (pageIndex <= 0) {
                    pageIndex = 0;
                } else {
                    pageIndex = pageIndex - 1;
                }
                this.tablePagination.pageIndex = pageIndex;
            },
            customTrKey(item) {
                return item.keyId || `result_${item.goods.id}`;
            },
            // form表单校验
            isPackagePriceErr(item) {
                if (item.afterPackagePrice !== 0 && !item.afterPackagePrice) { //不存在则错误
                    return '请输入调整后新售价';
                }
                const { goods } = item;
                // 总部未定价不提示
                if (this.needValidateTable && (isNull(goods.chainPackagePrice) || +goods.chainPackagePrice === 0)) {
                    return '';
                }
                const maxCount = Number(moneyDigit((Number(goods.chainPackagePrice) * this.maxPricePercent) / 100, 5));
                const minCount = Number(moneyDigit((Number(goods.chainPackagePrice) * this.minPricePercent) / 100, 5));
                if (this.needValidateTable && (Number(item.afterPackagePrice) < minCount || Number(item.afterPackagePrice) > maxCount)) {
                    return `总部允许的定价范围为${paddingMoney(minCount)} - ${paddingMoney(maxCount)}`;
                }
                return item.afterPackagePriceErrorInfo || '';
            },
            // form表单校验
            isPriceErr(item) {
                // 中药饮片和禁止调价的商品不需要校验
                if (isChineseMedicine(item.goods) || !item.goods.dismounting) {
                    return '';
                }
                if (item.afterPiecePrice !== 0 && !item.afterPiecePrice) { //不存在则错误
                    return '请输入调整后新售价(零)';
                }
                const { goods } = item;
                // 总部未定价不提示
                if (this.needValidateTable && (isNull(goods.chainPiecePrice) || +goods.chainPiecePrice === 0)) {
                    return '';
                }
                const maxCount = Number(moneyDigit((Number(goods.chainPiecePrice) * this.maxPricePercent) / 100, 5));
                const minCount = Number(moneyDigit((Number(goods.chainPiecePrice) * this.minPricePercent) / 100, 5));
                if (this.needValidateTable && (Number(item.afterPiecePrice) < minCount || Number(item.afterPiecePrice) > maxCount)) {
                    return `总部允许的定价范围为${paddingMoney(minCount)} - ${paddingMoney(maxCount)}`;
                }
                return item.afterPiecePriceErrorInfo || '';
            },
            formatDecimal(number) {
                // 检查是否为数字
                if (typeof number !== 'number') {
                    return '';
                }

                // 将数字转换成字符串
                const numStr = number.toString();

                // 检查是否包含小数点
                if (numStr.indexOf('.') === -1) {
                    // 没有小数点，则补充 '.00'
                    return `${numStr}.00`;
                }
                // 如果小数位数不足两位则在末尾补 '0'
                const decimalPartLength = numStr.split('.')[1].length;
                if (decimalPartLength < 2) {
                    return numStr.padEnd(numStr.length + 2 - decimalPartLength, '0');
                }
                return numStr;

            },
            async calculatePrice(ItemList = [], {
                opType,
                upPercent,
                profitRat,
                scaleType,
                roundingMode,
            }) {
                try {
                    const params = {
                        /**
                         * @type CalculatePriceItem[]
                         */
                        list: [],
                    };
                    ItemList.forEach((item) => {
                        const constParams = {
                            goodsId: item.id,
                            pieceNum: item.pieceNum,
                            piecePrice: item.piecePrice,
                            packagePrice: item.packagePrice,
                            packageCostPrice: item.packageCostPrice || item.lastPackageCostPrice, // 最近进价
                            avgPackageCostPrice: item.avgPackageCostPrice,
                            shebaoLimitPackagePrice: item.shebaoLimitPackagePrice,
                            roundingMode,
                            scaleType,
                            calPackageType: opType,
                            calPackageUpPercent: OpType.PROFIT === opType ? Number(profitRat) : upPercent,
                            calPieceType: opType,
                            calPieceUpPercent: OpType.PROFIT === opType ? Number(profitRat) : upPercent,
                        };


                        params.list.push(constParams);
                    });
                    const res = await GoodsV3API.calculatePrice(params);
                    return res;
                } catch (e) {
                    console.error(e);
                }
            },
            getConfirmRenderConfig() {
                let ConfirmTableConfigHandle = ConfirmTableConfig;
                // 快捷调价
                if (!this.showAdjustmentTimeAndReason) {
                    ConfirmTableConfigHandle = QuickConfirmTableConfig;
                }
                return ConfirmTableConfigHandle.extendConfig({
                    'memberTypeName': {
                        dataFormatter: (_, row) => {
                            if (!row.memberTypeName) {
                                return '无(零售价)';
                            }
                            return row.memberTypeName;
                        },
                    },
                    'effected': {
                        dataFormatter: (_, row) => {
                            if (!row.effected) {
                                return '';
                            }
                            return formatDate(row.effected,'YYYY-MM-DD HH:mm:ss');
                        },
                    },
                    'beforePriceType': {
                        dataFormatter: (_, row) => {
                            let text = '-';
                            if (row.beforePriceType === PriceType.PRICE) {
                                text = '固定售价';
                            } else if (row.beforePriceType === PriceType.PKG_PRICE_MAKEUP) {
                                text = '进价加成';
                            }
                            return text;
                        },
                    },
                    'afterPriceType': {
                        dataFormatter: (_, row) => {
                            let text = '-';
                            if (row.afterPriceType === PriceType.PRICE) {
                                text = '固定售价';
                            } else if (row.afterPriceType === PriceType.PKG_PRICE_MAKEUP) {
                                text = '进价加成';
                            }
                            return text;
                        },
                    },
                    'profitRat': {
                        dataFormatter: (_, row) => {
                            return `${row.goods.profitRat}%`;
                        },
                    },
                    'beforeProfitRat': {
                        dataFormatter: (_, row) => {
                            return row.beforeProfitRat !== undefined && row.beforeProfitRat !== '' ? `${row.beforeProfitRat}%` : '';
                        },
                    },
                    'afterProfitRat': {
                        dataFormatter: (_, row) => {
                            return row.afterProfitRat !== undefined && row.afterProfitRat !== '' ? `${row.afterProfitRat}%` : '';
                        },
                    },
                    'beforePackagePrice': {
                        customRender: (h, row) => {
                            if (row.memberTypeId) {
                                if (row.beforeDiscountType === DiscountTypes.discount) {
                                    return (
                                        <abc-table-cell>
                                            <span style="display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2;white-space: normal;word-break: break-all;"
                                                title={`售价${this.getDiscountValueView(row.beforeDiscountValue)}折(折后${this.$t('currencySymbol')}${paddingMoney(row.beforePackagePrice)})`}
                                            >
                                                {`售价${this.getDiscountValueView(row.beforeDiscountValue)}折(折后${this.$t('currencySymbol')}${paddingMoney(row.beforePackagePrice)})`}
                                            </span>
                                        </abc-table-cell>
                                    );
                                }
                            }
                            return (<abc-table-cell title={paddingMoney(row.beforePackagePrice)}>{paddingMoney(row.beforePackagePrice)}</abc-table-cell>);
                        },
                    },
                    'beforePiecePrice': {
                        customRender: (h, row) => {
                            if (row.memberTypeId) {
                                return (
                                    <abc-table-cell>
                                        <span>不允许拆零</span>
                                    </abc-table-cell>
                                );
                            }

                            if (!row.goods.dismounting) {
                                return (
                                    <abc-table-cell>
                                        <span>{!row.goods.dismounting ? '不允许拆零' : ''}</span>
                                    </abc-table-cell>
                                );
                            }
                            return (<abc-table-cell title={paddingMoney(row.beforePiecePrice)}>{paddingMoney(row.beforePiecePrice)}</abc-table-cell>);
                        },
                    },
                    'afterPackagePrice': {
                        customRender: (h, row) => {
                            const inputConfig = this.getPriceConfig(row.goods);

                            // 会员价相关不改变后端返回的类型
                            const packageOpType = row.memberTypeId ? row.packageOpType : OpType.MANUAL;

                            const onChange = async (value) => {
                                row.afterPackagePrice = value;
                                const modifyPriceParams = {
                                    opType: packageOpType,
                                    // 区分是上调还是下调
                                    upPercent: 0,
                                    profitRat: 0,
                                    scaleType: 1,
                                    roundingMode: 1,
                                };
                                const result = await this.calculatePrice([{
                                    ...row.goods,
                                    packagePrice: value,
                                }], modifyPriceParams);
                                row.afterProfitRat = result?.list?.[0]?.newProfitRat || '';

                                if (row.afterPackagePrice) {
                                    row.afterPackagePrice = moneyDigit(row.afterPackagePrice, inputConfig.formatLength);
                                }
                                if (isChineseMedicine(row.goods)) {
                                    row.afterPiecePrice = row.afterPackagePrice;
                                }

                                if (row.afterProfitRat) {
                                    row.afterProfitRat = moneyDigit(row.afterProfitRat, inputConfig.formatLength);
                                }
                                row.packagePriceState = {
                                    opType: packageOpType,
                                };
                                row.afterPackagePriceErrorInfo = '';
                            };

                            const appendInnerText = () => {
                                if (row.afterPackagePrice) {
                                    return `折(约${this.$t('currencySymbol')}${moneyDigit(row.afterPackagePrice, inputConfig.formatLength)})`;
                                }
                                return '折';
                            };

                            const handleDiscountValueInput = (v, row) => {
                                row.afterDiscountValueView = v;

                                if (row.afterDiscountValueView) {
                                    row.afterDiscountValue = this.getDiscountValue(row.afterDiscountValueView);
                                    row.afterPackagePrice = this.calcDiscountPrice(row);
                                    if (isChineseMedicine(row.goods)) {
                                        row.afterPiecePrice = row.afterPackagePrice;
                                    }
                                }
                            };

                            if (row.memberTypeId) {
                                if (row.afterDiscountType === DiscountTypes.discount) {
                                    const directives = [
                                        {
                                            name: 'abc-focus-selected',
                                        },
                                    ];
                                    return (
                                        <abc-form-item required>
                                            <abc-input
                                                value={row.afterDiscountValueView}
                                                adaptive-width
                                                type="number"
                                                config={{
                                                    formatLength: 2,
                                                    max: 9.99,
                                                }}
                                                input-custom-style={{
                                                    textAlign: 'center',
                                                }}
                                                disabled={this.disabled}
                                                data-cy="member-price-discount-input"
                                                { ...{ directives } }
                                                onInput={(v) => handleDiscountValueInput(v, row)}
                                                onChange={() => onChange(row.afterPackagePrice)}
                                            >
                                                <span
                                                    slot="appendInner"
                                                    class="ellipsis"
                                                    style="width:80px;color: var(--abc-color-T3);"
                                                    title={appendInnerText(row)}
                                                >{ appendInnerText(row) }</span>
                                            </abc-input>
                                        </abc-form-item>
                                    );
                                }
                            }
                            const validatePackageUnitPrice = (value, callback) => {
                                if (isNotNull(value) && +value === 0 && this.isNeedCheckSellPriceNotZero && isSupportShebaoPay(row.goods, {
                                    getShebaoInfo: (goodsInfo) => {
                                        return goodsInfo.shebaoNationalView;
                                    },
                                    getShebaoCode: (shebao) => {
                                        return shebao.shebaoCode;
                                    },
                                })) {
                                    callback({
                                        validate: false,
                                        message: '医保要求售价必填且不能为 0',
                                    });
                                    return;
                                }
                                callback({
                                    validate: true,
                                });
                            };
                            return (
                                  <abc-form-item validateEvent={validatePackageUnitPrice}>
                                      <abc-tooltip placement="top-start" content={this.isPackagePriceErr(row)} openDelay={200} customPopperClass="price-adjustment-confirm-table_custom--price" disabled={!this.isPackagePriceErr(row)}>
                                       <abc-input
                                        value={row.afterPackagePrice}
                                        config={inputConfig}
                                        disabled={this.disabled}
                                        onChange={onChange}
                                       class={this.isPackagePriceErr(row) ? 'price_input_error' : ''}
                                       type="money"
                                    />
                                      </abc-tooltip>
                                </abc-form-item>
                            );
                        },
                    },
                    'afterPiecePrice': {
                        customRender: (h, row) => {
                            const inputConfig = this.getPriceConfig(row.goods);

                            const disabled = isChineseMedicine(row.goods) || !row.goods.dismounting;

                            if (row.memberTypeId) {
                                return (
                                    <abc-table-cell>
                                        <span>不允许拆零</span>
                                    </abc-table-cell>
                                );
                            }

                            if (disabled) {
                                return (<abc-table-cell>{!row.goods.dismounting ? '不允许拆零' : ''}</abc-table-cell>);
                            }
                            const validatePieceUnitPrice = (value, callback) => {
                                if (isNotNull(value) && +value === 0 && this.isNeedCheckSellPriceNotZero && isSupportShebaoPay(row.goods, {
                                    getShebaoInfo: (goodsInfo) => {
                                        return goodsInfo.shebaoNationalView;
                                    },
                                    getShebaoCode: (shebao) => {
                                        return shebao.shebaoCode;
                                    },
                                })) {
                                    callback({
                                        validate: false,
                                        message: '医保要求售价必填且不能为 0',
                                    });
                                    return;
                                }
                                callback({
                                    validate: true,
                                });
                            };
                            return (
                                <abc-form-item validateEvent={validatePieceUnitPrice}>
                                    <abc-tooltip placement="top-start" content={this.isPriceErr(row)} openDelay={200} customPopperClass="price-adjustment-confirm-table_custom--price" disabled={!this.isPriceErr(row)}>
                                  <abc-input
                                      value={row.afterPiecePrice}
                                      config={inputConfig}
                                      disabled={this.disabled}
                                      onChange={(value) => {
                                        row.afterPiecePrice = value;
                                        if (row.afterPiecePrice) {
                                            row.afterPiecePrice = moneyDigit(row.afterPiecePrice, inputConfig.formatLength);
                                        }
                                        row.piecePriceState = {
                                          opType: OpType.MANUAL,
                                        };
                                        row.afterPiecePriceErrorInfo = '';
                                      }}
                                      class={this.isPriceErr(row) ? 'price_input_error' : ''}
                                      type="money"
                                  />
                                    </abc-tooltip>
                                </abc-form-item>
                            );
                        },
                    },
                });
            },
            handleConfirmTableSortChange({
                orderBy, orderType,
            }) {
                if (orderBy) {
                    const isCn = orderBy === 'displayName';
                    this.sortList(this.tableData, orderBy, orderType, isCn);
                }
            },
            handleConfirmTablePageChange(pageIndex) {
                this.tablePagination.pageIndex = pageIndex - 1;
            },
            handleConfirmTableDelete(index, childIndex) {
                const {
                    pageIndex = 0, pageSize,
                } = this.tablePagination;
                const number = pageIndex * pageSize;

                if (isNotNull(index) && isNotNull(childIndex)) {
                    const children = this.tableData[number + index]?.children ?? [];
                    children.splice(childIndex, 1);
                } else {
                    const currentItem = this.tableData[number + index];
                    if (currentItem.children && currentItem.children.length > 0) {
                        // 如果有子项，将当前项的数据合并到第一个子项
                        const firstChild = currentItem.children[0];
                        // 将第一个子项提升到父级位置
                        this.tableData.splice(number + index, 1, {
                            ...currentItem,
                            ...firstChild,
                            children: currentItem.children.slice(1),
                        });
                    } else {
                        // 如果没有子项，直接删除
                        this.tableData.splice(number + index, 1);
                    }
                }
                this.batchCalcDiscountPrice();
                this.tablePagination.count = this.tableData.reduce((count, item) => count + (item.children?.length || 0) + 1, 0);
            },
            calcDiscountPrice(row) {
                // 找到当前行父级数据
                const item = this.tableData.find((item) => item.itemId === row.itemId);
                // 获取零售价格，如果不是零售价，就取 goods上面的价格
                const retailPrice = item?.targetType === TargetType.normal ? item.afterPackagePrice : (row.goods.packagePrice || row.goods.packagePrice);
                const inputConfig = this.getPriceConfig(row.goods);

                return Big(getSafeNumber(retailPrice)).times(row.afterDiscountValue).toFixed(inputConfig.formatLength, RoundingMode.ROUND_UP);
            },
            batchCalcDiscountPrice() {
                this.tableData.forEach((item) => {
                    if (item.afterDiscountType === DiscountTypes.discount) {
                        item.afterPackagePrice = this.calcDiscountPrice(item);
                        if (isChineseMedicine(item.goods)) {
                            item.afterPiecePrice = item.afterPackagePrice;
                        }
                    }
                });
            },
        },
    });
</script>

<style lang="scss">
.price-adjustment-confirm-table {
    .abc-table-body {
        .price_input_error {
            .abc-input__inner {
                background-color: #fef7e9 !important;
                border: 1px solid #ff9933 !important;
            }
        }
    }
}

.price-adjustment-confirm-table_custom--price {
    color: #ffffff;
    background-color: #ff9933;
    border: 1px solid #ff9933;

    .popper__arrow::after {
        border-top-color: #ff9933 !important;
    }
}
</style>
