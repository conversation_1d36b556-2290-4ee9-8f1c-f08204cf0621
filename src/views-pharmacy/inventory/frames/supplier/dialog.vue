<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        class="pharmacy-supplier-dialog"
        :title="dialogTitle"
        :is-scroll="false"
        :auto-focus="false"
        responsive
        size="hugely"
        :show-close="true"
        append-to-body
    >
        <template slot="title">
            <abc-layout>
                <abc-space>
                    <abc-title level="1">
                        {{ dialogTitle }}
                    </abc-title>
                    <abc-tag-v2 v-if="showTag" v-bind="tagProps">
                        {{ tagName }}
                    </abc-tag-v2>
                </abc-space>
            </abc-layout>
        </template>
        <abc-form ref="supplierForm" v-abc-loading.coverOpaque="loading" class="supplier-archives-wrapper">
            <!--复用挂号预约选模式-->
            <abc-flex v-if="isChain" :gap="24" style="margin-bottom: 16px;">
                <abc-option-card
                    v-for="(o) in options.reservationModeOpts"
                    :key="o.value"
                    :value="supplier.isEntrustDelivery === o.value"
                    :title="o.title"
                    selectable="icon-inside"
                    :width="270"
                    :show-icon="false"
                    theme="success"
                    :description-config="{
                        theme: 'gray',
                    }"
                    :description="o.intro"
                    style="overflow: hidden;"
                    @change="handleModeCardClick(o.value)"
                >
                </abc-option-card>
            </abc-flex>
            <section class="group-item">
                <h3>基本信息</h3>
                <abc-row :gutter="[24, 16]" :wrap="'wrap'" class="supplier-archives-info">
                    <abc-col :span="6">
                        <abc-form-item
                            label="供应商名称"
                            required
                            :validate-event="checkRepeatSupplier"
                            :help="getHelpText('name', null, '供应商名称')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model.trim="supplier.name"
                                :width="270"
                                max-length="30"
                                :disabled="disabled || disabledInfo"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="6">
                        <abc-form-item
                            label="类型"
                            required
                            :help="getHelpText('companyType', (v)=>{
                                const label = options.type.find(item=>item.value === v)?.label
                                return isNull(label) ? '审批中：删除“类型”' : `审批中：${label}`
                            })"
                            help-theme="warning"
                        >
                            <abc-select
                                v-model="supplier.companyType"
                                :width="270"
                                :disabled="disabled || disabledInfo"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="it in options.type"
                                    :key="it.value"
                                    :label="it.label"
                                    :value="it.value"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-col>
                    <abc-col v-if="supplier.isEntrustDelivery" :span="6">
                        <abc-form-item
                            label="配货方式"
                            required
                            :help="getHelpText('entrustDeliveryType', (v)=>{
                                const label = options.entrustDeliveryType.find(item=>item.value === v)?.label
                                return isNull(label) ? '审批中：删除“配货方式”' : `审批中：${label}`
                            })"
                            help-theme="warning"
                        >
                            <abc-select
                                v-model="supplier.entrustDeliveryType"
                                :width="270"
                                :disabled="disabled || disabledInfo"
                                @enter="enterEvent"
                            >
                                <abc-option
                                    v-for="it in options.entrustDeliveryType"
                                    :key="it.value"
                                    :label="it.label"
                                    :value="it.value"
                                ></abc-option>
                            </abc-select>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="6">
                        <abc-form-item
                            label="经营范围"
                            :help="getHelpText('businessScope',({ businessScopeList = [] } = {})=>{
                                if(businessScopeList?.length){
                                    return `审批中：${ getBusinessScopeName(businessScopeList)}`
                                }
                                return '审批中：删除“经营范围”'
                            })"
                            help-theme="warning"
                        >
                            <abc-input
                                :width="270"
                                :value="businessScopesName"
                                :title="businessScopesName"
                                readonly
                                :disabled="disabled || disabledInfo"
                                clearable
                                @clear="handleBusinessScopesClear"
                                @click="handleBusinessScopesClick"
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>

                    <abc-col :span="6">
                        <abc-form-item
                            label="统一社会信用代码"
                            :help="getHelpText('unifiedSocialCreditIdentifier', null, '统一社会信用代码')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.unifiedSocialCreditIdentifier"
                                :width="270"
                                max-length="18"
                                :disabled="disabled || disabledInfo"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="6">
                        <abc-form-item
                            label="法人代表"
                            :help="getHelpText('legalRepresentative', null, '法人代表')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.legalRepresentative"
                                :width="270"
                                max-length="20"
                                :disabled="disabled || disabledInfo"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="6">
                        <abc-form-item
                            label="法人证件号"
                            :help="getHelpText('legalRepresentativeIdCard', null, '法人证件号')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.legalRepresentativeIdCard"
                                :width="270"
                                max-length="20"
                                :disabled="disabled || disabledInfo"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="6">
                        <abc-form-item
                            label="联系人"
                            :help="getHelpText('personInChargeOfEnterprise', null, '联系人')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.personInChargeOfEnterprise"
                                :width="270"
                                :disabled="disabled || disabledInfo"
                                max-length="20"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>

                    <abc-col :span="6">
                        <abc-form-item
                            label="联系电话"
                            :validate-event="validateMobile"
                            :help="getHelpText('mobile', null, '联系电话')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.mobile"
                                :width="270"
                                :disabled="disabled || disabledInfo"
                                type="phone"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="6">
                        <abc-form-item
                            label="电子邮箱"
                            :help="getHelpText('legalRepresentativeMail', null, '电子邮箱')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.legalRepresentativeMail"
                                :width="270"
                                :disabled="disabled || disabledInfo"
                                :max-length="30"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="12">
                        <abc-form-item
                            label="联系地址"
                            class="address-item"
                            :show-red-dot="addressRequired"
                            :validate-event="handleAddressValidate"
                            :help="getAddressHelpText()"
                            help-theme="warning"
                        >
                            <abc-address-selector
                                v-model="supplier.registeredAddressDetail"
                                clearable
                                :disabled="disabled || disabledInfo"
                            ></abc-address-selector>
                            <abc-input
                                v-model="supplier.registeredAddressDetail.addressDetail"
                                :disabled="disabled || disabledInfo"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                    <abc-col :span="6">
                        <abc-form-item
                            label="开户银行"
                            :help="getHelpText('openingBank', null, '开户银行')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.openingBank"
                                :width="270"
                                clearable
                                :max-length="30"
                                :disabled="disabled || disabledInfo"
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>

                    <abc-col :span="6">
                        <abc-form-item
                            label="银行账户"
                            :help="getHelpText('bankAccount', null, '银行账户')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.bankAccount"
                                :width="270"
                                :max-length="21"
                                :disabled="disabled || disabledInfo"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>

                    <abc-col :span="6">
                        <abc-form-item
                            label="备注"
                            :help="getHelpText('mark', null, '备注')"
                            help-theme="warning"
                        >
                            <abc-input
                                v-model="supplier.mark"
                                :width="270"
                                :max-length="50"
                                :disabled="disabled || disabledInfo"
                                clearable
                            ></abc-input>
                        </abc-form-item>
                    </abc-col>
                </abc-row>
            </section>
            <section class="group-item">
                <abc-flex justify="space-between" style="margin-bottom: 8px;">
                    <h3>销售员信息</h3>
                    <abc-button
                        icon="n-add-line-medium"
                        theme="primary"
                        variant="ghost"
                        @click="handleAddSaler"
                    >
                        新建销售员
                    </abc-button>
                </abc-flex>

                <abc-table
                    auto-height
                    :render-config="renderConfig"
                    :data-list="renderList"
                    :table-min-height="222"
                    :pagination="principalPagination"
                    @pageChange="handlePageChange"
                >
                    <template #action="{ trData: item }">
                        <abc-table-cell>
                            <abc-space>
                                <abc-button size="small" variant="text" @click="handleEditSaler(item)">
                                    编辑
                                </abc-button>
                                <abc-button
                                    theme="danger"
                                    size="small"
                                    variant="text"
                                    @click="handleDeleteSaler(item)"
                                >
                                    删除
                                </abc-button>
                            </abc-space>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </section>

            <section class="group-item">
                <h3>
                    <span>资质证照</span>
                    <abc-tips
                        v-if="getHelpText('extendInfo',({ certificationInfos = [] } = {})=>{
                            return certificationInfos?.length ? '审批中：修改资质证照' : '审批中：删除“资质证照”'
                        })"
                        theme="warning"
                        size="small"
                    >
                        {{
                            getHelpText('extendInfo',({ certificationInfos = [] } = {})=>{
                                return certificationInfos?.length ? '审批中：修改资质证照' : '审批中：删除“资质证照”'
                            })
                        }}
                    </abc-tips>
                </h3>
                <abc-row :gutter="[24, 16]" :wrap="'wrap'" class="supplier-archives-info">
                    <abc-col v-for="(cert, index) in certificationInfos" :key="cert.id" :span="6">
                        <cert-item
                            :width="270"
                            :disabled="disabled || disabledInfo"
                            :item.sync="certificationInfos[index]"
                            @delete="certificationInfos.splice(index, 1)"
                            @update="onUpdateCert"
                        ></cert-item>
                    </abc-col>
                </abc-row>
            </section>
        </abc-form>
        <abc-flex slot="footer" :justify="isGaspFinish ? 'space-between' : 'flex-end'">
            <abc-button
                v-if="isGaspFinish"
                type="danger"
                :disabled="disabledInfo"
                @click.stop="updateStatus"
            >
                {{ supplier.status === 1 ? '停用' : '启用' }}
            </abc-button>
            <abc-space>
                <abc-button
                    v-if="isVisibleSubmitGspBtn"
                    type="blank"
                    @click="onClickSubmitGsp"
                >
                    提交首营
                </abc-button>
                <abc-button
                    type="primary"
                    :loading="submitBtnLoading"
                    :disabled="submitBtnDisabled"
                    @click="handleSave"
                >
                    提交
                </abc-button>

                <abc-button type="blank" @click="handleCancel">
                    取消
                </abc-button>
            </abc-space>
        </abc-flex>

        <!--经营范围选择-->
        <business-scope-transfer-dialog
            v-model="showBusinessScopeTransferDialog"
            :scene="CatalogueEnum.BUSINESS_SCOPE_SUPPLIER"
            :default-checked-keys="defaultCheckedKeys"
            @confirm="onDialogConfirm"
        ></business-scope-transfer-dialog>
    </abc-dialog>
</template>

<script>
    import { PharmacyTypeEnum } from '@abc/constants';
    import EnterEvent from 'views/common/enter-event';
    import { BusinessTypeEnum } from '@/views/layout/mobile-upload-dialog/config';
    import SupplierApi from 'api/goods/supplier';
    import AbcSocket from 'views/common/single-socket';
    import Clone from 'utils/clone';
    import {
        clone, isEqual,
    } from '@abc/utils';
    import {
        gspStatusConst, gspStatusNameConst,
    } from '@/views-pharmacy/common/constants';

    import DialogSuppliersDetail from '@/views-pharmacy/gsp/frames/first-battalion/supplier/dialog-supplier-detail/index.vue';
    import * as tools from '@/views-pharmacy/common/tools';
    import { businessScopeOptions } from '@/views-pharmacy/common/options';
    import CertItem from '@/views-pharmacy/gsp/frames/first-battalion/supplier/dialog-supplier-detail/cert-item.vue';

    import {
        PurchaseSupplierCompanyTypeLabel,
    } from '@/views-pharmacy/inventory/constant';
    import {
        validateMobile,
        validateIdCard,
    } from 'utils/validate';
    import {
        mapActions, mapGetters,
    } from 'vuex';
    import {
        createGUID,
        isNotNull, isNull,
    } from '@/utils';
    import BusinessScopeTransferDialog from '@/views-pharmacy/inventory/frames/supplier/components/businessScopeTransfer.vue';
    import { CatalogueEnum } from '@/hooks/business/use-dictionary';
    import useBusinessScope from 'views/inventory/goods/archives/hook/useBusinessScope';
    import useSearchSupplier from 'views/inventory/hooks/useSearchSupplier';
    import fecha from 'utils/fecha';
    import { DATE_TIME_FORMATE } from '@/assets/configure/constants';
    import SupplierSalesmanDialog from '@/views-pharmacy/inventory/frames/supplier/components/supplier-salesman-dialog';
    import usePagination from '@/hooks/abc-ui/use-table-pagination';

    export default {
        name: 'SupplierDialog',
        components: {
            BusinessScopeTransferDialog,
            CertItem,
        },
        mixins: [EnterEvent],
        props: {
            type: {
                type: String,
                default: 'add',
                // required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            value: {
                type: Boolean,
                default: false,
            },
            supplierId: {
                type: String,
                default: '',
            },
            statusDisable: {
                type: Boolean,
                default: false,
            },
            defaultName: {
                type: String,
                default: '',
            },
            pharmacyType: {
                type: Number,
                default: PharmacyTypeEnum.LOCAL_PHARMACY,
            },
        },
        setup() {
            const {
                init,
                getBusinessScopeName,
            } = useBusinessScope(CatalogueEnum.BUSINESS_SCOPE_SUPPLIER);

            const {
                addSupplier,
                findSupplier,
            } = useSearchSupplier();

            const {
                pageParams,
                setPageSize,
                setPageTotal,
                changePageIndex,
            } = usePagination({
                pageSize: 8,
                pageIndex: 0,
                total: 0,
            });

            return {
                init,
                getBusinessScopeName,

                addSupplier,
                findSupplier,

                pageParams,
                setPageSize,
                setPageTotal,
                changePageIndex,
            };
        },
        data() {
            return {
                BusinessTypeEnum,
                showBusinessScopeTransferDialog: false,
                defaultCheckedKeys: [],
                supplier: {
                    id: '',
                    status: '',
                    pharmacyType: '',
                    // 发起gsp审批流
                    gspReviewStarted: '',
                    // 供应商名称
                    name: '',
                    // 企业类型
                    companyType: '',
                    // 企业编码
                    companyCode: '',
                    // 许可证编号
                    license: '',
                    // 统一社会信用代码
                    unifiedSocialCreditIdentifier: '',
                    // 企业法人
                    legalRepresentative: '',
                    // 企业法人证件号
                    legalRepresentativeIdCard: '',

                    // 企业负责人
                    personInChargeOfEnterprise: '',
                    // 质量负责人
                    qualityDirecter: '',
                    // 联系电话
                    mobile: '',
                    // 经营范围
                    businessScope: {
                        businessScopeList: [],
                    },
                    // 委托配送
                    isEntrustDelivery: 1,
                    // 注册地址
                    registeredAddressDetail: {
                        addressCityId: undefined,
                        addressProvinceId: undefined,
                        addressDistrictId: undefined,
                        addressDetail: undefined,
                    },
                    // 仓库地址
                    warehouseAddress: '',
                    // 备注
                    mark: '',
                    // 企业委托人
                    principal: '',
                    // 企业委托人身份证
                    principalIdCard: '',
                    // 企业委托人电话
                    principalTelephone: '',
                    // 企业委托开始时间
                    delegationBeginDate: '',
                    // 企业委托结束时间
                    delegationEndDate: '',
                    // 企业委托范围
                    delegationScope: '',
                    // 电子邮箱
                    legalRepresentativeMail: '',
                    // 开户银行
                    openingBank: '',
                    // 账户名称
                    accountName: '',
                    // 银行账号
                    bankAccount: '',
                    // 扩展信息
                    extendInfo: {
                        // 药品供应范围
                        restrictGoodsTypeIds: [],
                        // 证照信息-委托书附件
                        certificationPictureUrls: [],
                        // 证照信息
                        certificationInfos: [],
                    },
                    // 配送方式
                    entrustDeliveryType: '',
                },
                // 销售员数据
                principalList: [],
                options: {
                    type: PurchaseSupplierCompanyTypeLabel,
                    boolean: [
                        {
                            label: '是', value: 1,
                        },
                        {
                            label: '否', value: 0,
                        },
                    ],

                    reservationModeOpts: [
                        {
                            title: '总部集采',
                            intro: '采购机构为连锁总部，供应商根据配货方式配货（七统一：药品供应商必选）',
                            value: 1,
                        },{
                            title: '门店自采',
                            intro: '采购机构为连锁门店，供应商直接配货到门店（七统一：非药品供应商可选）',
                            value: 0,
                        },
                    ],

                    // 配送方式
                    entrustDeliveryType: [
                        {
                            label: '委托供应商配货', value: 1,
                        },
                        {
                            label: '总部自行配货', value: 2,
                        },
                    ],
                },
                cacheSupplier: {},
                loading: false,
                showDialog: this.value,
                submitBtnLoading: false,
                errorTip: '', // 供应商名称错误信息提示
                errorLicenseTip: '', // 重复许可证号错误提示
                businessScopesName: '',
                certificationInfos: [],
            };
        },
        computed: {
            CatalogueEnum() {
                return CatalogueEnum;
            },
            ...mapGetters(['isChain', 'isSingleStore', 'currentClinic', 'goodsConfig', 'employeeList', 'userInfo']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isSupportFirstCampApplicationBySupplier() {
                return this.viewDistributeConfig.Inventory.isSupportFirstCampApplicationBySupplier;
            },
            businessScopeOptions() {
                return businessScopeOptions;
            },
            submitBtnDisabled() {
                return isEqual(this.supplier, this.cacheSupplier) || this.disabledInfo;
            },
            // 是否待首营
            isGspAwait() {
                return this.cacheSupplier?.gspStatus === gspStatusConst.AWAIT;
            },
            // 是否已驳回
            isGspReject() {
                return this.cacheSupplier?.gspStatus === gspStatusConst.REJECT;
            },
            // 是否已首营
            isGaspFinish() {
                return this.cacheSupplier?.gspStatus === gspStatusConst.FINISH;
            },
            // 是否显示提交首营按钮
            isVisibleSubmitGspBtn() {
                return this.isGspAwait || this.isGspReject;
            },
            showTag() {
                return [gspStatusConst.AWAIT, gspStatusConst.DOING, gspStatusConst.REJECT].includes(this.cacheSupplier?.gspStatus);
            },
            tagName() {
                return gspStatusNameConst[this.cacheSupplier?.gspStatus] ?? '';
            },
            tagProps() {
                const waitConfig = {
                    shape: 'square',
                    theme: 'primary',
                    variant: 'outline',
                    size: 'medium',
                };
                const dangerConfig = {
                    shape: 'square',
                    theme: 'danger',
                    variant: 'outline',
                    size: 'medium',
                };

                const configMap = {
                    [gspStatusConst.AWAIT]: waitConfig,
                    [gspStatusConst.DOING]: waitConfig,
                    [gspStatusConst.REJECT]: dangerConfig,
                };

                return configMap[this.cacheSupplier?.gspStatus] || waitConfig;
            },
            dialogTitle() {
                let str = '';
                // const {
                //     Y2, R2, theme2,
                // } = this.$store.state?.theme?.style ?? {};

                if (this.type === 'add') {
                    str = '新增供应商';
                }
                if (this.type === 'edit') {
                    str = '编辑供应商';
                }
                // if (this.cacheSupplier?.gspStatus === gspStatusConst.AWAIT) {
                //     str += `<span class="goods-small-title" style="color: ${Y2}">待首营</span>`;
                // }
                // if (this.cacheSupplier?.gspStatus === gspStatusConst.DOING) {
                //     str += `<span class="goods-small-title" style="color: ${theme2}">待审核</span>`;
                // }
                // if (this.cacheSupplier?.gspStatus === gspStatusConst.REJECT) {
                //     str += `<span class="goods-small-title" style="color: ${R2}">已驳回</span>`;
                // }
                // if (this.cacheSupplier?.gspStatus === gspStatusConst.FINISH) {
                //     str += `<span class="goods-small-title" style="color: ${theme2}">已首营</span>`;
                // }
                return str;

            },
            disabledInfo() {
                return this.cacheSupplier?.gspModifyStatus === gspStatusConst.DOING;
            },
            addressRequired() {
                return this.goodsConfig.supplierMustField?.addressFlag === 1;
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            label: '姓名',
                            key: 'name',
                            // style: {
                            //     flex: '1',
                            //     width: '',
                            //     minWidth: '',
                            // },
                        },
                        {
                            label: '手机号',
                            key: 'mobile',
                            // style: {
                            //     flex: '1',
                            //     width: '',
                            //     minWidth: '',
                            // },
                        },
                        {
                            label: '身份证号',
                            key: 'idCard',
                            // style: {
                            //     flex: '1',
                            //     width: '',
                            //     minWidth: '',
                            // },
                        },
                        {
                            label: '操作',
                            key: 'action',
                            style: {
                                width: '108px',
                                minWidth: '108px',
                                maxWidth: '108px',
                                textAlign: 'center',
                            },
                        },
                    ],
                };
            },
            principalPagination() {
                return {
                    showTotalPage: true,
                    pageIndex: this.pageParams.pageIndex,
                    pageSize: this.pageParams.pageSize,
                    count: this.pageParams.total,
                };
            },
            renderList() {
                if (this.supplierId) {
                    return this.principalList;
                }
                const {
                    pageSize,
                    pageIndex,
                } = this.pageParams;
                return this.principalList.slice(pageSize * pageIndex, pageSize * (pageIndex + 1));
            },
        },
        watch: {
            showDialog(val) {
                this.$emit('input', val);
            },
        },
        async created() {
            await this.init();

            if (this.supplierId) {
                this.fetchSupplierDetail();
                this.fetchSupplierSellers();
            } else {
                this.supplier = {
                    ...this.supplier,
                    name: this.defaultName, // 供应商name
                    status: 1, // 启用状态
                    gspReviewStarted: 1,// 自动发起Gsp审批流
                    pharmacyType: this.pharmacyType,
                    isEntrustDelivery: this.isSingleStore ? 0 : 1,
                };
            }

            this.onUpdateCert();

            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('short-url.upload_attachment', this.handleImages);
        },
        beforeDestroy() {
            this._socket.off('short-url.upload_attachment', this.handleImages);
        },
        methods: {
            ...mapActions(['fetchSupplierList']),
            isNull,
            validateMobile,
            validateIdCard,
            handlePageChange(pageIndex) {
                this.changePageIndex(pageIndex - 1);
                if (this.supplierId) {
                    this.fetchSupplierSellers();
                }
            },
            handleAddSaler() {
                new SupplierSalesmanDialog({
                    visible: true,
                    supplier: this.supplier,
                    principalList: this.principalList,
                    onSuccess: this.handleAddSalerSuccess,
                }).generateDialogAsync();
            },
            handleEditSaler(item) {
                new SupplierSalesmanDialog({
                    visible: true,
                    supplier: this.supplier,
                    item,
                    principalList: this.principalList,
                    onSuccess: this.handleEditSalerSuccess,
                }).generateDialogAsync();
            },
            async handleDeleteSaler(item) {
                if (this.supplierId) {
                    let content = '确认删除销售员吗？';
                    const { data } = await SupplierApi.checkUnsettledOrdersExist(item.id);
                    if (data?.isYes) {
                        content = '当前销售员存在未结清的往来进货单据，确认删除吗？';
                    }
                    this.$confirm({
                        type: 'warn',
                        title: '删除确认',
                        content,
                        onConfirm: async () => {
                            await SupplierApi.deleteSupplierSeller(item.id);
                            this.$Toast({
                                type: 'success',
                                message: '删除成功',
                            });
                            this.changePageIndex(0);
                            this.fetchSupplierSellers();
                        },
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '删除确认',
                        content: '确认删除销售员吗？',
                        onConfirm: () => {
                            const index = this.principalList.findIndex((e) => (e.id ? e.id === item.id : e.keyId === item.keyId));
                            this.principalList.splice(index, 1);
                            this.changePageIndex(0);
                            this.setPageTotal(this.principalList.length);
                        },
                    });


                }
            },
            handleAddSalerSuccess(saler) {
                console.log('add saler', saler);
                if (this.supplierId) {
                    this.fetchSupplierSellers();
                } else {
                    this.principalList.push({
                        keyId: createGUID(),
                        ...saler,
                    });
                    this.setPageTotal(this.principalList.length);
                }
            },
            handleEditSalerSuccess(saler) {
                console.log('edit saler', saler);

                const index = this.principalList.findIndex((e) => {
                    return e.id ? e.id === saler.id : e.keyId === saler.keyId;
                });
                const item = this.principalList[index];
                this.principalList.splice(index, 1, Object.assign(item, saler));
            },
            handleModeCardClick(value) {
                if (this.disabled || this.disabledInfo) return;
                this.supplier.isEntrustDelivery = value;
            },
            handleBusinessScopesClick() {
                this.defaultCheckedKeys = this.supplier.businessScope.businessScopeList.map((e) => e.id);
                this.showBusinessScopeTransferDialog = true;
            },
            handleBusinessScopesClear() {
                this.supplier.businessScope.businessScopeList = [];
                this.businessScopesName = '';
            },
            onDialogConfirm(data, name) {
                this.supplier.businessScope.businessScopeList = data;
                this.businessScopesName = name;
            },
            getAddressHelpText() {
                return this.getHelpText('registeredAddressDetail', () => {
                    const {
                        addressProvinceName,
                        addressCityName,
                        addressDistrictName,
                        addressDetail,
                    } = this.supplier.modifySupplier?.registeredAddressDetail || {};

                    const _addressProvinceName = addressProvinceName || this.supplier?.registeredAddressDetail?.addressProvinceName || '';
                    const _addressCityName = addressCityName || this.supplier?.registeredAddressDetail?.addressCityName || '';
                    const _addressDistrictName = addressDistrictName || this.supplier?.registeredAddressDetail?.addressDistrictName || '';
                    const _addressDetail = addressDetail || this.supplier?.registeredAddressDetail?.addressDetail || '';
                    if (
                        this.supplier.modifySupplier?.registeredAddressDetail?.hasOwnProperty('addressProvinceName') ||
                        this.supplier.modifySupplier?.registeredAddressDetail?.hasOwnProperty('addressCityName') ||
                        this.supplier.modifySupplier?.registeredAddressDetail?.hasOwnProperty('addressDistrictName') ||
                        this.supplier.modifySupplier?.registeredAddressDetail?.hasOwnProperty('addressDetail')
                    ) {
                        if (addressProvinceName ||
                            addressCityName ||
                            addressDistrictName ||
                            addressDetail) {
                            return `审批中：${_addressProvinceName}/${_addressCityName}/${_addressDistrictName}/${_addressDetail}`;
                        }
                        return '审批中：删除“联系地址”';
                    }

                    return '';
                });
            },
            getHelpText(key, formatFn, label = '') {
                if (this.disabledInfo && key && this.supplier?.modifySupplier?.hasOwnProperty(key)) {
                    // 修改
                    const val = this.supplier?.modifySupplier[key];
                    const originVal = this.supplier[key];
                    // 没变化不提示
                    if (val === originVal) return '';
                    // 变化了自定义提示
                    if (formatFn) return formatFn(val);

                    return isNotNull(val) ? `审批中：${val}` : `审批中：删除“${label}”`;
                }
                return '';
            },
            handleImages(data) {
                const {
                    attachments = [],
                    businessType,
                } = data;
                if (+businessType === BusinessTypeEnum.GOODS_SUPPLER_ATTACHMENT && attachments?.length) {
                    const urls = [];
                    attachments.forEach((item) => {
                        const isExist = this.supplier.extendInfo.certificationPictureUrls?.find((it) => it.id === item.id);
                        if (!isExist) {
                            urls.push(item);
                        }
                    });
                    this.supplier.extendInfo.certificationPictureUrls = clone(urls);
                }
            },
            /**
             * @desc 查询是否有重复的供应商
             * <AUTHOR>
             * @date 2019/07/10 15:22:25
             */
            async checkRepeatSupplier(val, callback) {
                this.errorTip = '';
                if (!this.supplier.name) {
                    callback({
                        validate: false,
                        message: '供应商名称不能为空',
                    });
                    return;
                }
                if (this.supplier.name === this.cacheSupplier.name) return;
                try {
                    const { data } = await SupplierApi.checkRepeatSupplier(this.supplier.name, this.pharmacyType);
                    if (data.rows && data.rows.length >= 1) {
                        this.errorTip = '该供应商名称已存在';
                        callback({
                            validate: false,
                            message: '该供应商名称已存在',
                        });
                    }
                } catch (e) {
                    this.errorTip = '';
                }
            },
            handleAddressValidate(val, callback) {
                this.errorTip = '';
                if (this.addressRequired &&
                    !(this.supplier.registeredAddressDetail.addressProvinceId && this.supplier.registeredAddressDetail.addressDetail)
                ) {
                    return callback({
                        validate: false,
                        message: '联系地址不能为空',
                    });
                }
                return callback({ validate: true });
            },
            /**
             * @desc 查询是否有重复的许可证号
             * <AUTHOR>
             * @date 2019/07/10 15:41:45
             * @params
             */
            async checkRepeatLicense(val, callback) {
                this.errorLicenseTip = '';
                try {
                    if (!this.supplier.license || this.cacheSupplier.license === this.supplier.license) return;

                    const { data } = await SupplierApi.checkRepeatLicense(this.supplier.license);
                    if (data?.rows?.length) {
                        this.errorLicenseTip = '该许可证号已存在';
                        callback({
                            message: '该许可证号已存在',
                            validate: false,
                        });
                    }
                } catch (e) {
                    this.errorLicenseTip = '';
                }
            },

            createParams() {
                return {
                    ...this.supplier,
                    entrustDeliveryType: this.supplier?.isEntrustDelivery ? this.supplier?.entrustDeliveryType : undefined,
                    extendInfo: {
                        ...this.supplier.extendInfo,
                        certificationInfos: this.certificationInfos.filter((item) => item.pictureUrls?.[0]?.url),
                    },
                };
            },
            createGsp() {
                const gsp = {
                    applyOrganId: this.currentClinic?.clinicId, // 申请机构ID
                    applyOrganName: this.currentClinic?.clinicName, // 申请机构名称
                    applyTime: fecha.format(new Date(), DATE_TIME_FORMATE), // 申请时间，默认当前日期
                    applyUserId: (() => {
                        // 默认当前人员，没有就选第一个
                        const target = this.employeeList.find((item) => item.employeeId === this.userInfo?.id) || this.employeeList[0];
                        return target?.employeeId || '';
                    })(), // 申请人员
                    remark: '', // 备注
                    evaluateContent: '', // 质量体系情况评价
                };
                return gsp;
            },
            async createSupplier() {
                try {
                    this.submitBtnLoading = true;
                    const params = this.createParams();
                    params.gsp = this.createGsp();
                    params.supplierSellers = this.principalList.map((item) => {
                        return {
                            name: item.name,
                            mobile: item.mobile,
                            idCard: item.idCard,
                        };
                    });

                    const { data } = await SupplierApi.createSupplier(params);
                    const isSubmitGsp = params.gspReviewStarted === 1; // 是否提交GSP首营申请
                    if (!isSubmitGsp && this.isSupportFirstCampApplicationBySupplier) {
                        // 非自动提交GSP首营申请
                        this.$emit('submit-gsp', data.id);
                    }
                    this.$Toast({
                        type: 'success',
                        message: `保存成功${isSubmitGsp ? '并提交首营' : ''}`,
                        isSubmitGsp,
                    });
                    this.$emit('add', data);
                    this.$emit('refresh', 'add');
                    this.addSupplier(data);

                    this.fetchSupplierList({
                        keyword: '',
                        clinicId: this.currentClinic.clinicId,
                        status: '',
                        pharmacyType: this.pharmacyType,
                    });
                    this.showDialog = false;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.submitBtnLoading = false;
                }
            },
            async updateSupplier() {
                try {
                    this.submitBtnLoading = true;
                    const params = this.createParams();
                    const res = await SupplierApi.updateSupplier(this.supplierId, params);
                    // 开启审批时修改已首营供应商
                    if (res?.data?.data?.gspModifyStatus === gspStatusConst.DOING) {
                        this.$Toast({
                            type: 'success',
                            message: '修改申请已提交',
                        });
                    }
                    this.showDialog = false;
                    this.$emit('refresh');
                    // 更新全局供应商数据中当前供应商的数据
                    const supplier = this.findSupplier(this.supplierId);
                    Object.assign(supplier, res?.data?.data);
                    this.fetchSupplierList({
                        keyword: '',
                        clinicId: this.currentClinic.clinicId,
                        status: '',
                        pharmacyType: this.pharmacyType,
                    });
                } catch (e) {
                    console.error(e);
                } finally {
                    this.submitBtnLoading = false;
                }
            },
            async fetchSupplierDetail() {
                try {
                    this.loading = true;
                    const res = await SupplierApi.fetchSupplierDetail(this.supplierId);
                    const certificationInfos = res.data?.extendInfo?.certificationInfos || [];
                    const restrictGoodsTypeIds = res.data?.extendInfo?.restrictGoodsTypeIds || [];
                    const certificationPictureUrls = res.data?.extendInfo?.certificationPictureUrls || [];
                    const data = {
                        ...res.data,
                        extendInfo: {
                            restrictGoodsTypeIds,
                            certificationPictureUrls,
                            certificationInfos,
                        },
                    };
                    this.certificationInfos = certificationInfos;
                    this.onUpdateCert();
                    this.supplier = {
                        ...this.supplier,
                        ...data,
                    };
                    this.businessScopesName = this.getBusinessScopeName(this.supplier?.businessScope?.businessScopeList ?? []);
                    this.cacheSupplier = Clone(this.supplier);
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            async fetchSupplierSellers() {
                try {
                    const res = await SupplierApi.getSupplierSellerList(this.supplierId, {
                        limit: this.pageParams.limit,
                        offset: this.pageParams.offset,
                    });
                    this.principalList = res.data?.rows || [];
                    this.setPageTotal(res.data?.total || this.principalList.length);
                } catch (e) {
                    console.error(e);
                }
            },
            /**
             * 处理提交GSP供应商首营
             * <AUTHOR>
             * @date 2024-01-16
             */
            async onClickSubmitGsp() {
                const openResponse = await tools.openDialog({
                    propsData: {
                        supplierId: this.supplierId, // 供应商id
                    },
                    component: DialogSuppliersDetail,
                });
                if (openResponse.status === false) {
                    return openResponse;
                }
                this.showDialog = false;
                this.$emit('refresh');
            },
            handleSave() {
                this.$refs.supplierForm.validate(async (val) => {
                    if (val) {
                        if (this.type === 'add') {
                            await this.createSupplier();
                        } else {
                            if (this.supplier.name !== this.cacheSupplier.name) {
                                this.$confirm({
                                    type: 'warn',
                                    title: '修改确认',
                                    content: '修改供应商名称，所有入库当中供应商名称也会同步更新，是否确认？',
                                    onConfirm: async () => {
                                        await this.updateSupplier();
                                    },
                                });
                            } else {
                                await this.updateSupplier();
                            }
                        }
                    }
                });
            },
            handleCancel() {
                this.showDialog = false;
            },
            /**
             * 创建附件模版
             * <AUTHOR>
             * @date 2024-01-19
             * @returns {Object}
             */
            createCertTemplate() {
                const certTemplate = {
                    pictureUrls: [], // 图片
                    type: '', // 类型
                    name: '', // 名称
                    no: '', // 编号
                    validFrom: '', // 效期 - 开始日期
                    validTo: '', // 效期 - 截止日期
                };
                return certTemplate;
            },
            /**
             * 当改变附件时触发
             * <AUTHOR>
             * @date 2024-01-19
             */
            onUpdateCert() {
                const isExist = this.certificationInfos.find((item) => !item.pictureUrls?.[0]?.url);
                if (isExist) {
                    return;
                }
                const certTemplate = this.createCertTemplate();
                this.certificationInfos.push(certTemplate);
            },
            updateStatus() {
                if (this.supplier.status) {
                    this.$confirm({
                        type: 'warn',
                        title: '停用确认',
                        content: '停用后，将无法选择此供应商采购、入库',
                        onConfirm: () => {
                            this.supplier.status = 0;
                            this.updateSupplier();
                        },
                    });
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '启用确认',
                        content: '是否启用该供应商？',
                        onConfirm: () => {
                            this.supplier.status = 1;
                            this.updateSupplier();
                        },
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
.external-files-wrapper {
    margin-left: 0;

    .file-add {
        margin-left: 0;
    }
}

.supplier-archives-wrapper {
    .supplier-archives-info {
        .address-item {
            width: 100%;

            .abc-form-item-content {
                display: flex;
                width: 100%;

                .address-selector {
                    flex-basis: 33%;

                    .address-selector-input {
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }
                }

                .abc-input-wrapper {
                    flex-basis: 67%;

                    .abc-input__inner {
                        border-left: 1px solid transparent;
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                    }
                }
            }
        }
    }

    .tabs-wrapper {
        display: flex;
        margin-bottom: 16px;

        > div {
            position: relative;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            width: 264px;
            height: 83px;
            padding: 12px 16px 0;
            margin-right: 16px;
            cursor: pointer;
            background: #ffffff;
            border: 1px solid #e6eaee;
            border-radius: var(--abc-border-radius-small);
            box-shadow: 0 6px 24px 0 rgba(0, 0, 0, 0.08);

            &.active {
                border: 2px solid #0db951;

                &::before {
                    position: absolute;
                    top: -14px;
                    right: -4px;
                    width: 0;
                    height: 0;
                    content: '';
                    border: 20px solid transparent;
                    border-right: 0;
                    border-left-color: #0db951;
                    transform: rotate(-45deg);
                }
            }

            > div {
                margin-bottom: 4px;
                font-weight: 400;
            }

            > span {
                font-size: 12px;
                line-height: 16px;
                color: #7a8794;
            }

            > img {
                position: absolute;
                top: 1px;
                right: 1px;
                width: 12px;
                height: 12px;
            }
        }
    }
}
</style>
