import BaseProTable from '@/views/layout/tables/base-pro-table.js';
import { InventoryViewMode } from '@/views-pharmacy/inventory/constant';

export default class PharmacyArchivesTable extends BaseProTable {
    name = 'PharmacyArchivesTable';

    constructor() {
        super();
        // key与renderType的映射关系
        this.renderTypeMap = {
            displayName: 'displayNameRender',
            goodsTypeName: 'typeRender',
            displaySpec: 'specRender',
            manufacturer: 'manufacturerRender',
            profitCategory: 'profitCategoryRender',
            shortId: 'shortIdRender',
            piecePrice: 'priceRender',
            packagePrice: 'priceRender',
            lastPackageCostPrice: 'lastPackageCostPriceRender',
            profitRat: 'profitRatRender',
            turnoverDays: 'turnoverDaysRender',
            minExpiryDate: 'minExpiryDateRender',
            shebaoCode: 'shebaoCodeRender',
            standardCode: 'standardCodeRender',
            medicalFeeGradeName: 'medicalFeeGradeNameRender',
            shebaoCodePriceLimited: 'shebaoCodePriceLimitedRender',
            dispGoodsCount: 'currentCountRender',
            dispStockGoodsCount: 'stockCountRender',
            lastMonthSellCount: 'lastMonthSellCountRender',
            listingPrice: 'listingPriceRender',
            goodsTag: 'goodsTagRender',
            drugIdentificationCode: 'drugIdentificationCodeRender',
            goodsPosition: 'goodsPositionRender',
            memberPrice: 'memberPriceRender',
            firstInDate: 'firstInDateRender',
            stockWarnRule: 'stockWarnRuleRender',
        };

        // key与headerRenderType的映射关系
        this.headerRenderTypeMap = {

        };
        // key与headerAppendRenderType的映射关系
        this.headerAppendRenderTypeMap = {

        };
    }

    // 由产品.设计提供的静态配置, 开发只能修改key、renderType
    static staticConfig = {
        'hasInnerBorder': false,
        'list': [
            {
                'key': 'displayName',
                'parentKey': 'goodsBasicInfo',
                'prop': 'displayName',
                'label': '商品名称',
                'width': 356,
                'type': null,
                'renderType': 'displayNameRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 1,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 1,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': true,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '256px',
                    'maxWidth': '',
                    'minWidth': '256px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'goodsTypeName',
                'parentKey': 'goodsBasicInfo',
                'prop': 'goodsTypeName',
                'label': '类型',
                'width': 86,
                'type': null,
                'renderType': 'typeRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 1,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 2,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': true,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '98px',
                    'maxWidth': '',
                    'minWidth': '98px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'displaySpec',
                'parentKey': 'goodsBasicInfo',
                'prop': 'displaySpec',
                'label': '规格',
                'width': 158,
                'type': null,
                'renderType': 'specRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 1,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 3,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': true,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '158px',
                    'maxWidth': '',
                    'minWidth': '158px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'manufacturer',
                'parentKey': 'goodsBasicInfo',
                'prop': 'manufacturer',
                'label': '厂家',
                'width': 104,
                'type': null,
                'renderType': 'manufacturerRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 1,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 4,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': true,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '140px',
                    'maxWidth': '',
                    'minWidth': '140px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'profitCategory',
                'parentKey': 'goodsPriceInfo',
                'prop': 'profitCategory',
                'label': '利润分类',
                'width': 80,
                'type': null,
                'renderType': 'profitCategoryRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 1,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 5,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': true,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '98px',
                    'maxWidth': '',
                    'minWidth': '98px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'shortId',
                'parentKey': 'goodsBasicInfo',
                'prop': 'shortId',
                'label': '商品编码',
                'width': 130,
                'type': null,
                'renderType': 'shortIdRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 1,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 6,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': true,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '130px',
                    'maxWidth': '',
                    'minWidth': '130px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'piecePrice',
                'parentKey': 'goodsPriceInfo',
                'prop': 'piecePrice',
                'label': '拆零价',
                'width': 80,
                'type': 'money',
                'renderType': 'priceRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 7,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': 'money',
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '98px',
                    'maxWidth': '',
                    'minWidth': '98px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            }, {
                'key': 'lastPackageCostPrice',
                'parentKey': 'goodsPriceInfo',
                'prop': 'lastPackageCostPrice',
                'label': '最近进价',
                'width': 80,
                'type': 'number',
                'renderType': 'lastPackageCostPriceRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 8,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': 'number',
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '98px',
                    'maxWidth': '',
                    'minWidth': '98px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            }, {
                'key': 'businessScope',
                'parentKey': 'goodsBasicInfo',
                'prop': 'businessScope',
                'label': '所属经营范围',
                'width': 104,
                'type': null,
                'renderType': '',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 9,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '168px',
                    'maxWidth': '',
                    'minWidth': '168px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'profitRat',
                'parentKey': 'goodsPriceInfo',
                'prop': 'profitRat',
                'label': '毛利率',
                'width': 80,
                'type': null,
                'renderType': 'profitRatRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 10,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '98px',
                    'maxWidth': '',
                    'minWidth': '98px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            }, {
                'key': 'turnoverDays',
                'parentKey': 'goodsStockInfo',
                'prop': 'turnoverDays',
                'label': '周转天数',
                'width': 100,
                'type': null,
                'renderType': 'turnoverDaysRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 11,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '100px',
                    'maxWidth': '',
                    'minWidth': '100px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            }, {
                'key': 'minExpiryDate',
                'parentKey': 'goodsStockInfo',
                'prop': 'minExpiryDate',
                'label': '最近效期',
                'width': 100,
                'type': null,
                'renderType': 'minExpiryDateRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 12,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '100px',
                    'maxWidth': '',
                    'minWidth': '100px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            }, {
                'key': 'shebaoCode',
                'parentKey': 'goodsShebaoInfo',
                'prop': 'shebaoCode',
                'label': '医保码',
                'width': 80,
                'type': null,
                'renderType': 'shebaoCodeRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 13,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '98px',
                    'maxWidth': '',
                    'minWidth': '98px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'medicalFeeGradeName',
                'parentKey': 'goodsShebaoInfo',
                'prop': 'medicalFeeGradeName',
                'label': '医保类别',
                'width': 115,
                'type': null,
                'renderType': 'medicalFeeGradeNameRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 14,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '115px',
                    'maxWidth': '',
                    'minWidth': '115px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'shebaoCodePriceLimited',
                'parentKey': 'goodsShebaoInfo',
                'prop': 'shebaoCodePriceLimited',
                'label': '医保限价',
                'width': 115,
                'type': null,
                'renderType': 'shebaoCodePriceLimitedRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 15,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '115px',
                    'maxWidth': '',
                    'minWidth': '115px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'customTypeName',
                'parentKey': 'goodsBasicInfo',
                'prop': 'customTypeName',
                'label': '二级分类',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 16,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '104px',
                    'maxWidth': '',
                    'minWidth': '104px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'maintainTypeName',
                'parentKey': 'goodsBasicInfo',
                'prop': 'maintainTypeName',
                'label': '养护分类',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 17,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '104px',
                    'maxWidth': '',
                    'minWidth': '104px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'pharmacologicName',
                'parentKey': 'goodsBasicInfo',
                'prop': 'pharmacologicName',
                'label': '功效分类',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 18,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '104px',
                    'maxWidth': '',
                    'minWidth': '104px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'storage',
                'parentKey': 'goodsBasicInfo',
                'prop': 'storage',
                'label': '存储条件',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 19,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '168px',
                    'maxWidth': '',
                    'minWidth': '168px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'medcineNpmn',
                'parentKey': 'goodsBasicInfo',
                'prop': 'medcineNpmn',
                'label': '批准文号',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 20,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '196px',
                    'maxWidth': '',
                    'minWidth': '196px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'otcTypeName',
                'parentKey': 'goodsBasicInfo',
                'prop': 'otcTypeName',
                'label': '处方药/OTC',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 21,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '118px',
                    'maxWidth': '',
                    'minWidth': '118px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'remark',
                'parentKey': 'goodsBasicInfo',
                'prop': 'remark',
                'label': '备注',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 22,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '104px',
                    'maxWidth': '',
                    'minWidth': '104px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'barCode',
                'parentKey': 'goodsBasicInfo',
                'prop': 'barCode',
                'label': '条码',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 23,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '196px',
                    'maxWidth': '',
                    'minWidth': '196px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'baseMedicineTypeName',
                'parentKey': 'goodsBasicInfo',
                'prop': 'baseMedicineTypeName',
                'label': '基药',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 24,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'customRender': null,
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '104px',
                    'maxWidth': '',
                    'minWidth': '104px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            }, {
                'key': 'dispStockGoodsCount',
                'parentKey': 'goodsStockInfo',
                'prop': 'dispStockGoodsCount',
                'label': '可售库存',
                'width': 110,
                'type': null,
                'renderType': 'stockCountRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 25,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '110px',
                    'maxWidth': '',
                    'minWidth': '110px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            }, {
                'key': 'lastMonthSellCount',
                'parentKey': 'goodsStockInfo',
                'prop': 'lastMonthSellCount',
                'label': '30日销量',
                'width': 110,
                'type': null,
                'renderType': 'lastMonthSellCountRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 26,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '110px',
                    'maxWidth': '',
                    'minWidth': '110px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            }, {
                'key': 'clinicName',
                'parentKey': 'goodsBasicInfo',
                'prop': 'clinicName',
                'label': '门店',
                'width': 140,
                'type': null,
                'renderType': 'lastMonthSellCountRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 26,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '140px',
                    'maxWidth': '',
                    'minWidth': '140px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            }, {
                'key': 'packagePrice',
                'parentKey': 'goodsPriceInfo',
                'prop': 'packagePrice',
                'label': '售价',
                'width': 80,
                'type': 'money',
                'renderType': 'priceRender',
                'align': 'right',
                'titleAlign': 'right',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 27,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'colType': 'money',
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '98px',
                    'maxWidth': '',
                    'minWidth': '98px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            },
            {
                'key': 'standardCode',
                'parentKey': 'goodsPriceInfo',
                'prop': 'standardCode',
                'label': '本位码',
                'width': 196,
                'type': 'money',
                'renderType': 'standardCodeRender',
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 27,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.pharmacy.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'test',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'children': [],
                'headerRender': null,
                'pinned': false,
                'descriptionRender': null,
                'hidden': null,
                'style': {
                    'flex': 1,
                    'width': '196px',
                    'maxWidth': '',
                    'minWidth': '196px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'drugIdentificationCode',
                'label': '标识码',
                'style': {
                    'flex': 1,
                    'width': '115px',
                    'maxWidth': '',
                    'minWidth': '115px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'listingPrice',
                'parentKey': 'goodsShebaoInfo',
                'prop': 'listingPrice',
                'label': '挂网价',
                'width': 90,
                'type': 'money',
                'renderType': 'listingPriceRender',
                'align': 'right',
                'titleAlign': null,
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 1,
                'cellStyle': null,
                'groupBy': null,
                'position': 30,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.stocks.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'dev',
                'defaultValue': null,
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'style': {
                    'flex': 1,
                    'width': '90px',
                    'maxWidth': '',
                    'minWidth': '90px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'right',
                },
            },
            {
                'key': 'goodsTag',
                'parentKey': 'goodsBasicInfo',
                'label': '标签',
                'settingDisplayLabel': '标签',
                'env': 'dev',
                'renderType': 'goodsTagRender',
                'isHidden': 0,
                'isFixed': 0,
                'position': 30,
                'enableIndependentModifyColumnChildren': 0,
                'enableEmployeeModify': 1,
                'settingSort': 8,
                'children': null,
                'columnChildren': null,
                'isSettingHidden': 0,
                'style': {
                    'flex': 1,
                    'width': '160px',
                    'maxWidth': '',
                    'minWidth': '160px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'position',
                'parentKey': 'goodsBasicInfo',
                'label': '柜号',
                'settingDisplayLabel': '柜号',
                'env': 'dev',
                'renderType': 'goodsPositionRender',
                'isHidden': 0,
                'isFixed': 0,
                'position': 30,
                'enableIndependentModifyColumnChildren': 0,
                'enableEmployeeModify': 1,
                'settingSort': 8,
                'children': null,
                'columnChildren': null,
                'isSettingHidden': 0,
                'style': {
                    'flex': 1,
                    'width': '120px',
                    'maxWidth': '',
                    'minWidth': '120px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'memberPrice',
                'parentKey': 'goodsBasicInfo',
                'label': '会员价',
                'settingDisplayLabel': '会员价',
                'env': 'dev',
                'renderType': 'memberPriceRender',
                'isHidden': 0,
                'isFixed': 0,
                'position': 30,
                'enableIndependentModifyColumnChildren': 0,
                'enableEmployeeModify': 1,
                'settingSort': 8,
                'children': null,
                'columnChildren': null,
                'isSettingHidden': 0,
                'style': {
                    'flex': 1,
                    'width': '100px',
                    'maxWidth': '',
                    'minWidth': '100px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'lastSupplierName',
                'parentKey': 'goodsPriceInfo',
                'label': '最近供应商',
                'settingDisplayLabel': '最近供应商',
                'env': 'dev',
                'isHidden': 0,
                'isFixed': 0,
                'position': 30,
                'enableIndependentModifyColumnChildren': 0,
                'enableEmployeeModify': 1,
                'settingSort': 8,
                'children': null,
                'columnChildren': null,
                'isSettingHidden': 0,
                'style': {
                    'flex': 1,
                    'width': '148px',
                    'maxWidth': '',
                    'minWidth': '148px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'firstInDate',
                'parentKey': 'goodsStockInfo',
                'label': '首次入库时间',
                'settingDisplayLabel': '首次入库时间',
                'env': 'dev',
                'isHidden': 0,
                'isFixed': 0,
                'position': 30,
                'enableIndependentModifyColumnChildren': 0,
                'enableEmployeeModify': 1,
                'settingSort': 8,
                'children': null,
                'columnChildren': null,
                'isSettingHidden': 0,
                'style': {
                    'flex': 1,
                    'width': '148px',
                    'maxWidth': '',
                    'minWidth': '148px',
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': 'left',
                },
            },
            {
                'key': 'stockWarnRule',
                'parentKey': 'goodsBasicInfo',
                'prop': 'stockWarnRule',
                'label': '库存预警规则',
                'width': 104,
                'type': null,
                'renderType': null,
                'align': 'left',
                'titleAlign': 'left',
                'description': null,
                'descriptionWidth': null,
                'descriptionRenderType': null,
                'isFixed': 0,
                'sortable': 0,
                'cellStyle': null,
                'groupBy': null,
                'position': 3,
                'isHidden': 0,
                'isContentHidden': null,
                'contentHiddenRenderType': null,
                'headerRenderType': null,
                'tableKey': 'goods.stocks.goodsBasicInfo',
                'columnBeMergedIntoKey': null,
                'env': 'dev',
                'defaultValue': '0',
                'summary': null,
                'summaryRenderType': null,
                'columnChildren': null,
                'style': {
                    'width': '148px',
                    'minWidth': '148px',
                    'textAlign': 'left',
                },
            },
        ],
    };

    // abc-table组件的配置
    createRenderConfig(header, renderTypeList, viewMode) {
        const list = header.map((item) => {
            const {
                prop, isFixed, position,
            } = item;
            const propConfig = PharmacyArchivesTable.staticConfig.list.find((config) => config.key === prop);

            if (!propConfig) {
                console.error('propConfig is null', prop, item);
                return;
            }

            if (this.renderTypeMap[prop]) {
                const render = renderTypeList[this.renderTypeMap[prop]];
                propConfig.customRender = render;
            }

            if (this.headerRenderTypeMap[prop]) {
                const render = renderTypeList[this.headerRenderTypeMap[prop]];
                propConfig.headerRender = render;
            }

            if (this.headerAppendRenderTypeMap[prop]) {
                const render = renderTypeList[this.headerAppendRenderTypeMap[prop]];
                propConfig.headerAppendRender = render;
            }

            propConfig.pinned = !!isFixed;
            propConfig.position = position;

            return propConfig;
        }).filter(Boolean).sort((a, b) => a.position - b.position);

        if ([InventoryViewMode.BATCH, InventoryViewMode.BATCH_PRINT_PRICE_TAG].includes(viewMode)) {
            list.unshift({
                label: ' ',
                isCheckbox: true,
                pinned: true,
                style: {
                    flex: 'none',
                    width: '40px',
                    maxWidth: '',
                    paddingLeft: '',
                    paddingRight: '',
                    textAlign: 'left',
                },
            });
        }

        return {
            ...PharmacyArchivesTable.staticConfig,
            list,
        };
    }

    // abc-table-fixed2表头配置处理
    formatHeadersForTableFixed2(header) {
        const {
            prop,
        } = header;
        const propConfig = PharmacyArchivesTable.staticConfig.list.find((config) => config.key === prop);

        return header.map((item) => {

            item.width = propConfig.width;
            item.sortable = propConfig.sortable;
            item.sortable = propConfig.sortable;
            item.titleAlign = propConfig.titleAlign;
            item.align = propConfig.align;
            return item;
        }).sort((a, b) => a.position - b.position);
    }
}
