<template>
    <div class="inventory-goods-content goods-content">
        <abc-layout preset="page-table" style="height: 100%;">
            <abc-layout-header>
                <abc-flex class="goods-filter" justify="space-between">
                    <filter-bar
                        :fetch-params="fetchParams"
                        :model="model"
                        :panel-data="panelData"
                        :pharmacy-type="pharmacyType"
                        :pharmacy-no="pharmacyNo"
                        :search-only-stock="searchOnlyStock"
                        :enable-barcode-detector="enableBarcodeDetector"
                    ></filter-bar>

                    <abc-space v-if="!isBatchUpdateView">
                        <abc-dropdown
                            v-if="isAdmin"
                            :min-width="100"
                            placement="bottom-end"
                            @change="handleCreateArchive"
                        >
                            <template #reference>
                                <abc-check-access>
                                    <abc-tooltip :disabled="!disabledPharmacyArchives" :content="contentText" placement="top">
                                        <div>
                                            <abc-button
                                                :disabled="disabledPharmacyArchives"
                                                icon="s-b-add-line-medium"
                                                theme="success"
                                                data-cy="inventory-stock-add-archives"
                                            >
                                                新建档案
                                            </abc-button>
                                        </div>
                                    </abc-tooltip>
                                </abc-check-access>
                            </template>

                            <abc-dropdown-item :value="GoodsTypeEnum.MEDICINE" label="新建药品" data-cy="inventory-stock-add-medicine">
                            </abc-dropdown-item>

                            <abc-dropdown-item :label="materialText" :value="GoodsTypeEnum.MATERIAL" data-cy="inventory-stock-add-materials">
                            </abc-dropdown-item>

                            <abc-dropdown-item :value="GoodsTypeEnum.GOODS" label="新建商品" data-cy="inventory-stock-add-goods">
                            </abc-dropdown-item>

                            <abc-dropdown-item v-if="hasEyeglasses" :value="GoodsTypeEnum.EYEGLASSES" label="新建眼镜">
                            </abc-dropdown-item>
                        </abc-dropdown>

                        <abc-button
                            v-else
                            theme="success"
                            icon="s-b-add-line-medium"
                            @click="handleAddGoods"
                        >
                            添加商品
                        </abc-button>

                        <abc-dropdown
                            placement="bottom-start"
                            @change="handleBatchOperation"
                        >
                            <abc-button
                                slot="reference"
                                shape="square"
                                variant="ghost"
                            >
                                批量操作
                            </abc-button>

                            <abc-dropdown-item
                                v-if="showSubSetPrice"
                                :value="0"
                                :disabled="disabledPharmacySetPrice"
                            >
                                <abc-tooltip
                                    :disabled="!disabledPharmacySetPrice"
                                    content="无调价权限，可在 [设置-信息安全] 中设置"
                                    placement="top"
                                    :z-index="9999"
                                >
                                    <abc-text>批量调价</abc-text>
                                </abc-tooltip>
                            </abc-dropdown-item>

                            <abc-dropdown-item :value="1" :disabled="disabledPharmacyArchives">
                                <abc-tooltip
                                    :z-index="9999"
                                    :disabled="!disabledPharmacyArchives"
                                    :content="contentText"
                                    placement="top"
                                >
                                    <abc-text>批量修改档案</abc-text>
                                </abc-tooltip>
                            </abc-dropdown-item>

                            <abc-dropdown-item v-if="isShowPrintPriceTagButton" :value="2">
                                批量打印价签
                            </abc-dropdown-item>
                        </abc-dropdown>

                        <abc-button
                            :loading="exportLoading"
                            icon="n-upload-line"
                            type="blank"
                            @click="handleExport"
                        >
                            导出
                        </abc-button>
                        <abc-dropdown placement="bottom-end" @change="handleSettingChange">
                            <abc-button
                                slot="reference"
                                icon="n-more-line-medium"
                                variant="ghost"
                            >
                            </abc-button>
                            <abc-dropdown-item
                                v-for="it in modifyOptions"
                                :key="it.value"
                                :disabled="it.disabled"
                                :label="it.label"
                                :value="it.value"
                            ></abc-dropdown-item>
                        </abc-dropdown>
                    </abc-space>

                    <template v-else>
                        <abc-space v-if="viewMode === InventoryViewMode.BATCH">
                            <dialog-batch-update
                                :goods-id-list="goodsIdList"
                                :is-all-checked="isAllChecked"
                                :query-params="createListParams(true)"
                                :update-count="selectedCount"
                                :show-type-id-selector="showTypeIdSelector"
                                @success="handleUpdateSuccess"
                            >
                                <div>
                                    <abc-button :count="selectedCount" :disabled="!selectedCount">
                                        {{ selectedCount ? '填写修改内容' : '未选修改商品' }}
                                    </abc-button>
                                </div>
                            </dialog-batch-update>
                            <abc-button variant="ghost" @click="handleCancel">
                                取消
                            </abc-button>
                        </abc-space>

                        <abc-space v-if="viewMode === InventoryViewMode.BATCH_PRINT_PRICE_TAG">
                            <abc-button :count="selectedCount" :disabled="!selectedCount" @click="handleBatchPrintPriceTag">
                                {{ selectedCount ? '打印价签' : '未选择打印商品' }}
                            </abc-button>
                            <abc-button variant="ghost" @click="handleCancel">
                                取消
                            </abc-button>
                        </abc-space>
                    </template>
                </abc-flex>
            </abc-layout-header>
            <abc-layout-content @layout-mounted="handleMounted">
                <abc-table
                    ref="abcTable"
                    :custom-td-class="customTdClass"
                    :custom-tr-class="customTrClass"
                    :data-list="tableData"
                    :loading="loading"
                    :need-selected="false"
                    :render-config="renderConfig"
                    :tr-click-trigger-checked="true"
                    class="inventory-abc-table goods-table"
                    empty-content="暂无数据"
                    type="pro"
                    :custom-tr-key="customTrKey"
                    @changeAllChecked="onChangeAllChecked"
                    @changeChecked="onChangeChecked"
                    @handleClickTr="handleClickRow"
                    @sortChange="sortChangeHandler"
                >
                    <abc-flex slot="topHeader" class="goods-warning" justify="space-between">
                        <biz-mixed-selection-filter
                            v-model="model.warningKey"
                            type="radio"
                            :gap="16"
                            :options="warningList"
                            @change="handleChangeWarningType"
                        ></biz-mixed-selection-filter>
                    </abc-flex>
                    <template #clinicName="{ trData }">
                        <abc-table-cell>
                            <span class="ellipsis" style="text-overflow: clip;">
                                {{ trData.clinicName }}
                            </span>
                        </abc-table-cell>
                    </template>
                </abc-table>
            </abc-layout-content>
            <abc-layout-footer>
                <abc-pagination
                    :key="pageSizeList.join('-')"
                    :count="pageParams.count"
                    :page-sizes="pageSizeList"
                    :page-sizes-width="120"
                    :pagination-params="pageParams"
                    :show-total-page="true"
                    show-size
                    @current-change="pageTo"
                    @size-change="pageSizeChange"
                >
                    <abc-flex
                        v-if="panelData.count > 0 && !isBatchUpdateView"
                        slot="tipsContent"
                        style="margin-left: auto;"
                    >
                        <abc-space :size="4" style="flex-wrap: wrap;">
                            <abc-text theme="gray">
                                共
                            </abc-text>
                            <abc-text bold theme="black">
                                {{ panelData.saleCount }}
                            </abc-text>
                            <abc-text theme="gray">
                                个品种可售
                                <template v-if="showGoodsCost && !isVirtualPharmacy && (panelData.totalCostPrice ?? false)">
                                    ，总成本
                                </template>
                            </abc-text>

                            <abc-text v-if="showGoodsCost && !isVirtualPharmacy && (panelData.totalCostPrice ?? false)" bold theme="black">
                                {{ panelData.totalCostPrice | formatMoney }}
                            </abc-text>

                            <template v-if="panelData.totalPrice ?? false">
                                <abc-text theme="gray">
                                    ，售价
                                </abc-text>
                                <abc-text bold theme="black">
                                    {{ panelData.totalPrice | formatMoney }}
                                </abc-text>
                            </template>

                            <abc-text theme="gray">
                                ，医保数(国)
                            </abc-text>
                            <abc-text bold theme="black">
                                {{ shebaoMatchCount }}
                            </abc-text>
                            <abc-text theme="gray">
                                ，甲
                            </abc-text>
                            <abc-text bold theme="black">
                                {{
                                    panelData.medicalFeeGradeACount
                                }}({{ getMedicalFeeGradePercent(panelData.medicalFeeGradeACount) }})
                            </abc-text>
                            <abc-text theme="gray">
                                ，乙
                            </abc-text>
                            <abc-text bold theme="black">
                                {{
                                    panelData.medicalFeeGradeBCount
                                }}({{ getMedicalFeeGradePercent(panelData.medicalFeeGradeBCount) }})
                            </abc-text>
                            <abc-text theme="gray">
                                ，丙
                            </abc-text>
                            <abc-text bold theme="black">
                                {{
                                    panelData.medicalFeeGradeCCount
                                }}({{ getMedicalFeeGradePercent(panelData.medicalFeeGradeCCount) }})
                            </abc-text>

                            <div>
                                <social-catalog></social-catalog>
                            </div>
                        </abc-space>
                    </abc-flex>
                </abc-pagination>
            </abc-layout-footer>
        </abc-layout>
        <goods-detail
            v-if="showDetailDialog"
            v-model="showDetailDialog"
            :goods="currentGoods"
            :goods-id="currentGoods?.id"
            :goods-type="currentGoods?.type"
            :hidden-position="hiddenPosition"
            :init-tab-content="targetTabContent"
            :pharmacy-no="pharmacyNo"
            :pharmacy-type="pharmacyType"
            :selected-clinic-id="selectedClinicId"
            :stock-goods-id="currentGoods?.id"
            :sub-type="currentGoods?.subType"
            :view-type="1"
            @updateList="handleUpdateList"
        >
        </goods-detail>

        <add-goods-archives-dialog
            v-if="showAddDialog"
            v-model="showAddDialog"
            :goods-info="{
                // 自动发起gsp审批流
                gspReviewStarted: 1
            }"
            :pharmacy-type="pharmacyType"
            :type-id="currentTypeId"
            @close="closeDialogHandle"
            @updateList="handleUpdateList"
            @submit-gsp="handleSubmitGsp"
            @success="handleSuccessAdd"
        >
        </add-goods-archives-dialog>
    </div>
</template>

<script>
    import {
        mapGetters, mapActions,
    } from 'vuex';
    import {
        GoodsTypeEnum, GoodsTypeIdEnum,
        PharmacyTypeEnum,
    } from '@abc/constants';
    import GoodsAPI from 'api/goods';
    import {
        isNotNull,
        isNull, isNumber, moneyDigit, paddingMoney, parseTime,
    } from '@/utils';
    import { isEqual } from '@abc/utils';
    import Model from './model';
    import Presenter from './presenter';
    import PopoverStockCard from 'views/inventory/components/popover-stock-card.vue';

    import {
        PharmacyGoodsPresenterKey,
    } from '@/views-pharmacy/inventory/provideKeys';
    import InventoryDataPermission from 'views/inventory/mixins/inventory-data-permission';
    import SocialCatalog from 'views/inventory/social-catalog/social-catalog.vue';
    import GoodsDetail from 'views/inventory/goods/goods-detail.vue';
    import {
        count1, isChineseMedicine, percent,
    } from '@/filters';

    import DialogGoodsDetail from '@/views-pharmacy/gsp/frames/first-battalion/goods/dialog-goods-detail/index.vue';
    import * as tools from '@/views-pharmacy/common/tools';
    import PriceAdjustmentDialog
        from '@/views-pharmacy/inventory/frames/price-adjustment/components/price-adjustment-dialog';
    import PropertyAPI from 'api/property/index';
    import { debounce } from 'utils/lodash';

    const AddGoodsArchivesDialog = () => import('views/inventory/goods/archives/add.vue');
    // const DialogBatchSetting = () => import('../components/dialog-batch-setting.vue');
    import {
        PriceModifySourceType,
        PriceTypeName,
    } from 'views/common/inventory/constants';
    import { getUserLastTypeId } from '@/views-pharmacy/inventory/utils';
    import useClinicStorage, {
        CustomKeys,
        TableKeys,
    } from '@/hooks/business/use-clinic-storage';
    import PharmacyArchivesTable from '@/views-pharmacy/inventory/frames/goods/table-config';
    import { InventoryViewMode } from '@/views-pharmacy/inventory/constant';
    import { useSelectAllManager } from '@/views-pharmacy/inventory/hooks/useSelectAllManager';
    import BizMixedSelectionFilter from '@/components-composite/biz-mixed-selection-filter';
    const DialogBatchUpdate = () => import('../components/dialog-batch-update.vue');
    const FilterBar = () => import('./filter-bar.vue');
    import PriceFluctuationPopover from '@/views-pharmacy/inventory/frames/components/price-fluctuation-popover.vue';

    import MultipleCodePopover from '@/service/trace-code/components/multiple-code-popover.vue';
    // import PharmacyArchivesTable from '@/views-pharmacy/inventory/frames/goods/table-config';
    import OverflowFlexTagsWrapper from 'views/registration/components/overflow-flex-tags-wrapper.vue';
    import useIsOpenSocialChainAdmin from 'views/inventory/hooks/useIsOpenSocialChainAdmin';
    import PrintPriceTagDialog from 'views/inventory/components/print-price-tag-dialog';
    import { getMemberPriceRangeText } from 'views/inventory/goods/archives/utils';
    import { TargetType } from 'views/inventory/goods/archives/constant';
    import { usePriceTagSetupStatus } from 'views/settings/print-config/price-tag/hooks/price-tag-setup-status';
    import SettingAPI from 'api/settings';
    import { BizCustomHeader } from '@/components-composite/biz-custom-header';
    import TableHeaderAPI from 'views/statistics/core/api/table-header';
    import {
        ChainClinicAddArchivesDialog,
    } from '@/views-pharmacy/inventory/frames/components/dialog-add-archives-to-chain-clinic';

    export default {
        name: 'GoodsInventory',
        components: {
            BizMixedSelectionFilter,
            FilterBar,
            DialogBatchUpdate,
            GoodsDetail,
            AddGoodsArchivesDialog,
            SocialCatalog,
        },
        mixins: [
            InventoryDataPermission,
        ],
        inject: {
            $abcPage: {
                default: {},
            },
        },
        provide() {
            // ?data、computed可用
            this.model = new Model();
            if (this.isChainAdmin) {
                this.model.setParams('queryType', 1);
            }
            this.presenter = new Presenter(this.model, this);
            return {
                [PharmacyGoodsPresenterKey]: this.presenter,
            };
        },
        setup() {
            const {
                getStorage,
                setStorage,
            } = useClinicStorage();

            const { isSetupPriceTagTemplate } = usePriceTagSetupStatus();

            const {
                isAllChecked,
                includeMap,
                excludeMap,
                includeArr,
                excludeArr,
                submitArr,
                keepChecked,
                changeChecked,
                changeAllChecked,
                resetState,
            } = useSelectAllManager();

            const { isHaveClinicOpen } = useIsOpenSocialChainAdmin();

            return {
                getStorage,
                setStorage,

                isAllChecked,
                includeMap,
                excludeMap,
                includeArr,
                excludeArr,
                submitArr,
                keepChecked,
                changeChecked,
                changeAllChecked,
                resetState,

                isHaveClinicOpen,

                isSetupPriceTagTemplate,
            };
        },
        data() {
            return {
                GoodsTypeEnum,
                // !不允许在vue中直接this.model.xxx=xxx修改数据，双向绑定也不行
                model: {
                // ?数据查看Model类中定义
                },
                showCardName: '',
                currentHoverId: '',
                tableHeaderConfigKey: 'goods.pharmacy.goodsBasicInfo',
                clinicName: '全部门店',
                goodsAllTypes: [],
                loading: true,
                exportLoading: false,
                showAddDialog: false,
                showDetailDialog: false,
                showEyeAddDialog: false,
                showEyeDetailDialog: false,
                currentTypeId: '',
                currentGoods: null,
                currentGoodsIndex: -1,
                targetTabContent: '',
                tableMinHeight: 400,
                viewMode: InventoryViewMode.NORMAL,// 普通视图、批量修改视图
                batchModifyField: null,
                pageSizeList: [10, 50, 100],
                currentGoodsConfig: null,
            };
        },
        watch: {
            fetchParams: {
                handler(val) {
                    console.log('val=', val);
                    if (this.isChainAdmin) {
                        if (val.clinicId) {
                            this.getPharmacyClinicConfig(val.clinicId,
                            );
                        } else {
                            this.currentGoodsConfig = null;
                        }
                    }
                },
                deep: true,
            },
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'isChainAdmin',
                'isChainSubStore',
                'subClinics',
                'currentClinic',
                'currentPharmacy',
                'showSubSetPrice',
                'clinicBasicConfig',
                'profitClassificationList',
                'allowProfitWarnFlag',
                'goodsConfig',
                'isCanCreateGoodsArchivesInInventory',
                'isCanOperateGoodsAdjustPriceInInventory',
                'isEnableListingPrice',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isSupportPrintPriceTag() {
                return this.viewDistributeConfig.Inventory.isSupportPrintPriceTag;
            },
            isShowPrintPriceTagButton() {
                return this.isSupportPrintPriceTag && (this.isChainAdmin ? !!this.selectedClinicId : true);
            },
            isOpenSocial() {
                return this.isHaveClinicOpen || this.$abcSocialSecurity.isOpenSocial;
            },
            isSupportShebaoListingPrice() {
                return this.isEnableListingPrice;
            },
            InventoryViewMode() {
                return InventoryViewMode;
            },
            selectedCount() {
                return this.isAllChecked ? this.panelData.count - this.submitArr.length : this.submitArr.length;
            },
            goodsIdList() {
                return this.submitArr.map((item) => item.goodsId);
            },
            // 产品原话：批量修改档案信息时，若已选择的商品全部为【配方饮片】或【非配方饮片】填写修改内容时增加【一级分类】字段，选项仅有【配方饮片】【非配方饮片】两个值，确认修改后，已选择商品更改为指定类型
            showTypeIdSelector() {
                return this.submitArr.every((item) => item.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES) ||
                    this.submitArr.every((item) => item.typeId === GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES);
            },
            disabledPharmacySetPrice() {
                return this.operateGoodsAdjustPrice && !this.isCanOperateGoodsAdjustPriceInInventory;
            },
            disabledPharmacyArchives() {
                if (this.operateGoodsArchives) {
                    return !this.isCanCreateGoodsArchivesInInventory;
                }
                return !this.isAdmin;
            },
            contentText() {
                // 药店管家逻辑
                if (this.operateGoodsArchives) {
                    if (this.disabledPharmacyArchives) {
                        return this.isChainSubStore ? '请联系总部开启权限' : '无建档权限，可在 [设置-信息安全] 中设置';
                    }
                    return '';
                }

                // 诊所管家逻辑
                if (this.disabledPharmacyArchives) {
                    return this.isChainSubStore ? '请联系总部创建药品物资档案' : '无创建药品物资档案权限';
                }

                return '';
            },
            modifyOptions() {
                const _arr = [
                    {
                        value: 0,
                        label: '设置展示字段',
                    },
                ];
                return _arr;
            },
            operateGoodsAdjustPrice() {
                return this.viewDistributeConfig.Inventory.operateGoodsAdjustPrice;
            },
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            defaultGoodsTypeMap() {
                return this.viewDistributeConfig.Inventory.defaultGoodsTypeMap;
            },
            enableBarcodeDetector() {
                return !(this.showAddDialog || this.showDetailDialog);
            },
            materialText() {
                return `新建${this.viewDistributeConfig.Inventory.materialText}`;
            },
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            renderTypeList() {
                return {
                    displayNameRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <abc-flex
                                    title={row.displayName}
                                    align="center"
                                    justify="space-between"
                                    gap="small"
                                    style={{
                                        position: 'relative',
                                        'min-height': '20px',
                                        width: '100%',
                                    }}
                                >
                                    {
                                        this.isBatchUpdateView ? <abc-text theme="black" class="ellipsis">{row.displayName}</abc-text> :
                                            <span class="ellipsis">{row.displayName}</span>
                                    }

                                    <abc-flex gap="4">
                                        {
                                            row.v2DisableStatus || row.disable ? (
                                                <abc-tag-v2
                                                    shape="square"
                                                    size="small"
                                                    variant="ghost"
                                                    min-width={68}
                                                    style={{ 'white-space': 'nowrap' }}
                                                >
                                                    {row.v2DisableStatus === 20 || row.disable ? '禁售' : '可售'}
                                                </abc-tag-v2>
                                            ) : (row.prohibitPackageCount + row.prohibitPieceCount) ? (
                                                <abc-tag-v2
                                                    shape="square"
                                                    size="small"
                                                    variant="ghost"
                                                    min-width={68}
                                                    style={{ 'white-space': 'nowrap' }}
                                                >
                                                    部分停售
                                                </abc-tag-v2>
                                            ) : ''
                                        }
                                    </abc-flex>
                                </abc-flex>
                            </abc-table-cell>
                        );
                    },
                    typeRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div title={this.transGoodsClassificationName(row.goodsTypeName)}>{this.transGoodsClassificationName(row.goodsTypeName)}</div>
                            </abc-table-cell>
                        );
                    },
                    // specRender: (h, row) => {
                    //     return (
                    //         <abc-table-cell>
                    //             <div title={row.displaySpec}>{row.displaySpec}</div>
                    //         </abc-table-cell>
                    //
                    //     );
                    // },
                    outTaxRatRender: (h, row) => {
                        const displayValue = row?.outTaxRat ? `${row.outTaxRat}%` : '';
                        return (
                            <abc-table-cell>
                                <div title={displayValue}>{displayValue}</div>
                            </abc-table-cell>

                        );
                    },
                    inTaxRatRender: (h, row) => {
                        const displayValue = row?.inTaxRat ? `${row.inTaxRat}%` : '';
                        return (
                            <abc-table-cell>
                                <div title={displayValue}>{displayValue}</div>
                            </abc-table-cell>

                        );
                    },
                    // manufacturerRender: (h, row) => {
                    //     return (
                    //         <abc-table-cell>
                    //             <div title={row.manufacturer}>{row.manufacturer}</div>
                    //         </abc-table-cell>
                    //
                    //     );
                    // },
                    priceTypeRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div title={PriceTypeName[row.priceType] || ''}>{PriceTypeName[row.priceType] || ''}</div>
                            </abc-table-cell>
                        );
                    },
                    packagePriceRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div >{row.chainPackagePrice}</div>
                            </abc-table-cell>

                        );
                    },
                    lastPackageCostPriceRender: (h, trData) => {

                        const handleClick = (e) => {
                            this.handleClickTd(e, trData, this.getRowIndex(trData), 'GoodsBatch');
                        };
                        if (this.showGoodsCost) {
                            return (
                                <abc-table-cell>
                                    <PriceFluctuationPopover
                                        arrowOffset={1}
                                        disabled={true}
                                        goods={trData}
                                        clinicId={trData.clinicId}
                                        costPriceWarnData={{
                                            beforePackageCostPrice: trData.beforePackageCostPrice,
                                            packageCostPrice: trData.lastPackageCostPrice,
                                            costChangeMode: trData.costChangeMode,
                                            costChangeScale: trData.costChangeScale,
                                            costPriceWarnFlag: trData.costPriceWarnFlag,
                                        }}
                                        updatePriceSourceType={PriceModifySourceType.QUICK_MODIFY_INVENTORY}
                                    >
                                        {({
                                              isRise, showIcon,
                                          }) => (
                                            <AbcFlex align="center" gap={8} style={{ height: '100%' }}>
                                                {showIcon && (
                                                    isRise ? (
                                                        <AbcIcon icon="arrow_up" color="var(--abc-color-R2)"/>
                                                    ) : (
                                                        <AbcIcon icon="arrow_down" color="var(--abc-color-G2)"/>
                                                    )
                                                )}
                                                <div class={trData.costPriceWarnFlag ? 'warn-status' : ''}
                                                     onClick={handleClick}>{
                                                    isNull(trData.lastPackageCostPrice) ? '' : paddingMoney(trData.lastPackageCostPrice)
                                                }</div>
                                            </AbcFlex>
                                        )}
                                    </PriceFluctuationPopover>
                                </abc-table-cell>
                            );
                        }
                    },
                    positionRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div> {row.position}</div>
                            </abc-table-cell>

                        )
                        ;
                    },
                    profitCategoryRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div >{row.profitCategoryTypeName || ''}</div>
                            </abc-table-cell>
                        );
                    },
                    priceRender: (h, row, col) => {
                        const displayValue = this.fetchParams.isSpu ? row.clinicPackagePrice : row[col.prop];

                        const handleClick = (e) => {
                            this.handleClickTd(e, row, this.getRowIndex(row), 'GoodsDetailDis');
                        };
                        // 允许拆零才显示拆零价(中药不显示)
                        if (col.prop === 'piecePrice' && (isChineseMedicine(row) || !row.dismounting)) return '';
                        // 显示未定价
                        if ((col.prop === 'packagePrice' || col.prop === 'piecePrice') && isNull(row[col.prop])) {
                            return (<abc-table-cell class="ellipsis" theme="warning"><span>未定价</span></abc-table-cell>);
                        }
                        if (this.fetchParams.isSpu) {
                            return (<abc-table-cell class="ellipsis"><span onClick={handleClick}>{displayValue}</span></abc-table-cell>);
                        }
                        return (
                            <abc-table-cell class="ellipsis">
                                <span onClick={handleClick} >{isNull(displayValue) ? '' : paddingMoney(displayValue)}</span>
                            </abc-table-cell>
                        );
                    },
                    minExpiryDateRender: (h, row) => {
                        const handleClick = (e) => {
                            this.handleClickTd(e, row, this.getRowIndex(row), 'GoodsBatch');
                        };
                        return (
                            <abc-table-cell
                                onClick={handleClick}
                                title={row.minExpiryDate}
                                class={
                                row.expiredWarnFlag ? 'warn-status' : ''
                            }>
                                {row.minExpiryDate}
                            </abc-table-cell>
                        );
                    },
                    profitRatRender: (h, row) => {
                        const handleClick = (e) => {
                            this.handleClickTd(e, row, this.getRowIndex(row), 'GoodsDetailDis');
                        };
                        if (this.showGoodsProfit) {
                            return (
                                <abc-table-cell>
                                    <span class={
                                        row.negativeProfitWarnFlag ? 'warn-status' : ''
                                    } title={percent(row.profitRat)} onClick={handleClick}>
                                    {isNull(row.profitRat) ? '' : percent(row.profitRat)}</span>
                                </abc-table-cell>

                            );
                        }
                    },
                    // 当前库存（总库存）
                    currentCountRender: (h, row) => {
                        const key = this.fetchParams.isSpu ? 'spuGoodsId' : 'goodsId';
                        const val = this.fetchParams.isSpu ? row.spuGoodsId : row.goodsId;
                        return (
                            <abc-table-cell>
                                <abc-flex align="center" justify="flex-end"
                                    class={
                                        row.shortageWarnFlag ? 'warn-status' : ''
                                    }
                                >
                                    <abc-popover
                                        trigger="hover"
                                        ref="currentCountPopover"
                                        placement="bottom-start"
                                        theme="yellow"
                                        popper-style={{
                                            padding: '0px', 'min-width': '177px',
                                        }}
                                        open-delay={500}
                                        onShow={() => {
                                            this.currentHoverId = row.goodsId;
                                            this.showCardName = 'current';
                                        }}
                                        onHide={() => {
                                            this.currentHoverId = '';
                                        }}
                                        disabled={!!this.fetchParams.isSpu}
                                    >

                                    <span slot="reference" style="padding-right:2px;">
                                        {row.dispGoodsCount}
                                    </span>

                                        {this.showCardName === 'current' ? <PopoverStockCard
                                            row={row}
                                            rowKey={key}
                                            currentHoverId={this.currentHoverId}
                                            fetchParams={{
                                                [key]: val,
                                                clinicId: this.selectedClinicId,
                                                goodsId: row.goodsId,
                                                pharmacyNo: this.fetchParams.pharmacyNo,
                                            }}
                                        /> : null}

                                    </abc-popover>
                                </abc-flex>
                            </abc-table-cell>

                        );
                    },
                    // 可售数量
                    stockCountRender: (h, row) => {
                        const handleClick = (e) => {
                            if (!this.isChainAdmin) return;
                            this.handleClickTd(e, row, this.getRowIndex(row), 'GoodsDetailDis');
                        };
                        return (
                            <abc-table-cell>
                                <abc-flex
                                    align="center"
                                    justify="flex-end"
                                    class={
                                        row.shortageWarnFlag ? 'warn-status' : ''
                                    }
                                    style="align-items: center;display: inline-flex"
                                    onClick={handleClick}
                                >
                                    <abc-popover
                                        trigger="hover"
                                        ref="stockCountPopover"
                                        placement="bottom-start"
                                        theme="yellow"
                                        popper-style={{
                                            padding: '0px', 'min-width': '177px',
                                        }}
                                        onShow={() => {
                                            this.currentHoverId = row.goodsId;
                                            this.showCardName = 'stock';
                                        }}
                                        onHide={() => {
                                            this.currentHoverId = '';
                                        }}

                                    >

                                    <span slot="reference" style="padding-right:2px;">
                                        {row.dispStockGoodsCount}
                                    </span>

                                        {this.showCardName === 'stock' ? <PopoverStockCard
                                            row={row}
                                            rowKey="goodsId"
                                            isStockCount
                                            currentHoverId={this.currentHoverId}
                                            fetchParams={{
                                                clinicId: this.selectedClinicId,
                                                goodsId: row.goodsId,
                                                pharmacyNo: this.fetchParams.pharmacyNo,
                                            }}
                                        /> : null}

                                    </abc-popover>
                                </abc-flex>
                            </abc-table-cell>

                        );
                    },
                    totalCostRender: (h, row) => {
                        if (this.showGoodsCost) {
                            return (
                            <abc-table-cell>
                                <div title={moneyDigit(row.totalCost)}>
                                    {moneyDigit(row.totalCost)}</div>
                            </abc-table-cell>);
                        }
                    },
                    lastMonthSellCountRender: (h, row) => {
                        const formatHandler = this.formatLastMonthSellCount;
                        const handleClick = (e) => {
                            this.handleClickTd(e, row, this.getRowIndex(row), 'GoodsDetailDis');
                        };
                        return (
                            <abc-table-cell>
                                <div class={
                                    row.unsalableWarnFlag ? 'warn-status' : ''
                                } onClick={handleClick} title={formatHandler(row)}>
                                    {formatHandler(row)}</div>
                            </abc-table-cell>

                        );
                    },
                    turnoverDaysRender: (h, row) => {
                        const handleClick = (e) => {
                            this.handleClickTd(e, row, this.getRowIndex(row), 'GoodsDetailDis');
                        };
                        return (
                            <abc-table-cell>
                                <span
                                    onClick={handleClick}
                                    class={row.turnoverDaysWarnFlag ? 'warn-status' : ''}>
                                {isNull(row.turnoverDays) ? '-' : `${row.turnoverDays}天`}</span>
                            </abc-table-cell>

                        );
                    },

                    shortIdRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div > {row.shortId}</div>
                            </abc-table-cell>

                        );
                    },

                    medicineNmpnRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div>{row.medicineNmpn || row.medcineNpmn}</div>
                            </abc-table-cell>

                        );
                    },

                    lastSupplierNameRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div> {row.lastSupplierName}</div>
                            </abc-table-cell>

                        );

                    },

                    brandRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div title={row.brandName}>{row.brandName}</div>
                            </abc-table-cell>

                        );
                    },
                    clinicPriceRender: (h, row) => {
                        const price = this.fetchParams.isSpu ? row.clinicPackagePrice : row.packagePrice;
                        return (
                            <abc-table-cell>
                                <div

                                    title={price}
                                >{price}</div>
                            </abc-table-cell>

                        );
                    },
                    shebaoCodeRender: (h, row) => {
                        const { shebaoNationalView } = row;
                        let isWarning = false;
                        const {
                            specificationMatchStatus,
                            medicineNmpnMatchStatus,
                        } = this.formatShebaoView(shebaoNationalView);
                        if (specificationMatchStatus || medicineNmpnMatchStatus) {
                            isWarning = true;
                        }
                        return (
                            <abc-table-cell>
                                <abc-popover
                                    placement="top"
                                    trigger="hover"
                                    theme="yellow"
                                    disabled={this.formatShebaoView(shebaoNationalView).disabledPopover}
                                >
                                    <div
                                        slot="reference"
                                        class={ this.formatShebaoView(shebaoNationalView).warning || isWarning ? 'warn-status' : ''}
                                    >{this.formatShebaoView(shebaoNationalView).shebaoText}</div>
                                    <abc-flex vertical gap="small">
                                        {this.formatShebaoView(shebaoNationalView).shebaoCode}
                                        {
                                            isWarning ?
                                                <abc-flex align="baseline" gap="small">
                                                    <abc-icon icon="n-alert-fill" size="12" color="var(--abc-color-OR1)"></abc-icon>
                                                    <abc-flex vertical gap="small">
                                                        <abc-text size="mini" style={{ color: 'var(--abc-color-OR1)' }}>合规风险</abc-text>
                                                        {medicineNmpnMatchStatus === 1 && <abc-text size="mini" style={{ color: 'var(--abc-color-Y6)' }}>ABC 系统档案与医保目录准字不一致</abc-text>}
                                                        {specificationMatchStatus === 1 && <abc-text size="mini" style={{ color: 'var(--abc-color-Y6)' }}>ABC 系统档案与医保目录规格无法对应</abc-text>}
                                                    </abc-flex>
                                                </abc-flex> : ''
                                        }
                                    </abc-flex>
                                </abc-popover>
                            </abc-table-cell>


                        );
                    },
                    standardCodeRender: (h, row) => {
                        const { shebaoNationalView } = row;
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                    }}
                                    title={shebaoNationalView?.standardCode ?? ''}
                                >{shebaoNationalView?.standardCode ?? ''}</div>
                            </abc-table-cell>
                        );
                    },
                    listingPriceRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                        'is-disabled': row.v2DisableStatus,
                                        'warn-status': shebaoInfo?.overListingPriceWarnFlag,
                                    }}
                                    title={shebaoInfo?.listingPrice ?? ''}
                                >{shebaoInfo?.listingPrice ?? ''}</div>
                            </abc-table-cell>
                        );
                    },
                    medicalFeeGradeNameRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        return (
                            <abc-table-cell>
                                <div
                                    class={
                                        row.shebaoCodeWarnFlag ? 'warn-status' : ''
                                    }
                                    title={shebaoInfo?.medicalFeeGradeName}
                                >{shebaoInfo?.medicalFeeGradeName}</div>
                            </abc-table-cell>

                        );
                    },
                    barCodeRender: (h, row) => {
                        return (
                            <abc-table-cell>
                            <div title={row?.barCode}
                                >{row?.barCode}</div>
                            </abc-table-cell>

                        );
                    },
                    // 限制说明
                    restrictionRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        return (
                            <abc-table-cell>
                                <div title={shebaoInfo?.restriction}
                                >{shebaoInfo?.restriction}</div>
                            </abc-table-cell>

                        );
                    },

                    // 限价
                    shebaoCodePriceLimitedRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        return (
                            <abc-table-cell>
                                <div
                                    class={{
                                         'warn-status ': shebaoInfo?.shebaoOverPriceWarnFlag ,
                                        'ellipsis': true,
                                    }}
                                    title={shebaoInfo?.shebaoCodeCurrentPriceLimited}
                                >{shebaoInfo?.shebaoCodeCurrentPriceLimited}</div>
                            </abc-table-cell>
                        );
                    },
                    // 到期时间
                    shebaoCodeEndDateRender: (h, row) => {
                        const shebaoInfo = row.shebaoNationalView;
                        const endDate = shebaoInfo?.shebaoCodeCurrentEndDate ? parseTime(shebaoInfo?.shebaoCodeCurrentEndDate, 'y-m-d', true) : '';
                        return (
                            <abc-table-cell>
                                <span title={endDate}>{endDate}</span>
                            </abc-table-cell>
                        );
                    },
                    // 医保支付
                    shebaoCodePayModeRender: (h, row) => {
                        return (
                            <abc-table-cell>
                                <div
                                    title={row?.shebaoPayModeName}
                                >{row?.shebaoPayModeName}</div>
                            </abc-table-cell>

                        );
                    },
                    // 产品标识码
                    drugIdentificationCodeRender: (h, row) => {
                        if (!row.drugIdentificationCode) return '';

                        return (
                            <abc-table-cell>
                                <MultipleCodePopover
                                    goodsIdentificationCodeList={row.drugIdentificationCode?.split(',')}
                                    style="width:100%;"
                                >
                                </MultipleCodePopover>
                            </abc-table-cell>
                        );
                    },
                    goodsTagRender: (h, row) => {
                        const goodsTagList = (row.goodsTagList || []).map((item, index) => ({
                            tagId: index,
                            tagName: item.name,
                            viewMode: 0,
                        }));
                        return goodsTagList.length ?
                            <abc-table-cell>
                                <OverflowFlexTagsWrapper tags={goodsTagList} variant="outline" size="tiny"/>
                            </abc-table-cell> : '';
                    },
                    memberPriceRender: (h, row) => {
                        const memberPriceList = row.multiPriceList?.filter((priceInfo) => priceInfo.targetType === TargetType.vip) || [];
                        const text = getMemberPriceRangeText(memberPriceList, 4, false, false);
                        return <abc-table-cell>
                            <span title={text}>{text}</span>
                            </abc-table-cell>;
                    },
                    firstInDateRender: (h, row) => {
                        const dateStr = row.firstInDate ? parseTime(row.firstInDate, 'y-m-d', true) : '';
                        return (
                            <abc-table-cell>
                                <span title={dateStr}>{dateStr}</span>
                            </abc-table-cell>

                        );
                    },
                    stockWarnRuleRender: (h, row) => {
                        const {
                            shortageWarnEnable, warnStockCount, configTurnoverDays, shortageWarnCountSmallUnit, pieceUnit = '', packageUnit = '',
                        } = row;
                        let str = '';
                        if (shortageWarnEnable) {
                            str += `库存不足${warnStockCount}${shortageWarnCountSmallUnit ? pieceUnit : packageUnit}时`;
                        } else {
                            str += `周转天数不足${configTurnoverDays}天时`;
                        }
                        return (
                            <abc-table-cell class="ellipsis" title={str}>
                                <div class="ellipsis">{ str }</div>
                            </abc-table-cell>
                        );
                    },
                };
            },
            renderConfig() {
                const _tableConfig = new PharmacyArchivesTable();
                // 对表头进行过滤
                const renderHeader = (this.panelData.goodsHeader || []).filter((item) => {
                    if (item.key === 'goodsBasicInfo.position') {
                        // 总部选择了门店才展示柜号
                        if (this.isChainAdmin) return this.selectedClinicId;

                        return true;
                    }

                    if (item.key === 'goodsBasicInfo.clinicName') {
                        // 总部选择了门店才展示柜号
                        if (this.isChainAdmin) return this.fetchParams.queryType === 2;

                        return false;
                    }

                    return true;
                });

                return _tableConfig.createRenderConfig(renderHeader, this.renderTypeList, this.viewMode);
            },
            hiddenPosition() {
                // 汇总库房不显示柜号
                return isNull(this.pharmacyNo);
            },
            hasEyeglasses() {
                return this.viewDistributeConfig.Inventory.hasEyeglasses;
            },
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            pharmacyType() {
                return this.currentPharmacy?.type;
            },
            isVirtualPharmacy() {
                return this.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            isBatchUpdateView() {
                return [InventoryViewMode.BATCH, InventoryViewMode.BATCH_PRINT_PRICE_TAG].includes(this.viewMode);
            },
            warningList() {
                const {
                    unGspWarnCount,
                    stockWarnCount,
                    profitRatWarnCount,
                    expiredWarnCount,
                    unPriceWarnCount,
                    unsalableWarnCount,
                    costPriceWarnCount,
                    stockWarnTurnoverDays,
                    shebaoNationalInvalidCount,
                    shebaoNationalWarnCount,
                    shebaoOverPriceCount,
                    shebaoOverListingPriceCount,
                    unPrintPriceWarnCount,
                } = this.panelData;

                const stockWarnCountAndTurnoverDays = (stockWarnCount || 0) + (stockWarnTurnoverDays || 0);

                if (this.viewMode === InventoryViewMode.BATCH_PRINT_PRICE_TAG) {
                    return [
                        {
                            label: '待打印价签',
                            value: 'unPrintPriceWarn',
                            statisticsNumber: unPrintPriceWarnCount || 0,
                        },
                    ];
                }
                const goodsConfig = (this.isChainAdmin ? (this.currentGoodsConfig || this.goodsConfig || {}) : (this.goodsConfig || {}));
                const {
                    stockGoodsConfig = {},
                } = goodsConfig;
                const {
                    stockWarnGoodsTurnoverDays = 0,
                    stockWarnGoodsWillExpiredMonth = 0, costPriceWarnPercent = 0, unsalableWarnDays = 0,
                    profitWarnFlag = 0, costPriceWarnType = 3,
                } = stockGoodsConfig || {};
                const stockWarnCountAndTurnoverDaysStr = stockWarnGoodsTurnoverDays;
                const expiredMonthStr = stockWarnGoodsWillExpiredMonth;
                const unsalableDaysStr = unsalableWarnDays;
                const costPricePercentStr = costPriceWarnPercent;
                const costPriceTypeStr = costPriceWarnType;
                const costPriceTypeMap = {
                    3: '上涨或下跌',
                    1: '上涨',
                    2: '下跌',
                };

                const warningList = [
                    {
                        label: '未首营',
                        value: 'unGsp',
                        statisticsNumber: unGspWarnCount || 0,
                        hidden: this.isChainAdmin ? this.fetchParams.queryType === 2 : false,
                    },
                    {
                        label: '库存预警',
                        value: 'stock',
                        statisticsNumber: stockWarnCountAndTurnoverDays,
                        tipsText: `周转天数小于${stockWarnCountAndTurnoverDaysStr}天的商品`,
                    },
                    {
                        label: '效期预警',
                        value: 'expired',
                        statisticsNumber: expiredWarnCount || 0,
                        tipsText: `有效期不足${expiredMonthStr}个月的商品`,
                    },
                    {
                        label: '毛利异常',
                        value: 'profit',
                        statisticsNumber: profitRatWarnCount || 0,
                        hidden: !this.allowProfitWarnFlag,
                        tipsText: `${profitWarnFlag & 0x01 ? '毛利率为负' : ''}${((profitWarnFlag & 0x01) && (profitWarnFlag & 0x02)) ? '或' : ''}${profitWarnFlag & 0x02 ? '超出利润分类毛利率范围' : ''}的商品`,
                    },
                    {
                        label: '滞销商品',
                        value: 'unsalable',
                        statisticsNumber: unsalableWarnCount || 0,
                        tipsText: `购进超过30天，且近${unsalableDaysStr}天销量为0的商品`,
                    },
                    {
                        label: '进价异动',
                        value: 'costPrice',
                        statisticsNumber: costPriceWarnCount || 0,
                        tipsText: `最近进价${costPriceTypeMap[costPriceTypeStr] || '上涨或下跌'}超过${costPricePercentStr}%的商品`,
                    },
                    {
                        label: '未定价',
                        value: 'unPrice',
                        statisticsNumber: unPriceWarnCount || 0,
                    },
                    {
                        label: '对码已失效',
                        value: 'sbNationalInvalid',
                        statisticsNumber: shebaoNationalInvalidCount || 0,
                    },
                    {
                        label: '对码风险',
                        value: 'sbNationalWarn',
                        statisticsNumber: shebaoNationalWarnCount || 0,
                        tipsText: '档案与医保目录准字不一致的商品',
                    },
                    {
                        label: '超限价',
                        value: 'sbOverPrice',
                        statisticsNumber: shebaoOverPriceCount || 0,
                        hidden: !this.isOpenSocial,
                    },
                    {
                        label: '超挂网价',
                        value: 'sbOverListingPrice',
                        statisticsNumber: shebaoOverListingPriceCount || 0,
                        hidden: !this.isSupportShebaoListingPrice,
                    },
                    {
                        label: '待打印价签',
                        value: 'unPrintPriceWarn',
                        statisticsNumber: unPrintPriceWarnCount || 0,
                        hidden: !this.isSetupPriceTagTemplate || (this.isChainAdmin && !this.selectedClinicId),
                    },
                ].filter((e) => !e.hidden);

                return warningList;

            },
            fetchParams() {
                return this.model.fetchParams || {};
            },
            panelData() {
                return this.model.panelData || {};
            },
            pageParams() {
                const {
                    limit: pageSize, offset,
                } = this.fetchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    showTotalPage: true,
                    pageIndex,
                    pageSize,
                    count: this.panelData.count,
                };
            },
            tableData() {
                return this.panelData.rows ?? [];
            },
            // 国 医保数
            shebaoMatchCount() {
                return this.panelData.shebaoNationalMatchCount;
            },
            // 未对码数
            shebaoNotMatchCount() {
                return this.panelData.shebaoNationalNotMatchCount;
            },
            // 社保对码失效数量
            shebaoNationalInvalidCount() {
                return this.panelData.shebaoNationalInvalidCount || 0;
            },
            // 已超限价数
            shebaoNationalOverPriceCount() {
                return this.panelData.shebaoNationalOverPriceCount;
            },
            // 即将超限价数
            shebaoNationalGoingOverPriceCount() {
                return this.panelData.shebaoNationalGoingOverPriceCount;
            },
            // 选中的门店id
            selectedClinicId: {
                get() {
                    return this.fetchParams.clinicId || '';
                },
                set(val) {
                    this.presenter.setParams('clinicId', val);
                },
            },
            // 连锁总部选了门店之后都只能筛选出该门店入库的药品
            // 连锁总部的全部门店 和 单店 能筛选所有的药品
            // 在连锁总部选择门店之后，只能走后台搜索，本地的药品数据只初始化了本地的数据
            searchOnlyStock() {
                if (this.isChainAdmin && this.selectedClinicId) {
                    return true;
                }
                // return !this.isAdmin;
                // 现在药店子店引进档案的方式不只是入库才加，还可以直接添加，所以搜索这里不带onlyStock条件
                return false;
            },
            currentKey: {
                get() {
                    return this.model.searchKey;
                },
                set(val) {
                    this.presenter.setState('searchKey', val);
                },
            },
            currentSelectedTypes: {
                get() {
                    return this.model.selectedTypes || [];
                },
                set(v) {
                    this.presenter.setState('selectedTypes', v);
                },
            },
            sbNationalInvalid: {
                get() {
                    return this.fetchParams.sbNationalInvalid;
                },
                set(val) {
                    this.presenter.setParams('sbNationalInvalid', val);
                },
            },
            sbNationalWarn() {
                return this.model.warningKeys.includes('sbNationalWarn');
            },

            // 当前选中typeList
            selectedTypeIds() {
                const typeIds = [];
                this.currentSelectedTypes.forEach((it) => {
                    const _type = it[0];
                    if (_type && typeIds.indexOf(_type.id) === -1) {
                        typeIds.push(_type.id);
                    }
                });
                return typeIds;
            },
        },
        created() {
            this.model.setParams('limit', this.getStorage(CustomKeys.CLINIC_INVENTORY_PAGE_SIZE, TableKeys.inventoryGoodsTable) || 50);
            // 拉数据要用节流的，
            this._fetchData = debounce(this.fetchData, 500, true);

            if (!this.isChainAdmin) {
                this.selectedClinicId = this.currentClinic.clinicId;
            }
            //拉取所有门店数据
            this.$store.dispatch('fetchChainSubClinics');
            this.$store.dispatch('fetchGoodsPrimaryClassificationIfNeed');
            this.updateTagsByFetch();
        },
        async mounted() {
            await this.getEmployeeStockConfig();
            this.fetchAllApprovalList();
        },
        methods: {
            isNotNull,
            ...mapActions([
                'fetchAllApprovalList',
            ]),
            ...mapActions('goods', ['updateTagsByFetch']),
            async getPharmacyClinicConfig(clinicId) {
                try {
                    const res = await SettingAPI.clinicGoodsConfig.getClinicGoodsConfig({
                        clinicId,
                    });
                    this.currentGoodsConfig = res?.data || {};
                } catch (err) {
                    console.log(err);
                }
            },
            getRowIndex(row) {
                return this.tableData.findIndex((item) => item.goodsId === row.goodsId);
            },
            onChangeAllChecked(checked) {
                console.log('checked', checked);
                this.changeAllChecked(checked);
            },
            onChangeChecked(item) {
                console.log('checked item', item);
                this.changeChecked(item, 'goodsId');
            },
            handleSettingChange(val) {
                if (val === 0) {
                    this.handleCustomDisplay();

                }
                // if (val === BatchModifyEnum.CLASSIFICATION && this.selectedTypeIds.length !== 1) {
                //     this.$alert({
                //         type: 'warn',
                //         title: '提示',
                //         content: '选择的商品未指定一级分类或包含多个一级分类，不支持批量修改二级分类，请重新选择后设置',
                //     });
                //     return;
                // }
                // this.batchModifyField = val;
                // this.showBatchSettingDialog = true;
            },
            async getEmployeeStockConfig() {
                const { data } = await PropertyAPI.getV3('clinicEmployeeStockSettings', 'cli_emp');
                const disableMapConfig = {
                    1: 0,
                    0: undefined,
                    2: 1,
                    3: '0,1',
                };
                const stockMapConfig = {
                    1: 1,
                    0: 0,
                    2: 2,
                    3: '1, 2',
                };
                if (data?.list) {
                    this.presenter.setParams('onlyStock', stockMapConfig[data.list.onlyStock]);
                    this.presenter.setParams('disable', disableMapConfig[data.list.disable]);
                }
            },
            formatShebaoView(shebaoNationalView = {}) {
                const {
                    shebaoCode,
                    shebaoCodeWarnFlag,
                } = shebaoNationalView;
                let shebaoText = '';
                if (!isNull(shebaoCode)) {
                    shebaoText = '已对码';
                }
                if (shebaoCode === '未对码') {
                    shebaoText = '未对码';
                }
                if (shebaoCode === '不允许医保支付') {
                    shebaoText = '不刷医保';
                }

                return {
                    shebaoText,
                    warning: shebaoCode === '未对码' || shebaoCode === '不允许医保支付' || !!shebaoCodeWarnFlag,
                    disabledPopover: isNull(shebaoCode) || shebaoCode === '未对码' || shebaoCode === '不允许医保支付',
                    ...shebaoNationalView,
                };
            },
            async updateEmployeeStockConfig() {
                // 停用状态映射配置
                const disableMapConfig = {
                    0: 1, // 未停用
                    undefined: 0, // 全部
                    1: 2, // 已停用
                    '0,1': 3, // 未停用和已停用
                };
                const stockMapConfig = {
                    1: 1, // 有库存
                    0: 0, // 全部
                    2: 2, // 无库存
                    '1,2': 3, // 有库存和无库存
                };
                // 处理库存状态值
                const onlyStockValue = this.fetchParams?.onlyStock;
                let mappedStockValue = 0; // 默认为全部

                if (onlyStockValue !== undefined && onlyStockValue !== 0 && onlyStockValue !== '0') {
                    mappedStockValue = `${onlyStockValue}`.length > 1 ? 3 : stockMapConfig[Number(onlyStockValue)];
                }
                // 处理停用状态值
                const disableValue = this.fetchParams?.disable;
                let mappedDisableValue = 0; // 默认为全部
                if (disableValue !== undefined) {
                    mappedDisableValue = `${disableValue}`.length > 1 ? 3 : disableMapConfig[Number(disableValue)];
                }
                await PropertyAPI.updateV3('clinicEmployeeStockSettings', 'cli_emp', {
                    list: {
                        onlyStock: mappedStockValue,
                        disable: mappedDisableValue,
                    },
                });
            },
            createListParams(needMoreParam = false, isPriceAdjust = false) {
                const params = {};
                const fetchParams = this.createParams();
                if (needMoreParam) {
                    if (fetchParams.keyword) {
                        params.keyword = fetchParams.keyword;
                    }
                    if (fetchParams.clinicId) {
                        params.clinicId = fetchParams.clinicId;
                    }
                    if (fetchParams.goodsId) {
                        params.goodsId = fetchParams.goodsId;
                    }
                    if (fetchParams.unGspWarn) {
                        params.unGspWarn = fetchParams.unGspWarn;
                    }
                }

                if (fetchParams.typeId.length) {
                    params.typeId = fetchParams.typeId;
                }
                if (fetchParams.customTypeId.length) {
                    params.customTypeId = fetchParams.customTypeId;
                }
                if (fetchParams.minProfitRat) {
                    params.minProfitRat = fetchParams.minProfitRat;
                }
                if (fetchParams.maxProfitRat) {
                    params.maxProfitRat = fetchParams.maxProfitRat;
                }
                if (fetchParams.stockWarn) {
                    params.stockWarn = fetchParams.stockWarn;
                }
                if (fetchParams.expiredWarn) {
                    params.expiredWarn = fetchParams.expiredWarn;
                }
                if (fetchParams.profitWarn) {
                    params.profitWarn = fetchParams.profitWarn;
                }
                if (fetchParams.unsalableWarn) {
                    params.unsalableWarn = fetchParams.unsalableWarn;
                }
                if (fetchParams.costPriceWarn) {
                    params.costPriceWarn = fetchParams.costPriceWarn;
                }
                if (fetchParams.unPriceWarn) {
                    params.unPriceWarn = fetchParams.unPriceWarn;
                }
                if (fetchParams.profitCategoryTypeList?.length) {
                    params.profitCategoryTypeList = fetchParams.profitCategoryTypeList;
                }
                if (fetchParams.sbNationalMatched) {
                    params.sbNationalMatched = fetchParams.sbNationalMatched;
                }
                if (fetchParams.sbNationalNotMatched) {
                    params.sbNationalNotMatched = fetchParams.sbNationalNotMatched;
                }
                if (fetchParams.sbNationalNotPermit) {
                    params.sbNationalNotPermit = fetchParams.sbNationalNotPermit;
                }
                if (fetchParams.sbGoingOverPrice) {
                    params.sbGoingOverPrice = fetchParams.sbGoingOverPrice;
                }
                if (fetchParams.sbNotOverPrice) {
                    params.sbNotOverPrice = fetchParams.sbNotOverPrice;
                }
                if (fetchParams.sbOverPrice) {
                    params.sbOverPrice = fetchParams.sbOverPrice;
                }
                if (fetchParams.medicalFeeGrade?.length) {
                    params.medicalFeeGrade = fetchParams.medicalFeeGrade;
                }
                if (fetchParams.shebaoPayMode?.length) {
                    params.shebaoPayMode = fetchParams.shebaoPayMode;
                }
                if (fetchParams.remark) {
                    params.remark = fetchParams.remark;
                }


                if (isPriceAdjust) {
                    if (fetchParams.hasBarCode !== undefined) {
                        params.hasBarCode = `${fetchParams.hasBarCode}`?.length === 1 ? [Number(fetchParams.hasBarCode)] : (fetchParams.hasBarCode?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                    }
                    if (fetchParams.onlyStock !== undefined && fetchParams.onlyStock !== 0 && fetchParams.onlyStock !== '0') {
                        params.onlyStock = `${fetchParams.onlyStock}`?.length === 1 ? [Number(fetchParams.onlyStock)] : (fetchParams.onlyStock?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                    }
                    if (fetchParams.disable !== undefined) {
                        params.disable = `${fetchParams.disable}`?.length === 1 ? [Number(fetchParams.disable)] : (fetchParams.disable?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                    }
                    if (fetchParams.memberPriceFlag !== undefined) {
                        params.memberPriceFlag = `${fetchParams.memberPriceFlag}`?.length === 1 ? [Number(fetchParams.memberPriceFlag)] : (fetchParams.memberPriceFlag?.split(',')?.map((i) => {
                            return Number(i);
                        }) || []);
                    }
                    if (fetchParams.otcType !== undefined) {
                        params.otcType = this.fetchParams.otcType;
                    }
                } else {
                    if (fetchParams.hasBarCode !== undefined) {
                        params.hasBarCode = fetchParams.hasBarCode;
                    }
                    if (fetchParams.onlyStock) {
                        params.onlyStock = fetchParams.onlyStock;
                    }
                    if (fetchParams.disable !== undefined) {
                        params.disable = fetchParams.disable;
                    }
                    if (fetchParams.memberPriceFlag !== undefined) {
                        params.memberPriceFlag = fetchParams.memberPriceFlag;
                    }
                }

                if (fetchParams.position) {
                    params.position = fetchParams.position;
                }
                if (fetchParams.storage) {
                    params.storage = fetchParams.storage;
                }
                if (fetchParams.hasDrugIdentificationCode !== undefined) {
                    params.hasDrugIdentificationCode = fetchParams.hasDrugIdentificationCode;
                }
                if (fetchParams.unPrintPriceWarn) {
                    params.unPrintPriceWarn = fetchParams.unPrintPriceWarn;
                }
                return params;
            },
            createParams() {
                const params = {
                    ...this.fetchParams,
                    unGspWarn: this.model.warningKeys.includes('unGsp') ? 1 : undefined,
                    profitWarn: this.model.warningKeys.includes('profit') ? 1 : undefined,
                    stockWarn: this.model.warningKeys.includes('stock') ? 1 : undefined,
                    expiredWarn: this.model.warningKeys.includes('expired') ? 1 : undefined,
                    costPriceWarn: this.model.warningKeys.includes('costPrice') ? 1 : undefined,
                    unPriceWarn: this.model.warningKeys.includes('unPrice') ? 1 : undefined,
                    unsalableWarn: this.model.warningKeys.includes('unsalable') ? 1 : undefined,
                    sbNationalInvalid: this.model.warningKeys.includes('sbNationalInvalid') ? 1 : undefined,
                    sbNationalWarn: this.model.warningKeys.includes('sbNationalWarn') ? 1 : undefined,
                    sbOverPrice: this.model.warningKeys.includes('sbOverPrice') ? 1 : undefined,
                    sbOverListingPrice: this.model.warningKeys.includes('sbOverListingPrice') ? 1 : undefined,
                    unPrintPriceWarn: this.model.warningKeys.includes('unPrintPriceWarn') ? 1 : undefined,
                };
                delete params.isSpu;
                delete params.spuGoodsCondition;
                return params;
            },
            formatLastMonthSellCount(item) {
                if (!item) return '';
                const unit = isChineseMedicine(item) ? item.pieceUnit : item.packageUnit;
                return isNull(item.lastMonthSellCount) ? '' : (count1(item.lastMonthSellCount) + unit);
            },
            async fetchData() {
                try {
                    this.loading = true;
                    const params = this.createParams();
                    const res = await GoodsAPI.goodsList(params);
                    const afterParams = this.createParams();

                    if (isEqual(params, afterParams) && res?.data) {

                        // 控制数据状态
                        this.keepChecked(res.data?.rows ?? [], 'goodsId');
                        this.presenter.setState('panelData', res.data);
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                    this.$nextTick(() => {
                        this.$refs.abcTable.calcTableLayout();
                    });
                }

            },
            async handleExport() {
                try {
                    this.exportLoading = true;
                    const params = this.createParams();
                    const date = `${parseTime(new Date(), 'y-m-d', true)}.xlsx`;
                    await GoodsAPI.exportGoodsList(params, `商品列表_${date}`);
                } finally {
                    this.exportLoading = false;
                }

            },
            async updateData() {
                // 查询当前goods最新列表数据
                try {
                    this.loading = true;
                    const beforeParams = this.createParams();
                    beforeParams.goodsId = this.currentGoods.goodsId;
                    const { data } = await GoodsAPI.goodsList(beforeParams);

                    const item = data?.rows?.[0];

                    // 查到数据就更新（只有修改才会调取，按道理这里99%都能查询到），没有就走老逻辑请求列表数据。
                    if (item) {
                        const rows = [...this.panelData.rows];
                        rows.splice(this.currentGoodsIndex, 1, item);

                        this.keepChecked(rows, 'goodsId');
                        this.presenter.setState('panelData', {
                            ...this.panelData,
                            rows,
                        });
                    } else {
                        await this.fetchData();
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            handleChangeClinic(value) {
                const clinic = this.currentSubClinics.find((item) => item.value === value);
                this.clinicName = clinic?.label;
                this.presenter.setParams('clinicId', clinic?.value);
            },
            handleClickBatchAdjustPrice() {
                let clinicId = '';
                if (this.clinicName !== '全部门店' && this.clinicName !== '本店') {
                    clinicId = this.selectedClinicId;
                }
                new PriceAdjustmentDialog({
                    clinicId,
                    listParams: this.createListParams(false, true),
                    formGoodsList: true,
                    selectedTypes: this.currentSelectedTypes,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            handleClickBatchUpdateArchives() {
                this.viewMode = InventoryViewMode.BATCH;
            },
            handleClickBatchPrintPriceTag() {
                this.viewMode = InventoryViewMode.BATCH_PRINT_PRICE_TAG;
            },
            handleCustomDisplay() {
                const {
                    addressProvinceId,
                    addressCityId,
                    addressDistrictId,
                } = this.clinicBasicConfig;
                const regionId = `${addressProvinceId}_${addressCityId}_${addressDistrictId}`;

                new BizCustomHeader({
                    value: true,
                    tableKey: this.tableHeaderConfigKey,
                    titleName: '商品列表',
                    mode: 'draggle',
                    regionId,
                    finishFunc: this.fetchData,
                    tableHeaderApi: TableHeaderAPI,
                }).generateDialog({ parent: this });
            },

            //  医保对码 medicalFeeGradePercent(甲乙丙) 类百分比
            getMedicalFeeGradePercent(medicalFeeGradePercent) {
                if (isNumber(medicalFeeGradePercent) && this.shebaoMatchCount) {
                    const res = (medicalFeeGradePercent / this.shebaoMatchCount) * 100;
                    return `${res.toFixed(2)}%`;
                }
                return '0%';
            },
            // 翻页
            async pageTo(page) {
                const offset = (page - 1) * this.fetchParams.limit;
                this.presenter.setParams('offset', offset, false);
            },
            // 每页显示数量
            pageSizeChange(pageSize) {
                this.presenter.setParams('limit', pageSize, true);
                this.setStorage(CustomKeys.CLINIC_INVENTORY_PAGE_SIZE, TableKeys.inventoryGoodsTable, pageSize);
            },
            // 新建档案
            handleCreateArchive(goodsType) {
                // 未来可能支持眼镜
                if (goodsType === GoodsTypeEnum.EYEGLASSES) {
                    this.showEyeAddDialog = true;
                    return;
                }

                if (this.isVirtualPharmacy) {
                    this.currentTypeId = GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES;
                } else {
                    // 获取用户上次建档类型
                    const typeId = getUserLastTypeId(goodsType);
                    this.currentTypeId = typeId || this.defaultGoodsTypeMap[goodsType];
                }
                this.showAddDialog = true;
            },
            handleAddGoods() {
                console.log('连锁子店添加总部档案到当前门店');
                new ChainClinicAddArchivesDialog({
                    onConfirm: this.handleUpdateList,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            handleUpdateList(type, keepOrder = false, showToast = true, isSubmitGsp = false) {
                this.presenter.handleUpdateList(type, keepOrder, showToast, isSubmitGsp);
            },
            handleSuccessAdd() {
                // 不看0库存已勾选状态下，新增档案返回后取消勾选
                this.presenter.setParams('onlyStock', 0);
            },
            handleUpdateSuccess() {
                this.handleUpdateList(3);
                this.handleCancel();
            },
            /**
             * 处理提交GSP商品首营
             * <AUTHOR>
             * @date 2024-01-16
             * @param {String} goodsId
             * @param {Boolean} isNeedConfirm
             */
            async handleSubmitGsp(goodsId) {
                // 需要弹窗确认
                const isContinue = await new Promise((resolve) => {
                    this.$confirm({
                        type: 'warn',
                        title: '首营申请确认',
                        content: '档案创建成功，是否发起首营申请？',
                        onConfirm: () => resolve(true),
                        onCancel: () => resolve(false),
                    });
                });
                if (isContinue === false) {
                    return;
                }
                // 打开首营申请弹窗
                const openResponse = await tools.openDialog({
                    propsData: {
                        goodsId, // 商品id
                    },
                    component: DialogGoodsDetail,
                });
                if (openResponse.status === false) {
                    return openResponse;
                }
                this.fetchData();
            },
            // 关闭弹窗
            closeDialogHandle() {
                this.showAddDialog = false;
                this.showDetailDialog = false;
            },
            handleChangeWarningType(key) {
                this.presenter.handleChangeWarningType(key);
            },
            handleClickTd(event, row, rowIndex, targetTab) {
                if (!this.isChainAdmin) return;
                event.stopPropagation();
                this.currentGoods = row;
                this.currentGoodsIndex = rowIndex;
                this.targetTabContent = targetTab;
                this.showDetailDialog = true;
            },
            customTrClass(tr) {
                const classArr = ['clickable'];
                if (tr.v2DisableStatus) {
                    classArr.push('is-disabled');
                }
                return classArr;
            },
            customTdClass(config) {
                const classArr = [];
                const linkTdKey = ['lastPackageCostPrice', 'price', 'minExpiryDate' ,'profitRat', 'stockCount', 'lastMonthSellCount' , 'turnoverDays'];
                if (config.key === 'displayName') {
                    classArr.push('blue-text');
                }
                if (linkTdKey.includes(config.key) && this.isChainAdmin) {
                    classArr.push('link-td');
                }
                return classArr;
            },
            // disabledItemFunc(item) {
            //     return !!(item.disable || item.v2DisableStatus);
            // },
            handleClickRow(row) {
                if (this.isBatchUpdateView) return;

                this.targetTabContent = '';
                this.currentGoods = row;
                this.currentGoodsIndex = this.getRowIndex(row);
                this.showDetailDialog = true;
            },
            handleMounted(data) {
                console.log('handleMounted', data);
                const limit = data.paginationLimit - 1;
                if (!this.pageSizeList.includes(limit)) {
                    this.pageSizeList.push({
                        label: '推荐',
                        value: limit,
                    });
                    this.pageSizeList.sort((a, b) => {
                        return (a.value || a) - (b.value || b);
                    });
                }

                // 优先用上次的
                let preferPageSize = this.getStorage(CustomKeys.CLINIC_INVENTORY_PAGE_SIZE, TableKeys.inventoryGoodsTable);
                if (!this.pageSizeList.includes(preferPageSize)) {
                    // 没有上次的或者上次使用的分页条数不存在，优先用本次的
                    preferPageSize = limit;
                }
                this.model.setParams('limit', preferPageSize);
            },
            sortChangeHandler({
                orderBy, orderType,
            }) {
                this.presenter.handleChangeSort(orderBy, orderType);
            },

            handleCancel() {
                this.viewMode = 'normal';
                this.resetState();
                this.keepChecked(this.tableData, 'goodsId');
            },

            handleBatchOperation(value) {
                if (value === 0) {
                    this.handleClickBatchAdjustPrice();
                }
                if (value === 1) {
                    this.handleClickBatchUpdateArchives();
                }
                if (value === 2) {
                    this.handleClickBatchPrintPriceTag();
                }
            },

            handleBatchPrintPriceTag() {
                new PrintPriceTagDialog({
                    params: {
                        goodsIdList: this.goodsIdList,
                        isAllChecked: this.isAllChecked,
                        queryParams: this.createListParams(true),
                    },
                }).generateDialogAsync({
                    parent: this,
                });
            },
            customTrKey(item) {
                if (item.clinicId) {
                    return `${item.id}_${item.clinicId}`;
                }
                return item.id;
            },
        },
        beforeRouteEnter(to, from, next) {
            next((vm) => {
                if (to.params.fetchParams) {
                    vm.presenter.handleChangeWarningType(to.params.fetchParams.key);
                }
            });
        },
        beforeRouteLeave(to, from, next) {
            this.updateEmployeeStockConfig();
            next();
        },
    };
</script>
