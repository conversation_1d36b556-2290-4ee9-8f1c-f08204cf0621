<template>
    <abc-space>
        <biz-select-tabs
            v-if="isChainAdmin"
            v-model="selectedQueryType"
            :options="queryTypeOptions"
            :width="120"
            :inner-width="120"
        ></biz-select-tabs>
        <clinic-select
            v-if="isChainAdmin && selectedQueryType === 2"
            v-model="selectedClinicId"
            :clearable="true"
            :disabled="goodsFilterComponentDisabled"
            :show-all-clinic="false"
            placeholder="总部/门店"
            @change="handleChangeClinic"
        ></clinic-select>

        <!--商品搜索-->
        <goods-auto-complete
            ref="autoComplete"
            :auto-focus-first="false"
            :clear-search-key="false"
            :clinic-id="clinicId"
            :disabled="goodsFilterComponentDisabled"
            :enable-barcode-detector="enableBarcodeDetector"
            :enable-local-search="!searchOnlyStock && isAdmin"
            :only-stock="searchOnlyStock"
            :pharmacy-no="pharmacyNo"
            :search.sync="currentKey"
            :width="180"
            :with-stock="false"
            class="abc-autocomplete-search"
            clearable
            focus-show
            placeholder="商品名称/条码"
            @clear="clearSearch"
            @enter="initOffset"
            @searchGoods="searchGoods"
            @selectGoods="selectGoods"
        >
            <abc-search-icon slot="prepend"></abc-search-icon>
        </goods-auto-complete>
        <!--类型筛选-->
        <biz-goods-type-cascader
            v-model="currentSelectedTypes"
            :goods-type-options="goodsAllTypes"
            :cascader-config="{
                width: 100,
                panelMaxHeight: 274,
                disabled: goodsFilterComponentDisabled
            }"
            @change="handleChangeGoodsType"
        >
        </biz-goods-type-cascader>
        <abc-select
            v-model="currentProfitCategoryType"
            :disabled="goodsFilterComponentDisabled"
            :width="100"
            clearable
            multi-label-mode="text"
            multiple
            placeholder="利润分类"
        >
            <abc-option
                v-for="item in profitClassificationList"
                :key="`${item.id}-${item.name}`"
                :label="item.name"
                :value="item.id"
            >
            </abc-option>
            <abc-option :value="-1" label="未指定">
            </abc-option>
        </abc-select>
        <abc-input
            v-model="currentProfitRateMin"
            :config="{
                formatLength: 2,supportZero: true
            }"
            :disabled="goodsFilterComponentDisabled"
            :width="110"
            clearable
            hide-placeholder-when-focus
            placeholder="最低毛利率"
            type="money"
        >
            <label slot="appendInner" style="color: var(--abc-color-T3);">%</label>
        </abc-input>
        <span style="margin-inline-start: -4px; margin-inline-end: -4px; color: var(--abc-color-T3);">~</span>
        <abc-input
            v-model="currentProfitRateMax"
            :config="{
                formatLength: 2,supportZero: true
            }"
            :disabled="goodsFilterComponentDisabled"
            :width="110"
            clearable
            hide-placeholder-when-focus
            placeholder="最高毛利率"
            type="money"
        >
            <label slot="appendInner" style="color: var(--abc-color-T3);">%</label>
        </abc-input>
        <goods-filter-popover
            :disabled="goodsFilterComponentDisabled"
            :params="fetchParams"
            :pharmacy-type="pharmacyType"
            is-support-first-in-date
            :show-goods-basic-filter="true"
            :is-support-drug-identification-code="isSupportCodelessArea"
            :is-support-position="!isChainAdmin"
            :show-member-price="true"
            :shebao-count="panelData"
            @change="handleChangeSocialStatus"
        ></goods-filter-popover>
    </abc-space>
</template>

<script>
    import { mapGetters } from 'vuex';
    import GoodsAPI from 'api/goods';
    import { PharmacyGoodsPresenterKey } from '@/views-pharmacy/inventory/provideKeys';
    const ClinicSelect = () => import('views/layout/clinic-select/clinic-select.vue');
    const GoodsFilterPopover = () => import('views/inventory/components/goods-filter-popover.vue');
    const GoodsAutoComplete = () => import('views/inventory/common/goods-auto-complete.vue');
    import BizGoodsTypeCascader, { useGoodsType } from '@/components-composite/biz-goods-type-cascader';
    import BizSelectTabs from '@/components-composite/biz-select-tabs/src/views/index.vue';

    export default {
        name: 'FilterBar',
        components: {
            BizGoodsTypeCascader,
            GoodsFilterPopover,
            ClinicSelect,
            GoodsAutoComplete,
            BizSelectTabs,
        },
        inject: {
            presenter: {
                from: PharmacyGoodsPresenterKey,
            },
        },
        props: {
            fetchParams: {
                type: Object,
                default: () => ({}),
            },
            model: {
                type: Object,
                default: () => ({}),
            },
            panelData: {
                type: Object,
                default: () => ({}),
            },
            goodsFilterComponentDisabled: {
                type: Boolean,
                default: false,
            },
            enableBarcodeDetector: {
                type: Boolean,
                default: false,
            },
            searchOnlyStock: {
                type: Boolean,
                default: false,
            },
            pharmacyNo: Number,
            pharmacyType: Number,
        },
        setup() {
            const {
                allTypesCoverUnspecified,
                fetchAllGoodsTypes,
            } = useGoodsType();

            return {
                allTypesCoverUnspecified,
                fetchAllGoodsTypes,
            };
        },
        data() {
            return {
                queryTypeOptions: [
                    {
                        value: 1,
                        label: '总部档案',
                        icon: 's-s-drugstore-color',
                    },
                    {
                        value: 2,
                        label: '门店分布',
                        icon: 's-branchstore-color',
                    },
                ],
            };
        },
        computed: {
            ...mapGetters([
                'isAdmin',
                'isChainAdmin',
                'subClinics',
                'currentClinic',
                'currentPharmacy',
                'showSubSetPrice',
                'profitClassificationList',
                'allowProfitWarnFlag',
                'isCanOperateGoodsAdjustPriceInInventory',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isSupportCodelessArea() {
                return !!this.$abcSocialSecurity.defaultDrugTraceCode?.isSupportDefaultTraceCode;
            },
            selectedQueryType: {
                get() {
                    return this.fetchParams.queryType;
                },
                set(val) {
                    if (val === 2 && this.model.warningKey === 'unGsp') {
                        this.model.setState('warningKey', '');
                        this.model.setState('warningKeys', []);
                    }
                    if (val === 1) {
                        this.model.setParams('clinicId', '');
                    }
                    this.presenter.setParams('queryType', val);
                },
            },
            // 选中的门店id
            selectedClinicId: {
                get() {
                    return this.fetchParams.clinicId || '';
                },
                set(val) {
                    this.presenter.setParams('clinicId', val);
                },
            },
            clinicId() {
                return this.isChainAdmin ? this.selectedClinicId : this.currentClinic.clinicId;
            },
            currentSubClinics() {
                const { clinicId } = this.currentClinic;
                const clinics = this.subClinics?.map((item) => {
                    return {
                        ...item,
                        label: item.id === clinicId ? '本店' : (item.shortName || item.name),
                        value: item.id,
                    };
                }) ?? [];

                clinics.unshift({
                    value: '',
                    label: '全部门店',
                });
                return clinics;
            },
            currentKey: {
                get() {
                    return this.model.searchKey;
                },
                set(val) {
                    this.presenter.setState('searchKey', val);
                },
            },
            currentSelectedTypes: {
                get() {
                    return this.model.selectedTypes || [];
                },
                set(v) {
                    this.presenter.setState('selectedTypes', v);
                },
            },
            supportGoodsTypeIdMap() {
                return this.viewDistributeConfig.Inventory.supportGoodsTypeIdMap;
            },
            currentProfitCategoryType: {
                get() {
                    return this.fetchParams.profitCategoryTypeList || [];
                },
                set(val) {
                    this.presenter.setParams('profitCategoryTypeList', val);
                },
            },
            currentProfitRateMin: {
                get() {
                    return this.fetchParams.minProfitRat;
                },
                set(val) {
                    this.presenter.setParams('minProfitRat', val);
                },
            },
            currentProfitRateMax: {
                get() {
                    return this.fetchParams.maxProfitRat;
                },
                set(val) {
                    this.presenter.setParams('maxProfitRat', val);
                },
            },
            goodsAllTypes() {
                return this.allTypesCoverUnspecified.filter((item) => {
                    return this.supportGoodsTypeIdMap[+item.id];
                });
            },
        },
        created() {
            this.fetchAllGoodsTypes(() => {
                return GoodsAPI.fetchGoodsClassificationV3({
                    queryType: 1,
                    needCustomType: 1,
                });
            });
        },
        methods: {
            handleChangeClinic(value) {
                const clinic = this.currentSubClinics.find((item) => item.value === value);
                this.clinicName = clinic?.label;
                this.presenter.setParams('clinicId', clinic?.value);
            },
            searchGoods(val = '') {
                this.presenter.searchGoods(val);
            },
            selectGoods(goods) {
                this.presenter.selectGoods(goods);
            },
            clearSearch() {
                this.presenter.clearSearch();
            },
            initOffset() {
                this.presenter.initOffset();
            },
            handleChangeGoodsType(typeIdList, customTypeIdList) {
                this.presenter.handleChangeGoodsType(typeIdList, customTypeIdList);
            },
            handleChangeSocialStatus(params) {
                this.presenter.handleChangeSocialStatus(params);
            },
        },
    };
</script>
