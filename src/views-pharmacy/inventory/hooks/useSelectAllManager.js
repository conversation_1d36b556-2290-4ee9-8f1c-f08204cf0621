import {
    ref,
    computed,
} from 'vue';

export function useSelectAllManager() {
    const isAllChecked = ref(false);
    // 为了响应式变化-与includeMap、excludeMap配合使用
    const incrementNumber = ref(0);
    const includeMap = ref(new Map());
    const excludeMap = ref(new Map());
    // 两个数组数据互斥
    const includeArr = computed(() => (incrementNumber.value ? Array.from(includeMap.value.values()) : []));
    const excludeArr = computed(() => (incrementNumber.value ? Array.from(excludeMap.value.values()) : []));
    const submitArr = computed(() => (isAllChecked.value ? excludeArr.value : includeArr.value));

    function changeAllChecked(checked) {
        incrementNumber.value++;
        isAllChecked.value = checked;
        includeMap.value.clear();
        excludeMap.value.clear();
    }

    function changeChecked(item, customKeyId = 'keyId') {
        incrementNumber.value++;

        const { checked } = item;
        const keyId = item[customKeyId];
        // 全选-做排除
        if (isAllChecked.value) {
            if (checked) {
                excludeMap.value.delete(keyId);
            } else {
                excludeMap.value.set(keyId, item);
            }
        } else {
            // 非全选-做包含
            if (checked) {
                includeMap.value.set(keyId, item);
            } else {
                includeMap.value.delete(keyId);
            }
        }
    }

    // 保持状态-会修改原数组checked值
    function keepChecked(list, customKeyId = 'keyId') {
        list.forEach((item) => {
            const keyId = item[customKeyId];
            if (isAllChecked.value) {
                if (!excludeMap.value.has(keyId)) {
                    item.checked = true;
                } else {
                    item.checked = false;
                }
            } else {
                if (includeMap.value.has(keyId)) {
                    item.checked = true;
                } else {
                    item.checked = false;
                }
            }
        });

    }

    function resetState() {
        incrementNumber.value = 0;
        isAllChecked.value = false;
        includeMap.value.clear();
        excludeMap.value.clear();
    }

    function hasChecked (keyId = 'keyId') {
        if (isAllChecked.value) {
            return !excludeMap.value.has(keyId);
        }
        return includeMap.value.has(keyId);
    }

    return {
        isAllChecked,
        includeMap,
        excludeMap,
        includeArr,
        excludeArr,
        submitArr,
        hasChecked,
        keepChecked,
        changeChecked,
        changeAllChecked,
        resetState,
    };
}
