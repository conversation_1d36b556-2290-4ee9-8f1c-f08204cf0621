<template>
    <abc-dialog
        v-model="modalVisible"
        :title="``"
        size="huge"
        content-styles="padding: 0; height: 600px; overflow: hidden;"
        class="abc-transfer-tree-dialog"
        append-to-body
        :auto-focus="false"
    >
        <abc-transfer-v2
            :loading="loading"
            :data="data"
            node-key="id"
            :last-node-detach-tree="true"
            :default-check-disabled="true"
            :default-checked-keys="defaultCheckedKeys"
            :search-node-method="searchNodeMethod"
            search-placeholder="搜索商品"
            show-search
            result-title="选择商品"
            @cancel="cancel"
            @confirm="confirm"
        >
        </abc-transfer-v2>
    </abc-dialog>
</template>

<script>
    import { MedicineSelectionPresenter } from './presenter';
    import { mapGetters } from 'vuex';

    export default {
        model: {
            prop: 'visible',
            event: 'change',
        },

        props: {
            visible: {
                type: Boolean,
                default: false,
            },
            defaultCheckedKeys: Array,
            filterFn: Function, // 对目录数据进行过滤
            searchParams: {
                type: Object,
                default: () => ({}),
            },
        },

        data() {
            return {
                oriData: [],
                data: [],

                loading: false,

                presenter: null,
            };
        },

        computed: {
            ...mapGetters(['isChainSubStore']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            needTransGoodsClassificationName() {
                return this.viewDistributeConfig.needTransGoodsClassificationName;
            },
            transGoodsClassificationName() {
                return this.viewDistributeConfig.transGoodsClassificationName;
            },
            modalVisible: {
                get() {
                    return this.visible;
                },
                set(val) {
                    this.$emit('input', val);
                    this.$emit('change', val);
                },
            },
        },

        watch: {
            visible: {
                handler(val) {
                    if (!val) {
                        this.clear();
                        return;
                    }

                    this.doInit();
                },
                immediate: true,
            },
        },

        methods: {
            showLoading() {
                this.loading = true;
            },

            hideLoading() {
                this.loading = false;
            },

            initData(payload) {
                this.oriData = payload;
            },

            async doInit() {
                if (!this.presenter) {
                    this.presenter = new MedicineSelectionPresenter(this);
                }

                await this.presenter.init();

                if (this.filterFn) {
                    this.data = this.filterFn(this.oriData);
                } else {
                    this.data = this.oriData;
                }
            },

            confirm(payload) {
                this.$emit('confirm', payload);

                this.modalVisible = false;
            },

            cancel() {
                this.modalVisible = false;
            },

            async searchNodeMethod(key) {
                const params = {
                    offset: 0,
                    limit: 40,
                    key,
                    ...this.searchParams,
                };

                return this.presenter.searchNode(params);
            },
        },
    };
</script>

<style lang="scss">
@import 'styles/abc-common.scss';

</style>
