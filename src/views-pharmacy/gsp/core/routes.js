import Index from '../index.vue';

import {
    MODULE_ID_MAP, RouterScope,
} from 'utils/constants.js';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const FirstBattalion = () => import('../frames/first-battalion/index.vue');
const FirstBattalionGoods = () => import('../frames/first-battalion/goods/index.vue');
const FirstBattalionSupplier = () => import('../frames/first-battalion/supplier/index.vue');

const CheckAccept = () => import('../frames/check-accept/index.vue');
const CheckAcceptQualified = () => import('../frames/check-accept/qualified/index.vue');
const CheckAcceptUnqualified = () => import('../frames/check-accept/unqualified/index.vue');

const Storage = () => import('../frames/storage/index.vue');
const StorageConserve = () => import('../frames/storage/conserve/index.vue');
const StorageHumiture = () => import('../frames/storage/humiture/index.vue');
const StorageEnvironment = () => import('../frames/storage/environment/index.vue');
const StorageClearFunnel = () => import('../frames/storage/clear-funnel/index.vue');
const StorageInstallFunnel = () => import('../frames/storage/install-funnel/index.vue');

const AfterSales = () => import('../frames/after-sales/index.vue');
const AfterSalesAdverseReactions = () => import('../frames/after-sales/adverse-reactions/index.vue');
const AfterSalesRecall = () => import('../frames/after-sales/recall/index.vue');
const AfterSalesRecover = () => import('../frames/after-sales/recover/index.vue');

const Worker = () => import('../frames/worker/index.vue');
const WorkerHealth = () => import('../frames/worker/health/index.vue');
const WorkerTrain = () => import('../frames/worker/train/index.vue');

const SpecialDrugs = () => import('../frames/special-drugs/index.vue');
const SpecialDrugsRx = () => import('../frames/special-drugs/rx/index.vue');
const SpecialDrugsHemp = () => import('../frames/special-drugs/hemp/index.vue');
const SpecialDrugsPieceSale = () => import('../frames/special-drugs/piece-sale/index.vue');
const CheckAcceptTransportRecord = () => import('../frames/check-accept/transport-record/index.vue');

// 质量可疑上报
const SuspiciousQuality = () => import('../frames/suspicious-quality/index.vue');
// 不合格
const Unqualified = () => import('../frames/unqualified/index.vue');
// 报损
// const GoodsOut = () => import('../../inventory/frames/report-loss/index.vue');
const ReportLoss = () => import('../frames/report-loss/index.vue');
// 销毁
const Destroy = () => import('../frames/destroy/index.vue');
const DestroyApply = () => import('../frames/destroy/destroy-apply.vue');
const DestroyDone = () => import('../frames/destroy/destroy-done.vue');
// 外部跳转都应该用该字段
export const PharmacyGspRouterNameKeys = {
    index: '@PharmacyGsp',

    firstBattalion: '@PharmacyGspFirstBattalion',
    firstBattalionGoods: '@PharmacyGspFirstBattalionGoods',
    firstBattalionSupplier: '@PharmacyGspFirstBattalionSupplier',

    checkAccept: '@PharmacyGspCheckAccept',
    checkAcceptTransportRecord: '@PharmacyGspCheckAcceptTransportRecord',
    checkAcceptQualified: '@PharmacyGspCheckAcceptQualified',
    checkAcceptMedicalDeviceQualified: '@PharmacyGspCheckAcceptMedicalDeviceQualified',
    checkAcceptUnqualified: '@PharmacyGspCheckAcceptUnqualified',
    checkAcceptMedicalDeviceUnqualified: '@PharmacyGspCheckAcceptMedicalDeviceUnqualified',

    storage: '@PharmacyGspStorage',
    storageConserve: '@PharmacyGspStorageConserve',
    storageMedicalDeviceConserve: '@PharmacyGspStorageMedicalDeviceConserve',
    storageHumiture: '@PharmacyGspStorageHumiture',
    storageEnvironment: '@PharmacyGspStorageEnvironment',
    storageClearFunnel: '@PharmacyGspStorageClearFunnel',
    storageInstallFunnel: '@PharmacyGspStorageInstallFunnel',
    storageReportingLosses: '@PharmacyGspStorageReportingLosses',

    afterSales: '@PharmacyGspAfterSales',
    afterSalesAdverseReactions: '@PharmacyGspAfterSalesAdverseReactions',
    afterSalesRecall: '@PharmacyGspAfterSalesRecall',
    afterSalesRecover: '@PharmacyGspAfterSalesRecover',

    worker: '@PharmacyGspWorker',
    workerHealth: '@PharmacyGspWorkerHealth',
    workerTrain: '@PharmacyGspWorkerTrain',

    specialDrugs: '@PharmacyGspSpecialDrugs',
    specialDrugsRx: '@PharmacyGspSpecialDrugsRx',
    specialDrugsHemp: '@PharmacyGspSpecialDrugsHemp',
    specialDrugsPieceSale: '@PharmacyGspSpecialDrugsPieceSale',

    suspiciousQuality: '@PharmacyGspSuspiciousQuality',
    unqualified: '@PharmacyGspUnqualified',
    reportLoss: '@PharmacyGspReportLoss',
    reportLossHome: '@PharmacyGspReportLossHome',
    destroy: '@PharmacyGspDestroy',
    destroyApply: '@PharmacyGspDestroyApply',
    destroyDone: '@PharmacyGspDestroyDone',
};

export default {
    path: 'gsp',
    name: PharmacyGspRouterNameKeys.index,
    component: Index,
    meta: {
        name: 'GSP',
        scope: RouterScope.CHAIN_ADMIN | RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
        needAuth: true,
        moduleId: MODULE_ID_MAP.bizPharmacyGSP,
        pageAsyncClass: PageAsync,
        icon: 'n-safety-fill',
        selectedIcon: 'n-safety-fill',
    },
    children: [
        {
            path: 'first-battalion',
            name: PharmacyGspRouterNameKeys.firstBattalion,
            component: FirstBattalion,
            meta: {
                name: '首营',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.primaryBusiness,
                pageAsyncClass: PageAsync,
            },
            children: [
                {
                    path: 'goods',
                    component: FirstBattalionGoods,
                    name: PharmacyGspRouterNameKeys.firstBattalionGoods,
                    meta: {
                        name: '商品首营',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.primaryBusinessSubModule.goods,
                    },
                },
                {
                    path: 'supplier',
                    component: FirstBattalionSupplier,
                    name: PharmacyGspRouterNameKeys.firstBattalionSupplier,
                    meta: {
                        name: '供应商首营',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.primaryBusinessSubModule.supplier,
                    },
                },
            ],
        },
        {
            path: 'check-accept',
            name: PharmacyGspRouterNameKeys.checkAccept,
            component: CheckAccept,
            meta: {
                name: '验收',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.check,
                pageAsyncClass: PageAsync,
            },
            children: [
                {
                    path: 'transportRecord',
                    component: CheckAcceptTransportRecord,
                    name: PharmacyGspRouterNameKeys.checkAcceptTransportRecord,
                    meta: {
                        name: '运输记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.checkSubModule.transportRecord,
                        needAuth: true,
                    },
                },
                {
                    path: 'qualified',
                    component: CheckAcceptQualified,
                    name: PharmacyGspRouterNameKeys.checkAcceptQualified,
                    meta: {
                        name: '药品验收记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.checkSubModule.qualified,
                        needAuth: true,
                    },
                },
                {
                    path: 'unqualified',
                    component: CheckAcceptUnqualified,
                    name: PharmacyGspRouterNameKeys.checkAcceptUnqualified,
                    meta: {
                        name: '药品验收不合格记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.checkSubModule.unqualified,
                        needAuth: true,
                    },
                },
                {
                    path: 'medical-device-qualified',
                    component: CheckAcceptQualified,
                    name: PharmacyGspRouterNameKeys.checkAcceptMedicalDeviceQualified,
                    meta: {
                        name: '医疗器械验收记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.checkSubModule.medicalDeviceQualified,
                        needAuth: true,
                    },
                },
                {
                    path: 'medical-device-unqualified',
                    component: CheckAcceptUnqualified,
                    name: PharmacyGspRouterNameKeys.checkAcceptMedicalDeviceUnqualified,
                    meta: {
                        name: '医疗器械验收不合格记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.checkSubModule.medicalDeviceUnqualified,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'storage',
            name: PharmacyGspRouterNameKeys.storage,
            component: Storage,
            meta: {
                name: '存储',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.store,
                pageAsyncClass: PageAsync,
                todoKey: 'storageTodo',
            },
            children: [
                {
                    path: 'conserve',
                    component: StorageConserve,
                    name: PharmacyGspRouterNameKeys.storageConserve,
                    meta: {
                        name: '药品养护',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.conserve,
                        todoKey: 'gspTodo',
                    },
                },
                {
                    path: 'medical-device-conserve',
                    component: StorageConserve,
                    name: PharmacyGspRouterNameKeys.storageMedicalDeviceConserve,
                    meta: {
                        name: '器械养护',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.conserveMedicalDevice,
                        todoKey: 'materialMaintenanceTodoCount',
                    },
                },
                {
                    path: 'humiture',
                    component: StorageHumiture,
                    name: PharmacyGspRouterNameKeys.storageHumiture,
                    meta: {
                        name: '温湿度记录',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.humiture,
                    },
                },
                {
                    path: 'environment',
                    component: StorageEnvironment,
                    name: PharmacyGspRouterNameKeys.storageEnvironment,
                    meta: {
                        name: '陈列环境检查',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.environment,
                    },
                },
                {
                    path: 'clear-funnel',
                    component: StorageClearFunnel,
                    name: PharmacyGspRouterNameKeys.storageClearFunnel,
                    meta: {
                        name: '清斗记录',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.clearFunnel,
                    },
                },
                {
                    path: 'install-funnel',
                    component: StorageInstallFunnel,
                    name: PharmacyGspRouterNameKeys.storageInstallFunnel,
                    meta: {
                        name: '装斗记录',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.installFunnel,
                    },
                },
                // {
                //     path: 'reporting-losses',
                //     component: StorageReportingLosses,
                //     name: PharmacyGspRouterNameKeys.storageReportingLosses,
                //     meta: {
                //         name: '报损记录',
                //         needAuth: true,
                //         moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.reportingLosses,
                //     },
                // },
            ],
        },
        {
            path: 'special-drugs',
            name: PharmacyGspRouterNameKeys.specialDrugs,
            component: SpecialDrugs,
            meta: {
                name: '销售',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.specialMedicine,
                pageAsyncClass: PageAsync,
            },
            children: [
                {
                    path: 'rx',
                    component: SpecialDrugsRx,
                    name: PharmacyGspRouterNameKeys.specialDrugsRx,
                    meta: {
                        name: '处方药',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.specialMedicineSubModule.rx,
                    },
                },
                {
                    path: 'hemp',
                    component: SpecialDrugsHemp,
                    name: PharmacyGspRouterNameKeys.specialDrugsHemp,
                    meta: {
                        name: '含麻黄碱药',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.specialMedicineSubModule.hemp,
                    },
                },
                // 拆零药品
                {
                    path: 'zero',
                    component: SpecialDrugsPieceSale,
                    name: PharmacyGspRouterNameKeys.specialDrugsPieceSale,
                    meta: {
                        name: '拆零药品',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.specialMedicineSubModule.pieceSale,
                    },
                },
            ],
        },
        {
            path: 'after-sales',
            name: PharmacyGspRouterNameKeys.afterSales,
            component: AfterSales,
            meta: {
                name: '售后',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.afterSale,
                pageAsyncClass: PageAsync,
            },
            children: [
                {
                    path: 'adverse-reactions',
                    component: AfterSalesAdverseReactions,
                    name: PharmacyGspRouterNameKeys.afterSalesAdverseReactions,
                    meta: {
                        name: '不良反应记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.afterSaleSubModule.adverseReactions,
                        needAuth: true,
                    },
                },
                {
                    path: 'recall',
                    component: AfterSalesRecall,
                    name: PharmacyGspRouterNameKeys.afterSalesRecall,
                    meta: {
                        name: '召回',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.afterSaleSubModule.recall,
                        needAuth: true,
                    },
                },
                {
                    path: 'recover',
                    component: AfterSalesRecover,
                    name: PharmacyGspRouterNameKeys.afterSalesRecover,
                    meta: {
                        name: '追回',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.afterSaleSubModule.recover,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'workers',
            name: PharmacyGspRouterNameKeys.worker,
            component: Worker,
            meta: {
                name: '人员',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.person,
                pageAsyncClass: PageAsync,
            },
            children: [
                {
                    path: 'health',
                    component: WorkerHealth,
                    name: PharmacyGspRouterNameKeys.workerHealth,
                    meta: {
                        name: '健康档案',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.personSubModule.health,
                    },
                },
                {
                    path: 'train',
                    component: WorkerTrain,
                    name: PharmacyGspRouterNameKeys.workerTrain,
                    meta: {
                        name: '培训记录',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.personSubModule.train,
                    },
                },
            ],
        },
        {
            path: 'suspicious-quality',
            name: PharmacyGspRouterNameKeys.suspiciousQuality,
            component: SuspiciousQuality,
            meta: {
                name: '质量可疑',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.suspiciousQuality,
                pageAsyncClass: PageAsync,
            },
        },
        {
            path: 'unqualified',
            name: PharmacyGspRouterNameKeys.unqualified,
            component: Unqualified,
            meta: {
                name: '不合格品',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.unqualified,
                pageAsyncClass: PageAsync,
            },
        },
        {
            path: 'report-loss',
            name: PharmacyGspRouterNameKeys.reportLoss,
            component: ReportLoss,
            meta: {
                name: '报损',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.reportLoss,
                pageAsyncClass: PageAsync,
            },
        },
        {
            path: 'destroy',
            name: PharmacyGspRouterNameKeys.destroy,
            component: Destroy,
            meta: {
                name: '销毁',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.destroy,
                pageAsyncClass: PageAsync,
                todoKey: 'gspDestroyTodo',
                scope: RouterScope.CHAIN_ADMIN | RouterScope.SINGLE_STORE,
            },
            children: [
                {
                    path: 'destroy-apply',
                    component: DestroyApply,
                    name: PharmacyGspRouterNameKeys.destroyApply,
                    meta: {
                        name: '销毁申请',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.destroySubModule.destroyApply,
                    },
                },
                {
                    path: 'destroy-done',
                    component: DestroyDone,
                    name: PharmacyGspRouterNameKeys.destroyDone,
                    meta: {
                        name: '销毁处理',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.destroySubModule.destroyDone,
                        todoKey: 'gspDestroyTodo',
                    },
                },
            ],
        },
    ],
};
