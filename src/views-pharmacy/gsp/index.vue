<template>
    <abc-container class="abc-pharmacy-gsp-container">
        <div style="position: relative;">
            <app-tabs-card v-if="showTabCardInHospital" sup-name="@PharmacyGsp" :todo-data="todoData">
            </app-tabs-card>
            <abc-button
                v-if="isChainAdmin && thirdPartyWarehouseConfig.isEnable"
                style="position: absolute; top: 50%; right: 16px; z-index: 10; transform: translateY(-50%);"
                variant="ghost"
                @click="onClickQualityRecord"
            >
                委托仓库质量记录
            </abc-button>
        </div>
        <router-view></router-view>
    </abc-container>
</template>

<script>
    import * as core from '@/views-pharmacy/gsp/core/index.js';
    import { createAbcPage } from '@/core/page/factory.js';
    import { mapGetters } from 'vuex';
    import AppTabsCard from '@/views-pharmacy/layout/app-tabs-card.vue';
    import { windowOpen } from '@/core/navigate-helper';

    export default {
        name: 'PharmacyGsp',
        components: {
            AppTabsCard,
        },
        mixins: [
            createAbcPage(core),
        ],
        computed: {
            ...mapGetters([
                'gspTodo',
                'materialMaintenanceTodoCount',
                'gspDestroyTodo',
                'currentPharmacyId',
                'isChainAdmin',
                'thirdPartyWarehouseConfig',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            showTabCardInHospital() {
                return this.viewDistributeConfig.Gsp.showTabCardInHospital;
            },
            todoData() {
                return {
                    'storageTodo': Number(this.gspTodo + this.materialMaintenanceTodoCount),
                    'gspTodo': this.gspTodo,
                    'gspDestroyTodo': this.gspDestroyTodo,
                    'materialMaintenanceTodoCount': this.materialMaintenanceTodoCount,
                };
            },
            isReportLoss() {
                return this.$route.fullPath === '/gsp/report-loss';
            },
        },
        async created() {
            // 选中一个药房
            await this.$store.dispatch('selectStockRoomId', this.currentPharmacyId);
            this.$store.dispatch('fetchInventoryTodo');
            if (this.isChainAdmin) {
                this.$store.dispatch('initThirdPartyWarehouseConfig');
            }
        },
        methods: {
            onClickQualityRecord() {
                const {
                    path,
                    browserType,
                } = this.thirdPartyWarehouseConfig;
                if (window.require) {
                    if (browserType === 1) {
                        try {
                            // 启动IE浏览器
                            const { exec } = window.require('child_process');
                            exec(`start iexplore.exe "${path}"`);
                        } catch (error) {
                            console.error('启动IE浏览器失败:', error);
                            window.require('electron').shell.openExternal(path);
                        }
                    } else {
                        window.require('electron').shell.openExternal(path);
                    }
                } else {
                    windowOpen(path);
                }
            },
        },
    };
</script>

<style lang="scss">
    @import './_index.scss';
</style>


