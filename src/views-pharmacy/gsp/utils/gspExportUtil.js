import { exportFileByAxios } from 'utils/excel';
import Qs from 'qs';
import { parseTime } from '@/utils';

export const GSPExportEnum = Object.freeze({
    CHECK_ACCEPT: { // 验收
        // TRANSPORT_RECORD: 'transportRecord', // 运输记录
        QUALIFIED: '/api/v2/gsp/record/inspect/qualified/items/export', // 验收记录
        UNQUALIFIED: '/api/v2/gsp/record/inspect/unqualified/items/export', // 验收不合格品记录
    },
    STORAGE: {
        CONSERVE: { // 药品养护
            LIST: 'storage-conserve-conserve', // 列表
            ORDER: '/api/v2/gsp/record/medicine-maintenance/order/{id}/export', // 单据
        },
        HUMITURE: '/api/v2/gsp/record/temperature-humidity/export', // 温湿度记录
        ENVIRONMENT: '/api/v2/gsp/record/display-environment/order/export', // 陈列环境检查
        CLEAR_FUNNEL: { // 清斗记录
            LIST: '/api/v2/gsp/record/medicine_clear_bucket/order/list/export', // 列表
            ORDER: '/api/v2/gsp/record/medicine_clear_bucket/order/{id}/export', // 单据
        },
        INSTALL_FUNNEL: { // 装斗记录
            LIST: '/api/v2/gsp/record/medicine_assemble_bucket/order/list/export', // 列表
            ORDER: '/api/v2/gsp/record/medicine_assemble_bucket/order/{id}/export', // 单据
        },
    },
    SPECIAL_DRUGS: {
        RX: { // 处方药
            IN: '/api/v2/gsp/record/stock/in/items/export', // 入库
            OUT: '/api/v2/gsp/record/sell/export', // 销售
        },
        HEMP: { // 含麻药
            IN: '/api/v2/gsp/record/stock/in/items/export', // 入库
            OUT: '/api/v2/gsp/record/sell/export', // 销售
        },
        SALE: '/api/v2/gsp/record/dismounting/export',
    },
    AFTER_SALES: { // 售后
        ADVERSE_REACTIONS: '/api/v2/gsp/record/adverse-reaction/order/export', // 不良反应记录
        RECALL: '/api/v2/gsp/record/recall-record/list/export', // 召回
        RECOVER: '/api/v2/gsp/record/recover-record/list/export', // 追回
    },
    WORKERS: { // 人员
        HEALTH: '/api/v2/gsp/record/personnel_health/list/export', // 健康档案
        TRAIN: '/api/v2/gsp/record/personnel_train/list/export', // 培训记录
    },
    SUSPICIOUS_QUALITY: '/api/v2/gsp/record/suspicious/list/export', // 质量可疑
    UNQUALIFIED: '/api/v2/gsp/record/suspicious/list/export', // 不合格品
    // REPORT_LOSS: 'report-loss', // 报损
});

/**
 * 导出GSP模块excel-通用方法
  */
export function exportExcelCommon(GSPExportUrl, params, filename, extraData = {} ,isOrder = false) {
    let url;
    let extra;
    if (isOrder) {
        extra = `${extraData.title || ''}.xlsx`;
        url = GSPExportUrl.replace('{id}', params);
    } else {
        extra = `${parseTime(new Date(), 'y-m-d', true)}.xlsx`;
        url = `${GSPExportUrl}`;
    }

    return exportFileByAxios({
        filename: `${filename}_${extra}`,
        url,
        params,
        paramsSerializer(p) {
            return Qs.stringify(p);
        },
    });
}
