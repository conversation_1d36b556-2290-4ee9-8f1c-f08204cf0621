import BaseClinicTypeTable from 'views/layout/tables/base-clinic-type-table';

export class PurchaseTableConfig extends BaseClinicTypeTable {
    constructor(clinic) {
        super(clinic);
        this.chainTableConfig = {
            'list': [{
                'label': '单号',
                'key': 'orderNo',
                'style': {
                    'flex': '','width': '168px','maxWidth': '168px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '门店名称',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'clinicName',
                'show': true,
            },{
                'label': '库房',
                'style': {
                    'flex': 1,'width': '','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'pharmacyName',
            },
            {
                'key': 'status',
                'label': '状态',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },
            {
                'label': '品种数',
                'key': 'kindCount',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },
            {
                'label': '药品',
                'key': 'medicineName',
                'style': {
                    'flex': '1','width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作人',
                'key': 'operatorInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },
            {
                'label': '操作时间',
                'key': 'operationTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
        this.chainSubTableConfig = {
            'list': [{
                'label': '单号',
                'key': 'orderNo',
                'style': {
                    'flex': '','width': '168px','maxWidth': '168px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '库房',
                'style': {
                    'flex': 1,'width': '','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'pharmacyName',
            },
            {
                'key': 'status',
                'label': '状态',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },
            {
                'label': '品种数',
                'key': 'kindCount',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },
            {
                'label': '药品',
                'key': 'medicineName',
                'style': {
                    'flex': '1','width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作人',
                'key': 'operatorInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作时间',
                'key': 'operationTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
        this.singleTableConfig = {
            'list': [{
                'label': '单号',
                'key': 'orderNo',
                'style': {
                    'flex': '','width': '168px','maxWidth': '168px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '库房',
                'style': {
                    'flex': 1,'width': '','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
                'key': 'pharmacyName',
            },
            {
                'key': 'status',
                'label': '状态',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
                },
            },{
                'label': '品种数',
                'key': 'kindCount',
                'style': {
                    'flex': '','width': '100px','maxWidth': '100px','minWidth': '100px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                },
            },
            {
                'label': '药品',
                'key': 'medicineName',
                'style': {
                    'flex': '1','width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '操作人',
                'key': 'operatorInfo',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },
            {
                'label': '操作时间',
                'key': 'operationTime',
                'style': {
                    'flex': '','width': '148px','maxWidth': '148px','minWidth': '148px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            },{
                'label': '备注',
                'key': 'remark',
                'style': {
                    'flex': 1,'width': '','maxWidth': '','minWidth': '70px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                },
            }],
        };
    }
}
