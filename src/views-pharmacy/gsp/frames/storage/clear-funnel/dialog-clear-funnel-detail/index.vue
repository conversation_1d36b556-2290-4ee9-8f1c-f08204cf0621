<template>
    <frame-dialog
        v-model="showDialog"
        class="pharmacy__first-battalion__clear-funnel__dialog"
        :title="title"
        :auto-focus="false"
        :show-title-append="true"
        :order-no="order.orderNo"
        :status-name="statusName"
        :before-close="closeDialog"
        :tag-config="tagConfig"
        :loading="loading"
        @open="openDialog"
    >
        <abc-form
            v-if="!!formData"
            ref="formData"
            item-no-margin
            style="height: 100%;"
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-form-item-group is-excel>
                        <abc-descriptions
                            :column="4"
                            :label-width="88"
                            size="large"
                            grid
                            class="base-info"
                            background
                        >
                            <abc-descriptions-item label="门店" :span="multiPharmacyCanUse ? 1 : 2">
                                <div v-abc-title.ellipsis="formData.clinicName"></div>
                            </abc-descriptions-item>
                            <abc-descriptions-item v-if="multiPharmacyCanUse" label="库房" :span="1">
                                <div v-abc-title.ellipsis="formData.pharmacyName"></div>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="操作时间" :span="1" content-padding="0">
                                <abc-form-item>
                                    <abc-date-time-picker
                                        v-model="formData.operationTime"
                                        class="abc-date-time-picker__new-style"
                                        :picker-options="pickerOptions"
                                        :disabled="isDisabledForm"
                                        :clearable="false"
                                        :show-icon="false"
                                        :space="0"
                                        size="large"
                                    ></abc-date-time-picker>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="操作人" :span="1" content-padding="0">
                                <abc-form-item required>
                                    <abc-select
                                        v-model="formData.operator"
                                        :disabled="isDisabledForm"
                                        :inner-width="148"
                                        :fetch-suggestions="(searchKey) => searchKeyOperator = searchKey"
                                        with-search
                                        size="large"
                                    >
                                        <abc-option
                                            v-for="item in operatorOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="备注" :span="4" content-padding="0">
                                <abc-form-item>
                                    <abc-input
                                        v-model="formData.remark"
                                        :disabled="isDisabledForm"
                                        :max-length="100"
                                        :placeholder="isDisabledForm ? '' : '请输入备注'"
                                        size="large"
                                    ></abc-input>
                                </abc-form-item>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-layout style="height: 100%;">
                        <abc-layout-content>
                            <abc-table
                                ref="tableRef"
                                type="excel"
                                class="focus-table"
                                :render-config="renderConfig"
                                :data-list="formData.medicineClearBucketLists"
                                empty-size="small"
                                need-delete-confirm
                                :custom-tr-key="customTrKey"
                                :auto-height="true"
                                :support-delete-tr="isCreate || isDraft"
                                @delete-tr="onClickDeleteTr"
                                @sortChange="sortChange"
                            >
                                <template v-if="isShowGoodsSearch" #topHeader>
                                    <abc-flex align="center" justify="space-between" flex="1">
                                        <goods-auto-complete
                                            class="back-focus-to-autocomplete"
                                            placeholder="输入商品名称或扫码添加"
                                            :search.sync="fetchParams.keyword"
                                            :focus-show="true"
                                            :type-arr="fetchParams.type"
                                            :sub-type-arr="fetchParams.subType"
                                            :c-m-spec="fetchParams.cMSpec"
                                            :width="480"
                                            enable-local-search
                                            is-type-arr
                                            only-stock
                                            size="medium"
                                            :inorder-config="0"
                                            :clinic-id="clinicId"
                                            :pharmacy-no="pharmacy?.pharmacyNo"
                                            @selectGoods="onSelectGoods"
                                        >
                                            <abc-icon slot="prepend" icon="n-add-line-medium"></abc-icon>
                                        </goods-auto-complete>
                                        <abc-space>
                                            <abc-button
                                                variant="ghost"
                                                theme="default"
                                                @click="onClickBatchAddition"
                                            >
                                                批量添加
                                            </abc-button>
                                            <abc-button
                                                variant="ghost"
                                                theme="default"
                                                @click="openOrderSelect"
                                            >
                                                按入库单添加
                                            </abc-button>
                                        </abc-space>
                                    </abc-flex>
                                </template>
                                <template #batchs="{ trData: row }">
                                    <template v-if="isUseClinicBatchComponent">
                                        <abc-table-cell v-if="row.batchGroup && row.group || isDisabledForm">
                                            {{ row.batchNo }}
                                        </abc-table-cell>
                                        <abc-form-item v-else>
                                            <gsp-batch-select
                                                v-model="row.batchList"
                                                :batchs="row.batchs"
                                                style="height: 100%;"
                                                :goods="row.productInfo"
                                                :goods-id="row.goodsId"
                                                :pharmacy-no="pharmacy?.pharmacyNo || ''"
                                                :multi-pharmacy-can-use="!!multiPharmacyCanUse"
                                                @change="(selects) => changeBatchItem(selects, row)"
                                            ></gsp-batch-select>
                                        </abc-form-item>
                                    </template>
                                    <template v-else>
                                        <abc-table-cell v-if="row.batchGroup && row.group || isDisabledForm">
                                            {{ row.batchNo }}
                                        </abc-table-cell>
                                        <abc-table-cell
                                            v-else
                                            style="width: 100%; cursor: pointer;"
                                            @click.native="handleSelectBatches(row)"
                                        >
                                            <div v-abc-title.ellipsis="`已选择${ row.batchs && row.batchs.length || 0 }个批次`"></div>
                                        </abc-table-cell>
                                    </template>
                                </template>
                                <template #clearBucketDescription="{ trData: row }">
                                    <template v-if="isUseClinicBatchComponent">
                                        <template v-if="row.batchGroup && !row.group">
                                            <template v-if="row.groupEdit">
                                                <abc-input
                                                    v-model="row.clearBucketDescription"
                                                    :max-length="300"
                                                    @enter="enterEvent"
                                                ></abc-input>
                                            </template>
                                        </template>
                                        <abc-table-cell v-else-if="isDisabledForm" class="show-text-box">
                                            <abc-text-tips
                                                :content="row.clearBucketDescription"
                                                :width="140"
                                            ></abc-text-tips>
                                        </abc-table-cell>
                                        <abc-form-item v-else>
                                            <abc-input
                                                v-model="row.clearBucketDescription"
                                                :max-length="300"
                                                @enter="enterEvent"
                                            ></abc-input>
                                        </abc-form-item>
                                    </template>
                                    <template v-else>
                                        <abc-table-cell v-if="row.batchGroup && !row.group"></abc-table-cell>
                                        <abc-table-cell v-else-if="isDisabledForm" class="show-text-box">
                                            <abc-text-tips
                                                :content="row.clearBucketDescription"
                                                :width="140"
                                            ></abc-text-tips>
                                        </abc-table-cell>
                                        <abc-form-item v-else>
                                            <abc-input
                                                v-model="row.clearBucketDescription"
                                                :max-length="300"
                                                @enter="enterEvent"
                                            ></abc-input>
                                        </abc-form-item>
                                    </template>
                                </template>
                                <template #position="{ trData: row }">
                                    <abc-table-cell v-if="row.batchGroup && row.group"></abc-table-cell>
                                    <abc-form-item v-else>
                                        <abc-input
                                            v-model="row.position"
                                            :disabled="isDisabledForm"
                                            @change="()=>{
                                                changePosition(row)
                                            }"
                                            @enter="enterEvent"
                                        ></abc-input>
                                    </abc-form-item>
                                </template>
                                <template #goodsName="{ trData: row }">
                                    <abc-table-cell v-if="row.batchGroup && row.group"></abc-table-cell>
                                    <abc-table-cell v-else style="height: 44px;">
                                        <abc-layout
                                            v-abc-goods-hover-popper="{
                                                goods: row.productInfo,
                                                showShebaoCode: true,
                                                showF1: row.productInfo.type === GoodsTypeEnum.MEDICINE,
                                            }"
                                        >
                                            <abc-title v-abc-title.ellipsis="row.displayName"></abc-title>
                                            <abc-p
                                                v-abc-title.ellipsis="`${row.displaySpec || ''} ${row.manufacturer || ''}`"
                                                style="line-height: 14px;"
                                                gray
                                            >
                                            </abc-p>
                                        </abc-layout>
                                    </abc-table-cell>
                                </template>
                                <template #unit="{ trData: row }">
                                    <template v-if="isUseClinicBatchComponent">
                                        <template v-if="row.batchGroup && !row.group">
                                            <template v-if="row.groupEdit">
                                                <abc-form-item>
                                                    <abc-input
                                                        v-model="row.unit"
                                                        :disabled="true"
                                                    ></abc-input>
                                                </abc-form-item>
                                            </template>
                                            <template v-else>
                                                <abc-table-cell></abc-table-cell>
                                            </template>
                                        </template>
                                        <abc-form-item v-else required>
                                            <abc-input
                                                v-model="row.unit"
                                                :disabled="true"
                                            ></abc-input>
                                        </abc-form-item>
                                    </template>
                                    <template v-else>
                                        <abc-table-cell v-if="row.batchGroup && !row.group"></abc-table-cell>
                                        <abc-form-item v-else required>
                                            <abc-input
                                                v-model="row.unit"
                                                :disabled="true"
                                            ></abc-input>
                                        </abc-form-item>
                                    </template>
                                </template>
                                <template #clearBucketCount="{ trData: row }">
                                    <template v-if="isUseClinicBatchComponent">
                                        <template v-if="row.batchGroup && !row.group">
                                            <template v-if="row.groupEdit">
                                                <abc-form-item :validate-event="validateClearCount">
                                                    <abc-input
                                                        v-model="row.clearBucketCount"
                                                        :disabled="isDisabledForm"
                                                        type="number"
                                                        :config="{
                                                            max: 99999999.99,
                                                            min: 0,
                                                            formatLength: 2,
                                                        }"
                                                        @enter="enterEvent"
                                                    ></abc-input>
                                                </abc-form-item>
                                            </template>
                                        </template>
                                        <abc-form-item v-else :validate-event="validateClearCount">
                                            <abc-input
                                                v-model="row.clearBucketCount"
                                                :disabled="isDisabledForm"
                                                type="number"
                                                :config="{
                                                    max: 99999999.99,
                                                    min: 0,
                                                    formatLength: 2,
                                                }"
                                                @enter="enterEvent"
                                            ></abc-input>
                                        </abc-form-item>
                                    </template>
                                    <template v-else>
                                        <abc-table-cell v-if="row.batchGroup && !row.group"></abc-table-cell>
                                        <abc-form-item v-else :validate-event="validateClearCount">
                                            <abc-input
                                                v-model="row.clearBucketCount"
                                                :disabled="isDisabledForm"
                                                type="number"
                                                :config="{
                                                    max: 99999999.99,
                                                    min: 0,
                                                    formatLength: 2,
                                                }"
                                                @enter="enterEvent"
                                            ></abc-input>
                                        </abc-form-item>
                                    </template>
                                </template>
                            </abc-table>
                        </abc-layout-content>
                    </abc-layout>
                </abc-layout-content>
            </abc-layout>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-flex style="flex: 1;" justify="flex-start">
                <abc-button
                    v-if="id && !isChainAdmin"
                    theme="danger"
                    variant="ghost"
                    @click="onClickDelete"
                >
                    {{ isDraft ? '删除草稿' : '删除' }}
                </abc-button>
            </abc-flex>
            <abc-button
                v-if="isShowExport"
                icon="n-upload-line"
                variant="ghost"
                @click="exportExcelCommon(GSPExportEnum.STORAGE.CLEAR_FUNNEL.ORDER,
                                          id,
                                          '清斗记录单据',
                                          { title: originData?.orderNo },
                                          true)"
            >
                导出
            </abc-button>
            <abc-button
                v-if="isVisibleSubmitBtn"
                :loading="loadingSubmit"
                :disabled="disabledSubmit"
                @click="onClickSubmit(0)"
            >
                提交
            </abc-button>
            <abc-button
                v-if="isVisibleSubmitBtn"
                :loading="draftBtnLoading"
                variant="ghost"
                :disabled="disabledSubmit || !isModify"
                @click="onClickSubmit(1)"
            >
                保存草稿
            </abc-button>
            <abc-button
                type="blank"
                @click="closeDialog"
            >
                {{ isDisabledForm ? '关闭' : '取消' }}
            </abc-button>
        </div>
        <order-dialog
            v-if="showOrderSelectDialog"
            v-model="showOrderSelectDialog"
            :is-gsp="true"
            @select-order="selectOrder"
        ></order-dialog>
    </frame-dialog>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import AutoFocus from 'src/views/inventory/mixins/auto-focus';
    import pickerOptions from 'views/common/pickerOptions';
    import clone from 'utils/clone';
    import { pick } from 'utils/index';
    import { mapGetters } from 'vuex';
    import * as options from '@/views-pharmacy/common/options';
    import * as tools from '@/views-pharmacy/common/tools';
    import { DATA_PERMISSION_CONTROL_SCENE } from 'views/inventory/common/data-permission-control';
    import DialogBatches from '@/views-pharmacy/charge/components/dialog-bathces-gsp';
    import { useFillHeightInDialog } from '@/views-pharmacy/inventory/hooks/useFillHeightInDialog';
    import FrameDialog from '@/views-pharmacy/inventory/frames/components/order-frame-dialog.vue';
    import EnterEvent from 'views/common/enter-event';
    import {
        exportExcelCommon, GSPExportEnum,
    } from '@/views-pharmacy/gsp/utils/gspExportUtil';
    import { GoodsTypeEnum } from '@abc/constants';
    import GspBatchSelect from '@/views-pharmacy/gsp/frames/storage/component/gsp-batch-select.vue';
    import AbcTextTips from '@/views-pharmacy/components/abc-text-tips/index.vue';
    import { filterTreeByPharmacyNo } from '@/views-pharmacy/gsp/utils/filterTreeUtils';
    import { GoodsTypeIdEnum } from 'views/inventory/constant';
    import {
        ClearFunnelStatus,
        ClearFunnelStatusName, ClearFunnelStatusTagName,
    } from '@/views-pharmacy/inventory/constant';
    import { isEqual } from '@abc/utils';
    import { removeDuplicatesById } from 'utils/remove-duplicates-by-id';
    const GoodsAutoComplete = () => import('src/views/inventory/common/goods-auto-complete');
    const MedicineSelectionDialog = () => import('@/views-pharmacy/components/medicine-selection-dialog/index.vue');
    const OrderDialog = () => import('@/views-pharmacy/inventory/frames/purchase/return-goods/form.vue');
    export default {
        components: {
            AbcTextTips,
            GspBatchSelect,
            GoodsAutoComplete,
            FrameDialog,
            OrderDialog,
        },
        mixins: [
            AutoFocus,
            pickerOptions,
            EnterEvent,
        ],
        props: {
            id: {
                type: String,
                default: '',
            },
            pharmacy: {
                type: Object,
                default: () => ({}),
            },
            value: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const {
                tableRef,
                dialogBody,
                openDialog,
                fillBottomOffset,
            } = useFillHeightInDialog();
            return {
                tableRef,
                dialogBody,
                openDialog,
                fillBottomOffset,
            };
        },
        data() {
            return {
                GSPExportEnum,
                GoodsTypeEnum,
                DATA_PERMISSION_CONTROL_SCENE,
                options,
                pickerOptions: null,
                searchKeyOperator: '', // 搜索关键词 - 操作人
                loading: false,
                originData: null,
                formData: null,
                cacheFormData: null,
                loadingSubmit: false,
                draftBtnLoading: false,
                fetchParams: {
                    keyword: '',
                    typeId: '',
                    typeLabel: '全部类型',
                    type: [1],
                    subType: [2],
                    cMSpec: '',
                },
                order: {
                    id: '',
                    orderNo: '',
                    status: null,
                },
                showOrderSelectDialog: false,
                batchListData: [],
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'employeeList',
                'userInfo',
                'westernMedicineConfig',
                'multiPharmacyCanUse',
                'isChainAdmin',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            isModify() {
                return !isEqual(this.cacheFormData, this.formData);
            },
            disabledSubmit() {
                return !this.formData?.medicineClearBucketLists?.length;
            },
            isDraft() {
                return this.id && this.order.status === ClearFunnelStatus.DRAFT;
            },
            statusName() {
                return ClearFunnelStatusName[this.order.status];
            },
            tagConfig() {
                const config = {
                    shape: 'square',
                    theme: 'success',
                    variant: 'outline',
                };
                config.theme = ClearFunnelStatusTagName[this.order.status] || 'success';
                return config;
            },
            isUseClinicBatchComponent() {
                return this.viewDistributeConfig.Gsp.storage.clearFunnel.isUseClinicBatchComponent;
            },
            renderConfig() {
                return {
                    list: [
                        {
                            'key': 'shortId',
                            'label': '商品编码',
                            'style': {
                                maxWidth: '120px',
                                width: '120px',
                            },
                            dataFormatter: (_, row) => {
                                const {
                                    batchGroup, group,
                                } = row;
                                if (batchGroup && group) {
                                    return '';
                                }
                                return row.shortId;
                            },
                        },
                        {
                            'key': 'goodsName',
                            'label': '商品名称',
                            'style': {
                                flex: 1,
                                minWidth: '160px',
                            },
                        },
                        {
                            'key': 'position',
                            'label': '柜号',
                            sortable: true,
                            'style': {
                                maxWidth: '95px',
                                'width': '95px',
                            },
                        },
                        {
                            'key': 'batchs',
                            'label': '生产批号',
                            'style': {
                                maxWidth: '120px',
                                'width': '120px',
                            },
                        },
                        {
                            'key': 'clearBucketCount',
                            'label': '清斗数量',
                            'style': {
                                maxWidth: '120px',
                                'width': '120px',
                            },
                        },
                        {
                            'key': 'unit',
                            'label': ' 单位',
                            'style': {
                                maxWidth: '95px',
                                minWidth: '95px',
                                'width': '95px',
                            },
                        },
                        {
                            'key': 'productionDate',
                            'label': '生产日期',
                            'style': {
                                maxWidth: '120px',
                                minWidth: '120px',
                                'width': '120px',
                            },
                            dataFormatter: (_, row) => {
                                const {
                                    batchGroup, group,
                                } = row;
                                if (batchGroup && !group) {
                                    return '';
                                }
                                return row.productionDate;
                            },
                        },
                        {
                            'key': 'expiryDate',
                            'label': '有效日期',
                            'style': {
                                maxWidth: '120px',
                                minWidth: '120px',
                                'width': '120px',
                            },
                            dataFormatter: (_, row) => {
                                const {
                                    batchGroup, group,
                                } = row;
                                if (batchGroup && !group) {
                                    return '';
                                }
                                return row.expiryDate;
                            },
                        },
                        {
                            'key': 'clearBucketDescription',
                            'label': '清斗说明',
                            'style': {
                                maxWidth: '140px',
                                minWidth: '140px',
                                'width': '140px',
                            },
                        }],
                };
            },
            // 操作人选项
            operatorOptions() {
                let employeeList = this.employeeList || [];
                if (this.searchKeyOperator) {
                    // 有关键词时，过滤一下
                    employeeList = tools.filterEmployeeBySearchKey(employeeList, this.searchKeyOperator);
                }
                return employeeList.map((item) => ({
                    value: item.employeeId,
                    label: item.employeeName,
                }));
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 是否新增
            isCreate() {
                if (!this.id) {
                    // 没有ID的时候，是新增
                    return true;
                }
                return false;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            // 是否预览
            isPreview() {
                return !(this.isCreate || this.isDraft);
            },
            // 是否禁用表单
            isDisabledForm() {
                return this.isPreview;
            },
            // 标题
            title() {
                return '中药清斗';
            },
            // 是否显示提交按钮
            isVisibleSubmitBtn() {
                return this.isCreate || this.isDraft;
            },
            // 是否显示goods搜索
            isShowGoodsSearch() {
                return this.isCreate || this.isDraft;
            },
            // 是否显示导出按钮
            isShowExport() {
                return !(this.isCreate || this.isDraft);
            },
        },
        created() {
            this.formData = this.createFormData();
            this.pickerOptions = this.createPickerOptions();
        },
        async mounted () {
            this.loading = true;
            await this.fetchMedicineClearBucketInfo();
            // 根据草稿单拉取数据
            await this.fetchBatchListDataByDraft();
            this.formData = this.createFormData();
            this.cacheFormData = clone(this.formData);
            this.loading = false;
        },
        methods: {
            exportExcelCommon,
            removeDuplicatesById,
            validateClearCount(value, callback) {
                if (!value || Number(value) === 0) {
                    callback({
                        validate: false,
                        message: '请填写清斗数量',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            closeDialog() {
                // 新建或者草稿
                if (this.isCreate || this.isDraft) {
                    const confirmTitle = this.isDraft ? '草稿信息发生变动，是否保存？' : '是否需要保存为草稿？';
                    if (this.isModify && !this.disabledSubmit) {
                        const vm = this.$confirm({
                            type: 'warn',
                            title: '提示',
                            content: confirmTitle,
                            showConfirm: false,
                            showCancel: false,
                            footerPrepend: () => {
                                return (
                                    <abc-space>
                                        <abc-button
                                            onClick={() => {
                                                this.onClickSubmit(1);
                                                vm.close();// 手动关闭
                                            }}
                                        >
                                            保存
                                        </abc-button>
                                        <abc-button
                                            type="blank"
                                            onClick={() => {
                                                this.handleCancel();
                                                vm.close();// 手动关闭
                                            }}
                                        >
                                            不保存
                                        </abc-button>
                                    </abc-space>
                                );
                            },
                        });
                    } else {
                        this.handleCancel();
                    }
                } else {
                    this.handleCancel();
                }
            },
            handleCancel() {
                this.showDialog = false;
            },
            async selectOrder(order) {
                const orderList = [];
                order.list.filter((item) => {
                    // 只有中药可以
                    return [GoodsTypeIdEnum.CHINESE_MEDICINE,
                            GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                            GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES,
                            GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE].includes(item.goods.typeId);
                }).forEach((item) => {
                    if (!orderList.some((i) => { return i.goodsId === item.goodsId; })) {
                        orderList.push(item);
                    }
                });
                if (!orderList.length) {
                    this.$Toast({
                        type: 'info',
                        message: '当前入库单内没有中药',
                    });
                    return;
                }
                const filterGoodsList = this.formData.medicineClearBucketLists.map((i) => {
                    return i.goodsId;
                });
                const goodsIds = [];
                (orderList || []).map((item) => item.goodsId)?.forEach((it) => {
                    if (!filterGoodsList.includes(it)) {
                        goodsIds.push(it);
                    }
                });
                if (!goodsIds.length) {
                    this.$alert({
                        type: 'warn',
                        title: '商品重复',
                        content: '本次添加的商品、批次均已存在，无需重复添加',
                    });
                    return;
                }
                // 批量查询批次信息
                const params = {
                    goodsIds,
                    pharmacyNo: this.pharmacy?.pharmacyNo || 0,
                    clinicId: this.clinicId,
                };
                const { data } = await GspAPI.fetchInstallBatchListByGoodsIds(params);
                if (data) {
                    const batchListData = data || [];
                    if (batchListData.length !== goodsIds.length) {
                        this.$alert({
                            type: 'warn',
                            title: '中药未装斗',
                            content: `${goodsIds.length - batchListData.length}种中药无已装斗批次，无法进行清斗，请装斗后操作`,
                        });
                    }
                    const handleOrderList = orderList.filter((it) => {
                        return !!batchListData.find((i) => {
                            return i.goods.id === it.goods.id;
                        });
                    });
                    // 循环插入视图
                    handleOrderList.forEach((goodsInfo) => {
                        const target = batchListData.find((one) => one.goodsId === goodsInfo.goodsId);
                        const batchList = target?.batchs?.map((i) => {
                            return {
                                ...i,
                                goodsId: goodsInfo.goodsId,
                                position: i.position || target.goods.position,
                            };
                        }) || []; // 批次列表
                        goodsInfo.id = goodsInfo.goodsId;
                        goodsInfo.position = target?.goods?.position || '';
                        this.addGoodsInfo({
                            ...goodsInfo,
                            ...goodsInfo.goods,
                        }, batchList);
                        this.setBatchInfo(goodsInfo.goodsId);
                        const medicineClearBucketLists = this.formData.medicineClearBucketLists.find((item) => item.goodsId === goodsInfo.goodsId);
                        this.changeBatchs(medicineClearBucketLists.batchList, medicineClearBucketLists);
                    });
                    this.$Toast({
                        type: 'success',
                        message: '已添加入库单内中药',
                    });
                }
            },
            onClickDelete() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: this.isDraft ? '草稿删除后不可恢复，确认删除吗?' : '清斗记录删除后不可恢复，确认删除吗?',
                    onConfirm: async () => {
                        try {
                            const data = await GspAPI.deleteMedicineClearBucket(this.id);
                            if (data) {
                                this.$Toast({
                                    type: 'success',
                                    message: '删除成功',
                                });
                                this.$emit('refresh');
                                this.showDialog = false;
                            }
                        } catch (e) {
                            console.log(e);
                        }
                    },
                });
            },
            customTrKey(item) {
                return `${item.goodsId}_${item.batchId}_${item.id}`;
            },
            handleSelectBatches(row) {
                if (this.isDisabledForm) {
                    return;
                }
                const batchItem = {
                    ...row.productInfo,
                    ...row,
                    batchs: row.batchs?.map((item) => {
                        return {
                            ...item,
                            clearBucketCount: item.clearBucketCount || this.formData.medicineClearBucketLists.find((i) => {
                                return item.goodsId === i.goodsId && item.batchId === i.batchId;
                            }).clearBucketCount || '',
                        };
                    }) || [],
                };
                this._batcherDialog = new DialogBatches({
                    batchItem,
                    clinicId: this.clinicId,
                    pharmacyNo: this.pharmacy?.pharmacyNo || 0,
                    isClearBucket: true,
                    onConfirm: (item) => {
                        const {
                            dataList = [], batchItem = {},ownList = [],
                        } = item;
                        this.changeBatchs(dataList, batchItem);
                        this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.map((i) => {
                            const current = dataList.find((it) => {return it.goodsId === i.goodsId && it.batchId === i.batchId;});
                            if (current) {
                                const handle = {
                                    ...i,
                                    clearBucketCount: current.clearBucketCount || '',
                                };
                                if (current.clearBucketCount) {
                                    handle.inventoryCountWording = tools.getInventoryCountWording(handle);
                                }
                                return handle;
                            }
                            return {
                                ...i,
                            };
                        });
                        this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.map((i) => {
                            if (batchItem.goodsId === i.goodsId) {
                                const handle = {
                                    ...i,
                                    batchList: ownList.map((item) => {
                                        const current = dataList.find((it) => {return it.goodsId === item.goodsId && it.batchId === item.batchId;});
                                        return {
                                            ...item,
                                            clearBucketCount: current?.clearBucketCount || '',
                                        };
                                    }),
                                };
                                return handle;
                            }
                            return {
                                ...i,
                            };
                        });
                    },
                });
                this._batcherDialog.generateDialogAsync({
                    parent: this,
                });
            },
            changePosition(row) {
                const {
                    goodsId, position,
                } = row;
                this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.map((item) => {
                    if (item.batchId !== -1 && item.goodsId === goodsId) {
                        return {
                            ...item,
                            position,
                        };
                    }
                    return {
                        ...item,
                    };
                });
            },
            sortChange(res) {
                console.log('sortChange', res);
                const {
                    orderType, orderBy,
                } = res;
                return this.formData.medicineClearBucketLists.sort((a, b) => {
                    if (orderType === 'asc') {
                        return a[orderBy] - b[orderBy];
                    }
                    return b[orderBy] - a[orderBy];
                });
            },
            getBatch(batchId, batchList, goodsInfo, currentItem = null) {
                const batchInfo = batchList.find((one) => one.batchId === batchId);
                const clearBucketCount = currentItem?.clearBucketCount || ((batchInfo.pieceCount > 0) ? batchInfo.pieceCount : '') || '';
                const info = {
                    batchList, // 批次列表
                    sort: '', // 排序
                    id: '', // 中药清斗记录ID
                    goodsId: goodsInfo.goodsId, // 商品ID
                    goodsName: goodsInfo.goodsName, // 商品名称
                    position: goodsInfo.position, // 柜号
                    spec: goodsInfo.spec, // 规格
                    unit: goodsInfo.packageUnit || 'g', // 单位
                    batchNo: batchInfo.batchNo, // 商品生产批次号
                    expiryDate: '', // 效期
                    manufacturer: goodsInfo.manufacturer, // 生产厂家
                    productionDate: '', // 生产日期
                    clearBucketCount, // 清斗数量
                    clearBucketDescription: currentItem?.clearBucketDescription || '', // 清斗说明
                    // 追加字段
                    shortId: goodsInfo.shortId, // 商品编码
                    displayName: goodsInfo.displayName, // 商品名称
                    displaySpec: goodsInfo.displaySpec, // 商品规格
                    goodsInfoHtml: tools.createGoodsInfoHtml(goodsInfo),
                    productInfo: goodsInfo,
                };
                Object.assign(info, {
                    expiryDate: batchInfo.expiryDate, // 效期
                    productionDate: batchInfo.productDate || batchInfo.productionDate, // 生产日期
                    batchId: batchInfo.batchId,
                });
                return info;
            },
            changeBatchs(batchs, item) {
                const list = [];
                // 有批次数据 添加或者删除
                if (batchs?.length) {
                    // 1找到第一个数组的下标
                    let medicineMaintenancesIndex = this.formData.medicineClearBucketLists.findIndex((i) => {
                        return i.goodsId === item.goodsId;
                    });
                    // 先增加一条置顶的数据做切换
                    list.push({
                        batchList: this.isUseClinicBatchComponent ? [] : item.batchList, // 批次列表
                        goodsId: item.goodsId,
                        batchGroup: true,
                        batchId: -1,
                        batchs: this.isUseClinicBatchComponent ? [] : batchs,
                        position: item.position,
                        shortId: item.shortId, // 商品编码
                        displayName: item.displayName, // 商品名称
                        displaySpec: item.displaySpec, // 商品规格
                        manufacturer: item.manufacturer || item.manufacturerFull, // 生产厂商
                        goodsInfoHtml: tools.createGoodsInfoHtml(item),
                        productInfo: item,
                        unit: item.packageUnit || 'g',
                        groupEdit: true,
                    });
                    if (!this.isUseClinicBatchComponent) {
                        batchs.forEach((batchItem) => {
                            // 同样的数据非一个批次
                            const currentItem = this.formData.medicineClearBucketLists?.find((i) => {
                                return i.goodsId === item.goodsId && i.batchId === batchItem.batchId;
                            });
                            // 获取每个批次的商品
                            const it = this.getBatch(batchItem.batchId, item.batchList, item, currentItem);
                            list.push({
                                ...it,
                                group: true,
                                batchGroup: true,
                            });
                        });
                    }
                    // 下标为0 删除数据再组合
                    if (medicineMaintenancesIndex === 0) {
                        this.formData.medicineClearBucketLists = list.concat(this.formData.medicineClearBucketLists.filter((i) => {
                            return i.goodsId !== item.goodsId;
                        }));
                    } else {
                        // 插入的下标为当前的下标 -1
                        medicineMaintenancesIndex = medicineMaintenancesIndex - 1;
                        this.formData.medicineClearBucketLists = this.insertArrayAtIndex(this.formData.medicineClearBucketLists.filter((i) => {
                            return i.goodsId !== item.goodsId;
                        }), medicineMaintenancesIndex, list);
                    }
                } else {
                    // 没有选择任何对象 只保留第一个批次的数据 没有批次数据需要删除空对象
                    const medicineMaintenancesItem = this.formData.medicineClearBucketLists.find((i) => {
                        return i.goodsId === item.goodsId && i.batchId !== -1;
                    });
                    const { batchId } = medicineMaintenancesItem;
                    delete medicineMaintenancesItem.batchGroup;
                    // 删除分组标志
                    delete medicineMaintenancesItem.group;
                    // 删除头部对象
                    this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.filter((i) => {
                        return i.goodsId !== item.goodsId || (i.goodsId === item.goodsId && i.batchId === batchId && i.batchId !== -1);
                    });
                }
            },
            changeBatchItem(batchs, row) {
                const list = [];
                // 1找到第一个数组的下标
                let medicineMaintenancesIndex = this.formData.medicineClearBucketLists?.findIndex((i) => {
                    return i.goodsId === row.goodsId;
                });
                // 先增加一条置顶的数据做切换
                list.push({
                    batchList: row.batchList, // 批次列表
                    batchs,
                    goodsId: row.goodsId,
                    batchGroup: true,
                    groupEdit: !batchs.length,
                    batchId: -1,
                    position: row.position,
                    shortId: row.shortId, // 商品编码
                    displayName: row.displayName, // 商品名称
                    displaySpec: row.displaySpec, // 商品规格
                    manufacturer: row.manufacturer || row.manufacturerFull, // 生产厂商
                    goodsInfoHtml: tools.createGoodsInfoHtml(row),
                    productInfo: row,
                    unit: row.packageUnit || 'g', // 单位
                });
                batchs.forEach((batchItem) => {
                    // 同样的数据非一个批次
                    const currentItem = this.formData.medicineClearBucketLists?.find((i) => {
                        return i.goodsId === row.goodsId && i.batchId === batchItem.batchId;
                    });
                    // 获取每个批次的商品
                    const it = this.getBatch(batchItem.batchId, row.batchList, row, currentItem);
                    list.push({
                        ...it,
                        group: true,
                        batchGroup: true,
                    });
                });
                // 下标为0 删除数据再组合
                if (medicineMaintenancesIndex === 0) {
                    this.formData.medicineClearBucketLists = list.concat(this.formData.medicineClearBucketLists.filter((i) => {
                        return i.goodsId !== row.goodsId;
                    }));
                } else {
                    // 插入的下标为当前的下标 -1
                    medicineMaintenancesIndex = medicineMaintenancesIndex - 1;
                    this.formData.medicineClearBucketLists = this.insertArrayAtIndex(this.formData.medicineClearBucketLists.filter((i) => {
                        return i.goodsId !== row.goodsId;
                    }), medicineMaintenancesIndex, list);
                }
            },
            async fetchBatchListDataByDraft() {
                if (!this.isDraft) {
                    return;
                }
                const {
                    medicineClearBucketViews, // 中药装斗列表
                } = this.originData;
                // 从详情接口中拿到相关数据
                const params = {
                    goodsIds: medicineClearBucketViews?.map((item) => item?.goods?.goodsId),
                    pharmacyNo: this.pharmacy?.pharmacyNo || 0,
                    clinicId: this.clinicId,
                };
                params.goodsIds = Array.from(new Set(params.goodsIds));
                const fetchResponse = await GspAPI.fetchInstallBatchListByGoodsIds(params);
                if (fetchResponse.status === true) {
                    this.batchListData = fetchResponse.data?.flatMap((it) => {
                        return it?.batchs?.map((batch) => {
                            return {
                                ...batch,
                                ...it,
                                ...it.goods,
                            };
                        });
                    }) || [];
                }
            },
            /**
             * 查询中药清斗详情
             * <AUTHOR>
             * @date 2024-01-03
             */
            async fetchMedicineClearBucketInfo() {
                if (!this.id) {
                    return;
                }
                const fetchResponse = await GspAPI.fetchMedicineClearBucketInfo(this.id);
                if (fetchResponse.status === true) {
                    this.originData = fetchResponse.data;
                    this.order = fetchResponse.data;
                }
            },
            /**
             * 创建表单数据
             * <AUTHOR>
             * @date 2024-01-03
             * @returns {Object}
             */
            createFormData() {
                const formData = {
                    clinicName: this.currentClinic?.clinicName || '', // 门店名称
                    pharmacyName: this.pharmacy?.pharmacyName || '', // 库房名称
                    operator: (() => {
                        // 操作人，默认当前人员，没有就选第一个
                        const target = this.operatorOptions.find((item) => item.value === this.userInfo?.id) || this.operatorOptions[0];
                        return target?.value || '';
                    })(), // 操作人id
                    operationTime: tools.getDatetimeFormat(), // 操作时间
                    remark: '', // 备注
                    medicineClearBucketLists: [], // 中药清斗记录列表
                };
                if (this.originData) {
                    const {
                        operator, // 操作人id
                        operationTime, // 操作时间
                        remark, // 备注
                        medicineClearBucketViews = [], // 中药清斗列表
                        clinicName, // 门店名称
                        pharmacyName,
                    } = this.originData;
                    Object.assign(formData, {
                        clinicName,
                        pharmacyName,
                        operator: operator?.id, // 操作人id
                        operationTime: tools.getDatetimeFormat(operationTime), // 操作时间
                        remark, // 备注
                    });
                    if (this.isDraft) {
                        Object.assign(formData, {
                            // 中药清斗记录列表
                            // eslint-disable-next-line no-use-before-define
                            medicineClearBucketLists: (medicineClearBucketViews || []).map((item) => {
                                const info = {
                                    batchList: [], // 批次列表
                                    sort: item.sort, // 排序
                                    id: item.id, // 中药清斗记录ID
                                    goodsId: item.goodsId, // 商品ID
                                    goodsName: item.goodsName, // 商品名称
                                    position: item.position, // 柜号
                                    spec: item.spec, // 规格
                                    unit: item.unit, // 单位
                                    batchNo: item.batchNo, // 商品生产批次号
                                    batchId: item.batchId, // 商品生产批次号 内部使用
                                    expiryDate: item.expiryDate, // 效期
                                    manufacturer: item.manufacturer, // 生产厂家
                                    productionDate: item.productionDate, // 生产日期
                                    clearBucketCount: item.clearBucketCount, // 清斗数量
                                    clearBucketDescription: item.clearBucketDescription, // 清斗说明
                                    // 追加字段
                                    shortId: item.goods?.shortId, // 商品编码
                                    displayName: item.goods?.displayName, // 商品名称
                                    displaySpec: item.goods?.displaySpec, // 商品规格
                                    goodsInfoHtml: tools.createGoodsInfoHtml(item.goods),
                                    productInfo: item.goods,
                                };
                                info.batchList = this.batchListData.filter((one) => one.goodsId === info.goodsId);
                                info.batchList = this.removeDuplicatesById(info.batchList);
                                // 不应用诊所主题切换
                                if (!this.isUseClinicBatchComponent) {
                                    // 默认选第一个
                                    const batchInfo = info.batchList[0];
                                    if (batchInfo) {
                                        Object.assign(info, {
                                            batchNo: batchInfo.batchNo, // 商品生产批次号
                                            batchId: batchInfo.batchId, // 商品生产批次号
                                            expiryDate: batchInfo.expiryDate, // 效期
                                            productionDate: batchInfo.productDate ?? info?.productionDate, // 生产日期
                                        });
                                    }
                                }
                                // 说明存在重复数据
                                if ((medicineClearBucketViews || []).filter((i) => {
                                    return i.goodsId === item.goodsId;
                                }).length >= 1) {
                                    const batchInfo = info.batchList.find((one) => one.batchId === item.batchId) || {};
                                    Object.assign(info, {
                                        batchId: batchInfo.batchId || '', // 商品生产批次号
                                        batchNo: batchInfo.batchNo, // 商品生产批次号
                                        expiryDate: batchInfo.expiryDate, // 效期
                                        productionDate: batchInfo.productDate ?? info?.productionDate, // 生产日期
                                        group: true,
                                        batchGroup: true,
                                    });
                                }
                                return info;
                            }),
                        });
                        let goodsIds = formData.medicineClearBucketLists.map((i) => {
                            return i.goodsId;
                        });
                        goodsIds = Array.from(new Set(goodsIds));
                        let medicineClearBucketLists = [];
                        goodsIds.forEach((goodsId) => {
                            const medicineClearBucketsItem = formData.medicineClearBucketLists.find((it) => {
                                return it.goodsId === goodsId && it.group;
                            });
                            // 有分组
                            if (medicineClearBucketsItem) {
                                const list = formData.medicineClearBucketLists.filter((it) => {
                                    return it.goodsId === goodsId;
                                }) || [];
                                if (this.isUseClinicBatchComponent) {
                                    list.unshift({
                                        batchList: list[0].batchList, // 批次列表
                                        goodsId: list[0].goodsId,
                                        batchGroup: true,
                                        batchId: -1,
                                        batchs: list.filter((it) => {
                                            return it.batchId !== -1;
                                        }),
                                        position: list[0].position,
                                        shortId: list[0].shortId, // 商品编码
                                        displayName: list[0].displayName, // 商品名称
                                        displaySpec: list[0].displaySpec, // 商品规格
                                        manufacturer: list[0].manufacturer || list[0].manufacturerFull, // 生产厂商
                                        goodsInfoHtml: tools.createGoodsInfoHtml(list[0]),
                                        productInfo: list[0],
                                        unit: list[0].unit,
                                        clearBucketCount: list[0].clearBucketCount,
                                        clearBucketDescription: list[0].clearBucketDescription,
                                    });
                                    const onlyGroup = list[0].batchs.find((batchItem) => {
                                        return batchItem.batchId === '';
                                    });
                                    if (onlyGroup) {
                                        list[0].groupEdit = true;
                                        medicineClearBucketLists.push(list[0]);
                                    } else {
                                        list[0].groupEdit = false;
                                        list[0].clearBucketCount = '';
                                        list[0].clearBucketDescription = '';
                                        medicineClearBucketLists = medicineClearBucketLists.concat(list);
                                    }
                                } else {
                                    list.unshift({
                                        batchList: list[0].batchList, // 批次列表
                                        goodsId: list[0].goodsId,
                                        batchGroup: true,
                                        batchId: -1,
                                        batchs: list.filter((it) => {
                                            return it.batchId !== -1;
                                        }),
                                        position: list[0].position,
                                        shortId: list[0].shortId, // 商品编码
                                        displayName: list[0].displayName, // 商品名称
                                        displaySpec: list[0].displaySpec, // 商品规格
                                        manufacturer: list[0].manufacturer || list[0].manufacturerFull, // 生产厂商
                                        goodsInfoHtml: tools.createGoodsInfoHtml(list[0]),
                                        productInfo: list[0],
                                    });
                                    medicineClearBucketLists = medicineClearBucketLists.concat(list);
                                }
                            } else {
                                medicineClearBucketLists = medicineClearBucketLists.concat(formData.medicineClearBucketLists.filter((it) => {
                                    return it.goodsId === goodsId;
                                }));
                            }
                        });
                        formData.medicineClearBucketLists = medicineClearBucketLists;
                    } else {
                        Object.assign(formData, {
                            // 中药清斗记录列表
                            medicineClearBucketLists: (medicineClearBucketViews || []).map((item) => ({
                                batchList: [{ batchNo: item.batchNo }], // 批次列表
                                sort: item.sort, // 排序
                                id: item.id, // 中药清斗记录ID
                                goodsId: item.goodsId, // 商品ID
                                goodsName: item.goodsName, // 商品名称
                                position: item.position, // 柜号
                                spec: item.spec, // 规格
                                unit: item.unit, // 单位
                                batchNo: item.batchNo, // 商品生产批次号
                                batchId: item.batchId, // 商品生产批次号 内部使用
                                expiryDate: item.expiryDate, // 效期
                                manufacturer: item.manufacturer, // 生产厂家
                                productionDate: item.productionDate, // 生产日期
                                clearBucketCount: item.clearBucketCount, // 清斗数量
                                clearBucketDescription: item.clearBucketDescription, // 清斗说明
                                // 追加字段
                                shortId: item.goods?.shortId, // 商品编码
                                displayName: item.goods?.displayName, // 商品名称
                                displaySpec: item.goods?.displaySpec, // 商品规格
                                goodsInfoHtml: tools.createGoodsInfoHtml(item.goods),
                                productInfo: item.goods,
                            })),
                        });
                    }
                }
                return formData;
            },
            insertArrayAtIndex(originalArray, index, arrayToInsert) {
                if (index < 0 || index > originalArray.length) {
                    return originalArray;
                }
                // 使用 slice() 方法拆分原始数组
                const firstHalf = originalArray.slice(0, index + 1);
                const secondHalf = originalArray.slice(index + 1);

                // 将要插入的数组插入到两个拆分后的数组之间
                const resultArray = firstHalf.concat(arrayToInsert, secondHalf);

                return resultArray;
            },
            /**
             * 当选择一个商品时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} goods
             */
            async onSelectGoods(goods) {
                this.fetchParams.keyword = '';
                const isExist = this.formData.medicineClearBucketLists.some((item) => item.goodsId === goods.goodsId);
                if (isExist) {
                    return this.$Toast({
                        type: 'error',
                        message: `重复添加：${goods.medicineCadn || goods.name}已存在`,
                        duration: 2000,
                    });
                }
                // 查询批次列表
                const fetchResponse = await this.fetchBatchList(goods.goodsId, 0);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                if (!fetchResponse.data?.[0]?.batchs?.length) {
                    this.$alert({
                        type: 'warn',
                        title: '中药未装斗',
                        content: `"${goods.medicineCadn || goods.name}"无已装斗批次，无法进行清斗，请装斗后操作`,
                    });
                    return;
                }
                const batchList = fetchResponse.data?.[0]?.batchs?.map((i) => {
                    return {
                        ...i,
                        checked: false,
                        goods: fetchResponse.data?.[0]?.goods || {},
                        goodsId: fetchResponse.data?.[0]?.goods?.id || '',
                        position: fetchResponse.data?.[0]?.goods?.position || '',
                    };
                }) || [];

                // 将商品插入
                this.addGoodsInfo({
                    ...goods, ...fetchResponse.data?.[0]?.goods,
                }, batchList);
                this.setBatchInfo(goods.goodsId);
                const medicineClearBucketLists = this.formData.medicineClearBucketLists.find((item) => item.goodsId === goods.goodsId);
                this.changeBatchs(medicineClearBucketLists.batchList, medicineClearBucketLists);
                const medicineClearBucketListLength = this.formData.medicineClearBucketLists.length;
                const batchListLength = batchList.length;
                if (batchList.length > 1) {
                    this.$nextTick(() => this.autoFocus(medicineClearBucketListLength - batchListLength + 1, false, '.abc-input__inner'));
                } else {
                    this.$nextTick(() => this.autoFocus('', false, '.abc-input__inner'));
                }
            },
            /**
             * 添加goods
             * <AUTHOR>
             * @date 2024-01-29
             * @param {Object} goodsInfo
             * @param {Array} batchList
             */
            addGoodsInfo(goodsInfo, batchList, isDefaultFirst = true) {
                let batchNo = goodsInfo?.batchNo || '';
                if (!batchNo && isDefaultFirst) {
                    batchNo = batchList[0]?.batchNo || '';
                }
                this.formData.medicineClearBucketLists.push({
                    batchList, // 批次列表
                    sort: '', // 排序
                    id: '', // 中药清斗记录ID
                    goodsId: goodsInfo.goodsId, // 商品ID
                    goodsName: goodsInfo.goodsName, // 商品名称
                    position: goodsInfo.position, // 柜号
                    spec: goodsInfo.spec, // 规格
                    unit: goodsInfo.packageUnit || 'g', // 单位
                    batchNo, // 商品生产批次号
                    expiryDate: '', // 效期
                    manufacturer: goodsInfo.manufacturer, // 生产厂家
                    productionDate: '', // 生产日期
                    clearBucketCount: '', // 清斗数量 默认传入总量
                    clearBucketDescription: '', // 清斗说明
                    // 追加字段
                    shortId: goodsInfo.shortId, // 商品编码
                    displayName: goodsInfo.displayName, // 商品名称
                    displaySpec: goodsInfo.displaySpec, // 商品规格
                    goodsInfoHtml: tools.createGoodsInfoHtml(goodsInfo),
                    productInfo: goodsInfo,
                });
            },
            /**
             * 设置批次信息
             * <AUTHOR>
             * @date 2024-01-24
             */
            setBatchInfo(goodsId) {
                const medicineClearBucketItem = this.formData.medicineClearBucketLists.find((item) => item.goodsId === goodsId);
                if (!medicineClearBucketItem) {
                    return;
                }
                if (!medicineClearBucketItem.batchNo) {
                    return;
                }
                const batchInfo = (medicineClearBucketItem.batchList || []).find((item) => item.batchNo === medicineClearBucketItem.batchNo);
                if (!batchInfo) {
                    return;
                }
                Object.assign(medicineClearBucketItem, {
                    expiryDate: batchInfo.expiryDate, // 效期
                    productionDate: batchInfo.productionDate || batchInfo.productDate, // 生产日期
                    batchId: batchInfo.batchId,
                });
            },
            /**
             * 获取批次列表
             * <AUTHOR>
             * @date 2024-01-24
             * @param {String} goodsId
             * @returns {Promise<Response>}
             */
            async fetchBatchList(goodsId, pharmacyNo = '') {
                const params = {
                    clinicId: this.clinicId,
                    // batchNo: '',
                    goodsIds: [goodsId],
                    // supplierId: '',
                    // offset: 0,
                    // limit: 99,
                    pharmacyNo,
                    // batchViewMode: '',
                    // expiredWarn: '',
                    // costPriceWarn: '',
                    // scene: 1,
                };
                const fetchResponse = await GspAPI.fetchInstallBatchListByGoodsIds(params);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                return fetchResponse;
            },
            /**
             * 当点击删除一行时触发
             * <AUTHOR>
             * @date 2024-01-22
             * @param {String} goodsId
             */
            onClickDeleteTr(index) {
                if (this.isUseClinicBatchComponent) {
                    const {
                        goodsId, batchId,
                    } = this.formData.medicineClearBucketLists[index];
                    // -1 代表全部批次 删除全部批次
                    if (batchId === -1) {
                        this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.filter((item) => {
                            return item.goodsId !== goodsId;
                        });
                        return;
                    }
                    // 非全部批次 找到这个类型的商品的第一个元素
                    const medicineMaintenancesItem = this.formData.medicineClearBucketLists.find((item) => {
                        return item.goodsId === goodsId;
                    });
                    // 如果第一条是分组数据
                    if (medicineMaintenancesItem.batchId === -1) {
                        medicineMaintenancesItem.batchList = medicineMaintenancesItem.batchList.filter((item) => {
                            return item.batchNo !== this.formData.medicineClearBucketLists[index].batchNo;
                        });
                        medicineMaintenancesItem.batchs = medicineMaintenancesItem.batchs.filter((item) => {
                            return item.batchId !== this.formData.medicineClearBucketLists[index].batchId;
                        });
                        medicineMaintenancesItem.groupEdit = true;
                        this.formData.medicineClearBucketLists.splice(index, 1);
                    } else {
                        // 不是分组应该直接删除
                        this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.filter((item) => {
                            return !(item.goodsId === goodsId && item.batchId === batchId);
                        });
                    }
                } else {
                    const {
                        goodsId, batchId,
                    } = this.formData.medicineClearBucketLists[index];
                    // -1 代表全部批次 删除全部批次
                    if (batchId === -1) {
                        this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.filter((item) => {
                            return item.goodsId !== goodsId;
                        });
                        return;
                    }
                    // 非全部批次 找到这个类型的商品的第一个元素
                    const medicineMaintenancesItem = this.formData.medicineClearBucketLists.find((item) => {
                        return item.goodsId === goodsId;
                    });
                    // 如果第一条是分组数据
                    if (medicineMaintenancesItem.batchId === -1) {
                        // 如果同类型数据只有两条
                        if (this.formData.medicineClearBucketLists.filter((item) => {
                            return item.goodsId === goodsId;
                        }).length === 2) {
                            // 删除分组数据
                            this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.filter((item) => {
                                return !(item.goodsId === goodsId && item.batchId === -1);
                            });
                            // 恢复数据正常 不做删除
                            const currentItem = this.formData.medicineClearBucketLists.find((item) => {
                                return item.goodsId === goodsId;
                            });
                            delete currentItem.group;
                            delete currentItem.batchGroup;
                            // 代表有更多数据
                        } else {
                            // 直接删除
                            this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.filter((item) => {
                                return !(item.goodsId === goodsId && item.batchId === batchId);
                            });
                            // 将剩余数据传给第一个对象
                            const medicineMaintenancesBatchNos = this.formData.medicineClearBucketLists.filter((item) => {
                                return item.goodsId === goodsId && item.batchId !== -1;
                            }).map((i) => {
                                return i.batchId;
                            });
                            medicineMaintenancesItem.batchs = medicineMaintenancesItem.batchList.filter((i) => {
                                return medicineMaintenancesBatchNos.includes(i.batchId);
                            });
                        }
                    } else {
                        // 不是分组应该直接删除
                        this.formData.medicineClearBucketLists = this.formData.medicineClearBucketLists.filter((item) => {
                            return !(item.goodsId === goodsId && item.batchId === batchId);
                        });
                    }
                }
            },
            openOrderSelect() {
                this.showOrderSelectDialog = true;
            },

            /**
             * 当点击批量添加药品时
             * <AUTHOR>
             * @date 2024-01-29
             */
            async onClickBatchAddition() {
                const openResponse = await tools.openDialog({
                    propsData: {
                        visible: true,
                        defaultCheckedKeys: (this.formData?.medicineClearBucketLists || []).map((item) => item.goodsId),
                        filterFn: this.filterFn, // 只显示中药
                        searchParams: {
                            typeId: [GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES, GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE, GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES],
                            pharmacyNo: this.pharmacy?.pharmacyNo || 0,
                        },
                    },
                    component: MedicineSelectionDialog,
                });
                if (openResponse.status === false) {
                    return openResponse;
                }
                // 批量查询批次信息
                const params = {
                    goodsIds: (openResponse.data || []).map((item) => item.id),
                    pharmacyNo: this.pharmacy?.pharmacyNo || 0,
                    clinicId: this.clinicId,
                };
                params.goodsIds = Array.from(new Set(params.goodsIds));
                const fetchResponse = await GspAPI.fetchInstallBatchListByGoodsIds(params);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                const batchListData = fetchResponse.data || [];
                const addGoodsItemList = openResponse.data;
                if (batchListData.length !== addGoodsItemList.length) {
                    this.$alert({
                        type: 'warn',
                        title: '中药未装斗',
                        content: `${addGoodsItemList.length - batchListData.length}种中药无已装斗批次，无法进行清斗，请装斗后操作`,
                    });
                }
                const handleAddGoodsItemList = (addGoodsItemList || []).filter((it) => {
                    return !!batchListData.find((i) => {
                        return i.goods.id === it.id;
                    });
                });
                // 循环插入视图
                handleAddGoodsItemList.forEach((goodsInfo) => {
                    const target = batchListData.find((one) => one.goodsId === goodsInfo.id);
                    const batchList = target?.batchs?.map((i) => {
                        return {
                            ...i,
                            goodsId: goodsInfo.id,
                            position: i.position || target.goods.position,
                        };
                    }) || []; // 批次列表
                    goodsInfo.goodsId = goodsInfo.id;
                    goodsInfo.position = target?.goods?.position || '';
                    this.addGoodsInfo(goodsInfo, batchList);
                    this.setBatchInfo(goodsInfo.id);
                    const medicineClearBucketLists = this.formData.medicineClearBucketLists.find((item) => item.goodsId === goodsInfo.id);
                    this.changeBatchs(medicineClearBucketLists.batchList, medicineClearBucketLists);
                });
            },
            /**
             * 创建提交数据
             * <AUTHOR>
             * @date 2024-01-03
             * @returns {Object}
             */
            createPostData() {
                const formData = clone(this.formData);
                formData.operationTime += ':00';
                formData.medicineClearBucketLists.forEach((item, index) => {
                    item.sort = index;
                });
                const postData = {
                    pharmacyNo: this.pharmacy.pharmacyNo || 0, // 药房编号
                    operator: '', // 操作人id
                    operationTime: '', // 操作时间
                    remark: '', // 备注
                    // 中药清斗记录列表
                    medicineClearBucketLists: [
                        {
                            sort: '', // 排序
                            id: '', // 中药清斗记录ID
                            goodsId: '', // 商品ID
                            goodsName: '', // 商品名称
                            position: '', // 柜号
                            spec: '', // 规格
                            unit: '', // 单位
                            batchNo: '', // 商品生产批次号
                            batchId: '',
                            expiryDate: '', // 效期
                            manufacturer: '', // 生产厂家
                            productionDate: '', // 生产日期
                            clearBucketCount: '', // 清斗数量
                            clearBucketDescription: '', // 清斗说明
                        },
                    ],
                    status: ClearFunnelStatus.FINISHED,
                };
                return pick(postData, formData);
            },
            /**
             * 当点击提交时
             * <AUTHOR>
             * @date 2023-12-25
             */
            async onClickSubmit(isDraft = 0) {
                if (this.loadingSubmit || this.draftBtnLoading) {
                    return;
                }
                if (isDraft === 1) {
                    await this.createdOrder(isDraft);
                    return;
                }
                if (this.formData?.medicineClearBucketLists.find((item) => {return !item.clearBucketCount && item.batchId !== -1;})) {
                    this.$refs.tableRef.scrollToElement({
                        index: this.formData?.medicineClearBucketLists.findIndex((item) => {return !item.clearBucketCount && item.batchId !== -1;}),
                        top: 0,
                        time: 60,
                    });
                    const timer = setTimeout(() => {
                        this.$nextTick(() => {
                            this.$refs.formData.validate();
                        });
                        clearTimeout(timer);
                    }, 200);
                    return;
                }
                this.$refs.formData.validate(async (valid) => {
                    if (!valid) {
                        return;
                    }
                    await this.createdOrder();
                });
            },
            async createdOrder(isDraft = 0) {
                try {
                    if (isDraft) {
                        this.draftBtnLoading = true;
                    } else {
                        this.loadingSubmit = true;
                    }

                    const postData = this.createPostData();
                    if (isDraft) {
                        postData.status = ClearFunnelStatus.DRAFT;
                    }
                    if (this.isUseClinicBatchComponent) {
                        postData.medicineClearBucketLists = postData.medicineClearBucketLists.filter((item) => {
                            return item.batchId !== -1 || (item.batchId === -1 && item.clearBucketCount !== '');
                        });
                    } else {
                        postData.medicineClearBucketLists = postData.medicineClearBucketLists.filter((item) => {
                            // 非分组或者是分组但不是假数据
                            return item.batchId !== -1;
                        });
                    }
                    let data = null;
                    if (this.id) {
                        data = await GspAPI.updateMedicineClearBucketList(this.id, postData);
                    } else {
                        data = await GspAPI.createMedicineClearBucketList(postData);
                    }
                    if (data?.status) {
                        console.log('data=', data);
                        this.$Toast({
                            type: 'success',
                            message: '操作成功',
                        });
                        this.$emit('confirm');
                        this.showDialog = false;
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    if (isDraft) {
                        this.draftBtnLoading = false;
                    } else {
                        this.loadingSubmit = false;
                    }
                }
            },
            /**
             * 过滤函数
             * @param dataList 原始数据列表
             * @returns {Array}
             */
            filterFn(dataList) {
                const filterData = dataList.filter((item) => ['中药','配方饮片','非配方饮片'].includes(item.label));
                if (!this.pharmacy?.pharmacyNo) {
                    return filterData;
                }
                return filterTreeByPharmacyNo(filterData, this.pharmacy?.pharmacyNo);
            },

        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .pharmacy__first-battalion__clear-funnel__dialog {
        &--not-allow {
            width: 100%;
            height: 44px;
            padding: 0 10px !important;
            line-height: 44px;
            cursor: not-allowed;
            background-color: #f9fafc;
        }

        .base-info {
            width: 1152px;
        }

        .abc-table-normal-wrapper {
            .abc-table-header {
                background: #f6f8fa;
            }

            .abc-table-body {
                .goods-info-wrapper {
                    box-sizing: border-box;
                    width: 100%;
                    padding: 4px 12px;

                    > .name {
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 20px;
                        color: $T1;

                        @include ellipsis;
                    }

                    > .spec {
                        font-size: 12px;
                        line-height: 16px;
                        color: $T2;

                        @include ellipsis;
                    }
                }

                .show-text-box {
                    display: flex;
                    align-items: center;
                    height: 44px;
                    padding: 0 10px;
                }
            }

            .abc-table-footer {
                .prepend-input {
                    left: 6px;
                }

                .abc-input__inner {
                    padding-left: 36px !important;
                }

                .append-input {
                    width: 88px;
                }
            }
        }

        .abc-table-normal-wrapper.abc-table--excel .abc-table-body .abc-table-td .abc-form-item input,
        .abc-table-normal-wrapper.abc-table--excel .abc-table-body .abc-table-td .abc-input__inner {
            height: 44px;
        }
    }
</style>
