<template>
    <abc-layout preset="page-table" class="abc-pharmacy-gsp-frames-storage-clear-funnel-container">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-date-picker
                        v-model="toolsParams.dateRange"
                        :picker-options="pickerOptions"
                        type="daterange"
                        clearable
                    ></abc-date-picker>
                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="toolsParams.clinicId"
                        :show-all-clinic="false"
                        placeholder="总部/门店"
                        :clearable="true"
                        :width="160"
                    ></clinic-select>
                    <abc-select
                        v-if="(isChainAdmin && isMultiPharmacy) ||
                            ((isChainSubStore || isSingleStore) && multiPharmacyCanUse)"
                        v-model="toolsParams.pharmacyNo"
                        :width="140"
                        :max-height="250"
                        placeholder="库房"
                        clearable
                    >
                        <abc-option
                            v-for="it in inPharmacyList"
                            :key="it.no"
                            :label="it.name"
                            :value="it.no"
                        ></abc-option>
                    </abc-select>
                    <goods-auto-complete
                        placeholder="商品名称"
                        :search.sync="fetchParams.keyword"
                        :focus-show="true"
                        :type-arr="fetchParams.type"
                        :sub-type-arr="fetchParams.subType"
                        :c-m-spec="fetchParams.cMSpec"
                        :width="260"
                        enable-local-search
                        is-type-arr
                        format-count-key="stock"
                        :inorder-config="0"
                        :clinic-id="clinicId"
                        @selectGoods="onSelectGoods"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                        <div slot="append" class="search-icon" @click="onClickClearKeyword">
                            <i v-if="!!fetchParams.keyword" class="iconfont cis-icon-cross_small"></i>
                        </div>
                    </goods-auto-complete>
                </abc-space>
                <abc-space>
                    <abc-button
                        v-if="!isChainAdmin"
                        theme="success"
                        icon="s-b-add-line-medium"
                        @click="onClickCreate"
                    >
                        新增记录
                    </abc-button>
                    <abc-button
                        icon="n-upload-line"
                        variant="ghost"
                        :disabled="dataList.length === 0"
                        @click="exportExcelCommon(GSPExportEnum.STORAGE.CLEAR_FUNNEL.LIST,
                                                  createParams(),
                                                  '清斗记录')"
                    >
                        导出
                    </abc-button>
                </abc-space>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                :render-config="renderConfig"
                :loading="loading"
                :data-list="dataList"
                tr-clickable
                @handleClickTr="onClickRow"
            >
                <template #orderNo="{ trData: row }">
                    <abc-table-cell class="ellipsis" :style="{ color: $store.state.theme.style.theme1 }">
                        <span
                            v-if="row.status === ClearFunnelStatus.DRAFT"
                            v-abc-title="`最后修改：${ row.lastModified ? formatCacheTime(row.lastModified) : '-'}`"
                        ></span>
                        <span
                            v-else
                            v-abc-title="row.orderNo || '-'"
                        ></span>
                    </abc-table-cell>
                </template>
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="count"
                @current-change="onChangePage"
            ></abc-pagination>
        </abc-layout-footer>
        <multi-pharmacy-select-dialog
            v-model="showMultiPharmacyDialog"
            :pharmacy-list="inPharmacyList"
            title="清斗"
            :last-assemble-bucket-pharmacy-no="lastAssembleBucketPharmacyNo"
            @confirm="handleMultiPharmacyConfirm"
        >
        </multi-pharmacy-select-dialog>
        <dialog-clear-funnel-detail
            v-if="showDialogClearFunnelDetail"
            :id="detailId"
            v-model="showDialogClearFunnelDetail"
            :pharmacy="pharmacy"
            @confirm="refreshData"
            @refresh="refreshData"
        ></dialog-clear-funnel-detail>
    </abc-layout>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';
    import pickerOptions from 'views/common/pickerOptions';
    import mixinTable from '@/views-pharmacy/common/mixin-table';

    const GoodsAutoComplete = () => import('src/views/inventory/common/goods-auto-complete');
    const DialogClearFunnelDetail = () => import('./dialog-clear-funnel-detail/index.vue');
    const DialogInstallFunnelDetail = () => import('../install-funnel/dialog-install-funnel-detail/index.vue');

    import * as tools from '@/views-pharmacy/common/tools';
    import { mapGetters } from 'vuex';
    import { isEqual } from '@abc/utils';
    import { debounce } from 'utils/lodash';
    import { PurchaseTableConfig } from './table-config';
    import {
        exportExcelCommon, GSPExportEnum,
    } from '@/views-pharmacy/gsp/utils/gspExportUtil';
    import MultiPharmacySelectDialog from '@/views-pharmacy/gsp/frames/storage/component/multi-pharmacy-select.vue';
    import {
        ClearFunnelStatus, ClearFunnelStatusName,
    } from '@/views-pharmacy/inventory/constant';
    import { formatCacheTime } from '@/utils';
    export default {
        components: {
            MultiPharmacySelectDialog,
            GoodsAutoComplete,
            ClinicSelect,
            DialogClearFunnelDetail,
        },
        mixins: [
            pickerOptions,
            mixinTable,
        ],
        data() {
            return {
                GSPExportEnum,
                tools,
                fetchParams: {
                    keyword: '',
                    typeId: '',
                    typeLabel: '全部类型',
                    type: [1],
                    subType: [2],
                    cMSpec: '',
                },
                toolsParams: {
                    dateRange: this.createDateRangeCurMonth(), // 日期范围
                    clinicId: '', // 门店ID
                    goodsId: '', // 商品ID
                    pharmacyNo: '',
                },
                ClearFunnelStatus,
                tableHeader: [],
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                loading: false,
                originData: null,
                showMultiPharmacyDialog: false,
                lastAssembleBucketPharmacyNo: 0,
                showDialogClearFunnelDetail: false,
                detailId: '',
                pharmacy: {},
                formatCacheTime,
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'isChainSubStore',
                'isSingleStore',
                'currentClinic',
                'multiPharmacyCanUse',
                'pharmacyList',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            inPharmacyList() {
                return this.pharmacyList;
            },
            isMultiPharmacy() {
                return this.viewDistributeConfig.Gsp.storage.clearFunnel.isMultiPharmacy;
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 表格数据
            dataList() {
                return this.originData?.rows || [];
            },
            // 总条数
            count() {
                return this.originData?.total || 0;
            },
            renderConfig() {
                const tableConfig = new PurchaseTableConfig(this.currentClinic);
                const renderConfig = tableConfig.extendConfig({
                    clinicName: {
                        dataFormatter: (_,row) => (row.clinicId === this.clinicId ? '总部' : row.clinicName),
                    },
                    pharmacyName: {
                        hidden: !this.multiPharmacyCanUse,
                    },
                    operationTime: {
                        dataFormatter: (operationTime) => {
                            if (!operationTime) {
                                return '';
                            }
                            return tools.getDatetimeFormat(operationTime);
                        },
                    },
                    operatorInfo: {
                        dataFormatter: (operatorInfo) => operatorInfo?.name,
                    },
                    medicineName: {
                        dataFormatter: (_, row) => row?.goodsName?.join('、'),
                    },
                    // 包含草稿和已完成两种状态
                    status: {
                        customRender: (h, item) => {
                            return <abc-table-cell>
                                <abc-tag-v2
                                    variant={this.statusConfig(item).variant}
                                    theme={this.statusConfig(item).theme}
                                    min-width="52"
                                >
                                    { ClearFunnelStatusName[item.status] || item.statusName || '已完成' }
                                </abc-tag-v2>
                            </abc-table-cell>;
                        },
                    },
                });
                renderConfig.list = renderConfig.list.filter((item) => item?.show || true);
                return renderConfig;
            },
        },
        watch: {
            toolsParams: {
                handler() {
                    this.refreshData();
                },
                deep: true,
            },
        },
        created() {
            this._fetchDataList = debounce(this.fetchDataList, 500, true);
        },
        mounted() {
            this.setPageSizeWithTableHeight();
        },
        methods: {
            exportExcelCommon,
            statusConfig(row) {
                const waitConfig = {
                    theme: 'warning',
                    variant: 'outline',
                };
                const successConfig = {
                    theme: 'success',
                    variant: 'outline',
                };
                const configMap = {
                    [ClearFunnelStatus.DRAFT]: waitConfig,
                    [ClearFunnelStatus.FINISHED]: successConfig,
                };
                return configMap[row.status] || successConfig;
            },
            async refreshData() {
                this.initPageIndex();
                await this.fetchDataList();
            },
            handleMounted(data) {
                this.pageParams.pageSize = data.paginationLimit;
                this.fetchDataList();
            },
            /**
             * 当选择一个商品时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} goods
             */
            async onSelectGoods(goods) {
                this.toolsParams.goodsId = goods.goodsId;
                this.fetchParams.keyword = goods.displayName;
            },
            /**
             * 当点击情况关键词
             * <AUTHOR>
             * @date 2024-01-08
             */
            onClickClearKeyword() {
                this.toolsParams.goodsId = '';
                this.fetchParams.keyword = '';
            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2023-12-28
             * @returns {Object}
             */
            createParams() {
                const {
                    dateRange, // 日期范围
                    clinicId, // 门店ID
                    goodsId, // 商品ID
                    pharmacyNo, // 库房
                } = this.toolsParams;
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;
                const params = {
                    clinicId, // 门店ID
                    goodsId, // 商品ID
                    pharmacyNo, // 库房
                    dateStart: dateRange[0], // 开始日期
                    dateEnd: dateRange[1], // 截止日期
                    offset: pageIndex * pageSize, // 分页
                    limit: pageSize, // 每页条数
                };
                return params;
            },
            /**
             * 查询记录数据
             * <AUTHOR>
             * @date 2023-12-28
             */
            async fetchDataList() {
                this.loading = true;
                const params = this.createParams();
                const fetchResponse = await GspAPI.fetchMedicineClearBucketList(params);
                if (!isEqual(params, this.createParams())) {
                    return fetchResponse;
                }
                this.loading = false;
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                this.originData = fetchResponse.data;
                this.lastAssembleBucketPharmacyNo = this.originData?.lastAssembleBucketPharmacyNo || 0;
            },
            async createOrder(res) {
                this.detailId = '';
                this.pharmacy = res;
                this.showDialogClearFunnelDetail = true;
            },
            handleMultiPharmacyConfirm(res) {
                this.showMultiPharmacyDialog = false;
                this.createOrder(res);
            },
            /**
             * 当点击新增记录
             * <AUTHOR>
             * @date 2024-01-03
             */
            async onClickCreate() {
                if (this.multiPharmacyCanUse) {
                    this.showMultiPharmacyDialog = true;
                } else {
                    this.createOrder();
                }
            },
            /**
             * 当点击导出时
             * <AUTHOR>
             * @date 2024-01-03
             */
            onClickExport() {
                console.log('点击导出，todo');
            },
            /**
             * 当点击一行触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Object} row
             */
            onClickRow(row) {
                this.detailId = row.id;
                this.showDialogClearFunnelDetail = true;
            },
            /**
             * 当点击装斗时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} row
             */
            async onClickInstallFunnel(row) {
                const openResponse = await tools.openDialog({
                    propsData: {
                        $abcPage: this.$abcPage,
                        clearFunnelId: row.id, // 清斗ID
                    },
                    component: DialogInstallFunnelDetail,
                    extendProps: {
                        parent: this,
                    },
                });
                if (openResponse.status === false) {
                    return openResponse;
                }
                this.$router.push({
                    name: '@PharmacyGspStorageInstallFunnel', // 前往装斗列表
                });
            },
            /**
             * 当点击删除时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} row
             */
            onClickDelete(row) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不可恢复，是否确认删除?',
                    onConfirm: async () => {
                        const deleteResponse = await GspAPI.deleteMedicineClearBucket(row.id);
                        if (deleteResponse.status === false) {
                            return deleteResponse;
                        }
                        this.$Toast({
                            type: 'success',
                            message: '删除成功',
                        });
                        if (this.dataList.length === 1) {
                            // 如果当前页只有一条数据，删除成功后就没有数据了，所以页数减1
                            this.pageParams.pageIndex = Math.max(0, this.pageParams.pageIndex - 1);
                        }
                        this.fetchDataList();
                    },
                });
            },
            /**
             * 当切换页码时触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Number} pageIndex
             */
            onChangePage(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchDataList();
            },
            /**
             * 初始化页码
             * <AUTHOR>
             * @date 2023-12-29
             */
            initPageIndex() {
                this.pageParams.pageIndex = 0;
            },
        },
    };
</script>

