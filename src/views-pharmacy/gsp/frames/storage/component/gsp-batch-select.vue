<template>
    <abc-popover
        ref="batchSelect"
        trigger="click"
        placement="bottom-start"
        theme="custom"
        :close-delay="0"
        append-to-body
        class="mul-batch-select-wrapper"
        @show="fetchData"
    >
        <div slot="reference" class="batch-select">
            <abc-input
                v-model="batchsStr"
                placeholder="不指定批次"
                readonly
                :size="size"
                icon="cis-icon-dropdown_triangle"
                @enter="enterEvent"
            ></abc-input>
        </div>
        <div v-if="options && options.length > 0" v-abc-loading="loading" class="mul-batch-select-option ellipsis">
            <div class="option-title">
                <div class="batch">
                    批次
                </div>
                <div class="batch-no">
                    生产批号
                </div>
                <div class="in-date">
                    入库时间
                </div>
                <div class="expiry-date">
                    效期
                </div>
            </div>
            <ul class="option-content">
                <label v-for="option in options" :key="option.batchId">
                    <li class="option-tr" :style="options.length > 10 ? { 'padding': '0 2px 0 12px' } : {}">
                        <div class="batch ellipsis">
                            <abc-checkbox v-model="option.checked">{{ option.batchId }}</abc-checkbox>
                        </div>
                        <div class="batch-no ellipsis" :title="option.batchNo">{{ option.batchNo }}</div>
                        <div class="in-date ellipsis" :title="option.inDate | parseTime('y-m-d')">
                            {{ option.inDate | parseTime('y-m-d') }}
                        </div>
                        <div class="expiry-date ellipsis" :title="option.expiryDate">
                            {{ option.expiryDate }}
                        </div>
                    </li>
                </label>
            </ul>
            <div class="batch-select-footer">
                <abc-space>
                    <abc-button @click="confirmOption">
                        确定
                    </abc-button>
                    <abc-button variant="ghost" @click="closePopover">
                        取消
                    </abc-button>
                </abc-space>
            </div>
        </div>
    </abc-popover>
</template>

<script>
    import EnterEvent from 'views/common/enter-event';

    import { formatStock } from 'views/inventory/goods-check/common';
    import {
        createGUID, moneyDigit,
    } from '@/utils';

    import { mapGetters } from 'vuex';
    import Logger from 'utils/logger';
    import GspAPI from 'api/pharmacy/gsp';

    export default {
        name: 'GspBatchSelect',
        mixins: [EnterEvent],

        props: {
            goodsId: {
                type: String,
                required: true,
            },
            goods: {
                type: Object,
            },
            size: String,
            value: {
                type: Array,
                default() {
                    return [];
                },
            },
            batchs: {
                type: Array,
                default() {
                    return [];
                },
            },
            pharmacyNo: {
                type: [Number, String],
                required: true,
            },
            multiPharmacyCanUse: Boolean,

            dataPermissionScene: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                loading: false,
                options: [],

                costPricePlaceholder: () => (
                    <div class="cost-price ellipsis">
                    </div>
                ),
            };
        },
        computed: {
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            batchsStr() {
                const batchs = this.batchs?.filter((item) => item.batchId);
                return batchs?.length ? `已选批次(${batchs.length})` : '';
            },
        },
        methods: {
            moneyDigit,
            formatStock,
            // 展开选项，同步选中状态
            initOption() {
                const selectedMap = new Map();
                this.batchs.forEach((item) => {
                    selectedMap.set(`${item.batchId}`, item);
                });
                this.options.forEach((item) => {
                    const selectedBatch = selectedMap.get(item.batchId);
                    if (selectedBatch) {
                        item.checked = true;
                        item.packageCount = selectedBatch.packageCount;
                        item.pieceCount = selectedBatch.pieceCount;
                    } else {
                        item.checked = false;
                        item.packageCount = '';
                        item.pieceCount = '';
                    }
                });
            },
            confirmOption() {
                const selectedData = this.options.filter((item) => {
                    return item.checked;
                });
                this.$emit('input', selectedData);
                this.$emit('change', selectedData);
                this.$refs.batchSelect.doClose();
            },
            async fetchData() {
                try {
                    this.loading = true;
                    const params = {
                        clinicId: this.clinicId,
                        batchNo: '',
                        goodsId: this.goodsId,
                        supplierId: '',
                        offset: 0,
                        limit: 299,
                        pharmacyNo: this.pharmacyNo,
                        batchViewMode: '',
                        expiredWarn: '',
                        costPriceWarn: '',
                        scene: 1,
                    };
                    const { data } = await GspAPI.fetchBatchList(params);
                    if (data?.rows?.length) {
                        this.options = data.rows.map((item) => {
                            return {
                                ...item,
                                keyId: createGUID(),
                                batchId: `${item.batchId}`,
                                beforePieceCount: item.pieceCount,
                                beforePackageCount: item.packageCount,
                            };
                        });
                    } else {
                        Logger.reportAnalytics('goods-business', {
                            key: 'noBatchData',
                        });
                    }

                    this.initOption();
                    this.loading = false;
                    this.$nextTick(() => {
                        this.$refs.batchSelect.updatePopper();
                    });
                } catch (e) {
                    console.warn(e);
                    this.loading = false;
                }
            },
            closePopover() {
                this.$refs.batchSelect.doClose();
            },
        },
    };
</script>

<style lang="scss" scoped>
    .mul-batch-select-wrapper {
        position: relative;

        .batch-select {
            position: relative;
            display: flex;
            align-items: center;
            height: 100%;
            font-size: 14px;

            ::v-deep .iconfont {
                pointer-events: none;
            }

            input.abc-input__inner {
                width: 100%;
                height: 32px;
                line-height: 32px;
                cursor: pointer;
                border-radius: 0;
            }
        }
    }

    .mul-batch-select-option {
        width: 400px;
        background-color: #ffffff;
        border: 1px solid $P1;
        border-radius: var(--abc-border-radius-small);
        box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.24);

        label {
            cursor: pointer;
        }

        .option-title {
            display: flex;
            align-items: center;
            height: 28px;
            padding: 0 12px;
            font-size: 12px;
            color: $T2;
            background-color: $P5;
            border-bottom: 1px solid $P1;
        }

        .option-content {
            max-height: 321px;
            overflow-y: auto;
            overflow-y: overlay;
            border-bottom: 1px solid $P6;
        }

        .option-tr {
            display: flex;
            align-items: center;
            height: 32px;
            padding: 0 12px;
        }

        .option-title,
        .option-tr {
            .batch {
                flex: 1;
                max-width: 110px;
            }

            .cost-price,
            .current-count {
                width: 76px;
                min-width: 76px;
                max-width: 76px;
                padding-left: 8px;
                text-align: right;
            }

            .batch-no,
            .in-date,
            .expiry-date {
                width: 90px;
                min-width: 90px;
                max-width: 90px;
                padding-left: 8px;
            }
        }

        .batch-select-footer {
            padding: 12px;
        }
    }
</style>
