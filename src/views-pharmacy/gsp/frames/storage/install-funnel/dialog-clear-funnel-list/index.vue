<template>
    <abc-dialog
        class="pharmacy__first-battalion__dialog-clear-funnel-list"
        title="提取清斗"
        :value="true"
        :auto-focus="false"
        content-styles="width: 1200px; height: 490px;padding: 0 0;"
        @input="(val) => $emit('input', val)"
    >
        <abc-layout preset="dialog-table" style="padding: 24px 24px;">
            <abc-layout-header>
                <abc-flex justify="space-between">
                    <abc-space>
                        <clinic-select
                            v-if="isChainAdmin"
                            v-model="toolsParams.clinicId"
                            :width="160"
                        >
                        </clinic-select>
                        <abc-date-picker
                            v-model="toolsParams.dateRange"
                            :picker-options="pickerOptions"
                            type="daterange"
                            clearable
                        ></abc-date-picker>
                        <goods-auto-complete
                            placeholder="商品名称"
                            :search.sync="fetchParams.keyword"
                            :focus-show="true"
                            :type-arr="fetchParams.type"
                            :sub-type-arr="fetchParams.subType"
                            :c-m-spec="fetchParams.cMSpec"
                            :width="260"
                            need-filter-disable
                            is-type-arr
                            format-count-key="stock"
                            :inorder-config="0"
                            :clinic-id="clinicId"
                            @selectGoods="onSelectGoods"
                        >
                            <abc-search-icon slot="prepend"></abc-search-icon>
                            <div slot="append" class="search-icon" @click="onClickClearKeyword">
                                <i v-if="!!fetchParams.keyword" class="iconfont cis-icon-cross_small"></i>
                            </div>
                        </goods-auto-complete>
                    </abc-space>
                </abc-flex>
            </abc-layout-header>
            <abc-layout-content>
                <abc-section ref="abc-section-table">
                    <abc-table
                        :render-config="renderConfig"
                        :loading="loading"
                        :data-list="dataList"
                        :pagination="tablePagination"
                        tr-clickable
                        @pageChange="onChangePage"
                        @handleClickTr="onClickRow"
                    >
                    </abc-table>
                </abc-section>
            </abc-layout-content>
        </abc-layout>
        <div slot="footer" class="dialog-footer">
            <abc-button
                type="blank"
                @click="onClickCancel"
            >
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import pickerOptions from 'views/common/pickerOptions';

    import { mapGetters } from 'vuex';
    import { isEqual } from '@abc/utils';
    import { debounce } from 'utils/lodash';
    import * as tools from '@/views-pharmacy/common/tools';
    import { ClearFunnelStatus } from '@/views-pharmacy/inventory/constant';

    const GoodsAutoComplete = () => import('src/views/inventory/common/goods-auto-complete');

    export default {
        components: {
            GoodsAutoComplete,
        },
        mixins: [
            pickerOptions,
        ],
        data() {
            return {
                tools,
                fetchParams: {
                    keyword: '',
                    typeId: '',
                    typeLabel: '全部类型',
                    type: [1],
                    subType: [2],
                    cMSpec: '',
                },
                tablePagination: {
                    showTotalPage: true,
                    pageIndex: 0,
                    pageSize: 7,
                    count: 0,
                },
                toolsParams: {
                    clinicId: '', // 门店ID
                    dateRange: this.createDateRangeCurMonth(), // 日期范围
                    goodsId: '', // 商品ID
                },
                tableHeader: [],
                pageParams: {
                    pageIndex: 0,
                    pageSize: 8,
                },
                loading: false,
                originData: null,

                tableMinHeight: 360,
                tableMaxHeight: 360,
            };
        },
        computed: {
            ...mapGetters([
                'isChain',
                'isChainAdmin',
                'currentClinic',
            ]),
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 表格数据
            dataList() {
                return this.originData?.rows || [];
            },
            // 总条数
            count() {
                return this.originData?.total || 0;
            },
            renderConfig() {
                return {
                    list: [
                        {
                            label: '单号',
                            key: 'orderNo',
                            style: {
                                width: '140px',
                                minWidth: '140px',
                                flex: 1,
                            },
                        },
                        {
                            label: '门店',
                            key: 'clinicName',
                            style: {
                                width: '160px',
                                minWidth: '160px',
                                flex: 1,
                            },
                        },
                        {
                            label: '品种数',
                            key: 'kindCount',
                            style: {
                                width: '80px',
                                maxWidth: '80px',
                            },
                        },
                        {
                            label: '操作时间',
                            key: 'operationTime',
                            style: {
                                width: '160px',
                                maxWidth: '160px',
                            },
                            dataFormatter: (_,row) => {
                                if (!row.operationTime) {
                                    return '';
                                }
                                return tools.getDatetimeFormat(row.operationTime);
                            },
                        },
                        {
                            label: '操作人',
                            key: 'operatorInfo',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                            dataFormatter: (_,row) => row.operatorInfo?.name,
                        },
                        {
                            label: '备注',
                            key: 'remark',
                            style: {
                                width: '120px',
                                maxWidth: '120px',
                            },
                        },
                        {
                            label: '操作',
                            key: 'handlers',
                            style: {
                                width: '80px',
                                maxWidth: '80px',
                                textAlign: 'center',
                            },
                            customRender: (h, row) => {
                                return (<abc-table-cell class="handles-wrapper">
                                  <abc-button
                                      type="text"
                                      onClick={(event) => {
                                          event.stopPropagation();
                                          this.$emit('confirm', {
                                              clearFunnelId: row.id,
                                              pharmacyNo: row.pharmacyNo,
                                              pharmacyName: row.pharmacyName,
                                          });
                                      }}>
                                      提取
                                  </abc-button>
                              </abc-table-cell>);
                            },
                        },
                    ],
                };
            },
        },
        watch: {
            toolsParams: {
                handler() {
                    this.initPageIndex();
                    this.fetchDataList();
                },
                deep: true,
            },
        },
        created() {
            this.tableHeader = this.createTableHeader();
            this._fetchDataList = debounce(this.fetchDataList, 500, true);
        },
        mounted() {
            this.fetchDataList();
        },
        methods: {
            /**
             * 创建表头配置
             * <AUTHOR>
             * @date 2023-12-25
             * @returns {Array}
             */
            createTableHeader() {
                const tableHeader = [
                    {
                        label: '',
                        prop: 'zw-1',
                        width: 8,
                    },
                    {
                        label: '单号',
                        prop: 'orderNo',
                        width: 140,
                    },
                    {
                        label: '门店',
                        prop: 'clinicName',
                        width: 160,
                    },
                    {
                        label: '品种数',
                        prop: 'kindCount',
                        width: 80,
                    },
                    {
                        label: '操作时间',
                        prop: 'operationTime',
                        width: 160,
                        formatter: (row) => {
                            if (!row.operationTime) {
                                return '';
                            }
                            return tools.getDatetimeFormat(row.operationTime);
                        },
                    },
                    {
                        label: '操作人',
                        prop: 'operatorInfo',
                        width: 120,
                        formatter: (row) => row.operatorInfo?.name,
                    },
                    {
                        label: '备注',
                        prop: 'remark',
                        width: 120,
                    },
                    {
                        label: '操作',
                        prop: 'handlers',
                        width: 80,
                        render: (h, row) => {
                            return <div class="handles-wrapper">
                                <abc-button
                                    type="text"
                                    onClick={(event) => {
                                        event.stopPropagation();
                                        this.$emit('confirm', {
                                            clearFunnelId: row.id,
                                            pharmacyNo: row.pharmacyNo,
                                            pharmacyName: row.pharmacyName,
                                        });
                                    }}>
                                    提取
                                </abc-button>
                            </div>;
                        },
                    },
                ];
                return tools.handleTableHeaderBorder(tableHeader);
            },
            /**
             * 当选择一个商品时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} goods
             */
            async onSelectGoods(goods) {
                this.toolsParams.goodsId = goods.goodsId;
                this.fetchParams.keyword = goods.displayName;
            },
            /**
             * 当点击情况关键词
             * <AUTHOR>
             * @date 2024-01-08
             */
            onClickClearKeyword() {
                this.toolsParams.goodsId = '';
                this.fetchParams.keyword = '';
            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2023-12-28
             * @returns {Object}
             */
            createParams() {
                const {
                    clinicId, // 门店id
                    dateRange, // 日期范围
                    goodsId, // 商品ID
                } = this.toolsParams;
                const {
                    pageIndex,
                    pageSize,
                } = this.tablePagination;
                const params = {
                    clinicId, // 门店搜索
                    goodsId, // 商品ID
                    dateStart: dateRange[0], // 开始日期
                    dateEnd: dateRange[1], // 截止日期
                    offset: pageIndex * pageSize, // 分页
                    limit: pageSize, // 每页条数
                    status: ClearFunnelStatus.FINISHED,
                };
                return params;
            },
            /**
             * 查询记录数据
             * <AUTHOR>
             * @date 2023-12-28
             */
            async fetchDataList() {
                this.loading = true;
                const params = this.createParams();
                const fetchResponse = await GspAPI.fetchMedicineClearBucketList(params);
                if (!isEqual(params, this.createParams())) {
                    return fetchResponse;
                }
                this.loading = false;
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                this.originData = fetchResponse.data;
                this.tablePagination.count = this.originData?.total;
            },

            /**
             * 当点击一行触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Object} row
             */
            onClickRow(row) {
                this.$emit('confirm', {
                    clearFunnelId: row.id,
                    pharmacyNo: row.pharmacyNo,
                    pharmacyName: row.pharmacyName,
                });
            },
            /**
             * 当点击取消时
             * <AUTHOR>
             * @date 2024-01-22
             */
            onClickCancel() {
                this.$emit('input', false);
            },
            /**
             * 当切换页码时触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Number} pageIndex
             */
            onChangePage(pageIndex) {
                this.tablePagination.pageIndex = pageIndex - 1;
                this.fetchDataList();
            },
            /**
             * 初始化页码
             * <AUTHOR>
             * @date 2023-12-29
             */
            initPageIndex() {
                this.tablePagination.pageIndex = 0;
            },
        },
    };
</script>
