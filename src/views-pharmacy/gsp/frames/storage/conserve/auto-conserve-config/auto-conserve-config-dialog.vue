<template>
    <abc-dialog
        v-model="showDialog"
        class="auto-conserve-config_dialog"
        title="养护计划"
        content-styles="width: 720px; padding: 24px;"
    >
        <abc-layout v-abc-loading="loading" style="width: 672px;">
            <abc-section>
                <abc-space style="margin-bottom: 0;">
                    <abc-p style="line-height: 32px;" gray>
                        自动生成养护计划
                    </abc-p>
                    <abc-switch v-model="autoConserve" type="number"></abc-switch>
                    <abc-p v-if="!autoConserve" style="line-height: 32px;" gray>
                        开启后，将在每月指定日期按养护规则自动生成待养护单
                    </abc-p>
                    <template v-else>
                        <abc-p style="line-height: 32px;">
                            每月
                        </abc-p>
                        <abc-input
                            v-model="autoConserveDay"
                            :width="56"
                            type="number"
                            :config="{
                                max: 28,
                                supportZero: false,
                            }"
                        >
                            <span slot="appendInner">号</span>
                        </abc-input>
                        <abc-p style="line-height: 32px;">
                            根据以下养护规则自动生成待养护单
                        </abc-p>
                    </template>
                </abc-space>
            </abc-section>
            <abc-section>
                <div class="auto-conserve-config_dialog--desc">
                    <div class="auto-conserve-config_dialog--desc-title">
                        重点养护规则
                    </div>
                    <div class="auto-conserve-config_dialog--desc-content">
                        <abc-space
                            align="start"
                            direction="vertical"
                            style="margin-bottom: 0;"
                            :size="10"
                        >
                            <abc-flex>
                                <div class="auto-conserve-config_dialog--desc-content-label">
                                    品种范围
                                </div>
                                <div class="auto-conserve-config_dialog--desc-content-value">
                                    <abc-space style="margin-bottom: 4px;">
                                        <abc-title style="line-height: 22px;" :bold="false">
                                            重点养护类品类
                                        </abc-title>
                                        <abc-p style="line-height: 22px;" small gray>
                                            档案设置为重点养护的品种，如质量不稳定、储存要求特殊、药监重点监控品种等
                                        </abc-p>
                                    </abc-space>
                                    <abc-space style="margin-bottom: 0;">
                                        <abc-title style="line-height: 22px;" :bold="false">
                                            含近效批次品种
                                        </abc-title>
                                        <abc-p style="line-height: 22px;" small gray>
                                            库存批次中存在近效期批次的品种
                                        </abc-p>
                                    </abc-space>
                                </div>
                            </abc-flex>
                            <abc-flex>
                                <div class="auto-conserve-config_dialog--desc-content-label">
                                    养护周期
                                </div>
                                <div class="auto-conserve-config_dialog--desc-content-value">
                                    <abc-title style="line-height: 22px;" :bold="false">
                                        每1个月养护一次
                                    </abc-title>
                                </div>
                            </abc-flex>
                        </abc-space>
                    </div>
                </div>
            </abc-section>
            <abc-section>
                <div class="auto-conserve-config_dialog--desc">
                    <div class="auto-conserve-config_dialog--desc-title">
                        一般养护规则
                    </div>
                    <div class="auto-conserve-config_dialog--desc-content">
                        <abc-space
                            style="margin-bottom: 0;"
                            align="start"
                            direction="vertical"
                            :size="10"
                        >
                            <abc-flex>
                                <div class="auto-conserve-config_dialog--desc-content-label">
                                    品种范围
                                </div>
                                <div class="auto-conserve-config_dialog--desc-content-value">
                                    <abc-space style="margin-bottom: 0;">
                                        <abc-title style="line-height: 22px;" :bold="false">
                                            非重点养护类品种
                                        </abc-title>
                                        <abc-p style="line-height: 22px;" small gray>
                                            除重点养护品种范围外的剩余品种
                                        </abc-p>
                                    </abc-space>
                                </div>
                            </abc-flex>
                            <abc-flex>
                                <div class="auto-conserve-config_dialog--desc-content-label">
                                    养护周期
                                </div>
                                <div class="auto-conserve-config_dialog--desc-content-value">
                                    <abc-title style="line-height: 22px;" :bold="false">
                                        每3个月养护一次
                                    </abc-title>
                                </div>
                            </abc-flex>
                        </abc-space>
                    </div>
                </div>
            </abc-section>
        </abc-layout>
        <div slot="footer" class="dialog-footer">
            <abc-flex>
                <abc-space style="margin-bottom: 0;">
                    <abc-button :disabled="!isModify" @click="updatedMedicineMaintenancePlan">
                        保存
                    </abc-button>
                    <abc-button variant="ghost" @click="showDialog = false">
                        取消
                    </abc-button>
                </abc-space>
            </abc-flex>
        </div>
    </abc-dialog>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    export default {
        name: 'AutoConserveConfigDialog',
        props: {
            value: Boolean,
            isMedicalDeviceConserve: Boolean,
            category: {
                type: Number,
                default: 0,
            },
        },
        data() {
            return {
                autoConserve: 0,
                autoConserveCache: 0,
                autoConserveDay: 1,
                autoConserveDayCache: 1,
                loading: false,
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            isModify() {
                return this.autoConserve !== this.autoConserveCache || this.autoConserveDay !== this.autoConserveDayCache;
            },
        },
        created() {
            this.fetchMedicineMaintenancePlan();
        },
        methods: {
            async fetchMedicineMaintenancePlan() {
                this.loading = true;
                try {
                    const { data } = await GspAPI.fetchMedicineMaintenancePlan({
                        category: this.category,
                    });
                    this.autoConserve = data?.autoPlanSwitch || 0;
                    this.autoConserveDay = data?.startDays || 1;
                    this.autoConserveCache = data?.autoPlanSwitch || 0;
                    this.autoConserveDayCache = data?.startDays || 1;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            async updatedMedicineMaintenancePlan() {
                try {
                    const { data } = await GspAPI.updatedMedicineMaintenancePlan({
                        autoPlanSwitch: this.autoConserve,
                        startDays: this.autoConserveDay,
                        category: this.category,
                    });
                    if (data) {
                        this.$Toast({
                            type: 'success',
                            message: '修改养护计划成功',
                        });
                        this.showDialog = false;
                    }
                } catch (e) {
                    console.log(e);
                    this.$Toast({
                        type: 'error',
                        message: '修改养护计划失败',
                    });
                }
            },

        },
    };
</script>

<style lang="scss">
@import 'styles/abc-common.scss';

.auto-conserve-config_dialog {
    &--desc {
        border: 1px solid $P6;
        border-radius: var(--abc-border-radius-small);

        &-title {
            display: flex;
            height: var(--abc-size-l);
            padding: 0 12px;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: var(--abc-size-l);
            color: var(--abc-color-T1);
            text-align: center;
            border-bottom: 1px solid $P6;
        }

        &-content {
            padding: 12px 12px;
            line-height: 22px;

            &-label {
                width: 80px;
                font-size: 14px;
                color: var(--abc-color-T2);
            }

            &-value {
                flex: 1;
            }
        }
    }
}
</style>
