<template>
    <abc-layout preset="page-table" class="abc-pharmacy-gsp-frames-storage-conserve-container">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-date-picker
                        v-model="toolsParams.dateRange"
                        :picker-options="pickerOptions"
                        type="daterange"
                        clearable
                    ></abc-date-picker>
                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="toolsParams.clinicId"
                        :show-all-clinic="false"
                        placeholder="总部/门店"
                        :clearable="true"
                        :width="160"
                    ></clinic-select>
                    <abc-select
                        v-model="toolsParams.maintenanceType"
                        :width="160"
                        placeholder="养护类型"
                        clearable
                    >
                        <abc-option
                            v-for="item in options.maintenanceTypeOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                    <!--商品搜索-->
                    <goods-auto-complete
                        ref="autoComplete"
                        class="abc-autocomplete-search"
                        placeholder="商品名称"
                        :with-stock="false"
                        :width="180"
                        :auto-focus-first="false"
                        :clear-search-key="false"
                        :only-stock="false"
                        :search.sync="searchKey"
                        :pharmacy-no="pharmacyNo"
                        focus-show
                        enable-local-search
                        clearable
                        @selectGoods="selectGoods"
                        @clear="clearSearch"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                    </goods-auto-complete>
                </abc-space>
                <abc-space>
                    <abc-button
                        v-if="!(gspConservationAddButton && isChainAdmin)"
                        theme="success"
                        icon="s-b-add-line-medium"
                        @click="onClickCreate"
                    >
                        新增记录
                    </abc-button>
                    <abc-button
                        v-if="!(gspConservationPlanButton && isChainAdmin)"
                        variant="ghost"
                        @click="showAutoConserveConfigDialog = true"
                    >
                        养护计划
                    </abc-button>
                    <abc-button
                        icon="n-upload-line"
                        variant="ghost"
                        :loading="exportLoading"
                        :disabled="!(dataGoodsList.length !== 0 || dataList.length !== 0)"
                        @click="exportMedicineMaintenanceOrderList"
                    >
                        导出
                    </abc-button>
                </abc-space>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content>
            <abc-table
                v-if="toolsParams.goodsId"
                ref="abcTable"
                type="pro"
                :custom-tr-key="customGoodsTrKey"
                :render-config="renderConfigByGoodsId"
                :loading="loading"
                :data-list="dataGoodsList"
                @handleClickTr="onClickRow"
            >
            </abc-table>
            <abc-table
                v-else
                ref="abcTable"
                type="pro"
                :custom-tr-key="customTrKey"
                :render-config="renderConfig"
                :loading="loading"
                :data-list="dataList"
                @handleClickTr="onClickRow"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="tablePagination.count"
                @current-change="pageChange"
            ></abc-pagination>
        </abc-layout-footer>
        <auto-conserve-config-dialog
            v-if="showAutoConserveConfigDialog"
            v-model="showAutoConserveConfigDialog"
            :category="category"
            :is-medical-device-conserve="isMedicalDeviceConserve"
        ></auto-conserve-config-dialog>
        <dialog-conserve-detail
            v-if="conserveDetailVisible"
            v-bind="propsData"
            v-model="conserveDetailVisible"
            :category="category"
            :is-medical-device-conserve="isMedicalDeviceConserve"
            @confirm="confirm"
        ></dialog-conserve-detail>
    </abc-layout>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import pickerOptions from 'views/common/pickerOptions';
    import mixinTable from '@/views-pharmacy/common/mixin-table';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';
    const DialogConserveDetail = () => import('./dialog-conserve-detail/index.vue');
    const AutoConserveConfigDialog = () => import('./auto-conserve-config/auto-conserve-config-dialog.vue');
    import { goodsFullName } from '@/filters';
    import { mapGetters } from 'vuex';
    import { isEqual } from '@abc/utils';
    import * as constants from '@/views-pharmacy/common/constants';
    import * as options from '@/views-pharmacy/common/options';
    import * as tools from '@/views-pharmacy/common/tools';
    import GoodsAutoComplete from 'views/inventory/common/goods-auto-complete.vue';
    import {
        ConserveStatus, ConserveStatusName,
    } from '@/views-pharmacy/inventory/constant';
    import { PurchaseTableConfig } from './table-config';
    import { PurchaseGoodsTableConfig } from './table-goods-config';
    export default {
        components: {
            ClinicSelect,
            GoodsAutoComplete,
            AutoConserveConfigDialog,
            DialogConserveDetail,
        },
        mixins: [
            pickerOptions,
            mixinTable,
        ],
        data() {
            return {
                constants,
                options,
                tools,
                conserveDetailVisible: false,
                toolsParams: {
                    dateRange: this.createDateRangeCurMonth(), // 日期范围
                    clinicId: '', // 门店ID
                    maintenanceType: '', // 养护类型
                    goodsId: '',
                },
                tableHeader: [],
                pageParams: {
                    offset: 0,
                    limit: 10,
                    count: 0,
                },
                propsData: {},
                exportLoading: false,
                loading: false,
                originData: null,
                originGoodsData: null,
                searchKey: '',
                showAutoConserveConfigDialog: false,
            };
        },
        computed: {
            category() {
                return this.isMedicalDeviceConserve ? 10 : 0;
            },
            name() {
                return this.$route?.name || '';
            },
            isMedicalDeviceConserve() {
                return this.name === '@PharmacyGspStorageMedicalDeviceConserve';
            },
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
                'currentPharmacy',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            gspConservationPlanButton() {
                return this.viewDistributeConfig.Gsp.storage.conserve.gspConservationPlanButton;
            },
            gspConservationAddButton() {
                return this.viewDistributeConfig.Gsp.storage.conserve.gspConservationAddButton;
            },
            pharmacyNo() {
                return this.currentPharmacy?.no;
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 表格数据
            dataList() {
                return this.originData?.rows || [];
            },
            dataGoodsList() {
                return this.originGoodsData?.rows || [];
            },
            // 总条数
            count() {
                if (this.toolsParams.goodsId) {
                    return this.originGoodsData?.total || 0;
                }
                return this.originData?.total || 0;
            },
            tablePagination() {
                const {
                    limit, offset,
                } = this.pageParams;
                return {
                    showTotalPage: false,
                    pageIndex: (offset / limit),
                    pageSize: limit,
                    count: this.count || 0,
                };
            },
            /**
             * 创建表头配置
             * <AUTHOR>
             * @date 2023-12-25
             * @returns {Array}
             */
            renderConfig() {
                const tableConfig = new PurchaseTableConfig(this.currentClinic);
                return tableConfig.extendConfig({
                    status: {
                        customRender: (h, item) => {
                            return <abc-table-cell>
                                <abc-tag-v2
                                    variant={this.statusConfig(item).variant}
                                    theme={this.statusConfig(item).theme}
                                    min-width="52"
                                >
                                    { ConserveStatusName[item.status] || item.statusName || '已完成' }
                                </abc-tag-v2>
                            </abc-table-cell>;
                        },
                    },
                    clinicName: {
                        dataFormatter: (clinicName, row) => (row.clinicId === this.clinicId ? '总部' : clinicName),
                    },
                    maintenanceTime: {
                        dataFormatter: (maintenanceTime) => {
                            if (!maintenanceTime) return '';
                            return tools.getDatetimeFormat(maintenanceTime);
                        },
                    },
                    maintenanceType: {
                        dataFormatter: (maintenanceType) => tools.getMaintenanceTypeWording(maintenanceType),
                    },
                    maintainer: {
                        dataFormatter: (maintainer) => maintainer?.name,
                    },
                    handlers: {
                        customRender: (h, item) => {
                            return <abc-table-cell>
                                <abc-button
                                    type="text"
                                    disabled={this.isChainAdmin && item.clinicId !== item.chainId}
                                    onClick={(event) => {
                                        event.stopPropagation();
                                        this.onClickMultiplex(item);
                                    }}>
                                    复用
                                </abc-button>
                            </abc-table-cell>;
                        },
                    },
                });
            },
            renderConfigByGoodsId() {
                const tableConfig = new PurchaseGoodsTableConfig(this.currentClinic);
                return tableConfig.extendConfig({
                    status: {
                        pinned: true,
                        customRender: (h, item) => {
                            return <abc-table-cell>
                        <abc-tag-v2
                            variant={this.statusConfig(item).variant}
                            theme={this.statusConfig(item).theme}
                            min-width="52"
                        >
                            { ConserveStatusName[item.status] || item.statusName || '已完成' }
                        </abc-tag-v2>
                    </abc-table-cell>;
                        },
                    },
                    clinicName: {
                        pinned: true,
                        dataFormatter: (clinicName, row) => (row.clinicId === this.clinicId ? '总部' : clinicName),
                    },
                    maintenanceTime: {
                        pinned: true,
                        dataFormatter: (_,row) => {
                            if (!row.maintenanceTime) {
                                return '';
                            }
                            return tools.getDatetimeFormat(row.maintenanceTime);
                        },
                    },
                    maintenanceType: {
                        pinned: true,
                        dataFormatter: (maintenanceType) => tools.getMaintenanceTypeWording(maintenanceType),
                    },
                    maintainer: {
                        pinned: true,
                        dataFormatter: (maintainer) => maintainer?.name,
                    },
                    goods: {
                        pinned: true,
                        customRender: (h, row) => {
                            return (<abc-table-cell>
                        <abc-flex vertical align="flex-start" style="width:100%;">
                            <div className="ellipsis" style="width:100%;line-height: 22px;color: #000000;font-size:14px;font-weight: 500;overflow: hidden; text-overflow: ellipsis;word-break: keep-all;white-space: nowrap;" title={row.goods?.displayName || ''}>{row.goods?.displayName || ''}</div>
                            <div className="ellipsis" style="width:100%;font-size: 12px;color: #7A8794;line-height: 16px;overflow: hidden; text-overflow: ellipsis;word-break: keep-all;white-space: nowrap;">
                                {row.goods?.displaySpec || ''} {row.goods?.manufacturer || ''}
                            </div>
                        </abc-flex>
                    </abc-table-cell>);
                        },
                    },
                    goodsInventoryCountWording: {
                        dataFormatter: (_,row) => {
                            const {
                                pieceCount = 0, packageCount = 0, goods,
                            } = row;
                            const {
                                packageUnit,
                                pieceUnit,
                            } = goods || {};
                            let wording = '';
                            if (packageCount && packageUnit) {
                                wording += `${packageCount}${packageUnit}`;
                            }
                            if (pieceCount && pieceUnit) {
                                wording += `${pieceCount}${pieceUnit}`;
                            }
                            if (!wording && packageUnit) {
                                wording += `0${packageUnit}`;
                            }
                            if (!wording && pieceUnit) {
                                wording += `0${pieceUnit}`;
                            }
                            return wording;
                        },
                    },
                    appearancePackageCondition: {
                        dataFormatter: (appearancePackageCondition) => options.qualityConditionOptions.find((item) => {
                            return item.value === appearancePackageCondition;
                        })?.label || '',
                    },
                    qualityCondition: {
                        dataFormatter: (qualityCondition) => options.qualityConditionOptions.find((item) => {
                            return item.value === qualityCondition;
                        })?.label || '',
                    },
                    result: {
                        dataFormatter: (result) => options.resultOptions.find((item) => {
                            return item.value === result;
                        })?.label || '',
                    },
                });
            },
        },
        watch: {
            toolsParams: {
                handler() {
                    this.initPageIndex();
                    this.fetchDataList();
                },
                deep: true,
            },
            name: {
                handler() {
                    this.initFetchDataList();
                },
                deep: true,
            },
        },
        mounted() {
            this.setPageSizeWithTableHeight();
            const { tableLayout } = this.$refs.abcTable || {};
            if (tableLayout) {
                this.pageParams.limit = tableLayout.paginationLimit || 10;
                this.fetchDataList();
            }
        },
        methods: {
            customTrKey(item) {
                return `item_${item.id}`;
            },
            customGoodsTrKey(item) {
                return `goods_${item.id}_${item.batchId || 0}`;
            },
            statusConfig(row) {
                const waitConfig = {
                    theme: 'primary',
                    variant: row.clinicId === this.clinicId ? 'light-outline' : 'outline',
                };
                const successConfig = {
                    theme: 'success',
                    variant: 'outline',
                };
                const configMap = {
                    [ConserveStatus.REVIEW]: waitConfig,
                    [ConserveStatus.FINISHED]: successConfig,
                };
                return configMap[row.status] || successConfig;

            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2023-12-28
             * @returns {Object}
             */
            createParams() {
                const {
                    dateRange, // 日期范围
                    clinicId, // 门店ID
                    maintenanceType, // 养护类型
                    goodsId = '',
                } = this.toolsParams;
                const {
                    limit,
                    offset,
                } = this.pageParams;
                const params = {
                    clinicId, // 门店ID
                    goodsId,
                    maintenanceDateStart: dateRange[0], // 养护时间开始日期 yyyy-MM-dd
                    maintenanceDateEnd: dateRange[1], // 养护时间结束日期 yyyy-MM-dd
                    maintenanceType, // 养护类型
                    offset, // 分页
                    limit, // 每页条数
                    category: this.category,
                };
                return params;
            },
            selectGoods(goods) {
                if (!goods) return;
                this.searchKey = goodsFullName(goods);
                this.toolsParams.goodsId = goods.id;
            },

            clearSearch() {
                this.searchKey = '';
                this.toolsParams.goodsId = '';
            },
            async initFetchDataList() {
                this.pageParams.offset = 0;
                this.toolsParams = {
                    dateRange: this.createDateRangeCurMonth(), // 日期范围
                    clinicId: '', // 门店ID
                    maintenanceType: '', // 养护类型
                    goodsId: '',
                };
                await this.fetchDataList();
            },
            /**
             * 查询记录数据
             * <AUTHOR>
             * @date 2023-12-28
             */
            async fetchDataList() {
                this.loading = true;
                const params = this.createParams();
                let fetchResponse = null;
                if (params.goodsId) {
                    fetchResponse = await GspAPI.fetchMedicineMaintenanceOrderItemList(params);
                } else {
                    fetchResponse = await GspAPI.fetchMedicineMaintenanceOrderList(params);
                }
                if (!isEqual(params, this.createParams())) {
                    return fetchResponse;
                }
                this.loading = false;
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                if (params.goodsId) {
                    this.originGoodsData = fetchResponse.data;
                } else {
                    this.originData = fetchResponse.data;
                }
            },
            async exportMedicineMaintenanceOrderList() {
                try {
                    this.exportLoading = true;
                    const params = this.createParams();
                    await GspAPI.exportMedicineMaintenanceOrderList(params);
                } catch (e) {
                    console.log(e);
                } finally {
                    this.exportLoading = false;
                }
            },
            /**
             * 当点击新增记录
             * <AUTHOR>
             * @date 2024-01-03
             */
            async confirm() {
                this.conserveDetailVisible = false;
                this.initPageIndex();
                await this.fetchDataList();
            },
            async onClickCreate() {
                this.propsData = {};
                this.conserveDetailVisible = true;
            },
            /**
             * 当点击导出时
             * <AUTHOR>
             * @date 2024-01-03
             */
            onClickExport() {
                console.log('点击导出，todo');
            },
            /**
             * 当点击一行触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Object} row
             */
            async onClickRow(row) {
                this.propsData = {
                    id: row.id, // 药品养护单ID
                    status: row.status,
                    value: true,
                    // 非总部 可养护  总部 id相同 可养护
                    isMultiplex: (!this.isChainAdmin && !row.status) || (this.isChainAdmin && row.clinicId === this.clinicId && !row.status),
                    goodsId: this.toolsParams.goodsId || '',
                };
                this.conserveDetailVisible = true;
            },
            pageChange(page) {
                const offset = (page - 1) * this.pageParams.limit;
                this.pageParams.offset = offset;
                this.fetchDataList();
            },
            /**
             * 当点击复用时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} row
             */
            async onClickMultiplex(row) {
                this.propsData = {
                    id: row.id, // 药品养护单ID
                    isMultiplex: true, //复用
                    value: true,
                };
                this.conserveDetailVisible = true;
            },
            /**
             * 初始化页码
             * <AUTHOR>
             * @date 2023-12-29
             */
            initPageIndex() {
                this.pageParams.offset = 0;
            },
        },
    };
</script>
