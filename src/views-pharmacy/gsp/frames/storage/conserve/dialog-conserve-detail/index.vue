<template>
    <abc-dialog
        v-model="visible"
        class="pharmacy__first-battalion__medicines__dialog"
        :title="title"
        size="hugely"
        :responsive="true"
        append-to-body
        :auto-focus="false"
        @open="openDialog"
    >
        <abc-form
            v-if="!!formData"
            ref="formData"
            v-abc-loading="loading"
            style="height: 100%;"
            item-no-margin
        >
            <abc-layout preset="dialog-table">
                <abc-layout-header>
                    <abc-form-item-group is-excel>
                        <abc-descriptions
                            :column="4"
                            :label-width="100"
                            size="large"
                            grid
                            class="base-info"
                            background
                        >
                            <abc-descriptions-item label="门店" :span="1">
                                <div v-abc-title.ellipsis="formData.clinicName"></div>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="养护类型" :span="1" content-padding="0">
                                <!--自动养护的表不能修改养护类型-->
                                <abc-form-item required>
                                    <abc-select
                                        v-model="formData.maintenanceType"
                                        :no-icon="isDisabledForm || isWaitHandleConserve"
                                        :disabled="isDisabledForm || isWaitHandleConserve"
                                        size="large"
                                    >
                                        <abc-option
                                            v-for="item in options.maintenanceTypeOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="养护时间" :span="1" content-padding="0">
                                <abc-form-item required>
                                    <abc-date-time-picker
                                        v-model="formData.maintenanceTime"
                                        :picker-options="pickerOptions"
                                        :disabled="isDisabledForm || isWaitHandleConserve"
                                        :clearable="false"
                                        :show-icon="false"
                                        size="large"
                                    ></abc-date-time-picker>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="养护人" :span="1" content-padding="0">
                                <abc-form-item required>
                                    <abc-select
                                        v-model="formData.maintainer"
                                        :disabled="isDisabledForm"
                                        :no-icon="isDisabledForm"
                                        :fetch-suggestions="(searchKey) => searchKeyMaintainer = searchKey"
                                        with-search
                                    >
                                        <abc-option
                                            v-for="item in maintainerOptions"
                                            :key="item.value"
                                            :value="item.value"
                                            :label="item.label"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-descriptions-item>
                            <abc-descriptions-item label="备注" :span="4" content-padding="0">
                                <abc-form-item>
                                    <abc-input
                                        v-model="formData.remark"
                                        :disabled="isDisabledForm"
                                        :max-length="100"
                                        :placeholder="isDisabledForm ? '' : '请输入备注'"
                                        size="large"
                                    ></abc-input>
                                </abc-form-item>
                            </abc-descriptions-item>
                        </abc-descriptions>
                    </abc-form-item-group>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-layout style="height: 100%;">
                        <abc-layout-content>
                            <abc-table
                                ref="tableRef"
                                type="excel"
                                class="focus-table"
                                :render-config="renderConfig"
                                :data-list="medicineMaintenances"
                                empty-size="small"
                                :support-delete-tr="isCreate"
                                :auto-height="true"
                                need-delete-confirm
                                enable-virtual-list
                                :virtual-list-config="virtualListConfig"
                                :custom-tr-key="customTrKey"
                                @delete-tr="onClickDeleteTr"
                            >
                                <template v-if="isShowGoodsSearch" #topHeader>
                                    <abc-flex justify="space-between" align="center" flex="1">
                                        <goods-auto-complete
                                            only-stock
                                            class="back-focus-to-autocomplete"
                                            placeholder="输入商品名称或扫码添加"
                                            :search.sync="fetchParams.keyword"
                                            :focus-show="true"
                                            :type-arr="fetchParams.type"
                                            :sub-type-arr="fetchParams.subType"
                                            :type-id-list="fetchParams.typeIdList"
                                            :c-m-spec="fetchParams.cMSpec"
                                            :width="480"
                                            need-filter-disable
                                            is-type-arr
                                            size="medium"
                                            format-count-key="stock"
                                            :clinic-id="clinicId"
                                            :format-search-result-fn="formatSearchResultFn"
                                            :next-input-auto-focus="false"
                                            @selectGoods="onSelectGoods"
                                        >
                                            <abc-icon slot="prepend" icon="n-add-line-medium"></abc-icon>
                                        </goods-auto-complete>
                                        <abc-button
                                            variant="ghost"
                                            theme="default"
                                            @click="onClickBatchAddition"
                                        >
                                            批量新增
                                        </abc-button>
                                    </abc-flex>
                                </template>
                                <template #shortId="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="!row.group">
                                            <abc-input v-model="row.shortId" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <abc-form-item v-else>
                                        <abc-input v-model="row.shortId" disabled></abc-input>
                                    </abc-form-item>
                                </template>
                                <template #medicName="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-table-cell v-if="!row.group" style="width: 100%; height: 44px;">
                                            <abc-layout
                                                v-abc-goods-hover-popper="{
                                                    goods: row.productInfo,
                                                    showShebaoCode: true,
                                                    showF1: row.productInfo.type === GoodsTypeEnum.MEDICINE,
                                                }"
                                                style="width: 100%;"
                                            >
                                                <abc-title v-abc-title.ellipsis="row.displayName"></abc-title>
                                                <abc-p
                                                    v-abc-title.ellipsis="`${row.displaySpec || ''} ${row.manufacturer || ''}`"
                                                    style="line-height: 14px;"
                                                    gray
                                                >
                                                </abc-p>
                                            </abc-layout>
                                        </abc-table-cell>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <abc-table-cell v-else style="width: 100%; height: 44px;">
                                        <abc-layout
                                            v-abc-goods-hover-popper="{
                                                goods: row.productInfo,
                                                showShebaoCode: true,
                                                showF1: row.productInfo.type === GoodsTypeEnum.MEDICINE,
                                            }"
                                            style="width: 100%;"
                                        >
                                            <abc-title v-abc-title.ellipsis="row.displayName"></abc-title>
                                            <abc-p
                                                v-abc-title.ellipsis="`${row.displaySpec || ''} ${row.manufacturer || ''}`"
                                                style="line-height: 14px;"
                                                gray
                                            >
                                            </abc-p>
                                        </abc-layout>
                                    </abc-table-cell>
                                </template>
                                <template #batch="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <div
                                            v-if="!row.group"
                                            v-abc-title.ellipsis="`已选择${ row.batchs && row.batchs.length || 0 }个批次`"
                                            style="
                                width: 100%;
                                padding: 0 10px !important;
                                overflow: hidden;
                                line-height: 44px;
                                text-overflow: ellipsis;
                                word-break: keep-all;
                                white-space: nowrap;
                                cursor: pointer;"
                                            @click="handleSelectBatches(row)"
                                        >
                                        </div>
                                        <div v-else v-abc-title.ellipsis="row && (row.batchNo ?? '-')" class="goods-info-wrapper ellipsis pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </div>
                                    </template>
                                    <template v-else>
                                        <abc-form-item v-if="isDisabledForm" required>
                                            <abc-select
                                                v-model="row.batchNo"
                                                :disabled="isDisabledForm"
                                                :no-icon="isDisabledForm"
                                                :width="119"
                                                :inner-width="119"
                                                adaptive-width
                                            >
                                                <abc-option
                                                    v-for="one in row.batchList"
                                                    :key="one.batchNo"
                                                    :value="one.batchNo"
                                                    :label="one.batchNo"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-table-cell
                                            v-else
                                            v-abc-title.ellipsis="`已选择${ row.batchs && row.batchs.length || 0 }个批次`"
                                            style="padding: 0 10px !important; cursor: pointer;"
                                            @click.native="handleSelectBatches(row)"
                                        >
                                        </abc-table-cell>
                                    </template>
                                </template>
                                <template #productionDate="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group">
                                            <abc-input :value="row.productionDate ?? '-'" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <abc-form-item v-else>
                                        <abc-input v-if="isDisabledForm" :value="row.productionDate" disabled></abc-input>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </abc-form-item>
                                </template>
                                <template #expiryDate="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group">
                                            <abc-input :value="row.expiryDate ?? '-'" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <template v-else>
                                        <abc-form-item v-if="isDisabledForm">
                                            <abc-input :value="row.expiryDate" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                </template>
                                <template #stockInTime="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group">
                                            <abc-input :value="getFormatDate(row.stockInTime, true)" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <template v-else>
                                        <abc-form-item v-if="isDisabledForm">
                                            <abc-input :value="getFormatDate(row.stockInTime, true)" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                </template>
                                <template #lastMaintenanceTime="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group">
                                            <abc-input :value="getFormatDate(row.lastMaintenanceTime, true)" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <template v-else>
                                        <abc-form-item v-if="isDisabledForm">
                                            <abc-input :value="getFormatDate(row.lastMaintenanceTime, true)" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                </template>
                                <template #inventoryCountWording="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group">
                                            <abc-input :value="row.inventoryCountWording" disabled></abc-input>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <template v-else>
                                        <abc-form-item>
                                            <abc-input v-if="isDisabledForm" :value="row.inventoryCountWording" disabled></abc-input>
                                            <div v-else class="pharmacy__first-battalion__medicines__dialog--not-allow"></div>
                                        </abc-form-item>
                                    </template>
                                </template>
                                <template #appearancePackageCondition="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group" required>
                                            <abc-select
                                                v-model="row.appearancePackageCondition"
                                                :disabled="isDisabledForm"
                                                :no-icon="isDisabledForm"
                                                :inner-width="87"
                                                :width="88"
                                                adaptive-width
                                            >
                                                <abc-option
                                                    v-for="one in options.appearancePackageConditionOptions"
                                                    :key="one.value"
                                                    :value="one.value"
                                                    :label="one.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <template v-else>
                                        <abc-form-item required>
                                            <abc-select
                                                v-model="row.appearancePackageCondition"
                                                :disabled="isDisabledForm"
                                                :no-icon="isDisabledForm"
                                                :inner-width="87"
                                                :width="88"
                                                adaptive-width
                                            >
                                                <abc-option
                                                    v-for="one in options.appearancePackageConditionOptions"
                                                    :key="one.value"
                                                    :value="one.value"
                                                    :label="one.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                    </template>
                                </template>
                                <template #qualityCondition="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group" required>
                                            <abc-select
                                                v-model="row.qualityCondition"
                                                :disabled="isDisabledForm"
                                                :no-icon="isDisabledForm"
                                                :inner-width="87"
                                                :width="88"
                                                adaptive-width
                                            >
                                                <abc-option
                                                    v-for="one in options.qualityConditionOptions"
                                                    :key="one.value"
                                                    :value="one.value"
                                                    :label="one.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <abc-form-item v-else required>
                                        <abc-select
                                            v-model="row.qualityCondition"
                                            :disabled="isDisabledForm"
                                            :no-icon="isDisabledForm"
                                            :inner-width="87"
                                            :width="88"
                                            adaptive-width
                                        >
                                            <abc-option
                                                v-for="one in options.qualityConditionOptions"
                                                :key="one.value"
                                                :value="one.value"
                                                :label="one.label"
                                            ></abc-option>
                                        </abc-select>
                                    </abc-form-item>
                                </template>
                                <template #measure="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group" required>
                                            <abc-select
                                                v-model="row.measure"
                                                :disabled="isDisabledForm"
                                                :no-icon="isDisabledForm"
                                                adaptive-width
                                                :inner-width="119"
                                                :width="120"
                                                multiple
                                                multi-label-mode="text"
                                                :max-tag="1"
                                            >
                                                <abc-option
                                                    v-for="one in options.yhMeasureOptions"
                                                    :key="one.value"
                                                    :value="one.value"
                                                    :label="one.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <abc-form-item v-else required>
                                        <abc-select
                                            v-model="row.measure"
                                            :disabled="isDisabledForm"
                                            :no-icon="isDisabledForm"
                                            adaptive-width
                                            :inner-width="119"
                                            :width="120"
                                            multiple
                                            multi-label-mode="text"
                                            :max-tag="1"
                                        >
                                            <abc-option
                                                v-for="one in options.yhMeasureOptions"
                                                :key="one.value"
                                                :value="one.value"
                                                :label="one.label"
                                            ></abc-option>
                                        </abc-select>
                                    </abc-form-item>
                                </template>
                                <template #result="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-form-item v-if="row.group" required>
                                            <abc-select
                                                v-model="row.result"
                                                :disabled="isDisabledForm"
                                                :no-icon="isDisabledForm"
                                                :width="99"
                                                :inner-width="99"
                                                adaptive-width
                                            >
                                                <abc-option
                                                    v-for="one in options.resultOptions"
                                                    :key="one.value"
                                                    :value="one.value"
                                                    :label="one.label"
                                                ></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <abc-form-item v-else required>
                                        <abc-select
                                            v-model="row.result"
                                            :disabled="isDisabledForm"
                                            :no-icon="isDisabledForm"
                                            :inner-width="99"
                                            :width="99"
                                            adaptive-width
                                        >
                                            <abc-option
                                                v-for="one in options.resultOptions"
                                                :key="one.value"
                                                :value="one.value"
                                                :label="one.label"
                                            ></abc-option>
                                        </abc-select>
                                    </abc-form-item>
                                </template>
                                <template #storage="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <abc-table-cell v-if="row.group">
                                            <span v-abc-title.ellipsis="row.storage || ''"></span>
                                        </abc-table-cell>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <template v-else>
                                        <abc-table-cell v-if="isDisabledForm">
                                            <span v-abc-title.ellipsis="row.storage || ''"></span>
                                        </abc-table-cell>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                </template>
                                <template #remark="{ trData: row }">
                                    <template v-if="row.batchGroup">
                                        <template v-if="row.group">
                                            <abc-table-cell v-if="isDisabledForm" v-abc-title.ellipsis="row.remark">
                                            </abc-table-cell>
                                            <abc-form-item v-else>
                                                <abc-input
                                                    v-model="row.remark"
                                                    :max-length="100"
                                                    @enter="enterEvent"
                                                ></abc-input>
                                            </abc-form-item>
                                        </template>
                                        <abc-table-cell v-else class="pharmacy__first-battalion__medicines__dialog--not-allow">
                                        </abc-table-cell>
                                    </template>
                                    <template v-else>
                                        <abc-table-cell v-if="isDisabledForm" v-abc-title.ellipsis="row.remark">
                                        </abc-table-cell>
                                        <abc-form-item v-else>
                                            <abc-input
                                                v-model="row.remark"
                                                :max-length="100"
                                                @enter="enterEvent"
                                            ></abc-input>
                                        </abc-form-item>
                                    </template>
                                </template>
                                <template #footer>
                                    <abc-flex style="width: 100%; padding: 0 16px;">
                                        <abc-p>
                                            共
                                            <span style="color: #000000;">
                                                {{ total }}
                                            </span>
                                            条
                                        </abc-p>
                                    </abc-flex>
                                </template>
                            </abc-table>
                        </abc-layout-content>
                    </abc-layout>
                </abc-layout-content>
            </abc-layout>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button
                v-if="isShowExport"
                icon="n-upload-line"
                variant="ghost"
                @click="exportExcelCommon(GSPExportEnum.STORAGE.CONSERVE.ORDER,
                                          id,
                                          exportTitle,
                                          { title: originData?.orderNo },
                                          true)"
            >
                导出
            </abc-button>
            <abc-button
                v-if="isVisibleSubmitBtn"
                :loading="loadingSubmit"
                @click="onClickSubmit"
            >
                提交
            </abc-button>
            <abc-button
                type="blank"
                @click="onClickCancel"
            >
                {{ isDisabledForm ? '关闭' : '取消' }}
            </abc-button>
        </div>
        <filter-dialog
            v-if="openFilterDialog"
            v-model="openFilterDialog"
            :data-list="formData.medicineMaintenances"
            @sure-filter="sureFilter"
        ></filter-dialog>
    </abc-dialog>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import pickerOptions from 'views/common/pickerOptions';

    const GoodsAutoComplete = () => import('src/views/inventory/common/goods-auto-complete');
    const MedicineSelectionDialog = () => import('@/views-pharmacy/components/medicine-selection-dialog/index.vue');
    import {
        DATA_PERMISSION_CONTROL_SCENE,
    } from 'views/inventory/common/data-permission-control';
    import clone from 'utils/clone';
    import { pick } from 'utils/index';
    import { mapGetters } from 'vuex';
    import * as constants from '@/views-pharmacy/common/constants';
    import * as options from '@/views-pharmacy/common/options';
    import * as tools from '@/views-pharmacy/common/tools';
    import { insertArrayAtIndex } from 'utils/remove-duplicates-by-id';
    import DialogBatches from '@/views-pharmacy/charge/components/dialog-bathces-gsp';
    import { useFillHeightInDialog } from '@/views-pharmacy/inventory/hooks/useFillHeightInDialog';
    import FilterDialog from '@/views-pharmacy/gsp/frames/storage/conserve/filter-dialog/index.vue';
    import {
        ConserveStatus,
    } from '@/views-pharmacy/inventory/constant';
    import { formatDate } from '@abc/utils-date';
    import {
        StorageTypeArr, StorageTypeObj,
    } from 'views/common/inventory/constants';
    import EnterEvent from 'views/common/enter-event';
    import {
        exportExcelCommon, GSPExportEnum,
    } from '@/views-pharmacy/gsp/utils/gspExportUtil';
    import {
        GoodsTypeEnum,
    } from '@abc/constants';
    import { GoodsTypeIdEnum } from '@abc/constants/src/goods';

    export default {
        components: {
            GoodsAutoComplete,
            FilterDialog,
        },
        mixins: [
            pickerOptions,
            EnterEvent,
        ],
        props: {
            value: Boolean,
            id: {
                type: String,
                default: '',
            },
            status: {
                type: Number,
                default: ConserveStatus.FINISHED,
            },
            isMultiplex: {
                type: Boolean,
                default: false,
            },
            goodsId: {
                type: String,
                default: '',
            },
            isMedicalDeviceConserve: {
                type: Boolean,
                default: false,
            },
            pharmacy: {
                type: Object,
                default: () => ({}),
            },
            category: {
                type: Number,
                default: 0,
            },
        },
        setup() {
            const {
                dialogBody,
                tableRef,
                openDialog,
                fillBottomOffset,
            } = useFillHeightInDialog();
            return {
                dialogBody,
                tableRef,
                openDialog,
                fillBottomOffset,
            };
        },
        data() {
            return {
                GSPExportEnum,
                GoodsTypeEnum,
                DATA_PERMISSION_CONTROL_SCENE,
                options,
                pickerOptions: null,
                searchKeyMaintainer: '', // 搜索关键词 - 养护人员
                loading: false,
                originData: null,
                formData: null,
                // todo 待确认查询参数
                fetchParams: {
                    keyword: '',
                    typeId: '',
                    typeLabel: '全部类型',
                    type: [],
                    subType: [],
                    typeIdList: [],
                    cMSpec: '',
                },
                loadingSubmit: false,
                batchListData: null,
                openFilterDialog: false,
                openFilterType: 0,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'employeeList',
                'userInfo',
            ]),
            exportTitle() {
                if (this.isMedicalDeviceConserve) {
                    return '医疗器械养护记录单据';
                }
                return '药品养护记录单据';
            },
            visible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            total() {
                return this.medicineMaintenances.filter((item) => {
                    // 非分组或者是分组但不是假数据
                    return item.batchId !== -1;
                }).length || 0;
            },
            // 等待处理养护
            isWaitHandleConserve() {
                return this.status === ConserveStatus.REVIEW;
            },
            // 过滤了视图
            isFilterView() {
                return this.formData.medicineMaintenances.filter((item) => {return !item.isShow;}).length;
            },
            medicineMaintenances: {
                get() {
                    return this.formData.medicineMaintenances?.filter((item) => {
                        return item.isShow && !item.resultType;
                    });
                },
                set(val) {
                    const formData = clone(this.formData);
                    formData.medicineMaintenances = val;
                    this.$emit('update:formDate', formData);
                },
            },
            virtualListConfig() {
                return {
                    rowHeight: 40,
                    visibleCount: 7,
                    bufferLoad: true,
                    bufferSize: 10,
                };
            },
            renderConfig() {
                return {
                    hasInnerBorder: false,
                    list: [
                        {
                            'key': 'shortId',
                            'label': '商品编码',
                            'style': {
                                'max-width': '80px',
                                'width': '80px',
                            },
                        },
                        {
                            'key': 'medicName',
                            'label': '商品名称',
                            'style': {
                                'flex': '1',
                                'min-width': '160px',
                                'width': '160px',
                            },
                            headerAppendRender: () => {
                                const openFilterDialog = () => {
                                    this.openFilterDialog = true;
                                };
                                return <svg xmlns="http://www.w3.org/2000/svg" style="cursor: pointer;margin-left:2px;" width="14" height="14" viewBox="0 0 14 14" onClick={openFilterDialog} fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1.83652 4.41679L4.84219 5.91962C5.00276 5.99991 5.10415 6.16402 5.10415 6.34355V10.792C5.10415 11.1511 5.30702 11.4793 5.62817 11.6399L7.52402 12.5879C8.15429 12.9029 8.89585 12.4446 8.89585 11.74V6.34355C8.89585 6.16402 8.99724 5.99991 9.15781 5.91962L12.1635 4.41679C12.4847 4.25622 12.6875 3.92798 12.6875 3.56894V2.25895C12.6875 1.73543 12.2631 1.31104 11.7396 1.31104H2.2604C1.73688 1.31104 1.3125 1.73543 1.3125 2.25895V3.56894C1.3125 3.92798 1.51538 4.25622 1.83652 4.41679ZM6.0521 6.34355C6.0521 5.7577 5.79007 5.33377 5.26608 5.07178L2.3914 3.63444C2.31111 3.59429 2.2604 3.51224 2.2604 3.42248V2.49594C2.2604 2.36505 2.36653 2.25895 2.49741 2.25895H11.5026C11.6335 2.25895 11.7396 2.36505 11.7396 2.49594V3.42248C11.7396 3.51224 11.6889 3.59429 11.6087 3.63444L8.73392 5.07178C8.20993 5.33377 7.9479 5.7577 7.9479 6.34355V11.3566C7.9479 11.5327 7.76249 11.6472 7.60493 11.5685L6.18305 10.8575C6.10276 10.8174 6.0521 10.7353 6.0521 10.6456V6.34355Z" fill={ this.isFilterView ? '#459eff' : '#AAB4BF'}/>
                                </svg>;
                            },
                        },
                        {
                            'key': 'batch',
                            'label': '生产批号',
                            'style': {
                                'min-width': '120px',
                                'width': '120px',
                            },
                        },
                        {
                            'key': 'productionDate',
                            'label': '生产日期',
                            'style': {
                                'width': '100px',
                                'min-width': '100px',
                            },
                        },
                        {
                            'key': 'expiryDate',
                            'label': '有效日期',
                            'style': {
                                'width': '100px',
                                'min-width': '100px',
                            },
                        },
                        {
                            'key': 'stockInTime',
                            'label': '入库日期',
                            'style': {
                                'width': '100px',
                                'min-width': '100px',
                            },
                        },
                        {
                            'key': 'lastMaintenanceTime',
                            'label': '上次养护日期',
                            'style': {
                                'width': '100px',
                                'min-width': '100px',
                            },
                        },
                        {
                            'key': 'inventoryCountWording',
                            'label': '养护数量',
                            'style': {
                                'width': '90px',
                                'min-width': '90px',
                            },
                        },
                        {
                            'key': 'appearancePackageCondition',
                            'label': '外观包装',
                            'style': {
                                'width': '88px',
                                'min-width': '88px',
                            },
                        },
                        {
                            'key': 'qualityCondition',
                            'label': '质量状况',
                            'style': {
                                'width': '88px',
                                'min-width': '88px',
                            },
                        },
                        {
                            'key': 'measure',
                            'label': '养护措施',
                            'style': {
                                'width': '120px',
                                'min-width': '120px',
                            },
                        },
                        {
                            'key': 'result',
                            'label': '处理结果',
                            'style': {
                                'width': '100px',
                                'min-width': '100px',
                            },
                            headerAppendRender: () => {
                                const changeFilterDialog = (e) => {
                                    this.openFilterType = e;
                                    if (this.openFilterType === 0) {
                                        this.formData.medicineMaintenances = this.formData.medicineMaintenances.map((item) => {
                                            return {
                                                ...item,
                                                resultType: 0, // 都展示
                                            };
                                        });
                                    } else if (this.openFilterType === 1) {
                                        this.formData.medicineMaintenances = this.formData.medicineMaintenances.map((item) => {
                                            if (item.batchId !== -1 && item.result === 1) {
                                                return {
                                                    ...item,
                                                    resultType: 1,
                                                };
                                            }
                                            if (item.batchId === -1) {
                                                // 可展示的数量
                                                const flag = !!this.formData.medicineMaintenances.filter((it) => {
                                                    return it.goodsId === item.goodsId && it.batchId !== -1 && it.result === 0;
                                                })?.length;
                                                if (!flag) {
                                                    return {
                                                        ...item,
                                                        resultType: 1,
                                                    };
                                                }
                                            }
                                            return {
                                                ...item,
                                                resultType: 0,
                                            };
                                        });
                                    } else {
                                        this.formData.medicineMaintenances = this.formData.medicineMaintenances.map((item) => {
                                            if (item.batchId !== -1 && item.result === 0) {
                                                return {
                                                    ...item,
                                                    resultType: 1,
                                                };
                                            }
                                            if (item.batchId === -1) {
                                                // 可展示的数量
                                                const flag = !!this.formData.medicineMaintenances.filter((it) => {
                                                    return it.goodsId === item.goodsId && it.batchId !== -1 && it.result === 1;
                                                })?.length;
                                                if (!flag) {
                                                    return {
                                                        ...item,
                                                        resultType: 1,
                                                    };
                                                }
                                            }
                                            return {
                                                ...item,
                                                resultType: 0,
                                            };
                                        });
                                    }
                                };
                                return <abc-select
                                      value={this.openFilterType}
                                      reference-mode="icon"
                                      reference-icon="n-filter-line"
                                      style="margin-left:2px;height:36px;"
                                      class="conserve-detail-filter-select"
                                      reference-icon-options={{
size: '12', 'color': this.openFilterType !== 0 ? '#459eff' : '#AAB4BF',
}}
                                      onChange={changeFilterDialog}
                                      width={100}
                                    >
                                       <abc-option value={0} label="全部"></abc-option>
                                       <abc-option value={1} label="正常销售"></abc-option>
                                       <abc-option value={2} label="停止销售"></abc-option>
                                   </abc-select>;
                            },
                        },
                        {
                            'key': 'storage',
                            'label': '存储条件',
                            'style': {
                                'width': '100px',
                                'min-width': '100px',
                            },
                        },
                        {
                            'key': 'remark',
                            'label': '备注',
                            'style': {
                                'width': '100px',
                                'min-width': '100px',
                            },
                        },
                    ],
                };
            },
            // 养护人选项
            maintainerOptions() {
                let employeeList = this.employeeList || [];
                if (this.searchKeyMaintainer) {
                    // 有关键词时，过滤一下
                    employeeList = tools.filterEmployeeBySearchKey(employeeList, this.searchKeyMaintainer);
                }
                return employeeList.map((item) => ({
                    value: item.employeeId,
                    label: item.employeeName,
                }));
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 是否新增
            isCreate() {
                if (!this.id) {
                    // 没有ID的时候，是新增
                    return true;
                }
                if (this.isMultiplex) {
                    // 是复用的时候，是新增
                    return true;
                }
                return false;
            },
            // 是否预览
            isPreview() {
                return !this.isCreate;
            },
            // 是否禁用表单
            isDisabledForm() {
                return this.isPreview;
            },
            // 标题
            title() {
                let tit = this.isMedicalDeviceConserve ? '器械养护记录' : '药品养护记录';
                if (this.isPreview) {
                    // 预览时，标题显示单号
                    tit += ` ${this.originData?.orderNo || ''}`;
                }
                return tit;
            },
            // 是否显示提交按钮
            isVisibleSubmitBtn() {
                return this.isCreate;
            },
            // 是否显示goods搜索
            isShowGoodsSearch() {
                return this.isCreate;
            },
            // 是否显示导出按钮
            isShowExport() {
                return !this.isCreate;
            },
        },
        created() {
            if (this.isMedicalDeviceConserve) {
                this.fetchParams.typeIdList = [
                    GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
                ];
                this.fetchParams.typeLabel = '医疗器械';
            } else {
                this.fetchParams.typeIdList = [
                    GoodsTypeIdEnum.MEDICINE_WESTERN,
                    GoodsTypeIdEnum.CHINESE_MEDICINE,
                    GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                    GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES,
                    GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE,
                    GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT,
                    GoodsTypeIdEnum.MATERIAL_DISINFECTANT,
                ];
            }
            this.formData = this.createFormData();
            this.pickerOptions = this.createPickerOptions();
        },
        async mounted () {
            if (this.id) {
                this.loading = true;
            }
            await this.fetchMedicineMaintenanceOrder();
            await this.fetchBatchListByOriginData();

            this.formData = this.createFormData();
            if (this.id) {
                this.loading = false;
            }
        },
        methods: {
            exportExcelCommon,
            customTrKey(item) {
                return `${item.goodsId}_${item.batchId}`;
            },
            getFormatDate(time, isNotNull = false) {
                if (!time) {
                    if (!isNotNull) {
                        return '';
                    }
                    return null;
                }
                return formatDate(time, 'YYYY-MM-DD');
            },
            getStorageTypeName(val) {
                const arr = StorageTypeArr.filter((item) => val & item).map((type) => StorageTypeObj[type]);

                return arr.join('/');
            },
            insertArrayAtIndex,
            // 确认需要展示的列表数据
            sureFilter(filterList) {
                this.formData.medicineMaintenances = this.formData.medicineMaintenances?.map((item) => {
                    return {
                        ...item,
                        isShow: filterList.includes(item.goodsId),
                    };
                });
            },
            handleSelectBatches(row) {
                if (this.isDisabledForm) {
                    return;
                }
                const batchItem = {
                    ...row.productInfo,
                    ...row,
                };
                this._batcherDialog = new DialogBatches({
                    batchItem,
                    clinicId: this.clinicId,
                    onConfirm: (item) => {
                        const {
                            dataList = [], batchItem = {}, ownList = [],
                        } = item;
                        // dataList是完整的当前选中的数据 batchItem中的批次信息并不完整应该以当前数据为准
                        this.changeBatchs(dataList, {
                            ...batchItem,
                            batchList: dataList,
                        });
                        this.formData.medicineMaintenances = this.formData.medicineMaintenances.map((i) => {
                            // 匹配goodsId相同stockBatchId相同
                            const current = dataList.find((it) => {return it.goodsId === i.goodsId && it.batchId === i.batchId;});
                            if (current) {
                                const handle = {
                                    ...i,
                                    packageCount: current.conversePackageCount || '',
                                    pieceCount: current.conversePieceCount || '',
                                };
                                if (current.conversePackageCount || current.conversePieceCount) {
                                    handle.inventoryCountWording = tools.getInventoryCountWording(handle);
                                }
                                return handle;
                            }
                            return {
                                ...i,
                            };
                        });
                        this.formData.medicineMaintenances = this.formData.medicineMaintenances.map((i) => {
                            if (batchItem.goodsId === i.goodsId) {
                                const handle = {
                                    ...i,
                                    batchList: ownList.map((item) => {
                                        const current = dataList.find((it) => {return it.goodsId === item.goodsId && it.batchId === item.batchId;});
                                        return {
                                            ...item,
                                            packageCount: current?.conversePackageCount || '',
                                            pieceCount: current?.conversePieceCount || '',
                                        };
                                    }),
                                };
                                return handle;
                            }
                            return {
                                ...i,
                            };
                        });
                    },
                });
                this._batcherDialog.generateDialog();
            },
            // 传入当前batchNo, batchList
            getBatch(batchId, batchList, goodsInfo, currentItem = null) {
                const batchInfo = batchList.find((one) => one.batchId === batchId);
                const info = {
                    batchList, // 批次列表
                    appearancePackageCondition: currentItem?.appearancePackageCondition || constants.appearancePackageConditionConst.GOOD, // 外观包装状况 0:良好 1:破损
                    batchNo: batchInfo.batchNo, // 商品生产批次号
                    expiryDate: '', // 效期
                    goodsId: goodsInfo.goodsId, // 商品ID
                    goodsName: goodsInfo.name, // 商品名称
                    measure: currentItem?.measure || tools.getYhMeasureDefaultValue(), // 养护措施,
                    id: currentItem?.id || '',
                    packageCount: '', // 整包数量
                    pieceCount: '', // 小包装数量
                    pieceNum: '', // 制剂数量
                    productionDate: '', // 生产日期
                    lastMaintenanceTime: null,
                    stockInTime: null,
                    qualityCondition: currentItem?.qualityCondition || constants.qualityConditionConst.GOOD, // 质量状况 0:良好 1:变质
                    remark: currentItem?.remark || '', // 备注
                    result: currentItem?.result || constants.resultConst.NORMAL_SALES, // 处理结果 0:正常销售 1:停止销售
                    // 追加字段
                    shortId: goodsInfo.shortId, // 商品编码
                    displayName: goodsInfo.displayName, // 商品名称
                    storage: goodsInfo.storage, // 存储类型
                    isShow: true,
                    resultType: 0,
                    displaySpec: goodsInfo.displaySpec, // 商品规格
                    manufacturer: goodsInfo.manufacturer || goodsInfo.manufacturerFull, // 生产厂商
                    goodsInfoHtml: tools.createGoodsInfoHtml(goodsInfo),
                    productInfo: goodsInfo,
                    inventoryCountWording: '', // 账面数量
                };
                Object.assign(info, {
                    expiryDate: batchInfo.expiryDate, // 效期
                    packageCount: batchInfo.packageCount, // 整包数量
                    packageUnit: batchInfo.packageUnit || goodsInfo.packageUnit, // 整包单位
                    pieceCount: batchInfo.pieceCount, // 小包装数量
                    pieceUnit: batchInfo.pieceUnit || goodsInfo.pieceUnit, // 小包装单位
                    pieceNum: batchInfo.pieceNum, // 制剂数量
                    productionDate: batchInfo.productDate || batchInfo.productionDate, // 生产日期
                    lastMaintenanceTime: this.getFormatDate(batchInfo.lastMaintenanceTime),
                    stockInTime: this.getFormatDate(batchInfo.stockInTime),
                    batchId: batchInfo.batchId,
                    isShow: true,
                    resultType: 0,
                    inventoryCountWording: currentItem?.inventoryCountWording || tools.getInventoryCountWording({
                        ...batchInfo,
                        packageUnit: batchInfo.packageUnit || goodsInfo.packageUnit, // 整包单位
                        pieceUnit: batchInfo.pieceUnit || goodsInfo.pieceUnit, // 小包装单位
                    }), // 账面数量
                });
                return info;
            },
            changeBatchs(batchs, item) {
                const list = [];
                // 有批次数据 添加或者删除
                if (batchs?.length) {
                    // 1找到第一个数组的下标
                    let medicineMaintenancesIndex = this.formData.medicineMaintenances.findIndex((i) => {
                        return i.goodsId === item.goodsId;
                    });
                    // 先增加一条置顶的数据做切换
                    list.push({
                        batchList: item.batchList, // 批次列表
                        goodsId: item.goodsId,
                        batchGroup: true,
                        batchId: -1,
                        batchs,
                        shortId: item.shortId, // 商品编码
                        isShow: true,
                        resultType: 0,
                        displayName: item.displayName, // 商品名称
                        storage: item.storage, // 存储类型
                        displaySpec: item.displaySpec, // 商品规格
                        manufacturer: item.manufacturer || item.manufacturerFull, // 生产厂商
                        goodsInfoHtml: tools.createGoodsInfoHtml(item),
                        productInfo: item,
                        packageUnit: item.packageUnit, // 整包单位
                        pieceUnit: item.pieceUnit, // 小包装单位
                    });
                    batchs.forEach((batchItem) => {
                        // 同样的数据非一个批次
                        const currentItem = this.formData.medicineMaintenances?.find((i) => {
                            return i.goodsId === item.goodsId && i.batchId === batchItem.batchId;
                        });
                        // 获取每个批次的商品
                        const it = this.getBatch(batchItem.batchId, item.batchList, item, currentItem);
                        list.push({
                            ...it,
                            group: true,
                            batchGroup: true,
                        });
                    });
                    // 下标为0 删除数据再组合
                    if (medicineMaintenancesIndex === 0) {
                        this.formData.medicineMaintenances = list.concat(this.formData.medicineMaintenances.filter((i) => {
                            return i.goodsId !== item.goodsId;
                        }));
                    } else {
                        // 插入的下标为当前的下标 -1
                        medicineMaintenancesIndex = medicineMaintenancesIndex - 1;
                        this.formData.medicineMaintenances = this.insertArrayAtIndex(this.formData.medicineMaintenances.filter((i) => {
                            return i.goodsId !== item.goodsId;
                        }), medicineMaintenancesIndex, list);
                    }
                } else {
                    // 没有选择任何对象 只保留第一个批次的数据 没有批次数据需要删除空对象
                    const medicineMaintenancesItem = this.formData.medicineMaintenances.find((i) => {
                        return i.goodsId === item.goodsId && i.batchId !== -1;
                    });
                    const { batchId } = medicineMaintenancesItem;
                    delete medicineMaintenancesItem.batchGroup;
                    // 删除分组标志
                    delete medicineMaintenancesItem.group;
                    // 删除头部对象
                    this.formData.medicineMaintenances = this.formData.medicineMaintenances.filter((i) => {
                        return i.goodsId !== item.goodsId || (i.goodsId === item.goodsId && i.batchId === batchId && i.batchId !== -1);
                    });
                }
            },
            /**
             * 查询药品养护单详情
             * <AUTHOR>
             * @date 2024-01-03
             */
            async fetchMedicineMaintenanceOrder() {
                if (!this.id) {
                    return;
                }
                const fetchResponse = await GspAPI.fetchMedicineMaintenanceOrder(this.id);
                if (fetchResponse.status === true) {
                    if (this.goodsId) {
                        const handleMedicineMaintenances = fetchResponse.data;
                        handleMedicineMaintenances.medicineMaintenances = handleMedicineMaintenances.medicineMaintenances.filter((it) => {
                            return it.goods.goodsId === this.goodsId;
                        }).concat(handleMedicineMaintenances.medicineMaintenances.filter((it) => {
                            return it.goods.goodsId !== this.goodsId;
                        }));
                        this.originData = handleMedicineMaintenances;
                    } else {
                        this.originData = fetchResponse.data;
                    }
                }
            },
            /**
             * 查询批次流水号 - 通过复用信息
             * <AUTHOR>
             * @date 2024-01-24
             */
            async fetchBatchListByOriginData() {
                if (!this.isMultiplex) {
                    return;
                }
                if (!this.originData) {
                    return;
                }
                const goodsIdList = (this.originData?.medicineMaintenances || []).map((item) => item.goods?.id).filter((item) => !!item);
                const uniqueGoodsIdList = Array.from(new Set(goodsIdList));

                try {
                    const BATCH_SIZE = 990;
                    const allBatchData = {
                        rows: [],
                    };

                    // 如果商品ID列表长度超过990，分批请求
                    if (uniqueGoodsIdList.length > BATCH_SIZE) {
                        const promises = [];
                        for (let i = 0; i < uniqueGoodsIdList.length; i += BATCH_SIZE) {
                            const batchGoodsIds = uniqueGoodsIdList.slice(i, i + BATCH_SIZE);
                            const params = {
                                goodsIdList: batchGoodsIds,
                            };
                            promises.push(GspAPI.fetchHandleBatchListByGoodsIds(params));
                        }

                        const responses = await Promise.all(promises);
                        responses.forEach((response) => {
                            if (response?.data) {
                                allBatchData.rows = allBatchData.rows.concat(response?.data?.rows || []);
                            }
                        });
                    } else {
                        // 如果商品ID列表长度不超过990，直接请求
                        const params = {
                            goodsIdList: uniqueGoodsIdList,
                        };
                        const fetchResponse = await GspAPI.fetchHandleBatchListByGoodsIds(params);
                        allBatchData.rows = fetchResponse?.data?.rows || [];
                    }

                    this.batchListData = allBatchData;
                } catch (e) {
                    console.log(e);
                }
            },
            /**
             * 创建表单数据
             * <AUTHOR>
             * @date 2024-01-03
             * @returns {Object}
             */
            createFormData() {
                const formData = {
                    clinicName: this.currentClinic?.clinicName || '', // 门店名称
                    maintainer: (() => {
                        // 默认养护人，默认当前人员，没有就选项的第一个
                        const target = this.maintainerOptions.find((item) => item.value === this.userInfo?.id) || this.maintainerOptions[0];
                        return target?.value || '';
                    })(), // 养护人id
                    maintenanceTime: (this.id || this.isMultiplex) ? '' : tools.getDatetimeFormat(), // 养护时间
                    maintenanceType: '', // 养护类型
                    remark: '', // 备注
                    medicineMaintenances: [], // 药品养护记录列表
                };
                if (this.originData) {
                    const {
                        maintainer, // 养护人id
                        maintenanceTime, // 养护时间
                        maintenanceType, // 养护类型
                        remark, // 备注
                        medicineMaintenances, // 药品养护记录列表
                        clinicName, // 门店名称
                    } = this.originData;
                    Object.assign(formData, {
                        clinicName,
                        maintainer: maintainer?.id, // 养护人id
                        maintenanceTime: tools.getDatetimeFormat(maintenanceTime), // 养护时间
                        maintenanceType, // 养护类型
                        remark, // 备注
                        medicineMaintenances: (medicineMaintenances || []).map((item) => {
                            const info = {
                                batchList: [{ batchNo: item.batchNo }], // 批次列表
                                appearancePackageCondition: item.appearancePackageCondition, // 外观包装状况 0:良好 1:破损
                                batchNo: item.batchNo, // 商品生产批次号
                                batchId: item.batchId,
                                isShow: true,
                                resultType: 0,
                                expiryDate: item.expiryDate, // 效期
                                goodsId: item.goods.id, // 商品ID
                                goodsName: item.goods.name, // 商品名称
                                measure: tools.measureParse(item.measure), // 养护措施,
                                id: item.id || '',
                                packageCount: this.isWaitHandleConserve ? item.packageCount : (item.goods.packageCount || item.packageCount), // 整包数量
                                pieceCount: this.isWaitHandleConserve ? item.pieceCount : (item.goods.pieceCount || item.pieceCount), // 小包装数量
                                pieceNum: item.pieceNum, // 制剂数量
                                productionDate: item.productionDate, // 生产日期
                                lastMaintenanceTime: this.getFormatDate(item.lastMaintenanceTime),
                                stockInTime: this.getFormatDate(item.stockInTime),
                                qualityCondition: item.qualityCondition, // 质量状况 0:良好 1:变质
                                remark: item.remark, // 备注
                                result: item.result, // 处理结果 0:正常销售 1:停止销售
                                shortId: item.goods.shortId, // 商品编码
                                displayName: item.goods.displayName, // 商品名称
                                storage: item.goods.storage, // 存储类型
                                displaySpec: item.goods.displaySpec, // 商品规格
                                manufacturer: item.goods.manufacturer, // 生产厂商
                                goodsInfoHtml: tools.createGoodsInfoHtml(item.goods),
                                productInfo: item.goods,
                                inventoryCountWording: (() => {
                                    const goodsInfo = clone(item.goods);
                                    goodsInfo.packageCount = item.packageCount; // 整包数量
                                    goodsInfo.pieceCount = item.pieceCount; // 小包装数
                                    return tools.getInventoryCountWording(goodsInfo);
                                })(), // 账面数量
                            };
                            if (this.isMultiplex) {
                                // 复用，需要把批次列表加上去
                                const target = (this.batchListData?.rows || []).find((one) => one.goodsItem.goodsId === info.goodsId);
                                info.batchList = target?.rows || []; // 批次列表
                                const batchInfo = info.batchList.find((one) => one.batchId === info.batchId) || info.batchList[0];
                                if (batchInfo) {
                                    Object.assign(info, {
                                        batchNo: batchInfo.batchNo, // 商品生产批次号
                                        batchId: batchInfo.batchId, // 商品生产批次号
                                        isShow: true,
                                        resultType: 0,
                                        expiryDate: batchInfo.expiryDate, // 效期
                                        packageUnit: batchInfo.packageUnit || target.goodsItem.packageUnit, // 整包单位
                                        pieceUnit: batchInfo.pieceUnit || target.goodsItem.pieceUnit, // 小包装单位
                                        pieceNum: batchInfo.pieceNum, // 制剂数量
                                        productionDate: batchInfo.productDate || batchInfo.productionDate || '', // 生产日期
                                        lastMaintenanceTime: this.getFormatDate(batchInfo.lastMaintenanceTime),
                                        stockInTime: this.getFormatDate(batchInfo.stockInTime),
                                    });
                                    if (!this.isWaitHandleConserve) {
                                        Object.assign(info, {
                                            packageCount: batchInfo.packageCount, // 整包数量
                                            pieceCount: batchInfo.pieceCount, // 小包装数量
                                        });
                                    }

                                    Object.assign(info, {
                                        inventoryCountWording: (() => {
                                            return tools.getInventoryCountWording(info);
                                        })(), // 账面数量
                                    });
                                    if (medicineMaintenances.filter((it) => {
                                        return it.goods.id === item.goods.id;
                                    }).length >= 1) {
                                        // 是分组数据 加入分组数据
                                        Object.assign(info,{
                                            group: true,
                                            batchGroup: true,
                                        });
                                    }
                                }
                            }
                            return info;
                        }),
                    });
                }
                // 复用
                if (this.isMultiplex) {
                    let goodsIds = formData.medicineMaintenances.map((i) => {
                        return i.goodsId;
                    });
                    goodsIds = Array.from(new Set(goodsIds));
                    let medicineMaintenances = [];
                    goodsIds.forEach((goodsId) => {
                        const medicineMaintenancesItem = formData.medicineMaintenances.find((it) => {
                            return it.goodsId === goodsId && it.group;
                        });
                        // 有分组
                        if (medicineMaintenancesItem) {
                            const list = formData.medicineMaintenances.filter((it) => {
                                return it.goodsId === goodsId;
                            }) || [];
                            const target = (this.batchListData?.rows || []).find((one) => one.goodsItem.goodsId === goodsId);
                            list.unshift({
                                batchList: list[0].batchList, // 批次列表
                                goodsId: list[0].goodsId,
                                batchGroup: true,
                                batchId: -1,
                                isShow: true,
                                resultType: 0,
                                batchs: list.filter((it) => {
                                    return it.batchId !== -1;
                                })?.map((i) => {
                                    return {
                                        ...i,
                                        packageUnit: target.goodsItem.packageUnit,
                                        pieceUnit: target.goodsItem.pieceUnit,
                                    };
                                }),
                                shortId: list[0].shortId, // 商品编码
                                displayName: list[0].displayName, // 商品名称
                                storage: list[0].storage, // 存储类型
                                displaySpec: list[0].displaySpec, // 商品规格
                                manufacturer: list[0].manufacturer || list[0].manufacturerFull, // 生产厂商
                                goodsInfoHtml: tools.createGoodsInfoHtml(list[0]),
                                productInfo: list[0],
                                packageUnit: list[0].packageUnit, // 整包单位
                                pieceUnit: list[0].pieceUnit, // 小包装单位
                            });
                            medicineMaintenances = medicineMaintenances.concat(list);
                        } else {
                            medicineMaintenances = medicineMaintenances.concat(formData.medicineMaintenances.filter((it) => {
                                return it.goodsId === goodsId;
                            }));
                        }
                    });
                    formData.medicineMaintenances = medicineMaintenances;
                }
                return formData;
            },
            /**
             * 处理搜索结果
             * <AUTHOR>
             * @date 2024-01-30
             * @param {Array} list
             * @returns {Array}
             */
            formatSearchResultFn(list) {
                return list
                    .map((item) => ({
                        ...item,
                        disabled: item.packageCount === 0 && item.pieceCount === 0,
                    }))
                    .sort((a, b) => {
                        if (a.disabled === true && b.disabled === false) {
                            return 1;
                        }
                        return -1;
                    });
            },
            /**
             * 当选择一个商品时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} goods
             */
            async onSelectGoods(goods) {
                this.fetchParams.keyword = '';
                // 检查是否存在该商品
                const isExist = this.formData.medicineMaintenances.some((item) => item.goodsId === goods.goodsId);
                if (isExist) {
                    this.$Toast({
                        type: 'error',
                        message: `重复添加：${goods.medicineCadn || goods.name}已存在`,
                        duration: 2000,
                    });
                    return;
                }
                // 查询批次列表
                const fetchResponse = await this.fetchBatchList(goods.goodsId);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                const goodsItem = fetchResponse.data?.goods || fetchResponse.data?.goodsItem || {};
                const batchList = fetchResponse.data?.rows?.map((i) => {
                    return {
                        ...i,
                        goods: goodsItem,
                    };
                }) || [];

                // 将商品插入
                this.addGoodsInfo({
                    ...goods,
                    storage: goodsItem.storage,
                }, batchList);
                this.setBatchInfo(goods.goodsId);
                const medicineMaintenanceItem = this.formData.medicineMaintenances.find((item) => item.goodsId === goods.goodsId);
                this.changeBatchs(medicineMaintenanceItem.batchList, medicineMaintenanceItem);
            },
            /**
             * 添加goods
             * <AUTHOR>
             * @date 2024-01-29
             * @param {Object} goodsInfo
             * @param {Array} batchList
             */
            addGoodsInfo(goodsInfo, batchList, isDefaultFirst = true) {
                let batchNo = goodsInfo?.batchNo || '';
                if (!batchNo && isDefaultFirst) {
                    batchNo = batchList[0]?.batchNo || '';
                }
                this.formData.medicineMaintenances.push({
                    batchList, // 批次列表
                    appearancePackageCondition: constants.appearancePackageConditionConst.GOOD, // 外观包装状况 0:良好 1:破损
                    batchNo, // 商品生产批次号
                    expiryDate: '', // 效期
                    goodsId: goodsInfo.goodsId, // 商品ID
                    goodsName: goodsInfo.name, // 商品名称
                    measure: tools.getYhMeasureDefaultValue(), // 养护措施,
                    id: '',
                    packageCount: '', // 整包数量
                    pieceCount: '', // 小包装数量
                    pieceNum: '', // 制剂数量
                    productionDate: '', // 生产日期
                    lastMaintenanceTime: null,
                    stockInTime: null,
                    qualityCondition: constants.qualityConditionConst.GOOD, // 质量状况 0:良好 1:变质
                    remark: '', // 备注
                    result: constants.resultConst.NORMAL_SALES, // 处理结果 0:正常销售 1:停止销售
                    // 追加字段
                    shortId: goodsInfo.shortId, // 商品编码
                    displayName: goodsInfo.displayName, // 商品名称
                    storage: goodsInfo.storage, // 存储类型
                    displaySpec: goodsInfo.displaySpec, // 商品规格
                    manufacturer: goodsInfo.manufacturer || goodsInfo.manufacturerFull, // 生产厂商
                    goodsInfoHtml: tools.createGoodsInfoHtml(goodsInfo),
                    productInfo: goodsInfo,
                    inventoryCountWording: '', // 账面数量
                });
            },
            /**
             * 设置批次信息
             * <AUTHOR>
             * @date 2024-01-24
             */
            setBatchInfo(goodsId) {
                const medicineMaintenanceItem = this.formData.medicineMaintenances.find((item) => item.goodsId === goodsId);
                if (!medicineMaintenanceItem) {
                    return;
                }
                if (!medicineMaintenanceItem.batchNo) {
                    return;
                }
                const batchInfo = (medicineMaintenanceItem.batchList || []).find((item) => item.batchNo === medicineMaintenanceItem.batchNo);
                if (!batchInfo) {
                    return;
                }
                Object.assign(medicineMaintenanceItem, {
                    expiryDate: batchInfo.expiryDate, // 效期
                    packageCount: batchInfo.packageCount, // 整包数量
                    packageUnit: batchInfo.packageUnit, // 整包单位
                    pieceCount: batchInfo.pieceCount, // 小包装数量
                    pieceUnit: batchInfo.pieceUnit, // 小包装单位
                    pieceNum: batchInfo.pieceNum, // 制剂数量
                    productionDate: batchInfo.productionDate || batchInfo.productDate, // 生产日期
                    lastMaintenanceTime: this.getFormatDate(batchInfo.lastMaintenanceTime),
                    stockInTime: this.getFormatDate(batchInfo.stockInTime),
                    batchId: batchInfo.batchId,
                    isShow: true,
                    resultType: 0,
                    inventoryCountWording: tools.getInventoryCountWording(batchInfo), // 账面数量
                });
            },
            /**
             * 获取批次列表
             * <AUTHOR>
             * @date 2024-01-24
             * @param {String} goodsId
             * @returns {Promise<Response>}
             */
            async fetchBatchList(goodsId) {
                const params = {
                    clinicId: this.clinicId,
                    batchNo: '',
                    goodsId,
                    supplierId: '',
                    offset: 0,
                    limit: 299,
                    onlyStock: 1,
                    pharmacyNo: '',
                    batchViewMode: '',
                    expiredWarn: '',
                    costPriceWarn: '',
                };
                const fetchResponse = await GspAPI.fetchBatchListByGoodsId(goodsId,params);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                return fetchResponse;
            },
            onClickDeleteTr(index) {
                const {
                    goodsId, batchId,
                } = this.formData.medicineMaintenances[index];
                // -1 代表全部批次 删除全部批次
                if (batchId === -1) {
                    this.formData.medicineMaintenances = this.formData.medicineMaintenances.filter((item) => {
                        return item.goodsId !== goodsId;
                    });
                    return;
                }
                // 非全部批次 找到这个类型的商品的第一个元素
                const medicineMaintenancesItem = this.formData.medicineMaintenances.find((item) => {
                    return item.goodsId === goodsId;
                });
                // 如果第一条是分组数据
                if (medicineMaintenancesItem.batchId === -1) {
                    // 如果同类型数据只有两条
                    if (this.formData.medicineMaintenances.filter((item) => {
                        return item.goodsId === goodsId;
                    }).length === 2) {
                        // 删除分组数据
                        this.formData.medicineMaintenances = this.formData.medicineMaintenances.filter((item) => {
                            return !(item.goodsId === goodsId && item.batchId === -1);
                        });
                        // 恢复数据正常 不做删除
                        const currentItem = this.formData.medicineMaintenances.find((item) => {
                            return item.goodsId === goodsId;
                        });
                        delete currentItem.group;
                        delete currentItem.batchGroup;
                        // 代表有更多数据
                    } else {
                        // 直接删除
                        this.formData.medicineMaintenances = this.formData.medicineMaintenances.filter((item) => {
                            return !(item.goodsId === goodsId && item.batchId === batchId);
                        });
                        // 将剩余数据传给第一个对象
                        const medicineMaintenancesBatchNos = this.formData.medicineMaintenances.filter((item) => {
                            return item.goodsId === goodsId && item.batchId !== -1;
                        }).map((i) => {
                            return i.batchId;
                        });
                        medicineMaintenancesItem.batchs = medicineMaintenancesItem.batchList.filter((i) => {
                            return medicineMaintenancesBatchNos.includes(i.batchId);
                        });
                    }
                } else {
                    // 不是分组应该直接删除
                    this.formData.medicineMaintenances = this.formData.medicineMaintenances.filter((item) => {
                        return !(item.goodsId === goodsId && item.batchId === batchId);
                    });
                }
            },
            filterFn(dataList) {
                return dataList.filter((item) => {
                    // 器械养护只展示器械
                    if (this.isMedicalDeviceConserve) {
                        return ['医疗器械'].includes(item.label);
                    }
                    // 药品养护刚好过滤器械
                    return ['中药','配方饮片','非配方饮片','消毒用品','中成药','西成药'].includes(item.label);
                });
            },
            /**
             * 当点击批量添加药品时
             * <AUTHOR>
             * @date 2024-01-29
             */
            async onClickBatchAddition() {
                const openResponse = await tools.openDialog({
                    propsData: {
                        visible: true,
                        defaultCheckedKeys: (this.formData?.medicineMaintenances || []).map((item) => item.goodsId),
                        searchParams: this.isMedicalDeviceConserve ? {
                            typeId: [GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL],
                        } : {
                            typeId: [
                                GoodsTypeIdEnum.MEDICINE_WESTERN,
                                GoodsTypeIdEnum.CHINESE_MEDICINE,
                                GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                                GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES,
                                GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE,
                                GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT,
                                GoodsTypeIdEnum.MATERIAL_DISINFECTANT,
                            ],
                        },
                        filterFn: this.filterFn, // 只显示中药
                    },
                    component: MedicineSelectionDialog,
                });
                if (openResponse.status === false) {
                    return openResponse;
                }
                this._loading = this.$Loading({
                    text: '加载数据中~',
                });
                this._timer = setTimeout(() => {
                    if (this._loading) {
                        this._loading.close();
                        this._loading = null;
                    }
                }, 5000);
                // 批量查询批次信息
                const params = {
                    goodsIdList: (openResponse.data || []).map((item) => item.id),
                };
                params.goodsIdList = Array.from(new Set(params.goodsIdList));
                const fetchResponse = await GspAPI.fetchHandleBatchListByGoodsIds(params);
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                const batchListData = fetchResponse.data?.rows || [];
                // 循环插入视图
                (openResponse.data || []).forEach((goodsInfo) => {
                    const target = batchListData.find((one) => one.goodsItem.goodsId === goodsInfo.id);
                    const batchList = target?.rows?.map((i) => {
                        return {
                            ...i,
                            goodsId: goodsInfo.id,
                            packageUnit: target.goodsItem.packageUnit,
                            pieceUnit: target.goodsItem.pieceUnit,
                        };
                    }) || []; // 批次列表
                    goodsInfo.goodsId = goodsInfo.id;
                    this.addGoodsInfo({
                        ...goodsInfo,
                        storage: target.goodsItem?.storage,
                    }, batchList);
                    this.setBatchInfo(goodsInfo.id);
                    const medicineMaintenanceItem = this.formData.medicineMaintenances.find((item) => item.goodsId === goodsInfo.id);
                    this.changeBatchs(medicineMaintenanceItem.batchList, medicineMaintenanceItem);
                });
                if (this._loading) {
                    this._loading.close();
                    this._loading = null;
                }
            },
            /**
             * 创建提交数据
             * <AUTHOR>
             * @date 2024-01-03
             * @returns {Object}
             */
            createPostData() {
                const formData = clone(this.formData);
                formData.maintenanceTime += ':00';
                formData.medicineMaintenances?.forEach((item, index) => {
                    item.sort = index;
                    item.measure = tools.measureStringify(item.measure);
                });
                const postData = {
                    maintainer: '', // 养护人id
                    maintenanceTime: '', // 养护时间
                    maintenanceType: '', // 养护类型
                    remark: '', // 备注
                    // 药品养护记录列表
                    medicineMaintenances: [
                        {
                            appearancePackageCondition: '', // 外观包装状况 0:良好 1:破损
                            batchNo: '', // 商品生产批次号
                            batchId: '',
                            expiryDate: '', // 效期
                            goodsId: '', // 商品ID
                            goodsName: '', // 商品名称
                            measure: '', // 养护措施
                            packageCount: '', // 整包数量
                            pieceCount: '', // 小包装数量
                            pieceNum: '', // 制剂数量
                            productionDate: '', // 生产日期
                            qualityCondition: '', // 质量状况 0:良好 1:变质
                            remark: '', // 备注
                            result: '', // 处理结果 0:正常销售 1:停止销售
                            sort: '', // 排序
                            lastMaintenanceTime: null,
                            stockInTime: null,
                        },
                    ],
                    category: this.category,
                };
                return pick(postData, formData);
            },
            createPutData() {
                const formData = clone(this.formData);
                formData.maintenanceTime += ':00';
                formData.medicineMaintenances?.forEach((item, index) => {
                    item.sort = index;
                    item.measure = tools.measureStringify(item.measure);
                });
                const postData = {
                    maintainer: '', // 养护人id
                    maintenanceTime: '', // 养护时间
                    maintenanceType: '', // 养护类型
                    remark: '', // 备注
                    // 药品养护记录列表
                    medicineMaintenances: [
                        {
                            appearancePackageCondition: '', // 外观包装状况 0:良好 1:破损
                            batchNo: '', // 商品生产批次号
                            batchId: '',
                            expiryDate: '', // 效期
                            goodsId: '', // 商品ID
                            goodsName: '', // 商品名称
                            measure: '', // 养护措施
                            packageCount: '', // 整包数量
                            pieceCount: '', // 小包装数量
                            pieceNum: '', // 制剂数量
                            productionDate: '', // 生产日期
                            qualityCondition: '', // 质量状况 0:良好 1:变质
                            remark: '', // 备注
                            result: '', // 处理结果 0:正常销售 1:停止销售
                            sort: '', // 排序
                            id: '',
                            lastMaintenanceTime: null,
                            stockInTime: null,
                        },
                    ],
                    category: this.category,
                };
                return pick(postData, formData);
            },
            /**
             * 当点击提交时
             * <AUTHOR>
             * @date 2023-12-25
             */
            async onClickSubmit() {
                this.formData.medicineMaintenances = this.formData.medicineMaintenances.map((item) => {
                    return {
                        ...item,
                        isShow: true,
                        resultType: 0,
                    };
                });
                if (this.loadingSubmit) {
                    return;
                }
                if ((this.formData?.medicineMaintenances || []).length === 0) {
                    return this.$Toast({
                        type: 'error',
                        message: '请至少添加一个养护商品',
                    });
                }
                this.$refs.formData.validate(async (valid) => {
                    if (!valid) {
                        return;
                    }
                    this.loadingSubmit = true;
                    let postData = null;
                    if (this.isWaitHandleConserve && this.isMultiplex) {
                        postData = this.createPutData();
                    } else {
                        postData = this.createPostData();
                    }
                    postData.medicineMaintenances = postData.medicineMaintenances.filter((item) => {
                        // 非分组或者是分组但不是假数据
                        return item.batchId !== -1;
                    }).map((item) => {
                        return {
                            ...item,
                            lastMaintenanceTime: item.lastMaintenanceTime ? formatDate(item.lastMaintenanceTime, 'YYYY-MM-DD HH:mm:ss') : null,
                            stockInTime: item.stockInTime ? formatDate(item.stockInTime, 'YYYY-MM-DD HH:mm:ss') : null,
                        };
                    });
                    let createResponse = null;
                    if (this.isWaitHandleConserve && this.isMultiplex) {
                        createResponse = await GspAPI.updateMedicineMaintenanceOrder(this.originData.id, postData);
                    } else {
                        createResponse = await GspAPI.createMedicineMaintenanceOrder(postData);
                    }
                    this.loadingSubmit = false;
                    if (createResponse.status === false) {
                        return createResponse;
                    }
                    this.$Toast({
                        type: 'success',
                        message: '操作成功',
                    });
                    this.$emit('confirm');
                });
            },
            /**
             * 当点击取消时
             * <AUTHOR>
             * @date 2023-12-25
             */
            onClickCancel() {
                this.visible = false;
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .pharmacy__first-battalion__medicines__dialog {
        &--not-allow {
            width: 100%;
            height: 44px;
            padding: 0 10px !important;
            line-height: 44px;
            background-color: transparent;
        }

        .base-info {
            width: 1152px;
        }

        .conserve-detail-filter-select {
            .abc-icon {
                font-size: 12px !important;
            }
        }

        .abc-table-normal-wrapper {
            .abc-table-header {
                background: #f6f8fa;
            }

            .abc-table-body {
                .goods-info-wrapper {
                    box-sizing: border-box;
                    width: 100%;
                    padding: 4px 12px;

                    > .name {
                        font-size: 14px;
                        font-weight: 500;
                        line-height: 20px;
                        color: $T1;

                        @include ellipsis;
                    }

                    > .spec {
                        font-size: 12px;
                        line-height: 16px;
                        color: $T2;

                        @include ellipsis;
                    }
                }

                .show-text-box {
                    display: flex;
                    align-items: center;
                    height: 44px;
                    padding: 0 10px;
                    background: #f9fafc;
                }
            }

            .abc-table-footer {
                .prepend-input {
                    left: 6px;
                }

                .abc-input__inner {
                    padding-left: 36px !important;
                }

                .append-input {
                    width: 88px;
                }
            }
        }

        .abc-table-normal-wrapper.abc-table--excel .abc-table-body .abc-table-td .abc-form-item input,
        .abc-table-normal-wrapper.abc-table--excel .abc-table-body .abc-table-td .abc-input__inner {
            height: 44px;
        }
    }
</style>
