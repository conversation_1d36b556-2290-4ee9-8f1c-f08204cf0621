<template>
    <abc-layout preset="page-table" class="abc-pharmacy-gsp-check-accept-qualified-container">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-date-picker
                        v-model="toolsParams.dateRange"
                        :picker-options="pickerOptions"
                        type="daterange"
                        clearable
                    ></abc-date-picker>
                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="toolsParams.clinicId"
                        :show-all-clinic="false"
                        placeholder="总部/门店"
                        :clearable="true"
                        :width="160"
                    ></clinic-select>
                    <abc-select
                        v-model="toolsParams.supplierId"
                        :width="160"
                        :fetch-suggestions="(searchKey) => searchKeySupplier = searchKey"
                        with-search
                        clearable
                        placeholder="供应商"
                    >
                        <abc-option
                            v-for="item in supplierOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                    <goods-auto-complete
                        placeholder="商品名称"
                        :search.sync="fetchParams.keyword"
                        :focus-show="true"
                        :type-arr="fetchParams.type"
                        :sub-type-arr="fetchParams.subType"
                        :c-m-spec="fetchParams.cMSpec"
                        :width="260"
                        enable-local-search
                        is-type-arr
                        format-count-key="stock"
                        :inorder-config="0"
                        :clinic-id="clinicId"
                        @selectGoods="onSelectGoods"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                        <div slot="append" class="search-icon" @click="onClickClearKeyword">
                            <i v-if="!!fetchParams.keyword" class="iconfont cis-icon-cross_small"></i>
                        </div>
                    </goods-auto-complete>
                </abc-space>
                <abc-button
                    icon="n-upload-line"
                    variant="ghost"
                    :disabled="dataList.length === 0"
                    @click="exportExcelCommon(GSPExportEnum.CHECK_ACCEPT.QUALIFIED,
                                              createParams(),
                                              exportTitle)"
                >
                    导出
                </abc-button>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="abcTable"
                type="pro"
                :render-config="renderConfig"
                :custom-tr-key="customTrKey"
                :loading="loading"
                :data-list="dataList"
            ></abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="count"
                @current-change="onChangePage"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import SupplierApi from 'api/goods/supplier.js';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';

    const GoodsAutoComplete = () => import('src/views/inventory/common/goods-auto-complete');

    import pickerOptions from 'views/common/pickerOptions';
    import mixinTable from '@/views-pharmacy/common/mixin-table';

    import { mapGetters } from 'vuex';
    import { isEqual } from '@abc/utils';

    import * as tools from '@/views-pharmacy/common/tools';
    import { PurchaseTableConfig } from './table-config';
    import { formatMoney } from 'utils';
    import {
        exportExcelCommon, GSPExportEnum,
    } from '@/views-pharmacy/gsp/utils/gspExportUtil';
    export default {
        components: {
            GoodsAutoComplete,
            ClinicSelect,
        },
        mixins: [
            pickerOptions,
            mixinTable,
        ],
        data() {
            return {
                GSPExportEnum,
                tools,
                fetchParams: {
                    keyword: '',
                    typeId: '',
                    typeLabel: '全部类型',
                    type: [1, 2, 7],
                    subType: [],
                    cMSpec: '',
                },
                searchKeySupplier: '', // 搜索关键词 - 供应商
                toolsParams: {
                    dateRange: this.createDateRangeCurMonth(), // 日期范围
                    clinicId: '', // 门店ID
                    goodsId: '', // 商品ID
                    supplierId: '', // 供应商ID
                },
                tableHeader: [],
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                loading: false,
                originData: null,
                originDataSupplier: null,
                showOrderSelectDialog: false,
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            name() {
                return this.$route?.name || '';
            },
            isMedicalDevice() {
                return this.name === '@PharmacyGspCheckAcceptMedicalDeviceQualified';
            },
            exportTitle() {
                if (this.isMedicalDevice) {
                    return '医疗器械验收记录';
                }
                return '药品验收记录';
            },
            showGspReceiveOrderNo() {
                return this.viewDistributeConfig.Gsp.check.showGspReceiveOrderNo;
            },
            renderConfig() {
                const tableConfig = new PurchaseTableConfig(this.currentClinic);
                return tableConfig.extendConfig({
                    inspectTime: {
                        dataFormatter: (inspectTime) => {
                            if (!inspectTime) return '';
                            return tools.getDateFormat(inspectTime);
                        },
                    },
                    clinicName: {
                        dataFormatter: (_,row) => (row.clinicId === this.clinicId ? '总部' : row.clinicName),
                    },
                    displayName: {
                        dataFormatter: (_,row) => row.goods?.displayName,
                    },
                    displaySpec: {
                        dataFormatter: (_,row) => row.goods?.displaySpec,
                    },
                    manufacturerFull: {
                        dataFormatter: (_,row) => row.goods?.manufacturerFull,
                    },
                    dosageFormTypeName: {
                        dataFormatter: (_,row) => {
                            const dosageFormTypeName = row.goods?.dosageFormTypeName;
                            return dosageFormTypeName === '未知' ? '' : dosageFormTypeName;
                        },
                        hidden: this.isMedicalDevice,
                    },
                    receivePackageCount: {
                        dataFormatter: (_,row) => {
                            const goods = {
                                ...row.goods,
                                packageCount: row.receivePackageCount,
                                pieceCount: row.receivePieceCount,
                            };
                            return tools.getInventoryCountWording(goods);
                        },
                    },
                    inspectPackageCount: {
                        dataFormatter: (_,row) => {
                            const goods = {
                                ...row.goods,
                                packageCount: row.receivePackageCount,
                                pieceCount: row.receivePieceCount,
                            };
                            return tools.getInventoryCountWording(goods);
                        },
                    },
                    qualifiedPackageCount: {
                        dataFormatter: (_,row) => {
                            const goods = {
                                ...row.goods,
                                packageCount: row.inspectPackageCount,
                                pieceCount: row.inspectPieceCount,
                            };
                            return tools.getInventoryCountWording(goods);
                        },
                    },
                    receiveTime: {
                        dataFormatter: (receiveTime) => {
                            if (!receiveTime) {
                                return '';
                            }
                            return tools.getDateFormat(receiveTime);
                        },
                    },
                    medicineNmpn: {
                        dataFormatter: (_,row) => row.goods?.medicineNmpn,
                    },
                    mha: {
                        dataFormatter: (_,row) => row.goods?.mha,
                    },
                    shortId: {
                        dataFormatter: (_,row) => row.goods?.shortId,
                    },
                    receiveOrderNo: {
                        hidden: !this.showGspReceiveOrderNo,
                    },
                    packagePrice: {
                        dataFormatter: (_,row) => formatMoney(row.packagePrice, false),
                    },
                });
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 供应商选项
            supplierOptions() {
                let supplierList = this.originDataSupplier?.rows || [];
                if (this.searchKeySupplier) {
                    // 有关键词时，过滤一下
                    const searchKeyStr = this.searchKeySupplier.toLocaleLowerCase();
                    supplierList = supplierList.filter((item) => {
                        return (item.name || '').toLocaleLowerCase().indexOf(searchKeyStr) !== -1 || (item.namePy || '').toLocaleLowerCase().indexOf(searchKeyStr) !== -1 || (item.namePyFirst || '').toLocaleLowerCase().indexOf(searchKeyStr) !== -1 ;
                    });
                }
                return supplierList.map((item) => ({
                    value: item.id,
                    label: item.name,
                }));
            },
            // 表格数据
            dataList() {
                return this.originData?.rows || [];
            },
            // 总条数
            count() {
                return this.originData?.total || 0;
            },
        },
        watch: {
            toolsParams: {
                handler() {
                    this.initPageIndex();
                    this.fetchDataList();
                },
                deep: true,
            },
            name: {
                handler() {
                    this.toolsParams = {
                        dateRange: this.createDateRangeCurMonth(), // 日期范围
                        clinicId: '', // 门店ID
                        goodsId: '', // 商品ID
                        supplierId: '', // 供应商ID
                    };
                    this.initPageIndex();
                    this.fetchDataList();
                    this.fetchInventorySupplierList();
                },
                deep: true,
            },
        },
        async mounted() {
            this.setPageSizeWithTableHeight();
            await this.fetchInventorySupplierList();
        },
        methods: {
            exportExcelCommon,
            customTrKey(item) {
                return `${item.id}_${item.orderNo}`;
            },
            handleMounted(data) {
                this.pageParams.pageSize = data.paginationLimit;
                this.fetchDataList();
            },
            /**
             * 拉取供应商数据列表
             * <AUTHOR>
             * @date 2023-12-27
             */
            async fetchInventorySupplierList() {
                try {
                    const params = {
                        offset: 0, // 分页
                        limit: 999, // 每页条数，拉取全部供应商数据
                    };
                    const res = await SupplierApi.fetchInventorySupplierList(params);
                    this.originDataSupplier = res?.data;
                } catch (error) {
                    console.log('fetchDataList error', error);
                }
            },
            /**
             * 创建表头配置
             * <AUTHOR>
             * @date 2023-12-25
             * @returns {Array}
             */
            createTableHeader() {
                let tableHeader = [
                    {
                        label: '',
                        prop: 'zw-1',
                        width: 8,
                    },
                    {
                        label: '验收日期',
                        width: 110,
                        prop: 'inspectTime',
                        formatter: (row) => {
                            if (!row.inspectTime) {
                                return '';
                            }
                            return tools.getDateFormat(row.inspectTime);
                        },
                    },
                    {
                        label: '门店名称',
                        width: 260,
                        prop: 'clinicName',
                        formatter: (row) => (row.clinicId === this.clinicId ? '总部' : row.clinicName),
                        show: this.isChainAdmin,
                    },
                    {
                        label: '商品名称',
                        width: 240,
                        prop: 'displayName',
                        formatter: (row) => row.goods?.displayName,
                    },
                    {
                        label: '规格',
                        prop: 'displaySpec',
                        width: 200,
                        formatter: (row) => row.goods?.displaySpec,
                    },
                    {
                        label: '厂家/产地',
                        prop: 'manufacturerFull',
                        width: 260,
                        formatter: (row) => row.goods?.manufacturerFull,
                    },
                    {
                        label: '剂型',
                        prop: 'dosageFormTypeName',
                        width: 160,
                        formatter: (row) => {
                            const dosageFormTypeName = row.goods?.dosageFormTypeName;
                            return dosageFormTypeName === '未知' ? '' : dosageFormTypeName;
                        },
                    },
                    {
                        label: '生产批号',
                        prop: 'batchNo',
                        width: 160,
                    },
                    {
                        label: '到货数量',
                        prop: 'receivePackageCount',
                        width: 160,
                        formatter: (row) => {
                            const goods = {
                                ...row.goods,
                                packageCount: row.receivePackageCount,
                                pieceCount: row.receivePieceCount,
                            };
                            return tools.getInventoryCountWording(goods);
                        },
                    },
                    {
                        label: '验收数量',
                        prop: 'inspectPackageCount',
                        width: 160,
                        formatter: (row) => {
                            const goods = {
                                ...row.goods,
                                packageCount: row.receivePackageCount,
                                pieceCount: row.receivePieceCount,
                            };
                            return tools.getInventoryCountWording(goods);
                        },
                    },
                    {
                        label: '合格数量',
                        prop: 'qualifiedPackageCount',
                        width: 160,
                        formatter: (row) => {
                            const goods = {
                                ...row.goods,
                                packageCount: row.inspectPackageCount,
                                pieceCount: row.inspectPieceCount,
                            };
                            return tools.getInventoryCountWording(goods);
                        },
                    },
                    {
                        label: '验收结果',
                        prop: 'inspectStatus',
                        width: 200,
                    },
                    {
                        label: '供应商',
                        prop: 'supplierName',
                        width: 260,
                    },
                    {
                        label: '进价',
                        prop: 'packagePrice',
                        width: 160,
                    },
                    {
                        label: '验收人',
                        prop: 'inspector',
                        width: 160,
                    },
                    {
                        label: '生产日期',
                        prop: 'productionDate',
                        width: 110,
                    },
                    {
                        label: '有效日期',
                        prop: 'expiryDate',
                        width: 110,
                    },
                    {
                        label: '到货日期',
                        prop: 'receiveTime',
                        width: 110,
                        formatter: (row) => {
                            if (!row.receiveTime) {
                                return '';
                            }
                            return tools.getDateFormat(row.receiveTime);
                        },
                    },
                    {
                        label: '批准文号',
                        prop: 'medicineNmpn',
                        width: 160,
                        formatter: (row) => row.goods?.medicineNmpn,
                    },
                    {
                        label: '上市许可持有人/器械注册人',
                        prop: 'mha',
                        width: 260,
                        formatter: (row) => row.goods?.mha,
                    },
                    {
                        label: '商品编码',
                        width: 160,
                        prop: 'shortId',
                        formatter: (row) => row.goods?.shortId,
                    },
                    {
                        label: '验收单号',
                        prop: 'orderNo',
                        width: 160,
                    },
                    {
                        label: '收货单号',
                        prop: 'receiveOrderNo',
                        width: 160,
                    },
                    {
                        label: '',
                        prop: 'zw-2',
                        width: 54,
                    },
                ];
                tableHeader = tableHeader.filter((item) => item.show !== false);
                return tools.handleTableHeaderBorder(tableHeader);
            },
            /**
             * 当选择一个商品时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} goods
             */
            async onSelectGoods(goods) {
                this.toolsParams.goodsId = goods.goodsId;
                this.fetchParams.keyword = goods.displayName;
            },
            /**
             * 当点击情况关键词
             * <AUTHOR>
             * @date 2024-01-08
             */
            onClickClearKeyword() {
                this.toolsParams.goodsId = '';
                this.fetchParams.keyword = '';
            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2023-12-27
             * @returns {Object}
             */
            createParams() {
                const {
                    dateRange, // 日期范围
                    clinicId, // 门店ID
                    goodsId, // 商品ID
                    supplierId, // 供应商ID
                } = this.toolsParams;
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;
                const params = {
                    clinicId, // 门店ID
                    dateStart: dateRange[0], // 开始日期
                    dateEnd: dateRange[1], // 截止日期
                    goodsId, // 商品ID
                    supplierId, // 供应商ID
                    offset: pageIndex * pageSize, // 分页
                    limit: pageSize, // 每页条数
                    category: this.isMedicalDevice ? 10 : 0,
                };
                return params;
            },
            /**
             * 拉取数据列表
             * <AUTHOR>
             * @date 2023-12-27
             */
            async fetchDataList() {
                this.loading = true;
                const params = this.createParams();
                const fetchResponse = await GspAPI.fetchInspectQualifiedItems(params);
                if (!isEqual(params, this.createParams())) {
                    return fetchResponse;
                }
                this.loading = false;
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                this.originData = fetchResponse.data;
            },
            /**
             * 当切换页码时触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Number} pageIndex
             */
            onChangePage(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchDataList();
            },
            /**
             * 初始化页码
             * <AUTHOR>
             * @date 2023-12-29
             */
            initPageIndex() {
                this.pageParams.pageIndex = 0;
            },
        },
    };
</script>
