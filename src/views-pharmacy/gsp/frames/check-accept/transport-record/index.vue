<template>
    <abc-layout preset="page-table" class="abc-pharmacy-gsp-check-accept-transport-record-container">
        <abc-layout-header>
            <abc-space>
                <abc-date-picker
                    v-model="toolsParams.dateRange"
                    :picker-options="pickerOptions"
                    type="daterange"
                    clearable
                    @change="changeTime"
                ></abc-date-picker>
                <clinic-select
                    v-if="isChainAdmin"
                    v-model="fetchParams.queryClinicId"
                    :show-all-clinic="false"
                    placeholder="总部/门店"
                    :clearable="true"
                    :width="160"
                    @change="initFetch"
                ></clinic-select>
                <abc-input
                    v-model="fetchParams.receiveOrderNo"
                    :width="240"
                    placeholder="收货单号"
                    clearable
                    @input="_initFetch"
                >
                    <abc-search-icon slot="prepend"></abc-search-icon>
                </abc-input>
            </abc-space>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="abcTable"
                type="pro"
                :loading="loading"
                :render-config="renderConfig"
                :data-list="dataList"
                :tr-clickable="true"
                @handleClickTr="handleClickTr"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="count"
                @current-change="onChangePage"
            ></abc-pagination>
        </abc-layout-footer>
        <order-dialog
            v-if="showDialog"
            v-model="showDialog"
            :can-edit="canEdit"
            :transport-record-id="transportRecordId"
            @refresh="refresh"
        ></order-dialog>
    </abc-layout>
</template>
<script>
    import * as tools from '@/views-pharmacy/common/tools';
    import pickerOptions from 'views/common/pickerOptions';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    import OrderDialog from '@/views-pharmacy/gsp/frames/check-accept/transport-record/order-dialog';
    import { mapGetters } from 'vuex';
    import GspAPI from '@/api/pharmacy/gsp';
    import { debounce } from 'utils/lodash';
    import {
        formatDate,
    } from '@abc/utils-date';
    import { PurchaseTableConfig } from './table-config';
    export default {
        name: 'Index',
        components: {
            ClinicSelect,
            OrderDialog,
        },
        mixins: [
            pickerOptions,
        ],
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
            ]),
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            renderConfig() {
                const tableConfig = new PurchaseTableConfig(this.currentClinic);
                return tableConfig.extendConfig({
                    receiveDate: {
                        dataFormatter: (receiveDate) => {
                            if (!receiveDate) {
                                return '';
                            }
                            return formatDate(receiveDate, 'YYYY-MM-DD');
                        },
                    },
                    departureTime: {
                        dataFormatter: (departureTime) => {
                            if (!departureTime) {
                                return '';
                            }
                            return formatDate(departureTime, 'YYYY-MM-DD HH:mm');
                        },
                    },
                    departureTemperature: {
                        dataFormatter: (departureTemperature) => {
                            if (!departureTemperature) {
                                return '';
                            }
                            return `${departureTemperature}℃`;
                        },
                    },
                    arrivalTime: {
                        dataFormatter: (arrivalTime) => {
                            if (!arrivalTime) {
                                return '';
                            }
                            return formatDate(arrivalTime, 'YYYY-MM-DD HH:mm');
                        },
                    },
                    arrivalTemperature: {
                        dataFormatter: (arrivalTemperature) => {
                            if (!arrivalTemperature) {
                                return '';
                            }
                            return `${arrivalTemperature}℃`;
                        },
                    },
                    operator: {
                        customRender: (h, row) => {
                            return (<abc-table-cell class="handles-wrapper">
                                <abc-button
                                    type="text"
                                    disabled={row.clinicId !== this.clinicId}
                                    onClick={(event) => {
                                        event.stopPropagation();
                                        this.modifyClickTr(row);
                                    }}>
                                    修改
                                </abc-button>
                            </abc-table-cell>);
                        },
                    },
                });
            },
        },
        data() {
            return {
                tools,
                showDialog: false,
                fetchParams: {
                    receiveOrderNo: '',
                    receiveDateStart: '',
                    receiveDateEnd: '',
                    offset: 0,
                    limit: 10,
                    queryClinicId: '',
                },
                transportRecordId: '',
                canEdit: false,
                toolsParams: {
                    dateRange: this.createDateRangeCurMonth(), // 日期范围
                },
                tableHeader: [],
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                loading: false,
                dataList: [],
                count: 0,
            };
        },
        created() {
            this.fetchParams.receiveDateStart = this.toolsParams.dateRange?.[0] || '';
            this.fetchParams.receiveDateEnd = this.toolsParams.dateRange?.[1] || '';
            this._initFetch = debounce(this.initFetch, 200, true);
        },
        methods: {
            onChangePage(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchTransportRecord();
            },
            handleMounted(data) {
                this.pageParams.pageSize = data.paginationLimit;
                this.fetchTransportRecord();
            },
            async changeTime() {
                this.fetchParams.receiveDateStart = this.toolsParams.dateRange?.[0] || '';
                this.fetchParams.receiveDateEnd = this.toolsParams.dateRange?.[1] || '';
                await this.initFetch();
            },
            async initFetch() {
                this.pageParams.pageIndex = 0;
                await this.fetchTransportRecord();
            },
            refresh() {
                this.fetchTransportRecord();
            },
            createParams() {
                const {
                    receiveOrderNo,
                    receiveDateStart,
                    receiveDateEnd,
                    queryClinicId,
                } = this.fetchParams;
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;
                const params = {
                    receiveOrderNo,
                    receiveDateStart,
                    receiveDateEnd,
                    queryClinicId,
                    offset: pageIndex * pageSize, // 分页
                    limit: pageSize, // 每页条数
                };
                return params;
            },
            async fetchTransportRecord() {
                this.loading = true;
                const params = this.createParams();
                try {
                    const { data } = await GspAPI.fetchTransportList(params);
                    this.dataList = data?.rows || [];
                    this.count = data.total;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            modifyClickTr(item) {
                this.canEdit = true;
                this.transportRecordId = item.id;
                this.showDialog = true;
            },
            handleClickTr(item) {
                console.log('click-tr', item);
                this.canEdit = false;
                this.transportRecordId = item.id;
                this.showDialog = true;
            },
        },
    };
</script>
