<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        class="transport-record_dialog--box"
        :title="title"
        :append-to-body="true"
        :auto-focus="false"
        content-styles="width: 1200px;min-width:1200px;"
    >
        <abc-layout v-abc-loading="loading">
            <abc-section>
                <abc-form
                    ref="form"
                    :label-width="100"
                    is-excel
                    item-no-margin
                >
                    <abc-descriptions
                        :column="4"
                        :label-width="96"
                        grid
                        background
                        size="large"
                    >
                        <abc-descriptions-item label="收货机构">
                            <div
                                v-abc-title.ellipsis="descriptions.clinicName"
                            ></div>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="关联收货单">
                            <div
                                v-abc-title.ellipsis="descriptions.orderNo"
                                style="color: #005ed9; cursor: pointer;"
                            ></div>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="随货单号">
                            <div v-abc-title.ellipsis="descriptions.outOrderNo"></div>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="收货件数">
                            <div v-abc-title.ellipsis="descriptions.kindCount"></div>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="收货日期" :content-style="{ padding: '0px' }">
                            <abc-form-item :required="requiredReceiveDate">
                                <abc-date-picker
                                    v-model="descriptions.receiveDate"
                                    placeholder="收货日期"
                                    :disabled="disabledReceiveDate"
                                    :describe-list="describeList"
                                    :picker-options="pickerOptions"
                                    clearable
                                    size="medium"
                                >
                                </abc-date-picker>
                            </abc-form-item>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="记录人" :content-style="{ padding: '0px' }">
                            <abc-form-item required>
                                <abc-select
                                    v-model="transportRecordInfo.recorder"
                                    :fetch-suggestions="(searchKey) => searchKeyRecorder = searchKey"
                                    with-search
                                    :no-icon="disabled"
                                    :disabled="disabled"
                                    size="medium"
                                >
                                    <abc-option
                                        v-for="item in recorderOptions"
                                        :key="item.value"
                                        :value="item.value"
                                        :label="item.label"
                                    ></abc-option>
                                </abc-select>
                            </abc-form-item>
                        </abc-descriptions-item>
                        <abc-descriptions-item label="备注" :span="2" :content-style="{ padding: '0px' }">
                            <abc-form-item>
                                <abc-input
                                    v-model="transportRecordInfo.remark"
                                    :disabled="disabled"
                                    :max-length="300"
                                    size="large"
                                ></abc-input>
                            </abc-form-item>
                        </abc-descriptions-item>
                    </abc-descriptions>
                </abc-form>
            </abc-section>
            <abc-form ref="table" :item-no-margin="true">
                <abc-section style="margin-top: 24px;">
                    <abc-title level="1" :bold="false">
                        出发
                    </abc-title>
                </abc-section>
                <abc-section>
                    <abc-space :size="16" :wrap="true">
                        <abc-form-item label="运输工具">
                            <abc-input
                                v-model="form.transportMode"
                                :disabled="disabled"
                                :max-length="30"
                                :width="276"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="温控方式">
                            <abc-input
                                v-model="form.temperatureControlMode"
                                :disabled="disabled"
                                :max-length="30"
                                :width="276"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="运输车牌号">
                            <abc-input
                                v-model="form.transportNo"
                                :disabled="disabled"
                                :max-length="30"
                                :width="276"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="出发时间">
                            <abc-space is-compact compact-block>
                                <abc-date-time-picker
                                    v-model="form.departureTime"
                                    :disabled="disabled"
                                    :date-width="152"
                                    :time-width="124"
                                    :time-step="1"
                                    :time-row="20"
                                    :picker-options="pickerOptions"
                                >
                                </abc-date-time-picker>
                            </abc-space>
                        </abc-form-item>
                        <abc-form-item label="出发温度" required>
                            <abc-input
                                v-model="form.departureTemperature"
                                type="number"
                                :disabled="disabled"
                                :config="{
                                    max: 999.99,
                                    min: -999.99,
                                    supportZero: true,
                                    supportNegative: true,
                                    formatLength: 2,
                                }"
                                :width="276"
                            >
                                <abc-icon slot="appendInner" color="#AAB4BF" icon="n-degrees-celsius-line"></abc-icon>
                            </abc-input>
                        </abc-form-item>
                        <abc-form-item label="驾驶员">
                            <abc-input
                                v-model="form.driverName"
                                :disabled="disabled"
                                :max-length="30"
                                :width="276"
                            ></abc-input>
                        </abc-form-item>
                        <abc-form-item label="发货地址">
                            <abc-space is-compact compact-block>
                                <abc-address-selector
                                    v-model="form.address"
                                    :disabled="disabled"
                                    :width="188"
                                    clearable
                                ></abc-address-selector>
                                <abc-input
                                    v-model="form.departureAddressDetail"
                                    :disabled="disabled"
                                    :max-length="50"
                                    :width="380"
                                ></abc-input>
                            </abc-space>
                        </abc-form-item>
                        <abc-form-item label="承运单位">
                            <abc-input
                                v-model="form.carrierName"
                                :disabled="disabled"
                                :max-length="30"
                                :width="276"
                            ></abc-input>
                        </abc-form-item>
                    </abc-space>
                </abc-section>
                <abc-section style="margin-top: 24px;">
                    <abc-title level="1" :bold="false">
                        到达
                    </abc-title>
                </abc-section>
                <abc-section>
                    <abc-space :size="16" :wrap="true">
                        <abc-form-item label="到达时间">
                            <abc-space is-compact compact-block>
                                <abc-date-time-picker
                                    v-model="form.arrivalTime"
                                    :disabled="disabled"
                                    :date-width="152"
                                    :time-width="124"
                                    :time-step="1"
                                    :time-row="20"
                                    :picker-options="pickerOptions"
                                >
                                </abc-date-time-picker>
                            </abc-space>
                        </abc-form-item>
                        <abc-form-item label="到达温度" :required="requiredArrivalTemperature">
                            <abc-input
                                v-model="form.arrivalTemperature"
                                :disabled="disabled"
                                type="number"
                                :config="{
                                    max: 999.99,
                                    min: -999.99,
                                    supportZero: true,
                                    supportNegative: true,
                                    formatLength: 2,
                                }"
                                :width="276"
                            >
                                <abc-icon slot="appendInner" color="#AAB4BF" icon="n-degrees-celsius-line"></abc-icon>
                            </abc-input>
                        </abc-form-item>
                    </abc-space>
                </abc-section>
                <abc-section v-if="!disabled || (disabled && form.attachments && form.attachments.length)" style="margin-top: 24px;">
                    <abc-title level="1" :bold="false">
                        附件
                    </abc-title>
                </abc-section>
                <abc-section>
                    <external-file
                        v-model="form.attachments"
                        :disabled="disabled"
                        :hidden-left="true"
                        :business-type="BusinessTypeEnum.GSP_TRANSPORT_RECORD"
                        oss-filepath="pharmacy-gsp-transport-record"
                        :max-upload-count="9"
                        business-desc="上传运输记录附件"
                        :accept="['.jpg', '.JPG', '.jpeg', '.JPEG', '.png', '.PNG', '.pdf', '.PDF']"
                        width="96px"
                        height="96px"
                        upload-description="运输记录附件支持图片、PDF格式"
                    ></external-file>
                </abc-section>
            </abc-form>
        </abc-layout>
        <div
            slot="footer"
            class="dialog-footer"
        >
            <abc-button
                v-if="allowSave"
                :loading="loadingSubmit"
                :disabled="disabledBtn"
                @click="submit"
            >
                {{ btnText }}
            </abc-button>
            <abc-button
                variant="ghost"
                @click="cancel()"
            >
                {{ cancelText }}
            </abc-button>
        </div>
    </abc-dialog>
</template>
<script>
    import { mapGetters } from 'vuex';
    import * as tools from '@/views-pharmacy/common/tools';
    import ExternalFile from 'views/layout/external-file/index.vue';
    import {
        formatDate,
    } from '@abc/utils-date';
    import { BusinessTypeEnum } from '@/views/layout/mobile-upload-dialog/config';
    import {
        getSourceOrder,
    } from '@/views-pharmacy/inventory/utils';
    import {
        RelatedOrderType,
    } from '@/views-pharmacy/inventory/constant';
    import GspAPI from '@/api/pharmacy/gsp';
    import { isEqual } from '@abc/utils';
    import clone from 'utils/clone';
    import AbcSocket from 'views/common/single-socket';
    export default {
        name: 'OrderDialog',
        components: {
            ExternalFile,
        },
        props: {
            value: Boolean,
            // 草稿单
            isDraft: Boolean,
            order: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            computedOrder: {
                type: Object,
                default: () => {
                    return {};
                },
            },
            // 运输记录id
            transportRecordId: {
                type: String,
                default: '',
            },
            canEdit: Boolean,
            disabledReceiveDate: {
                type: Boolean,
                default: true,
            },
            requiredArrivalTemperature: {
                type: Boolean,
                default: true,
            },
            requiredReceiveDate: {
                type: Boolean,
                default: true,
            },
            // 来源
            sourceType: {
                type: Number,
                default: 0,
            },
            clinicEmployees: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            orderClinicName: {
                type: String,
                default: '',
            },
        },
        data() {
            const pickerOptions = {
                disabledDate(date) {
                    return date > new Date() || date < new Date('2001-11-01');
                },
            };
            return {
                pickerOptions,
                BusinessTypeEnum,
                loadingSubmit: false,
                transportRecordInfo: {
                    remark: '',
                    recorder: '',
                },
                transportRecordInfoCache: {
                    remark: '',
                    recorder: '',
                },
                searchKeyRecorder: '', // 搜索关键词 - 记录人
                describeList: [{
                    date: formatDate(new Date(), 'YYYY-MM-DD'),
                    describe: '今天',
                }],
                form: {
                    clinicId: '',
                    arrivalTime: '',
                    arrivalTemperature: '',
                    departureTime: '',
                    departureTemperature: '',
                    transportMode: '', // 运输工具
                    temperatureControlMode: '', // 温控方式
                    transportNo: '',// 车牌号
                    driverName: '', // 驾驶员
                    carrierName: '',// 单位
                    // 发货地址
                    address: {
                        addressCityId: '',
                        addressCityName: '',
                        addressProvinceId: '',
                        addressProvinceName: '',
                        addressDistrictId: '',
                        addressDistrictName: '',
                    },
                    departureAddressDetail: '',
                    attachments: [],
                },
                formCache: null,
                detailInfo: {},
                loading: false,
                transportOrderRecordId: '',
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'employeeList',
                'userInfo',
                'westernMedicineConfig',
                'isChainAdmin',
            ]),
            descriptions() {
                return {
                    receiveDate: this.order.receiveTime || this.detailInfo.receiveDate || '',
                    clinicName: this.orderClinicName || this.order.clinicName || this.detailInfo.clinicName || '',
                    orderNo: this.receiveOrder?.orderNo || this.detailInfo.receiveOrderNo || '',
                    outOrderNo: this.order.outOrderNo || this.detailInfo.receiveOrderOutOrderNo || '',
                    kindCount: this.computedOrder?.kindCount || this.order.kindCount || this.detailInfo.receiveKindCount || '',
                };
            },
            btnText() {
                return '保存';
            },
            allowSave() {
                return this.canEdit;
            },
            disabledBtn() {
                return this.canEdit && this.transportOrderRecordId && isEqual(this.form, this.formCache) && isEqual(this.transportRecordInfo, this.transportRecordInfoCache);
            },
            // 收货单
            receiveOrder() {
                const { relatedOrders = {} } = this.order;
                const [order] = getSourceOrder(relatedOrders?.list ?? [], RelatedOrderType.RECEIPT);
                return order || {};
            },
            title() {
                return `${this.isModify ? '修改' : this.canEdit ? '新增' : ''}运输记录`;
            },
            cancelText() {
                return this.canEdit ? '取消' : '关闭';
            },
            isModify() {
                return this.transportRecordId && this.canEdit;
            },
            disabled() {
                return !this.canEdit;
            },
            // 记录人选项
            recorderOptions() {
                let employeeList = this.clinicEmployees?.length ? this.clinicEmployees : (this.employeeList || []);
                if (this.searchKeyRecorder) {
                    // 有关键词时，过滤一下
                    employeeList = tools.filterEmployeeBySearchKey(employeeList, this.searchKeyRecorder);
                }
                return employeeList.map((item) => ({
                    value: item.employeeId,
                    label: item.employeeName,
                }));
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        async created() {
            // 传入运输单Id
            this.transportOrderRecordId = this.transportRecordId;
            // 有详情拉取详情 没有id设置草稿
            if (this.transportOrderRecordId) {
                await this.fetchTransportRecord(this.transportOrderRecordId);
            } else {
                await this.createdDraft();
            }
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._socket.on('short-url.upload_attachment', this.handleImages);
        },
        beforeDestroy() {
            this._socket?.off('short-url.upload_attachment', this.handleImages);
        },
        methods: {
            getSourceOrder,
            handleImages(data) {
                const {
                    attachments = [],
                    businessType,
                } = data;
                if (businessType === BusinessTypeEnum.GSP_TRANSPORT_RECORD && attachments && attachments.length) {
                    this.form.attachments = this.form.attachments.concat(attachments.map((item) => {
                        return {
                            fileName: item.fileName,
                            fileSize: item.fileSize,
                            sort: item.sort,
                            url: item.url,
                        };
                    }));
                    this.form.attachments = this.form.attachments.map((item, index) => {
                        return {
                            ...item,
                            sort: index,
                        };
                    });
                }
            },
            setOrderInfo() {
                Object.assign(this.form, this.detailInfo);
                this.form.address = {
                    addressCityId: this.detailInfo?.departureCityId,
                    addressCityName: this.detailInfo?.departureCityName,
                    addressProvinceId: this.detailInfo?.departureProvinceId,
                    addressProvinceName: this.detailInfo?.departureProvinceName,
                    addressDistrictId: this.detailInfo?.departureDistrictId,
                    addressDistrictName: this.detailInfo?.departureDistrictName,
                };
                this.form.arrivalTime = this.form.arrivalTime ? formatDate(this.form.arrivalTime, 'YYYY-MM-DD HH:mm') : '';
                this.form.departureTime = this.form.departureTime ? formatDate(this.form.departureTime, 'YYYY-MM-DD HH:mm') : '';
                this.transportRecordInfo = {
                    remark: this.detailInfo.remark,
                    recorder: this.detailInfo.recorderId,
                };
                this.formCache = clone(this.form);
                this.transportRecordInfoCache = clone(this.transportRecordInfo);
                // 如果有收货门店id
                if (this.order.clinicId) {
                    this.form.clinicId = this.order.clinicId;
                    if (this.transportRecordInfo.recorder && !this.recorderOptions?.find((item) => {
                        return item.value === this.transportRecordInfo.recorder;
                    })) {
                        this.transportRecordInfo.recorder = this.order?.receiveBy || '';
                    }
                }
            },
            createdParams() {
                const {
                    form = {}, transportRecordInfo = {},
                } = this;
                const { address = {} } = form;
                const params = {
                    id: this.detailInfo?.id || '',
                    clinicId: this.order.clinicId || '',
                    clinicName: this.order.clinicName || '',
                    recorderId: transportRecordInfo.recorder || '',
                    recorderName: this.recorderOptions.find((item) => {
                        return item.value === transportRecordInfo.recorder;
                    })?.label || '',
                    receiveOrderId: this.order.id || '', // 收货单id
                    receiveOrderNo: this.order.orderNo || '', // 收货单号
                    receiveOrderOutOrderNo: this.order?.outOrderNo || '', // 随货单号
                    receiveKindCount: this.order.kindCount || 0, // 收货件数
                    transportMode: form.transportMode,
                    transportNo: form.transportNo,
                    temperatureControlMode: form.temperatureControlMode,
                    departureTime: form.departureTime ? formatDate(form.departureTime, 'YYYY-MM-DD HH:mm:ss') : '',
                    departureTemperature: form.departureTemperature,
                    driverName: form.driverName,
                    departureProvinceId: address.addressProvinceId,
                    departureProvinceName: address.addressProvinceName,
                    departureCityId: address.addressCityId,
                    departureCityName: address.addressCityName,
                    departureDistrictId: address.addressDistrictId,
                    departureDistrictName: address.addressDistrictName,
                    departureAddressDetail: form.departureAddressDetail,
                    carrierName: form.carrierName,
                    arrivalTime: form.arrivalTime ? formatDate(form.arrivalTime, 'YYYY-MM-DD HH:mm:ss') : '',
                    arrivalTemperature: form.arrivalTemperature,
                    remark: transportRecordInfo.remark,
                    attachments: form.attachments,
                    sourceType: this.sourceType,
                };
                if (this.sourceType === 1) {
                    params.deliveryOrderId = this.order.id || '';
                    params.deliveryOrderNo = this.order.orderNo || '';
                }
                return params;
            },
            async createdDraft() {
                try {
                    const { data } = await GspAPI.createdTransportRecord({
                        sourceType: this.sourceType,
                        clinicId: this.isChainAdmin ? this.order?.clinicId || '' : '',
                    });
                    this.detailInfo = {
                        id: data?.id || '',
                        clinicId: data?.clinicId || '',
                        clinicName: data?.clinicName || '',
                    };
                    this.transportOrderRecordId = data?.id || '';
                } catch (e) {
                    console.log(e);
                }
            },
            async submit() {
                this.$refs.form.validate((val) => {
                    if (val) {
                        this.$refs.table.validate(async (value) => {
                            if (value) {
                                // 草稿单保存
                                const params = this.createdParams();
                                this.loadingSubmit = true;
                                try {
                                    const { data } = await GspAPI.modifyTransportRecord(this.transportOrderRecordId, params);
                                    if (data?.id) {
                                        this.$Toast({
                                            type: 'success',
                                            message: '保存运输记录成功',
                                        });
                                        this.$emit('refresh', data?.id);
                                        this.showDialog = false;
                                    }
                                } catch (e) {
                                    console.log(e);
                                } finally {
                                    this.loadingSubmit = false;
                                }
                            }
                        });
                    }
                });
            },
            async fetchTransportRecord(transportRecordId) {
                this.loading = true;
                try {
                    const { data } = await GspAPI.fetchTransportRecord(transportRecordId);
                    this.detailInfo = data;
                    this.setOrderInfo();
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            cancel() {
                this.showDialog = false;
            },
        },
    };
</script>
