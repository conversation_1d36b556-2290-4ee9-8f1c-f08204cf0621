<template>
    <abc-layout preset="page-table" class="abc-pharmacy-gsp-check-accept-unqualified-container">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <abc-date-picker
                        v-model="toolsParams.dateRange"
                        :picker-options="pickerOptions"
                        type="daterange"
                        clearable
                    ></abc-date-picker>
                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="toolsParams.clinicId"
                        :show-all-clinic="false"
                        placeholder="总部/门店"
                        :clearable="true"
                        :width="160"
                    ></clinic-select>
                    <abc-select
                        v-model="toolsParams.supplierId"
                        :width="160"
                        :fetch-suggestions="(searchKey) => searchKeySupplier = searchKey"
                        with-search
                        clearable
                        placeholder="供应商"
                    >
                        <abc-option
                            v-for="item in supplierOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                    <goods-auto-complete
                        placeholder="商品名称"
                        :search.sync="fetchParams.keyword"
                        :focus-show="true"
                        :type-arr="fetchParams.type"
                        :sub-type-arr="fetchParams.subType"
                        :c-m-spec="fetchParams.cMSpec"
                        :width="260"
                        enable-local-search
                        is-type-arr
                        format-count-key="stock"
                        :inorder-config="0"
                        :clinic-id="clinicId"
                        @selectGoods="onSelectGoods"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                        <div slot="append" class="search-icon" @click="onClickClearKeyword">
                            <i v-if="!!fetchParams.keyword" class="iconfont cis-icon-cross_small"></i>
                        </div>
                    </goods-auto-complete>
                </abc-space>
                <abc-button
                    icon="n-upload-line"
                    variant="ghost"
                    :disabled="dataList.length === 0"
                    @click="exportExcelCommon(GSPExportEnum.CHECK_ACCEPT.UNQUALIFIED,
                                              createParams(),
                                              exportTitle)"
                >
                    导出
                </abc-button>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="abcTable"
                type="pro"
                :render-config="renderConfig"
                :loading="loading"
                :custom-tr-key="customTrKey"
                :data-list="dataList"
            ></abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="count"
                @current-change="onChangePage"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import GspAPI from '@/api/pharmacy/gsp';
    import SupplierApi from 'api/goods/supplier.js';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select';

    const GoodsAutoComplete = () => import('src/views/inventory/common/goods-auto-complete');

    import pickerOptions from 'views/common/pickerOptions';
    import mixinTable from '@/views-pharmacy/common/mixin-table';

    import { mapGetters } from 'vuex';
    import { isEqual } from '@abc/utils';

    import * as tools from '@/views-pharmacy/common/tools';
    import { PurchaseTableConfig } from './table-config';
    import { formatMoney } from 'utils';
    import {
        exportExcelCommon, GSPExportEnum,
    } from '@/views-pharmacy/gsp/utils/gspExportUtil';
    export default {
        components: {
            GoodsAutoComplete,
            ClinicSelect,
        },
        mixins: [
            pickerOptions,
            mixinTable,
        ],
        data() {
            return {
                GSPExportEnum,
                tools,
                fetchParams: {
                    keyword: '',
                    typeId: '',
                    typeLabel: '全部类型',
                    type: [1, 2, 7],
                    subType: [],
                    cMSpec: '',
                },
                searchKeySupplier: '', // 搜索关键词 - 供应商
                toolsParams: {
                    dateRange: this.createDateRangeCurMonth(), // 日期范围
                    clinicId: '', // 门店ID
                    goodsId: '', // 商品ID
                    supplierId: '', // 供应商ID
                },
                tableHeader: [],
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                },
                loading: false,
                originData: null,
                originDataSupplier: null,
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
            ]),
            isMedicalDevice() {
                return this.name === '@PharmacyGspCheckAcceptMedicalDeviceUnqualified';
            },
            exportTitle() {
                if (this.isMedicalDevice) {
                    return '医疗器械验收不合格品记录';
                }
                return '药品验收不合格品记录';
            },
            name() {
                return this.$route?.name || '';
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            // 供应商选项
            supplierOptions() {
                let supplierList = this.originDataSupplier?.rows || [];
                if (this.searchKeySupplier) {
                    // 有关键词时，过滤一下
                    const searchKeyStr = this.searchKeySupplier.toLocaleLowerCase();
                    supplierList = supplierList.filter((item) => (item.name || '').toLocaleLowerCase().indexOf(searchKeyStr) !== -1);
                }
                return supplierList.map((item) => ({
                    value: item.id,
                    label: item.name,
                }));
            },
            // 表格数据
            dataList() {
                return this.originData?.rows || [];
            },
            // 总条数
            count() {
                return this.originData?.total || 0;
            },
            renderConfig() {
                const tableConfig = new PurchaseTableConfig(this.currentClinic);
                return tableConfig.extendConfig({
                    inspectTime: {
                        dataFormatter: (inspectTime) => {
                            if (!inspectTime) return '';
                            return tools.getDateFormat(inspectTime);
                        },
                    },
                    clinicName: {
                        dataFormatter: (_,row) => (row.clinicId === this.clinicId ? '总部' : row.clinicName),
                    },
                    displayName: {
                        dataFormatter: (_,row) => row.goods?.displayName,
                    },
                    displaySpec: {
                        dataFormatter: (_,row) => row.goods?.displaySpec,
                    },
                    manufacturerFull: {
                        dataFormatter: (_,row) => row.goods?.manufacturerFull,
                    },
                    unqualifiedPackageCount: {
                        dataFormatter: (_,row) => {
                            const goods = {
                                ...row.goods,
                                packageCount: row.unqualifiedPackageCount,
                                pieceCount: row.unqualifiedPieceCount,
                            };
                            return tools.getInventoryCountWording(goods);
                        },
                    },
                    receiveTime: {
                        dataFormatter: (receiveTime) => {
                            if (!receiveTime) {
                                return '';
                            }
                            return tools.getDateFormat(receiveTime);
                        },
                    },
                    shortId: {
                        dataFormatter: (_,row) => row.goods?.shortId,
                    },
                    barCode: {
                        dataFormatter: (_,row) => row.goods?.barCode,
                    },
                    medicineNmpn: {
                        dataFormatter: (_,row) => row.goods?.medicineNmpn,
                    },
                    unqualifiedTraceableCodeList: {
                        customRender: (h, row) => {
                            if (!row.unqualifiedTraceableCodeList?.length) return '';
                            return <AbcPopover
                                        placement='top-start'
                                        trigger="hover"
                                        theme="yellow"
                                        popperStyle={{ 'padding-right': '0px' }}
                            >
                                        <abc-table-cell slot='reference'>
                                            <span>已采集：{row.unqualifiedTraceableCodeList.length}</span>
                                        </abc-table-cell>
                                        <abc-scrollbar style="max-height: 400px;" paddingSize="none">
                                            <abc-flex vertical>
                                                {
                                                    row.unqualifiedTraceableCodeList.map((item) => {
                                                        return <abc-text theme="black">
                                                            { item?.no ? item.no : item }
                                                            {
                                                                item?.count > 1 ? <abc-text theme="gray" style="margin-left:8px">x{item.count}</abc-text> : null
                                                            }
                                                        </abc-text>;
                                                    })
                                                }
                                            </abc-flex>
                                        </abc-scrollbar>
                                    </AbcPopover>;
                        },
                    },
                    packagePrice: {
                        dataFormatter: (_,row) => formatMoney(row.packagePrice, false),
                    },
                });
            },
        },
        watch: {
            toolsParams: {
                handler() {
                    this.initPageIndex();
                    this.fetchDataList();
                },
                deep: true,
            },
            name: {
                handler() {
                    this.toolsParams = {
                        dateRange: this.createDateRangeCurMonth(), // 日期范围
                        clinicId: '', // 门店ID
                        goodsId: '', // 商品ID
                        supplierId: '', // 供应商ID
                    };
                    this.initPageIndex();
                    this.fetchDataList();
                    this.fetchInventorySupplierList();
                },
                deep: true,
            },
        },
        async mounted() {
            this.setPageSizeWithTableHeight();
            await this.fetchInventorySupplierList();
        },
        methods: {
            exportExcelCommon,
            customTrKey(item) {
                return `${item.id}`;
            },
            handleMounted(data) {
                this.pageParams.pageSize = data.paginationLimit;
                this.fetchDataList();
            },
            /**
             * 拉取供应商数据列表
             * <AUTHOR>
             * @date 2023-12-27
             */
            async fetchInventorySupplierList() {
                try {
                    const params = {
                        offset: 0, // 分页
                        limit: 999, // 每页条数，拉取全部供应商数据
                    };
                    const res = await SupplierApi.fetchInventorySupplierList(params);
                    this.originDataSupplier = res?.data;
                } catch (error) {
                    console.log('fetchDataList error', error);
                }
            },
            /**
             * 当选择一个商品时
             * <AUTHOR>
             * @date 2024-01-03
             * @param {Object} goods
             */
            async onSelectGoods(goods) {
                this.toolsParams.goodsId = goods.goodsId;
                this.fetchParams.keyword = goods.displayName;
            },
            /**
             * 当点击情况关键词
             * <AUTHOR>
             * @date 2024-01-08
             */
            onClickClearKeyword() {
                this.toolsParams.goodsId = '';
                this.fetchParams.keyword = '';
            },
            /**
             * 创建查询参数
             * <AUTHOR>
             * @date 2023-12-27
             * @returns {Object}
             */
            createParams() {
                const {
                    dateRange, // 日期范围
                    clinicId, // 门店ID
                    goodsId, // 商品ID
                    supplierId, // 供应商ID
                } = this.toolsParams;
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;
                const params = {
                    clinicId, // 门店ID
                    dateStart: dateRange[0], // 开始日期
                    dateEnd: dateRange[1], // 截止日期
                    goodsId, // 商品ID
                    supplierId, // 供应商ID
                    offset: pageIndex * pageSize, // 分页
                    limit: pageSize, // 每页条数
                    category: this.isMedicalDevice ? 10 : 0,
                };
                return params;
            },
            /**
             * 拉取数据列表
             * <AUTHOR>
             * @date 2023-12-27
             */
            async fetchDataList() {
                this.loading = true;
                const params = this.createParams();
                const fetchResponse = await GspAPI.fetchInspectUnqualifiedItems(params);
                if (!isEqual(params, this.createParams())) {
                    return fetchResponse;
                }
                this.loading = false;
                if (fetchResponse.status === false) {
                    return fetchResponse;
                }
                this.originData = fetchResponse.data;
            },
            /**
             * 当切换页码时触发
             * <AUTHOR>
             * @date 2023-12-25
             * @param {Number} pageIndex
             */
            onChangePage(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchDataList();
            },
            /**
             * 初始化页码
             * <AUTHOR>
             * @date 2023-12-29
             */
            initPageIndex() {
                this.pageParams.pageIndex = 0;
            },
        },
    };
</script>
