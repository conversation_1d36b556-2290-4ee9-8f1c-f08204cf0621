<template>
    <abc-layout preset="page-table" class="abc-pharmacy-gsp-frames__special-drugs--piece-sale">
        <abc-layout-header>
            <abc-flex
                justify="space-between"
                align="center"
                style="width: 100%;"
            >
                <abc-space>
                    <abc-date-picker
                        v-model="searchForm.dateRange"
                        type="daterange"
                        placeholder="选择日期范围"
                        :picker-options="pickerOptions"
                        @change="initDataList"
                    ></abc-date-picker>
                    <goods-auto-complete
                        placeholder="扫码/商品名/首字母/条码"
                        :search.sync="fetchParams.keyword"
                        :focus-show="true"
                        :type-arr="fetchParams.type"
                        :sub-type-arr="fetchParams.subType"
                        :c-m-spec="fetchParams.cMSpec"
                        :width="220"
                        enable-local-search
                        is-type-arr
                        format-count-key="stock"
                        :inorder-config="0"
                        :clinic-id="clinicId"
                        @selectGoods="onSelectGoods"
                    >
                        <abc-search-icon slot="prepend"></abc-search-icon>
                        <div slot="append" class="search-icon" @click="onClickClearKeyword">
                            <i v-if="!!fetchParams.keyword" class="iconfont cis-icon-cross_small"></i>
                        </div>
                    </goods-auto-complete>
                    <abc-input
                        v-model="searchForm.batchNo"
                        placeholder="生产批号"
                        :width="100"
                        @input="_fetchDataList"
                    ></abc-input>
                    <abc-select
                        v-model="searchForm.dismountingBy"
                        :width="100"
                        :fetch-suggestions="(search) => searchKey = search"
                        with-search
                        clearable
                        placeholder="分拆人员"
                        @change="initDataList"
                    >
                        <abc-option
                            v-for="item in personOptions"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                        ></abc-option>
                    </abc-select>
                    <clinic-select
                        v-if="isChainAdmin"
                        v-model="searchForm.clinicId"
                        :show-all-clinic="false"
                        placeholder="总部/门店"
                        :clearable="true"
                        :width="150"
                        @change="initDataList"
                    ></clinic-select>
                </abc-space>
                <abc-button variant="ghost" icon="export" @click="handleExportFromForm">
                    导出
                </abc-button>
            </abc-flex>
        </abc-layout-header>
        <abc-layout-content @layout-mounted="handleMounted">
            <abc-table
                ref="abcTable"
                :render-config="renderConfig"
                :loading="loading"
                :data-list="dataList"
            >
            </abc-table>
        </abc-layout-content>
        <abc-layout-footer>
            <abc-pagination
                :show-total-page="true"
                :pagination-params="pageParams"
                :count="pageParams.total"
                @current-change="onChangePage"
            ></abc-pagination>
        </abc-layout-footer>
    </abc-layout>
</template>

<script>
    import pickerOptions from 'views/common/pickerOptions';
    import ClinicSelect from 'views/layout/clinic-select/clinic-select.vue';
    import { mapGetters } from 'vuex';
    import GspAPI from '@/api/pharmacy/gsp';
    import {
        exportExcelCommon, GSPExportEnum,
    } from '@/views-pharmacy/gsp/utils/gspExportUtil';
    import { debounce } from 'utils/lodash';
    import * as tools from '@/views-pharmacy/common/tools';
    const GoodsAutoComplete = () => import('src/views/inventory/common/goods-auto-complete');
    export default {
        name: 'PharmacyGspPieceSale',
        components: {
            ClinicSelect,
            GoodsAutoComplete,
        },
        mixins: [
            pickerOptions,
        ],
        data() {
            return {
                fetchParams: {
                    keyword: '',
                    typeId: '',
                    typeLabel: '全部类型',
                    type: [],
                    subType: [],
                    cMSpec: '',
                },
                loading: false,
                dataList: [],
                // 查询表单数据
                searchForm: {
                    dateRange: this.createDateRangeCurMonth(), // 时间范围
                    keyword: '',
                    batchNo: '',
                    dismountingBy: '',
                    store: '',
                    clinicId: '',
                    goodsId: '',
                },
                // 分页数据
                pageParams: {
                    pageIndex: 0,
                    pageSize: 10,
                    total: 0,
                },
                searchKey: '',
            };
        },
        computed: {
            GSPExportEnum,
            ...mapGetters([
                'isChainAdmin',
                'currentClinic',
                'employeeList',
            ]),
            // 人员选项
            personOptions() {
                let employeeList = this.employeeList || [];
                if (this.searchKey) {
                    // 有关键词时，过滤一下
                    employeeList = tools.filterEmployeeBySearchKey(employeeList, this.searchKey);
                }
                return employeeList.map((item) => ({
                    value: item.employeeId,
                    label: item.employeeName,
                }));
            },
            renderConfig() {
                return {
                    'hasInnerBorder': false,
                    'list': [{
                        'key': 'dismountingTime',
                        'label': '起始拆零日期',
                        'style': {
                            'flex': 1,'width': '140px','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                        dataFormatter: (dismountingTime) => {
                            if (!dismountingTime) {
                                return '';
                            }
                            return tools.getDatetimeFormat(dismountingTime);
                        },
                    },{
                        'key': 'saleTime',
                        'label': '销售日期',
                        'style': {
                            'flex': 1,'width': '130px','maxWidth': '','minWidth': '130px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                        dataFormatter: (saleTime) => {
                            if (!saleTime) {
                                return '';
                            }
                            return tools.getDatetimeFormat(saleTime);
                        },
                    },{
                        'key': 'clinicName',
                        'label': '门店',
                        'style': {
                            'flex': 1,'width': '120px','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                        hidden: !this.isChainAdmin,
                    },{
                        'key': 'shortId',
                        'label': '商品编码',
                        'style': {
                            'flex': 1,'width': '96px','maxWidth': '','minWidth': '96px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                    },{
                        'key': 'goodsName',
                        'label': '商品名称',
                        'style': {
                            'flex': '1','width': '','maxWidth': '','minWidth': '220px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                    },{
                        'key': 'displaySpec',
                        'label': '规格',
                        'style': {
                            'flex': 1,'width': '120px','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                    },{
                        'key': 'manufacturer',
                        'label': '厂家',
                        'style': {
                            'flex': 1,'width': '120px','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                    },{
                        'key': 'batchNo',
                        'label': '批号',
                        'style': {
                            'flex': 1,'width': '100px','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                    },{
                        'key': 'expiryDate',
                        'label': '有效日期',
                        'style': {
                            'flex': 1,'width': '100px','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                    },{
                        'key': 'saleCount',
                        'label': '销售数量',
                        'style': {
                            'flex': 1,'width': '90px','maxWidth': '','minWidth': '90px','paddingLeft': '','paddingRight': '','textAlign': 'right',
                        },
                    },{
                        'key': 'remainingDismountingCount',
                        'label': '剩余数量',
                        'style': {
                            'flex': 1,'width': '90px','maxWidth': '','minWidth': '90px','paddingLeft': '','paddingRight': '16px','textAlign': 'right',
                        },
                    },{
                        'key': 'saleNo',
                        'label': '销售单号',
                        'style': {
                            'flex': 1,'width': '160px','maxWidth': '','minWidth': '160px','paddingLeft': '16px','paddingRight': '','textAlign': 'left',
                        },
                    },{
                        'key': 'saleByName',
                        'label': '销售人员',
                        'style': {
                            'flex': 1,'width': '100px','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                    },{
                        'key': 'dismountingByName',
                        'label': '分拆人员',
                        'style': {
                            'flex': 1,'width': '100px','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
                        },
                    }],
                };
            },
            // 当前门店id
            clinicId() {
                return this.currentClinic?.clinicId;
            },
            checkedCount() {
                return this.dataList.filter((item) => item.checked).length;
            },
        },
        created() {
            if (!this._fetchDataList) {
                this._fetchDataList = debounce(this.initDataList, 500, true);
            }
        },
        methods: {
            exportExcelCommon,
            async onSelectGoods(goods) {
                this.searchForm.goodsId = goods.goodsId;
                this.fetchParams.keyword = goods.displayName;
                await this.initDataList();
            },
            async onClickClearKeyword() {
                this.searchForm.goodsId = '';
                this.fetchParams.keyword = '';
                await this.initDataList();
            },
            handleMounted(data) {
                this.pageParams.pageSize = data.paginationLimit;
                this.fetchDataList();
            },
            /**
             * 从表单区域导出
             */
            handleExportFromForm() {
                exportExcelCommon(GSPExportEnum.SPECIAL_DRUGS.SALE, this.createParams(),'拆零药品销售');
            },
            async initDataList() {
                this.pageParams.pageIndex = 0;
                await this.fetchDataList();
            },

            /**
             * 获取数据列表
             */
            async fetchDataList() {
                try {
                    this.loading = true;
                    // TODO: 调用API获取特药拆零销售数据
                    const params = this.createParams();
                    const { data } = await GspAPI.fetchPieceSaleList(params);
                    this.dataList = data?.rows || [];
                    this.pageParams.total = data?.total || 0;
                } catch (error) {
                    console.error('获取数据失败:', error);
                } finally {
                    this.loading = false;
                }
            },

            /**
             * 分页变化处理
             */
            onChangePage(pageIndex) {
                this.pageParams.pageIndex = pageIndex - 1;
                this.fetchDataList();
            },

            /**
             * 创建查询参数
             */
            createParams() {
                const {
                    dateRange,
                    keyword,
                    batchNo,
                    dismountingBy,
                    goodsId,
                    clinicId,
                } = this.searchForm;
                const {
                    pageIndex,
                    pageSize,
                } = this.pageParams;

                return {
                    startDate: dateRange[0], // 开始时间
                    endDate: dateRange[1], // 结束时间
                    keyword,
                    batchNo,
                    dismountingBy,
                    goodsId,
                    clinicId,
                    offset: pageIndex * pageSize,
                    limit: pageSize,
                };
            },
        },
    };
</script>
