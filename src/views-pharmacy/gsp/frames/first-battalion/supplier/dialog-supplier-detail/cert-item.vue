<template>
    <div
        class="pharmacy__first-battalion__supplier__cert-item"
        :class="{
            'is-cursor': isNotFile,
            'is-disabled': disabled,
        }"
        :style="{ width: `${width}px` }"
        @click="onClickDetail"
    >
        <template v-if="isNotFile">
            <abc-icon
                icon="plus"
                :color="$store.state.theme.style.P1"
                size="12"
            ></abc-icon>
            <span class="label">资质证照</span>
        </template>
        <template v-else>
            <img :src="url" alt="" @click.stop="previewImg" />
            <div class="info-wrapper">
                <p class="name" :title="item.name">
                    {{ item.name }}
                </p>
                <abc-text :theme="validToTheme">
                    {{ validToWording }}
                </abc-text>
            </div>
            <template v-if="isVisibleHandles">
                <div
                    class="icon-btn"
                    @click.stop="onClickUpdate"
                >
                    <abc-icon
                        icon="n-edit-line"
                        :color="$store.state.theme.style.theme1"
                        size="14"
                    ></abc-icon>
                </div>
                <div
                    class="icon-btn"
                    @click.stop="onClickDelete"
                >
                    <abc-icon
                        icon="n-delete-2-line"
                        :color="$store.state.theme.style.R1"
                        size="14"
                    ></abc-icon>
                </div>
            </template>
        </template>

        <abc-preview
            v-if="showPreviewImage"
            v-model="showPreviewImage"
            :lists="[{ url: url }]"
            :index="0"
        ></abc-preview>
    </div>
</template>

<script>
    import { BusinessTypeEnum } from '@/views/layout/mobile-upload-dialog/config';
    import * as tools from '@/views-pharmacy/common/tools';
    import { nextMonth } from '@abc/utils-date';

    const DialogCertEdit = () => import('./dialog-cert-edit.vue');

    export default {
        name: 'CertItem',
        props: {
            item: {
                type: Object,
                default: () => {},
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            width: {
                type: Number,
                default: 400,
            },
        },
        data() {
            return {
                BusinessTypeEnum,
                showPreviewImage: false,
            };
        },
        computed: {
            // 是否有文件
            isHasFile() {
                return !!this.item?.name;
            },
            // 是否无文件
            isNotFile() {
                return !this.isHasFile;
            },
            // 有效期文案
            validToWording() {
                let wording = '';
                if (this.isExpired) {
                    return '已过期';
                }
                if (this.isAdvent) {
                    return `${this.adventDays} 天后到期`;
                }
                if (this.item?.validTo) {
                    wording = `有效期至 ${this.item?.validTo}`;
                }
                return wording;
            },
            // 有效期文案主题
            validToTheme() {
                if (this.isExpired) {
                    return 'danger';
                }
                if (this.isAdvent) {
                    return 'warning-light';
                }
                return 'gray-light';
            },
            // 是否显示操作
            isVisibleHandles() {
                if (this.disabled) {
                    return false;
                }
                return true;
            },
            // 图片url
            url() {
                return this.item?.pictureUrls[0]?.url;
            },
            currentTime() {
                if (!this.item?.validTo) {
                    return '';
                }
                const validToDate = new Date(this.item.validTo);
                // 格式化为 YYYY-MM-DD 23:59:59
                const year = validToDate.getFullYear();
                const month = String(validToDate.getMonth() + 1).padStart(2, '0');
                const day = String(validToDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day} 23:59:59`;
            },
            // 是否已到期
            isExpired() {
                const date1 = new Date(this.currentTime);
                const date2 = new Date();
                return date1.getTime() - date2.getTime() < 0;
            },
            // 是否即将到期
            isAdvent() {
                const date1 = new Date(this.currentTime);
                const date2 = new Date(this.getThreeMonthDay());
                return date1.getTime() <= date2.getTime();
            },
            // 临期时间
            adventDays() {
                if (this.isExpired) {
                    return 0;
                }
                const date1 = new Date(this.currentTime);
                const date2 = new Date();
                // 计算时间差并转换为天数
                const diffTime = date1.getTime() - date2.getTime();
                const diffDays = Math.floor(diffTime / (24 * 3600 * 1000));
                return diffDays;
            },
        },
        methods: {
            getThreeMonthDay() {
                // 产品要求计算3个自然月后的时间
                const threeMonthDays = new Date(nextMonth(new Date(nextMonth(new Date(nextMonth(new Date()))))));
                const validToDate = new Date(threeMonthDays);
                const year = validToDate.getFullYear();
                const month = String(validToDate.getMonth() + 1).padStart(2, '0');
                const day = String(validToDate.getDate()).padStart(2, '0');
                return `${year}-${month}-${day} 00:00:00`;
            },
            /**
             * 预览图片
             * <AUTHOR>
             * @date 2024-01-19
             * @param {Object} item
             */
            previewImg() {
                if (!this.url) {
                    return;
                }
                this.showPreviewImage = true;
            },
            /**
             * 当点击打开明细弹窗
             * <AUTHOR>
             * @date 2024-01-19
             */
            onClickDetail() {
                if (this.isHasFile) {
                    return;
                }
                this.openEditDialog();
            },
            /**
             * 当点击打开明细弹窗
             * <AUTHOR>
             * @date 2024-01-19
             */
            onClickUpdate() {
                this.openEditDialog();
            },
            /**
             * 打开编辑弹窗
             * <AUTHOR>
             * @date 2024-01-30
             * @returns {Promise<Response>}
             */
            async openEditDialog() {
                if (this.disabled) {
                    return;
                }
                const openResponse = await tools.openDialog({
                    propsData: {
                        item: this.item, // 证照信息
                    },
                    component: DialogCertEdit,
                });
                if (openResponse.status === false) {
                    return;
                }
                this.$emit('update:item', openResponse.data);
                this.$emit('update');
                return openResponse;
            },
            /**
             * 当点击删除
             * <AUTHOR>
             * @date 2024-01-19
             */
            onClickDelete() {
                this.$emit('delete');
                this.$emit('update');
            },
        },
    };
</script>

<style lang="scss">
    @import 'styles/abc-common.scss';

    .pharmacy__first-battalion__supplier__cert-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 400px;
        height: 48px;
        padding-right: 8px;
        margin-right: 24px;
        margin-bottom: 16px;
        border: 1px solid $P7;
        border-radius: var(--abc-border-radius-small);

        &.is-cursor {
            cursor: pointer;

            &:hover {
                border: 1px solid var(--abc-color-Theme3, #459eff);
            }
        }

        &.is-disabled {
            background-color: var(--abc-color-bg-disabled);
        }

        &:hover .delete-bar {
            visibility: visible;
            opacity: 1;
        }

        > .abc-icon {
            margin-left: 12px;
        }

        > .label {
            margin-left: 8px;
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            color: var(--abc-color-T3, #aab4bf);
        }

        > img {
            width: 52.5px;
            height: 40px;
            margin: 0 8px 0 4px;
            cursor: pointer;
            border-radius: var(--abc-border-radius-small);
        }

        .info-wrapper {
            flex: 1;

            .name {
                height: 22px;
                margin-bottom: 2px;
                font-size: 14px;
                line-height: 22px;
                color: $T1;

                @include ellipsis();
            }

            .to {
                font-size: 12px;
                line-height: 16px;
                color: $T3;
            }

            .is-expired {
                color: $Y2;
            }
        }

        .icon-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 26px;
            height: 26px;
            cursor: pointer;
        }
    }
</style>
