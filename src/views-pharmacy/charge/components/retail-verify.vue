<template>
    <div>
        <div class="retail-verify-wrapper">
            <abc-flex align="center" gap="24">
                <abc-flex align="center" gap="small">
                    <abc-text theme="gray">
                        AI 合规助手
                    </abc-text>
                    <abc-tooltip-info
                        :max-width="420"
                        placement="top-start"
                        style="cursor: pointer;"
                        class="test"
                    >
                        <abc-flex
                            class="retail-verify-tip-popover"
                            vertical
                            gap="large"
                        >
                            <abc-text size="mini" theme="gray">
                                AI 合规助手基于药品零售规范、医保政策打造的智能规范用药工具，内置「用药合规」「医保合规」两套检测机制，帮助药店规避经营风险。
                            </abc-text>
                            <abc-flex vertical gap="small">
                                <abc-flex gap="middle">
                                    <abc-text bold>
                                        用药合规
                                    </abc-text>
                                    <abc-text theme="success">
                                        已开启
                                    </abc-text>
                                </abc-flex>
                                <abc-text size="mini" theme="gray">
                                    自动检测零售环节用药规范性，如药品配伍禁忌、十八反、超量开药等
                                </abc-text>
                            </abc-flex>
                            <abc-flex vertical gap="small">
                                <abc-flex gap="middle">
                                    <abc-text bold>
                                        医保合规
                                    </abc-text>
                                    <abc-text :theme="restrictSwitch ? 'success' : 'warning'">
                                        {{ restrictSwitch ? '已开启' : '未开启' }}
                                    </abc-text>
                                    <abc-link v-if="hasSocialModule && !restrictSwitch" @click="toOpenSocialAssistant">
                                        去开启
                                        <abc-icon icon="s-b-right-line-medium"></abc-icon>
                                    </abc-link>
                                </abc-flex>
                                <abc-text size="mini" theme="gray">
                                    智能检测医保目录用药限制，如不符合机构等级/人群性别/诊断病种等
                                </abc-text>
                            </abc-flex>
                        </abc-flex>
                    </abc-tooltip-info>
                </abc-flex>

                <abc-flex v-if="allVerifyItems && allVerifyItems.length" align="center" gap="middle">
                    <abc-flex
                        gap="6"
                        align="center"
                        :class="{
                            pass: firstCheckItems.level === 'PASS',
                            warn: firstCheckItems.level === 'WARN',
                            danger: firstCheckItems.level === 'DANGER',
                            done: !!firstCheckItems.isDeal,
                        }"
                    >
                        <template v-if="firstCheckItems.level === 'PASS'">
                            <abc-icon icon="s-shield-fill" size="14px"></abc-icon>
                            <span>无风险</span>
                        </template>
                        <template v-else-if="firstCheckItems.level === 'DANGER'">
                            <abc-icon icon="s-compliance-fill" size="14px"></abc-icon>
                            <span>风险</span>
                        </template>
                        <template v-else>
                            <abc-icon icon="s-compliance-fill" size="14px"></abc-icon>
                            <span>提醒</span>
                        </template>
                    </abc-flex>
                    <abc-flex
                        gap="6"
                        align="center"
                        :class="[
                            'abstract',
                            'ellipsis',
                            {
                                pass: firstCheckItems.level === 'PASS',
                                warn: firstCheckItems.level === 'WARN',
                                danger: firstCheckItems.level === 'DANGER',
                                done: !!firstCheckItems.isDeal,
                            },
                        ]"
                        style="max-width: 500px;"
                    >
                        <span class="ellipsis" :title="firstCheckItems.verifyResult">{{ firstCheckItems.verifyResult }}</span>
                    </abc-flex>

                    <abc-select
                        v-if="firstCheckItems.isShebaoVerify"
                        v-model="firstCheckItems.shebaoPayMode"
                        reference-mode="text"
                        reference-text-justify="end"
                        :width="82"
                        @change="onShebaoPayModeChange(firstCheckItems)"
                    >
                        <abc-option :value="ShebaoPayMode.OVERALL" label="使用医保"></abc-option>
                        <abc-option :value="ShebaoPayMode.NO_USE" label="不刷医保"></abc-option>
                    </abc-select>
                </abc-flex>
                <abc-flex v-else align="center" gap="middle">
                    <abc-flex
                        gap="6"
                        align="center"
                        :class="[
                            'status',
                            {
                                pass: verifyStatus === 'PASS',
                                warn: verifyStatus === 'WARN',
                                danger: verifyStatus === 'DANGER',
                            },
                        ]"
                    >
                        <template v-if="verifyStatus === 'PASS'">
                            <abc-icon icon="s-shield-fill" size="14px"></abc-icon>无风险
                        </template>
                        <template v-else-if="verifyStatus === 'DANGER'">
                            <abc-icon icon="s-compliance-fill" size="14px"></abc-icon>风险
                        </template>
                        <template v-else>
                            <abc-icon icon="s-compliance-fill" size="14px"></abc-icon>提醒
                        </template>
                    </abc-flex>

                    <div
                        :class="[
                            'abstract',
                            {
                                pass: verifyStatus === 'PASS',
                                warn: verifyStatus === 'WARN',
                                danger: verifyStatus === 'DANGER',
                            },
                        ]"
                    >
                        {{ abstract?.replaceAll('中西成药处方一：', '') }}
                    </div>
                </abc-flex>

                <abc-flex v-if="allVerifyItems && allVerifyItems.length > 1" align="flex-end">
                    <abc-button
                        variant="text"
                        size="small"
                        theme="default"
                        icon="s-up-line-medium"
                        icon-position="right"
                        class="show-more-btn"
                        data-cy="展开更多"
                        @click="() => showMore = true"
                    >
                        展开更多 ({{ allVerifyItems.length - 1 }})
                    </abc-button>

                    <abc-popover
                        v-model="showMore"
                        trigger="manual"
                        theme="yellow"
                        placement="left-end"
                        :visible-arrow="false"
                        popper-class="retail-verify-popover"
                        :offset="-8"
                    >
                        <div ref="reference" slot="reference"></div>
                        <div
                            v-abc-click-outside="onClickOutside"
                            :style="popoverStyle"
                            class="retail-verify-item-popover"
                            data-cy="abc-wrapper-prescription-review"
                        >
                            <abc-flex align="center" gap="small" style="height: 26px;">
                                <abc-text theme="gray">
                                    AI 合规助手
                                </abc-text>
                                <abc-tooltip-info
                                    :max-width="420"
                                    placement="top-start"
                                    style="cursor: pointer;"
                                    class="test"
                                >
                                    <abc-flex
                                        class="retail-verify-tip-popover"
                                        vertical
                                        gap="large"
                                    >
                                        <abc-text size="mini" theme="gray">
                                            AI 合规助手基于药品零售规范、医保政策打造的智能规范用药工具，内置「用药合规」「医保合规」两套检测机制，帮助药店规避经营风险。
                                        </abc-text>
                                        <abc-flex vertical gap="small">
                                            <abc-flex gap="middle">
                                                <abc-text bold>
                                                    用药合规
                                                </abc-text>
                                                <abc-text theme="success">
                                                    已开启
                                                </abc-text>
                                            </abc-flex>
                                            <abc-text size="mini" theme="gray">
                                                自动检测零售环节用药规范性，如药品配伍禁忌、十八反、超量开药等
                                            </abc-text>
                                        </abc-flex>
                                        <abc-flex vertical gap="small">
                                            <abc-flex gap="middle">
                                                <abc-text bold>
                                                    医保合规
                                                </abc-text>
                                                <abc-text :theme="restrictSwitch ? 'success' : 'warning'">
                                                    {{ restrictSwitch ? '已开启' : '未开启' }}
                                                </abc-text>
                                                <abc-link v-if="hasSocialModule && !restrictSwitch" @click="toOpenSocialAssistant">
                                                    去开启
                                                    <abc-icon icon="s-b-right-line-medium"></abc-icon>
                                                </abc-link>
                                            </abc-flex>
                                            <abc-text size="mini" theme="gray">
                                                智能检测医保目录用药限制，如不符合机构等级/人群性别/诊断病种等
                                            </abc-text>
                                        </abc-flex>
                                    </abc-flex>
                                </abc-tooltip-info>
                            </abc-flex>
                            <abc-flex vertical :gap="10">
                                <abc-flex vertical :gap="6">
                                    <div
                                        v-for="(item, index) in allVerifyItems"
                                        :key="index"
                                        :class="['retail-verify-item', { 'with-social-button': shebaoRestrictItems.length }]"
                                    >
                                        <abc-flex
                                            gap="6"
                                            align="center"
                                            :class="{
                                                pass: item.level === 'PASS',
                                                warn: item.level === 'WARN',
                                                danger: item.level === 'DANGER',
                                                done: !!item.isDeal,
                                            }"
                                        >
                                            <template v-if="item.level === 'PASS'">
                                                <abc-icon icon="s-shield-fill" size="14px"></abc-icon>
                                                <span>无风险</span>
                                            </template>
                                            <template v-else-if="item.level === 'DANGER'">
                                                <abc-icon icon="s-compliance-fill" size="14px"></abc-icon>
                                                <span>风险</span>
                                            </template>
                                            <template v-else>
                                                <abc-icon icon="s-compliance-fill" size="14px"></abc-icon>
                                                <span>提醒</span>
                                            </template>
                                        </abc-flex>

                                        <abc-flex
                                            wrap="wrap"
                                            :class="[
                                                'abstract',
                                                {
                                                    pass: item.level === 'PASS',
                                                    warn: item.level === 'WARN',
                                                    danger: item.level === 'DANGER',
                                                    done: item.isDeal,
                                                },
                                            ]"
                                        >
                                            <span>{{ item.verifyResult || '' }}</span>
                                        </abc-flex>

                                        <abc-select
                                            v-if="item.isShebaoVerify"
                                            v-model="item.shebaoPayMode"
                                            reference-mode="text"
                                            reference-text-justify="end"
                                            :width="82"
                                            size="tiny"
                                            custom-class="select-pay-mode-options"
                                            @change="onShebaoPayModeChange(item)"
                                        >
                                            <abc-option :value="ShebaoPayMode.OVERALL" label="使用医保"></abc-option>
                                            <abc-option :value="ShebaoPayMode.NO_USE" label="不刷医保"></abc-option>
                                        </abc-select>
                                    </div>

                                    <abc-divider
                                        v-if="shebaoRestrictItems.length"
                                        variant="dashed"
                                        margin="none"
                                    ></abc-divider>
                                </abc-flex>
                                <abc-flex
                                    v-if="shebaoRestrictItems && shebaoRestrictItems.length"
                                    justify="end"
                                >
                                    <abc-dropdown custom-class="select-pay-mode-options" @change="onShebaoPayModeChangeAll">
                                        <abc-button
                                            slot="reference"
                                            variant="text"
                                        >
                                            批量操作
                                        </abc-button>
                                        <abc-dropdown-item :value="ShebaoPayMode.OVERALL" label="使用医保"></abc-dropdown-item>
                                        <abc-dropdown-item :value="ShebaoPayMode.NO_USE" label="不刷医保"></abc-dropdown-item>
                                    </abc-dropdown>
                                </abc-flex>
                            </abc-flex>
                        </div>
                    </abc-popover>
                </abc-flex>
            </abc-flex>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import { ShebaoPayTypeByModeEnum } from 'views/outpatient/constants';
    import { ShebaoPayMode } from 'views/inventory/goods/archives/components/social-code-autocomplete/constant';
    import { MFE_ROUTER_NAME } from 'abc-micro-frontend';
    import ModulePermission from 'views/permission/module-permission';

    export default {
        name: 'RetailVerify',
        mixins: [
            ModulePermission,
        ],
        props: {
            verifyOutpatient: {
                validator: (prop) => typeof prop === 'object' || prop === null,
                required: true,
            },
            // 门诊提交的postData
            postData: {
                type: Object,
                required: true,
            },
        },
        data() {
            return {
                ShebaoPayMode,
                interactions: [],
                verifyItems: [],

                signedMedicineMap: new Map(),

                regulation: [],
                fitness: [],
                showMore: false,
                popoverStyle: {},
                popperPosition: 0,
            };
        },
        computed: {
            ...mapGetters('shebaoRestrict', ['restrictSwitch']),
            checkItems() {
                if (!this.verifyOutpatient) return [];
                return this.verifyOutpatient.checkItems;
            },
            shebaoRestrictItems() {
                if (!this.restrictSwitch || !this.verifyOutpatient) return [];
                const {
                    medicateVerifyLists = [],
                    behaviorVerifyLists = [],
                } = this.verifyOutpatient;
                return medicateVerifyLists.concat(behaviorVerifyLists).reduce((pre, cur) => {
                    if (cur?.verifyDetails) {
                        cur.verifyDetails.forEach((item) => {
                            if (item.level === 'DANGER' || item.level === 'WARN') {
                                pre.push({
                                    ...item,
                                    dealType: cur.dealType,
                                });
                            }
                        });
                    }
                    return pre;
                }, []);
            },
            abstract() {
                let arrDanger = [];
                let arrWarn = [];
                let str = '';
                let arr = [];
                this.fitness.concat(this.regulation).forEach((item) => {
                    if (item.level === 'DANGER') {
                        if (item.name === 'MedicineInteractionRule') {
                            arrDanger = arrDanger.concat(this.interactions);
                        } else {
                            arrDanger.push({ reason: item.detail });
                        }
                    } else if (item.level === 'WARN') {
                        if (item.name === 'MedicineInteractionRule') {
                            arrWarn = arrWarn.concat(this.interactions);
                        } else {
                            arrWarn.push({ reason: item.detail });
                        }
                    }
                });
                arr = [...arr, ...arrDanger, ...arrWarn];
                if (arr.length) {
                    const it = arr[ 0 ];
                    if (it.influence) {
                        it.influence = it.influence.replace(/{A}/g, `${it.medicineA} `);
                        it.influence = it.influence.replace(/{B}/g, ` ${it.medicineB} `);
                        str = it.influence;
                    } else {
                        str = it.reason;
                    }
                }
                return str || '';
            },

            verifyStatus() {
                const dangerArr = []; const warnArr = [];

                this.regulation.forEach((item) => {
                    if (item.level === 'DANGER') {
                        dangerArr.push(item);
                    } else if (item.level === 'WARN') {
                        warnArr.push(item);
                    }
                });
                this.fitness.forEach((item) => {
                    if (item.level === 'DANGER') {
                        dangerArr.push(item);
                    } else if (item.level === 'WARN') {
                        warnArr.push(item);
                    }
                });
                this.shebaoRestrictItems.forEach((item) => {
                    if (item.level === 'DANGER') {
                        dangerArr.push(item);
                    } else if (item.level === 'WARN') {
                        warnArr.push(item);
                    }
                });
                if (dangerArr.length) {
                    return 'DANGER';
                }
                if (warnArr.length) {
                    return 'WARN';
                }
                return 'PASS';
            },

            allVerifyItems() {
                const checkItems = this.checkItems.map((item) => ({
                    ...item,
                    verifyResult: item.detail.reason?.replaceAll('中西成药处方一：', '') || '',
                }));
                if (this.restrictSwitch) {
                    checkItems.push(...this.shebaoRestrictItems.map((item) => ({
                        ...item,
                        verifyResult: item.hint || '',
                        isShebaoVerify: true,
                    })));
                }
                return checkItems;
            },
            firstCheckItems() {
                return this.allVerifyItems && this.allVerifyItems[0];
            },
        },
        watch: {
            verifyOutpatient: {
                handler(val) {
                    this.initVerifyResults(val);
                    const timer = setTimeout(() => {
                        this.calcPopoverWidth();
                        clearTimeout(timer);
                    }, 300);
                },
                immediate: true,
            },
        },

        methods: {
            initVerifyResults(val) {
                if (!val) return;
                const { verifyItems = {} } = val || {};

                const {
                    FITNESS,
                    REGULATION,
                } = verifyItems || {};
                this.initRegulation(REGULATION);
                this.initFitness(FITNESS);
            },
            calcPopoverWidth() {
                const showMoreBtn = document.querySelector('.show-more-btn');
                if (!showMoreBtn) return;
                const width = showMoreBtn.offsetWidth + showMoreBtn.offsetLeft - 8;
                this.popoverStyle = { width: `${width}px` };
            },

            initRegulation(regulation) {
                this.regulation = regulation || [];
            },
            initFitness(fitness) {
                this.fitness = fitness?.filter((item) => item.name !== 'ControlledSubstancesRule' && !item.checkItems?.length) || [];
                this.interactions = [];
                this.fitness = this.fitness.map((item) => {
                    if (item.name === 'MedicineInteractionRule' && item.detail) {
                        try {
                            if (typeof item.detail === 'string') {
                                const detail = JSON.parse(item.detail) || {};
                                detail.chinese = detail.chinese || [];
                                detail.western = detail.western || [];
                                this.interactions = detail.chinese.concat(detail.western);
                            }
                        } catch (e) {
                            console.error(e);
                        }
                    }
                    return item;
                });
            },
            onShebaoPayModeChange(it) {
                const payMode = it.shebaoPayMode;
                const prescriptionFormDetails = (it.prescriptionFormDetails || [])[0];
                this.postData.chargeForms.forEach((form) => {
                    if (form.keyId === prescriptionFormDetails?.prescriptionFormId) {
                        form.chargeFormItems.forEach((item) => {
                            if (item.keyId === prescriptionFormDetails?.prescriptionFormItemId) {
                                this.$set(item, 'usageInfo', {
                                    ...(item.usageInfo || {}),
                                    payType: ShebaoPayTypeByModeEnum[payMode],
                                });
                            }
                        });
                    }
                });
                this.verifyOutpatient.medicateVerifyLists.forEach((list) => {
                    list.verifyDetails && list.verifyDetails.forEach((item) => {
                        const { prescriptionFormItemId } = item.prescriptionFormDetails?.[0] || {};
                        if (prescriptionFormItemId === prescriptionFormDetails?.prescriptionFormItemId) {
                            item.shebaoPayMode = payMode;
                            item.isDeal = item.shebaoPayMode === ShebaoPayMode.NO_USE;
                        }
                    });
                });
                this.verifyOutpatient.behaviorVerifyLists.forEach((list) => {
                    list.verifyDetails && list.verifyDetails.forEach((item) => {
                        const { prescriptionFormItemId } = item.prescriptionFormDetails?.[0] || {};
                        if (prescriptionFormItemId === prescriptionFormDetails?.prescriptionFormItemId) {
                            item.shebaoPayMode = payMode;
                            item.isDeal = item.shebaoPayMode === ShebaoPayMode.NO_USE;
                        }
                    });
                });
            },
            onShebaoPayModeChangeAll(payMode) {
                this.verifyOutpatient.medicateVerifyLists?.forEach((list) => {
                    list.verifyDetails && list.verifyDetails.forEach((item) => {
                        item.shebaoPayMode = payMode;
                        item.isDeal = item.shebaoPayMode === ShebaoPayMode.NO_USE;
                    });
                });
                this.verifyOutpatient.behaviorVerifyLists?.forEach((list) => {
                    list.verifyDetails && list.verifyDetails.forEach((item) => {
                        item.shebaoPayMode = payMode;
                        item.isDeal = item.shebaoPayMode === ShebaoPayMode.NO_USE;
                    });
                });
                const shebaoList = (this.verifyOutpatient.medicateVerifyLists || []).concat(this.verifyOutpatient.behaviorVerifyLists || []).reduce((pre, cur) => {
                    if (cur.verifyDetails) pre.push(...cur.verifyDetails);
                    return pre;
                }, []);
                this.postData.chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        const target = shebaoList.find((one) => one.prescriptionFormDetails[0]?.prescriptionFormItemId === item.keyId);
                        if (target) {
                            this.$set(item, 'usageInfo', {
                                ...(item.usageInfo || {}),
                                payType: ShebaoPayTypeByModeEnum[payMode],
                            });
                        }
                    });
                });
            },
            async toOpenSocialAssistant() {
                if (this.hasSocialModule) {
                    const moduleRoute = this.$router.options.routes.find((route) => route.name === MFE_ROUTER_NAME);
                    const mfeBasePath = moduleRoute?.meta?.mfeBasePath || '/';
                    const modulePath = `social${this.$abcSocialSecurity.isEnableSocial ? '/compliance-risk-control/content' : ''}`;
                    const toPath = `${mfeBasePath}${modulePath}`;
                    this.$router.push(toPath, null, () => {
                        const route = this.$router.match(toPath);
                        this.$abcPlatform.notifyPlatformRouterChange(route);
                    });
                }
            },
            onClickOutside(e) {
                if (this.$refs.reference.contains(e.target)) {
                    return;
                }
                if (e && e.path) {
                    const isInnerEl = e.path.some(
                        (el) => {
                            return el.classList && (
                                Array.from(el.classList).includes('retail-verify-item-popover') ||
                                Array.from(el.classList).includes('select-pay-mode-options') ||
                                Array.from(el.classList).includes('retail-verify-tip-popover')
                            );
                        },
                    );
                    if (isInnerEl) {
                        return;
                    }
                }
                this.showMore = false;
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .retail-verify-wrapper {
        position: relative;
        width: 100%;
        background-color: #ffffff;
        border-radius: var(--abc-border-radius-small);
    }

    .retail-verify-popover {
        .retail-verify-item-popover {
            display: grid;
            flex: 1;
            grid-template-columns: 92px 1fr;
            gap: 24px;
            align-items: baseline;
            padding-left: 10px;

            .retail-verify-item {
                display: grid;
                grid-template-columns: 60px 1fr;

                &.with-social-button {
                    grid-template-columns: 60px 1fr 82px;
                }
            }
        }
    }

    .retail-verify-wrapper,
    .retail-verify-popover {
        .abstract {
            line-height: 26px;
        }

        .pass {
            color: var(--abc-color-G2);
        }

        .warn {
            color: var(--abc-color-Y2);
        }

        .danger {
            color: #ff3333;

            .iconfont {
                color: #ff3333;
            }
        }

        .done {
            color: var(--abc-color-T3);

            .cis-icon-Attention {
                color: var(--abc-color-T3);
            }

            span {
                text-decoration: line-through;
            }
        }
    }

    .retail-verify-tip-popover {
        padding: 8px 6px;
        text-align: justify;
    }
</style>
