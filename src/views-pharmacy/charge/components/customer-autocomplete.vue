<template>
    <abc-flex class="customer-autocomplete-wrapper" gap="4">
        <abc-popover
            v-if="patient && patient.id"
            ref="customerPopover"
            :value="showCustomerPopover"
            :visible-arrow="false"
            class="customer-autocomplete-popover"
            theme="white"
            trigger="manual"
            :disabled="disabledPopover"
            :popper-style="{
                padding: 0
            }"
        >
            <div
                slot="reference"
                class="customer-section"
                :class="[
                    `customer-section--${size}`,
                    {
                        'is-active': showCustomerPopover
                    }
                ]"
                :style="{
                    minWidth: `${width }px`
                }"
                @click="handleClickSection"
                @mouseenter="handleMouseEnter"
            >
                <abc-space>
                    <abc-avatar
                        class="img-wrapper"
                        :border-visible="false"
                        :is-male="patient.sex === '男'"
                    ></abc-avatar>
                    <div class="name">
                        {{ patient.name }}
                    </div>
                    <div class="mobile">
                        {{ (patient.mobile || '').substring(7) }}
                    </div>
                    <abc-tag-v2
                        v-if="customerMemberTypeStr"
                        variant="light-outline"
                        shape="square"
                        size="mini"
                        theme="warning"
                    >
                        {{ customerMemberTypeStr }}
                    </abc-tag-v2>
                </abc-space>

                <abc-space v-if="showBalance || showPoints" style="margin-left: 40px;">
                    <span v-if="showBalance">余额 {{ balanceTotal | formatMoney }}</span>
                    <span v-if="showPoints && pointsStr">积分 {{ pointsStr }}</span>
                </abc-space>

                <abc-flex
                    v-if="!disabled"
                    class="delete-icon-wrapper"
                    align="center"
                    justify="center"
                >
                    <abc-delete-icon data-cy="删除" theme="dark" @delete="clearPatient"></abc-delete-icon>
                </abc-flex>
            </div>

            <customer-section-popover
                v-show="showCustomerPopover"
                ref="customerSection"
                v-abc-click-outside="handleClosePopover"
                :patient-info="patientInfo"
                @mouseleave.native="handleClosePopover"
                @update-customer="handleUpdateCustomer"
                @update-promotion="handleChangePromotion"
                @get-customer-extend-info="handleCustomerExtendInfo"
            ></customer-section-popover>
        </abc-popover>
        <abc-autocomplete
            v-else
            ref="customerAutocomplete"
            v-model.trim="keyword"
            :disabled="disabled"
            :inner-width="customInnerWidth"
            :width="width"
            :delay-time="0"
            :size="size"
            :placeholder="placeholder"
            :focus-placeholder="focusPlaceholder"
            :readonly="readonly"
            async-fetch
            placement="top-start"
            :focus-show="focusShow"
            :fetch-suggestions="querySearchAsync"
            custom-class="customer-autocomplete-suggestions-popper"
            clearable
            @focus="handleFocus"
            @blur="handleBlur"
            @enter="
                (event) => {
                    $emit('enter', event);
                }
            "
            @enterEvent="selectCustomer"
            @clear="clearPatient"
        >
            <template #prepend>
                <abc-icon icon="n-user-line"></abc-icon>
            </template>

            <template #suggestion-header>
                <abc-flex class="suggestion-title" align="center">
                    <div class="customer-info" style="flex: 1;">
                        会员信息
                    </div>
                    <div class="customer-mobile" style="flex: 1;">
                        联系电话
                    </div>
                    <div class="member-info" style="flex: 1;">
                        会员等级
                    </div>
                    <div class="birth-day" style="width: 88px;">
                        生日
                    </div>
                    <div v-if="!isSingleStore" class="created-clinic" style="width: 138px;">
                        创建门店
                    </div>
                </abc-flex>
            </template>

            <template #suggestions="props">
                <dt
                    class="patient-suggestions suggestions-item"
                    :class="{ selected: props.index === props.currentIndex }"
                    @mousedown="selectCustomer(props.suggestion)"
                >
                    <div class="customer-info" style="flex: 1;">
                        {{ props.suggestion.name }}
                    </div>
                    <div class="customer-mobile" style="flex: 1;">
                        <template v-if="isCanSeePatientMobileInCashier">
                            {{ props.suggestion.mobile }}
                        </template>
                        <template v-else>
                            {{ props.suggestion.mobile | encryptMobile }}
                        </template>
                    </div>
                    <div class="member-info" style="flex: 1;">
                        {{ getMemberTypeStr(props.suggestion) }}
                    </div>
                    <div class="birth-day" style="width: 88px;">
                        {{ props.suggestion.birthday }}
                    </div>
                    <div
                        v-if="!isSingleStore"
                        class="created-clinic"
                        :title="props.suggestion.createdClinicName"
                        style="width: 138px; padding-left: 6px;"
                    >
                        {{ props.suggestion.createdClinicName }}
                    </div>
                </dt>
            </template>

            <template v-if="showF2" #appendInner>
                F2
            </template>>
        </abc-autocomplete>
    </abc-flex>
</template>

<script>
    import PatientsAPI from 'api/patients';
    import CrmAPI from 'api/crm';
    import { getCustomerInfo } from '@/views-pharmacy/charge/utils';
    import { debounce } from 'utils/lodash';
    import AbcAvatar from 'views/layout/abc-avatar/index.vue';
    import { mapGetters } from 'vuex';

    export default {
        name: 'CustomerAutocomplete',
        components: {
            AbcAvatar,
            CustomerSectionPopover: () => import('./customer-section-popover.vue'),
        },

        props: {
            patient: {
                type: Object,
                required: true,
            },
            size: {
                type: String,
                validator: (value) => ['large', 'medium', ''].indexOf(value) > -1,
            },
            readonly: {
                type: Boolean,
                default: false,
            },
            width: {
                type: Number,
            },
            disabled: Boolean,
            disabledPopover: Boolean,
            isShowEmpty: {
                type: Boolean,
                default: true,
            },
            focusShow: {
                type: Boolean,
                default: false,
            },
            canEdit: {
                type: Boolean,
                default: true,
            },
            showBalance: {
                type: Boolean,
                default: true,
            },
            showPoints: {
                type: Boolean,
                default: true,
            },
            placeholder: {
                type: String,
                default: '会员姓名 / 手机号后4位',
            },
            focusPlaceholder: {
                type: String,
                default: '会员姓名 / 手机号后4位',
            },
            showF2: {
                type: Boolean,
                default: false,
            },
        },

        data() {
            return {
                loading: true,
                keyword: '',
                isFocus: false,
                showCustomerPopover: false,
                patientInfo: {},
                shebaoCardInfo: null,
            };
        },

        computed: {
            ...mapGetters([
                'isCanSeePatientMobileInCashier',
            ]),
            ...mapGetters([
                'isSingleStore',
            ]),
            customInnerWidth() {
                return this.isSingleStore ? 480 : 580;
            },
            customerMemberTypeStr() {
                const {
                    memberTypeInfo,
                } = this.patientInfo?.memberInfo || {};
                return memberTypeInfo?.memberTypeName || memberTypeInfo?.name || '';
            },

            pointsStr() {
                return this.patientInfo?.points || '';
            },

            balanceTotal() {
                const {
                    principal = 0,
                    present = 0,
                } = this.patientInfo?.memberInfo || {};
                return principal + present || 0;
            },
        },

        watch: {
            'patient.id': function() {
                this._fetchPatientOverview();
                // 患者修改需要清空 shebaoCardInfo
                this.shebaoCardInfo = null;
            },
        },
        created() {
            this._fetchPatientOverview = debounce(this.fetchPatientOverview, 50, true);
            this._fetchPatientOverview();
        },
        methods: {
            handleCustomerExtendInfo(data) {
                this.$emit('get-customer-extend-info', data);
            },
            handleClickSection() {
                this.showCustomerPopover = true;
            },
            handleMouseEnter() {
                this._timer = setTimeout(() => {
                    this.showCustomerPopover = true;
                }, 150);
            },
            handleClosePopover() {
                const $customerSection = this.$refs.customerSection;
                const hasOpenDialog = $customerSection?.getDialogOpenStatus();
                if (hasOpenDialog) return;
                this.showCustomerPopover = false;
                clearTimeout(this._timer);
            },
            getMemberTypeStr(item) {
                const {
                    memberInfo,
                } = item;
                const {
                    memberTypeInfo,
                } = memberInfo || {};
                return memberTypeInfo?.memberTypeName || '';
            },
            focusInput() {
                this.$refs.customerAutocomplete?.focus();
            },
            handleFocus(e) {
                this.isFocus = true;
                this.$emit('focus', e);
            },
            handleBlur(e) {
                this.isFocus = false;
                this.$emit('blur', e);
            },
            /**
             * @desc 输入时根据输入内容 异步查询患者数据
             * <AUTHOR>
             * @date 2018/10/18 12:36:51
             * @param queryString
             * @param callback
             */
            async querySearchAsync(queryString, callback) {
                this.loading = true;
                queryString = queryString.trim();

                if (!queryString && !this.focusShow) {
                    callback([]);
                    this.loading = false;
                    return false;
                }

                try {
                    const res = await PatientsAPI.fetchPatientsByName(queryString);
                    const data = res?.data;

                    if ((queryString && (data.keyword || data.name) !== queryString) || !data) {
                        callback([]);
                        this.loading = false;
                        return false;
                    }

                    const list = (data.list || data.rows);
                    callback(
                        list,
                    );
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },

            async selectCustomer(customer) {
                this.keyword = '';
                const data = getCustomerInfo(customer);
                this.$emit('select', data);
            },

            async fetchPatientOverview() {
                const patientId = this.patient.id;
                if (!patientId) return false;
                const { data } = await CrmAPI.fetchPatientOverviewV2(patientId, {
                    wx: 1,
                });
                if (patientId !== this.patient.id) return;
                this.patientInfo = data;
                this.handleCustomerExtendInfo({
                    balanceTotal: this.balanceTotal,
                    points: this.pointsStr,
                });
                this.handleChangePromotion();
            },

            handleChangePromotion() {
                this.$emit('update-customer-promotion');
            },
            async handleUpdateCustomer(data) {
                if (data) {
                    Object.assign(this.patientInfo, data);
                    this.$emit('update-customer', data);
                } else {
                    await this.fetchPatientOverview();
                    this.$emit('update-customer', this.patientInfo);
                }
            },

            async clearPatient() {
                this.selectCustomer(null);
            },
        },
    };
</script>

<style lang="scss">
    .customer-autocomplete-wrapper {
        position: relative;

        .empty-alert {
            position: absolute;
            top: 44px;
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 24px 0;
            background-color: $S2;
            border: 1px solid var(--abc-color-P7);
            border-radius: var(--abc-border-radius-small);
            box-shadow: var(--abc-shadow-1);

            .search-empty {
                font-size: 14px;
                color: $T3;
            }
        }

        .customer-section {
            position: relative;
            display: inline-flex;
            flex-shrink: 0;
            align-items: center;
            justify-content: space-between;
            height: 32px;
            padding: 0 32px 0 8px;
            cursor: pointer;
            background: var(--abc-color-cp-white);
            border: 1px solid $P7;
            border-radius: var(--abc-border-radius-small);

            &.customer-section--large,
            &.customer-section--medium, {
                height: 40px;
            }

            &.is-active {
                border-color: $theme3;
                box-shadow: 0 0 0 2px #c6e2ff;
            }

            .delete-icon-wrapper {
                position: absolute;
                right: 0;
                width: 32px;
                height: 40px;
                visibility: visible;
            }
        }
    }

    .customer-autocomplete-suggestions-popper {
        .suggestion-title {
            display: flex;
            align-items: center;
            padding: 0 12px;

            .patient-info {
                width: 300px;
            }
        }
    }
</style>
