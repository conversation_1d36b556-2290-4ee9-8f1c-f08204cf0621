<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        class="sales-record-detail-dialog"
        data-cy="sales-record-detail-dialog"
        :auto-focus="false"
        content-styles="width: 1200px; height: 600px;padding: 0;"
        :title="title"
        @close="$emit('close')"
    >
        <template #title-append>
            <img
                v-if="chargeStatus >= ChargeStatusEnum.CHARGED && chargeStatus < ChargeStatusEnum.REFUND"
                class="charge-seal"
                src="~assets/images/Paid.png"
                alt=""
            />
            <img
                v-if="chargeStatus === ChargeStatusEnum.REFUND"
                class="charge-seal"
                src="~assets/images/icon-money-back.png"
                alt=""
            />
            <img
                v-if="chargeStatus === ChargeStatusEnum.CLOSED"
                class="charge-seal"
                src="~assets/images/icon/<EMAIL>"
                alt=""
            />
        </template>
        <abc-flex v-abc-loading="contentLoading" class="dialog-content clearfix">
            <div class="left-content">
                <abc-tips-card-v2
                    v-if="lockedInfo"
                    style="margin-bottom: 16px;"
                    theme="warning"
                    align="center"
                >
                    {{ lockedTips }}
                    <template v-if="showCancelPay" #operate>
                        <abc-button
                            variant="text"
                            size="small"
                            @click="onClickCancelPay"
                        >
                            支付遇到问题？
                        </abc-button>
                    </template>
                </abc-tips-card-v2>
                <customer-autocomplete
                    v-if="hasPatient"
                    :patient="chargeSheetDetail.patient"
                    :member-info="chargeSheetDetail.memberInfo"
                    disabled
                    :disabled-popover="disabled"
                    :width="244"
                    style="margin-bottom: 16px;"
                ></customer-autocomplete>

                <abc-table
                    show-order
                    :custom-order-render="customOrderRender"
                    cell-size="large"
                    :need-min-height="false"
                    :render-config="renderConfig"
                    :data-list="chargeFormItems"
                    :custom-tr-class="customTrClass"
                    style="max-height: 100%;"
                >
                    <template v-if="cooperationDetail" #topHeader>
                        <cooperation-detail :value="cooperationDetail" :charge-sheet-id="chargeSheetId"></cooperation-detail>
                    </template>
                    <template #goodsInfo="{ trData }">
                        <abc-table-cell>
                            <goods-filed
                                v-if="trData.productInfo"
                                :disabled="trData.status === ChargeItemStatusEnum.REFUND"
                                style="flex: 1; width: 0;"
                                :goods="trData.productInfo"
                                :show-cost-price="isCanSeeGoodsCostPriceInRetail"
                            ></goods-filed>
                            <abc-tag-v2
                                v-if="trData.status === ChargeItemStatusEnum.REFUND"
                                theme="danger"
                                size="small"
                                variant="outline"
                            >
                                已退费
                            </abc-tag-v2>
                        </abc-table-cell>
                    </template>
                    <template #batchesInfo="{ trData }">
                        <abc-table-cell>
                            <batches-infos
                                :disabled="trData.status === ChargeItemStatusEnum.REFUND"
                                :batch-styles="{
                                    width: 'auto', maxWidth: '109px',
                                }"
                                readonly
                                :charge-item="trData"
                            ></batches-infos>
                        </abc-table-cell>
                    </template>
                    <template #unitCount="{ trData }">
                        <abc-table-cell>
                            <div>
                                <abc-text style="display: block; width: 100%; line-height: 22px;">
                                    {{ `${trData.unitCount * (trData.doseCount || 1)}` }}<span style="display: inline-block; width: 22px; text-align: right;">{{ trData.unit }}</span>
                                </abc-text>
                                <abc-text
                                    v-if="isChineseGoods(trData) && !trData.isGift"
                                    style="display: block; width: 100%; line-height: 16px;"
                                    size="mini"
                                    theme="gray"
                                >
                                    {{ trData.unitCount }}{{ trData.unit }} × {{ trData.doseCount }}剂
                                </abc-text>
                            </div>
                        </abc-table-cell>
                    </template>
                </abc-table>

                <discount-list
                    v-if="showDiscountList"
                    class="discount-list"
                    disabled
                    :charge-sheet-id="chargeSheetId"
                    :charge-forms="chargeSheetDetail.chargeForms"
                    :gift-rule-promotions.sync="chargeSheetDetail.giftRulePromotions"
                    :coupon-promotions.sync="chargeSheetDetail.couponPromotions"
                    :patient-points-info.sync="chargeSheetDetail.patientPointsInfo"
                    :summary="chargeSheetSummary"
                ></discount-list>
            </div>
            <div class="right-content">
                <cashier-summary
                    :charge-id="chargeSheetId"
                    :status="chargeStatus"
                    :patient="chargeSheetDetail.patient"
                    :invoice-status="chargeSheetDetail.invoiceStatus"
                    :invoice-data="chargeSheetDetail.invoice"
                    :member-id="chargeSheetDetail.memberId"
                    :summary="chargeSheetSummary"
                    :pharmacist-name="chargeSheetDetail.pharmacistName"
                    :seller-name="chargeSheetDetail.sellerName"
                    :actions.sync="chargeSheetDetail.chargeActions"
                    :transactions.sync="chargeSheetDetail.chargeTransactions"
                    :is-disabled-operate="chargeSheetDetail.isDisabledOperate || +detailIsDisabled || +disabled"
                    :disabled-operate-reason="chargeSheetDetail.disabledOperateReason"
                    :invoice-list="invoiceList"
                    discount-fee-key="discountTotalFee"
                    :show-adjustment-fee="false"
                    :show-unit-adjustment-fee="false"
                    show-remarks
                    :remarks="chargeSheetDetail.remarks"
                    adjustment-text="议价"
                    :change-pay-mode-records="chargeSheetDetail.changePayModeRecords"
                    @pay-mode-change-success="fetchChargeDetail"
                    @update-remarks="handleUpdateRemarks"
                ></cashier-summary>
            </div>
        </abc-flex>

        <div slot="footer" class="dialog-footer">
            <abc-flex v-if="detailIsDisabled" justify="space-between" style="width: 100%;">
                <div></div>
                <abc-space>
                    <abc-check-access v-if="chargeStatus === ChargeStatusEnum.CLOSED">
                        <abc-button
                            data-cy="recharge-button"
                            variant="ghost"
                            @click="handleRecharge(chargeStatus)"
                        >
                            重新收费
                        </abc-button>
                    </abc-check-access>
                    <abc-button variant="ghost" @click="showDialog = false">
                        关闭
                    </abc-button>
                </abc-space>
            </abc-flex>
            <abc-flex v-else justify="space-between" style="width: 100%;">
                <abc-space>
                    <abc-button data-cy="real-name-regis-button" variant="ghost" @click="showRealNameRegisDialog = true">
                        实名登记
                    </abc-button>
                    <abc-button data-cy="prescription-regis-button" variant="ghost" @click="showPrescriptionRegisDialog = true">
                        处方登记
                    </abc-button>
                    <abc-tooltip v-if="traceCodeCollectionCheck" content="本单项目均无需采集追溯码" :disabled="needTraceCodeFormItems.length !== 0">
                        <div>
                            <abc-button
                                variant="ghost"
                                data-cy="trace-code-button"
                                :disabled="needTraceCodeFormItems.length === 0 || !!lockedInfo"
                                @click="handleOpenTraceCodeDialog"
                            >
                                追溯码
                            </abc-button>
                        </div>
                    </abc-tooltip>
                </abc-space>
                <abc-space v-if="!contentLoading">
                    <template v-if="chargeStatus === ChargeStatusEnum.UN_CHARGE">
                        <abc-check-access>
                            <abc-button :disabled="!!lockedInfo" @click="continuePay">
                                结账
                            </abc-button>
                        </abc-check-access>
                        <abc-check-access>
                            <abc-button
                                v-if="canEdit"
                                :disabled="!!lockedInfo"
                                variant="ghost"
                                @click="handleEdit(chargeStatus)"
                            >
                                修改
                            </abc-button>
                        </abc-check-access>
                        <abc-button :disabled="!!lockedInfo" variant="ghost" @click="closeOrderHandle">
                            关闭收费单
                        </abc-button>
                    </template>
                    <template v-else-if="chargeStatus === ChargeStatusEnum.PART_CHARGED">
                        <abc-check-access v-if="canContinuePay">
                            <abc-button :disabled="!!lockedInfo" @click="continuePay">
                                继续收费
                            </abc-button>
                        </abc-check-access>
                        <abc-tooltip
                            v-if="chargeSheetSummary.netIncomeFee"
                            :content="chargeSheetDetail.disabledRefundReason"
                            :disabled="!chargeSheetDetail.disabledRefundReason"
                        >
                            <div>
                                <abc-check-access>
                                    <abc-button
                                        data-cy="refund-button"
                                        :disabled="refundDisabled"
                                        variant="ghost"
                                        @click="refundFee"
                                    >
                                        退货
                                    </abc-button>
                                </abc-check-access>
                            </div>
                        </abc-tooltip>
                    </template>
                    <template v-else>
                        <abc-tooltip
                            v-if="chargeStatus !== ChargeStatusEnum.REFUND"
                            :content="chargeSheetDetail.disabledRefundReason"
                            :disabled="!chargeSheetDetail.disabledRefundReason"
                        >
                            <div>
                                <abc-check-access>
                                    <abc-button
                                        data-cy="refund-button"
                                        :disabled="refundDisabled"
                                        variant="ghost"
                                        @click="showRefundDialog = true"
                                    >
                                        退货
                                    </abc-button>
                                </abc-check-access>
                            </div>
                        </abc-tooltip>
                    </template>

                    <abc-check-access v-if="chargeStatus === ChargeStatusEnum.REFUND">
                        <abc-button
                            data-cy="recharge-button"
                            variant="ghost"
                            @click="handleRecharge(chargeStatus)"
                        >
                            重新收费
                        </abc-button>
                    </abc-check-access>

                    <abc-button
                        v-if="showOpenInvoiceBtn"
                        variant="ghost"
                        :disabled="!!chargeSheetDetail.isDisabledOperate || !!lockedInfo"
                        @click="openInvoiceDialog"
                    >
                        开票
                    </abc-button>
                    <print-popper
                        style="margin: 0;"
                        size="small"
                        :width="64"
                        placement="top"
                        :box-style="{ width: '124px' }"
                        :options="printOptions"
                        @print="printHandler"
                        @select-print-setting="openPrintConfigSettingDialog"
                    >
                        <template #label="{ option }">
                            <abc-tooltip
                                :disabled="!option.tooTips"
                                :content="option.tooTips"
                                :z-index="10000"
                            >
                                <span>
                                    {{ option.value === '药店收费小票' ? '收费小票' : option.value === '药店退费小票' ? '退费小票' : option.value }}
                                </span>
                            </abc-tooltip>
                        </template>
                    </print-popper>
                </abc-space>
            </abc-flex>
        </div>
        <div>
            <!--这个div千万不能删除，原因咨询jason-->
        </div>
        <refund-dialog
            v-if="showRefundDialog"
            :id="chargeSheetId"
            ref="refundPro"
            v-model="showRefundDialog"
            :member-id="chargeSheetDetail.memberId"
            :patient-id="chargeSheetDetail.patient.id"
            :forms="chargeSheetDetail.chargeForms"
            :package-discount-total-fee="chargeSheetSummary.packageDiscountTotalFee"
            :net-income-fee="chargeSheetSummary.netIncomeFee"
            :owed-refund-fee="chargeSheetSummary.owedRefundFee"
            :adjustment-fee="chargeSheetSummary.adjustmentFee"
            :net-adjustment-fee="chargeSheetSummary.netAdjustmentFee"
            :refund-auto-destroy="writeInvoiceConfig.refundAutoDestroy"
            @confirm="refundConfirm"
        ></refund-dialog>

        <div>
            <!--这个div千万不能删除，原因咨询jason-->
        </div>

        <refund-way-dialog
            v-if="showRefundWayList"
            ref="refundWay"
            v-model="showRefundWayList"
            :patient-name="chargeSheetDetail.patient.name"
            :charge-sheet-id="chargeSheetId"
            :refund-fee="refundTotalFee"
            :refund-data="refundData"
            :receivable-fee="chargeSheetSummary.receivableFee"
            :net-income-fee="chargeSheetSummary.netIncomeFee"
            :refund-type="curRefundType"
            :payment-summary-infos.sync="chargeSheetSummary.paymentSummaryInfos"
            :charge-transactions="chargeSheetDetail.chargeTransactions"
            :charge-config="chargeConfig"
            :can-refund-shebao="refundData.canRefundShebao"
            @refund="refundHandler"
            @finish="refundFinish"
            @auto-destroy-invoice="autoDestroyInvoice"
            @re-open-audit="reOpenAuditFlow"
        ></refund-way-dialog>
        <div>
            <!--这个div千万不能删除，原因咨询jason-->
        </div>
        <real-name-registration-dialog
            v-if="showRealNameRegisDialog"
            v-model="showRealNameRegisDialog"
            :disabled="detailIsDisabled"
            :register-info-id="chargeSheetDetail.registerInfoId"
            :charge-sheet-id="chargeSheetDetail.id"
            :patient="PRRegistrationDefaultPatient"
            @success="fetchChargeDetail"
        ></real-name-registration-dialog>
        <prescription-registration-dialog
            v-if="showPrescriptionRegisDialog"
            v-model="showPrescriptionRegisDialog"
            :disabled="detailIsDisabled"
            :register-info-id="chargeSheetDetail.registerInfoId"
            :charge-sheet-id="chargeSheetDetail.id"
            :charge-sheet-type="chargeSheetDetail.type"
            :patient="PRRegistrationDefaultPatient"
            :cooperation-id="cooperationDetail && cooperationDetail.id"
            :is-prescription-out="isPrescriptionOut"
            @success="fetchChargeDetail"
        >
        </prescription-registration-dialog>
    </abc-dialog>
</template>

<script>
    import GoodsFiled from '@/views-pharmacy/charge/components/goods-filed.vue';
    import BatchesInfos from '@/views-pharmacy/charge/components/batches-infos.vue';
    import ChargeAPI from 'api/charge.js';
    import PrintAPI from 'api/print';
    const CashierSummary = () => import('views/cashier/sidebar/cashier-summary.vue');
    import {
        ChargeItemStatusEnum, ChargeSheetTypeEnum,
        ChargeStatusEnum,
        CreateCashierPostData,
        PayModeEnum,
        RefundTypeEnum,
        UseMemberFlagEnum,
    } from '@/service/charge/constants.js';

    import {
        InvoiceBusinessScene, InvoiceCategory, InvoiceSupplierId, InvoiceViewType,
    } from 'views/cashier/invoice/constants.js';
    import InvoiceService from 'views/cashier/invoice/write-invoice-core-v2/invoice-service.js';
    import InvoiceDialog from 'views/cashier/invoice/index.js';
    import {
        mapGetters,
    } from 'vuex';
    import { PharmacyChargeRouterNameKeys } from '@/views-pharmacy/charge/core/routes';
    import { autoDestroyInvoice } from 'views/cashier/invoice/utils.js';
    import AbcPrinter from '@/printer/index.js';
    import { getAbcPrintOptions } from '@/printer/print-handler.js';
    import QRCode from 'qrcode';
    import TpsAPI from 'api/tps.js';
    import AbcChargeDialog from '@/service/charge/components/dialog-charge/index.js';
    import DiscountList from '@/views-pharmacy/charge/components/discount/list.vue';
    import { formatMoney } from '@/filters';
    import CustomerAutocomplete from '@/views-pharmacy/charge/components/customer-autocomplete.vue';
    import { ANONYMOUS_ID } from '@abc/constants';

    const RefundDialog = () => import('./refund/refund-dialog.vue');
    const RefundWayDialog = () => import('views/cashier/refund-way-dialog.vue');
    const RealNameRegistrationDialog = () => import('./dialog-real-name-registration.vue');
    const PrescriptionRegistrationDialog = () => import('./dialog-prescription-registration.vue');
    import PrintPopper from 'views/print/popper';
    import { ABCPrintConfigKeyMap } from '@/printer/constants';
    import { TagV2 as AbcTagV2 } from '@abc/ui-pc';

    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import Printer from 'views/print';
    import CollectionTraceCodeDialog from '@/service/trace-code/dialog-collection-trace-code';
    import CooperationDetail from '@/views-pharmacy/charge/components/cooperation-detail.vue';
    import {
        getTraceCodeChargedItems,
    } from 'views/cashier/utils';
    import useChargeLock from '@/views/cashier/hooks/useChargeLock.js';
    import AbcSocket from 'views/common/single-socket';
    import { LockBusinessKeyEnum } from '@/common/constants/business-lock';
    import { debounce } from 'utils/lodash';
    import { navigateToInvoiceConfig } from '@/core/navigate-helper';
    import SocialAPI from 'api/social';
    import { CashierPrintApi } from '@/printer/print-api/cashier';
    import { getOrigin } from 'views/settings/micro-clinic/decoration/config';
    import ShortUrlAPI from 'api/short-url';
    import { GoodsTypeIdEnum } from '@abc/constants';
    import { SceneTypeEnum } from '@/service/trace-code/service';
    import PrintManager from '@/printer/manager/print-manager';
    export default {
        name: 'SalesRecordDetailDialog',
        components: {
            CustomerAutocomplete,
            DiscountList,
            GoodsFiled,
            BatchesInfos,
            CashierSummary,
            RefundDialog,
            RefundWayDialog,
            RealNameRegistrationDialog,
            PrescriptionRegistrationDialog,
            PrintPopper,
            CooperationDetail,
        },
        props: {
            value: Boolean,
            chargeSheetId: {
                type: String,
                required: true,
            },
            disabled: {
                type: Boolean,
                default: false,
            },
            title: {
                type: String,
                default: '查看零售单',
            },
            canEdit: {
                type: Boolean,
                default: false,
            },
            handleEditCallback: {
                type: Function,
                default: null,
            },
        },
        setup() {
            const {
                lockedInfo,
                lockedTips,
                showCancelPay,
                onConfirmCancelPay,
                getLockInfo,
                setLockInfo,
            } = useChargeLock();
            return {
                lockedInfo,
                lockedTips,
                showCancelPay,
                onConfirmCancelPay,
                getLockInfo,
                setLockInfo,
            };
        },
        data() {
            return {
                ChargeStatusEnum,
                ChargeItemStatusEnum,
                contentLoading: false,
                buttonLoading: false,
                showRefundDialog: false,
                showRefundWayList: false,

                dataList: [],
                invoiceList: [],

                chargeStatus: ChargeStatusEnum.UN_CHARGE,
                chargeFormItems: [],
                chargeSheetDetail: {
                    patient: {},
                },
                chargeSheetSummary: {},

                curRefundType: '', // 当前退费类型
                refundTotalFee: 0, // 需要退的费用
                refundData: {}, // 项目退费时，{chargeForms, refundFee, needRefundFee}

                showRealNameRegisDialog: false,
                showPrescriptionRegisDialog: false,

                printable: {},
                cooperationDetail: null,
                canReReportTraceCode: false, //是否支持补录追溯码

            };
        },
        computed: {
            ...mapGetters([
                'printBillConfig',
                'printMedicalListConfig',
                'chargeConfig',
                'traceCodeConfig',
                'userInfo',
                'currentClinic',
                'isCanSeeProfitInRetail',
                'isCanSeeGoodsCostPriceInRetail',
            ]),
            ...mapGetters('invoice', [
                'isOpenInvoice',
                'isOpenMedicalInvoice',
                'isOpenIsvDigitalInvoice',
                'isOpenNuonuoDigitalInvoice',
                'medicalElectronicAPIConfig',
                'writeInvoiceConfig',
                'invoiceConfigList',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),

            renderConfig() {
                const list = [
                    {
                        key: 'goodsInfo',
                        'label': '商品',
                        'style': {
                            'flex': '2',
                        },
                    }, {
                        key: 'batchesInfo',
                        'label': '批号/有效期',
                        'style': {
                            'flex': '1',
                            'minWidth': '133px',
                            'maxWidth': '133px',
                        },
                    }, {
                        key: 'unitCount',
                        'label': '数量',
                        'style': {
                            'flex': '1',
                            'maxWidth': '116px',
                            'textAlign': 'right',
                        },
                    },
                    // {
                    //     key: 'unit',
                    //     'label': '单位',
                    //     'style': {
                    //         'flex': '1',
                    //         'maxWidth': '52px',
                    //         'textAlign': 'center',
                    //     },
                    // },
                    {
                        key: 'sourceUnitPrice',
                        'label': '单价',
                        'colType': 'money4',
                        'style': {
                            'flex': '1',
                            'maxWidth': '80px',
                            'textAlign': 'right',
                        },
                    },
                    {
                        key: 'unitPrice',
                        label: '实价',
                        'colType': 'money',
                        'style': {
                            'flex': '1',
                            'maxWidth': '80px',
                            'textAlign': 'right',
                        },
                    },
                    {
                        key: 'totalPrice',
                        label: '小计',
                        'colType': 'money',
                        'style': {
                            'flex': '1',
                            'maxWidth': '80px',
                            'textAlign': 'right',
                        },
                    },
                ];
                if (this.isCanSeeProfitInRetail) {
                    list.push({
                        key: 'grossProfitRate',
                        label: '毛利率',
                        'style': {
                            'flex': '1',
                            'minWidth': '120px',
                            'maxWidth': '120px',
                            'textAlign': 'right',
                        },
                        dataFormatter: (_, item) => {
                            if (item.grossProfitRate === undefined) return '-';
                            const grossProfit = (item.grossProfitRate * 100).toFixed(2);
                            return `${grossProfit}%`;
                        },
                    });
                }
                return {
                    'hasInnerBorder': false,
                    list,
                };
            },

            // 追溯码提醒
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            needTraceCodeFormItems() {
                return getTraceCodeChargedItems(this.chargeSheetDetail.chargeForms);
            },
            printOptions() {
                const res = [
                    {
                        value: this._printOptions.PHARMACY_CASHIER.label,
                        disabled: this.disabledCashierPrint,
                    },
                    {
                        value: this._printOptions.PHARMACY_REFUND_CASHIER.label,
                        disabled: !this.printable.refundChargeSheet,
                    },
                ].filter((item) => item.show !== false);
                // 获取社保打印类型
                res.push(...this.socialPrintType);
                return res;
            },
            hasAdjustmentFee() {
                const {
                    oddFee = 0,
                    draftAdjustmentFee = 0,
                    outpatientAdjustmentFee = 0,
                } = this.chargeSheetSummary || {};
                return oddFee || draftAdjustmentFee || outpatientAdjustmentFee;
            },
            showDiscountList() {
                const {
                    giftRulePromotions,
                    couponPromotions,
                    patientPointsInfo,
                } = this.chargeSheetDetail;

                const hasAdjustment = this.hasAdjustmentFee;
                const hasCoupons = couponPromotions && couponPromotions.length;
                const hasGifts = giftRulePromotions && giftRulePromotions.length;
                const hasPoint = patientPointsInfo && patientPointsInfo.checked;
                return hasAdjustment || hasCoupons || hasGifts || hasPoint;
            },
            disabledCashierPrint() {
                return (this.chargeStatus < ChargeStatusEnum.CHARGED ||
                    this.chargeStatus === ChargeStatusEnum.REFUND ||
                    this.chargeStatus === ChargeStatusEnum.CLOSED) &&
                    !this.printable.chargeSheet;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            showOpenInvoiceBtn() {
                return [
                    ChargeStatusEnum.CHARGED,
                    ChargeStatusEnum.PART_REFUND,
                    ChargeStatusEnum.REFUND,
                ].indexOf(this.chargeStatus) > -1;
            },
            refundDisabled() {
                if (this.buttonLoading) return true;
                if (this.chargeSheetDetail.isDisabledRefund) return true;
                if (this.chargeSheetDetail.isDisabledOperate) return true;
                if (this.lockedInfo) return true;
                return this.chargeStatus === ChargeStatusEnum.REFUND;
            },

            detailIsDisabled() {
                return this.disabled || this.chargeStatus === ChargeStatusEnum.CLOSED;
            },
            hasPatient() {
                const {
                    patient,
                } = this.chargeSheetDetail;
                return patient?.id && patient.id !== ANONYMOUS_ID;
            },
            canContinuePay() {
                return !this.chargeSheetDetail.isDisabledPay;
            },

            sourcePatientInfo() {
                const {
                    sourcePatientInfo,
                } = this.cooperationDetail || {};
                return sourcePatientInfo;
            },
            PRRegistrationDefaultPatient() {
                const {
                    type,
                    patient,
                } = this.chargeSheetDetail;
                if (type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                    return this.sourcePatientInfo;
                }
                return patient;
            },
            isPrescriptionOut() {
                return this.chargeSheetDetail.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT;
            },
            socialPrintType() {
                const isWillCharge = this.chargeStatus < ChargeStatusEnum.CHARGED; // 是否待收费
                const isRefundCharge = this.chargeStatus === ChargeStatusEnum.REFUND; // 是否已退费
                const isSocialPay = !!this.shebaoSettlePrintSheetId; // 是否社保支付
                const getSocialPrintTypeParams = {
                    isWillCharge,
                    isSocialPay,
                    isRefundCharge,
                };
                // 获取社保打印类型
                return this.$abcSocialSecurity.getSocialPrintType(getSocialPrintTypeParams);
            },
            isNewPharmacyCashierVersion() {
                return PrintManager.getInstance().isNewPharmacyCashierVersion();
            },
        },
        created() {
            if (this.chargeSheetId) {
                this.fetchChargeDetail();
                this.invoiceService = new InvoiceService();
            }
            this._fetchChargeDetail = debounce(this.fetchChargeDetail, 200, true);
            this._printOptions = this.viewDistributeConfig.Print.printOptions;
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._handleSocketLock = (data) => {
                this.handleOnLockSocket(data, 'lock');
            };
            this._handleSocketUnlock = (data) => {
                this.handleOnLockSocket(data, 'unlock');
            };
            this._socket.on('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.on('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },
        mounted() {
        },
        beforeDestroy() {
            this._socket.off('patientOrder.sheet_lock', this._handleSocketLock);
            this._socket.off('patientOrder.sheet_unlock', this._handleSocketUnlock);
        },
        methods: {
            // 是中药饮片
            isChineseGoods(item) {
                return item?.productInfo?.typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES;
            },
            customOrderRender(h, trData, orderIndex) {
                if (trData.isGift) {
                    return <AbcTagV2 shape="square" size="small" theme="danger" variant="dark">赠</AbcTagV2>;
                }
                return <span>{trData.orderIndex || orderIndex}</span>;
            },
            customTrClass(item) {
                const {
                    status,
                } = item || {};

                const _arr = [];
                if (status === ChargeItemStatusEnum.REFUND) {
                    _arr.push('is-disabled');
                }
                return _arr.join(' ');
            },
            autoDestroyInvoice,
            async fetchChargeDetail() {
                this.contentLoading = true;
                this.chargeFormItems = [];
                const res = await ChargeAPI.fetch(this.chargeSheetId);
                const { data } = res;
                if (data.id !== this.chargeSheetId) return;
                data.chargeForms.forEach((form) => {
                    this.chargeFormItems = this.chargeFormItems.concat(form.chargeFormItems || []);
                    this.chargeFormItems.sort((a, b) => a.isGift - b.isGift);
                });
                let orderIndex = 0; // 序号，不能用index的原因是存在虚拟列表
                this.chargeFormItems.forEach((item) => {
                    orderIndex++;
                    item.orderIndex = orderIndex;
                });
                if (data.isClosed) {
                    data.status = ChargeStatusEnum.CLOSED;
                }
                this.chargeStatus = data.status;
                this.chargeSheetSummary = data.chargeSheetSummary;
                this.chargeSheetDetail = data || {};
                this.printable = data.printable;
                this.shebaoSettlePrintSheetId = data.shebaoSettlePrintSheetId; // 社保结算单打印 id
                this.canReReportTraceCode = data.canReReportTraceCode === 1;

                this.$emit('refresh-charge-sheet', data);

                if (data.type === ChargeSheetTypeEnum.COOPERATION_ORDER && data.sourceId) {
                    // 获取合作订单信息
                    const coDetail = await ChargeAPI.fetchCooperationDetailById(data.sourceId);
                    this.cooperationDetail = coDetail.data;
                }

                this.getLockInfo({
                    remote: false,
                    lockList: data.patientOrderLocks,
                });
                // 支付方式有医保卡时，获取医保结算信息
                if (data.chargeTransactions.some((x) => x.payMode === PayModeEnum.SOCIAL_CARD)) {
                    this.getSocialSettleInfo();
                }

                await this.fetchInvoiceList(data);
                this.contentLoading = false;
            },

            // 获取社保结算信息
            async getSocialSettleInfo() {
                try {
                    if (!this.$abcSocialSecurity.isOpenSocial) return;
                    if (this.chargeStatus === ChargeStatusEnum.UN_CHARGE) return;
                    const { data } = await SocialAPI.getSocialSettleInfo(this.chargeSheetId);
                    const {
                        acctPay,
                        fundPaymentFee,
                        medTypeLabel,
                        medfeeSumamt,
                        specialNeedsHint,
                    } = data || {};
                    this.$set(this.chargeSheetSummary, 'acctPay', acctPay || 0);
                    this.$set(this.chargeSheetSummary, 'fundPaymentFee', fundPaymentFee || 0);
                    // 以下信息住院不用管，所以跟住院同名方法有差异
                    this.$set(this.chargeSheetSummary, 'medTypeLabel', medTypeLabel || '');
                    this.$set(this.chargeSheetSummary, 'medfeeSumamt', medfeeSumamt || 0);
                    this.$set(this.chargeSheetSummary, 'specialNeedsHint', specialNeedsHint || '');
                } catch (e) {
                    console.error(e);
                }
            },

            /**
             * 拉取发票列表
             */
            async fetchInvoiceList(data) {
                if (data.status >= ChargeStatusEnum.CHARGED && data.status !== ChargeStatusEnum.CLOSED) {
                    try {
                        const res = await this.invoiceService.fetchCashierSideBarInvoiceList(this.chargeSheetId,
                                                                                             InvoiceBusinessScene.CHARGE,
                                                                                             InvoiceCategory.PAPER,
                                                                                             0);
                        this.invoiceList = res.rows || [];
                    } catch (e) {
                        this.invoiceList = [];
                        console.warn('获取发票列表失败\n', e);
                    }
                } else {
                    this.invoiceList = [];
                }
            },
            async openInvoiceDialog() {
                const _trans = this.chargeSheetDetail.chargeTransactions.find((item) => {
                    return item.payMode === 5 && item.thirdPartyPayInfo;
                });
                let buyerName = '';
                let disabledBuyerName = false;
                if (_trans) {
                    disabledBuyerName = true;
                    buyerName = _trans.thirdPartyPayInfo.cardOwner || '';
                }
                buyerName = buyerName || this.chargeSheetDetail.patient.name;

                await new InvoiceDialog({
                    chargeSheetId: this.chargeSheetId,
                    chargeStatus: this.chargeStatus,
                    patientInfo: {
                        patientId: this.chargeSheetDetail.patient.id,
                        buyerPhone: this.chargeSheetDetail.patient.mobile,
                        buyerName,
                        disabledBuyerName,
                    },
                    printBillConfig: this.printBillConfig,
                    printMedicalListConfig: this.printMedicalListConfig,
                    medicalElectronicAPIConfig: this.medicalElectronicAPIConfig,
                    writeInvoiceConfig: this.writeInvoiceConfig,
                    isOpenInvoice: this.isOpenInvoice,
                    isOpenMedicalInvoice: this.isOpenMedicalInvoice,
                    invoiceStatus: this.chargeSheetDetail.invoiceStatus,
                    userInfo: this.userInfo,
                    businessType: InvoiceBusinessScene.CHARGE,
                    isBizPharmacy: true,
                    invoiceConfigList: this.invoiceConfigList,
                    showPaperInvoice: false,
                    showInvoiceType: false,
                    toBillPrintSetting: () => {
                        navigateToInvoiceConfig(this.currentClinic);
                    },
                    updateInvoiceStatus: (status) => {
                        this.chargeSheetDetail.invoiceStatus = status;
                    },
                    updateInvoice: () => {
                        // 发票更新,刷新QL列表
                        this.$abcEventBus.$emit('open-invoice-refresh-quick-list');
                        // 发票更新,重新拉取发票列表
                        this.fetchInvoiceList();
                    },

                }).generateDialog({ parent: this });

            },
            getPostData(data) {
                return Object.assign(CreateCashierPostData(), {
                    id: data.id,
                    type: data.type,
                    lockStatus: data.lockStatus,
                    chiefComplaint: data.chiefComplaint, // 医生拍方主诉
                    diagnosis: data.diagnosis, // 医生拍方诊断
                    extendDiagnosisInfos: data.extendDiagnosisInfos || [], // 医保诊断编码
                    patient: data.patient,
                    departmentId: data.departmentId,
                    departmentName: data.departmentName,
                    doctorId: data.doctorId,
                    doctorName: data.doctorName,
                    retailType: data.retailType,
                    registration: null,
                    memberId: data.memberId,
                    memberInfo: data.memberInfo,
                    sellerId: data.sellerId,
                    sellerName: data.sellerName,
                    sellerDepartmentId: data.sellerDepartmentId,
                    sellerDepartmentName: data.sellerDepartmentName,
                    dataSignature: data.dataSignature || null,
                    deliveryInfo: data.deliveryInfo || {},
                    contactMobile: data.contactMobile || '',
                    shebaoCardInfo: data.shebaoCardInfo || null,
                    expectedAdjustmentFee: data.chargeSheetSummary.draftAdjustmentFee,
                    roundingType: data.chargeSheetSummary.roundingType, // 收费时需要透传 roundingType
                    promotions: data.promotions || [],
                    giftRulePromotions: data.giftRulePromotions || [],
                    couponPromotions: data.couponPromotions || [],
                    patientPointsInfo: data.patientPointsInfo,
                    patientCardPromotions: data.patientCardPromotions || [],
                    isOweSheetCanPayForShebao: data.isOweSheetCanPayForShebao,
                    patientPointDeductProductPromotions: data.patientPointDeductProductPromotions || [],
                    useMemberFlag: data?.useMemberFlag || UseMemberFlagEnum.USE_DEFAULT,
                    patientOrderNo: data.patientOrderNo,
                    airPharmacyOrderId: data.airPharmacyOrderId,
                    consultantName: data.consultantName,
                    consultantId: data.consultantId,
                    prescriptionUrls: data.prescriptionUrls,
                    remarks: data.remarks,
                    chargeForms: data.chargeForms.map((form) => {
                        form.chargeFormItems.forEach((item) => {
                            item.checked = true;
                        });
                        return form;
                    }),
                });
            },
            continuePay() {
                this.startCharging = true;
                this._chargeDialogInstance = new AbcChargeDialog({
                    pcRouterVm: this.$router,
                    pcStoreVm: this.$store,
                    chargeSheetSummary: this.chargeSheetSummary, // 收费单价格信息
                    postData: this.getPostData(this.chargeSheetDetail), // 提交的收费单数据
                    chargeSheetId: this.chargeSheetId,
                    chargeStatus: this.chargeStatus,
                    needLockInventory: false,
                    disableDispensingBtn: true,
                    disableAdjustmentBtn: true,
                    onPartChargeSuccess: this.partChargeSuccess,
                    onChargeSuccess: this.chargeSuccess,
                    onChargeError: this.chargeError,
                    onClose: this.closeChargeDialog,
                    scene: 'pharmacy-housekeeper',
                    isNeedCheckDispensingBatch: true,
                    hiddenPayModeList: [
                        PayModeEnum.ARREARS,
                    ],
                }).generateDialog({
                    parent: this,
                });
            },
            partChargeSuccess() {
                // 获取最新的详情
                this._fetchChargeDetail();
            },

            /**
             * desc [完成收费]
             */
            async chargeSuccess(data) {
                if (data && data.status !== undefined) {
                    this.chargeStatus = data.status;
                }
                this._chargeDialogInstance = null;
                await this.partChargeSuccess();

                // 同时打印
                const { cache } = Printer;
                const printCache = cache.get();
                // 此次是否同时打印
                const {
                    directSmallNeedPrint, cashier,
                } = printCache;
                if (directSmallNeedPrint) {
                    this.printMeanwhileHandler(cashier);
                    cache.set({
                        directSmallNeedPrint: false,
                    });
                }
            },
            // 部分收费退费
            refundFee() {
                this.$confirm({
                    type: 'warn',
                    title: '退费提示',
                    content: [ `已收费 <span>${this.$t('currencySymbol')} ${formatMoney(this.chargeSheetSummary.netIncomeFee)}</span>，退费后将关闭销售单，确认退费吗？` ],
                    onConfirm: this.openRefundDialog,
                });
            },
            openRefundDialog() {
                this.refundTotalFee = this.chargeSheetSummary.netIncomeFee; // 需要退的总费用
                this.curRefundType = RefundTypeEnum.REFUND_PAID; // 部分退费
                this.showRefundWayList = true;
            },
            async chargeError(err) {
                console.error(err);
                this._fetchChargeDetail();
            },
            /**
             * @desc 关闭收费单
             * <AUTHOR> Yang
             */
            closeOrderHandle() {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '确定关闭收费单？',
                    onConfirm: this.closeChargeOrderSubmit,
                });
            },
            async closeChargeOrderSubmit() {
                try {
                    await ChargeAPI.closeChargeOrder(this.chargeSheetId);
                    this.$Toast({
                        message: '关闭成功',
                        type: 'success',
                    });
                    await this._fetchChargeDetail();
                } catch (e) {
                    console.error(e);
                    const {
                        code, message,
                    } = e;
                    if (code === 17010) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    }
                }
            },
            refundConfirm(data) {
                this.refundTotalFee = data.refundFee; // 需要退的总费用
                this.refundData = data;
                this.curRefundType = RefundTypeEnum.NORMAL;// 选择项目退费时

                // 已开电子发票需要做提示
                const {
                    invoiceType, invoiceSupplierId,
                } = this.invoiceData || {};
                if (invoiceType === InvoiceCategory.ELECTRONIC && invoiceSupplierId !== InvoiceSupplierId.NANJING_WUAI) {
                    this.$confirm({
                        type: 'warn',
                        title: '退货提示',
                        content: '退货后将退费，已开具的电子发票将全部冲红，确认继续退货？',
                        confirmText: '继续退货',
                        onConfirm: () => {
                            this.showRefundWayList = true;
                        },
                    });
                    return false;
                }
                this.showRefundWayList = true;
            },
            async refundHandler(data) {
                if (this.buttonLoading) return false;
                this.buttonLoading = true;
                Object.assign(this.refundData, data);
                try {
                    const { data } = await ChargeAPI.refund(this.chargeSheetId, this.refundData, 0, true);
                    this.showRefund = false;
                    this.showRefundWayList = false;
                    this.buttonLoading = false;
                    this.cashier.quickList.forEach((item) => {
                        if (item.id === data.id) {
                            Object.assign(item, {
                                status: data.status,
                                statusName: data.statusName || '',
                            });
                        }
                    });
                    this.$Toast({
                        message: '退货成功',
                        type: 'success',
                    });
                    this._fetchChargeDetail();
                } catch (err) {
                    this.buttonLoading = false;
                }
            },

            /**
             * desc [完成退费时]
             */
            refundFinish() {
                this.showRefundWayList = false;
                this._fetchChargeDetail();
                this.$nextTick(() => {
                    this.showRefundDialog = false;
                });
            },

            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'pharmacy-housekeeper' }).generateDialogAsync({ parent: this });
            },

            /**
             * 同时打印回调
             */
            printMeanwhileHandler(selectedList) {
                if (!Array.isArray(selectedList) || !selectedList.length) return;
                AbcPrinter.abcPrint(async () => {
                    const printPropsList = [];
                    for (let i = 0; i < selectedList.length; i++) {
                        const select = selectedList[i];
                        const printData = await this.fetchPrintData(select);
                        const printTaskOptions = getAbcPrintOptions(select, printData);
                        if (printTaskOptions) {
                            printPropsList.push(printTaskOptions);
                        }
                    }
                    return printPropsList;
                });
            },

            async printHandler(printType) {
                // 退费小票
                if (printType.includes(this._printOptions.PHARMACY_REFUND_CASHIER.label)) {
                    const printData = await this.fetchPrintData(this._printOptions.PHARMACY_REFUND_CASHIER.label);
                    printData.isRefund = true;
                    if (this.isNewPharmacyCashierVersion) {
                        await CashierPrintApi.printEscPosCashier(printData);
                    } else {
                        await CashierPrintApi.printPharmacyCashier(printData);
                    }
                    return;
                }
                // 社保其余打印类型
                if (this.socialPrintType) {
                    for (const socialPrintType of this.socialPrintType) {
                        if (printType.includes(socialPrintType.value)) {
                            const printResponse = await this.$abcSocialSecurity.executePrint({
                                printId: socialPrintType.id,
                                shebaoSettlePrintSheetId: this.shebaoSettlePrintSheetId,
                                isPharmacySettle: true,
                            });
                            if (printResponse.status === false) {
                                return this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: printResponse.message || '调用结算单打印出错',
                                });
                            }
                            await this.printSocialSettlementSheet(printResponse);
                            return;
                        }
                    }
                }

                const printData = await this.fetchPrintData(this._printOptions.PHARMACY_CASHIER.label);
                if (this.isNewPharmacyCashierVersion) {
                    await CashierPrintApi.printEscPosCashier(printData);
                } else {
                    await CashierPrintApi.printPharmacyCashier(printData);
                }
            },
            async fetchPrintData(printType) {
                let printData = null;
                const isPrintQrcode = true;
                const printLoading = this.$Loading({
                    text: '准备打印...',
                    customClass: 'print-loading-wrapper',
                });
                try {
                    // 收费小票
                    if (printType === this._printOptions.PHARMACY_CASHIER.label) {
                        const { data } = await ChargeAPI.fetchChargePrint(this.chargeSheetId);
                        // 构造电子发票链接二维码
                        const {
                            type,
                            digitalInvoice,
                            invoiceCategory,
                            invoiceSupplierId,
                        } = data?.invoiceView || {};
                        const invoiceImageUrl = digitalInvoice?.invoiceImageUrl; // 发票url
                        const clinicId = this.currentClinic?.clinicId || data.organ.id;

                        try {
                            // 构造电子发票链接二维码
                            if (invoiceImageUrl && type !== InvoiceViewType.RED &&
                                invoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC &&
                                invoiceSupplierId === InvoiceSupplierId.FUJIAN_BOSI) {
                                data.invoiceQrcode = await QRCode.toDataURL(invoiceImageUrl, { margin: 0 });
                            } else {
                                const path = invoiceImageUrl && type !== InvoiceViewType.RED ? 'invoice-preview' : 'view-invoice';
                                const fullUrl = `${getOrigin()}/mp/${path}?clinicId=${clinicId}&businessScene=${InvoiceBusinessScene.CHARGE}&businessId=${data.id}`;

                                const { data: shortUrlData } = await ShortUrlAPI.createShortUrl({
                                    fullUrl,
                                });
                                const QrcodeStr = shortUrlData.shortUrl;
                                data.invoiceQrcode = await QRCode.toDataURL(QrcodeStr, { margin: 0 });
                            }
                        } catch (e) {
                            console.error('构造电子发票二维码失败\n', e);
                        }
                        printData = data;
                    }
                    // 退费小票
                    if (printType === this._printOptions.PHARMACY_REFUND_CASHIER.label) {
                        const { data } = await PrintAPI.printCashierRefund(this.chargeSheetId);
                        // 构造电子发票链接二维码
                        if (data.invoiceUrl) {
                            data.invoiceQrcode = await QRCode.toDataURL(data.invoiceUrl, { margin: 0 });
                        }
                        printData = data;
                    }

                    if (printData && isPrintQrcode) {
                        // 二维码
                        const qrCode = await this.fetchQrCode();
                        const { printData: data } = printData;
                        printData.qrCode = qrCode;
                        if (data) {
                            data.qrCode = qrCode;
                        }
                    }
                    return printData;
                } catch (e) {
                    console.error(e);
                } finally {
                    printLoading.close();
                }
            },
            async fetchQrCode() {
                let qrcode = '';
                if (this.isOpenMp) {
                    try {
                        qrcode = await TpsAPI.genQrCode(this._patientOrderId);
                        return qrcode;
                    } catch (e) {
                        qrcode = '';
                    }
                }
                return qrcode;
            },
            async printSocialSettlementSheet(printResponse) {
                const { html } = printResponse.data || {};
                if (html) {
                    await AbcPrinter.abcPrint({
                        templateKey: window.AbcPackages.AbcTemplates.medicalFeeSocial,
                        printConfigKey: ABCPrintConfigKeyMap.social,
                        data: {},
                        extra: {
                            // 移除医保传递的外部div
                            // 避免分页的问题
                            getHTML: () => html.replace('<div class="print-stat-wrapper">', '').replace(/<\/div>$/, ''),
                        },
                    });
                }
            },
            handleOpenTraceCodeDialog() {
                this._collectionTraceCodeDialog = new CollectionTraceCodeDialog({
                    dispensedFormItems: this.needTraceCodeFormItems,
                    sceneType: SceneTypeEnum.CHARGE,
                    disabled: true,
                    patientOrderId: this.chargeSheetDetail.patientOrderId,
                    canReReportTraceCode: this.canReReportTraceCode,
                    isSelectedSocialPay: (this.chargeSheetDetail.chargeTransactions ?? []).some((x) => x.payMode === PayModeEnum.SOCIAL_CARD),
                    onClose: () => {
                        this._collectionTraceCodeDialog = null;
                    },
                    onConfirm: (_,action) => {
                        const isReReport = action === 'reReport' && this.canReReportTraceCode;
                        if (isReReport) {
                            this.saveTraceCodeHandler();
                        }
                    },
                });
                this._collectionTraceCodeDialog.generateDialogAsync();
            },
            async saveTraceCodeHandler() {
                try {
                    await ChargeAPI.saveTraceCode(this.chargeSheetId, {
                        isReReport: 1,
                        list: this.needTraceCodeFormItems.map((item) => {
                            return {
                                id: item.id,
                                shebaoDismountingFlag: item.shebaoDismountingFlag,
                                traceableCodeRule: item.traceableCodeRule,
                                traceableCodeList: item.traceableCodeList ? item.traceableCodeList.map((code) => {
                                    const {
                                        count, ...restCode
                                    } = code;
                                    return restCode;
                                }) : [],
                                composeChildren: item.composeChildren && item.composeChildren.map((it) => {
                                    return {
                                        id: it.id,
                                        shebaoDismountingFlag: it.shebaoDismountingFlag,
                                        traceableCodeRule: it.traceableCodeRule,
                                        traceableCodeList: it.traceableCodeList ? it.traceableCodeList.map((code) => {
                                            const {
                                                count, ...restCode
                                            } = code;
                                            return restCode;
                                        }) : [],
                                    };
                                }),
                            };
                        }),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '保存成功',
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            handleUpdateRemarks(remarks) {
                this.chargeSheetDetail.remarks = remarks;
            },
            onClickCancelPay() {
                // 后台的数据可能存在有锁信息但是没有lockPayTransactionInfo
                if (!this.chargeSheetDetail.lockPayTransactionInfo) return;
                this.onConfirmCancelPay({
                    chargePayTransactionId: this.chargeSheetDetail.lockPayTransactionInfo.id,
                });
            },
            async handleOnLockSocket(data, type) {
                const {
                    businessKey,
                    key: patientOrderId,
                    value,
                } = data || {};
                if (businessKey !== LockBusinessKeyEnum.CHARGE) return;
                if (this.chargeSheetDetail.patientOrderId !== patientOrderId) return;
                if (type === 'lock') {
                    const { businessDetail } = value || {};
                    const { chargePayTransactionId } = businessDetail || {};
                    this.setLockInfo(data || null);
                    if (!this.chargeSheetDetail.lockPayTransactionInfo) {
                        this.chargeSheetDetail.lockPayTransactionInfo = {};
                    }
                    Object.assign(this.chargeSheetDetail.lockPayTransactionInfo, {
                        id: chargePayTransactionId,
                    });
                } else {
                    this._fetchChargeDetail();
                }
            },

            /**
             * @desc 授权码过期，重启审核流程
             */
            reOpenAuditFlow() {
                this.showRefundWayList = false;
                this.$refs?.refundPro?.reOpenAuditFn();
            },
            /**
             * @desc 重新收费功能，跳转到零售页面并带上chargeSheetId参数
             * <AUTHOR> Yang
             * @date 2025-04-16
             */
            async handleRecharge(chargeStatus) {
                this.$router.push({
                    name: PharmacyChargeRouterNameKeys.retail,
                    query: {
                        copyChargeSheetId: this.chargeSheetId, chargeStatus,
                    },
                });
            },
            /**
             * @desc 跳转到零售页面并带上chargeSheetId参数
             * @desc 如果存在handleEditCallback，则关闭弹窗，执行回调函数
             * @date 2025-07-02
             */
            handleEdit(chargeStatus) {
                if (this.handleEditCallback) {
                    this.showDialog = false;
                    this.handleEditCallback(this.chargeSheetId, chargeStatus);
                    return;
                }
                this.handleRecharge(chargeStatus);
            },
        },
    };
</script>
<style lang="scss">
    @import "src/styles/abc-common.scss";

    .sales-record-detail-dialog {
        .dialog-content {
            height: 100%;
            min-height: 600px;
        }

        .left-content {
            position: relative;
            flex: 1;
            height: 100%;
            padding: 24px 14px 24px 24px;
            overflow-y: scroll;

            @include scrollBar();

            .discount-list {
                display: flex;
                flex-direction: column;
                align-self: stretch;
                padding: 0;
                margin-top: 16px;
                border: 1px solid $P7;
                border-radius: var(--abc-border-radius-small);

                .discount-item {
                    min-height: 32px;
                }
            }
        }

        .right-content {
            width: 320px;
            height: 100%;
            overflow-y: scroll;
            background-color: $abcDivGrey;
            border-left: 1px solid $P8;

            @include scrollBar();
        }

        .charge-seal {
            position: absolute;
            top: 4px;
            left: 160px;
            z-index: 1;
            width: 64px;
            transform: scale(1);
        }
    }
</style>

