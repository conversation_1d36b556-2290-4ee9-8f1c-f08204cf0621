<template>
    <abc-dialog
        v-if="showDialog"
        v-model="showDialog"
        append-to-body
        class="hang-up-order-dialog"
        :auto-focus="false"
        size="hugely"
        title="提取挂单"
        data-cy="hang-up-order-dialog"
        @close="$emit('close')"
    >
        <abc-layout class="dialog-content clearfix">
            <abc-layout-content style="padding: 0;">
                <abc-section>
                    <abc-flex justify="space-between" align="center">
                        <abc-space>
                            <abc-input
                                v-model.trim="searchParams.keyword"
                                type="text"
                                placeholder="姓名 / 手机"
                                data-cy="search-input"
                                clearable
                                @clear="() => searchParams.keyword = ''"
                            >
                                <abc-search-icon slot="prepend"></abc-search-icon>
                            </abc-input>
                            <abc-date-picker
                                v-model="datePickerValue"
                                :picker-options="pickerOptions"
                                value-format="YYYY-MM-DD"
                                type="daterange"
                                data-cy="date-picker"
                                :width="262"
                                :clearable="false"
                                @change="changeDate"
                                @clear="clearDatePicker"
                            ></abc-date-picker>
                        </abc-space>

                        <abc-space v-if="isBatchPickUp">
                            <abc-button
                                :loading="batchLoading"
                                data-cy="batch-pick-up-button"
                                @click="handleConfirmBatchPickUp"
                            >
                                确定提单{{ checkedList.length ? ` · ${checkedList.length}` : '' }}
                            </abc-button>
                            <abc-button
                                variant="ghost"
                                data-cy="batch-pick-up-button"
                                @click="handleCancelBatchPickUp"
                            >
                                取消
                            </abc-button>
                        </abc-space>
                        <abc-button
                            v-else
                            variant="ghost"
                            data-cy="batch-pick-up-button"
                            @click="handleBatchPickUp"
                        >
                            批量提单
                        </abc-button>
                    </abc-flex>
                </abc-section>
                <abc-section>
                    <abc-table
                        ref="abcTable"
                        class="hang-up-order-table"
                        :render-config="renderConfig"
                        :data-list="dataList"
                        :pagination="tablePagination"
                        style="height: 511px;"
                        :loading="contentLoading"
                        :custom-tr-class="customTrClass"
                        @handleClickTr="handleClickTr"
                        @pageChange="pageChange"
                    >
                        <template #patient="{ trData }">
                            <abc-table-cell>
                                {{ trData.memberInfo?.name || trData.patient?.name || '' }}
                            </abc-table-cell>
                        </template>
                        <template #customCheckbox="{ trData }">
                            <abc-popover
                                :disabled="!disabledItemFunc(trData).flag"
                                theme="yellow"
                                placement="bottom-start"
                                trigger="hover"
                                :open-delay="300"
                            >
                                <abc-table-cell slot="reference">
                                    <abc-checkbox
                                        v-model="trData.checked"
                                        :disabled="disabledItemFunc(trData).flag"
                                        @change="handleCheckedItem(trData)"
                                    ></abc-checkbox>
                                </abc-table-cell>
                                <div>
                                    {{ disabledItemFunc(trData).tips }}
                                </div>
                            </abc-popover>
                        </template>
                        <template #operateGroup="{ trData }">
                            <abc-table-cell>
                                <abc-button
                                    class="use-btn"
                                    size="small"
                                    data-cy="pick-up-button"
                                    :loading="btnLoading"
                                    @click="handleClickUse(trData)"
                                >
                                    提单
                                </abc-button>
                                <abc-button
                                    class="use-btn"
                                    size="small"
                                    variant="ghost"
                                    theme="danger"
                                    data-cy="pick-up-button"
                                    :loading="btnLoading"
                                    @click="handleClickDelete(trData)"
                                >
                                    删除
                                </abc-button>
                            </abc-table-cell>
                        </template>
                        <ul slot="paginationTipsContent">
                            <li v-if="isBatchPickUp">
                                已选：<abc-money :show-symbol="false" :value="totalFee"></abc-money>
                            </li>
                            <li v-else>
                                总金额：<abc-money :show-symbol="false" :value="draftReceivableTotalFee"></abc-money>
                            </li>
                        </ul>
                    </abc-table>
                </abc-section>
            </abc-layout-content>
        </abc-layout>
    </abc-dialog>
</template>

<script>
    import ChargeAPI from 'api/charge.js';
    import {
        debounce, isEqual,
    } from 'utils/lodash.js';
    import quickListCommon from 'views/common/quick-list-common.js';
    import { parseTime } from '@abc/utils-date';
    import {
        ChargeSheetTypeEnum,
    } from '@/service/charge/constants.js';
    import { mapGetters } from 'vuex';
    import useBarcodeScanner from 'views/inventory/hooks/useBarcodeScanner';
    export default {
        name: 'HangUpOrderDialog',
        components: {
        },
        mixins: [quickListCommon],
        props: {
            value: Boolean,
            postData: Object,
        },
        setup() {
            const {
                setDisabledScanBarcode,
            } = useBarcodeScanner();

            return {
                setDisabledScanBarcode,
            };
        },
        data() {
            return {
                contentLoading: false,
                btnLoading: false,
                batchLoading: false,
                isBatchPickUp: false,
                dataList: [],

                searchParams: {
                    keyword: '',
                    beginDate: '',
                    endDate: '',
                    tab: 3,
                    offset: 0,
                    limit: 10,
                },

                tablePagination: {
                    showTotalPage: true,
                    pageIndex: 0,
                    pageSize: 10,
                    count: 0,
                },
                draftReceivableTotalFee: 0,
                checkedList: [],
            };
        },
        computed: {
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            renderConfig() {
                let list = [];
                if (this.isBatchPickUp) {
                    list.push({
                        label: ' ',
                        key: 'customCheckbox',
                        headerStyle: {
                            textAlign: 'center',
                        },
                        style: {
                            flex: 'none',
                            width: '40px',
                            maxWidth: '',
                            paddingLeft: '',
                            paddingRight: '',
                            textAlign: 'left',
                        },
                    });
                }
                list = list.concat([
                    {
                        'key': 'created',
                        'label': '挂单时间',
                        'style': {
                            'flex': '1',
                            width: '180px',
                        },
                        dataFormatter: (created) => parseTime(created, 'y-m-d h:i', true) || '-',
                    },
                    {
                        'key': 'goodsAbstract',
                        'label': '商品',
                        'style': {
                            'flex': '3',
                            'maxWidth': '380px',
                        },
                    },
                ]);


                if (this.isOpenSocial || this.hasBindHistory) {
                    list.push({
                        'key': 'type',
                        'label': '类型',
                        'style': {
                            'flex': '1',
                            'max-width': '120px',
                        },
                        dataFormatter: (type) => {
                            if (ChargeSheetTypeEnum.COOPERATION_ORDER === type) {
                                return '合作药房处方';
                            }
                            if (ChargeSheetTypeEnum.PRESCRIPTION_OUT === type) {
                                return '电子处方流转';
                            }
                            return '零售';
                        },
                    });
                }

                list = list.concat([
                    {
                        'key': 'receivableFee',
                        'label': '应收金额',
                        colType: 'money',
                        'style': {
                            textAlign: 'right',
                            'flex': '1',
                            'max-width': '200px',
                            'padding-right': '24px',
                        },
                    },
                    {
                        'key': 'patient',
                        'label': '会员',
                        'style': {
                            'flex': '1',
                            'max-width': '120px',
                        },
                    },
                    {
                        'key': 'remarks',
                        'label': '备注',
                        'style': {
                            'flex': '1',
                            'max-width': '160px',
                            'min-width': '120px',
                        },
                    },
                    {
                        'key': 'sellerName',
                        'label': '销售人',
                        'style': {
                            'flex': '1',
                            'max-width': '100px',
                        },
                    },
                ]);

                if (!this.isBatchPickUp) {
                    list.push({
                        'key': 'operateGroup',
                        'label': '操作',
                        'style': {
                            'flex': '1',
                            'max-width': '120px',
                            'min-width': '120px',
                        },
                    });
                }

                return {
                    hasInnerBorder: false,
                    list,
                };
            },

            totalFee() {
                if (this.isBatchPickUp) {
                    return this.checkedList.reduce((total, item) => total + item.receivableFee, 0);
                }
                return 0;
            },

            isOpenSocial() {
                return this.$abcSocialSecurity.isOpenSocial;
            },
            ...mapGetters('coPharmacyClinic', ['hasBindHistory']),
        },
        watch: {
            'searchParams.keyword': function(val) {
                if (val) {
                    this._debounceSearch();
                } else {
                    this.clearKey();
                }
            },
        },
        created() {
            // 【hotfix | 零售挂单列表默认时间范围为本月】https://www.tapd.cn/tapd_fe/67459320/story/detail/1167459320001089226
            this.datePickerValue = [this._monthStartDate, this._today];
            this.initDataHandler();
            this._debounceSearch = debounce(this.fetchList, 250, true);
        },
        mounted() {
            this.setDisabledScanBarcode(true);
        },
        beforeDestroy() {
            this.setDisabledScanBarcode(false);
        },
        methods: {
            disabledItemFunc(item) {
                if (item.type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                    return {
                        flag: true,
                        tips: '合作诊所处方的单据不可合并支付',
                    };
                }
                if (item.type === ChargeSheetTypeEnum.PRESCRIPTION_OUT) {
                    return {
                        flag: true,
                        tips: '电子处方流转的单据不可合并支付',
                    };
                }
                if (item.isChinesePrescriptionPiecesSheet) {
                    return {
                        flag: true,
                        tips: '配方饮片销售单据不可合并提单',
                    };
                }
                if (this.checkedList.length === 0) {
                    return {
                        flag: false,
                        tips: '',
                    };
                }
                if (this.checkedList.some((it) => it.patientId !== item.patient?.id)) {
                    return {
                        flag: true,
                        tips: '非同一会员的单据不可合并支付',
                    };
                }
                return {
                    flag: false,
                    tips: '',
                };
            },
            handleClickTr(row) {
                if (this.isBatchPickUp && !this.disabledItemFunc(row).flag) {
                    row.checked = !row.checked;
                    this.handleCheckedItem(row);
                }
            },
            handleBatchPickUp() {
                this.isBatchPickUp = true;
                this.$refs.abcTable?.calcTableLayout();
            },
            handleCancelBatchPickUp() {
                this.checkedList = [];
                this.isBatchPickUp = false;
                this.dataList.forEach((item) => {
                    item.checked = false;
                });
                this.$refs.abcTable?.calcTableLayout();
            },
            handleCheckedItem() {
                this.dataList.forEach((item) => {
                    if (item.checked) {
                        if (!this.checkedList.find((it) => it.id === item.id)) {
                            this.checkedList.push({
                                id: item.id,
                                patientId: item.patient?.id,
                                receivableFee: item.receivableFee,
                                type: item.type,
                            });
                        }
                    } else {
                        this.checkedList = this.checkedList.filter((it) => it.id !== item.id);
                    }
                });
            },
            async handleConfirmBatchPickUp() {
                if (this.checkedList.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '请选择需要合并的单据',
                    });
                    return;
                }
                this.batchLoading = true;
                try {
                    const { data } = await ChargeAPI.batchExtract({
                        chargeSheetIds: this.checkedList.map((it) => it.id),
                    });
                    data.chargeSheet.patient = data.chargeSheet.patient || {
                        id: null,
                        name: '',
                        mobile: '',
                        sex: '男',
                        age: {
                            year: null,
                            month: null,
                            day: null,
                        },
                        wxOpenId: null,
                        isMember: null,
                        wxBindStatus: 0,
                        appFlag: 0,
                        arrearsFlag: 0,
                    };
                    // 就当是草稿单，需要清空chargeSheetId
                    delete data.chargeSheet.id;
                    this.$emit('use', data.chargeSheet);
                    this.$Toast({
                        type: 'success',
                        message: '批量提单成功',
                    });
                    this.showDialog = false;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.batchLoading = false;
                }
            },
            customTrClass() {
                return 'hang-up-order-table-tr';
            },
            clearKey() {
                this.searchParams.keyword = '';
                this.initDataHandler();
            },
            pageChange(page) {
                this.searchParams.offset = (page - 1) * this.tablePagination.pageSize;
                this.fetchList();
            },
            changeDate() {
                this.pickerStartDate = '';
                this.initDataHandler();
            },
            initDataHandler() {
                this.searchParams.beginDate = this.datePickerValue[ 0 ];
                this.searchParams.endDate = this.datePickerValue[ 1 ];
                this.searchParams.offset = 0;
                this.fetchList();
            },
            async fetchList() {
                this.contentLoading = true;
                const { searchParams } = this;
                const { data } = await ChargeAPI.fetchQuickList(searchParams, true);
                if (!isEqual(searchParams, this.searchParams)) return;
                this.tablePagination.count = data.totalCount;
                this.draftReceivableTotalFee = data.draftReceivableTotalFee || 0;
                this.dataList = data.result?.map((item) => {
                    item.checked = this.checkedList.find((it) => it.id === item.id);
                    return item;
                }) || [];
                this.contentLoading = false;
            },
            handleClickUse(chargeSheet) {
                const {
                    patient,
                    chargeForms,
                } = this.postData || {};
                const hasData = patient.id || chargeForms.length;
                if (hasData) {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '原销售单未保存，提单后将丢失，确认提单吗？',
                        onConfirm: () => {
                            this.fetchChargeDetail(chargeSheet.id);
                        },
                        onClose: () => {
                            this._showTips = false;
                        },
                    });
                    return;
                }

                this.fetchChargeDetail(chargeSheet.id);
            },
            async fetchChargeDetail(chargeSheetId) {
                this.btnLoading = true;
                const res = await ChargeAPI.fetch(chargeSheetId);
                const { data } = res;
                if (data.id !== chargeSheetId) return;
                this.$emit('use', data);
                this.btnLoading = false;
                this.$Toast({
                    type: 'success',
                    message: '提单成功',
                });
                this.showDialog = false;
            },
            async handleClickDelete(chargeSheet) {
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    content: '删除后不能恢复，是否确定删除？',
                    onConfirm: () => {
                        this.deleteServerDraftSubmit(chargeSheet);
                    },
                    onClose: () => {
                        this._showTips = false;
                    },
                });
            },
            async deleteServerDraftSubmit(chargeSheet) {
                try {
                    await ChargeAPI.deleteChargeSheet(chargeSheet.id);
                    this.$Toast({
                        message: '删除成功',
                        type: 'success',
                    });
                    this.fetchList();
                } catch (err) {
                    console.error(err);
                }
            },
        },
    };
</script>
<style lang="scss">
    @import "src/styles/abc-common.scss";

    .hang-up-order-dialog {
        .dialog-content {
            min-height: 560px;
        }

        .left-content {
            flex: 1;
            height: 100%;
            padding: 24px 14px 24px 24px;
            overflow-y: scroll;

            @include scrollBar();
        }

        .right-content {
            width: 320px;
            height: 100%;
            padding: 24px;
            background-color: $abcDivGrey;
            border-left: 1px solid $P8;
        }

        .hang-up-order-table {
            .use-btn {
                visibility: hidden;
            }

            .hang-up-order-table-tr {
                &:hover {
                    .use-btn {
                        visibility: visible;
                    }
                }
            }
        }
    }
</style>
