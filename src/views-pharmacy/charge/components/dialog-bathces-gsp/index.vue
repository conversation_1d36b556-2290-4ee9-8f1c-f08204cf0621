<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        class="batches-selector-dialog"
        :auto-focus="false"
        content-styles="width: 960px; padding: 24px;"
        :title="title"
        @close="$emit('close')"
    >
        <abc-layout v-abc-loading="contentLoading" class="dialog-content clearfix">
            <abc-form ref="abcForm" item-no-margin>
                <abc-table
                    :render-config="renderConfig"
                    :data-list="dataList"
                    style="height: 440px;"
                    type="excel"
                    empty-content="暂无库存"
                    @sortChange="sortChange"
                >
                    <template #topHeader>
                        <abc-flex justify="space-between" style="width: 100%;">
                            <goods-filed :vertical="false" :goods="productInfo"></goods-filed>
                        </abc-flex>
                    </template>
                    <template #batchNo="{ trData }">
                        <abc-table-cell :theme="getBatchNoTheme(trData)">
                            {{ trData.batchNo }}
                        </abc-table-cell>
                    </template>
                    <template #expiryDate="{ trData }">
                        <abc-table-cell :theme="getBatchNoTheme(trData)">
                            {{ trData.expiryDate }}
                        </abc-table-cell>
                    </template>
                    <template #unitCount="{ trData }">
                        <abc-space is-compact style="height: 100%;">
                            <abc-form-item v-if="!isChineseMedicine(trData.goods)">
                                <abc-input
                                    v-model.number="trData.conversePackageCount"
                                    v-abc-focus-selected
                                    :width="(allowDismounting(trData.goods) && (!unitEqual(trData.goods) || isChineseMedicine(trData.goods))) ? 70 : 140"
                                    type="number"
                                    size="medium"
                                    :config="getConfig(trData.goods)"
                                >
                                    <div slot="appendInner">
                                        {{ trData.goods.packageUnit }}
                                    </div>
                                </abc-input>
                            </abc-form-item>
                            <abc-form-item v-if="allowDismounting(trData.goods) && (!unitEqual(trData.goods) || isChineseMedicine(trData.goods))">
                                <abc-input
                                    v-model.number="trData.conversePieceCount"
                                    v-abc-focus-selected
                                    size="medium"
                                    :width="!isChineseMedicine(trData.goods) ? 70 : 140"
                                    type="number"
                                    :config="getConfig(trData.goods)"
                                >
                                    <div slot="appendInner">
                                        {{ trData.goods.pieceUnit }}
                                    </div>
                                </abc-input>
                            </abc-form-item>
                        </abc-space>
                    </template>
                    <template #assembleBucketCount="{ trData }">
                        <abc-form-item trigger="change">
                            <abc-input
                                v-model.number="trData.assembleBucketCount"
                                v-abc-focus-selected
                                type="number"
                                :config="getConfig(trData.goods)"
                            >
                            </abc-input>
                        </abc-form-item>
                    </template>
                    <template #clearBucketCount="{ trData }">
                        <abc-form-item trigger="change">
                            <abc-input
                                v-model.number="trData.clearBucketCount"
                                v-abc-focus-selected
                                type="number"
                                :config="getConfig(trData.goods)"
                            >
                            </abc-input>
                        </abc-form-item>
                    </template>
                </abc-table>
            </abc-form>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button @click="handleClickConfirm">
                确定
            </abc-button>
            <abc-button type="blank" @click="closed = true">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import GoodsFiled from '@/views-pharmacy/charge/components/goods-filed.vue';
    import Clone from 'utils/clone';
    import { createGUID } from '@/utils';
    import GspAPI from '@/api/pharmacy/gsp';
    import { GoodsTypeEnum } from '@abc/constants';
    import {
        isChineseMedicine,
    } from 'src/filters/goods';
    import { unitEqual } from 'views/inventory/goods-utils';
    export default {
        name: 'Batches',
        components: {
            GoodsFiled,
        },
        props: {
            value: Boolean,
            batchItem: {
                type: Object,
                required: true,
            },
            onConfirm: {
                type: Function,
                required: true,
            },
            clinicId: String,
            isClearBucket: Boolean, // 是否是清斗
            isAssembleBucketCount: Boolean, // 是否是装斗
            pharmacyNo: {
                type: [Number, String],
                default: 0,
            },
        },
        data() {
            return {
                visible: false,
                closed: false,
                contentLoading: false,
                dataList: [],

                curUnitCount: this.batchItem.unitCount,
                lastUnitCount: this.batchItem.unitCount,
                curUnit: this.batchItem.unit,
                isExpectedBatch: this.batchItem.isExpectedBatch,
            };
        },
        computed: {
            title() {
                return `选择${this.batchText}批次`;
            },
            batchText() {
                return this.isClearBucket ? '清斗' : this.isAssembleBucketCount ? '装斗' : '养护';
            },
            renderConfig() {
                let list = [
                    {
                        'key': 'batchNo',
                        'label': '生产批号',
                        'style': {
                            flex: 1,
                            'min-width': '120px',
                        },
                    },
                    {
                        'key': 'productDate',
                        'label': '生产日期',
                        sortable: true,
                        style: {
                            width: '100px',
                            minWidth: '100px',
                            maxWidth: '100px',
                        },
                    },
                    {
                        'key': 'expiryDate',
                        'label': '效期',
                        sortable: true,
                        style: {
                            width: '100px',
                            minWidth: '100px',
                            maxWidth: '100px',
                        },
                    },
                    {
                        'key': 'packageCostPrice',
                        'label': '进价',
                        'colType': 'money4',
                        sortable: true,
                        style: {
                            width: '90px',
                            minWidth: '90px',
                            maxWidth: '90px',
                            'textAlign': 'right',
                        },
                    },
                    {
                        'key': 'inDate',
                        'label': '入库日期',
                        'colType': 'date',
                        sortable: true,
                        style: {
                            width: '110px',
                            minWidth: '110px',
                            maxWidth: '110px',
                        },
                    },
                    {
                        'key': 'dispGoodsCount',
                        'label': '库存',
                        sortable: true,
                        style: {
                            width: '100px',
                            minWidth: '100px',
                            maxWidth: '100px',
                        },
                    },{
                        'key': 'unitCount',
                        'label': '数量',
                        'style': {
                            width: '142px',
                            minWidth: '142px',
                            maxWidth: '142px',
                            'textAlign': 'center',
                        },
                    },
                ];
                if (this.isClearBucket) {
                    list = [
                        {
                            'key': 'batchNo',
                            'label': '生产批号',
                            'style': {
                                flex: 1,
                                'min-width': '120px',
                            },
                        },
                        {
                            'key': 'productDate',
                            'label': '生产日期',
                            sortable: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                            },
                        },
                        {
                            'key': 'expiryDate',
                            'label': '效期',
                            sortable: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                            },
                        },
                        {
                            'key': 'packageCostPrice',
                            'label': '进价',
                            'colType': 'money4',
                            sortable: true,
                            style: {
                                width: '90px',
                                minWidth: '90px',
                                maxWidth: '90px',
                                'textAlign': 'right',
                            },
                        },
                        {
                            'key': 'inDate',
                            'label': '入库日期',
                            'colType': 'date',
                            sortable: true,
                            style: {
                                width: '110px',
                                minWidth: '110px',
                                maxWidth: '110px',
                            },
                        },
                        {
                            'key': 'dispStockGoodsCount',
                            'label': '库存',
                            sortable: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                            },
                        },{
                            'key': 'clearBucketCount',
                            'label': '数量',
                            'style': {
                                'flex': '1',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'key': 'pieceUnit',
                            'label': '单位',
                            style: {
                                width: '60px',
                                minWidth: '60px',
                                maxWidth: '60px',
                                textAlign: 'center',
                            },
                        },
                    ];
                }
                if (this.isAssembleBucketCount) {
                    list = [
                        {
                            'key': 'batchNo',
                            'label': '生产批号',
                            'style': {
                                flex: 1,
                                'min-width': '120px',
                            },
                        },
                        {
                            'key': 'productDate',
                            'label': '生产日期',
                            sortable: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                            },
                        },
                        {
                            'key': 'expiryDate',
                            'label': '效期',
                            sortable: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                            },
                        },
                        {
                            'key': 'packageCostPrice',
                            'label': '进价',
                            'colType': 'money4',
                            sortable: true,
                            style: {
                                width: '90px',
                                minWidth: '90px',
                                maxWidth: '90px',
                                'textAlign': 'right',
                            },
                        },
                        {
                            'key': 'inDate',
                            'label': '入库日期',
                            'colType': 'date',
                            sortable: true,
                            style: {
                                width: '110px',
                                minWidth: '110px',
                                maxWidth: '110px',
                            },
                        },
                        {
                            'key': 'dispStockGoodsCount',
                            'label': '库存',
                            sortable: true,
                            style: {
                                width: '100px',
                                minWidth: '100px',
                                maxWidth: '100px',
                            },
                        },{
                            'key': 'assembleBucketCount',
                            'label': '数量',
                            'style': {
                                'flex': '1',
                                'textAlign': 'center',
                            },
                        },
                        {
                            'key': 'pieceUnit',
                            'label': '单位',
                            style: {
                                width: '60px',
                                minWidth: '60px',
                                maxWidth: '60px',
                                textAlign: 'center',
                            },
                        },
                    ];
                }
                return {
                    hasInnerBorder: false,
                    list,
                };
            },
            productInfo() {
                return this.batchItem.productInfo;
            },
            batchInfoDetails() {
                const {
                    chargeFormItemBatchInfos,
                } = this.batchItem;

                let list = [];
                // 算费取 chargeFormItemBatchInfos 字段
                if (chargeFormItemBatchInfos) {
                    list = chargeFormItemBatchInfos;
                } else {
                    // 详情中取 chargeFormItemBatchInfos 字段
                    let {
                        batchInfoViewList,
                    } = this.batchItem;
                    batchInfoViewList = batchInfoViewList || [];
                    // 这里只用取第一个
                    const curBatchInfoViewList = batchInfoViewList[0];
                    list = curBatchInfoViewList?.batchInfoDetails || [];
                }
                return list || [];
            },
            useDismounting() {
                const {
                    dismounting,
                    pieceUnit,
                    packageUnit,
                } = this.productInfo?.productInfo || {};
                return +(dismounting && this.curUnit === pieceUnit && this.curUnit !== packageUnit);
            },

            batchCountLimit() {
                if (this.curUnitCount > 1) {
                    return 0;
                }
                return 1;
            },
            totalCountLimit() {
                const {
                    packageCount,
                    pieceCount,
                } = this.batchItem;
                let totalStock = packageCount;
                if (this.useDismounting) {
                    const {
                        pieceNum,
                    } = this.productInfo?.productInfo || {};
                    totalStock = pieceNum * packageCount + pieceCount;
                }
                return totalStock;
            },
            errorInfo() {
                return {
                    error: this.curUnitCount > this.totalCountLimit,
                    message: `库存不足（${this.totalCountLimit}${this.curUnit}）`,
                };
            },
        },
        watch: {
            closed(newVal) {
                if (newVal) {
                    this.visible = false;
                    this.destroyElement();
                }
            },
        },
        created() {
            this.fetchBatchesByGoods();
        },
        methods: {
            isChineseMedicine,
            unitEqual,
            allowDismounting(goods) {
                return !!goods.dismounting;
            },
            getConfig(goods) {
                return {
                    formatLength: (this.isChineseMedicine(goods) || goods.type === GoodsTypeEnum.GOODS) ? 2 : 0,
                    max: 10000000,
                    supportZero: false,
                };
            },
            getBatchNoTheme(trData) {
                const {
                    expiredWarnFlag,
                } = trData;
                if (expiredWarnFlag === 1) return 'warning';
                if (expiredWarnFlag === 2) return 'danger';
                return undefined;
            },
            getCount(item) {
                const {
                    packageCount = 0,
                    pieceCount = 0,
                } = item;
                let totalStock = packageCount;
                if (this.useDismounting) {
                    const {
                        pieceNum,
                    } = this.productInfo?.productInfo || {};
                    totalStock = pieceNum * packageCount + pieceCount;
                }
                return totalStock;
            },
            getStockCount(item) {
                const {
                    packageCount = 0,
                    pieceCount = 0,
                } = item;
                let totalStock = packageCount;
                if (this.useDismounting) {
                    const {
                        pieceNum,
                    } = this.productInfo?.productInfo || {};
                    totalStock = pieceNum * packageCount + pieceCount;
                }
                return totalStock;
            },
            handleInput() {
                const diffCount = this.curUnitCount - this.lastUnitCount;
                if (diffCount > 0) {
                    // 增加数量，从最优批次开始加，需要判断库存限制
                    this.addUnitCount(diffCount, 0);
                }
                if (diffCount < 0) {
                    // 减少数量，需要从最差匹配批次倒序减少
                    const reduceSort = this.dataList.length - 1;
                    this.reduceUnitCount(diffCount, reduceSort);
                }

                this.lastUnitCount = this.curUnitCount;
                this.refreshSort();
            },
            addUnitCount(diffCount, addSort) {
                // 增加数量，直接加到最优解批次（批次填了数量sort越小）上
                this.dataList.forEach((it) => {
                    if (it.addSort === addSort) {
                        const diff = +it.unitCount + +diffCount;
                        const stockCount = this.getStockCount(it);
                        const overLimitCount = diff - stockCount;
                        if (overLimitCount > 0) {
                            it.unitCount = stockCount;
                            if (this.dataList.find((item) => item.addSort === addSort + 1)) {
                                this.addUnitCount(overLimitCount, addSort + 1);
                            } else {
                                it.unitCount = diff;
                            }
                        } else {
                            it.unitCount = diff;
                        }
                    }
                });
            },
            reduceUnitCount(diffCount, reduceSort) {
                this.dataList.forEach((it) => {
                    if (it.reduceSort === reduceSort) {
                        const diff = +it.unitCount + +diffCount;

                        if (diff < 0) {
                            it.unitCount = 0;
                            this.reduceUnitCount(diff, reduceSort - 1);
                        }

                        if (diff >= 0) {
                            it.unitCount = diff;
                        }
                    }
                });
            },

            async fetchBatchesByGoods() {
                this.contentLoading = true;

                const {
                    productInfo, unit,
                } = this.batchItem;
                const {
                    dismounting,
                    pieceUnit,
                    packageUnit,
                } = productInfo || {};
                const useDismounting = +(dismounting && unit === pieceUnit && unit !== packageUnit);

                try {
                    const params = {
                        clinicId: this.clinicId,
                        batchNo: '',
                        goodsId: this.batchItem.goodsId,
                        supplierId: '',
                        offset: 0,
                        limit: 299,
                        pharmacyNo: this.pharmacyNo,
                        batchViewMode: '',
                        expiredWarn: '',
                        costPriceWarn: '',
                    };
                    let fetchResponse = null;
                    if (this.isClearBucket) {
                        fetchResponse = await GspAPI.fetchInstallBatchListByGoodsIds({
                            pharmacyNo: this.pharmacyNo,
                            clinicId: this.clinicId,
                            goodsIds: [this.batchItem.goodsId],
                        });
                    } else if (this.isAssembleBucketCount) {
                        params.scene = 1;
                        fetchResponse = await GspAPI.fetchBatchList(params);
                    } else {
                        fetchResponse = await GspAPI.fetchBatchListByGoodsId(this.batchItem.goodsId, params);
                    }
                    let batchList = [];
                    if (this.isClearBucket) {
                        batchList = fetchResponse.data?.[0]?.batchs?.map((i) => {
                            return {
                                ...(fetchResponse.data?.[0]?.goods || {}),
                                ...i,
                                goods: fetchResponse.data?.[0]?.goods || {},
                                goodsId: fetchResponse.data?.[0]?.goods?.id || '',
                            };
                        }) || [];
                    } else {
                        batchList = fetchResponse.data?.rows?.map((i) => {
                            return {
                                ...i,
                                goods: fetchResponse.data?.goods || fetchResponse.data?.goodsItem || {},
                            };
                        }) || [];
                    }
                    this.batchList = Clone(batchList);
                    console.log('当前数据=', this.batchList, this.batchItem);
                    this.dataList = batchList.map((it, index) => {
                        it.keyId = it.keyId || createGUID();
                        it.addSort = index;
                        it.reduceSort = index;
                        it.unitCount = this.getUnitCount(it, useDismounting);
                        const currentItem = this.batchItem.batchs?.find((i) => {
                            return i.batchId === it.batchId;
                        }) || null;
                        if (this.isClearBucket) {
                            if (currentItem) {
                                it.clearBucketCount = currentItem.clearBucketCount || '';
                            } else {
                                it.clearBucketCount = '';
                            }
                        } else if (this.isAssembleBucketCount) {
                            if (currentItem) {
                                it.assembleBucketCount = currentItem.assembleBucketCount || '';
                            } else {
                                it.assembleBucketCount = '';
                            }
                        } else {
                            if (currentItem) {
                                it.conversePackageCount = currentItem.conversePackageCount || currentItem.packageCount || '';
                                it.conversePieceCount = currentItem.conversePieceCount || currentItem.pieceCount || '';
                            } else {
                                it.conversePackageCount = '';
                                it.conversePieceCount = '';
                            }
                        }
                        return it;
                    });
                    this.refreshSort();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.contentLoading = false;
                }
            },

            // 有数量的批次sort越靠前
            refreshSort() {
                this.dataList.slice().sort((a,b) => {
                    return new Date(a.expiryDate) - new Date(b.expiryDate);
                }).sort((a,b) => {
                    const aVal = a.unitCount > 0 ? 1 : 0;
                    const bVal = b.unitCount > 0 ? 1 : 0;
                    return bVal - aVal;
                }).forEach((it, index) => {
                    it.addSort = index;
                });
            },
            getUnitCount(batch, useDismounting) {
                if (this.isExpectedBatch) {
                    const res = this.batchInfoDetails.find((detail) => {
                        return detail.batchId === batch.batchId;
                    });
                    return res?.unitCount || 0;
                }
                const {
                    cutPieceCount = 0,
                    cutPackageCount = 0,
                } = batch;
                return useDismounting ? cutPieceCount : cutPackageCount;
            },

            handleInputBatchCount() {
                this.curUnitCount = this.dataList.reduce((sum, cur) => {
                    sum += +cur.unitCount || 0;
                    return sum;
                }, 0);
                this.lastUnitCount = this.curUnitCount;
                this.isExpectedBatch = 1;
                this.refreshSort();
            },

            sortChange(res) {
                const {
                    orderBy,
                    orderType,
                } = res;
                this.dataList.sort((a,b) => {
                    let aVal = a[orderBy];
                    let bVal = b[orderBy];
                    if ([
                        'productDate',
                        'expiryDate',
                        'inDate',
                    ].indexOf(orderBy) > -1) {
                        aVal = new Date(aVal);
                        bVal = new Date(bVal);
                    } else if (orderBy === 'dispStockGoodsCount') {
                        aVal = this.getStockCount(a);
                        bVal = this.getStockCount(b);
                    } else if (orderBy === 'dispGoodsCount') {
                        aVal = this.getCount(a);
                        bVal = this.getCount(b);
                    }
                    return orderType === 'asc' ? aVal - bVal : bVal - aVal;
                });
            },


            handleClickConfirm() {
                const currentDataList = this.dataList.filter((i) => {
                    // 需要有清斗数量
                    if (this.isClearBucket) {
                        return i.clearBucketCount;
                    }
                    if (this.isAssembleBucketCount) {
                        return i.assembleBucketCount;
                    }
                    return i.conversePackageCount || i.conversePieceCount;
                });
                if (currentDataList.length <= 0) {
                    this.$Toast({
                        type: 'error',
                        message: `请至少选择一个批次的药品进行${this.batchText}`,
                    });
                    return;
                }
                this.$refs.abcForm.validate((val) => {
                    if (val) {
                        this.onConfirm && this.onConfirm({
                            // 暂时的规则是选中的患者
                            dataList: currentDataList,
                            ownList: this.dataList,
                            batchItem: this.batchItem,
                        });
                        this.closed = true;
                    }
                });
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
<style lang="scss">
    .batches-selector-dialog {
        .price {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            min-width: 100px;
            margin-right: 10px;
        }
    }
</style>
