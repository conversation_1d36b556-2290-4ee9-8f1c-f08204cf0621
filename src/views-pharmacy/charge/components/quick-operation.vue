<template>
    <div ref="quickWrapper" class="quick-operation-wrapper">
        <template v-if="quickColList.length > showButtonCount">
            <div
                v-for="(item, index) in visibleQuickColList"
                :key="item.keyMap + index"
                class="quick-col clickable"
                :data-cy="`quick-operation-wrapper-${item.label}`"
                @click="item.handler"
            >
                <div class="label">
                    {{ item.label }}
                    <abc-badge
                        v-if="item.todo"
                        :value="item.todo"
                        class="todo"
                        theme="danger"
                    ></abc-badge>
                </div>
                <div class="key-map">
                    {{ item.keyMap }}
                </div>
            </div>
            <abc-popover
                theme="white"
                trigger="click"
                placement="top"
                class="quick-col clickable"
                popper-class="biz-pharmacy-quick-col-popover"
                :width="140"
            >
                <div slot="reference" class="more-quick-col" data-cy="quick-operation-wrapper-更多">
                    更多
                </div>
                <div>
                    <div
                        v-for="(it, index) in hiddenQuickColList"
                        :key="it.keyMap"
                        class="popover-quick-col-item"
                        :data-cy="`quick-operation-wrapper-${it.label}`"
                        @click="it.handler"
                    >
                        <div class="cut-line">
                            <abc-divider
                                v-if="index !== 0"
                                theme="light"
                                margin="none"
                                variant="solid"
                            ></abc-divider>
                        </div>
                        <div class="label">
                            {{ it.label }}
                        </div>
                        <div class="key-map" style="color: var(--abc-color-T3);">
                            {{ it.keyMap }}
                        </div>
                    </div>
                </div>
            </abc-popover>
        </template>
        <template v-else>
            <div
                v-for="(item, index) in quickColList"
                :key="item.keyMap + index"
                class="quick-col clickable"
                @click="item.handler"
            >
                <div class="label">
                    {{ item.label }}
                    <abc-badge
                        v-if="item.todo"
                        :value="item.todo"
                        class="todo"
                        theme="danger"
                    ></abc-badge>
                </div>
                <div class="key-map">
                    {{ item.keyMap }}
                </div>
            </div>
        </template>

        <dialog-hang-up-order
            v-if="showHangUpDialog"
            v-model="showHangUpDialog"
            :post-data="postData"
            @use="handleUse"
        ></dialog-hang-up-order>
        <real-name-registration-dialog
            v-if="showRealNameRegisDialog"
            v-model="showRealNameRegisDialog"
            :charge-sheet-id="chargeSheetId"
            :register-info-id="postData.registerInfoId"
            :patient="PRRegistrationDefaultPatient"
            @success="onRegisterIdentitySuccess"
        ></real-name-registration-dialog>
        <remarks-dialog
            v-if="showRemarksDialog"
            v-model="showRemarksDialog"
            :post-remarks="postData.remarks"
            @confirm="onConfirmRemarks"
        ></remarks-dialog>
        <abc-dialog
            v-if="showAdjustmentDialog"
            v-model="showAdjustmentDialog"
            title="整单议价"
            content-styles="width: 360px; min-width: 300px;padding: 0;border-radius: 0 0 var(--abc-dialog-border-radius) var(--abc-dialog-border-radius);"
            class="biz-pharmacy-adjustment-popover-wrapper"
            append-to-body
        >
            <adjustment-dialog
                v-model="showAdjustmentDialog"
                :has-cover="false"
                size="normal"
                :adjustment-format-length="2"
                :personal-local-storage-key="personalLocalStorageKey"
                :need-pay-fee="chargeSheetSummary.needPayFee || 0"
                :origin-total-fee="chargeSheetSummary.afterRoundingDiscountedTotalFee"
                @change="changeExpectedAdjustmentFeeHandle"
            ></adjustment-dialog>
        </abc-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import { mapGetters } from 'vuex';
    import getChargeDialogOptions from '@/service/charge/components/dialog-charge/init-charge-dialog-props';
    import {
        on, off,
    } from 'utils/dom';
    const DialogHangUpOrder = () => import('./dialog-hang-up-order.vue');
    const RealNameRegistrationDialog = () => import('./dialog-real-name-registration.vue');
    const RemarksDialog = () => import('./dialog-remarks.vue');
    const AdjustmentDialog = () => import('@/service/charge/components/dialog-charge/dialog-adjustment.vue');
    import { ChargeSheetTypeEnum } from '@/service/charge/constants.js';
    import ChargeAPI from 'api/charge';
    import BarcodeDetectorV2 from 'utils/barcode-detector-v2';
    import { checkHasAbcDialog } from '@/utils';
    import { debounce } from 'utils/lodash';

    export default {
        name: 'QuickOperation',
        components: {
            DialogHangUpOrder,
            RealNameRegistrationDialog,
            AdjustmentDialog,
            RemarksDialog,
        },
        props: {
            chargeSheetId: {
                type: String,
            },
            postData: {
                type: Object,
                required: true,
            },
            chargeSheetSummary: {
                type: Object,
                required: true,
            },
            cooperationDetail: {
                type: Object,
            },
            isPrescriptionOut: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                buttonLoading: false,
                showHangUpDialog: false,
                showRealNameRegisDialog: false,
                showAdjustmentDialog: false,
                showRemarksDialog: false,
                personalLocalStorageKey: '',
                coOrderTodo: 0,
                cacheDiagnosis: {},
                shebaoCardInfo: null,
                showButtonCount: 5,
            };
        },
        computed: {
            ...mapGetters([
                'chargeConfig',
                'traceCodeConfig',
            ]),
            patientId() {
                return this.postData.patient?.id;
            },
            // 追溯码提醒
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            isOpenSocial() {
                return this.$abcSocialSecurity.isOpenSocial;
            },
            quickColList() {
                let _arr = [
                    {
                        label: '议价',
                        keyMap: 'F7',
                        handler: this.handleUpdatePrice,
                    },
                    {
                        label: '改折率',
                        keyMap: 'F6',
                        handler: this.handleUpdatePriceRatio,
                    },
                    {
                        label: '整单议价',
                        keyMap: 'Shift + F7',
                        handler: this.handleClickAdjustment,
                    },
                    {
                        label: '挂单',
                        keyMap: 'F8',
                        handler: this.hangUpOrder,
                    },
                    {
                        label: '提单',
                        keyMap: 'Shift + F8',
                        handler: () => {this.showHangUpDialog = true;},
                    },
                    {
                        label: '设为赠品',
                        keyMap: 'Ctrl + Z',
                        handler: this.onClickSetGift,
                    },
                ];
                if (this.traceCodeCollectionCheck) {
                    _arr.push({
                        label: '追溯码采集',
                        keyMap: 'Ctrl + G',
                        handler: this.handleOpenTraceCodeDialog,
                    });
                }
                _arr = _arr.concat([
                    {
                        label: '医保信息查询',
                        keyMap: 'Ctrl + X',
                        handler: this.handleReadCard,
                    },
                    {
                        label: '备注',
                        keyMap: 'Ctrl + B',
                        handler: this.openRemarksDialog,
                    },
                    {
                        label: '实名登记',
                        keyMap: 'Ctrl + M',
                        handler: this.onClickRegisterIdentity,
                    },
                    {
                        label: '切换搜索范围',
                        keyMap: 'Ctrl + /',
                        handler: this.handleSearchRangeSwitch,
                    },
                    {
                        label: '配方饮片剂数',
                        keyMap: 'Ctrl + J',
                        handler: this.handleChineseMedicineDose,
                    },
                    {
                        label: '选择销售批次',
                        keyMap: 'Ctrl + K',
                        handler: this.handleUpdateBatches,
                    },
                ]);
                if (_arr.length < 5) {
                    _arr = _arr.concat(new Array(5 - _arr.length).fill({
                        label: '',
                        keyMap: '',
                        handler: () => {},
                    }));
                }
                return _arr;
            },
            // 根据屏幕宽度显示的按钮
            visibleQuickColList() {
                return this.quickColList.slice(0, this.showButtonCount - 1);
            },
            // 隐藏在更多菜单中的按钮
            hiddenQuickColList() {
                return this.quickColList.slice(this.showButtonCount - 1, this.quickColList.length);
            },

            sourcePatientInfo() {
                const {
                    sourcePatientInfo,
                } = this.cooperationDetail || {};
                return sourcePatientInfo;
            },

            PRRegistrationDefaultPatient() {
                const {
                    type,
                    patient,
                } = this.postData;
                if (type === ChargeSheetTypeEnum.COOPERATION_ORDER) {
                    return this.sourcePatientInfo;
                }
                return patient;
            },
            dialogVisible() {
                return this.showHangUpDialog ||
                    this.showRealNameRegisDialog ||
                    this.showAdjustmentDialog;
            },
        },
        watch: {
            patientId() {
                // 患者修改需要清空 shebaoCardInfo
                this.shebaoCardInfo = null;
            },
        },
        created() {
            this.initHandler();
        },
        mounted() {
            on(document, 'keydown', this.keydownHandle);
            this._handleResize = debounce(this.handleResize, 100, true);
            on(window, 'resize', this._handleResize);
            this.updateButtonCountByWidth();
        },
        beforeDestroy() {
            off(document, 'keydown', this.keydownHandle);
            off(window, 'resize', this._handleResize);
        },
        methods: {
            /**
             * 根据容器宽度更新显示的按钮数量
             * 屏幕越宽显示越多的按钮，小于阈值的按钮被放入更多弹窗中
             */
            updateButtonCountByWidth() {
                // 获取容器宽度，初始化时可能为0
                const screenWidth = window.innerWidth;
                // 根据容器宽度动态调整显示的按钮数量
                if (screenWidth >= 1920) { // 针对1745px的宽度
                    this.showButtonCount = 11;
                } else if (screenWidth >= 1800) {
                    this.showButtonCount = 10;
                } else if (screenWidth >= 1700) {
                    this.showButtonCount = 9;
                } else if (screenWidth >= 1601) {
                    this.showButtonCount = 9;
                } else if (screenWidth >= 1441) {
                    this.showButtonCount = 9;
                } else if (screenWidth >= 1440) {
                    this.showButtonCount = 9;
                } else if (screenWidth >= 1366) {
                    this.showButtonCount = 8;
                } else if (screenWidth >= 1280) {
                    this.showButtonCount = 8;
                } else {
                    this.showButtonCount = 5;
                }
            },

            /**
             * 当窗口大小改变时调用
             */
            handleResize() {
                this.$nextTick(() => {
                    this.updateButtonCountByWidth();
                });
            },
            keydownHandle(event) {
                const scanner = BarcodeDetectorV2.getInstance();
                if (scanner && scanner.isScanning) return;

                const hasDialog = checkHasAbcDialog();
                if (hasDialog) return;

                const KEY_F6 = 'F6';
                const KEY_F7 = 'F7';
                const KEY_F8 = 'F8';
                const KEY_Z = 'KeyZ';
                const KEY_M = 'KeyM';
                const KEY_G = 'KeyG';
                const KEY_B = 'KeyB';
                const KEY_SLASH = 'Slash';
                const KEY_NUMPAD_SLASH = 'NumpadDivide'; // 小键盘的除号键
                const KEY_J = 'KeyJ';
                const KEY_K = 'KeyK';
                const KEY_X = 'KeyX';

                const keyCode = event.code;
                const isCtrlKey = event.ctrlKey;
                const isShiftKey = event.shiftKey;
                if (isShiftKey && keyCode === KEY_F7) {
                    this.quickKeyboardHandler(event);
                    // 整体议价
                    this.handleClickAdjustment();
                } else if (keyCode === KEY_F7) {
                    this.quickKeyboardHandler(event);
                    // 议价
                    this.handleUpdatePrice();
                } else if (isShiftKey && keyCode === KEY_F8) {
                    this.quickKeyboardHandler(event);
                    // 提取挂单
                    this.showHangUpDialog = true;
                } else if (keyCode === KEY_F8) {
                    this.quickKeyboardHandler(event);
                    // 挂单
                    this.hangUpOrder();
                } else if (isCtrlKey && keyCode === KEY_Z) {
                    this.quickKeyboardHandler(event);
                    // 设置赠品
                    this.onClickSetGift();
                } else if (isCtrlKey && keyCode === KEY_M) {
                    this.quickKeyboardHandler(event);

                    if (!this.validateChargeItems()) {
                        return;
                    }
                    // 实名登记
                    this.showRealNameRegisDialog = true;
                } else if (isCtrlKey && keyCode === KEY_G) {
                    if (!this.traceCodeCollectionCheck) return;
                    this.quickKeyboardHandler(event);

                    if (!this.validateChargeItems()) {
                        return;
                    }
                    if (document.activeElement) {
                        document.activeElement.blur();
                    }
                    // 追溯码
                    this.handleOpenTraceCodeDialog();
                } else if (isCtrlKey && keyCode === KEY_B) {
                    this.quickKeyboardHandler(event);
                    this.openRemarksDialog();
                } else if (isCtrlKey && (keyCode === KEY_SLASH || keyCode === KEY_NUMPAD_SLASH)) {
                    this.quickKeyboardHandler(event);
                    this.handleSearchRangeSwitch();
                } else if (isCtrlKey && keyCode === KEY_J) {
                    this.quickKeyboardHandler(event);
                    this.handleChineseMedicineDose();
                } else if (isCtrlKey && keyCode === KEY_K) {
                    this.quickKeyboardHandler(event);
                    this.handleUpdateBatches();
                } else if (isCtrlKey && keyCode === KEY_X) {
                    this.quickKeyboardHandler(event);
                    this.handleReadCard();
                } else if (keyCode === KEY_F6) {
                    this.quickKeyboardHandler(event);
                    this.handleUpdatePriceRatio();
                }
            },
            quickKeyboardHandler(event) {
                if (event.preventDefault) event.preventDefault();
                if (event.stopPropagation) event.stopPropagation();

                this.showHangUpDialog = false;
                this.showRealNameRegisDialog = false;
            },
            async initHandler() {
                this.fetchCooperationOrderTodo();
                const {
                    personalLocalStorageKey,
                } = await getChargeDialogOptions();
                this.personalLocalStorageKey = personalLocalStorageKey;
            },
            validateChargeItems() {
                if (this.postData.chargeForms.length === 0) {
                    if (!this._showTips) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '未添加商品',
                            onClose: () => {
                                this._showTips = false;
                            },
                        });
                        this._showTips = true;
                    }
                    return false;
                }
                return true;
            },
            async handleReadCard() {
                if (this._readLoading) return;
                this._readLoading = true;
                let { shebaoCardInfo } = this;
                if (!shebaoCardInfo) {
                    const res = await this.$abcSocialSecurity.readCardInfo();
                    if (res.status === false) {
                        if (res.message) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: res.message,
                            });
                        }
                        this._readLoading = false;
                        return;
                    }
                    shebaoCardInfo = res.data;
                    // 有患者信息才可以重复查看社保信息
                    if (this.patientId) {
                        this.shebaoCardInfo = shebaoCardInfo;
                    }

                }
                const showRes = await this.$abcSocialSecurity.showCardInfo({
                    cardInfo: shebaoCardInfo,
                    showFooter: false,
                });
                if (showRes.status === false && showRes.message) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: showRes.message,
                    });
                }
                this._readLoading = false;
            },
            handleClickAdjustment() {
                if (!this.chargeConfig.bargainSwitch) {
                    if (!this._showTips) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '请在【零售设置】中开启“允许整单议价”',
                            onClose: () => {
                                this._showTips = false;
                            },
                        });
                        this._showTips = true;
                    }
                    return;
                }
                if (!this.validateChargeItems()) {
                    return;
                }
                this.showAdjustmentDialog = true;
            },
            handleUpdatePriceRatio() {
                if (!this.chargeConfig.singleBargainSwitch) {
                    if (!this._showTips) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '请在【零售设置】中开启“允许单项议价”',
                            onClose: () => {
                                this._showTips = false;
                            },
                        });
                        this._showTips = true;
                    }
                    return;
                }
                if (!this.validateChargeItems()) {
                    return;
                }
                this.$emit('update-price-ratio');
            },
            handleUpdatePrice() {
                if (!this.chargeConfig.singleBargainSwitch) {
                    if (!this._showTips) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '请在【零售设置】中开启“允许单项议价”',
                            onClose: () => {
                                this._showTips = false;
                            },
                        });
                        this._showTips = true;
                    }
                    return;
                }
                if (!this.validateChargeItems()) {
                    return;
                }
                this.$emit('update-price');
            },
            handleUpdateBatches() {
                if (!this.validateChargeItems()) {
                    return;
                }
                this.$emit('update-batches');
            },

            async hangUpOrder() {
                if (!this.validateChargeItems()) {
                    return;
                }
                this.$emit('hangup');
            },

            handleUse(data) {
                this.$emit('use-hangup', data);
            },

            // 设为赠品
            onClickSetGift() {
                if (!this.validateChargeItems()) {
                    return;
                }
                this.$emit('set-gift');
            },
            // 实名登记
            onClickRegisterIdentity() {
                if (!this.validateChargeItems()) {
                    this.postData.registerInfoId = '';
                    this.postData.isRegisteredIdentity = false;
                    return;
                }
                this.showRealNameRegisDialog = true;
            },
            onRegisterIdentitySuccess(val) {
                this.postData.registerInfoId = val;
                this.postData.isRegisteredIdentity = true;
            },
            openRemarksDialog() {
                this.showRemarksDialog = true;
            },
            onConfirmRemarks(val) {
                this.postData.remarks = val;
                this.showRemarksDialog = false;
            },
            async changeExpectedAdjustmentFeeHandle(data) {
                this.postData.expectedTotalFee = data.expectedTotalFee;
                this.postData.expectedAdjustmentFee = null;
                this.$emit('calc-fee');
            },
            handleOpenTraceCodeDialog() {
                this.$emit('open-trace-code-dialog');
            },
            handleSearchRangeSwitch() {
                this.$emit('handle-search-range-switch');
            },
            handleChineseMedicineDose() {
                this.$emit('handle-chinese-medicine-dose');
            },
            async fetchCooperationOrderTodo() {
                const { data } = await ChargeAPI.fetchCooperationOrderTodo();
                this.coOrderTodo = data?.count;
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/mixin.scss";

.quick-operation-wrapper {
    display: flex;
    height: 40px;
    padding: 4px 8px;
    background: var(--abc-color-P4);
    border-top: 1px solid var(--abc-color-P10);
    border-radius: 0 0 0 var(--abc-border-radius-large);

    .quick-row {
        display: flex;
        flex: 1;
        align-items: center;
        height: 100%;

        + .quick-row {
            border-top: 1px solid $P8;
        }
    }

    .more-quick-col {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        font-size: 14px;
        color: var(--abc-color-T1);
    }

    .quick-col {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        padding: 5px 12px;
        white-space: nowrap; // 防止文字换行
        border-radius: var(--abc-border-radius-small);

        .label {
            position: relative;
            margin-right: 4px;
            font-size: 14px;
            color: var(--abc-color-T1);
            text-align: center;

            .todo {
                position: absolute;
            }
        }

        &.clickable {
            cursor: pointer;
        }

        + .quick-col {
            margin-left: 6px;
        }

        &:hover {
            background: var(--abc-color-Deep3);
        }
    }

    .key-map {
        @include fontSize('normal');

        color: var(--abc-color-T3);
    }
}

.biz-pharmacy-adjustment-popover-wrapper {
    .adjustment-popover-wrapper {
        padding: 18px 24px 0;

        .inp-box {
            .abc-input-wrapper {
                flex: 1;
            }

            .reset-btn {
                width: 75px;
                min-width: 75px;
                border-radius: var(--abc-border-radius-small, 6px);
            }
        }

        .radio-choise-box .abc-radio-group .abc-radio-button .abc-radio-button-label {
            display: inline-flex;
            align-items: center;
            min-width: 75px;
            height: 32px;
            padding: 0 12px;
            font-size: 14px;
            line-height: 22px;
            border: var(--abc-border-1, 1px) solid var(--abc-color-P7, #e0e5ee);
            border-radius: var(--abc-border-radius-small, 6px);
        }

        li {
            flex: 1;
            height: 40px;
            line-height: 40px;
        }

        .line-s {
            display: none;
        }

        .radio-choise-box,
        .change-fee-box {
            margin-top: 16px;
        }

        .change-fee-box .discount-box {
            height: 40px;

            .abc-input-wrapper {
                width: 100%;
            }

            input {
                width: 100% !important;
                height: 40px;
            }

            input::-webkit-input-placeholder {
                font-size: 14px;
            }
        }

        .footer-btns {
            display: flex;
            align-items: center;
            height: 56px;
            margin-top: 24px;
        }
    }
}

.biz-pharmacy-quick-col-popover {
    padding: 10px;

    .popover-quick-col-item {
        position: relative;
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 108px;
        height: 64px;
        cursor: pointer;
        border-radius: var(--abc-border-radius-small);

        .cut-line {
            position: absolute;
            top: 0;
            width: 100%;
        }

        &:hover {
            background: var(--abc-color-cp-grey4);

            .cut-line {
                visibility: hidden;
            }

            + .popover-quick-col-item {
                .cut-line {
                    visibility: hidden;
                }
            }
        }
    }
}
</style>
