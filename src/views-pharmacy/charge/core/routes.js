import Index from '../index.vue';
import {
    MODULE_ID_MAP, RouterScope,
} from 'utils/constants.js';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const Retail = () => import('../frames/retail.vue');
const SalesRecord = () => import('../frames/sales-record.vue');
const TradeFlow = () => import('../frames/trade-flow.vue');

// 外部跳转都应该用该字段
export const PharmacyChargeRouterNameKeys = {
    index: '@PharmacyCharge',
    blank: '@PharmacyChargeBlank',
    retail: '@PharmacyChargeRetail',
    salesRecord: '@PharmacyChargeSalesRecord',
    tradeFlow: '@PharmacyChargeTradeFlow',
};

export default {
    path: 'charge',
    name: PharmacyChargeRouterNameKeys.index,
    component: Index,
    meta: {
        name: '零售',
        needAuth: true,
        moduleId: MODULE_ID_MAP.bizPharmacyCharge,
        pageAsyncClass: PageAsync,
        icon: 'n-currency-fill',
        selectedIcon: 'n-currency-fill',
        scope: RouterScope.SINGLE_STORE | RouterScope.CHAIN_SUB,
    },
    children: [
        {
            path: 'retail',
            component: Retail,
            name: PharmacyChargeRouterNameKeys.retail,
            meta: {
                name: '零售开单',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyCharge,
            },
        },
        {
            path: 'sales-record',
            component: SalesRecord,
            name: PharmacyChargeRouterNameKeys.salesRecord,
            meta: {
                name: '零售记录',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyChargeSubModule.record,
                todoKey: 'sales-record',
            },
        },
        {
            path: 'trade-flows',
            component: TradeFlow,
            name: PharmacyChargeRouterNameKeys.tradeFlow,
            meta: {
                name: '对账',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyChargeSubModule.summary,
            },
        },
    ],
};
