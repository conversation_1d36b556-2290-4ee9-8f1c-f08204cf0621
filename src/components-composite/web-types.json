{"$schema": "https://json.schemastore.org/web-types", "framework": "vue", "contributions": {"html": {"types-syntax": "typescript", "tags": [{"name": "biz-charge-stat-popover", "description": "", "attributes": [{"name": "title", "value": {"kind": "expression", "type": "string"}}, {"name": "forms", "required": true, "value": {"kind": "expression", "type": "array"}, "default": "[]"}], "slots": [{"name": "default"}], "source": {"module": "@/components-composite/biz-charge-stat-popover/src/views/index.vue", "symbol": "default"}}, {"name": "biz-crm-track", "description": "", "attributes": [{"name": "patientId", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "dataList", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "isLast", "value": {"kind": "expression", "type": "boolean"}}, {"name": "loading", "value": {"kind": "expression", "type": "boolean"}}, {"name": "nonePadding", "value": {"kind": "expression", "type": "boolean"}}, {"name": "filterList", "value": {"kind": "expression", "type": "array"}, "default": "[\n    ActionStatus.OUTPATIENT_STATUS,\n    ActionStatus.RETAIL_STATUS,\n    ActionStatus.FOLLOW_UP_STATUS,\n    ActionStatus.REGISTER_APPOINTMENT_STATUS,\n    ActionStatus.INHOSPITAL_STATUS,\n]"}], "events": [{"name": "handleOpenDetail"}, {"name": "fetchRevisitQuickList"}, {"name": "openAppointmentCardFromTrack"}], "source": {"module": "@/components-composite/biz-crm-track/src/views/index.vue", "symbol": "default"}}, {"name": "biz-custom-header", "description": "", "attributes": [{"name": "value", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "<PERSON><PERSON><PERSON>", "required": true, "description": "弹窗名", "value": {"kind": "expression", "type": "string"}}, {"name": "table<PERSON><PERSON>", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "showFixed", "description": "是否展示固定列", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "mode", "description": "是否可拖动顺序，fixed模式下不可拖动, draggle模式下可拖动, 默认fixed", "value": {"kind": "expression", "type": "string"}, "default": "'fixed'"}, {"name": "maxFixedCount", "description": "最大固定列数量", "value": {"kind": "expression", "type": "number"}, "default": "6"}, {"name": "valueProps", "description": "传入设置项的 key 值", "value": {"kind": "expression", "type": "object"}, "default": "() => {}"}, {"name": "regionId", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "tableHeaderApi", "value": {"kind": "expression", "type": "object"}, "default": "null"}, {"name": "fetchFunc", "description": "自定义配置拉取函数", "value": {"kind": "expression", "type": "func"}, "default": "null"}, {"name": "resetFunc", "description": "自定义配置重置函数", "value": {"kind": "expression", "type": "func"}, "default": "null"}, {"name": "saveFunc", "description": "自定义配置保存函数", "value": {"kind": "expression", "type": "func"}, "default": "null"}, {"name": "finishFunc", "value": {"kind": "expression", "type": "func"}, "default": "null"}, {"name": "closeFunc", "value": {"kind": "expression", "type": "func"}, "default": "null"}], "events": [{"name": "input"}], "source": {"module": "@/components-composite/biz-custom-header/src/views/index.vue", "symbol": "default"}}, {"name": "biz-customization-options", "description": "", "attributes": [{"name": "value", "description": "弹窗是否展示", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "title", "description": "标题，设置空字符串可以不展示，参考dialog组件", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "showIcon", "description": "是否展示icon", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "defaultIcon", "description": "默认icon图标", "value": {"kind": "expression", "type": "string"}, "default": "'s-user-color'"}, {"name": "closeOnClickModal", "description": "开启后点击蒙层也会关弹窗，参考dialog组件", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "size", "description": "dialog size 影响宽度，参考dialog组件\ndefault,\nsmall: 360px\nmedium: 420px\nlarge: 640px\nxlarge: 780px\nhuge: 960px\nhugely: 1200px", "value": {"kind": "expression", "type": "string"}, "values": ["default", "small", "medium", "large", "xlarge", "huge", "hugely"], "default": "'large'"}, {"name": "responsive", "description": "是否响应式，参考dialog组件，响应式规则：\nwidth: 80vw，当 size 指定为 hugely 时，宽度固定为 1200px\nheight: 90vh 当 size 指定为 hugely 时，宽度固定为 76vh\nmin-width: 1200px\n弹窗中心点，距离顶部 50%", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "sortable", "description": "是否支持排序", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "disabledDelete", "description": "是否禁用删除", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "dataList", "description": "数据源，格式如下：\n{\n    id: string,\n    content: string,\n    sort: number,\n    icon?: string,\n    disabled?: boolean,\n    disableDelete?: boolean,\n}\nid: 唯一标识\ncontent: 内容\nsort: 排序序号，从0开始\nicon: 图标，不支持排序时可展示该图标\ndisabled: 是否禁用，禁用后不能编辑不能删除\ndisableDelete: 是否禁用删除", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "singleRowMaxLength", "description": "编辑/新增时可输入的最大字数", "value": {"kind": "expression", "type": "number"}, "default": "500"}, {"name": "max<PERSON><PERSON><PERSON>", "description": "最大条数", "value": {"kind": "expression", "type": "number"}, "default": "20"}, {"name": "onClose", "description": "关闭弹窗的回调", "value": {"kind": "expression", "type": "func"}, "default": "() => {}"}, {"name": "onSubmit", "required": true, "description": "确定的回调，入参有2个，第1个是调整后的数据源，第2个是被删除的数据源", "value": {"kind": "expression", "type": "func"}}, {"name": "addNewItemFunc", "description": "新增item的回调\n需要返回一个JSON对象，可选属性参考dataList\n如果未传递改回调，则默认新增的格式是 { content: string, id: string, sort: number }", "value": {"kind": "expression", "type": "func"}, "default": "null"}], "events": [{"name": "input"}], "slots": [{"name": "editor"}, {"name": "content"}], "source": {"module": "@/components-composite/biz-customization-options/src/views/index.vue", "symbol": "default"}}, {"name": "biz-data-statistics-card", "description": "", "attributes": [{"name": "list", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "size", "description": "abc-statistic title字体大小", "value": {"kind": "expression", "type": "string"}, "values": ["normal", "large", "largex", "xlarge", "xxlarge", "xxxlarge"], "default": "'xxlarge'"}], "slots": [{"name": "prependInner"}, {"name": "append"}], "source": {"module": "@/components-composite/biz-data-statistics-card/src/views/index.vue", "symbol": "default"}}, {"name": "biz-dispenser-selector", "description": "", "attributes": [{"name": "value", "required": true, "description": "选中的发药员ID列表", "value": {"kind": "expression", "type": "array|object"}}, {"name": "dispensedByEmployee", "description": "默认发药员列表", "value": {"kind": "expression", "type": "array|object"}, "default": "() => []"}, {"name": "employees", "required": true, "description": "可选的发药员列表", "value": {"kind": "expression", "type": "array"}}, {"name": "disabled", "description": "是否禁用", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "isDefaultDispensedByEmployeeModified", "description": "默认发药员是否可修改", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}], "events": [{"name": "change"}], "source": {"module": "@/components-composite/biz-dispenser-selector/src/views/index.vue", "symbol": "default"}}, {"name": "biz-employee-panel-selector", "description": "", "attributes": [{"name": "value", "description": "选中的员工列表，支持 v-model 双向绑定", "value": {"kind": "expression", "type": "Array<Employee>"}, "default": "[]"}, {"name": "addText", "description": "添加按钮的文本", "value": {"kind": "expression", "type": "string"}, "default": "'添加'"}, {"name": "searchPlaceholder", "description": "搜索框的占位文本", "value": {"kind": "expression", "type": "string"}, "default": "'搜索姓名'"}, {"name": "resultTitle", "description": "结果面板的标题", "value": {"kind": "expression", "type": "string"}, "default": "'请选择'"}, {"name": "employees", "description": "员工数据列表", "value": {"kind": "expression", "type": "Array<Employee>"}, "default": "[]"}, {"name": "defaultCheckedKeys", "description": "默认选中的员工ID列表", "value": {"kind": "expression", "type": "Array<string>"}, "default": "[]"}], "events": [{"name": "input"}, {"name": "confirm"}], "source": {"module": "@/components-composite/biz-employee-panel-selector/src/views/index.vue", "symbol": "default"}}, {"name": "biz-exam-business-tag", "description": "", "attributes": [{"name": "cloudSupplierFlag", "value": {"kind": "expression", "type": "number|string"}, "default": "0"}, {"name": "coopFlag", "value": {"kind": "expression", "type": "number|string"}, "default": "undefined"}, {"name": "type", "value": {"kind": "expression", "type": "number|string"}, "default": "GoodsTypeEnum.EXAMINATION"}, {"name": "subType", "value": {"kind": "expression", "type": "number|string"}, "default": "GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test"}, {"name": "isCloudTag", "value": {"kind": "expression", "type": "boolean"}}, {"name": "isOutSourcingTag", "value": {"kind": "expression", "type": "boolean"}}, {"name": "isText", "value": {"kind": "expression", "type": "boolean"}}], "source": {"module": "@/components-composite/biz-exam-business-tag/src/views/index.vue", "symbol": "default"}}, {"name": "biz-express-address-selector", "description": "", "attributes": [{"name": "placeholder", "description": "输入框的placeholder", "value": {"kind": "expression", "type": "string"}, "default": "() => '请选择收货地址'"}, {"name": "disabled", "description": "是否禁用", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "value", "description": "当前的地址信息", "value": {"kind": "expression", "type": "object"}}, {"name": "addressList", "description": "地址列表", "value": {"kind": "expression", "type": "array"}, "default": "[]"}], "events": [{"name": "create"}, {"name": "select"}, {"name": "edit"}], "source": {"module": "@/components-composite/biz-express-address-selector/src/views/index.vue", "symbol": "default"}}, {"name": "biz-goods-info-tag-group", "description": "", "attributes": [{"name": "productInfo", "value": {"kind": "expression", "type": "object"}}, {"name": "showAntibiotic", "description": "是否展示限制标签", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "showDangerIngredient", "description": "是否展示成分标签", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "showNormalGoodsTag", "description": "是否展示普通标签", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "isFoldTags", "description": "是否根据父组件宽度计算折叠标签", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}], "source": {"module": "@/components-composite/biz-goods-info-tag-group/src/views/index.vue", "symbol": "default"}}, {"name": "biz-goods-select-dialog", "description": "", "attributes": [{"name": "categoryList", "description": "目录 - 左侧目录列表-只支持两级，协议：[name: '检查项目', value: 1, subCategoryList: [name: 'CT', value: 2]]", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "defaultCategoryValue", "description": "目录 - 左侧默认选中的的分类", "value": {"kind": "expression", "type": "number"}}, {"name": "getCategoryFn", "description": "目录 - 自定义获取目录，< categoryList >", "value": {"kind": "expression", "type": "func"}}, {"name": "fetchFn", "required": true, "description": "list-查询方法（切换分类、header search）", "value": {"kind": "expression", "type": "func"}}, {"name": "featureSupportFilterEyeGlasses", "description": "list - viewDistribute属性-预留眼镜查询栏", "value": {"kind": "expression", "type": "boolean"}}, {"name": "disableNoStockGoods", "description": "$store.getters开关-关键字搜索模式下影响item判断库存", "value": {"kind": "expression", "type": "boolean"}}, {"name": "feeTypeName", "description": "费别-item中判断医保自付比例参数", "value": {"kind": "expression", "type": "string"}}, {"name": "patient", "description": "患者-item中判断医保自付比例参数", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "onSubmit", "description": "确定回调", "value": {"kind": "expression", "type": "func"}}], "events": [{"name": "onSelectGoods"}], "source": {"module": "@/components-composite/biz-goods-select-dialog/src/views/index.vue", "symbol": "default"}}, {"name": "biz-goods-type-cascader", "description": "", "attributes": [{"name": "value", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "cascaderConfig", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "goodsTypeOptions", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "isAdapterData", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}], "events": [{"name": "input"}, {"name": "change"}], "slots": [{"name": "default"}], "source": {"module": "@/components-composite/biz-goods-type-cascader/src/views/index.vue", "symbol": "default"}}, {"name": "biz-grid-panel-selector", "description": "", "attributes": [{"name": "dataSource", "description": "数据源\neg. [{\n    title: '',\n    list: [\n        {label: '测一', value: '测一'},\n        {label: '测二', value: '测二'},\n    ]\n}]", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "labelField", "description": "列表展示数据-label字段名", "value": {"kind": "expression", "type": "string"}, "default": "'label'"}, {"name": "valueField", "description": "列表展示数据-value字段名", "value": {"kind": "expression", "type": "string"}, "default": "'value'"}, {"name": "columns", "description": "列数-默认4列", "value": {"kind": "expression", "type": "number"}, "default": "4"}, {"name": "width", "description": "select-input组件宽度，默认80", "value": {"kind": "expression", "type": "number|string"}, "default": "80"}, {"name": "panelWidth", "description": "面板宽度-默认352", "value": {"kind": "expression", "type": "number|string"}, "default": "352"}, {"name": "placeholder", "description": "select-input组件placeholder", "value": {"kind": "expression", "type": "string"}, "default": "'请选择'"}, {"name": "value", "description": "绑定的value，单选模式[String, Number],多选模式[Array]", "value": {"kind": "expression", "type": "string|number|array"}, "default": "null"}, {"name": "onlyBottomBorder", "description": "是否只展示下边框", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "clearable", "description": "是否可清除", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "multiple", "description": "是否多选", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "disabled", "description": "是否禁用", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "editable", "description": "是否可编辑，单选模式下使用，多选模式下无效", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "stopEmitEnter", "description": "用于防止在panel展开时回车emit enter事件，默认false", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}], "events": [{"name": "input"}, {"name": "change"}, {"name": "enter"}, {"name": "clear"}], "source": {"module": "@/components-composite/biz-grid-panel-selector/src/views/index.vue", "symbol": "default"}}, {"name": "biz-inspect-report-list", "description": "", "attributes": [{"name": "value", "required": true, "value": {"kind": "expression", "type": "array"}}, {"name": "alone", "description": "独占一行", "value": {"kind": "expression", "type": "boolean"}}, {"name": "customStyles", "value": {"kind": "expression", "type": "object"}}], "events": [{"name": "checkReport"}], "source": {"module": "@/components-composite/biz-inspect-report-list/src/views/index.vue", "symbol": "default"}}, {"name": "biz-marketing-button", "description": "", "attributes": [{"name": "variant", "description": "按钮尺寸\nnormal 普通(渐变) light 亮 ghost 暗", "value": {"kind": "expression", "type": "string"}, "default": "'normal'"}, {"name": "size", "description": "按钮尺寸", "value": {"kind": "expression", "type": "string"}, "values": ["small", "normal", "large", "xlarge"], "default": "'large'"}, {"name": "width", "description": "按钮宽度", "value": {"kind": "expression", "type": "string|number"}, "default": "'100%'"}, {"name": "disabled", "description": "禁用", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "buttonText", "description": "按钮文本", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "discountText", "description": "折扣文本", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "zIndex", "value": {"kind": "expression", "type": "number"}, "default": "1"}, {"name": "customStyle", "value": {"kind": "expression", "type": "object"}, "default": "() => {}"}], "events": [{"name": "click"}], "source": {"module": "@/components-composite/biz-marketing-button/src/views/index.vue", "symbol": "default"}}, {"name": "biz-mixed-selection-filter", "description": "", "attributes": [{"name": "value", "value": {"kind": "expression", "type": "number|string|array"}}, {"name": "type", "value": {"kind": "expression", "type": "string"}, "values": ["checkbox-button", "radio", "checkbox"], "default": "'checkbox-button'"}, {"name": "options", "description": "选项配置\n{\n     label: any, // 设置文案\n     value: any,  // 选中值\n     disabled: boolean, // 禁用态\n     statisticsNumber: number, // 统计数据\n}", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "gap", "value": {"kind": "expression", "type": "number"}, "default": "24"}, {"name": "externalConfig", "value": {"kind": "expression", "type": "object"}, "default": "{}"}], "events": [{"name": "input"}, {"name": "change"}], "source": {"module": "@/components-composite/biz-mixed-selection-filter/src/views/index.vue", "symbol": "default"}}, {"name": "biz-outpatient-history-card", "description": "", "attributes": [{"name": "loading", "value": {"kind": "expression", "type": "boolean"}}, {"name": "customStyle", "value": {"kind": "expression", "type": "object"}}, {"name": "detail", "required": true, "value": {"kind": "expression", "type": "object"}}, {"name": "hasPermission", "value": {"kind": "expression", "type": "boolean"}}, {"name": "showOperationBtnGroup", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "hasCopy", "value": {"kind": "expression", "type": "boolean"}}, {"name": "hasCopyMr", "value": {"kind": "expression", "type": "boolean"}}, {"name": "canViewHistoryPr", "value": {"kind": "expression", "type": "boolean"}}, {"name": "canViewHistoryGlassesPr", "value": {"kind": "expression", "type": "boolean"}}, {"name": "clientWidth", "value": {"kind": "expression", "type": "number"}, "default": "1920"}, {"name": "showEditBtn", "value": {"kind": "expression", "type": "boolean"}}, {"name": "printOptions", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "showPrintMr", "value": {"kind": "expression", "type": "boolean"}}, {"name": "canCopyForm", "value": {"kind": "expression", "type": "boolean"}}, {"name": "doctorAdviceInForm", "value": {"kind": "expression", "type": "boolean"}}, {"name": "isSupportSurgery", "value": {"kind": "expression", "type": "boolean"}}, {"name": "showTotalPrice", "value": {"kind": "expression", "type": "boolean"}}, {"name": "formatDiagnosisTreatmentUnit", "value": {"kind": "expression", "type": "func"}}], "events": [{"name": "copy"}, {"name": "print"}, {"name": "edit"}, {"name": "copySingleForm"}, {"name": "viewReport"}, {"name": "previewIt"}, {"name": "openSurgeryDetailDialog"}], "source": {"module": "@/components-composite/biz-outpatient-history-card/src/views/index.vue", "symbol": "default"}}, {"name": "biz-panel-selector", "description": "", "attributes": [{"name": "value", "description": "选中的列表，支持 v-model 双向绑定", "value": {"kind": "expression", "type": "Array<Employee>"}, "default": "[]"}, {"name": "addText", "description": "添加按钮的文本", "value": {"kind": "expression", "type": "string"}, "default": "'添加'"}, {"name": "icon", "description": "tag图标", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "formatItemName", "description": "item名称format函数", "value": {"kind": "expression", "type": "Function"}, "default": "(item) => item.name"}, {"name": "customRenderIcon", "value": {"kind": "expression", "type": "boolean"}}, {"name": "maxNameTextLength", "value": {"kind": "expression", "type": "number"}, "default": "3"}, {"name": "isMatchAll", "description": "匹配全部时显示全部", "value": {"kind": "expression", "type": "Number"}, "default": "0"}], "events": [{"name": "input"}, {"name": "add"}, {"name": "deleteItem"}], "source": {"module": "@/components-composite/biz-panel-selector/src/views/index.vue", "symbol": "default"}}, {"name": "biz-patient-card", "description": "", "attributes": [{"name": "abcSocialSecurity", "value": {"kind": "expression", "type": "object"}, "default": "null"}, {"name": "supportOneClickBilling", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "familyDoctor", "value": {"kind": "expression", "type": "object"}, "default": "null"}, {"name": "isOpenFamilyDoctor", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "isOpenSocial", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "allowRechargeCard", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "patientId", "value": {"kind": "expression", "type": "string|number"}, "default": "''"}, {"name": "coverTop", "value": {"kind": "expression", "type": "number"}, "default": "0"}, {"name": "patientInfo", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "consultantList", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "isAddPatient", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "hasConsultant", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "userInfo", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "showRecommendation", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "crmConfigList", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "origin<PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "viewDistributeConfig", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "isSingleStore", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "currentClinic", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "crmAPI", "value": {"kind": "expression", "type": "func"}, "default": "null"}, {"name": "currencySymbol", "value": {"kind": "expression", "type": "string"}, "default": "'¥'"}, {"name": "needPatientBaseComponentShowTags", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "isCanSeePatientMobile", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "sourceList", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "cascaderValue", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "disabledModifyFirstSourceText", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "isEnableChildHealth", "value": {"kind": "expression", "type": "number"}, "default": "0"}, {"name": "hasMemberCardAdminModule", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "hasMemberModule", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "hasChargeModule", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "isOpenMp", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "number|string"}, "default": "460"}, {"name": "cardHeight", "value": {"kind": "expression", "type": "number|string"}, "default": "778"}], "events": [{"name": "change-patient-tags"}, {"name": "updateTagsByFetch"}, {"name": "handleCascaderValueChange"}, {"name": "handleCascaderVisible"}, {"name": "handleCascaderMouseEnter"}, {"name": "handleCascaderMouseLeave"}, {"name": "update:cascaderValue"}, {"name": "handleReCharge"}, {"name": "handleVisitSourceEdit"}, {"name": "update-patient-info"}, {"name": "change-patient"}, {"name": "add-patient"}, {"name": "function-click"}, {"name": "close-tag"}, {"name": "change-tag"}], "slots": [{"name": "default"}], "source": {"module": "@/components-composite/biz-patient-card/src/views/index.vue", "symbol": "default"}}, {"name": "biz-project-multiple-select", "description": "", "attributes": [{"name": "value", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "fetchFn", "required": true, "description": "获取推荐项目的方法", "value": {"kind": "expression", "type": "func"}, "default": "() => {}"}], "events": [{"name": "input"}], "source": {"module": "@/components-composite/biz-project-multiple-select/src/views/index.vue", "symbol": "default"}}, {"name": "biz-quick-list", "description": "", "attributes": [{"name": "tools", "description": "配置下面的小工具栏\n{\n  label: '上机检验',\n  describe: '仪器上机检验',\n  icon: 's-flask-color',\n  iconColor: '#0072F9',\n  handler: () => {\n     console.log('');\n  },\n}", "value": {"kind": "expression", "type": "array"}}, {"name": "loading", "value": {"kind": "expression", "type": "boolean"}}, {"name": "isLast", "value": {"kind": "expression", "type": "boolean"}}, {"name": "showEmpty", "description": "showEmpty && !loading\n就会展示 “暂无患者”\n!showEmpty && isLast\n就会展示 “没有更多了”", "value": {"kind": "expression", "type": "boolean"}}, {"name": "quickFooterTabs", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "defaultToolTab", "value": {"kind": "expression", "type": "number"}, "default": "0"}, {"name": "enableVirtual", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "quickListData", "value": {"kind": "expression", "type": "array"}}, {"name": "quickListTotal", "value": {"kind": "expression", "type": "number"}}, {"name": "virtualListConfig", "value": {"kind": "expression", "type": "object"}, "default": "{\n    visibleCount: 20,\n    rowHeight: 48,\n    bufferSize: 40,\n}"}, {"name": "scrollLoadFunc", "value": {"kind": "expression", "type": "func"}}], "events": [{"name": "scroll-load"}, {"name": "change-footer-tab"}], "slots": [{"name": "search"}, {"name": "operate"}, {"name": "quickListTop"}, {"name": "quickListItem"}, {"name": "default"}, {"name": "quickFooter"}, {"name": "customTools"}, {"name": "customToolsContent"}], "source": {"module": "@/components-composite/biz-quick-list/src/views/index.vue", "symbol": "default"}}, {"name": "biz-quick-list-item", "description": "", "attributes": [{"name": "quickItem", "required": true, "value": {"kind": "expression", "type": "object"}}, {"name": "draftList", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "patientHeadImgUrl", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "contentClass", "value": {"kind": "expression", "type": "array|object|string"}, "default": "() => ''"}, {"name": "showStatus", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "statusClass", "value": {"kind": "expression", "type": "array|object|string"}, "default": "() => ''"}, {"name": "isDone", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "isActive", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "withDescribe", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "iconName", "value": {"kind": "expression", "type": "string"}}], "events": [{"name": "select"}], "slots": [{"name": "prepend"}, {"name": "patient-name"}, {"name": "patient-name-append"}, {"name": "content"}, {"name": "status"}, {"name": "abstract"}, {"name": "date"}], "source": {"module": "@/components-composite/biz-quick-list-item/src/views/index.vue", "symbol": "default"}}, {"name": "biz-quick-options-panel", "description": "", "attributes": [{"name": "width", "description": "面板的宽度，可选", "value": {"kind": "expression", "type": "string|number"}, "default": "'100%'"}, {"name": "contentMaxHeight", "description": "面板的最大高度，不传默认根据内容撑开", "value": {"kind": "expression", "type": "string|number"}, "default": "''"}, {"name": "tabsValue", "description": "tabs 的值", "value": {"kind": "expression", "type": "string|number"}, "default": "''"}, {"name": "tabsOptions", "description": "tabs 的选项列表，不传则没有 tabs", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "labelPosition", "description": "分组 label 的位置，可选值：top、left", "value": {"kind": "expression", "type": "string"}, "values": ["top", "left"], "default": "'top'"}, {"name": "labelWidth", "description": "分组 label 的宽度，不传默认根据内容撑开", "value": {"kind": "expression", "type": "number|string"}, "default": "''"}, {"name": "showSplitLine", "description": "是否展示分组之间的间隔线", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "list", "description": "内容列表，label 省略则不展示标题，showSplitLine 表示该组后单独跟分割线(即使 showSplitLine 为 false 也会有分割线)，list 中的 json 对象的 label key 可以随意更换，只要与 itemLabel props 保持一致即可\neg:\n[\n    {\n        'label': '既往有',\n        'showSplitLine': true,\n        'list': [\n            { label: '既往有高血压', value: '既往有高血压' },\n            { label: '高血脂', value: '高血脂' },\n            { label: '心脏病', value: '心脏病' },\n            { label: '糖尿病', value: '糖尿病' },\n            { label: '精神疾病', value: '精神疾病' },\n            { label: '脑梗死', value: '脑梗死', isBreak: true },\n            { label: '既往有肝炎', value: '既往有肝炎' },\n            { label: '胃炎', value: '胃炎' },\n            { label: '肺结核', value: '肺结核' },\n            { label: '哮喘', value: '哮喘', isSplit: true },\n            { label: '鼻炎', value: '鼻炎' },\n            { label: '甲亢', value: '甲亢' },\n            { label: '既往有高血压', value: '既往有高血压' },\n            { label: '高血脂', value: '高血脂' },\n            { label: '心脏病', value: '心脏病' },\n            { label: '糖尿病', value: '糖尿病' },\n            { label: '精神疾病', value: '精神疾病' },\n            { label: '脑梗死', value: '脑梗死' },\n            { label: '既往有肝炎', value: '既往有肝炎' },\n            { label: '胃炎', value: '胃炎' },\n            { label: '肺结核', value: '肺结核' },\n            { label: '哮喘', value: '哮喘' },\n            { label: '鼻炎', value: '鼻炎' },\n            { label: '甲亢', value: '甲亢' }\n        ]\n    },\n    {\n        'label': '个人史',\n        'list': [\n            { label: '吸烟', value: '吸烟' },\n            { label: '偶尔吸烟', value: '偶尔吸烟' },\n            { label: '长期吸烟', value: '长期吸烟' isSplit: true },\n            { label: '不饮酒', value: '不饮酒' },\n            { label: '偶尔饮酒', value: '偶尔饮酒' },\n            { label: '长期饮酒', value: '长期饮酒', isBreak: true },\n            { label: '未婚', value: '未婚' },\n            { label: '已婚', value: '已婚' },\n            { label: '未孕', value: '未孕' },\n            { label: '备孕', value: '备孕' },\n            { label: '怀孕', value: '怀孕' },\n            { label: '闭经', value: '闭经', isBreak: true },\n            { label: '有早产史', value: '有早产史' },\n            { label: '有流产史', value: '有流产史' }\n        ]\n    }\n]", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "vertical", "description": "内容排列方向，true: 竖向排列，false: 横向排列(或者不传)", "value": {"kind": "expression", "type": "boolean|number"}, "default": "false"}, {"name": "itemLabel", "description": "与 list props 搭配使用，表示面板展示的文案取 list 中 json 元素指定的 key\neg:\nlist: [\n     {\n         label: '标题',\n         value: '内容',\n     }\n]\nitemLabel: 'label'\n那么第一个选项就会展示 list[0][itemLabel]", "value": {"kind": "expression", "type": "string"}, "default": "'label'"}, {"name": "showSetting", "description": "是否展示 Settings, 触发事件 onSettingClick, 也可以自行传入 setting 插槽, 插槽优先级高于 showSetting", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "closeDataCy", "description": "自定义关闭按钮的 data-cy", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "customHeaderStyle", "description": "自定义头部样式", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "itemSize", "value": {"kind": "expression", "type": "string"}, "values": ["small", "medium"], "default": "'small'"}], "events": [{"name": "select"}, {"name": "onSettingClick"}, {"name": "update:tabsValue"}, {"name": "changeTabs"}, {"name": "close"}], "slots": [{"name": "header"}, {"name": "default"}, {"name": "item.groupSlotName"}, {"name": "extra"}, {"name": "setting"}], "source": {"module": "@/components-composite/biz-quick-options-panel/src/views/index.vue", "symbol": "default"}}, {"name": "biz-select-tabs", "description": "", "attributes": [{"name": "value", "value": {"kind": "expression", "type": "number|string|boolean"}}, {"name": "size", "description": "大小，支持 large/medium/small/tiny", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "width", "value": {"kind": "expression", "type": "number"}, "default": "148"}, {"name": "placeholder", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "dataCy", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "options", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "max<PERSON><PERSON><PERSON>", "description": "下拉选项最大宽度", "value": {"kind": "expression", "type": "number|string"}}, {"name": "maxHeight", "description": "下拉选项最大高度", "value": {"kind": "expression", "type": "number|string"}, "default": "308"}, {"name": "innerWidth", "value": {"kind": "expression", "type": "number|string"}, "default": "240"}, {"name": "inputColor", "value": {"kind": "expression", "type": "string"}, "default": "'#005ED9'"}, {"name": "triggerIcon", "description": "触发icon", "value": {"kind": "expression", "type": "string"}, "default": "'s-triangle-select-color'"}], "events": [{"name": "input"}, {"name": "change"}], "source": {"module": "@/components-composite/biz-select-tabs/src/views/index.vue", "symbol": "default"}}, {"name": "biz-tag-selector", "description": "", "attributes": [{"name": "patientId", "value": {"kind": "expression", "type": "string|number"}, "default": "''"}, {"name": "patientInfo", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "addPatient", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "origin<PERSON><PERSON><PERSON>", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "viewDistributeConfig", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "isSingleStore", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "currentClinic", "value": {"kind": "expression", "type": "object"}, "default": "{}"}, {"name": "crmAPI", "value": {"kind": "expression", "type": "func"}, "default": "() => ({})"}], "events": [{"name": "changeShowTagPopover"}, {"name": "change-patient-tags"}, {"name": "updateTagsByFetch"}], "slots": [{"name": "default"}], "source": {"module": "@/components-composite/biz-tag-selector/src/views/index.vue", "symbol": "default"}}, {"name": "biz-value-added-card", "description": "", "attributes": [{"name": "title", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "content", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "openStatus", "description": "1:开通中 2:未开通 默认:0不显示", "value": {"kind": "expression", "type": "number"}, "default": "0"}, {"name": "btnText", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "plainBtnText", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "icon", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "iconColor", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "theme", "value": {"kind": "expression", "type": "string"}, "values": ["yellow", "blue"], "default": "'yellow'"}], "events": [{"name": "click"}, {"name": "plain-click"}], "slots": [{"name": "prepend"}], "source": {"module": "@/components-composite/biz-value-added-card/src/views/index.vue", "symbol": "default"}}, {"name": "biz-version-tips", "description": "", "attributes": [{"name": "text", "description": "提示文字", "value": {"kind": "expression", "type": "string"}, "default": "'旗舰版及以上支持'"}, {"name": "btnText", "description": "按钮文字", "value": {"kind": "expression", "type": "string"}, "default": "'去升级'"}, {"name": "showButton", "description": "是否显示按钮", "value": {"kind": "expression", "type": "boolean"}, "default": "true"}, {"name": "disabled", "description": "按钮是否禁用", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}], "events": [{"name": "click"}], "source": {"module": "@/components-composite/biz-version-tips/src/views/index.vue", "symbol": "default"}}, {"name": "biz-week-schedule", "description": "", "attributes": [{"name": "variant", "value": {"kind": "expression", "type": "string"}, "values": ["outline", "fill"], "default": "'outline'"}, {"name": "border", "description": "边框 - 是否需要外边框", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "radius", "description": "圆角 - 指定圆角，直接传border-radius的值：'4px 2px 4px 2px'", "value": {"kind": "expression", "type": "string"}}, {"name": "startDate", "description": "开始日期，日期字符串即可，可被new Date使用", "value": {"kind": "expression", "type": "string"}, "default": "''"}, {"name": "data", "description": "整个周的日期数据\n[\n {\n     xxx,\n     schedules: []   // 对应scheduleKey\n }\n]", "value": {"kind": "expression", "type": "array"}, "default": "[]"}, {"name": "prependWidth", "description": "第一列宽度，必须指定", "value": {"kind": "expression", "type": "number"}, "default": "100"}, {"name": "emptyText", "description": "显示空数据文案", "value": {"kind": "expression", "type": "string"}, "default": "'暂无数据'"}, {"name": "contentMaxHeight", "description": "内容最大高度，单位 px", "value": {"kind": "expression", "type": "number"}}, {"name": "<PERSON><PERSON><PERSON>", "description": "data数据每一行对应的key", "value": {"kind": "expression", "type": "string"}, "default": "'schedules'"}, {"name": "loading", "description": "loading", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}, {"name": "pagination", "description": "组件是否提供分页\n注意：variant为outline时，无效\n{\n   visible: false,\n   prevDisable: false, -> 会触发prev事件\n   nextDisable: false, -> 会触发next事件\n}", "value": {"kind": "expression", "type": "object"}, "default": "{\n    visible: false,\n    prevDisable: false,\n    nextDisable: false\n}"}, {"name": "enablePersistentScrollbar", "description": "是否常驻滚动条", "value": {"kind": "expression", "type": "boolean"}, "default": "false"}], "events": [{"name": "prev"}, {"name": "next"}], "slots": [{"name": "headerPrepend"}, {"name": "bodyPrepend"}, {"name": "default"}], "source": {"module": "@/components-composite/biz-week-schedule/src/views/index.vue", "symbol": "default"}}]}}}