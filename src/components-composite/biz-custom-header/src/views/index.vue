<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        append-to-body
        size="huge"
        :title="dialogTitle"
        content-styles="padding: 0; height: 80vh"
        header-size="large"
        class="custom-header-wrapper"
    >
        <abc-flex v-abc-loading="loading" class="custom-header-wrapper__content">
            <abc-flex
                vertical
                :gap="24"
                class="custom-header-wrapper__left-content"
            >
                <section v-for="group in tableList" :key="group.label">
                    <abc-text theme="gray" class="stat__custom-header-group-title">
                        {{ group.label }}
                    </abc-text>
                    <abc-row gutter="middle" :wrap="true" class="stat__custom-header-group">
                        <abc-col
                            v-for="(item, index) in group.children"
                            :key="index"
                            :span="6"
                        >
                            <abc-checkbox
                                v-model="item.isSelected"
                                :disabled="item.disabled"
                                @change="handleSelectedChange($event, item)"
                            >
                                <span
                                    :title="item.label"
                                    class="ellipsis"
                                    style="max-width: 130px;"
                                >
                                    {{ item.label }}
                                    <abc-popover
                                        v-if="item && item.description"
                                        trigger="hover"
                                        placement="top"
                                        style="display: inline-block; line-height: 32px; color: #e6eaee;"
                                        theme="yellow"
                                    >
                                        <abc-icon slot="reference" icon="info_bold" size="12"></abc-icon>
                                        <div>
                                            {{ item.description }}
                                        </div>
                                    </abc-popover>
                                </span>
                            </abc-checkbox>
                        </abc-col>
                    </abc-row>
                </section>
            </abc-flex>
            <abc-flex vertical class="custom-header-wrapper__right-content">
                <section v-if="showFixed">
                    <div class="custom-header-wrapper__right-content-label">
                        <abc-text theme="gray">
                            固定列
                            <abc-tooltip-info placement="right">
                                <div>
                                    将指定列固定，不会因滚动页面而移动位置
                                </div>
                            </abc-tooltip-info>
                        </abc-text>
                    </div>
                    <ul class="custom-header-wrapper__right-content-list">
                        <template v-if="draggable">
                            <draggable
                                v-model="selectedFixedColumnList"
                                :group="fixedGroupKey"
                                @add="val => handleDraggleField(val, true)"
                            >
                                <template v-if="selectedFixedColumnList && selectedFixedColumnList.length">
                                    <li
                                        v-for="fixedItem in selectedFixedColumnList"
                                        :key="fixedItem.key"
                                        :class="[
                                            'stat__custom-header-selected-item',
                                            'stat__custom-header-selected-item-drag',
                                        ]"
                                        @mouseenter="handleMouseEnter(fixedItem)"
                                        @mouseleave="handleMouseLeave(fixedItem)"
                                    >
                                        <abc-flex justify="space-between" align="center">
                                            <abc-flex align="center" gap="small">
                                                <abc-icon
                                                    icon="s-dots-line"
                                                    size="16"
                                                    :color="fixedItem.enableEmployeeModify ? 'var(--abc-color-T3)' : 'var(--abc-color-P1)'"
                                                ></abc-icon>
                                                <abc-text
                                                    :theme="fixedItem.enableEmployeeModify ? 'black' : 'gray-light'"
                                                    class="ellipsis"
                                                    :title="fixedItem.label"
                                                    style="max-width: 120px;"
                                                >
                                                    {{ fixedItem.label }}
                                                </abc-text>
                                            </abc-flex>
                                            <abc-delete-icon
                                                v-if="activeKey === fixedItem.key && deleteIconVisible && fixedItem.enableEmployeeModify"
                                                size="large"
                                                theme="dark"
                                                @delete="handleDeleteField(fixedItem, true)"
                                            ></abc-delete-icon>
                                        </abc-flex>
                                    </li>
                                </template>
                                <li v-else class="stat__custom-header-selected-item">
                                    <div>
                                        <abc-text theme="gray-light">
                                            将字段拖拽到此处
                                        </abc-text>
                                    </div>
                                </li>
                            </draggable>
                        </template>
                        <template v-else>
                            <li
                                v-for="item in selectedFixedColumnList"
                                :key="item.key"
                                :class="[
                                    'stat__custom-header-selected-item',
                                    { 'stat__custom-header-selected-item-disable': !item.enableEmployeeModify }
                                ]"
                                @mouseenter="handleMouseEnter(item)"
                                @mouseleave="handleMouseLeave(item)"
                            >
                                <abc-flex justify="space-between" align="center">
                                    <abc-flex align="center" gap="small">
                                        <abc-icon
                                            icon="s-lock-line"
                                            size="16"
                                            :color="item.enableEmployeeModify ? 'var(--abc-color-T3)' : 'var(--abc-color-P1)'"
                                        ></abc-icon>
                                        <abc-text
                                            :theme="item.enableEmployeeModify ? 'black' : 'gray-light'"
                                            class="ellipsis"
                                            :title="item.label"
                                            style="max-width: 120px;"
                                        >
                                            {{ item.label }}
                                        </abc-text>
                                    </abc-flex>
                                    <abc-delete-icon
                                        v-if="activeKey === item.key && deleteIconVisible && item.enableEmployeeModify"
                                        size="large"
                                        theme="dark"
                                        @delete="handleDeleteField(item, true)"
                                    ></abc-delete-icon>
                                </abc-flex>
                            </li>
                        </template>
                    </ul>
                </section>
                <section>
                    <div class="custom-header-wrapper__right-content-label">
                        <abc-text theme="gray">
                            {{ showFixed ? '其他列' : '配置列' }}
                        </abc-text>
                    </div>
                    <ul class="custom-header-wrapper__right-content-list">
                        <template v-if="draggable">
                            <draggable
                                v-model="selectedCommonColumnList"
                                group="selected"
                                @add="handleDraggleField"
                            >
                                <li
                                    v-for="selectedItem in selectedCommonColumnList"
                                    :key="selectedItem.key"
                                    :class="[
                                        'stat__custom-header-selected-item',
                                        'stat__custom-header-selected-item-drag',
                                    ]"
                                    @mouseenter="handleMouseEnter(selectedItem)"
                                    @mouseleave="handleMouseLeave(selectedItem)"
                                >
                                    <abc-flex justify="space-between" align="center">
                                        <abc-flex align="center" gap="small">
                                            <abc-icon
                                                v-if="draggable"
                                                icon="s-dots-line"
                                                size="16"
                                                :color="selectedItem.enableEmployeeModify ? 'var(--abc-color-T3)' : 'var(--abc-color-P1)'"
                                            ></abc-icon>
                                            <abc-text
                                                :theme="selectedItem.enableEmployeeModify ? 'black' : 'gray-light'"
                                                class="ellipsis"
                                                :title="selectedItem.label"
                                                style="max-width: 120px;"
                                            >
                                                {{ selectedItem.label }}
                                            </abc-text>
                                        </abc-flex>
                                        <abc-delete-icon
                                            v-if="activeKey === selectedItem.key && deleteIconVisible && selectedItem.enableEmployeeModify"
                                            size="large"
                                            theme="dark"
                                            @delete="handleDeleteField(selectedItem)"
                                        ></abc-delete-icon>
                                    </abc-flex>
                                </li>
                            </draggable>
                        </template>
                        <template v-else>
                            <li
                                v-for="selectedItem in selectedCommonColumnList"
                                :key="selectedItem.key"
                                :class="[
                                    'stat__custom-header-selected-item',
                                    { 'stat__custom-header-selected-item-disable': !selectedItem.enableEmployeeModify }
                                ]"
                                @mouseenter="handleMouseEnter(selectedItem)"
                                @mouseleave="handleMouseLeave(selectedItem)"
                            >
                                <abc-flex justify="space-between" align="center">
                                    <abc-flex align="center" gap="small">
                                        <abc-icon
                                            v-if="draggable"
                                            icon="s-dots-line"
                                            size="16"
                                            :color="selectedItem.enableEmployeeModify ? 'var(--abc-color-T3)' : 'var(--abc-color-P1)'"
                                        ></abc-icon>
                                        <abc-text
                                            :theme="selectedItem.enableEmployeeModify ? 'black' : 'gray-light'"
                                            class="ellipsis"
                                            :title="selectedItem.label"
                                            style="max-width: 120px;"
                                        >
                                            {{ selectedItem.label }}
                                        </abc-text>
                                    </abc-flex>
                                    <abc-delete-icon
                                        v-if="activeKey === selectedItem.key && deleteIconVisible && selectedItem.enableEmployeeModify"
                                        size="large"
                                        theme="dark"
                                        @delete="handleDeleteField(selectedItem)"
                                    ></abc-delete-icon>
                                </abc-flex>
                            </li>
                        </template>
                    </ul>
                </section>
            </abc-flex>
        </abc-flex>
        <div slot="footer" class="dialog-footer">
            <abc-button
                type="blank"
                :loading="loading"
                @click="handleReset"
            >
                恢复默认
            </abc-button>
            <div style="flex: 1;"></div>
            <abc-button
                :loading="loading"
                @click="handleConfirm"
            >
                确定
            </abc-button>
            <abc-button
                :loading="loading"
                type="blank"
                @click="visible = false"
            >
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script type="text/ecmascript-6">
    import Draggable from 'vuedraggable';

    export default {
        name: 'BizCustomHeader',
        components: { Draggable },
        props: {
            value: {
                type: Boolean,
                default: false,
            },
            /**
             * 弹窗名
             */
            titleName: {
                type: String,
                required: true,
            },
            tableKey: {
                type: String,
                default: '',
            },
            /**
             * 是否展示固定列
             */
            showFixed: {
                type: Boolean,
                default: true,
            },
            /**
             * 是否可拖动顺序，fixed模式下不可拖动, draggle模式下可拖动, 默认fixed
             */
            mode: {
                type: String,
                default: 'fixed',
            },
            /**
             * 最大固定列数量
             */
            maxFixedCount: {
                type: Number,
                default: 6,
            },
            /**
             * 传入设置项的 key 值
             */
            valueProps: {
                type: Object,
                default: () => {},
            },
            regionId: {
                type: String,
                default: '',
            },
            tableHeaderApi: {
                type: Object,
                default: null,
            },
            /**
             * 自定义配置拉取函数
             */
            fetchFunc: {
                type: Function,
                default: null,
            },
            /**
             * 自定义配置重置函数
             */
            resetFunc: {
                type: Function,
                default: null,
            },
            /**
             * 自定义配置保存函数
             */
            saveFunc: {
                type: Function,
                default: null,
            },
            finishFunc: {
                type: Function,
                default: null,
            },
            closeFunc: {
                type: Function,
                default: null,
            },
        },
        data() {
            return {
                loading: false,

                tableList: [],

                selectedFixedColumnList: [],
                selectedCommonColumnList: [],

                fixedGroupKey: 'selected',
                activeKey: null,
                deleteIconVisible: false,

                postData: [],
            };
        },
        computed: {
            visible: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                    if (!val) {
                        this.closeFunc && this.closeFunc();
                        this.destroyElement();
                    }
                },
            },
            dialogTitle() {
                return `自定义表头 - ${this.titleName}视图`;
            },
            draggable() {
                return this.mode !== 'fixed';
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.onClose && this.onClose();
                    this.destroyElement();
                }
            },
        },
        async created() {
            await this.fetchHeaderList();
        },
        methods: {
            async fetchHeaderList(isDefault = 0) {
                let tableList = [];
                try {
                    this.loading = true;
                    if (this.fetchFunc) {
                        tableList = await this.fetchFunc();
                    } else {
                        tableList = await this.tableHeaderApi.getEmployeeTableHeaders(this.tableKey, isDefault, this.regionId || null);
                    }
                } catch (error) {
                    this.$Toast({
                        type: 'error',
                        message: error,
                    });
                } finally {
                    this.transFormatList(tableList);
                    this.loading = false;
                }
            },
            transFormatList(list) {
                const valueProps = {
                    key: 'key',
                    label: 'label',
                    children: 'children',
                    childrenProps: {
                        key: 'key',
                        label: 'label',
                        parentKey: 'parentKey',
                        isHidden: 'isHidden',
                        isFixed: 'isFixed',
                        position: 'position',
                        description: 'description',
                    },
                };
                Object.assign(valueProps, this.valueProps);
                this.tableList = list.map((item) => {
                    const itemInfo = {
                        ...item,
                        key: item[valueProps.key],
                        label: item[valueProps.label],
                        children: item[valueProps.children],
                    };
                    if (itemInfo.children && itemInfo.children.length) {
                        itemInfo.children = itemInfo.children.map((child) => {
                            return {
                                ...child,
                                key: child[valueProps.childrenProps.key],
                                label: child[valueProps.childrenProps.label],
                                parentKey: child[valueProps.childrenProps.parentKey],
                                isHidden: child[valueProps.childrenProps.isHidden],
                                isFixed: child[valueProps.childrenProps.isFixed],
                                position: child[valueProps.childrenProps.position],
                                description: child[valueProps.childrenProps.description],
                            };
                        });
                    }
                    return itemInfo;
                });
                this.tableList = this.initTableFields(this.tableList);
                this.initHeader();
            },
            initHeader() {
                const list = [];
                this.tableList.forEach((group) => {
                    group.children.forEach((item) => {
                        item.isSelected = !item.enableEmployeeModify ? true : !item.isHidden;
                        item.disabled = !item.enableEmployeeModify;
                        item.isFixed = this.showFixed ? item.isFixed : 0;
                        if (item.isSelected) {
                            list.push(item);
                        }
                    });
                });
                const sortList = list.sort((a, b) => a?.position - b?.position);
                this.selectedFixedColumnList = sortList.filter((item) => item.isFixed);
                this.selectedCommonColumnList = sortList.filter((item) => !item.isFixed);
            },
            handleSelectedChange(selected, item) {
                if (selected) {
                    this.handleSelectedFields(item);
                } else {
                    // 取消选中 | 在其他列 | 固定列
                    this.handleUnselectedFields(item);
                }
                this.tableList.forEach((group) => {
                    group.children.forEach((one) => {
                        if (one.label === item.label) one.isSelected = selected;
                    });

                    if (group.minSelect) {
                        const isSelectArr = group.children.filter((it) => it.isSelected);
                        group.children.forEach((one) => {
                            const disabled = one.isSelected && isSelectArr.length === group.minSelect;
                            one.disabled = disabled;
                            one.enableEmployeeModify = !disabled;
                        });
                    }
                });
            },
            handleSelectedFields(item) {
                const hasExistSelectedItem = this.checkIsDuplicate(this.selectedCommonColumnList, item);
                const hasExistFixedSelectedItem = this.checkIsDuplicate(this.selectedFixedColumnList, item);

                if (this.showFixed) {
                    if (!hasExistSelectedItem && !hasExistFixedSelectedItem) {
                        if (item.isFixed) {
                            const arr = this.selectedFixedColumnList.map((it) => it.position).sort((a, b) => a - b);
                            const insertIndex = this.insertNumber(arr, item.position);
                            this.selectedFixedColumnList.splice(insertIndex, 0, item);
                        } else {
                            const arr = this.selectedCommonColumnList.map((it) => it.position).sort((a, b) => a - b);
                            const insertIndex = this.insertNumber(arr, item.position);
                            this.selectedCommonColumnList.splice(insertIndex, 0, item);
                        }
                    }
                } else if (!hasExistSelectedItem) {
                    const arr = this.selectedCommonColumnList.map((it) => it.position).sort((a, b) => a - b);
                    const insertIndex = this.insertNumber(arr, item.position);
                    this.selectedCommonColumnList.splice(insertIndex, 0, item);
                }
            },
            handleUnselectedFields(item) {
                const {
                    isFixed, key,
                } = item;
                if (isFixed) {
                    this.handleDeleteSelectedField(this.selectedFixedColumnList, key);
                } else {
                    this.handleDeleteSelectedField(this.selectedCommonColumnList, key);
                }
            },
            handleDeleteSelectedField(list, key) {
                const deleteIndex = list.findIndex((it) => it.key === key);
                deleteIndex !== -1 && list.splice(deleteIndex, 1);

                this.tableList.forEach((group) => {
                    group.children && group.children.forEach((it) => {
                        if (it.key === key) {
                            it.isSelected = false;
                            it.isFixed = this.showFixed && !this.draggable ? it.isFixed : 0;
                        }
                    });
                });
            },
            checkIsDuplicate(list = [], item = {}) {
                const { key } = item;
                return list?.find((it) => it.key === key);
            },
            insertNumber(arr, num) {
                let left = 0;
                let right = arr.length - 1;
                while (left <= right) {
                    const mid = Math.floor((left + right) / 2);
                    if (arr[mid] === num) {
                        return mid;
                    } if (arr[mid] < num) {
                        left = mid + 1;
                    } else {
                        right = mid - 1;
                    }
                }
                return left;
            },
            handleDraggleField(val, isFixed = false) {
                const draggleFieldKey = val?.item._underlying_vm_?.key;
                const fieldItem = {
                    draggleFieldKey, isFixed,
                };
                if (isFixed) {
                    if (this.selectedFixedColumnList.length > this.maxFixedCount) {
                        this.$Toast({
                            message: '冻结数量不能超过6个',
                            type: 'error',
                        });
                        const index = this.selectedFixedColumnList.findIndex((item) => item.key === draggleFieldKey);
                        this.selectedCommonColumnList.push({
                            ...this.selectedFixedColumnList[index],
                        });
                        index !== -1 && this.selectedFixedColumnList.splice(index, 1);
                        return false;
                    }
                    this.selectedFixedColumnList = this.draggleFieldFixedHandler(this.selectedFixedColumnList, fieldItem);
                } else {
                    this.selectedCommonColumnList = this.draggleFieldFixedHandler(this.selectedCommonColumnList, fieldItem);
                }
            },
            draggleFieldFixedHandler(list = [], fieldItem) {
                const {
                    key, fieldIsFixed,
                } = fieldItem;
                return list.map((item) => {
                    if (item.key === key) {
                        item.isFixed = fieldIsFixed;
                    }
                    return item;
                });
            },
            handleMouseEnter(item) {
                this.activeKey = item.key;
                this.deleteIconVisible = true;
            },
            handleMouseLeave() {
                this.deleteIconVisible = false;
                this.activeKey = null;
            },
            handleDeleteField(item, isFixed = false) {
                if (isFixed) {
                    this.handleDeleteSelectedField(this.selectedFixedColumnList, item.key);
                } else {
                    this.handleDeleteSelectedField(this.selectedCommonColumnList, item.key);
                }
            },
            async handleReset() {
                try {
                    this.loading = true;
                    let data = null;
                    if (this.resetFunc) {
                        data = await this.resetFunc();
                        this.transFormatList(data);
                    } else {
                        data = await this.fetchHeaderList(1);
                    }
                } catch (error) {
                    this.$Toast({
                        type: 'error',
                        message: error,
                    });
                } finally {
                    this.loading = false;
                }
            },
            async handleConfirm() {
                const selectedFixedColumnList = this.selectedFixedColumnList.map((item) => ({
                    ...item,
                    isFixed: 1,
                    isHidden: 0,
                }));
                const selectedCommonColumnList = this.selectedCommonColumnList.map((item) => ({
                    ...item,
                    isFixed: 0,
                    isHidden: 0,
                }));
                const selectedList = selectedFixedColumnList.concat(selectedCommonColumnList);
                this.postData = [
                    ...selectedList,
                ];
                this.tableList.forEach((group) => {
                    group.children.forEach((item) => {
                        if (!item.isSelected) {
                            item.isHidden = 1;
                            item.isFixed = 0;
                            this.postData.push(item);
                        }
                    });
                });
                if (this.draggable) {
                    this.postData.forEach((item, index) => {
                        item.position = index + 1;
                    });
                }
                if (this.saveFunc) {
                    this.saveFunc(this.postData);
                } else {
                    const tableHeaderEmployeeItems = this.postData.map((item) => ({
                        key: item.key,
                        position: item.position,
                        isFixed: item.isFixed,
                        isHidden: item.isHidden,
                    }));
                    await this.tableHeaderApi.updateEmployeeTableHeaders({
                        tableHeaderEmployeeItems,
                        tableKey: this.tableKey,
                    });
                }
                this.finishFunc && this.finishFunc();
                this.visible = false;
            },
            destroyElement() {
                this.$destroy();
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            initTableFields(fields = []) {
                const newFields = fields;
                const initTableFieldsFn = this.initTableFields;
                for (let i = 0; i < newFields.length; i++) {
                    const item = newFields[i];
                    item.children = this.getChildren(item, initTableFieldsFn);
                }
                return newFields;
            },
            getChildren(item, initTableFieldsFn) {
                let { children } = item;
                if (children && children.length) {
                    const firstChild = children[0];
                    if (firstChild && firstChild.enableIndependentModifyColumnChildren && firstChild.columnChildren && firstChild.columnChildren.length) {
                        children = initTableFieldsFn(firstChild.columnChildren);
                        for (let j = 0; j < children.length; j++) {
                            const cItem = children[j];
                            if (!cItem.enableIndependentModifyColumnChildren) {
                                continue;
                            }
                            const list = [];
                            list.push(...cItem.columnChildren);
                            children = initTableFieldsFn(list);
                            break;
                        }
                    } else {
                        children = initTableFieldsFn(children);
                    }
                }
                return children;
            },
        },
    };
</script>

<style lang="scss">
@import "src/styles/theme.scss";
@import 'src/styles/abc-common.scss';

.custom-header-wrapper {
    .custom-header-wrapper__content {
        height: 100%;

        .custom-header-wrapper__left-content {
            flex: 1;
            padding: 14px 32px 24px;
            overflow-y: auto;

            @include scrollBar;

            .stat__custom-header-group-title {
                padding-left: 8px;
                line-height: 22px;
            }

            .stat__custom-header-group {
                padding: 16px 8px;
                margin-top: 8px;
                border: 1px solid var(--abc-color-P6);
                border-radius: var(--abc-border-radius-small);
            }
        }

        .custom-header-wrapper__right-content {
            width: 200px;
            overflow-y: auto;
            border-left: 1px solid var(--abc-color-P6);

            @include scrollBar;

            .custom-header-wrapper__right-content-label {
                padding: 0 12px;
                line-height: 40px;
                border-bottom: 1px solid var(--abc-color-P8);
            }

            .custom-header-wrapper__right-content-list {
                .stat__custom-header-selected-item {
                    padding: 0 12px;
                    line-height: 32px;

                    &:hover {
                        cursor: pointer;
                        background-color: var(--abc-color-cp-grey4);
                    }

                    &:active {
                        background-color: var(--abc-color-cp-grey4);
                        border: none;
                    }

                    &:last-child {
                        border-bottom: 1px solid var(--abc-color-P8);
                    }

                    &-drag {
                        &:hover {
                            cursor: grab;
                        }

                        &:active {
                            cursor: grabbing;
                        }
                    }

                    &-disable {
                        &:hover {
                            cursor: not-allowed;
                        }

                        &:active {
                            cursor: not-allowed;
                        }
                    }
                }
            }
        }

        .ellipsis {
            @include ellipsis;
        }
    }
}
</style>
