<template>
    <!--内容区域-->
    <abc-container
        class="upgrade-registration-container"
        :has-left-container="hasLeftContainer"
        :left-container-min-width="320"
        :left-container-max-width="320"
        :is-support-center-scroll="false"
        left-container-width-fixed
    >
        <abc-container-left v-show="hasLeftContainer" class="left-wrapper">
            <div v-if="disableQueryView" class="filter-cover"></div>
            <abc-flex justify="space-between" align="center" class="tab-wrapper-box">
                <abc-tabs-v2
                    v-model="businessType"
                    :option="tabOptions"
                    class="tab-wrapper"
                    size="huge"
                    :border="false"
                    data-cy="business-type-tabs-v2"
                    :disable-indicator="tabOptions.length === 1"
                    @change="handleTabChanged"
                ></abc-tabs-v2>
                <abc-flex align="center">
                    <div
                        :class="{
                            'tab-wrapper-box-picker': true,
                            'margin-right12': !fullScreenMode && isBoardViewMode
                        }"
                    >
                        <abc-icon
                            icon="Arrow_Left"
                            class="Arrow_hover"
                            :class="['prev-icon', canPrev ? '' : 'is-disabled']"
                            size="18"
                            style="cursor: pointer;"
                            @click.native="handlePrevMonthClick"
                        >
                        </abc-icon>
                        <abc-dropdown
                            ref="yearDropdown"
                            class="year-dropdown"
                            custom-class="date-navigator-dropdown-wrapper date-navigator-dropdown-wrapper-opacity-1"
                            style="width: auto; height: 26px; margin-left: 4px; line-height: 26px; text-align: right;"
                            @content-click="setScrollTop('yearDropdown')"
                            @change="changeYearHandle"
                        >
                            <div slot="reference" class="year-reference">
                                <span class="reference-text">{{ currentYear }}-</span>
                            </div>
                            <abc-dropdown-item
                                v-for="item in YEAR_LIST"
                                :key="item.value"
                                style="min-width: 60px;"
                                :label="item.label"
                                :value="item.value"
                                :selected="`${currentYear } ` + `年`"
                            ></abc-dropdown-item>
                        </abc-dropdown>
                        <abc-dropdown
                            ref="monthDropdown"
                            class="month-dropdown"
                            custom-class="date-navigator-dropdown-wrapper date-navigator-dropdown-wrapper-opacity-1"
                            style="width: 16px; height: 26px; margin-right: 4px; line-height: 26px; text-align: left;"
                            @content-click="setScrollTop('monthDropdown')"
                            @change="changeMonthHandle"
                        >
                            <div slot="reference" class="month-reference">
                                <span class="reference-text">
                                    {{ 10 > Number(currentMonth) ? `0${currentMonth}` : currentMonth }}
                                </span>
                            </div>
                            <abc-dropdown-item
                                v-for="item in MONTH_LIST"
                                :key="item.value"
                                style="min-width: 60px;"
                                :label="item.label"
                                :value="item.value"
                                :selected="`${currentMonth }月`"
                            ></abc-dropdown-item>
                        </abc-dropdown>
                        <abc-icon
                            icon="Arrow_Rgiht"
                            class="Arrow_hover"
                            :class="['next-icon', canNext ? '' : 'is-disabled']"
                            size="18"
                            style="cursor: pointer;"
                            @click.native="handleNextMonthClick"
                        ></abc-icon>
                    </div>

                    <abc-button
                        v-if="!fullScreenMode && isBoardViewMode"
                        style="margin-right: 12px;"
                        variant="ghost"
                        icon="sidebar1"
                        @click="toggleFullScreen"
                    ></abc-button>
                </abc-flex>
            </abc-flex>

            <div class="registration-quick-list-wrapper">
                <div class="quick-search-wrapper">
                    <div class="picker_body_auto">
                        <date-list
                            ref="dataList"
                            :value="cursorDate"
                            :date="date"
                            :show-weeks="true"
                            @input="handleDateInput"
                        >
                        </date-list>
                    </div>
                </div>
                <ul class="quick-search-content">
                    <li
                        v-for="(item, key) in quickListModel.list"
                        :key="key"
                        :class="{
                            'quick-list-item': true,
                            'is-selected': item.doctor.id === selectedDoctor.doctor.id,
                        }"
                        @click="handleClickSelectPatient(item)"
                    >
                        <div class="item-left">
                            <abc-flex align="center" gap="small" class="employee-name ellipsis">
                                {{ item.doctor.name }}
                                <abc-tag-v2
                                    v-if="isOutpatient && item?.status === REGISTRATION_DOCTOR_STATUS.CONTAINS_STOP_DIAGNOSE"
                                    shape="square"
                                    theme="warning"
                                    variant="dark"
                                    size="tiny"
                                >
                                    停诊
                                </abc-tag-v2>
                            </abc-flex>
                            <!--门诊 start-->
                            <div v-if="isOutpatient" class="status-list">
                                <template v-if="dateFilterStr === 'today'">
                                    <abc-text
                                        class="status-item"
                                        size="mini"
                                        theme="gray"
                                        style="width: 44px !important;"
                                    >
                                        共 <span>{{ item.notRefundedCount }}</span>
                                    </abc-text>
                                    <abc-text
                                        v-if="needSignIn"
                                        class="status-item"
                                        size="mini"
                                        theme="gray"
                                    >
                                        待签 <span>{{ item.signInCount }}</span>
                                    </abc-text>
                                    <abc-text class="status-item" size="mini" theme="gray">
                                        待诊 <span>{{ item.treatCount }}</span>
                                    </abc-text>
                                </template>
                                <template v-else>
                                    <abc-text class="status-item" size="mini" theme="gray">
                                        已约
                                        <span>{{ item.totalCount - item.refundedCount }}</span>
                                    </abc-text>
                                    <!-- 全部医生屏蔽余号 -->
                                    <abc-text
                                        v-if="!isPastDate"
                                        class="status-item"
                                        size="mini"
                                        theme="gray"
                                    >
                                        <template v-if="isFixOrderMode">
                                            余号
                                            <span>{{ item.restCount }}</span>
                                        </template>
                                    </abc-text>
                                    <abc-text
                                        v-else
                                        class="status-item"
                                        size="mini"
                                        theme="gray"
                                    >
                                        <template v-if="quickListModel.needSignIn">
                                            已签
                                            <span>{{ item.signedCount }}</span>
                                        </template>
                                        <template v-else>
                                            已诊
                                            <span>{{ item.treatedCount }}</span>
                                        </template>
                                    </abc-text>
                                </template>
                            </div>
                            <!--门诊 end-->
                            <!--理疗 start-->
                            <div v-else class="status-list">
                                <abc-text class="status-item" size="mini" theme="gray">
                                    待签
                                    <span>{{ item.signInCount }}</span>
                                </abc-text>
                                <!-- 全部医生屏蔽余号 -->
                                <abc-text class="status-item" size="mini" theme="gray">
                                    已签 <span>{{ item.signedCount }}</span>
                                </abc-text>
                            </div>
                            <!--理疗 end-->
                        </div>
                        <div class="item-right">
                            <template v-if="item.status && item.status !== REGISTRATION_DOCTOR_STATUS.CONTAINS_STOP_DIAGNOSE">
                                <span v-if="item.status === REGISTRATION_DOCTOR_STATUS.NOT_DOCTOR" class="removed">
                                    {{ isTherapy ? '非理疗师' : '非医生' }}
                                </span>
                                <span v-if="item.status === REGISTRATION_DOCTOR_STATUS.REMOVED_FROM_CLINIC" class="removed">已移出</span>
                                <span v-if="item.status === 4" class="removed">已停用</span>
                            </template>
                            <abc-check-access v-else-if="isCanSeeModifyRegistrationInfo && !isPastDate">
                                <abc-button
                                    :variant="item.doctor.id ? 'outline' : 'fill'"
                                    :theme="item.doctor.id ? 'default' : 'success'"
                                    size="small"
                                    class="registration-button"
                                    @click.stop="(e) => openAppointmentCardDialog({
                                        doctorId: item.doctor.id, doctorName: item.doctor.name
                                    }, null, e, 'registration-quick')"
                                >
                                    {{ dateFilterStr === 'today' && isOutpatient ? '挂号' : '预约' }}
                                </abc-button>
                            </abc-check-access>
                        </div>
                    </li>
                </ul>
                <div class="quick-footer-wrapper">
                    <abc-tabs-v2
                        v-model="quickFooterTabValue"
                        :option="quickFooterTabOptions"
                        size="middle"
                        :disable-indicator="true"
                        :border-style="{
                            borderBottomColor: 'var(--abc-color-P8)'
                        }"
                        class="quick-footer-tab-wrapper"
                    ></abc-tabs-v2>
                    <smart-tools
                        :type="SmartToolsType.REGISTRATION"
                        :doctor-id="``"
                        :business-type="businessType"
                        :visible-call-control="visibleCallControl"
                    >
                        <div
                            v-if="isShowScanRegisterDetail"
                            v-abc-click-outside="closeScanRegisterDetails"
                            class="scan-register-info"
                        >
                            <abc-popover
                                v-model="isShowScanRegisterDetailFlag"
                                width="416px"
                                placement="right"
                                trigger="manual"
                                popper-class="scan-register-epidemiological-pop-over"
                                theme="white"
                            >
                                <div
                                    v-if="scanQrCodePatientList.length"
                                    class="scan-register-epidemiological-list"
                                    @click.prevent.stop="isShowScanRegisterDetailFlag = true"
                                >
                                    <div class="scan-register-epidemiological-list-time">
                                        {{ todayStr }}
                                    </div>
                                    <div class="scan-register-epidemiological-list-detail">
                                        <scan-register-detail-info
                                            v-for="(item,index) in scanQrCodePatientList"
                                            :key="index"
                                            :detail-info="item"
                                            :is-outpatient="isOutpatient"
                                            @openRegisterDialog="openRegisterDialog"
                                        ></scan-register-detail-info>
                                    </div>
                                </div>
                                <div v-else class="scan-register-epidemiological-list-no-info">
                                    <div class="no-info-box">
                                        <div>暂无登记患者</div>
                                        <div>请前往 [管理] → [自助登记] 下载登记二维码</div>
                                    </div>
                                </div>
                                <div
                                    slot="reference"
                                    class="scan-register-epidemiological entry-item entry-item-no-border"
                                    @click.prevent.stop="openScanRegisterDetailFlag"
                                >
                                    <div class="scan-register-epidemiological-box">
                                        <abc-icon icon="s-order-color" :size="20"></abc-icon>
                                        <div class="scan-register-epidemiological-text">
                                            已登记
                                        </div>
                                    </div>
                                    <div class="scan-register-epidemiological-box">
                                        <div v-if="scanQrCodePatientTotal" class="scan-personal">
                                            已登记{{ scanQrCodePatientTotal }}人
                                        </div>
                                        <abc-icon
                                            icon="s-b-right-line-medium"
                                            size="16"
                                            color="var(--abc-color-T3)"
                                            style="cursor: pointer;"
                                        ></abc-icon>
                                    </div>
                                </div>
                            </abc-popover>
                        </div>
                    </smart-tools>
                </div>
            </div>
        </abc-container-left>

        <abc-container-center :class="['content-wrapper', { 'board-view-content-wrapper': isBoardViewMode }]">
            <abc-container-center-top-head class="content-header-wrapper">
                <abc-space>
                    <abc-button
                        v-if="fullScreenMode && isBoardViewMode"
                        variant="ghost"
                        icon="sidebar1"
                        @click="toggleFullScreen"
                    ></abc-button>

                    <abc-button
                        variant="ghost"
                        theme="default"
                        style="min-width: 54px; padding: 0 12px;"
                        @click="setTabToday"
                    >
                        今天
                    </abc-button>

                    <div v-if="isBoardViewMode" class="tab-board-view-mode" @click="handleClickBoardView">
                        <abc-tabs-v2
                            v-model="boardViewMode"
                            :option="boardViewModeOptions"
                            size="middle"
                            type="outline"
                            :item-min-width="54"
                        >
                        </abc-tabs-v2>
                    </div>
                </abc-space>

                <date-time-selector
                    v-if="isBoardViewMode"
                    ref="dateTimeSelector"
                    v-model="timeRange"
                    :mode="isBoardWeekViewMode ? DATETIME_SELECTOR_MODE.RANGE : DATETIME_SELECTOR_MODE.DATE_TIME"
                    @change="handleChangeTimeRange"
                ></date-time-selector>

                <date-time-selector
                    v-else
                    ref="dateTimeSelector"
                    v-model="listModeDate"
                    :mode="DATETIME_SELECTOR_MODE.DATE_TIME"
                ></date-time-selector>

                <div style="flex: 1;"></div>

                <abc-space>
                    <div class="board-view-search">
                        <abc-search
                            v-if="isListViewMode && !viewDistributeConfig.Registration.isRegistrationListPerformance"
                            v-model.trim="params.keyword"
                            type="text"
                            :width="searchInputWidth"
                            :placeholder="searchInputPlaceholder"
                            @focus="handleQLSearchFocus"
                            @blur="handleQLSearchBlur"
                            @clear="clearInput"
                            @search="inputHandler"
                        >
                        </abc-search>

                        <registration-list-search
                            v-else
                            :width="listSearchWidth"
                            :is-board-view-mode="isBoardViewMode"
                            :business-type="businessType"
                            :is-can-modify-registration-info="isCanSeeModifyRegistrationInfo"
                            @autocomplete-item-button-click="handleBoardListAutocompleteItemButtonClick"
                            @patient-id-change="handlePatientIdChange"
                        ></registration-list-search>
                    </div>

                    <abc-tabs-v2
                        v-model="viewMode"
                        :option="viewModeOptions"
                        size="middle"
                        type="outline"
                    >
                    </abc-tabs-v2>
                    <template v-if="isShowSettingButton">
                        <abc-dropdown
                            style="width: 32px;"
                            :min-width="130"
                            :max-width="150"
                            placement="bottom-end"
                        >
                            <abc-button
                                slot="reference"
                                icon="s-b-settings-line"
                                variant="ghost"
                                :width="32"
                            ></abc-button>
                            <div v-if="isListViewMode">
                                <abc-dropdown-item style="padding: 0;">
                                    <div
                                        style="display: flex; align-items: center; justify-content: space-between; padding: 6px 12px;"
                                        @click="handleOpenCustomHeaderDialog"
                                    >
                                        <abc-text theme="black">
                                            设置展示字段
                                        </abc-text>
                                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                                    </div>
                                </abc-dropdown-item>
                            </div>
                            <div v-if="showOpenAppointmentAudioSetting">
                                <abc-dropdown-item style="padding: 0;">
                                    <div
                                        style="display: flex; align-items: center; justify-content: space-between; padding: 6px 12px;"
                                        @click="openAppointmentAudioSetting"
                                    >
                                        <abc-text theme="black">
                                            设置预约提醒
                                        </abc-text>
                                        <abc-icon icon="Arrow_Rgiht"></abc-icon>
                                    </div>
                                </abc-dropdown-item>
                            </div>
                        </abc-dropdown>
                    </template>
                </abc-space>
            </abc-container-center-top-head>

            <abc-layout v-if="isListViewMode" preset="page-table" class="registration-table-body-wrapper">
                <abc-layout-header class="table-handle-bar">
                    <div v-if="disableQueryView" class="filter-cover"></div>

                    <abc-flex justify="space-between" align="center" style="width: 100%;">
                        <abc-space>
                            <abc-tabs-v2
                                v-model="params.displayStatus"
                                :option="_statusFilter"
                                size="middle"
                                type="outline"
                                :item-min-width="80"
                                @change="filterDisplayStatus"
                            ></abc-tabs-v2>

                            <!--科室筛选-->
                            <abc-select
                                v-if="hasDepartments && isOutpatient"
                                v-model="params.departmentId"
                                :width="120"
                                placeholder="全部科室"
                                @change="changeDepartment"
                            >
                                <abc-option :value="''" label="全部科室"></abc-option>
                                <abc-option
                                    v-for="d in listFilterDepartments"
                                    :key="d.departmentId"
                                    :label="d.departmentName || '其他'"
                                    :value="d.departmentId"
                                >
                                    <abc-flex justify="space-between" style="width: 100%;">
                                        <span class="ellipsis">
                                            {{ d.departmentName }}
                                        </span>
                                        <abc-tag-v2
                                            v-if="d.departmentStatus === RELATIVE_STATUS.DEACTIVE"
                                            size="small"
                                            variant="outline"
                                            theme="danger"
                                            :min-width="44"
                                        >
                                            停用
                                        </abc-tag-v2>
                                    </abc-flex>
                                </abc-option>
                            </abc-select>
                            <abc-flex
                                v-if="isOutpatient && showStopDiagnoseButton"
                                style="
                                    gap: 22px;
                                    padding: 0 12px;
                                    background-color: var(--abc-color-Y4);
                                    border: 1px solid var(--abc-color-Y5);
                                    border-radius: var(--abc-border-radius-small);
"
                            >
                                <abc-tooltip
                                    max-width="262px"
                                    placement="bottom"
                                >
                                    <abc-flex
                                        style="height: 32px;"
                                        justify="space-between"
                                        align="center"
                                    >
                                        <div style="width: 152px; overflow: hidden; color: #ff9933; text-overflow: ellipsis; white-space: nowrap;">
                                            <abc-icon icon="n-volume-line" size="16"></abc-icon>
                                            <span>停诊：{{ shiftTimes }}</span>
                                        </div>
                                    </abc-flex>
                                    <template slot="content">
                                        <div>{{ currentDoctorName }}医生停诊时段：</div>
                                        <div v-for="item in clinicList" :key="item.name">
                                            <span v-if="item.name">{{ item.name }}:</span>
                                            {{ item.shiftTime }}
                                        </div>
                                    </template>
                                </abc-tooltip>
                                <abc-tooltip
                                    placement="bottom"
                                    :disabled="isDisabledSend"
                                    content="当前无待退号患者，无需发送停诊通知"
                                >
                                    <abc-flex
                                        style="height: 32px;"
                                        justify="space-between"
                                        align="center"
                                    >
                                        <abc-link
                                            :disabled="!isDisabledSend"
                                            @click="sendMessageHandler"
                                        >
                                            发送停诊通知
                                        </abc-link>
                                    </abc-flex>
                                </abc-tooltip>
                            </abc-flex>
                        </abc-space>

                        <abc-button
                            v-if="isListViewMode"
                            icon="n-upload-line"
                            :loading="exportLoading"
                            variant="ghost"
                            :disabled="loading"
                            @click="handleExportList"
                        >
                            导出
                        </abc-button>
                    </abc-flex>
                </abc-layout-header>

                <abc-layout-content>
                    <abc-table
                        v-if="isOutpatient"
                        ref="table"
                        class="table"
                        :loading="loading"
                        :render-config="outPatientRenderConfig"
                        :data-list="tableData"
                        tr-clickable
                        @handleClickTr="openDialog"
                        @sortChange="handleChangeSort"
                    >
                        <template #reserveRender="{ trData: item }">
                            <abc-table-cell class="reserve-table-cell">
                                <abc-flex>
                                    <abc-icon v-if="item.registrationFormItem?.isReserved" icon="s-reserve-tag-color" :size="14"></abc-icon>
                                    <abc-icon
                                        v-if="item.registrationFormItem?.referralFlag && item.registrationFormItem?.referralFlag !== ReferralFlagEnum.REVISIT_IN_THREE_DAYS"
                                        icon="s-change-tag-color"
                                        :size="14"
                                    ></abc-icon>
                                    <abc-tooltip
                                        v-if="item.registrationFormItem?.referralFlag === ReferralFlagEnum.REVISIT_IN_THREE_DAYS"
                                        placement="top"
                                        content="3日内复诊"
                                    >
                                        <abc-icon icon="s-followup-tag-color" :size="14"></abc-icon>
                                    </abc-tooltip>
                                </abc-flex>
                            </abc-table-cell>
                        </template>
                        <template #patientName="{ trData: item }">
                            <abc-table-cell>
                                <abc-flex class="ellipsis" align="center">
                                    <abc-text :title="item.patient.name || ''" theme="primary-light">
                                        {{ item.patient.name || '' }}
                                    </abc-text>
                                    <img
                                        v-if="item.patient.isMember"
                                        style="width: 16px; height: 16px; margin-left: 4px;"
                                        src="~assets/images/vip.png"
                                        :title="item.patient?.memberTypeName || ''"
                                        alt=""
                                    />
                                </abc-flex>
                            </abc-table-cell>
                        </template>
                        <template #patientSex="{ trData: item }">
                            <abc-table-cell>{{ item.patient.sex || '' }}</abc-table-cell>
                        </template>
                        <template #patientMobile="{ trData: item }">
                            <abc-table-cell>
                                <template v-if="!isCanSeePatientMobileInRegistration">
                                    {{ encryptMobile(item.patient.mobile) }}
                                </template>
                                <template v-else>
                                    {{ item.patient.mobile }}
                                </template>
                            </abc-table-cell>
                        </template>
                        <template #patientAge="{ trData: item }">
                            <abc-table-cell>
                                {{ formatAge(item.patient.age, {
                                    monthYear: 12, dayYear: 1,
                                }) }}
                            </abc-table-cell>
                        </template>
                        <template #patientSn="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.patient.sn || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientIdCard="{ trData: item }">
                            <abc-table-cell v-if="item.patient.idCard">
                                <span v-abc-title.ellipsis="`[${item.patient.idCardType || '身份证'}]${item.patient.idCard || ''}`"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientSourceFromName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.patient.sourceFromName || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientTags="{ trData: item }">
                            <abc-table-cell>
                                <div v-if="item.patient.tags?.length" style="width: 100%;" class="ellipsis">
                                    <overflow-flex-tags-wrapper :tags="item.patient.tags"></overflow-flex-tags-wrapper>
                                </div>
                            </abc-table-cell>
                        </template>
                        <template #patientMemberTypeName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.patient.memberTypeName || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientRemark="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.patient.remark || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientPrimaryTherapistName="{ trData: item }">
                            <abc-table-cell>{{ item.patient.primaryTherapistName || '' }}</abc-table-cell>
                        </template>
                        <template #patientDutyTherapistName="{ trData: item }">
                            <abc-table-cell>{{ item.patient.dutyTherapistName || '' }}</abc-table-cell>
                        </template>
                        <template #registrationFormItemType="{ trData: item }">
                            <abc-table-cell>
                                {{ formatTypeName(item.registrationFormItem.type) }}
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemStatus="{ trData: item }">
                            <abc-table-cell v-if="viewDistributeConfig.Registration.isRegistrationListPerformance">
                                <abc-button
                                    v-if="!getStatusInfo(item)?.dropdownItems?.length"
                                    :variant="getStatusInfo(item).variant"
                                    :theme="getStatusInfo(item).theme"
                                    class="patient-status-wrapper"
                                    :width="84"
                                >
                                    {{ getStatusInfo(item).displayValue }}
                                </abc-button>
                                <div v-else class="patient-status-wrapper dentistry-patient-status-wrapper">
                                    <abc-dropdown
                                        :margin-top="4"
                                        size="small"
                                        :min-width="84"
                                        :max-width="84"
                                        @change="(value) => onDropdownChange(value, item)"
                                        @content-click="handleContentClick"
                                    >
                                        <abc-tooltip
                                            slot="reference"
                                            placement="top"
                                            :open-delay="500"
                                            :content="getStatusInfo(item).displayValueDesc"
                                            :disabled="!getStatusInfo(item).displayValueDesc"
                                        >
                                            <abc-button
                                                :variant="getStatusInfo(item).variant"
                                                :theme="getStatusInfo(item).theme"
                                                :icon="getStatusInfo(item).waitingSignInConfirm ? 'check' : ''"
                                                :style="getStatusInfo(item).style"
                                                icon-position="right"
                                                icon-color="var(--abc-color-S2)"
                                                :width="84"
                                            >
                                                {{ getStatusInfo(item).displayValue }}
                                            </abc-button>
                                        </abc-tooltip>
                                        <abc-dropdown-item
                                            v-for="dropdownItem in getStatusInfo(item).dropdownItems"
                                            :key="dropdownItem.value"
                                            :label="dropdownItem.label"
                                            :value="dropdownItem.value"
                                        ></abc-dropdown-item>
                                    </abc-dropdown>
                                </div>
                            </abc-table-cell>
                            <abc-table-cell v-else>
                                <abc-tag-v2
                                    size="medium"
                                    :theme="getStatusTagV2Style(item).theme"
                                    :variant="getStatusTagV2Style(item).variant"
                                >
                                    {{ item.registrationFormItem.statusName }}
                                </abc-tag-v2>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemFee="{ trData: item }">
                            <abc-table-cell>
                                <abc-money :value="item.registrationFormItem.fee || 0" is-show-space></abc-money>
                                <abc-text
                                    v-if="item.registrationFormItem.payStatusV2 < PayStatusV2.PAID && item.registrationFormItem.statusV2 < StatusV2.REFUNED"
                                    size="mini"
                                    theme="gray"
                                    style="margin-left: 4px;"
                                >
                                    待收费
                                </abc-text>
                                <abc-text
                                    v-else-if="item.registrationFormItem.payStatusV2 === PayStatusV2.REFUNED"
                                    size="mini"
                                    theme="danger-light"
                                    style="margin-left: 4px;"
                                >
                                    已退费
                                </abc-text>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemReceivedFee="{ trData: item }">
                            <abc-table-cell>
                                <abc-money :value="item.registrationFormItem.receivedFee || 0" is-show-space></abc-money>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemInvoiceStatusFlag="{ trData: item }">
                            <abc-table-cell>
                                {{ getInvoiceStatusDesc(item.registrationFormItem.invoiceStatusFlag) }}
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemDepartmentName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.departmentName || '其他'" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemDoctorName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.doctorName || '--'" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemOrderNoStr="{ trData: item }">
                            <abc-table-cell>
                                {{ item.registrationFormItem.orderNoStr || '' }}
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemRevisitStatus="{ trData: item }">
                            <abc-table-cell>
                                {{ item.registrationFormItem.revisitStatus === RevisitStatus.FIRST ? '初诊' : '复诊' }}
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemRegistrationProductNames="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.registrationProductNames || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemReserveDateTime="{ trData: item }">
                            <abc-table-cell>
                                <template v-if="item.registrationFormItem?.oldReserveInfo">
                                    <abc-popover
                                        popper-class="registration-info-popper"
                                        placement="bottom-end"
                                        trigger="hover"
                                        :width="240"
                                        :close-delay="0"
                                        theme="yellow"
                                        :offset="10"
                                        :arrow-offset="120"
                                    >
                                        <span slot="reference">
                                            {{ formatReserveDate(item.registrationFormItem) }}
                                            <abc-icon
                                                icon="history"
                                                color="#7a8794"
                                                size="14"
                                                style="margin-left: 4px;"
                                            ></abc-icon>
                                        </span>
                                        <div>
                                            <div class="type">
                                                {{ formatReserveInfoType(item.registrationFormItem) }}
                                            </div>
                                            <div class="desc">
                                                预计就诊时间: {{ formatReserveInfo(item.registrationFormItem.oldReserveInfo) }}
                                            </div>
                                            <div class="desc">
                                                实际就诊时间: {{ formatReserveInfo(item.registrationFormItem) }}
                                            </div>
                                        </div>
                                    </abc-popover>
                                </template>
                                <template v-else>
                                    {{ formatReserveDate(item.registrationFormItem) }}
                                </template>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemConsultingRoomName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.consultingRoomName || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemVisitSourceRemark="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.visitSourceRemark || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemVisitSourceFromName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.visitSourceFromName || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemConsultantName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.consultantName || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #chargeSheetQueryExceptionType="{ trData: item }">
                            <abc-table-cell>
                                <abc-icon v-if="showExceptionIcon(item)" icon="Attention"></abc-icon>
                            </abc-table-cell>
                        </template>
                    </abc-table>
                    <abc-table
                        v-else
                        ref="table"
                        class="table"
                        :loading="loading"
                        :render-config="therapyRenderConfig"
                        :data-list="therapyTableData"
                        tr-clickable
                        @handleClickTr="openDialog"
                    >
                        <template #patientName="{ trData: item }">
                            <abc-table-cell>
                                <abc-flex class="ellipsis" align="center">
                                    <abc-icon
                                        v-if="viewDistributeConfig.Registration.isRegistrationListPerformance && item.patient.sex"
                                        :style="{ marginRight: '2px' }"
                                        icon="user"
                                        :size="12"
                                        :color="item.patient.sex === '男' ? '#58A0FF' : '#FF6082'"
                                    ></abc-icon>
                                    <abc-text :title="item.patient.name || ''" theme="primary-light">
                                        {{ item.patient.name || '' }}
                                    </abc-text>
                                    <img
                                        v-if="item.patient.isMember"
                                        style="width: 16px; height: 16px; margin-left: 4px;"
                                        src="~assets/images/vip.png"
                                        :title="item.patient?.memberTypeName || ''"
                                        alt=""
                                    />
                                    <img
                                        v-if="item.registrationFormItem?.registrationCategory === RegistrationCategory.CONVENIENCE"
                                        style="width: 16px; height: 16px; margin-left: 2px;"
                                        src="~assets/images/icon-convenience.png"
                                        alt=""
                                    />
                                </abc-flex>
                            </abc-table-cell>
                        </template>
                        <template #patientSex="{ trData: item }">
                            <abc-table-cell>{{ item.patient.sex || '' }}</abc-table-cell>
                        </template>
                        <template #patientMobile="{ trData: item }">
                            <abc-table-cell>
                                <template v-if="!isCanSeePatientMobileInRegistration">
                                    {{ encryptMobile(item.patient.mobile) }}
                                </template>
                                <template v-else>
                                    {{ item.patient.mobile }}
                                </template>
                            </abc-table-cell>
                        </template>
                        <template #patientAge="{ trData: item }">
                            <abc-table-cell>
                                {{ formatAge(item.patient.age, {
                                    monthYear: 12, dayYear: 1,
                                }) }}
                            </abc-table-cell>
                        </template>
                        <template #patientSn="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.patient.sn || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientIdCard="{ trData: item }">
                            <abc-table-cell v-if="item.patient.idCard">
                                <span v-abc-title.ellipsis="`[${item.patient.idCardType || '身份证'}]${item.patient.idCard || ''}`"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientSourceFromName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.patient.sourceFromName || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientTags="{ trData: item }">
                            <abc-table-cell>
                                <div v-if="item.patient.tags?.length" style="width: 100%;" class="ellipsis">
                                    <overflow-flex-tags-wrapper :tags="item.patient.tags"></overflow-flex-tags-wrapper>
                                </div>
                            </abc-table-cell>
                        </template>
                        <template #patientMemberTypeName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.patient.memberTypeName || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientRemark="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.patient.remark || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #patientPrimaryTherapistName="{ trData: item }">
                            <abc-table-cell>{{ item.patient.primaryTherapistName || '' }}</abc-table-cell>
                        </template>
                        <template #patientDutyTherapistName="{ trData: item }">
                            <abc-table-cell>{{ item.patient.dutyTherapistName || '' }}</abc-table-cell>
                        </template>
                        <template #registrationFormItemConsultingRoomName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.consultingRoomName || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemDoctorName="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.doctorName || '--'" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemIsExecuted="{ trData: item }">
                            <abc-table-cell>
                                <abc-popover
                                    v-if="item.registrationFormItem?.isExecuted"
                                    popper-class="execute-item-info-popper"
                                    placement="bottom-end"
                                    trigger="hover"
                                    :width="240"
                                    :close-delay="0"
                                    theme="yellow"
                                    :offset="10"
                                >
                                    <abc-icon slot="reference" icon="positive_ active" color="#0eba52"></abc-icon>
                                    <ul class="execute-item-list">
                                        <li class="header">
                                            <span>项目</span>
                                            <span>执行次数</span>
                                        </li>
                                        <template v-if="item.registrationFormItem?.executeActions?.length">
                                            <li v-for="(action, index) in item.registrationFormItem?.executeActions" :key="`${action.productName}-${index}`">
                                                <span>{{ action.productName }}</span>
                                                <span>{{ action.executeCount }} {{ action.executeUnit }}</span>
                                            </li>
                                        </template>
                                    </ul>
                                </abc-popover>
                                <abc-icon v-else icon="positive_" color="#dadbe0"></abc-icon>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemOrderNoStr="{ trData: item }">
                            <abc-table-cell>
                                {{ item.registrationFormItem.orderNoStr || '' }}
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemRegistrationProductNames="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.registrationProductNames || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemReserveDateTime="{ trData: item }">
                            <abc-table-cell>
                                <template v-if="item.registrationFormItem?.oldReserveInfo">
                                    <abc-popover
                                        popper-class="registration-info-popper"
                                        placement="bottom-end"
                                        trigger="hover"
                                        :width="240"
                                        :close-delay="0"
                                        theme="yellow"
                                        :offset="10"
                                        :arrow-offset="120"
                                    >
                                        <span slot="reference">
                                            {{ formatReserveDate(item.registrationFormItem) }}
                                            <abc-icon
                                                icon="history"
                                                color="#7a8794"
                                                size="14"
                                                style="margin-left: 4px;"
                                            ></abc-icon>
                                        </span>
                                        <div>
                                            <div class="type">
                                                {{ formatReserveInfoType(item.registrationFormItem) }}
                                            </div>
                                            <div class="desc">
                                                预计就诊时间: {{ formatReserveInfo(item.registrationFormItem.oldReserveInfo) }}
                                            </div>
                                            <div class="desc">
                                                实际就诊时间: {{ formatReserveInfo(item.registrationFormItem) }}
                                            </div>
                                        </div>
                                    </abc-popover>
                                </template>
                                <template v-else>
                                    {{ formatReserveDate(item.registrationFormItem) }}
                                </template>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemStatus="{ trData: item }">
                            <abc-table-cell>
                                <abc-tag-v2
                                    size="medium"
                                    :theme="getStatusTagV2Style(item).theme"
                                    :variant="getStatusTagV2Style(item).variant"
                                >
                                    {{ item.registrationFormItem.statusName }}
                                </abc-tag-v2>
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemType="{ trData: item }">
                            <abc-table-cell>
                                {{ formatTypeName(item.registrationFormItem.type) }}
                            </abc-table-cell>
                        </template>
                        <template #registrationFormItemVisitSourceRemark="{ trData: item }">
                            <abc-table-cell>
                                <span v-abc-title.ellipsis="item.registrationFormItem.visitSourceRemark || ''" class="ellipsis"></span>
                            </abc-table-cell>
                        </template>
                    </abc-table>
                </abc-layout-content>
                <abc-layout-footer>
                    <abc-pagination
                        style="width: 100%;"
                        :show-total-page="!isSearching && !!isInSearch"
                        :total-page-align-right="!isSearching && !!isInSearch"
                        :pagination-params="params"
                        :count="tableDataCount"
                        :page-sizes="pageSizeList"
                        :page-sizes-width="100"
                        show-size
                        hide-last-page-count
                        @current-change="pageChange"
                        @size-change="pageSizeChange"
                    >
                        <ul v-if="!isInSearch && isOutpatient" slot="tipsContent">
                            <template v-if="summaryDataLoaded">
                                <li>
                                    {{ currentDisplayStatusName }} <span>{{ statCount || 0 }}</span>，
                                </li>
                                <li>
                                    {{ $t('registrationFeeName') }}实收：
                                    <span><abc-money :value="statAmount" is-show-space></abc-money></span>
                                </li>
                            </template>
                        </ul>
                    </abc-pagination>
                </abc-layout-footer>
            </abc-layout>

            <!--board-view需要预知高度才能计算 style设置为calc(100vh - 134px)-->
            <board-view
                v-if="isBoardViewMode && quickListModel.list && isConfigInit"
                ref="borderView"
                :is-board-day-view-mode="isBoardDayViewMode"
                :is-board-week-view-mode="isBoardWeekViewMode"
                :is-single-doctor="isSingleDoctor"
                :board-view-source="boardViewSource"
                :doctor-list="doctorList"
                :time-range="timeRange"
                :has-departments="hasDepartments"
                :departments="departments"
                :business-type="businessType"
                :style="showConsultant ? 'height: calc(100vh - 86px);' : 'height: calc(100vh - 134px);'"
                :is-can-modify-registration-info="isCanSeeModifyRegistrationInfo"
                @prev-week="handleClickPrevWeekBtn"
                @next-week="handleClickNextWeekBtn"
                @finish-registration="handleFinishRegistrationInfo"
                @new-add="handleNewAdd"
                @board-view-registration-update="handleBoardViewRegistrationUpdate"
                @refresh="refreshRegistrations"
            ></board-view>

            <finish-dialog
                v-if="showFinishDialog"
                v-model="showFinishDialog"
                :business-type="businessType"
                :old-registration="finishRegistration.before"
                :registration-info="finishRegistration.after"
                :is-show-add-icon="finishRegistration.isShowAddIcon"
                @refresh="refreshRegistrations"
            ></finish-dialog>

            <register-appointment-setting-dialog
                v-if="isShowRegisterAppointmentSettingDialog"
                v-model="isShowRegisterAppointmentSettingDialog"
            >
            </register-appointment-setting-dialog>

            <stop-diagnose-dialog
                v-if="showSendDiscontinuationMessage"
                v-model="showSendDiscontinuationMessage"
                :table-data="patientData"
                :current-department-id="currentDepartmentId"
                :current-doctor-id="currentDoctorId"
                :current-reserve-date="currentReserveDate"
            >
            </stop-diagnose-dialog>
        </abc-container-center>
    </abc-container>
</template>

<script>
    import Base from 'views/registration/base.js';
    const BoardView = () => import('@/views-dentistry/registration/board-view');
    const StopDiagnoseDialog = () => import('@/views-dentistry/registration/stop-diagnose-dialog');
    import {
        BOARD_VIEW_MODE, BOARD_VIEW_SOURCE, VIEW_MODE_ENUM, REGISTRATION_DOCTOR_STATUS,
    } from './constants';
    import { mapGetters } from 'vuex';
    import SmartTools, { SmartToolsType } from 'views/layout/smart-tools';
    import DateTimeSelector, {
        DATETIME_SELECTOR_MODE,
    } from '@/views-dentistry/registration/components/datetime-selector';
    import {
        getOneWeekStartAndEndDate,
        getTodayStartAndEndDate,
        showRegistrationProducts,
        statusV2Color,
    } from '@/views-dentistry/registration/drag-grid-v1/common';
    import {
        formatAge, parseTime,
    } from 'utils/index';
    import { WEEK } from 'views/registration/date-config';
    const RegisterAppointmentSettingDialog = () => import('./register-appointment-setting-dialog');
    import RegistrationsAPI from 'api/registrations/index';
    import QuickListModel from 'views/registration/adapter/quick-list-model-new';
    import Clone from 'utils/clone';
    import RegistrationListModel from 'views/registration/adapter/registration-list-model';
    import { flatObjectByPath } from 'views/statistics/common/custom-stat-header-dialog/util';
    import {
        getUserDefaultView, setUserDefaultView,
    } from '@/views-dentistry/registration/common';
    import {
        PayStatusV2, StatusV2, StatusV2ToText,
    } from 'views/registration/common/constants';
    import AbcAccess from '@/access/utils';
    import {
        debounce,
        isEqual,
        uniqueWithKey,
    } from 'utils/lodash';
    import AbcSocket from 'views/common/single-socket.js';
    const RegistrationListSearch = () => import('./components/registration-list-search.vue');
    import PatientsAPI from 'api/patients';
    import AppointmentCardDialog from '@/views-dentistry/registration/appointment-card-dialog';
    import {
        RESERVATION_MODE_TYPE,
        RESERVATION_TYPE,
    } from 'views/settings/registered-reservation/constant';
    import { SCHEDULE_SHIFTS_STATUS } from './diagnose-config';
    import { RegistrationCategory } from '@/views-hospital/registered-fee/constant';
    import OverflowFlexTagsWrapper from 'src/views/registration/components/overflow-flex-tags-wrapper.vue';
    import AbcMoney from 'src/components/abc-money/index.vue';
    import { RevisitStatus } from 'assets/configure/constants';
    import {
        formatReserveDate,
        formatReserveInfoType,
        formatReserveInfo,
        formatTypeName,
    } from 'src/views/registration/outpatient-header-config.js';
    import { encryptMobile } from 'utils/crm';
    import { getTimeDifferenceWithNow } from '@/utils';
    import { formatDate } from '@abc/utils-date';
    import AllocationDoctorConsultantDialog from 'views/layout/allocation-doctor-consultant';
    import OutpatientAPI from 'api/outpatient';
    import { LockBusinessKeyEnum } from '@/common/constants/business-lock';
    import AbcSearch from '@/components/abc-search/index.vue';
    import { ReferralFlagEnum } from '@/common/constants/registration';
    export const boardViewModeOptions = [
        {
            label: '日',
            value: BOARD_VIEW_MODE.DAY,
        },
        {
            label: '周',
            value: BOARD_VIEW_MODE.WEEK,
        },
    ];

    export default {
        name: 'RegistrationTableV2',
        components: {
            DateTimeSelector,
            SmartTools,
            BoardView,
            RegisterAppointmentSettingDialog, // 挂号预约的设置弹窗
            RegistrationListSearch,
            StopDiagnoseDialog,
            OverflowFlexTagsWrapper,
            AbcMoney,
            AbcSearch,
        },
        mixins: [Base],
        data() {
            return {
                ReferralFlagEnum,
                REGISTRATION_DOCTOR_STATUS,
                VIEW_MODE_ENUM,
                BOARD_VIEW_MODE,
                DATETIME_SELECTOR_MODE,
                statusV2Color,
                StatusV2ToText,
                BOARD_VIEW_SOURCE,
                viewMode: VIEW_MODE_ENUM.LIST,
                boardViewMode: BOARD_VIEW_MODE.DAY,
                isSingleDoctorView: false,
                SmartToolsType,
                RegistrationCategory,
                RevisitStatus,
                StatusV2,
                PayStatusV2,
                timeRange: getTodayStartAndEndDate(),
                qlSearchHint: '姓名/手机/诊号',
                isShowRegisterAppointmentSettingDialog: false,
                boardViewModeOptions,
                viewModeOptions: [
                    {
                        label: '列表',
                        value: VIEW_MODE_ENUM.LIST,
                    },
                    {
                        label: '看板',
                        value: VIEW_MODE_ENUM.BOARD,
                    },
                ],
                isShowScanRegisterDetailFlag: false,
                fullScreenMode: false,
                tableFixed2HoverIndex: -1,
                accessMap: AbcAccess.accessMap,
                exportLoading: false,
                showSendDiscontinuationMessage: false,
                showStopDiagnoseButton: false,
                isDisabledSend: false,
                patientData: [],
                currentDepartmentId: '',
                currentDoctorId: '',
                currentDoctorName: '',
                currentReserveDate: '',
                shiftTimes: '',
                clinicList: [],
                searchInputWidth: 200,
                listSearchWidth: 260,
                quickFooterTabValue: 0,
                quickFooterTabOptions: [{
                    label: '小工具',
                    value: 0,
                }],
            };
        },
        computed: {
            ...mapGetters([
                'isOpenCall',
                'registrationsConfig',
                'registrationsConfigIsInit',
                'therapyReservationConfig',
                'therapyReservationConfigIsInit',
                'isEnableRegUpgrade',
                'isCanSeePatientMobileInRegistration',
                'isCanSeeModifyRegistrationInfo',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            hasLeftContainer() {
                return !this.fullScreenMode || this.isListViewMode;
            },
            outPatientRenderConfig() {
                const options = this.getOutpatientTableHeader();
                options.push({
                    label: '',
                    prop: 'charge.sheetQueryExceptionType',
                    width: 30,
                });
                const list = options.map((item) => {
                    const {
                        label,
                        width,
                        align,
                        sortable,
                        prop,
                    } = item;
                    const propList = prop.split('.');
                    const key = propList[0] + propList[1]?.charAt(0)?.toUpperCase() + propList[1]?.slice(1);

                    return {
                        label,
                        key,
                        sortable: !!sortable,
                        style: {
                            flex: 'none',
                            width: `${width}px`,
                            textAlign: align,
                        },
                    };
                });

                return {
                    hasInnerBorder: false,
                    list,
                };
            },
            therapyRenderConfig() {
                const options = this.getTherapyTableHeader();
                const list = options.map((item) => {
                    const {
                        label,
                        width,
                        titleAlign,
                        prop,
                    } = item;
                    const propList = prop.split('.');
                    const key = propList[0] + propList[1]?.charAt(0)?.toUpperCase() + propList[1]?.slice(1);

                    return {
                        label,
                        key,
                        style: {
                            flex: 'none',
                            width: `${width}px`,
                            textAlign: titleAlign,
                        },
                    };
                });

                return {
                    hasInnerBorder: false,
                    list,
                };
            },
            showConsultant() {
                const { showConsultant } = this.viewDistributeConfig.Registration;
                return showConsultant;
            },
            isConfigInit() {
                if (this.isTherapy) {
                    return this.therapyReservationConfigIsInit;
                }
                return this.registrationsConfigIsInit;
            },
            listModeDate: {
                get() {
                    return [this.cursorDate];
                },
                set(value) {
                    this.$refs.dataList.selectDateHandle(value[0]);
                },
            },
            isShowSettingButton() {
                return this.isListViewMode || this.showOpenAppointmentAudioSetting;
            },
            showOpenAppointmentAudioSetting() {
                // 灵活模式
                // 患者微信发起预约申请，需诊所确认后才可完成预约
                return this.registrationsConfig.wechatReserveNeedApply === 1 && !this.isFixOrderMode;
            },
            //展示预约设置语音
            showRegistrationVoiceSetting() {
                return this.viewDistributeConfig.Registration.audioOpen;
            },
            isFixOrderMode() {
                // modeType 0: 固定号源模式 1: 灵活预约模式
                return (this.businessType === 0 ? this.registrationsConfig.modeType : this.therapyReservationConfig.modeType) === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            boardViewSource() {
                return this.isFixOrderMode ? BOARD_VIEW_SOURCE.CODE : BOARD_VIEW_SOURCE.TIME;
            },
            doctorList() {
                return this.isSingleDoctor ? [this.selectedDoctor] : this.quickListModel.list;
            },
            showAllDoctor() {
                return !!(this.isClinicAdmin || this.hasDoctorHelperModule);
            },
            isListViewMode() {
                return this.viewMode === VIEW_MODE_ENUM.LIST;
            },
            isBoardViewMode() {
                return this.viewMode === VIEW_MODE_ENUM.BOARD;
            },
            isBoardDayViewMode() {
                return this.boardViewMode === BOARD_VIEW_MODE.DAY;
            },
            isBoardWeekViewMode() {
                return this.boardViewMode === BOARD_VIEW_MODE.WEEK;
            },
            isSingleDoctor() {
                return !!this.selectedDoctor.doctor.id;
            },
            visibleCallControl() {
                if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.CALLING_NUM)) return false;
                if (!this.isOpenCall) return false;
                // 门店管理员、医生权限、医助  均显示叫号器
                return this.isClinicAdmin || this.canDiagnosis || this.permissionHasAssistant;
            },
            // @override
            currentValue: {
                get() {
                    return this.value;
                },
                set() {
                    // DO NOTHING
                },
            },
            /**
             * 是否禁止挂号费议价
             */
            isDisableRegisteredCharge() {
                return !this.userInfo?.isAdmin && this.chargeConfig.reservationRegisteredBargainSwitch === 0;
            },
        },
        watch: {
            cursorDate(newValue) {
                this.resetSearchParams();
                this.handleChangeDate(newValue);
            },
            viewMode(newValue) {
                this.resetSearchParams();
                if (newValue === VIEW_MODE_ENUM.LIST) {
                    this.fetchTableList(true);
                }
                setUserDefaultView({
                    viewMode: newValue,
                    boardViewMode: this.boardViewMode,
                    fullScreenMode: this.fullScreenMode,
                });
            },
            boardViewMode(newValue) {
                setUserDefaultView({
                    viewMode: this.viewMode,
                    boardViewMode: newValue,
                    fullScreenMode: this.fullScreenMode,
                });
            },
            fullScreenMode(newValue) {
                setUserDefaultView({
                    viewMode: this.viewMode,
                    boardViewMode: newValue,
                    fullScreenMode: this.fullScreenMode,
                });
            },
            tableData() {
                this.$nextTick(() => {
                    this.initTableTickerAndScroll();
                });
            },
        },
        beforeMount() {
            const defaultView = getUserDefaultView(this.isFixOrderMode);
            console.warn(defaultView);
            this.viewMode = defaultView.viewMode;
            this.boardViewMode = defaultView.boardViewMode === undefined ? BOARD_VIEW_MODE.DAY : defaultView.boardViewMode;
            this.fullScreenMode = defaultView.fullScreenMode;
            if (defaultView.boardViewMode === BOARD_VIEW_MODE.WEEK) {
                this.timeRange = getOneWeekStartAndEndDate(new Date());
            }
        },
        mounted() {
            const { socket } = AbcSocket.getSocket();
            this._socket = socket;
            this._handleRegistrationStatusTypeChange = () => {
                this.fetchTableList(false);
            };
            this._socket.on('registration.status_change', this._handleRegistrationStatusTypeChange);
            this.$abcEventBus.$on('save-patient-info-success',(data) => {
                if (this.isListViewMode) {
                    this.fetchTableList(false);
                } else {
                    this.handleBoardViewRegistrationUpdate(data.registrationData);
                }
            },this);
            this.screenSizeChange();
            this._screenChange = debounce(this.screenSizeChange, 300, true);
            window.addEventListener('resize', this._screenChange);
            this.$on('hook:beforeDestroy', () => {
                window.removeEventListener('resize', this._screenChange);
            });
        },
        beforeDestroy() {
            if (this._intervalTimer) {
                clearInterval(this._intervalTimer);
                this._intervalTimer = null;
            }
            this._socket.off('registration.status_change', this._handleRegistrationStatusTypeChange);
            this.$abcEventBus.$offVmEvent(this._uid);
            this._appointmentCardDialogInstance?.destroyDialog();
        },
        methods: {
            formatAge,
            formatReserveDate,
            formatReserveInfoType,
            formatReserveInfo,
            formatTypeName,
            encryptMobile,
            showRegistrationProducts,
            initTableTickerAndScroll() {
                if (this._intervalTimer) {
                    clearInterval(this._intervalTimer);
                    this._intervalTimer = null;
                }
                // 15s刷新一下表格
                // 确保等待时间计算是实时的
                if (this.isOutpatient && this.viewDistributeConfig.Registration.isRegistrationListPerformance) {
                    this._intervalTimer = setInterval(() => {
                        if (this.$refs.table) {
                            this.$refs.table.$forceUpdate();
                        } else {
                            clearInterval(this._intervalTimer);
                        }
                    }, 15 * 1000);
                    this.$watch(() => this.$refs?.table?.hoverIndex, (newVal) => {
                        this.tableFixed2HoverIndex = newVal;
                    });
                    const scrollTable = this.$refs?.table?.$el?.querySelector('.abc-table__body-wrapper');
                    if (scrollTable) {
                        const handler = () => {
                            // this.$el.querySelector('.registration-table-fixed-right-handler-bar').style.transform = `translateY(-${scrollTable.scrollTop}px)`;
                        };
                        if (!scrollTable.__isAddScrollListener) {
                            scrollTable.addEventListener('scroll', handler);
                            scrollTable.__isAddScrollListener = true;
                        }
                        handler();
                    }
                }
            },
            openAppointmentAudioSetting() {
                this.isShowRegisterAppointmentSettingDialog = true;
            },
            handleClickPrevWeekBtn() {
                this.$refs.dateTimeSelector.prev();
            },
            handleClickNextWeekBtn() {
                this.$refs.dateTimeSelector.next();
            },
            handleChangeTimeRange(type) {
                this.resetSearchParams();
                if (this.isBoardWeekViewMode) {
                    const newDate = parseTime(new Date(this.cursorDate).getTime() + (type === 'prev' ? -WEEK : WEEK), 'y-m-d', true);
                    this.$refs.dataList.selectDateHandle(newDate);
                }
                if (this.isBoardDayViewMode) {
                    this.$refs.dataList.selectDateHandle(this.timeRange[0]);
                }
            },
            handleChangeDate(date) {
                if (this.isBoardWeekViewMode) {
                    this.timeRange = getOneWeekStartAndEndDate(date);
                }
                if (this.isBoardDayViewMode) {
                    const startDate = parseTime(date, 'y-m-d', true);
                    this.timeRange = [startDate, startDate];
                }
            },

            handlePatientIdChange(patientId) {
                this.params.offset = 0;
                this.params.displayStatus = '';
                this.params.patientId = patientId;
                this.isSearching = false;
                this._fetch();
            },

            resetSearchParams() {
                this.isSearching = false;
                this.params.keyword = '';
                this.params.patientId = '';
            },

            async handleBoardListAutocompleteItemButtonClick(registrationData = {}) {
                const patient = {
                    ...registrationData,
                };
                delete patient.registrationFormItem;
                const today = parseTime(new Date(), 'y-m-d', true);
                const signItem = registrationData?.registrationFormItem;
                const isSign = !!signItem;
                const handleNotFoundRegistration = () => {
                    this.$Toast({
                        message: '未找到预约信息',
                        type: 'error',
                    });
                };

                // 搜索中需要重置
                if (this.params.patientId) {
                    this.resetSearchParams();
                    this._fetch();
                }

                // 不是签到直接新增
                if (!isSign) {
                    // 如果当前日期不是今天
                    if (this.params.date !== today) {
                        await this.handleDateInput(today);
                    }
                    this.openAppointmentCardDialog({ doctorId: null }, null, null, '', {
                        itemPatient: patient,
                    });
                    return;
                }

                if (this.isBoardViewMode) {
                    this.handleBoardViewAutocompleteInputSelect({
                        registrationFormItem: signItem,
                        id: signItem?.registrationSheetId,
                    });
                    return;
                }

                const searchRegistrationId = signItem?.id;
                const searchRegistrationDate = signItem?.reserveDate;

                // 重置参数
                Object.assign(this.params, {
                    offset: 0,
                    limit: 100,
                    departmentId: '',
                    doctorId: '',
                    keyword: '',
                    displayStatus: '',
                    patientId: '',
                });

                // 跳转到对应天的列表
                await this.handleDateInput(searchRegistrationDate);

                // 再次在列表列表中查找
                const searchRegistrationIndex = this.tableData.findIndex((item) => item.id === searchRegistrationId);
                if (searchRegistrationIndex !== -1) {
                    const row = this.tableData[searchRegistrationIndex];
                    this.openDialog(row);
                    return;
                }

                handleNotFoundRegistration();
            },
            handleBoardViewAutocompleteInputSelect(registrationData, selectTriggerCardOpen = true) {
                if (this.$refs.borderView) {
                    this.$refs.borderView.handleBoardViewSearch(registrationData ?? {}, async (searchOptions, next) => {
                        if (searchOptions.boardViewMode !== undefined && selectTriggerCardOpen) {
                            this.boardViewMode = searchOptions.boardViewMode;
                        }

                        if (searchOptions.changeDate) {
                            this.datePicker = searchOptions.changeDate;
                            this.cursorDate = new Date(searchOptions.changeDate);
                            this.params.offset = 0;
                            this.params.date = this.datePicker || this.today;
                            this.dateFilterStr = this.datePicker === parseTime(new Date(), 'y-m-d', true) ? 'today' : 'datePicker';
                            this.handleChangeDate(this.cursorDate);
                            await this.fetchQuickList(false);
                        } else {
                            // 看板更新之后也需要更新quickList
                            if (!selectTriggerCardOpen) {
                                await this.fetchQuickList(false);
                            }
                        }

                        if (searchOptions.doctorId) {
                            if (!this.doctorList.some((item) => item.doctor.id === searchOptions.doctorId)) {
                                const findedDoctor = this.quickListModel.list.find((item) => item.doctor.id === searchOptions.doctorId);
                                if (findedDoctor) {
                                    this.selectedDoctor = findedDoctor;
                                }
                            }
                        }

                        // 视图切换完成并且搜索完成后设置激活的元素id
                        await this.$nextTick();
                        next(selectTriggerCardOpen);
                    });
                }
            },
            handleClickBoardView() {
                this.handleChangeDate(this.cursorDate);
                this.updateBoardView();
            },
            // @override
            getParams() {
                let params = {};
                // 有搜索条件的，只根据搜索条件查询
                if (this.isInSearch) {
                    const {
                        offset,
                        limit,
                    } = this.params;
                    params = {
                        offset,
                        limit,
                        departmentId: '',
                        doctorId: '',
                        date: '',
                        keyword: this.params.keyword,
                        patientId: this.params.patientId,
                        registrationType: this.isOutpatient ? 0 : 1,
                        invoiceStatusOrderBy: this.params.invoiceStatusOrderBy,
                    };
                } else {
                    params = Clone(this.params);
                }

                if (this.isOutpatient || this.isEnableRegUpgrade) {
                    params.registrationType = this.isOutpatient ? 0 : 1;
                }

                return params;
            },
            // @override
            async fetchTableList(loading = true) {
                if (!this.isListViewMode) {
                    return;
                }
                this.loading = loading;
                // 顶部状态数据

                const params = this.getParams();
                try {
                    this.fetchSummaryData(params);

                    let data = null;
                    if (this.isOutpatient || this.isEnableRegUpgrade) {
                        data = await RegistrationsAPI.registrationsQuickList({ ...params });
                    } else {
                        data = await RegistrationsAPI.fetchTherapyItemList(params);
                    }

                    if (isEqual(params,this.getParams())) {
                        this.registrationListModel = new RegistrationListModel(data, this.businessType);
                        this.registrations = this.registrationListModel.list;

                        if (!params.departmentId) {
                            this.listFilterDepartments = uniqueWithKey(this.registrations.map((item) => {
                                return {
                                    departmentName: item.departmentName || '其他',
                                    departmentId: item.departmentId,
                                    departmentStatus: item.departmentStatus,
                                };
                            }), 'departmentId');
                        }

                        if (this.isOutpatient) {
                            this.tableHeader = Clone(data.data.header);
                            this.tableHeader.splice(0, 0, {
                                prop: 'reserve.render',
                                label: '',
                                width: 36,
                                renderType: 'reserveRender',
                            });
                            this.tableData = this.registrations.map((item) => flatObjectByPath(item));
                        } else {
                            this.therapyTableHeader = data.data.header;
                            this.therapyTableData = this.registrations.map((item) => flatObjectByPath(item));
                        }

                        const {
                            currTotalCount,
                            totalCount,
                        } = data?.data || {};

                        this.tableDataCount = (this.isInSearch ? totalCount : currTotalCount) || 0;
                    }
                } catch (e) {
                    console.error(e);
                    if (isEqual(params,this.getParams())) {
                        this.registrationListModel = new RegistrationListModel({}, this.businessType);
                        this.registrations = this.registrationListModel.list;
                    }
                }

                this.loading = false;
                this.isSearching = false;
            },
            // @override
            async fetchQuickList(select, insertDoctorWhenNotInList = true) {
                const {
                    date,
                    offset,
                } = this.params;
                // 拉取医生ql列表，limit 默认9999，保证能全拉
                const limit = 9999;
                try {
                    const data = await RegistrationsAPI.fetchQuickList(date, limit, offset, this.businessType);
                    const quickListModel = new QuickListModel(data, this.businessType, date);
                    // 如果list中没有选中的医生 则增加到list中
                    if (insertDoctorWhenNotInList) {
                        const isQuickListIncludeSelectedDoctor = quickListModel.viewData.list.some((item) => item.doctor.id === this.selectedDoctor.doctor.id);
                        if (!isQuickListIncludeSelectedDoctor) {
                            const holderDoctor = {
                                ...QuickListModel.defaultFirstItem,
                                doctor: this.selectedDoctor.doctor,
                            };
                            // 插入到全部医生下面
                            quickListModel.viewData.list.splice(1, 0, holderDoctor);
                            this.selectedDoctor = holderDoctor;
                        } else {
                            this.selectedDoctor = quickListModel.viewData.list.find((item) => item.doctor.id === this.selectedDoctor.doctor.id);
                        }
                    }
                    this.quickListModel = quickListModel;
                    this.todayCount = this.quickListModel.todayCount;
                    this.tomorrowCount = this.quickListModel.tomorrowCount;
                    this.dayAfterTomorrowCount = this.quickListModel.dayAfterTomorrowCount;
                    this.dateCount = this.quickListModel.totalCount;
                    this.today = this.quickListModel.today;
                    if (select) {
                        this.select(this.quickListModel.firstItem);
                    }
                } catch (e) {
                    console.error('fetchQuickList', e);
                } finally {
                    if (this._cursorDateChangePromiseResolve) {
                        this._cursorDateChangePromiseResolve();
                        this._cursorDateChangePromiseResolve = null;
                    }
                }
            },

            async handleNewAdd(patient = {}) {
                let pastHistory = '', allergicHistory = '';
                if (patient.id) {
                    const history = await PatientsAPI.fetchPastHistory(patient.id);
                    pastHistory = history.pastHistory;
                    allergicHistory = history.allergicHistory;
                }

                this.openAppointmentCardDialog({
                    patient, doctorId: '',
                }, null, null, 'card-dropdown', {
                    registrationId: undefined,
                    isReserved: 1,
                    date: undefined,
                    pastHistory,
                    allergicHistory,
                });
            },

            // @override
            openDialog(item, index, e, formSource = '') {
                this.openAppointmentCardDialog(item, index, e, formSource, {
                    isLookDetail: true,
                });
            },
            openAppointmentCardDialog(item, index, e, formSource = '', extendCardProps = {}) {
                // 存在dropdown时需要不出现弹窗
                if (e?.composedPath()?.some((dom) => dom.classList?.contains('abc-dropdown-wrapper'))) {
                    return;
                }

                if (this._appointmentCardDialogInstance) {
                    this._appointmentCardDialogInstance?.destroyDialog();
                    this._appointmentCardDialogInstance = null;
                }
                this._appointmentCardDialogInstance = new AppointmentCardDialog(Object.assign({
                    value: true,
                    doctorId: item.doctorId,
                    doctorName: item.doctorName || '',
                    departmentId: item?.departmentId,
                    formSource,
                    disabledPatient: formSource === 'scan-register',
                    hasDepartments: this.hasDepartments,
                    departments: this.departments,
                    date: item.reserveDate || parseTime(this.cursorDate, 'y-m-d', true),
                    registrationId: item.registrationFormItem?.registrationSheetId,
                    businessType: this.businessType,
                    isReserved: item.isReserved || +(!(this.dateFilterStr === 'today' && this.isOutpatient)),
                    itemPatient: item.patient,
                    itemStatusV2: item.statusV2,
                    registrationCategory: item.registrationFormItem?.registrationCategory,
                    isCanModifyRegistrationInfo: this.isCanSeeModifyRegistrationInfo,
                    referralFlag: item.referralFlag,
                    refresh: (...props) => {
                        this.refreshRegistrations();
                        this.handleBoardViewRegistrationUpdate(...props);
                    },
                    close: () => {},
                    finishRegistration: (...props) => {
                        this.handleFinishRegistrationInfo(...props);
                        if (this.isShowScanRegisterDetailFlag) {
                            this.isShowScanRegisterDetailFlag = false;
                        }
                    },
                    newAdd: (patient) => {
                        this.handleNewAdd(patient);
                    },
                }, extendCardProps));
                this._appointmentCardDialogInstance.generateDialog({
                    parent: this,
                });
            },
            // @override
            async handleDateInput(date) {
                const value = this.formatToValue(date);
                this.datePicker = value;
                this.cursorDate = new Date(value);
                this.params.offset = 0;
                this.params.date = this.datePicker || this.today;
                this.dateFilterStr = this.datePicker === parseTime(new Date(), 'y-m-d', true) ? 'today' : 'datePicker';
                await this.fetchQuickList(false);
                if (this.isListViewMode) {
                    await this.fetchTableList();
                } else {
                    await this.updateBoardView();
                }
            },
            // @override
            refreshRegistrations() {
                if (this.isListViewMode) {
                    this.fetchQuickList(false);
                    this.fetchTableList();
                }
            },
            // @override
            handleTabChanged(value) {
                // reset params
                this.params = {
                    offset: 0,
                    limit: 100,
                    departmentId: '',
                    doctorId: '',
                    date: parseTime(new Date(this.cursorDate), 'y-m-d', true),
                    keyword: '',
                    displayStatus: '',
                    patientId: '', // 健康卡扫描之后，使用 patientId 进行搜索
                    registrationType: value,
                };

                this.$router.push({
                    name: 'registration',
                    query: {
                        tab: value,
                    },
                });

            },
            // @override
            prepareData(isImmediateCall) {
                this.fetchPrintRegistrationConfig();
                this.fetchPrintAllConfigIfNeed();
                this.isOutpatient && this.fetchReserveConfig();

                this.fetchDoctors();

                this.fetchAllDoctor();

                this.fetchQuickList(true, false).then(() => {
                    !isImmediateCall && this.updateBoardView();
                });
            },
            // 获取停诊通知人员数据
            async getSendPatientList(doctorId, departmentId, date, doctorName) {
                try {
                    const { data } = await RegistrationsAPI.fetchScheduleEmployee(doctorId, {
                        departmentId,
                        registrationType: 0,
                        workingDate: date,
                        status: SCHEDULE_SHIFTS_STATUS.STOP_DIAGNOSE,
                    });
                    this.clinicList = [];
                    const shiftTimeList = [];
                    data.rows?.forEach((item) => {
                        shiftTimeList.push(`${item.shiftStart}-${item.shiftEnd}`);
                        if (!this.clinicList.some((i) => i.name === item.departmentName)) {
                            this.clinicList.push({
                                name: item.departmentName,
                                shiftTime: `${item.shiftStart}-${item.shiftEnd}`,
                            });
                        } else {
                            this.clinicList.find((o) => o.name === item.departmentName).shiftTime += `,${item.shiftStart}-${item.shiftEnd}`;
                        }
                    });
                    this.shiftTimes = shiftTimeList?.join(',') || '';
                    const { data: patientData } = await RegistrationsAPI.fetchPatientsList({
                        doctorId,
                        departmentId,
                        registrationType: 0,
                        reserveDate: date,
                    });
                    this.patientData = patientData?.rows;
                    this.isDisabledSend = this.patientData.length > 0;
                    this.currentDepartmentId = departmentId;
                    this.currentDoctorId = doctorId;
                    this.currentDoctorName = doctorName;
                    this.currentReserveDate = date;
                } catch (e) {
                    console.log(e);
                }
            },
            async handleClickSelectPatient(item) {
                if (item.status === REGISTRATION_DOCTOR_STATUS.CONTAINS_STOP_DIAGNOSE) { // 停诊状态
                    const {
                        departmentId,
                        date,
                    } = this.getParams();
                    await this.getSendPatientList(item.doctor.id, departmentId, date, item.doctor.name);
                }
                this.showStopDiagnoseButton = item.status === REGISTRATION_DOCTOR_STATUS.CONTAINS_STOP_DIAGNOSE;
                this.params.doctorId = item.doctor.id;
                this.selectedDoctor = item;
                this.fetchTableList();
                this.updateBoardView();
            },
            handleBoardViewRegistrationUpdate(registrationInfo) {
                this.handleBoardViewAutocompleteInputSelect(registrationInfo, false);
            },
            async updateBoardView() {
                if (this.isBoardViewMode && this.$refs.borderView) {
                    console.warn('更新看板视图');
                    await this.$nextTick();
                    this.$refs.borderView.updateBoardView();
                }
            },
            async updateBoardViewData() {
                if (this.isBoardViewMode && this.$refs.borderView) {
                    console.warn('更新看板数据');
                    await this.$nextTick();
                    this.$refs.borderView.updateBoardViewData();
                }
            },
            getEventPath(evt) {
                if (!evt) return '';
                return evt.path || (evt.composedPath && evt.composedPath()) || '';
            },
            closeScanRegisterDetails(mousedown, mouseup) {
                if (!this.isShowScanRegisterDetailFlag) return;
                const classNames = [
                    'scan-register-detail-info-add',
                    'scan-register-epidemiological-list',
                    'drag-grid-mask',
                ];
                let exit = false;
                const mouseupPath = this.getEventPath(mouseup);
                if (mouseupPath) {
                    mouseupPath.forEach((item) => {
                        if (item.classList) {
                            const classList = Array.from(item.classList);
                            classNames.forEach((one) => {
                                if (Array.from(classList).includes(one)) {
                                    exit = true;
                                }
                            });
                        }
                    });
                }
                if (exit || this._appointmentCard) return;
                this.isShowScanRegisterDetailFlag = false;
            },
            openScanRegisterDetailFlag() {
                this.isShowScanRegisterDetailFlag = !this.isShowScanRegisterDetailFlag;
            },
            // 挂号的信息
            openRegisterDialog(item, e) {
                this.openDialog({ patient: item.patientInfo }, null, e, 'scan-register');
            },
            toggleFullScreen() {
                this.fullScreenMode = +(!this.fullScreenMode);
                this.updateBoardView();
            },
            async handleExportList() {
                const {
                    departmentId,
                    doctorId,
                    registrationType,
                    date,
                } = this.getParams();

                const params = {
                    departmentId,
                    doctorId,
                    registrationType,
                    start: date,
                    end: date,
                };

                const currentSelectedDoctor = this.selectedDoctor.doctor;
                const selectedDoctorName = currentSelectedDoctor.id ? currentSelectedDoctor.name : '';
                const registrationTypeName = registrationType === RESERVATION_TYPE.OUTPATIENT_RESERVATION ? '预约挂号名单' : '理疗预约名单';
                const fileName = `${registrationTypeName}-${selectedDoctorName}${date}.xlsx`;

                try {
                    this.exportLoading = true;
                    await RegistrationsAPI.exportRegistrationsQuickList(params, `${fileName}`);
                } catch (e) {
                    const { message } = e;
                    this.$Toast({
                        message,
                        type: 'error',
                        duration: 1000,
                    });
                } finally {
                    this.exportLoading = false;
                }
            },
            handleChangeSort({
                orderType,
            }) {
                this.params.invoiceStatusOrderBy = orderType;
                this.fetchTableList();
            },
            async sendMessageHandler() {
                const {
                    departmentId,
                    doctorId,
                    date,
                } = this.getParams();
                await this.getSendPatientList(doctorId, departmentId, date, this.currentDoctorName);
                this.showSendDiscontinuationMessage = true;
            },
            getInvoiceStatusDesc(type) {
                const obj = {
                    '1': '待开票',
                    '2': '已开票',
                    '4': '开票金额异常',
                };
                return obj[type] || '';
            },
            getStatusInfo(item) {
                const {
                    statusName,
                    statusV2,
                    isReserved,
                    signInTime,
                    estimatedDiagnoseTime,
                    created,
                    diagnosingTime,
                    waitDiagnoseTime,
                } = item?.registrationFormItem || {};
                let displayValue = statusName || '';
                let dropdownItems = [];
                let theme = 'primary';
                let variant = 'fill';
                let style = {};

                let displayValueDesc = '';
                if (statusV2 === StatusV2.WAITING_SIGN_IN_CONFIRM || statusV2 === StatusV2.WAITING_SIGN_IN) {
                    displayValue = '待签';
                    theme = 'warning';
                }

                if (statusV2 === StatusV2.CONTINUE_DIAGNOSED) {
                    displayValue = '回诊';
                    variant = 'ghost';
                }

                if (statusV2 === StatusV2.WAITING_SIGN_IN) {
                    dropdownItems = [{
                        label: '待签(已确认)',
                        value: StatusV2.WAITING_SIGN_IN_CONFIRM,
                    }, {
                        label: '待诊',
                        value: StatusV2.WAITING_DIAGNOSE,
                    }];
                }

                if (statusV2 === StatusV2.WAITING_SIGN_IN_CONFIRM) {
                    dropdownItems = [{
                        label: '待诊',
                        value: StatusV2.WAITING_DIAGNOSE,
                    }];
                }

                if (statusV2 === StatusV2.WAITING_DIAGNOSE) {
                    const time = isReserved ? (signInTime || estimatedDiagnoseTime) : created;
                    displayValue = `待诊${getTimeDifferenceWithNow(time)}`;
                    displayValueDesc = `已到店等候${getTimeDifferenceWithNow(time, true, true)}`;
                    const now = new Date();
                    const waitTime = waitDiagnoseTime ? new Date(waitDiagnoseTime) : new Date();

                    const timeDifference = now - waitTime;
                    const minutes = Math.floor(timeDifference / (1000 * 60));
                    if (minutes > 15 && minutes <= 30) {
                        style = {
                            'background-color': '#7cbbff',
                            'border-color': '#7cbbff',
                        };
                    }

                    dropdownItems = [{
                        label: '接诊',
                        value: StatusV2.PROCESSING_DIAGNOSE,
                    }, {
                        label: '已诊',
                        value: StatusV2.DIAGNOSED,
                    }];
                }

                if (statusV2 === StatusV2.PROCESSING_DIAGNOSE) {
                    displayValue = `诊中${diagnosingTime ? `${getTimeDifferenceWithNow(diagnosingTime)}` : ''}`;
                    displayValueDesc = `已就诊${diagnosingTime ? `${getTimeDifferenceWithNow(diagnosingTime, true, true)}` : ''}`;
                    dropdownItems = [{
                        label: '已诊',
                        value: StatusV2.DIAGNOSED,
                    }];
                    theme = 'success';
                }
                if ([StatusV2.DIAGNOSED,StatusV2.EXPIRED, StatusV2.REFUNED, StatusV2.CANCELED].includes(statusV2)) {
                    theme = 'default';
                }

                const waitingSignInConfirm = statusV2 === StatusV2.WAITING_SIGN_IN_CONFIRM;

                return {
                    displayValue,
                    dropdownItems,
                    displayValueDesc,
                    waitingSignInConfirm,
                    theme,
                    variant,
                    style,
                };
            },
            getStatusTagV2Style(item) {
                const outlineWarning = {
                    variant: 'outline',
                    theme: 'warning',
                };
                const ghostDefault = {
                    variant: 'ghost',
                    theme: 'default',
                };
                const outlinePrimary = {
                    variant: 'outline',
                    theme: 'primary',
                };
                const outlineDanger = {
                    variant: 'outline',
                    theme: 'danger',
                };
                const statusTextTheme = {
                    [StatusV2.WAITING_SIGN_IN]: outlineWarning, // 待签
                    [StatusV2.WAITING_SIGN_IN_CONFIRM]: outlineWarning, // 待签(已确认)
                    [StatusV2.WAITING_DIAGNOSE]: outlinePrimary, // 待诊
                    [StatusV2.PROCESSING_DIAGNOSE]: outlinePrimary, // 就诊中
                    [StatusV2.CONTINUE_DIAGNOSED]: outlinePrimary, // 回诊
                    [StatusV2.HAD_SIGN_IN]: ghostDefault, // 已签
                    [StatusV2.DIAGNOSED]: ghostDefault, // 已诊
                    [StatusV2.EXPIRED]: outlineDanger, // 退号
                    [StatusV2.REFUNED]: outlineDanger, // 已过期
                    [StatusV2.CANCELED]: outlineDanger, // 已取消
                };
                const statusV2 = item?.registrationFormItem?.statusV2;
                return statusTextTheme[statusV2] || {
                    variant: 'ghost',
                    theme: 'default',
                };
            },
            handleContentClick(e) {
                e.stopPropagation();
            },
            async onDropdownChange(statusV2, item) {
                try {
                    this.loading = true;
                    // 签到
                    if (statusV2 === StatusV2.WAITING_DIAGNOSE) {
                        if (this.showConsultant) {
                            const registrationFormItem = Clone(item.registrationFormItem);
                            let time = `${formatDate(new Date(registrationFormItem.reserveDate), 'YYYY-MM-DD')}  ${registrationFormItem.reserveStart}~${registrationFormItem.reserveEnd}`;
                            if (this.isFixOrderMode) {
                                let orderNo = '';
                                // 签到后确认号源的模式下这步无法确认号源
                                if (registrationFormItem.orderNo) {
                                    orderNo = ` ${registrationFormItem.orderNo}号`;
                                }
                                time = `${formatDate(new Date(registrationFormItem.reserveDate), 'YYYY-MM-DD')}${orderNo} ${registrationFormItem.reserveStart}~${registrationFormItem.reserveEnd}`;
                            }
                            Object.assign(registrationFormItem, {
                                time,
                                consultantId: registrationFormItem?.consultantId || item.consultantId || '',
                                reserveDate: formatDate(new Date(registrationFormItem.reserveDate), 'YYYY-MM-DD'),
                                reserveTime: {
                                    start: registrationFormItem.reserveStart,
                                    end: registrationFormItem.reserveEnd,
                                },
                            });
                            await new AllocationDoctorConsultantDialog({
                                value: true,
                                needSign: true,
                                patientId: item.patient.id,
                                info: registrationFormItem,
                                registrationId: item.registrationId,
                                finishRegistrationFunction: async (...props) => {
                                    this.handleFinishRegistrationInfo(...props);
                                },
                            }).generateDialogAsync({
                                parent: this,
                            });
                            return;
                        }
                        await RegistrationsAPI.registrationSignin(item.registrationId);
                    }
                    // 待签(已确认)
                    if (statusV2 === StatusV2.WAITING_SIGN_IN_CONFIRM) {
                        await RegistrationsAPI.modifyStatusV2(item.registrationId,{
                            targetStatus: statusV2,
                        });
                    }

                    // 接诊
                    if (statusV2 === StatusV2.PROCESSING_DIAGNOSE) {
                        await RegistrationsAPI.setRegistrationStatusToDiagnosing(item.registrationSheetId);
                    }

                    // 已诊
                    if (statusV2 === StatusV2.DIAGNOSED) {
                        const res = await OutpatientAPI.lockOutpatient(item.patientOrderId, LockBusinessKeyEnum.OUTPATIENT);
                        const {
                            result,
                            identity,
                        } = res.data || {};
                        if (result === 1) {
                            await RegistrationsAPI.setRegistrationStatusToDiagnosed(item.registrationSheetId);
                            await OutpatientAPI.unlockOutpatient(item.patientOrderId, {
                                businessKey: LockBusinessKeyEnum.OUTPATIENT,
                                identity,
                            });
                        } else {
                            return this.$Toast({
                                message: '门诊单正在修改中',
                                type: 'error',
                            });
                        }
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.loading = false;
                }
            },
            screenSizeChange() {
                const { innerWidth } = window;
                this.searchInputWidth = innerWidth < 1660 ? 182 : 200;
                this.listSearchWidth = innerWidth < 1660 ? 152 : 260;
            },
        },
    };
</script>

<style lang="scss">
@import "src/views-dentistry/registration/_registration.scss";
@import 'src/styles/theme.scss';
@import 'src/styles/mixin';

.date-navigator-dropdown-wrapper-opacity-1 {
    opacity: 1 !important;
}

.suggestions-wrapper .board-view-suggestions-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 8px 12px;
    border-bottom: 1px solid #e6eaee;

    &.selected * {
        color: #ffffff !important;
    }

    .top-container {
        display: flex;
        align-items: center;
        width: 100%;
        line-height: 20px;

        .name,
        .mobile {
            font-size: 14px;
            font-weight: bold;
        }

        .mobile {
            margin-left: 8px;
        }

        .holder {
            flex: 1;
        }

        .status-v2-text {
            margin-right: 8px;
            color: #005ed9;

            &-orange {
                color: $Y2;
            }

            &-light-grey {
                color: $T2;
            }
        }

        .date {
            font-size: 14px;
            font-weight: 400;
            color: #7a8794;
        }
    }

    .bottom-container {
        width: 100%;
        margin-top: 2px;
        line-height: 20px;

        span {
            margin-right: 8px;
            font-size: 14px;
            font-weight: 400;
            color: #7a8794;
        }
    }
}
</style>
