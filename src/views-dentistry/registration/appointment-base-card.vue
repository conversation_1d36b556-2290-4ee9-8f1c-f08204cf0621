<template>
    <div class="appointment-base-card-wrapper">
        <div v-if="!isEditorStatus" v-abc-loading="loading" class="display-status-wrapper">
            <div class="patient-info-wrapper" :class="{ 'other-color': isNotSignInStatus }">
                <div :class="['title',{ 'title2': isNotSignInStatus }]">
                    患者
                </div>
                <div class="patient-info">
                    <span class="name ellipsis">{{ patient.name || '匿名患者' }}</span>
                    <span class="sex">{{ patient.sex }}</span>
                    <span class="age"> {{ formatAge(patient.age, {
                        monthYear: 150, dayYear: 1
                    }) }}</span>
                    <span class="mobile">{{ patientMobile }}</span>
                </div>
            </div>
            <abc-tips-card-v2
                v-if="socialPayExceptionInfo && socialPayExceptionInfo.payType > 1"
                theme="warning"
                align="center"
                :border-radius="false"
            >
                医保中心退费异常，请稍后重试
                <template #operate>
                    <abc-button
                        variant="text"
                        size="small"
                        :loading="socialExceptionRefundLoading"
                        @click="() => handleClickSocialRefund(fetchDetail)"
                    >
                        重试
                    </abc-button>
                </template>
            </abc-tips-card-v2>
            <abc-tips-card-v2
                v-if="lockedInfo"
                theme="warning"
                align="center"
                :border-radius="false"
            >
                {{ lockedTips }}
                <template v-if="showCancelPay" #operate>
                    <abc-button
                        variant="text"
                        size="small"
                        @click="onClickCancelPay"
                    >
                        支付遇到问题？
                    </abc-button>
                </template>
            </abc-tips-card-v2>
            <div class="registration-content">
                <div v-if="referralSource" class="row-line">
                    <label>转诊</label>
                    <div class="row-content">
                        <span>{{ referralSource.doctorName || '不指定' }}<span v-if="referralSource.departmentName">-{{ referralSource.departmentName }}</span></span>
                        <span style="margin-left: 8px;">{{ `${formatDate(referralSource.referralTime)} 转出` }}</span>
                    </div>
                </div>
                <div class="row-line">
                    <label>医生</label>
                    <div v-if="!loading" class="row-content">
                        <span>{{ registration.doctorName || '不指定' }}<span v-if="registration.departmentName">-{{ registration.departmentName }}</span></span>
                        <span>{{ revisitStatus === 1 ? '初诊' : '复诊' }}</span>
                    </div>
                </div>
                <div v-if="isShowRegistrationCategory" class="row-line">
                    <label>号种</label>
                    <div v-if="!loading" class="row-content">
                        <span>{{ RegistrationCategoryText[registration.registrationCategory] || '' }}</span>
                    </div>
                </div>
                <div v-if="showConsultant" class="row-line">
                    <label>咨询</label>
                    <div v-if="!loading" class="row-content">
                        <span>{{ registration.consultantName || '不指定' }}</span>
                    </div>
                </div>
                <div class="row-line">
                    <label>时间</label>
                    <div class="row-content time-status">
                        <div>
                            <span v-if="!loading">{{ showDisplayTime }} {{ registration.timeOfDay }}</span>
                            <template v-if="!loading">
                                <span v-if="isFixOrderMode && registration.orderNo">{{ `${(`${registration.orderNo }`).padStart(2,'0')}号` }}</span>
                                <span>{{ registration.reserveTime.start }}~{{ registration.reserveTime.end }}</span>
                            </template>
                        </div>
                        <div class="status">
                            <span v-if="registration.statusV2 === StatusV2.WAITING_SIGN_IN" class="waiting-sign-in">待签到</span>
                            <span v-if="registration.statusV2 === StatusV2.WAITING_SIGN_IN_CONFIRM" class="waiting-sign-in">待签(已确认)</span>
                            <span v-if="registration.statusV2 === StatusV2.WAITING_DIAGNOSE" class="waiting-diagnose">待诊</span>
                            <span v-if="registration.statusV2 === StatusV2.DIAGNOSED" class="diagnosed">已诊</span>
                            <span v-if="registration.statusV2 === StatusV2.CONTINUE_DIAGNOSED" class="continue-diagnose">回诊</span>
                            <span v-if="registration.statusV2 === StatusV2.REFUNED" class="diagnosed">已退</span>
                        </div>
                    </div>
                </div>
                <div class="row-line">
                    <label>诊室</label>
                    <div class="row-content">
                        <span>{{ registration.consultingRoomName || '无' }}</span>
                    </div>
                </div>
                <div v-if="!isFixOrderMode && !!showRegistrationProducts" class="row-line">
                    <label>项目</label>
                    <div class="row-content">
                        <span>{{ showRegistrationProducts }}</span>
                    </div>
                </div>
                <div v-if="visitSourceRemark " class="row-line">
                    <label>备注</label>
                    <div class="row-content">
                        <span>{{ visitSourceRemark }}</span>
                    </div>
                </div>
                <div v-if="!!displayRecommendedInformation && displayRecommendedInformation !== '不指定'" class="row-line">
                    <label>推荐</label>
                    <div class="row-content">
                        <span>{{ displayRecommendedInformation }}</span>
                    </div>
                </div>
                <div v-if="!!displayMedicalRecordProfile && !registrationCategoryIsConvenience" class="row-line">
                    <label>预诊</label>
                    <div v-abc-title.ellipsis="displayMedicalRecordProfile" class="row-content ellipsis"></div>
                </div>
                <div v-if="isShowChargeInfo" class="row-line">
                    <label>费用</label>
                    <div v-if="itemPayStatusV2 === PayStatusV2.NOT_PAID || registration.payStatusV2 === PayStatusV2.NOT_PAID" class="row-content pay-status">
                        <span class="price"><abc-money :value="pay.receivable || 0" :is-show-space="true"></abc-money></span>
                        <span class="not-received">未收</span>
                    </div>
                    <div v-else class="row-content pay-status">
                        <template v-if="itemPayStatusV2 === PayStatusV2.PAID || registration.payStatusV2 === PayStatusV2.PAID">
                            <span class="price"><abc-money :value="chargeSheet.chargeSheetSummary.receivedFee || 0" :is-show-space="true"></abc-money></span>
                            <span class="received">已收</span>
                        </template>
                        <div v-else class="part-paid">
                            <template v-if="itemPayStatusV2 === PayStatusV2.PARTED_REFUNDED || registration.payStatusV2 === PayStatusV2.PARTED_REFUNDED || itemPayStatusV2 === PayStatusV2.REFUNED || registration.payStatusV2 === PayStatusV2.REFUNED">
                                <span class="price"><abc-money :value="chargeSheet.chargeSheetSummary.receivedFee || 0" :is-show-space="true"></abc-money></span>
                                <span>已退</span>
                                <span v-if="itemPayStatusV2 === PayStatusV2.PARTED_REFUNDED || registration.payStatusV2 === PayStatusV2.PARTED_REFUNDED">（<abc-money :value="Math.abs(chargeSheet.chargeSheetSummary.refundFee) || 0" :is-show-space="true"></abc-money>）</span>
                            </template>
                            <template v-else>
                                <span class="price"><abc-money :value="chargeSheet.chargeSheetSummary.needPayFee" :is-show-space="true"></abc-money></span>
                                <span class="part-pay">欠收</span>
                            </template>
                        </div>
                        <abc-popover
                            theme="yellow"
                            placement="top"
                            trigger="hover"
                            popper-class="custom-part-paid-popper-style"
                        >
                            <abc-icon
                                slot="reference"
                                icon="info"
                                size="12"
                                color="#dadbe0"
                                class="fee-detail-info"
                            ></abc-icon>
                            <div class="popover-content">
                                <abc-scrollbar padding-size="tiny" style="max-height: 259px;">
                                    <div class="popover-item">
                                        <span class="label">应收</span>
                                        <span><abc-money :value="pay.receivable" :is-show-space="true"></abc-money></span>
                                    </div>
                                    <template v-if="itemPayStatusV2 !== PayStatusV2.NOT_PAID || registration.payStatusV2 !== PayStatusV2.NOT_PAID">
                                        <div class="popover-item">
                                            <span class="label">实收</span>
                                            <span><abc-money :value="chargeSheet.chargeSheetSummary.netIncomeIgnoreOweFee" :is-show-space="true"></abc-money></span>
                                        </div>
                                        <div v-if="chargeSheet.chargeSheetSummary.owedFee" class="popover-item">
                                            <span class="label">欠费</span>
                                            <span><abc-money :value="chargeSheet.chargeSheetSummary.owedFee" :is-show-space="true"></abc-money></span>
                                        </div>
                                    </template>

                                    <div
                                        v-if="(itemPayStatusV2 !== PayStatusV2.NOT_PAID || registration.payStatusV2 !== PayStatusV2.NOT_PAID) &&
                                            chargeSheet.chargeSheetSummary.needPayFee"
                                        class="popover-item"
                                    >
                                        <span class="label">欠收</span>
                                        <span><abc-money :value="chargeSheet.chargeSheetSummary.needPayFee" :is-show-space="true"></abc-money></span>
                                    </div>

                                    <abc-divider
                                        size="normal"
                                        margin="small"
                                        variant="solid"
                                        theme="dark"
                                    ></abc-divider>

                                    <div
                                        v-if="chargeFromRegistration &&
                                            (itemPayStatusV2 > PayStatusV2.NOT_PAID || registration.payStatusV2 > PayStatusV2.NOT_PAID) &&
                                            chargeSheet.chargeActions.length"
                                    >
                                        <div v-for="(item,idx) in chargeSheet.chargeActions" :key="item.id" class="charge-sheet-list">
                                            <div class="charge-sheet-actions">
                                                <!--类型（0：收费；1：退费；2：修改支付方式）-->
                                                <div class="charge-item">
                                                    <div>
                                                        <span>{{ actionType(item.type) }}</span>
                                                        <span class="person">{{ item.createdByName }}</span>
                                                    </div>
                                                    <div class="price">
                                                        {{ actionAmount(item) }}
                                                    </div>
                                                </div>
                                                <div class="charge-item">
                                                    <span>{{ parseTime(item.created,'y-m-d h:i', true) }}</span>
                                                    <span>{{ actionPayMode(item) }}</span>
                                                </div>
                                                <span v-if="item.chargeComment">
                                                    备注：{{ item.chargeComment }}
                                                </span>

                                                <abc-divider
                                                    v-if="idx !== chargeSheet.chargeActions.length - 1"
                                                    size="normal"
                                                    margin="mini"
                                                    variant="dashed"
                                                    theme="dark"
                                                ></abc-divider>
                                            </div>
                                        </div>
                                    </div>
                                </abc-scrollbar>
                                <template v-if="isCanUpdatePayModeInRegistration">
                                    <abc-divider
                                        size="normal"
                                        margin="small"
                                        variant="solid"
                                        theme="dark"
                                        style="margin-top: 0;"
                                    ></abc-divider>
                                    <abc-flex justify="center" style="margin: 8px 0;">
                                        <abc-link
                                            size="small"
                                            style="font-size: 12px;"
                                            @click="showModifyPayMode = true"
                                        >
                                            修改支付方式
                                        </abc-link>
                                    </abc-flex>
                                </template>
                            </div>
                        </abc-popover>
                    </div>
                </div>
                <div class="row-line">
                    <label>创建</label>
                    <div v-if="!loading" class="row-content">
                        <span>{{ displayCreatedInfo }}</span>
                    </div>
                </div>
            </div>
            <div v-if="showFooterBtn && !loading" class="horizontal-split-line"></div>
            <div v-if="showFooterBtn && !loading" class="footer-btn-wrapper">
                <abc-button
                    v-if="isCanModifyRegistrationInfo && showEditBtn && registration.statusV2 < StatusV2.REFUNED"
                    variant="ghost"
                    size="small"
                    :disabled="!!lockedInfo || !!socialPayExceptionInfo"
                    :data-cy="`${PrimaryDataCyKey}-edit-btn`"
                    @click="handleEditRegistration"
                >
                    编辑
                </abc-button>
                <template v-if="!readonly">
                    <!--挂号已收、部分退、全退都展示 发票 -->
                    <abc-button
                        v-if="showOpenInvoiceBtn"
                        variant="ghost"
                        size="small"
                        :disabled="!!socialPayExceptionInfo"
                        :data-cy="`${PrimaryDataCyKey}-open-invoice-btn`"
                        @click="openInvoiceDialog"
                    >
                        开票
                    </abc-button>
                    <abc-button
                        v-if="registration.statusV2 === StatusV2.REFUNED && registration.payStatusV2 === PayStatusV2.PARTED_REFUNDED"
                        variant="ghost"
                        size="small"
                        :data-cy="`${PrimaryDataCyKey}-refund-btn`"
                        @click="handleRefund"
                    >
                        退费
                    </abc-button>
                    <template v-if="registration.statusV2 !== StatusV2.REFUNED">
                        <abc-button
                            v-if="showContinueDiagnosedBtn"
                            variant="ghost"
                            size="small"
                            :loading="continueDiagnosedBtnLoading"
                            :data-cy="`${PrimaryDataCyKey}-continue-diagnosed-btn`"
                            @click="handleContinueDiagnosed"
                        >
                            回诊
                        </abc-button>
                        <abc-check-access
                            v-if="isCanModifyRegistrationInfo && (registration.payStatusV2 === PayStatusV2.NOT_PAID || registration.payStatusV2 === PayStatusV2.PARTED_PAID) &&
                                registration.statusV2 < StatusV2.REFUNED && chargeFromRegistration && showChargeBth && chargeSheet.id"
                        >
                            <abc-button
                                variant="ghost"
                                size="small"
                                :disabled="!!lockedInfo"
                                :data-cy="`${PrimaryDataCyKey}-charge-btn`"
                                @click="charge()"
                            >
                                收费
                            </abc-button>
                        </abc-check-access>
                        <abc-button
                            v-if="isCanModifyRegistrationInfo && showPreDiagnosisBtn && !registrationCategoryIsConvenience"
                            variant="ghost"
                            size="small"
                            :disabled="!!lockedInfo || !!socialPayExceptionInfo"
                            :data-cy="`${PrimaryDataCyKey}-pre-diagnosis-btn`"
                            @click="showMedicalRecord = true"
                        >
                            预诊
                        </abc-button>
                        <abc-check-access v-if="isCanModifyRegistrationInfo && (registration.statusV2 === StatusV2.WAITING_SIGN_IN || registration.statusV2 === StatusV2.WAITING_SIGN_IN_CONFIRM)">
                            <abc-button
                                size="small"
                                :loading="signInBtnLoading"
                                :data-cy="`${PrimaryDataCyKey}-sign-in-btn`"
                                @click="signIn"
                            >
                                签到
                            </abc-button>
                        </abc-check-access>
                        <template
                            v-if="(registration.statusV2 < StatusV2.REFUNED &&
                                registration.payStatusV2 < PayStatusV2.REFUNED) ||
                                (registration.statusV2 === StatusV2.REFUNED &&
                                    registration.payStatusV2 === PayStatusV2.PARTED_REFUNDED)
                            "
                        >
                            <print-popper
                                v-model="printSelect"
                                :loading="printLoading"
                                :height="26"
                                :input-width-styles="{
                                    width: '44px',
                                    'min-width': '44px'
                                }"
                                btn-size="small"
                                style="margin-left: 4px;"
                                :box-style="{
                                    width: '128px', right: 0
                                }"
                                placement="bottom"
                                :options="printOptions"
                                @print="print"
                                @select-print-setting="openPrintConfigSettingDialog"
                            >
                            </print-popper>
                            <abc-dropdown v-if="isCanModifyRegistrationInfo && chargeFromRegistration" ref="dropdownListWrapper" custom-class="dropdown-list-wrapper">
                                <div slot="reference">
                                    <abc-check-access>
                                        <abc-button
                                            icon="three_dot"
                                            variant="ghost"
                                            size="small"
                                            :data-cy="`${PrimaryDataCyKey}-more-btn`"
                                            :width="26"
                                            :min-width="26"
                                            style="margin-left: 4px;"
                                        >
                                        </abc-button>
                                    </abc-check-access>
                                </div>
                                <template>
                                    <abc-tooltip v-if="!PARTED_PAID_0" :content="disabledRefundFeeOrNoReason" :disabled="!disabledRefundFeeOrNoReason">
                                        <abc-dropdown-item
                                            :disabled="isDisabledRefundFeeOrNoBtn"
                                            :label="dropdownItemLabel"
                                            value="refundFeeOrNo"
                                            style="padding: 0;"
                                        >
                                            <div style="padding: 6px 12px;" @click="handleRefund">
                                                {{ dropdownItemLabel }}
                                            </div>
                                        </abc-dropdown-item>
                                    </abc-tooltip>

                                    <abc-dropdown-item
                                        value="newAdd"
                                        style="padding: 0;"
                                    >
                                        <div style="padding: 6px 12px;" @click="handleNewAdd">
                                            新增预约
                                        </div>
                                    </abc-dropdown-item>
                                    <abc-dropdown-item
                                        v-if="revisitExternalLink"
                                        style="padding: 0;"
                                    >
                                        <div style="padding: 6px 12px;" @click="goFellowUpOuterChain">
                                            公卫随访
                                        </div>
                                    </abc-dropdown-item>
                                </template>
                            </abc-dropdown>
                        </template>
                    </template>
                </template>
            </div>
        </div>
        <div v-else class="edit-status-wrapper">
            <div class="content-wrapper">
                <abc-flex justify="space-between" align="center">
                    <registration-type-tabs
                        :value="postData.isReserved"
                        :options="registrationTypeOptions"
                        :width="260"
                        :disabled="disabledForm"
                        @click="changeType"
                    ></registration-type-tabs>
                    <registration-read-card
                        :patient-info="patient"
                        :department-id="postData.departmentId"
                        :disabled="!!(registrationId || oldRegistrationId) || disabledPatient"
                        @change-patient="selectPatient"
                    ></registration-read-card>
                </abc-flex>
                <abc-form
                    ref="form"
                    item-no-margin
                    :class="{
                        'appointment-base-card-form': true,
                        'show-reserve-product': showReserveProduct,
                    }"
                >
                    <div class="patient-info-wrapper">
                        <patient-section
                            v-model="patient"
                            :is-required="true"
                            :allow-show-tags="false"
                            :need-hover-popover="false"
                            :show-read-card="false"
                            :show-add-new="false"
                            source="registration"
                            size="medium"
                            :disabled="!!(registrationId || oldRegistrationId) || disabledPatient"
                            :is-can-see-patient-mobile="isCanSeePatientMobileInRegistration"
                            @change-patient="selectPatient"
                            @patient-info-blur="handlePatientInfoBlur"
                            @patient-sex-blur="handlePatientSexBlur"
                            @enterEvent="enterEvent"
                        ></patient-section>
                    </div>

                    <div class="form-content">
                        <abc-space is-compact compact-block class="item">
                            <abc-form-label
                                :width="72"
                                label="医生"
                                size="medium"
                                required
                                :disabled="disabledForm && disabledEditRevisitStatus"
                            ></abc-form-label>
                            <abc-form-item
                                class="space-form-item"
                                hidden-red-dot
                                :error="errorDoctorInfo"
                                :validate-event="
                                    (_, callback) => callback({
                                        validate: !errorDoctorInfo.error, message: errorDoctorInfo.message
                                    })
                                "
                            >
                                <abc-tooltip
                                    placement="top-start"
                                    :arrow-offset="20"
                                    :open-delay="900"
                                    :disabled="errorDoctorInfo.error || !showLastDoctorInfo"
                                >
                                    <doctor-select-section
                                        :disabled="disabledForm || isThreeDaysRepeat"
                                        :primary-data-cy-key="PrimaryDataCyKey"
                                        :departments="departmentsList"
                                        :post-data="postData"
                                        :width="192"
                                        :revisit-status="revisitStatus"
                                        :registration-id="registrationId"
                                        :default-doctor-info="defaultDoctorInfo"
                                        :registration-type="registrationType"
                                        :registration-product-ids="registrationProductIds"
                                        :order-no-and-time="orderNoAndTime"
                                        :all-doctors="allDoctors"
                                        :is-reserved="postData.isReserved"
                                        @doctor-selected-change="handleDoctorSelectedChange"
                                        @selected-order-confirm="handleSelectedOrderConfirm"
                                        @selected-time-range-confirm="handleSelectedTimeRangeConfirm"
                                        @clear="handleSelectedClear"
                                        @enterEvent="enterEvent"
                                    >
                                    </doctor-select-section>

                                    <abc-flex
                                        v-if="lastDoctorInfo"
                                        slot="content"
                                        justify="center"
                                        align="center"
                                        :gap="12"
                                    >
                                        <abc-text
                                            tag="div"
                                            theme="black"
                                            size="normal"
                                        >
                                            最近：{{ `${ lastDoctorInfo.departmentName || '其他' } · ${ lastDoctorInfo.doctorName || '' }` }}
                                        </abc-text>
                                        <abc-button
                                            tag="div"
                                            variant="text"
                                            theme="primary"
                                            size="small"
                                            @click="handleAgainVisit"
                                        >
                                            再次就诊
                                        </abc-button>
                                    </abc-flex>
                                </abc-tooltip>
                            </abc-form-item>
                            <abc-space is-compact compact-block :border-style="false">
                                <abc-select
                                    v-if="isShowRegistrationCategory"
                                    v-model="postData.registrationCategory"
                                    :width="97"
                                    :max-height="210"
                                    size="medium"
                                    :disabled="disabledForm || disabledEditCharge"
                                    class="category-select"
                                    :data-cy="`${PrimaryDataCyKey}-category`"
                                    :no-icon="true"
                                    @enter="enterEvent"
                                    @change="changeCategory"
                                >
                                    <abc-option
                                        v-for="item in registrationCategoryOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    >
                                    </abc-option>
                                </abc-select>
                                <abc-select
                                    v-model="revisitStatus"
                                    :width="isShowRegistrationCategory ? 98 : 194"
                                    :disabled="disabledEditRevisitStatus || isThreeDaysRepeat"
                                    :data-cy="`${PrimaryDataCyKey}-revisit-status`"
                                    size="medium"
                                    class="revisit-status-select"
                                    @enter="enterEvent"
                                    @change="handleRevisitChange"
                                >
                                    <abc-option label="初诊" :value="1"></abc-option>
                                    <abc-option label="复诊" :value="2"></abc-option>
                                </abc-select>
                            </abc-space>
                        </abc-space>
                        <abc-space is-compact compact-block class="item time-item-wrapper">
                            <abc-form-label
                                :width="72"
                                label="时间"
                                size="medium"
                                required
                                :disabled="disabledForm"
                            ></abc-form-label>
                            <template v-if="isFixOrderMode">
                                <abc-space is-compact compact-block>
                                    <abc-space is-compact compact-block :border-style="false">
                                        <abc-form-item required class="space-form-item">
                                            <abc-date-picker
                                                v-if="postData.isReserved === 1"
                                                v-model="postData.reserveDate"
                                                value-format="YYYY-MM-DD"
                                                :width="141"
                                                class="date-wrapper"
                                                :focus-show-options="false"
                                                :describe-list="describeList"
                                                :disabled="disabledForm"
                                                :clearable="false"
                                                :picker-options="fixModePickerOptions"
                                                :data-cy="`${PrimaryDataCyKey}-date-picker`"
                                                size="medium"
                                                @enter="enterEvent"
                                                @change-date-pivot="getDailyReserveStatus"
                                                @change="handleReserveDate"
                                            ></abc-date-picker>
                                            <abc-select
                                                v-else
                                                v-model="postData.reserveDate"
                                                class="fixed-select-wrapper"
                                                custom-class="fixed-select-options"
                                                :width="61"
                                                inner-width="62"
                                                :disabled="disabledForm"
                                                :data-cy="`${PrimaryDataCyKey}-date-select`"
                                                no-icon
                                                size="medium"
                                                @enter="enterEvent"
                                                @change="changeReserveDate"
                                            >
                                                <abc-option :value="todayTime" label="今天"></abc-option>
                                                <abc-option v-if="!isNewAdd && date < todayTime" :value="date" :label="date"></abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                        <abc-form-item v-if="!postData.isReserved" required class="space-form-item">
                                            <abc-select
                                                v-model="registrationTime"
                                                class="date-select-wrapper"
                                                :width="81"
                                                inner-width="137"
                                                :disabled="disabledForm"
                                                :data-cy="`${PrimaryDataCyKey}-time-of-day`"
                                                size="medium"
                                                @change="changeTimeOfDay"
                                                @enter="enterEvent"
                                            >
                                                <abc-option
                                                    v-for="t in timeOptions"
                                                    :key="t.id"
                                                    :label="t.time"
                                                    :value="t.id"
                                                >
                                                    <abc-text>
                                                        {{ t.time }}
                                                        <abc-text theme="gray-light">
                                                            {{ t.num ? `(共 ${t.num} 号)` : '(未排班)' }}
                                                        </abc-text>
                                                    </abc-text>
                                                </abc-option>
                                            </abc-select>
                                        </abc-form-item>
                                    </abc-space>
                                    <abc-form-item
                                        v-if="!postData.isReserved"
                                        :required="!isStopDiagnose"
                                        class="space-form-item"
                                        :validate-event="!isStopDiagnose ? null : (val,callback) => callback({
                                            validate: false, message: '请更换时间'
                                        })"
                                    >
                                        <abc-select
                                            v-if="!isStopDiagnose"
                                            v-model="orderNoAndTime"
                                            :show-value="displayTime"
                                            :width="245"
                                            :disabled="disabledForm"
                                            :empty-text="isGenerateOrderNoOnSign && doctorNoInfoList.length ? '无可用号源' : '暂无排班'"
                                            show-empty
                                            :class="['order-no-and-time-wrapper',{
                                                'show-description': showDescription,
                                            }]"
                                            custom-class="order-no-select"
                                            :data-cy="`${PrimaryDataCyKey}-order-no-select`"
                                            size="medium"
                                            placeholder="无可用号源"
                                            @enter="enterEvent"
                                        >
                                            <template v-if="showDescription" #description>
                                                <abc-text
                                                    tag="div"
                                                    theme="black"
                                                    bold
                                                    style="position: absolute;
                                                    top: 50%;
                                                    left: -228px;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: space-between;
                                                    max-width: 50px;
                                                    font-size: 14px;
                                                    transform: translateY(-50%);"
                                                >
                                                    {{ isGenerateOrderNoOnSignDisplayNoStr }}
                                                </abc-text>
                                            </template>
                                            <template v-else-if="isShowAdditionalOrderNoMark" #description>
                                                <abc-tag-v2
                                                    size="mini"
                                                    variant="outline"
                                                    theme="success"
                                                    style="position: absolute; top: 50%; right: 6px; transform: translateY(-50%);"
                                                >
                                                    加
                                                </abc-tag-v2>
                                            </template>
                                            <template v-if="orderNoAndTimeOptions.length">
                                                <abc-option
                                                    v-for="item in orderNoAndTimeOptions"
                                                    :key="`${item.orderNo}-${item.start}-${item.end}-${item.type}-${item.timeOfDay}`"
                                                    style="padding-right: 0; padding-left: 12px;"
                                                    :value="`${item.orderNo || ''}-${item.start}-${item.end}-${item.type}-${item.timeOfDay}`"
                                                    @click="clickOrderNoAndTimeOption(item)"
                                                >
                                                    <abc-flex align="center">
                                                        <abc-text
                                                            v-if="!isGenerateOrderNoOnSign"
                                                            tag="div"
                                                            bold
                                                        >
                                                            {{ (`${item.orderNo}`).padStart(2,'0') }}
                                                        </abc-text>
                                                        <abc-text style="margin-left: 31px;">
                                                            {{ item.start }} ~ {{ item.end }}
                                                        </abc-text>
                                                        <abc-text
                                                            v-if="isGenerateOrderNoOnSign && item.residueCount"
                                                            theme="gray-light"
                                                            size="mini"
                                                            style="margin-left: 8px;"
                                                        >
                                                            余{{ item.residueCount }}
                                                        </abc-text>
                                                        <span v-if="!!item.type" style="margin-left: 8px;">
                                                            <abc-icon v-if="item.type === 1" icon="s-reservation-tag-color" size="18"></abc-icon>
                                                            <abc-icon v-else icon="s-vip-color" size="18"></abc-icon>
                                                        </span>
                                                    </abc-flex>
                                                </abc-option>
                                            </template>
                                        </abc-select>
                                        <abc-input
                                            v-else
                                            :width="245"
                                            :data-cy="`${PrimaryDataCyKey}-stop-diagnose`"
                                            readonly
                                            size="medium"
                                        >
                                            <abc-flex slot="cover" align="center" style="height: 100%;">
                                                <abc-text size="normal" theme="warning-light">
                                                    停诊
                                                </abc-text>
                                            </abc-flex>
                                        </abc-input>
                                    </abc-form-item>
                                    <abc-form-item
                                        v-else
                                        :required="!isStopDiagnose"
                                        class="space-form-item"
                                        :validate-event="!isStopDiagnose ? null : (val,callback) => callback({
                                            validate: false, message: '请更换时间'
                                        })"
                                    >
                                        <order-no-select-section
                                            :disabled="disabledForm"
                                            :primary-data-cy-key="PrimaryDataCyKey"
                                            :registration-type="registrationType"
                                            :post-data="postData"
                                            :width="245"
                                            :all-doctors="allDoctors"
                                            :doctor-no-info-list="doctorNoInfoList"
                                            :order-no-and-time="orderNoAndTime"
                                            custom-popper-class="appointment-order-no-select-popper"
                                            @enterEvent="enterEvent"
                                            @confirm-order="handleConfirmOrder"
                                        >
                                        </order-no-select-section>
                                    </abc-form-item>
                                </abc-space>
                            </template>
                            <template v-else>
                                <abc-space is-compact compact-block>
                                    <abc-form-item required class="space-form-item">
                                        <abc-date-picker
                                            v-if="postData.isReserved === 1"
                                            v-model="postData.reserveDate"
                                            value-format="YYYY-MM-DD"
                                            :width="192"
                                            class="flexible-date-wrapper"
                                            :focus-show-options="false"
                                            :describe-list="describeList"
                                            :disabled="disabledForm"
                                            :clearable="false"
                                            :picker-options="pickerOptions"
                                            :data-cy="`${PrimaryDataCyKey}-date-picker`"
                                            size="medium"
                                            @enter="enterEvent"
                                            @change-date-pivot="getDailyReserveStatus"
                                            @change="handleReserveDate"
                                        ></abc-date-picker>
                                        <abc-select
                                            v-else
                                            v-model="postData.reserveDate"
                                            class="flexible-select-wrapper"
                                            custom-class="flexible-select-options"
                                            :width="192"
                                            :disabled="disabledForm"
                                            :data-cy="`${PrimaryDataCyKey}-date-select`"
                                            no-icon
                                            size="medium"
                                            @enter="enterEvent"
                                            @change="changeReserveDate"
                                        >
                                            <abc-option :value="todayTime" label="今天"></abc-option>
                                            <abc-option v-if="!isNewAdd && date < todayTime" :value="date" :label="date"></abc-option>
                                        </abc-select>
                                    </abc-form-item>
                                    <abc-form-item required :validate-event="validateDatePicker" class="space-form-item">
                                        <abc-time-range-picker
                                            v-if="showTimeRangePicker"
                                            v-model="reserveTimeRange"
                                            :picker-options="timeRangePickerOptions"
                                            :width="194"
                                            :focus-show-options="false"
                                            :disabled="disabledForm || isFirstEditorDisabled"
                                            :clearable="false"
                                            class="time-range-picker"
                                            :data-cy="`${PrimaryDataCyKey}-time-range-picker`"
                                            size="medium"
                                            @enter="enterEvent"
                                            @change="handleChangeReserveTimeRange"
                                        >
                                            <template slot="option-describe" slot-scope="{ option }">
                                                <div style="display: inline-flex; align-items: center; margin-right: 4px;">
                                                    <abc-icon icon="s-user-small" :color="'var(--abc-color-P10)'"></abc-icon>
                                                    <span style="margin-left: 2px;">{{ getRegistrationCount(option) }}</span>
                                                </div>
                                            </template>
                                        </abc-time-range-picker>
                                        <abc-select
                                            v-else
                                            :width="193"
                                            :disabled="disabledForm"
                                            no-icon
                                            custom-class="order-no-select"
                                            :data-cy="`${PrimaryDataCyKey}-order-no-select`"
                                            size="medium"
                                            placeholder="暂无排班"
                                            show-empty
                                            empty-text="暂无排班"
                                            @enter="enterEvent"
                                        >
                                        </abc-select>
                                    </abc-form-item>
                                </abc-space>
                            </template>
                        </abc-space>
                        <abc-space
                            v-if="!isFixOrderMode && showReserveProduct"
                            is-compact
                            compact-block
                            class="item"
                        >
                            <abc-form-label
                                :width="72"
                                label="项目"
                                size="medium"
                                :disabled="disabledForm"
                            ></abc-form-label>
                            <abc-form-item>
                                <abc-popover
                                    width="324px"
                                    placement="top-start"
                                    :arrow-offset="20"
                                    trigger="hover"
                                    theme="yellow"
                                    :disabled="!notExistProducts"
                                >
                                    <abc-select
                                        slot="reference"
                                        v-model="registrationProductIds"
                                        :width="385"
                                        :max-height="226"
                                        multiple
                                        custom-class="products-custom-class"
                                        :class="['products-list-select',{ 'not-exist-products': notExistProducts }]"
                                        multi-label-mode="tag"
                                        :max-tag="3"
                                        :tag-max-width="98"
                                        placeholder="预约项目"
                                        :fetch-suggestions="handleSearchProducts"
                                        with-search
                                        show-empty
                                        :disabled="disabledForm"
                                        :data-cy="`${PrimaryDataCyKey}-products`"
                                        size="medium"
                                        @enter="enterEvent"
                                        @change="handleChangeProducts"
                                    >
                                        <abc-option
                                            v-for="d in productOptions"
                                            :key="d.id"
                                            :label="d.displayName"
                                            :value="d.id"
                                        >
                                        </abc-option>
                                    </abc-select>
                                    <div>
                                        {{ curDoctorNotProductsInfo }}
                                    </div>
                                </abc-popover>
                            </abc-form-item>
                        </abc-space>
                        <abc-space is-compact compact-block class="item fee-item">
                            <abc-form-label
                                :width="72"
                                label="费用"
                                size="medium"
                                :disabled="disabledFeeInput && disabledDiscount"
                            ></abc-form-label>
                            <div class="fee-item-value">
                                <abc-form-item required>
                                    <abc-popover
                                        theme="yellow"
                                        placement="top-start"
                                        :arrow-offset="20"
                                        trigger="hover"
                                        :open-delay="300"
                                        :disabled="payFeePopoverDisabled"
                                        popper-class="custom-pay-fee-popover"
                                    >
                                        <abc-input
                                            slot="reference"
                                            v-model="pay.fee"
                                            v-abc-focus-selected
                                            placeholder="费用"
                                            :input-custom-style="{ 'text-align': 'left' }"
                                            :config="{
                                                formatLength: 2, max: 10000000, supportZero: true
                                            }"
                                            :width="140"
                                            type="money"
                                            :disabled="disabledFeeInput"
                                            :data-cy="`${PrimaryDataCyKey}-fee`"
                                            size="medium"
                                            @input="inputFee"
                                            @enter="enterEvent"
                                        >
                                            <abc-currency-symbol-icon slot="prepend" :size="16" :color="'var(--abc-color-T3)'"></abc-currency-symbol-icon>
                                        </abc-input>

                                        <div>
                                            <abc-text
                                                tag="div"
                                                theme="black"
                                                size="normal"
                                                bold
                                            >
                                                {{ payFeePopoverInfo.departmentName }}{{ `${payFeePopoverInfo.doctorName ? ` - ${payFeePopoverInfo.doctorName}` : ''}` }}
                                            </abc-text>
                                            <abc-text tag="div" theme="gray" size="mini">
                                                {{ payFeePopoverInfo.modeTypeDesc }}
                                            </abc-text>
                                            <div class="price-wrapper">
                                                <div v-for="item in payFeePopoverInfo.priceDetail" :key="item.label" class="price-item">
                                                    <abc-flex justify="space-between">
                                                        <abc-text>{{ item.label }}</abc-text>
                                                        <abc-money :value="item.value"></abc-money>
                                                    </abc-flex>
                                                </div>
                                            </div>
                                            <abc-divider variant="dashed" margin="small"></abc-divider>
                                            <abc-text tag="div" theme="gray" size="mini">
                                                {{ `可前往【管理-诊疗项目-${$t('registrationFeeName')}】修改设置` }}
                                            </abc-text>
                                        </div>
                                    </abc-popover>
                                </abc-form-item>
                                <div
                                    v-abc-click-outside="clickOutsideDiscountWrapper"
                                    :class="{
                                        'discount-wrapper': true,
                                        'disabled': disabledDiscount,
                                        'active': showDiscountTable,
                                    }"
                                >
                                    <abc-popover
                                        v-model="showDiscountTable"
                                        width="385px"
                                        placement="bottom-end"
                                        trigger="manual"
                                        theme="white"
                                        :offset="1"
                                        :visible-arrow="false"
                                        :disabled="disabledDiscount"
                                        class="discount-popover-wrapper"
                                        popper-class="discount-popover"
                                        :popper-style="{
                                            padding: 0,
                                        }"
                                    >
                                        <div slot="reference" class="discount-info-wrapper" @click="toggleDiscount">
                                            <div class="discount-money">
                                                <abc-text v-if="hasAvailablePreferential && !discountFee" theme="gray-light">
                                                    有可用优惠
                                                </abc-text>
                                                <abc-text v-else-if="discountFee" theme="gray-light">
                                                    优惠： <span class="money"> {{ discountFee | formatMoney }}</span>
                                                </abc-text>
                                                <abc-text v-else theme="gray-light">
                                                    无可用优惠
                                                </abc-text>
                                            </div>
                                        </div>
                                        <div class="same-time-charge-card">
                                            <discount-table
                                                v-abc-click-outside="handleClickDiscountTableOutside"
                                                :member-id.sync="pay.memberId"
                                                :use-member-flag="pay.useMemberFlag"
                                                is-simple
                                                :show-header="false"
                                                class="registration-discount"
                                                :charge-status="chargeSheet.status"
                                                :post-data="chargeDialogData.postData"
                                                :is-replay="chargeDialogData.isReplay"
                                                :charge-sheet-id="chargeSheet.id"
                                                :charge-forms="chargeForms"
                                                :promotions.sync="promotions"
                                                :gift-rule-promotions.sync="giftRulePromotions"
                                                :coupon-promotions.sync="couponPromotions"
                                                :patient-points-info.sync="patientPointsInfo"
                                                :patient-card-promotions.sync="patientCardPromotions"
                                                :member-info.sync="memberInfo"
                                                :summary="chargeSheet.chargeSheetSummary"
                                                :readonly="disabledEditCharge"
                                                @change="(flag)=>{
                                                    changeDiscount(flag)
                                                }"
                                            ></discount-table>
                                        </div>
                                    </abc-popover>
                                </div>
                            </div>
                        </abc-space>

                        <div class="additional-info">
                            <abc-space
                                v-if="showConsultant"
                                is-compact
                                compact-block
                                class="item"
                            >
                                <abc-form-label
                                    :width="72"
                                    label="咨询"
                                    size="medium"
                                    :disabled="disabledForm"
                                ></abc-form-label>
                                <abc-form-item>
                                    <abc-select
                                        v-model="postData.consultantId"
                                        :disabled="disabledForm"
                                        :width="385"
                                        :max-height="166"
                                        :clearable="!disabledForm"
                                        placeholder="咨询师"
                                        class="doctor-list-select"
                                        custom-class="doctor-list"
                                        :data-cy="`${PrimaryDataCyKey}-consultant-doctor-select`"
                                        size="medium"
                                        with-search
                                        :fetch-suggestions="handleConsultantSearch"
                                        @change="changeConsultantName"
                                        @enter="enterEvent"
                                    >
                                        <abc-option
                                            v-for="(o) in consultantOptions"
                                            :key="o.employeeId"
                                            :label="o.employeeName"
                                            :value="o.employeeId"
                                        ></abc-option>
                                    </abc-select>
                                </abc-form-item>
                            </abc-space>
                            <abc-space
                                v-if="isRecommendAndSourceDisplay"
                                is-compact
                                compact-block
                                class="item"
                            >
                                <abc-form-label
                                    :width="72"
                                    label="推荐"
                                    size="medium"
                                    :required="requireConfig.recommend.required"
                                    :disabled="disabledRecommendation"
                                ></abc-form-label>
                                <abc-form-item :required="requireConfig.recommend.required">
                                    <abc-popover
                                        v-model="showRecommendation"
                                        theme="yellow"
                                        placement="top-start"
                                        trigger="manual"
                                        :arrow-offset="20"
                                        :control="false"
                                        :disabled="disabledRecommendation"
                                    >
                                        <div @mouseenter="showRecommend" @mouseleave="hiddenRecommend">
                                            <div v-if="isFirstVisitDoctor">
                                                首次就诊患者，本次推荐将自动填充至首诊来源
                                            </div>
                                            <div v-else class="registration-visit-source-item-popover-info">
                                                <div class="popover-info-line">
                                                    <b>上次就诊医生:</b><span>{{ lastDoctorInfo?.doctorName || '' }}</span>
                                                </div>
                                                <div class="popover-info-line">
                                                    <b>上次就诊推荐:</b><span>{{ recommendedInformation || '不指定' }}</span>
                                                    <span v-if="!needAutoFillReferral && recommendedInformation !== '不指定'" style="color: #007aff; cursor: pointer;" @click="coverNowVisitInfoByLastVisitHistoryVisit">复制到本次</span>
                                                </div>
                                            </div>
                                        </div>

                                        <abc-cascader
                                            slot="reference"
                                            ref="visit-source-cascader"
                                            v-model="visitSourceOption"
                                            :disabled="disabledRecommendation"
                                            :props="{
                                                children: 'children',
                                                label: 'name',
                                                value: 'id'
                                            }"
                                            separation="-"
                                            clearable
                                            :width="385"
                                            :panel-max-height="200"
                                            panel-width="264px"
                                            :options="options"
                                            :data-cy="`${PrimaryDataCyKey}-visit-source`"
                                            class="visit-source-cascader"
                                            size="medium"
                                            placeholder="本次挂号推荐人"
                                            @enter="enterEvent"
                                            @panel-visible="handleCascaderVisible"
                                            @reference-mouse-enter="handleCascaderMouseEnter"
                                            @reference-mouse-leave="handleCascaderMouseLeave"
                                        >
                                            <div class="visit-source-edit-wrapper">
                                                <abc-button
                                                    v-if="isClinicAdmin"
                                                    icon="s-b-settings-line"
                                                    size="small"
                                                    variant="text"
                                                    theme="default"
                                                    @click="handleVisitSourceEdit"
                                                ></abc-button>

                                                <abc-popover
                                                    v-else
                                                    trigger="hover"
                                                    placement="top-start"
                                                    :popper-style="{
                                                        zIndex: 99999
                                                    }"
                                                    theme="yellow"
                                                >
                                                    <abc-button
                                                        slot="reference"
                                                        variant="text"
                                                        theme="default"
                                                        icon="s-b-settings-line"
                                                        size="small"
                                                    ></abc-button>

                                                    <span>修改本次推荐请联系管理员</span>
                                                </abc-popover>
                                            </div>
                                        </abc-cascader>
                                    </abc-popover>
                                </abc-form-item>
                            </abc-space>
                            <abc-space is-compact compact-block class="item">
                                <abc-form-label
                                    :width="72"
                                    label="备注"
                                    size="medium"
                                    :required="requireConfig.remark.required"
                                    :disabled="disabledRecommendation"
                                ></abc-form-label>
                                <abc-form-item :required="requireConfig.remark.required">
                                    <visit-remark
                                        ref="visitRemark"
                                        :show-remark.sync="visitSourceRemark"
                                        class="registration-remark"
                                        :disabled="disabledRecommendation"
                                        type="registrationRemarks"
                                        :is-outpatient="true"
                                        placeholder=""
                                        :max-length="300"
                                        :width="385"
                                        :ul-height="112"
                                        focus-show-options
                                        placement="bottom-start"
                                        :readonly="false"
                                        :data-cy="`${PrimaryDataCyKey}-remark`"
                                        input-size="medium"
                                        @enter="enterEvent"
                                    >
                                    </visit-remark>
                                </abc-form-item>
                            </abc-space>
                            <abc-space
                                v-if="!registrationCategoryIsConvenience"
                                is-compact
                                compact-block
                                class="item"
                            >
                                <abc-form-label
                                    :width="72"
                                    label="预诊"
                                    size="medium"
                                    :required="requireConfig.preDiagnosis.required"
                                    :disabled="disabledMR || readonly"
                                ></abc-form-label>
                                <abc-form-item style="display: block;" :required="requireConfig.preDiagnosis.required">
                                    <abc-edit-div
                                        v-model="medicalRecordProfile"
                                        spellcheck="false"
                                        style="width: 385px; cursor: pointer;"
                                        class="mr-info ellipsis"
                                        :content-editable="false"
                                        :disabled="disabledMR || readonly"
                                        :data-cy="`${PrimaryDataCyKey}-pre-diagnose`"
                                        size="medium"
                                        @click="showMedicalRecord = true"
                                        @enter="enterEvent"
                                    ></abc-edit-div>
                                </abc-form-item>
                            </abc-space>
                        </div>
                    </div>
                </abc-form>
            </div>
            <div v-if="isFieldLayout" class="form-cover"></div>
            <abc-divider margin="none"></abc-divider>
            <appointment-card-footer
                ref="appointment-card-footer"
                :business-type="businessType"
                :old-registration="prePostData"
                :registration-info="registrationInfo"
                :charge-dialog-data="chargeDialogData"
                :show-print="!(registrationSheetId || registrationId) && supportShortFlow"
            >
                <template v-if="isSupportShortFlow">
                    <abc-button
                        title="F6"
                        :disabled="notChangeData && !!registration.id || isStopDiagnose"
                        :loading="!isFieldLayout && buttonLoading"
                        :data-cy="`${PrimaryDataCyKey}-charge-btn`"
                        size="large"
                        :min-width="104"
                        @click="handleConfirm(true)"
                    >
                        {{ chargeBtnName }}
                    </abc-button>
                    <abc-button
                        title="F4"
                        :disabled="notChangeData && !!registration.id"
                        variant="ghost"
                        :loading="!isFieldLayout && noChargeButtonLoading"
                        :data-cy="`${PrimaryDataCyKey}-not-charge-btn`"
                        size="large"
                        :min-width="104"
                        @click="handleConfirm(false)"
                    >
                        暂不收费
                    </abc-button>
                </template>
                <abc-button
                    v-else
                    title="F4"
                    :min-width="120"
                    :disabled="notChangeData && !!registration.id"
                    :loading="!isFieldLayout && (buttonLoading || noChargeButtonLoading || dragBtnLoading)"
                    :data-cy="`${PrimaryDataCyKey}-confirm-btn`"
                    size="large"
                    @click="handleConfirm(false)"
                >
                    确定
                </abc-button>
                <abc-button
                    title="ESC"
                    variant="ghost"
                    :min-width="isSupportShortFlow ? 104 : 120"
                    :data-cy="`${PrimaryDataCyKey}-cancel-btn`"
                    size="large"
                    @click="handleCancel"
                >
                    取消
                </abc-button>
            </appointment-card-footer>
        </div>

        <visit-source-dialog
            v-if="isShowVisitSourceDialog"
            :is-show.sync="isShowVisitSourceDialog"
            :patient-source-type="patientSourceType"
            @close="isShowVisitSourceDialog = false"
        ></visit-source-dialog>

        <!--选择预诊信息-->
        <medical-record-dialog
            v-if="showMedicalRecord"
            v-model="showMedicalRecord"
            class="medical-record-dialog"
            :patient="patientInfo"
            :disabled="disabledMR || readonly"
            :medical-record="medicalRecord"
            :change-medical-record="changeMedicalRecord"
            :post-data="postDataForPreDiagnosis"
        >
        </medical-record-dialog>

        <refund-dialog
            v-if="showRefund"
            :id="chargeSheet.id"
            ref="refundPro"
            v-model="showRefund"
            :append-to-body="true"
            :member-id="chargeSheet.memberId"
            :forms="chargeSheet.chargeForms"
            :discount-fee="chargeSheet.chargeSheetSummary.discountFee"
            :net-income-fee="chargeSheet.chargeSheetSummary.netIncomeFee"
            :owed-refund-fee="chargeSheet.chargeSheetSummary.owedRefundFee"
            :adjustment-fee="chargeSheet.chargeSheetSummary.adjustmentFee"
            :net-adjustment-fee="chargeSheet.chargeSheetSummary.netAdjustmentFee"
            :refund-auto-destroy="writeInvoiceConfig.refundAutoDestroy"
            @confirm="refundConfirm"
        >
            <span slot="tips">&nbsp;</span>
        </refund-dialog>

        <refund-way-dialog
            v-if="showRefundWayList"
            ref="refundWay"
            v-model="showRefundWayList"
            :patient-name="patient.name"
            :charge-sheet-id="chargeSheet.id"
            :refund-fee="refundTotalFee"
            :refund-data="refundData"
            :refund-type="curRefundType"
            :receivable-fee="chargeSheet.chargeSheetSummary.receivableFee"
            :net-income-fee="chargeSheet.chargeSheetSummary.netIncomeFee"
            :payment-summary-infos.sync="chargeSheet.chargeSheetSummary.paymentSummaryInfos"
            :charge-transactions="chargeSheet.chargeTransactions"
            :charge-config="chargeConfig"
            @input="onChangeValue"
            @finish="refundFinish"
            @auto-destroy-invoice="autoDestroyInvoice"
        ></refund-way-dialog>

        <modify-pay-mode-dialog
            v-if="showModifyPayMode"
            v-model="showModifyPayMode"
            :charge-sheet="chargeSheet"
            @refresh="fetchDetail(true)"
        ></modify-pay-mode-dialog>

        <three-days-repeat-modal
            v-if="showThreeDaysRepeatModal"
            v-model="showThreeDaysRepeatModal"
            :table-data="showThreeDaysRepeatData"
            @confirm="handleThreeDaysRepeatConfirm"
            @close="closeThreeDaysRepeatModal"
        >
        </three-days-repeat-modal>
    </div>
</template>

<script>
    import RegistrationsAPI from 'api/registrations/index';
    import {
        debounce, isEqual, pick1,
    } from 'utils/lodash';
    import {
        BusinessType, PayStatusV2, StatusV2, PrimaryDataCyKey,
    } from 'views/registration/common/constants';
    import {
        ChargeStatusEnum, PayModeEnum, PayModeList, RefundTypeEnum, UseMemberFlagEnum,
    } from '@/service/charge/constants';
    import { RevisitStatus } from 'assets/configure/constants';
    import {
        mapGetters, mapState, mapActions,
    } from 'vuex';
    import MixinModulePermission from 'views/permission/module-permission';
    import { RecommendService } from '@/service/recommend';

    const VisitSourceDialog = () => import('views/registration/visit-source-dialog');
    import CrmAPI from 'api/crm';
    import localStorage from 'utils/localStorage-handler';
    import SettingAPI from 'api/settings';
    import OutpatientAPI from 'api/outpatient';

    const MedicalRecordDialog = () => import('views/registration/medical-record-dialog/medical-record-dialog');
    import {
        formatDentistry2Text,
        formatEpidemiologicalHistory2Str,
        MedicalRecordTypeEnum,
    } from 'views/outpatient/common/medical-record/utils';

    const DiscountTable = () => import('@/views/cashier/discount/table.vue');
    import ReservationAPI from 'api/registrations/reservation';
    import Clone from 'utils/clone';
    import PatientsAPI from 'api/patients';
    import ChargeDialog from 'views/cashier/charge-dialog';
    import ChargeAPI from 'api/charge';
    import AbcChargeDialog from '@/service/charge/components/dialog-charge';
    import InvoiceDialog from 'views/cashier/invoice';
    import { InvoiceBusinessScene } from 'views/cashier/invoice/constants.js';
    import AbcPrinter from '@/printer';

    const PrintPopper = () => import('views/print/popper');
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import {
        formatMoney, parseTime,
    } from '@/utils';

    const RefundWayDialog = () => import('views/cashier/refund-way-dialog');
    const RefundDialog = () => import('views/cashier/refund-dialog');
    const ThreeDaysRepeatModal = () => import('src/components/three-days-repeat/index.vue');
    import {
        formatDate,
    } from '@abc/utils-date';
    import inputSelect from 'views/common/input-select';
    import { ANONYMOUS_DOCTOR_ID } from '@/assets/configure/constants';
    import VisitRemark from 'views/registration/visit-source-remark/index.vue';
    import BoardApi from 'api/registrations/board';
    import { handleMemberInfo } from '@/utils/handle-member-info';
    import { formatAge } from 'utils';
    import {
        validateAge, validateMobile,
    } from 'utils/validate';
    import AbcCurrencySymbolIcon from 'views/common/components/currency-symbol-icon/index.vue';
    import { defaultCountryCode } from '@/utils/country-codes.js';
    import CallingAPI from 'api/call';
    import {
        GENERATE_ORDER_NO_TIME_TYPE,
        RESERVATION_MODE_TYPE,
        RESERVATION_TIME_TYPE,
        SERVICE_TYPE_ENUM,
        GENERATE_ORDER_NO_TIME_ENUM,
        RESERVATION_EMPLOYEE,
    } from 'views/settings/registered-reservation/constant';
    import AllocationDoctorConsultantDialog from 'views/layout/allocation-doctor-consultant/index.js';
    import { encryptMobile } from 'utils/crm';
    import InvoiceService from 'views/cashier/invoice/write-invoice-core-v2/invoice-service';
    import { autoDestroyInvoice } from 'views/cashier/invoice/utils';
    import { ABCPrintConfigKeyMap } from '@/printer/constants';
    import RegistrationPrint from 'views/registration/registration-print';
    import {
        RegistrationCategory, RegistrationCategoryText, RegistrationCategoryText2,
    } from '@/views-hospital/registered-fee/constant';
    import AppointmentCardFooter from 'src/views-dentistry/registration/appointment-card-footer.vue';
    import {
        navigateToInvoiceConfig, windowOpen,
    } from '@/core/navigate-helper';
    import { REGISTERED_FEE_MODE_TYPE } from 'views/settings/diagnosis-treatment/registered-fee/components/constants';
    import useShowRegistrationFeePopoverInfo from '@/views-dentistry/registration/useShowRegistrationFeePopoverInfo';
    // hooks
    import useChargeLock from 'views/cashier/hooks/useChargeLock';
    import RegistrationTypeTabs from '@/views-dentistry/registration/components/registration-type-tabs.vue';
    import PatientSection from 'views/layout/patient/patient-section/index.vue';
    import DoctorSelectSection from '@/views-dentistry/registration/components/doctor-select-section/index.vue';
    import OrderNoSelectSection from '@/views-dentistry/registration/components/order-no-select-section/index.vue';
    import RegistrationMixin from '@/views-dentistry/registration/registration-mixin.js';
    import RegistrationReadCard from '@/views-dentistry/registration/components/registration-read-card/index.vue';
    import { ReferralFlagEnum } from '@/common/constants/registration';
    import Logger from 'utils/logger';
    import { DEFAULT_CERT_TYPE } from 'views/crm/constants';
    import PropertyAPI from 'api/property';

    export default {
        name: 'AppointmentBaseCard',
        components: {
            ModifyPayModeDialog: () => import('@/views-dentistry/registration/components/modify-pay-mode-dialog/index.vue'),
            VisitSourceDialog,
            MedicalRecordDialog,
            DiscountTable,
            PrintPopper,
            RefundWayDialog,
            RefundDialog,
            VisitRemark,
            AbcCurrencySymbolIcon,
            AppointmentCardFooter,
            RegistrationTypeTabs,
            PatientSection,
            DoctorSelectSection,
            OrderNoSelectSection,
            RegistrationReadCard,
            ThreeDaysRepeatModal,
        },
        mixins: [
            MixinModulePermission,
            inputSelect,
            RegistrationPrint,
            RegistrationMixin,
        ],
        provide() {
            return {
                main: this,
            };
        },
        props: {
            finishRegistrationFunction: {
                type: Function,
                default() {
                    return () => {};
                },
            },
            refresh: {
                type: Function,
                default() {
                    return () => {};
                },
            },
            editorStatus: {
                type: Boolean,
                default: false,
            },
            confirmFinishRegistration: {
                type: Function,
                default() {
                    return () => {};
                },
            },
            refreshTable: {
                type: Function,
                default() {
                    return () => {};
                },
            },
            handleAppointmentCardInfo: {
                type: Function,
                default() {
                    return () => {};
                },
            },
            showEditBtn: {
                type: Boolean,
                default: true,
            },
            value: {
                type: Boolean,
                required: true,
            },
            patientInfo: {
                type: Object,
                default: () => {},
            },
            patientTags: {
                type: Array,
                default: () => [],
            },
            pastHistory: {
                type: String,
                default: '',
            },
            allergicHistory: {
                type: String,
                default: '',
            },
            registrationId: {
                type: String,
                default: '',
            },
            // 特殊挂号单id 包含待定预约的挂号单 用做特殊处理
            registrationSheetId: {
                type: String,
                default: '',
            },
            oldRegistrationId: {
                type: String,
                default: '',
            },
            // 医生默认是未指定
            doctorId: {
                type: String,
                default: null,
            },
            doctorName: {
                type: String,
                default: '',
            },
            departmentId: {
                type: String,
                default: '',
            },
            departmentName: {
                type: String,
                default: '',
            },
            timeOfDay: {
                type: String,
                default: '',
            },
            orderNoTimeOfDay: {
                type: String,
                default: '',
            },
            orderNo: {
                type: String,
                default: '',
            },
            orderNoType: {
                type: [Number, String],
                default: '',
            },
            date: {
                type: String,
                default: '',
            },
            timeRange: {
                type: Array,
            },
            itemStatusV2: Number,
            hasDepartments: {
                type: Boolean,
                default: true,
            },
            departments: {
                type: Array,
                default: () => [],
            },
            isShowScanRegisterDetail: { // 是否展示登记
                type: Boolean,
                required: false,
            },
            patientScanCodeInfoId: { // 流行病登记面板
                type: [Number, String],
                default: 0,
            },
            scanQrCodePatientList: { // 流调登记
                type: Array,
                default: () => {
                    return [];
                },
            },
            // 只有是拖动修改的时候才传
            itemPayStatusV2: {
                type: Number,
                default: -1,
            },
            itemFee: {
                type: [Number, String],
                default: '',
            },
            // 项目ID
            itemRegistrationProductIds: {
                type: Array,
                default: () => {
                    return [];
                },
            },
            itemVisitSourceRemark: {
                type: String,
                default: '',
            },
            itemVisitSourceId: {
                type: String,
                default: '',
            },
            itemVisitSourceFrom: {
                type: String,
                default: '',
            },
            itemVisitSourceFromName: {
                type: String,
                default: '',
            },
            appointmentModel: {
                type: String,
                default: 'views',
            },
            disabledPatient: {
                type: Boolean,
                default: false,
            },
            isReserved: Number,
            isModified: Boolean,
            formSource: String,
            readonly: {
                type: Boolean,
                required: false,
            },
            businessType: { // 0 门诊  1 治疗理疗
                type: Number,
                default: BusinessType.REGISTRATION,
            },
            onLoaded: {
                type: Function,
                default() {
                },
            },
            newAdd: {
                type: Function,
                default() {
                },
            },
            registrationCategory: {
                type: Number,
                default: RegistrationCategory.ORDINARY,
            },
            referralFlag: {
                type: Number,
                default: 0,
            },
            isCanModifyRegistrationInfo: {
                type: Boolean,
                default: true,
            },
            requireConfigList: {
                type: Array,
                default: () => [],
            },
        },

        setup() {
            const {
                getRegistrationFeeInfo,
            } = useShowRegistrationFeePopoverInfo();

            const {
                lockedInfo,
                lockedTips,
                showCancelPay,
                queryExceptionType,
                socialPayExceptionInfo,
                socialExceptionRefundLoading,
                onConfirmCancelPay,
                getLockInfo,
                setQueryExceptionType,
                getSocialExceptionInfo,
                handleClickSocialRefund,
            } = useChargeLock();
            return {
                getRegistrationFeeInfo,

                lockedInfo,
                lockedTips,
                showCancelPay,
                queryExceptionType,
                socialPayExceptionInfo,
                socialExceptionRefundLoading,
                onConfirmCancelPay,
                getLockInfo,
                setQueryExceptionType,
                getSocialExceptionInfo,
                handleClickSocialRefund,
            };
        },

        data() {
            return {
                formatDate,
                PayStatusV2,
                StatusV2,
                BusinessType,
                PrimaryDataCyKey,
                ChargeStatusEnum,
                RegistrationCategory,
                RegistrationCategoryText,
                RegistrationCategoryText2,
                REGISTERED_FEE_MODE_TYPE,
                showRegSetting: false,
                showAppointment: false,
                showMedicalRecord: false,
                showChargeDialog: false,
                showRefund: false,
                showRefundWayList: false,
                isShowVisitSourceDialog: false,
                showTodayNoNumber: false,
                showTodayNoNumberText: '',
                loading: true,
                signInBtnLoading: false,
                continueDiagnosedBtnLoading: false,
                buttonLoading: false,
                noChargeButtonLoading: false,
                refundButtonLoading: false,
                printLoading: false,
                isFirstPrint: true,
                printBillLoading: false,
                isFirstBillPrint: true,
                isStopDiagnose: false,
                type: 0,
                registrationFee: null,
                medicalRecord: {
                    type: MedicalRecordTypeEnum.WESTERN,
                    chiefComplaint: '',
                    presentHistory: '',
                    pastHistory: '',
                    allergicHistory: '',
                    familyHistory: '',
                    personalHistory: '',
                    physicalExamination: '',
                    epidemiologicalHistory: '',
                    // 口腔检查
                    dentistryExaminations: [],
                    // 预诊上传附件
                    preDiagnosisAttachments: [],
                },

                patient: {
                    id: '',
                    name: '',
                    sex: '男',
                    birthday: null,
                    age: {
                        year: '',
                        month: '',
                        day: '',
                    },
                    appFlag: 0,
                    arrearsFlag: 0,
                    mobile: '',
                    countryCode: defaultCountryCode,
                    isMember: null,
                    wxBindStatus: 0,
                    addressCityId: null,
                    addressCityName: null,
                    addressDetail: null,
                    addressDistrictId: null,
                    addressDistrictName: null,
                    addressProvinceId: null,
                    addressProvinceName: null,
                    idCard: null,
                    idCardType: null,
                    company: null,
                    source: {
                        id: '',
                        name: '',
                        sourceFrom: '',
                        sourceFromName: '',
                    },
                    sn: '',
                    profession: '',
                    shebaoCardInfo: null,
                    remark: null,
                    ...this.patientInfo,
                    tags: [],
                    originCardInfo: null,
                },
                visitSourceRemark: '',
                cloneVisitSourceRemark: '',
                visitSourceOption: [],
                cloneVisitSourceOption: [],

                // 初复诊
                revisitStatus: RevisitStatus.FIRST, // 初复诊状态 null 无记录；1 初诊； 2 复诊
                // 最近就诊时间
                lastDiagnosedTime: null,

                patientId: '',

                // -1 表示 mrCount 还没有加载完成
                mrCount: -1,
                lastDoctorInfo: null,

                registration: {
                    departmentId: '',
                    departmentName: '',
                    doctorId: '',
                    timeOfDay: this.timeOfDay,
                    doctorName: '',
                    reserveDate: this.date,
                    reserveOrderNo: '',
                    reserveTime: {
                        start: '',
                        end: '',
                    },
                    isAdditional: 0,
                    signIn: 0, // 0不需要签到，1需要签到，2已经签到
                    isReserved: 0, // 预约=1、挂号=0
                    payStatusV2: PayStatusV2.NOT_PAID,
                    statusV2: StatusV2.UNKNOWN,
                    type: 0, // type: 3，微诊所预约/挂号。2，门诊快速接诊挂号。1，微信预约/挂号（老接口）。0，pc预约/挂号
                    orderNo: '',
                    orderNoType: '',
                    registrationProducts: [],
                    sourceId: '',
                    sourceFrom: null,
                    range: 0,
                    consultantId: '', // 咨询师ID
                    consultantName: '', // 咨询师名称
                    consultingRoomName: '', // 诊室
                    registrationCategory: RegistrationCategory.ORDINARY, // 0 普通门诊  1 专家门诊  2 便民门诊
                },
                displayCreatedByName: '', // 创建人
                displayCreatedTime: '', // 创建时间
                registrationProductIds: [],
                curDoctorNotExistList: [], // 拖动到当前医生不支持的项目列表
                cacheRegistrationProductIds: [],
                postData: null,
                prePostData: null,
                cachePostData: null,

                pay: {
                    memberId: null,
                    fee: 0,
                    receivable: 0,
                    useMemberFlag: UseMemberFlagEnum.USE_DEFAULT,
                },
                sourceUnitPrice: '',
                expectedUnitPrice: null,
                discountFee: 0,
                promotions: [],
                giftRulePromotions: [], // 满减返
                couponPromotions: [], // 优惠券
                patientPointsInfo: null, // 积分
                patientCardPromotions: [], // 卡项
                memberInfo: {
                    patient: {
                        id: '',
                        name: '',
                        mobile: '',
                    },
                    memberType: {
                        name: '',
                    },
                },

                departmentObj: Object.create(null),
                departmentsList: [],

                patientOrderId: '',

                refundOriginalPayMode: 0,
                originPayMode: null,
                originPaySubMode: null,

                // 患者有编辑操作被更改
                hasPatientEdited: false,

                calculateOrderNo: null,

                // 提供给收费数据使用
                chargeDialogData: {
                    postData: {},
                    chargeSheetSummary: {},
                    printOpt: {},
                },

                chargeSheet: {
                    chargeActions: [],
                    chargeForms: [],
                    chargeSheetSummary: {},
                    chargeTransactions: [],
                    id: '',
                    memberId: '',
                    memberInfo: {},
                    patientOrderId: '',
                    promotions: [], // 折扣活动
                    couponPromotions: [], // 优惠券
                    giftRulePromotions: [], // 满减返
                    patientCardPromotions: [], // 卡项
                    patientPointsInfo: null,
                    type: 1,
                    status: ChargeStatusEnum.UN_CHARGE,
                    invoiceStatus: 0,
                    // 收费异常类型
                    queryExceptionType: 0,
                },

                curRefundType: '', // 当前退费类型
                refundTotalFee: 0, // 需要退的费用
                refundData: {}, // 项目退费时，{chargeForms, refundFee, needRefundFee}
                workingDateNow: '',
                shebaoCardInfo: null,

                specifyRegistVisible: false,
                uuid: 1,
                appointmentPeriod: null,
                consultantSearchKey: '',
                doctorSearchKey: '',
                productSearchKey: '',
                // 不能操作，不能操作的原因
                isDisabledOperate: 0,
                disabledOperateReason: '',

                options: [],
                visibleInsertPatient: false,

                doctorEnableCategories: [],
                doctorRegistrationFees: [],
                visitHistoryInfo: {
                    visitSourceFrom: null,
                    visitSourceId: null,
                    visitSourceRemark: '',
                    visitSourceText: null,
                    visitSourceFromName: null,
                },

                showDiscountTable: false,
                isEditor: false,
                therapyDoctors: [],
                allDoctor: [],
                fixModePickerOptions: {
                    disabledDate(date) {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return date < today;
                    },
                },
                pickerOptions: {
                    disabledDate(date) {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        return date < today;
                    },
                    shortcuts: [{
                        text: '今天',
                        onClick(cb) {
                            const start = new Date();
                            cb(start);
                        },
                    }],
                },
                describeList: [],
                patientSourceType: [],
                reservationProductList: [],

                finishRegistration: {
                    before: {},
                    after: {},
                    isShowAddIcon: 0,
                },
                orderNoPlaceholderStr: '',
                orderNoAndTimeStr: '',
                orderNoAndTimeOptions: [],
                selectedOrderNoTimeOfDay: '',
                cacheSelectedOrderNoTimeOfDay: '',
                doctorNoInfoList: [],
                registrationTime: '',
                reserveTimeRange: ['', ''],
                cacheReserveTimeRange: ['', ''],
                registrationCount: [],
                cascaderPanelVisible: false, // 本次推荐面板是否展示
                showRecommendation: false, // 是否展示复制提示文案
                showRecommendedInformation: false,
                referralSource: null, // 门诊转诊信息
                isGenerateOrderNoOnSignDisplayNoStr: '',
                doctorConsultantObject: {},
                printSelect: [],
                isChangeDoctorInfo: false,
                registrationIdByCreate: '',
                registrationInfo: {},
                showModifyPayMode: false,

                defaultDoctorInfo: null,
                isDesignatedTimeRange: false, // 是否已指定时间范围
                errorDoctorInfo: {
                    error: false,
                    message: '请选择医生',
                },
                dragBtnLoading: false,

                showThreeDaysRepeatModal: false,
                showThreeDaysRepeatData: [],
                // 存储三日复诊所需的参数
                threeDaysRepeatParams: null,

                preDiagnosisExtendInfo: {
                    visitSourceFrom: null,
                    visitSourceId: null,
                    visitSourceFromName: null,
                    visitRemark: null,
                    extendFlag: 0,
                },

                isAutoFillReferrer: 0, // 推荐来源是否自动填入
            };
        },
        computed: {
            ...mapGetters([
                'clinicBasic',
                'currentClinic',
                'clinicConfig',
                'printRegistrationConfig',
                'userConfig',
                'chargeConfig', // 收费设置
                'printBillConfig',
                'printMedicalListConfig',
                'chainBasic',
                'isCanSeePatientMobileInCrm',
                'defaultRegistrationFees',
                'defaultEnableCategories',
                'isEnableIdCardReader',
                'registrationsConfig',
                'therapyReservationConfig',
                'isOpenMp',
                'userInfo',
                'callConfig',
                'isCanSeePatientMobileInRegistration',
                'isCanUpdatePayModeInRegistration',
                'clinicRegistrationFieldConfig',
                'isEnableRegUpgrade',
            ]),
            ...mapState('crm', [
                'originLabels', // 全部标签
            ]),
            ...mapGetters('crm', ['crmAccessVisibility']),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            ...mapGetters('consult', ['consultantList']),
            ...mapGetters('invoice', ['isOpenInvoice', 'isOpenMedicalInvoice', 'medicalElectronicAPIConfig', 'writeInvoiceConfig', 'invoiceConfigList']),

            postDataForPreDiagnosis() {
                if (!this.postData) {
                    return {};
                }

                const {
                    registrationSheetId,
                    departmentName,
                    registrationProducts,
                    doctorName,
                    reserveTime,
                    reserveDate,
                    doctorId,
                } = this.postData;

                const preDiagnosisExtendInfo = this.preDiagnosisExtendInfo || {};

                return {
                    outpatientFrom: 1, // 预约
                    registrationInfo: registrationSheetId ? {
                        registrationSheetId,
                        reserveStart: reserveTime?.start,
                        reserveEnd: reserveTime?.end,
                        registrationProducts,
                        departmentName,
                        ...preDiagnosisExtendInfo,
                        reserveDate,
                    } : null,
                    referralFlag: this.referralFlag,
                    referralSource: this.referralSource,
                    doctorName,
                    medicalRecord: this.medicalRecord,
                    patient: this.patient,
                    doctorId,
                };
            },

            patientMobile() {
                const { mobile = '' } = this.patient;
                return this.isCanSeePatientMobileInRegistration ? mobile : encryptMobile(mobile);
            },
            isRecommendAndSourceDisplay() {
                return this.clinicBasic.isRecommendAndSourceDisplay;
            },
            timeOptions() {
                const timeOptions = ['上午', '下午', '晚上'];
                const timeOfDayLen = timeOptions.map((t) => this.doctorNoInfoList?.find((item) => item.timeOfDay === t)?.list?.length);
                return [
                    {
                        id: 1,
                        date: '今天',
                        time: '上午',
                        num: timeOfDayLen[0] || 0,
                        value: {
                            start: '00:00',
                            end: '12:00',
                        },
                    },
                    {
                        id: 2,
                        date: '今天',
                        time: '下午',
                        num: timeOfDayLen[1] || 0,
                        value: {
                            start: '12:00',
                            end: '18:00',
                        },
                    },
                    {
                        id: 3,
                        date: '今天',
                        time: '晚上',
                        num: timeOfDayLen[2] || 0,
                        value: {
                            start: '18:00',
                            end: '24:00',
                        },
                    },
                ];
            },

            showCategoryRevisitStatusValue() {
                if (!this.isShowRegistrationCategory) {
                    return this.revisitStatus === RevisitStatus.FIRST ? '初诊' : '复诊';
                }

                return `${RegistrationCategoryText2[this.postData.registrationCategory]}     ${this.revisitStatus === RevisitStatus.FIRST ? '初诊' : '复诊'}`;
            },
            categoryRevisitStatusValue() {
                if (!this.isShowRegistrationCategory) {
                    return [{
                        label: this.revisitStatus === RevisitStatus.FIRST ? '初诊' : '复诊',
                        value: this.revisitStatus,
                    }];
                }

                if (this.chainBasic.isEnableEditRevisitStatus) {
                    return [{
                        label: RegistrationCategoryText2[this.postData.registrationCategory],
                        value: this.postData.registrationCategory,
                    }, {
                        label: this.revisitStatus === RevisitStatus.FIRST ? '初诊' : '复诊',
                        value: this.revisitStatus,
                    }];
                }

                return [{
                    label: RegistrationCategoryText2[this.postData.registrationCategory],
                    value: this.postData.registrationCategory,
                }];

            },
            categoryRevisitStatusOptions() {
                const options = [{
                    label: '初诊',
                    value: RevisitStatus.FIRST, // 1
                }, {
                    label: '复诊',
                    value: RevisitStatus.REVISIT, // 2
                }];

                if (this.isShowRegistrationCategory) {
                    const arr = [{
                        label: '普通号',
                        value: RegistrationCategory.ORDINARY, // 0
                        options,
                    }];

                    if (this.doctorEnableCategories?.includes(RegistrationCategory.SPECIALIST)) {
                        arr.push({
                            label: '专家号',
                            value: RegistrationCategory.SPECIALIST, // 1
                            options,
                        });
                    }

                    if (this.postData.isReserved === 0 && this.doctorEnableCategories?.includes(RegistrationCategory.CONVENIENCE)) {
                        arr.push({
                            label: '便民号',
                            value: RegistrationCategory.CONVENIENCE, // 2
                            options,
                        });
                    }

                    return arr;
                }

                return options;
            },

            categoryRevisitPanelMaxHeight() {
                if (this.isShowRegistrationCategory) {
                    return 128;
                }

                return 88;
            },
            showConsultant() {
                const { showConsultant } = this.viewDistributeConfig.Registration;
                return showConsultant;
            },
            supportShortFlow() {
                const { supportShortFlow } = this.viewDistributeConfig.Registration;
                return supportShortFlow;
            },
            needValidateName() {
                const {
                    id,
                } = this.patient;
                // 选择的患者不做校验
                if (id) return false;
                return true;
            },
            needValidateMobile() {
                const { requiredMobile } = this.chainBasic?.crm || {};
                return !!requiredMobile;
            },
            needValidateAge() {
                const {
                    id,
                    age,
                } = this.patient;
                // 选择的患者不做校验
                if (id) return false;
                const {
                    year,
                    month,
                } = age || {};

                return !(month || year);
            },
            showReserveProduct() {
                return !this.isFixOrderMode && !!this.registrationsConfig.showReserveProduct;
            },
            isSelectedRegistrationProducts() {
                return !!this.registrationProductIds.length;
            },
            hasAvailablePreferential() {
                return !!(this.promotions.length || this.giftRulePromotions.length ||
                    this.couponPromotions.filter((item) => item.isCanBeUsed).length ||
                    this.patientCardPromotions.length || this.patientPointsInfo);
            },
            disabledFeeInput() {
                return this.disabledForm || (!this.disabledForm && (this.disabledEditCharge || this.readonly)) || this.isDisableRegisteredCharge;
            },
            // 已收费 ｜ 已退费
            disabledDiscount() {
                return this.disabledForm || (!this.disabledForm && (this.disabledEditCharge || this.readonly));
            },
            registrationType() {
                return BusinessType.REGISTRATION;
            },
            isNewAdd() {
                return this.formSource === 'card-dropdown' || this.formSource === 'registration-board-dialog-card-dropdown';
            },
            isQuickReservation() {
                return ['patientFileBtn', 'outpatientMoreBtn', 'scan-register', 'card-dropdown', 'registration-board-dialog-card-dropdown'].includes(this.formSource);
            },
            // 门诊预约看板
            isFromRegistrationBoardDialog() {
                return ['registration-board-dialog', 'registration-board-dialog-card-dropdown'].includes(this.formSource);
            },
            isWaitAppointment() {
                return this.formSource === 'wechatCommunication';
            },
            isShowChargeInfo() {
                return this.chargeSheet?.id && this.chargeFromRegistration;
            },
            isFieldLayout() {
                return this.formSource === 'field-layout';
            },
            notChangeData() {
                let flag = true;
                if (!this.isFixOrderMode) {
                    flag = (this.registrationProductIds?.toString() === (this.cacheRegistrationProductIds ?? []).toString()) && this.reserveTimeRange[0] === this.cacheReserveTimeRange[0] && this.reserveTimeRange[1] === this.cacheReserveTimeRange[1];
                }
                return flag && this.isNotChange;
            },
            addNewOrderIsNotChange() {
                const {
                    id: oldPatientId = '',
                    name: oldPatientIName,
                    sex: oldPatientSex,
                    age: oldPatientAge,
                } = this.$options.data().patient;
                const oldPatientInfo = {
                    id: oldPatientId,
                    name: oldPatientIName,
                    sex: oldPatientSex,
                    age: oldPatientAge,
                };
                const {
                    id,
                    name,
                    sex,
                    age,
                } = this.patient;
                const patientInfo = {
                    id: id ?? '',
                    name,
                    sex,
                    age: {
                        year: age.year ?? '',
                        month: age.month ?? '',
                    },
                };
                if (this.isQuickReservation) {
                    return this.notChangeData;
                }
                return this.notChangeData && isEqual(oldPatientInfo, patientInfo);
            },
            isCardModified() {
                if (this.isQuickReservation) {
                    return !this.addNewOrderIsNotChange;
                }
                return (!this.notChangeData && this.isEditor) || (!this.addNewOrderIsNotChange && !this.registrationId);
            },
            isNotChange() {
                // 1. 检查 pay.fee 是否一致
                const payNoEdit = +this._cacheFee === +this.pay.fee;
                // 2. 检查 memberInfo 是否一致，会员被清空后，会置为空对象，而不是 null，需要额外比较
                const memberNoEdit =
                    isEqual(this._memberInfoCache, this.memberInfo) ||
                    (this._memberInfoCache === null && this.memberInfo && this.memberInfo.patient.id === '');
                // 3. 检查 promotions 是否一致，通过检查数组中每项的 id + checked 进行简单判断
                let promotionsNoEdit = true;
                if (Array.isArray(this._promotionsCache) && Array.isArray(this.promotions)) {
                    if (this._promotionsCache.length !== this.promotions.length) {
                        promotionsNoEdit = false;
                    } else {
                        promotionsNoEdit = this._promotionsCache.every((cacheItem) => {
                            return this.promotions.some(
                                (item) => cacheItem.id === item.id && cacheItem.checked === item.checked,
                            );
                        });
                    }
                }
                // 4. 检查 giftRulePromotionsNoEdit 是否一致，通过检查数组中每项的 id + checked 进行简单判断
                let giftRulePromotionsNoEdit = true;
                if (Array.isArray(this._giftRulePromotionsCache) && Array.isArray(this.giftRulePromotions)) {
                    if (this._giftRulePromotionsCache.length !== this.giftRulePromotions.length) {
                        giftRulePromotionsNoEdit = false;
                    } else {
                        giftRulePromotionsNoEdit = this._giftRulePromotionsCache.every((cacheItem) => {
                            return this.giftRulePromotions.some(
                                (item) => cacheItem.id === item.id && cacheItem.checked === item.checked,
                            );
                        });
                    }
                }
                // 5. 检查 couponPromotions 是否一致，通过检查数组中每项的 id + checked 进行简单判断
                let couponPromotionsNoEdit = true;
                if (Array.isArray(this._couponPromotions) && Array.isArray(this.couponPromotions)) {
                    if (this._couponPromotions.length !== this.couponPromotions.length) {
                        couponPromotionsNoEdit = false;
                    } else {
                        couponPromotionsNoEdit = this._couponPromotions.every((cacheItem) => {
                            return this.couponPromotions.some(
                                (item) =>
                                    cacheItem.id === item.id &&
                                    cacheItem.checked === item.checked &&
                                    +cacheItem.currentCount === +item.currentCount,
                            );
                        });
                    }
                }

                // 6. 检查积分是否一致
                let pointsNoEdit = true;
                if (this.patientPointsInfo && this._patientPointsInfo) {
                    pointsNoEdit =
                        this.patientPointsInfo.checked === this._patientPointsInfo.checked &&
                        this.patientPointsInfo.checkedDeductionPrice === this._patientPointsInfo.checkedDeductionPrice;
                }

                // 7. 检查初复诊是否一致
                const revisitNoEdit = this._revisitStatus === this.revisitStatus;
                // 8. 检查卡项是否一致
                let patientCardPromotionsNoEdit = true;
                if (Array.isArray(this._patientCardPromotionsCache) && Array.isArray(this.patientCardPromotions)) {
                    if (this._patientCardPromotionsCache.length !== this.patientCardPromotions.length) {
                        patientCardPromotionsNoEdit = false;
                    } else {
                        patientCardPromotionsNoEdit = this._patientCardPromotionsCache.every((cacheItem) => {
                            return this.patientCardPromotions.some(
                                (item) => cacheItem.id === item.id && cacheItem.checked === item.checked,
                            );
                        });
                    }
                }
                // 9. 检查本次推荐人是否一致
                const visitSourceOptionEdit = isEqual(this.cloneVisitSourceOption, this.visitSourceOption);
                // 10. 检查就诊备注是否一致
                const visitSourceRemarkEdit = isEqual(this.cloneVisitSourceRemark, this.visitSourceRemark);
                // 11. 挂号信息是否一致
                const isRegistrationEdit = isEqual(this.cachePostData, this.postData) && this.cacheSelectedOrderNoTimeOfDay === this.selectedOrderNoTimeOfDay;

                return (payNoEdit &&
                    memberNoEdit &&
                    promotionsNoEdit &&
                    giftRulePromotionsNoEdit &&
                    patientCardPromotionsNoEdit &&
                    couponPromotionsNoEdit &&
                    pointsNoEdit &&
                    revisitNoEdit &&
                    visitSourceOptionEdit &&
                    visitSourceRemarkEdit &&
                    isRegistrationEdit
                );
            },
            isNotSignInStatus() {
                // 非待签到状态
                return (this.itemStatusV2 || this.registration.statusV2) > StatusV2.WAITING_SIGN_IN && (this.itemStatusV2 || this.registration.statusV2) !== StatusV2.WAITING_SIGN_IN_CONFIRM;
            },
            dropdownItemLabel() {
                return this.isShowRefundFeeBtn ?
                    '退费' :
                    this.isFixOrderMode ? '退号' : '取消预约';
            },
            isShowRefundFeeBtn() {
                return (this.registration.payStatusV2 === PayStatusV2.PAID ||
                    this.registration.payStatusV2 === PayStatusV2.PARTED_PAID ||
                    this.registration.payStatusV2 === PayStatusV2.PARTED_REFUNDED) &&
                    this.chargeSheet.chargeSheetSummary.receivedFee > 0;
            },
            // 部分收费且收0元
            PARTED_PAID_0() {
                return this.registration.payStatusV2 === PayStatusV2.PARTED_PAID && this.chargeSheet.chargeSheetSummary.netIncomeFee === 0;
            },
            isDisabledRefundNoBtn() {
                return !this.isShowRefundFeeBtn && [StatusV2.DIAGNOSED, StatusV2.CONTINUE_DIAGNOSED].includes(this.registration.statusV2);
            },
            isDisabledRefundFeeOrNoBtn() {
                return this.isDisabledRefundNoBtn ||
                    !!this.isDisabledOperate ||
                    !!this.lockedInfo ||
                    !!this.socialPayExceptionInfo;
            },
            disabledRefundFeeOrNoReason() {
                if (this.isDisabledRefundNoBtn) return '医生已完成接诊，不能退号';
                return this.disabledOperateReason;
            },
            doctors() {
                if (this.postData.departmentId) {
                    return this.departmentObj[this.postData.departmentId] || [];
                }
                return [];
            },
            displayTime() {
                if (this.orderNoAndTimeStr) {
                    const arr = this.orderNoAndTime.split('-') || [];
                    if (this.isGenerateOrderNoOnSign) {
                        if (arr[0]) {
                            return `${(`${arr[0]}` + '').padStart(2, '0')}        ${arr[1]} ~ ${arr[2]}`;
                        }
                        return `${arr[1]} ~ ${arr[2]}`;
                    }
                    return `${(`${arr[0]}` + '').padStart(2, '0')}        ${arr[1]} ~ ${arr[2]}`;
                }
                return '';
            },
            orderNoAndTime: {
                get() {
                    return this.orderNoAndTimeStr;
                },
                set(val) {
                    if (!val) {
                        this.orderNoAndTimeStr = '';
                        return;
                    }
                    this.orderNoAndTimeStr = val;
                    const arr = val.split('-') || [];
                    if (!this.postData?.reserveTime) {
                        this.postData.reserveTime = {
                            start: '',
                            end: '',
                        };
                    }
                    this.postData.reserveTime = {
                        start: arr[1] || '',
                        end: arr[2] || '',
                    };
                    this.postData.orderNo = +arr[0] || '';
                    this.postData.orderNoType = +arr[3] || 0;
                    this.selectedOrderNoTimeOfDay = arr[4] || '';
                },
            },
            todayTime() {
                return formatDate(new Date(), 'YYYY-MM-DD');
            },
            isToday() {
                return this.date === this.todayTime;
            },
            showDisplayTime() {
                const time = new Date(this.registration.reserveDate);
                const month = time.getMonth() + 1;
                const day = time.getDate();
                const week = time.getDay();
                const weekArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

                return `${month}月${day}日` + ` ${weekArr[week]}`;
            },
            showRegistrationProducts() {
                const arr = this.registration?.registrationProducts?.map((item) => item.displayName);
                return arr.join('、');
            },
            notExistProducts() {
                return this.curDoctorNotExistList?.length;
            },
            curDoctorNotProductsInfo() {
                let str = '';
                this.curDoctorNotExistList?.forEach((item) => {
                    str += `「${item.displayName}」`;
                });
                return `患者预约的${str}因医生不可预约已被取消`;
            },
            displayRecommendedInformation() {
                if (!this.isRecommendAndSourceDisplay) {
                    return '';
                }
                if (this.visitSourceOption.length === 0) {
                    return '不指定';
                }
                if (this.visitSourceOption.length === 1) {
                    return `${this.visitSourceOption[0].label}`;
                }
                if (this.visitSourceOption.length === 2) {
                    return `${this.visitSourceOption[0].label}-${this.visitSourceOption[1].label}`;
                }
                return `${this.visitSourceOption[0].label}-${this.visitSourceOption[1].label}-${this.visitSourceOption[2].label}`;

            },
            // 创建人信息
            displayCreatedInfo() {
                return `${this.displayCreatedByName || ''}（${formatDate(this.displayCreatedTime, 'YYYY-MM-DD')}）`;
            },
            // 本次推荐信息
            recommendedInformation() {
                const visitSourceOption = RecommendService.getInstance().initCascaderValue({
                    visitSourceId: this.visitHistoryInfo?.visitSourceId,
                    visitSourceName: null,
                    visitSourceFrom: this.visitHistoryInfo?.visitSourceFrom || null,
                    visitSourceFromName: this.visitHistoryInfo?.visitSourceFromName || null,
                });
                if (visitSourceOption.length === 0) {
                    return '不指定';
                }
                if (visitSourceOption.length === 1) {
                    return `${visitSourceOption[0].label}`;
                }
                if (visitSourceOption.length === 2) {
                    return `${visitSourceOption[0].label}-${visitSourceOption[1].label}`;
                }
                return `${visitSourceOption[0].label}-${visitSourceOption[1].label}-${visitSourceOption[2].label}`;

            },
            // 展示回诊按钮- 1. 门诊设置开启了回诊患者免挂号 2.已诊 3. 今天
            showContinueDiagnosedBtn() {
                const {
                    statusV2,
                    reserveDate,
                } = this.registration || {};
                const {
                    settings = {
                        isOpenContinueDiagnoseWithoutReg: 0,
                    },
                } = this.clinicBasic.outpatient;
                return settings.isOpenContinueDiagnoseWithoutReg && statusV2 === StatusV2.DIAGNOSED && reserveDate === this.todayTime;
            },
            showPreDiagnosisBtn() {
                return this.registration.statusV2 < StatusV2.DIAGNOSED;
            },
            // 待诊及以前这个预诊状态可以修改
            isWatingDiagnoseScanCodeModify() {
                return this.registration.statusV2 <= this.StatusV2.WAITING_DIAGNOSE;
            },
            isEditorStatus() {
                return !this.registrationId || this.isEditor;
            },
            isFixOrderMode() {
                // modeType 0: 固定号源模式 1: 灵活时间模式
                return this.registrationsConfig.modeType === RESERVATION_MODE_TYPE.FIXED_NUMBER;
            },
            // 预约时可不指定医生
            notMustReserveEmployee() {
                return this.registrationsConfig.notMustReserveEmployee === RESERVATION_EMPLOYEE.NOT_MUST;
            },
            // 是否是精确时间模式
            isAccurateTime() {
                return this.isFixOrderMode && this.registrationsConfig?.serviceType === SERVICE_TYPE_ENUM.ACCURATE_TIME;
            },

            // 号源约满后是否展示加号标记
            isShowAdditionalOrderNoMark() {
                const {
                    generateOrderNoTime,
                    noneScheduleDisableRegistration,
                    remindAdditionalOrderNo,
                } = this.registrationsConfig || {};
                return !!(this.isFixOrderMode && generateOrderNoTime === GENERATE_ORDER_NO_TIME_ENUM.REGISTRATION && noneScheduleDisableRegistration && remindAdditionalOrderNo) && this.postData.isAdditional;
            },

            // 固定模式 - 分段&开启签到取号
            isGenerateOrderNoOnSign() {
                const {
                    fixedOrderDisplayServiceType,
                    generateOrderNoTime,
                } = this.registrationsConfig || {};
                return this.isFixOrderMode && fixedOrderDisplayServiceType === RESERVATION_TIME_TYPE.OTHER && generateOrderNoTime === GENERATE_ORDER_NO_TIME_TYPE.SIGN_IN;
            },
            // 创建时挂号是否展示号数
            showDescription() {
                return !this.postData.orderNo && this.isGenerateOrderNoOnSign && !this.postData.isReserved && this.orderNoAndTimeOptions?.length;
            },

            disabledMR() {
                // 已退已诊不能修改患者历史
                return this.registration.statusV2 >= StatusV2.DIAGNOSED;
            },
            showAppointmentCard: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
            displayMedicalRecordProfile() {
                return this.medicalRecordProfile.replace(/<br\s*\/?>/g, '');
            },
            // 预诊信息
            medicalRecordProfile() {
                const existsItem = [];
                if (this.medicalRecord.chiefComplaint) {
                    existsItem.push(this.medicalRecord.chiefComplaint);
                }
                if (this.medicalRecord.presentHistory) {
                    existsItem.push(this.medicalRecord.presentHistory);
                }
                if (this.medicalRecord.pastHistory) {
                    existsItem.push(this.medicalRecord.pastHistory);
                }
                if (this.medicalRecord.allergicHistory) {
                    existsItem.push(this.medicalRecord.allergicHistory);
                }
                if (this.medicalRecord.epidemiologicalHistory) {
                    existsItem.push(formatEpidemiologicalHistory2Str(this.medicalRecord.epidemiologicalHistory));
                }
                if (this.medicalRecord.physicalExamination) {
                    existsItem.push(this.medicalRecord.physicalExamination);
                }
                if (this.medicalRecord.dentistryExaminations) {
                    existsItem.push(formatDentistry2Text(this.medicalRecord.dentistryExaminations));
                }
                if (this.medicalRecord.preDiagnosisAttachments) {
                    existsItem.push(formatDentistry2Text(this.medicalRecord.preDiagnosisAttachments));
                }
                return existsItem.join('').replace(/<br\s*\/?>/g, '');
            },
            allDoctors() {
                return this.departments.reduce((acc, item) => {
                    if (item.doctors) {
                        item.doctors.forEach((doctor) => {
                            doctor.departmentId = item.departmentId;
                            doctor.departmentName = item.departmentName || '其他';
                            acc.push(doctor);
                        });
                    }
                    return acc;
                }, []);
            },
            consultantOptions() {
                if (!this.consultantSearchKey) {
                    return this.consultantList;
                }
                return this.consultantList.filter((item) => {
                    return (
                        (item.employeeName && item.employeeName.indexOf(this.consultantSearchKey) > -1) ||
                        (item.employeeNamePy && item.employeeNamePy.toLocaleLowerCase().indexOf(this.consultantSearchKey) > -1) ||
                        (item.employeeNamePyFirst && item.employeeNamePyFirst.toLocaleLowerCase().indexOf(this.consultantSearchKey) > -1)
                    );
                });
            },
            productOptions() {
                if (this.disabledForm) {
                    return this.registration.registrationProducts;
                }
                const disabledReservationProductList = this.getDisabledRegistrationProduct(this.registration.registrationProducts);
                if (!this.productSearchKey) {
                    return this.reservationProductList.concat(disabledReservationProductList);
                }
                return this.reservationProductList.concat(disabledReservationProductList).filter((item) => item.name.includes(this.productSearchKey));
            },
            /**
             * @desc 能否修改收费相关信息（禁用：退号 || 已收费 || 已退费）
             * <AUTHOR>
             * @date 2019/08/30 00:18:34
             */
            disabledEditCharge() {
                return (
                    this.registration.payStatusV2 > PayStatusV2.NOT_PAID ||
                    this.registration.statusV2 >= StatusV2.REFUNED ||
                    this.chargeSheet.status > ChargeStatusEnum.UN_CHARGE ||
                    this.itemPayStatusV2 > PayStatusV2.NOT_PAID
                );
            },
            /**
             * @desc 构建算费结构
             * <AUTHOR>
             * @date 2019/09/02 19:47:42
             */
            chargeForms() {
                if (this.chargeSheet.id) {
                    // 存在 chargeSheet，直接使用 chargeForms
                    const forms = Clone(this.chargeSheet.chargeForms);

                    forms.forEach((form) => {
                        form.chargeFormItems.forEach((item) => {
                            item.unitPrice = this.pay.fee;
                            item.sourceUnitPrice = this.sourceUnitPrice;
                            item.expectedUnitPrice = this.expectedUnitPrice;
                        });
                    });
                    return forms;
                }
                return [
                    {
                        // 不存在 chargeSheet，构造一个
                        sourceFormType: 1,
                        chargeFormItems: [
                            {
                                checked: true,
                                unit: '次',
                                name: this.$t('registrationFeeName'),
                                unitCount: 1,
                                doseCount: 1,
                                unitPrice: this.pay.fee,
                                sourceUnitPrice: this.sourceUnitPrice,
                                expectedUnitPrice: this.expectedUnitPrice,
                                doctorInfo: {
                                    departmentId: this.postData.departmentId || '',
                                    doctorId: this.postData.doctorId || '',
                                    registrationCategory: this.postData.registrationCategory,
                                },
                            },
                        ],
                    },
                ];

            },
            disabledForm() {
                return this.registration.statusV2 > StatusV2.WAITING_DIAGNOSE || this.readonly;
            },
            // 是否第一次看病
            isFirstVisitDoctor() {
                return this.mrCount <= 0;
            },
            // 是否禁用本次推荐修改
            disabledRecommendation() {
                // 有挂号单并且不可以修改本次推荐或者配置的只读都需要禁用本次推荐修改
                return this.readonly || (!!this.registrationId && !this.canModifyRecommendation);
            },
            // 允许修改本次推荐的状态 true 可以 false 不可以
            // 【待签到，待付款未就诊，待就诊，未知】【理疗-已签到】四种状态下允许修改本次推荐/备注 【完成诊断后的状态都不能修改】
            canModifyRecommendation() {
                return [StatusV2.WAITING_SIGN_IN,
                        StatusV2.WAITING_SIGN_IN_CONFIRM,
                        StatusV2.WAITING_DIAGNOSE,
                        StatusV2.WAITING_PAY,
                        StatusV2.HAD_SIGN_IN,
                        StatusV2.UNKNOWN].includes(this.registration.statusV2);
            },
            disabledEditRevisitStatus() {
                // 连锁禁用了编辑
                if (!this.chainBasic.isEnableEditRevisitStatus) {
                    return true;
                }

                return this.registration.statusV2 >= StatusV2.REFUNED || this.readonly;
            },
            chargeFromRegistration() {
                return this.chargeSheet.type === 1;
            },
            showChargeBth() {
                return this.formSource !== 'registration-board-dialog';
            },
            /**
             * 是否禁止挂号费议价
             */
            isDisableRegisteredCharge() {
                return !this.userInfo?.isAdmin && this.chargeConfig.reservationRegisteredBargainSwitch === 0;
            },
            // 是否展示开票按钮
            showOpenInvoiceBtn() {
                return this.chargeSheet.type === 1 &&
                    (this.registration.payStatusV2 === PayStatusV2.PAID ||
                        this.registration.payStatusV2 === PayStatusV2.PARTED_REFUNDED ||
                        this.registration.payStatusV2 === PayStatusV2.REFUNED);
            },
            // 是否展示详情操作按钮
            showFooterBtn() {
                if (!this.readonly && this.showOpenInvoiceBtn) {
                    return true;
                }
                if (this.registration.statusV2 === StatusV2.REFUNED) {
                    return this.registration.payStatusV2 === PayStatusV2.PARTED_REFUNDED;
                }
                return this.showEditBtn || !this.readonly;
            },

            /**
             * @description: 打印选项
             * @author: ff
             * @date: 2024/2/1
             * @params
             * @return
             */
            printOptions() {
                return [
                    {
                        label: '挂号小票',
                        value: '挂号小票',
                    },
                    {
                        label: '医疗收费清单',
                        value: '医疗收费清单',
                        disabled: this.chargeSheet.type !== 1 || this.registration.payStatusV2 !== PayStatusV2.PAID,
                    },
                    {
                        label: '患者标签',
                        value: '患者标签',
                    },
                ];
            },
            // 未排班是否可以挂号
            unscheduledCanRegistration() {
                return !this.registrationsConfig.noneScheduleDisableRegistration;
            },

            isShowRegistrationCategory() {
                return this.viewDistributeConfig.Settings.schedule.isShowRegistrationCategory;
            },
            registrationCategoryOptions() {
                const arr = [{
                    label: '普通门诊',
                    value: RegistrationCategory.ORDINARY,
                }];

                if (this.doctorEnableCategories?.includes(RegistrationCategory.SPECIALIST)) {
                    arr.push({
                        label: '专家门诊',
                        value: RegistrationCategory.SPECIALIST,
                    });
                }

                if (this.postData.isReserved === 0 && this.doctorEnableCategories?.includes(RegistrationCategory.CONVENIENCE)) {
                    arr.push({
                        label: '便民门诊',
                        value: RegistrationCategory.CONVENIENCE,
                    });
                }

                return arr;
            },
            registrationCategoryIsConvenience() {
                return this.postData.registrationCategory === RegistrationCategory.CONVENIENCE;
            },
            // 转诊
            isReferral() {
                return this.referralFlag === 1 || this.referralSource;
            },
            // 挂号&&三日复诊
            isThreeDaysRepeat() {
                return !this.postData.isReserved && this.referralFlag === ReferralFlagEnum.REVISIT_IN_THREE_DAYS;
            },
            // 从修改进入，且未切换过医生排班的情况下，需要 disabled 时间控件 (切换走了再切换回来不需要 disabled)
            isFirstEditorDisabled() {
                if (!this.unscheduledCanRegistration && this.registrationId) {
                    if (this.isChangeDoctorInfo) return false;
                    const curSelectedDoctor = this.doctors.find((item) => item.doctorId === this.postData.doctorId);
                    if (!curSelectedDoctor) return false;
                    if (curSelectedDoctor.isScheduled === 0) {
                        return this.postData.departmentId === this.cachePostData.departmentId && this.postData.doctorId === this.cachePostData.doctorId;
                    }
                    return false;
                }
                return false;
            },
            showTimeRangePicker() {
                if (!this.unscheduledCanRegistration) {
                    if (this.registrationId && this.isFirstEditorDisabled) return true;

                    let flag = false;
                    const curSelectedDoctor = this.doctors.find((item) => item.doctorId === this.postData.doctorId);
                    flag = curSelectedDoctor ? curSelectedDoctor.isScheduled !== 0 : true;
                    return flag && curSelectedDoctor?.scheduledRegistrationCategories?.includes(this.postData.registrationCategory);
                }
                return true;
            },
            chargeBtnName() {
                if (this.postData.isReserved === 0) {
                    return '挂号并收费';
                }
                return '预约并收费';
            },
            revisitExternalLink() {
                return this.clinicBasic.revisitExternalLink;
            },
            isSupportMultipleMode() {
                return this.viewDistributeConfig.Settings.project.registrationFee.isSupportMultipleMode;
            },
            payFeePopoverDisabled() {
                if (!this.isSupportMultipleMode) return true;

                const {
                    NOT_PAID,
                    PARTED_PAID,
                } = PayStatusV2;
                return ![NOT_PAID, PARTED_PAID].includes(this.registration.payStatusV2);
            },
            payFeePopoverInfo() {
                const department = this.departments.find((item) => {
                    return item.departmentId === this.postData.departmentId;
                });
                const doctor = this.doctors.find((item) => {
                    return item.doctorId === this.postData.doctorId;
                });
                const doctorFee = this.doctorRegistrationFees.find((item) => item.registrationCategory === this.postData.registrationCategory);
                const {
                    modeTypeDesc,
                    priceDetail,
                    regUnitPrice,
                    revisitedRegUnitPrice,
                } = this.getRegistrationFeeInfo(doctorFee);

                return {
                    departmentName: department?.departmentName || '其他',
                    doctorName: doctor?.doctorName || '',
                    modeTypeDesc,
                    priceDetail,
                    regUnitPrice,
                    revisitedRegUnitPrice,
                };
            },

            registrationTypeOptions() {
                return [{
                    label: '挂号',
                    value: 0,
                    icon: 's-b-registered-line',
                    dataCy: `${PrimaryDataCyKey}-type-registration`,
                }, {
                    label: '预约',
                    value: 1,
                    icon: 's-b-reserve-line',
                    dataCy: `${PrimaryDataCyKey}-type-appointment`,
                }];
            },
            showLastDoctorInfo() {
                if (this.registrationId || !this.patient.id || !this.lastDoctorInfo) {
                    return false;
                }

                const {
                    clinicId: lastDoctorClinicId,
                    doctorId,
                } = this.lastDoctorInfo || {};

                if (this.isFromRegistrationBoardDialog && this.postData.doctorId !== doctorId) {
                    return false;
                }

                return this.currentClinic?.clinicId === lastDoctorClinicId;
            },
            requireConfig() {
                const model = {
                    type: {
                        required: true,
                    },
                    department: {
                        required: true,
                    },
                    doctor: {
                        required: true,
                    },
                    firstFollowUpVisit: {
                        required: true,
                    },
                    time: {
                        required: true,
                    },
                    recommend: {
                        required: false,
                    },
                    remark: {
                        required: false,
                    },
                    preDiagnosis: {
                        required: false,
                    },
                };
                if (this.requireConfigList?.length && this.isFieldLayout) {
                    for (const key in model) {
                        if (model.hasOwnProperty(key)) {
                            model[key].required = !!this.requireConfigList?.find((item) => {
                                return item.key === key;
                            })?.required || false; // 修改 required 的值
                        }
                    }
                    return model;
                }
                for (const key in model) {
                    if (model.hasOwnProperty(key)) {
                        model[key].required = !!this.clinicRegistrationFieldConfig?.[key]?.required || false; // 修改 required 的值
                    }
                }
                return model;
            },
            isSupportShortFlow() {
                return this.supportShortFlow && !(this.registrationSheetId || this.registrationId) && +this.pay.fee > 0;
            },
            // 是否需要自动填入推荐人
            needAutoFillReferral() {
                return this.isAutoFillReferrer && this.revisitStatus === 2;
            },
        },
        watch: {
            patientInfo: {
                handler(val) {
                    this.patient = {
                        ...this.patient, ...val,
                    };
                    // 有咨询功能 是未挂号状态
                    if (this.showConsultant && !this.registrationId && this.postData) {
                        this.postData.consultantId = this.patient.consultantId;
                    }
                },
                immediate: true,
            },
            isCardModified: {
                handler(newValue) {
                    this.$emit('update:isModified', newValue);
                },
                immediate: true,
            },
            patientSourceType: {
                handler(val) {
                    if (val) {
                        this.options = val.map((item) => {
                            if (item.name === '顾客推荐') {
                                return {
                                    ...item,
                                    slot: '新增患者档案',
                                };
                            }

                            return { ...item };
                        });
                    }
                },
                immediate: true,

                deep: true,
            },
            visitSourceOption(val) {
                if (val.length) {
                    if (val.length > 2) {
                        this.registration.sourceId = val[val.length - 2].value;
                        this.registration.sourceFrom = val[val.length - 1] ? val[val.length - 1].value : null;
                    } else if (['顾客推荐', '员工推荐', '医生推荐', '转诊医生'].includes(val[0].label)) {
                        this.registration.sourceId = val[0].value;
                        this.registration.sourceFrom = val[1] ? val[1].value : null;
                    } else {
                        this.registration.sourceId = val[val.length - 1].value;
                        this.registration.sourceFrom = null;

                    }
                } else {
                    this.registration.sourceId = null;
                    this.registration.sourceFrom = null;
                }
            },
            'postData.isReserved': {
                handler(val) {
                    // 灵活预约
                    if (!this.isFixOrderMode && val) {
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);
                        this.pickerOptions = {
                            disabledDate(date) {
                                return date < today;
                            },
                            shortcuts: [{
                                text: '今天',
                                onClick(cb) {
                                    const start = today;
                                    cb(start);
                                },
                            }],
                        };
                    }
                },
            },
            defaultEnableCategories: {
                handler(val) {
                    this.doctorEnableCategories = Clone(val);
                },
                immediate: true,
                deep: true,
            },
            defaultRegistrationFees: {
                handler(val) {
                    this.doctorRegistrationFees = Clone(val);
                },
                immediate: true,
                deep: true,
            },
        },
        async created() {
            if (!this.isFieldLayout) {
                // 新建焦点落在姓名上
                this._timer = setTimeout(() => {
                    $('.patient-info-wrapper').find('input').eq(0).focus();
                }, 0);
            }
            this.invoiceService = new InvoiceService();
            this.departments.forEach((item) => {
                this.departmentObj[item.departmentId] = item.doctors;
            });
            // 开启咨询师功能才拉取咨询师列表 查看挂号详情不需要拉取
            if (this.showConsultant && this.isEditorStatus) {
                this.$store.dispatch('consult/updateConsultantList');
            }
            this._calcFee = debounce(this.calcFee, 200, true);
            this._calcOrderNo = debounce(this.calcOrderNo, 200, true);
            this._keydownHandle = debounce(this.keydownHandle, 300, true);
            this.registration.isReserved = this.isReserved ?? (this.isToday ? 0 : 1);
            this.registration.doctorId = this.doctorId ?? '';
            this.registration.registrationCategory = this.registrationCategory;
            this.postData = Clone(this.registration);
            this.prePostData = this.postData;
            if (this.isFixOrderMode) {
                // 点击左侧ql,对应到当前时间段[上午、下午、晚上]的号源
                if (!this.timeOfDay && (!this.postData.isReserved) || this.isQuickReservation) {
                    // 挂号
                    this.handleRegistrationTimeOfDay();
                } else {
                    this.registrationTime = this.timeOfDay ? (this.timeOptions.find((item) => item.time === this.timeOfDay)?.id || '') : this.timeOptions[0].id;
                }
                if (this.timeRange) {
                    this.orderNoAndTime = `${this.orderNo || ''}-${this.timeRange?.[0]}-${this.timeRange?.[1]}-${this.orderNoType || 0}-${this.orderNoTimeOfDay || this.postData.timeOfDay}`;
                }
            }

            // 新创建时，避免用户马上点开医生选择面板导致的数据闪现问题
            if (!this.registrationId && this.departments.length) {
                await this.setDefaultDoctorInfo();
            }

            await Promise.all([
                !this.isFieldLayout && this.$store.dispatch('initClinicRegistrationFieldConfig'),
                this.$store.dispatch('initCallConfig'),
                this.$store.dispatch('initChargeConfig'),
                // 挂号就诊备注
                this.$store.dispatch('initRegistrationRemarkList'),
                // 拉取门店配置-获取门诊设置
                this.$store.dispatch('initClinicBasic'),
                this.fetchRegistrationFee(),
                this.initRevisitExternalLink(),
                // 拉取预约property配置
                this.fetchRegistrationProperty(),
            ]);

            /**
             * @desc 拉取挂号详情状态
             * 1：未收费需要计算折扣(已收费直接读取)
             * <AUTHOR>
             * @date 2019/09/02 17:30:53
             */
            if (this.registrationId) {
                await this.fetchDetail();

                // 1:
                if (!this.disabledEditCharge) {
                    await this.calcFee();
                }
            } else {
                if (this.oldRegistrationId) {
                    this.dragBtnLoading = true;
                }
                // 快速预约直接带入患者信息，也需带入既往史
                if (this.isQuickReservation) {
                    this.medicalRecord.pastHistory = this.pastHistory || '';
                    this.medicalRecord.allergicHistory = this.allergicHistory || '';
                }
                // 1:
                this.setPayFeeByDoctorFee();
                // 2:
                await this.fetchDepartmentsDoctors(!this.registrationId);

                if (!this.isFixOrderMode) {
                    await this.getFlexibleTimeCount(); // 后续需要this.registrationCount参数，所以必须先调用
                    if (this.oldRegistrationId) {
                        this.timeRangePickerOptions = {
                            timeRange: {
                                begin: '06:00',
                                end: '23:00',
                            },
                        };
                        this.reserveTimeRange = this.timeRange;
                        this.postData.reserveTime.start = this.reserveTimeRange[0];
                        this.postData.reserveTime.end = this.reserveTimeRange[1];
                    } else {
                        this.handleTimeRangePickerOptions(this.timeRange, true);
                    }
                }

                // 3:
                this._key = `${this.currentClinic?.clinicId}_${this.currentClinic?.userId}`;
                localStorage.setObj('reg_fee_delay', this._key, null);

                if (!this.doctorId || this.doctorId === ANONYMOUS_DOCTOR_ID) {
                    if (this.isFixOrderMode) {
                        await this.handleOrderNoList();
                    }

                    await this.calcFee();
                }

                this.cacheData();
                this.cachePostData = Clone(this.postData);
                this.cloneVisitSourceRemark = this.visitSourceRemark;
                this.cloneVisitSourceOption = Clone(this.visitSourceOption);

                // 若是拖动
                if (this.oldRegistrationId) {
                    let result = null;
                    const isCharged = this.itemPayStatusV2 > PayStatusV2.NOT_PAID;
                    try {
                        if (isCharged) {
                            this.changeVisitStatus({
                                doctorId: this.postData.doctorId,
                                patientId: this.patient.id,
                            });
                        }

                        result = await RegistrationsAPI.fetch(this.oldRegistrationId);
                        if (result?.data?.chargeSheet) {
                            this.chargeSheet = result.data.chargeSheet;
                        }
                    } catch (e) {
                        console.log('fetchDetail error');
                    }
                    // 已收费
                    if (isCharged) {
                        this.pay.fee = result?.data?.registrationFormItem?.fee;

                        const { chargeSheetSummary } = this.chargeSheet || {};
                        this.pay.receivable = chargeSheetSummary.receivableFee;
                        this.discountFee = chargeSheetSummary.discountFee;
                    }
                    // 初始化数据
                    const { data } = result || {};
                    Object.assign(this.medicalRecord, {
                        pastHistory: data?.pastHistory,
                        allergicHistory: data?.allergicHistory,
                        physicalExamination: data?.physicalExamination,
                        familyHistory: data?.familyHistory,
                        personalHistory: data?.personalHistory,
                        chiefComplaint: data?.chiefComplaint,
                        presentHistory: data?.presentHistory,
                        epidemiologicalHistory: data?.epidemiologicalHistory,
                        dentistryExaminations: data?.dentistryExaminations,
                        preDiagnosisAttachments: data?.preDiagnosisAttachments,
                    });
                    // 拖动到当前医生不支持的项目列表
                    this.showReserveProduct && await this.fetchReservationList(this.doctorId);
                    this.itemRegistrationProductIds.forEach((item) => {
                        const curIndex = this.reservationProductList.findIndex((item2) => item2.id === item.id);
                        if (curIndex === -1) {
                            this.curDoctorNotExistList.push(item);
                        } else {
                            this.registrationProductIds.push(item.id);
                        }
                    });
                    this.visitSourceOption = RecommendService.getInstance().initCascaderValue({
                        visitSourceId: this.itemVisitSourceId,
                        visitSourceName: null,
                        visitSourceFrom: this.itemVisitSourceFrom,
                        visitSourceFromName: this.itemVisitSourceFromName,
                    });
                    this.visitSourceRemark = this.itemVisitSourceRemark;
                    this.dragBtnLoading = false;
                }
                if (this.patient?.id) {
                    !this.oldRegistrationId && await this.setPatientPastHistory(this.patient.id);
                    await this.getMedicalRecommendationInfo(this.patient.id);
                }
            }
            if (this.isEditorStatus) {
                await this.onLoadEditStatusData(false);
            } else {
                this.onLoaded();
            }
            // 如果不是预约详情 是待定预约
            if (!this.registrationId && this.isWaitAppointment) {
                // 有费用 且已经支付过了
                if (this.itemFee && this.itemPayStatusV2 >= PayStatusV2.PAID) {
                    this.pay.fee = this.itemFee;
                }
                this.registrationProductIds = this.itemRegistrationProductIds?.map((item) => item.id) || [];
                await this.handleChangeProducts();
            }
        },
        mounted() {
            document.addEventListener('keydown', this._keydownHandle, true);
            this.$once('hook:beforeDestroy', () => {
                document.removeEventListener('keydown', this._keydownHandle, true);
            });
        },
        methods: {
            ...mapActions([
                'fetchWaitRegisterAppointmentPatientList',
                'fetchRegistrationFee',
                'initRevisitExternalLink',
                'initClinicRegistrationFieldConfig',
            ]),
            formatAge,
            parseTime,
            validateMobile,
            validateAge,
            autoDestroyInvoice,
            validateMonth(value, callback) {
                const {
                    age,
                } = this.patient;
                const {
                    month,
                } = age || {};
                if (!month) {
                    callback({ validate: true });
                    return;
                }
                if (!/^(0?[1-9]|1[0-1])$/.test(value)) {
                    callback({
                        validate: false,
                        message: '最多11月',
                    });
                } else {
                    callback({ validate: true });
                }
            },
            validateDatePicker(value, callback) {
                if (value?.[0]) {
                    callback({ validate: true });
                } else {
                    callback({
                        validate: false,
                        message: '不能为空',
                    });
                }
            },
            /**
             * @desc 姓名和手机号 失焦后请求patientId
             * <AUTHOR>
             * @date 2018/10/22 22:20:56
             */
            async fetchPatient() {
                const {
                    id,
                    name,
                    mobile,
                } = this.patient;

                if (id || !name || !mobile) return false;
                const { data } = await PatientsAPI.fetchPatientByNameMobile(name, mobile);
                await this.selectPatient(data);
            },
            async selectPatient(patient, markShebaoCardInfo = false, healthCard, originCardInfo = null) {
                if (!patient) return false;

                const {
                    id,
                    name,
                    age,
                    sex,
                    birthday,
                    created,
                    mobile,
                    isMember,
                    idCard,
                    company,
                    profession,
                    marital,
                    weight,
                    sn,
                    tags,
                    remark,
                    wxBindStatus = 0,
                    wxNickName = '',
                    appFlag = 0,
                    arrearsFlag = 0,
                    isMemberFamily,
                    shebaoCardInfo,
                    parentName = '',
                    patientSource,
                    memberInfo,
                    address,
                    childCareArchives,
                    chronicArchivesInfo,
                    promotionCardPatientListView,
                    pastHistory,
                    allergicHistory,
                } = patient;

                const {
                    addressCityId = null,
                    addressCityName = null,
                    addressDistrictId = null,
                    addressDistrictName = null,
                    addressProvinceId = null,
                    addressProvinceName = null,
                    addressDetail = null,
                } = address || {};

                // 去除age中的0
                if (+age.year === 0) {
                    age.year = null;
                }
                if (+age.month === 0) {
                    age.month = null;
                }

                // 重新赋值
                this.patient = {
                    id,
                    name,
                    age,
                    sex,
                    birthday,
                    created,
                    mobile,
                    isMember,
                    idCard,
                    company,
                    profession,
                    marital,
                    weight,
                    sn,
                    tags,
                    remark,
                    isMemberFamily,
                    wxBindStatus,
                    wxNickName,
                    shebaoCardInfo: markShebaoCardInfo ? shebaoCardInfo : null,
                    healthCard,
                    patientSource,
                    parentName,
                    memberInfo,
                    childCareArchives,
                    chronicArchivesInfo,
                    promotionCardPatientListView,
                    appFlag,
                    arrearsFlag,
                    address,
                    addressCityId,
                    addressCityName,
                    addressDistrictId,
                    addressDistrictName,
                    addressProvinceId,
                    addressProvinceName,
                    addressDetail,
                    pastHistory,
                    allergicHistory,
                    originCardInfo,
                };
                this.$emit('select-patient-info', this.patient, markShebaoCardInfo);
                if (this.patient.id) {
                    this.timer2 = setTimeout(() => {
                        $('.form-content .abc-input__inner').eq(0).focus();
                    }, 0);
                } else {
                    this._timer = setTimeout(() => {
                        $('.patient-info-wrapper').find('input').eq(0).focus();
                    }, 0);
                }

                await this.changePatientHandler(patient);
            },
            async handleAgainVisit() {
                const {
                    doctorId,
                    doctorName,
                    departmentId,
                } = this.lastDoctorInfo || {};

                const lastDepartment = this.departmentsList.find((item) => item.departmentId === departmentId);
                const lastDoctor = lastDepartment?.doctors?.find((doctor) => doctor.doctorId === doctorId);
                // 上次就诊的科室或医生信息发生变化
                if (!lastDepartment || !lastDoctor) {
                    this.postData.departmentId = '';
                    this.defaultDoctorInfo = {
                        doctorId,
                        doctorName: doctorName || '',
                        departmentId: '',
                        departmentName: '',
                    };
                    this.handleErrorDoctorInfo();

                    return;
                }

                const currentDepartmentDoctor = this.allDoctors.find((item) => item.doctorId === doctorId && item.departmentId === departmentId);
                const oldDoctorId = this.postData.doctorId;
                this.postData.doctorId = doctorId || '';
                this.postData.departmentId = departmentId || '';
                const info = {
                    ...currentDepartmentDoctor,
                    departmentName: currentDepartmentDoctor?.departmentName || '其他',
                };
                this.defaultDoctorInfo = Clone(info);
                this.handleErrorDoctorInfo();
                await this.handleSelectDoctor(this.postData.doctorId, oldDoctorId, true);
            },
            handlePatientInfoBlur() {
                const {
                    name,
                    mobile,
                    age: {
                        year,
                        month,
                    },
                } = this.patient;
                if (name || mobile || year !== '' || month !== '') {
                    this.$emit('change-patient-info', this.patient);
                }
            },
            handlePatientSexBlur() {
                this.$emit('change-patient-info', this.patient);
            },
            handleAppointmentCard(type) {
                this.$emit('handleAppointmentCard', type);
            },
            changeDiscount(useMemberFlag) {
                if (useMemberFlag) {
                    this.pay.useMemberFlag = useMemberFlag;
                }
                this._calcFee();
            },
            async handleReserveDate() {
                this.isChangeDoctorInfo = true;
                this.setPayFeeByDoctorFee();
                // 切换日期, 重新拉取医生的排班信息
                this.fetchDepartmentsDoctors();
                if (this.isFixOrderMode) {
                    this.changeReserveDate();
                } else {
                    await this.getFlexibleTimeCount();
                    this.handleTimeRangePickerOptions();
                }
            },
            handleChangeProducts() {
                this.handleTimeRangePickerOptions(this.reserveTimeRange);
            },

            // 通过初复诊来获得预约时间范围
            handleTimeRangePickerOptions(timeArr = null, init = false) {
                if (this.isFirstEditorDisabled) return;
                // 是否是初诊
                const isFirstRevisit = this.revisitStatus === RevisitStatus.FIRST;
                // 初诊最大时长
                let firstRevisitMaxTime = 0;
                // 复诊最大时长
                let revisitMaxTime = 0;
                const selectedProducts = this.productOptions.filter((item) => {
                    if (this.registrationProductIds?.indexOf(item.id) !== -1) {
                        return item;
                    }
                });
                selectedProducts?.forEach((item) => {
                    let time = 0;
                    if (isFirstRevisit) {
                        const {
                            hour,
                            min,
                        } = item?.visitServiceDuration || {};
                        time = hour * 60 + min;
                        if (firstRevisitMaxTime < time) {
                            firstRevisitMaxTime = time;
                        }
                    } else {
                        const {
                            hour,
                            min,
                        } = item?.revisitedServiceDuration || {};
                        time = hour * 60 + min;
                        if (revisitMaxTime < time) {
                            revisitMaxTime = time;
                        }
                    }
                });
                let timeRangeEnd = '';
                if (this.isSelectedRegistrationProducts) {
                    timeRangeEnd = isFirstRevisit ? firstRevisitMaxTime : revisitMaxTime;
                } else {
                    const {
                        hour: visitServiceHour,
                        min: visitServiceMin,
                    } = this.registrationsConfig?.serviceDuration?.visitServiceDuration ?? {};
                    const {
                        hour: revisitedServiceHour,
                        min: revisitedServiceMin,
                    } = this.registrationsConfig?.serviceDuration?.revisitedServiceDuration ?? {};
                    const visitServiceDuration = visitServiceHour * 60 + visitServiceMin; // 初诊时长
                    const revisitedServiceDuration = revisitedServiceHour * 60 + revisitedServiceMin; // 复诊时长
                    timeRangeEnd = isFirstRevisit ? visitServiceDuration : revisitedServiceDuration;
                }
                this.setReserveTime(timeArr, init, timeRangeEnd);
            },
            setReserveTime(timeArr, init, timeRangeEnd) {
                const timeRange = ['06:00', '23:00'];
                const isToday = this.postData.reserveDate === this.todayTime;
                // 当前时间
                const nowDate = this.handleReserveTimeRange(timeRangeEnd)[0];
                let reserveTimeRange = [];
                if (timeArr?.[0]) {
                    const endTime = this.getReserveEndTime(timeArr[0], timeRangeEnd);
                    reserveTimeRange = [timeArr[0], endTime];
                } else if (isToday) {
                    reserveTimeRange = this.handleReserveTimeRange(timeRangeEnd);
                } else {
                    const endTime = this.getReserveEndTime('08:00', timeRangeEnd);
                    reserveTimeRange = ['08:00', endTime];
                }
                const disabledTimeList = [];
                this.registrationCount.forEach((item) => {
                    if (item.isStopDiagnose) disabledTimeList.push(item.localTime);
                });
                this.timeRangePickerOptions = isToday ? ({
                    disabledMinTime(time) {
                        if (nowDate === '23:00') {
                            return time <= reserveTimeRange[0];
                        }
                        return time < nowDate || disabledTimeList.includes(time);
                    },

                    disabledMaxTime(time) {
                        return time <= nowDate || disabledTimeList.includes(time);
                    },

                    timeRange: {
                        begin: timeRange[0],
                        end: timeRange[1],
                    },
                }) : ({
                    disabledMinTime(time) {
                        return disabledTimeList.includes(time);
                    },

                    disabledMaxTime(time) {
                        return disabledTimeList.includes(time);
                    },

                    timeRange: {
                        begin: timeRange[0],
                        end: timeRange[1],
                    },
                });
                this.reserveTimeRange = reserveTimeRange;
                // 计算后的时间若已停诊则置空
                const reserveTimeRangeHasDisabledTime = disabledTimeList.some((disTime) => this.getMinutesByTime(reserveTimeRange[0]) <= this.getMinutesByTime(disTime) && this.getMinutesByTime(disTime) <= this.getMinutesByTime(reserveTimeRange[1]));

                // 看板特殊处理 若选中时间比当前时间大则清空
                let reserveTimeRangeLessNow = false;
                if (disabledTimeList.length > 0) reserveTimeRangeLessNow = this.getMinutesByTime(reserveTimeRange[0]) < this.getMinutesByTime(nowDate);

                if (reserveTimeRangeHasDisabledTime || reserveTimeRangeLessNow) this.reserveTimeRange = ['', ''];

                if (init) {
                    this.postData.reserveTime.start = this.reserveTimeRange[0];
                    this.postData.reserveTime.end = this.reserveTimeRange[1];
                }
            },
            handleRegistrationTimeOfDay() {
                const { reserveTime } = this.postData;
                if (!reserveTime.start && !reserveTime.end) {
                    const hours = new Date().getHours();
                    if (hours >= 0 && hours < 12) {
                        this.registrationTime = this.timeOptions[0].id;
                        this.postData.timeOfDay = '上午';
                    } else if (hours >= 12 && hours < 18) {
                        this.registrationTime = this.timeOptions[1].id;
                        this.postData.timeOfDay = '下午';
                    } else {
                        this.registrationTime = this.timeOptions[2].id;
                        this.postData.timeOfDay = '晚上';
                    }
                }
            },
            async setPatientPastHistory(id) {
                try {
                    if (id) {
                        const {
                            pastHistory,
                            allergicHistory,
                        } = await PatientsAPI.fetchPastHistory(id);
                        this.medicalRecord.pastHistory = pastHistory || '';
                        this.medicalRecord.allergicHistory = allergicHistory || '';
                    } else {
                        this.medicalRecord.pastHistory = '';
                        this.medicalRecord.allergicHistory = '';
                        this.resetHistoryList();
                    }
                } catch (e) {
                    console.log('err', e);
                }
            },
            async changePatientHandler(patient) {
                await this.setPatientPastHistory(patient.id);
                if (patient.id) {
                    await Promise.all([
                        this.getMedicalRecommendationInfo(patient.id),
                        this.findHistory(),
                        this.fetchDepartmentsDoctors(),
                    ]);
                }
                // 不能编辑收费时，就不能更新 member 信息
                if (this.disabledEditCharge) {
                    return;
                }
                this.chargeDialogData.postData.patient = patient; // 优惠券选择需要patientId
                // 切换患者需要清空卡项
                this.patientCardPromotions = [];
                this.clearMember();
                await this.setPayFee();
                await this.calcFee();
                if (!this.isFixOrderMode) {
                    this.handleTimeRangePickerOptions(this.reserveTimeRange, true);
                }
            },
            clearMember() {
                this.pay.memberId = null;
                this.pay.useMemberFlag = UseMemberFlagEnum.USE_DEFAULT;
                this.memberInfo = {
                    patient: {
                        id: '',
                        name: '',
                        mobile: '',
                    },
                    memberType: {
                        name: '',
                    },
                };
                this._calcFee();
            },
            async handleEditRegistration() {
                // 当前科室医生班次全部停诊时 展示本身号源
                this.isStopDiagnose = false;

                // 开启咨询师功能才拉取咨询师列表 查看挂号详情不需要拉取
                if (this.showConsultant) {
                    this.$store.dispatch('consult/updateConsultantList');
                }
                this.cachePostData = Clone(this.postData);
                this.cacheData();
                // 从查看变为编辑, 重新拉取医生的排班信息
                await this.fetchDepartmentsDoctors();
                await this.onLoadEditStatusData();
                // 编辑进来根据停诊状态限制时间
                const timeRange = ['06:00', '23:00'];
                const reserveTimeRange = this.handleReserveTimeRange(0);
                const disabledTimeList = [];
                this.registrationCount.forEach((item) => {
                    if (item.isStopDiagnose) disabledTimeList.push(item.localTime);
                });
                this.timeRangePickerOptions = {
                    disabledMinTime(time) {
                        return time < reserveTimeRange[0] || disabledTimeList.includes(time);
                    },

                    disabledMaxTime(time) {
                        return time <= reserveTimeRange[0] || disabledTimeList.includes(time);
                    },

                    timeRange: {
                        begin: timeRange[0],
                        end: timeRange[1],
                    },
                };
                this.isEditor = true;
                this.$emit('update:editorStatus', true);
                // 查询挂号单的时候也需要查询最近推荐人信息
                await this.getMedicalRecommendationInfo(this.patient.id);
            },
            async onLoadEditStatusData(fetchFlexibleTimeCount = true) {
                if (!this.isFixOrderMode) {
                    // 灵活预约拉取预约项目非拖动
                    if (!this.oldRegistrationId && this.showReserveProduct) {
                        await this.fetchReservationList(this.postData.doctorId);
                    }
                    fetchFlexibleTimeCount && await this.getFlexibleTimeCount();
                }

                Promise.all([
                    this.findHistory(),
                    this.getListSource(),
                    this.getDailyReserveStatus(),
                ]);
            },
            handleCancel() {
                this.showAppointmentCard = false;
            },
            async changeReserveDate(val, index, oldVal) {
                this.isChangeDoctorInfo = true;
                if (this.isFixOrderMode) {
                    if (val && val === oldVal) return;
                    this.orderNoAndTime = '';
                    this.postData.isAdditional = false;
                    this.orderNoAndTimeOptions = [];
                    await this.handleOrderNoList();
                }
            },
            async changeTimeOfDay(val, index, oldVal) {
                if (val === oldVal) return;
                this.orderNoAndTime = '';
                this.postData.isAdditional = false;
                this.orderNoAndTimeOptions = [];
                for (let i = 0; i < this.timeOptions.length; i++) {
                    const option = this.timeOptions[i];
                    if (option.id === val) {
                        this.postData.timeOfDay = option.time;
                        this.postData.reserveTime.start = option.value.start;
                        this.postData.reserveTime.end = option.value.end;
                        break;
                    }
                }
                if (this.isFixOrderMode) {
                    await this.getOrderNoAndTimeOptions();
                }
            },
            async changeType(item) {
                if (this.postData.isReserved === item?.value) return;

                const {
                    doctorId,
                    departmentId,
                } = this.postData || {};
                this.postData.isReserved = item?.value;
                const cacheReserveDate = this.postData.reserveDate;
                this.isChangeDoctorInfo = true;
                //  切换到预约清空时间和号源
                this.resetReserveDate();
                if (item?.value === 0) {
                    this.postData.reserveDate = formatDate(new Date(), 'YYYY-MM-DD');
                    if (this.isFixOrderMode) {
                        this.handleRegistrationTimeOfDay();
                    } else {
                        await this.getFlexibleTimeCount(); //  先查取停诊时间段
                        this.handleTimeRangePickerOptions(this.timeRange);
                    }
                    this.setPayFeeByDoctorFee();
                    this.$nextTick(() => {
                        this.errorDoctorInfo = {
                            error: !departmentId || !doctorId,
                            message: `请选择${!departmentId && !doctorId || !doctorId ? '医生' : '科室'}`,
                        };
                    });
                } else {
                    this.postData.reserveDate = this.date;
                    // 预约无便民门诊
                    if (this.postData.registrationCategory === RegistrationCategory.CONVENIENCE) {
                        this.postData.registrationCategory = RegistrationCategory.ORDINARY;
                    }
                    // 挂号切换预约时, 如果选中的是不指定医生, 且配置为必须指定医生, 则清空医生信息
                    if (!this.notMustReserveEmployee && this.postData.doctorId === ANONYMOUS_DOCTOR_ID) {
                        this.handleSelectedClear();
                    }
                    this.setPayFeeByDoctorFee();
                    if (!departmentId || !doctorId) {
                        this.$nextTick(() => {
                            this.errorDoctorInfo = {
                                error: true,
                                message: `请选择${!doctorId ? '医生' : '科室'}`,
                            };
                        });
                    }
                    await this.getDailyReserveStatus();
                }
                // 如果切换挂号和预约时, 时间改变, 则重新拉取医生的排班信息
                if (cacheReserveDate !== this.postData.reserveDate) {
                    await this.fetchDepartmentsDoctors();
                }
                this._calcFee();
                if (this.isFixOrderMode) {
                    this.orderNoAndTime = '';
                    this.postData.isAdditional = false;
                    this.orderNoAndTimeOptions = [];
                    await this.handleOrderNoList();
                }
            },
            setPayFeeByDoctorFee() {
                const doctorFee = this.doctorRegistrationFees.find((item) => item.registrationCategory === this.postData.registrationCategory);
                const {
                    regUnitPrice,
                    revisitedRegUnitPrice,
                    referralRegUnitPrice,
                    referralRevisitedRegUnitPrice,
                    isDiffForRevisited,
                    revisitedFeeCustomUseRule,
                } = doctorFee || {};

                // 是转诊且开启了多号种(目前就医院有多号种)
                const isReferralAndShowRegistrationCategory = this.isReferral && this.isShowRegistrationCategory;
                // 根据医生的模式取费用(只有医院有转诊费)
                // 初诊/首诊
                const firstVisitedPrice = (isReferralAndShowRegistrationCategory ? referralRegUnitPrice : regUnitPrice) || 0;
                // 复诊/再诊
                const reVisitedPrice = (isReferralAndShowRegistrationCategory ? referralRevisitedRegUnitPrice : revisitedRegUnitPrice) || 0;

                if (isDiffForRevisited === REGISTERED_FEE_MODE_TYPE.SHORT_DIFFERENT) {
                    if (!this.lastDiagnosedTime) {
                        this.pay.fee = this.sourceUnitPrice = firstVisitedPrice;
                    } else {
                        const { effectiveDays } = revisitedFeeCustomUseRule || {};
                        const reserveTime = new Date(this.postData.reserveDate);
                        const reserveDate = new Date(reserveTime.getFullYear(), reserveTime.getMonth(), reserveTime.getDate());
                        const lastTime = new Date(this.lastDiagnosedTime);
                        const lastDate = new Date(lastTime.getFullYear(), lastTime.getMonth(), lastTime.getDate());
                        const differenceDays = Math.round(Math.abs((reserveDate - lastDate) / (24 * 60 * 60 * 1000)));
                        // 包含上次就诊挂号当天开始计算
                        this.pay.fee = this.sourceUnitPrice = differenceDays + 1 > effectiveDays ? firstVisitedPrice : reVisitedPrice;
                    }
                } else {
                    this.pay.fee = this.sourceUnitPrice = (this.revisitStatus === RevisitStatus.FIRST) ? firstVisitedPrice : reVisitedPrice;
                }
                this.expectedUnitPrice = null;
            },
            async findHistory() {
                if (!this.patient.id || this.patient.id === ANONYMOUS_DOCTOR_ID) {
                    this.mrCount = 0;
                    return;
                }
                try {
                    const { data } = await PatientsAPI.fetchHistoryAbs(this.patient.id, {
                        offset: 0,
                        limit: 1,
                        withPrescriptionInfo: 0,
                    }, true);
                    this.mrCount = data.totalCount;
                    this.lastDoctorInfo = data?.result?.[0];
                } catch (err) {
                    this.mrCount = 0;
                    this.lastDoctorInfo = null;
                    console.error(err);
                }
            },
            resetHistoryList() {
                this.mrCount = 0;
                this.lastDoctorInfo = null;
                this.revisitStatus = RevisitStatus.FIRST;
                this.lastDiagnosedTime = null;
            },
            save() {
                const {
                    visitSourceId,
                    visitSourceFrom,
                } = this.getVisitSourceInfo();
                const isCalculateNo = +this.calculateOrderNo?.orderNo === +this.postData.orderNo;
                const registrationFormItem = {
                    ...this.registration,
                    ...this.postData,
                    registrationProductIds: this.registrationProductIds,
                    ...(this.isFixOrderMode && { orderNo: isCalculateNo ? '' : +this.postData.orderNo }),
                };
                delete registrationFormItem.registrationProducts;
                return this.update(registrationFormItem, visitSourceId, visitSourceFrom);
            },
            /**
             * @param {boolean} needCheckPrint 是否需要走打印流程，详情里面的支付不走
             */
            async charge(needCheckPrint = false) {
                const {
                    chargeSheetSummary,
                    id: chargeSheetId,
                    status: chargeStatus,
                    isReplay,
                    postData,
                } = this.chargeDialogData;
                // 修改价格为0元，点击收费拉取详情应是收费了，此时不再调用收费弹窗
                if (chargeStatus === 2) return;

                this._chargeDialogInstance = new AbcChargeDialog({
                    pcRouterVm: this.$router,
                    postData, // 提交的收费单数据
                    chargeSheetSummary,
                    chargeSheetId,
                    chargeStatus,
                    isReplay,
                    disableAdjustmentBtn: true,
                    disableDispensingBtn: true,
                    disablePrintBtn: true,
                    hiddenPayModeList: [PayModeEnum.ARREARS],
                    showClose: !needCheckPrint,
                    onPartChargeSuccess: this.partChargeSuccess,
                    onChargeSuccess: (data) => this.chargeSuccess(data, needCheckPrint),
                    onClose: this.onClose,
                }).generateDialog({
                    parent: this,
                });
                this.$emit('hide-left-extend');
            },
            async onClose(scene) {
                if (!scene) {
                    this.$refs['appointment-card-footer']?.updatePrintConfig();
                    await this.$refs['appointment-card-footer']?.registrationSuccessCallback();
                    this.showAppointmentCard = false;
                }
            },
            async partChargeSuccess(refresh = true) {
                this.prePostData = Clone(this.prePostData);
                await this.fetchDetail();
                refresh && this.$emit('refresh');
            },
            async chargeSuccess({ chargeTransactionId }, needCheckPrint) {
                if (needCheckPrint) {
                    this.$refs['appointment-card-footer'].updatePrintConfig();
                    await this.partChargeSuccess(false);
                    await this.$nextTick();
                    await this.$refs['appointment-card-footer'].registrationSuccessCallback();
                    await this.$refs['appointment-card-footer'].chargeSuccessCallback(chargeTransactionId);
                } else {
                    await this.partChargeSuccess();
                }
                this.showAppointmentCard = false;
            },
            toggleDiscount() {
                if (this.disabledDiscount) return;
                this.showDiscountTable = !this.showDiscountTable;
            },
            clickOutsideDiscountWrapper(e) {
                const eventPath = e.path || (e.composedPath && e.composedPath());
                if (eventPath && this.showDiscountTable) {
                    const eventPathClassNameList = eventPath.map((item) => item.className);
                    const flag = eventPathClassNameList.some((item) => (`${item}`).includes('edit-status-wrapper'));
                    if (flag) {
                        this.showDiscountTable = false;
                    }
                }
            },
            getEventPath(evt) {
                if (!evt) return '';
                return evt.path || (evt.composedPath && evt.composedPath()) || '';
            },
            handleClickDiscountTableOutside(mousedown, mouseup) {
                if (!this.showDiscountTable) return;
                const classNames = [
                    'discount-money',
                    'abc-dialog-headerbtn',
                    'dialog-footer',
                    'abc-dialog-body',
                    'ellipsis',
                    'charge-select-member-dialog',
                    'charge-select-promotion-card-dialog',
                    'suggestions-item',
                    'card-dialog-footer',
                ];
                let exit = false;
                const mouseupPath = this.getEventPath(mouseup);
                if (mouseupPath) {
                    mouseupPath.forEach((item) => {
                        if (item.classList) {
                            const classList = Array.from(item.classList);
                            classNames.forEach((one) => {
                                if (Array.from(classList).includes(one)) {
                                    exit = true;
                                }
                            });
                        }
                    });
                }
                if (exit) return;
                this.showDiscountTable = false;
            },
            /**
             * 关闭三日复诊确认弹窗
             */
            closeThreeDaysRepeatModal() {
                this.buttonLoading = false;
                this.noChargeButtonLoading = false;
                this.showThreeDaysRepeatModal = false;
                this.showThreeDaysRepeatData = [];
                this.threeDaysRepeatParams = null;
            },
            /**
             * 处理三日复诊确认
             * @param {Object} threeDaysParams 复诊参数
             */
            async handleThreeDaysRepeatConfirm(threeDaysParams) {
                const {
                    patient,
                    registrationFormItem,
                    visitSourceId,
                    visitSourceFrom,
                    chargeAfterConfirm,
                    operateType,
                } = this.threeDaysRepeatParams || {};

                if (!this.threeDaysRepeatParams) {
                    return;
                }

                try {
                    if (operateType === 'create') {
                        await this.create({
                            patient,
                            registrationFormItem,
                            visitSourceId,
                            visitSourceFrom,
                            chargeAfterConfirm,
                            threeDaysParams,
                        });
                        this.shebaoRegistration();
                    } else if (operateType === 'update') {
                        const data = await this.update(registrationFormItem, visitSourceId, visitSourceFrom, threeDaysParams);
                        this.buttonLoading = false;
                        if (data) {
                            this.$Toast({
                                message: '保存成功',
                                type: 'success',
                            });
                            this.$emit('refresh', {
                                id: this.registrationId,
                                registrationFormItem,
                            });
                            this.showAppointmentCard = false;
                        }
                    }
                } finally {
                    // 清理相关数据，避免内存泄漏
                    this.showThreeDaysRepeatModal = false;
                    this.showThreeDaysRepeatData = [];
                    this.threeDaysRepeatParams = null;
                }
            },

            /**
             * 处理创建挂号预约，包含辽宁沈阳的三日复诊逻辑
             */
            async handleCreateRegistration({
                patient,
                registrationFormItem,
                visitSourceId,
                visitSourceFrom,
                chargeAfterConfirm,
            }) {
                // 挂号&&辽宁沈阳地区，三日复诊选择
                if (!this.postData.isReserved && this.$abcSocialSecurity.config.isLiaoningShenyang && patient?.id) {
                    const {
                        departmentId,
                        doctorId,
                    } = this.postData;
                    try {
                        const result = await OutpatientAPI.fetchPatientRevisitUse(patient.id, {
                            departmentId,
                            doctorId,
                            withShebaoCharge: 1,
                        });
                        if (result?.data?.rows?.length) {
                            this.threeDaysRepeatParams = {
                                patient,
                                registrationFormItem,
                                visitSourceId,
                                visitSourceFrom,
                                chargeAfterConfirm,
                                operateType: 'create',
                            };
                            this.showThreeDaysRepeatModal = true;
                            this.showThreeDaysRepeatData = result.data.rows;
                        }
                    } catch (err) {
                        Logger.error({
                            scene: 'getThreeDaysRepeatData',
                            err,
                        });
                    }
                }

                // 未走三日接诊逻辑则直接创建
                if (!this.showThreeDaysRepeatModal) {
                    await this.create({
                        patient,
                        registrationFormItem,
                        visitSourceId,
                        visitSourceFrom,
                        chargeAfterConfirm,
                    });
                    this.shebaoRegistration();
                }
            },
            /**
             * 处理修改挂号预约，包含辽宁沈阳的三日复诊逻辑
             */
            async handleUpdateRegistration({
                patient,
                registrationFormItem,
                visitSourceId,
                visitSourceFrom,
            }) {
                // 挂号&&可修改收费信息&&非三日复诊单&&辽宁沈阳地区，走三日复诊选择
                if (!this.postData.isReserved && !this.disabledEditCharge && !this.isThreeDaysRepeat && this.$abcSocialSecurity.config.isLiaoningShenyang) {
                    const {
                        departmentId,
                        doctorId,
                    } = this.postData;
                    const result = await OutpatientAPI.fetchPatientRevisitUse(patient.id, {
                        departmentId,
                        doctorId,
                        withShebaoCharge: 1,
                        patientOrderId: this.patientOrderId,
                    });
                    if (result?.data?.rows?.length) {
                        this.threeDaysRepeatParams = {
                            patient,
                            registrationFormItem,
                            visitSourceId,
                            visitSourceFrom,
                            operateType: 'update',
                        };
                        this.showThreeDaysRepeatModal = true;
                        this.showThreeDaysRepeatData = result.data.rows;
                        return;
                    }
                }

                const data = await this.update(registrationFormItem, visitSourceId, visitSourceFrom);
                this.buttonLoading = false;
                if (data) {
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });
                    this.$emit('refresh', {
                        id: this.registrationId,
                        registrationFormItem,
                    });
                    this.showAppointmentCard = false;
                }
            },
            /**
             * @date 2024/05/14 17:00:43
             * @param {boolean} chargeAfterConfirm 确定后收费
             */
            async handleConfirm(chargeAfterConfirm) {
                await this.handleErrorDoctorInfo();
                if (!this.patient.id) {
                    let flag = false;
                    this.$abcEventBus.$emit('validate-patient-info', (result) => {
                        flag = result;
                    });

                    if (flag) {
                        this.$abcEventBus.$emit('edit-patient-info', true);
                        return;
                    }
                }
                if (this.needValidateMobile && !this.patient.mobile) {
                    this.$alert({
                        type: 'warn',
                        title: '手机号不能为空',
                        content: '请在患者资料中完善手机号后，再继续挂号预约',
                        showClose: false,
                        onConfirm: () => {
                            this.$abcEventBus.$emit('edit-patient-info');
                        },
                    });

                    return;
                }
                const selectList = this.productOptions.filter((item) => this.registrationProductIds.includes(item.id));
                const disabledReservationProductList = this.getDisabledRegistrationProduct(selectList);
                if (disabledReservationProductList.length) {
                    this.$alert({
                        type: 'warn',
                        title: '以下预约项目已被停用：',
                        content: [`<span>${disabledReservationProductList.map((item) => {
                            return item.displayName;
                        }).join('、')}</span>`],
                    });
                    return;
                }
                this.$refs.form.validate(async (valid) => {
                    if (valid) {
                        this.$refs['appointment-card-footer'] && (this.$refs['appointment-card-footer'].isConfirm = true);
                        if (!this.supportShortFlow) {
                            this.buttonLoading = true;
                        } else {
                            if (this.registrationSheetId || this.registrationId || this.oldRegistrationId) {
                                // 保存
                                this.buttonLoading = true;
                            } else {
                                // 新增
                                if (chargeAfterConfirm) {
                                    // 收费
                                    this.buttonLoading = true;
                                } else {
                                    // 暂不收费
                                    this.noChargeButtonLoading = true;
                                }
                            }
                        }

                        // 更新 科室、医生 name
                        const department = this.departments.find((item) => {
                            return item.departmentId === this.postData.departmentId;
                        });
                        this.postData.departmentName = department?.departmentName || '';
                        const doctor = this.doctors.find((item) => {
                            return item.doctorId === this.postData.doctorId;
                        });
                        this.postData.doctorName = doctor?.doctorName || '';
                        if (!this.isFixOrderMode) {
                            this.postData.reserveTime.start = this.reserveTimeRange[0];
                            this.postData.reserveTime.end = this.reserveTimeRange[1];
                        }

                        const {
                            visitSourceId,
                            visitSourceFrom,
                        } = this.getVisitSourceInfo();

                        const patient = Clone(this.patient);
                        delete patient.shebaoCardInfo;
                        const isCalculateNo = +this.calculateOrderNo?.orderNo === +this.postData.orderNo;
                        const postDataOrderNo = (isCalculateNo || this.isGenerateOrderNoOnSign) ? '' : +this.postData.orderNo;

                        const registrationFormItem = {
                            ...this.registration,
                            ...this.postData,
                            registrationProductIds: this.registrationProductIds,
                            ...(this.isFixOrderMode && { orderNo: postDataOrderNo }),
                        };
                        delete registrationFormItem.registrationProducts;

                        // 固定模式-自定义时段&非签到取号
                        if (this.isFixOrderMode && this.registrationsConfig?.serviceType !== SERVICE_TYPE_ENUM.ACCURATE_TIME && !this.isGenerateOrderNoOnSign) {
                            const selectedItem = this.orderNoAndTimeOptions.find((item) => {
                                const {
                                    orderNo,
                                    start,
                                    end,
                                    type,
                                    timeOfDay,
                                } = item;
                                const {
                                    orderNo: postDataOrderNo,
                                    reserveTime,
                                    orderNoType,
                                } = this.postData;

                                return +orderNo === +postDataOrderNo && start === reserveTime.start && end === reserveTime.end && +type === +orderNoType && timeOfDay === this.selectedOrderNoTimeOfDay;
                            });
                            registrationFormItem.reserveTime.orderNoStart = selectedItem?.orderNoStart || '';
                            registrationFormItem.reserveTime.orderNoEnd = selectedItem?.orderNoEnd || '';
                        }

                        try {
                            // 有待定预约的id 有待定预约的id待表需要处理预约状态
                            if (this.registrationSheetId) {
                                // 更新
                                const data = await this.setRegistrationAuditStatus(registrationFormItem, visitSourceId, visitSourceFrom);
                                this.buttonLoading = false;
                                if (data) {
                                    this.$Toast({
                                        message: '保存成功',
                                        type: 'success',
                                    });
                                    this.fetchWaitRegisterAppointmentPatientList();
                                    this.showAppointmentCard = false;
                                }
                            } else if (!this.registrationId && !this.oldRegistrationId) {
                                // 新增
                                if (patient?.id) {
                                    // 选择患者进行校验（手动填写新患者不选择没有id）
                                    const { data: isExistRegistration } = await RegistrationsAPI.fetchPatientIsExistRegistrations(patient.id, {
                                        patientId: patient.id,
                                        registrationType: this.registrationType,
                                        reserveDate: registrationFormItem.reserveDate,
                                    }) || {};
                                    const { isYes } = isExistRegistration || {};
                                    const reserveDateStr = registrationFormItem.reserveDate === this.todayTime ? '今天' : registrationFormItem.reserveDate;
                                    if (isYes) {
                                        this.$modal({
                                            type: 'warn',
                                            preset: 'alert',
                                            title: '重复预约提示',
                                            content: `${patient.name}已在${reserveDateStr}存在预约，是否完成本次预约`,
                                            onConfirm: async () => {
                                                // 处理挂号创建逻辑，包含辽宁沈阳的三日复诊逻辑
                                                await this.handleCreateRegistration({
                                                    patient,
                                                    registrationFormItem,
                                                    visitSourceId,
                                                    visitSourceFrom,
                                                    chargeAfterConfirm,
                                                });
                                            },
                                            onCancel: () => {
                                                this.buttonLoading = false;
                                                this.noChargeButtonLoading = false;
                                            },
                                        });
                                        return;
                                    }
                                }

                                // 处理挂号创建逻辑，包含辽宁沈阳的三日复诊逻辑
                                await this.handleCreateRegistration({
                                    patient,
                                    registrationFormItem,
                                    visitSourceId,
                                    visitSourceFrom,
                                    chargeAfterConfirm,
                                });
                            } else {
                                // 更新
                                await this.handleUpdateRegistration({
                                    patient,
                                    registrationFormItem,
                                    visitSourceId,
                                    visitSourceFrom,
                                });
                            }
                            if (this.appointmentModel === 'waitAppointment') {
                                this.$emit('handleAppointmentCard', 'addSuccess');
                            }
                        } catch (err) {
                            console.log(err);
                            this.buttonLoading = false;
                            const {
                                code,
                                message,
                                detail,
                            } = err;
                            if ([13502, 13503].includes(code) && this.crmAccessVisibility) {
                                this.$abcEventBus.$emit('edit-patient-info', false);
                                this.handlePatientExist(code, detail);
                            } else if (code === 17005) {
                                this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: message,
                                });
                            } else if (code === 15123 || code === 15122 || code === 15125) {
                                this.$alert({
                                    type: 'warn',
                                    title: '提示',
                                    content: message,
                                });
                            }
                        }
                    }
                });
            },
            // TODO CRM 患者已经存在处理
            handlePatientExist(code = 13503, detail = []) {
                const {
                    name = '',
                    mobile = '',
                    idCard = '',
                    idCardType = DEFAULT_CERT_TYPE,
                } = detail;
                this.$confirm({
                    type: 'warn',
                    title: '提示',
                    confirmText: '添加患者',
                    showCancel: false,
                    content: code === 13502 ? `证件号[${idCardType}]${idCard}已经被连锁中存在的患者 ${name} 注册` : `同名患者 ${name} ${mobile ? `${this.isCanSeePatientMobileInCrm ? mobile : encryptMobile(mobile)}` : ''} ${idCard ? `[${idCardType}]${idCard}` : ''}已在连锁中存在`,
                    onConfirm: async () => {
                        await this.addPatientFromOtherClinic();
                    },
                });
            },
            // 从其他门店添加患者
            async addPatientFromOtherClinic() {
                const params = {};
                try {
                    await CrmAPI.handlePatientExist(params);
                    // TODO CRM
                } catch (e) {
                    console.log(e);
                }
            },
            handleRegisterParams(registrationFormItem, visitSourceId, visitSourceFrom) {
                return {
                    ...this.medicalRecord,
                    couponPromotions: this.couponPromotions,
                    giftRulePromotions: this.giftRulePromotions,
                    patientCardPromotions: this.patientCardPromotions,
                    patientPointsInfo: this.patientPointsInfo,
                    pay: this.pay,
                    promotions: this.promotions,
                    registrationFormItem,
                    visitSourceId,
                    visitSourceFrom,
                    visitSourceRemark: this.visitSourceRemark,
                    revisitStatus: this.revisitStatus,
                };
            },
            async setRegistrationAuditStatus(registrationFormItem, visitSourceId, visitSourceFrom) {
                const registrationId = this.registrationSheetId;
                const data = await CrmAPI.setAuditStatus(registrationId, 2, this.handleRegisterParams(registrationFormItem, visitSourceId, visitSourceFrom));
                return data;
            },
            async create({
                patient,
                registrationFormItem,
                visitSourceId,
                visitSourceFrom,
                chargeAfterConfirm,
                threeDaysParams,
            }) {
                try {
                    const { data } = await RegistrationsAPI.registrationsManage({
                        ...this.medicalRecord,
                        ...threeDaysParams,
                        patient: {
                            id: patient.id,
                            name: patient.name,
                            sex: patient.sex,
                            age: patient.age,
                            mobile: patient.mobile,
                            birthday: patient.birthday,
                            tags: this.patientTags || [],
                        },
                        pay: this.pay,
                        registrationFormItem,
                        registrationType: this.registrationType,
                        promotions: this.promotions,
                        giftRulePromotions: this.giftRulePromotions,
                        patientCardPromotions: this.patientCardPromotions,
                        couponPromotions: this.couponPromotions,
                        patientPointsInfo: this.patientPointsInfo,
                        shebaoCardInfo: this.patient.shebaoCardInfo,
                        visitSourceId,
                        visitSourceFrom,
                        visitSourceRemark: this.visitSourceRemark,
                        revisitStatus: this.revisitStatus,
                        healthCard: this.patient.healthCard,
                    });
                    this.buttonLoading = false;
                    localStorage.setObj('reg_fee_delay', this._key, null);
                    this.patientOrderId = data?.chargeSheet?.patientOrderId;
                    const message = this.postData.isReserved ? '预约成功' : '挂号成功';
                    if (!this.supportShortFlow) {
                        this.$emit('finish-registration', {
                            before: this.postData,
                            after: data,
                            message,
                        });
                    } else {
                        this.registrationInfo = data;
                        this.chargeDialogData = await ChargeDialog.convertChargeSheet(data?.chargeSheet);
                        this.$Toast({
                            message,
                            type: 'success',
                        });
                        if (chargeAfterConfirm) {
                            // 确定并收费
                            this.$emit('refresh', data);
                            this.registrationIdByCreate = data.id;
                            await this.charge(true);
                            return;
                        }
                        this.$refs['appointment-card-footer'].updatePrintConfig();
                        await this.$nextTick();
                        await this.$refs['appointment-card-footer'].registrationSuccessCallback();
                        if (this.chargeDialogData.status === 2) {
                            const { chargeTransactions } = this.chargeDialogData;
                            const chargeTransactionId = chargeTransactions[0]?.id;
                            if (!chargeTransactionId) return;
                            await this.$refs['appointment-card-footer'].chargeSuccessCallback(chargeTransactionId);
                        }
                    }
                    this.$emit('refresh', data);
                    this.showAppointmentCard = false;
                } catch (e) {
                    this.buttonLoading = false;
                    const {
                        code,
                        message,
                    } = e;
                    if (code === 17005) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    } else if (code === 15123 || code === 15122 || code === 15125) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: message,
                        });
                    }
                    if (!patient?.id) {
                        Logger.report({
                            scene: 'registration_create_new_patient_error',
                            data: {
                                info: '新患者创建挂号预约异常',
                                data: {
                                    acceptFnParams: {
                                        patient,
                                        registrationFormItem,
                                        visitSourceId,
                                        visitSourceFrom,
                                        chargeAfterConfirm,
                                    },
                                    error: e,
                                },
                            },
                        });
                    }
                } finally {
                    this.noChargeButtonLoading = false;
                }
            },

            /**
             * 修改
             * @params threeDaysParams 三日复诊参数
             */
            async update(registrationFormItem, visitSourceId, visitSourceFrom, threeDaysParams) {
                const registrationId = this.registrationId || this.oldRegistrationId;
                let params = this.handleRegisterParams(registrationFormItem, visitSourceId, visitSourceFrom);
                if (threeDaysParams) {
                    params = {
                        ...params,
                        ...threeDaysParams,
                    };
                }
                const data = await RegistrationsAPI.updateRegistrations(registrationId, params);
                return data;
            },

            async handleRefundConfirm() {
                const {
                    payMode,
                    payModeName,
                    paySubMode,
                    paySubModeName,
                    amount,
                    thirdPartyPayCardId,
                    chargePayTransactionId,
                    transactionIds,
                } = this.chargeSheet?.chargeSheetSummary?.paymentSummaryInfos?.[0] || {};

                const params = {
                    adjustmentFee: 0,
                    chargeComment: '',
                    chargeForms: this.chargeSheet?.chargeForms || [],
                    combinedPayItems: [{
                        payMode,
                        payModeName,
                        paySubMode,
                        paySubModeName,
                        amount,
                        thirdPartyPayCardId,
                        chargePayTransactionId,
                        transactionIds,
                    }],
                    needRefundFee: 0,
                };
                try {
                    await ChargeAPI.refund(this.chargeSheet.id, params);
                    this.refundFinish();
                } catch (e) {
                    console.log('refund Error', e);
                    const {
                        code,
                        message,
                    } = e || {};
                    if (code === 17009) {
                        this.$Toast({ message });
                    }
                }
            },
            async handleRefund() {
                if (this.isDisabledRefundFeeOrNoBtn) return;
                if (this.$refs?.dropdownListWrapper) {
                    this.$refs.dropdownListWrapper.showPopper = false;
                }
                /**
                 * @desc 三种情况
                 * 0. 原价0元挂号费，退费新流程（左柚预约优化）
                 * 1. 完全收费退费，弹出退费 dialog
                 * 2. 部分收费，提示退费，调用 paidback 接口
                 * 3. 否则提示退号
                 * <AUTHOR>
                 * @date 2019/11/29 16:59:45
                 * @params
                 * @return
                 */
                const { chargeSheetSummary } = this.chargeSheet || {};
                // 0. 原价0元挂号费，退费新流程（左柚预约优化）
                if (chargeSheetSummary?.totalFee === 0 && chargeSheetSummary?.needPayFee === 0 && this.registration.payStatusV2 === PayStatusV2.PAID) {
                    const timeStr = this.isFixOrderMode ? `${this.registration.timeOfDay + (`${this.registration.orderNo}`).padStart(2,'0')}号` : `${this.registration.reserveTime.start}~${this.registration.reserveTime.end}`;
                    this.$modal({
                        title: `是否确认${this.isFixOrderMode ? '退号' : '取消预约'}?`,
                        content: () => (<div style=" display:flex;flex-direction: column;">
                            <div><label>患者：</label><span>{ this.patient.name || '' }</span><span style="margin-left: 8px;">{ this.patient.mobile || ''}</span></div>
                            <div><label>医生：</label><span>{ this.registration.doctorName || '不指定' }</span></div>
                            <div><label>时间：</label><span>{ formatDate(this.registration.reserveDate, 'YYYY-MM-DD')}</span><span style="margin-left: 8px;">{ timeStr }</span></div>
                        </div>),
                        contentStyles: {
                            width: '300px',
                        },
                        onConfirm: this.handleRefundConfirm,
                    });
                } else if (
                    this.registration.payStatusV2 === PayStatusV2.PAID ||
                    this.registration.payStatusV2 === PayStatusV2.PARTED_REFUNDED
                ) {
                    // 1. 完全收费退费，弹出退费 dialog
                    this.showRefund = true;
                } else if (this.registration.payStatusV2 === PayStatusV2.PARTED_PAID) {
                    // 2. 部分收费，提示退费，调用 paidback 接口
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: [
                            `已收费 <span>${this.$t('currencySymbol')} ${formatMoney(
                                this.chargeSheet.chargeSheetSummary.netIncomeFee,
                            )}</span>，确定要退费吗？`,
                        ],
                        onConfirm: this.openRefundDialog,
                    });
                } else {
                    // 3. 否则提示退号
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: '退号后不可恢复，是否继续？',
                        onConfirm: this.cancelReg,
                    });
                }
            },
            handleNewAdd() {
                this.newAdd(this.patient);
            },
            goFellowUpOuterChain() {
                const idCard = this.patient?.idCard;
                const url = `${this.revisitExternalLink}?idCardNo=${idCard}`;
                windowOpen(url);
            },
            async cancelReg() {
                try {
                    this.refundButtonLoading = true;
                    await RegistrationsAPI.cancel(this.registrationId);
                    this.refundButtonLoading = false;
                    this.showRefundWayList = false;

                    if (this.registration.payStatusV2 === PayStatusV2.NOT_PAID) {
                        this.$Toast({
                            message: '退号成功',
                            type: 'success',
                        });
                    } else {
                        this.$Toast({
                            message: '退费成功',
                            type: 'success',
                        });
                    }

                    this.$emit('refresh');
                    this.showAppointmentCard = false;
                    this.shebaoRegistrationRevoke();
                } catch (err) {
                    this.refundButtonLoading = false;
                }
            },
            /**
             * @desc 部分退费逻辑
             * <AUTHOR>
             * @date 2019-12-05 00:58:00
             */
            openRefundDialog() {
                this.refundTotalFee = this.chargeSheet.chargeSheetSummary.netIncomeFee; // 需要退的总费用
                this.curRefundType = RefundTypeEnum.REFUND_PAID; // 部分退费
                this.showRefundWayList = true;
            },
            /**
             * @desc 退费相关数据回调
             * <AUTHOR>
             * @date 2019-12-01 17:46:15
             * @params data {list, refund}
             */
            refundConfirm(data) {
                this.refundTotalFee = data.refundFee; // 需要退的总费用
                this.refundData = data;
                this.curRefundType = RefundTypeEnum.NORMAL; // 选择项目退费时
                this.showRefundWayList = true;
            },
            /**
             * @desc 完成退费
             * <AUTHOR>
             * @date 2019/12/01 18:19:25
             * @params
             * @return
             */
            refundFinish() {
                this.showRefund = false;
                this.showRefundWayList = false;
                // this.fetchDetail()
                this.$emit('refresh');
                this.showAppointmentCard = false;
                this.shebaoRegistrationRevoke();
            },
            /**
             * @desc 监听退费弹框关闭
             * <AUTHOR>
             * @date 2019/12/01 18:17:34
             * @params
             * @return
             */
            onChangeValue(visible) {
                if (!visible) {
                    this.fetchDetail();
                }
            },
            async setDefaultDoctorInfo() {
                if (this.doctorId) {
                    const currentDoctor = this.allDoctors.filter((item) => item.doctorId === this.doctorId);
                    if (currentDoctor.length === 1) {
                        const {
                            doctorId,
                            departmentId,
                            departmentName,
                        } = currentDoctor[0] || {};
                        this.postData.doctorId = doctorId || '';
                        this.postData.departmentId = departmentId || '';
                        const info = {
                            ...Clone(currentDoctor[0]),
                            departmentName: departmentName || '其他',
                        };
                        this.defaultDoctorInfo = Clone(info);
                        this.changeMRType(currentDoctor[0]);
                        await this.setPayFee();
                        if (this.isFixOrderMode && this.postData.reserveDate) {
                            await this.handleOrderNoList();
                        }
                        await this.calcFee();
                    } else {
                        let currentDepartmentDoctor = null;
                        // 匹配对应的科室
                        if (this.departmentId) {
                            currentDepartmentDoctor = currentDoctor.find((item) => item.departmentId === this.departmentId);
                        } else {
                            // 挂号，没有科室，就匹配唯一一个有排班的科室
                            if (!this.postData.isReserved && currentDoctor.filter((item) => item.isScheduled).length === 1) {
                                currentDepartmentDoctor = currentDoctor.find((item) => item.isScheduled);
                            }
                        }
                        if (currentDepartmentDoctor) {
                            const {
                                doctorId,
                                departmentId,
                                departmentName,
                            } = currentDepartmentDoctor || {};
                            this.postData.doctorId = doctorId || '';
                            this.postData.departmentId = departmentId || '';
                            const info = {
                                ...currentDepartmentDoctor,
                                departmentName: departmentName || '其他',
                            };
                            this.defaultDoctorInfo = Clone(info);
                            await this.selectDoctor(this.postData.doctorId, false);
                        } else {
                            this.defaultDoctorInfo = {
                                doctorId: this.doctorId,
                                doctorName: this.doctorName || '',
                                departmentId: this.departmentId,
                                departmentName: this.departmentName || '',
                            };
                            this.handleErrorDoctorInfo();
                        }
                    }
                } else {
                    // 如果只有一个其他科室并且有不指定医生，就默认其他-不指定医生； 否则就为空
                    if (this.departments?.length === 1 && this.notMustReserveEmployee) {
                        const departmentId = this.postData.departmentId = this.departments[0].departmentId || '';
                        const departmentName = this.postData.departmentName = this.departments[0].departmentName || '其他';
                        this.defaultDoctorInfo = {
                            doctorId: ANONYMOUS_DOCTOR_ID,
                            doctorName: '不指定医生',
                            departmentId,
                            departmentName,
                        };
                    } else {
                        this.defaultDoctorInfo = null;
                    }
                }
            },
            async handleErrorDoctorInfo() {
                this.$nextTick(() => {
                    const {
                        departmentId,
                        doctorId,
                    } = this.postData;
                    this.errorDoctorInfo.error = !departmentId || !doctorId;
                    this.errorDoctorInfo.message = `请选择${!departmentId && !doctorId || !doctorId ? '医生' : '科室'}`;
                });
            },
            /**
             * @desc 获取挂号详情
             * <AUTHOR>
             * @date 2019/08/26 17:03:24
             */
            async fetchDetail(isInit = true) {
                if (isInit) {
                    this.loading = true;
                }
                try {
                    const promiseList = [RegistrationsAPI.fetch(this.registrationId || this.registrationIdByCreate)];
                    if (this.isFixOrderMode) {
                        promiseList.push(this.fetchDoctorShifts({
                            registrationDoctorId: this.doctorId,
                            registrationDepartmentId: this.departmentId,
                            registrationReserveDate: this.date,
                            registrationIsReserved: this.isReserved,
                            registrationCategory: this.registrationCategory,
                        }));
                    }

                    if (this.doctorId && this.departmentId) {
                        promiseList.push(this.fetchDoctorRegistrationFeeByCategories({
                            doctorId: this.doctorId,
                            departmentId: this.departmentId,
                        }, false));
                    }

                    const [{ data }] = await Promise.all(promiseList);

                    this.referralSource = data?.referralSource;
                    Object.assign(this.patient, data.patient);
                    this.patientOrderId = data?.patientOrderId; // 预诊的数据没有收费单,从挂号单上获取patientOrderId
                    if (data.chargeSheet) {
                        const {
                            sourceUnitPrice,
                            expectedUnitPrice,
                        } = data.chargeSheet.chargeForms?.[0]?.chargeFormItems?.[0] || {};

                        this.sourceUnitPrice = sourceUnitPrice;
                        this.expectedUnitPrice = expectedUnitPrice;
                        this.chargeSheet = data.chargeSheet;

                        // 挂号后收费不能操作，不能操作的原因
                        this.isDisabledOperate = data.chargeSheet.isDisabledOperate;
                        this.disabledOperateReason = data.chargeSheet.disabledOperateReason;
                    }

                    this.pay.fee = data.registrationFormItem.fee;
                    this.pay.receivable = this.chargeSheet.chargeSheetSummary.receivableFee;
                    this.pay.memberId = this.chargeSheet.memberId;
                    this.pay.useMemberFlag = this.chargeSheet?.useMemberFlag || UseMemberFlagEnum.USE_DEFAULT;
                    data.registrationFormItem.doctorId = data.registrationFormItem.doctorId || ANONYMOUS_DOCTOR_ID;
                    const detailDoctorInfo = {
                        ...pick1(data.registrationFormItem, ['doctorId', 'departmentId', 'doctorName', 'departmentName']),
                    };
                    this.defaultDoctorInfo = {
                        ...detailDoctorInfo,
                        departmentName: detailDoctorInfo.departmentName || '其他',
                    };
                    Object.assign(this.registration, data.registrationFormItem);
                    Object.assign(this.postData, data.registrationFormItem);
                    this.cacheSelectedOrderNoTimeOfDay = this.selectedOrderNoTimeOfDay = this.postData.timeOfDay;
                    this.displayCreatedByName = data.registrationFormItem.displayCreatedByName;
                    this.displayCreatedTime = data.registrationFormItem.created;
                    this.cachePostData = Clone(this.postData);
                    if (this.isFixOrderMode) {
                        await this.getOrderNoAndTimeOptions(data.registrationFormItem.orderNo);
                        let currentNoInfo = {};
                        const {
                            start,
                            end,
                        } = data.registrationFormItem?.reserveTime || {};
                        const {
                            orderNo,
                            orderNoType,
                            timeOfDay,
                        } = data.registrationFormItem || {};
                        if (!this.isGenerateOrderNoOnSign) {
                            // 避免切换模式，时间跟现在的模式
                            currentNoInfo = this.orderNoAndTimeOptions?.find((item) => item.orderNo === orderNo && item.timeOfDay === timeOfDay);
                            this.registration.reserveTime.start = currentNoInfo?.start;
                            this.registration.reserveTime.end = currentNoInfo?.end;
                            this.orderNoAndTime = `${orderNo || ''}-${currentNoInfo?.start}-${currentNoInfo?.end}-${currentNoInfo?.type || 0}-${timeOfDay}`;
                        } else {
                            this.orderNoAndTime = `${orderNo || ''}-${start}-${end}-${orderNoType || 0}-${timeOfDay}`;
                        }
                        this.registrationTime = this.timeOptions.find((item) => item.time === timeOfDay)?.id || '';
                    } else {
                        this.handleTimeRangePickerOptions(this.timeRange);
                        this.registrationProductIds = data.registrationFormItem?.registrationProducts?.map((item) => item.id) || [];
                        this.reserveTimeRange = [data.registrationFormItem.reserveTime.start, data.registrationFormItem.reserveTime.end];
                    }
                    this.cloneVisitSourceRemark = this.visitSourceRemark = data.visitSourceRemark;
                    // ! 构建cascader初始化数据
                    this.visitSourceOption = RecommendService.getInstance().initCascaderValue({
                        visitSourceId: data.visitSourceId,
                        visitSourceName: null,
                        visitSourceFrom: data.visitSourceFrom,
                        visitSourceFromName: data.visitSourceFromName,
                    });
                    // 设置本次推荐 本次推荐和就诊来源不是一个对象，本次推荐只有在患者第一次就诊且患者信息中就诊来源为空的情况下会映射一个本次推荐到就诊来源中，其他时候这两者相互不影响
                    this.cloneVisitSourceOption = Clone(this.visitSourceOption);

                    this.preDiagnosisExtendInfo = pick1(data, [
                        'visitSourceId',
                        'visitSourceFrom',
                        'visitSourceFromName',
                        'visitRemark',
                        'extendFlag',
                    ]);

                    this.revisitStatus = data.revisitStatus;

                    this.registration.doctorName = this.registration.doctorName || '';

                    Object.assign(this.medicalRecord, {
                        pastHistory: data.pastHistory,
                        allergicHistory: data.allergicHistory,
                        physicalExamination: data.physicalExamination,
                        familyHistory: data.familyHistory,
                        personalHistory: data.personalHistory,
                        chiefComplaint: data.chiefComplaint,
                        presentHistory: data.presentHistory,
                        epidemiologicalHistory: data.epidemiologicalHistory,
                        dentistryExaminations: data.dentistryExaminations,
                        preDiagnosisAttachments: data.preDiagnosisAttachments,
                    });
                    this.changeMRType({ mainMedicalType: data.registrationFormItem.mainMedicalType });
                    const { memberInfo } = handleMemberInfo(this.chargeSheet?.memberInfo || {});
                    this.memberInfo = memberInfo;

                    this.promotions = Clone(this.chargeSheet.promotions || []);
                    this.giftRulePromotions = Clone(this.chargeSheet.giftRulePromotions || []);
                    this.patientCardPromotions = Clone(this.chargeSheet.patientCardPromotions || []);
                    this.couponPromotions = Clone(this.chargeSheet.couponPromotions || []);
                    this.patientPointsInfo = Clone(this.chargeSheet.patientPointsInfo || null);

                    const { chargeSheetSummary } = this.chargeSheet;
                    this.discountFee = chargeSheetSummary.discountFee;
                    this.refundOriginalPayMode = chargeSheetSummary.refundOriginalPayMode;
                    this.originPayMode = chargeSheetSummary.originPayMode;
                    this.originPaySubMode = chargeSheetSummary.originPaySubMode;

                    this.shebaoCardInfo = data.shebaoCardInfo;

                    // 设置是否锁单
                    this.getLockInfo({
                        remote: false,
                        lockList: data.chargeSheet.patientOrderLocks,
                    });
                    this.getSocialExceptionInfo(data.chargeSheet.id);

                    // 将返回的 chargeSheet 进行转换，用于传递给 ChargeDialog 使用
                    this.chargeDialogData = await ChargeDialog.convertChargeSheet(this.chargeSheet);
                    this.chargeDialogData.postData.patient = data.patient;
                } catch (e) {
                    console.log('fetchDetail error', e);
                } finally {
                    this.loading = false;
                }
            },
            cacheData() {
                this._medicalRecordCache = Object.assign({}, this.medicalRecord);
                this._cacheFee = this.pay.fee;
                this._memberInfoCache = Clone(this.memberInfo);
                this._promotionsCache = Clone(this.promotions);
                this._giftRulePromotionsCache = Clone(this.giftRulePromotions);
                this._couponPromotions = Clone(this.couponPromotions);
                this._patientPointsInfo = Clone(this.patientPointsInfo);
                this._revisitStatus = this.revisitStatus;
                this._patientCardPromotionsCache = Clone(this.patientCardPromotions);
                if (!this.isFixOrderMode) {
                    this.cacheRegistrationProductIds = Clone(this.registrationProductIds);
                    this.cacheReserveTimeRange = Clone(this.reserveTimeRange);
                }
            },
            // 用上次的推荐历史覆盖本次的历史
            coverNowVisitInfoByLastVisitHistoryVisit() {
                this.visitSourceRemark = this.visitHistoryInfo.visitSourceRemark;
                // ! 构建cascader初始化数据
                // 需要有推荐类型id和列表
                this.visitSourceOption = RecommendService.getInstance().initCascaderValue({
                    visitSourceId: this.visitHistoryInfo.visitSourceId || null,
                    visitSourceName: null,
                    visitSourceFrom: this.visitHistoryInfo.visitSourceFrom || null,
                    visitSourceFromName: this.visitHistoryInfo.visitSourceFromName || null,
                });
            },
            // 根据病患id获取上次本次推荐人信息
            async getMedicalRecommendationInfo(patientId) {
                // 非修改状态
                const params = {
                    patientId,
                };
                try {
                    const data = await RegistrationsAPI.getMedicalRecommendationInfo(params);
                    if (data) {
                        this.visitHistoryInfo = data;
                    }
                    this.needAutoFillReferral && this.coverNowVisitInfoByLastVisitHistoryVisit();
                } catch (e) {
                    console.log(e);
                }
            },
            /**
             * 拉取首诊来源数据
             * <AUTHOR>
             * @date 2020-06-29
             */
            async getListSource() {
                if (!RecommendService.getInstance().originOptions.length) {
                    await RecommendService.getInstance().structureOriginOptions();
                }
                this.patientSourceType = this.options = RecommendService.getInstance().cascaderOptions;
            },
            async fetchAllDoctor() {
                await RecommendService.getInstance().structureOriginOptions();
                this.patientSourceType = this.options = RecommendService.getInstance().cascaderOptions;
            },
            /**
             * @desc 修改预诊信息
             * <AUTHOR>
             * @date 2019/10/16 17:46:44
             * @params
             * @return
             */
            async changeMedicalRecord(medicalRecord) {
                try {
                    if (this.registrationId) {
                        await RegistrationsAPI.modifyPreDiagnosisInfo(this.registrationId, medicalRecord);
                    }
                    this.medicalRecord = medicalRecord;
                } catch (e) {
                    console.log(e);
                }
            },
            changeMRType(doctor) {
                if (doctor) {
                    const { mainMedicalType } = doctor;
                    let type = MedicalRecordTypeEnum.WESTERN;
                    if (mainMedicalType === 2) {
                        type = MedicalRecordTypeEnum.CHINESE;
                    } else if (mainMedicalType === 3) {
                        type = MedicalRecordTypeEnum.ORAL;
                    } else if (mainMedicalType === 4) {
                        type = MedicalRecordTypeEnum.OPHTHALMOLOGY;
                    }
                    this.medicalRecord.type = type;
                }
            },

            /**
             * @desc 签到
             * <AUTHOR>
             * @date 2019/09/23 16:22:08
             * @params
             * @return
             */
            signIn() {
                if (this.registration.isSignTime) {
                    this.signInAllocation();
                } else {
                    this.$confirm({
                        type: 'warn',
                        title: '提示',
                        content: `患者预约时间（${this.registration.reserveDate}）不是今天。签到后会更改预约就诊时间，是否确定`,
                        onConfirm: () => {
                            this.signInAllocation();
                        },
                    });
                }
            },
            // 有咨询师功能
            signInAllocation() {
                if (this.showConsultant) {
                    this.openRegistrationConsultantDialog();
                    return;
                }
                this.signInSubmit();
            },
            openRegistrationConsultantDialog() {
                let time = `${formatDate(new Date(this.registration.reserveDate), 'YYYY-MM-DD')} ${this.registration.reserveTime.start}~${this.registration.reserveTime.end}`;
                if (this.isFixOrderMode) {
                    let orderNo = '';
                    // 签到后确认号源的模式下这步无法确认号源
                    if (this.registration.orderNo) {
                        orderNo = ` ${this.registration.orderNo}号`;
                    }
                    time = `${formatDate(new Date(this.registration.reserveDate), 'YYYY-MM-DD')}${orderNo} ${this.registration.reserveTime.start}~${this.registration.reserveTime.end}`;
                }
                Object.assign(this.doctorConsultantObject, {
                    time,
                    doctorId: this.registration.doctorId,
                    departmentId: this.registration.departmentId,
                    consultantId: this.registration.consultantId || this.patientInfo.consultantId || '',
                    reserveDate: formatDate(new Date(this.registration.reserveDate), 'YYYY-MM-DD'),
                    reserveTime: this.registration.reserveTime,
                });
                new AllocationDoctorConsultantDialog({
                    value: true,
                    needSign: true,
                    info: this.doctorConsultantObject,
                    consultants: this.consultantList,
                    registrationId: this.registrationId,
                    registrationSign: this.registration,
                    patientId: this.patient.id,
                    finishRegistrationFunction: this.finishRegistrationFunction,
                    refresh: this.refresh,
                }).generateDialogAsync({
                    parent: this,
                });
                this.$nextTick(() => {
                    this.showAppointmentCard = false;
                });
            },
            // 签到方法
            async signInSubmit() {
                try {
                    this.signInBtnLoading = true;
                    const { data } = await RegistrationsAPI.registrationSignin(this.registrationId);
                    this.$emit('finish-registration', {
                        before: this.registration,
                        after: data,
                        message: '签到成功',
                    });
                    this.$emit('refresh', false);
                    this.showAppointmentCard = false;
                } catch (err) {
                    console.error(err);
                    this.$Toast({
                        message: '签到失败',
                        type: 'error',
                    });
                } finally {
                    this.signInBtnLoading = false;
                }
            },
            // 获取本次推荐信息
            getVisitSourceInfo() {
                let visitSourceId = null, visitSourceFrom = null;

                // 如果没有来源选项，清空来源备注
                if (this.visitSourceOption.length) {
                    if (this.visitSourceOption.length > 2) {
                        visitSourceId = this.visitSourceOption[this.visitSourceOption.length - 2].value;
                        visitSourceFrom = this.visitSourceOption[this.visitSourceOption.length - 1] ? this.visitSourceOption[this.visitSourceOption.length - 1].value : null;
                    } else if (['顾客推荐', '员工推荐', '医生推荐', '转诊医生'].includes(this.visitSourceOption[0].label)) {
                        visitSourceId = this.visitSourceOption[0].value;
                        visitSourceFrom = this.visitSourceOption[1] ? this.visitSourceOption[1].value : null;
                    } else {
                        visitSourceId = this.visitSourceOption[this.visitSourceOption.length - 1].value;
                    }
                }
                return {
                    visitSourceId,
                    visitSourceFrom,
                };
            },

            inputFee(val) {
                this.expectedUnitPrice = val;
                this._calcFee();
            },

            async calcFee() {
                if (this.disabledForm || this.disabledEditCharge || this.isFieldLayout) {
                    return;
                }
                this.buttonLoading = true;
                try {
                    // 如果选择就诊后收费，不需要计算折扣
                    const { data } = await ChargeAPI.calcFee(
                        {
                            chargeSheetId: this.chargeSheet.id,
                            payType: 0,
                            memberId: this.pay.memberId,
                            chargeForms: this.chargeForms,
                            promotions: this.promotions,
                            giftRulePromotions: this.giftRulePromotions,
                            couponPromotions: this.couponPromotions,
                            patientPointsInfo: this.patientPointsInfo,
                            patientCardPromotions: this.patientCardPromotions,
                            patientId: this.patient.id,
                            useMemberFlag: this.pay.useMemberFlag,
                        },
                        false,
                    );
                    this.promotions = data.promotions || [];
                    this.giftRulePromotions = data.giftRulePromotions || [];
                    this.couponPromotions = data.couponPromotions || [];
                    this.patientCardPromotions = data.patientCardPromotions || [];
                    this.patientPointsInfo = data.patientPointsInfo || null;
                    this.pay.receivable = data.receivableFee;
                    this.discountFee = data.discountFee;
                    this.chargeDialogData.chargeSheetSummary = data;
                    this.chargeDialogData.postData.promotions = data.promotions || [];
                    this.chargeDialogData.postData.giftRulePromotions = data.giftRulePromotions || [];
                    this.chargeDialogData.postData.couponPromotions = data.couponPromotions || [];
                    this.chargeDialogData.postData.patientPointsInfo = data.patientPointsInfo || null;
                    this.chargeDialogData.postData.patientCardPromotions = data.patientCardPromotions || [];
                    const {
                        memberId,
                        memberInfo,
                    } = handleMemberInfo(data?.memberInfo || {});
                    this.pay.memberId = memberId;
                    this.memberInfo = memberInfo;
                } catch (e) {
                    console.log('calcFee error');
                } finally {
                    this.buttonLoading = false;
                }
            },
            // 获取科室医生排班表
            async fetchDoctorShifts(params = {}) {
                const {
                    registrationDoctorId,
                    registrationDepartmentId,
                    registrationReserveDate,
                    registrationIsReserved,
                    registrationCategory,
                } = params;

                try {
                    const reserveDate = registrationReserveDate || this.postData.reserveDate;
                    const isReserved = registrationIsReserved || this.postData.isReserved;
                    const departmentId = registrationDepartmentId || this.postData.departmentId || '';
                    const doctorId = registrationDoctorId || this.postData.doctorId || ANONYMOUS_DOCTOR_ID;
                    const reserveCategory = registrationCategory ?? this.postData.registrationCategory;

                    if (!departmentId) return;
                    if (reserveDate) {
                        const params = {
                            doctorId,
                            departmentId,
                            workingDate: reserveDate,
                            forNormalRegistration: isReserved === 1 ? 0 : 1,
                            registrationType: this.registrationType,
                            registrationCategory: reserveCategory,
                        };
                        const { data } = await RegistrationsAPI.fetchDoctorShiftsByDate(params);

                        const registrationCategoryScheduleIntervals = data?.registrationCategoryScheduleIntervals || [];
                        this.doctorNoInfoList = registrationCategoryScheduleIntervals.find((item) => item.registrationCategory === this.postData.registrationCategory)?.scheduleIntervals || [];
                        return this.doctorNoInfoList;
                    }
                } catch (e) {
                    console.error('fetchData', e);
                }
            },
            clickOrderNoAndTimeOption(item) {
                const { signInTimeFirstEnableUseOrderNo } = item;
                this.isGenerateOrderNoOnSignDisplayNoStr = signInTimeFirstEnableUseOrderNo ? `${(`${signInTimeFirstEnableUseOrderNo}`).padStart(2, '0')}` : '';
            },
            handleConfirmOrder(order) {
                const {
                    orderNo,
                    start,
                    end,
                    type,
                    timeOfDay,
                } = order || {};

                this.orderNoAndTime = `${orderNo || ''}-${start}-${end}-${type}-${timeOfDay}`;
                this.postData.timeOfDay = timeOfDay;
                this.registrationTime = this.timeOptions.find((item) => item.time === this.postData.timeOfDay)?.id || '';
            },
            handleChangeReserveTimeRange(val) {
                this.postData.reserveTime.start = val[0];
                this.postData.reserveTime.end = val[1];
            },
            // TODO 取号逻辑
            async getOrderNoAndTimeOptions(orderNo = '') {
                let timeOfDayList = [];
                let currentTimeList = [];
                const timeOptions = ['上午', '下午', '晚上'];
                timeOfDayList = timeOptions.map((timeOfDay) => {
                    const doctorNoInfoList = Clone(this.doctorNoInfoList);
                    const timeOfDayArr = doctorNoInfoList?.filter((item) => item.timeOfDay === timeOfDay) || [];
                    // 根据当前选择时段 判断是否停诊 处理展示已停诊信息
                    if (timeOfDay === this.postData.timeOfDay) {
                        if (timeOfDayArr.length > 0) {
                            this.isStopDiagnose = timeOfDayArr.every((item) => item.isStopDiagnose === 1);
                        } else {
                            this.isStopDiagnose = false;
                        }
                    }
                    let allNoList = [];
                    // 签到取号
                    if (this.isGenerateOrderNoOnSign) {
                        timeOfDayArr.forEach((item) => {
                            if (item.availableCellTypes?.length) {
                                const availableList = item.list.filter((it) => it.available);
                                const normalNoCount = availableList.filter((it) => !it.type).length;
                                const xianNoCount = availableList.filter((it) => it.type === 1).length;
                                const vipNoCount = availableList.filter((it) => it.type === 2).length;
                                item.availableCellTypes.forEach((type) => {
                                    allNoList.push({
                                        orderNo: '',
                                        start: item.start,
                                        end: item.end,
                                        timeOfDay: item.timeOfDay,
                                        type,
                                        signInTimeFirstEnableUseOrderNo: item.signInTimeFirstEnableUseOrderNo,
                                        residueCount: [normalNoCount, xianNoCount, vipNoCount][type],
                                    });
                                });
                            }
                        });
                        return allNoList;
                    }
                    if (this.isAccurateTime) {
                        allNoList = timeOfDayArr.map((item) => item.list).flat();
                    } else {
                        allNoList = timeOfDayArr.map((item) => {
                            item.list?.forEach((it) => {
                                it.orderNoStart = it.start;
                                it.orderNoEnd = it.end;
                                it.start = item.start;
                                it.end = item.end;
                            });
                            return item.list;
                        }).flat();
                    }
                    return allNoList?.filter((item) => item.available || item.orderNo === orderNo && item.timeOfDay === this.postData.timeOfDay) || [];
                });
                if (this.registration.isReserved && !this.postData.timeOfDay) {
                    // 初始化预约
                    let index = 0;
                    for (let i = 0; i < timeOfDayList.length; i++) {
                        if (timeOfDayList[i].length) {
                            index = i;
                            break;
                        }
                    }
                    this.registrationTime = this.timeOptions[index].id;
                    this.postData.timeOfDay = timeOptions[index];
                    currentTimeList = timeOfDayList[index];
                } else {
                    const index = timeOptions.findIndex((item) => item === this.postData.timeOfDay);
                    currentTimeList = timeOfDayList[index] ?? [];
                }
                this.orderNoPlaceholderStr = '';

                // 是否是算出来的号 -- 非签到取号模式
                if (!this.isGenerateOrderNoOnSign) {
                    const isCalcOrderNo = currentTimeList?.findIndex((order) => order.orderNo === orderNo) === -1;
                    if (orderNo && isCalcOrderNo) {
                        currentTimeList.push({
                            orderNo,
                            start: this.registration.reserveTime.start,
                            end: this.registration.reserveTime.end,
                            orderNoStart: this.registration.reserveTime.start,
                            orderNoEnd: this.registration.reserveTime.end,
                            type: 0,
                            timeOfDay: this.registration.timeOfDay,
                        });
                    }
                }

                const normalList = currentTimeList.filter((item) => !item.type);
                const reserveList = currentTimeList.filter((item) => [1, 2].includes(item.type));
                // 挂号
                if (!this.postData.isReserved) {
                    // 挂号默认出号逻辑-优先展示非会员和预留号 (type: 2-会员 1-预留 0-其他)
                    if (normalList.length) {
                        if (this.postData.reserveDate && !this.orderNoAndTime) {
                            this.orderNoAndTime = `${normalList[0].orderNo}-${normalList[0].start}-${normalList[0].end}-${normalList.type || 0}-${normalList[0].timeOfDay}`;
                        }
                        this.orderNoAndTimeOptions = currentTimeList;
                        this.calculateOrderNo = null;
                    } else if (reserveList.length && this.postData.reserveDate && this.orderNo) {
                        if (!this.orderNoAndTime) {
                            this.orderNoAndTime = `${reserveList[0].orderNo}-${reserveList[0].start}-${reserveList[0].end}--${reserveList[0].type}-${reserveList[0].timeOfDay}`;
                        }
                        this.orderNoAndTimeOptions = reserveList;
                        this.calculateOrderNo = null;
                    } else {
                        // 没有选择上午、下午、晚上 return
                        if (!this.postData.timeOfDay) return;
                        if (this.postData.reserveTime.start === '' || this.postData.reserveTime.end === '') {
                            for (let i = 0; i < this.timeOptions.length; i++) {
                                const option = this.timeOptions[i];
                                if (option.time === this.postData.timeOfDay) {
                                    this.postData.reserveTime.start = option.value.start;
                                    this.postData.reserveTime.end = option.value.end;
                                    break;
                                }
                            }
                        }
                        const cacheDoctorNoInfoList = Clone(this.doctorNoInfoList);
                        const listByDay = cacheDoctorNoInfoList.find((item) => item.timeOfDay === this.postData.timeOfDay);
                        if (!this.unscheduledCanRegistration && !listByDay && !this.registrationCategoryIsConvenience) {
                            // 如果未排班不可挂号, 且当前时间段内未排班, 则不算号
                            return;
                        }

                        // 便民门诊处理
                        if (!this.unscheduledCanRegistration && this.registrationCategoryIsConvenience) {
                            const currentSelectedDoctor = this.doctors.find((item) => item.doctorId === this.postData.doctorId);
                            const hasConvenienceSchedule = currentSelectedDoctor?.scheduledRegistrationCategories?.includes(RegistrationCategory.CONVENIENCE);
                            const timeOfDayHasSchedule = this.doctorNoInfoList.some((item) => item.timeOfDay === this.postData.timeOfDay);

                            if (!hasConvenienceSchedule || !timeOfDayHasSchedule) {
                                return;
                            }
                        }

                        await this.calcOrderNo();
                        this.$nextTick(() => {
                            if (this.calculateOrderNo) {
                                const {
                                    orderNo = '',
                                    reserveTime = {
                                        start: '',
                                        end: '',
                                        orderNoStart: '',
                                        orderNoEnd: '',
                                    },
                                    type = 0,
                                    timeOfDay = '',
                                } = this.calculateOrderNo;

                                const orderNoNum = !this.isGenerateOrderNoOnSign ? orderNo : '';

                                if (this.postData.reserveDate) {
                                    this.orderNoAndTime = `${orderNoNum}-${reserveTime.start}-${reserveTime.end}-${type}-${timeOfDay}`;
                                }
                                this.orderNoAndTimeOptions = [{
                                    orderNo: orderNoNum,
                                    start: reserveTime.start,
                                    end: reserveTime.end,
                                    orderNoStart: reserveTime.orderNoStart,
                                    orderNoEnd: reserveTime.orderNoEnd,
                                    type: 0,
                                    timeOfDay,
                                    signInTimeFirstEnableUseOrderNo: orderNo,
                                }].concat(reserveList).sort((a, b) => +a.orderNo - +b.orderNo);
                            }
                        });
                    }

                    this.$nextTick(() => {
                        if (this.showDescription) {
                            const arr = this.orderNoAndTime?.split('-') || [];
                            const selectedItem = this.orderNoAndTimeOptions.find((item) => item.start === arr[1] && item.end === arr[2] && +item.type === +arr[3]);

                            if (selectedItem) {
                                this.isGenerateOrderNoOnSignDisplayNoStr = selectedItem.signInTimeFirstEnableUseOrderNo ? `${(`${selectedItem.signInTimeFirstEnableUseOrderNo}`).padStart(2, '0')}` : '';
                            }
                        }
                    });
                } else {
                    if (currentTimeList.length) {
                        if (!this.orderNoAndTime) {
                            this.orderNoAndTime = `${currentTimeList[0].orderNo}-${currentTimeList[0].start}-${currentTimeList[0].end}-${currentTimeList[0].type}-${currentTimeList[0].timeOfDay}`;
                        }
                        this.orderNoAndTimeOptions = currentTimeList;
                    } else {
                        this.calculateOrderNo = null;
                        this.orderNoPlaceholderStr = '无可用号源';
                        this.orderNoAndTimeOptions = [];
                    }
                }
            },
            async handleOrderNoList() {
                await this.fetchDoctorShifts();
                await this.getOrderNoAndTimeOptions();
            },
            // 更换号种
            async changeCategory(value,index, oldVal) {
                if (value === oldVal) return;
                if (this.isFixOrderMode) {
                    this.orderNoAndTime = '';
                    this.postData.isAdditional = false;
                    this.orderNoAndTimeOptions = [];
                }
                // 允许修改价格
                if (!this.disabledEditCharge) {
                    this.setPayFeeByDoctorFee();
                }

                try {
                    await Promise.all([
                        this._calcFee(),
                        this.isFixOrderMode ? this.handleOrderNoList() : this.getFlexibleTimeCount(),
                        this.getDailyReserveStatus(),
                    ]);
                    !this.isFixOrderMode && this.handleTimeRangePickerOptions();
                } catch (e) {
                    console.log('changeCategory error', e);
                }
            },
            async handleRevisitChange(value,index, oldVal) {
                if (value === oldVal) return;
                if (!this.isFixOrderMode && !(this.registrationId || this.oldRegistrationId)) {
                    this.handleTimeRangePickerOptions(this.reserveTimeRange,true);
                }
                // 允许修改价格
                if (!this.disabledEditCharge) {
                    // 号种列表变更，默认选择普通门诊
                    if (!this.doctorEnableCategories.includes(this.postData.registrationCategory)) {
                        this.postData.registrationCategory = RegistrationCategory.ORDINARY;
                    }
                    // 挂号费第三种模式，费用与初复诊无关
                    const doctorFee = this.doctorRegistrationFees.find((item) => item.registrationCategory === this.postData.registrationCategory);
                    if (doctorFee?.isDiffForRevisited === REGISTERED_FEE_MODE_TYPE.SHORT_DIFFERENT) {
                        return;
                    }
                    this.setPayFeeByDoctorFee();
                }

                this._calcFee();
            },

            /**
             * @desc 预约号数
             * <AUTHOR>
             * @date 2019/10/24 23:59:59
             * @params
             * @return
             */
            async calcOrderNo() {
                try {
                    const {
                        reserveTime,
                        reserveDate,
                    } = this.postData;
                    // 没有填时间就不请求 / 预约不去算号
                    if (!reserveDate || this.postData.isReserved) {
                        this.calculateOrderNo = null;
                        this.orderNoPlaceholderStr = '无可用号源';
                        return;
                    }
                    const { data } = await RegistrationsAPI.calcOrderNo({
                        registrationFormItem: {
                            departmentId: this.postData.departmentId || '',
                            doctorId: this.postData.doctorId || ANONYMOUS_DOCTOR_ID,
                            reserveTime,
                            reserveDate,
                            isReserved: this.postData.isReserved,
                            registrationCategory: this.postData.registrationCategory,
                        },
                        registrationType: this.registrationType,
                    });
                    this.postData.isAdditional = true;
                    this.calculateOrderNo = {
                        ...data,
                        type: 0,
                    };
                } catch (err) {
                    this.calculateOrderNo = null;
                    // 无可用号源
                    if (err?.code === 15125) {
                        this.orderNoPlaceholderStr = '无可用号源';
                    } else {
                        this.$Toast({
                            type: 'error',
                            message: err.message,
                        });
                        this.orderNoPlaceholderStr = '无可用号源';
                    }
                }
            },

            resetReserveDate() {
                if (this.isFixOrderMode) {
                    this.orderNoAndTime = '';
                    this.postData.isAdditional = false;
                    this.orderNoAndTimeOptions = [];
                    this.doctorNoInfoList = [];
                    if (this.postData.isReserved) {
                        // 预约
                        this.postData.reserveTime = {
                            start: '',
                            end: '',
                        };
                        this.postData.orderNo = '';
                        this.postData.reserveDate = '';
                    } else {
                        // 挂号
                        const timeOptionId = this.registrationTime;
                        for (let i = 0; i < this.timeOptions.length; i++) {
                            const option = this.timeOptions[i];
                            if (option.id === timeOptionId) {
                                this.postData.timeOfDay = option.time;
                                this.postData.reserveTime.start = option.value.start;
                                this.postData.reserveTime.end = option.value.end;
                                break;
                            }
                        }
                        this.postData.reserveDate = formatDate(new Date(), 'YYYY-MM-DD');
                    }
                    // 计算的号数也要置空
                    this.calculateOrderNo = null;
                }
            },
            async print(selected) {
                switch (selected[0]) {
                    case '挂号小票': {
                        this.$nextTick(async () => {
                            this.printLoading = true;
                            await this.handleRegistrationPrint(this.patientOrderId);
                            this.printLoading = false;
                        });
                        break;
                    }
                    case '医疗收费清单':
                        await this.printBillFeeListHandler();
                        break;
                    case '患者标签':
                        this.$nextTick(async () => {
                            this.printLoading = true;
                            await this.handlePatientTagPrint(this.patientOrderId);
                            this.printLoading = false;
                        });
                        break;
                    default:
                        console.error('不支持该打印类型', selected);
                        break;
                }


            },
            async printBillFeeListHandler() {
                try {
                    // 获取医疗收费清单项目
                    const { data } = await ChargeAPI.fetchChargePrint(this.chargeDialogData.id);
                    const { format } = this.printMedicalListConfig;
                    if (!format) {
                        return console.error('没有format，无法挂载AbcPint');
                    }
                    const templateName = `medicalFeeList${format[0].toUpperCase()}${format.slice(1)}`;
                    console.log(`正在初始化模板:[${templateName}]`);
                    await AbcPrinter.abcPrint({
                        templateKey: window.AbcPackages.AbcTemplates[templateName],
                        printConfigKey: ABCPrintConfigKeyMap.feeList,
                        data,
                        extra: {
                            $abcSocialSecurity: this.$abcSocialSecurity,
                        },
                        isDevTools: false,
                    });
                } catch (e) {
                    console.error('打印医疗收费清单报错', e);
                }
            },


            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'registration' }).generateDialogAsync({ parent: this });
            },

            enterEvent(e, el, type) {
                // 中文输入但是未选择 autocomplete 选项
                if (e.keyCode !== 13) return;
                // 找到所有的非disabled的input输入框
                const inputs = $('.appointment-base-card-form .abc-input__inner').not(':disabled');
                let targetIndex = -1;
                const eventPath = e.path || (e.composedPath && e.composedPath());
                let isProductsList = false;
                let isDoctorList = false;
                let fromTimeRange = false;
                if (eventPath) {
                    const eventPathClassNameList = eventPath.map((item) => item.className);
                    isDoctorList = eventPathClassNameList.some((item) => (`${item}`).includes('doctor-list'));
                    isProductsList = eventPathClassNameList.some((item) => (`${item}`).includes('products-custom-class'));
                    fromTimeRange = eventPathClassNameList.some((item) => (`${item}`).includes('time-range-picker'));
                }
                if (fromTimeRange) {
                    // 时间范围组件下一个是项目则在跳到下一个
                    targetIndex = inputs.index(e.target);
                    if (this.showReserveProduct) {
                        targetIndex += 1;
                    }
                } else if (e.target.className === 'select-inner-input') {
                    let currenIndex = -1;
                    inputs?.toArray()?.forEach((item, index) => {
                        if (isDoctorList && item?.parentNode?.className?.includes('doctor-list-select')) {
                            currenIndex = index;
                        }
                        if (isProductsList && item?.parentNode?.className?.includes('products-list-select')) {
                            currenIndex = index;
                        }
                    });
                    if (isProductsList) {
                        if (type === 'option') return;
                        targetIndex = currenIndex;
                    }
                    targetIndex = currenIndex;
                } else {
                    targetIndex = inputs.index(e.target);
                }

                let nextInput = inputs[targetIndex + 1];

                if (nextInput?.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }

                nextInput && this.$nextTick(() => {
                    nextInput.focus();
                });
            },
            handleConsultantSearch(key) {
                this.consultantSearchKey = key;
            },
            changeConsultantName() {
                this.postData.consultantName = this.consultantOptions?.find((item) => {
                    return item.employeeId === this.postData.consultantId;
                })?.employeeName;
            },
            async setDoctorFee(params) {
                const {
                    doctorId,
                    departmentId,
                } = params;
                if (!doctorId || !departmentId) {
                    this.doctorEnableCategories = Clone(this.defaultEnableCategories);
                    this.doctorRegistrationFees = Clone(this.defaultRegistrationFees);
                    return;
                }

                try {
                    await this.fetchDoctorRegistrationFeeByCategories(params);
                } catch (e) {
                    console.log('setDoctorFeeError', e);
                }
            },
            async fetchDoctorRegistrationFeeByCategories(params, needChangeRegistrationCategory = true) {
                try {
                    const { data } = await SettingAPI.registeredFee.loadDoctorRegistrationFeeByCategories(params);
                    this.doctorEnableCategories = data.enableCategories || [];
                    this.doctorRegistrationFees = data?.registrationFees || [];

                    // 默认选择普通挂号
                    if (needChangeRegistrationCategory && !this.doctorEnableCategories.includes(this.postData.registrationCategory)) {
                        this.postData.registrationCategory = RegistrationCategory.ORDINARY;
                    }
                } catch (e) {
                    console.log('fetchDoctorRegistrationFeeByCategoriesError', e);
                }
            },
            async changeVisitStatus(params) {
                const {
                    patientId,
                    doctorId,
                } = params;
                if (!patientId || !doctorId) {
                    this.revisitStatus = RevisitStatus.FIRST;
                    this.lastDiagnosedTime = null;
                    return;
                }

                try {
                    const { data } = await OutpatientAPI.getPatientVisitStatus(params);
                    this.revisitStatus = data.revisitStatus;
                    this.lastDiagnosedTime = data.lastDiagnosedTime;
                    this.needAutoFillReferral && this.coverNowVisitInfoByLastVisitHistoryVisit();
                } catch (e) {
                    console.log('getPatientVisitStatus error');
                }
            },

            async setPayFee() {
                await this.setDoctorFee({
                    ...pick1(this.postData, ['doctorId', 'departmentId']),
                });

                await this.changeVisitStatus({
                    doctorId: this.postData.doctorId,
                    patientId: this.patient.id,
                });

                if (this.disabledEditCharge) {
                    return;
                }

                this.setPayFeeByDoctorFee();
            },
            async fetchReservationList(doctorId) {
                try {
                    const { data } = await ReservationAPI.fetchReservationList({
                        employeeId: doctorId || ANONYMOUS_DOCTOR_ID,
                        registrationType: this.registrationType,
                        keywords: '',
                    });
                    this.reservationProductList = data.rows || [];
                } catch (e) {
                    console.log('fetch reservationProductList Error');
                }
            },
            /**
             * @desc 过滤预约项目
             */
            handleSearchProducts(key) {
                this.productSearchKey = key.trim();
            },
            /**
             * @desc 选择医生后，还要填入挂号费
             * <AUTHOR>
             * @date 2019/08/26 15:20:10
             */
            async selectDoctor(doctorId, clearOrderNo = false) {
                if (clearOrderNo) {
                    // 切换医生需要清除预约时间
                    this.resetReserveDate();
                }

                const doctor = this.doctors.find((o) => {
                    return o.doctorId === doctorId;
                });
                this.changeMRType(doctor);

                this.postData.doctorName = doctor ? doctor.doctorName : '';

                if (!this.disabledEditCharge) {
                    if (doctor) {
                        await this.setPayFee();
                    } else {
                        this.doctorEnableCategories = Clone(this.defaultEnableCategories);
                        this.doctorRegistrationFees = Clone(this.defaultRegistrationFees);
                        this.setPayFeeByDoctorFee();
                    }
                }

                if (this.isFixOrderMode && this.postData.reserveDate) {
                    await this.handleOrderNoList();
                }
                await this.calcFee();
            },
            async handleSelectDoctor(val, oldDoctorId = '', clearOrderNo) {
                this.isChangeDoctorInfo = true;
                await this.selectDoctor(val, clearOrderNo);
                if (!this.isFixOrderMode) {
                    if (val !== oldDoctorId) {
                        this.reservationProductList = [];
                        this.registrationProductIds = [];
                        this.curDoctorNotExistList = [];
                    }
                    if (!this.isDesignatedTimeRange) {
                        await this.getFlexibleTimeCount();
                        this.handleTimeRangePickerOptions();
                    }
                    this.showReserveProduct && await this.fetchReservationList(val);
                }
                await this.getDailyReserveStatus();
            },
            handleSelectedClear() {
                const oldDoctorId = this.postData.doctorId;
                this.postData.doctorId = '';
                this.postData.departmentId = '';
                this.describeList = [];
                this.$nextTick(() => {
                    this.errorDoctorInfo = {
                        error: true,
                        message: '请选择医生',
                    };
                });
                this.handleSelectDoctor(this.postData.doctorId, oldDoctorId, true);
            },
            handleDoctorSelectedChange(doctor) {
                const {
                    doctorId,
                    departmentId,
                } = doctor || {};
                const oldDoctorId = this.postData.doctorId;
                this.postData.doctorId = doctorId || '';
                this.postData.departmentId = departmentId || '';
                this.handleErrorDoctorInfo();
                this.handleSelectDoctor(this.postData.doctorId, oldDoctorId, true);
            },
            handleSelectedOrderConfirm(selectedOrderInfo) {
                const {
                    doctorInfo,
                    timeOfDay,
                    orderNo,
                    start,
                    end,
                    type,
                    workingDate,
                } = selectedOrderInfo || {};
                const {
                    doctorId,
                    departmentId,
                } = doctorInfo || {};
                this.postData.isReserved = 1;
                const oldDoctorId = this.postData.doctorId;
                this.postData.doctorId = doctorId || '';
                this.postData.departmentId = departmentId || '';
                this.postData.timeOfDay = timeOfDay;
                this.registrationTime = this.timeOptions.find((item) => item.time === this.postData.timeOfDay)?.id || '';
                this.orderNoAndTime = `${orderNo || ''}-${start}-${end}-${type}-${timeOfDay}`;
                this.postData.reserveDate = workingDate;
                this.calculateOrderNo = null;
                this.handleErrorDoctorInfo();
                this.handleSelectDoctor(this.postData.doctorId, oldDoctorId, false);
            },
            async handleSelectedTimeRangeConfirm(timeRangeInfo) {
                const {
                    doctorInfo,
                    workingDate,
                    timeRange,
                    registrationCount,
                    timeRangePickerOptions,
                } = timeRangeInfo || {};
                const {
                    doctorId,
                    departmentId,
                } = doctorInfo || {};
                this.reserveTimeRange = timeRange || [];
                this.postData.reserveTime.start = timeRange?.[0];
                this.postData.reserveTime.end = timeRange?.[1];
                this.postData.isReserved = 1;
                const oldDoctorId = this.postData.doctorId;
                this.postData.doctorId = doctorId || '';
                this.postData.departmentId = departmentId || '';
                this.isDesignatedTimeRange = true;
                this.registrationCount = registrationCount;
                this.timeRangePickerOptions = timeRangePickerOptions;
                this.postData.reserveDate = workingDate;
                this.handleErrorDoctorInfo();
                await this.handleSelectDoctor(this.postData.doctorId, oldDoctorId, false);
                this.isDesignatedTimeRange = false;
            },
            async getDailyReserveStatus(date = new Date()) {
                try {
                    if (!this.postData.isReserved) return;
                    const year = date.getFullYear();
                    const month = date.getMonth();
                    const monthStartDate = new Date(year, month, 1);
                    const monthEndDate = new Date(year, month + 1, 0);
                    const data = await BoardApi.getDailyReserveStatus({
                        departmentId: this.postData.departmentId || '',
                        doctorId: this.postData.doctorId || ANONYMOUS_DOCTOR_ID,
                        start: formatDate(monthStartDate, 'YYYY-MM-DD'),
                        end: formatDate(monthEndDate, 'YYYY-MM-DD'),
                        registrationType: this.registrationType,
                        isRevisited: this.postData.isRevisited,
                        registrationCategory: this.postData.registrationCategory,
                    });
                    this.describeList = data?.map((item) => {
                        if (item.status !== 5 && item.date >= this.todayTime) {
                            const describeClass = item.status === 6 ? 'describe-stop-work' : ''; // 停诊
                            return ({
                                date: item.date,
                                describe: item.statusName,
                                describeClass,
                            });
                        }
                    })?.filter(Boolean);
                } catch (e) {
                    console.log('fetch getDailyReserveStatus error');
                }
            },
            async getFlexibleTimeCount() {
                try {
                    if (!this.postData.departmentId || !this.showTimeRangePicker) return;
                    const data = await BoardApi.getFlexibleTimeCount({
                        departmentId: this.postData.departmentId || '',
                        doctorId: this.postData.doctorId || ANONYMOUS_DOCTOR_ID,
                        start: '06:00',
                        end: '23:00',
                        registrationType: this.registrationType,
                        reserveDate: this.postData.reserveDate,
                        registrationCategory: this.postData.registrationCategory,
                    });
                    this.registrationCount = data || [];
                } catch (e) {
                    console.log('fetch getFlexibleTimeCount error');
                }
            },
            getRegistrationCount(option) {
                const item = this.registrationCount.find((item) => item.localTime === option.label);
                return item?.count || 0;
            },
            handleCascaderVisible(val) {
                this.cascaderPanelVisible = val;
                if (val) {
                    this.showRecommendation = !val;
                }
            },
            handleCascaderMouseEnter() {
                if (!this.cascaderPanelVisible) {
                    this.showRecommendationTimer = setTimeout(() => {
                        this.showRecommendation = true;
                    }, 500);
                }
            },
            handleCascaderMouseLeave() {
                clearTimeout(this.showRecommendationTimer);
                if (this.showRecommendation) {
                    this.showRecommendationTimer = setTimeout(() => {
                        if (this.showRecommendedInformation) return;
                        this.showRecommendation = false;
                    }, 300);
                }
            },
            showRecommend() {
                this.showRecommendedInformation = true;
            },
            hiddenRecommend() {
                this.showRecommendation = false;
                this.showRecommendedInformation = false;
            },
            handleVisitSourceEdit() {
                this.$refs['visit-source-cascader'].outside();

                this.isShowVisitSourceDialog = true;
            },
            /**
             * @desc 发票相关
             * <AUTHOR>
             * @date 2021-08-17 15:34:43
             * @params
             * @return
             */
            openInvoiceDialog() {
                const _trans = this.chargeSheet.chargeTransactions.find((item) => {
                    return item.payMode === 5 && item.thirdPartyPayInfo;
                });
                let buyerName = '';
                let disabledBuyerName = false;
                if (_trans) {
                    disabledBuyerName = true;
                    buyerName = _trans.thirdPartyPayInfo.cardOwner || '';
                }
                buyerName = buyerName || this.patient.name;

                new InvoiceDialog({
                    chargeSheetId: this.chargeSheet.id,
                    chargeStatus: this.chargeSheet.status,
                    patientInfo: {
                        patientId: this.patient.id,
                        buyerPhone: this.patient.mobile,
                        idCard: this.patient.idCard,
                        buyerName,
                        disabledBuyerName,
                    },
                    printBillConfig: this.printBillConfig,
                    printMedicalListConfig: this.printMedicalListConfig,
                    medicalElectronicAPIConfig: this.medicalElectronicAPIConfig,
                    writeInvoiceConfig: this.writeInvoiceConfig,
                    isOpenInvoice: this.isOpenInvoice,
                    isOpenMedicalInvoice: this.isOpenMedicalInvoice,
                    invoiceConfigList: this.invoiceConfigList,
                    invoiceStatus: this.chargeSheet.invoiceStatus,
                    userInfo: this.userInfo,
                    businessType: InvoiceBusinessScene.REGISTRATION,
                    toBillPrintSetting: () => {
                        navigateToInvoiceConfig(this.currentClinic);
                    },
                    updateInvoiceStatus: (status) => {
                        this.chargeSheet.invoiceStatus = status;
                    },
                }).generateDialog({ parent: this });
            },
            actionType(type) {
                let str = '';
                switch (type) {
                    case 0:
                        str = '收费';
                        break;
                    case 1:
                        str = '退费';
                        break;
                    case 2:
                        str = '改';
                        break;
                    default:
                        str = '';
                        break;

                }
                return str;
            },
            actionAmount(item) {
                let arr = [];
                if (item.payActionInfos) {
                    //新版自定义收费方式
                    arr = item.payActionInfos.map(({ amount }) => `${this.$t('currencySymbol')} ${Math.abs(amount).toFixed(2)}`);
                } else {
                    //兼容旧版
                    PayModeList.forEach((one) => {
                        const fee = item[one.key];
                        if (fee) arr.push(`${this.$t('currencySymbol')} ${Math.abs(fee).toFixed(2)}`);
                    });
                }
                return arr.join(' + ');
            },
            actionPayMode(item) {
                let arr = [];
                if (item.payActionInfos) {
                    //新版自定义收费方式
                    // 新版自定义收费方式
                    arr = item.payActionInfos.map(({
                        payModeName, paySubModeName,
                    }) => {
                        let name = payModeName;
                        if (paySubModeName) {
                            name += `-${paySubModeName}`;
                        }
                        return name;
                    });
                } else {
                    //兼容旧版
                    PayModeList.forEach((one) => {
                        const fee = item[one.key];
                        if (fee) arr.push(one.name);
                    });
                }
                return arr.join('+');
            },
            getDisabledRegistrationProduct(list = []) {
                const reservationProductIds = this.reservationProductList?.map((item) => item.id) || [];
                return list.filter((item) => !reservationProductIds.includes(item.id)) || [];
            },

            /**
             * @desc 医保挂号,开启了his挂医保号需要在挂号的时候调一下，不能影响his流程，不关注医保挂号结果
             */
            async shebaoRegistration() {
                try {
                    if (!this.$abcSocialSecurity.isEnableHisCallRegistration) return;
                    if (!this.patient.originCardInfo) return;
                    const {
                        departmentId,
                        doctorId,
                    } = this.postData;
                    if (!departmentId || !doctorId) return;
                    await this.$abcSocialSecurity.registration({
                        departmentId,
                        doctorId,
                        patientOrderId: this.patientOrderId,
                        originCardInfo: this.patient.originCardInfo,
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            /**
             * @desc 医保退挂号
             */
            async shebaoRegistrationRevoke() {
                try {
                    if (!this.$abcSocialSecurity.isEnableHisCallRegistration) return;
                    if (!this.patientOrderId) return;
                    await this.$abcSocialSecurity.registrationRevoke({
                        patientOrderId: this.patientOrderId,
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            // 点击回诊
            async handleContinueDiagnosed() {
                try {
                    this.continueDiagnosedBtnLoading = true;
                    const { data } = await OutpatientAPI.getExaminationsByPatientOrderId(this.patientOrderId) || {};
                    if (!data.examItems?.length) {
                        this.$confirm({
                            type: 'warn',
                            title: '是否确认回诊签到',
                            content: '该患者本次就诊并未开具检查检验项目，是否确认回诊？',
                            onConfirm: this.confirmContinueDiagnosed,
                        });
                    } else {
                        await this.confirmContinueDiagnosed();
                    }
                } catch (e) {
                    console.log('获取检查检验项目列表失败', e);
                } finally {
                    this.continueDiagnosedBtnLoading = false;
                }
            },
            // 确认回诊
            async confirmContinueDiagnosed() {
                try {
                    const { data: continueDiagnosedData } = await RegistrationsAPI.modifyStatusV2(this.registration.registrationSheetId, {
                        targetStatus: StatusV2.CONTINUE_DIAGNOSED,
                    });
                    this.$emit('refresh', false);
                    this.showAppointmentCard = false;

                    const {
                        registrationFormItem,
                        patient,
                    } = continueDiagnosedData || {};

                    const {
                        doctorId = '',
                        departmentId = '',
                        timeOfDay = '',
                        orderNo = '',
                    } = registrationFormItem;

                    const patientId = patient?.id || '';
                    let callSchedulesResult = null;
                    let index = -1;

                    if (this.callConfig.enableCalling) {
                        callSchedulesResult = await CallingAPI.callSchedules(doctorId, '') || {};
                        // 当前预约挂号医生无排班
                        index = callSchedulesResult.data?.schedules?.findIndex((item) => item.doctorId === doctorId && item.departmentId === departmentId);
                    }
                    if (!this.callConfig.enableCalling || index === -1) {
                        const content = `${!this.callConfig.enableCalling ? '未开启排队叫号' : '由于医生当前无排班，故无法叫号'}，请患者直接前往医生诊室门口候诊。`;
                        this.$confirm({
                            type: 'success',
                            title: '回诊签到成功',
                            content,
                            showCancel: false,
                        });
                    } else {
                        this.$Toast({
                            message: '回诊签到成功',
                            type: 'success',
                        });
                        this.$abcEventBus.$emit('continue-diagnosed-open-ctr-view', {
                            doctorId,
                            departmentId,
                            patientId,
                            timeOfDay,
                            orderNo,
                        });
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.continueDiagnosedBtnLoading = false;
                }
            },
            /**
             * @desc 获取诊疗科室-医生
             */
            async fetchDepartmentsDoctors(isClear = true) {
                const params = {
                    registrationType: this.registrationType,
                    type: 1,
                    date: this.postData.reserveDate,
                    registrationCategory: this.postData.registrationCategory,
                    patientId: this.patient?.id || '',
                    isMustNeedDefaultEmployee: this.isEnableRegUpgrade,
                };
                const { data } = await RegistrationsAPI.fetchDepartmentsDoctors(params);
                const cacheDepartmentObj = {};
                let tempDepartments = Clone(data?.departments || []);
                // 门诊预约看板的departments数据经过了筛选，这里数据也需要处理
                if (this.isFromRegistrationBoardDialog) {
                    tempDepartments = Clone(data?.departments || []).filter((department) => {
                        department.doctors = department.doctors?.filter((doctor) => doctor.doctorId === this.doctorId);
                        return department.doctors?.length;
                    });
                }

                tempDepartments.forEach((item) => {
                    cacheDepartmentObj[item.departmentId] = item.doctors;
                });
                this.departmentObj = Clone(cacheDepartmentObj);
                this.departmentsList = tempDepartments.map((item) => {
                    item.doctors?.forEach((doctor) => {
                        doctor.departmentId = item.departmentId;
                        doctor.departmentName = item.departmentName || '其他';
                    });
                    return item;
                });
                // 切换挂号和预约, 需要判断已选择医生在选择的时间那天是否有排班
                if (isClear && !this.unscheduledCanRegistration) {
                    this.$nextTick(() => {
                        // 没有排班则清空已选择医生
                        const findFlag = this.doctors.find((item) => item.doctorId === this.postData.doctorId);
                        if (!findFlag) {
                            this.postData.doctorId = '';
                            this.getDailyReserveStatus();
                        }
                    });
                }
            },
            async fetchRegistrationProperty() {
                try {
                    const { data } = await PropertyAPI.get('registration.config.outpatient', 'clinic');
                    this.isAutoFillReferrer = data?.outpatient?.isAutoFillReferrer;
                } catch (err) {
                    console.log(err);
                }
            },
            onClickCancelPay() {
                // 后台的数据可能存在有锁信息但是没有lockPayTransactionInfo
                if (!this.chargeSheet.lockPayTransactionInfo) return;
                this.onConfirmCancelPay({
                    chargePayTransactionId: this.chargeSheet.lockPayTransactionInfo.id,
                    callback: this.fetchDetail,
                });
            },
            keydownHandle(event) {
                const hasAbcModalDialog = document.querySelector('.abc-modal-dialog');
                const KEY_F4 = 115;
                const KEY_F6 = 117;
                const KEY_ESC = 27;
                if (event.keyCode === KEY_F4) {
                    if (hasAbcModalDialog) return;
                    this.handleConfirm(false);
                }

                if (event.keyCode === KEY_F6) {
                    if (hasAbcModalDialog) return;
                    if (this.isSupportShortFlow) {
                        this.handleConfirm(true);
                    }
                }

                if (event.keyCode === KEY_ESC) {
                    this.handleCancel();
                }
            },
        },
        beforeDestroy() {
            this.showAppointmentCard = false;
            clearTimeout(this._timer);
            clearTimeout(this.timer2);
            clearTimeout(this.showRecommendationTimer);
            this._timer = null;
            this.timer2 = null;
            this.showRecommendationTimer = null;
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
@import 'src/views-dentistry/registration/appointment-base-card.scss';
</style>
