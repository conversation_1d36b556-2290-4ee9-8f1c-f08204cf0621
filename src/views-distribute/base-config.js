// ---- 组件 ----
import { ExaminationClinicRouteKey } from '@/router/route-key/examination';
import tableStyleConfig from 'src/views-distribute/style-config/base.js';
import MedicalRecord from 'views/outpatient/common/medical-record/index.vue';
import { EXAMPLE_DATA } from 'views/settings/print-config/medical-documents/medical/constants.js';
// ---- 静态资源 ----
import baseProductExampleImg from '@/assets/images/<EMAIL>';
import clinicBindRelationImg1
    from '@/assets/images/marketing/referral/guide-activity-sample/referrer-bind-relationship-clinic-1.png';
import clinicH5DefaultShareImg from '@/assets/images/marketing/referral/share/clinic-h5-default-share.png';
import clinicH5ShareImg from '@/assets/images/marketing/referral/share/clinic-h5-share.png';
import clinicWeappDefaultShareImg from '@/assets/images/marketing/referral/share/clinic-weapp-default-share.png';
import clinicWeappShareImg from '@/assets/images/marketing/referral/share/clinic-weapp-share.png';
import baseNotOpenImg from '@/assets/images/reservation-item/<EMAIL>';
import baseProductDetailImg from '@/assets/images/reservation-item/<EMAIL>';
import baseProductShowImg from '@/assets/images/reservation-item/<EMAIL>';

import noticeImg from '@/assets/images/micro-clinic/service-introduction/notice-brown.png';
import noticeBlueImg from '@/assets/images/micro-clinic/service-introduction/notice-blue.png';
import noticeYellowImg from '@/assets/images/micro-clinic/service-introduction/notice-yellow.png';
import noticeCyanImg from '@/assets/images/micro-clinic/service-introduction/notice-cyan.png';
import noticeTopImg from '@/assets/images/announcement/notice-top-brown.png';
import noticeTopBlueImg from '@/assets/images/announcement/notice-top-blue.png';
import noticeTopYellowImg from '@/assets/images/announcement/notice-top-yellow.png';
import noticeTopCyanImg from '@/assets/images/announcement/notice-top-cyan.png';

import { parseDiagnosis } from '@/filters/index.js';
import { PRINT_OPTIONS } from '@/printer/common/constant.js';
import { PRINT_DEFAULT_CONFIG_KEY } from '@/utils/constants';
import { GoodsCategoryList } from '@/views/common/goods-search';
import {
    GoodsSubTypeEnum, GoodsTypeEnum, GoodsTypeIdEnum,
} from '@abc/constants';
import ExaminationApi from 'api/examination';
import {
    CATEGORY_DEVICE_TYPE, CATEGORY_TYPE_ENUM, SYSTEM_GOODS_TYPE,
} from 'views/common/goods-search/constants';
import {
    DefaultMedicalRecord,
    MedicalRecordStructKey,
    MedicalRecordStructList,
} from 'views/outpatient/common/medical-record/constants.js';

import { InspectClinicRouteKey } from '@/router/route-key/inspect';
import { ShebaoPayMode } from 'views/inventory/goods/archives/components/social-code-autocomplete/constant';
import { AppTabName } from '@/core/constants';
import { MedicalRecordTypeEnum } from 'views/outpatient/common/medical-record/utils';

const CashierOutpatientInfoDiagnosis = () => import('views/cashier/components/cashier-outpatient-info-diagnosis.vue');
const AppClinicDropdownIcon = () => import('views/layout/app-clinic-dropdown-icon.vue');
const AppLogo = () => import('views/layout/app-logo.vue');
const AppFullscreenBtn = () => import('views/layout/app-fullscreen-btn.vue');
const MedicalSettingPopover = () => import('views/outpatient/common/outpatient-setting-popover/medical-setting-popover.vue');
const PrescriptionSettingPopover = () => import('views/outpatient/common/outpatient-setting-popover/prescription-setting-popover.vue');
const RegistrationTable = () => import('views/registration/table');
const AppZoomBtnReference = () => import('views/layout/app-zoom-btn-reference.vue');
const AppNoticeReference = () => import('views/layout/app-notice-reference.vue');
import SettleListDialogTemplate from 'src/views/layout/social-report/settle-list-dialog.vue';


export default class BaseClinicViewConfig {
    constructor(hisType) {
        this.hisType = hisType;
        /**
         * @desc 功能点
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewFeature = {
            theme: true,
            therapy: true, // 理疗
            process: true, // 加工
            familyDoctor: true, // 家庭医生
            chronicRecovery: true, // 慢病康复
            continueMR: true, // 自助续方
            childHealth: true, // 儿保
            inventoryMedicine: true, // 库存主要是药品
            treatOnline: true, // 网络问诊
            hospital: true, // 长护，住院
            inspectReportSync: false, // 检查报告同步功能
            airPharmacy: true, // 空中药房
            oralProcess: false, // 义齿加工
            compose: true, // 套餐
            feeCompose: false, // 支持费用项组合
            supportGlassesPrescription: false, // 是否支持配镜处方
            supportFilterEyeGlasses: false, // 是否支持搜索时过滤眼镜
            supportFeiMu: false, // 支持首页费目
            supportRatioPrice: false, // 支持按价格的比例（折扣）来议价
            supportPatchOrder: false, // 支持补单（创建单据的时候选择其它时间）
        };
        /**
         * @desc 差异组件
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewComponents = {
            medicalRecord: MedicalRecord,
            medicalSettingPopover: MedicalSettingPopover,
            cashierOutpatientInfoDiagnosis: CashierOutpatientInfoDiagnosis,
            registrationTable: RegistrationTable,
            prescriptionSettingPopover: PrescriptionSettingPopover,
            AppLogo,
            AppClinicDropdownIcon,
            AppZoomBtnReference,
            AppFullscreenBtn,
            AppNoticeReference,
        };

        this.viewDistributeConfig = {
            useAbcUIV2: false,
            isSupportDrawer: true, // 是否支持侧边栏抽屉，口腔先不支持
            isFullScreen: true,
            institutionTypeWording: '诊所',
            organTypeWording: '诊所',
            customerLabel: '患者',
            roleLabel: '患者',
            diagnosisTreamentTypeWording: '项目',
            showEditionTag: true,
            statisticText: '统计',
            pharmacyArchiveFixPricePermission: false, // 是否开启建档和定价权限限制
            supportDeactiveEmployeeRelation: false, // 是否支持停用员工关联(科室、人员、病区)
            supportSeparateOutpatientAndHospital: false, // 是否支持区分诊所和医院
            templateManagerVersion: 0,
            bindRelationType: 1, // 绑定关系类型
            needTransGoodsClassificationName: false,
            // 转换药品分类名称
            transGoodsClassificationName(name) {
                return name;
            },

            /**
             * @desc 读取诊断方式
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            getDisplayDiagnosisText: (medicalRecord) => {
                const { diagnosis = '' } = medicalRecord || {};
                return parseDiagnosis(diagnosis) || '';
            },

            // 执行,收费开单,门诊诊疗项目 是否可以填入医生护士
            chargeItemSupportDoctorNurse: false,

            AppLayout: {
                appTabName: AppTabName[window.appTabId] || '', // 浏览器 tab 名字
            },

            /**
             * AppHeader 相关
             */
            AppHeader: {
                showClinicSelector: true, // 是否显示门店切换
                showNavMenu: true, // 是否显示导航菜单
                showWardSelector: false, // 是否显示病区切换
                showDepartmentSelector: false, // 是否显示科室切换
                showStockRoomSelector: false, // 是否显示库房切换
                menNavIsCenter: false, // 导航菜单是否居中

                showNavIcon: false,
                isFixedLeftNav: false, // 导航栏是否固定在左侧

                showClientServiceText: true, // 客服文案是否显示
                /**
                 * 获取菜单项 padding
                 * @param menuItemCount 菜单个数
                 * @param menuContainerWidth 菜单容器宽度
                 * @returns {*|number}
                 */
                getMenuItemPadding(menuItemCount, menuContainerWidth) {
                    const MAX_COUNT = 8;
                    const paddingList = [
                        {
                            min: 100,
                            max: 600,
                            padding: 16,
                        },
                        {
                            min: 600,
                            max: 700,
                            padding: 18,
                        },
                        {
                            min: 700,
                            max: 800,
                            padding: 20,
                        },
                        {
                            min: 800,
                            max: 900,
                            padding: 21,
                        },
                        {
                            min: 900,
                            max: 1200,
                            padding: 22,
                        },
                    ];

                    const paddingRightLeft =
                        (menuItemCount < MAX_COUNT &&
                            paddingList.find(
                                (item) =>
                                    menuContainerWidth >= item.min &&
                                    menuContainerWidth <= item.max,
                            )?.padding) ||
                        16;
                    return `0px ${paddingRightLeft}px`;
                },

                // 是否启用医保菜单项
                isEnableSocialMenuItem: () => {
                    return true;
                },

                // 是否启用商城菜单项
                isEnableMallMenuItem: () => {
                    return true;
                },

                // 医保放在哪个菜单后面-取routeName
                getSocialAfterItemName: () => {
                    return 'crm';
                },

                showNavCrmTodo: false,

                showUserInfoNationalCode: true,
            },

            /**
             * 工作台模块
             */
            Dashboard: {
                // 今日看板
                patientBulletinBoard: {
                    showDispensingAndTreatment: true, // 展示发药和治疗
                },
                todoOutpatientRouterPath: '/outpatient',
                judgeHasOutpatientByRole: false, // 是否通过角色判断展示待接诊模块
                isSupportStockWarning: false,//是否支持库存预警
                isUpdateCmsPush: false,//是否升级员工推送
            },

            /**
             * @desc 打印相关
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Print: {
                printOptions: PRINT_OPTIONS,
                exampleData: EXAMPLE_DATA,
                treatmentExecuteLabel: '治疗理疗单',
                // 检查检验打印选项 - 检查检验单
                examPrintOptions: {
                    examination: PRINT_OPTIONS.EXAMINATION,
                    inspection: PRINT_OPTIONS.INSPECT,
                },
                allowExamReportPrint: false, // 是否允许检查检验报告支持打印
                isSupportCashierA5: false, // 是否支持A5收费单print

                registrationEnableCategory: false,
                isSupportPaperOrientation: true, // 支持选择进纸方向支持A5横放
            },
            // 营销模块
            Marketing: {
                allowIntegralRulesSettingByProfit: false,
                referrer: {
                    h5ShareImg: clinicH5ShareImg,
                    weappShareImg: clinicWeappShareImg,
                    weappDefaultShareImg: clinicWeappDefaultShareImg,
                    h5DefaultShareImg: clinicH5DefaultShareImg,
                    bindRelationImg1: clinicBindRelationImg1,
                },
                integralShowPointsGoodsDeduction: true, //是否显示积分-折扣商品
                isShowNonMember: true, //会员范围是否显示非会员
                isFilterOperations: true, //是否过滤手术
                overview: {
                    isShowWxCount: true, //消息推送概览是否显示微信服务
                },
                member: {
                    isShowMemberWxCharge: true, //会员设置弹窗是否显示微信自助充值
                    isShowWxChargeRule: true, //会员设置弹窗是否显示微信充值规则
                    isShowUpgradeSetting: true, //会员设置弹窗是否显示升级设置
                    isShowMemberPriceGoods: false, //会员设置弹窗是否显示会员价商品
                },

                // 科室多挂号费相关
                adaptRegType(type) {
                    if (type.goodsType === GoodsTypeIdEnum.REGISTRATION && type.children && type.children.length) {
                        const children = type.children.find((subType) => {
                            return subType.goodsType === GoodsTypeIdEnum.REGISTRATION && subType.goodsSubType === 0;
                        });

                        if (children) {
                            return children;
                        }
                    }

                    return type;
                },

                // 卡项服务
                card: {
                    isSupportDeductionBenefitsRangeRule: false, // 抵扣权益-自选项目-指定范围选择
                },

                // 单品选择组件
                usePharmacyGoodsSelect: false,
            },

            /**
             * @desc 执行模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Treatment: {
                productFormCanAddToothNo: false, // 项目能否添加牙位
                sidebarShowHistory: false, // 右侧sidebar是否展示就诊历史
                showExecuteRecord: true, // 执行划扣是否展示执行记录
                showAddTreatmentForm: true, // 允许开单
                billSupportOtherFee: true, // 开单是否支持其他费用，医院线中OTHER-19是费用项，搜出来和医嘱会重复，医院线开单不需要OTHER
            },

            /**
             * @desc 挂号模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Registration: {
                routerName: '挂号预约',
                settings: {
                    regsHiddenMedicalRecord: true,
                    regsHiddenReCommend: true,
                },

                // 病历结构开关
                medicalRecordStruct: {
                    chiefComplaint: 1, // 主诉
                    presentHistory: 1, // 现病史
                    pastHistory: 1, // 既往史
                    allergicHistory: 1, // 过敏史
                    physicalExamination: 1, // 体格检查
                    epidemiologicalHistory: 1, // 流行病学史
                    preDiagnosisAttachments: 1, // 附件上传
                },
                // 挂号是否开启身份证与年龄生日联动 1开启 0关闭
                idCardLinkageSwitch: 1, //口腔管家默认开启 普通门诊默认关闭
                isOpenWechatWindowRegisterAppointment: false, //是否开启微信窗口挂号预约
                isOpenAppointProject: true, // 是否开启项目预约
                audioOpen: false, // 预约签到，挂号成功口腔店开启语音提示
                showDiagnosing: false, // 是否显示接诊中筛选
                showConsultant: false, // 是否显示咨询师
                showDiscontinuation: false, // 是否开启停诊功能
                supportShortFlow: false, // 是否支持缩短流程（挂号并收费）
            },
            /**
             * @desc 门诊模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Outpatient: {
                requiredChiefComplaint: true, // 主诉必填
                requiredDiagnosis: true, // 诊断必填

                productFormCanAddToothNo: false, // 项目能否添加牙位
                sidebarShowHistory: true, // 门诊右侧sidebar是否展示就诊历史
                sidebarShowReport: true, // 门诊右侧sidebar是否展示报告
                doctorAdviceInForm: true,

                showHistoryPrescription: true, // 历史处方 快捷按钮
                showHistoryMR: true, // 历史病历 快捷按钮
                westernPrescriptionNameText: '中西成药处方',
                employeePrescriptionConfigKey: 'prescription',
                supportAddCNPR: true, // 是否支持添加 中药处方
                supportAddExternalPR: true, // 是否支持添加 外治处方

                showDTRemarkMeridian: true, //诊疗项目备注选择穴位
                showDTDayCol: true, //诊疗项目支持录入天数

                defaultMedicalRecord: DefaultMedicalRecord,
                outpatientConfigScope: 'employee',
                multiMedicalRecord: true,
                defaultMedicalRecordType: MedicalRecordTypeEnum.WESTERN, // 默认病历类型
                medicalRecordStructKey: MedicalRecordStructKey,
                medicalRecordStructList: MedicalRecordStructList,
                medicalRecordIsNewOralKey: false, // 病历中口腔检查使用新key dentistryExaminations

                supportNewRegistrationBoard: false, // 小工具是否支持新版预约看板
                supportNewRegistration: false, // 是否支持发起新的预约

                routeBasePath: '/', // 路由的 basePath，在医院管家下，会放到 his-outpatient 下面
                supportExamApplySheetView: false, // 是否支持检查检验申请单查看

                isPrintExamApplySheet: false, // 是否打印检查检验申请单

                supportAddOtherFee: true, // 门诊开单支持添加其他费用
                supportAddNursing: false, // 门诊开单支持添加护理医嘱
                tableStyleConfig: tableStyleConfig.Outpatient, // 门诊各个诊疗、处方表格style配置
                isSupportMedicalDocument: true, // 是否支持病历文书 现在默认全部开启
                showMedicalRecordPrintCount: true, // 是否显示病历打印次数
                isShowPrintHospitalizationCertificate: false, //是否显示打印住院证
                canCustomizeShebaoChargeType: false, // 定制费别

                isSupportSurgery: false, // 门诊是否支持手术
                doctorAdviseNeedDepartmentId: false, // 医嘱建议是否需要科室
            },
            /**
             * 儿保模块
             */
            ChildHealth: {
                routeBasePath: '/', // 路由的 basePath，在医院管家下，会放到 his-outpatient 下面
                isSupportMedicalDocument: true, // 是否支持病历文书 现在默认全部开启
            },
            /**
             * @desc 收费模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Cashier: {
                sidebarShowHistory: false, // 右侧sidebar是否展示就诊历史
                sidebarShowReport: false,
                productFormCanAddToothNo: false, // 项目能否添加牙位
                routeBasePath: '/', // 路由的 basePath，在医院管家下，会放到 his-charge 下面
                sidebarShowTreatmentCustomList: false, // 右侧sidebar支持展示自定义收费项目列表
                supportSelectPharmacy: false, // 是否支持选择药房
                chargeTitleUnify: false, // 收费单标题是否统一
                supportAddOutpatientSheet: false, // 是否支持补录门诊单
                showFeeType: false, // 零售收费搜索框区分医嘱和费用项
                showOnlineChargePlaySetting: true, // 收费台设置- 新网诊收费单播报
                showContinueSheetChargePlaySetting: true, // 收费台设置- 新网诊收费单播报
                showProcessSetting: true, // 收费 -是否展示加工
                supportAddConsultant: false, // 零售开单支持添加咨询师
                supportDirectSaleToggle: false, // 支持零售开单开关控制
                showOwedAmount: true, // 收费看板是否展示还款
                showOweAmount: true, // 收费看板是否展示欠费
                showSalesDetail: false, // 收费看板是否展示销售明细
            },
            /**
             * 药房模块
             */
            Pharmacy: {
                routeBasePath: '/', // 路由的 basePath，在医院管家下，会放到 his-pharmacy 下面
                showSellerInput: false, // 门诊药房是否展示开单人
                showProcessTab: true, // 药房 quicklist 是否展示加工tab
                showChinesePrescriptionNum: true, // 药房看板是否展示中药处方数
                showProcessNum: true, // // 药房看板是否展示加工数
            },
            /**
             * @desc 检查检验模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Examination: {
                routerName: '检验',
                containInspect: false,

                templateClinicType: 'normalChainId',
                physicalExaminationTemplateClinicType:
                    'physicalExaminationChainId',
                hasInspectBargainSetting: false,
                needReportVersion: false, // 是否需要判断检验报告版本
                async fetchExaminationEmployee() {
                    const { data } =
                        await ExaminationApi.fetchExaminationEmployees();
                    return data;
                },
                isSupportFastCreateSheet: false, // 是否支持快速创建检验单
                routeKey: ExaminationClinicRouteKey,
                inspectCanRelativeProject: false,
                isSupportChannelFilter: false, // 是否支持渠道筛选
                isSupportAutoPrintSampleCollection: true, // 是否支持检验样本自动打印
                isSupportShowFeeItem: false,
            },

            /**
             * @desc 微诊所模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            WeClinic: {
                supportWeShopPurchase: true, // 是否支持购买微商城
                supportSetupHomePageStatistics: true, // 是否支持首页统计
                reservationsDoctorSortRouterName: '医生/理疗师排序（预约）',
                caseHistoryIntroduce: '包括：既往史、现病史、体格检查、诊断',
                treatmentTitle: '展示检查检验、治疗理疗信息',
                tabs: [
                    'homepage',
                    'clinic',
                    'doctor-sort',
                    'online-doctor-sort',
                    'reservation-doctor-sort',
                ],
                medicalRecordOptions: [
                    {
                        label: '主诉',
                        key: 'chiefComplaint',
                        value: '咳嗽，发烧',
                    },
                    {
                        label: '既往史',
                        key: 'pastHistory',
                        value: '否认药物过敏史',
                    },
                    {
                        label: '诊断',
                        key: 'diagnosis',
                        value: '急性上呼吸道感染',
                    },
                    {
                        label: '体格检查',
                        key: 'bodyCheck',
                        value: '扁桃体肿大，咽喉部充血',
                    },
                ],
                medicalRecordsOtherOptions: [
                    {
                        label: '医嘱',
                        value: 1,
                    },
                    {
                        label: '附件',
                        value: 2,
                    },
                ],
            },

            /**
             * @desc 设置模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Settings: {
                isSupportChargeBasicSetting: true, // 是否支持基础收费设置
                isSupportChargeAbcSetting: true, // 是否支持聚合支付设置
                isSupportChargeInvoiceSetting: true, // 是否支持收费发票设置

                isSupportPrintMedicalDocumentSetting: true, // 是否支持医疗文书打印设置
                isSupportPrintTicketSetting: true, // 是否支持小票打印设置
                isSupportPrintRegistrationTicketSetting: true, // 是否支持挂号小票打印设置
                isSupportPrintCashierTicketSetting: true, // 是否支持收费小票打印设置
                isSupportPrintDispenseTicketSetting: true, // 是否支持发药小票打印设置
                isSupportPrintTagSetting: true, // 是否支持标签打印设置
                isSupportPrintConfigMedicalBills: true, // 是否支持医疗票据打印设置
                isSupportPrintMedicalBillsFeeList: true, // 是否支持医疗清单打印设置
                isSupportPrintInventoryGoodsIn: true, // 是否支持库存入库单打印
                isSupportPrintReceipt: true, // 是否支持打印收据

                needValidatePower: false, //校验子权限
                projectBindChargeType: 'MEDICAL_INSURANCE', // 诊疗项目绑定的收费项目类型 MEDICAL_INSURANCE - 医保对码
                showMedicalCodeColum: true, // 诊疗项目中是否展示医保对码列
                otherTypeGoodsName: '其他费用', // 其他类型名称
                showGuanBiaoBtn: true,
                registeredFeeRoleLabel: '医生',
                showProductEditionIntroduce: true, // 是否展示【版本功能列表】按钮
                showInvoiceActionSetting: true, // 是否展示【进销存动作设置】模块
                traceCodeHasDispensingPreCheck: true, // 追溯码有 发药前检查
                showHospitalPharmacyTraceCode: false, // 是否展示住院发药追溯码采集开关
                isEnableAddPACSServer: false, //是否支持添加pacs服务器
                isSupportTaxRateClassification: true, //是否支持税率分类
                isSupportFinanceInvoice: true, // 是否支持财政电子发票
                isSupportPaperInvoice: true, // 是否支持纸质发票
                isSupportDefaultInvoice: true, // 是否支持默认开票
                invoiceWriteOffText: '自动作废/冲红', // 发票冲红文案
                isSupportTicketNumberManagement: true, // 是否支持票号管理

                clinic: {
                    baseInfo: {
                        organNameTip:
                            '主要用于外部展示，如病历、处方、挂号单、收费单等各种打印单据上店名的展示，一般为营业执照上的名称',
                    },
                },

                // 诊疗项目
                project: {
                    examination: {
                        isSupportIdentification: false, // 是否支持互认标识
                    },
                    inspect: {
                        isSupportIdentification: false, // 是否支持互认标识
                    },
                    registrationFee: {
                        // 是否支持挂号费三种模式
                        isSupportMultipleMode: true,
                    },
                },

                examinations: {
                    examinationTestDescribe:
                        '检查通常是影像检查项目，如B超、DR、心电图',
                    showCombineDescribe: true,
                },

                employees: {
                    needSplitRole: false,
                    hasAdminRole: true,
                    isSupportPracticeProfile: true, // 是否支持执业简介
                    isSupportJobSelect: true, //是否支持职务选择
                    isSupportBusinessDate: true, // 是否支持执业时间
                    isSupportPrescribeRight: true, // 是否支持处方权
                    isSupportMultipointPractice: true, // 是否支持多点执业
                    isSupportFamilyDoctorQualification: true, // 是否支持家庭医生资格
                    //展示医师代码的判断条件
                    checkIsShowDoctorSocialCode: ({ isChainAdmin }) => {
                        return !isChainAdmin;
                    },
                    isSupportDepartment: true, // 是否支持科室
                    isSupportModifyEmployeeNameInPermission: false, //是否支持在数据权限那设置修改成员姓名/签名
                },
                microClinic: {
                    isNeedNursingItem: false,
                    treatmentText: '治疗理疗',
                    isNeedOther: true,
                    allowJumpToMarket: true, // 允许跳转营销
                    showSelfServicePrescription: true, // 是否展示自助续方
                    allowJumpToSetting: true, // 允许跳转设置
                    serviceIntroImg: {
                        noticeImg,
                        noticeBlueImg,
                        noticeYellowImg,
                        noticeCyanImg,
                    },
                    noticeTopImgInfo: {
                        noticeTopImg,
                        noticeTopBlueImg,
                        noticeTopYellowImg,
                        noticeTopCyanImg,
                    },
                    // 示例图片前缀
                    retrieveMobilePrefix: 'clinic',
                },
                treatment: {
                    isAllowFeatureTherapy: true, // 是否允许理疗
                    isRelationProductVisible: false, // 是否展示关联项目
                    implementText: '执行划扣',
                    isIncludeOphthalmologyGoods: false, // 执行站开单可开项目是否包含眼科物资
                    isIncludeNurse: false, // 执行站开单可开项目是否包含护理
                    isShowCustomTypeName: true,
                    isShowOpenFormConfig: true, //是否显示开单设置
                    isShowInHospitalExecute: false, // 治疗理疗是否展示住院执行选项
                },

                compose: {
                    searchGoodsType: [
                        { type: GoodsTypeEnum.MEDICINE },
                        {
                            type: GoodsTypeEnum.MATERIAL,
                            subType: [
                                GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL]
                                    .MedicalMaterials,
                            ],
                        },
                        { type: GoodsTypeEnum.GOODS },
                        { type: GoodsTypeEnum.EXAMINATION },
                        { type: GoodsTypeEnum.TREATMENT },
                        { type: GoodsTypeEnum.OTHER },
                    ],
                },

                other: {
                    isNeedCheckFeeUsed: false,
                },

                template: {
                    medicalRecordStruct: {
                        chiefComplaint: 1, // 主诉
                        presentHistory: 1, // 现病史
                        pastHistory: 1, // 既往史
                        obstetricalHistory: 1, // 月经婚育史
                        epidemiologicalHistory: 1, // 流行病史
                        physicalExamination: 1, // 体格检查
                        chineseExamination: 1, // 望闻问切
                        oralExamination: 1, // 口腔检查
                        auxiliaryExaminations: 1, // 辅助检查
                        diagnosis: 1, // 诊断
                        syndrome: 1, // 辩证
                        therapy: 1, // 治法
                        chinesePrescription: 1, // 方药
                        prognosis: 1, // 预后
                        target: 1, // 目标
                        allergicHistory: 1, // 过敏史
                        disposals: 1, // 处置
                        symptomTime: 0, // 发病日期
                    },
                    showEmrTemplateSetting: true, // 是否展示文书模板
                    showClassicPRCataloguesAndClinicalPRCatalogues: true, // 模板设置是否展示经典方剂 和临床验方
                    hiddenExecuteTemplateSetting: false, // 模板设置是否展示执行记录模板
                    hiddenChildGrowthCareTemplateSetting: false, // 模板设置是否展示执行儿保看护模板
                },
                dashboardKey: 'dashboard',
                registeredChainRouterTabLabel: '门诊预约项目', // 设置index tabs label
                registeredClinicRouterTabLabel: '门诊预约', // 设置index tabs label
                treatmentRegisteredChainRouterTabLabel: '理疗预约项目', // 设置index tabs label
                treatmentRegisteredClinicRouterTabLabel: '治疗理疗预约', // 设置index tabs label
                showProcessDispensingSettings: true, // 设置 '发药/加工/配送'
                reservation: {
                    reservationDentistry: false,
                    fetchReservationNotFromAPI: false,
                    productExampleImg: baseProductExampleImg, // 预约项目示例图
                    productShowImg: baseProductShowImg, // 预约项目详情展示图片
                    productDetailImg: baseProductDetailImg, // 预约项目详情示例图
                    productNotOpenImg: baseNotOpenImg, // 预约项目未开通微诊所示例图
                    isEnablePayModeSetting: false, //预约流程 挂号收费
                },
                // 排班设置
                schedule: {
                    isShowOutpatientSchedule: true, // 是否展示门诊排班
                    isUseScheduleSettingControlPermission: false, // 是否使用排班设置模块作为排班权限控制
                    isScheduleWithConsultingRoom: true, // 是否排班时设置诊所
                    isShowRegistrationCategory: false, // 是否展示多号种选项
                },
                callNumber: {
                    newOpenSwitchMethods: false,
                    isCheckByEdition: true, // 是否需要判断版本（小屏需要大客户版，医院直接购买功能不需要升级版本）
                },
                common: {
                    isOpenRelationModelInOutpatientSetting: true, // 门诊是否展示关联项目
                },
                inspect: {
                    regModeVisible: false,
                    autoReservationSettingVisible: false,
                    isIncludeRISInspect: true, // 是否包含RIS检查
                    isIncludeClinicalInspect: false, // 是否包含临床检查
                },
                showMigration: false, // 是否展示口腔诊所一键迁移
                print: {
                    defaultConfigKey: PRINT_DEFAULT_CONFIG_KEY.NORMAL, // 默认打印配置的 key
                    isShowApplySheetSetting: false, // 是否展示医疗文书中检查检验申请单的设置
                    showChineseLayoutForHechiSetting: true, // 是否展示医疗文书-处方正文-中药竖版排列设置
                    showChineseMedicineSetting: true, // 是否展示医疗文书-处方正文-中药设置
                    showProcessInfoSetting: true, // 是否展示医疗文书-处方正文-  加工信息
                    showTicketsChineseMedicineDetailSetting: true, // 是否展示医疗文书-小票-  中药明细
                    showTicketsChineseMedicineSetting: true, // 是否展示医疗文书-小票-  中药
                    showDispensingTicketsChineseMedicine: true, // 是否展示发药小票- 正文 中药
                    showTagChineseRemark: true, // 是否展示用药标签- 中药备注
                    //收费小票配置
                    chargeTicket: {
                        isSupportTitleSetAll: true, // 是否支持小票标题应用到全部
                        isSupportOutpatientBarcode: true, // 是否支持就诊条码
                        isSupportPatientOrderNo: true, // 是否支持诊号
                        isSupportDoctorInfo: true, // 是否支持展示医生信息
                        openSheetPersonText: '开单人', // 开单人文案
                        contentIsSupportRegistration: true, //小票正文支持挂号费
                        contentIsSupportExam: true, //小票正文支持检查检验项目
                        contentIsSupportTreatment: true, //小票正文支持治疗理疗项目
                        contentIsSupportCompose: true, //小票正文支持套餐项目
                        contentIsSupportOtherFee: true, // 小票正文收费项目支持打印其他费用
                        contentIsSupportComposeDetail: true, // 小票正文支持套餐明细
                        chineseMedicineDetailIsSupportUnitCount: true, // 中药明细支持单剂克数
                        chineseMedicineDetailIsSupportSpecialRequirement: true, // 中药明细支持煎法
                        chineseMedicineDetailIsSupportTotalCount: true, // 中药明细支持总重
                        footerIsSupportWeClinicQrCode: true, // 是否支持展示微诊所二维码
                        isUsePharmacyExampleData: false, // 是否使用药房示例数据
                    },
                    // 医保结算单打印配置
                    socialSettlePrint: {
                        isPrintPharmacySocialSettle: false, // 是否渲染药店医保结算单模板
                    },
                    // 检查检验单
                    examinationSheet: {
                        isSupportExaminationNoPrint: true, // 是否支持检查检验单号打印
                    },
                    medicalDocument: {
                        isSupportCDUPrintConfig: true, // 是否支持彩超打印配置
                        isSupportCTPrintConfig: true, // 是否支持 CT 打印配置
                        isSupportDRPrintConfig: true, // 是否支持 DR 打印配置
                        isSupportMRPrintConfig: true, // 是否支持 MR 打印配置
                        isSupportMGPrintConfig: true, // 是否支持 MG 打印配置
                        isSupportGastroscopePrintConfig: true, // 是否支持 Gastroscope, // 是否支持 GASTROSCOPE 打印配置
                    },
                    examinationReport: {
                        isSupportIdentification: false,
                    },
                    isSupportMedicalTechnologyPrintConfig: false, // 是否支持医技打印设置
                },
                // 开出设置
                openSetting: {
                    hasWard: false, // 下达规则是否有病区
                    // 允许补开医嘱
                    allowOpenAdviceSupplement: false,
                    outpatientLockGoodsName: '锁库时机',
                    openPositionName: '药房',
                },
                examination: {
                    sampleGroupIsRelateProject: false,
                },
                // 门诊设置
                outpatient: {
                    openContinueDiagnoseWithoutReg: false,
                    // 采样组是否关联项目
                    sampleGroupIsRelateProject: false,
                    showChinesePrescriptionSupportMix: true, // 是否开出饮片、颗粒混开策略
                    quickDiagnosis: true, // 快速接诊
                    openMedicalRecordUpdateLimit: true, // 门诊病历修改时间限制
                    openPrescriptionUpdateLimit: true, // 门诊处方修改时间限制
                    outpatientSummaryIncomeType: 'clinic',
                },
                product: {
                    productType: 'clinic',
                },
                showDecoctionSettings: true, // 是否展示加工设置
                showAirPharmacySettings: true, // 是否展示空中药房设置
                showMergeChinesePrescriptionSetting: true, // 是否展示微诊所设置-就诊报告- 合并展示为"中药处方"设置

                chainShowEmployeeFormRole: false, // 连锁总部是否展示员工角色
                isEnableCustomizeRole: false, // 是否支持自定义角色

                //收费设置
                charge: {
                    isSupportChargeSheetAutoClose: true, // 是否支持收费单自动关闭
                    isSupportChargeConditionLimit: true, // 是否支持收费条件限制
                    chargeBargainText: '议价', // 议价设置文案
                    isSupportDoctorSelfBargain: true, // 是否支持医生自主议价
                    isSupportTreatmentSheetBargain: true, // 执行站开单可对金额自主议价
                    isSupportRegistrationBargain: true, // 挂号预约可议价
                    isSupportWeClinicPay: true, // 是否支持微诊所自助支付
                    isSupportChargeWithArrears: true, // 是否支持欠款收费
                    isSupportCardCharge: true, //是否支持卡项收费
                    isSupportMessageNotification: true, // 是否支持消息推送
                    isSupportRefundRestriction: true, //是否支持 退费条件限制
                    isSupportWholeBillCharge: true, // 是否支持整单收退费设置
                    isSupportDoctorBargainSwitch: true, // 是否支持医生整单议价
                },

                //收费发票设置
                chargeInvoice: {
                    isSupportPaperBill: true, // 是否支持纸质票据
                    isSupportElectronicBill: true, // 是否支持电子票据
                    isSupportAutoElectronicBill: true, // 是否支持自动开具电子发票
                },

                // 产品中心设置
                productCenter: {
                    isSupportOpenInvoicing: true, // 是否支持开发票
                    isSupportRenew: true, // 是否支持续费
                    isSupportUpdateGrade: true, // 是否支持升级版本
                    isSupportShowAccount: true, // 是否支持展示账号数
                    isSupportShowDataSpace: true, // 是否支持展示数据空间
                    isSupportWeClinicUIDesign: true, // 是否支持微诊所UI设计
                    isSupportDesktopDownload: true, // 是否支持桌面端下载
                },

                // 诊所信息设置
                clinicInfo: {
                    isSupportTrustCode: false, // 是否支持信用代码
                    isSupportBusinessScope: false, // 是否支持经营范围
                    isSupportOrganType: true, // 是否支持机构类型
                    isSupportOrganGrade: true, // 是否支持机构登记
                    isSupportBusinessSubject: true, // 是否支持执业许可科目
                    isSupportNationalCode: true, // 是否支持国家医保代码
                    isSupportCompanyWeChat: true, // 是否支持企业微信
                    isSupportBusMode: false, // 是否支持类型
                },

                // 库存
                isSupportWareHouse: true, // 是否支持库房管理设置
                isSupportWarningProcurementForChainAdmin: false, // 总部是否支持预警采购设置
                isSupportInvoiceAction: true, // 是否支持进销存动作设置
                // 是否支持供应商首营申请
                isSupportFirstCampApplicationBySupplier: false,

                // 患者
                patient: {
                    // 患者标签
                    patientTag: {
                        autoSignRule: {
                            isSupportHistoryDiagnosis: true, // 是否支持历史诊断
                            isSupportPreviousHistory: true, // 是否支持既往史
                            isSupportBeLate: true, // 是否支持迟到
                            isSupportNotAttendance: true, // 是否支持缺诊
                        },
                    },
                },

                surgery: {
                    isSupportRelateRoom: false, // 是否支持关联手术室
                },

                // 发药/加工/配送
                dispensing: {
                    supportHospitalDispensingRule: false, // 住院发药规则
                    isSupportWholeBillDispensing: true, // 是否支持整单发退药设置
                },

                medicalOrderRevokeRule: { // 医嘱可撤销相关配置文案
                    label: '项目撤销规则',
                    options: [
                        {
                            label: '项目已执行，支持撤销',
                            tip: '体检项目支持弃检、退项',
                            value: 1,
                        },
                        {
                            label: '项目已执行，不支持撤销',
                            tip: '体检项目不支持弃检、退项',
                            value: 0,
                        },
                    ],
                },

                //区域中心检查检验
                areaInspectionCenter: {
                    centerProjectCreateDialogTitle: '从已有项目中选择',
                    centerProjectCreateTableEmptyContent: '没有项目，请先到诊疗项目设置中新建项目',
                    centerProjectUpdateSourceGoodsLabel: '关联项目',
                },
                // 抗菌药物管理
                antimicrobial: {
                    gradeLevel: 0, // 分级管理, 0: 诊所管家, 1: 医院管家
                },
                // 项目设置
                projectSetting: {
                    name: '项目',
                    basicInfoTitle: '基础信息',
                },
                // 字段设置
                fieldLayoutSetting: {
                    registration: false,
                },
            },
            /**
             * @desc 库存模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Inventory: {
                orderMainNameText: '药品',
                topTabText: '药品/物资',
                searchHint: '药品名称/条形码',
                tableNameText: '药名',
                tableTotalCostText: '药品成本',
                materialText: '物资',
                hasEyeglasses: false,
                showCreateEyeGlassesBtn: false,
                showBatchViewModePharmacy: true, // 批次明细-是否展示库房tab
                shebaoFilterPayModeText: '支付',
                shebaoFilterPayModeAllowOptionText: '优先统筹',
                shebaoFilterPayModeNotAllowOptionText: '不过医保',
                countLabel: '当前库存',
                chainPriceLabel: '总部定价',
                clinicPriceLabel: '门店定价',
                packagePriceLabel: '零售价格',
                goodsTransNameText: '调拨',
                showPriceTypeTips: true, // 是否展示定价方式hover提示
                showCheckOptionInSupplierSelect: true,// 供应商下拉选择展示初始化入库选项
                isSupportClearInventoryOnDelete: false,// 删除药品档案时有剩余库存是否走清空库存流程
                isSupportReCreateArchive: false,// 删除并重新建档
                isSupportManageGoodsArchivesInSubClinic: false,// 连锁子店支持管理档案
                showGoodsApplyListStatusFilter: true,// 库存领用-展示状态筛选select
                visibleShebaoPayModeSelfCost() {
                    // 显示医保支付方式-自费选项（就只控制这个 只在杭州/宁波才有）
                    return (
                        window.$abcSocialSecurity.config.isZhejiangHangzhou ||
                        window.$abcSocialSecurity.config.isZhejiangNingbo
                    );
                },
                showCheckOrderFilterStatus: true, // 是否展示盘点单筛选状态
                allowReOrder: true,
                reportLossErrText: '当前药品库存为0，不可出库',
                isEnableTraceableCode() {
                    // 是否启用追溯码
                    return window.$abcSocialSecurity.config
                        .isMountainwestLvliang || window.$abcSocialSecurity.config.isShandongRizhao; //山西吕梁 | 山东日照
                },
                useRadio: false,
                // 是否支持删除药品资料-【库存药品档案-停用并删除药品资料按钮】
                isSupportDeleteGoodsInfo: true,
                // 是否支持修改入库单-【入库单-hover药品修改按钮】
                isSupportUpdateGoodsInOrder: true,
                // 调价是否支持进价加成模式
                supportAdjustPriceByCost: true,
                // 调价是否支持医保限价
                supportAdjustPriceByPriceLimit: true,
                // 是否支持总部给门店定价-场景：总部下药品详情-门店定价tab
                supportChainAdminAdjustPrice: true,
                // 是否支持创建、修改供应商
                isSupportCreateSupplier: true,
                // 是否启用药店创建供应商弹窗
                isEnablePharmacyCreateSupplier: false,
                // 供应商展示经营资质和经营范围
                showSupplierQualificationAndBusinessScope: false,
                // 是否支持供应商修改配送模式
                isSupportEntrustDelivery: false,
                // 是否支持进价异动预警
                isSupportCostPriceWarning: false,

                // 是否展示利润分类-场景：商品档案-定价信息-利润分类下拉
                showProfitClassification: false,
                // 药品档案-物资是否禁用typeId select
                disabledMaterialSelect: false,
                // 批次信息中是否展示进价变动按钮
                showPurchasePriceChangeButton: false,
                // 门店分布中，是否展示滞销商品按钮
                showSlowSellingGoods: false,
                supportFirstOperation: false, // 是否支持首营操作
                // 获取药品资料时是否需要显示会员价信息
                withMemberTypeInfo: 0,

                // 是否显示医保支付icon
                showShebaoPayIcon: true,
                // 显示医保支付支持的选项
                shebaoPayModeObj: {
                    overAll: {
                        value: ShebaoPayMode.OVERALL,
                        label: '优先统筹',
                    },
                    self: {
                        value: ShebaoPayMode.SELF,
                        label: '优先个账',
                    },
                    noUse: {
                        value: ShebaoPayMode.NO_USE,
                        label: '不过医保',
                    },
                },
                // 停用按钮文案显示
                archiveDetailDisabledBtnText: '药品',

                // 是否支持门店自主定价-到药品维度（以前的自主定价都是到门店的）
                isSupportSubFixedPrice: false,
                // 是否支持商品会员价
                isSupportGoodsMemberPrice: false,
                // 库存明细弹框 - 药品信息tab
                goodsDetailTabName: '药品',
                goodsOutHeaderConfig: [
                    {
                        label: '出库单号',
                        key: 'orderNo',
                        style: {
                            width: '172px',
                            minWidth: '172px',
                            cursor: 'pointer',
                        },
                    },
                    {
                        label: '状态',
                        key: 'status',
                        style: {
                            width: '40px',
                        },
                    },
                    {
                        label: '门店',
                        key: 'clinicName',
                        style: {
                            width: '60px',
                        },
                    },
                    {
                        label: '出库类型',
                        key: 'outType',
                        style: {
                            width: '40px',
                        },
                    },
                    {
                        label: '出库库房',
                        key: 'pharmacy',
                        style: {
                            width: '60px',
                        },
                    },
                    {
                        label: '品种数',
                        key: 'kindCount',
                        style: {
                            width: '40px',
                            textAlign: 'right',
                        },
                    },
                    {
                        label: '数量',
                        key: 'count',
                        style: {
                            width: '40px',
                            textAlign: 'right',
                        },
                    },
                    {
                        label: '金额',
                        key: 'amount',
                        style: {
                            width: '50px',
                            textAlign: 'right',
                        },
                    },
                    {
                        label: '出库人',
                        key: 'createdUser',
                        style: {
                            width: '60px',
                            paddingLeft: '60px',
                        },
                    },
                    {
                        label: '创建日期',
                        key: 'createdDate',
                        style: {
                            width: '40px',
                            textAlign: 'left',
                        },
                    },
                    {
                        label: '出库日期',
                        key: 'outDate',
                        style: {
                            width: '40px',
                            textAlign: 'left',
                        },
                    },
                ],
                needSearchNoStock: true,
                needValidateGoodsOutBatch: false,
                // 效期预警设置
                warnSetConfigOldUI: true,
                //库存报损 审批流
                isShowGoodsOutCanApprove: false,
                isEnableTransInFormFullTransOut: false, // 是否启用入库单整单调出
                isSupportGoodsTransAdd: true,// 是否启用新增调拨单
                isFilterChainClinicsInGoodsTrans: false,// 是否在调拨内过滤总部
                // isSupportDecimalsFourMedicine方法对应的药品类型的小数位数
                fractionDigits: 4,
                // 后勤材料-固定资产使用小弹窗(714px)
                useSmallDialog: true,
                filterChainClinic: false,
                useNewGoodsArchives: true,
                // 是否支持进价加成
                isSupportCostPriceMakeUp: true,
                useAbcTableInGoodsOut: false,
                useAbcTableInGoodsCheck: false,
                // 库存档案支持的商品类型
                supportGoodsTypeIdMap: {
                    [GoodsTypeIdEnum.MEDICINE_WESTERN]: true, //西药
                    [GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES]: true, //中药饮片
                    [GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES]: false, //非配方饮片
                    [GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE]: true, //中药颗粒
                    [GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT]: true, //中成药

                    [GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL]: true, //医疗器械
                    [GoodsTypeIdEnum.MATERIAL_DISINFECTANT]: true, //消毒用品
                    [GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL]: true, //后勤材料
                    [GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS]: true, //固定资产

                    [GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT]: true, //自制成品
                    [GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE]: true, //保健药品
                    [GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD]: true, //保健食品
                    [GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT]: true, //其他商品
                    [GoodsTypeIdEnum.ADDITIONAL_COSMETIC]: true, //化妆品
                },
                // 药品档案-中药饮片支持类型切换
                chineseHerbSliceModifiable: false,
                // 知道type选择一个默认typeId
                defaultGoodsTypeMap: {
                    // 默认西药
                    [GoodsTypeEnum.MEDICINE]: GoodsTypeIdEnum.MEDICINE_WESTERN,
                    // 默认医疗器械
                    [GoodsTypeEnum.MATERIAL]:
                        GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
                    // 默认自制成品
                    [GoodsTypeEnum.GOODS]:
                        GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT,
                },
                batchSelectPlaceholder: '不指定批次',
                operateGoodsAdjustPrice: false, // 定价权限
                operateGoodsArchives: false, // 建档权限
                isExcludeChainAdmin: false, // 是否排除总部
                isEditLossReportBatch: true, // 报损原因出库数量批次是否可编辑
                isEditTransBatch: true, // 调剂单批次是否可编辑
                isEditCheckBatch: true, // 盘点单批次是否可编辑
                isShowLossReportPriceHover: true, // 报损价格hover
                isSupportProductionOut: true, // 是否支持生产出库
                isSupportLockingDetail: true, // 是否支持锁定明细
                isAllotLockingText: '调拨', // 调拨锁库文本
                isDispensingLockingText: '门诊', // 发药锁库文本
                isSupportPrintPriceTag: true, // 打印设置-是否支持价签
                isSupportPrintPriceTagMemberPrice: false, // 价签是否支持会员价
                isSupportPrintPriceTagProfitClassify: true, // 价签是否支持利润分类

                // 是否支持挂网价
                isSupportShebaoListingPrice: true,
                isSupportStockWarningFilterProfitClassify: false, //采购单库存预警筛选是否支持利润分类
                isShowAcctFilterItem: true, // 展示优先个账的筛选项
                isChineseMedicineAutoLinkPrescription: false, //药品资料-中药饮片类型是否自动关联处方药
            },
            /**
             * @desc 统计模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            Statistics: {
                // 统计二级tab 高度
                statTabHeight: '40px',
                // 是否使用统计二级tab
                useLayoutTabsV2: false,
                backGroundColor: '#ffffff',
                showsStatisticsBorder: true,
                // 是否展示慢病统计
                showChronicCarePatientStat: true,
                // 挂号结算和开单业绩是否需要交换顺序
                outpatientRevenueStatSupportSort: false,
                // 是否展示加工结算
                showProcessOrder: false,
                showConsultantStat: false, // 是否展示咨询师业绩
                // 门诊日志-口腔检查
                outpatientLog: {
                    // 是否展示口腔检查
                    showOralExamination: false,
                    // 是否展示眼科检查
                    showOphthalmology: false,
                },
                // 开单业绩明细
                showRevenueDetail: false,
                // 提成报表检查检验类型-检查是否只展示次数提成
                isShowCountCommission: false,
                // 提成报表是否支持眼镜
                isShowEyeglasses: false,
                showAirPharmacyStat: true, // 是否展示空中药房统计
                showProcessStat: true,
                isSearchCommon: true, // 是否支持搜索统用
                businessTollLabel: '营业收费人次',
                memberContributionLabel: '会员充值金额',
                memberRechargePeopleLabel: '会员充值人次',
                businessTrendLabel: '营业收费人次趋势',
                showPromotionSummary: true,
                amountSettingSpecial: false, // 统计口径设置是否特殊处理
                showHospitalFeeCommissionTiming: false,
                showProfitClassification: false,

                operationStat: {
                    showRegistrationFee: true, // 收费日报展示挂号费筛选
                    showOweInfo: true, // 收费明细展示还款合计
                    revenuePersonLabel: '开单人', // 收费明细开单人
                    outpatientLabel: '搜索患者', // 收费明细患者
                    recommendVisitPlaceHolder: '本次推荐',
                    sourcePlaceHolder: '来源',
                    feeTypeAmountLabelDesc: '',
                    productSearchPlaceHolder: '搜索收费项目',
                    showMemberTrend: false,
                    chargeProductSearchPlaceHolder: '搜索项目',
                    showNo: true, // 收费明细 popover 是否展示诊号
                    patientLabel: '患者',
                    dimensionByProduct: false, // 按商品维度统计
                },

                performanceStat: {
                    showRevenuePersonType: true, // 开单业绩人员类型
                    showRevenueEmployeeType: true,
                    doctorPlaceHolder: '人员',
                    showRevenueStatPopover: true, // 开单业绩代录面板
                    showRevenueDepartmentSelect: true, // 开单业绩科室select
                    showRevenueFeeTypeSelect: false, // 开单业绩费用类型select
                    showRevenueVisitSourceSelect: true, // 开单业绩本次推荐费select
                    defaultRevenueDimension: 'employee', // 默认开单业绩维度
                    revenueDimensionOptionsConfig: { //开单业绩 可选维度选项配置
                        employee: {
                            order: 1, visible: true,
                        },
                        department: {
                            order: 2, visible: true,
                        },
                        item: {
                            order: 3, visible: true,
                        },
                        detail: {
                            order: 4, visible: true,
                        },
                    },
                    defaultHospitalRevenueDimension: 'employee',
                    hospitalRevenueDimensionOptionsConfig: { //住院开单业绩 可选维度选项配置
                        employee: {
                            order: 1, visible: true,
                        },
                        product: {
                            order: 2, visible: true,
                        },
                    },
                    productTabName: '项目',
                    revenueStatPersonalTableKey: 'achievement.charge.personnel',
                    revenueStatItemTableKey: 'achievement.charge.goods',
                    revenueStatSheetTableKey: 'achievement.charge.transaction',
                },

                stockEntryStat: {
                    showWareSearch: true,
                    detailTableKey: 'goods.inventory.record',
                    layoutHeightDiff: 178,
                    goodsLabel: '药品',
                    stockEntryTableKey: 'goods.stock.entry',
                },
                financeStatistics: {
                    showPharmacySearch: false,
                },

                promotionStat: {
                    showCardOverview: true, // 卡项统计
                    showCouponOverview: false, // 优惠券统计
                    discountStatSectionTitle: '折扣活动',
                    giftStatSectionTitle: '满减返',
                    showSpecialStatSection: false, // 是否展示特价统计
                    showDiscountStatSection: false, // 是否展示折扣统计
                    showGiftStatSection: false, // 是否展示满赠统计
                    couponSearchPlaceHolder: '全部患者',
                    showPharmacyGiftSection: false, // 是否展示买赠
                },

                commissionStat: {
                    saleCommissionSpecial: false,
                    isEnableHospitalCommissionRule: false, //提成方案 提成项目 住院提成规则
                    isCanSearchOtherGoods: true, // 是否支持搜索费用项类型
                    isSupportNotInventoryItem: true, // 是否支持非库存单品
                    isEnableProfitRulesCommissionRule: false, //提成方案 提成项目 利润规则
                },
                statClinicPlaceholder: '门店',
                // 筛选配置 tableKey
                chargeStat: {
                    sheetTableKey: 'clinic-statistics-operation-charge-sheet',
                    typeTableKey: 'clinic-statistics-operation-charge-type',
                    detailTableKey: 'clinic-statistics-operation-charge-detail',
                    productTableKey: 'common-statistics-operation-charge-product',
                    chargeProductDimension: 'outpatient',
                },

                patientStat: {
                    plistCustomStatHeaderTableKey: 'stat.patient.stat', //患者清单统计 自定义表头
                },

                revenueDetailExportConfig: {
                    sheet: {
                        label: '单据',
                        exportSubTask: 'revenue.detail.transaction',
                    },
                    type: {
                        label: '分类',
                        exportSubTask: 'revenue.detail.classify',
                    },
                    detail: {
                        label: '明细',
                        exportSubTask: 'revenue.detail.items',
                    },
                },
                invoice: {
                    showInvoiceCategory: true, // 开票方式
                    showInvoiceType: true, // 发票类型
                    showInvoiceFeeType: true, // 费用类型
                    showInvoiceNumberScope: true, // 发票号码范围
                },
                chargeExportItemLabel: '项目',
                inventory: {
                    supplierSettlement: {
                        isBatchesDetail: false, // 按批次维度
                    },
                },
            },
            /**
             * @desc 患者模块
             * <AUTHOR>
             * @date 2022-04-15 09:00:16
             */
            CRM: {
                supportAppointmentCard: false,
                isAllowVisitSendMsgByScrm: false, // 允许随访通过企业微信发送信息
                // 是否展示口腔加工记录
                showOralProcessRecord: false,
                isNeedAutoPatientListLeftWidth: false,
                // 随访模版需要分组
                visitModelNeedGroup: false,
                needPatientBaseComponentShowTags: false,
                // 随访模版需要分组
                consultantFilter: false,
                allowVisibleAppFlag: true, //展示企业微信标签
                supportRetail: false, // 支持零售开单
                allowCreatedPatientAndNewOutpatient: false, // 允许创建患者并接诊
                baseInfoTabLabel: '档案详情',
                feeRecordTabLabel: '收费记录',
                showAppointmentRecord: true, //是否展示预约记录
                showOutpatientHistory: true, //是否展示病历记录
                showExamImg: true, //是否展示影像报告
                showVisit: true, //是否展示随访记录
                showInspection: true, //是否展示检查检验记录
                showTreatment: true, //是否展示治疗理疗记录
                hiddenOperator: false, //是否隐藏随访/预约/微信操作栏
                isShowRegLeaveForMember: true, //是否展示会员预留号
                showMemberProfitRate: false,// 是否展示会员毛利率
                cardPatientInfo: {
                    crmBusinessCount: {
                        outpatient: true, //门诊
                        inHospital: false, //住院
                        physicalExamination: false, //体检
                    },
                    isPharmacy: false, //是否药房
                    isShouldGoTagSetting: false, //是否跳转患者/会员标签管理
                },
                retailCountLabel: '零售',
                lastOutpatientInfoLabel: '最近就诊',
                patientLabel: '患者标签',
                sourceName: '首诊来源',
                crmFamily: {
                    isShowOperator: true, //家庭成员 table表头显示操作栏
                    recordCreatedLabel: '时间',
                    renderContentLabel: '内容',
                },
                useCrmDataPermission: true,
                dataPermission: {
                    crmMemberLevelText: '当前字段需要在“诊所设置-成员管理-权限”中配置“患者-会员管理”权限后使用',
                    crmCardText: '当前字段需要在“诊所设置-成员管理-权限”中配置“患者-持卡人管理”权限后使用',
                },
                isShowMemberIcon: true, //是否展示会员icon
                isShowWxIcon: true, //是否展示微信绑定icon
                patientList: {
                    isShowRevisit: true, //患者列表展示随访入口
                    isShowLastOutpatientDateWithDesc: true, //是否显示按就诊时间降序
                    isShowLastPayDateWithDesc: false, // 是否显示按消费时间降序
                },
                patientMember: {
                    isShowMemberItem: false, //新建弹窗表单 是否填写会员
                    isShowReferralDoctorSource: true, //新建弹窗表单 来源是否展示转诊医师
                    isShowDoctorPutSource: true, //新建弹窗表单 来源是否展示医生推荐
                    isShowChangePassword: false, //修改基本信息弹窗 是否允许修改密码
                },
                patientFilter: {
                    isShowWxPatientQuery: true, //筛选患者弹窗 是否展示微信绑定
                    isShowClinicVisit: true, //筛选患者弹窗 是否展示门店初复诊
                    isShowByMedicalFilter: true, //筛选患者弹窗 是否展示按就诊信息筛选
                    isShowNonMember: true, //筛选患者弹窗 会员类型是否展示非会员
                },
                patientMerge: {
                    isShowWx: true, //合并患者弹窗，是否展示微信
                    isMobileItemFirst: false, //合并患者弹窗 手机字段是否在首位
                    isShowOutPatientItem: true, //是否展示门诊字段
                },
                chargeRecord: {
                    chargedLabel: '开单',
                    isShowTotalOweAmount: true, //消费记录表单 是否显示欠费
                    sellerNameLabel: '开单人',
                    viewSalesRecordDetail: false, //查看收费详情是否变为查看销售详情
                },
                cardMemberInfo: {
                    isShowMemberTypeIcon: false, //会员卡片信息 是否展示会员等级入口
                    isShowCardItem: true, //会员卡片信息 是否展示卡项
                    isChangeMemberInfo: false, //会员卡片信息 修改入口 是修改会员信息还是患者信息
                },
                isSupportTherapist: false, //是否支持治疗师
                allowCrmSystemSetting: true,// crm模块是否展示crm设置
                showImportCrm: true,
                isSupportInHospitalInfoMerge: false, // 是否支持住院信息合并
                visitCountByRules: 10, // 通过规则创建随访的上限
                isSupportPrintPointsVoucher: false, // 是否支持打印积分凭证
                modifyCrmNameConfirmText: '修改患者姓名则该患者的历史就诊记录信息也会同时修改',
            },
            /**
             * @desc 医院模块
             * <AUTHOR>
             * @date 2022-12-19 17:50:16
             */
            Hospital: {
                showHandleInpatient: false, // 门诊/儿保更多是否展示住院办理
                wareHouseMemberModuleIds: [
                    8, 105, 106, 107, 108, 109, 51209, 51210, 51211, 512113,
                ], // 库房设置 - 库房成员选择权限 - 只有库存模块才有
                wareHouseMemberPharmacyModuleIds: [
                    8, 105, 106, 107, 108, 109, 51209, 51210, 51211, 512113, 4,
                ], // 库房设置 - 库房成员选择权限 - 库存和药房权限
                cashierDailyReportTableName: '收费日报',
            },
            /**
             * @desc goods search 模块
             * <AUTHOR>
             * @date 2023/05/17 13:59:29
             */
            GoodsSearch: {
                // 目录全量
                categoryList: [
                    {
                        name: '检查项目',
                        key: CATEGORY_TYPE_ENUM.INSPECTION,
                        typeIds: [GoodsTypeIdEnum.EXAMINATION_INSPECTION],
                        jsonType: {
                            type: GoodsTypeEnum.EXAMINATION,
                            subType: [
                                GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION]
                                    .Test,
                            ],
                        },
                        subCategoryList: [
                            {
                                value: SYSTEM_GOODS_TYPE.HOT,
                                name: '常用项目',
                            },
                            ...CATEGORY_DEVICE_TYPE.filter((x) => {
                                return ![
                                    SYSTEM_GOODS_TYPE.EYE_DEVICE_TYPE,
                                    SYSTEM_GOODS_TYPE.CLINICAL_INSPECT,
                                ].includes(x.value);
                            }),
                        ],
                    },
                    ...GoodsCategoryList,
                ],
                inspectionFilterFiled: 'deviceType', // 检查筛选字段
                // 门诊开单处的目录范围
                outpatientCategoryRange: [
                    CATEGORY_TYPE_ENUM.INSPECTION,
                    CATEGORY_TYPE_ENUM.ASSAY,
                    CATEGORY_TYPE_ENUM.TREATMENT,
                    CATEGORY_TYPE_ENUM.MATERIAL,
                    CATEGORY_TYPE_ENUM.PRODUCT,
                    CATEGORY_TYPE_ENUM.COMPOSE,
                    CATEGORY_TYPE_ENUM.OTHER,
                ],
            },

            // 检查
            Inspect: {
                isOphthalmology: false, // 检查是否为眼科检查
                routeKey: InspectClinicRouteKey, // 路由key,医院和诊所之间的差异
                isOpenCreateInspectSheet: false, // 是否开启创建检查单
                isNeedApplySheet: false,
                isNeedCheckPacsDeviceModel: true, // 是否需要检查机构是否开通了某种型号的pacs设备
                isExistAutoReversion: false, // 是否存在自动预约流程
                createWorkListBtnVisible: true, // 上机检查按钮的显示
                isSupportChannelFilter: false, // 是否支持渠道筛选
                validatePACSDevice: true, // 是否需要验证PACS设备
                isSupportInspectNoPrint: true, // 是否支持检查单号打印
            },

            // 医疗文书
            EMR: {
                isSupportQuickSuggest: false, // 是否支持右侧快捷操作
                isSupportMedicalVisit: false, // 是否支持本次就诊
            },

            // 实际打印，注入到打印模板中
            PrintConfig: {
                cashier: {
                    showPatientOrderNo: true, //展示收费小票诊号
                    showChineseDoseCount: true, //展示收费小票中药剂数
                    onlyShowHasContentName: false, //是否只展示有值的name
                    onlyShowHasContentMobile: false, //是否只展示有值的手机号
                    isSupportDoctorInfo: true, // 是否展示医生信息
                    openSheetPersonText: '开单人', // 开单人文案
                    chineseMedicineDetailIsSupportSpecialRequirement: true, // 中药明细支持煎法
                    chineseMedicineDetailIsSupportUnitCount: true, // 中药明细支持单剂克数
                    chineseMedicineDetailIsSupportTotalCount: true, // 中药明细支持总重
                    footerIsSupportWeClinicQrCode: true, // 是否支持展示微诊所二维码
                    isSupportOutpatientBarcode: true, // 是否支持就诊条码
                    medicineNameOptimization: false, // 药名排版优化
                },
                medicalRecord: {
                    style: {
                        isSupportShowThemeColor: false, // 是否支持主题色
                    },
                    content: {
                        isSupportShowInspectResult: false, // 是否支持展示检查结果
                    },
                },
                // 治疗理疗
                treatment: {
                    isSupportPhysiotherapy: true, // 是否支持理疗
                },
            },

            //体检
            PhysicalExamination: {
                examinationAddProjectTitle: '从已有项目关联创建',
            },

            // GSP
            Gsp: {
                showGsp: true,
                showTabCardInHospital: true,
                afterSale: {
                    recall: {
                        showGspRecallTableStatus: false, // 是否显示退货状态
                        showGspRecallTableButton: false, // 是否显示退货按钮
                        gspRecallAddButton: false, // 是否控制新增召回按钮显示
                    },
                    recover: {
                        showGspRecoverTableStatus: false, // 是否显示追回状态
                        showGspRecoverEditTableButton: false, // 是否显示修改按钮
                        gspRecoverAddButton: false, // 是否控制新增追回按钮
                    },
                    adverseReactions: {
                        gspAdverseReactionsAddButton: false, // 是否控制新增按钮
                    },
                },
                check: {
                    showContCardTab: true, // 是否显示收验下层tab
                    showGspReceiveOrderNo: false, // 是否显示收货单号
                },
                storage: {
                    conserve: {
                        gspConservationAddButton: false, // 是否控制新增记录按钮
                        gspConservationPlanButton: false, // 是否控制养护计划按钮
                    },
                    humiture: {
                        gspHumitureAddButton: false, // 是否控制新增记录按钮
                    },
                    environment: {
                        gspEnvironmentAddButton: false, // 是否控制新增记录按钮
                    },
                    clearFunnel: {
                        isMultiPharmacy: true, // 支持多库房
                        isUseClinicBatchComponent: true, // 是否使用诊所批次组件
                    },
                    installFunnel: {
                        isMultiPharmacy: true, // 支持多库房
                        isUseClinicBatchComponent: true, // 是否使用诊所批次组件
                    },
                },
            },

            Icpc: {
                SettleListDialogTemplate,
            },
        };
    }

    static getInstance(hisType) {
        return new BaseClinicViewConfig(hisType);
    }
}
