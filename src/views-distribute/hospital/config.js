import RegistrationAPI from '@/api/registrations/index';
import { AppTabName } from '@/core/constants.js';
import { AppTabId } from '@/core/index.js';
import { PRINT_OPTIONS } from '@/printer/common/constant.js';
import { ExaminationHospitalRouteKey } from '@/router/route-key/examination';
import { InspectHospitalRouteKey } from '@/router/route-key/inspect';
import {
    MODULE_ID_MAP,
    ROLE_SURVEYOR_ID,
    ROLE_OUTPATIENT_SURVEYOR_ID,
    ROLE_HOSPITAL_SURVEYOR_ID,
    ROLE_PHYSICAL_SURVEYOR_ID,
} from '@/utils/constants';
import BaseClinicViewConfig from '@/views-distribute/base-config.js';
// ---- 组件 ----
import { handleDentistry } from '@/views-distribute/hospital/handle-dentistry.js';
import { handleOphthalmology } from '@/views-distribute/hospital/handle-ophthalmology.js';
import { GoodsCategoryList } from '@/views/common/goods-search';
import {
    GoodsSubTypeEnum,
    GoodsTypeEnum,
    GoodsTypeIdEnum,
} from '@abc/constants';
import {
    CATEGORY_DEVICE_TYPE,
    CATEGORY_TYPE_ENUM,
    SYSTEM_GOODS_TYPE,
} from 'views/common/goods-search/constants';
const AppLogoHospital = () => import('views/layout/app-logo-hospital.vue');
import SettleListDialogHospitalTemplate from 'src/views/layout/social-report/hospital/settle-list-dialog.vue';

import hospitalBindRelationImg1 from '@/assets/images/marketing/referral/guide-activity-sample/referrer-bind-relationship-hospital-1.png';
import hospitalH5DefaultShareImg from '@/assets/images/marketing/referral/share/hospital-h5-default-share.png';
import hospitalH5ShareImg from '@/assets/images/marketing/referral/share/hospital-h5-share.png';
import hospitalWeappDefaultShareImg from '@/assets/images/marketing/referral/share/hospital-weapp-default-share.png';
import hospitalWeappShareImg from '@/assets/images/marketing/referral/share/hospital-weapp-share.png';
import hospitalNoticeImg from '@/assets/images/micro-clinic/service-introduction/hospital-notice-brown.png';
import hospitalNoticeBlueImg from '@/assets/images/micro-clinic/service-introduction/hospital-notice-blue.png';
import hospitalNoticeYellowImg from '@/assets/images/micro-clinic/service-introduction/hospital-notice-yellow.png';
import hospitalNoticeCyanImg from '@/assets/images/micro-clinic/service-introduction/hospital-notice-cyan.png';
import hospitalNoticeTopImg from '@/assets/images/announcement/hospital-notice-top-brown.png';
import hospitalNoticeTopBlueImg from '@/assets/images/announcement/hospital-notice-top-blue.png';
import hospitalNoticeTopYellowImg from '@/assets/images/announcement/hospital-notice-top-yellow.png';
import hospitalNoticeTopCyanImg from '@/assets/images/announcement/hospital-notice-top-cyan.png';
import { MedicalRecordTypeEnum } from 'views/outpatient/common/medical-record/utils';

/**
 * 医院管家配置
 */
export default class HospitalViewConfig extends BaseClinicViewConfig {
    constructor(hisType, clinicInfo) {
        super();

        /**
         * @desc 根据业务场景决定是否开启一些功能
         * <AUTHOR>
         * @date 2023-09-22 11:18:47
         */
        handleDentistry.bind(this, clinicInfo)();
        handleOphthalmology.bind(this, clinicInfo)();

        this.hisType = hisType;

        this.viewFeature.feeCompose = true; // 支持费用项组合
        this.viewFeature.supportFeiMu = true; // 支持首页费目

        const { appTabId } = window;

        this.viewDistributeConfig.isFullScreen = true;
        this.viewDistributeConfig.institutionTypeWording = '医院';
        this.viewDistributeConfig.appTabId = appTabId;
        this.viewDistributeConfig.diagnosisTreamentTypeWording = '医嘱';
        this.viewDistributeConfig.showEditionTag = false;
        this.viewDistributeConfig.supportDeactiveEmployeeRelation = true; // 支持停用员工关联(科室、人员、病区)
        this.viewDistributeConfig.supportSeparateOutpatientAndHospital = true; // 支持区分门诊和住院
        this.viewDistributeConfig.templateManagerVersion = 1; // 模板管理版本

        this.viewDistributeConfig.AppLayout.appTabName = AppTabName[appTabId];
        this.viewDistributeConfig.AppHeader.getMenuItemPadding = () => {
            return '0 20px';
        };
        // 工作台展示门店选择器
        this.viewDistributeConfig.AppHeader.showClinicSelector =
            appTabId === AppTabId.HOSPITAL_DASHBOARD;
        this.viewDistributeConfig.AppHeader.showNavMenu =
            [
                AppTabId.HOSPITAL_DASHBOARD,
                AppTabId.TREATMENT,
                AppTabId.REGISTRATION,
                AppTabId.SOCIAL,
                AppTabId.MARKETING,
                AppTabId.MALL,
                AppTabId.SETTING,
            ].indexOf(appTabId) === -1; // 工作台不显示导航菜单
        this.viewDistributeConfig.AppHeader.showWardSelector =
            appTabId === AppTabId.HOSPITAL_NURSE;
        this.viewDistributeConfig.AppHeader.showDepartmentSelector =
            appTabId === AppTabId.HOSPITAL_DOCTOR;
        this.viewDistributeConfig.AppHeader.showStockRoomSelector =
            appTabId === AppTabId.INVENTORY;
        this.viewDistributeConfig.AppHeader.menNavIsCenter = true;

        this.viewDistributeConfig.AppHeader.isEnableSocialMenuItem = () => {
            return location.pathname.startsWith('/hospital/social');
        };

        this.viewDistributeConfig.AppHeader.isEnableMallMenuItem = () => {
            return location.pathname.startsWith('/hospital/mall');
        };

        this.viewDistributeConfig.Outpatient.doctorAdviceInForm = false;
        this.viewDistributeConfig.Outpatient.outpatientConfigScope = 'dep_emp';
        this.viewDistributeConfig.Outpatient.multiMedicalRecord = true;
        this.viewDistributeConfig.Outpatient.defaultMedicalRecordType = MedicalRecordTypeEnum.WESTERN;
        this.viewDistributeConfig.Outpatient.disabledWithDomainSearchMedicine = true;
        this.viewDistributeConfig.Outpatient.supportExamApplySheetView = true;
        this.viewDistributeConfig.Outpatient.isPrintExamApplySheet = true;
        this.viewDistributeConfig.Outpatient.supportAddOtherFee = false;
        this.viewDistributeConfig.Outpatient.isSupportSurgery = true; // 门诊是否支持手术
        this.viewDistributeConfig.Outpatient.supportAddNursing = true;
        this.viewDistributeConfig.Outpatient.showMedicalRecordPrintCount = false;

        this.viewDistributeConfig.Outpatient.routeBasePath = '/his-outpatient/'; // 路由的 basePath，在医院管家下，会放到 his-outpatient 下面
        this.viewDistributeConfig.Outpatient.isShowPrintHospitalizationCertificate = true;
        this.viewDistributeConfig.Outpatient.canCustomizeShebaoChargeType = true;
        this.viewDistributeConfig.Outpatient.doctorAdviseNeedDepartmentId = true;

        this.viewDistributeConfig.ChildHealth.routeBasePath =
            '/his-outpatient/'; // 路由的 basePath，在医院管家下，会放到 his-outpatient 下面

        this.viewDistributeConfig.Cashier.routeBasePath = '/his-charge/'; // 路由的 basePath，在医院管家下，会放到 his-charge 下面
        this.viewDistributeConfig.Cashier.supportSelectPharmacy = true;
        this.viewDistributeConfig.Cashier.showFeeType = true;
        this.viewDistributeConfig.Cashier.usedDiscountNotAllowShebaoSwitch = true;
        this.viewDistributeConfig.Cashier.supportDirectSaleToggle = true;

        this.viewDistributeConfig.Pharmacy.routeBasePath = '/his-pharmacy/'; // 路由的 basePath，在医院管家下，会放到 his-pharmacy 下面
        this.viewDistributeConfig.Pharmacy.showSellerInput = true;

        this.viewDistributeConfig.Inventory.isSupportDeleteGoodsInfo = false;// 医院不允许删除药品资料
        this.viewDistributeConfig.Inventory.isSupportUpdateGoodsInOrder = false;// 医院不允许修改入库单
        this.viewDistributeConfig.Inventory.showCheckOrderFilterStatus = false;// 医院连锁子店不显示状态筛选
        this.viewDistributeConfig.Inventory.isEnableTransInFormFullTransOut = true;// 医院支持入库单整单调出
        this.viewDistributeConfig.Inventory.canAddArchiveByHasHospitalSupplyCenterGoodsModule = true;// 医院能否建档-要判断是否有医院供应中心商品模块
        this.viewDistributeConfig.Inventory.isSupportProductionOut = false;// 不支持生产出库
        this.viewDistributeConfig.Inventory.isSupportMoreLockingDetail = true;// 支持类型20 45 46锁定明细
        this.viewDistributeConfig.Inventory.isSupportPrintPriceTagProfitClassify = false; // 不支持支持利润分类

        this.viewDistributeConfig.Settings.showProductEditionIntroduce = false; // 医院管家隐藏【版本功能列表】按钮
        this.viewDistributeConfig.Settings.showInvoiceActionSetting = false; // 医院管家隐藏【进销存动作设置】模块
        this.viewDistributeConfig.Settings.openSetting.hasWard = true; // 医院管家下达规则可以选病区
        this.viewDistributeConfig.Settings.openSetting.openPositionName = '科室';
        this.viewDistributeConfig.Settings.treatment.isAllowFeatureTherapy = false;
        this.viewDistributeConfig.Settings.treatment.isRelationProductVisible = true;
        this.viewDistributeConfig.Settings.treatment.implementText = '门诊执行';
        this.viewDistributeConfig.Settings.treatment.isShowInHospitalExecute = true; // 治疗理疗是否展示住院执行选项
        this.viewDistributeConfig.Settings.treatment.isIncludeOphthalmologyGoods = true;
        this.viewDistributeConfig.Settings.treatment.isIncludeNurse = true;
        this.viewDistributeConfig.Settings.treatment.isShowCustomTypeName = false;
        this.viewDistributeConfig.Settings.microClinic.isNeedNursingItem = true;
        this.viewDistributeConfig.Settings.microClinic.treatmentText = '治疗';
        this.viewDistributeConfig.Settings.microClinic.isNeedOther = false;
        this.viewDistributeConfig.Settings.microClinic.allowJumpToMarket = false;
        this.viewDistributeConfig.Settings.microClinic.allowJumpToSetting = false;
        this.viewDistributeConfig.Settings.microClinic.serviceIntroImg = {
            noticeImg: hospitalNoticeImg,
            noticeBlueImg: hospitalNoticeBlueImg,
            noticeYellowImg: hospitalNoticeYellowImg,
            noticeCyanImg: hospitalNoticeCyanImg,
        };
        this.viewDistributeConfig.Settings.microClinic.noticeTopImgInfo = {
            noticeTopImg: hospitalNoticeTopImg,
            noticeTopBlueImg: hospitalNoticeTopBlueImg,
            noticeTopYellowImg: hospitalNoticeTopYellowImg,
            noticeTopCyanImg: hospitalNoticeTopCyanImg,
        };

        this.viewDistributeConfig.Settings.microClinic.retrieveMobilePrefix = 'hospital';

        this.viewDistributeConfig.Settings.showGuanBiaoBtn = false; // 是否显示贯标统计按钮；
        this.viewDistributeConfig.Settings.otherTypeGoodsName = '费用项目'; // 医院管家其他类型商品名称变更为费用项目
        this.viewDistributeConfig.Settings.compose.searchGoodsType = [
            { type: GoodsTypeEnum.MEDICINE },
            {
                type: GoodsTypeEnum.MATERIAL,
                subType: [
                    GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials,
                ],
            },
            { type: GoodsTypeEnum.GOODS },
            { type: GoodsTypeEnum.EXAMINATION },
            {
                type: GoodsTypeEnum.TREATMENT,
                subType: [GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Treatment],
            },
            { type: GoodsTypeEnum.NURSE },
            { type: GoodsTypeEnum.OTHER },
            {
                type: GoodsTypeEnum.SURGERY,
                subType: [GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY],
            },
        ];
        this.viewDistributeConfig.Settings.employees.needSplitRole = true;
        this.viewDistributeConfig.Settings.other.isNeedCheckFeeUsed = true;
        this.viewDistributeConfig.Settings.common.isOpenRelationModelInOutpatientSetting = false;
        this.viewDistributeConfig.Settings.print.isShowApplySheetSetting = true;
        this.viewDistributeConfig.Settings.showMedicalCodeColum = false;
        this.viewDistributeConfig.Settings.employees.hasAdminRole = false;
        this.viewDistributeConfig.Settings.employees.isSupportModifyEmployeeNameInPermission = true;
        this.viewDistributeConfig.Settings.examination.sampleGroupIsRelateProject = false;
        this.viewDistributeConfig.Settings.needValidatePower = true;
        this.viewDistributeConfig.Settings.surgery.isSupportRelateRoom = true;
        this.viewDistributeConfig.Settings.showHospitalPharmacyTraceCode = true; // 是否展示住院发药追溯码采集开关
        this.viewDistributeConfig.Settings.isEnableAddPACSServer = true; //是否支持添加pacs服务器
        this.viewDistributeConfig.Settings.isSupportTaxRateClassification = false; //是否支持税率分类

        this.viewDistributeConfig.Settings.medicalOrderRevokeRule = { // 医嘱可撤销相关配置文案
            label: '项目撤销规则',
            options: [
                {
                    label: '项目已执行，支持撤销',
                    tip: '项目已执行住院医嘱支持撤销医嘱，体检项目支持弃检、退项',
                    value: 1,
                },
                {
                    label: '项目已执行，不支持撤销',
                    tip: '项目已执行住院医嘱不支持撤销医嘱，体检项目不支持弃检、退项',
                    value: 0,
                },
            ],
        };

        this.viewDistributeConfig.Settings.projectSetting.name = '医嘱';
        this.viewDistributeConfig.Settings.projectSetting.basicInfoTitle = `${this.viewDistributeConfig.Settings.projectSetting.name}信息`;
        this.viewDistributeConfig.Settings.openSetting.outpatientLockGoodsName = '门诊锁库';


        this.viewDistributeConfig.Hospital.showHandleInpatient = true;
        this.viewDistributeConfig.Hospital.cashierDailyReportTableName = '门诊收费报表';
        this.viewDistributeConfig.Hospital.wareHouseMemberModuleIds = [8, 105, 106, 51209, 51210, 51211, 51212, 512113, 51214];
        this.viewDistributeConfig.Hospital.wareHouseMemberPharmacyModuleIds = [8, 105, 106, 51209, 51210, 51211, 51212, 512113, 51214, 508, 4, 50802];

        this.viewDistributeConfig.Examination.needReportVersion = true;
        this.viewDistributeConfig.Examination.isSupportFastCreateSheet = true; // 是否支持快速开单
        this.viewDistributeConfig.Examination.routeKey = ExaminationHospitalRouteKey; // 路由key

        this.viewDistributeConfig.Examination.inspectCanRelativeProject = true;
        this.viewDistributeConfig.Examination.fetchExaminationEmployee =
                async function () {
                    const { data } = await RegistrationAPI.fetchlistByCondition(
                        {
                            moduleIds: [
                                MODULE_ID_MAP.hospitalExaminationSampleExamination,
                            ],
                        },
                    );
                    return (data.rows || []).filter((r) => {
                        const _roles = new Set([
                            ...r.roles,
                            ROLE_SURVEYOR_ID,
                            ROLE_OUTPATIENT_SURVEYOR_ID,
                            ROLE_HOSPITAL_SURVEYOR_ID,
                            ROLE_PHYSICAL_SURVEYOR_ID,
                        ]);
                        return _roles.size < r.roles.length + 4;
                    });
                };
        this.viewDistributeConfig.Examination.isSupportChannelFilter = true;
        this.viewDistributeConfig.Examination.isSupportShowFeeItem = true;

        this.viewDistributeConfig.Settings.inspect.regModeVisible = true;
        this.viewDistributeConfig.Settings.inspect.isIncludeClinicalInspect = true;
        this.viewDistributeConfig.Settings.inspect.autoReservationSettingVisible = true;
        this.viewDistributeConfig.Settings.projectBindChargeType = 'FEE_ITEM'; // 诊疗项目绑定的收费项目类型 FEE_ITEM - 费用项目
        this.viewDistributeConfig.Settings.openSetting.allowOpenAdviceSupplement = true; // 是否允许开启医嘱补录
        this.viewDistributeConfig.Settings.outpatient.openContinueDiagnoseWithoutReg = false; // 是否允许门诊设置回诊功能
        this.viewDistributeConfig.Settings.outpatient.openMedicalRecordUpdateLimit = true;
        this.viewDistributeConfig.Settings.outpatient.openPrescriptionUpdateLimit = true;
        this.viewDistributeConfig.Settings.outpatient.outpatientSummaryIncomeType = 'hospital';
        this.viewDistributeConfig.Settings.print.examinationSheet.isSupportExaminationNoPrint = false; // 是否支持检查检验单号打印
        this.viewDistributeConfig.Settings.schedule.isShowRegistrationCategory = true; // 是否展示多号种选项
        this.viewDistributeConfig.Settings.reservation.isEnablePayModeSetting = true;
        this.viewDistributeConfig.Settings.isSupportPrintInventoryGoodsIn = false;
        this.viewDistributeConfig.Settings.project.examination.isSupportIdentification = true; // 是否支持互认标识
        this.viewDistributeConfig.Settings.project.inspect.isSupportIdentification = true; // 是否支持互认标识
        this.viewDistributeConfig.Settings.project.registrationFee.isSupportMultipleMode = false; // 是否支持互认标识
        this.viewDistributeConfig.Settings.print.examinationReport.isSupportIdentification = true; // 是否支持互认标识
        this.viewDistributeConfig.Settings.callNumber.isCheckByEdition = false; // 是否需要判断版本（小屏需要大客户版，医院直接购买功能不需要升级版本）
        this.viewDistributeConfig.Settings.print.isSupportMedicalTechnologyPrintConfig = true; // 是否支持医技打印设置

        this.viewDistributeConfig.Settings.charge.isSupportWholeBillCharge = false; // 整单收退费
        this.viewDistributeConfig.Settings.dispensing.supportHospitalDispensingRule = true; // 住院发药规则
        this.viewDistributeConfig.Settings.dispensing.isSupportWholeBillDispensing = false; // 整单发退药
        this.viewDistributeConfig.Settings.antimicrobial.gradeLevel = 1; // 分级管理, 0: 诊所管家, 1: 医院管家

        //区域中心检查检验
        this.viewDistributeConfig.Settings.areaInspectionCenter.centerProjectCreateDialogTitle = '从已有医嘱项选择';
        this.viewDistributeConfig.Settings.areaInspectionCenter.centerProjectCreateTableEmptyContent = '没有医嘱项目，请先到医嘱项目设置中新建项目';
        this.viewDistributeConfig.Settings.areaInspectionCenter.centerProjectUpdateSourceGoodsLabel = '关联医嘱';

        this.viewDistributeConfig.Print.examPrintOptions = {
            examination: PRINT_OPTIONS.EXAMINATION_APPLY_SHEET,
            inspection: PRINT_OPTIONS.INSPECT_APPLY_SHEET,
        };
        this.viewDistributeConfig.Print.allowExamReportPrint = true; // 医院管家产品线支持检查报告打印
        this.viewDistributeConfig.Print.isSupportCashierA5 = false; // 是否支持A5收费单
        this.viewDistributeConfig.Print.registrationEnableCategory = true; // 是否支持A5收费单
        this.viewDistributeConfig.Print.printOptions = Object.assign({}, PRINT_OPTIONS, {
            // 治疗理疗单
            TREATMENT_EXECUTE: {
                label: '治疗单',
                value: '治疗单',
                templateKey: 'treatmentExecute',
                printConfigKey: 'treatmentExecute',
            },
        });
        this.viewDistributeConfig.Print.treatmentExecuteLabel = '治疗单';

        this.viewDistributeConfig.PrintConfig.treatment.isSupportPhysiotherapy = false; // 是否支持理疗

        // 检查
        this.viewDistributeConfig.Inspect.routeKey = InspectHospitalRouteKey; // 路由key,诊所和医院的差异
        this.viewDistributeConfig.Inspect.isOpenCreateInspectSheet = true; // 是否开启创建检查单
        this.viewDistributeConfig.Inspect.isNeedApplySheet = true; // 是否开启创建检查单
        this.viewDistributeConfig.Inspect.isNeedCheckPacsDeviceModel = false; // 是否需要检查机构是否开通了某种型号的pacs设备
        this.viewDistributeConfig.Inspect.isExistAutoReversion = true; // 是否需要检查机构是否开通了某种型号的pacs设备
        this.viewDistributeConfig.Inspect.createWorkListBtnVisible = false; // 上机检查按钮的显示
        this.viewDistributeConfig.Inspect.isSupportChannelFilter = true; // 是否支持渠道筛选
        this.viewDistributeConfig.Inspect.validatePACSDevice = false;
        this.viewDistributeConfig.Inspect.isSupportInspectNoPrint = false;

        // ---- 差异化组件 ----
        this.viewComponents.AppLogo = AppLogoHospital;

        /**
         * @desc goods search模块
         * <AUTHOR>
         * @date 2023/05/17 13:59:29
         */
        let currentGoodsCategoryList = GoodsCategoryList;
        if (!this.viewFeature.supportFilterEyeGlasses) {
            currentGoodsCategoryList = currentGoodsCategoryList.filter((item) => {
                return item.key !== CATEGORY_TYPE_ENUM.EYEGLASSES;
            });
        }
        this.viewDistributeConfig.GoodsSearch = {
            categoryList: [
                {
                    name: '检查项目',
                    key: CATEGORY_TYPE_ENUM.INSPECTION,
                    typeIds: [GoodsTypeIdEnum.EXAMINATION_INSPECTION],
                    jsonType: {
                        type: GoodsTypeEnum.EXAMINATION,
                        subType: [GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test],
                    },
                    subCategoryList: [
                        {
                            value: SYSTEM_GOODS_TYPE.HOT,
                            name: '常用项目',
                        },
                        ...CATEGORY_DEVICE_TYPE,
                    ],
                },
                ...currentGoodsCategoryList,
            ],
            inspectionFilterFiled: 'extendSpec', // 检查筛选字段
            outpatientCategoryRange: [
                CATEGORY_TYPE_ENUM.INSPECTION,
                CATEGORY_TYPE_ENUM.ASSAY,
                CATEGORY_TYPE_ENUM.TREATMENT,
                CATEGORY_TYPE_ENUM.NURSE,
                CATEGORY_TYPE_ENUM.MATERIAL,
                CATEGORY_TYPE_ENUM.PRODUCT,
                CATEGORY_TYPE_ENUM.COMPOSE,
                CATEGORY_TYPE_ENUM.EYEGLASSES,
                CATEGORY_TYPE_ENUM.SURGERY,
            ],
        };

        // 营销模块
        this.viewDistributeConfig.Marketing.referrer.h5ShareImg = hospitalH5ShareImg;
        this.viewDistributeConfig.Marketing.referrer.weappShareImg = hospitalWeappShareImg;
        this.viewDistributeConfig.Marketing.referrer.weappDefaultShareImg = hospitalWeappDefaultShareImg;
        this.viewDistributeConfig.Marketing.referrer.h5DefaultShareImg = hospitalH5DefaultShareImg;
        this.viewDistributeConfig.Marketing.referrer.bindRelationImg1 = hospitalBindRelationImg1;
        this.viewDistributeConfig.Marketing.adaptRegType = function(type) {
            if (type.goodsType === GoodsTypeIdEnum.REGISTRATION && type.children && type.children.length) {
                const children = type.children.find((subType) => {
                    return subType.goodsType === GoodsTypeIdEnum.REGISTRATION && subType.goodsSubType === 0;
                });

                if (children) {
                    children.name = '普通门诊';
                }
            }

            return type;
        };
        this.viewDistributeConfig.Marketing.isFilterOperations = false;

        //患者模块
        this.viewDistributeConfig.CRM.isNeedAutoPatientListLeftWidth = true;
        this.viewDistributeConfig.CRM.isSupportTherapist = true;
        this.viewDistributeConfig.CRM.cardPatientInfo.crmBusinessCount = {
            outpatient: true, //门诊
            inHospital: true, //住院
            physicalExamination: true, //体检
        };
        this.viewDistributeConfig.CRM.isSupportInHospitalInfoMerge = true; // 是否支持住院信息合并
        this.viewDistributeConfig.CRM.isSupportDischargeVisit = true; // 是否支持出院随访
        this.viewDistributeConfig.CRM.modifyCrmNameConfirmText = '修改患者姓名则该患者的历史文书也会同时修改';
        this.viewDistributeConfig.CRM.dataPermission = {
            crmMemberLevelText: '当前字段需要在“机构/人员设置-人员管理-权限”中配置“患者-会员管理”权限后使用',
            crmCardText: '当前字段需要在“机构/人员设置-人员管理-权限”中配置“患者-持卡人管理”权限后使用',
        };

        // 执行模块
        this.viewDistributeConfig.Treatment.billSupportOtherFee = false;

        // 挂号模块
        this.viewDistributeConfig.Registration.supportShortFlow = true;

        // 文书模块
        this.viewDistributeConfig.EMR.isSupportQuickSuggest = true;
        this.viewDistributeConfig.EMR.isSupportMedicalVisit = true;

        // 统计模块
        const { Statistics } = this.viewDistributeConfig;
        Statistics.financeStatistics.showPharmacySearch = true;
        Statistics.financeStat = true;
        Statistics.chargeStat.sheetTableKey = 'hospital-statistics-operation-charge-sheet';
        Statistics.chargeStat.typeTableKey = 'hospital-statistics-operation-charge-advice';
        Statistics.chargeStat.detailTableKey = 'hospital-statistics-operation-charge-detail';
        Statistics.performanceStat.showRevenueVisitSourceSelect = false;
        Statistics.performanceStat.showRevenueStatPopover = false;
        Statistics.performanceStat.defaultRevenueDimension = 'detail';
        Statistics.performanceStat.revenueDimensionOptionsConfig = { //开单业绩 可选维度选项配置
            employee: {
                order: 1, visible: true,
            },
            department: {
                order: 2, visible: true,
            },
            item: {
                order: 4, visible: true,
            },
            detail: {
                order: 3, visible: true,
            },
        };
        Statistics.performanceStat.defaultHospitalRevenueDimension = 'product';
        Statistics.performanceStat.hospitalRevenueDimensionOptionsConfig = { //开单业绩 可选维度选项配置
            product: {
                order: 2, visible: true,
            },
        };
        Statistics.performanceStat.showRevenuePersonType = true;
        Statistics.performanceStat.showRevenueEmployeeType = true;
        Statistics.performanceStat.doctorPlaceHolder = '开单人';
        Statistics.performanceStat.showRevenueFeeTypeSelect = true;
        Statistics.performanceStat.revenueStatSheetTableKey = 'achievement.charge.transaction.hospital';
        Statistics.performanceStat.revenueStatItemTableKey = 'achievement.charge.goods.hospital';
        Statistics.chargeStat.chargeProductDimension = 'summary';
        Statistics.patientStat.plistCustomStatHeaderTableKey = 'stat.his.marketing.patient.stat';
        Statistics.commissionStat.isEnableHospitalCommissionRule = true;
        Statistics.commissionStat.isCanSearchOtherGoods = false;
        Statistics.showHospitalFeeCommissionTiming = false;
        Statistics.revenueDetailExportConfig = {
            sheet: {
                label: '按单据',
                exportSubTask: 'revenue.detail.transaction',
            },
            type: {
                label: '按分类',
                exportSubTask: 'revenue.detail.advice',
            },
            detail: {
                label: '按项目',
                exportSubTask: 'revenue.detail.project',
            },
        };
        Statistics.chargeExportItemLabel = '医嘱';

        //体检模块
        this.viewDistributeConfig.PhysicalExamination.examinationAddProjectTitle = '从已有医嘱项关联创建';

        //GSP
        this.viewDistributeConfig.Gsp.afterSale.recall.gspRecallAddButton = true;
        this.viewDistributeConfig.Gsp.afterSale.recover.gspRecoverAddButton = true;
        this.viewDistributeConfig.Gsp.afterSale.adverseReactions.gspAdverseReactionsAddButton = true;
        this.viewDistributeConfig.Gsp.storage.conserve.gspConservationAddButton = true;
        this.viewDistributeConfig.Gsp.storage.conserve.gspConservationPlanButton = true;
        this.viewDistributeConfig.Gsp.storage.humiture.gspHumitureAddButton = true;
        this.viewDistributeConfig.Gsp.storage.environment.gspEnvironmentAddButton = true;
        this.viewDistributeConfig.Gsp.showTabCardInHospital = false;

        this.viewDistributeConfig.WeClinic.supportWeShopPurchase = true;
        this.viewDistributeConfig.WeClinic.supportSetupHomePageStatistics = false;

        // ICPC
        this.viewDistributeConfig.Icpc = {
            SettleListDialogTemplate: SettleListDialogHospitalTemplate,
        };
    }

    static getInstance(hisType, clinicInfo) {
        console.log('%c view 分发【医院管家】业务配置', 'background: #5199f8; padding: 4px; font-weight: bold; color: white'); // eslint-disable-next-line no-console
        return new HospitalViewConfig(hisType, clinicInfo);
    }
}
