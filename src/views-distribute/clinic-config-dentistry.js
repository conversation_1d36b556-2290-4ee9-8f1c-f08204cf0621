import BaseClinicViewConfig from '@/views-distribute/base-config.js';
const RegistrationTable = () => import('@/views-dentistry/registration/table');

// 组件
const CashierOutpatientInfoDiagnosis = () => import('@/views-dentistry/cashier/components/cashier-outpatient-info-diagnosis.vue');
const DentistryMedicalRecord = () => import('@/views-dentistry/outpatient/common/medical-record/index.vue');
const MedicalSettingPopover =
    () => import('@/views-dentistry/outpatient/common/outpatient-setting-popover/medical-setting-popover.vue');
const PrescriptionSettingPopover =
    () => import('@/views-dentistry/outpatient/common/outpatient-setting-popover/prescription-setting-popover.vue');

import dentistryProductExampleImg from '@/assets/images/<EMAIL>';
import dentistryNotOpenImg from '@/assets/images/reservation-item/dentistry-not-open.png';
import dentistryProductDetailImg from '@/assets/images/reservation-item/<EMAIL>';
import dentistryProductShowImg from '@/assets/images/reservation-item/<EMAIL>';

import dentistryH5ShareImg from '@/assets/images/marketing/referral/share/dentistry-h5-share.png';
import dentistryWeappShareImg from '@/assets/images/marketing/referral/share/dentistry-weapp-share.png';
import dentistryWeappDefaultShareImg from '@/assets/images/marketing/referral/share/dentistry-weapp-default-share.png';
import dentistryH5DefaultShareImg from '@/assets/images/marketing/referral/share/dentistry-h5-default-share.png';
import { parseDiagnosis } from '@/filters/index.js';
import { PRINT_OPTIONS } from '@/printer/common/constant.js';
import {
    DefaultMedicalRecord,
    MedicalRecordStructKey,
    MedicalRecordStructList,
} from 'src/views-dentistry/outpatient/common/medical-record/constants.js';
import {
    formatDentistry2Text, MedicalRecordTypeEnum,
} from 'views/outpatient/common/medical-record/utils.js';
import tableStyleConfig from 'src/views-distribute/style-config/dentistry.js';

/**
 * @desc 口腔管家 配置
 * <AUTHOR>
 * @date 2022-04-15 08:55:53
 */
export default class DentistryClinicViewConfig extends BaseClinicViewConfig {
    constructor(hisType) {
        super();
        this.hisType = hisType;
        /**
         * @desc 功能点
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewFeature.therapy = false; // 理疗
        this.viewFeature.familyDoctor = false; // 家庭医生
        this.viewFeature.chronicRecovery = false; // 慢病康复
        this.viewFeature.continueMR = false; // 自助续方
        this.viewFeature.childHealth = false; // 儿保
        this.viewFeature.inventoryMedicine = false; // 库存主要是药品
        this.viewFeature.treatOnline = false; // 网络问诊
        this.viewFeature.hospital = false; // 长护，住院
        this.viewFeature.oralProcess = true; // 义齿加工
        this.viewFeature.supportRatioPrice = true; // 按价格的比例（折扣）来议价
        this.viewFeature.supportPatchOrder = true; // 支持补单（创建单据的时候选择其它时间）
        /**
         * @desc 差异组件
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewComponents.medicalRecord = DentistryMedicalRecord;
        this.viewComponents.medicalSettingPopover = MedicalSettingPopover;
        this.viewComponents.cashierOutpatientInfoDiagnosis = CashierOutpatientInfoDiagnosis;
        this.viewComponents.prescriptionSettingPopover = PrescriptionSettingPopover;
        this.viewComponents.registrationTable = RegistrationTable;
        // this.viewComponents.Inspect = Inspect;
        // this.viewComponents.InspectForm = InspectForm;

        this.viewDistributeConfig.isSupportDrawer = false;

        /**
         * @desc 读取诊断方式
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.getDisplayDiagnosisText = (medicalRecord) => {
            const {
                extendDiagnosisInfos = [], diagnosis = '',
            } = medicalRecord || {};
            if (extendDiagnosisInfos && extendDiagnosisInfos.length) {
                return formatDentistry2Text(extendDiagnosisInfos) || '';
            }
            return parseDiagnosis(diagnosis) || '';
        };

        this.viewDistributeConfig.chargeItemSupportDoctorNurse = true;
        this.viewDistributeConfig.institutionTypeWording = '口腔';

        /**
         * @desc 打印相关
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.Print.printOptions = Object.assign({}, PRINT_OPTIONS, {
            // 治疗理疗单
            TREATMENT_EXECUTE: {
                label: '治疗单',
                value: '治疗单',
                templateKey: 'treatmentExecute',
                printConfigKey: 'treatmentExecute',
                filename: '治疗单',
            },
            // 输注执行单
            INFUSION_EXECUTE: {
                label: '输注单',
                value: '输注单',
                templateKey: 'infusionExecute',
                printConfigKey: 'infusionExecute',
                filename: '输注单',
            },
            INFUSION_TREATMENT: {
                label: '输液注射治疗',
                value: '输液注射治疗',
                filename: '输液注射治疗',
            },
        });
        this.viewDistributeConfig.Print.treatmentExecuteLabel = '治疗单';
        this.viewDistributeConfig.Print.isSupportCashierA5 = false; // 是否支持A5收费单
        this.viewDistributeConfig.Settings.print.showChineseLayoutForHechiSetting = false;
        this.viewDistributeConfig.Settings.print.showChineseMedicineSetting = false;
        this.viewDistributeConfig.Settings.print.showProcessInfoSetting = false;
        this.viewDistributeConfig.Settings.print.showTicketsChineseMedicineSetting = false;
        this.viewDistributeConfig.Settings.print.showDispensingTicketsChineseMedicine = false;
        this.viewDistributeConfig.Settings.print.showTicketsChineseMedicineDetailSetting = false;
        this.viewDistributeConfig.Settings.print.showTagChineseRemark = false;
        this.viewDistributeConfig.Settings.charge.isSupportDoctorBargainSwitch = false;

        /**
         * 工作台
         */
        this.viewDistributeConfig.Dashboard.todoOutpatientRouterPath = '/crm';
        this.viewDistributeConfig.Dashboard.judgeHasOutpatientByRole = true;


        /**
         * @desc 执行模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        // 执行站右侧sidebar是否展示就诊历史
        this.viewDistributeConfig.Treatment.sidebarShowHistory = true;
        this.viewDistributeConfig.Treatment.productFormCanAddToothNo = true;
        this.viewDistributeConfig.Treatment.showExecuteRecord = false;
        this.viewDistributeConfig.Treatment.showAddTreatmentForm = false;

        // 营销模块
        this.viewDistributeConfig.Marketing.referrer.h5ShareImg = dentistryH5ShareImg;
        this.viewDistributeConfig.Marketing.referrer.weappShareImg = dentistryWeappShareImg;
        this.viewDistributeConfig.Marketing.referrer.weappDefaultShareImg = dentistryWeappDefaultShareImg;
        this.viewDistributeConfig.Marketing.referrer.h5DefaultShareImg = dentistryH5DefaultShareImg;

        /**
         * @desc 挂号模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.Registration.routerName = '预约';
        this.viewDistributeConfig.Registration.settings.regsHiddenMedicalRecord = false;
        this.viewDistributeConfig.Registration.settings.regsHiddenReCommend = false;
        // 病历结构开关
        this.viewDistributeConfig.Registration.medicalRecordStruct = {
            chiefComplaint: 1, // 主诉
            presentHistory: 1, // 现病史
            pastHistory: 1, // 既往史
            dentistryExaminations: 1, // 口腔检查
            epidemiologicalHistory: 1, // 流行病学史
            preDiagnosisAttachments: 1, // 附件上传
        };
        // 挂号是否开启身份证与年龄生日联动 1开启 0关闭
        this.viewDistributeConfig.Registration.idCardLinkageSwitch = 1; //口腔管家默认开启 普通门诊默认关闭
        this.viewDistributeConfig.Registration.isOpenWechatWindowRegisterAppointment = true;//是否开启微信聊天窗口预约挂号功能
        this.viewDistributeConfig.Registration.isOpenAppointProject = true;
        this.viewDistributeConfig.Registration.isRegistrationListPerformance = true; // 预约列表优化 仅口腔诊所
        this.viewDistributeConfig.Registration.audioOpen = true;
        this.viewDistributeConfig.Registration.showDiagnosing = true; // 口腔筛选是否展示接诊
        this.viewDistributeConfig.Registration.showConsultant = true; // 口腔是否展示咨询师
        this.viewDistributeConfig.Registration.showDiscontinuation = true; // 口腔是否开启停诊功能
        /**
         * @desc 门诊模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.Outpatient.productFormCanAddToothNo = true;
        this.viewDistributeConfig.Outpatient.supportAddCNPR = false;
        // 门诊右侧sidebar是否展示就诊历史
        this.viewDistributeConfig.Outpatient.sidebarShowHistory = true;
        this.viewDistributeConfig.Outpatient.sidebarShowReport = false;

        this.viewDistributeConfig.Outpatient.doctorAdviceInForm = false;
        this.viewDistributeConfig.Outpatient.multiMedicalRecord = false;
        this.viewDistributeConfig.Outpatient.defaultMedicalRecordType = MedicalRecordTypeEnum.ORAL;
        // 历史处方 快捷按钮
        this.viewDistributeConfig.Outpatient.showHistoryPrescription = false;
        // 历史病历 快捷按钮
        this.viewDistributeConfig.Outpatient.showHistoryMR = false;
        this.viewDistributeConfig.Outpatient.westernPrescriptionNameText = '成药处方';
        this.viewDistributeConfig.Outpatient.employeePrescriptionConfigKey = 'oralPrescription';
        this.viewDistributeConfig.Outpatient.supportAddExternalPR = false;
        //诊疗项目备注选择穴位
        this.viewDistributeConfig.Outpatient.showDTRemarkMeridian = false;
        //诊疗项目支持录入天数
        this.viewDistributeConfig.Outpatient.showDTDayCol = false;
        this.viewDistributeConfig.Outpatient.defaultMedicalRecord = DefaultMedicalRecord;
        this.viewDistributeConfig.Outpatient.medicalRecordStructKey = MedicalRecordStructKey;
        this.viewDistributeConfig.Outpatient.medicalRecordStructList = MedicalRecordStructList;

        // 病历中口腔检查使用新key dentistryExaminations
        this.viewDistributeConfig.Outpatient.medicalRecordIsNewOralKey = true;

        // 小工具支持新版预约看板
        this.viewDistributeConfig.Outpatient.supportNewRegistrationBoard = true;
        // 是否支持发起新的预约
        this.viewDistributeConfig.Outpatient.supportNewRegistration = true;

        this.viewDistributeConfig.Outpatient.tableStyleConfig = tableStyleConfig.Outpatient;

        // 患者模块-预约记录支持卡片显示
        this.viewDistributeConfig.CRM.supportAppointmentCard = true;
        this.viewDistributeConfig.CRM.needPatientBaseComponentShowTags = true;
        this.viewDistributeConfig.CRM.visitModelNeedGroup = true;
        this.viewDistributeConfig.CRM.allowVisibleAppFlag = false;
        this.viewDistributeConfig.CRM.supportRetail = true;

        /**
         * @desc 收费模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        // 右侧sidebar是否展示就诊历史
        this.viewDistributeConfig.Cashier.sidebarShowHistory = true;
        this.viewDistributeConfig.Cashier.productFormCanAddToothNo = true;
        this.viewDistributeConfig.Cashier.sidebarShowTreatmentCustomList = true;
        this.viewDistributeConfig.Cashier.chargeTitleUnify = true;
        this.viewDistributeConfig.Cashier.supportAddOutpatientSheet = true;
        this.viewDistributeConfig.Cashier.showOnlineChargePlaySetting = false;
        this.viewDistributeConfig.Cashier.showContinueSheetChargePlaySetting = false;
        this.viewDistributeConfig.Cashier.showProcessSetting = false;
        this.viewDistributeConfig.Cashier.supportAddConsultant = true;


        // 药房模块
        this.viewDistributeConfig.Pharmacy.showProcessTab = false;
        this.viewDistributeConfig.Pharmacy.showChinesePrescriptionNum = false;
        this.viewDistributeConfig.Pharmacy.showProcessNum = false;

        /**
         * @desc 检查检验模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.Examination.routerName = '检验';
        this.viewDistributeConfig.Examination.templateClinicType = 'dentistryChainId';
        /**
         * @desc 微诊所模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.WeClinic.reservationsDoctorSortRouterName = '医生排序（预约）';
        this.viewDistributeConfig.WeClinic.caseHistoryIntroduce = '包括：口腔检查、诊断、处置、医嘱';
        this.viewDistributeConfig.WeClinic.treatmentTitle = '展示检查、治疗信息';
        this.viewDistributeConfig.WeClinic.tabs = ['homepage', 'clinic', 'doctor-sort', 'reservation-doctor-sort'];
        this.viewDistributeConfig.WeClinic.medicalRecordOptions = [
            {
                label: '主诉',
                key: 'chiefComplaint',
                value: '牙疼',
            },
            {
                label: '检查',
                key: 'dentistryExaminations',
                value: '牙周检查',
            },
            {
                label: '诊断',
                key: 'diagnosis',
                value: '急性上呼吸道感染',
            },
            {
                label: '处置',
                key: 'disposals',
                value: '根管治疗',
            },
        ];

        /**
         * @desc 设置模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.Settings.examinations.examinationTestDescribe = '影像检查项目，如全景片、CBCT、小牙片';
        this.viewDistributeConfig.Settings.examinations.defaultSubType = 2;
        this.viewDistributeConfig.Settings.examinations.defaultCombineType = 0;
        this.viewDistributeConfig.Settings.examinations.showCombineDescribe = false;
        this.viewDistributeConfig.Settings.showProcessDispensingSettings = false;
        this.viewDistributeConfig.Settings.template.medicalRecordStruct = {
            chiefComplaint: 1, // 主诉
            presentHistory: 1, // 现病史
            pastHistory: 1, // 既往史
            obstetricalHistory: 1, // 月经婚育史
            epidemiologicalHistory: 1, // 流行病史
            physicalExamination: 1, // 体格检查
            auxiliaryExaminations: 1, // 辅助检查
            diagnosis: 1, // 诊断
            dentistryExaminations: 1, // 牙科口腔检查
            treatmentPlans: 1, // 治疗计划
            disposals: 1, // 处置
            doctorAdvice: 1, // 医嘱
            allergicHistory: 1, // 过敏史
        };
        this.viewDistributeConfig.Settings.template.showEmrTemplateSetting = true;
        this.viewDistributeConfig.Settings.template.showClassicPRCataloguesAndClinicalPRCatalogues = false,
        this.viewDistributeConfig.Settings.template.hiddenExecuteTemplateSetting = true,
        this.viewDistributeConfig.Settings.template.hiddenChildGrowthCareTemplateSetting = true,
        this.viewDistributeConfig.Settings.dashboardKey = 'oralDashboard';
        this.viewDistributeConfig.Settings.registeredChainRouterTabLabel = '预约项目'; // 设置index tabs label
        this.viewDistributeConfig.Settings.registeredClinicRouterTabLabel = '预约设置'; // 设置index tabs label
        this.viewDistributeConfig.Settings.callNumber.newOpenSwitchMethods = true;
        this.viewDistributeConfig.Settings.reservation.reservationDentistry = true;
        this.viewDistributeConfig.Settings.reservation.fetchReservationNotFromAPI = true;
        this.viewDistributeConfig.Settings.showMigration = true; // 是否展示口腔诊所一键迁移 = true,
        this.viewDistributeConfig.Settings.outpatient.showChinesePrescriptionSupportMix = false; // 是否展示饮片、颗粒混开策略,
        this.viewDistributeConfig.Settings.showDecoctionSettings = false;
        this.viewDistributeConfig.Settings.showAirPharmacySettings = false;
        this.viewDistributeConfig.Settings.showMergeChinesePrescriptionSetting = false;
        this.viewDistributeConfig.Settings.microClinic.showSelfServicePrescription = false;
        this.viewDistributeConfig.Settings.treatment.isShowOpenFormConfig = false; // 是否显示开单设置
        this.viewDistributeConfig.Settings.openSetting.outpatientLockGoodsName = '门诊/咨询锁定库存'; // 门诊锁库文案

        /**
         * @desc 设置模块 -- 预约设置
         * <AUTHOR>
         * @date 2022-12-08 14:03:45
         */
        this.viewDistributeConfig.Settings.reservation.productExampleImg = dentistryProductExampleImg;
        this.viewDistributeConfig.Settings.reservation.productShowImg = dentistryProductShowImg;
        this.viewDistributeConfig.Settings.reservation.productDetailImg = dentistryProductDetailImg;
        this.viewDistributeConfig.Settings.reservation.productNotOpenImg = dentistryNotOpenImg;

        /**
         * @desc 设置模块 -- 门诊设置
         * <AUTHOR>
         * @date 2023-09-14 10:32:24
         */
        this.viewDistributeConfig.Settings.outpatient.openContinueDiagnoseWithoutReg = true;
        this.viewDistributeConfig.Settings.outpatient.quickDiagnosis = false;
        this.viewDistributeConfig.Settings.product.productType = 'dentistry';

        /**
         * @desc 设置模块 -- 字段设置
         * <AUTHOR>
         * @date 2025-01-14 17:06:28
         */
        this.viewDistributeConfig.Settings.fieldLayoutSetting.registration = true;

        /**
         * @desc 库存模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.Inventory.orderMainNameText = '物品';
        this.viewDistributeConfig.Inventory.topTabText = '耗材/药品';
        this.viewDistributeConfig.Inventory.searchHint = '耗材名称/条形码';
        this.viewDistributeConfig.Inventory.tableNameText = '品名';
        this.viewDistributeConfig.Inventory.tableTotalCostText = '成本';
        this.viewDistributeConfig.Inventory.isSupportCostPriceMakeUp = false;
        this.viewDistributeConfig.Inventory.isSupportProductionOut = false;

        /**
         * @desc 统计模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.Statistics.showChronicCarePatientStat = false;
        this.viewDistributeConfig.Statistics.outpatientRevenueStatSupportSort = true;
        this.viewDistributeConfig.Statistics.showProcessOrder = true;
        this.viewDistributeConfig.Statistics.showConsultantStat = true;
        this.viewDistributeConfig.Statistics.outpatientLog.showOralExamination = true;
        this.viewDistributeConfig.Statistics.showRevenueDetail = true;
        this.viewDistributeConfig.Statistics.isShowCountCommission = false;
        this.viewDistributeConfig.Statistics.showAirPharmacyStat = false;
        this.viewDistributeConfig.Statistics.showProcessStat = false;
        this.viewDistributeConfig.Statistics.chargeStat.sheetTableKey = 'dentistry-statistics-operation-charge-sheet';
        this.viewDistributeConfig.Statistics.chargeStat.typeTableKey = 'dentistry-statistics-operation-charge-type';
        this.viewDistributeConfig.Statistics.chargeStat.detailTableKey = 'dentistry-statistics-operation-charge-detail';
        this.viewDistributeConfig.Statistics.stockEntryStat.layoutHeightDiff = 130;
        /**
         * @desc 患者模块
         * <AUTHOR>
         * @date 2022-04-15 09:00:16
         */
        this.viewDistributeConfig.CRM.showOralProcessRecord = true;
        this.viewDistributeConfig.CRM.isAllowVisitSendMsgByScrm = true;
        this.viewDistributeConfig.CRM.consultantFilter = true;
        this.viewDistributeConfig.CRM.allowCreatedPatientAndNewOutpatient = true;
        this.viewDistributeConfig.CRM.allowCrmSystemSetting = false;
        /**
         * @desc AppHeader
         * <AUTHOR>
         * @date 2023/06/26 16:54:09
         */
        this.viewDistributeConfig.AppHeader.isFixedLeftNav = true;
        this.viewDistributeConfig.AppHeader.getSocialAfterItemName = () => {
            return 'treatment';
        };

        this.viewDistributeConfig.AppHeader.getMenuItemPadding = () => '5px';
        this.viewDistributeConfig.AppHeader.showNavIcon = true;
        // 医保放在哪个菜单后面-取routeName
        this.viewDistributeConfig.AppHeader.getSocialAfterItemName = () => {
            return 'examination';
        },
        this.viewDistributeConfig.AppHeader.showNavCrmTodo = true,

        /**
         * 工作台模块
         */
        this.viewDistributeConfig.Dashboard.patientBulletinBoard.showDispensingAndTreatment = false;
    }

    static getInstance(hisType) {
        console.log('%c view 分发【口腔管家】业务配置', 'background: #5199f8; padding: 4px; font-weight: bold; color: white'); // eslint-disable-next-line no-console
        return new DentistryClinicViewConfig(hisType);
    }
}
