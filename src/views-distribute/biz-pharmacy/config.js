import BaseClinicViewConfig from '@/views-distribute/base-config.js';
import {
    PRINT_DEFAULT_CONFIG_KEY, ROLE_PHARMACY_DOCTOR,
} from 'utils/constants';
import { ShebaoPayMode } from 'views/inventory/goods/archives/components/social-code-autocomplete/constant';
import AppClinicDropdownIconPharmacy from 'views/layout/app-clinic-dropdown-icon-pharmacy.vue';
import {
    GoodsTypeEnum, GoodsTypeIdEnum,
} from '@abc/constants';

const AppFullscreenBtnPharmacy = () => import('views/layout/app-fullscreen-btn-pharmacy.vue');
const AppZoomBtnReferencePharmacy = () => import('views/layout/app-zoom-btn-reference-pharmacy.vue');
const AppNoticeReferencePharmacy = () => import('views/layout/app-notice-reference-pharmacy.vue');

/**
 * @desc 药店管家 配置
 * <AUTHOR>
 */
export default class PharmacyViewConfig extends BaseClinicViewConfig {
    constructor(hisType) {
        super();
        this.hisType = hisType;

        this.viewFeature.theme = false;
        this.viewFeature.therapy = false;

        this.viewComponents.AppClinicDropdownIcon = AppClinicDropdownIconPharmacy;
        this.viewComponents.AppZoomBtnReference = AppZoomBtnReferencePharmacy;
        this.viewComponents.AppNoticeReference = AppNoticeReferencePharmacy;
        this.viewComponents.AppFullscreenBtn = AppFullscreenBtnPharmacy;

        // this.viewDistributeConfig.AppHeader.isFixedLeftNav = true;
        this.viewDistributeConfig.AppHeader.showNavIcon = true;
        // 是否启用商城菜单项
        this.viewDistributeConfig.AppHeader.isEnableMallMenuItem = () => {
            return true;
        };
        // 工作台模块
        this.viewDistributeConfig.Dashboard.isSupportStockWarning = true;
        this.viewDistributeConfig.Dashboard.isUpdateCmsPush = true;
        // 使用组件库2.0
        this.viewDistributeConfig.useAbcUIV2 = true;
        // 药店环境需要转换中药饮片名称，相关逻辑可以用这个开关控制
        this.viewDistributeConfig.needTransGoodsClassificationName = true;
        // 转换中药饮片为配方饮片，只是展示层面
        this.viewDistributeConfig.transGoodsClassificationName = (name) => {
            if (name === '中药饮片') {
                return '配方饮片';
            }
            return name;
        };
        // 临时处理 药店不支持浅色主题
        // 药房支持浅色主题后移除
        this.viewDistributeConfig.isForceDarkTheme = true;
        this.viewDistributeConfig.institutionTypeWording = '药店';
        this.viewDistributeConfig.organTypeWording = '机构';
        this.viewDistributeConfig.customerLabel = '会员';
        this.viewDistributeConfig.roleLabel = '顾客';
        this.viewDistributeConfig.statisticText = '报表';
        this.viewDistributeConfig.pharmacyArchiveFixPricePermission = true;
        this.viewDistributeConfig.bindRelationType = 0;
        this.viewDistributeConfig.Settings.chainShowEmployeeFormRole = true;
        this.viewDistributeConfig.Settings.isEnableCustomizeRole = true;
        this.viewDistributeConfig.Settings.isSupportFinanceInvoice = false;
        this.viewDistributeConfig.Settings.isSupportPaperInvoice = false;
        this.viewDistributeConfig.Settings.isSupportDefaultInvoice = false;
        this.viewDistributeConfig.Settings.invoiceWriteOffText = '自动冲红';
        this.viewDistributeConfig.Settings.isSupportTicketNumberManagement = false;
        this.viewDistributeConfig.Marketing.integralShowPointsGoodsDeduction = false;
        this.viewDistributeConfig.Marketing.isShowNonMember = false;
        this.viewDistributeConfig.Marketing.overview.isShowWxCount = false;
        this.viewDistributeConfig.Marketing.usePharmacyGoodsSelect = true;
        this.viewDistributeConfig.Marketing.member = {
            isShowMemberWxCharge: false,
            isShowWxChargeRule: false,
            isShowUpgradeSetting: false,
        };
        this.viewDistributeConfig.Marketing.member.isShowMemberPriceGoods = true;
        this.viewDistributeConfig.Marketing.allowIntegralRulesSettingByProfit = true;
        this.viewDistributeConfig.Cashier.showOwedAmount = false;
        this.viewDistributeConfig.Cashier.showOweAmount = false;
        this.viewDistributeConfig.Cashier.showSalesDetail = true;

        this.viewDistributeConfig.CRM.baseInfoTabLabel = '会员档案';
        this.viewDistributeConfig.CRM.feeRecordTabLabel = '消费记录';
        this.viewDistributeConfig.CRM.showAppointmentRecord = false;
        this.viewDistributeConfig.CRM.showOwedAmount = false;
        this.viewDistributeConfig.CRM.showOutpatientHistory = false;
        this.viewDistributeConfig.CRM.showExamImg = false;
        this.viewDistributeConfig.CRM.showVisit = false;
        this.viewDistributeConfig.CRM.showTreatment = false;
        this.viewDistributeConfig.CRM.showInspection = false;
        this.viewDistributeConfig.CRM.hiddenOperator = true;
        this.viewDistributeConfig.CRM.isShowRegLeaveForMember = false;
        this.viewDistributeConfig.CRM.showMemberProfitRate = true;
        this.viewDistributeConfig.CRM.useCrmDataPermission = false;
        this.viewDistributeConfig.CRM.cardPatientInfo = {
            crmBusinessCount: {
                outpatient: false, //门诊
                inHospital: false, //住院
                physicalExamination: false, //体检
            },
            isPharmacy: true,
            isShouldGoTagSetting: true,
        };
        this.viewDistributeConfig.CRM.retailCountLabel = '消费次数';
        this.viewDistributeConfig.CRM.lastOutpatientInfoLabel = '最近消费';
        this.viewDistributeConfig.CRM.patientLabel = '标签';
        this.viewDistributeConfig.CRM.sourceName = '来源';
        this.viewDistributeConfig.CRM.isSupportPrintPointsVoucher = true;
        this.viewDistributeConfig.CRM.crmFamily = {
            isShowOperator: false,
            recordCreatedLabel: '消费时间',
            renderContentLabel: '消费项目',
        };
        this.viewDistributeConfig.CRM.showImportCrm = false;
        this.viewDistributeConfig.CRM.isShowMemberIcon = false;
        this.viewDistributeConfig.CRM.isShowWxIcon = false;
        this.viewDistributeConfig.CRM.patientList = {
            isShowRevisit: false,
            isShowLastOutpatientDateWithDesc: false,
            isShowLastPayDateWithDesc: true,
        };
        this.viewDistributeConfig.CRM.patientMember = {
            isShowMemberItem: true,
            isShowReferralDoctorSource: false,
            isShowDoctorPutSource: false,
            isShowChangePassword: true,
        };
        this.viewDistributeConfig.CRM.patientFilter = {
            isShowWxPatientQuery: false,
            isShowClinicVisit: false,
            isShowByMedicalFilter: false,
            isShowNonMember: false,
        };
        this.viewDistributeConfig.CRM.patientMerge = {
            isShowWx: false,
            isMobileItemFirst: true,
            isShowOutPatientItem: false,
        };
        this.viewDistributeConfig.CRM.chargeRecord = {
            chargedLabel: '消费',
            isShowTotalOweAmount: false, //消费记录表单 是否显示欠费
            sellerNameLabel: '销售人',
            viewSalesRecordDetail: true,
        };
        this.viewDistributeConfig.CRM.cardMemberInfo = {
            isShowMemberTypeIcon: true,
            isShowCardItem: false,
            isChangeMemberInfo: true,
        };

        this.viewDistributeConfig.Inventory.goodsTransNameText = '调剂';
        this.viewDistributeConfig.Inventory.orderMainNameText = '商品';
        this.viewDistributeConfig.Inventory.goodsDetailTabName = '商品';
        this.viewDistributeConfig.Inventory.materialText = '械材';
        this.viewDistributeConfig.Inventory.searchHint = '商品名称/条码';
        this.viewDistributeConfig.Inventory.supportAdjustPriceByCost = false;
        this.viewDistributeConfig.Inventory.supportAdjustPriceByPriceLimit = false;
        this.viewDistributeConfig.Inventory.supportChainAdminAdjustPrice = false;
        this.viewDistributeConfig.Inventory.showProfitClassification = true;
        this.viewDistributeConfig.Inventory.disabledMaterialSelect = false;
        this.viewDistributeConfig.Inventory.showBatchViewModePharmacy = false;
        this.viewDistributeConfig.Inventory.goodsCheckOrderAddGoodsAllBatch = true;
        this.viewDistributeConfig.Inventory.showPurchasePriceChangeButton = true;
        this.viewDistributeConfig.Inventory.showSlowSellingGoods = true;
        this.viewDistributeConfig.Inventory.isSupportCostPriceWarning = true;
        this.viewDistributeConfig.Inventory.showSupplierQualificationAndBusinessScope = true;
        this.viewDistributeConfig.Inventory.isShowGoodsAutocompleteShortId = true;
        this.viewDistributeConfig.Inventory.isSupportClearInventoryOnDelete = true;
        this.viewDistributeConfig.Inventory.isSupportReCreateArchive = true;
        this.viewDistributeConfig.Inventory.isSupportManageGoodsArchivesInSubClinic = true;
        // 药店-日均销量改为30日销量
        this.viewDistributeConfig.Inventory.showGoodsDetailDisTableLastMonthSellCount = true;
        this.viewDistributeConfig.Inventory.showShebaoPayIcon = false;
        // this.viewDistributeConfig.Inventory.useRadio = true;
        // 药店-使用单独定价模式
        this.viewDistributeConfig.Inventory.useIndividualPricingModel = true;
        // 药店-不支持进价加成模式
        this.viewDistributeConfig.Inventory.isSupportCostPriceMakeUp = false;
        // 药店不展示停用具体的类型
        this.viewDistributeConfig.Inventory.archiveDetailDisabledBtnText = '';
        // 药店-使用新版药品档案
        this.viewDistributeConfig.Inventory.useNewGoodsArchives = true;
        // 药店-使用新版预警设置
        this.viewDistributeConfig.Inventory.useNewClinicWarnSet = true;
        // 药品-供应商不展示盘点入库
        this.viewDistributeConfig.Inventory.showCheckOptionInSupplierSelect = false;
        // 药店-后勤材料-固定资产使用小弹窗
        this.viewDistributeConfig.Inventory.useSmallDialog = true;
        this.viewDistributeConfig.Inventory.useAbcTableInGoodsOut = true;
        this.viewDistributeConfig.Inventory.useAbcTableInGoodsCheck = true;
        this.viewDistributeConfig.Inventory.allowReOrder = false;
        this.viewDistributeConfig.Inventory.isFilterChainClinicsInGoodsTrans = true;
        this.viewDistributeConfig.Inventory.reportLossErrText = '当前商品批次无库存，无法出库';
        this.viewDistributeConfig.Inventory.supportFirstOperation = true;
        this.viewDistributeConfig.Inventory.shebaoPayModeObj = {
            overAll: {
                value: ShebaoPayMode.OVERALL,
                label: '允许支付',
            },
            noUse: {
                value: ShebaoPayMode.NO_USE,
                label: '不允许支付',
            },
        };

        this.viewDistributeConfig.Inventory.supportGoodsTypeIdMap = {
            [GoodsTypeIdEnum.MEDICINE_WESTERN]: true, //西药
            [GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES]: true, //中药饮片更名为配方饮片
            [GoodsTypeIdEnum.MEDICINE_NON_PRESCRIPTION_PIECES]: true, //非配方饮片
            [GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE]: false, //中药颗粒
            [GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT]: true, //中成药

            [GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL]: true, //医疗器械
            [GoodsTypeIdEnum.MATERIAL_DISINFECTANT]: true, //消毒用品
            [GoodsTypeIdEnum.MATERIAL_LOGISTICS_MATERIAL]: false, //后勤材料
            [GoodsTypeIdEnum.MATERIAL_FIXED_ASSETS]: false, //固定资产

            [GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT]: false, //自制成品
            [GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE]: true, //保健药品
            [GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD]: true, //保健食品
            [GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT]: true, //其他商品
            [GoodsTypeIdEnum.ADDITIONAL_COSMETIC]: true, //化妆品
        };
        // 药品档案-中药饮片支持类型切换
        this.viewDistributeConfig.Inventory.chineseHerbSliceModifiable = true;
        this.viewDistributeConfig.Inventory.warnSetConfigOldUI = false;
        // 是否支持盘点单状态筛选
        this.viewDistributeConfig.Inventory.showCheckOrderFilterStatus = true;
        // 是否支持盘点审批流
        this.viewDistributeConfig.Inventory.isSupportGoodsCheckApprovalProcess = true;
        this.viewDistributeConfig.Inventory.defaultGoodsTypeMap = {
            // 默认西药
            [GoodsTypeEnum.MEDICINE]: GoodsTypeIdEnum.MEDICINE_WESTERN,
            // 默认医疗器械
            [GoodsTypeEnum.MATERIAL]: GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
            // 默认保健药品
            [GoodsTypeEnum.GOODS]: GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE,
        };
        this.viewDistributeConfig.Inventory.isShowGoodsOutCanApprove = true;
        this.viewDistributeConfig.Inventory.operateGoodsAdjustPrice = true;
        this.viewDistributeConfig.Inventory.operateGoodsArchives = true;
        this.viewDistributeConfig.Inventory.filterChainClinic = true;
        this.viewDistributeConfig.Inventory.isExcludeChainAdmin = true;
        this.viewDistributeConfig.Inventory.isEditLossReportBatch = false;
        this.viewDistributeConfig.Inventory.isEditTransBatch = false;
        this.viewDistributeConfig.Inventory.isEditCheckBatch = false;
        this.viewDistributeConfig.Inventory.isShowLossReportPriceHover = false;
        this.viewDistributeConfig.Inventory.isSupportProductionOut = false;// 不支持生产出库
        this.viewDistributeConfig.Inventory.isAllotLockingText = '调剂';
        this.viewDistributeConfig.Inventory.isDispensingLockingText = '零售';
        this.viewDistributeConfig.Inventory.isSupportStockWarningFilterProfitClassify = true;
        this.viewDistributeConfig.Inventory.isSupportGoodsMemberPrice = true; // 商品会员价
        this.viewDistributeConfig.Inventory.withMemberTypeInfo = 1; // 商品获取会员价信息
        this.viewDistributeConfig.Inventory.isSupportOrderCloudLocking = true;// 订单云锁库
        this.viewDistributeConfig.Inventory.isChineseMedicineAutoLinkPrescription = true;// 药品资料-中药饮片类型是否自动关联处方药
        this.viewDistributeConfig.Inventory.isShowAcctFilterItem = false;// 展示优先个账的筛选项

        this.viewDistributeConfig.Statistics.useLayoutTabsV2 = true; // 使用layout-tabs-v2
        this.viewDistributeConfig.Statistics.isSupportPrintReconciliationInfo = true; // 药店零售-对账信息打印
        this.viewDistributeConfig.Statistics.statClinicPlaceholder = '总部/门店';
        this.viewDistributeConfig.Statistics.statTabHeight = '48px';
        this.viewDistributeConfig.Statistics.backGroundColor = '#f5f7fb';
        this.viewDistributeConfig.Statistics.amountSettingSpecial = true;
        this.viewDistributeConfig.Statistics.businessTollLabel = '消费人次';
        this.viewDistributeConfig.Statistics.memberContributionLabel = '会员贡献收入';
        this.viewDistributeConfig.Statistics.memberRechargePeopleLabel = '会员消费人次';
        this.viewDistributeConfig.Statistics.showPromotionSummary = false;
        this.viewDistributeConfig.Statistics.businessTrendLabel = '消费人次趋势';
        this.viewDistributeConfig.Statistics.operationStat.showRegistrationFee = false;
        this.viewDistributeConfig.Statistics.operationStat.showOweInfo = false;
        this.viewDistributeConfig.Statistics.operationStat.revenuePersonLabel = '销售人';
        this.viewDistributeConfig.Statistics.operationStat.showNo = false;
        this.viewDistributeConfig.Statistics.operationStat.patientLabel = '顾客';
        this.viewDistributeConfig.Statistics.operationStat.outpatientLabel = '搜索顾客';
        this.viewDistributeConfig.Statistics.operationStat.recommendVisitPlaceHolder = '来源';
        this.viewDistributeConfig.Statistics.operationStat.sourcePlaceHolder = '开单来源';
        this.viewDistributeConfig.Statistics.operationStat.feeTypeAmountLabelDesc = '分类收入金额 = 收费金额 - 退费金额';
        this.viewDistributeConfig.Statistics.operationStat.showMemberTrend = true;
        this.viewDistributeConfig.Statistics.operationStat.productSearchPlaceHolder = '搜索商品';
        this.viewDistributeConfig.Statistics.operationStat.dimensionByProduct = true;
        this.viewDistributeConfig.Statistics.performanceStat.showRevenuePersonType = false;
        this.viewDistributeConfig.Statistics.performanceStat.showRevenueStatPopover = false;
        this.viewDistributeConfig.Statistics.performanceStat.showRevenueDepartmentSelect = false;
        this.viewDistributeConfig.Statistics.showsStatisticsBorder = false;
        this.viewDistributeConfig.Statistics.performanceStat.revenueDimensionOptionsConfig = { //开单业绩 可选维度选项配置
            employee: {
                order: 1, visible: true,
            },
            department: {
                order: 2, visible: false,
            },
            item: {
                order: 3, visible: true,
            },
            detail: {
                order: 4, visible: true,
            },
        };
        this.viewDistributeConfig.Statistics.performanceStat.revenueStatPersonalTableKey = 'achievement.charge.personnel.pharmacy';
        this.viewDistributeConfig.Statistics.performanceStat.revenueStatItemTableKey = 'achievement.charge.goods.pharmacy';
        this.viewDistributeConfig.Statistics.performanceStat.revenueStatSheetTableKey = 'achievement.charge.transaction.pharmacy';
        this.viewDistributeConfig.Statistics.stockEntryStat.showWareSearch = false;
        this.viewDistributeConfig.Statistics.stockEntryStat.detailTableKey = 'stat.pharmacy.goods.inventory.record';
        this.viewDistributeConfig.Statistics.stockEntryStat.layoutHeightDiff = 0;
        this.viewDistributeConfig.Statistics.stockEntryStat.goodsLabel = '商品';

        this.viewDistributeConfig.Statistics.performanceStat.doctorPlaceHolder = '销售人';
        this.viewDistributeConfig.Statistics.performanceStat.productTabName = '商品';
        this.viewDistributeConfig.Statistics.promotionStat.showCardOverview = false;
        this.viewDistributeConfig.Statistics.promotionStat.showSpecialStatSection = true;
        this.viewDistributeConfig.Statistics.promotionStat.showCouponOverview = true;
        this.viewDistributeConfig.Statistics.promotionStat.showDiscountStatSection = true;
        this.viewDistributeConfig.Statistics.promotionStat.showGiftStatSection = true;
        this.viewDistributeConfig.Statistics.promotionStat.showPharmacyGiftSection = true;
        this.viewDistributeConfig.Statistics.promotionStat.couponSearchPlaceHolder = '搜索顾客';
        this.viewDistributeConfig.Statistics.promotionStat.discountStatSectionTitle = '折扣';
        this.viewDistributeConfig.Statistics.promotionStat.giftStatSectionTitle = '满减赠';
        this.viewDistributeConfig.Statistics.operationStat.chargeProductSearchPlaceHolder = '搜索商品';
        this.viewDistributeConfig.Statistics.commissionStat.saleCommissionSpecial = true;
        this.viewDistributeConfig.Statistics.commissionStat.isSupportNotInventoryItem = false;
        this.viewDistributeConfig.Statistics.commissionStat.isEnableProfitRulesCommissionRule = true;
        this.viewDistributeConfig.Statistics.chargeStat.sheetTableKey = 'pharmacy-statistics-operation-charge-sheet';
        this.viewDistributeConfig.Statistics.chargeStat.typeTableKey = 'pharmacy-statistics-operation-charge-advice';
        this.viewDistributeConfig.Statistics.chargeStat.detailTableKey = 'pharmacy-statistics-operation-charge-detail';
        this.viewDistributeConfig.Statistics.chargeStat.productTableKey = 'pharmacy-statistics-operation-charge-product';
        this.viewDistributeConfig.Statistics.showProfitClassification = true;
        this.viewDistributeConfig.Statistics.revenueDetailExportConfig = {
            sheet: {
                label: '按流水',
                exportSubTask: 'revenue.detail.transaction',
            },
            type: {
                label: '按分类',
                exportSubTask: 'revenue.detail.classify',
            },
            detail: {
                label: '按商品',
                exportSubTask: 'revenue.detail.items',
            },
        };
        this.viewDistributeConfig.Statistics.chargeExportItemLabel = '商品';

        this.viewDistributeConfig.Statistics.invoice.showInvoiceCategory = false;
        this.viewDistributeConfig.Statistics.invoice.showInvoiceType = false;
        this.viewDistributeConfig.Statistics.invoice.showInvoiceFeeType = false;
        this.viewDistributeConfig.Statistics.invoice.showInvoiceNumberScope = false;
        this.viewDistributeConfig.Statistics.inventory.supplierSettlement.isBatchesDetail = true;
        this.viewDistributeConfig.Statistics.retailStat = {
            retailOrderTableKey: 'retail.order',
            cashierWorkSummaryTableKey: 'cashier.work.summary',
        };

        // 设置
        this.viewDistributeConfig.Settings.schedule.isShowOutpatientSchedule = false;
        this.viewDistributeConfig.Settings.schedule.isUseScheduleSettingControlPermission = true;
        this.viewDistributeConfig.Settings.schedule.isScheduleWithConsultingRoom = false;

        this.viewDistributeConfig.Settings.isSupportChargeAbcSetting = false;
        this.viewDistributeConfig.Settings.isSupportChargeInvoiceSetting = true;

        this.viewDistributeConfig.Settings.isSupportPrintMedicalDocumentSetting = false;
        this.viewDistributeConfig.Settings.isSupportPrintRegistrationTicketSetting = false;
        this.viewDistributeConfig.Settings.isSupportPrintDispenseTicketSetting = false;
        this.viewDistributeConfig.Settings.isSupportPrintTagSetting = false;
        this.viewDistributeConfig.Settings.isSupportPrintConfigMedicalBills = false;
        this.viewDistributeConfig.Settings.isSupportPrintMedicalBillsFeeList = false;
        this.viewDistributeConfig.Settings.isSupportPrintInventoryGoodsIn = false;// 打印设置-是否支持库存单据
        this.viewDistributeConfig.Settings.isSupportPrintReceipt = false;

        this.viewDistributeConfig.Settings.employees.isSupportPracticeProfile = false;
        this.viewDistributeConfig.Settings.employees.isSupportBusinessDate = false;
        this.viewDistributeConfig.Settings.employees.isSupportFamilyDoctorQualification = false;
        this.viewDistributeConfig.Settings.employees.isSupportPrescribeRight = false;
        this.viewDistributeConfig.Settings.employees.isSupportJobSelect = false;
        this.viewDistributeConfig.Settings.employees.isSupportMultipointPractice = false;
        this.viewDistributeConfig.Settings.employees.checkIsShowDoctorSocialCode = ({
            roles,
        }) => {
            return roles.includes(ROLE_PHARMACY_DOCTOR);
        };
        this.viewDistributeConfig.Settings.employees.isSupportDepartment = false;
        //收费设置
        this.viewDistributeConfig.Settings.charge.isSupportChargeSheetAutoClose = false;
        this.viewDistributeConfig.Settings.charge.isSupportChargeConditionLimit = false;
        this.viewDistributeConfig.Settings.charge.isSupportDoctorSelfBargain = false;
        this.viewDistributeConfig.Settings.charge.isSupportTreatmentSheetBargain = false;
        this.viewDistributeConfig.Settings.charge.isSupportRegistrationBargain = false;
        this.viewDistributeConfig.Settings.charge.isSupportWeClinicPay = false;
        this.viewDistributeConfig.Settings.charge.isSupportChargeWithArrears = false;
        this.viewDistributeConfig.Settings.charge.isSupportCardCharge = false;
        this.viewDistributeConfig.Settings.charge.isSupportMessageNotification = false;
        this.viewDistributeConfig.Settings.charge.isSupportRefundRestriction = false;
        this.viewDistributeConfig.Settings.charge.isSupportWholeBillCharge = false;// 整单收退费
        // 发药设置
        this.viewDistributeConfig.Settings.dispensing.isSupportWholeBillDispensing = false;// 整单发退药

        // 收费发票设置
        this.viewDistributeConfig.Settings.chargeInvoice.isSupportPaperBill = false;
        this.viewDistributeConfig.Settings.chargeInvoice.isSupportElectronicBill = false;
        this.viewDistributeConfig.Settings.chargeInvoice.isSupportAutoElectronicBill = false;

        // 收费小票打印设置
        this.viewDistributeConfig.Settings.print.defaultConfigKey = PRINT_DEFAULT_CONFIG_KEY.PHARMACY;
        this.viewDistributeConfig.Settings.print.chargeTicket.isSupportTitleSetAll = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.isSupportOutpatientBarcode = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.isSupportDoctorInfo = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.isSupportPatientOrderNo = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.openSheetPersonText = '销售员';
        this.viewDistributeConfig.Settings.print.chargeTicket.contentIsSupportCompose = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.contentIsSupportExam = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.contentIsSupportRegistration = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.contentIsSupportTreatment = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.contentIsSupportOtherFee = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.contentIsSupportComposeDetail = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.chineseMedicineDetailIsSupportSpecialRequirement = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.chineseMedicineDetailIsSupportUnitCount = true;
        this.viewDistributeConfig.Settings.print.chargeTicket.chineseMedicineDetailIsSupportTotalCount = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.footerIsSupportWeClinicQrCode = false;
        this.viewDistributeConfig.Settings.print.chargeTicket.isUsePharmacyExampleData = true;

        // 医保结算单打印配置
        this.viewDistributeConfig.Settings.print.socialSettlePrint.isPrintPharmacySocialSettle = true;

        //产品中心
        this.viewDistributeConfig.Settings.productCenter.isSupportWeClinicUIDesign = false;
        this.viewDistributeConfig.Settings.productCenter.isSupportRenew = false;
        this.viewDistributeConfig.Settings.productCenter.isSupportUpdateGrade = false;
        this.viewDistributeConfig.Settings.productCenter.isSupportShowAccount = false;
        this.viewDistributeConfig.Settings.productCenter.isSupportShowDataSpace = false;
        this.viewDistributeConfig.Settings.showProductEditionIntroduce = false;

        // 追溯码设置
        this.viewDistributeConfig.Settings.traceCodeHasDispensingPreCheck = false;

        // 库存
        this.viewDistributeConfig.Inventory.isRequiredGoodsCheckBatch = true;
        this.viewDistributeConfig.Inventory.isRequiredGoodsTransBatch = true;
        this.viewDistributeConfig.Inventory.isRequiredGoodsApplyBatch = true;
        this.viewDistributeConfig.Inventory.showPriceTypeTips = true;
        this.viewDistributeConfig.Inventory.showGoodsApplyListStatusFilter = false;
        this.viewDistributeConfig.Inventory.fractionDigits = 4;
        this.viewDistributeConfig.Inventory.countLabel = '可售库存';
        this.viewDistributeConfig.Inventory.packagePriceLabel = '售价';
        this.viewDistributeConfig.Inventory.chainPriceLabel = '连锁统一定价';
        this.viewDistributeConfig.Inventory.clinicPriceLabel = '门店自主定价';
        this.viewDistributeConfig.Inventory.shebaoFilterPayModeText = '医保';
        // 药店-购进下采购/要货tab文案
        this.viewDistributeConfig.Inventory.purchaseAndRequireText = '采购';
        this.viewDistributeConfig.Inventory.shebaoFilterPayModeAllowOptionText = '允许支付';
        this.viewDistributeConfig.Inventory.shebaoFilterPayModeNotAllowOptionText = '不允许支付';
        this.viewDistributeConfig.Inventory.visibleShebaoPayModeSelfCost = function () {
            return false;
        };
        this.viewDistributeConfig.Inventory.isSupportCreateSupplier = true;
        this.viewDistributeConfig.Inventory.isEnablePharmacyCreateSupplier = true;
        this.viewDistributeConfig.Inventory.isSupportEntrustDelivery = true;
        this.viewDistributeConfig.Inventory.isSupportSubFixedPrice = true;
        this.viewDistributeConfig.Inventory.showGoodsInfoPositionField = true;
        // 药店-支持商品档案中的“所属经营范围”字段
        this.viewDistributeConfig.Inventory.isSupportBusinessScopeInGoodsArchives = true;
        this.viewDistributeConfig.Inventory.isSupportDrugAdministrationCenterCode = true;
        // 药品详情tab-供应商商品绑定
        this.viewDistributeConfig.Inventory.isSupportSupplierGoodsBind = true;
        // 价签是否支持会员价
        this.viewDistributeConfig.Inventory.isSupportPrintPriceTagMemberPrice = true;
        // 是否支持挂网价
        this.viewDistributeConfig.Inventory.isSupportShebaoListingPrice = false;
        this.viewDistributeConfig.Inventory.goodsOutHeaderConfig = [
            {
                label: '出库单号',
                key: 'orderNo',
                style: {
                    width: '160px',
                    minWidth: '160px',
                },
            },
            {
                label: '状态',
                key: 'status',
                style: {
                    width: '40px',
                    textAlign: 'center',
                },
            },
            {
                label: '门店',
                key: 'clinicName',
                style: {
                    width: '60px',
                    paddingLeft: '30px',
                },
            },
            {
                label: '品种数',
                key: 'kindCount',
                style: {
                    width: '40px',
                    textAlign: 'right',
                },
            }, {
                label: '数量',
                key: 'count',
                style: {
                    width: '50px',
                    textAlign: 'right',
                },
            }, {
                label: '金额',
                key: 'amount',
                style: {
                    width: '40px',
                    textAlign: 'right',
                },
            }, {
                label: '出库人',
                key: 'createdUser',
                style: {
                    width: '60px',
                    paddingLeft: '60px',
                },
            }, {
                label: '创建日期',
                key: 'createdDate',
                style: {
                    width: '40px',
                    textAlign: 'left',
                },
            }, {
                label: '出库日期',
                key: 'outDate',
                style: {
                    width: '40px',
                    textAlign: 'left',
                },
            },
        ];
        this.viewDistributeConfig.Inventory.needSearchNoStock = false;
        this.viewDistributeConfig.Inventory.needValidateGoodsOutBatch = true;
        this.viewDistributeConfig.Inventory.isSupportFirstCampApplicationBySupplier = true;
        this.viewDistributeConfig.Inventory.batchSelectPlaceholder = '选择批次';

        // 机构信息设置
        this.viewDistributeConfig.Settings.clinicInfo.isSupportBusinessScope = true;
        this.viewDistributeConfig.Settings.clinicInfo.isSupportTrustCode = true;
        this.viewDistributeConfig.Settings.clinicInfo.isSupportBusinessSubject = false;
        this.viewDistributeConfig.Settings.clinicInfo.isSupportCompanyWeChat = false;
        this.viewDistributeConfig.Settings.clinicInfo.isSupportNationalCode = false;
        this.viewDistributeConfig.Settings.clinicInfo.isSupportOrganGrade = false;
        this.viewDistributeConfig.Settings.clinicInfo.isSupportOrganType = false;
        this.viewDistributeConfig.Settings.clinicInfo.isSupportBusMode = true;

        // 库存
        this.viewDistributeConfig.Settings.isSupportInvoiceAction = false;
        this.viewDistributeConfig.Settings.isSupportWareHouse = false;
        // 总部是否支持预警采购设置
        this.viewDistributeConfig.Settings.isSupportWarningProcurementForChainAdmin = true;

        // 医保放在哪个菜单后面-取routeName
        this.viewDistributeConfig.AppHeader.getSocialAfterItemName = () => {
            return '@PharmacyCrm';
        };
        // 不展示个人编码
        this.viewDistributeConfig.AppHeader.showUserInfoNationalCode = false;

        // 患者设置
        this.viewDistributeConfig.Settings.patient.patientTag.autoSignRule.isSupportBeLate = false;
        this.viewDistributeConfig.Settings.patient.patientTag.autoSignRule.isSupportHistoryDiagnosis = false;
        this.viewDistributeConfig.Settings.patient.patientTag.autoSignRule.isSupportNotAttendance = false;
        this.viewDistributeConfig.Settings.patient.patientTag.autoSignRule.isSupportPreviousHistory = false;

        // 实际打印 会影响到 abc-print 实际打印
        this.viewDistributeConfig.PrintConfig.cashier.showPatientOrderNo = false; // 展示收费小票诊号
        this.viewDistributeConfig.PrintConfig.cashier.showChineseDoseCount = false; // 展示收费小票中药剂数
        this.viewDistributeConfig.PrintConfig.cashier.onlyShowHasContentName = true; // 是否只展示有值的name
        this.viewDistributeConfig.PrintConfig.cashier.onlyShowHasContentMobile = true; // 是否只展示有值的手机号
        this.viewDistributeConfig.PrintConfig.cashier.isSupportDoctorInfo = false;
        this.viewDistributeConfig.PrintConfig.cashier.openSheetPersonText = '销售员';
        this.viewDistributeConfig.PrintConfig.cashier.chineseMedicineDetailIsSupportSpecialRequirement = false;
        this.viewDistributeConfig.PrintConfig.cashier.chineseMedicineDetailIsSupportUnitCount = true;
        this.viewDistributeConfig.PrintConfig.cashier.chineseMedicineDetailIsSupportTotalCount = false;
        this.viewDistributeConfig.PrintConfig.cashier.footerIsSupportWeClinicQrCode = false;
        this.viewDistributeConfig.PrintConfig.cashier.isSupportOutpatientBarcode = false;
        this.viewDistributeConfig.PrintConfig.cashier.medicineNameOptimization = true; // 药名排版优化

        // GSP
        this.viewDistributeConfig.Gsp.afterSale.recall.showGspRecallTableButton = true;
        this.viewDistributeConfig.Gsp.afterSale.recall.showGspRecallTableStatus = true;
        this.viewDistributeConfig.Gsp.afterSale.recover.showGspRecoverEditTableButton = true;
        this.viewDistributeConfig.Gsp.afterSale.recover.showGspRecoverTableStatus = true;
        this.viewDistributeConfig.Gsp.check.showGspReceiveOrderNo = true;
        this.viewDistributeConfig.Gsp.check.showContCardTab = true;
        this.viewDistributeConfig.Gsp.storage.clearFunnel.isMultiPharmacy = false;
        this.viewDistributeConfig.Gsp.storage.installFunnel.isMultiPharmacy = false;
        this.viewDistributeConfig.Gsp.storage.installFunnel.isUseClinicBatchComponent = false;
        this.viewDistributeConfig.Gsp.storage.clearFunnel.isUseClinicBatchComponent = false;

        // 诊所设置
        this.viewDistributeConfig.Settings.clinic.baseInfo.organNameTip = '主要用于外部展示，如收费单等打印单据上店名的展示，一般为营业执照上的名称';
    }

    static getInstance(hisType) {
        console.log('%c view 分发【药店管家】业务配置', 'background: #5199f8; padding: 4px; font-weight: bold; color: white'); // eslint-disable-next-line no-console
        return new PharmacyViewConfig(hisType);
    }
}
