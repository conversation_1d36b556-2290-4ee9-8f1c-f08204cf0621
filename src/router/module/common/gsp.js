import Index from 'src/views/gsp/index.vue';

import {
    MODULE_ID_MAP, RouterScope,
} from 'utils/constants.js';
import { PharmacyGspRouterNameKeys } from '@/views-pharmacy/gsp/core/routes';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('src/views-pharmacy/gsp/core/page.js');

const CheckAccept = () => import('src/views-pharmacy/gsp/frames/check-accept/index.vue');
const CheckAcceptQualified = () => import('src/views-pharmacy/gsp/frames/check-accept/qualified/index.vue');

const Storage = () => import('src/views-pharmacy/gsp/frames/storage/index.vue');
const StorageConserve = () => import('src/views-pharmacy/gsp/frames/storage/conserve/index.vue');
const StorageHumiture = () => import('src/views-pharmacy/gsp/frames/storage/humiture/index.vue');
const StorageEnvironment = () => import('src/views-pharmacy/gsp/frames/storage/environment/index.vue');
const StorageClearFunnel = () => import('src/views-pharmacy/gsp/frames/storage/clear-funnel/index.vue');
const StorageInstallFunnel = () => import('src/views-pharmacy/gsp/frames/storage/install-funnel/index.vue');

const AfterSales = () => import('src/views-pharmacy/gsp/frames/after-sales/index.vue');
const AfterSalesAdverseReactions = () => import('src/views-pharmacy/gsp/frames/after-sales/adverse-reactions/index.vue');
const AfterSalesRecall = () => import('src/views-pharmacy/gsp/frames/after-sales/recall/index.vue');
const AfterSalesRecover = () => import('src/views-pharmacy/gsp/frames/after-sales/recover/index.vue');

const Worker = () => import('src/views-pharmacy/gsp/frames/worker/index.vue');
const WorkerHealth = () => import('src/views-pharmacy/gsp/frames/worker/health/index.vue');
const WorkerTrain = () => import('src/views-pharmacy/gsp/frames/worker/train/index.vue');

export default {
    path: 'gsp',
    name: PharmacyGspRouterNameKeys.index,
    component: Index,
    meta: {
        name: 'GSP',
        scope: RouterScope.CHAIN_ADMIN | RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
        needAuth: true,
        menuRoot: true,
        moduleId: MODULE_ID_MAP.bizPharmacyGSP,
        pageAsyncClass: PageAsync,
        icon: 'n-safety-fill',
        selectedIcon: 'n-safety-fill',
        visible(store) {
            return store.state.viewDistribute.viewDistributeConfig?.Gsp?.showGsp;
        },
    },
    children: [
        {
            path: 'check-accept',
            name: PharmacyGspRouterNameKeys.checkAccept,
            component: CheckAccept,
            meta: {
                name: '验收',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.check,
            },
            children: [
                {
                    path: 'qualified',
                    component: CheckAcceptQualified,
                    name: PharmacyGspRouterNameKeys.checkAcceptQualified,
                    meta: {
                        name: '药品验收记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.checkSubModule.qualified,
                        needAuth: true,
                    },
                },
                {
                    path: 'medical-device-qualified',
                    component: CheckAcceptQualified,
                    name: PharmacyGspRouterNameKeys.checkAcceptMedicalDeviceQualified,
                    meta: {
                        name: '医疗器械验收记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.checkSubModule.medicalDeviceQualified,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'storage',
            name: PharmacyGspRouterNameKeys.storage,
            component: Storage,
            meta: {
                name: '存储',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.store,
                todoKey: 'storageTodo',
            },
            children: [
                {
                    path: 'conserve',
                    component: StorageConserve,
                    name: PharmacyGspRouterNameKeys.storageConserve,
                    meta: {
                        name: '药品养护',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.conserve,
                        todoKey: 'gspTodo',
                    },
                },
                // 器械养护
                {
                    path: 'medical-device-conserve',
                    component: StorageConserve,
                    name: PharmacyGspRouterNameKeys.storageMedicalDeviceConserve,
                    meta: {
                        name: '器械养护',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.conserveMedicalDevice,
                        todoKey: 'materialMaintenanceTodoCount',
                    },
                },
                {
                    path: 'humiture',
                    component: StorageHumiture,
                    name: PharmacyGspRouterNameKeys.storageHumiture,
                    meta: {
                        name: '温湿度记录',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.humiture,
                    },
                },
                {
                    path: 'environment',
                    component: StorageEnvironment,
                    name: PharmacyGspRouterNameKeys.storageEnvironment,
                    meta: {
                        name: '陈列环境检查',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.environment,
                    },
                },
                {
                    path: 'clear-funnel',
                    component: StorageClearFunnel,
                    name: PharmacyGspRouterNameKeys.storageClearFunnel,
                    meta: {
                        name: '清斗记录',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.clearFunnel,
                    },
                },
                {
                    path: 'install-funnel',
                    component: StorageInstallFunnel,
                    name: PharmacyGspRouterNameKeys.storageInstallFunnel,
                    meta: {
                        name: '装斗记录',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.storeSubModule.installFunnel,
                    },
                },
            ],
        },

        {
            path: 'after-sales',
            name: PharmacyGspRouterNameKeys.afterSales,
            component: AfterSales,
            meta: {
                name: '售后',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.afterSale,
            },
            children: [
                {
                    path: 'adverse-reactions',
                    component: AfterSalesAdverseReactions,
                    name: PharmacyGspRouterNameKeys.afterSalesAdverseReactions,
                    meta: {
                        name: '不良反应记录',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.afterSaleSubModule.adverseReactions,
                        needAuth: true,
                    },
                },
                {
                    path: 'recall',
                    component: AfterSalesRecall,
                    name: PharmacyGspRouterNameKeys.afterSalesRecall,
                    meta: {
                        name: '召回',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.afterSaleSubModule.recall,
                        needAuth: true,
                    },
                },
                {
                    path: 'recover',
                    component: AfterSalesRecover,
                    name: PharmacyGspRouterNameKeys.afterSalesRecover,
                    meta: {
                        name: '追回',
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.afterSaleSubModule.recover,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'workers',
            name: PharmacyGspRouterNameKeys.worker,
            component: Worker,
            meta: {
                name: '人员',
                needAuth: true,
                moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.person,
            },
            children: [
                {
                    path: 'health',
                    component: WorkerHealth,
                    name: PharmacyGspRouterNameKeys.workerHealth,
                    meta: {
                        name: '健康档案',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.personSubModule.health,
                    },
                },
                {
                    path: 'train',
                    component: WorkerTrain,
                    name: PharmacyGspRouterNameKeys.workerTrain,
                    meta: {
                        name: '培训记录',
                        needAuth: true,
                        moduleId: MODULE_ID_MAP.bizPharmacyGSPSubModule.personSubModule.train,
                    },
                },
            ],
        },
    ],
};
