import {
    AppEvents, getApp,
} from '@/core/index.js';
import {
    ModalFunc as AbcModal, ToastFunc as Toast,
} from '@abc/ui-pc';
import { formatDate } from '@abc/utils-date';
import axios from 'axios';
import AbcError from 'src/utils/error/abc-error';
import Vue from 'vue';
import Response from './Response';
import {
    ABCYUN_TOKEN, CSRF_TOKEN,
} from 'utils/local-storage-key';
import cookieService from '@/cookie-service';

import AbcImportErrorDialog from 'views/inventory/components/import-error-dialog';
import Logger from 'utils/logger';
import { generatePromise } from 'utils/promise-task';
import {
    getOfflineBundleByClinicInfo, getRegionByHostname, isAbcClientSchema,
} from 'utils/electron';
import { EditionErrorCode } from 'views/edition/configs/constants';
import Clone from 'utils/clone.js';

import SignService from './sign-service';
import CaptchaVerify from '@/service/captcha-verify';

const buildInfo = process.env.buildInfo || {};
console.log('buildInfo', buildInfo);
const buildTime = formatDate(buildInfo.BUILD_TIME, 'YYYY-MM-DD HH:mm:ss');


const { CancelToken } = axios;
Vue.cancel = {};
const errorCodes = [
    470,
    454,
    491,
    495,
    496,
    951,
    940,
    950,
    457,
    455,
    456,
    450,
    477,
    497,
    962,
    463,
    1012,
    1011,
    10409, // 患者信息修改，姓名+手机号冲突
    11000, // 该成员已接受邀请，请返回列表确认
    11001,
    11003, // 系统内同手机号用户已经绑定了微信
    11452, // 验证码已过期，请重新获取验证码
    11453, // 验证码不正确，请检查后重新输入
    11454, // 您的输入次数已达上限，请重新获取验证码
    11456, // 账号或者密码错误
    11470, // 密码错误超过5次，锁定账号
    11471, // NVC 需要二次校验
    11457, // 账号已经绑定了手机号
    11458, // 两个账号都存在活跃诊所，不能合并
    11461, // 用户已经绑定了其他的微信账号，绑定失败
    11501, // 手机号已经被使用
    11504, // 医生在本科室已有排班，将医生移出科室将清空排班，已有挂号预约将保留，是否确定移出？
    12004, // 删除商品物资失败，还有库存
    12005,
    12006,
    12033, // 添加检查检验项，项目代码重复
    12204, // 添加检查检验项，项目编码重复
    12007,
    12206,
    12207,
    12208,
    12010,
    12038, // 删除厂家时存在加工项目出错
    12039, // 批量导入加工项目，表格行错误
    12040, // 批量导入加工项目为空文件
    12046, // 批量导入加工项目文件表格字段格式不匹配
    12050, // 当前品种已发生拆零进销存，修改最小包装<br>数量将导致历史记录及当前库存错乱，如需<br>修改请重新建档
    12100, // 入库单导入，表格行错误
    12218, // 商城采购失败
    12221, // 当前修改的采购计划已被他人删除
    12222, // 该采购单已被删除
    12238, // 存在未盘点药品
    12235, // 盘点单内容有更新
    12236, // 盘点维度不统一
    12243, // 盘点中药品规格发生变化
    12500,
    12502,
    12503,
    12504,
    12505,
    12162, // 该盘点单已创建完成，请在列表中查看
    12703,
    12802, // 自定义二级分类
    12804, // 存在套餐中的检查等不能停用 删除
    12805, // 修改药品规格
    12808, // 药品已被停用
    12811, // 费用类型-批量校验失败
    12812, // 云草稿-当前草稿已保存
    12813, // 云草稿-当前草稿已删除
    12814, // 云草稿-当前草稿已提交单据
    12815, // 解析文件数据失败
    13005, // 保存随访模板 "该模板名字已经存在"
    13006,
    13502, // 患者证件在其他连锁存在
    13503, // 其他连锁存在同名患者
    13100, // 该随访单内容已被修改，点击确定刷新
    13101, // 该随访单已经被执行，点击确定刷新
    13115,
    13992, // 创建患者姓名手机号重复
    13993, // 创建患者身份证号重复
    13994, // 该患者已经绑定社保卡
    16007,
    16008,
    16074, // 叫号队列顺序发生变更
    17006, // 收费单不存在（0元取消收费）
    17053, // 当前收费单状态不支持取消（0元取消收费）
    17054, // 当前收费单已收费金额不为0，不能取消（0元取消收费）
    17013,
    17005,
    17022,
    17025, // 会员卡退费金额错误
    17023,
    17026,
    17030, // 同时收费，后台1s内收到多个请求提示
    17050,
    17051,
    17052,
    21101,
    21102,
    21103,
    21105,
    12015,
    12024, // 采购计划名称重复
    26035, // ABC退费卡余额不足
    17009, // xxx退款单位数量大于可退款数
    17010,
    11032,
    15168, // 排班失败
    15123, // 预约时间已被预定，请重新选择
    15125, // 预约时间错误
    15122, // 该时间已被预定，请重新选择
    17056, // "当前收费单不能挂单"
    17057, // "当前收费单不能删除"
    17058, // "请勿重复添加药品或项目"
    28001, // 活动时间、门店、对象相同时，同一分类/单品的满减返活动,只能存在一个
    31007, // 【申请试用诊所】您已提交过申请，请勿重复提交
    17090, // 快递费发生改变
    36005, // 【空中药房】供应商不支持该制法
    17208, // 收费时，微诊所已经支付了
    17055, // 操作时，该单被锁定
    19116, // 指标名重复
    19125, //指标代码重复
    12037, // 样本容器编码已存在
    16052, // 该门诊单已收费，部分处方更新失败
    25030, // 重复支付请求
    43001, // 绑定诺诺发票 请使用同一账户操作
    43002, // 绑定诺诺发票 accessToken 解析失败
    43007, // 绑定诺诺发票 该税号已被其它诊所绑定
    17112, // 当前收费单不能开发票
    43005, // 诺诺发票开具失败
    12809, // 检查检验类型切换异常
    16003, // 门诊单不存在
    17125, // 支付金额有误
    30004, // 未找到对应的消息通知开关
    17083, // 空中药房订单状态发生变化
    17210, // 空中药房没有添加药品
    17128, // 当前收费单内有皮试项目尚未完成，需要皮试通过后才能执行收费
    17129, // 当前收费单内有皮试项目结果呈阳性，请医生调整医嘱后才能执行收费
    17338, // 收费单内有部分药品已发药，请退药后再整单退费，17337、17339 错误码合并到17338一起了
    23055, // 诊所配置不存在
    17214, // 签约收费单不能重新收费
    17011, // 无有效待收金额
    28003, // 赠送商品不能重复
    14001, // 卡已被领取，不能进行修改
    14020, // 患者无法召回
    14999, // 有未完结事项待处理
    16067, // 门诊单价格发生变化，请在费用预览处确认价格！
    17257, // 算费时存在数量全为0的处方
    10001, // 企业微信code 重复登录
    49996, // 执行医嘱任务，其中有需要自动发药的医嘱
    49998, // 开出院医嘱，需要先下出院诊断
    49997, // 开药品医嘱，需要先下入院诊断
    84108, // 收费成功，发药失败
    84109, // 没有发药单
    84115, // 发药批次不足自动盘点
    84301, // 发药失败
    84302, // 退药失败
    28012, // 药店营销活动创建失败
    23014, // 小程序未授权
    28028, // 药店营销活动 - 多倍积分患者范围异常
    11481, // 安全登录限制
    55007, // 微商城删除分类/门店存在某商品提示
    430004, // 数电发票重置密码
    15500, // 门诊单初复诊状态变更
];

const pendingRequest = new Map();
const repeatRequest = new Map();
const repeatRequestReportMap = new Map();
const repeatRequestTime = 100;
let isShowUpdateInfoConfirm = false;

const generateKey = (config) => {
    let {
        url,
        params,
    } = config;

    if (url.indexOf('?') !== -1) {
        url = url.split('?')[0];
    }

    try {
        params = JSON.stringify(params);
    } catch (e) {
        params = '';
    }

    return params ? `${url}-${config.method}-${params}` : `${url}-${config.method}`;
};

const getNewUrl = (url, targetStr) => {
    const index = url.indexOf(targetStr);

    if (index !== -1) {
        return url.substring(index);
    }

    return url;
};

const reportRepeatRequest = (config) => {
    const {
        method, url, params, data, reqKey,
    } = config;

    function report(reqKey) {
        // 已经上报过的就不再上报
        if (repeatRequestReportMap.has(reqKey)) {
            return;
        }
        Logger.reportAnalytics('repeat_request', {
            method,
            url: getNewUrl(url, '/api'),
            params,
            data,
        });
    }

    if (repeatRequest.has(reqKey)) {
        clearTimeout(repeatRequest.get(reqKey));
        report(reqKey);
        repeatRequestReportMap.set(reqKey, true);
    }

    const timerId = setTimeout(() => {
        clearTimeout(repeatRequest.get(reqKey));
        repeatRequest.delete(reqKey);
    }, repeatRequestTime);

    repeatRequest.set(reqKey, timerId);
};

const addSignToRequest = async (config, timestamp) => {
    config.headers['Abc-Client-Info'] =
        `${buildInfo.BUILD_TAG};${buildTime}`;
    config.headers['X-AUA'] = `ABCMallPC/${buildInfo.BUILD_TAG}`;
    config.headers['X-ABC-Ts'] = timestamp;
    const abcSecret = await cookieService.get(ABCYUN_TOKEN);
    const signature = await SignService.generateSignature({
        protocol: location.protocol,
        config,
        timestamp,
        abcSecret,
    });
    config.headers['X-ABC-Sign'] = signature;
};

const requestCache = new Map();

const dedupeAdapter = (config) => {
    // 仅处理 GET 请求
    if (config.method?.toLowerCase() !== 'get') {
        return axios.defaults.adapter(config); // 非 GET 请求直接返回
    }

    const reqKey = generateKey(config);

    // 存在相同 GET 请求时，将新请求加入订阅队列
    if (requestCache.has(reqKey)) {
        return new Promise((resolve, reject) => {
            const originReq = requestCache.get(reqKey);
            originReq.interceptors.push({
                resolve,
                reject,
            });
        });
    }

    // 首次发起 GET 请求
    const newReq = {
        request: axios.defaults.adapter(config),
        interceptors: [],
    };
    requestCache.set(reqKey, newReq);

    // 处理响应并通知订阅者
    newReq.request
        .then((rsp) => {
            newReq.interceptors.forEach((item) => item.resolve(rsp));
            return rsp;
        })
        .catch((error) => {
            newReq.interceptors.forEach((item) => item.reject(error)); // 修正：应使用 reject 传递错误
        })
        .finally(() => {
            requestCache.delete(reqKey); // 清理缓存
        });

    return newReq.request;
};


// 创建axios实例
const service = axios.create({
    baseURL: `//${location.host}`, // api的base_url
    timeout: 100000, // 请求超时时间
    adapter: dedupeAdapter,
});


// request拦截器
service.interceptors.request.use(
    async (config) => {
        // config使用的请求中原始对象，业务中params变更会导致请求中参数异常
        if (config.params) {
            config.params = Clone(config.params);
        }
        const reqKey = config.reqKey = generateKey(config);
        /**
         * @desc 默认开启切换路由cancel请求，如果需要切换路由也不cancel请求，需要给该请求指定
         * disabledCancel = true
         * <AUTHOR> Yang
         * @date 2020-08-04 20:26:52
         */
        if (!config.disabledCancel) {
            const guid = Date.now();
            config.guid = guid;
            config.cancelToken = new CancelToken((c) => {
                Vue.cancel[guid] = c;
                if (config.disallowDuplicate) {
                    const cancel = pendingRequest.get(reqKey);
                    if (cancel) {
                        pendingRequest.delete(reqKey);
                        cancel({
                            key: reqKey,
                            reason: 'disallowDuplicate',
                        });
                    }
                    pendingRequest.set(reqKey, c);
                }
            });
        }

        config.url = encodeURI(config.url);
        // 请求处理（重复上报）
        reportRepeatRequest(config);

        const timestamp = `${new Date().getTime()}`;
        if (!config.disabledCache) {
            if (config.url.indexOf('?') !== -1) {
                config.url = `${config.url}&${timestamp}`;
            } else {
                config.url = `${config.url}?${timestamp}`;
            }
        }

        const csrfToken = await cookieService.get(CSRF_TOKEN);
        if (csrfToken) {
            config.headers[CSRF_TOKEN] = csrfToken; // 让每个请求携带token--[CSRF_TOKEN_KEY]为自定义key
        }

        await addSignToRequest(config, timestamp);
        return config;
    },
    (error) => {
        // Do something with request error
        Promise.reject(error);
    },
);

// respone拦截器
service.interceptors.response.use(
    (response) => {
        // if(response.data.status.code >= 500) {
        //     Toast( {
        //         message: response.data.status.message,
        //         type: 'error',
        //         duration: 3 * 1000
        //     } );
        // }
        if (response.config) {
            if (response.config.guid) {
                delete Vue.cancel[response.config.guid];
            }
            if (response?.data?.data?.error?.code === 6001) {
                const dialogDom = document.querySelector('.stat-limit-warn-dialog');
                if (!dialogDom) {
                    AbcModal.alert({
                        customClass: 'stat-limit-warn-dialog',
                        type: 'warn',
                        title: '提示',
                        content: '由于同时搜索该表格的用户数量过多，请缩小搜索时间范围或稍后再试',
                    });
                }
            }
            const key = response.config.reqKey;
            // 找到key，清除
            if (pendingRequest.has(key)) {
                pendingRequest.delete(key);
            }
        }
        return response;
    },
    (error) => {
        if (error.message === 'cancel') {
            console.warn('中断切换路由之前的请求');
            // eslint-disable-next-line prefer-promise-reject-errors
            return Promise.reject({ message: '中断切换路由之前的请求' });
        }

        if (error.config?.guid) {
            delete Vue.cancel[error.config.guid];
        }

        if (
            error.code === 'ECONNABORTED' &&
            error.message.indexOf('timeout') !== -1
        ) {
            console.warn('请求超时');
            return Promise.reject(new AbcError(AbcError.Constants.TIMEOUT));
        }



        if (
            error.status === 504 &&
            error.error.indexOf('Gateway Timeout') !== -1
        ) {
            console.warn('请求超时');
            return Promise.reject(new AbcError(AbcError.Constants.TIMEOUT));
        }

        if (error instanceof axios.Cancel) {
            if (error.message.reason === 'disallowDuplicate') {
                // pendingRequest.delete( error.message.key );
                console.warn('取消之前的重复请求');
                // eslint-disable-next-line prefer-promise-reject-errors
                return Promise.reject({ message: '取消之前的重复请求' });
            }
        }

        if (error.message === 'Network Error') {
            Toast({
                message: '你的网络有问题，请检查网络设置！',
                type: 'error',
                duration: 3 * 1000,
            });

            const {
                method, url, params, data,
            } = error.config || {};

            Logger.error({
                scene: 'FETCH_NETWORK_ERROR',
                data: {
                    method,
                    url,
                    params,
                    data,
                },
            });

            // eslint-disable-next-line prefer-promise-reject-errors
            return Promise.reject({ message: '没有网络连接' });
        }

        // Add error checking before accessing error.response
        if (!error.response) {
            console.error('Response not available:', error);
            return Promise.reject(new Error('请求失败，请稍后重试'));
        }

        const { config } = error.response;
        const {
            data, status,
        } = error.response;

        let errorInfo = data?.error ?? {};

        if (config.responseType === 'arraybuffer') {
            try {
                const enc = new TextDecoder('utf-8');
                const parseData = JSON.parse(enc.decode(new Uint8Array(data)));
                //在这里如果能获取到parseData,就表示遇到错误了。解析处理的数据就是后端返回的错误信息
                if (parseData) {
                    errorInfo = parseData.error ?? {};
                }
            } catch (e) {
                //new TextDecoder('utf-8') 这个方法无法解析文件的arraybuffer
                //这里表示正确返回arraybuffer文件,然后对文件处理下载即可
                errorInfo = data.error ?? {};
            }
        }
        const {
            code, message, detail,
        } = errorInfo;

        if (code === 4070) {
            CaptchaVerify.getInstance().showCaptcha();
            return Promise.reject(new Error('请求失败，请稍后重试'));
        }

        // 接口不兼容
        // 状态码400
        // 错误码10000
        if (status === 400 && code === 10000) {
            const {
                promise, reject,
            } = generatePromise();
            if (isShowUpdateInfoConfirm) {
                reject(errorInfo);
                return promise;
            }
            isShowUpdateInfoConfirm = true;
            AbcModal.confirm({
                type: 'info',
                title: '更新提示',
                content: message,
                confirmText: '更新',
                onConfirm: async () => {
                    // 1. 客户端需要更新并刷新
                    // 2. 浏览器直接刷新
                    if (isAbcClientSchema()) {
                        const url = new URL(location.href);
                        await getOfflineBundleByClinicInfo({
                            region: getRegionByHostname(url.hostname),
                            env: buildInfo.BUILD_ENV,
                        });
                    }
                    window.location.reload();
                },
                onCancel: () => {
                    reject(errorInfo);
                    isShowUpdateInfoConfirm = false;
                },
            });
            return promise;
        }

        // token验证失败退出登陆
        // 4003 分区切换token不匹配，退出登录
        if (code === 401 || code === 402 || code === 4003) {
            // 更改状态；让monitor阻止 上报错误
            getApp().changeErrorMonitorEnable(false);

            getApp().emit(AppEvents.APP_EVENT_UN_AUTH, message);
            return;
        }

        // 签名验证失败
        if (status === 403 && code === 4040) {
            Logger.report({
                scene: 'api_sign_error',
                data: {
                    source: {
                        client: `${buildInfo.BUILD_TAG};${buildTime}`,
                        error,
                    },
                },
            });
        }

        // 版本到期、功能未购买
        if (code === EditionErrorCode.EXPIRED || code === EditionErrorCode.NOT_PURCHASED || code === EditionErrorCode.INEFFECTIVE) {
            getApp().emit(AppEvents.APP_EVENT_EDITION_EXPIRE, {
                code, message,
            });
            return Promise.reject(errorInfo);
        }

        // 版本未生效，功能未购买，不弹出提示，用户无感知
        if (code === EditionErrorCode.FEATURE_INEFFECTIVE) {
            return Promise.reject(errorInfo);
        }

        if (status === 503 &&
            (code === 900001 || code === 900002)
        ) {
            const detailObj = JSON.parse(detail);
            const { redirectUrl } = detailObj;
            window.location = redirectUrl;
            return;
        }

        if (errorInfo) {
            // 50X 的错误是服务器端的错误
            if (/^5\d{2}$/.test(code)) {
                Toast({
                    message: '请求响应超时，请稍后再试！',
                    type: 'error',
                    duration: 3 * 1000,
                });
            } else if (/^4\d{2}$/.test(code) && code !== 452) {
                // 452 该成员已在本门店中存在
                return Promise.reject(errorInfo);
            } else {
                // 需要由出错页面自行展示错误
                if (config.customErrorTips || errorCodes.indexOf(code) !== -1) {
                    if (code === 10409) {
                        // 患者信息修改，存在姓名+手机号相同患者，特殊处理（患者合并）
                        // eslint-disable-next-line prefer-promise-reject-errors
                        return Promise.reject({
                            ...errorInfo,
                            data: data.data,
                        });
                    }
                    // 库存相关三个状态的错误弹窗展示
                    if (!config.customErrorTips && (code === 12100 || code === 12206 || code === 12207)) {
                        const { detail } = errorInfo;
                        new AbcImportErrorDialog({
                            errorCode: code,
                            errorTitle: detail.errorTitle,
                            errorList: code === 12207 ? detail : detail.errorList,
                        }).generateDialogAsync();
                        errorInfo.alerted = true;
                    }
                    return Promise.reject(errorInfo);
                }
                // 统一错误
                if (message) {
                    AbcModal.alert({
                        type: 'warn',
                        title: '提示',
                        content: message,
                    });
                    errorInfo.alerted = true;
                }
                return Promise.reject(errorInfo);

            }
        } else {
            // 老接口协议兼容
            const { status } = data;
            // 50X 的错误是服务器端的错误
            if (/^5\d{2}$/.test(status.code)) {
                Toast({
                    message: '请求响应超时，请稍后再试！',
                    type: 'error',
                    duration: 3 * 1000,
                });
            } else {
                // 需要由出错页面自行展示错误
                if (errorCodes.indexOf(status.code) !== -1) {
                    return Promise.reject(data);
                }
                // 统一错误
                if (status.message) {
                    AbcModal.alert({
                        type: 'warn',
                        title: '提示',
                        content: status.message,
                    });
                }
            }
        }
        return Promise.reject(data);
    },
);

/**
 * fetch方法包装一下，返回Response结构数据
 * <AUTHOR>
 * @date 2024-01-03
 * @param {any} ...args
 * @returns {Promise<Response>}
 */
const fetchPack = async (...args) => {
    let response = null;
    try {
        const res = await service(...args);
        response = Response.success(res.data.data);
    } catch (error) {
        console.log('fetchPack error', error);
        response = Response.error(error?.message || '接口异常', error);
        response.isNeedToast = errorCodes.includes(error?.code); // 是否需要自行提示
    }
    return response;
};

/**
 * 取消 pending 的请求
 */
const cancelPendingRequest = function() {
    for (const k in Vue.cancel) {
        if (Object.prototype.hasOwnProperty.call(Vue.cancel, k)) {
            Vue.cancel[k]('cancel');
            delete Vue.cancel[k];
        }
    }
};

export {
    errorCodes,
    fetchPack,
    cancelPendingRequest,
    addSignToRequest,
};

export default service;
