import { jsonrepair } from 'jsonrepair';

export function cleanJsonString(jsonString) {
    if (!jsonString || typeof jsonString !== 'string') {
        return '';
    }

    // 去掉首尾空白字符
    const cleaned = jsonString.trim();

    // 再次去掉首尾空白字符
    return cleaned.replace(/^```json\s*|\s*```$/g, '');
}

export function isJsonString(str) {
    return str.trim().startsWith('```json');
}

function jsonParse(jsonString) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        return false;
    }
}

export function safeParseJson(jsonString, defaultValue = null) {
    // 基本验证
    if (!jsonString || typeof jsonString !== 'string') {
        return defaultValue;
    }

    const baseResult = jsonParse(jsonString);
    if (baseResult) {
        return baseResult;
    }

    try {
        return jsonrepair(jsonString);
    } catch (e) {
        return defaultValue;
    }
}

export function isObject(data) {
    return Object.prototype.toString.call(data) === '[object Object]';
}
