import GoodsAPI from 'api/goods';
import SettingApi from 'api/settings';
import {
    isNull,
    getPharmacyModulePermission, isNotNull,
} from '@/utils';
import AbcAccess from '@/access/utils';
import { PharmacyTypeEnum } from '@abc/constants';
import * as repository from 'MfFeEngine/repository';
import {
    CHECK_IN_SUPPLIER_ID,
    SUMMARY_PHARMACY, SUMMARY_PHARMACY_ID,
} from 'views/inventory/constant';
import {
    MODULE_ID_MAP,
    LocalSearchFlag,
    RemoteSearchFlag,
    _CURRENT_STOCK_ROOM_ID_,
    TraceCodeCollectStrictCountFlag,
    BIT_FLAG_LOCK_BATCH_NOT_MODIFY,
} from 'utils/constants';
import {
    BIT_FLAG_SELL_PRICE_NOT_ZERO,
    BIT_FLAG_STOCK_IN_BATCH_NO_REQUIRED,
    BIT_FLAG_STOCK_IN_COST_PRICE_NOT_ZERO,
} from '@/views-pharmacy/inventory/constant';
import { GoodsTypeIdEnum } from '@abc/constants';
import LocalStore from 'utils/localStorage-handler';
import SupplierApi from 'api/goods/supplier';
import ClinicAPI from 'api/clinic';
import GoodsAPIV3 from 'api/goods/index-v3';
import PharmacyApprovalAPI from 'api/pharmacy/approval-setting.js';
import GoodsV3API from 'api/goods/index-v3';
import TraceCode from '@/service/trace-code/service';
const inventory = {
    state: {
        todo: {
            // 药品数量
            westCount: 0,//西药数量
            chinesePiecesCount: 0,//饮片
            chineseGranuleCount: 0,//颗粒
            medicalMaterialCount: 0,//耗材
            materialCount: 0,//物资
            additionalCount: 0,//商品
            eyeCount: 0,//眼镜

            purchaseTodoCount: 0,
            stockInTodoCount: 0,
            stockOutTodoCount: 0,
            stockTransInTodoCount: 0,
            stockTransOutTodoCount: 0,
            stockTransReviewTodoCount: 0,
            settlementTodoCount: 0,
            stockCheckTodoCount: 0,
            stockInTodoPharmacyNoList: [],
            stockReceptTodoPharmacyNoList: [],
            stockCheckTodoPharmacyNoList: [],
            stockDeptOutTodoPharmacyNoList: [],
            stockOtherOutTodoPharmacyNoList: [],
            stockLossOutTodoPharmacyNoList: [],
            stockTransTodoPharmacyNoList: [],
            profitRatWarnPharmacyNoList: [],
            expiredWarnPharmacyNoList: [],
            stockWarnShortagePharmacyNoList: [],
            stockTransTodoCount: 0,
            stockReceptTodoCount: 0,
            stockOtherOutTodoCount: 0,
            stockLossOutTodoCount: 0,
            stockDeliveryTodoCount: 0, // TODD DELIVERY
            stockDeptOutTodoCount: 0,
            modifyPriceTodoCount: 0,
            inspectTodoCount: 0,
            receiveTodoCount: 0,
            supplierValidCount: 0,
        },
        curPharmacy: null,
        curPharmacyId: LocalStore.get(_CURRENT_STOCK_ROOM_ID_, true) || '',
        goodsConfig: {},
        goodsConfigIsInit: false,
        goodsPrimaryClassification: [],
        goodsAllTypes: [], // 系统所有的药品物资商品分类以及二级分类
        isFirstLookPriceAdjustment: true, //是否第一次查看批量调价
        priceAdjustmentTabType: 1, // 默认售价类型 1 售价调整 2 进价调整 0 手动调整 3 调整至医保限价
        priceRatio: null, //默认百分比
        priceMode: 1, //调价模式（priceAdjustmentTabType为 1 时才有用）1 上调 0 下调
        roundingMode: 1,// 1=向上取整、2=四舍五入、3=向下取整
        scaleType: 1,//1=2位小数、2=3位小数、3=4位小数
        // 供应商列表
        supplierList: [],
        // 库存成员列表
        employeeList: [],
        // 利润分类
        profitClassificationList: [],
        // 药店
        allowPharmacyAdjust: false,
        // 调价列表
        adjustmentPriceListTotal: 0,
        // 采集追溯码是否强校验数量
        isStrictCountWithTraceCodeCollect: false,
    },
    /**
     * @desc 此module没有 namespace所以这里的 getters在全局可以使用
     * <AUTHOR>
     * @date 2022/5/27 15:08
     */
    getters: {
        currentPharmacy(state) {
            return state.curPharmacy;
        },
        currentPharmacyId(state) {
            return state.curPharmacyId;
        },
        // 入库单批号必填
        isNeedCheckStockInfoNotEmpty(state) {
            return !!((state.goodsConfig?.chainReview?.chainExternalFlag ?? 0) & BIT_FLAG_STOCK_IN_BATCH_NO_REQUIRED);
        },
        // 入库单进价不能为0
        isNeedCheckStockInCostPriceNotZero(state) {
            return !!((state.goodsConfig?.chainReview?.chainExternalFlag ?? 0) & BIT_FLAG_STOCK_IN_COST_PRICE_NOT_ZERO);
        },
        // 售价不能为0
        isNeedCheckSellPriceNotZero(state) {
            return !!((state.goodsConfig?.chainReview?.chainExternalFlag ?? 0) & BIT_FLAG_SELL_PRICE_NOT_ZERO);
        },
        // 强锁批次开关不能修改
        isLockBatchNotModify(state) {
            return !!((state.goodsConfig?.clinicExternalFlag ?? 0) & BIT_FLAG_LOCK_BATCH_NOT_MODIFY);
        },
        // 1 表示 【库存不足 不允许在在门诊、收费处、执行站开出】
        disableNoStockGoods(state) {
            return state.goodsConfig?.stockGoodsConfig?.disableNoStockGoods === 1;
        },
        // 0 表示 【库存不足 允许在在门诊、收费处、执行站开出，系统会警示库存不足】
        enableNoStockGoodsWarning(state) {
            return state.goodsConfig?.stockGoodsConfig?.disableNoStockGoods === 0;
        },
        // 2 表示 【库存不足 允许在在门诊、收费处、执行站开出，系统不做任何警示】
        enableNoStockGoodsNoWarning(state) {
            return state.goodsConfig?.stockGoodsConfig?.disableNoStockGoods === 2;
        },
        // 控制是否需要检查库存
        needCheckStock(state, getters) {
            return !getters.enableNoStockGoodsNoWarning;
        },
        // 开通了多库房，并且有多个库房
        multiPharmacyCanUse(state) {
            // openPharmacyFlag 0 未开通 10 单库房 20 多库房
            return AbcAccess.getAccessByKey(AbcAccess.accessMap.MULTI_PHARMACY) && (state.goodsConfig.openPharmacyFlag === 20);
        },
        // 开通了多库房，并且有一个默认库房，这时候去库房管理新建一个出来就会变为真正的多库房（状态20）
        multiPharmacyCanUseWithDefault(state) {
            // openPharmacyFlag 0 未开通 10 单库房 20 多库房
            return AbcAccess.getAccessByKey(AbcAccess.accessMap.MULTI_PHARMACY) && state.goodsConfig.openPharmacyFlag === 10;
        },
        // 当前库房模块功能权限
        currentPharmacyModulePermission(state) {
            // 医院管家加载时机问题，这里不能直接使用state.curPharmacy
            const curPharmacy = state.goodsConfig.pharmacyList.find((item) => item.id === state.curPharmacyId);
            return getPharmacyModulePermission(state.curPharmacyId === SUMMARY_PHARMACY_ID ? SUMMARY_PHARMACY : curPharmacy || {});
        },
        pharmacyList(state) {
            return state.goodsConfig.pharmacyList || [];
        },
        pharmacyUserList(state, getters) {
            const {
                id,
                isAdmin,
                moduleIds,
            } = getters.userInfo || {};
            const list = state.goodsConfig.pharmacyList || [];
            return list.filter((item) => {
                // 管理员默认有全部权限，不需要进行判断
                if (isAdmin) return true;

                // 拥有库存权限的人
                if (item.defaultMember) {
                    if (!moduleIds) return false;
                    if (moduleIds === '0') return true;

                    const moduleArr = moduleIds.split(',');
                    const moduleChildrenArr = [
                        MODULE_ID_MAP.inventory,
                        MODULE_ID_MAP.pharmacy,
                        MODULE_ID_MAP.goods,
                        MODULE_ID_MAP.goodsIn,
                        MODULE_ID_MAP.goodsOut,
                        MODULE_ID_MAP.goodsCheck,
                        MODULE_ID_MAP.goodsTrans,
                        MODULE_ID_MAP.inventoryStock,
                        MODULE_ID_MAP.goodsProductionOut,
                        MODULE_ID_MAP.inventoryPurchaseIn,
                        MODULE_ID_MAP.inventoryApplyIn,
                        MODULE_ID_MAP.inventoryLossOut,
                        MODULE_ID_MAP.hospitalInventoryDepartmentOut,
                        MODULE_ID_MAP.hospitalInventoryOtherOut,
                        MODULE_ID_MAP.supplier,
                        MODULE_ID_MAP.settlementApplication,
                        MODULE_ID_MAP.settlementReview,
                    ];

                    return moduleChildrenArr?.some((id) => {
                        return moduleArr?.includes(id);
                    });

                }
                // 库房指定成员可见
                return item?.employees?.some((user) => user.id === id);
            });
        },
        // 药房下单规则列表
        pharmacyRuleList(state) {
            return state.goodsConfig.pharmacyRuleList || [];
        },
        isOpenVirtualPharmacy(state, getters) {
            return getters.pharmacyList.some((p) => p.type === PharmacyTypeEnum.VIRTUAL_PHARMACY);
        },

        // 本地多药房列表
        localPharmacyList(state) {
            const list = state.goodsConfig.pharmacyList || [];
            if (!list || !list.length) return [];
            return list.filter((it) => it.type === PharmacyTypeEnum.LOCAL_PHARMACY) || [];
        },
        // 当前用户的本地药房
        localPharmacyUserList(state, getters) {
            return getters.pharmacyUserList.filter((it) => it.type === PharmacyTypeEnum.LOCAL_PHARMACY) || [];
        },
        // 可以发药的药房
        enableLocalPharmacyList(state, getters) {
            return getters.localPharmacyList.filter((it) => {
                // status 1 启用 0 停用
                // enableDispense 能够发药
                return it.status === 1 && it.enableDispense;
            });
        },
        // 分类 id 和 type subType 映射关系，获取时 key需要为 string
        primaryClassificationMap(state) {
            const typeMap = new Map();
            state.goodsPrimaryClassification.forEach((item) => {
                if (!typeMap.get(`${item.id}`)) {
                    typeMap.set(`${item.id}`, item);
                }
            });
            return typeMap;
        },
        lockInventoryConfigs(state) {
            // config {
            //    lockFlag  诊所：0 不锁定，10 开单后锁定，20 收费后锁定
            //              医院：0 不锁定，110 医嘱下达，120 收费后锁定
            //    sceneType 0 诊所 10 医院
            // };
            return state.goodsConfig.lockConfigs || [];// 可能有多个配置。诊所、医院
        },
        goodsAllTypes(state) {
            return state.goodsAllTypes || [];
        },
        supplierList(state) {
            return state.supplierList || [];
        },

        stockEmployeeList(state) {
            return state.employeeList || [];
        },

        profitClassificationList(state) {
            return state.profitClassificationList || [];
        },

        cooperationPharmacyList(state) {
            const list = state.goodsConfig.pharmacyList || [];
            if (!list || !list.length) return [];
            return list.filter((it) => it.type === PharmacyTypeEnum.COOPERATION_PHARMACY && it.status === 1) || [];
        },
        isStrictCountWithTraceCodeCollect(state) {
            return state.isStrictCountWithTraceCodeCollect;
        },
    },
    mutations: {
        SET_INVENTORY_TODO(state, todo) {
            Object.assign(state.todo, todo);
        },
        SET_GOODS__PRIMARY_CLASSIFICATION(state, todo) {
            Object.assign(state.goodsPrimaryClassification, todo);
        },
        SET_GOODS_CONFIG: (state, goodsConfig) => {
            state.goodsConfig = goodsConfig;
            state.goodsConfigIsInit = true;
        },
        SET_STRICT_COUNT_SWITCH: (state, data) => {
            state.isStrictCountWithTraceCodeCollect = data;
        },
        SET_FIRST_LOOK_PRICE_ADJUSTMENT: (state, data) => {
            state.isFirstLookPriceAdjustment = data;
        },
        SET_PRICE_ADJUSTMENT_TAB_TYPE: (state, data) => {
            state.priceAdjustmentTabType = data;
        },
        SET_PRICE_RATIO: (state, data) => {
            state.priceRatio = data;
        },
        SET_PRICE_MODE: (state, data) => {
            state.priceMode = data;
        },
        SET_ROUNDING_MODE: (state, data) => {
            state.roundingMode = data;
        },
        SET_SCALE_TYPE: (state, data) => {
            state.scaleType = data;
        },
        SET_CUR_PHARMACY: (state, data) => {
            state.curPharmacy = data;
        },
        SET_CUR_PHARMACY_ID: (state, data) => {
            state.curPharmacyId = data;
            LocalStore.set(_CURRENT_STOCK_ROOM_ID_, data);
        },
        SET_GOODS_ALL_TYPES: (state, data) => {
            state.goodsAllTypes = data;
        },
        SET_SUPPLIER_LIST: (state, data) => {
            state.supplierList = data;
        },
        SET_STOCK_EMPLOYEE_LIST: (state, data) => {
            state.employeeList = data;
        },
        SET_PROFIT_CLASSIFICATION_LIST: (state, data) => {
            state.profitClassificationList = data;
        },
        SET_ALLOW_ADJUST: (state, data) => {
            state.allowPharmacyAdjust = data;
        },
        SET_ADJUSTMENT_LIST_TOTAL: (state, data) => {
            state.adjustmentPriceListTotal = data;
        },
    },
    actions: {
        async setFirstPriceAdjustment({ commit }, data) {
            commit('SET_FIRST_LOOK_PRICE_ADJUSTMENT', data);
        },

        async setPriceAdjustmentTabType({ commit }, data) {
            commit('SET_PRICE_ADJUSTMENT_TAB_TYPE', data);
        },
        async setPriceRatio({ commit }, data) {
            commit('SET_PRICE_RATIO', data);
        },
        async setPriceMode({ commit }, data) {
            commit('SET_PRICE_MODE', data);
        },
        async setRoundingMode({ commit }, data) {
            commit('SET_ROUNDING_MODE', data);
        },
        async setScaleType({ commit }, data) {
            commit('SET_SCALE_TYPE', data);
        },
        async setCurPharmacy({ commit }, data) {
            commit('SET_CUR_PHARMACY', data);
        },
        async fetchAdjustmentList({ commit }) {
            try {
                const fetchParams = {
                    priceAdjuster: '', // 调价人
                    departmentId: '',
                    status: '',
                    offset: 0,
                    limit: 3,
                    affectedClinicId: '',
                    begDate: '',
                    endDate: '',
                    goodsId: '',
                    sourceTypeList: [0, 1],
                };
                const { data } = await GoodsV3API.fecthPriceAdjustment(fetchParams);
                console.log('SET_ADJUSTMENT_LIST_TOTAL');
                commit('SET_ADJUSTMENT_LIST_TOTAL', data?.total || 0);
            } catch (e) {
                console.log(e);
            }
        },
        async fetchSupplierList({ commit }, params) {
            const { data } = await SupplierApi.searchSupplier(params || this.state.defaultSupplierParams || {});
            const supplierList = data?.rows?.filter((item) => {
                return !!item && item.id !== CHECK_IN_SUPPLIER_ID && item.status === 1;
            }) || [];

            commit('SET_SUPPLIER_LIST', supplierList);
        },
        async fetchStockEmployeeListIfNeed({
            state, dispatch,
        }) {
            if (
                !state.employeeList?.length
            ) {
                dispatch('fetchStockEmployeeList');
            }
        },
        async fetchStockEmployeeList({ commit }) {
            const { data } = await ClinicAPI.fetchEmployeeByModuleId({
                // moduleId: [MODULE_ID_MAP.bizPharmacyInventory],
            });
            if (data) {
                commit('SET_STOCK_EMPLOYEE_LIST', data);
            }
        },
        async fetchAllApprovalList({ commit }) {
            try {
                const res = await PharmacyApprovalAPI.getAllApprovalList();
                const list = res?.rows || [];
                const allowAdjust = !!list.find((item) => {
                    return item.approvalName === '调价申请';
                })?.status;
                commit('SET_ALLOW_ADJUST', allowAdjust);
            } catch (e) {
                console.log(e);
            }
        },

        // ?拉取利润分类数据
        async fetchProfitClassificationList({ commit }, params) {
            const data = await GoodsAPIV3.getProfitCategoryTypes(params);
            commit('SET_PROFIT_CLASSIFICATION_LIST', data.list || []);
        },
        async fetchInventoryTodo({
            state, commit, getters,
        }, pharmacyNo = state.curPharmacy?.no ?? '') {
            try {
                if (getters.isChainAdmin) {
                    pharmacyNo = '';
                }
                const { data } = await GoodsAPI.fetchTodoCount(isNull(pharmacyNo) ? '' : pharmacyNo);
                commit('SET_INVENTORY_TODO', data);
            } catch (e) {
                console.error('fetchInventoryTodo error', e);
            }
        },
        async fetchGoodsPrimaryClassification({ commit }) {
            try {
                const {
                    data,
                } = await GoodsAPI.fetchGoodsClassificationV3();
                commit('SET_GOODS__PRIMARY_CLASSIFICATION', data.list);
            } catch (e) {
                console.error('fetchGoodsPrimaryClassification error', e);
            }
        },
        async fetchGoodsPrimaryClassificationIfNeed({
            state, dispatch,
        }) {
            if (
                !state.goodsPrimaryClassification ||
                !state.goodsPrimaryClassification.length
            ) {
                dispatch('fetchGoodsPrimaryClassification');
            }
        },

        async initGoodsConfig({
            state, dispatch,
        }) {
            !state.goodsConfigIsInit && (await dispatch('getGoodsConfig'));
        },
        /**
         * @desc  获取连锁门店药品相关的配置（库存设置&&定价、税率）
         * <AUTHOR>
         * @date 2021/09/01 15:20:18
         */
        async getGoodsConfig({
            commit,
        }) {
            try {
                const {
                    data,
                } = await SettingApi.clinicGoodsConfig.getClinicGoodsConfig();

                data.isDengbaoClinic = data.isDengbaoClinic || 0;
                // 门店本地搜索各模块开关、追溯码强校验开关等
                const clinicExternalFlag = data?.clinicExternalFlag || 0;

                commit('SET_GOODS_CONFIG', data);
                // 更新可采集标识码的药品类型
                let typeIdList = [
                    GoodsTypeIdEnum.MEDICINE_WESTERN,
                    GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT,
                    GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
                ];
                if (data.traceCodeConfig?.usableTraceCodeGoodsTypeIdList) {
                    typeIdList = data.traceCodeConfig.usableTraceCodeGoodsTypeIdList.map((item) => {
                        return item.typeId || item.id;
                    });
                }
                TraceCode.setSupportTraceCodeTypeIdList(typeIdList);

                // 明确展示追溯码采集数量的场景
                if (data.traceCodeConfig?.traceCodeForceCheckScenes) {
                    TraceCode.setTraceCodeForceCheckScenes(data.traceCodeConfig.traceCodeForceCheckScenes);
                }

                if (data.traceCodeConfig?.traceCodeFormatRuleCheckList) {
                    TraceCode.setTraceCodeFormatRuleCheckList(data.traceCodeConfig?.traceCodeFormatRuleCheckList);
                }

                // 是否强校验实采必须等于应采 且还用来判断是否开启追溯码暂存功能
                if (isNotNull(data.traceCodeConfig?.requireCollectTraceCode)) {
                    TraceCode.setRequireCollectTraceCode(data.traceCodeConfig.requireCollectTraceCode);
                }

                /**
                 * @description 门店是否开通码上放心平台
                 * @property {number} entType 0诊所，1医院，10 药店
                 * @property {string} refEntId 阿里码上放心平台RefEntId
                 * @property {number} status 0 未配置refEntId, 1 已配置refEntId未通过阿里验证, 2 已配置refEntId且通过阿里验证
                 */
                if (data.traceCodeConfig?.aliHealth) {
                    // 是否开通码上放心平台
                    TraceCode.setHasCodeSafeOpened(data.traceCodeConfig.aliHealth.status === 2);
                }

                /**
                 * @description 是否开启采集强控制模式
                 * @property {number} enableTraceableCodeRule 1:启用 0:不启用
                 */
                TraceCode.setHasEnableCollCheckStrictMode(data.traceCodeConfig?.enableTraceableCodeRule === 1);

                /**
                 * @description 是否开启拆零不采功能
                 * @property {number} shebaoDismountingCollectStrategy 没有字段或者null:无限制 0:拆零不采集 1:拆零采集
                 */
                TraceCode.setHasEnableDismountingMode(data.traceCodeConfig.shebaoDismountingCollectStrategy === 0);

                // 追溯码强校验开关
                const isStrictCountWithTraceCodeCollect = !!(clinicExternalFlag & TraceCodeCollectStrictCountFlag);
                commit('SET_STRICT_COUNT_SWITCH', isStrictCountWithTraceCodeCollect);
                TraceCode.setStrictCountSwitch(isStrictCountWithTraceCodeCollect);



                // 更新其他配置到search engine
                const goodsRepoInstance = repository.GoodsRepositoryService.getInstance();
                if (goodsRepoInstance?.searchConfig) {
                    // 总部暂时不走本地搜索
                    // if (getters.isChainAdmin) {
                    //     goodsRepoInstance.closeLocalSearch();
                    // } else {
                    goodsRepoInstance.setSearchConfig({
                        // true表示使用前端搜索，false表示使用后端搜索，理论上和下面的配置是互斥的
                        useLocalSearchGoodsAPI: !(clinicExternalFlag & LocalSearchFlag),
                        // true表示使用后端搜索，false表示使用前端搜索，理论上和上面的配置是互斥的
                        useRemoteSearchGoodsAPI: !(clinicExternalFlag & RemoteSearchFlag),
                    });

                    // }
                }
            } catch (err) {
                console.error(err);
            }
        },
        // 更新 goodsConfig 后，后台返回的数据需要同步到 goodsConfig 中，不需要重新去拉取数据
        async setGoodsConfig({ commit }, submitData) {
            const { data: goodsConfig } = await SettingApi.clinicGoodsConfig.updateClinicGoodsConfig(submitData);

            goodsConfig.isDengbaoClinic = goodsConfig.isDengbaoClinic || 0;
            commit('SET_GOODS_CONFIG', goodsConfig);

            return goodsConfig;
        },
        // 更新锁库配置
        async updateStockLockConfig({ commit }, submitData) {
            const { data } = await SettingApi.clinicGoodsConfig.updateStockLockConfig({
                lockConfigs: submitData,
            });

            if (data) {
                data.isDengbaoClinic = data.isDengbaoClinic || 0;
                commit('SET_GOODS_CONFIG', data);
            }
        },

        async selectStockRoomId({
            commit, state, getters,
        }, stockRoomId = '') {
            // 没有拉到库房 id，认为选择失败
            if (!getters.pharmacyUserList?.length) {
                commit('SET_CUR_PHARMACY_ID', '');
                commit('SET_CUR_PHARMACY', null);
                return '';
            }

            // 选中了汇总库房（诊所管家）
            if (stockRoomId === SUMMARY_PHARMACY_ID) {
                commit('SET_CUR_PHARMACY_ID', SUMMARY_PHARMACY_ID);
                commit('SET_CUR_PHARMACY', SUMMARY_PHARMACY);
                return SUMMARY_PHARMACY_ID;
            }

            // 没有选中的库房，优先使用当前的
            if (!stockRoomId) {
                stockRoomId = state.curPharmacyId;
            }
            // 期望选择的库房
            let expectedStockRoom = getters.pharmacyUserList?.find((p) => p.id === stockRoomId);

            if (!expectedStockRoom) {
                // 库房没有匹配中，默认选择一个非合作库房的药房
                expectedStockRoom = getters.pharmacyUserList?.find((p) => p.type !== 10);
            }

            commit('SET_CUR_PHARMACY_ID', expectedStockRoom.id);
            commit('SET_CUR_PHARMACY', expectedStockRoom);

            return expectedStockRoom.id;
        },
        async fetchAllGoodsTypes({ commit }) {
            const { data } = await GoodsAPI.fetchGoodsClassificationV3({
                queryType: 1,
                needCustomType: 1,
            });

            const goodsAllTypes = data?.list?.map((item) => {
                const children = item.customTypes || [];
                if (children.length) {
                    children.push({
                        id: -item.id,
                        name: '未指定',
                        sort: 999,
                        typeId: +item.id,
                    });
                }
                return {
                    ...item,
                    children,
                };
            }) ?? [];

            commit('SET_GOODS_ALL_TYPES', goodsAllTypes);
        },
    },
};
export default inventory;
