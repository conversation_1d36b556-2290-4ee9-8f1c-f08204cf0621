/**
 * 设置相关，会替代 ./config.js
 */
import request from 'api/clinic';
import API from 'api/property';
import clone from 'utils/clone';
import PropertyAPI from 'api/property';
import Logger from 'utils/logger';
import TraceCode from '@/service/trace-code/service';
import { getIsProxyNetworkWuhanRegion } from 'utils/electron-tools';

const SET_DATA_PERMISSION = 'SET_DATA_PERMISSION';
const SET_DATA_PERMISSION_INIT = 'SET_DATA_PERMISSION_INIT';
const SET_CRM_PERMISSION = 'SET_CRM_PERMISSION';
const SET_CRM_PERMISSION_INIT = 'SET_CRM_PERMISSION_INIT';
const SET_SELF_SERVICE_PERMISSION = 'SET_SELF_SERVICE_PERMISSION';
const SET_NURSE_SETTINGS = 'SET_NURSE_SETTINGS';
const SET_CHAIN_BASIC_DISABLED_WX_AUTO_REPLY = 'SET_CHAIN_BASIC_DISABLED_WX_AUTO_REPLY';
const SET_SELF_SERVICE_PERMISSION_INIT = 'SET_SELF_SERVICE_PERMISSION_INIT';
const SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DIMENSION = 'SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DIMENSION';
const SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DISTINGUISH = 'SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DISTINGUISH';
const SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_TYPE = 'SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_TYPE';
const SET_STATISTICS_CHARGE_SHEET_STAT_CONFIG_STAT_DIMENSION = 'SET_STATISTICS_CHARGE_SHEET_STAT_CONFIG_STAT_DIMENSION';
const SET_CHAIN_BASIC = 'SET_CHAIN_BASIC';
const SET_CHAIN_BASIC_INIT = 'SET_CHAIN_BASIC_INIT';
const SET_CHAIN_BASIC_ENABLE_CHILD_HEALTH = 'SET_CHAIN_BASIC_ENABLE_CHILD_HEALTH';
const SET_CLINIC_BASIC = 'SET_CLINIC_BASIC';
const SET_CLINIC_BASIC_INIT = 'SET_CLINIC_BASIC_INIT';
const SET_TIPS = 'SET_TIPS';
const SET_TIPS_INIT = 'SET_TIPS_INIT';
const SET_OUTPATIENT_SETTING = 'SET_OUTPATIENT_SETTING';
const SET_OUTPATIENT_DIRECT_REG_ORDER_NO_STRATEGY = 'SET_OUTPATIENT_DIRECT_REG_ORDER_NO_STRATEGY';
const SET_HIS_SURGEY = 'SET_HIS_SURGEY';
const SET_HIS_SURGEY_INIT = 'SET_HIS_SURGEY_INIT';
const SET_STATISTICS_INVENTORY_STAT_CONFIG_STOCK_HAVE_CHANGE = 'SET_STATISTICS_INVENTORY_STAT_CONFIG_STOCK_HAVE_CHANGE';
const SET_WX_REPLY_SOUND = 'SET_WX_REPLY_SOUND';
const SET_FOLLOW_UP_TASK_SILENT_TIP = 'SET_FOLLOW_UP_TASK_SILENT_TIP';

const property = {
    state: {
        // 数据权限, scope: clinic
        dataPermission: {
            dashboard: {
                // 收费员查看账目
                // [{"label":"不允许查看账目","value":0},
                // {"label":"只允许查看个人经手账目","value":1},
                // {"label":"允许查看门店全部账目","value":2}]
                chargerPermission: 2,
                // 医生查看诊疗收入 [{"label":"不允许查看个人诊疗收入","value":0},{"label":"允许查看个人诊疗收入","value":1}]
                doctorOutpatientFee: 0,
                // 医生查看挂号收入 [{"label":"不允许查看个人挂号收入","value":0},{"label":"允许查看个人挂号收入","value":1}]
                doctorRegistrationFee: 1,
                // 医生查看患者看板 [{"label":"只允许查看自己的患者看板","value":0},{"label":"允许查看当天所有患者看板","value":1}]
                kanbanPermission: 0,
            },
            // 社保业务相关
            shebao: {
                // 支持中心码
                isSupportCenterCode: 0,
            },

            outpatient: {
                // 医生查看药品价格
                // [{"label":"允许查看药品明细,总价","value":1},
                // {"label":"只允许查看药品总价","value":0},
                // {"label":"不允许查看药品明细,总价","value":2}]
                goodsPrice: 1,
                // 医生查看处方价格 [{"label":"允许","value":1},{"label":"不允许","value":0}]
                prescriptionPrice: 1,
                // 医生查看门诊单总价 [{"label":"允许","value":1},{"label":"不允许","value":0}]
                totalPrice: 1,
                // 医生查看历史处方 [{"label":"允许查看患者所有的历史处方","value":1},{"label":"只允许查看自己开出的历史处方","value":0}]
                historyPrescription: 1,
                // 医生查看药品进价 [{"label":"允许","value":1},{"label":"不允许","value":0}]
                goodsCostPrice: 0,
                patientMobile: {
                    // 查看患者手机号 [{"label":"有XX权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可查看","value":2}]
                    value: 0,
                    // 指定成员
                    employees: [],
                },
            },
            cashier: {
                // 收费员查看最近进价 0不行，1可以
                goodsCostPrice: 0,
                // 收费员查看患者就诊历史：1允许 0不允许
                patientHistory: 0,
                patientMobile: {
                    // 查看患者手机号 [{"label":"有XX权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可查看","value":2}]
                    value: 0,
                    // 指定成员
                    employees: [],
                },
                payMode: {
                    // 修改支付方式 [{"label":"有收费权限可修改","value":0},{"label":"仅管理员权限可修改","value":1},{"label":"指定成员可修改","value":2}]
                    modifyPayMode: 0,
                    // 指定成员
                    employees: [],
                },
            },
            pharmacy: {
                // 发药员查看患者就诊历史 [{"label":"不允许查看","value":0},{"label":"允许查看","value":1}]
                patientHistory: 0,
                patientMobile: {
                    // 查看患者手机号 [{"label":"有XX权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可查看","value":2}]
                    value: 0,
                    // 指定成员
                    employees: [],
                },
            },
            inventory: {
                goodsCostConfig: {
                    // 指定成员
                    employees: [],
                    // 查看药品物资成本 [{"label":"有库存权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    goodsCost: 0,
                },
                goodsProfitConfig: {
                    // 指定成员
                    employees: [],
                    // 查看药品物资毛利 [{"label":"有库存权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    goodsProfit: 1,
                },
                checkGoodsPrice: {
                    // 指定成员
                    employees: [],
                    // 查看盘点药品价格 [{"label":"有库存权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    goodsProfit: 1,
                },
                damageGoodsPrice: {
                    // 指定成员
                    employees: [],
                    // 查看报损药品价格 [{"label":"有库存权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    goodsProfit: 1,
                },
                obtainGoodsPrice: {
                    // 指定成员
                    employees: [],
                    // 查看领用药品价格 [{"label":"有库存权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    goodsProfit: 1,
                },
                transGoodsPrice: {
                    // 指定成员
                    employees: [],
                    // 查看调拨药品价格 [{"label":"有库存权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    goodsProfit: 1,
                },
                goodsAdjustPriceConfig: {
                    // 指定成员
                    employees: [],
                    // 指定角色
                    roles: [],
                    // 操作药品物质价格 [{"label":"有商品权限可查看","value":0},{"label":"管理员/运营人员可操作","value":3},{"label":"指定成员操作","value":2}]
                    goodsAdjustPrice: 1,
                },
                goodsArchivesConfig: {
                    // 指定成员
                    employees: [],
                    // 指定角色
                    roles: [],
                    // 操作药品物质价格 [{"label":"有商品权限可查看","value":0},{"label":"管理员/店长/质量负责人/质量管理员/采购负责人可操作","value":3},{"label":"指定成员操作","value":2}]
                    goodsArchives: 1,
                },
                goodsCreateArchivesConfig: {
                    // 指定成员
                    employees: [],
                    // 指定角色
                    roles: [],
                    // 操作药品物质价格 [{"label":"有商品权限可查看","value":0},{"label":"管理员/店长可操作","value":3},{"label":"指定成员操作","value":2}]
                    goodsArchives: 0,
                },
                goodsModifyArchivesConfig: {
                    // 指定成员
                    employees: [],
                    // 指定角色
                    roles: [],
                    // 操作药品物质价格 [{"label":"有商品权限可查看","value":0},{"label":"管理员/店长可操作","value":3},{"label":"指定成员操作","value":2}]
                    goodsArchives: 0,
                },
                goodsDeleteArchivesConfig: {
                    // 指定成员
                    employees: [],
                    // 指定角色
                    roles: [],
                    // 操作药品物质价格 [{"label":"有商品权限可查看","value":0},{"label":"管理员/店长可操作","value":3},{"label":"指定成员操作","value":2}]
                    goodsArchives: 0,
                },
            },
            statistics: {
                goodsCostConfig: {
                    // 指定成员
                    employees: [],
                    // 查看药品物资成本 [{"label":"有统计权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    goodsCost: 0,
                },
                goodsProfitConfig: {
                    // 指定成员
                    employees: [],
                    // 查看药品物资毛利 [{"label":"有统计权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    goodsProfit: 0,
                },
                patientMobileConfig: {
                    // 指定成员
                    employees: [],
                    // 查看药品物资毛利 [{"label":"有统计权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可见","value":2}]
                    patientMobile: 0,
                },
            },
            mall: { // 拥有查看价格权利
                goodsPriceConfig: {
                    goodsPrice: 0,
                    employees: [],
                }, //拥有采购权利
                goodsPurchaseConfig: {
                    goodsPurchase: 0,
                    employees: [],
                },
            },
            medicalInsurance: {
                // 查看医保主页
                viewHomePageConfig: {
                    viewHome: 0,
                    employees: [],
                },
                // 查看医保账目
                viewAccountConfig: {
                    viewAccount: 0,
                    employees: [],
                },
                // 查看医保登记信息
                viewBusinessRegistrationRecordConfig: {
                    viewBusinessRegistrationRecord: 0,
                    employees: [],
                },
                // 查看医保机构资料
                viewProfileDataConfig: {
                    viewProfileData: 0,
                    employees: [],
                },
                // 查看医保设置
                viewSetupInformationConfig: {
                    viewSetupInformation: 0,
                    employees: [],
                },
            },
            crm: {
                // 医生查看患者信息 [{"label":"查看全部患者","value":1},{"label":"仅查看自己接诊过的患者","value":0}]
                doctorPatients: 0,
                // 执行人查看患者信息 [{"label":"查看全部患者","value":1},{"label":"仅查看自己参与过的患者（包括开单、执行）","value":0}]
                executorPatients: 0,
                patientMobile: {
                    // 查看患者手机号 [{"label":"有XX权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可查看","value":2}]
                    value: 0,
                    // 指定成员
                    employees: [],
                },
                modifyFirstFromAway: {
                    value: 0,
                    employees: [],
                },
                patientPayAmount: {
                    value: 0,
                    employees: [],
                },
                modifyName: {
                    value: 0, // 0 全部成员可修改 1 仅管理员可修改 2 指定成员可修改
                    employees: [],
                },
                modifyIdCard: {
                    value: 0, // 0 全部成员可修改 1 仅管理员可修改 2 指定成员可修改
                    employees: [],
                },
                modifySn: {
                    value: 0, // 0 全部成员可修改 1 仅管理员可修改 2 指定成员可修改
                    employees: [],
                },
            },
            nurse: {
                // 执行站查看历史执行单 0-允许查看所有 1-仅允许查看自己开出的
                historySheet: 0,
                // 执行站查看已完成的单据 [{"label":"允许所有人查看","value":0},{"label":"只允许参与人查看（包括开单人及执行人）","value":1}]
                executedSheetDetail: 0,
                // 执行站查看患者就诊历史 [{"label":"允许查看","value":1},{"label":"不允许查看","value":0}]
                medicalHistory: 1,
                patientMobile: {
                    // 查看患者手机号 [{"label":"有XX权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可查看","value":2}]
                    value: 0,
                    // 指定成员
                    employees: [],
                },
            },
            registration: {
                // 预约挂号查看患者就诊历史 [{"label":"允许查看","value":1},{"label":"不允许查看","value":0}]
                medicalHistory: 1,
                patientMobile: {
                    // 查看患者手机号 [{"label":"有XX权限可查看","value":0},{"label":"仅管理员权限可查看","value":1},{"label":"指定成员可查看","value":2}]
                    value: 0,
                    // 指定成员
                    employees: [],
                },
                modifyPayMode: {
                    // 修改支付方式 [{"label":"有挂号预约权限可修改","value":0},{"label":"仅管理员权限可修改","value":1},{"label":"指定成员可修改","value":2}]
                    value: 0,
                    // 指定成员
                    employees: [],
                },
            },
            settings: {
                //系统设置 修改成员姓名/签名 0:成员修改 1:管理员修改
                modifyEmployeeName: 0,
            },
        },

        crmPermission: {
            patient: {
                enableMemberPassword: 0,
                enableMemberRecharge: 1,
                enableTherapist: 0, // 支持首评治疗师理疗师
            },
        },
        statistics: {
            inventoryStatConfig: {
                statDimension: 0,
                stockHaveChange: 0,
                statType: 1,
                isDistinguishClassify: 0,
            },
            chargeSheetConfig: {
                statDimension: 0,
            },
        },
        // 自助服务机设置, scope: clinic
        selfServiceSettings: {
            // 开启签到取号
            enableSignIn: 0,
            // 是否缴费后才可签到
            enablePaymentSignIn: 0,
            // 是否打印挂号单
            enableRegistrationPrint: 0,

            // 是否开启自助缴费 0或者1
            enableAutoPay: 0,
            // 待缴费清单显示 1：显示每项费用名称、数量及金额，2：仅显示费用分类及金额
            waitPayInventory: 1,
            //  支付方式 1：会员卡余额支付，2：微信支付，   传值方式：[1,2]
            payModes: [],
            //  支付方式 1：会员卡余额支付，2：微信支付，   传值方式：[1,2]
            registrationPayModes: [],
            // 是否打印收费单 0或者1
            enablePrintChargeSheet: 0,
            // 是否打印发药单 0后者1
            enablePrintDispensingSheet: 0,
            // 缴费完成引导语
            guideMessage: '',
        },

        nurseSettings: {
            listDisplaySetting: 0,
            onlyExecuteAfterPaid: 0,
            canOpenGoodsTypes: [],
        },

        // 连锁基础配置, scope: chain
        chainBasic: {
            // 收费时销售员支持必填设置：0：非必填，1：必填
            chargeRequiredSeller: 0,
            // 微诊所
            weClinic: {
                // 领券入口开关，[{"label":"领券入口开关","checked":1,"unchecked":0}]
                getCouponSwitch: 0,
                // 自助续方
                continueSheet: {
                    // 在线自助续方，[{"label":"未开启","value":0},{"label":"已开启","value":1}]
                    autoContinueSwitch: 0,
                    // 拍照续方，[{"label":"未开启","value":0},{"label":"已开启","value":1}]
                    photographSwitch: 0,
                    // 历史处方续方，[{"label":"未开启","value":0},{"label":"已开启","value":1}]
                    historySheetContinueSwitch: 0,
                },
                // 是否可以升级小程序，后台控制，默认不能升级
                canUpgradeWeapp: 0,
                disableWxAutoReply: 0, // 1 关闭 0 开启
            },

            // 营销配置
            promotion: {
                // 退款后是否返还优惠券，[{"label":"退款后是否返还优惠券","checked":1,"unchecked":0}]
                refundCoupon: 1,
                // 是否添加过优惠券，[{"label":"是否添加过优惠券","checked":1,"unchecked":0}]
                isAddedCoupon: 0,
            },

            // [{"label":"是否开启儿保","checked":1,"unchecked":0}]
            isEnableChildHealth: 0,

            // [{"label":"是否开启成都电子健康卡","checked":1,"unchecked":0}]
            isEnableChengduHealthCard: 0,

            // 是否开启就诊原因
            isEnableVisitSource: 0,

            // 是否允许编辑初复诊
            isEnableEditRevisitStatus: 0,

            // 是否开通慢病
            isEnableChronicRecovery: 0,

            // 是否能够使用身份证阅读器
            isEnableIdCardReader: 0,

            // 是否能够使用预约升级
            isEnableRegUpgrade: 0,

            // 是否支持眼科检查报告书打印
            isEnableEyeInspectReportV2: 0,

            //是否能够使用操作日志功能
            isShowUserOperationLog: 1,

            // 门诊相关连锁配置
            outpatient: {
                // 主诉必填
                requiredChiefComplaint: 0,
                // 诊断必填
                requiredExtendDiagnosisInfos: 0,
                // 过敏史必填
                requiredAllergicHistory: 0,
                // 辅助检查必填
                requiredAuxiliaryExaminations: 0,
                // 处置必填
                requiredDisposals: 0,
                // 治法必填
                requiredTherapy: 0,
                // 医嘱事项必填
                requiredDoctorAdvice: 0,
                // 现病史必填
                requiredPresentHistory: 0,
                // 既往史必填
                requiredPastHistory: 0,
                // 流行病学史必填
                requiredEpidemiologicalHistory: 0,
                // 望闻切诊必填
                requiredChineseExamination: 0,
                // 辩证必填
                requiredSyndrome: 0,
                // 体格检查
                requiredPhysicalExamination: 0,
                // 默认添加药品后focus到第一个没填的输入框，为1的话 填了的情况默认focus到单次剂量
                prescriptionFocusDosage: 0,
            },

            // crm 连锁配置
            crm: {
                // 手机号必填
                requiredMobile: 0,
                // 身份证必填
                requiredIdCard: 0,
                // 地址必填
                requiredAddress: 0,
                // 详细地址必填
                requiredAddressDetail: 0,
            },
            // 挂号
            registration: {
                requiredVisitSource: 0, // 就诊推荐
            },

            // 连锁货币符号
            currencyType: 0, // 0: 人民币 1: 澳门元
            // 是否支持煎药工艺卡打印
            isEnableDecoctionCraftCard: 0,
            // 张仲景门店-退货申请按钮
            stockInReturnOutApply: 0, // 是否开启退货申请按钮 0 关闭 1 开启
            disableSubClinicStockInReturnOut: 0, // 是否禁用子店退货按钮 0 不禁用 1 禁用
        },

        clinicBasic: {
            // 是否开启武汉健康卡 [{"label":"是否开启武汉健康卡","checked":1,"unchecked":0}]
            isEnableWuhanHealthyCard: 0,

            // 是否开启绵阳电子健康卡
            isEnableMianyangHealthCard: 0,

            // 医保限制地域，城市拼音，如 shenzhen
            region: '',
            template: {
                prescriptionCatalogTier: 1,
            },
            // 深圳相关的配置
            shenzhen: {
                isEnablePinganCity: 0, // 是否开启平安智慧城市
            },
            outpatient: {
                settings: {
                    // 输注用量计算策略
                    infusion: 0,
                    // 雾化用量计算策略
                    nebulizer: 1,
                    // 中药处方是否允许混开
                    chinesePrescriptionSupportMix: 1,
                    // 疫情哨点检测
                    covid19Detection: 0,
                    // 新冠症状
                    covid19Keyword: [],
                    // 传染病哨点监控
                    infectiousDiseases: 0,
                    // 诊所在线问诊检查
                    isOpenOnlineAudit: 0,
                },
                directRegOrderNoStrategy: 0,
            },
            crm: {
                settings: {
                    needRealAuth: 0,
                },
            },
            feeNameDisplay: {
                registrationFee: '挂号费',
            },
            scanRegisterEpidemiological: 0,//是否开启自助登记 0 关闭 1 开启
            isChinesePrescriptionShowSort: 0, // 中药处方是否加上序号 0 关闭 1 开启
            isEnableCA: 0, // 是否开启CA电子签名
            hospital: {
                createAdvice: {
                    beforeToday: 0, //是否允许补开
                },
                inpatientDaysConfig: 0, //默认 0 ,0 计入院当天不计出院当天  1 计出院当天计入院当天
            },
            // 是否支持批量调价-场景库存内批量调价入口
            isSupportBatchPriceAdjustment: 1,
            revisitExternalLink: '', // 随访外链
            // 是否支持退药小票自动打印
            isShowAutoPrintUnDispenseSwitch: 0,
            // 是否显示推荐和来源
            isRecommendAndSourceDisplay: 1,

            isEnableIntranet: 0, // 是否内网限制
            wxReplySound: 0, // 是否开启微信消息提示音
            wxRevisitNotify: 0, // 是否开启随访任务静默提示
        },

        tips: {
            index: {
                implNationalTip: 1, // 是否点击过 工作台 未贯标提醒 0 => 否， 1 => 是
                medicalDevelopment: 1, // 是否点击过 精准医疗推广计划弹窗 0 => 否， 1 => 是
                isReadRefuseReturnGoodsDialog: 0, // 是否阅读过 不退货提示弹窗 0 => 否， 1 => 是
            },
        },

        // 诊所+员工配置
        clinicEmployeeSetting: {
            paid: {
                sameTimeDispensing: null,
            },
        },

        // 诊所检查 pacs 设备配置
        clinicPacsSetting: {
            deviceList: [],
        },

        // 手术配置
        hisSurgery: {
            staticOptions: {
                surgeryLevel: [],
                surgeryIncisionHealingLevel: [],
                surgerySite: [],
                surgeryAnesthesiaMethod: [],
            },
        },

        isDataPermissionInited: false,
        isCrmPermissionInited: false,
        isSelfServicePermissionInited: false,
        isChainBasicInited: false,
        isClinicBasicInited: false,
        isTipsInited: false,
        isChinesePrescriptionShowSortInited: false,
        isClinicEmployeeSettingInited: false,
        isClinicPacsSettingsInit: false,
        isEnablePacsUpgrade: false,
        isHisSurgeryInit: false,
        isRevisitExternalLinkInit: false,

        isInitTraceCodeConfig: false,
        traceCodeConfig: {
            collectionSwitch: 0,
            collectionCheck: 0,
            inventory: {
                isEnableNoCodeReport: 0,// 是否开启无码上报
            },
            autoUseDecomposeTraceCode: 0, //自动使用拆零追溯码
        },

        hospitalAutoRule: {
            isInit: false,
            types: [],
            isPreciousDevice: 2,
        },
    },

    mutations: {
        SET_DATA_PERMISSION: (state, data) => {
            Object.assign(state.dataPermission, data || {});
        },
        SET_DATA_PERMISSION_INIT: (state) => {
            state.isDataPermissionInited = true;
        },
        SET_CRM_PERMISSION: (state, data) => {
            Object.assign(state.crmPermission, data || {});
        },
        SET_CRM_PERMISSION_INIT: (state) => {
            state.isCrmPermissionInited = true;
        },
        SET_SELF_SERVICE_PERMISSION: (state, data) => {
            Object.assign(state.selfServiceSettings, data || {});
        },
        SET_OUTPATIENT_SETTING: (state, data) => {
            state.clinicBasic.outpatient.settings = clone(data) || {};
        },
        SET_OPEN_SCAN_QR_CODE_REGISTER: (state, isOpenScanQrCodeRegister) => {
            state.clinicBasic.scanRegisterEpidemiological = isOpenScanQrCodeRegister;
        },
        SET_CHINESE_PRESCRIPTION_SHOW_SORT: (state, isChinesePrescriptionShowSort) => {
            state.clinicBasic.isChinesePrescriptionShowSort = isChinesePrescriptionShowSort;
        },
        SET_CHINESE_PRESCRIPTION_SHOW_SORT_INIT: (state) => {
            state.isChinesePrescriptionShowSortInited = true;
        },
        SET_REVISIT_EXTERNAL_LINK: (state, revisitExternalLink) => {
            state.clinicBasic.revisitExternalLink = revisitExternalLink;
        },
        SET_REVISIT_EXTERNAL_LINK_INIT: (state) => {
            state.isRevisitExternalLinkInit = true;
        },

        SET_OUTPATIENT_DIRECT_REG_ORDER_NO_STRATEGY: (state, data) => {
            state.clinicBasic.outpatient.directRegOrderNoStrategy = data || 0;
        },

        SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DIMENSION: (state, data) => {
            state.statistics.inventoryStatConfig.statDimension = data || 0;
        },
        SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DISTINGUISH: (state, data) => {
            state.statistics.inventoryStatConfig.isDistinguishClassify = data || 0;
        },
        SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_TYPE: (state, data) => {
            state.statistics.inventoryStatConfig.statType = data || 1;
        },
        SET_STATISTICS_INVENTORY_STAT_CONFIG_STOCK_HAVE_CHANGE: (state, data) => {
            state.statistics.inventoryStatConfig.stockHaveChange = data || 0;
        },

        SET_STATISTICS_CHARGE_SHEET_STAT_CONFIG_STAT_DIMENSION: (state, data) => {
            state.statistics.chargeSheetConfig.statDimension = data || 0;
        },

        SET_SELF_SERVICE_PERMISSION_INIT: (state) => {
            state.isSelfServicePermissionInited = true;
        },

        SET_NURSE_SETTINGS: (state, data) => {
            Object.assign(state.nurseSettings, data || {});
        },
        SET_CHAIN_BASIC_DISABLED_WX_AUTO_REPLY: (state, data) => {
            state.chainBasic.weClinic.disableWxAutoReply = data;
        },
        SET_WX_REPLY_SOUND: (state, data) => {
            state.clinicBasic.wxReplySound = data;
        },
        SET_FOLLOW_UP_TASK_SILENT_TIP: (state, data) => {
            state.clinicBasic.wxRevisitNotify = data;
        },
        SET_CHAIN_BASIC: (state, data) => {
            Object.assign(state.chainBasic, data || {});
        },
        SET_CHAIN_BASIC_INIT: (state) => {
            state.isChainBasicInited = true;
        },

        SET_CLINIC_BASIC: (state, data) => {
            Object.assign(state.clinicBasic, data || {});
        },

        SET_CLINIC_BASIC_INIT: (state) => {
            state.isClinicBasicInited = true;
        },

        SET_CHAIN_BASIC_ENABLE_CHILD_HEALTH: (state, isEnableChildHealth) => {
            state.chainBasic.isEnableChildHealth = isEnableChildHealth;
        },

        SET_TIPS: (state, data) => {
            Object.assign(state.tips, data);
        },

        SET_TIPS_INIT: (state) => {
            state.isTipsInited = true;
        },

        SET_HIS_SURGEY: (state, data) => {
            Object.assign(state.hisSurgery, data);
        },

        SET_HIS_SURGEY_INIT: (state) => {
            state.isHisSurgeryInit = true;
        },

        SET_CLINIC_EMPLOYEE_SETTING: (state, data) => {
            Object.assign(state.clinicEmployeeSetting, data || {});
        },
        SET_CLINIC_EMPLOYEE_SETTING_INIT: (state) => {
            state.isClinicEmployeeSettingInited = true;
        },

        SET_CLINIC_PACS_SETTING: (state, data) => {
            Object.assign(state.clinicPacsSetting, data || {});
        },
        SET_CLINIC_PACS_SETTING_INIT: (state) => {
            state.isClinicPacsSettingsInit = true;
        },
        SET_CLINIC_PACS_IS_UPGRADE: (state,data) => {
            state.isEnablePacsUpgrade = data;
        },
        SET_TRACE_CODE_CONFIG_INIT: (state) => {
            state.isInitTraceCodeConfig = true;
        },
        SET_TRACE_CODE_CONFIG: (state, data) => {
            Object.assign(state.traceCodeConfig, data);
        },

        SET_HOSPITAL_AUTO_RULE_CONFIG: (state, data) => {
            state.hospitalAutoRule = Object.assign({}, state.hospitalAutoRule, data);
            state.hospitalAutoRule.isInit = true;
        },
    },

    actions: {
        async initDataPermission({
            state, dispatch,
        }) {
            if (!state.isDataPermissionInited) {
                dispatch('fetchDataPermission');
            }
        },
        // 获取数据权限
        async fetchDataPermission({ commit }) {
            const res = await request.fetchDataPermission();
            // 接口修改所有权限都返回在data中了
            commit(SET_DATA_PERMISSION, res?.data || {});
            commit(SET_DATA_PERMISSION_INIT, true);
        },
        setScanQrCodeRegister({ commit },isOpenScanQrCodeRegister) {
            commit('SET_OPEN_SCAN_QR_CODE_REGISTER', isOpenScanQrCodeRegister);
        },

        // 获取中药处方是否显示排序
        async getChinesePrescriptionShowSort({ commit }) {
            try {
                const { data } = await API.getV3('chainBasic.outpatient.isChinesePrescriptionShowSortNo', 'chain');
                commit('SET_CHINESE_PRESCRIPTION_SHOW_SORT', !!data);
                commit('SET_CHINESE_PRESCRIPTION_SHOW_SORT_INIT');
            } catch (e) {
                console.error(e);
            }
        },
        async initChinesePrescriptionShowSort({
            state, dispatch,
        }) {
            if (!state.isChinesePrescriptionShowSortInited) {
                dispatch('getChinesePrescriptionShowSort');
            }
        },

        // 获取随访外链，目前只有青岛李沧区康万家一家在用
        async getRevisitExternalLink({ commit }) {
            try {
                const { data } = await PropertyAPI.getV3('crm.patient.revisitExternalLink', 'chain');
                commit('SET_REVISIT_EXTERNAL_LINK', data);
                commit('SET_REVISIT_EXTERNAL_LINK_INIT');
            } catch (e) {
                console.error(e);
            }
        },
        async initRevisitExternalLink({
            state, dispatch,
        }) {
            if (!state.isRevisitExternalLinkInit) {
                dispatch('getRevisitExternalLink');
            }
        },

        async updateDataPermission({
            commit,dispatch,
        }, data) {
            await request.updateDataPermission(data);
            commit(SET_DATA_PERMISSION, data);
            // 拉取用户信息，更新用户权限
            dispatch('acFetchUserInfo');
        },

        async updateInventoryStatDimension({ commit }, data) {
            await API.update('statistics.inventoryStatConfig.statDimension', 'clinic', {
                ...data,
            });
            commit(SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DIMENSION, data.statDimension || 0);
        },
        async updateInventoryStatDistinguish({ commit }, data) {
            await API.update('statistics.inventoryStatConfig.isDistinguishClassify', 'clinic', {
                ...data,
            });
            commit(SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DISTINGUISH, data.isDistinguishClassify || 0);
        },
        async updateInventoryStatType({ commit }, data) {
            await API.update('statistics.inventoryStatConfig.classifyStatisticsType', 'clinic', {
                ...data,
            });
            commit(SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_TYPE, data.classifyStatisticsType || 0);
        },

        async updateChargeSheetStatDimension({ commit }, data) {
            await API.update('statistics.chargeSheetConfig.statDimension', 'clinic', {
                ...data,
            });
            commit(SET_STATISTICS_CHARGE_SHEET_STAT_CONFIG_STAT_DIMENSION, data.statDimension || 0);
        },

        async initCrmPermission({
            state, dispatch,
        }) {
            if (!state.isCrmPermissionInited) {
                await dispatch('fetchCrmPermission');
            }
        },
        async fetchCrmPermission({ commit }) {
            const res = await API.get('crm', 'chain');
            const { crm } = res.data;
            commit(SET_CRM_PERMISSION, crm || {});
            commit(SET_CRM_PERMISSION_INIT, true);
        },

        async updateCrmPermission({ commit }, data) {
            try {
                await API.update('crm', 'chain', {
                    crm: data,
                });
                commit(SET_CRM_PERMISSION, data);
            } catch (e) {
                console.error(e);
            }
        },

        async updateInventoryStatStockHaveChange({ commit }, data) {
            await API.update('statistics.inventoryStatConfig.stockHaveChange', 'clinic', {
                ...data,
            });
            commit(SET_STATISTICS_INVENTORY_STAT_CONFIG_STOCK_HAVE_CHANGE, data.statDimension || 0);
        },

        async fetchSelfServiceSettings({ commit }) {
            const res = await API.getV3('selfService.settings', 'clinic');
            console.log(res.data);
            commit(SET_SELF_SERVICE_PERMISSION, res.data || {});
            commit(SET_SELF_SERVICE_PERMISSION_INIT, true);
        },

        async fetchNurseSettings({ commit }) {
            const res = await API.getV3('clinicNurseSettings', 'clinic');
            commit(SET_NURSE_SETTINGS, res.data || {});
        },

        async updateOutpatientSettings({
            state, commit,
        }, data) {
            try {
                await API.updateV3('clinicBasic.outpatient.settings', 'clinic', {
                    ...state.clinicBasic.outpatient.settings,
                    ...data,
                });
                commit(SET_OUTPATIENT_SETTING, data);

            } catch (err) {
                console.log(err);
            }
        },
        async updateOutpatientDirectRegOrderNoStrategy({ commit }, data) {
            try {
                await API.updateV3('clinicBasic.outpatient', 'clinic', {
                    directRegOrderNoStrategy: data,
                });
                commit(SET_OUTPATIENT_DIRECT_REG_ORDER_NO_STRATEGY, data);

            } catch (err) {
                console.log(err);
            }
        },
        async updateDisableWxAutoReply({ commit }, disableWxAutoReply) {
            try {
                await API.updateV3('chainBasic.weClinic', 'chain', {
                    disableWxAutoReply,
                });
                commit(SET_CHAIN_BASIC_DISABLED_WX_AUTO_REPLY, disableWxAutoReply);
            } catch (error) {
                console.error('updateEnableChildHealth error', error);
            }
        },

        async updateWxReplySound({ commit }, wxReplySound) {
            try {
                await API.updateV3('clinicBasic', 'clinic', {
                    wxReplySound,
                });
                commit(SET_WX_REPLY_SOUND, wxReplySound);
            } catch (error) {
                console.error('wxReplySound error', error);
            }
        },

        async updateFollowUpTaskSilentTip({ commit }, wxRevisitNotify) {
            try {
                await API.updateV3('clinicBasic', 'clinic', {
                    wxRevisitNotify,
                });
                commit(SET_FOLLOW_UP_TASK_SILENT_TIP, wxRevisitNotify);
            } catch (error) {
                console.error('followUpTaskSilentTip error', error);
            }
        },

        async updateNurseSettings({ commit }, data) {
            try {
                await API.updateV3('clinicNurseSettings', 'clinic', {
                    ...data,
                });
                commit(SET_NURSE_SETTINGS, data);
            } catch (e) {
                console.error(e);
            }
        },

        async updateSelfServiceSettings({ commit }, data) {
            try {
                await API.updateV3('selfService.settings', 'clinic', {
                    ...data,
                });
                commit(SET_SELF_SERVICE_PERMISSION, data);
            } catch (e) {
                console.error(e);
            }
        },

        async fetchChainBasic({ commit }) {
            try {
                const res = await API.get('chainBasic', 'chain');
                const { chainBasic } = res.data;
                commit(SET_CHAIN_BASIC, chainBasic || {});
                commit(SET_CHAIN_BASIC_INIT, true);
            } catch (e) {
                console.error(e);
            }
        },

        async updateChainBasic({ commit }, data) {
            try {
                await API.update('chainBasic', 'chain', {
                    chainBasic: data,
                });
                commit(SET_CRM_PERMISSION, data);
            } catch (e) {
                console.error(e);
            }
        },

        /**
         * 更新开启儿保配置
         * <AUTHOR>
         * @date 2020-09-27
         * @param {any} {commit}
         * @param {Boolean} isEnableChildHealth 是否开启儿保，0 不开启；1 开启
         */
        async updateEnableChildHealth({ commit }, isEnableChildHealth) {
            try {
                await API.update('chainBasic.isEnableChildHealth', 'chain', {
                    isEnableChildHealth,
                });
                commit(SET_CHAIN_BASIC_ENABLE_CHILD_HEALTH, isEnableChildHealth);
            } catch (error) {
                console.error('updateEnableChildHealth error', error);
            }
        },

        async fetchClinicBasic({ commit }) {
            const res = await API.getV3('clinicBasic', 'clinic');
            commit(SET_CLINIC_BASIC, res.data || {});
            commit(SET_CLINIC_BASIC_INIT, true);
        },

        async initClinicBasic({
            state, dispatch,
        }) {
            if (!state.isClinicBasicInited) {
                dispatch('fetchClinicBasic');
            }
        },

        async fetchTipsFlag({ commit }) {
            const res = await API.getV3('pc.tips', 'employee');
            commit(SET_TIPS, res.data || {});
            commit(SET_TIPS_INIT, true);
        },

        async fetchHisSurgery({
            commit, state,
        }) {
            if (state.isHisSurgeryInit) {
                return;
            }
            const res = await API.get('hisSurgery', 'chain');
            commit(SET_HIS_SURGEY, res.data.hisSurgery || {});
            commit(SET_HIS_SURGEY_INIT, true);
        },

        async fetchInventoryStatDimension({ commit }) {
            const res = await API.getV3('statistics.inventoryStatConfig.statDimension', 'clinic');
            commit(SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DIMENSION, res.data || 0);
        },
        async fetchInventoryStatIsDistinguishClassify({ commit }) {
            const res = await API.getV3('statistics.inventoryStatConfig.isDistinguishClassify', 'clinic');
            commit(SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_DISTINGUISH, res.data || 0);
        },
        async fetchInventoryStockHaveChange({ commit }) {
            const res = await API.getV3('statistics.inventoryStatConfig.stockHaveChange', 'clinic');
            commit(SET_STATISTICS_INVENTORY_STAT_CONFIG_STOCK_HAVE_CHANGE, res.data || 0);
        },
        async fetchInventoryStatType({ commit }) {
            const res = await API.getV3('statistics.inventoryStatConfig.classifyStatisticsType', 'clinic');
            commit(SET_STATISTICS_INVENTORY_STAT_CONFIG_STAT_TYPE, res.data || 1);
        },
        async fetchChargeSheetStatDimension({ commit }) {
            const res = await API.getV3('statistics.chargeSheetConfig.statDimension', 'clinic');
            commit(SET_STATISTICS_CHARGE_SHEET_STAT_CONFIG_STAT_DIMENSION, res.data || 0);
        },

        async setImplNationTip({ commit }) {
            try {
                await API.updateV3('pc.tips.index', 'employee', {
                    implNationalTip: 1,
                });
            } catch (e) {
                console.error(e);
            }
            // loginPassword POST 操作仍旧可以关闭
            commit(SET_TIPS, {
                index: {
                    implNationalTip: 1,
                },
            });
        },
        // 上报用户已经阅读过了不退货弹窗
        async setReadRefuseReturnGoodsDialog({ commit }) {
            try {
                await API.updateV3('pc.tips.index', 'employee', {
                    isReadRefuseReturnGoodsDialog: 1,
                });
            } catch (e) {
                console.error(e);
            }
            // loginPassword POST 操作仍旧可以关闭
            commit(SET_TIPS, {
                index: {
                    isReadRefuseReturnGoodsDialog: 1,
                },
            });
        },
        // 上报用户已经查看过精准医疗发展计划推广弹窗数据
        async setMedicalDevelopmentLooked({ commit }) {
            try {
                await API.updateV3('pc.tips.index', 'employee', {
                    medicalDevelopment: 1,
                });
            } catch (e) {
                console.error(e);
            }
            // loginPassword POST 操作仍旧可以关闭
            commit(SET_TIPS, {
                index: {
                    medicalDevelopment: 1,
                },
            });
        },

        /**
         * @desc 获取诊所员工纬度配置
         * <AUTHOR>
         * @date 2022/11/02 14:39:50
         * @param{ String } clinicEmployeeChargeSettings
         * @param{ String } cli_emp
         * @return { Object }
         */
        async getClinicEmployeeSetting({ commit }) {
            try {
                const { data } = await API.getV3('clinicEmployeeChargeSettings', 'cli_emp');
                commit('SET_CLINIC_EMPLOYEE_SETTING', data);
                commit('SET_CLINIC_EMPLOYEE_SETTING_INIT');
            } catch (e) {
                Logger.error({
                    scene: 'cli_emp_charge_settings_error',
                    err: e,
                });
            }
        },



        /**
         * 获取诊所 pacs 设备列表配置
         * @date 2023/12/21 - 15:03:36
         * <AUTHOR>
         *
         * @async
         * @param {{ commit: any; }} param0
         * @param {*} param0.commit
         * @returns {*}
         */
        async getClinicPacsSetting({ commit }) {
            try {
                const { data = [] } = await API.getV3('examination.settings.inspect.pacsSettings.deviceList', 'clinic') || {};
                commit('SET_CLINIC_PACS_SETTING', {
                    deviceList: data,
                });
                commit('SET_CLINIC_PACS_SETTING_INIT');
            } catch (error) {
                console.error(error);
            }
        },

        async fetchClinicIsEnablePacsUpgrade({ commit }) {
            try {
                const { data } = await API.getV3('examination.settings.inspect.pacsSettings.isEnablePacsUpgrade', 'clinic') || {};
                commit('SET_CLINIC_PACS_IS_UPGRADE',data);
            } catch (error) {
                console.error(error);
            }
        },

        // 更新诊所员工纬度配置
        async updateClinicEmployeeSetting({ commit }, data) {
            try {
                await API.updateV3('clinicEmployeeChargeSettings', 'cli_emp', data);
                commit('SET_CLINIC_EMPLOYEE_SETTING', data);
            } catch (e) {
                console.error(e);
            }
        },
        async initClinicEmployeeSetting({
            state, dispatch,
        }) {
            if (!state.isClinicEmployeeSettingInited) {
                dispatch('getClinicEmployeeSetting');
            }
        },

        async initClinicPacsSetting({
            state, dispatch,
        }) {
            if (!state.isClinicPacsSettingsInit) {
                dispatch('getClinicPacsSetting');
            }
        },

        /**
         * @desc 初始化追溯码设置
         * <AUTHOR> Yang
         * @date 2024-06-12 17:59:03
        */
        async initTraceCodeConfig({
            state, dispatch,
        }) {
            if (!state.isInitTraceCodeConfig) {
                dispatch('fetchTraceCodeConfig');
            }
        },
        async fetchTraceCodeConfig({ commit }) {
            try {
                const { data } = await PropertyAPI.getV3('traceCodeConfig', 'clinic');
                TraceCode.setHasAutoUseDecomposeTraceCode(!!data.autoUseDecomposeTraceCode);
                commit('SET_TRACE_CODE_CONFIG', data);
                commit('SET_TRACE_CODE_CONFIG_INIT');
                return data;
            } catch (e) {
                console.log(e);
            }
        },

        async fetchHospitalAutoRule({
            commit, state,
        }) {
            if (!state.hospitalAutoRule.isInit) {
                const { data } = await PropertyAPI.getV3('dispensing.process.hospitalAutoRule', 'clinic', {});
                commit('SET_HOSPITAL_AUTO_RULE_CONFIG', data ?? {});
            }
            return state.hospitalAutoRule;
        },
        async updateHospitalAutoRule({ commit }, data) {
            try {
                await PropertyAPI.updateV3('dispensing.process.hospitalAutoRule', 'clinic', data);
                commit('SET_HOSPITAL_AUTO_RULE_CONFIG', data);
            } catch (e) {
                console.error(e);
            }
        },
    },

    getters: {
        isReadRefuseReturnGoodsDialog: (state) => {
            const { index } = state.tips;
            return index?.isReadRefuseReturnGoodsDialog || 0;
        },
        hospitalClinicConfig(state) {
            return state.clinicBasic.hospital;
        },
        enableTherapist(state) {
            return state.crmPermission.patient.enableTherapist;
        },
        surgeryLevelList(state) {
            return state.hisSurgery.staticOptions.surgeryLevel;
        },
        surgeryIncisionHealingLevelList(state) {
            return state.hisSurgery.staticOptions.surgeryIncisionHealingLevel;
        },
        surgerySiteList(state) {
            return state.hisSurgery.staticOptions.surgerySite;
        },
        surgeryAnesthesiaMethodList(state) {
            return state.hisSurgery.staticOptions.surgeryAnesthesiaMethod;
        },
        traceCodeConfig(state) {
            return state.traceCodeConfig;
        },
        isEnableAiVoiceMr(state) {
            return state.chainBasic.deepseek?.voiceMr === 1;
        },
        isEnableAiTongue(state) {
            return state.chainBasic.deepseek?.tongue === 1;
        },
        // 限制外网访问
        isIntranetUser(state) {
            return state.clinicBasic.isEnableIntranet || getIsProxyNetworkWuhanRegion();
        },

    },
};

export default property;
