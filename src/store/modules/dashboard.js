import DashboardAP<PERSON> from 'api/dashboard.js';
import SettingsApi from 'api/settings';
import { MODULE_ID_MAP } from 'utils/constants';

const APP_VERSION_CLICKED = '__app_version_clicked__';

function countSettingTodo(state, rootState,rootGetters) {
    // 总部视角
    const { isChainAdmin } = rootGetters;
    const { userInfo } = rootState.user;
    const moduleIds = userInfo.moduleIds.split(',');
    const isAdmin = moduleIds.includes(MODULE_ID_MAP.globalModule);
    const hasClinicSettingPermission = isAdmin || moduleIds.includes(MODULE_ID_MAP.settingSub.clinicSetting);
    const hasAggregatePaymentPermission = isAdmin || moduleIds.includes(MODULE_ID_MAP.settingSub.aggregatePayment);

    const unUploadOrganCertCount = hasClinicSettingPermission && !isChainAdmin ? state.setting.unUploadOrganCertCount : 0;
    const undoReviewCount = hasClinicSettingPermission ? state.setting.undoReviewCount : 0;
    const aggregatePayment = hasAggregatePaymentPermission ? state.setting.aggregatePayment : 0;

    return unUploadOrganCertCount + undoReviewCount + aggregatePayment;
}

const dashboard = {
    state: {
        // 挂号
        registration: {
            todo: 0,
        },
        outpatientEmployee: {
            todo: 0,
        },
        childHealthEmployee: {
            todo: 0,
        },
        cashier: {
            todo: 0,
        },
        approval: {
            todo: 0,
        },
        gspCount: {
            todo: 0,
        },
        materialMaintenanceTodoCount: {
            todo: 0,
        },
        gspDestroy: {
            todo: 0,
        },
        pharmacy: {
            todo: 0,
        },
        hospitalDispensingOrderCount: {
            todo: 0,
        },
        consultation: {
            todo: 0,
        },
        examination: {
            todo: 0,
        },
        inspect: {
            todo: 0,
        },
        setting: {
            todo: 0,
            unUploadOrganCertCount: 0,
            undoReviewCount: 0,
            // 未通过审核的通联支付资料
            aggregatePayment: 0,
        },
        stock: {
            todo: 0,
        },
        stockIn: {
            todo: 0,
            pharmacyNoList: [],
        },
        stockOut: {
            todo: 0,
            pharmacyNoList: [],
        },
        stockApply: {
            todo: 0,
            pharmacyNoList: [],
        },
        stockTrans: {
            todo: 0,
            pharmacyNoList: [],
        },
        expiredWarn: {
            todo: 0,
            pharmacyNoList: [],
        },

        profitRatWarn: {
            todo: 0,
            pharmacyNoList: [],
        },

        stockWarn: {
            todo: 0,
            pharmacyNoList: [],
        },

        stockSocialGoodsWarn: {
            todo: 0,
        },
        // 供应商资质过期提醒
        supplierValidCount: {
            todo: 0,
        },
        stockSocialDiagnosisTreatmentWarn: {
            todo: 0,
        },

        implNationalWarningCount: {
            todo: 0,
        },

        // 咨询患者数量
        medicalPlanFollowup: {
            todo: 0,
        },

        // 待审核列表
        undoReviewList: [],

        appVersionClicked: localStorage.getItem(APP_VERSION_CLICKED) || 0,

        // 合作药房todo
        coCashier: {
            todo: 0,
        },
        // 微诊所-订单管理待发货数量
        cisMallWaitSend: {
            todo: 0,
        },
        // 住院收费
        hospitalSettlingWaitChargeCount: {
            todo: 0,
        },

        // 医保模块
        social: {
            todo: 0,
        },

        // 订单云提醒
        orderCloud: {
            todo: 0,
        },
    },
    getters: {
        unUploadOrganCertCount: (state) => state.setting.unUploadOrganCertCount,
        undoReviewCount: (state) => state.setting.undoReviewCount,
        aggregatePaymentTodo: (state) => state.setting.aggregatePayment,
    },
    mutations: {
        SET_UNDOREVIEW_LIST: (state, {
            list,
            rootState,
            rootGetters,
        }) => {
            state.undoReviewList = list;
            state.setting.undoReviewCount = list.length;
            state.setting.todo = countSettingTodo(state, rootState, rootGetters);
        },

        SET_TODOS: (state, {
            todos, rootState,rootGetters,
        }) => {
            state.registration.todo = todos.registration ?? state.registration.todo;
            state.outpatientEmployee.todo = todos.outpatientEmployee ?? state.outpatientEmployee.todo;
            state.childHealthEmployee.todo = todos.childHealthEmployee ?? state.childHealthEmployee.todo;
            state.cashier.todo = todos.cashier ?? state.cashier.todo;
            state.pharmacy.todo = todos.pharmacy ?? state.pharmacy.todo;
            state.hospitalDispensingOrderCount.todo = todos.hospitalDispensingOrderCount ?? state.hospitalDispensingOrderCount.todo;
            state.consultation.todo = todos.consultation ?? state.consultation.todo;
            state.examination.todo = todos.examination ?? state.examination.todo;
            state.inspect.todo = todos.inspect ?? state.inspect.todo;
            state.approval.todo = todos.approval ?? state.approval.todo;
            state.gspCount.todo = todos.gsp ?? state.gspCount.todo;
            state.materialMaintenanceTodoCount.todo = todos.gspMaterialMaintenance ?? state.materialMaintenanceTodoCount.todo;
            state.gspDestroy.todo = todos.gspDestroy ?? state.gspDestroy.todo;
            state.stock.todo = todos.stockTodoCount ?? state.stock.todo;
            state.stockIn.todo = todos.stockInTodoCount ?? state.stockIn.todo;
            state.stockIn.pharmacyNoList = todos.stockInTodoPharmacyNoList ?? state.stockIn.pharmacyNoList;

            state.stockOut.todo = todos.stockLossOutTodoCount ?? state.stockOut.todo;
            state.stockOut.pharmacyNoList = todos.stockLossOutTodoPharmacyNoList ?? state.stockOut.pharmacyNoList;

            state.stockApply.todo = todos.stockReceptTodoCount ?? state.stockApply.todo;
            state.stockApply.pharmacyNoList = todos.stockReceptTodoPharmacyNoList ?? state.stockApply.pharmacyNoList;

            state.stockTrans.todo = todos.stockTransTodoCount ?? state.stockTrans.todo;
            state.stockTrans.pharmacyNoList = todos.stockTransTodoPharmacyNoList ?? state.stockTrans.pharmacyNoList;

            state.expiredWarn.todo = todos.expiredWarnCount ?? state.expiredWarn.todo;
            state.expiredWarn.pharmacyNoList = todos.expiredWarnPharmacyNoList ?? state.expiredWarn.pharmacyNoList;

            state.profitRatWarn.todo = todos.profitRatWarnCount ?? state.profitRatWarn.todo;
            state.profitRatWarn.pharmacyNoList = todos.profitRatWarnPharmacyNoList ?? state.profitRatWarn.pharmacyNoList;

            state.stockWarn.todo = todos.stockWarnShortageCount ?? state.stockWarn.todo;// 周转天数预警+库存不足预警
            state.stockWarn.pharmacyNoList = todos.stockWarnShortagePharmacyNoList ?? state.stockWarn.pharmacyNoList;// 周转天数预警+库存不足预警

            state.stockSocialGoodsWarn.todo = todos.stockSocialGoodsWarn ?? state.stockSocialGoodsWarn.todo;
            state.supplierValidCount.todo = todos.supplierValidCount ?? state.supplierValidCount.todo;
            state.stockSocialDiagnosisTreatmentWarn.todo = todos.stockSocialDiagnosisTreatmentWarn ?? state.stockSocialDiagnosisTreatmentWarn.todo;
            state.implNationalWarningCount.todo = todos.implNationalWarningCount ?? state.implNationalWarningCount.todo;


            state.social.todo = todos.shebaoTodoCount ?? state.social.todo;


            state.medicalPlanFollowup.todo = todos.oralPatientTodoCount ?? state.medicalPlanFollowup.todo;

            state.coCashier.todo = todos.coCashier ?? state.coCashier.todo;

            state.cisMallWaitSend.todo = todos.cisMallWaitSend ?? state.cisMallWaitSend.todo;

            state.hospitalSettlingWaitChargeCount.todo = todos.hospitalSettlingWaitChargeCount ?? state.hospitalSettlingWaitChargeCount.todo;

            state.orderCloud.todo = todos.orderCloud ?? state.orderCloud.todo;
            state.setting.unUploadOrganCertCount = todos.organCerts ?? state.setting.unUploadOrganCertCount;
            state.setting.aggregatePayment = todos.aggregatePayment ?? state.setting.aggregatePayment;
            state.setting.todo = countSettingTodo(state, rootState, rootGetters);
        },

        SET_APP_VERSION_CLICKED(state, version) {
            state.appVersionClicked = version;
            localStorage.setItem(APP_VERSION_CLICKED, version);
        },
    },
    actions: {
        /**
         * @desc 获取待审核列表
         * @deprecated
         * @param commit
         * @param rootState
         * @param rootGetters
         */
        async fetchUnReviewList({
            commit,
            rootState,
            rootGetters,
        }) {
            try {
                const {
                    status, data,
                } = await SettingsApi.employee.fetchUndoList();

                if (status.code === 200) {
                    commit('SET_UNDOREVIEW_LIST', {
                        list: data,
                        rootState,
                        rootGetters,
                    });
                }
            } catch (e) {
                console.log('fetchUnReviewList e', e);
            }
        },

        /**
         * @desc 获取待办项，V3版API，收费和检查检验的迁移到该接口，会逐步替换V2版接口
         * @param commit
         * @param rootState
         * @param rootGetters
         * @return {Promise<void>}
         */
        async fetchTodosV3({
            commit,
            rootState,
            rootGetters,
        }) {
            try {
                const { data } = await DashboardAPI.fetchTodosV3();
                commit('SET_TODOS', {
                    todos: data,
                    rootState,
                    rootGetters,
                });
            } catch (e) {
                console.log('fetchTodosV3 e', e);
            }
        },

        async setAppVersionClicked({ commit }, version) {
            commit('SET_APP_VERSION_CLICKED', version);
        },

        /**
         * 更新 todo
         * @param commit
         * @param rootState,
         * @param partialTodos
         */
        updateTodos({
            commit, rootState,rootGetters,
        }, partialTodos) {
            console.log('%c updateTodos\n', 'background: green; padding: 0 5px', JSON.parse(JSON.stringify(partialTodos)));
            commit('SET_TODOS', {
                todos: partialTodos,
                rootState,
                rootGetters,
            });
        },
    },
};
export default dashboard;
