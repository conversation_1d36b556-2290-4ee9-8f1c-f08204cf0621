<template>
    <div>
        <abc-dropdown @change="onPrint">
            <abc-button
                slot="reference"
                type="blank"
            >
                打印
            </abc-button>
            <abc-dropdown-item
                label="收费项目清单"
                :value="printOptions.CHARGE_LIST.value"
            >
            </abc-dropdown-item>
            <abc-dropdown-item
                disabled
                label=""
                value=""
                style="width: 100%; height: 0; min-height: 0 !important; padding: 0 !important; cursor: default; border-top: 1px solid #e6eaee;"
            ></abc-dropdown-item>
            <abc-dropdown-item
                label="设置..."
                value="printSetting"
            ></abc-dropdown-item>
        </abc-dropdown>

        <cost-print-date-dialog
            v-if="showDialog"
            v-model="showDialog"
            @confirm="onConfirm"
        ></cost-print-date-dialog>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import AbcPrinter from '@/printer';
    import { getAbcPrintOptions } from '@/printer/print-handler';

    import CostPrintDateDialog from './cost-print-date-dialog';
    import PrintAPI from 'api/hospital/print';
    import { PrintMode } from '@/printer/constants';
    import Qs from 'qs';
    import { getOrigin } from 'views/settings/micro-clinic/decoration/config';
    import ShortUrlAPI from 'api/short-url';
    import QRCode from 'qrcode';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');

    export default {
        name: 'CostPrint',
        components: {
            CostPrintDateDialog,
        },
        props: {
            patientOrderId: {
                type: String,
                required: true,
            },
        },
        data() {
            return {
                showDialog: false,
                printOptions: {},
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'printHospitalFeeBillsConfig',
            ]),
            ...mapGetters('viewDistribute',[
                'viewDistributeConfig',
            ]),
        },
        created() {
            this.printOptions = this.viewDistributeConfig.Print.printOptions;
        },
        methods: {
            async onPrint(val) {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                switch (val) {
                    case this.printOptions.CHARGE_LIST.value:
                        this.showDialog = true;
                        break;
                    case 'printSetting':
                        new PrintConfigDialog({ scene: 'hospital-nurse-cost-check' }).generateDialogAsync({ parent: this });
                        break;
                    default:
                        break;
                }

            },
            onConfirm(val) {
                try {
                    const beginDate = val[0];
                    const endDate = val[1];
                    AbcPrinter.abcPrint((async () => {
                        const printPropsList = [];
                        const printData = await this.fetchPrintData({
                            beginDate,
                            endDate,
                            patientOrderIds: [this.patientOrderId],
                        });
                        const printOptions = getAbcPrintOptions(this.printOptions.CHARGE_LIST.label, printData);
                        if (printOptions) {
                            printPropsList.push({
                                ...printOptions,
                                data: printOptions.data ?? {},
                                mode: PrintMode.Electron,
                            });
                        }
                        return printPropsList;
                    }));
                } catch (e) {
                    console.error(e);
                }
            },

            async fetchPrintData(printParams) {
                const printLoading = this.$Loading({
                    text: '准备打印...',
                    customClass: 'print-loading-wrapper',
                });
                try {
                    const { data } = await PrintAPI.getFeeListBatch(printParams);
                    const { feeDetailPrintInfoList } = data;

                    const { chargeFeeList } = this.printHospitalFeeBillsConfig;
                    const { traceCode } = chargeFeeList ?? {};
                    if (traceCode) {
                        // 构造追溯码二维码
                        try {
                            const {
                                chainId, chain, id: clinicInnerId, clinicId,
                            } = this.currentClinic || {};
                            const { id: chainInnerId } = chain || {};
                            const patientOrderId = feeDetailPrintInfoList[0].patientOrderInfo.id;
                            // 获取追溯码二维码
                            const queryParams = Qs.stringify({
                                chainId: chainInnerId || chainId || '',
                                clinicId: clinicInnerId || clinicId || '',
                                chargeSheetId: patientOrderId || '',
                                type: 1,
                            });
                            const fullUrl = `${getOrigin()}/mp/trace-code?${queryParams}`;
                            const { data: shortUrlData } = await ShortUrlAPI.createShortUrl({
                                fullUrl,
                            });
                            const traceCodeQrLink = shortUrlData.shortUrl;
                            feeDetailPrintInfoList[0].traceCodeQrCodeUrl = await QRCode.toDataURL(traceCodeQrLink, { margin: 0 });
                        } catch (e) {
                            console.error('构造追溯码二维码失败\n', e);
                        }
                    }

                    return {
                        clinicName: this.currentClinic.shortName || this.currentClinic.clinicName,
                        nationalCode: this.currentClinic.nationalCode,
                        inHospital: true,
                        ...feeDetailPrintInfoList[0],
                    };
                } catch (e) {
                    console.error('获取打印数据失败\n', e);
                } finally {
                    printLoading.close();
                }
            },
        },
    };
</script>

