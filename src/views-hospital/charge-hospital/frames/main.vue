<template>
    <div v-abc-loading:page="contentLoading">
        <abc-container-center-top-head>
            <h2>住院收费</h2>
            <div class="buttons-wrapper">
                <div class="price-info">
                    <abc-money
                        :value-style="{ fontSize: '20px' }"
                        :symbol-icon-size="16"
                        :value="displayChargedFee"
                        class="price"
                        is-show-space
                    ></abc-money>
                </div>

                <abc-button v-if="canSettle" @click="handleClickSettle">
                    {{ isPartSettle ? '继续结算' : '出院结算' }}
                </abc-button>

                <abc-button v-if="canWithdrawal" type="danger" @click="handleClickCancel">
                    撤销结算
                </abc-button>

                <abc-button
                    v-if="showOpenInvoiceBtn"
                    type="blank"
                    @click="openInvoiceDialog"
                >
                    开票
                </abc-button>

                <abc-button v-if="canRenew" type="blank" @click="handleClickRenew">
                    重新结算
                </abc-button>

                <deposit-group
                    ref="depositGroup"
                    :patient-order-id="patientOrderId"
                    :his-charge-deposit="hisChargeDeposit"
                    :pay-transactions="hisChargeDeposit.payTransactions"
                    :show-charge-deposit-btn="canChargeDeposit"
                    :show-refund-deposit-btn="canChargeDeposit || canRenew || canWithdrawal"
                    @refresh="fetchDetail"
                ></deposit-group>

                <abc-button v-if="isShowSocialInHospitalStatus" type="blank" @click="handlerOutHospital">
                    医保出院
                </abc-button>

                <abc-button type="blank" @click="showChargeDetailDialog = true">
                    费用详情
                </abc-button>

                <print-popper
                    placement="bottom"
                    :box-style="{ width: '92px' }"
                    :options="printOptions"
                    style="margin-left: 8px;"
                    @print="printHandler"
                    @select-print-setting="openPrintConfigSettingDialog"
                >
                </print-popper>
            </div>

            <template #bottom-extend>
                <hospital-patient-info-container
                    :key="patientInfoRefreshKey"
                    ref="patientHospitalInfo"
                    class="hospital-form-patient-info"
                    :visible-change-fee-type-name="false"
                    :patient-order-id="patientOrderId"
                    @init-patient-info="initPatientInfo"
                    @change-patient-info="changePatientInfo"
                    @init-patient-hospital-info="initPatientHospitalInfo"
                ></hospital-patient-info-container>
            </template>
        </abc-container-center-top-head>

        <abc-container-center-main-content>
            <charge-fee-type-name-selector
                :fee-type-name="feeTypeName"
                :shebao-card-info="shebaoCardInfo"
                :enable-fee-type-change="canChangeFeeType"
                :show-extra-info="showFeeExtraInfo"
                :charged-total-price="chargedTotalPrice"
                :receivable-fee="receivableFee"
                :deposit-balance="depositAvailableFee"
                @change="handleFeeTypeNameChange"
            ></charge-fee-type-name-selector>
            <hospital-charge-sheet-pro-table
                :data-list="chargeSheets"
                :price-scope-type="priceScopeType"
            ></hospital-charge-sheet-pro-table>
        </abc-container-center-main-content>

        <charge-detail-dialog
            v-if="showChargeDetailDialog"
            v-model="showChargeDetailDialog"
            :charge-summary="chargeSummary"
            :shebao-card-info="shebaoCardInfo"
            :price-scope-type="priceScopeType"
            :price-settle-type="priceSettleType"
            :group-by-goods-type-form-items="groupByGoodsTypeFormItems"
        ></charge-detail-dialog>

        <sidebar
            :settle-status="settleStatus"
            :pay-status="payStatus"
            :patient-order="patientOrder"
            :charge-summary="chargeSummary"
            :his-charge-deposit="hisChargeDeposit"
            :charge-settle-transactions="hisChargeSettle.chargeSettleTransactions"
            :his-charge-settle="hisChargeSettle"
            :charge-deposit-transactions="hisChargeDeposit.chargeDepositTransactions"
            :invoice-list="invoiceList"
            :charged-total-price="chargedTotalPrice"
        ></sidebar>

        <withdrawal-settle-dialog
            v-if="showWithdrawalDialog"
            v-model="showWithdrawalDialog"
            :patient-order-id="patientOrderId"
            :pay-transactions.sync="hisChargeSettle.payTransactions"
            :can-refund-fee="+chargeSummary.canRefundFee"
            :current-patient="currentPatient"
            @refresh="handleRefundRefresh"
        ></withdrawal-settle-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import {
        mapActions, mapGetters,
    } from 'vuex';

    import HospitalChargeAPI from 'api/hospital/charge.js';
    import PayService from '@/service/charge/pay.js';

    import HospitalPatientInfoContainer from 'views/layout/patient/hospital-patient-info-container.vue';
    import Sidebar from '../components/sidebar.vue';
    import AbcHospitalSettleDialog from '@/views-hospital/charge-hospital/components/dialog-hospital-settle/index.js';
    import ChargeDetailDialog from '@/views-hospital/charge-hospital/components/charge-detail-dialog.vue';
    import WithdrawalSettleDialog from '@/views-hospital/charge-hospital/components/withdrawal-settle-dialog.vue';
    import DepositGroup from '@/views-hospital/charge-hospital/components/deposit-group.vue';
    import AbcCommonChargeDialog from '@/components/common-charge-dialog/index.js';

    import {
        HospitalStatusEnum, socialInHospitalStatusEnum,
    } from '@/views-hospital/register/utils/constants.js';
    import {
        PayModeEnum, PreCheckBeforeDischargeEnum,
    } from '@/service/charge/constants.js';
    import {
        HospitalChargeStatusEnum, SettleStatusEnum,
    } from '@/views-hospital/charge-hospital/utils/index.js';
    import {
        navigateToAggregatePaymentContentSetting , navigateToInvoiceConfig,
    } from '@/core/navigate-helper.js';
    import PrintPopper from 'views/print/popper.vue';
    import PrintAPI from 'api/hospital/print/index.js';
    import AbcPrinter from '@/printer/index.js';
    import { getAbcPrintOptions } from '@/printer/print-handler';
    import SocialApi from 'api/social';
    import { PrintMode } from '@/printer/constants';
    import InvoiceDialog from 'views/cashier/invoice/index.js';
    import {
        InvoiceBusinessScene, InvoiceCategory, InvoiceSupplierId, InvoiceViewType,
    } from 'views/cashier/invoice/constants.js';
    import InvoiceService from 'views/cashier/invoice/write-invoice-core-v2/invoice-service';
    import { autoDestroyInvoice } from 'views/cashier/invoice/utils';
    import Printer from 'views/print';
    import { tryFetchPrintData } from '@/printer/utils';
    import Logger from 'utils/logger';
    import {
        createGUID, isNull,
    } from '@/utils';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');
    import TpsAPI from 'api/tps';
    import ChargeFeeTypeNameSelector from '../components/charge-fee-type-name-selector.vue';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import {
        HOSPITAL_PRICE_SCOPE_TYPE, HOSPITAL_PRICE_SETTLE_TYPE,
    } from 'utils/constants';
    import { ChargeDialogFeeTypeEnum } from '@/views-hospital/charge-hospital/utils/constants';
    import HospitalChargeSheetProTable from 'views/layout/tables/table-hospital-charge-sheet/index.vue';
    import QRCode from 'qrcode';
    import { getOrigin } from 'views/settings/micro-clinic/decoration/config';
    import ShortUrlAPI from 'api/short-url';
    import Qs from 'qs';

    export default {
        name: 'HospitalChargeHospitalMain',
        components: {
            HospitalChargeSheetProTable,
            PrintPopper,
            Sidebar,
            HospitalPatientInfoContainer,
            ChargeDetailDialog,
            DepositGroup,
            WithdrawalSettleDialog,
            ChargeFeeTypeNameSelector,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                HospitalStatusEnum,
                settleStatus: SettleStatusEnum.SETTLED,
                payStatus: HospitalChargeStatusEnum.UNCHARGED,
                feeTypeName: '',
                patientOrderId: null,
                shebaoCardInfo: null, // 患者社保卡信息
                patientOrder: {},
                chargeSummary: {},
                groupByGoodsFormItems: [],
                groupByGoodsTypeFormItems: [],
                chargeSettleTransactions: [], // 结算流水
                chargeDepositTransactions: [], // 押金流水
                socialPayInfo: {},

                hisChargeDeposit: {},
                hisChargeSettle: {},

                // 医保住院登记状态
                inHospitalStatus: socialInHospitalStatusEnum.NOT_ADMISSION,

                patientInfoRefreshKey: false,
                contentLoading: false,
                showChargeDetailDialog: false,

                showWithdrawalDialog: false,

                invoiceList: [],

                priceScopeType: HOSPITAL_PRICE_SCOPE_TYPE.SOURCE,
                priceSettleType: HOSPITAL_PRICE_SETTLE_TYPE.SOURCE,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'printHospitalBillConfig',
                'printMedicalListConfig',
                'clinicBasicConfig',
                'isOpenMp',
                'userInfo',
                'printHospitalFeeBillsConfig',
            ]),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            ...mapGetters('invoice', ['isOpenInvoice', 'isOpenMedicalInvoice', 'medicalElectronicAPIConfig', 'writeInvoiceConfig', 'invoiceConfigList']),
            // 能否结算
            canSettle() {
                return this.settleStatus === SettleStatusEnum.SETTLING;
            },

            // 是部分结算
            isPartSettle() {
                return this.settleStatus === SettleStatusEnum.SETTLING &&
                    this.payStatus === HospitalChargeStatusEnum.PART_CHARGED;
            },

            // 能否撤销结算
            canWithdrawal() {
                return this.settleStatus === SettleStatusEnum.SETTLED &&
                    [
                        HospitalChargeStatusEnum.CHARGED,
                        HospitalChargeStatusEnum.PART_REFUND,
                    ].indexOf(this.payStatus) > -1;
            },

            // 能否重新结算
            canRenew() {
                return this.settleStatus === SettleStatusEnum.SETTLED &&
                    this.payStatus === HospitalChargeStatusEnum.REFUNDED;
            },
            /**
             * @desc 显示开票按钮
             * <AUTHOR>
             * @date 2023-12-18 18:08:17
             */
            showOpenInvoiceBtn() {
                return [
                    HospitalChargeStatusEnum.REFUNDED,
                    HospitalChargeStatusEnum.PART_REFUND,
                    HospitalChargeStatusEnum.CHARGED,
                ].indexOf(this.payStatus) > -1;
            },

            // 能否交押金
            canChargeDeposit() {
                return this.settleStatus === SettleStatusEnum.WAIT_SETTLE;
            },

            // 能否修改费别
            canChangeFeeType() {
                return this.payStatus === HospitalChargeStatusEnum.UNCHARGED;
            },

            showFeeExtraInfo() {
                return [HospitalChargeStatusEnum.UNCHARGED, HospitalChargeStatusEnum.PART_CHARGED].includes(this.payStatus);
            },

            // 是否展示医保出院登记按钮
            isShowSocialInHospitalStatus() {
                return this.inHospitalStatus === socialInHospitalStatusEnum.ADMISSION &&
                    (this.payStatus === HospitalChargeStatusEnum.PART_CHARGED || this.payStatus === HospitalChargeStatusEnum.CHARGED);
            },
            currentQuickItem() {
                return this.$abcPage.$store.selectedQuickItem;
            },

            currentPatient() {
                return this.currentQuickItem.patient || {};
            },

            // 完成结算前展示待结算金额
            // 完成结算后展示实收金额
            displayChargedFee() {
                if (this.payStatus < HospitalChargeStatusEnum.CHARGED) {
                    return this.receivableFee;
                }
                return this.chargeSummary.receivedFee;
            },

            // 计费金额
            chargedTotalPrice() {
                const {
                    chargedTotalPrice,
                    shebaoDisplayChargedTotalPrice,
                    settleChargedTotalPrice,
                } = this.chargeSummary;
                return {
                    [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: chargedTotalPrice,
                    [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: shebaoDisplayChargedTotalPrice,
                    [HOSPITAL_PRICE_SCOPE_TYPE.SETTLE]: settleChargedTotalPrice,
                }[this.priceScopeType];
            },

            receivableFee() {
                const {
                    receivableFee,
                    shebaoDisplayReceivableFee,
                } = this.chargeSummary;

                if (this.priceScopeType === HOSPITAL_PRICE_SCOPE_TYPE.SETTLE) {
                    return this.priceSettleType === HOSPITAL_PRICE_SETTLE_TYPE.SHEBAO ?
                        shebaoDisplayReceivableFee :
                        receivableFee;
                }

                return {
                    [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: receivableFee,
                    [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: shebaoDisplayReceivableFee,
                }[this.priceScopeType];
            },

            depositAvailableFee() {
                const {
                    availableFee,
                } = this.hisChargeDeposit;
                return availableFee || 0;
            },

            chargeSheets() {
                return [
                    {
                        chargeForms: [
                            {
                                chargeFormItems: this.groupByGoodsFormItems,
                            },
                        ],
                    },
                ];
            },
            socialPrintType() {
                const isWillCharge = this.payStatus < HospitalChargeStatusEnum.CHARGED; // 是否待收费
                const isSocialPay = this.socialPayInfo.thirdPartyPayTransactionId; // 是否社保支付
                const getSocialPrintTypeParams = {
                    isWillCharge,
                    isSocialPay,
                };
                // 获取社保打印类型
                return this.$abcSocialSecurity.getSocialPrintType(getSocialPrintTypeParams);
            },
            printOptions() {
                return [
                    {
                        value: this._printOptions.HOSPITAL_DEPOSIT_RECEIPT.label,
                        disabled: !this.hisChargeDeposit.chargeDepositTransactions?.length || !this.canChargeDeposit,
                    },
                    ...this.socialPrintType,
                    {
                        value: this._printOptions.CHARGE_LIST.label,
                        disabled: this.payStatus >= HospitalChargeStatusEnum.REFUNDED,
                    },
                    {
                        value: '收费小票',
                        disabled: this.payStatus !== HospitalChargeStatusEnum.CHARGED,
                    },
                ];
            },
        },
        watch: {
            '$route.params.id': {
                handler() {
                    this.fetchDetail();
                },
                immediate: true,
            },
        },

        created() {
            this._printOptions = this.viewDistributeConfig.Print.printOptions;
            this.$store.dispatch('invoice/initInvoiceConfig');
            this.fetchPrintAllConfigIfNeed();
            this.invoiceService = new InvoiceService();

            // 监听收费后自动开票的消息,如果开票成功,则重新拉取发票列表
            this.$abcEventBus.$on('refresh-hospital-invoice-list', () => {
                this.fetchInvoiceList();
            }, this);

        },

        methods: {
            ...mapActions(['fetchPrintAllConfigIfNeed']),
            async handleRefundRefresh(result) {
                await this.fetchDetail();

                if (result) {
                    const { requestId } = result;
                    autoDestroyInvoice(requestId, InvoiceBusinessScene.HIS_HOSPITAL);
                }
            },
            async fetchDetail(isPrintDeposit = false) {
                this.contentLoading = true;
                this.patientOrderId = this.$route.params.id;
                const { data } = await HospitalChargeAPI.fetchDetail(this.patientOrderId);
                this.settleStatus = data.settleStatus;
                this.payStatus = data.payStatus;
                this.patientOrder = data.patientOrder || {};
                this.chargeSummary = data.summary || {};
                this.groupByGoodsFormItems = data.groupByGoodsFormItems || [];
                this.groupByGoodsTypeFormItems = data.groupByGoodsTypeFormItems || [];
                this.hisChargeDeposit = data.hisChargeDeposit || {};
                this.hisChargeSettle = data.hisChargeSettle || {};
                this.chargeDepositTransactions = data.chargeDepositTransactions || [];
                this.socialPayInfo = (this.hisChargeSettle.payTransactions || []).find((item) => item.payMode === PayModeEnum.SOCIAL_CARD) || {};
                this.priceScopeType = data.priceScopeType || HOSPITAL_PRICE_SCOPE_TYPE.SOURCE;
                this.priceSettleType = data.settleType || HOSPITAL_PRICE_SETTLE_TYPE.SOURCE;

                // 收费单状态为已收费时, 判断是否需要同时打印
                this.$nextTick(() => {
                    /**
                     * --------------------- 日志上报 start ---------------------
                     * 目的: 方便排查问题
                     */
                    const patientOrder = data.patientOrder || {};
                    const patient = patientOrder.patient || {};
                    const patientOrderId = patientOrder.id || '';
                    const patientId = patient.id || '';
                    const patientName = patient.name || '';
                    const { payStatus } = data;

                    const { cache } = Printer;
                    // 需要同时打印的单据信息 & 此次是否同时打印
                    const {
                        hospitalCashier,
                        hospitalCashierNeedPrint,
                    } = cache.get();

                    const uuid = createGUID();
                    Logger.report({
                        scene: 'hospital_cashier_meanwhile_print',
                        data: {
                            uuid,
                            patientOrderId,
                            patientId,
                            patientName,
                            payStatus,
                            hospitalCashier,
                            hospitalCashierNeedPrint,
                        },
                    });
                    /**
                     * --------------------- 日志上报 end ---------------------
                     */

                    if (payStatus === HospitalChargeStatusEnum.CHARGED) {
                        if (hospitalCashierNeedPrint) {
                            this.printHandler(hospitalCashier, uuid);
                            cache.set({
                                hospitalCashierNeedPrint: false,
                            });
                        }
                    }

                    // 同时打印押金收据
                    if (isPrintDeposit && (hospitalCashier || []).includes(this._printOptions.HOSPITAL_DEPOSIT_RECEIPT.label)) {
                        this.printHandler([this._printOptions.HOSPITAL_DEPOSIT_RECEIPT.label], uuid);
                    }
                });

                // 查询医保住院登记状态
                try {
                    const { data: resp } = await SocialApi.fetchSocialInHospitalStatus(this.patientOrderId);
                    this.inHospitalStatus = resp?.status;
                } catch (e) {
                    console.error(e);
                }
                // 支付方式有医保卡时，获取医保结算信息
                if (this.hisChargeSettle.payModeViews?.some((x) => x.payMode === PayModeEnum.SOCIAL_CARD)) {
                    this.getSocialSettleInfo();
                }

                await this.fetchInvoiceList();
                this.contentLoading = false;
            },
            /**
             * 拉取发票列表
             */
            async fetchInvoiceList() {
                if (this.payStatus > HospitalChargeStatusEnum.UNCHARGED) {
                    try {
                        const invoiceListResp = await this.invoiceService.fetchCashierSideBarInvoiceList(this.patientOrderId,
                                                                                                         InvoiceBusinessScene.HIS_HOSPITAL,
                                                                                                         InvoiceCategory.MEDICAL_ELECTRONIC,
                                                                                                         0);
                        this.invoiceList = invoiceListResp.rows || [];
                    } catch (e) {
                        this.invoiceList = [];
                        console.warn('获取发票列表失败\n', e);
                    }
                } else {
                    this.invoiceList = [];
                }
            },

            /**
             * 结算前校验
             * @param {string} patientOrderId
             * @return {Promise<boolean>}
             */
            async preCheckBeforeSettle(patientOrderId) {
                const { data = {} } = await HospitalChargeAPI.preCheckBeforeDischarge(patientOrderId);
                const { status } = data;
                let message = '';
                if (PreCheckBeforeDischargeEnum.CHECKING === status) {
                    // 正在校验中
                    message = '正在校验患者收费信息，这个过程可能需要几分钟，请稍候尝试。';
                }
                if (isNull(status) || [PreCheckBeforeDischargeEnum.ITEM_BILL_CHECK_FAIL, PreCheckBeforeDischargeEnum.PATIENT_ORDER_CHECK_FAIL].includes(status)) {
                    // 校验未通过
                    message = '部分收费项目异常，请联系ABC工程师处理。';
                }
                if (message) {
                    this.$alert({
                        type: 'warn',
                        title: '结算提示',
                        content: message,
                    });
                    return false;
                }
                return true;
            },

            async handleClickSettle() {
                const isPreCheckSuccess = await this.preCheckBeforeSettle(this.patientOrderId);
                if (!isPreCheckSuccess) return;

                let defaultFeeType = ChargeDialogFeeTypeEnum.SOURCE;
                if (this.priceScopeType !== HOSPITAL_PRICE_SCOPE_TYPE.SETTLE) {
                    defaultFeeType = this.priceScopeType === HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO ?
                        ChargeDialogFeeTypeEnum.SHEBAO :
                        ChargeDialogFeeTypeEnum.SOURCE;
                } else {
                    // defaultFeeType = this.priceSettleType === HOSPITAL_PRICE_SETTLE_TYPE.SHEBAO ?
                    //     ChargeDialogFeeTypeEnum.SHEBAO :
                    //     ChargeDialogFeeTypeEnum.SOURCE;
                    defaultFeeType = ChargeDialogFeeTypeEnum.SOURCE;
                }
                this._settleDialog = new AbcHospitalSettleDialog({
                    patientOrder: this.patientOrder,
                    settleStatus: this.settleStatus,
                    payStatus: this.payStatus,
                    chargeSummary: this.chargeSummary,
                    hisChargeDeposit: this.hisChargeDeposit,
                    onConfirm: this.chargeHandler,
                    defaultFeeType,
                });
                this._settleDialog.generateDialog({ parent: this });
            },
            chargeHandler(data, callback) {
                autoDestroyInvoice(this.patientOrderId, InvoiceBusinessScene.HIS_HOSPITAL);

                // 医保结算流程
                if (data.feeType === ChargeDialogFeeTypeEnum.SHEBAO) {
                    this.socialCardPayHandler(callback);
                    return;
                }

                const depositAmount = data.feeType === ChargeDialogFeeTypeEnum.SHEBAO ?
                    this.chargeSummary.shebaoDepositReceivableFee :
                    this.chargeSummary.depositReceivableFee;

                // 自费结算流程
                // 有押金需要优先押金结算
                if (depositAmount) {
                    this.depositPayHandler(callback, data, depositAmount);
                    return;
                }

                // 可以结算
                if (this.canSettle) {
                    this.selfPayHandler(callback, data);
                    return;
                }

                // 退
                // 押金有余额
                if (this.hisChargeDeposit.availableFee) {
                    this.$refs.depositGroup.depositRefundPrice = this.hisChargeDeposit.availableFee;
                    this.$refs.depositGroup.confirmRefund(callback);
                    this.closeSettleDialog();
                }
            },

            /**
             * @desc 押金结算
             * <AUTHOR>
             * @date 2023-02-18 15:34:41
             */
            async depositPayHandler(callback, { feeType }, amount) {
                this.showMessage({
                    type: 'loading',
                    title: '正在使用押金结算...',
                });
                const receivableFee = feeType === ChargeDialogFeeTypeEnum.SHEBAO ?
                    this.chargeSummary.shebaoDisplayReceivableFee :
                    this.chargeSummary.receivableFee;
                const settleResult = await HospitalChargeAPI.pay(this.patientOrderId, {
                    amount,
                    receivableFee,
                    payMode: PayModeEnum.DEPOSIT,
                });
                const {
                    requestId,
                } = settleResult;
                const { data } = await PayService.pay(requestId, {
                    payMode: PayModeEnum.DEPOSIT,
                    paySubMode: 0,
                    thirdPartyPayCardId: this.patientOrderId,
                });
                const {
                    payStatus,
                    errorMessage,
                } = data;

                if (
                    payStatus === PayService.payStatusEnum.ORDER_SUCCESS_BUSINESS_FAIL ||
                    payStatus === PayService.payStatusEnum.ORDER_FAIL
                ) {
                    this.showMessage({
                        type: 'warn',
                        title: '押金结算失败',
                        content: [errorMessage || '支付失败'],
                        showClose: true,
                    });
                } else if (payStatus === PayService.payStatusEnum.ORDER_SUCCESS_BUSINESS_SUCCESS) {
                    this.showMessage({
                        type: 'success',
                        title: '押金结算成功',
                    });
                }
                if (this._settleDialog && payStatus === PayService.payStatusEnum.ORDER_SUCCESS_BUSINESS_SUCCESS) {
                    this._timer = setTimeout(async () => {
                        this.closeMessage();
                        this.closeSettleDialog();
                        // 还能结算 || 押金可退
                        if (this.canSettle || this.hisChargeDeposit.availableFee) {
                            await this.fetchDetail();
                            this.handleClickSettle();
                        } else {
                            typeof callback === 'function' && callback();
                            await this.fetchDetail();
                        }
                    }, 1000);
                } else {
                    await this.fetchDetail();
                }
            },

            showMessage(options, closeDelay = 0) {
                this.closeMessage();
                const defaultOption = {
                    customClass: 'charge-message-dialog',
                    referenceEl: document.querySelector('.abc-hospital-settle-dialog-wrapper .abc-dialog'),
                    dialogType: 'tiny',
                    type: '',
                    title: '',
                    content: [],
                    showFooter: false,
                    showClose: false,
                    showCancel: false,
                    showConfirm: false,
                    onConfirm: () => {
                    },
                    noDialogAnimation: true,
                    confirmText: '确定',
                    onCancel: () => {
                    },
                    cancelText: '取消',
                };
                this._messageInstance = this.$message(Object.assign(defaultOption, options));
                if (closeDelay) {
                    const _timer = setTimeout(() => {
                        this._messageInstance.close();
                        clearTimeout(_timer);
                    }, closeDelay);
                }
            },

            closeMessage() {
                if (this._messageInstance) {
                    this._messageInstance.close();
                    this._messageInstance = null;
                }
            },

            async socialCardPayHandler(callback) {
                this.showMessage({
                    type: 'loading',
                    title: '正在调起医保支付端...',
                });
                const amount = this.chargeSummary.shebaoReceivableFee;
                const receivableFee = this.chargeSummary.shebaoDisplayReceivableFee;
                let payServiceResponse = null;
                try {
                    const hisChargeResponse = await this.submitHandler({
                        amount,
                        receivableFee,
                        payMode: PayModeEnum.SOCIAL_CARD,
                    });
                    payServiceResponse = await PayService.pay(hisChargeResponse.requestId, {
                        payMode: PayModeEnum.SOCIAL_CARD,
                    });
                } catch (err) {
                    return this.showMessage({
                        type: 'warn',
                        title: '收费失败',
                        content: [err.message],
                        showClose: true,
                    });
                }
                const params = {
                    taskId: payServiceResponse.data.thirdPartyPayTaskId,
                    receivableFee: this.chargeSummary.receivableFee,
                    payMode: PayModeEnum.SOCIAL_CARD,
                };
                const tradeResponse = await this.$abcSocialSecurity.trade(params, (eventName) => {
                    if (eventName === 'ping-success') {
                        this.showMessage({
                            type: 'loading',
                            title: '等待刷卡结果',
                            content: ['请确保医保刷卡机可正常使用'],
                            showFooter: true,
                            showCancel: true,
                            onCancel: () => {
                            },
                        });
                    }
                });
                if (tradeResponse.status === false) {
                    if (tradeResponse.isUserCancel === true) {
                        // 用户主动取消
                        return this.closeMessage();
                    }
                    if (!tradeResponse.message) {
                        // 无报错原因
                        console.warn('无报错原因', tradeResponse);
                        return this.closeMessage();
                    }
                    return this.showMessage({
                        type: 'warn',
                        title: '收费失败',
                        content: [tradeResponse.message],
                        showFooter: true,
                        showConfirm: true,
                    });
                }
                this.showMessage({
                    type: 'loading',
                    title: '查询支付结果',
                    content: ['订单创建成功，等待社保支付成功'],
                });
                this.closeMessage();
                this.closeSettleDialog();
                // 还能结算 || 押金可退
                if (this.canSettle || this.hisChargeDeposit.availableFee) {
                    await this.fetchDetail();
                    this.handleClickSettle();
                } else {
                    typeof callback === 'function' && callback();
                    await this.fetchDetail();
                }
            },

            selfPayHandler(callback, { feeType }) {
                const receivableFee = feeType === ChargeDialogFeeTypeEnum.SHEBAO ? this.chargeSummary.shebaoReceivableFee : this.chargeSummary.receivableFee;
                this._chargeDialog = new AbcCommonChargeDialog({
                    dialogTitle: '出院结算',
                    hiddenPayModeList: [
                        PayModeEnum.SOCIAL_CARD,
                        PayModeEnum.ARREARS,
                        PayModeEnum.PATIENT_CARD,
                    ],
                    receivableFee,
                    currentPatient: this.currentPatient,
                    onAbcPayOpenCallback: () => {
                        navigateToAggregatePaymentContentSetting(this.currentClinic);
                    },
                    submit: this.submitHandler,
                    onPartChargeSuccess: this.chargePartSuccess,
                    onChargeSuccess: () => this.chargeSuccess(callback),
                    onChargeError: this.chargeError,
                });
                this._chargeDialog.generateDialog({ parent: this });
            },

            chargePartSuccess() {
                this.closeSettleDialog();
                this.fetchDetail();
            },
            chargeSuccess(callback) {
                this.closeSettleDialog();
                if (this._chargeDialog) {
                    this._chargeDialog.destroyDialog();
                    this._chargeDialog = null;
                }
                typeof callback === 'function' && callback();
                this.fetchDetail();
            },
            chargeError() {
                this.closeSettleDialog();
                if (this._chargeDialog) {
                    this._chargeDialog.destroyDialog();
                    this._chargeDialog = null;
                }
                this.fetchDetail();
            },

            closeSettleDialog() {
                if (this._settleDialog) {
                    this._settleDialog.destroyDialog();
                    this._settleDialog = null;
                }
            },

            async submitHandler(chargeData) {
                return HospitalChargeAPI.pay(this.patientOrderId, {
                    ...chargeData,
                });
            },

            handleClickCancel() {
                this.showWithdrawalDialog = true;
            },

            async handleClickRenew() {
                await HospitalChargeAPI.renewSettle(this.patientOrderId);
                await this.fetchDetail();
                this.patientInfoRefreshKey = true;
                this.$nextTick(() => {
                    this.patientInfoRefreshKey = false;
                });
            },

            async openInvoiceDialog() {
                await new InvoiceDialog({
                    chargeSheetId: this.patientOrderId,
                    chargeStatus: this.chargeStatus,
                    payStatus: this.payStatus,
                    patientInfo: {
                        patientId: this.currentPatient.id,
                        idCard: this.currentPatient.idCard,
                        buyerPhone: this.currentPatient.mobile,
                        buyerName: this.currentPatient.name,
                        disabledBuyerName: true,
                    },
                    printMedicalListConfig: this.printMedicalListConfig,
                    medicalElectronicAPIConfig: this.medicalElectronicAPIConfig,
                    writeInvoiceConfig: this.writeInvoiceConfig,
                    isOpenMedicalInvoice: this.isOpenMedicalInvoice,
                    printBillConfig: this.printHospitalBillConfig,
                    isOpenInvoice: this.isOpenInvoice,
                    invoiceConfigList: this.invoiceConfigList,
                    userInfo: this.userInfo,
                    businessType: InvoiceBusinessScene.HIS_HOSPITAL,
                    toBillPrintSetting: () => {
                        navigateToInvoiceConfig(this.currentClinic);
                    },
                    updateInvoice: () => {
                        // 发票更新,刷新QL列表
                        this.$abcEventBus.$emit('open-invoice-refresh-hospital-quick-list');
                        // 发票更新,重新拉取发票列表
                        this.fetchInvoiceList();
                    },
                }).generateDialog({ parent: this });
            },

            initPatientInfo(data) {
                this.shebaoCardInfo = data.shebaoCardInfo;
            },

            initPatientHospitalInfo(data) {
                this.feeTypeName = data.feeTypeName;
            },

            changePatientInfo(data) {
                this.$abcPage.$store.updatePatientInfo(data);
            },

            async fetchPrintData(printType, uuid) {
                const printLoading = this.$Loading({
                    text: '准备打印...',
                    customClass: 'print-loading-wrapper',
                });

                if (uuid) {
                    Logger.report({
                        scene: 'hospital_cashier_meanwhile_print',
                        data: {
                            uuid,
                            info: '准备请求打印数据',
                            params: printType,
                        },
                    });
                }

                try {
                    // 押金收据
                    if (printType === this._printOptions.HOSPITAL_DEPOSIT_RECEIPT.label) {
                        const { patientHospitalInfo } = this.$refs;

                        if (uuid) {
                            Logger.report({
                                scene: 'hospital_cashier_meanwhile_print',
                                data: {
                                    uuid,
                                    info: '准备请求押金收据的数据',
                                    params: patientHospitalInfo,
                                },
                            });
                        }

                        if (!patientHospitalInfo) return;
                        const postData = patientHospitalInfo.getPrintData?.() ?? {};
                        // const { data } = await PrintAPI.getDepositInfo(this.patientOrderId);
                        const { data } = await tryFetchPrintData(PrintAPI.getDepositInfo)(this.patientOrderId);
                        if (data) {
                            data.qrCode = await this.fetchQrCode();
                        }

                        if (uuid) {
                            Logger.report({
                                scene: 'hospital_cashier_meanwhile_print',
                                data: {
                                    uuid,
                                    info: '押金收据的数据请求完成',
                                    res: data,
                                },
                            });
                        }

                        return {
                            data: {
                                clinicName: this.currentClinic.clinicName,
                                logo: this.clinicBasicConfig.logo,
                                ...postData,
                                ...data,
                            },
                        };
                    }
                    // 医保结算单
                    if (printType === this._printOptions.SOCIAL_SETTLEMENT_SHEET.label) {
                        const query = {
                            taskId: this.socialPayInfo.thirdPartyPayTransactionId,
                            isHospitalSettle: true,
                        };
                        // const printResponse = await this.$abcSocialSecurity.settlementSheetPrint(query);
                        // 医保结算单的方法有 this 指向问题
                        // const printResponse = await tryFetchPrintData(this.$abcSocialSecurity.settlementSheetPrint)(query);

                        let count = 0;
                        while (count < 5) {
                            const printResponse = await this.$abcSocialSecurity.settlementSheetPrint(query);

                            if (printResponse.status === false) {
                                console.error(printResponse.message || '调用结算单打印出错');
                                count++;
                                continue;
                            }

                            if (uuid) {
                                Logger.report({
                                    scene: 'hospital_cashier_meanwhile_print',
                                    data: {
                                        uuid,
                                        info: '医保结算单的数据请求完成',
                                        res: printResponse,
                                    },
                                });
                            }

                            const { html } = printResponse.data || {};
                            if (html) {
                                return {
                                    data: {},
                                    extra: {
                                        // 移除医保传递的外部div
                                        // 避免分页的问题
                                        getHTML: () => html.replace('<div class="print-stat-wrapper">', '').replace(/<\/div>$/, ''),
                                    },
                                };
                            }
                            count++;
                        }
                    }
                    // 医保告知单
                    if (printType === this._printOptions.SOCIAL_INFORM_SHEET.label) {
                        const query = {
                            taskId: this.socialPayInfo.thirdPartyPayTransactionId,
                            isHospitalSettle: true,
                            djlx: 'GZD',
                        };
                        const printResponse = await this.$abcSocialSecurity.settlementSheetPrint(query);
                        if (printResponse.status === false) {
                            throw new Error(printResponse.message || '调用告知单打印出错');
                        }
                    }
                    if (printType === this._printOptions.CHARGE_LIST.label || printType === '收费小票') {
                        // const { data } = await PrintAPI.getFeeList(this.patientOrderId);
                        const { data } = await tryFetchPrintData(PrintAPI.getFeeList)(this.patientOrderId);

                        if (data && Object.keys(data).length) {
                            // 微医院二维码
                            data.qrCode = await this.fetchQrCode();
                        }

                        if (printType === '收费小票') {
                            const { invoiceCode } = this.printHospitalFeeBillsConfig.hospitalCashier;

                            const {
                                invoiceCategory, // 发票类型
                                invoiceSupplierId, // 开票供应商
                                type,
                                digitalInvoice,
                            } = data?.invoiceView || {};
                            const invoiceImageUrl = digitalInvoice?.invoiceImageUrl; // 发票url
                            const clinicId = this.currentClinic?.clinicId || data.organ.id;
                            // 构造电子发票链接二维码
                            if (invoiceCode && invoiceCategory !== InvoiceCategory.PAPER) {
                                if (
                                    invoiceImageUrl &&
                                    type !== InvoiceViewType.RED &&
                                    invoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC &&
                                    invoiceSupplierId === InvoiceSupplierId.FUJIAN_BOSI
                                ) {
                                    data.invoiceQrcode = await QRCode.toDataURL(invoiceImageUrl, { margin: 0 });
                                } else {
                                    const path = invoiceImageUrl && type !== InvoiceViewType.RED ? 'invoice-preview' : 'view-invoice';
                                    const fullUrl =
                                        `${getOrigin()}/mp/${path}?clinicId=${clinicId}&businessScene=${InvoiceBusinessScene.HIS_HOSPITAL}&businessId=${this.patientOrderId}`;

                                    const { data: shortUrlData } = await ShortUrlAPI.createShortUrl({
                                        fullUrl,
                                    });
                                    const QrcodeStr = shortUrlData.shortUrl;
                                    data.invoiceQrcode = await QRCode.toDataURL(QrcodeStr, { margin: 0 });
                                }
                            }
                        }

                        const {
                            hospitalCashier, chargeFeeList,
                        } = this.printHospitalFeeBillsConfig;
                        const { clinicInfo } = hospitalCashier ?? {};
                        const { traceCodeQrCode } = clinicInfo ?? {};
                        const { traceCode } = chargeFeeList ?? {};
                        if (
                            (printType === '收费小票' && traceCodeQrCode) ||
                            (printType === this._printOptions.CHARGE_LIST.label && traceCode)
                        ) {
                            // 构造追溯码二维码
                            try {
                                // 获取追溯码二维码
                                const {
                                    chainId, chain, id: clinicInnerId, clinicId,
                                } = this.currentClinic || {};
                                const { id: chainInnerId } = chain || {};
                                const queryParams = Qs.stringify({
                                    chainId: chainInnerId || chainId || '',
                                    clinicId: clinicInnerId || clinicId || data.organ.id || '',
                                    chargeSheetId: this.patientOrderId || '',
                                    type: 1,
                                });
                                const fullUrl = `${getOrigin()}/mp/trace-code?${queryParams}`;
                                const { data: shortUrlData } = await ShortUrlAPI.createShortUrl({
                                    fullUrl,
                                });
                                const traceCodeQrLink = shortUrlData.shortUrl;
                                data.traceCodeQrCodeUrl = await QRCode.toDataURL(traceCodeQrLink, { margin: 0 });
                            } catch (e) {
                                console.error('构造追溯码二维码失败\n', e);
                            }
                        }

                        if (uuid) {
                            Logger.report({
                                scene: 'hospital_cashier_meanwhile_print',
                                data: {
                                    uuid,
                                    info: '住院费用清单的数据请求完成',
                                    res: data,
                                },
                            });
                        }

                        return {
                            data: {
                                clinicName: this.currentClinic.shortName || this.currentClinic.clinicName,
                                nationalCode: this.currentClinic.nationalCode,
                                clinicBasicConfig: this.clinicBasicConfig,
                                ...data,
                            },
                        };
                    }
                    return null;
                } catch (e) {
                    console.error('获取打印数据失败\n', e);

                    if (uuid) {
                        Logger.report({
                            scene: 'hospital_cashier_meanwhile_print',
                            data: {
                                uuid,
                                info: '请求打印数据失败',
                                res: e.message || '',
                            },
                        });
                    }
                } finally {
                    printLoading.close();
                }
            },

            async printHandler(selected, uuid) {
                AbcPrinter.abcPrint((async () => {
                    const printPropsList = [];

                    if (uuid) {
                        Logger.report({
                            scene: 'hospital_cashier_meanwhile_print',
                            data: {
                                uuid,
                                info: '判断是否可以打印',
                                selected,
                                options: this.printOptions,
                            },
                        });
                    }

                    for (let i = 0; i < selected.length; i++) {
                        let select = selected[i];

                        // 由于住院收费小票和门诊收费小票同名, 所以这里特殊处理
                        if (select === this._printOptions.HOSPITAL_CASHIER.label) {
                            select = '收费小票';
                        }

                        const selectPrintOption = this.printOptions.find((it) => it.value === select);
                        if (selectPrintOption && selectPrintOption.disabled) continue;

                        const printData = await this.fetchPrintData(select, uuid);
                        let printOptions = null;
                        // 由于住院收费小票和门诊收费小票同名, 所以这里特殊处理
                        if (select === '收费小票') {
                            printOptions = getAbcPrintOptions(this._printOptions.HOSPITAL_CASHIER.label, printData?.data, printData?.extra);
                        } else {
                            printOptions = getAbcPrintOptions(select, printData?.data, printData?.extra);
                        }
                        if (printOptions) {
                            printPropsList.push({
                                ...printOptions,
                                data: printOptions.data ?? {},
                                mode: PrintMode.Electron,
                            });
                        }
                    }

                    if (uuid) {
                        const cachePrintPropsList = printPropsList.map((it) => ({
                            data: it.data || {},
                            printConfigKey: it.printConfigKey || {},
                            businessKey: it.templateKey?.businessKey || '',
                        }));
                        Logger.report({
                            scene: 'hospital_cashier_meanwhile_print',
                            data: {
                                uuid,
                                info: '打印数据准备完成, 准备打印',
                                res: cachePrintPropsList,
                            },
                        });
                    }

                    return printPropsList;
                }));
            },

            async openPrintConfigSettingDialog() {
                const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                new PrintConfigDialog({ scene: 'hospital-cashier' }).generateDialogAsync({ parent: this });
            },

            /**
             * @desc 医保出院登记
             * @return {Promise<void>}
             */
            async handlerOutHospital() {
                try {
                    const response = await this.$abcSocialSecurity.outHospital({ patientOrderId: this.patientOrderId });
                    if (response && response.status) {
                        this.$Toast.success('办理医保出院登记成功');
                    } else {
                        this.$Toast.error(response?.message || '办理医保出院登记失败');
                    }
                } catch (e) {
                    this.$Toast.error('办理医保出院登记失败');
                    console.error(e);
                } finally {
                    this.fetchDetail();
                }
            },

            // 获取社保结算信息
            async getSocialSettleInfo() {
                try {
                    if (this.payStatus === HospitalChargeStatusEnum.UNCHARGED) return;
                    const { data } = await SocialApi.getSocialSettleInfo(this.hisChargeSettle.id);
                    const {
                        acctPay,
                        fundPaymentFee,
                    } = data || {};
                    this.$set(this.chargeSummary, 'acctPay', acctPay || 0);
                    this.$set(this.chargeSummary, 'fundPaymentFee', fundPaymentFee || 0);
                } catch (e) {
                    console.error(e);
                }
            },
            /**
             * @desc 获取打印二维码
             * <AUTHOR>
             * @date 2021-12-09 16:18:52
             */
            async fetchQrCode() {
                let qrcode = '';
                if (this.isOpenMp) {
                    try {
                        qrcode = await TpsAPI.genQrCode(this.patientOrderId || '');
                        return qrcode;
                    } catch (e) {
                        qrcode = '';
                    }
                }
                return qrcode;
            },

            async handleFeeTypeNameChange(feeTypeName) {
                try {
                    await PatientOrderAPI.updateFeeTypeName(this.patientOrderId, {
                        feeTypeName,
                    });
                    this.fetchDetail();
                    this.$refs.patientHospitalInfo.fetchPatientInHospitalInfo(this.patientOrderId);
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>
