<template>
    <abc-form-item :required="required">
        <abc-select
            ref="freqSelector"
            :show-value="freq"
            :value="freq"
            :width="width"
            no-icon
            :inner-width="innerWidth"
            placeholder="频率"
            custom-class="prescription-select-freq-options"
            :disabled="disabled"
            focus-show-options
            @enter="enterEvent"
            @change="selectFreq"
        >
            <abc-option
                v-for="it in freqList"
                :key="it.id || it.code"
                :label="it.description"
                :value="it.code"
                :class="{
                    'has-top-border': it.code === 'qnd',
                }"
            >
                <div style="width: 46px;">
                    {{ it.code }}
                </div>
                <div class="gray">
                    {{ it.description }}
                </div>
            </abc-option>
        </abc-select>
    </abc-form-item>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        DoctorMedicalPrescriptionTypeEnum,
        MedicalAdviceTypeEnum, PRN_FREQ, ST_FREQ,
    } from '@/views-hospital/medical-prescription/utils/constants.js';

    export default {
        name: 'MedicalFreq',
        props: {
            value: {
                type: String,
                required: true,
            },
            width: {
                type: Number,
                default: 48,
            },
            innerWidth: {
                type: Number,
                default: 148,
            },
            disabled: Boolean,
            adviceRuleType: [String, Number],
            adviceGroupType: [String, Number],
            required: {
                type: Boolean,
                default: true,
            },
            createdType: {
                type: Number,
                default: 0,
            },
        },
        computed: {
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            executeTimeList() {
                return this.allExecuteTimeList || [];
            },
            freqList() {
                if (this.adviceGroupType === MedicalAdviceTypeEnum.ONE_TIME) {
                    return this.executeTimeList.filter((item) => {
                        return item?.enableAdviceRuleTypes?.includes(this.adviceRuleType) && item.code === ST_FREQ;
                    });
                }
                if (this.createdType !== DoctorMedicalPrescriptionTypeEnum.WRITTEN) {
                    return this.executeTimeList.filter((item) => {
                        return item.code !== PRN_FREQ;
                    });
                }
                return this.executeTimeList.filter((item) => {
                    return item?.enableAdviceRuleTypes?.includes(this.adviceRuleType) && item.code !== ST_FREQ;
                });
            },
        },
        watch: {
            value(val) {
                this.freq = val;
            },
        },
        data() {
            return {
                freq: this.value,
            };
        },
        methods: {
            enterEvent(e, $el, val) {
                this.$emit('enter', e, $el, val);
            },
            selectFreq(v) {
                if (['qnd', 'qnw', 'nid'].indexOf(v) > -1) {
                    this.$emit('change', v);
                    return;
                }
                this.freq = v;
                const res = this.freqList.find((item) => {
                    return item.code === v;
                });
                this.$emit('change', v, res);
            },
        },
    };
</script>

<style scoped lang="scss">
@import "src/styles/theme.scss";
@import "src/styles/mixin.scss";

.options-wrapper.prescription-select-freq-options {
    width: 148px !important;

    .option-item-wrapper {
        max-height: 216px;
    }

    .abc-option-item {
        display: flex;
        align-items: center;
        padding: 8px 0 8px 6px;
    }

    .has-top-border {
        border-top: 1px solid $P6;
    }

    .gray {
        color: $T2;
    }
}
</style>
