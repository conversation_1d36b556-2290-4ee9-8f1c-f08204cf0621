<template>
    <div class="advice-item-medicine-autocomplete">
        <abc-autocomplete
            :key="closeSwitch"
            ref="abcAutocomplete"
            v-model="itemCache"
            :fetch-suggestions="queryItemAsync"
            :show-suggestions="showSuggestions"
            :custom-class="`${ customClass} advice-item-autocomplete`"
            :async-fetch="true"
            :keyboard-event="['/']"
            :inner-width="innerWidth"
            focus-show
            :delay-time="10"
            :placeholder="placeholder"
            :close-on-click-outside="handleCloseOnClickOutside"
            @enter="enterHandler"
            @blur="handleBlur"
            @focus="handleFocus"
            @enterEvent="selectItem"
        >
            <template slot="suggestion-header">
                <div class="suggestion-title">
                    <div v-if="isShowGoodsAutocompleteShortId" class="goods-code" style=" flex: none !important; width: 70px; padding-right: 6px;">
                        商品编码
                    </div>
                    <div class="name" style="flex: 2;">
                        药名
                    </div>
                    <div class="spec" style="flex: 1; margin-left: 10px;">
                        规格
                    </div>
                    <div class="stock" style="flex: 1; margin-right: 10px; text-align: right;">
                        库存
                    </div>
                    <div class="price" style="width: 80px; margin-right: 10px; text-align: right;">
                        价格
                    </div>
                    <div class="manufacturer" style="width: 100px; margin-left: 10px;">
                        <manufacturer-select
                            v-if="isSupportManufacturerFilter"
                            v-model="selectedManufacturer"
                            :manufacturer-options="manufacturerOptions"
                            size="tiny"
                            placeholder="厂家"
                            @change="handleManufacturerChange"
                        ></manufacturer-select>
                        <template v-else>
                            厂家
                        </template>
                    </div>
                    <div style="width: 138px; text-align: left;">
                        医保
                    </div>
                    <div class="manufacturer" style="width: 100px; margin-left: 10px;">
                        医保限制
                    </div>
                    <div class="manufacturer" style="width: 100px; margin-left: 10px;">
                        OTC
                    </div>
                    <div class="manufacturer" style="width: 100px; margin-left: 10px;">
                        基药
                    </div>
                    <div class="min-expiry-date" style="width: 100px; margin-left: 10px;">
                        效期
                    </div>
                    <div class="remark" style="width: 112px; margin-left: 10px;">
                        备注
                    </div>
                </div>
            </template>

            <template slot="suggestions" slot-scope="props">
                <dt
                    slot="reference"
                    class="suggestions-item"
                    :class="{
                        selected: props.index === props.currentIndex,
                        'is-tips': props.suggestion.isTips,
                        'not-source': isNoStock(props.suggestion),
                    }"
                    :disabled="props.suggestion.disabled"
                    @mousedown="selectItem(props.suggestion)"
                >
                    <div
                        v-if="isShowGoodsAutocompleteShortId"
                        class="goods-code gray"
                        :title="props.suggestion.shortId || ''"
                        style=" flex: none !important; width: 70px; padding-right: 6px;"
                    >
                        <template v-if="!isNoStock(props.suggestion)">
                            {{ props.suggestion.shortId || '' }}
                        </template>
                    </div>

                    <abc-flex class="name" style="flex: 2;" :title="props.suggestion | goodsHoverTitle">
                        <span class="ellipsis" style="max-width: calc(100% - 22px);">
                            {{ getName(props.suggestion) }}
                        </span>

                        <!-- 精麻毒 / 限制级别 -->
                        <biz-goods-info-tag-group
                            v-if="isWesternMedicine(props.suggestion)"
                            :product-info="props.suggestion"
                            :is-fold-tags="true"
                            style="display: inline-flex; flex: 1;"
                        >
                        </biz-goods-info-tag-group>

                        <span v-if="props.suggestion.deviceInfo && !props.suggestion.deviceInfo.innerFlag" class="device-name">
                            {{ props.suggestion.deviceInfo.deviceUuid || '' }}
                        </span>

                        <compose-tag v-if="props.suggestion.type === 11"></compose-tag>
                    </abc-flex>

                    <template v-if="!props.suggestion.isTips">
                        <div
                            v-if="fromPharmacy === PharmacyTypeEnum.AIR_PHARMACY"
                            class="gray spec"
                            :title="props.suggestion.spec || ''"
                            style="flex: 1; padding-left: 10px;"
                        >
                            {{ props.suggestion.spec || '' }}
                        </div>

                        <div
                            v-else
                            class="gray spec"
                            :title="getSpec(props.suggestion)"
                            style="flex: 1; margin-left: 10px;"
                        >
                            {{ props.suggestion | getSpec }}
                        </div>

                        <div
                            class="stock"
                            :title="displayInventory(props.suggestion)"
                            style="flex: 1; margin-right: 10px; text-align: right;"
                        >
                            <template v-if="!isNoStock(props.suggestion)">
                                {{ displayInventory(props.suggestion) }}
                            </template>
                        </div>

                        <div
                            v-if="!isSysItem(props.suggestion)"
                            class="gray price"
                            style="width: 80px; margin-right: 10px; text-align: right;"
                        >
                            <template v-if="!isNoStock(props.suggestion)">
                                {{ $t('currencySymbol') }}{{ props.suggestion.packagePrice | formatMoney(false) }}
                            </template>
                        </div>

                        <div
                            class="gray manufacturer"
                            :title="props.suggestion.manufacturer"
                            style="width: 100px; margin-left: 10px;"
                        >
                            {{ props.suggestion.manufacturer }}
                        </div>

                        <div class="gray" style="width: 138px; text-align: left;">
                            <span>{{ displayMedicalFeeGrade(props.suggestion) }}</span>
                        </div>

                        <!-- 医保限制 -->
                        <div
                            class="gray manufacturer"
                            :title="props.suggestion.restriction || ''"
                            style="width: 100px; margin-left: 10px;"
                        >
                            {{ props.suggestion.restriction || '' }}
                        </div>

                        <!-- OTC -->
                        <div
                            class="gray manufacturer"
                            :title="props.suggestion.otcTypeName"
                            style="width: 100px; margin-left: 10px;"
                        >
                            {{ props.suggestion.otcTypeName }}
                        </div>

                        <!-- 基药 -->
                        <div
                            class="gray manufacturer"
                            :title="props.suggestion.baseMedicineTypeName !== '否' ? props.suggestion.baseMedicineTypeName : ''"
                            style="width: 100px; margin-left: 10px;"
                        >
                            {{ props.suggestion.baseMedicineTypeName !== '否' ? props.suggestion.baseMedicineTypeName : '' }}
                        </div>

                        <div
                            class="gray min-expiry-date"
                            :title="props.suggestion.minExpiryDate"
                            style="width: 100px; margin-left: 10px;"
                        >
                            {{ props.suggestion.minExpiryDate }}
                        </div>

                        <div
                            class="gray remark"
                            :title="props.suggestion.remark"
                            style="width: 112px; margin-left: 10px;"
                        >
                            {{ props.suggestion.remark }}
                        </div>
                    </template>
                </dt>
            </template>

            <div v-if="icon" slot="prepend" class="search-icon">
                <i class="iconfont cis-icon-plus"></i>
            </div>
        </abc-autocomplete>
    </div>
</template>

<script type="text/ecmascript-6">
    // API
    import ChargeAPI from 'api/charge';

    import {
        getSpec, medicalFeeGradeFormatStr,
    } from 'src/filters';
    import ComposeTag from 'src/views/outpatient/common/compose-tag';
    import { mapGetters } from 'vuex';
    import {
        GoodsSubTypeEnum,
        GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import {
        IngredientArr, IngredientObj, RestrictLevelEnum,
    } from 'views/common/inventory/constants.js';
    import BizGoodsInfoTagGroup from '@/components-composite/biz-goods-info-tag-group/index.js';
    import ManufacturerSelect from 'views/inventory/common/manufacturer-select/index.vue';
    import useAutoCompleteManufacturerSelect from 'views/inventory/common/manufacturer-select';

    export default {
        name: 'MedicineItemAutocomplete',
        components: {
            BizGoodsInfoTagGroup,
            ComposeTag,
            ManufacturerSelect,
        },
        filters: {
            dangerIngredientFilter(val) {
                const arr = IngredientArr.filter((item) => val & item);
                let str = '';
                arr.forEach((it, index) => {
                    str += index ? ` ${IngredientObj[it]}` : IngredientObj[it];
                });
                return str;
            },
        },
        props: {
            closeSwitch: Boolean,
            keyboardEnable: {
                type: Boolean,
                default: true,
            },
            placeholder: {
                type: String,
                default: '输入药品或项目拼音码',
            },
            customClass: {
                type: String,
                default: '',
            },
            canKeyboardCharge: {
                type: Boolean,
                default: true,
            },
            queryFunction: {
                type: Function,
            },
            disabledChangeSpecification: {
                type: Boolean,
                default: false,
            },
            // 0本地药房，1空中药房
            fromPharmacy: {
                type: Number,
                default: 0,
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            icon: {
                type: Boolean,
                default: true,
            },
            innerWidth: [Number,String],
            // 是否支持厂家筛选
            isSupportManufacturerFilter: {
                type: Boolean,
                default: true,
            },
        },
        setup() {
            const {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            } = useAutoCompleteManufacturerSelect();

            return {
                selectedManufacturer,
                manufacturerOptions,
                createManufacturerOptions,
                filterManufacturer,
                clearManufacturerData,
            };
        },
        data() {
            return {
                PharmacyTypeEnum,
                RestrictLevelEnum,
                itemCache: '',
                showSuggestions: false,
                searchType: '',
                refAutoComplete: null,
            };
        },
        computed: {
            ...mapGetters([
                'currentClinic',
                'clinicConfig',
                'disableNoStockGoods',
                'isEnableListingPrice',
            ]),
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            isShowGoodsAutocompleteShortId() {
                return this.viewDistributeConfig.Inventory.isShowGoodsAutocompleteShortId;
            },
        },
        watch: {
            itemCache: {
                handler (val) {
                    if (!val.trim()) {
                        this.$emit('clear');
                    }
                    if (typeof this.clearManufacturerData === 'function') this.clearManufacturerData();
                },
            },
        },
        mounted() {
            if (this.isSupportManufacturerFilter) {
                this.refAutoComplete = this.$refs.abcAutocomplete;
            }
        },
        methods: {
            getSpec,
            isSysItem(goods) {
                return goods.type === GoodsTypeEnum.REGISTRATION ||
                    goods.type === GoodsTypeEnum.EXPRESS_DELIVERY ||
                    goods.type === GoodsTypeEnum.DECOCTION;
            },

            isNoStock(goods) {
                return this.isGoods(goods) && goods.noStocks;
            },

            /** ------------------------------------------------------------------------------------
             * 根据输入queryString 做快捷键处理
             * 查询诊所的所有检查项目，中药，西药
             * @param queryString input输入的内容
             * @param callback
             */
            async queryItemAsync(queryString, callback) {
                queryString = queryString.trim();

                // 解析queryString，使用空格分隔，取第一项为原始keyword，最后一项为parseManufacturer
                let parseManufacturer = '';

                if (queryString) {
                    const keyParts = queryString.split(' ').filter((part) => part.trim());
                    if (keyParts.length > 1) {
                        queryString = keyParts[0];
                        parseManufacturer = keyParts[keyParts.length - 1];
                    }
                }

                if (!queryString) {
                    callback([]);
                    return false;
                }
                try {
                    const params = {
                        clinicId: this.currentClinic.clinicId,
                        key: queryString,
                        type: '',
                        subType: '',
                        cMSpec: '',
                        sysType: [], // 系统收费项目type
                        searchSystemGoods: 1,
                        intersectionManufacturer: this.selectedManufacturer || parseManufacturer,
                    };
                    if (this.searchType === '饮片') {
                        params.type = 1;
                        params.subType = 2;
                        params.cMSpec = '中药饮片';
                    } else if (this.searchType === '颗粒') {
                        params.type = 1;
                        params.subType = 2;
                        params.cMSpec = '中药颗粒';
                    } else if (this.searchType === '成药') {
                        params.type = 1;
                        params.subType = [1, 3];
                        params.cMSpec = '';
                    }

                    /**
                     * @desc 2：物资；3：检查；4：治疗；7：商品；11：套餐 19: 其他
                     * <AUTHOR>
                     * @date 2019/10/28 12:40:58
                     */
                    if (!this.keyboardEnable) {
                        params.type = [2, 3, 4, 7, 11, 19];
                        params.sysType = [ ];
                    }

                    let data;
                    if (typeof this.queryFunction === 'function') {
                        data = await this.queryFunction(queryString);
                    } else {
                        const res = await ChargeAPI.fetchAllGoods(params);
                        data = res.data;
                    }
                    if (!data) {
                        callback([]);
                        return false;
                    }

                    if (this.disableNoStockGoods && this.fromPharmacy !== PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                        data.list = data.list.map((item) => {
                            if (this.isGoods(item)) {
                                item.disabled = item.noStocks || (item.stockPackageCount + item.stockPieceCount) <= 0;
                            }
                            return item;
                        });
                    }

                    // 处理厂家筛选
                    const filteredList = data.list || [];
                    if (this.isSupportManufacturerFilter && this.createManufacturerOptions) {
                        this.createManufacturerOptions(filteredList);
                    }

                    // 如果选择了厂家，则过滤结果
                    if (this.isSupportManufacturerFilter && this.filterManufacturer) {
                        data.list = this.filterManufacturer(filteredList);
                    }

                    // 透传 data.query.key 保证请求回来和搜索key一致
                    let keyword = '';
                    if (data.query) {
                        keyword = data.query.keyword || data.query.key;
                    }

                    if (callback) {
                        callback(data.list, keyword || '');
                    } else if (this.refAutoComplete) {
                        this.refAutoComplete.focus();
                        this.refAutoComplete.callbackHandler(data.list, keyword || '');
                    }
                } catch (err) {
                    callback([]);
                }
            },
            /**
             * @desc 拼接商品name
             * <AUTHOR>
             * @date 2019/03/30 11:20:12
             */
            getName(item) {
                if (item.type === 1 && item.subType === 2) {
                    return item.medicineCadn || item.name;
                }
                if (item.medicineCadn) {
                    return item.medicineCadn + (item.name ? `(${item.name})` : '');
                }
                return item.name;

            },

            isWesternMedicine(item) {
                return item.type === GoodsTypeEnum.MEDICINE && (item.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine || item.subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM);
            },

            isGoods(item) {
                return item.type === GoodsTypeEnum.MEDICINE || item.type === GoodsTypeEnum.MATERIAL || item.type === GoodsTypeEnum.GOODS || item.type === 24;
            },

            /**
             * @desc display autocomplete 下拉框 库存信息
             * <AUTHOR>
             * @date 2018/08/02 15:11:50
             */
            displayInventory(item) {
                if (item.type !== 1 && item.type !== 2 && item.type !== 7) return '';
                let str = '';
                if (item.stockPackageCount) {
                    str += `${item.stockPackageCount}${item.packageUnit}`;
                }
                if (item.stockPieceCount) {
                    str += `${item.stockPieceCount}${item.pieceUnit}`;
                }
                if (!item.stockPackageCount && !item.stockPieceCount) {
                    if (item.packageUnit) {
                        str += `0${item.packageUnit || ''}`;
                    } else {
                        str += `0${item.pieceUnit || 'g'}`;
                    }
                }
                return str;
            },

            /**
             * @desc autocomplete 响应回车事件
             *  /  切换分类  全部、饮片、颗粒、西成药
             *  .  删除指定  .2 删除第二个药品
             *  *  付数     *3 中药剂数 置为3
             *  -  结算     调起收费
             * <AUTHOR>
             * @date 2018/12/29 12:18:37
             */
            async enterHandler(event) {
                const { value } = event.currentTarget;

                if (this.keyboardEnable) {
                    if (/^\.\d+$/.test(value)) {
                        this.$emit('deleteItem', value.replace(/[^\d]/g, ''));
                        this.itemCache = '';
                        return false;
                    }

                    if (/^\*\d+/.test(value)) {
                        let doseCount = value.replace(/\*/g, '');
                        doseCount = Math.floor(doseCount);
                        if (!doseCount || +doseCount === 0 || isNaN(doseCount)) {
                            doseCount = '';
                        }
                        this.$emit('inputDoseCount', doseCount);
                        this.itemCache = '';
                        return false;
                    }
                }

                if (/^-$/.test(value) && this.canKeyboardCharge) {
                    event.cancelBubble = true;
                    event.returnValue = false;
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.$el.querySelector('input').blur();
                    this.itemCache = '';
                    this.$emit('submit');
                    return false;
                }
            },
            async handleBarcode(barcode) {
                const { data } = await ChargeAPI.fetchAllGoods({
                    clinicId: this.currentClinic.clinicId,
                    key: barcode,
                });
                if (barcode === data.query.key) {
                    if (data.list.length) {
                        this.selectItem(data.list[ 0 ], true);
                        this.itemCache = '';
                    } else {
                        this.$alert({
                            type: 'warn',
                            title: '没有获取到药品信息',
                            content: [ '请联系总部药品管理员添加该药品' ],
                            onClose: () => {
                                $('.retail-western-medicine-autocomplete input').focus();
                            },
                        });
                    }
                }
            },
            handleBlur() {
                this.$emit('blur');
            },
            handleFocus() {
                this.$emit('focus');
            },

            /**
             * @desc 选择后处理函数
             * <AUTHOR>
             * @date 2019/03/25 19:15:58
             */
            async selectItem(selected, focusInput = true) {
                if (!selected) return false;
                this.$emit('select', selected, focusInput);
                this.itemCache = '';
            },
            dangerIngredientMethod(val) {
                const arr = IngredientArr.filter((item) => val & item);
                let str = '';
                arr.forEach((it, index) => {
                    str += index ? ` ${IngredientObj[it]}` : IngredientObj[it];
                });
                return str;
            },
            displayMedicalFeeGrade(suggestion) {
                const _arr = [];
                const medicalFeeGradeStr = medicalFeeGradeFormatStr(suggestion, {
                    shebaoCardInfo: this.shebaoCardInfo,
                });
                if (medicalFeeGradeStr) {
                    _arr.push(medicalFeeGradeStr);
                }
                if (suggestion.shebao) {
                    if (this.isEnableListingPrice && suggestion.shebao.listingPrice) {
                        _arr.push('挂网');
                    }
                    if (suggestion.shebao.priceLimit) {
                        _arr.push('限价');
                    }
                }
                return _arr.join('/');
            },

            handleManufacturerChange() {
                // 如果不支持厂家筛选，直接返回
                if (!this.isSupportManufacturerFilter) {
                    return;
                }

                // 配合focus-show重新触发查询
                this.$nextTick(() => {
                    this.refAutoComplete?.focus?.();
                });
            },

            handleCloseOnClickOutside(e) {
                const eventPath = e?.path || (e?.composedPath?.());

                // 检查是否点击了厂家筛选下拉框
                if (this.isSupportManufacturerFilter && eventPath?.some((item) => {
                    if (!item.className) return false;
                    if (typeof item.className !== 'string') return false;
                    return item.className.includes('goods-auto-complete-manufacturer-select');
                })) {
                    return false;
                }

                // 厂家筛选关闭面板时重置搜索结果
                if (this.isSupportManufacturerFilter && this.selectedManufacturer) {
                    // 清除选中厂家
                    if (typeof this.clearManufacturerData === 'function') {
                        this.clearManufacturerData(true);
                    }
                    // 更新列表数据
                    this.queryItemAsync(this.itemCache, (list) => {
                        // 手动触发下拉框更新-面板不展示
                        if (this.refAutoComplete) {
                            this.refAutoComplete.isFocus = false;
                            this.refAutoComplete.callbackHandler(list);
                        }
                    });
                }

                // 添加 closePanel 事件
                this.$emit('closePanel');

                return true;
            },
        },
    };
</script>
<style lang="scss">
.advice-item-autocomplete {
    .suggestion-title {
        display: flex;
        align-items: center;
        padding: 0 20px 0 12px;
    }

    .suggestion-title,
    .suggestions-item {
        .goods-code {
            flex: none !important;
            width: 70px;
            padding-right: 6px;
        }
    }

    .abc-scrollbar-wrapper {
        overflow-y: scroll;
    }
}

.advice-item-medicine-autocomplete .abc-autocomplete-wrapper {
    position: relative;
    width: 100%;

    .prepend-input {
        width: 40px;
    }

    .prepend-input i,
    .abc-popover__reference i {
        color: $P1;
        outline: none;
    }

    .abc-input__inner {
        height: 40px;
        padding-left: 40px;
    }

    .search-type-str {
        position: absolute;
        top: 0;
        right: 34px;
        z-index: 3;
        height: 40px;
        font-size: 14px;
        line-height: 40px;
        color: $T2;
    }
}
</style>
