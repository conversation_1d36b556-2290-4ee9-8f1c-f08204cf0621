<template>
    <div class="medical-prescription-detail-tabs">
        <abc-tabs
            v-model="currentTab"
            :option="tabOptions"
            size="middle"
            style="padding: 0 16px;"
        ></abc-tabs>

        <div class="medical-prescription-detail-tabs-content">
            <!--处方明细-->
            <template v-if="currentTab === 4">
                <abc-table-fixed2
                    class="prescription-table"
                    :header="prescriptionTableHeader"
                    :data="prescriptionDetailList"
                    :empty-opt="emptyOption"
                    :has-inner-border="false"
                ></abc-table-fixed2>
                <p class="chinese-desc">
                    {{ totalWeightInfo.desc }}
                </p>
            </template>
            <!--执行明细-->
            <abc-table-fixed2
                v-if="currentTab === 0"
                :header="exeDetailTableHeader"
                :data="exeDetailList"
                :empty-opt="emptyOption"
                :has-inner-border="false"
            ></abc-table-fixed2>
            <!--医嘱流转-->
            <status-logs v-if="currentTab === 1" :status-logs="detailInfo.statusLogs"></status-logs>
            <!--费用统计-->
            <abc-table-fixed2
                v-if="currentTab === 2"
                :header="expenseStatTableHeader"
                :data="expenseStatList"
                :empty-opt="emptyOption"
                :has-inner-border="false"
            ></abc-table-fixed2>
            <!--计费明细-->
            <abc-table-fixed2
                v-if="currentTab === 3"
                :header="billDetailTableHeader"
                :data="billDetailList"
                :empty-opt="emptyOption"
                :has-inner-border="false"
            ></abc-table-fixed2>
        </div>

        <consultation-details-dialog
            v-if="isShowConsultationDetailsDialog"
            v-model="isShowConsultationDetailsDialog"
            :patient="curPatientHospitalInfo"
            :consultation-id="consultationId"
            :patient-order-id="patientOrderId"
            :allow-modify="false"
            :append-to-body="false"
        ></consultation-details-dialog>
    </div>
</template>

<script>
    import { parseTime } from '@abc/utils-date';
    import {
        GoodsSubTypeEnum,
        GoodsTypeEnum,
    } from '@abc/constants';
    import ConsultationDetailsDialog
        from '@/views-hospital/medical-prescription/components/consultation/components/consultation-details-dialog';
    import StatusLogs from './status-logs';
    import MedicalPrescriptionAPI from '@/api/hospital/medical-prescription';
    import HospitalChargeAPI from '@/api/hospital/charge';
    import {
        TreatmentTypeEnum,
        MedicalAdviceStatusStr,
        AdviceRuleType,
    } from '@/views-hospital/medical-prescription/utils/constants.js';
    import ICON_EMPTY from 'src/assets/images/icon/<EMAIL>';
    import { ConsultationStatusName } from '@/views-hospital/consultation/utils/constants';
    import { HospitalConsultationRouterNameKeys } from '@/views-hospital/consultation/core/routes.js';
    import { mapGetters } from 'vuex';
    import { medicalFeeGradeFormatStr } from '@/filters/index.js';
    import { PriceType } from 'views/common/inventory/constants';
    import { PriceStatusEnum } from '@/views-hospital/cost/utils/constants.js';
    import { formatMoney } from '@/filters';
    import ReportDetailDialog from 'views/outpatient/common/report-detal-dialog';
    import {
        HOSPITAL_PRICE_SCOPE_TYPE, HOSPITAL_PRICE_SETTLE_TYPE,
    } from 'utils/constants';
    import { isImgUrl } from 'views/physical-examination/assessment/utils';

    const GoodsBatchInfoPopover = () => import('src/views/layout/goods-batch-info-popover/index.vue');

    export default {
        components: {
            StatusLogs,
            ConsultationDetailsDialog,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        props: {
            id: {
                type: String,
                require: true,
                default: '',
            },
            detailInfo: {
                type: Object,
                default() {
                    return {};
                },
            },
            businessType: {
                type: String,
                default: 'nurse',
            },
            loading: Boolean,
        },
        data() {
            return {
                currentTab: 0,
                // 执行明细
                exeDetailList: [],
                patientOrderId: '',
                isShowConsultationDetailsDialog: false,
                // 处方统计
                prescriptionTableHeader: [
                    {
                        label: '药名',
                        prop: 'goodsName',
                        align: 'left',
                        width: 160,
                        render: (h, row) => {
                            const {
                                goodsName, verifySignatory,
                            } = row || {};
                            const {
                                name, handSign,
                            } = verifySignatory ?? {};
                            return (
                                <abc-flex align="center" justify="space-between">
                                    <div>{ goodsName }</div>
                                    {
                                        handSign && isImgUrl(handSign) ? (
                                            <img src={handSign} alt="" style={{ width: '44px' }} />
                                        ) : name ? (
                                            <abc-text size="mini" theme="gray-light">
                                                { name }
                                            </abc-text>
                                        ) : null
                                    }
                                </abc-flex>
                            );
                        },
                    },
                    {
                        label: '煎法',
                        prop: 'remark',
                        align: 'left',
                    },
                    {
                        label: '单剂数量',
                        prop: 'unitCountStr',
                        align: 'right',
                    },
                    {
                        label: '剂数',
                        prop: 'dosageCountStr',
                        align: 'right',
                    },
                    {
                        label: '总量',
                        prop: 'itemCountStr',
                        align: 'right',
                    },
                ],
                expenseStatList: [],
                expensePriceScopeType: HOSPITAL_PRICE_SCOPE_TYPE.SOURCE,
                expensePriceSettleType: HOSPITAL_PRICE_SETTLE_TYPE.SOURCE,
                consultationId: '',
                // 计费明细
                billDetailList: [],
                billDetailPriceScopeType: HOSPITAL_PRICE_SCOPE_TYPE.SOURCE,
                billDetailPriceSettleType: HOSPITAL_PRICE_SETTLE_TYPE.SOURCE,
                showExamReportDialog: false,
                curReportSheetId: '',
                curExamReportList: [],
                emptyOption: {
                    label: '暂无数据',
                    imgUrl: ICON_EMPTY,
                },

                curType: '',
            };
        },
        computed: {
            expenseStatTableHeader() {
                const currentConfig = [
                    {
                        label: '项目名称',
                        prop: 'name',
                        titleAlign: 'left',
                        align: 'left',
                        render(h, row) {
                            const isBold = row.name === '合计';
                            return (
                                <span style={{ fontWeight: isBold ? 'bold' : 'normal' }}>{ row.name }</span>
                            );
                        },
                    },
                    {
                        label: '总费用',
                        prop: 'totalPrice',
                        align: 'right',
                        titleAlign: 'right',
                        width: 40,
                        render(h, row) {
                            const isBold = row.name === '合计';
                            return (
                                <span style={{ fontWeight: isBold ? 'bold' : 'normal' }}>{ formatMoney(row.totalPrice) }</span>
                            );
                        },
                    },
                    {
                        label: '已计费',
                        prop: 'chargedTotalPrice',
                        align: 'right',
                        titleAlign: 'right',
                        width: 40,
                        render(h, row) {
                            const isBold = row.name === '合计';
                            return (
                                <span style={{ fontWeight: isBold ? 'bold' : 'normal' }}>{ formatMoney(row.chargedTotalPrice) }</span>
                            );
                        },
                    },
                    {
                        label: '单价',
                        prop: 'unitPrice',
                        align: 'right',
                        titleAlign: 'right',
                        width: 40,
                        render(h, row) {
                            return (
                            <span>{formatMoney(row.unitPrice)}</span>
                            );
                        },
                    },
                    {
                        label: '数量',
                        prop: 'totalCount',
                        align: 'right',
                        titleAlign: 'right',
                        width: 30,
                    },
                    {
                        label: '单位',
                        prop: 'unit',
                        align: 'right',
                        titleAlign: 'right',
                        width: 30,
                    },
                    {
                        label: '医保',
                        prop: '',
                        align: 'right',
                        titleAlign: 'right',
                        width: 30,
                        render: (h, row) => {
                            return h(
                                'span',
                                `${medicalFeeGradeFormatStr(row.productInfoSnapshot, {}, row.medicalInsurancePayType)}`,
                            );
                        },
                    },
                ];
                return currentConfig.map((it) => {
                    const { prop } = it;
                    let newLabel = it.label;
                    const newProp = {
                        totalPrice: {
                            [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: 'totalPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: 'shebaoDisplayTotalPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SETTLE]: 'settleTotalPrice',
                        },
                        chargedTotalPrice: {
                            [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: 'chargedTotalPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: 'shebaoDisplayChargedTotalPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SETTLE]: 'settleChargedTotalPrice',
                        },
                        unitPrice: {
                            [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: 'unitPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: 'shebaoDisplayUnitPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SETTLE]: 'settleUnitPrice',
                        },
                    }[prop]?.[this.expensePriceScopeType];

                    if (newProp) {
                        if (this.expensePriceScopeType === HOSPITAL_PRICE_SCOPE_TYPE.SETTLE) {
                            if (this.expensePriceSettleType === HOSPITAL_PRICE_SETTLE_TYPE.SHEBAO) {
                                newLabel = `${it.label}(医保)`;
                            }
                        }
                        if (this.expensePriceScopeType === HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO) {
                            newLabel = `${it.label}(医保)`;
                        }
                    }

                    return {
                        ...it,
                        prop: newProp || prop,
                        label: newLabel,
                    };
                });
            },

            billDetailTableHeader() {
                const chargedTotalPriceKey = {
                    [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: 'chargedTotalPrice',
                    [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: 'shebaoDisplayChargedTotalPrice',
                    [HOSPITAL_PRICE_SCOPE_TYPE.SETTLE]: 'settleChargedTotalPrice',
                }[this.billDetailPriceScopeType];

                const currentConfig = [
                    {
                        label: '状态',
                        prop: 'statusName',
                        titleAlign: 'left',
                        align: 'left',
                        width: 40,
                    },
                    {
                        label: '计费时间',
                        prop: 'chargedTime',
                        titleAlign: 'left',
                        align: 'left',
                        width: 80,
                        render(h, row) {
                            const time = row.chargedTime ? parseTime(row.chargedTime, 'm-d h:i', true) : '';
                            return h('span', time);
                        },
                    },
                    {
                        label: '项目名称',
                        prop: 'name',
                        titleAlign: 'left',
                        align: 'left',
                    },
                    {
                        label: '费别',
                        prop: 'goodsType',
                        titleAlign: 'left',
                        align: 'left',
                        width: 48,
                        render(h, row) {
                            return (
                                <div class={'ellipsis'} title={row.goodsFeeTypeName}>
                                    <span class={'ellipsis'}>{row.goodsFeeTypeName}</span>
                                </div>
                            );
                        },
                    },
                    {
                        label: '单价',
                        prop: 'unitPrice',
                        align: 'right',
                        titleAlign: 'right',
                        width: 62,
                        render(h, row, col) {
                            const { priceStatus } = row;
                            if (priceStatus === PriceStatusEnum.DETERMINATE || !priceStatus) {
                                return (
                                <span>{formatMoney(row[col.prop])}</span>
                                );
                            }
                            return (
                            <span>
                                    -
                                    <abc-tooltip-info
                                        placement="top"
                                        content="库存不足导致锁库失败，发药后将更新价格"
                                        style="margin-left: 4px;cursor: pointer;"
                                    >
                                    </abc-tooltip-info>
                                </span>
                            );
                        },
                    },
                    {
                        label: '数量',
                        prop: 'count',
                        align: 'right',
                        titleAlign: 'right',
                        width: 40,
                    },
                    {
                        label: '单位',
                        prop: 'unit',
                        align: 'right',
                        titleAlign: 'right',
                        width: 40,
                    },
                    {
                        label: '金额',
                        prop: 'totalPrice',
                        align: 'right',
                        titleAlign: 'right',
                        width: 50,
                        render(h, row, col) {
                            const iconStyle = {
                                color: 'var(--abc-color-P1)',
                                marginLeft: '4px',
                                cursor: 'pointer',
                            };
                            const {
                                batchInfos, goodsSnapshotVersion,
                            } = row;
                            const { priceType } = goodsSnapshotVersion || {};
                            const isShowIcon = priceType === PriceType.PKG_PRICE_MAKEUP && batchInfos?.length > 0;
                            // 已计费的展示chargedTotalPrice, 其余的展示totalPrice
                            const displayPrice = row.status === 10 ? row[chargedTotalPriceKey] : row[col.prop];

                            return (
                            <GoodsBatchInfoPopover item={row} batches={row.batchInfos}>
                                    <span style="display: flex;align-items: center;justify-content: flex-end;">
                                        <span>{formatMoney(displayPrice, true)}</span>
                                        {isShowIcon && (
                                            <abc-icon
                                                icon="info_bold"
                                                size={14}
                                                style={iconStyle}>
                                            </abc-icon>
                                        )}
                                    </span>
                            </GoodsBatchInfoPopover>
                            );
                        },
                    },
                    {
                        label: '医保',
                        prop: '',
                        align: 'right',
                        titleAlign: 'right',
                        width: 50,
                        render(h, row) {
                            return h('span', medicalFeeGradeFormatStr(row.productInfoSnapshot, {}, row.medicalInsurancePayType));
                        },
                    },
                ];

                return currentConfig.map((it) => {
                    const { prop } = it;
                    let newLabel = it.label;
                    const newProp = {
                        totalPrice: {
                            [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: 'totalPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: 'shebaoDisplayTotalPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SETTLE]: 'settleTotalPrice',
                        },
                        unitPrice: {
                            [HOSPITAL_PRICE_SCOPE_TYPE.SOURCE]: 'unitPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO]: 'shebaoDisplayUnitPrice',
                            [HOSPITAL_PRICE_SCOPE_TYPE.SETTLE]: 'settleUnitPrice',
                        },
                    }[prop]?.[this.billDetailPriceScopeType];
                    if (newProp) {
                        if (this.billDetailPriceScopeType === HOSPITAL_PRICE_SCOPE_TYPE.SETTLE) {
                            if (this.billDetailPriceSettleType === HOSPITAL_PRICE_SETTLE_TYPE.SHEBAO) {
                                newLabel = `${it.label}(医保)`;
                            }
                        }
                        if (prop === 'unitPrice' && this.billDetailPriceScopeType === HOSPITAL_PRICE_SCOPE_TYPE.SHEBAO) {
                            newLabel = `${it.label}(医保)`;

                            // eslint-disable-next-line no-unused-vars
                            it.headerRender = (h) => {
                                return (
                                <abc-flex align="center" gap={2}>
                                    {newLabel}
                                    <abc-tooltip-info icon-size={12} placement="bottom">
                                        <div style="max-width: 250px;">
                                            费别为医保的患者，若在「医保管理」--「设置」--「限价设置」中设置了医保限价规则，将按设置的限价规则计费
                                        </div>
                                    </abc-tooltip-info>
                                </abc-flex>
                                );
                            };
                        }
                    }
                    return {
                        ...it,
                        prop: newProp || prop,
                        label: newLabel,
                    };
                });
            },

            isChineseMedicalPrescription() {
                const { adviceRule } = this.detailInfo;
                const { type } = adviceRule || {};
                return [
                    AdviceRuleType.CHINESE_MEDICINE_TABLETS,
                    AdviceRuleType.CHINESE_MEDICINE_GRANULES,
                ].includes(type);
            },
            tabOptions() {
                const options = [
                    {
                        label: '执行明细',
                        value: 0,
                    },
                    {
                        label: '医嘱流转',
                        value: 1,
                    },
                    {
                        label: '费用统计',
                        value: 2,
                    },
                    {
                        label: '计费明细',
                        value: 3,
                    },
                ];
                if (this.isChineseMedicalPrescription) {
                    options.unshift({
                        label: '处方明细',
                        value: 4,
                    });
                }
                return options;
            },
            curPatientHospitalInfo() {
                return this.$abcPage.$store.curPatientHospitalInfo || {};
            },
            ...mapGetters('viewDistribute', ['viewDistributeConfig']),
            allowExamReportPrint() {
                return this.viewDistributeConfig.Print.allowExamReportPrint;
            },
            exeDetailTableHeader() {
                const { diagnosisTreatmentType } = this.detailInfo || {};
                const that = this;
                switch (diagnosisTreatmentType) {
                    case TreatmentTypeEnum.INSPECTION:
                        return [
                            {
                                label: '计划时间',
                                prop: 'planExecuteTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.planExecuteTime ? parseTime(row.planExecuteTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '状态',
                                prop: 'status',
                                titleAlign: 'left',
                                align: 'left',
                                width: 30,
                                render(h, row) {
                                    return h('span', MedicalAdviceStatusStr[row.status]);
                                },
                            },
                            {
                                label: '执行护士',
                                prop: 'executorName',
                                width: 58,
                                titleAlign: 'left',
                                align: 'left',
                            },
                            {
                                label: '执行时间',
                                prop: 'executeTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.executeTime ? parseTime(row.executeTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '打印',
                                prop: 'isPrint',
                                titleAlign: 'left',
                                align: 'left',
                                width: 40,
                            },
                            {
                                label: '接收科室',
                                prop: 'departmentName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '检查报告',
                                titleAlign: 'left',
                                align: 'left',
                                render(h, row) {
                                    const { examSheetSimpleViews = [] } = row;

                                    const isExistDone = examSheetSimpleViews.some((e) => e.status === 1);

                                    if (!isExistDone) {
                                        return h('span', {
                                            style: {
                                                color: that.$style.T2,
                                            },
                                        }, '未出');
                                    }

                                    return h('span', {
                                        style: {
                                            color: that.$style.B1,
                                            cursor: 'pointer',
                                        },
                                        on: {
                                            click: () => {
                                                that.curExamReportList = examSheetSimpleViews.map((item) => ({
                                                    ...item,
                                                    id: item.examinationSheetId,
                                                }));
                                                that.curType = examSheetSimpleViews[0]?.goodsSubType || '';
                                                new ReportDetailDialog({
                                                    outReportList: that.curExamReportList,
                                                    appendToBody: true,
                                                    type: that.curType,
                                                    isFunctionDialog: true,
                                                }).generateDialogAsync({ parent: that });
                                            },
                                        },
                                    }, '报告详情');
                                },
                            },
                            {
                                label: '报告时间',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    // 排序，取最新一次报告时间
                                    const curLatestReport = (row.examSheetSimpleViews || []).sort((a, b) => {
                                        return new Date(b.reportTime).getTime() - new Date(a.reportTime).getTime();
                                    })[0] || {};

                                    const time = curLatestReport.reportTime ?
                                        parseTime(curLatestReport.reportTime, 'm-d h:i', true) :
                                        '';
                                    return h('span', time);
                                },
                            },
                        ];
                    case TreatmentTypeEnum.ASSAY:
                        return [
                            {
                                label: '计划时间',
                                prop: 'planExecuteTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.planExecuteTime ? parseTime(row.planExecuteTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '状态',
                                prop: 'status',
                                titleAlign: 'left',
                                align: 'left',
                                width: 30,
                                render(h, row) {
                                    return h('span', MedicalAdviceStatusStr[row.status]);
                                },
                            },
                            {
                                label: '执行护士',
                                prop: 'executorName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '执行时间',
                                prop: 'executeTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.executeTime ? parseTime(row.executeTime, 'm-d h:i') : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '打印',
                                prop: 'isPrint',
                                titleAlign: 'left',
                                align: 'left',
                                width: 40,
                            },
                            {
                                label: '标本号',
                                prop: 'examSheetSimpleView.sampleNo',
                                titleAlign: 'left',
                                align: 'left',
                                width: 100,
                                render(h, row) {
                                    const { examSheetSimpleViews = [] } = row;

                                    const sampleNo = examSheetSimpleViews.map((item) => item.sampleNo || '').join('、');
                                    return h('div', {
                                        style: {
                                            overflow: 'hidden',
                                            marginRight: '8px',
                                            whiteSpace: 'nowrap',
                                        },
                                        attrs: {
                                            title: sampleNo,
                                        },
                                    }, sampleNo);
                                },
                            },
                            {
                                label: '接收科室',
                                prop: 'departmentName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '检验报告',
                                titleAlign: 'left',
                                align: 'left',
                                render(h, row) {
                                    const { examSheetSimpleViews = [] } = row;

                                    const isExistDone = examSheetSimpleViews.some((e) => e.status === 1);

                                    if (!isExistDone) {
                                        return h('span', {
                                            style: {
                                                color: that.$style.T2,
                                            },
                                        }, '未出');
                                    }

                                    return h('span', {
                                        style: {
                                            color: that.$style.B1,
                                            cursor: 'pointer',
                                        },
                                        on: {
                                            click: () => {
                                                that.curExamReportList = examSheetSimpleViews.map((item) => ({
                                                    ...item,
                                                    id: item.examinationSheetId,
                                                }));
                                                that.curType = examSheetSimpleViews[0]?.goodsSubType || '';
                                                new ReportDetailDialog({
                                                    outReportList: that.curExamReportList,
                                                    appendToBody: true,
                                                    type: that.curType,
                                                    isFunctionDialog: true,
                                                }).generateDialogAsync({ parent: that });
                                            },
                                        },
                                    }, '报告详情');
                                },
                            },
                            {
                                label: '报告时间',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    // 排序，取最新一次报告时间
                                    const curLatestReport = (row.examSheetSimpleViews || []).sort((a, b) => {
                                        return new Date(b.reportTime).getTime() - new Date(a.reportTime).getTime();
                                    })[0] || {};

                                    const time = curLatestReport.reportTime ?
                                        parseTime(curLatestReport.reportTime, 'm-d h:i', true) :
                                        '';
                                    return h('span', time);
                                },
                            },
                        ];
                    case TreatmentTypeEnum.NURSE:
                        return [
                            {
                                label: '计划时间',
                                prop: 'planExecuteTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.planExecuteTime ? parseTime(row.planExecuteTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '状态',
                                prop: 'status',
                                titleAlign: 'left',
                                align: 'left',
                                width: 30,
                                render(h, row) {
                                    return h('span', MedicalAdviceStatusStr[row.status]);
                                },
                            },
                            {
                                label: '执行护士',
                                prop: 'executorName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '执行时间',
                                prop: 'executeTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.executeTime ? parseTime(row.executeTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '打印',
                                prop: 'isPrint',
                                titleAlign: 'left',
                                align: 'left',
                                width: 40,
                            },
                        ];
                    case TreatmentTypeEnum.TRANSFER_DEPARTMENT:
                        return [
                            {
                                label: '计划转科时间',
                                prop: 'planExecuteTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.planExecuteTime ? parseTime(row.planExecuteTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '状态',
                                prop: 'status',
                                titleAlign: 'left',
                                align: 'left',
                                width: 30,
                                render(h, row) {
                                    return h('span', MedicalAdviceStatusStr[row.status]);
                                },
                            },
                            {
                                label: '执行护士',
                                prop: 'executorName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '执行时间',
                                prop: 'executeTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.executeTime ? parseTime(row.executeTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '备注',
                                prop: 'remark',
                                titleAlign: 'left',
                                align: 'left',
                            },
                            {
                                label: '转入科室',
                                prop: 'transferToDepartmentName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                        ];
                    case TreatmentTypeEnum.CONSULTATION:
                        return [
                            {
                                label: '计划执行时间',
                                prop: 'planExecuteTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.planExecuteTime ? parseTime(row.planExecuteTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '状态',
                                prop: 'status',
                                titleAlign: 'left',
                                align: 'left',
                                width: 30,
                                render(h, row) {
                                    return h('span', MedicalAdviceStatusStr[row.status]);
                                },
                            },
                            {
                                label: '执行护士',
                                prop: 'executorName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '执行时间',
                                prop: 'executeTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.executeTime ? parseTime(row.executeTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '会诊状态',
                                prop: 'consultationStatus',
                                titleAlign: 'left',
                                align: 'left',
                                render(h, row) {
                                    const status = row?.consultationSheet?.status || 0;
                                    return h('span', status ? ConsultationStatusName[status] : '');
                                },
                            },
                            {
                                label: '会诊单',
                                prop: 'consultation',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                                render(h, row) {
                                    return h('span', {
                                        style: {
                                            color: that.$style.B1,
                                            cursor: 'pointer',
                                        },
                                        on: {
                                            click: () => {
                                                const {
                                                    status, consultationSheet = {}, patientOrderId = '',
                                                } = row;
                                                const { path } = that.$route;
                                                // 核对后跳转
                                                if (status >= 20 && path.indexOf('nurse') === -1) {
                                                    that.$router.push({
                                                        name: HospitalConsultationRouterNameKeys.main,
                                                        params: {
                                                            id: consultationSheet?.id,
                                                        },
                                                        query: {
                                                            tab: 0,
                                                            status: consultationSheet?.status,
                                                        },
                                                    });
                                                } else {
                                                    that.consultationId = consultationSheet?.id || '';
                                                    that.patientOrderId = patientOrderId || '',
                                                    that.isShowConsultationDetailsDialog = true;
                                                }

                                            },
                                        },
                                    }, '查看详情');
                                },
                            },
                        ];
                    case TreatmentTypeEnum.TRANSFER_WITH_MEDICINE: // 转院和出院用同一套逻辑
                    case TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE:
                        return [
                            {
                                label: '计划出院时间',
                                prop: 'dischargeHospitalTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.dischargeHospitalTime ? parseTime(row.dischargeHospitalTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '状态',
                                prop: 'status',
                                titleAlign: 'left',
                                align: 'left',
                                width: 30,
                                render(h, row) {
                                    return h('span', MedicalAdviceStatusStr[row.status]);
                                },
                            },
                            {
                                label: '执行护士',
                                prop: 'executorName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '执行时间',
                                prop: 'executeTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.executeTime ? parseTime(row.executeTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '出院原因',
                                prop: 'dischargeHospitalReason',
                                titleAlign: 'left',
                                align: 'left',
                            },
                        ];
                    case TreatmentTypeEnum.SURGERY:
                        return [
                            {
                                label: '计划时间',
                                prop: 'planExecuteTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.planExecuteTime ? parseTime(row.planExecuteTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '状态',
                                prop: 'status',
                                titleAlign: 'left',
                                align: 'left',
                                width: 30,
                                render(h, row) {
                                    return h('span', MedicalAdviceStatusStr[row.status]);
                                },
                            },
                            {
                                label: '执行护士',
                                prop: 'executorName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '执行时间',
                                prop: 'executeTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.executeTime ? parseTime(row.executeTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                        ];
                    default:
                        return [
                            {
                                label: '计划时间',
                                prop: 'planExecuteTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.planExecuteTime ? parseTime(row.planExecuteTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '状态',
                                prop: 'status',
                                titleAlign: 'left',
                                align: 'left',
                                width: 30,
                                render(h, row) {
                                    return h('span', MedicalAdviceStatusStr[row.status]);
                                },
                            },
                            {
                                label: '执行护士',
                                prop: 'executorName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                            },
                            {
                                label: '执行时间',
                                prop: 'executeTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.executeTime ? parseTime(row.executeTime, 'm-d h:i', true) : '';
                                    return h('span', time);
                                },
                            },
                            {
                                label: '执行单',
                                prop: 'isPrint',
                                titleAlign: 'left',
                                align: 'left',
                                width: 40,
                            },
                            {
                                label: '发药',
                                prop: 'dispensingSheet.statusName',
                                titleAlign: 'left',
                                align: 'left',
                                width: 76,
                                render(h, row) {
                                    const statusName = row.dispensingSheet?.statusName || '';
                                    return (
                                        <abc-flex align="center" gap="4">
                                            { statusName }
                                            {
                                                row.isNeedRefundDispensing ? <abc-tag-v2 size="mini" theme="danger">
                                                    待退
                                                </abc-tag-v2> : null
                                            }
                                        </abc-flex>
                                    );
                                },
                            },
                            {
                                label: '发药量',
                                prop: 'dispensingFormItems',
                                titleAlign: 'left',
                                align: 'left',
                                width: 46,
                                render(h, row) {
                                    const { dispensingSheet } = row;
                                    if (dispensingSheet) {
                                        let unitCount = 0;
                                        (dispensingSheet?.dispensingForms[0]?.dispensingFormItems || []).forEach((item) => {
                                            unitCount += item.unitCount;
                                        });
                                        const {
                                            productType, productSubType, doseCount, unit,
                                        } = dispensingSheet?.dispensingForms[0]?.dispensingFormItems[0] || {};
                                        if (
                                            productType === GoodsTypeEnum.MEDICINE &&
                                            productSubType === GoodsSubTypeEnum[productType].ChineseMedicine
                                        ) {
                                            return h('span', `${unitCount}${unit}*${doseCount}剂`);
                                        }
                                        const str = (unitCount && unit) ? `${unitCount}${unit}` : '';
                                        return h('span', str);
                                    }
                                },
                            },
                            {
                                label: '发药科室',
                                prop: 'pharmacyInfo.name',
                                titleAlign: 'left',
                                align: 'left',
                                width: 58,
                                render(h, row) {
                                    const { name } = row.pharmacyInfo || {};
                                    return h('span', name);
                                },
                            },
                            {
                                label: '发药时间',
                                prop: 'dispensingSheet.dispensedTime',
                                titleAlign: 'left',
                                align: 'left',
                                width: 80,
                                render(h, row) {
                                    const time = row.dispensingSheet?.dispensedTime ?
                                        parseTime(row.dispensingSheet?.dispensedTime, 'm-d h:i', true) :
                                        '';
                                    return h('span', time);
                                },
                            },
                        ];
                }
            },

            prescriptionDetailList() {
                const { adviceRule } = this.detailInfo;
                const {
                    dosageCount,
                    ruleItems,
                } = adviceRule || {};

                return (ruleItems || [])
                    .filter((x) => x.goodsType === GoodsTypeEnum.MEDICINE)
                    .map((item) => {
                        const {
                            goodsId,
                            goodsName,
                            unitCount,
                            unit,
                            remark,
                            verifySignatureStatus,
                            verifySignatory,
                        } = item;

                        const unitCountStr = `${unitCount}${unit}`;
                        const dosageCountStr = `${dosageCount}剂`;
                        const itemCountStr = `${unitCount * dosageCount}${unit}`;
                        return {
                            goodsId,
                            goodsName,
                            remark,
                            dosageCountStr,
                            unitCountStr,
                            itemCountStr,
                            verifySignatory: verifySignatureStatus ? verifySignatory : {},
                        };
                    });
            },
            totalWeightInfo() {
                const { adviceRule } = this.detailInfo;
                const {
                    dosageCount,
                    ruleItems,
                } = adviceRule || {};
                const kinds = [];
                let count = 0;
                let totalCount = 0;
                let str = '';

                ruleItems
                    .filter((x) => x.goodsType === GoodsTypeEnum.MEDICINE)
                    .forEach((item) => {
                        if (item.goodsName && item.unitCount) {
                            if (item.goodsId && item.unit === 'g') {
                                count += +item.unitCount || 0;
                            }
                            if (kinds.indexOf(item.goodsName) === -1) {
                                kinds.push(item.goodsName);
                            }
                        }
                    });
                if (kinds.length) {
                    totalCount = count * (dosageCount || 0);

                    count = Number(count.toFixed(2));
                    totalCount = Number(totalCount.toFixed(2));

                    str += `共 ${dosageCount} 剂，${kinds.length} 味， 单剂 ${count}g， 总计 ${totalCount}g`;

                }
                return {
                    desc: str,
                    totalCount,
                };
            },
        },
        watch: {
            'detailInfo.patientOrderId': {
                handler(val) {
                    this.getExecutesDetails();
                    this.getAdviceStatistics(val);
                    this.getAdviceBillDetail(val);
                    this.currentTab = this.isChineseMedicalPrescription ? 4 : 0;
                },
            },
        },
        methods: {
            // 执行明细
            async getExecutesDetails() {
                try {
                    this.$emit('update:loading', true);
                    const { data } = await MedicalPrescriptionAPI.getExecutesDetails(this.id);
                    this.exeDetailList = data?.rows;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.$emit('update:loading', false);
                }
            },
            // 费用统计
            async getAdviceStatistics(patientOrderId) {
                try {
                    const { data } = await HospitalChargeAPI.getAdviceStatistics(
                        patientOrderId,
                        this.id,
                    );
                    const cacheExpenseStatList = data?.rows || [];
                    if (cacheExpenseStatList.length) {
                        cacheExpenseStatList.push({
                            name: '合计',
                            totalPrice: (data?.rows || []).reduce((prev, cur) => {
                                return prev + cur.totalPrice;
                            }, 0),
                            chargedTotalPrice: (data?.rows || []).reduce((prev, cur) => {
                                return prev + cur.chargedTotalPrice;
                            }, 0),
                        });
                    }
                    this.expenseStatList = cacheExpenseStatList;
                    this.expensePriceScopeType = data?.priceScopeType || HOSPITAL_PRICE_SCOPE_TYPE.SOURCE;
                    this.expensePriceSettleType = data?.priceSettleType || HOSPITAL_PRICE_SETTLE_TYPE.SOURCE;
                } catch (e) {
                    console.error(e);
                }
            },
            // 计费明细
            async getAdviceBillDetail(patientOrderId) {
                try {
                    const { data } = await HospitalChargeAPI.getAdviceBillDetail(
                        patientOrderId,
                        this.id,
                    );
                    this.billDetailList = data?.rows;
                    this.billDetailPriceScopeType = data?.priceScopeType || HOSPITAL_PRICE_SCOPE_TYPE.SOURCE;
                    this.billDetailPriceSettleType = data?.priceSettleType || HOSPITAL_PRICE_SETTLE_TYPE.SOURCE;
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
<style lang="scss">
@import 'src/styles/theme';
@import "src/styles/abc-common.scss";

.medical-prescription-detail-tabs {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 0;
    background-color: transparent !important;

    .medical-prescription-detail-tabs-content {
        flex: 1;
        height: 0;
        padding: 0 6px 0 16px;
        overflow-y: scroll;

        @include scrollBar();
    }

    .abc-tabs {
        background-color: transparent !important;
    }

    .abc-fixed-table {
        background-color: transparent !important;
        border: none;

        .abc-table__header-wrapper {
            background-color: transparent !important;
        }

        &.prescription-table {
            table th {
                padding-right: 20px;
                text-align: right !important;

                &:first-child,
                &:nth-child(2) {
                    text-align: left !important;
                }
            }

            tbody td {
                div.cell {
                    padding-right: 20px;
                }
            }
        }

        table th {
            padding: 0;
            font-size: 12px;
            color: #8d9aa8;
            background-color: transparent !important;
            border-top: none;
            border-right: none;
            border-bottom-style: dashed;
        }

        tbody tr {
            border-bottom: 1px dashed $P6;
        }

        tbody td {
            height: 36px !important;
            font-size: 12px;
            line-height: 36px !important;
            border-right: none;
            border-left: none;

            div.cell {
                padding: 0;
            }
        }

        .detail-table-container::after {
            border-bottom: none;
        }
    }

    .chinese-desc {
        padding-right: 24px;
        font-size: 12px;
        line-height: 36px;
        color: $T2;
        text-align: right;
        border-bottom: 1px dashed $P6;
    }
}
</style>
