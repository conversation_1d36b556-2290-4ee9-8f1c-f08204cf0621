import {
    AttributesEnum,
    ExtensionTypeEnum,
    TimeFormatEnum,
    TimeFormatRegEnum,
    WidgetTypeEnum,
} from '@abc-emr-editor/constants';
import EmrAPI from 'api/hospital/emr';
import { CATALOGUE_CATEGORY_TYPE_NAME } from 'utils/constants';
import {
    createGUID, parseTime,
} from '@/utils';
import AbcPrinter from '@/printer/index.js';
import {
    ABCPrintConfigKeyMap, PrintMode,
} from '@/printer/constants.js';
import Clone from 'utils/clone.js';
import {
    collectionField, decodeExtensionValue,
} from '@abc-emr-editor/tools';
import { getGlobalHost } from 'utils/host.js';
import { isDev } from '@/assets/configure/build-env.js';
import AbcSocket from 'views/common/single-socket.js';
import ShortUrlAPI from 'api/short-url';
import { RefKeyTypeEnum } from '@/views-hospital/medical-prescription/components/emr-editor/common/constants';
import {
    isEmptyObject, isObject,
} from 'utils/lodash';
import { injectContentToHTML } from '@/printer/utils/electron';

export const Cache = {
    globalRefKeyConfig: null,
    remoteRefKeys: {},
};

function isValidateNumber(v) {
    return /^(\d|-)*(\.\d*)?$/.test(v);
}

function expressionRunner(exp, ctx = {}) {
    const ctxWithProxy = new Proxy(ctx, {
        has: (target, prop) => {
            if (!target.hasOwnProperty(prop)) {
                throw new Error(`非法表达式 - ${prop}`);
            }
            return target.hasOwnProperty(prop);
        },
    });
    return new Function('global', `with(global){return ${exp}}`).call(ctxWithProxy, ctxWithProxy);
}

async function getGlobalRefKeyConfig() {
    const { data } = await EmrAPI.fetchRefKeyList();
    return data.list;
}

async function getRefKeyList(
    businessType,
    businessId,
    keys,
) {
    const key = JSON.stringify({
        businessType,
        businessId,
        keys,
    });
    if (Cache.remoteRefKeys[key]) {
        return Cache.remoteRefKeys[key];
    }
    const response = await EmrAPI.fetchPatientMedicalRefKeyListData(
        businessType,
        businessId,
        keys,
    );
    Cache.remoteRefKeys[key] = response;
    return response;
}

export function clearRefKeyListCache() {
    Cache.remoteRefKeys = {};
}

export function formatFieldContent(field) {
    return field.content?.map((it) => it.text).join('');
}

export function getFieldData(field, refKeyConfig, refDatas) {
    const refKey = field.attrs[AttributesEnum.RefKey];
    const currentRefKeyConfig = refKeyConfig.find((it) => it.value === refKey);
    if (!currentRefKeyConfig) {
        console.debug(`未找到refKey: [${refKey}]对应的refKeyConfig`);
        return;
    }
    let refData = refDatas[refKey];
    const refKeyType = currentRefKeyConfig.attr?.dataType ?? RefKeyTypeEnum.String;
    const isValidateData = (any) => {
        if (typeof any === 'string') {
            return !!any;
        }
        if (typeof any === 'number') {
            return !!`${any}`;
        }
        return false;
    };

    // 字符串
    if (refKeyType === RefKeyTypeEnum.String) {
        if (!isValidateData(refData)) {
            return;
        }
        // 日期处理
        if (field.attrs[AttributesEnum.WidgetType] === WidgetTypeEnum.Date) {
            let timeFormat = field.attrs[AttributesEnum.TimeFormat];
            if (!timeFormat) {
                timeFormat = TimeFormatEnum.YMD;
            }
            const timeFormatReg = TimeFormatRegEnum[timeFormat];
            refData = parseTime(new Date(refData), timeFormatReg, true);
            console.debug(`[RefKeyTypeEnum.String]: 日期数据[${refData}]`);
        }
        return `${refData}`;
    }

    // 字符串数组
    if (refKeyType === RefKeyTypeEnum.ListString) {
        if (!Array.isArray(refData)) {
            return console.debug(`[RefKeyTypeEnum.ListString]: [${refKey}]对应value不是一个数组`, refData);
        }

        // 如果没有设置索引 默认返回所有数据
        if (!field.attrs[AttributesEnum.RefKeyIndex]) {
            return `${refData.join('，')}`;
        }

        let refKeyIndex = field.attrs[AttributesEnum.RefKeyIndex];
        if (!isNaN(+refKeyIndex)) {
            refKeyIndex = Math.max(refKeyIndex - 1, 0);
        }
        const newRefData = refData[refKeyIndex] ?? '';
        if (!isValidateData(newRefData)) {
            return;
        }
        return `${newRefData}`;
    }

    // 对象
    if (refKeyType === RefKeyTypeEnum.Object) {
        if (!isObject(refData)) {
            return console.debug(`[RefKeyTypeEnum.Object]: [${refKey}]对应value不是一个对象`, refData);
        }
        // 获取属性名
        const refKeyAttr = field.attrs[AttributesEnum.RefKeyAttr];
        if (refKeyAttr) {
            if (!isValidateData(refData[refKeyAttr])) {
                return;
            }
            return `${refData[refKeyAttr]}`;
        }
        return;
    }

    // 对象数组
    if (refKeyType === RefKeyTypeEnum.ListObject) {
        if (!Array.isArray(refData)) {
            console.debug(`[RefKeyTypeEnum.ListObject]: [${refKey}]对应value不是一个数组`, refData);
            return;
        }

        // 如果没有设置索引 默认返回所有数据
        if (!field.attrs[AttributesEnum.RefKeyIndex]) {
            const refKeyAttr = field.attrs[AttributesEnum.RefKeyAttr];
            if (refKeyAttr) {
                return refData.map((it) => `${it[refKeyAttr]}`).join('，');
            }
            return;
        }

        let refKeyIndex = field.attrs[AttributesEnum.RefKeyIndex];
        if (!isNaN(+refKeyIndex)) {
            refKeyIndex = Math.max(refKeyIndex - 1, 0);
        }
        const newRefData = refData[refKeyIndex] ?? '';
        if (!isObject(newRefData)) {
            console.debug(`ListObject: [${refKey}]对应value不是一个对象`, newRefData);
            return;
        }
        // 获取属性名
        const refKeyAttr = field.attrs[AttributesEnum.RefKeyAttr];
        if (refKeyAttr) {
            if (!isValidateData(newRefData[refKeyAttr])) {
                return;
            }
            return `${newRefData[refKeyAttr]}`;
        }
    }
}

export async function mergeRemoteRefFieldsData(doc, businessType, businessId, refKeyService) {
    const fields = collectionField(doc).filter(({ attrs }) => attrs[AttributesEnum.RefKey]);
    const keys = fields.map(({ attrs }) => attrs[AttributesEnum.RefKey]);
    const { data: { refDatas } } = await (refKeyService ? refKeyService.transform(keys) : EmrAPI.fetchPatientMedicalRefKeyListData(
        businessType,
        businessId,
        keys,
    ));
    const refKeyConfig = await getGlobalRefKeyConfig();
    // 就地修改数据
    fields.forEach((field) => {
        const fieldData = getFieldData(field, refKeyConfig, refDatas);
        if (fieldData) {
            // 数字组件需要校验值
            if (field.attrs[AttributesEnum.WidgetType] === WidgetTypeEnum.Number) {
                if (!isValidateNumber(fieldData)) {
                    console.error('填入的引用数据不是合法数字: ', fieldData);
                    return;
                }
            }
            field.content = [{
                type: 'text',
                text: fieldData,
            }];
        }
    });
}

function getFieldLabel(refKeyConfig, field) {
    let label = refKeyConfig?.label;
    if (refKeyConfig?.attr) {
        const isObjectRefField = [RefKeyTypeEnum.ListObject, RefKeyTypeEnum.Object].includes(+refKeyConfig.attr.dataType);
        const name = refKeyConfig.attr?.attrMap[field.attrs[AttributesEnum.RefKeyAttr]];
        if (isObjectRefField && name) {
            label += `(${name})`;
        }
    }

    return label;
}

export async function getRemoteRefFieldsWithCache(doc, businessType, businessId, refKeyService) {
    const fields = collectionField(doc).filter(({ attrs }) => attrs[AttributesEnum.RefKey]);
    const keys = fields.map(({ attrs }) => attrs[AttributesEnum.RefKey]);
    const { data: { refDatas } } = await (refKeyService ? refKeyService.transform(keys) : getRefKeyList(businessType, businessId, keys));
    const refKeyConfig = await getGlobalRefKeyConfig();
    return fields.map((field) => {
        let fieldData = getFieldData(field, refKeyConfig, refDatas);
        if (fieldData && field.attrs[AttributesEnum.WidgetType] === WidgetTypeEnum.Number) {
            if (!isValidateNumber(fieldData)) {
                console.error('引用的远程数据不是合法数字: ', fieldData);
                fieldData = undefined;
            }
        }
        const refKey = field.attrs[AttributesEnum.RefKey];
        const currentRefKeyConfig = refKeyConfig.find((it) => it.value === refKey);
        return {
            content: fieldData,
            refKey: field.attrs[AttributesEnum.RefKey],
            refKeyIndex: field.attrs[AttributesEnum.RefKeyIndex],
            refKeyAttr: field.attrs[AttributesEnum.RefKeyAttr],
            label: getFieldLabel(currentRefKeyConfig, field),
            index: field.index,
        };
    });
}

export function getFieldKey(field) {
    return `${field.refKey}_${field.refKeyIndex}_${field.refKeyAttr}`;
}

export async function getSomeFieldByFilter(doc, filter) {
    // 收集所有强制必填field组件
    const refFields = collectionField(doc).filter(filter);
    const refKeyConfig = await getGlobalRefKeyConfig();
    return refFields.map((field) => {
        const refKey = field.attrs[AttributesEnum.RefKey];
        const currentRefKeyConfig = refKeyConfig.find((it) => it.value === refKey);
        return {
            refKey,
            refKeyIndex: field.attrs[AttributesEnum.RefKeyIndex],
            refKeyAttr: field.attrs[AttributesEnum.RefKeyAttr],
            content: formatFieldContent(field),
            index: field.index,
            label: getFieldLabel(currentRefKeyConfig, field) ?? field.attrs[AttributesEnum.Placeholder],
            id: field.attrs[AttributesEnum.ID],
            field,
        };
    });
}

export async function getEditorRefFields(doc) {
    return getSomeFieldByFilter(doc, ({ attrs }) => attrs[AttributesEnum.RefKey] && +attrs[AttributesEnum.UpdateIgnore] !== 1);
}

export async function getAllEditorRefFields(doc) {
    return getSomeFieldByFilter(doc, ({ attrs }) => attrs[AttributesEnum.RefKey]);
}

export function getEditorContentNodes(doc, callback) {
    const nodes = [];

    const walk = (node) => {
        if (callback(node)) {
            nodes.push(node);
        }
        if (node.content) {
            node.content.forEach(walk);
        }
    };
    walk(doc);
    return nodes;
}

export async function getEditorRequiredFields(doc) {
    const $fields = {};
    const allFields = await getSomeFieldByFilter(doc,() => true);
    allFields.forEach((field) => {
        $fields[field.id] = {
            content: field.content,
        };
    });
    return (await getSomeFieldByFilter(doc, ({ attrs }) => attrs[AttributesEnum.Required])).filter(({
        content,
        field,
    }) => {
        // 如果有表达式则需要判断表达式是否成立
        // 否则判断是否有内容
        const requiredExp = field.attrs[AttributesEnum.RequiredExp];
        if (requiredExp) {
            try {
                const isRequired = !!expressionRunner(requiredExp, {
                    $fields,
                });
                if (!isRequired) {
                    return false;
                }
            } catch (e) {
                // 表达式执行失败视为不必填
                return false;
            }
        }
        if (field.attrs[AttributesEnum.ExtensionType] === ExtensionTypeEnum.Sign) {
            const extensionValue = decodeExtensionValue(field.attrs[AttributesEnum.ExtensionValue]);
            if (!extensionValue) {
                return true;
            }
            return isEmptyObject(extensionValue);
        }
        return !content;
    });
}

export async function getEditorRequiredWarningFields(doc) {
    return (await getSomeFieldByFilter(doc, ({ attrs }) => attrs[AttributesEnum.RequiredWarning])).filter((it) => !it.content);
}

export async function mergeCommonTemplate(content) {
    if (Array.isArray(content)) {
        const combineIds = [];
        const waitCombineContents = [];
        content.forEach((it) => {
            if ([ExtensionTypeEnum.HEADER, ExtensionTypeEnum.CONTENT, ExtensionTypeEnum.FOOTER].includes(it.type)) {
                // 清空内容不用于保存
                combineIds.push(it.attrs[AttributesEnum.CombineId]);
                waitCombineContents.push(it);
            }
        });
        if (combineIds.length) {
            const { data = [] } = await EmrAPI.fetchCommonTemplates({
                ids: combineIds,
            });

            waitCombineContents.forEach((waitCombineContent) => {
                const commonTemplate = data.find((it) => it.id === waitCombineContent.attrs?.[AttributesEnum.CombineId]);
                const commonTemplateContent = commonTemplate?.content?.templateContent?.content;
                if (commonTemplateContent) {
                    waitCombineContent.content = commonTemplateContent;
                    console.log('母版已替换');
                } else {
                    const deleteIndex = waitCombineContents.indexOf(waitCombineContent);
                    if (deleteIndex > -1) {
                        content.splice(deleteIndex, 1);
                        console.warn('母版已删除');
                    }
                }
            });
        }
    }
}

export function alertCommonTemplatesRelations(vm, operationName = '保存', tips, next) {
    const h = vm.$createElement;

    const tipsNode = tips.map((tip) => h('div', {
        style: {
            display: 'flex',
            alignItems: 'center',
        },
    }, [h('span', {
        style: {
            color: '#7A8794',
            display: 'inline-block',
            width: '70px',
        },
        class: 'ellipsis',
    }, [CATALOGUE_CATEGORY_TYPE_NAME[tip.ownerType]]), h('span', {
        style: {
            color: '#7A8794',
            display: 'inline-block',
            width: '150px',
        },
        class: 'ellipsis',
    }, [tip.templateName]), h('span', {
        style: {
            color: '#7A8794',
            display: 'inline-block',
            width: '42px',
            marginLeft: '8px',
        },
        class: 'ellipsis',
    }, [tip.ownerName])]));

    vm.$confirm({
        type: 'warn',
        title: `确认${operationName}母版`,
        content: h('div', [h('div', {
            style: {
                fontSize: '14px',
            },
        }, `${operationName}后，以下文书使用母版的地方将同步更新！`), h('div', {
            style: {
                height: '4px',
            },
        }), ...tipsNode]),
        onConfirm: () => {
            next();
        },
    });
}

export function handlePrintEmr(
    html,
    defaultPageSizeReduce,
    defaultPageOrient,
    defaultPageSize,
    printOptions = {},
) {
    /**
     * 使用dom转canvas的方式打印文书现在还不稳定
     * 1. 有些文书打印出来的内容不全（已经发现原因是因为dom2canvas库对背景图片处理的差异）
     * 2. dom2canvas本身就会增加一些额外的任务处理工作，导致打印速度变慢
     *
     * 处理方案
     * 1. 通过localstorage的开关来控制是否使用dom2canvas
     *    当开关开启时才使用canvas2dom 这样可以尽量减少对线上存量用户的影响
     * 2. 当该实现稳定后且线上用户的使用情况也稳定后，可以考虑将dom2canvas作为默认打印方式
     * 3. 开关常量名为：useDOM2Canvas
     */
    const isUseDOM2Canvas = !!localStorage.getItem('useDOM2Canvas');
    const { isSupportMedicalDocumentContinuePrintMode } = printOptions;

    const pageExtraTemplate = isSupportMedicalDocumentContinuePrintMode ?
        '<span class="print-pagination" style="font-family: SimSun, STSong,serif;line-height: 1;position: absolute; bottom: 16px;font-size: 12px;left: 50%; transform: translateX(-50%)">第{{pageNo}}页</span>' :
        '<span class="print-pagination" style="font-family: SimSun, STSong,serif;line-height: 1;position: absolute; bottom: 16px;font-size: 12px;left: 50%; transform: translateX(-50%)">第{{pageNo}}页/共{{pageCount}}页</span>';

    const extraProps = {
        onUpdatePrintPosition: printOptions.onUpdatePrintPosition,
    };

    // position规则 top@pageIndex
    if (printOptions.defaultPosition) {
        const [top, pageIndex, printMethod] = printOptions.defaultPosition.split('@');

        extraProps.defaultPosition = {
            top: +top,
            pageIndex: +pageIndex,
            printMethod: +printMethod,
        };
    }

    if (printOptions.onUpdatePrintPosition) {
        extraProps.onUpdatePrintPosition = function({
            top, pageIndex, printMethod,
        }) {
            printOptions.onUpdatePrintPosition(`${top}@${pageIndex}@${printMethod}`);
        };
    }

    if (isUseDOM2Canvas) {
        return AbcPrinter.abcPrint({
            templateKey: window.AbcPackages.AbcTemplates.hospitalMedicalRecord,
            printConfigKey: ABCPrintConfigKeyMap.hospitalMedicalDocument,
            data: {},
            extra: {
                getHTML: () => html,
                defaultPageSizeReduce: Clone(defaultPageSizeReduce),
                defaultPageSize,
                defaultPageOrient,
                pageExtraTemplate,
                forceMultiPage: true,
                useDOM2Canvas: true,
                /**
                 * 该库现在的问题
                 * 1. 表格线很粗
                 * 2. 有背景色的多行文本会存在遮挡问题
                 * 处理方案
                 * 1. 将表格线设置为0.5px
                 * 2. 将有背景色的多行文本设置为透明背景色
                 */
                onHandleElectronOutputHTML: (html) => injectContentToHTML(html, `
                    <style type="text/css">
                        table td, table th {
                            border-width: 0.5px !important;
                        }
                        p span {
                            background-color: transparent !important;
                        }
                    </style>
                `),
                isSupportMedicalDocumentContinuePrintMode,
                ...extraProps,
                pdfOptions: printOptions.pdfOptions ? {
                    ...printOptions.pdfOptions,
                } : null,
            },
            mode: PrintMode.Electron,
            ...printOptions,
        });
    }
    return AbcPrinter.abcPrint({
        templateKey: window.AbcPackages.AbcTemplates.hospitalMedicalRecord,
        printConfigKey: ABCPrintConfigKeyMap.hospitalMedicalDocument,
        data: {},
        extra: {
            getHTML: () => html,
            defaultPageSizeReduce: Clone(defaultPageSizeReduce),
            defaultPageSize,
            defaultPageOrient,
            // 客户端输出后打印时 打印机在转换为PDF时会有一些间距上的差异
            // 根据实际情况给了一个合适的letter-spacing
            // 文本渲染这里还是有些问题 后续考虑是否用dom2canvas的方案
            // 现在先使用这个默认配置
            onHandleElectronOutputHTML: (html) => injectContentToHTML(html, `
                <style type="text/css">
                    .ProseMirror * {
                        word-spacing: -0.5px;
                        letter-spacing: -0.2px;
                    }
                </style>
            `),
            pageExtraTemplate,
            forceMultiPage: true,
            isSupportMedicalDocumentContinuePrintMode,
            ...extraProps,
            pdfOptions: printOptions?.pdfOptions ? {
                ...printOptions.pdfOptions,
            } : null,
        },
        mode: PrintMode.Electron,
        ...printOptions,
    });
}

export function handlePrintSettlementForm(html,
    defaultPageSizeReduce,
    defaultPageOrient,
    isAutoPrint) {
    return AbcPrinter.abcPrint({
        templateKey: window.AbcPackages.AbcTemplates.hospitalMedicalRecord,
        printConfigKey: ABCPrintConfigKeyMap.hospitalMedicalDocument,
        data: {},
        extra: {
            getHTML: () => html,
            defaultPageSizeReduce: Clone(defaultPageSizeReduce),
            defaultPageOrient,
            scaleFactor: 96,
        },
        mode: PrintMode.Electron,
        isAutoPrint,
    });
}

export async function handleGetSignCode(onCreateSignUrl, onSignedUrl) {
    try {
        const business = 'emr-medical-doc-sign';

        onCreateSignUrl({
            handSignPicUrl: this.userInfo.handSignPicUrl,
        });

        // 创建签名串
        const { data } = await EmrAPI.fetchEventGroupList({
            businessList: [{
                business,
                params: [createGUID()],
            }],
        });
        const { groupId } = data.rows[0];
        const fullUrl = `${getGlobalHost()}/m/signature?from=patient&groupId=${groupId}&business=${business}&region=${isDev ? 'dev.abczs.cn' : location.host}`;
        const shortUrlRes = await ShortUrlAPI.createShortUrl({
            fullUrl,
        });
        console.debug('shortUrl', shortUrlRes.data.shortUrl);
        onCreateSignUrl({
            groupId,
            business,
            handSignPicUrl: this.userInfo.handSignPicUrl,
            url: shortUrlRes.data.shortUrl,
        });
        const { socket } = AbcSocket.getSocket();
        socket.on('emr-medical-doc-sign', (res) => {
            const {
                eventGroupId,
                data: { signUrl },
            } = res;
            if (eventGroupId !== groupId) {
                return;
            }
            onSignedUrl(signUrl);
        }, groupId);
    } catch (e) {
        console.error(e);
    }
}

export function mapRefFieldsValue([ local, remote ]) {
    return {
        label: remote.label,
        localContent: local.content,
        remoteContent: remote.content ?? '-',
        checked: !!remote.content,
        index: local.index,
    };
}

export function getCreateMedicalDocumentDetail(list) {
    return list.find((it) => it.id === null);
}
