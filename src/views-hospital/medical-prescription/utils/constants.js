import { GoodsTypeIdEnum } from '@abc/constants';
import { parseTime } from '@/utils';
// 医嘱类型
export const MedicalAdviceTypeEnum = Object.freeze({
    /**
     * 一次性/临时
     */
    ONE_TIME: 0,

    /**
     * 长期
     */
    LONG_TIME: 10,

    /**
     * 出院带药
     */
    DISCHARGE_WITH_MEDICINE: 20,
});

export const MedicalAdviceTypeStr = Object.freeze({
    [MedicalAdviceTypeEnum.ONE_TIME]: '临时医嘱',
    [MedicalAdviceTypeEnum.LONG_TIME]: '长期医嘱',
    [MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE]: '出院带药',
});
export const MedicalAdviceTypeShortStr = Object.freeze({
    [MedicalAdviceTypeEnum.ONE_TIME]: '临时',
    [MedicalAdviceTypeEnum.LONG_TIME]: '长期',
    [MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE]: '带药',
});
export const MedicalAdviceTypeSelectEnum = Object.freeze([
    {
        label: '全部医嘱',
        value: -1,
    },
    {
        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.ONE_TIME],
        value: MedicalAdviceTypeEnum.ONE_TIME,
    },
    {
        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.LONG_TIME],
        value: MedicalAdviceTypeEnum.LONG_TIME,
    },
    {
        label: MedicalAdviceTypeStr[MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE],
        value: MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE,
    },
]);

export const DischargeTypeEnum = Object.freeze({
    TODAY: '今日出院',//今日
    TOMORROW: '明日出院',//明日
    DEATH: '死亡出院',// 死亡
});

export const medicalPrescriptionOptionsEnum = Object.freeze([
    {
        label: '今日出院',
        value: DischargeTypeEnum.TODAY,
    },
    {
        label: '明日出院',
        value: DischargeTypeEnum.TOMORROW,
    },
    {
        label: '死亡出院',
        value: DischargeTypeEnum.DEATH,
    },
]);

// 医嘱状态
export const MedicalAdviceStatusEnum = Object.freeze({
    /**
     * 下达/初始化
     */
    INIT: 0,
    /**
     * 已核对/待执行
     */
    CHECKED: 10,
    /**
     * 已执行
     */
    EXECUTED: 20,
    /**
     * 已停止
     */
    STOPPED: 30,
    /**
     * 停止确认
     */
    STOPPED_CONFIRM: 31,
    /**
     * 已撤销
     */
    UNDONE: 90,

    STOPPED_UNDONE: 3090, // 停止/撤销
});


// 医嘱打印状态
export const MedicalAdvicePrintStatusEnum = Object.freeze({
    /**
     * 无打印内容
     */
    NONE: -1,
    /**
     * 有打印内容但是还未打印
     */
    NOT_PRINTED: 0,
    /**
     * 部分打印
     */
    PART_PRINTED: 10,
    /**
     * 全部打印
     */
    ALL_PRINTED: 20,
});


export const MedicalAdviceStatusStr = Object.freeze({
    [MedicalAdviceStatusEnum.INIT]: '下达',
    [MedicalAdviceStatusEnum.CHECKED]: '核对',
    [MedicalAdviceStatusEnum.EXECUTED]: '执行',
    [MedicalAdviceStatusEnum.STOPPED]: '停止',
    [MedicalAdviceStatusEnum.STOPPED_CONFIRM]: '停止',
    [MedicalAdviceStatusEnum.UNDONE]: '撤销',
});

// 诊疗类型
export const TreatmentTypeEnum = Object.freeze({
    /**
     * 未知
     */
    UN_KNOW: 0,
    /**
     * 药品
     */
    MEDICINE: 1,
    /**
     * 检查
     */
    INSPECTION: 10,
    /**
     * 检验
     */
    ASSAY: 20,
    /**
     * 输血
     */
    BLOOD_TRANSFUSION: 30,
    /**
     * 会诊
     */
    DOCTORS_CONSULTATION: 40,
    /**
     * 诊断
     */
    DIAGNOSIS: 50,
    /**
     * 护理
     */
    NURSE: 60,
    /**
     * 手术
     */
    SURGERY: 70,
    /**
     * 转科
     */
    TRANSFER_DEPARTMENT: 80,
    /**
     * 出院
     */
    DISCHARGE_WITH_MEDICINE: 90,
    /**
     * 转院
     */
    TRANSFER_WITH_MEDICINE: 91,

    /**
     * 会诊
     */
    CONSULTATION: 100,
    /**
     * 物资
     */
    MATERIALS: 110,

});

export const TreatmentTypeStr = Object.freeze({
    [TreatmentTypeEnum.UN_KNOW]: '未知',
    [TreatmentTypeEnum.MEDICINE]: '药品',
    [TreatmentTypeEnum.INSPECTION]: '检查',
    [TreatmentTypeEnum.ASSAY]: '检验',
    [TreatmentTypeEnum.BLOOD_TRANSFUSION]: '输血',
    [TreatmentTypeEnum.DOCTORS_CONSULTATION]: '会诊',
    [TreatmentTypeEnum.DIAGNOSIS]: '诊断',
    [TreatmentTypeEnum.NURSE]: '护理',
    [TreatmentTypeEnum.SURGERY]: '手术',
    [TreatmentTypeEnum.TRANSFER_DEPARTMENT]: '转科',
    [TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE]: '出院',
    [TreatmentTypeEnum.TRANSFER_WITH_MEDICINE]: '转院', // 转院同出院
    [TreatmentTypeEnum.CONSULTATION]: '会诊',
    [TreatmentTypeEnum.MATERIALS]: '物资',
});
export const TreatmentTypeSelectEnum = Object.freeze([
    {
        label: '全部',
        value: -1,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.UN_KNOW],
        value: TreatmentTypeEnum.UN_KNOW,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.MEDICINE],
        value: TreatmentTypeEnum.MEDICINE,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.INSPECTION],
        value: TreatmentTypeEnum.INSPECTION,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.ASSAY],
        value: TreatmentTypeEnum.ASSAY,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.BLOOD_TRANSFUSION],
        value: TreatmentTypeEnum.BLOOD_TRANSFUSION,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.DOCTORS_CONSULTATION],
        value: TreatmentTypeEnum.DOCTORS_CONSULTATION,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.DIAGNOSIS],
        value: TreatmentTypeEnum.DIAGNOSIS,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.NURSE],
        value: TreatmentTypeEnum.NURSE,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.SURGERY],
        value: TreatmentTypeEnum.SURGERY,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.TRANSFER_DEPARTMENT],
        value: TreatmentTypeEnum.TRANSFER_DEPARTMENT,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE],
        value: TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.TRANSFER_WITH_MEDICINE],
        value: TreatmentTypeEnum.TRANSFER_WITH_MEDICINE,
    },
    {
        label: TreatmentTypeStr[TreatmentTypeEnum.CONSULTATION],
        value: TreatmentTypeEnum.CONSULTATION,
    },
]);

// 医嘱单个项目类型
export const AdviceRuleType = Object.freeze({
    /**
     * 西成药
     */
    WESTERN_MEDICINE: 0,
    /**
     * 中药饮片
     */
    CHINESE_MEDICINE_TABLETS: 10,
    /**
     * 中药颗粒
     */
    CHINESE_MEDICINE_GRANULES: 20,
    /**
     * 检查
     */
    INSPECTION: 30,
    /**
     * 检验
     */
    ASSAY: 40,
    /**
     * 护理
     */
    NURSE: 50,
    /**
     * 护理等级
     */
    NURSE_LEVEL: 51,
    /**
     * 治疗
     */
    TREATMENT: 60,
    /**
     * 理疗
     */
    PHYSIOTHERAPY: 70,
    /**
     * 转科
     */
    TRANSFER_DEPARTMENT: 80,
    /**
     * 出院
     */
    DISCHARGE_WITH_MEDICINE: 90,
    /**
     * 转院
     */
    TRANSFER_WITH_MEDICINE: 91,
    /**
     * 会诊
     */
    CONSULTATION: 100,
    /**
     * 手术医嘱
     */
    SURGERY: 110,
    /**
     * 术后医嘱
     */
    POSTOPERATIVE: 111,
    /**
     * 医疗器械
     */
    MEDICINE_MATERIAL: 120,
    /**
     * 自制成品
     */
    SELF_PRODUCT: 121,
    /**
     * 保健药品
     */
    HEALTH_MEDICINE: 122,
    /**
     * 保健食品
     */
    HEALTH_FOOD: 123,
    /**
     * 其他商品
     */
    OTHER_PRODUCT: 124,
});

/**
 * @desc 医生开医嘱区分类型
 */
export const DoctorMedicalPrescriptionTypeEnum = Object.freeze({
    NORMAL: 0, // 正常医嘱
    WRITTEN: 1, // 手写嘱托
});

/**
 * @desc 开医嘱单次剂量所选择的单位类型
 * <AUTHOR>
 * @date 2023-03-02 14:08:37
 * @params
 * @return
 */
export const SingleDosageUnitType = Object.freeze({
    PACKAGE: 0, // 包装单位
    PIECE: 1, // 制剂单位
    MEDICINE_DOSAGE: 2, // 剂量单位
    COMPONENT_CONTENT: 3, // 容量单位
});


/**
 * @desc 内置customerTypeId
 * <AUTHOR>
 * @date 2023-02-28 17:18:50
 * @params
 * @return
 */
export const CustomTypeIdEnum = {
    NURSE_NURSE_LEVEL: 100, // 护理 护理等级
    OTHER_BED_FEE: 200, // 其他费用  床位费
    TRANS_LEVEL: 300, // 转院 转院等级
};

/**
 * @desc 护理等级
 * <AUTHOR>
 * @date 2023-02-28 17:18:32
 */
export const NurseRelevantIdEnum = {
    NURSE_LEVEL_SPECIAL: 0,
    NURSE_LEVEL_ONE: 1,
    NURSE_LEVEL_TWO: 2,
    NURSE_LEVEL_THREE: 3,

};

export const GoodsTypeIdMapAdviceRuleType = Object.freeze({
    [GoodsTypeIdEnum.MEDICINE_WESTERN]: AdviceRuleType.WESTERN_MEDICINE, // 西药 -> 西成药
    [GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT]: AdviceRuleType.WESTERN_MEDICINE, // 中成药 -> 西成药
    [GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES]: AdviceRuleType.CHINESE_MEDICINE_TABLETS, // 中药饮片
    [GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE]: AdviceRuleType.CHINESE_MEDICINE_GRANULES, // 中药颗粒
    [GoodsTypeIdEnum.EXAMINATION_INSPECTION]: AdviceRuleType.INSPECTION, // 检查
    [GoodsTypeIdEnum.EXAMINATION_ASSAY]: AdviceRuleType.ASSAY, // 检验
    [GoodsTypeIdEnum.NURSE]: AdviceRuleType.NURSE, // 护理费 -> 护理
    [CustomTypeIdEnum.NURSE_NURSE_LEVEL]: AdviceRuleType.NURSE_LEVEL, // 自定义二级分类护理 -> 护理
    [GoodsTypeIdEnum.TREATMENT_TREATMENT]: AdviceRuleType.TREATMENT, // 治疗 -> 治疗
    [GoodsTypeIdEnum.TREATMENT_PHYSIOTHERAPY]: AdviceRuleType.PHYSIOTHERAPY, // 理疗 -> 理疗
    [GoodsTypeIdEnum.TRANS]: AdviceRuleType.TRANSFER_DEPARTMENT, // 转科
    [GoodsTypeIdEnum.LEAVE]: AdviceRuleType.DISCHARGE_WITH_MEDICINE, // 出院
    [CustomTypeIdEnum.TRANS_LEVEL]: AdviceRuleType.TRANSFER_WITH_MEDICINE, // 转院
    [GoodsTypeIdEnum.CONSULTATION]: AdviceRuleType.CONSULTATION, // 出院
    [GoodsTypeIdEnum.SURGERY_SURGERY]: AdviceRuleType.SURGERY, // 手术医嘱
    [GoodsTypeIdEnum.SURGERY_AFTER_SURGERY]: AdviceRuleType.POSTOPERATIVE, // 术后医嘱
    [GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL]: AdviceRuleType.MEDICINE_MATERIAL, // 医疗器械
    [GoodsTypeIdEnum.ADDITIONAL_SELF_PRODUCT]: AdviceRuleType.SELF_PRODUCT, // 自制成品
    [GoodsTypeIdEnum.ADDITIONAL_HEALTH_MEDICINE]: AdviceRuleType.HEALTH_MEDICINE, // 保健药品
    [GoodsTypeIdEnum.ADDITIONAL_HEALTH_FOOD]: AdviceRuleType.HEALTH_FOOD, // 保健食品
    [GoodsTypeIdEnum.ADDITIONAL_OTHER_PRODUCT]: AdviceRuleType.OTHER_PRODUCT, // 其他商品
    [GoodsTypeIdEnum.OTHER_GOODS]: AdviceRuleType.OTHER_PRODUCT, // 其他商品
});

export const AdviceRuleTypeMapTreatmentType = Object.freeze({
    [AdviceRuleType.WESTERN_MEDICINE]: TreatmentTypeEnum.MEDICINE,
    [AdviceRuleType.CHINESE_MEDICINE_TABLETS]: TreatmentTypeEnum.MEDICINE,
    [AdviceRuleType.CHINESE_MEDICINE_GRANULES]: TreatmentTypeEnum.MEDICINE,
    [AdviceRuleType.INSPECTION]: TreatmentTypeEnum.INSPECTION,
    [AdviceRuleType.ASSAY]: TreatmentTypeEnum.ASSAY,
    [AdviceRuleType.NURSE]: TreatmentTypeEnum.NURSE,
    [AdviceRuleType.NURSE_LEVEL]: TreatmentTypeEnum.NURSE,
    [AdviceRuleType.TREATMENT]: TreatmentTypeEnum.NURSE,
    [AdviceRuleType.PHYSIOTHERAPY]: TreatmentTypeEnum.NURSE,
    [AdviceRuleType.TRANSFER_DEPARTMENT]: TreatmentTypeEnum.TRANSFER_DEPARTMENT,
    [AdviceRuleType.DISCHARGE_WITH_MEDICINE]: TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE,
    [AdviceRuleType.TRANSFER_WITH_MEDICINE]: TreatmentTypeEnum.DISCHARGE_WITH_MEDICINE,
    [AdviceRuleType.CONSULTATION]: TreatmentTypeEnum.CONSULTATION,
    [AdviceRuleType.SURGERY]: TreatmentTypeEnum.SURGERY,
    [AdviceRuleType.POSTOPERATIVE]: TreatmentTypeEnum.SURGERY,
    [AdviceRuleType.MEDICINE_MATERIAL]: TreatmentTypeEnum.MATERIALS, // 医疗器械
    [AdviceRuleType.SELF_PRODUCT]: TreatmentTypeEnum.MATERIALS, // 自制成品
    [AdviceRuleType.HEALTH_MEDICINE]: TreatmentTypeEnum.MATERIALS, // 保健药品
    [AdviceRuleType.HEALTH_FOOD]: TreatmentTypeEnum.MATERIALS, // 保健食品
    [AdviceRuleType.OTHER_PRODUCT]: TreatmentTypeEnum.MATERIALS, // 其他商品
});


// 诊断类型
export const DiagnosisTypeEnum = Object.freeze({
    // 初步诊断
    FIRST: 0,
    // 入院诊断
    IN_HOSPITAL: 10,
    // 出院诊断
    OUT_HOSPITAL: 20,
    // 门诊
    OUTPATIENT: 30,
});


export const DiagnosisTypeTextEnum = Object.freeze({
    [DiagnosisTypeEnum.FIRST]: '初步诊断',
    [DiagnosisTypeEnum.IN_HOSPITAL]: '入院诊断',
    [DiagnosisTypeEnum.OUT_HOSPITAL]: '出院诊断',
    [DiagnosisTypeEnum.OUTPATIENT]: '门诊',
});


export const DiagnosisTypeOptionsEnum = Object.freeze([
    {
        label: '初步诊断',
        value: DiagnosisTypeEnum.FIRST,
    },
    {
        label: '入院诊断',
        value: DiagnosisTypeEnum.IN_HOSPITAL,
    },
    {
        label: '出院诊断',
        value: DiagnosisTypeEnum.OUT_HOSPITAL,
    },
    {
        label: '门诊',
        value: DiagnosisTypeEnum.OUTPATIENT,
    },
]);
export const ST_FREQ = 'st';
export const PRN_FREQ = 'prn';
export const DailyDosageList = [
    {
        id: 1,
        name: '1日1剂',
        namePY: 'meitianyiji',
        namePYFirst: 'MTYJ',
        dosageCount: 1,
    },
    {
        id: 2,
        name: '2日1剂',
        namePY: 'meiliangtianyiji',
        namePYFirst: 'MLTYJ',
        dosageCount: 0.5,
    },
    {
        id: 3,
        name: '1日半1剂',
        namePY: 'meiyitianbanyiji',
        namePYFirst: 'MYTBYJ',
        dosageCount: 0.666666, // 1 / 1.5
    },
];

export const AdviceRemarkTypeEnum = {
    Examination: 0, // 检查
    Inspect: 1, // 检验
    Treatment: 10, // 护理治疗
    Discharge: -1, // 出院
    Transfer: -2, // 转科
    Consultation: -3, //会诊
    Surgery: -4, // 手术
    Materials: -5, // 物资
};

export const AstEnum = Object.freeze({
    MIAN_SHI: 0, //免试
    PI_SHI: 1, // 皮试
    XU_YONG: 2, //续用
});

export const AdviceSocketTypeEnum = Object.freeze({
    ADVICE: 0, // 医嘱
    ADVICE_EXECUTE: 1, // 医嘱任务
});

// 医嘱打印内容类型
export const MedicalPrintContentTypeEnum = Object.freeze({
    /**
     * 执行单
     */
    EXECUTION: 0,
    /**
     * 输注单
     */
    INFUSION: 1,
    /**
     * 治疗护理单
     */
    TREATMENT_NURSE: 2,
    /**
     * 口服药单
     */
    ORAL_MEDICINE: 3,
    /**
     * 瓶贴
     */
    BOTTLE_LABEL: 4,
    /**
     * 检查申请单
     */
    INSPECTION_APPLY_SHEET: 5,
    /**
     * 检验申请单
     */
    ASSAY_APPLY_SHEET: 6,
    /**
     * 样本条码
     */
    SAMPLE_NO: 7,
    /**
     * 输液记录单
     */
    INFUSION_RECORD: 8,
});

export const AdviceExaminationExtendSpec = Object.freeze({
    // 其他检查
    OTHER: '0',
    // 检查
    DEFAULT: '10',
    // 眼科检查
    EYE: '20',
});


export const AdviceTagEnum = Object.freeze({
    SUPPLEMENT: 0, // 补录
    URGENT: 1, // 紧急
    MA_ZUI: 2, //麻醉
    JING_1: 3, // 精1
    JING_2: 4, // 精2
    DU: 5,// 毒
    OPERATE_ING: 6, // 术中
    OPERATE_AFTER: 7, // 术后
});
export const AdviceTagEnumTEXT = Object.freeze({
    [AdviceTagEnum.SUPPLEMENT]: '补',
    [AdviceTagEnum.URGENT]: '急',
    [AdviceTagEnum.OPERATE_ING]: '术中',
    [AdviceTagEnum.OPERATE_AFTER]: '术后',
});

export const AdviceTagEnumSingleText = Object.freeze({
    [AdviceTagEnum.SUPPLEMENT]: '补',
    [AdviceTagEnum.URGENT]: '急',
    [AdviceTagEnum.OPERATE_ING]: '术中',
    [AdviceTagEnum.OPERATE_AFTER]: '术后',
    [AdviceTagEnum.JING_1]: '精一',
    [AdviceTagEnum.JING_2]: '精二',
    [AdviceTagEnum.DU]: '毒',
    [AdviceTagEnum.MA_ZUI]: '麻',
});

export const AdviceTagEnumTextColor = Object.freeze({
    [AdviceTagEnum.URGENT]: '#e52d5b',// R1
    [AdviceTagEnum.SUPPLEMENT]: '#e5892d',// Y1
    [AdviceTagEnum.OPERATE_ING]: '#000000', // T1
    [AdviceTagEnum.OPERATE_AFTER]: '#000000', // T1
    [AdviceTagEnum.JING_1]: '#000000', // T1
    [AdviceTagEnum.JING_2]: '#000000', // T1
    [AdviceTagEnum.DU]: '#000000', // T1
    [AdviceTagEnum.MA_ZUI]: '#000000', // T1
});

// 会诊界面操作来源
export const OperateFromEnum = Object.freeze({
    // 医生站医嘱页面
    DOCTOR_MP: 0,
    // 医生站会诊页面
    CONSULTATION: 1,
    // 护士站医嘱页面
    NURSE_MP: 10,
});


export const AdviceDispenseType = Object.freeze({
    SINGLE: 0, // 单次发药
    ALL: 1, // 总量发药
});

// 计算总量时需要考虑首日执行次数的频率
export const FirstExecuteFreqList = Object.freeze([
    'qd',
    'bid',
    'tid',
    'qid',
    'hs',
    'qn',
    'nid',
]);

// 计算总量时频率对应每天/每周执行次数
export const ExecuteTimesByFreq = Object.freeze({
    'qd': 1,
    'bid': 2,
    'tid': 3,
    'qid': 4,
    'qod': 1,
    'qw': 1,
    'biw': 1,
    'hs': 1,
    'qn': 1,
    'qnd': 1,
    'qnw': 1,
    'nid': 1, // nid 特殊处理
});

/**
 * 获取手术申请单默认值
 */
export function getInitSurgeryReq(surgeryDate) {
    return {
        isInit: true,
        name: '',
        surgeryDepartmentId: '', // 手术科室id
        surgeryDoctorId: '', // 手术医生id
        assistantEmployeeIds: [], // 手术助手id列表
        preoperativeDiagnosis: [], // 术前诊断
        intendedSurgeries: [], // 拟施手术
        type: '', // 手术类型
        isDaytimeSurgery: 0, // 是否日间手术(患者在一个工作日内完成入院、手术和出院的一种手术模式)
        surgeryPosture: '', // 手术体位
        estimateMinutes: '', // 预计时长分钟数
        surgicalRequirements: '', // 手术需求
        // 麻醉信息
        surgeryArrangement: {
            surgeryDate: parseTime(surgeryDate ? surgeryDate : new Date(), 'y-m-d', true), // 手术日期
            operatingRoomId: '', // 手术室id
            operatingRoomName: '', // 手术室名称
            anesthesiaDoctorId: '', // 麻醉医生
            anesthesiaAssistantEmployeeIds: [], // 麻醉助手
            surgeryNurseEmployeeIds: [], // 手术护士
            circulatingNurseEmployeeIds: [], // 巡回护士
            anesthesiaMethod: '', // 麻醉方式
            asaLevel: '', // asa分级
            preoperativeFastingFlag: '', // 术前进食
        },
    };
}
