import { mapGetters } from 'vuex';
import {
    ABCPrintConfigKeyMap,
    PrintMode,
} from '@/printer/constants';
import {
    MedicalAdviceTypeEnum,
    MedicalAdviceStatusEnum, TreatmentTypeEnum,
} from '@/views-hospital/medical-prescription/utils/constants';
import AbcPrinter from '@/printer';
import { ROLE_HOSPITAL_NURSE_ID } from 'utils/constants';
import MedicalPrescriptionAPI from 'api/hospital/medical-prescription';
import PatientOrderAPI from 'api/hospital/patient-order';
import PrintAPI from 'api/hospital/print';
import { getAbcPrintOptions } from '@/printer/print-handler';
import { preProcessPrescriptionDataHandler } from '@/views-hospital/medical-prescription/utils/print-data-handler';
import PharmacyAPI from 'api/hospital/pharmacy';
import TpsAPI from 'api/tps';
import {
    GoodsSubTypeEnum, GoodsTypeEnum,
} from '@abc/constants';
import { clone } from '@/common/utils';
import {
    hospitalAdviceShandongPageLimit,
} from '@/views-hospital/settings-common/frames/print-config/hospital-medical-record/doctor-medical-prescription/constant';
const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');

export default {

    computed: {
        ...mapGetters([ 'currentClinic', 'clinicBasicConfig', 'userInfo', 'isOpenMp', 'printHospitalMedicalDocumentsConfig']),

        isNurse() {
            return this.userInfo.roleIds.includes(ROLE_HOSPITAL_NURSE_ID);
        },
    },

    methods: {
        /**
         * 获取微诊所二维码
         */
        async fetchQrCode(patientOrderId) {
            let qrcode = '';
            if (this.isOpenMp) {
                qrcode = await TpsAPI.genQrCode(patientOrderId);
            }
            return qrcode;
        },
        /**
         * 获取打印数据
         */
        async fetchPrintData(printType, params) {
            const {
                patientOrderId, adviceIds = [], reqParams = {},
            } = params;
            try {
                // 收费清单
                if (printType === '收费清单') {
                    const { data } = await PrintAPI.getFeeList(patientOrderId);
                    return {
                        data: {
                            clinicName: this.currentClinic.shortName || this.currentClinic.clinicName,
                            nationalCode: this.currentClinic.nationalCode,
                            ...data,
                        },
                    };
                }
                // 住院处方
                if (printType === '住院处方') {
                    const { data } = await PrintAPI.getPrescriptionListById({ adviceIds });
                    const printData = preProcessPrescriptionDataHandler({
                        type: 1, data: data || {},
                    });
                    const qrCode = await this.fetchQrCode(patientOrderId);
                    return {
                        data: {
                            prescriptionList: printData,
                            clinicInfo: this.clinicBasicConfig,
                            qrCode,
                        },
                    };
                }

                // 住院药房处方
                if (printType === '住院药房处方') {
                    const { data } = await PharmacyAPI.pharmacyPrescriptionPrint(reqParams);
                    const printData = preProcessPrescriptionDataHandler({
                        type: 2,
                        data,
                    });
                    // 发药单的 patientOrderId 需要从处方打印接口中获取
                    // 因为发药界面没有 patientOrderId
                    let qrCode = '';
                    const dispensingPatientOrderId = data?.rows?.[0]?.patientOrderId;
                    if (dispensingPatientOrderId) {
                        qrCode = await this.fetchQrCode(dispensingPatientOrderId);
                    }
                    return {
                        data: {
                            prescriptionList: printData,
                            clinicInfo: this.clinicBasicConfig,
                            qrCode,
                        },
                    };
                }
                // 住院药房处方
                if (printType === '床头卡') {
                    if (!patientOrderId) return;
                    const { data } = await PrintAPI.fetchBedsideCardData(patientOrderId);
                    return {
                        data,
                    };
                }

                return null;
            } catch (e) {
                console.error('获取打印数据失败\n', e);
            }
        },
        /**
         * @desc 医嘱单打印
         * <AUTHOR>
         * @date 2023-11-09 18:00:52
         */
        async handlePrintMedicalPrescription(params) {
            const {
                adviceType,
                list,
                prescriptionList = [],
                examinationList = [],
                patientOrderId,
                reqParams = {},
            } = params;

            const { default: PrintConfigDialog } = await PrintConfigDialogModule();

            // 医生站-医嘱病历-医嘱-打印设置
            if (adviceType === 'doctorPrintSetting') {
                new PrintConfigDialog({ scene: 'hospital-doctor-advice' }).generateDialogAsync({ parent: this });
                return;
            }

            // 护士站站-床位-打印设置
            if (adviceType === 'nursePrintSetting') {
                new PrintConfigDialog({ scene: 'hospital-nurse-advice' }).generateDialogAsync({ parent: this });
                return;
            }

            // 护士站站-床位-打印设置
            if (adviceType === 'nurseOutHospitalPrintConfig') {
                new PrintConfigDialog({ scene: 'hospital-nurse-out-hospital-advice' }).generateDialogAsync({ parent: this });
                return;
            }

            // 如果打印收费清单
            if (adviceType === '收费清单' || adviceType === '床头卡') {
                await AbcPrinter.abcPrint((async () => {
                    const printPropsList = [];
                    const printData = await this.fetchPrintData(adviceType, { patientOrderId });
                    const printOptions = getAbcPrintOptions(adviceType, printData?.data, printData?.extra);
                    if (printOptions) {
                        printPropsList.push({
                            ...printOptions,
                            data: printOptions.data ?? {},
                            mode: PrintMode.Electron,
                        });
                    }
                    return printPropsList;
                }));
                return;
            }

            // 打印处方
            if (adviceType === '住院处方') {
                const adviceIds = prescriptionList.map((it) => it.id);
                if (!adviceIds.length) return;

                await AbcPrinter.abcPrint((async () => {
                    const printPropsList = [];
                    const printData = await this.fetchPrintData(adviceType, {
                        adviceIds, patientOrderId,
                    });
                    const printOptions = getAbcPrintOptions(adviceType, printData?.data, printData?.extra);
                    if (printOptions) {
                        printPropsList.push({
                            ...printOptions,
                            data: printOptions.data ?? {},
                            mode: PrintMode.Electron,
                        });
                    }
                    return printPropsList;
                }));
                return;
            }

            // 打印药房处方
            if (adviceType === '住院药房处方') {
                await AbcPrinter.abcPrint((async () => {
                    const printPropsList = [];
                    const printData = await this.fetchPrintData('住院药房处方', {
                        reqParams,
                    });
                    const printOptions = getAbcPrintOptions('住院处方', printData?.data, printData?.extra);
                    if (printOptions) {
                        printPropsList.push({
                            ...printOptions,
                            data: printOptions.data ?? {},
                            mode: PrintMode.Electron,
                        });
                    }
                    return printPropsList;
                }));
                return;
            }

            // 住院信息
            const patientHospitalInfo = await this.fetchPatientInfo(patientOrderId);

            if (adviceType === '检查申请单') {
                const rows = this.getExaminationApplySheetData(examinationList, patientHospitalInfo);
                const printConfig = {
                    templateKey: window.AbcPackages.AbcTemplates?.examinationApplySheet,
                    printConfigKey: ABCPrintConfigKeyMap.examineApplySheet,
                    data: {
                        isInHospital: true,
                        rows,
                    },
                    mode: PrintMode.Electron,
                };
                AbcPrinter.abcPrint({ ...printConfig });
                return;
            }

            if (adviceType === '检验申请单') {
                const rows = this.getExaminationApplySheetData(examinationList, patientHospitalInfo);
                const printConfig = {
                    templateKey: window.AbcPackages.AbcTemplates?.examinationApplySheet,
                    printConfigKey: ABCPrintConfigKeyMap.examinationInspectApplySheet,
                    data: {
                        isInHospital: true,
                        rows,
                    },
                    mode: PrintMode.Electron,
                };
                AbcPrinter.abcPrint({ ...printConfig });
                return;
            }

            // ---- 医嘱单打印 ----
            // 山东地区医嘱单打印
            if (this.printHospitalMedicalDocumentsConfig.advice?.template === 1) {
                // ---- todo lxl 山东医嘱 接口报错 ----
                const printCategory = adviceType === MedicalAdviceTypeEnum.LONG_TIME ? 10 : 0;
                const adviceInfo = await this.fetchAdvicePrintInfoById(patientOrderId, printCategory, hospitalAdviceShandongPageLimit);
                const originAdviceGroups = clone(adviceInfo.adviceGroups || []);
                console.log('%c adviceInfo\n', 'background: green; padding: 0 5px', clone(adviceInfo));

                // ---- todo lxl 山东医嘱 暂时数据 ----
                // const adviceInfo = {
                //     adviceGroups: list.filter((it) => {
                //         if (adviceType === MedicalAdviceTypeEnum.ONE_TIME) {
                //             return it.type === MedicalAdviceTypeEnum.ONE_TIME || it.type === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE;
                //         }
                //         return it.type === adviceType;
                //     }),
                //     printPageLastRecords: [],
                //     existNeedReprintPage: 0,
                //     firstNeedReprintPageIndex: null,
                //     firstNeedReprintPageReason: null,
                // };
                // ---- todo lxl 暂时数据 ----

                await AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages?.AbcTemplates?.hospitalAdviceShandong,
                    printConfigKey: ABCPrintConfigKeyMap.hospitalAdviceShandong,
                    mode: PrintMode.Electron,
                    isPreview: true,
                    data: {
                        ...adviceInfo,
                        adviceType,
                        patientHospitalInfo,
                        clinicName: this.currentClinic.clinicName,
                        patientOrderId,
                        printCategory,
                    },
                    onPrintSuccess: (printingTask) => {
                        console.log('%c printingTask\n', 'background: green; padding: 0 5px', printingTask);
                        console.log('%c adviceInfo\n', 'background: green; padding: 0 5px', clone(adviceInfo));
                        const availableMaxPageIndex = printingTask.staticRenderConfig[0].list[0].pages.length || 1;
                        const renderConfig = printingTask.renderConfig[0];
                        const printType = renderConfig.value;
                        const printMethodConfig = renderConfig.list[renderConfig.value];
                        const { pages } = printMethodConfig;
                        const printPage = pages.filter((page) => page.value).map((page) => {
                            return {
                                pageIndex: page.page,
                                printPropertyConfig: page.printConfig,
                                adviceIds: page.list.map((it) => {
                                    return it.id;
                                }),
                            };
                        });
                        const data = {
                            printCategory,
                            printType,
                            adviceGroups: originAdviceGroups,
                            pages: printPage,
                            availableMaxPageIndex: availableMaxPageIndex - 1,
                        };
                        console.log('%c onPrintSuccess\n', 'background: green; padding: 0 5px', clone(data));
                        MedicalPrescriptionAPI.createAdvicePrintRecord({
                            patientOrderId, data,
                        });
                    },
                });

                return;
            }

            // 通用医嘱单打印
            const printConfig = {
                templateKey: window.AbcPackages.AbcTemplates?.hospitalDoctorMedicalPrescription,
                printConfigKey: ABCPrintConfigKeyMap.hospitalDoctorMedicalPrescription,
                mode: PrintMode.Electron,
                extra: {
                    forceMultiPage: true,
                },
            };

            // 医嘱列表
            let adviceList = [];
            if (list) {
                adviceList = list;
            } else {
                adviceList = await this.fetchAdviceList(patientOrderId);
            }
            const mpList = adviceList.filter((item) => {
                if (item.status === MedicalAdviceStatusEnum.UNDONE) return false;
                if (adviceType === MedicalAdviceTypeEnum.ONE_TIME) {
                    return item.type === adviceType || item.type === MedicalAdviceTypeEnum.DISCHARGE_WITH_MEDICINE;
                }
                return item.type === adviceType;
            });

            if (!mpList.length) return;

            await AbcPrinter.abcPrint({
                ...printConfig,
                data: {
                    medicalPrescriptionList: mpList,
                    medicalPrescriptionTitle: adviceType === MedicalAdviceTypeEnum.ONE_TIME ? '临时医嘱单' : '长期医嘱单',
                    medicalAdviceType: adviceType,
                    patientHospitalInfo,
                    patient: patientHospitalInfo.patient,
                    clinicName: this.currentClinic.clinicName,
                    operateNurseHandSign: this.isNurse ? this.userInfo.handSignPicUrl : '', // 护士手写签名, 当前打印的护士
                    operateNurseName: this.isNurse ? this.userInfo.name : '', // 护士签名，当前打印的护士
                },
            });
        },

        // 获取患者住院信息
        async fetchPatientInfo(id) {
            try {
                if (!id) return {};
                const { data } = await PatientOrderAPI.getPatientInHospitalInfo(id);
                return data;
            } catch (e) {
                console.error(e);
            }
        },
        // 获取患者医嘱
        async fetchAdviceList(id) {
            try {
                if (!id) return [];
                const { data } = await MedicalPrescriptionAPI.getListById(id);
                const { rows } = data;
                return rows.filter((x) => x.status !== MedicalAdviceStatusEnum.UNDONE);
            } catch (e) {
                console.error(e);
            }
        },
        async fetchAdvicePrintInfoById(patientOrderId, printCategory, printNumEachPage = hospitalAdviceShandongPageLimit) {
            try {
                if (!patientOrderId) return {};
                const { data } = await MedicalPrescriptionAPI.fetchAdvicePrintInfoById({
                    patientOrderId, printCategory, printNumEachPage,
                });
                console.log('%c fetchAdvicePrintInfoById\n', 'background: green; padding: 0 5px', clone(data));
                return data || {};
            } catch (e) {
                console.error('获取医嘱打印信息失败\n', e);
            }
        },
        getExaminationApplySheetData(list, patientHospitalInfo) {
            const applyList = list.map((l) => ({
                // 机构信息
                organPrintView: {
                    name: this.currentClinic.name,
                    qrUrl: '',
                },
                id: l.examApplySheet?.id || '',
                no: l.examApplySheet?.no || '',
                type: l.diagnosisTreatmentType === TreatmentTypeEnum.ASSAY ?
                    GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect : GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test,
                patient: patientHospitalInfo.patient,
                healthCardPayLevel: patientHospitalInfo.feeTypeName,
                healthCardNo: '',
                bedNo: patientHospitalInfo.bedNo,
                wardName: patientHospitalInfo.wardName,
                departmentName: patientHospitalInfo.departmentName,
                created: l.startTime,
                diagnosisInfos: l.examApplySheet?.diagnosisInfos || [],
                inpatientNo: patientHospitalInfo.no,
                businessFormItems: [
                    {
                        id: l.adviceGoodsItems?.[0]?.goodsId,
                        name: l.name,
                        unit: l.singleDosageUnit,
                        unitCount: l.singleDosageCount,
                        unitPrice: l.adviceGoodsItems?.[0]?.unitPrice,
                    },
                ],
                chiefComplaint: l.examApplySheet?.chiefComplaint,
                presentHistory: l.examApplySheet?.presentHistory,
                physicalExamination: l.examApplySheet?.physicalExamination,
                purpose: l.examApplySheet?.purpose,
                doctorName: l.createdByName,
                deviceType: l.examApplySheet?.deviceType,
                subType: l.examApplySheet?.subType,
            }));

            // 合并同一张申请单上的项目
            return applyList.reduce((res, item) => {
                const idx = res.findIndex((r) => r.id === item.id);
                if (idx !== -1) {
                    res[idx].businessFormItems.push(...(item.businessFormItems));
                } else {
                    res.push(item);
                }

                return res;
            }, []);
        },
    },
};
