import {
    isChineseGoods,
} from '@/views-hospital/medicine-apply/utils/handle-medicinal-list';
import clone from 'utils/clone';
import { formatDate } from '@abc/utils-date';
import {
    GoodsSubTypeEnum, GoodsTypeEnum,
} from '@abc/constants';
import { AdviceTagEnum } from '@/views-hospital/medical-prescription/utils/constants';

export function createModel() {
    return {
        shebaoPayment: {},
        patientInfo: {},
        diagnoses: [],
        chinese: [],
        infusion: [],
        western: [],
    };
}
export function createGoodsModel() {
    return {
        doctorInfo: {},
        tag: null,
        startTime: '',
        otherInfo: {},
        goods: [],
    };
}

export function isInfusion(goods) {
    const { usage = '' } = goods.usageInfo || {};
    const isInfusionList = [
        '静脉滴注', '静脉注射', '肌内注射', '腔内注射',
        '雾化吸入', '皮下注射', '皮内注射', '穴位注射',
        '直肠滴注', '局部注射', '局部麻醉', '超声透药',
        '入壶静滴', '输液冲管', '鼻饲', '膀胱给药',
        '椎管内注射',
    ];
    return isInfusionList.includes(usage);
}

const infusionUsage = ['静脉滴注', '静脉注射', '肌内注射', '腔内注射', '雾化吸入', '皮下注射', '皮内注射', '穴位注射', '直肠滴注', '椎管内注射', '局部注射', '局部麻醉', '超声透药', '入壶静滴', '输液冲管', '鼻饲', '膀胱给药'];
const adviceTagList = [AdviceTagEnum.JING_1, AdviceTagEnum.JING_2, AdviceTagEnum.DU, AdviceTagEnum.MA_ZUI];

/**
 * 获取医嘱的药品类型
 * @param advice {Object} 医嘱
 * @return {'chinese'|'infusion'|'western'}
 */
export function getGoodsTypeByAdvice(advice) {
    // 中药
    if (
        advice.advices?.[0]?.adviceGoodsItems?.[0]?.goodsType === GoodsTypeEnum.MEDICINE &&
        advice.advices?.[0]?.adviceGoodsItems?.[0]?.goodsSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine
    ) {
        return 'chinese';
    }
    // 输注
    if (infusionUsage.includes(advice.usage)) {
        return 'infusion';
    }
    // 西药
    return 'western';
}

/**
 * 处理医嘱数据
 */
export function handleAdvicePrintData(data) {
    const { rows = {} } = data;
    const res = [];
    rows.forEach((dataByPatient) => {
        const resByPatient = {
            patientInfo: dataByPatient.patientOrder,
            shebaoPayment: dataByPatient.shebaoSettleInfo,
            diagnoses: dataByPatient.diagnoses || [],
            chinese: [],
            infusion: [],
            western: [],
        };
        (dataByPatient.advices || []).forEach((dataByAdvice) => {
            const goodsType = getGoodsTypeByAdvice(dataByAdvice);
            // 医生信息
            const doctorInfo = clone(dataByAdvice);
            delete doctorInfo.advices;
            // 药品类型信息
            const tagInfo = (dataByAdvice.tags || []).find((it) => adviceTagList.includes(it.type));
            const tag = tagInfo ? tagInfo.type : null;
            // 开始时间
            const { startTime: startTimeToSecond } = dataByAdvice;
            const startTime = formatDate(startTimeToSecond, 'YYYY-MM-DD');
            // 中药不需要再细分, 直接 push 即可
            if (goodsType === 'chinese') {
                // 其他信息
                const otherInfo = clone(dataByAdvice.advices?.[0] || {});
                delete otherInfo.adviceGoodsItems;
                // 药品
                const goods = dataByAdvice.advices?.[0]?.adviceGoodsItems || [];
                resByPatient.chinese.push({
                    doctorInfo,
                    tag,
                    startTime,
                    otherInfo,
                    goods,
                });
            } else {
                // 西药或输注
                const medicineListByType = resByPatient[goodsType];
                // 其他信息
                const otherInfo = {};
                // 药品
                const goods = [];
                const advices = dataByAdvice.advices || [];
                advices.forEach((advice) => {
                    const adviceGoodsItem = advice.adviceGoodsItems?.[0] || {};
                    const goodsId = adviceGoodsItem.id || '';
                    if (goodsId) {
                        goods.push(adviceGoodsItem);
                        const cacheAdvice = clone(advice);
                        delete cacheAdvice.adviceGoodsItems;
                        otherInfo[goodsId] = {
                            ...cacheAdvice,
                            ivgtt: dataByAdvice.ivgtt,
                        };
                    }
                });
                const resMedicine = {
                    doctorInfo,
                    tag,
                    startTime,
                    otherInfo,
                    goods,
                };
                const findMedicine = medicineListByType.find((it) => {
                    return it.doctorInfo.createdBy === resMedicine.doctorInfo.createdBy &&
                        it.tag === resMedicine.tag &&
                        it.startTime === resMedicine.startTime;
                });
                if (findMedicine) {
                    findMedicine.otherInfo = {
                        ...findMedicine.otherInfo,
                        ...resMedicine.otherInfo,
                    };
                    findMedicine.goods = findMedicine.goods.concat(resMedicine.goods);
                } else {
                    medicineListByType.push(resMedicine);
                }
            }
        });
        res.push(resByPatient);
    });
    return res;
}

// 1 精麻毒
// 2 日期
// 3 医生信息
export function splitPharmacyPrintData(list) {
    // 1 精麻毒
    const splitHandle = list.map((item) => {
        return {
            ...item,
            // 拆出精麻毒的数据
            adviceTag: item.adviceTags,
            startTime: formatDate(item.created, 'YYYY-MM-DD'),
            doctorInfo: item.doctorInfo,
        };
    });

    // 创建一个空的分类结果对象
    const classifiedData = {};

    // 遍历数组中的每个对象
    for (const obj of splitHandle) {
        const {
            adviceTag, startTime, doctorInfo,
        } = obj;
        const {
            departmentId, doctorId,
        } = doctorInfo;

        // 创建分类的键，格式为 `${adviceTag}-${startTime}-${departmentId}-${doctorId}`
        const key = `${adviceTag}-${startTime}-${departmentId}-${doctorId}`;

        // 如果该键不存在，创建一个空数组作为初始值
        if (!classifiedData[key]) {
            classifiedData[key] = createGoodsModel();
            classifiedData[key].startTime = obj.startTime;
            classifiedData[key].doctorInfo = obj.doctorInfo;
            classifiedData[key].tag = obj.adviceTag;
        }

        // 将当前对象添加到对应的分类数组中
        classifiedData[key].goods.push(obj);
        classifiedData[key].otherInfo[obj.id] = obj.otherInfo;
    }

    return Object.values(classifiedData);
}

// 处理打印数据
export function handlePharmacyPrintData(data) {
    // 患者维度拆分
    return data?.rows.map((item) => {
        const handleModel = createModel();
        const chinese = []; // 中西成药
        handleModel.shebaoPayment = item.shebaoSettle || {};
        handleModel.patientInfo = item.patientOrderHospitalVO || {};
        handleModel.diagnoses = item.diagnoses || [];
        let infusionGoods = [];
        let westernGoods = [];
        // 药品维度拆分
        item.dispensingSheetList.forEach((goods) => {
            const goodsFirstItem = goods.dispensingForms[0].dispensingFormItems[0];
            // 中药只需要取第一个药做判断 中药的标签 医生 日期都是一个 所以不用拆单
            if (isChineseGoods(goodsFirstItem)) {
                const goodsModel = createGoodsModel();
                // 存入中药医生信息
                goodsModel.doctorInfo = {
                    doctorId: goods.doctorId,
                    departmentId: goods.departmentId,
                    doctorName: goods.doctorName,
                    departmentName: goods.departmentName,
                };
                const ruleItems = goods.adviceView?.adviceRules?.[0]?.ruleItems ?? [];
                // 存入中药的药品列表
                goodsModel.goods = goods.dispensingForms[0].dispensingFormItems.map((formItem) => {
                    const findRuleItem = ruleItems.find((ruleItem) => ruleItem.goodsId === formItem.productId);
                    const {
                        verifySignatureStatus, verifySignatory,
                    } = findRuleItem ?? {};
                    return {
                        ...formItem,
                        verifySignatureStatus,
                        verifySignatory,
                    };
                });
                // 取加急补录精麻标签 adviceTags未必有 需要保护下
                goodsModel.tag = goods.dispensingForms[0].usageInfo?.adviceTags?.find((i) => {
                    return adviceTagList.includes(i.type);
                })?.type || null;
                goodsModel.startTime = formatDate(goodsFirstItem.created, 'YYYY-MM-DD');
                // 存入其他信息 主要是使用方法
                goodsModel.otherInfo = {
                    ...goods.dispensingForms[0].usageInfo,
                };
                // 存入中药中
                chinese.push(goodsModel);
                // 输注和西药
            } else {
                // 拆分出西药和输注
                // 同组的药在一起 同类型的药放在一起
                if (isInfusion(goods.dispensingForms[0])) {
                    infusionGoods = infusionGoods.concat(goods.dispensingForms[0].dispensingFormItems.map((i) => {
                        return {
                            ...i,
                            doctorInfo: {
                                doctorId: goods.doctorId,
                                departmentId: goods.departmentId,
                                doctorName: goods.doctorName,
                                departmentName: goods.departmentName,
                            },
                            adviceTags: goods.dispensingForms[0].usageInfo?.adviceTags?.find((i) => {
                                return adviceTagList.includes(i.type);
                            })?.type || null,
                            otherInfo: {
                                ...goods.dispensingForms[0].usageInfo,
                                groupId: goods.groupId,
                                days: goods.adviceView.adviceRules[0].days,
                            },
                        };
                    }));
                } else {
                    westernGoods = westernGoods.concat(goods.dispensingForms[0].dispensingFormItems.map((i) => {
                        return {
                            ...i,
                            doctorInfo: {
                                doctorId: goods.doctorId,
                                departmentId: goods.departmentId,
                                doctorName: goods.doctorName,
                                departmentName: goods.departmentName,
                            },
                            adviceTags: goods.dispensingForms[0].usageInfo?.adviceTags?.find((i) => {
                                return adviceTagList.includes(i.type);
                            })?.type || null,
                            otherInfo: {
                                ...goods.dispensingForms[0].usageInfo,
                                groupId: goods.groupId,
                                days: goods.adviceView.adviceRules[0].days,
                            },
                        };
                    }));
                }
            }
        });
        // 拆分筛选出来的西药和输注单子
        // 1 精麻毒
        // 2 日期
        // 3 医生信息
        handleModel.chinese = chinese;
        handleModel.infusion = splitPharmacyPrintData(infusionGoods, item);
        handleModel.western = splitPharmacyPrintData(westernGoods, item);
        return handleModel;
    });
}

/**
 * 住院处方数据预处理函数
 * @param params {Object}
 * @param params.type {number} 处方数据来源 1:医嘱 2:药房
 * @param params.data {Object} 处方数据
 */
export function preProcessPrescriptionDataHandler(params) {
    const {
        type, data,
    } = params;

    // 医嘱
    if (type === 1) {
        return handleAdvicePrintData(data);
    }

    // 药房
    if (type === 2) {
        // todo
        return handlePharmacyPrintData(data);
    }
    return data;
}
