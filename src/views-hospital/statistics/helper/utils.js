import { clone } from '@abc/utils';

export function resolveToFilterParams(featureConfigs = []) {
    return featureConfigs.reduce((acc, {
        valueKey, initData,
    }) => ({
        ...acc, [valueKey]: initData,
    }), {});
}

export function transTemplateToTotalInfo(data = [], template = '') {
    if (!template) return '';
    let index = 0;
    return template.replace(
        RegExp('%s', 'g'),
        () => {
            return `<span style="color: #000; font-weight: bold">${data[index++]}</span>`;
        },
    );
}

function normalizeHeader(headers = []) {
    return headers?.map((item) => {
        item.children = item.columnChildren;
        if (item?.columnChildren?.length) {
            item.children = normalizeHeader(item.columnChildren);
        }
        return {
            ...item,
        };
    });
}

// 匹配相邻的数组中连续相同的值出现的次数
function countAdjacent(arr, value, key) {
    // 找出所有匹配的索引
    const allIndices = [];
    for (let i = 0; i < arr.length; i++) {
        if (arr[i][key] === value) {
            allIndices.push(i);
        }
    }

    if (allIndices.length === 0) {
        return null;
    }

    // 按照连续索引分组
    const groups = [];
    let currentGroup = [allIndices[0]];

    for (let i = 1; i < allIndices.length; i++) {
        // 如果当前索引与前一个索引连续，则添加到当前组
        if (allIndices[i] === allIndices[i - 1] + 1) {
            currentGroup.push(allIndices[i]);
        } else {
            // 否则创建新组
            groups.push([...currentGroup]);
            currentGroup = [allIndices[i]];
        }
    }

    // 添加最后一个组
    if (currentGroup.length > 0) {
        groups.push([...currentGroup]);
    }

    return {
        allGroups: groups,
        allIndices,
    };
}

// 计算行合并
function calcRowSpan(tableData, trData, item) {
    const findIndex = tableData.findIndex((obj) =>
        Object.keys(trData).every((key) => obj[key] === trData[key]),
    );

    if (item.groupBy) {
        // 获取所有分组信息
        const groupInfo = countAdjacent(tableData, trData[item.groupBy], item.groupBy);

        if (!groupInfo) {
            return 1;
        }

        const {
            allGroups, allIndices,
        } = groupInfo;

        // 如果当前行不在任何组中，返回1
        if (!allIndices.includes(findIndex)) {
            return 1;
        }

        // 找到当前行所在的组
        let currentGroup = null;
        for (const group of allGroups) {
            if (group.includes(findIndex)) {
                currentGroup = group;
                break;
            }
        }

        if (!currentGroup) {
            return 1;
        }

        // 如果是组中的第一行，返回组的长度，否则返回0
        return currentGroup[0] === findIndex ? currentGroup.length : 0;
    }

    return 1;
}

export function resolveRenderConfig(
    header = [],
    renderTypeList = {},
    staticConfig = {},
    renderTypeMap = {},
    headerRenderTypeMap = {},
    headerAppendRenderTypeMap = {},
    descriptionMap = {},
    tableData = [],
) {
    const newHeaders = normalizeHeader(header).filter((item) => (item.prop !== '#others')); //#others列是代表其他列，用在打印分割
    function processHeaderItem(item, _staticConfig) {
        const {
            prop, isFixed, position, children, sortable, type,
        } = item;
        let propConfig = clone(_staticConfig.list.find((config) => config.key === prop)) || clone(_staticConfig.customPropConfig);

        if (!propConfig) {
            console.error('propConfig is not configured', prop);
            propConfig = {
                label: item.label,
                key: item.prop,
                colType: item.type,
                style: {
                    'flex': 1,
                    'width': '',
                    'maxWidth': '',
                    'minWidth': `${item.width}px`,
                    'paddingLeft': '',
                    'paddingRight': '',
                    'textAlign': item.align,
                },
                children: item.columnChildren,
            };
        }

        const {
            isRandomCol = false, commonStyle = {}, commonColType , pinned, headerStyle = {},
        } = propConfig;
        propConfig.pinned = pinned ?? !!isFixed;
        propConfig.position = propConfig.position || position;
        propConfig.colType = propConfig.colType || type;
        propConfig.sortable = !!sortable;
        if (_staticConfig.customPropConfig) {
            propConfig.label = propConfig.label ?? item.label;
            propConfig.key = propConfig.key ?? item.prop;
        }
        if (descriptionMap[prop]) {
            propConfig.descriptionRender = renderTypeList[descriptionMap[prop]];
        }
        if (renderTypeMap[prop]) {
            propConfig.customRender = renderTypeList[renderTypeMap[prop]];
        }
        if (headerRenderTypeMap[prop]) {
            propConfig.headerRender = renderTypeList[headerRenderTypeMap[prop]];
        }
        if (headerAppendRenderTypeMap[prop]) {
            propConfig.headerAppendRender = renderTypeList[headerAppendRenderTypeMap[prop]];
        }
        propConfig.rowSpan = tableData.length ? (trData) => {
            return calcRowSpan(tableData, trData, item);
        } : null;
        if (children && children.length) {
            if (isRandomCol) {
                const childStaticConfig = children.map((config) => {
                    return ({
                        children: config.columnChildren,
                        key: config.prop,
                        label: config.label,
                        style: config.columnChildren ? { 'textAlign': 'center' } : commonStyle,
                        colType: commonColType ?? config.type,
                        isRandomCol,
                        commonStyle,
                        commonColType,
                        headerStyle,
                    });
                });
                propConfig.children = children.map((_item) => processHeaderItem(_item, { list: childStaticConfig }));
            } else {
                propConfig.children = children.map((_item) => processHeaderItem(_item, { list: propConfig.children }));
            }
        }
        return propConfig;
    }

    const processedHeaders = newHeaders.map((item) => processHeaderItem(item, staticConfig)).filter(Boolean).sort((a, b) => a.position - b.position);

    return {
        ...staticConfig,
        list: processedHeaders,
    };
}
