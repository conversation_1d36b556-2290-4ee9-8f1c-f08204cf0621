<!--医保-青岛-->
<template>
    <table class="custom-report-table" data-type="mix-box">
        <tbody>
            <tr data-type="item">
                <td colspan="6" class="title-td">
                    {{ name }}
                </td>
            </tr>
            <tr>
                <td colspan="1" rowspan="2">
                    普通门诊
                </td>
                <td colspan="1">
                    医保支付
                </td>
                <td colspan="1" :title="dataSource.normalOutpatient?.receivedFee || 0" style="text-align: right;">
                    {{ dataSource.normalOutpatient?.receivedFee || 0 }}
                </td>
                <td colspan="1" rowspan="2">
                    大病门诊
                </td>
                <td colspan="1">
                    医保支付
                </td>
                <td colspan="1" :title="dataSource.seriousIllnessOutpatient?.receivedFee || 0" style="text-align: right;">
                    {{ dataSource.seriousIllnessOutpatient?.receivedFee || 0 }}
                </td>
            </tr>
            <tr>
                <td colspan="1">
                    个人负担
                </td>
                <td colspan="1" :title="dataSource.normalOutpatient?.personalBurden || 0" style="text-align: right;">
                    {{ dataSource.normalOutpatient?.personalBurden || 0 }}
                </td>
                <td colspan="1">
                    个人负担
                </td>
                <td colspan="1" :title="dataSource.seriousIllnessOutpatient?.personalBurden || 0" style="text-align: right;">
                    {{ dataSource.seriousIllnessOutpatient?.personalBurden || 0 }}
                </td>
            </tr>
            <tr>
                <td colspan="1" rowspan="2">
                    长期护理
                </td>
                <td colspan="1">
                    医保支付
                </td>
                <td colspan="1" :title="dataSource.longCare?.receivedFee || 0" style="text-align: right;">
                    {{ dataSource.longCare?.receivedFee || 0 }}
                </td>
                <td colspan="1"></td>
                <td colspan="1"></td>
                <td colspan="1"></td>
            </tr>
            <tr>
                <td colspan="1">
                    个人负担
                </td>
                <td colspan="1" :title="dataSource.longCare?.personalBurden || 0" style="text-align: right;">
                    {{ dataSource.longCare?.personalBurden || 0 }}
                </td>
                <td colspan="1"></td>
                <td colspan="1"></td>
                <td colspan="1"></td>
            </tr>
        </tbody>
    </table>
</template>

<script>
    export default {
        name: 'SheBaoQingDao',
        props: {
            detail: {
                type: Object,
                default: () => ({}),
            },
        },
        computed: {
            dataSource() {
                return this.detail?.value || {};
            },
            name() {
                return this.detail?.name || '';
            },
        },
    };
</script>

