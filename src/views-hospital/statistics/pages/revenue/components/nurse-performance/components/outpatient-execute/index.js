import { isEqual } from 'utils/lodash';
import usePermission from '@/views-hospital/statistics/hooks/usePermission';
import { resolveRenderConfig } from '@/views-hospital/statistics/helper/utils';
import ExportService from 'views/statistics/core/services/export/export-service';
import StatBaseTable from '@/views-hospital/statistics/helper/stat-base-table';
import {
    DIMENSION_ENUM, dimensionLabelEnum, dimensionApiEnum,
} from '@/views-hospital/statistics/pages/revenue/components/nurse-performance/components/outpatient-execute/constant';
import PerformanceStatAPI from 'views/statistics/core/api/performance-stat.js';
import {
    PersonnelTableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/nurse-performance/components/outpatient-execute/personnel';
import PerformanceAPI from 'views/statistics/core/api/performance';
import {
    ProjectTableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/nurse-performance/components/outpatient-execute/project';
import {
    OrderTableConfig,
} from '@/views-hospital/statistics/pages/revenue/components/nurse-performance/components/outpatient-execute/order';
import Selection<PERSON><PERSON> from '@/views-hospital/statistics/api/selection';
import {
    GoodsSubTypeEnum, GoodsTypeEnum,
} from '@abc/constants/src';

// 门诊执行
export default class OutpatientExecuteTable extends StatBaseTable {
    constructor(view) {
        super(view);
        this.dimension = DIMENSION_ENUM.PERSONNEL;

        this.employeeList = [];
        this.departmentList = [];
        // key与renderType的映射关系
        this.renderTypeMap = {

        };
        // key与headerRenderType的映射关系
        this.headerRenderTypeMap = {

        };
        // key与headerAppendRenderType的映射关系
        this.headerAppendRenderTypeMap = {

        };
    }

    async init() {
        this.initFeatureConfig();
    }

    initFeatureConfig() {
        this.view.setTableCustomConfig(this.createFeatureConfigs(), true);
        this.initFilterOptions();
    }
    async initFilterOptions() {
        this.refreshFeatureConfig();
    }

    async refreshFeatureConfig() {
        const fetchEmployeeSelection = this.fetchEmployeeSelection();
        const fetchClinicDepartments = this.fetchClinicDepartments();
        const mapping = {
            [DIMENSION_ENUM.PERSONNEL]: [fetchEmployeeSelection, fetchClinicDepartments],
            [DIMENSION_ENUM.PROJECT]: [fetchEmployeeSelection, fetchClinicDepartments],
            [DIMENSION_ENUM.ORDER]: [fetchEmployeeSelection, fetchClinicDepartments],
        };
        await Promise.all(mapping[this.dimension]);
        this.view.setTableCustomConfig(this.createFeatureConfigs());
    }

    createRenderList() {
        return {};
    }

    createStaticConfig() {
        const mapping = {
            [DIMENSION_ENUM.PERSONNEL]: PersonnelTableConfig,
            [DIMENSION_ENUM.PROJECT]: ProjectTableConfig,
            [DIMENSION_ENUM.ORDER]: OrderTableConfig,
        };
        return mapping[this.dimension];
    }

    createDescriptionMap() {
        return {};
    }

    createFeatureConfigs() {
        const { permission } = usePermission('isChainAdmin', 'subClinics');
        const { subClinics } = permission;
        return {
            exportTaskType: 'achievement-execute',
            tableKey: `outpatient-execute-${this.dimension}`,
            isEnablePagination: true,
            filterTools: [
                {
                    type: 'datePicker',
                    valueKey: 'dateFilter',
                    initData: this.view.comDateFilter,
                },
                {
                    type: 'select',
                    valueKey: 'clinicId',
                    clearable: true,
                    placeholder: '门店',
                    width: 120,
                    options: subClinics,
                    isHidden: !permission.isChainAdmin,
                },
                {
                    type: 'productSelector',
                    valueKey: 'productId',
                    placeholder: '搜索医嘱',
                    productTypes: [
                        {
                            type: GoodsTypeEnum.TREATMENT,
                        },
                        {
                            type: GoodsTypeEnum.MEDICINE,
                            subType: [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM],
                        },
                        {
                            type: GoodsTypeEnum.GOODS,
                            subType: [GoodsSubTypeEnum[GoodsTypeEnum.GOODS].ManufacturedGoods],
                        },
                    ],
                    isHidden: this.dimension !== DIMENSION_ENUM.ORDER,
                },
                {
                    type: 'select',
                    valueKey: 'employeeId',
                    clearable: true,
                    placeholder: '执行人',
                    width: 120,
                    options: this.employeeList,
                },
                {
                    type: 'select',
                    valueKey: 'departmentId',
                    clearable: true,
                    placeholder: '开单科室',
                    withSearch: true,
                    width: 120,
                    options: this.departmentList,
                },
            ].filter((item) => !item.isHidden),
            dimensions: {
                options: [
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.PERSONNEL],
                        value: DIMENSION_ENUM.PERSONNEL,
                    },
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.PROJECT],
                        value: DIMENSION_ENUM.PROJECT,
                    },
                    {
                        label: dimensionLabelEnum[DIMENSION_ENUM.ORDER],
                        value: DIMENSION_ENUM.ORDER,
                    },
                ],
                curDimension: this.dimension,
            },
        };
    }

    createFetchParams() {
        const {
            begin, end,
        } = this.view.filterParams.dateFilter;
        const {
            employeeId, departmentId, productId,
        } = this.view.filterParams;
        const { permission: PermissionParams } = usePermission('enablePatientMobile');
        const baseParams = {
            beginDate: begin,
            endDate: end,
            offset: this.view.pageParams.offset,
            size: this.view.pageParams.limit,
            clinicId: this.queryClinicId(),
            employeeId,
            departmentId,
            ...PermissionParams,
        };
        if (this.dimension === DIMENSION_ENUM.ORDER) {
            return {
                ...baseParams,
                productId,
            };
        }
        return baseParams;
    }

    async loadTableData() {
        const beforeParams = this.createFetchParams();
        this.loading = true;
        try {
            const { data } = await PerformanceStatAPI.execute[dimensionApiEnum[this.dimension]]({
                ...beforeParams,
            });
            const afterParams = this.createFetchParams();
            if (isEqual(beforeParams, afterParams)) {
                // 项目不需要汇总
                if (this.dimension === DIMENSION_ENUM.PROJECT) {
                    data.summary = null;
                }
                this.setTableInfo({
                    tableInfo: data,
                });
            }
        } catch (e) {
            console.log(e);
            this.setTableInfo({ isClear: true });
        } finally {
            this.loading = false;
        }
    }

    // abc-table组件的配置
    createRenderConfig() {
        const header = this.table?.tableHeader ?? [];

        const renderTypeList = this.createRenderList();
        const staticConfig = this.createStaticConfig();
        const descriptionMap = this.createDescriptionMap();
        this.tableRenderConfig = resolveRenderConfig(header, renderTypeList, staticConfig,
            this.renderTypeMap, this.headerRenderTypeMap, this.headerAppendRenderTypeMap, descriptionMap);
    }

    async fetchEmployeeSelection() {
        const {
            beginDate, endDate, clinicId,
        } = this.createFetchParams();
        try {
            const { data } = await PerformanceAPI.execute.selectOptions({
                beginDate,
                endDate,
                clinicId,
            });
            this.employeeList = (data?.employees ?? []).map((item) => ({
                label: item.name,
                value: item.id,
            }));
        } catch (e) {
            console.log(e);
            this.employeeList = [];
        }
    }

    async fetchClinicDepartments() {
        const {
            beginDate, endDate, clinicId,
        } = this.createFetchParams();
        try {
            const { data } = await SelectionAPI.department.getExecuteDepartmentSelection({
                scope: 1,
                beginDate,
                endDate,
                clinicId,
            });
            this.departmentList = data?.map((item) => {
                return {
                    label: item.name,
                    value: item.id,
                };
            });
        } catch (e) {
            console.log(e);
            this.departmentList = [];
        }
    }


    async export() {
        this.exportService = new ExportService();
        const params = this.createFetchParams();
        try {
            await this.exportService.startExport(this.view.tableCustomConfig?.exportTaskType, {
                ...params,
            });
        } catch (e) {
            console.error(e);
            return false;
        }
        return true;
    }

    setDimension(val) {
        this.dimension = val;
        this.view.setTableCustomConfig(this.createFeatureConfigs(), true);
    }
}
