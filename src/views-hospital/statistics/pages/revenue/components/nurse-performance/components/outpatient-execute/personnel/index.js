
export const PersonnelTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    'list': [{
        'label': '执行门店',
        'key': 'executeClinicName',
        'pinned': false,
        'position': 1,
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
        'testValue': '执行门店',
    },{
        'label': '执行人',
        'key': 'executorName',
        'pinned': false,
        'position': 2,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
        'testValue': '李思思',
    },{
        'label': '执行人数',
        'key': 'patientCount',
        'pinned': false,
        'position': 3,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'description': '执行项目对应的患者人数（当天同一患者去重）',
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'testValue': '李思思',
    },{
        'label': '执行次数',
        'key': 'executeCount',
        'pinned': false,
        'position': 6,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'testValue': '3',
    },{
        'label': '项目原价',
        'key': 'originalFee',
        'colType': 'money',
        'pinned': false,
        'position': 7,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '项目实收',
        'key': 'receivedFee',
        'colType': 'money',
        'description': '包含收费、欠费、不含还款',
        'pinned': false,
        'position': 8,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    }],
});
