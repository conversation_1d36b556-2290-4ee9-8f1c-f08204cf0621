export const PersonnelTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    'list': [{
        'label': '检验人',
        'key': 'testerName',
        'pinned': false,
        'position': 1,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '86px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'left',
        },
    }, {
        'label': '检验项目',
        'key': 'goodsName',
        'pinned': false,
        'position': 2,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '248px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'left',
        },
    }, {
        'label': '二级分类',
        'key': 'subTypeName',
        'pinned': false,
        'position': 4,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'center',
        },
    }, {
        'label': '次数',
        'key': 'countNum',
        'pinned': false,
        'position': 5,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }, {
        'label': '金额',
        'key': 'amountNum',
        'colType': 'money',
        'pinned': false,
        'position': 6,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }, {
        'label': '成本',
        'key': 'costPrice',
        'colType': 'money',
        'pinned': false,
        'position': 7,
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1, 'width': '', 'maxWidth': '', 'minWidth': '100px', 'paddingLeft': '', 'paddingRight': '', 'textAlign': 'right',
        },
    }],
});
