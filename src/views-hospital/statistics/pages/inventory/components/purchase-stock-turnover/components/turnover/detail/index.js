export const DetailTableConfig = Object.freeze({
    'hasInnerBorder': true,
    'hasHeaderBorder': true,
    'list': [{
        'label': '时间',
        'key': 'createDate',
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
        'headerStyle': {
            'textAlign': 'center',
        },
        'sortable': false,
    },{
        'label': '库存变更数(按大包装)',
        'key': 'actionCount',
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'sortable': false,
    },{
        'label': '库存变更数(按小包装)',
        'key': 'actionConvertPieceCount',
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
        'sortable': false,
    },{
        'label': '门店',
        'key': 'organText',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '库房',
        'key': 'pharmacyName',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '动作',
        'key': 'action',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '商品备注',
        'key': 'remark',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '210px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '药品名称',
        'key': 'goodsText',
        'sortable': false,
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '248px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '药品编码',
        'key': 'goodsShortId',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '一级分类',
        'key': 'feeType1Text',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '二级分类',
        'key': 'feeType2Text',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '费用类型',
        'key': 'feeTypeName',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '规格',
        'key': 'specification',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '156px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '厂家',
        'key': 'manufacturer',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '包装单位',
        'key': 'packageUnit',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '110px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '最小包装单位',
        'key': 'minPieceUnit',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '110px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '生产批号',
        'key': 'batchNo',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '效期',
        'key': 'expiryDate',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '104px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '进价',
        'key': 'inPrice',
        'colType': 'money',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '售价',
        'key': 'outPrice',
        'colType': 'money',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '原始库存',
        'key': 'beforeCountText',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '库存变更',
        'key': 'actionCountText',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '变更总进价',
        'key': 'actionCost',
        'colType': 'money',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '变更总售价',
        'key': 'actionPrice',
        'description': '部分住院药品未结算',
        'colType': 'money',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'right',
        },
    },{
        'label': '结余库存',
        'key': 'afterCountText',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '追溯码',
        'key': 'traceableCodeCountStr',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '140px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '操作人',
        'key': 'operatorText',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '患者',
        'key': 'patientName',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '86px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '医保码',
        'key': 'sheBaoCodeNationalCode',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '进项税',
        'key': 'inTaxRatText',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '90px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '国药准字',
        'key': 'nmpn',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '110px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '供应商',
        'key': 'supplierText',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '160px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '生产日期',
        'key': 'productionDate',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '110px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '计量单位',
        'key': 'pieceUnit',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '90px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '销项税',
        'key': 'outTaxRatText',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '90px','paddingLeft': '','paddingRight': '','textAlign': 'center',
        },
    },{
        'label': '备注',
        'key': 'comment',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '90px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '诊号',
        'key': 'patientOrderNo',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '100px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '批次id',
        'key': 'batchId',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    },{
        'label': '就诊单id',
        'key': 'patientOrderId',
        'sortable': false,
        'headerStyle': {
            'textAlign': 'center',
        },
        'style': {
            'flex': 1,'width': '','maxWidth': '','minWidth': '120px','paddingLeft': '','paddingRight': '','textAlign': 'left',
        },
    }],
});

export const DetailRenderTypeMap = Object.freeze({
    traceableCodeCountStr: 'traceableCodeCountStrRender',
});
