<template>
    <div>
        <abc-dialog
            v-if="showDialog"
            ref="detailDialog"
            v-model="showDialog"
            class="patient-info-detail-wrapper"
            title="''"
            :auto-focus="false"
            append-to-body
            :content-styles="contentStyle"
            header-style="padding-top:0px;padding-bottom:0px;"
        >
            <abc-tabs
                slot="title"
                v-model="currentTabIndex"
                class="tabs-wrapper"
                size="middle"
                :disable-indicator="displayTabOptions && displayTabOptions.length === 1"
                :option="displayTabOptions"
            ></abc-tabs>

            <template v-if="currentTabIndex === 0">
                <div v-abc-loading:page="loading">
                    <div v-if="hospitalStatusTips" class="hospital-status-tips">
                        <abc-space>
                            <abc-icon icon="info" :size="14" color="#ced0da">
                            </abc-icon>
                            <span>
                                {{ hospitalStatusTips }}
                            </span>
                        </abc-space>
                    </div>
                    <div v-if="bedId" class="print-btn-group">
                        <social-in-hospital
                            v-if="$abcSocialSecurity.isOpenSocial && curPatientOrderId && bedInfo?.patientOrderHospital?.status"
                            :patient-order-id="curPatientOrderId"
                            :hospital-status="bedInfo.patientOrderHospital.status"
                            :parent-component-class="'.patient-info-detail-wrapper .abc-dialog'"
                        >
                        </social-in-hospital>
                        <template v-if="false">
                            <abc-button type="blank">
                                床头卡打印
                            </abc-button>
                            <abc-button type="blank">
                                腕带打印
                            </abc-button>
                        </template>
                        <abc-button
                            type="danger"
                            @click="checkPatientInHospitalStatus"
                        >
                            退院
                        </abc-button>
                        <abc-button type="blank" @click="showChangeBedDialog = true">
                            换床
                        </abc-button>
                        <abc-dropdown
                            style="width: auto; margin-left: 8px;"
                            @change="val => handlePrintMedicalPrescription({
                                adviceType: val,
                                patientOrderId: curPatientOrderId,
                            })"
                        >
                            <abc-button
                                slot="reference"
                                type="blank"
                            >
                                打印
                            </abc-button>
                            <abc-dropdown-item
                                label="临时"
                                :value="MedicalAdviceTypeEnum.ONE_TIME"
                            >
                                临时医嘱单
                            </abc-dropdown-item>
                            <abc-dropdown-item
                                label="长期"
                                :value="MedicalAdviceTypeEnum.LONG_TIME"
                            >
                                长期医嘱单
                            </abc-dropdown-item>
                            <abc-dropdown-item
                                label="床头卡"
                                value="床头卡"
                            >
                                床头卡
                            </abc-dropdown-item>
                            <template>
                                <abc-dropdown-item
                                    disabled
                                    label=""
                                    value=""
                                    style="width: 100%; height: 0; min-height: 0 !important; padding: 0 !important; cursor: default; border-top: 1px solid #e6eaee;"
                                ></abc-dropdown-item>
                                <abc-dropdown-item label="设置..." value="nursePrintSetting"></abc-dropdown-item>
                            </template>
                        </abc-dropdown>
                    </div>
                    <patient-hospital-info
                        ref="patientHospitalInfo"
                        :new-add="newAdd"
                        :form-source="formSource"
                        :loading.sync="loading"
                        :btn-loading.sync="btnLoading"
                        :patient-order-id="curPatientOrderId"
                        :bed-id="bedId"
                        :table-column="tableColumn"
                        :patient-id="patientId"
                        :disabled="disabled"
                        :current-patient-hospital-status.sync="hospitalStatus"
                        :need-edit="isHospitalProcess"
                        :outpatient-doctor-id="outpatientDoctorId"
                        :extend-diagnosis-infos="extendDiagnosisInfos"
                        :shebao-card-info="shebaoCardInfo"
                        :btn-disabled.sync="btnDisabled"
                        :allow-edit="allowEdit"
                        @add-success="handleSuccess"
                    ></patient-hospital-info>
                </div>
                <div v-if="!curPatientOrderId" slot="footer" class="dialog-footer patient-info-footer">
                    <abc-button
                        v-if="!isHospitalProcess || (isHospitalProcess && allowEdit)"
                        type="primary"
                        :loading="btnLoading"
                        :disabled="isHospitalProcess && btnDisabled"
                        @click="handleFinish"
                    >
                        {{ btnText }}
                    </abc-button>
                    <abc-button
                        v-else-if="isHospitalProcess && !allowEdit"
                        type="primary"
                        @click="allowEdit = true"
                    >
                        修改
                    </abc-button>
                    <abc-button type="blank" @click="handleCancel">
                        取消
                    </abc-button>
                </div>
            </template>

            <template v-else-if="currentTabIndex === 1">
                <medical-prescription
                    :patient-order-ids="[curPatientOrderId]"
                    :advice-status-list="[MedicalAdviceStatusEnum.INIT]"
                    :current-tab="MedicalAdviceStatusEnum.CHECKED"
                    :fill-reference-el="fillReferenceEl"
                ></medical-prescription>
            </template>

            <template v-else-if="currentTabIndex === 2">
                <nurse-execute
                    :patient-order-ids="[curPatientOrderId]"
                    :fill-reference-el="fillReferenceEl"
                ></nurse-execute>
            </template>

            <template v-else-if="currentTabIndex === 3">
                <medical-document-main
                    :department-id="departmentId"
                    :patient-id="patientId"
                    :patient-order-id="curPatientOrderId"
                    :business-id="curPatientOrderId"
                    :business-type="MedicalDocumentBusinessType.HOSPITAL"
                ></medical-document-main>
            </template>
            <template v-else-if="currentTabIndex === 4">
                <medical-diagnosis
                    :patient-id="patientId"
                    :patient-order-id="curPatientOrderId"
                    :disabled-diagnosis="true"
                    :fill-reference-el="fillReferenceEl"
                ></medical-diagnosis>
            </template>
            <template v-else-if="currentTabIndex === 5">
                <bed-exam
                    :key="2"
                    :type="2"
                    :patient-id="patientId"
                    :patient-order-id="curPatientOrderId"
                    :fill-reference-el="fillReferenceEl"
                ></bed-exam>
            </template>
            <template v-else-if="currentTabIndex === 6">
                <bed-exam
                    :key="1"
                    :type="1"
                    :patient-id="patientId"
                    :patient-order-id="curPatientOrderId"
                    :fill-reference-el="fillReferenceEl"
                ></bed-exam>
            </template>
            <template v-else-if="currentTabIndex === 7">
                <div v-abc-loading:page="loading">
                    <fee-statistics
                        :page-loading.sync="loading"
                        :patient-order-id="curPatientOrderId"
                        :patient-info="curPatientInfo"
                        :fill-reference-el="fillReferenceEl"
                        is-show-detail
                    >
                    </fee-statistics>
                </div>
            </template>
        </abc-dialog>

        <change-bed-dialog
            v-if="showChangeBedDialog"
            v-model="showChangeBedDialog"
            :department-id="departmentId"
            :bed-info="bedInfo"
            :bed-service="bedService"
            @close-dialog="showDialog = false"
        ></change-bed-dialog>
    </div>
</template>

<script>
    import ChangeBedDialog from '@/views-hospital/beds/components/change-bed-dialog/index';
    const PatientHospitalInfo = () => import('@/views-hospital/beds/components/patient-hospital-info/index');
    import MedicalDocumentMain from '@/views-hospital/nursing/components/medical-document-main.vue';
    import MedicalPrescription from '@/views-hospital/nurse-prescription/frames/medical-prescription.vue';
    import NurseExecute from '@/views-hospital/nurse-prescription/frames/nurse-execute.vue';
    import { MedicalAdviceStatusEnum } from '@/views-hospital/medical-prescription/utils/constants.js';
    import MedicalDiagnosis from '@/views-hospital/medical-prescription/frames/medical-diagnosis.vue';
    import FeeStatistics from '@/views-hospital/cost/frames/fee-statistics.vue';
    import SocialInHospital from '@/views-hospital/register/components/social-in-hospital/index.vue';
    import BedExam from '@/views-hospital/beds/components/bed-inspect';
    import {
        InHospitalStatus, MedicalDocumentBusinessType,
    } from '@/views-hospital/nursing/common/constants.js';
    import printCommon from '@/views-hospital/medical-prescription/utils/print-common';
    import { MedicalAdviceTypeEnum } from '@/views-hospital/medical-prescription/utils/constants';
    import {
        HospitalActionEnum,
        HospitalStatusEnum,
    } from '@/views-hospital/register/utils/constants';
    import PatientOrderAPI from 'api/hospital/patient-order';
    import { noop } from '@/utils';
    import { mapGetters } from 'vuex';
    export default {
        name: 'BedDetailDialog',
        components: {
            ChangeBedDialog,
            PatientHospitalInfo,
            MedicalDocumentMain,
            MedicalDiagnosis,
            MedicalPrescription,
            NurseExecute,
            FeeStatistics,
            SocialInHospital,
            BedExam,
        },
        mixins: [printCommon],
        props: {
            value: Boolean,
            newAdd: {
                type: Boolean,
                default: false,
            },
            disabledDiagnosis: {
                type: Boolean,
                default: false,
            },
            bedInfo: {
                type: Object,
                default: () => ({}),
            },
            formSource: {
                type: String,
                default: '',
            },
            patientId: {
                type: String,
                default: '',
            },
            outpatientDoctorId: {
                type: String,
                default: '',
            },
            departmentId: {
                type: String,
                default: '',
            },
            extendDiagnosisInfos: {
                type: Array,
                default: () => [],
            },
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            bedService: Object,

            onFinish: {
                type: Function,
                default: noop,
            },
        },
        data() {
            return {
                MedicalAdviceTypeEnum,
                MedicalAdviceStatusEnum,
                currentTabIndex: 0,
                loading: false,
                btnLoading: false,
                showChangeBedDialog: false,
                MedicalDocumentBusinessType,
                allowEdit: false,
                hospitalStatus: null,
                btnDisabled: false,
            };
        },
        computed: {
            ...mapGetters(['printHospitalMedicalDocumentsConfig']),
            btnText() {
                return !this.isHospitalProcess ? '完成' : '保存';
            },
            // 住院中不可修改
            disabled() {
                return this.isHospitalProcess && !this.allowEdit;
            },
            // 住院待登记
            isHospitalProcess() {
                return this.newAdd && this.hospitalStatus !== null && this.hospitalStatus >= HospitalStatusEnum.REQUEST_REGISTER;
            },
            hospitalStatusTips() {
                return this.isHospitalProcess && this.newAdd ? '患者正在住院中' : '';
            },
            fillReferenceEl() {
                const $detailDialog = this.$refs.detailDialog;
                if (!$detailDialog) return;
                return $detailDialog.$el.querySelector('.abc-dialog-body');
            },
            bedId() {
                return this.bedInfo?.id || '';
            },
            curPatientOrderId() {
                return this.bedInfo?.curPatientOrderId || '';
            },
            curPatientInfo() {
                return {
                    id: this.bedInfo?.curPatientId || '',
                    name: this.bedInfo?.name || '',
                };
            },
            contentStyle() {
                if (this.displayTabOptions.length === 1) {
                    return 'width: 834px; height: auto';
                }

                const width = `${window.innerWidth * 0.8}px`;
                let height = `${window.innerHeight - 104}px`;
                let padding = '16px 24px';

                if (!this.curPatientOrderId && this.currentTabIndex === 0) {
                    height = `${window.innerHeight - 158}px`;
                }

                if (this.currentTabIndex === 3) {
                    padding = 0;
                }

                return `min-width: 1200px; width: ${width}; height: ${height};padding: ${padding};`;
            },
            displayTabOptions() {
                const arr = [{
                    label: this.newAdd ? '住院办理' : '档案',
                    value: 0,
                },{
                    label: '核对',
                    value: 1,
                },{
                    label: '执行',
                    value: 2,
                },{
                    label: '病历',
                    value: 3,
                },{
                    label: '诊断',
                    value: 4,
                },{
                    label: '检查',
                    value: 5,
                },{
                    label: '检验',
                    value: 6,
                },{
                    label: '费用',
                    value: 7,
                }];

                if (this.newAdd) {
                    return [{ ...arr[0] }];
                }

                return arr;
            },
            tableColumn() {
                return this.displayTabOptions.length > 1 ? 4 : 3;
            },
            showDialog: {
                get() {
                    return this.value;
                },
                set(val) {
                    this.$emit('input', val);
                },
            },
        },
        methods: {
            handleFinish() {
                this.$refs?.patientHospitalInfo?.handleSubmit(this.onFinish);
            },
            handleCancel() {
                this.showDialog = false;
            },
            handleSuccess() {
                this.showDialog = false;
                if (this.isHospitalProcess) {
                    this.allowEdit = false;
                }
            },
            /**
             * 获取患者住院状态：是否产生过医嘱或费用
             */
            async checkPatientInHospitalStatus() {
                try {
                    const { data } = await PatientOrderAPI.fetchInHospitalStatus(this.curPatientOrderId);
                    const { checkResult } = data || {};
                    if (checkResult === InHospitalStatus.ENABLE) {
                        // 允许退院
                        this.$confirm({
                            type: 'warn',
                            title: '是否确认将当前患者退院',
                            content: (
                                <div>
                                    确定后患者将变为待分配状态，已开出的文书、诊断等信息将不会保存。<br />
                                    退院后请患者继续前住院登记窗口完成退院办理及住院押金退费。
                                </div>
                            ),
                            onConfirm: () => {
                                PatientOrderAPI.updatePatientOrderStatus(this.curPatientOrderId, {
                                    action: HospitalActionEnum.DISCHARGE_REQUEST,
                                });
                                this.handleCancel();
                            },
                        });
                    } else if (checkResult === InHospitalStatus.DISABLE) {
                        // 不允许退院
                        this.$confirm({
                            type: 'warn',
                            title: '当前状态不支持退院',
                            content: (
                                <div>
                                    当前患者已开出医嘱或产生费用<br />
                                    1.请撤销所有医嘱并确认撤销、退回所有费用后再办理退院<br />
                                    2.如需保留已产生的医嘱及费用，可走正常出院流程
                                </div>
                            ),
                            onConfirm: () => {},
                        });
                    }
                } catch (e) {
                    console.error(e);
                }
            },
        },
    };
</script>
<style lang="scss" rel="stylesheet/scss">
@import 'src/styles/theme.scss';
@import 'src/styles/mixin.scss';

.patient-info-detail-wrapper {
    .tabs-wrapper {
        border-bottom: none;

        .abc-tabs-item {
            font-size: 16px;

            &:not(:first-child) {
                margin-left: 32px !important;
            }
        }
    }

    .print-btn-group {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 16px;
    }

    .hospital-status-tips {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 14px;
        margin-bottom: 16px;
        font-size: 14px;
        line-height: 14px;
        color: $T2;
    }

    .patient-info-footer {
        padding-right: 8px;
    }
}
</style>
