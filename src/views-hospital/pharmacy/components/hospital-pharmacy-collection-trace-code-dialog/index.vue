<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="追溯码采集"
        size="hugely"
        :auto-focus="false"
        append-to-body
        content-styles="height: 590px; padding: 0"
        disabled-keyboard
        custom-class="hospital-pharmacy-collection-trace-code-dialog"
        @close-dialog="handleClose"
        @open="handleOpen"
    >
        <template #top-extend>
            <abc-tips-card-v2
                v-if="hasEnableCollCheckStrictMode"
                theme="primary"
                title="依码支付重要提醒"
            >
                机构在医保结算时必须确定本次<abc-text bold>
                    应采组合（整装+拆零数量）
                </abc-text>，以便算出<abc-text bold>
                    “本次应采追溯码数量”
                </abc-text>上传医保完成结算。
            </abc-tips-card-v2>
            <trace-code-activation-card v-else></trace-code-activation-card>
        </template>
        <abc-layout has-sidebar style="height: 100%;">
            <abc-layout-sidebar class="side-bar">
                <abc-flex
                    v-for="patient in listByPatient"
                    :key="patient.id"
                    align="center"
                    class="patient-item-wrapper"
                    :class="`${selectedPatient.id === patient.id ? 'is-selected' : ''}`"
                    @click="changePatient(patient)"
                >
                    <abc-text size="normal">
                        {{ patient.name }}
                    </abc-text>
                </abc-flex>
            </abc-layout-sidebar>
            <abc-layout-content class="hospital-pharmacy-collection-panel-wrapper">
                <abc-form ref="form" style="margin: 24px 0;">
                    <collection-panel
                        v-if="selectedPatient.items.length"
                        :key="selectedPatient.id ? selectedPatient.id : 'hospital-pharmacy-collection-panel-key'"
                        ref="collectionPanel"
                        :form-items="formItems?.items ?? []"
                        :dispensed-form-items="dispensedFormItems?.items ?? []"
                        :patient-order-id="selectedPatient.patientOrderId"
                        :confirm-inner-set="confirmInnerSet"
                        :callback-confirm="callbackConfirm"
                        :scene-type="sceneType"
                    ></collection-panel>
                </abc-form>
            </abc-layout-content>
        </abc-layout>

        <div slot="footer" class="dialog-footer">
            <abc-button @click.stop="confirm">
                {{ confirmText }}
            </abc-button>
            <abc-button type="blank" @click.stop="close">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import TraceCode, { TraceCodeScenesEnum } from '@/service/trace-code/service';
    import CollectionPanel from '@/service/trace-code/components/collection-panel/index.vue';
    import TraceCodeActivationCard from '@/service/trace-code/components/trace-code-activation-card.vue';

    export default {
        name: 'HospitalPharmacyCollectionTraceCodeDialog',
        components: {
            TraceCodeActivationCard,
            CollectionPanel,
        },
        props: {
            list: {
                type: Array,
                required: true,
            },
            dispensedList: {
                type: Array,
                default: () => ([]),
            },
            confirmText: {
                type: String,
                default: '确定',
            },
            confirmCallback: {
                type: Function,
                required: true,
            },
            cancelCallback: {
                type: Function,
                required: true,
            },
            // 确定后内部直接回写追溯码
            confirmInnerSet: {
                type: Boolean,
                default: true,
            },
            sceneType: {
                type: String,
                default: '',
            },
        },
        data() {
            return {
                visible: false,
                dispensingListByPatient: [],
                dispensedListByPatient: [],
                listByPatient: [],
                selectedPatient: {
                    id: '',
                    name: '',
                    items: [],
                    patientOrderId: '',
                },
                hasEnableCollCheckStrictMode: false,
            };
        },
        computed: {
            formItems() {
                const { id } = this.selectedPatient;
                return this.dispensingListByPatient.find((item) => item.id === id) ?? {};
            },
            dispensedFormItems() {
                const { id } = this.selectedPatient;
                return this.dispensedListByPatient.find((item) => item.id === id) ?? {};
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        created() {
            this.hasEnableCollCheckStrictMode = TraceCode.hasEnableCollCheckStrictMode;
        },
        methods: {
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
            handleOpen() {
                this.dispensingListByPatient = this.formatDispensingListByPatient(this.list);
                this.dispensedListByPatient = this.formatDispensingListByPatient(this.dispensedList);
                const list = this.handleListByPatient();
                this.listByPatient = list ?? [];
                if (list.length) {
                    this.selectedPatient = list[0];
                }
            },
            handleListByPatient() {
                const patientMap = {};
                [...this.dispensingListByPatient, ...this.dispensedListByPatient].forEach((p) => {
                    if (patientMap[p.id]) {
                        // If patient already exists, merge items
                        patientMap[p.id].items.push(...p.items);
                    } else {
                        // Otherwise, add the patient to the map (with a copy of items)
                        patientMap[p.id] = {
                            ...p, items: [...p.items],
                        };
                    }
                });
                return Object.values(patientMap);
            },
            close() {
                this.cancelCallback?.();
                this.visible = false;
            },
            callbackConfirm(formItems) {
                this.confirmCallback(formItems);
                this.visible = false;
            },
            confirm() {
                this.$refs.form.validate((valid) => {
                    if (valid) {
                        this.$refs.collectionPanel.confirm();
                    }
                });
            },
            handleClose() {
                this.cancelCallback?.();
            },
            formatDispensingListByPatient(list = []) {
                const res = [];
                list.forEach((item) => {
                    const patient = item._patient || {};
                    const {
                        id, name,
                    } = patient;
                    const findPatient = res.find((it) => it.id === id);
                    if (findPatient) {
                        findPatient.items.push(item);
                    } else {
                        res.push({
                            id,
                            name,
                            items: [item],
                            patientOrderId: item._patientOrderId,
                        });
                    }
                });
                return res;
            },
            async getCollectionTraceCodeStatusName(items) {
                const { flag } = await TraceCode.validate({
                    scene: TraceCodeScenesEnum.PHARMACY,
                    dataList: items,
                    needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckPharmacy(),
                });
                return {
                    flag,
                    statusName: flag ? '已完成' : '未完成',
                };
            },
            changePatient(patient) {
                this.$refs.collectionPanel?.handleInnerSet?.();
                this.selectedPatient = patient;
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/mixin.scss';

.hospital-pharmacy-collection-trace-code-dialog {
    .side-bar {
        height: 100%;
        padding: 16px 4px 16px 14px;
        overflow-y: auto;
        background-color: var(--abc-color-cp-grey2);

        @include scrollBar;
    }

    .patient-item-wrapper + .patient-item-wrapper {
        margin-top: var(--abc-paddingTB-s);
    }

    .patient-item-wrapper {
        padding: 5px 10px;
        overflow: hidden;
        cursor: pointer;
        border-radius: var(--abc-border-radius-small);

        &:hover {
            background-color: var(--abc-color-cp-grey4);
        }

        &.is-selected {
            color: var(--abc-color-T4);
            background-color: var(--abc-color-B3);
        }
    }

    .hospital-pharmacy-collection-panel-wrapper {
        padding-top: 0;
        padding-bottom: 0;
        overflow-y: scroll;

        @include scrollBar;
    }
}
</style>
