<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="退药信息确认"
        size="hugely"
        responsive
        :auto-focus="false"
        append-to-body
        @open="handleOpen"
    >
        <abc-layout preset="dialog-table">
            <abc-layout-content ref="hospitalPharmacyUnDispensingCollectionTraceCodeLayoutContent" @layout-mounted="layoutMounted">
                <hospital-pharmacy-un-dispensing-collection-trace-code-pro-table
                    v-if="tableFillReferenceEl && unDispensingList"
                    ref="hospitalPharmacyUnDispensingCollectionTraceCodeTableRef"
                    :data-list="unDispensingList"
                    :fill-reference-el="tableFillReferenceEl"
                ></hospital-pharmacy-un-dispensing-collection-trace-code-pro-table>
            </abc-layout-content>
        </abc-layout>

        <template #footer>
            <abc-flex justify="flex-end" align="center">
                <abc-button variant="ghost" theme="danger" @click="handleConfirm">
                    退药
                </abc-button>
                <abc-button @click="handleClose">
                    取消
                </abc-button>
            </abc-flex>
        </template>
    </abc-dialog>
</template>

<script>
    import HospitalPharmacyUnDispensingCollectionTraceCodeProTable from '@/views/layout/tables/table-hospital-pharmacy-un-dispensing-collection-trace-code/index.vue';
    import cloneDeep from 'lodash.clonedeep';
    import {
        handleSearch, isCompatibleHistoryData, isNoTraceCodeGoods,
    } from 'views/pharmacy/utils';
    import Big from 'big.js';
    import { isNull } from '@/common/utils';
    import { dispenseOrderOperationTypeEnum } from '@/views-hospital/pharmacy/utils/constant';

    export default {
        name: 'HospitalPharmacyUnDispensingCollectionTraceCodeDialog',
        components: {
            HospitalPharmacyUnDispensingCollectionTraceCodeProTable,
        },
        props: {
            list: {
                type: Array,
                required: true,
            },
            onConfirm: {
                type: Function,
                required: true,
            },
            onClosed: {
                type: Function,
                default: () => {},
            },
        },
        data() {
            return {
                visible: false,
                tableFillReferenceEl: null,
                unDispensingList: null,
            };
        },
        methods: {
            calcShouldRefundCount(item) {
                const {
                    unitCount, associateDispensingFormItemDispensedCount, associateDispensingFormItemUndispensedCount,
                } = item;

                if (isNull(associateDispensingFormItemDispensedCount) || isNull(associateDispensingFormItemUndispensedCount)) {
                    return unitCount;
                }

                try {
                    const dispensedCount = new Big(associateDispensingFormItemDispensedCount);
                    const undispensedCount = new Big(associateDispensingFormItemUndispensedCount);

                    return dispensedCount.minus(undispensedCount).toNumber();
                } catch (e) {
                    console.warn(e);
                    return unitCount;
                }
            },
            initSelectedTraceCodeList(item) {
                if ([dispenseOrderOperationTypeEnum.REJECT_RETURN, dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN].includes(item.dispenseType)) {
                    item.selectedTraceCodeList = [];
                    return;
                }

                // 修改的数量是全退需要默认选中所有追溯码并且不可修改，否则清空
                if (+item._unitCount === +item.canRefundUnitCount && item._traceableCodeListOptions) {
                    if (isCompatibleHistoryData(item) || isNoTraceCodeGoods(item)) {
                        item.selectedTraceCodeList = item._traceableCodeListOptions.map((it) => it.keyId);
                        item.disabledTraceCodeList = true;
                    } else {
                        const traceCodeList = cloneDeep(item._traceableCodeListCache ?? []);
                        const dispensedCodeList = [];
                        traceCodeList.forEach((code) => {
                            // 已发药的追溯码
                            if (code.used === 1) {
                                dispensedCodeList.push(code);
                            }
                        });
                        item.dispensedTraceCodeList = dispensedCodeList;
                        item.selectedTraceCodeList = dispensedCodeList;
                    }
                } else if (item.checked && isNoTraceCodeGoods(item) && item._traceableCodeListOptions) {
                    item.selectedTraceCodeList = item._traceableCodeListOptions.map((it) => it.keyId);
                    item.disabledTraceCodeList = true;
                } else {
                    item.selectedTraceCodeList = [];
                    item.disabledTraceCodeList = false;
                    if (!isCompatibleHistoryData(item)) {
                        const traceCodeList = cloneDeep(item._traceableCodeListCache ?? []);
                        const dispensedCodeList = [];
                        traceCodeList.forEach((code) => {
                            // 已发药的追溯码
                            if (code.used === 1) {
                                dispensedCodeList.push(code);
                            }
                        });
                        item.dispensedTraceCodeList = dispensedCodeList;
                    }
                }
            },
            initTraceCodeList() {
                const cacheList = cloneDeep(this.list);
                cacheList.forEach((item) => {
                    if (item.isChineseForm) {
                        item.dispensingFormItems.forEach((chineseItem) => {
                            // 用来退追溯码
                            chineseItem.selectedTraceCodeList = [];
                            chineseItem.checked = true;
                            chineseItem._unitCount = chineseItem.unitCount;
                            chineseItem.canRefundUnitCount = this.calcShouldRefundCount(chineseItem);
                            handleSearch('', chineseItem);
                            this.filterItemTraceCodeList(chineseItem);
                            this.initSelectedTraceCodeList(chineseItem);
                        });
                    } else {
                        // 用来退追溯码
                        item.selectedTraceCodeList = [];
                        item.checked = true;
                        item._unitCount = item.unitCount;
                        item.canRefundUnitCount = this.calcShouldRefundCount(item);
                        handleSearch('', item);
                        this.filterItemTraceCodeList(item);
                        this.initSelectedTraceCodeList(item);
                    }
                });
                this.unDispensingList = cacheList;
            },
            handleOpen() {
                this.initTraceCodeList();
            },
            layoutMounted() {
                this.tableFillReferenceEl = this.$refs.hospitalPharmacyUnDispensingCollectionTraceCodeLayoutContent?.$el ?? null;
            },
            filterItemTraceCodeList(item) {
                item._traceableCodeListCache = item._traceableCodeListCache || item.traceableCodeList || [];
                item.traceableCodeList = item._traceableCodeListCache.filter((it) => it.used === 1);
            },
            handleConfirm() {
                if (typeof this.onConfirm === 'function') {
                    // 获取 traceCodeOperation 引用
                    const hospitalPharmacyUnDispensingCollectionTraceCodeTableRefs = this.$refs.hospitalPharmacyUnDispensingCollectionTraceCodeTableRef.$refs;
                    const traceCodeOperationRef = [];
                    Object.keys(hospitalPharmacyUnDispensingCollectionTraceCodeTableRefs).forEach((key) => {
                        if (key.startsWith('traceCodeOperation-')) {
                            traceCodeOperationRef.push(hospitalPharmacyUnDispensingCollectionTraceCodeTableRefs[key]);
                        }
                    });
                    let operations = [];

                    // 处理三种情况
                    if (traceCodeOperationRef) {
                        // 如果存在引用
                        if (Array.isArray(traceCodeOperationRef)) {
                            // 多个元素的情况
                            operations = traceCodeOperationRef;
                        } else {
                            // 单个元素的情况，转为数组
                            operations = [traceCodeOperationRef];
                        }
                    }

                    let finalValidate = false;
                    operations.forEach((operation) => {
                        const validate = operation.validateTraceableCodeCount();
                        if (validate) {
                            finalValidate = true;
                        }
                    });
                    if (finalValidate) return false;
                    this.onConfirm(this.unDispensingList);
                }
                this.visible = false;
            },
            handleClose() {
                if (typeof this.onClosed === 'function') {
                    this.onClosed();
                }
                this.visible = false;
            },
        },
    };
</script>
