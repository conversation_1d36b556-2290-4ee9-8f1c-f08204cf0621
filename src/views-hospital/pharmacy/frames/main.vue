<template>
    <div v-abc-loading:page="loading" class="pharmacy-wrapper">
        <abc-container-center-top-head>
            <abc-row
                justify="space-between"
                :custom-style="{
                    'width': '100%'
                }"
            >
                <abc-col flex="1">
                    <abc-space>
                        <abc-tabs
                            v-model="params.tabValue"
                            :option="tabOptions"
                            type="outline"
                            @change="onAdviceTypeTabChange"
                        >
                        </abc-tabs>

                        <abc-cascader
                            v-model="selectedTypes"
                            :options="goodsTypeOptions"
                            placeholder="类型"
                            multiple
                            :collapse="true"
                            :mutually-exclusive="exclusive"
                            :width="110"
                            :props="{
                                label: 'name',
                                value: 'id',
                                children: 'children'
                            }"
                            :value-props="{
                                _label: 'name',
                                _value: 'id'
                            }"
                            @change="handleChangeType"
                        >
                        </abc-cascader>

                        <abc-select
                            v-if="isReturnOrder || params.tabValue2 === 2"
                            v-model="params.patientId"
                            placeholder="患者"
                            :width="110"
                            clearable
                        >
                            <abc-option
                                v-for="option in patientOptions"
                                :key="option.value"
                                :value="option.value"
                                :label="option.label"
                            ></abc-option>
                        </abc-select>
                    </abc-space>
                </abc-col>
                <abc-col>
                    <abc-space split>
                        <abc-space>
                            <abc-tooltip content="没有选择药品" :disabled="isSelectDrugBeforeDispensing">
                                <abc-button
                                    :loading="buttonLoading"
                                    :disabled="!isSelectDrugBeforeDispensing"
                                    :count="selectedDispenseList.length"
                                    @click="handleExecute"
                                >
                                    执行
                                </abc-button>
                            </abc-tooltip>
                            <abc-tooltip v-if="traceCodeCollectionCheck && showHospitalPharmacyTraceCode" content="本单项目均无需采集追溯码" :disabled="showTraceCodeButton.length !== 0">
                                <abc-button
                                    variant="ghost"
                                    :disabled="showTraceCodeButton.length === 0"
                                    data-cy="trace-code-button"
                                    @click="handleFormatTraceCodeData"
                                >
                                    追溯码
                                </abc-button>
                            </abc-tooltip>
                            <!--                            <abc-tooltip content="没有选择药品" :disabled="isSelectDrugBeforePrinting">-->
                            <!--                                <abc-button-->
                            <!--                                    :loading="buttonLoading"-->
                            <!--                                    :disabled="!isSelectDrugBeforePrinting"-->
                            <!--                                    :count="selectedPrintList.length"-->
                            <!--                                    type="blank"-->
                            <!--                                    @click="handlePrint"-->
                            <!--                                >-->
                            <!--                                    打印-->
                            <!--                                </abc-button>-->
                            <!--                            </abc-tooltip>-->
                            <abc-dropdown
                                :disabled="!isSelectDrugBeforePrinting"
                                style="width: auto;"
                                @change="val => handlePrintMedicalPrescriptionBtn({ adviceType: val })"
                            >
                                <abc-button
                                    slot="reference"
                                    :disabled="!isSelectDrugBeforePrinting"
                                    type="blank"
                                >
                                    打印
                                </abc-button>
                                <abc-dropdown-item label="打印" :value="1" :disabled="!isSelectDrugBeforePrinting">
                                    {{ isReturnOrder ? '退药单' : '领药单' }} <span v-if="selectedPrintList.length"> &middot; </span>{{
                                        selectedPrintList.length || ''
                                    }}
                                </abc-dropdown-item>
                                <abc-dropdown-item
                                    v-if="isPatientPrintMode"
                                    label="处方"
                                    :value="2"
                                    :disabled="!selectedMedicinePrintList.length"
                                >
                                    处方 <span v-if="selectedMedicinePrintList.length"> &middot; </span>{{
                                        selectedMedicinePrintList.length || ''
                                    }}
                                </abc-dropdown-item>
                                <abc-dropdown-item
                                    v-if="isPatientPrintMode"
                                    label="用药标签"
                                    :value="3"
                                    :disabled="!selectedMedicinePrintList.length"
                                >
                                    用药标签 <span v-if="selectedMedicinePrintList.length"> &middot; </span>{{
                                        selectedMedicinePrintList.length || ''
                                    }}
                                </abc-dropdown-item>
                                <abc-dropdown-item
                                    disabled
                                    label=""
                                    value=""
                                    style="width: 100%; height: 0; min-height: 0 !important; padding: 0 !important; cursor: default; border-top: 1px solid #e6eaee;"
                                ></abc-dropdown-item>
                                <abc-dropdown-item
                                    label="设置..."
                                    value="pharmacyPrintSetting"
                                ></abc-dropdown-item>
                            </abc-dropdown>

                            <abc-button
                                variant="ghost"
                                theme="primary"
                                icon="n-settings-line"
                                @click="handleOpenAudioAnnouncementSetting"
                            ></abc-button>
                        </abc-space>


                        <template v-if="!isReturnOrder">
                            <abc-tabs
                                v-model="params.tabValue2"
                                :option="switchOptions"
                                type="outline"
                                @change="v => onTabChange(v, true)"
                            >
                            </abc-tabs>
                        </template>
                    </abc-space>
                </abc-col>
            </abc-row>
        </abc-container-center-top-head>

        <abc-container-center-main-content>
            <abc-descriptions
                v-if="!isBatchMode"
                :column="4"
                :bordered="false"
                style="margin-left: -12px;"
            >
                <abc-descriptions-item label="接收药房：">
                    {{
                        dispensingSheetView?.pharmacyName ?? ''
                    }}
                </abc-descriptions-item>
                <abc-descriptions-item label="申请病区：">
                    {{
                        dispensingSheetView?.wardAreaView?.name ?? ''
                    }}
                </abc-descriptions-item>
                <abc-descriptions-item label="申请时间：">
                    <span :title="formatDate(new Date(dispensingSheetView.created), 'YYYY-MM-DD HH:mm')">
                        {{ formatDate(new Date(dispensingSheetView.created), 'MM-DD HH:mm') }}
                    </span>
                </abc-descriptions-item>
            </abc-descriptions>

            <div v-else style="height: 16px;"></div>

            <abc-table-fixed2
                ref="pharmacy-table-fixed2"
                :key="renderKey"
                class="pharmacy-table"
                :header="renderConfig"
                :data="newRenderList"
                :cell-merge="cellMerge"
                :max-height="tableHeight"
                :empty-opt="emptyOpt"
                @select-all="onCheckboxAll"
            >
            </abc-table-fixed2>

            <template v-if="logs.length > 0 && !isBatchMode">
                <abc-button
                    type="text"
                    style="margin: 16px 0 12px -4px;"
                    @click="showLog = !showLog"
                >
                    操作记录
                    <abc-icon v-if="!showLog" icon="dropdown_triangle"></abc-icon>
                    <abc-icon v-else icon="dropdown_triangle_up"></abc-icon>
                </abc-button>

                <div v-show="showLog" class="record-wrapper">
                    <div
                        v-for="(o, key) in logs"
                        :key="key"
                        class="record-item"
                    >
                        <span class="date">{{ o.date }}</span>
                        <span class="name">{{ o.name }}</span>
                        <span class="type">{{ o.type }}</span>
                        <span class="content">{{ o.content }}
                            <abc-popover
                                v-if="o.newDetails.length"
                                trigger="hover"
                                placement="top-start"
                                theme="yellow"
                                :visible-arrow="false"
                                popper-class="hospital-pharmacy-operation-details-popover"
                                style="display: inline-block;"
                            >
                                <abc-link slot="reference" size="small">
                                    详情
                                </abc-link>
                                <abc-flex vertical>
                                    <template v-for="(detail, idx) in o.newDetails">
                                        <!-- 分割线 -->
                                        <abc-divider
                                            v-if="idx"
                                            :key="`hospital-pharmacy-detail-popover-divider-${idx}`"
                                            variant="dashed"
                                            theme="dark"
                                            margin="small"
                                        >
                                        </abc-divider>

                                        <abc-flex
                                            :key="`hospital-pharmacy-detail-popover-patient-info-${idx}`"
                                            vertical
                                            :gap="4"
                                        >
                                            <!-- 患者信息 -->
                                            <abc-flex align="center" :gap="24">
                                                <abc-text style="font-weight: 500;">患者：{{ detail.patient && detail.patient.patientName || '' }}</abc-text>
                                                <abc-text>{{ detail.patient && detail.patient.departmentName || '' }}</abc-text>
                                                <abc-text v-if="detail.patient && detail.patient.bedNo">{{ detail.patient.bedNo || '' }}床</abc-text>
                                            </abc-flex>
                                            <!-- 药品信息 -->
                                            <abc-flex
                                                v-for="(form, formIdx) in detail.formRecordViews"
                                                :key="`hospital-pharmacy-detail-popover-form-${formIdx}`"
                                                vertical
                                                :gap="6"
                                            >
                                                <!-- form 类型 -->
                                                <abc-text style="font-weight: 500;">{{ form.sourceFormTypeName }}</abc-text>
                                                <!-- 药品详情 -->
                                                <!-- 中西成药、耗材 -->
                                                <template v-if="[SourceFormTypeEnum.PRESCRIPTION_WESTERN, SourceFormTypeEnum.MATERIAL].includes(form.sourceFormType)">
                                                    <abc-flex
                                                        v-for="(formItem, formItemIdx) in form.operationFormItems"
                                                        :key="`hospital-pharmacy-detail-popover-form-item-${formItemIdx}`"
                                                        vertical
                                                        :gap="6"
                                                    >
                                                        <!-- 中西成药、耗材 -->
                                                        <abc-flex align="center" justify="space-between" style="font-size: 12px;">
                                                            <abc-text>{{ formItem.name }}</abc-text>
                                                            <abc-text><template v-if="isShowMinusSign(o.action)">-</template>{{ formItem.unitCount }} {{ formItem.unit }}</abc-text>
                                                        </abc-flex>
                                                    </abc-flex>
                                                </template>
                                                <!-- 中药 -->
                                                <template v-else-if="form.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE">
                                                    <abc-flex align="center" wrap="wrap" style="gap: 6px 0;">
                                                        <abc-flex
                                                            v-for="(formItem, formItemIdx) in form.operationFormItems"
                                                            :key="`hospital-pharmacy-detail-popover-form-item-chinese-${formItemIdx}`"
                                                            align="center"
                                                            :gap="8"
                                                            style="width: 25%; font-size: 12px;"
                                                        >
                                                            <abc-text>{{ formItem.name }}</abc-text>
                                                            <abc-text><template v-if="isShowMinusSign(o.action)">-</template>{{ formItem.unitCount }} {{ formItem.unit }}</abc-text>
                                                        </abc-flex>
                                                    </abc-flex>
                                                    <abc-text style="font-size: 12px; color: var(--abc-color-T3);">[计量] 共{{ form.operationFormItems[0].doseCount }}剂</abc-text>
                                                </template>
                                            </abc-flex>
                                        </abc-flex>
                                    </template>
                                </abc-flex>
                            </abc-popover>

                            <abc-popover
                                v-else-if="o.details?.length"
                                trigger="hover"
                                placement="bottom-start"
                                theme="yellow"
                                style="display: inline-block;"
                                popper-class="pharmacy-operation-popover_wrapper"
                            >
                                <div v-for="(e, i) in o.details" :key="i">
                                    <div v-for="(m, n) in e.formRecordViews" :key="n">
                                        <div
                                            v-if="m.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE"
                                            class="pharmacy-operation-title"
                                        >
                                            {{ m.sourceFormTypeName || '中药处方' }}
                                        </div>

                                        <div
                                            :class="['pharmacy-operation-content', {
                                                'is-chinese': m.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE
                                            }]"
                                        >
                                            <div
                                                v-for="(p, q) in m.operationFormItems"
                                                :key="q"
                                                class="pharmacy-operation-item"
                                            >
                                                <span class="pharmacy-operation-item-name">{{ p.name }}</span>

                                                <span>{{ handleOperationCount(p) }}</span>
                                            </div>
                                        </div>

                                        <div
                                            v-if="m.sourceFormType === SourceFormTypeEnum.PRESCRIPTION_CHINESE"
                                            class="pharmacy-operation-custom-item"
                                        >
                                            {{
                                                `剂量[共${m.operationFormItems?.[0]?.doseCount ?? 0}剂]`
                                            }}
                                        </div>
                                    </div>
                                </div>
                                <abc-button
                                    slot="reference"
                                    type="text"
                                    size="small"
                                    style=" height: 12px; font-size: 12px;"
                                >
                                    详情
                                </abc-button>
                            </abc-popover>
                        </span>
                    </div>
                </div>
            </template>
        </abc-container-center-main-content>

        <!--发药、退药-->
        <abc-dialog
            v-if="showDialog"
            v-model="showDialog"
            content-styles="width: 498px;"
            :title="!isReturnOrder ? '完成发药' : '完成退药'"
            disabled-keyboard
            append-to-body
        >
            <template>
                <abc-card background="gray" padding-size="small">
                    <template #title>
                        <abc-flex align="center" style="margin-bottom: 8px;">
                            <label class="hospital-pharmacy-dispensing-dialog-label">执行人</label>
                            <abc-text>{{ userInfo?.name }}</abc-text>
                        </abc-flex>
                        <abc-flex v-if="isBatchMode" align="center" style="margin-bottom: 8px;">
                            <label class="hospital-pharmacy-dispensing-dialog-label">申请单</label>
                            <abc-text>{{ getBatchesTotalCount() }}张</abc-text>
                        </abc-flex>
                        <abc-flex align="center">
                            <label class="hospital-pharmacy-dispensing-dialog-label">执行时间</label>
                            <abc-text>{{ formatDate(new Date(), 'YYYY-MM-DD HH:mm') }}</abc-text>
                        </abc-flex>
                    </template>

                    <abc-flex v-if="isReturnOrder || !showHospitalPharmacyTraceCode || !needTraceCodeFormItems.length" align="flex-start">
                        <label class="hospital-pharmacy-dispensing-dialog-label">执行内容</label>
                        <abc-text>{{ dispenseContent }}</abc-text>
                    </abc-flex>
                    <template v-if="!isReturnOrder && showHospitalPharmacyTraceCode && needTraceCodeFormItems.length">
                        <abc-flex align="center" style="margin-bottom: 6px;">
                            <label class="hospital-pharmacy-dispensing-dialog-label">执行内容</label>
                            <abc-link theme="primary" @click="handleFormatTraceCodeData">
                                追溯码（{{ traceCodeCount }}）
                            </abc-link>
                        </abc-flex>
                        <abc-flex align="flex-start" style="padding-left: 70px;">
                            <abc-text>{{ dispenseContent }}</abc-text>
                        </abc-flex>
                    </template>
                </abc-card>

                <div slot="footer" class="dialog-footer">
                    <div v-if="!isReturnOrder" class="left">
                        <abc-checkbox
                            v-model="hospitalDispensingNeedPrint"
                            @change="changeHospitalDispensingNeedPrint"
                        >
                            同时打印领药单
                        </abc-checkbox>
                    </div>

                    <abc-button :loading="saveLoading" @click="onConfirm">
                        确定
                    </abc-button>
                    <abc-button :disabled="saveLoading" type="blank" @click="onCancel">
                        取消
                    </abc-button>
                </div>
            </template>
        </abc-dialog>
        <!--拒发、拒退-->
        <abc-dialog
            v-if="showRefund"
            v-model="showRefund"
            :title="!isReturnOrder ? '药品拒发' : '药品拒退'"
            append-to-body
            content-styles="max-height: calc(100vh - 200px)"
        >
            <div class="medical-prescription-list-table-wrapper has-inner-border">
                <div class="medical-prescription-list-table">
                    <div class="table-header">
                        <template v-for="item in NPListTable.refundTable">
                            <div
                                v-if="item.prop === 'reason'"
                                :key="item.label"
                                class="th"
                                :style="item.style"
                            >
                                {{ !isReturnOrder ? '拒发原因' : '拒退原因' }}
                            </div>
                            <div
                                v-else
                                :key="item.label"
                                class="th"
                                :style="item.style"
                            >
                                {{ item.label }}
                            </div>
                        </template>
                    </div>
                    <div class="table-body">
                        <div
                            v-for="row in filterRejectList(rejectList)"
                            :key="row._id"
                            class="tr"
                            style="align-items: center;"
                        >
                            <template v-for="col in NPListTable.refundTable">
                                <div
                                    v-if="col.prop === 'patientName'"
                                    :key="col.label"
                                    class="td"
                                >
                                    <div v-abc-title.ellipsis="row['patient']['name']" :style="col.style" class="custom-td-cell"></div>
                                </div>

                                <div
                                    v-else-if="col.prop === 'reason'"
                                    :key="col.label"
                                    class="td"
                                >
                                    <div class="custom-td-cell is-component" :style="col.style">
                                        <abc-input
                                            v-model="row['renderData']['comment']"
                                            v-abc-focus-selected
                                            size="small"
                                            :input-custom-style="{
                                                textAlign: 'left',
                                                height: '100%',
                                                border: '1px solid transparent',
                                                borderRadius: '0px !important',
                                                background: 'transparent',
                                            }"
                                        >
                                        </abc-input>
                                    </div>
                                </div>
                                <template v-else>
                                    <template v-if="!row.isChineseForm">
                                        <div
                                            :key="col.label"
                                            class="td"
                                        >
                                            <div v-abc-title.ellipsis="col.prop === 'count' ? formatCount(row.productInfo, row['renderData'][isReturnOrder ? 'returnCount' : 'commitCount'], row.useDismounting) : row['renderData'][col.prop]" class="custom-td-cell" :style="col.style"></div>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <div
                                            :key="col.label"
                                            class="td"
                                        >
                                            <div class="custom-td-cell no-padding" :style="col.style">
                                                <div
                                                    v-for="item in filterRejectListChineseItem(row.dispensingFormItems)"
                                                    :key="item._id"
                                                    v-abc-title.ellipsis="col.prop === 'count' ? item.renderData.statusCountStr : item['renderData'][col.prop]"
                                                    class="dd"
                                                >
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </template>
                            </template>
                        </div>
                    </div>
                    <div class="table-footer"></div>
                </div>
            </div>

            <div slot="footer" class="dialog-footer">
                <abc-button :loading="saveLoading" @click="onRefundConfirm">
                    确定
                </abc-button>
                <abc-button :disabled="saveLoading" type="blank" @click="onRefundCancel">
                    取消
                </abc-button>
            </div>
        </abc-dialog>
        <!--库存不足弹窗-->
        <abc-dialog
            v-if="showStockShortage"
            v-model="showStockShortage"
            title="库存不足"
            append-to-body
            size="medium"
            show-header-border-bottom
            content-styles="max-height: 312px"
        >
            <abc-flex vertical :gap="16">
                <abc-tips-card-v2 theme="warning">
                    以下药品库存不足，无法发药
                </abc-tips-card-v2>

                <abc-flex vertical :gap="24">
                    <abc-flex
                        v-for="(table, i) in stockShortageList"
                        :key="`hospital-pharmacy-stock-shortage-${i}`"
                        vertical
                        style="border: 1px solid var(--abc-color-P8); border-radius: var(--abc-border-radius-small);"
                    >
                        <abc-flex align="center" justify="space-between" style=" height: 48px; padding: 0 10px; border-bottom: 1px solid var(--abc-color-P8);">
                            <abc-text size="large" :title="table.goodsName">
                                {{ table.goodsName }}
                            </abc-text>
                            <abc-flex align="center" :gap="10">
                                <abc-text size="mini" theme="gray" :title="table.spec">
                                    {{ table.spec }}
                                </abc-text>
                                <abc-text
                                    size="mini"
                                    theme="gray"
                                    :title="table.manufactor"
                                    class="ellipsis"
                                    style="max-width: 100px;"
                                >
                                    {{ table.manufactor }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>

                        <abc-flex vertical>
                            <abc-flex
                                v-for="item in table.children"
                                :key="item._id"
                                align="center"
                                justify="space-between"
                                class="hospital-pharmacy-stock-shortage-item-patient"
                                @click="handleCheckNoStockShortageItem(item, table)"
                            >
                                <abc-flex align="center">
                                    <abc-checkbox
                                        v-model="item.checked"
                                        :disabled="item.isChineseForm || stockShortageCheckboxDisabled(table, item)"
                                    ></abc-checkbox>
                                    <abc-text style="margin-left: 10px;">
                                        {{ formatBedsNo(item.beds) }}
                                    </abc-text>
                                    <abc-text style="margin-left: 30px;">
                                        {{ item.patient && item.patient.name }}
                                    </abc-text>
                                </abc-flex>
                                <abc-text>
                                    {{
                                        item.isChineseForm ? (item.usageInfo ? `${item.usageInfo.doseCount}剂` : '') : formatCount(item.productInfo, item.renderData && item.renderData.commitCount, item.useDismounting)
                                    }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>

                        <abc-flex
                            v-if="table.isChineseForm"
                            align="center"
                            justify="flex-end"
                            :gap="16"
                            style="height: 40px; padding: 0 10px;"
                        >
                            <abc-flex align="center">
                                <abc-text theme="gray">
                                    库存：
                                </abc-text>
                                <abc-text>
                                    {{ table.pieceCount }}g
                                </abc-text>
                            </abc-flex>
                            <abc-flex align="center">
                                <abc-text theme="gray">
                                    已选：
                                </abc-text>
                                <abc-text>
                                    {{ table.commitCount }}g
                                </abc-text>
                            </abc-flex>
                        </abc-flex>
                        <abc-flex
                            v-else
                            align="center"
                            justify="flex-end"
                            :gap="16"
                            style="height: 40px; padding: 0 10px;"
                        >
                            <abc-flex align="center">
                                <abc-text theme="gray">
                                    库存：
                                </abc-text>
                                <abc-text>
                                    {{ formatCount(table.productInfo, table.pieceCount) }}
                                </abc-text>
                            </abc-flex>
                            <abc-flex align="center">
                                <abc-text theme="gray">
                                    已选：
                                </abc-text>
                                <abc-text>
                                    {{ formatCount(table.productInfo, stockShortageSelectedPatient(table.children)) }}
                                </abc-text>
                            </abc-flex>
                        </abc-flex>
                    </abc-flex>
                </abc-flex>
            </abc-flex>

            <div slot="footer" class="dialog-footer">
                <abc-button :loading="saveLoading" @click="onStockShortageConfirm">
                    确定
                </abc-button>
                <abc-button :disabled="saveLoading" type="blank" @click="onStockShortageCancel">
                    取消
                </abc-button>
            </div>
        </abc-dialog>

        <abc-dialog
            v-if="showDispensingSendLoading"
            v-model="showDispensingSendLoading"
            class="hospital-dispense-send-loading-dialog-wrapper"
            content-styles="padding: 24px; width: 550px; height: 344px;"
            disabled-keyboard
            append-to-body
        >
            <abc-delete-icon class="delete-icon-wrapper" size="huge" @delete="handleDispenseSendClose"></abc-delete-icon>
            <abc-progress-panel
                title="申请单药品较多，批量发药中"
                :is-finish="sendFinish"
                :total-count="sendTotalCount"
                :current-count="sendCurrentCount"
                :process-text="currentProcessText"
                finish-text="发药完成"
            >
            </abc-progress-panel>
        </abc-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    import Big from 'big.js';
    import { mapGetters } from 'vuex';
    import printCommon from '@/views-hospital/medical-prescription/utils/print-common';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import { getViewDistributeConfig } from '@/views-distribute/utils';
    import { cascaderDataAdapter } from '@/utils';
    import { formatMoney } from '@/filters';
    import {
        off, on,
    } from 'utils/dom';
    import clone from 'utils/clone';
    import {
        formatDate, isSameDay,
    } from '@abc/utils-date';
    import GoodsAPI from 'api/goods';
    import {
        dispenseOrderFormItemTypeEnum,
        dispenseOrderOperationTypeEnum, DispenseRecordActionEnum,
        TagType,
    } from '@/views-hospital/pharmacy/utils/constant';
    import {
        createDispenseOrderReqs, formatCount,
        formatDispenseOrderViewList,
        NPListTable,
    } from '../utils/index';
    import {
        AdviceTagEnumSingleText,
        AdviceTagEnumTextColor,
    } from '@/views-hospital/medical-prescription/utils/constants';
    import SourceTag from 'views/layout/source-tag.vue';
    import { SourceFormTypeEnum } from '@/service/charge/constants';

    import ICON_EMPTY from 'src/assets/images/icon/<EMAIL>';
    import { calcTableHeight } from '@/views-hospital/daily/utils';
    import {
        ABCPrintConfigKeyMap, PrintMode,
    } from '@/printer/constants.js';
    import AbcPrinter from '@/printer/index.js';
    import {
        getProcessUsageInfo, getDispensingFormItemInfo, getRequirement, updateGroupIds,
    } from '@/views-hospital/medicine-apply/utils/handle-medicinal-list.js';
    import { PriceType } from 'views/common/inventory/constants';
    import PrintAPI from 'api/hospital/print';
    import Printer from 'views/print';
    import AbcProgressPanel from 'views/physical-examination/integrated/public-health-sync/components/abc-process-panel.vue';
    import { sleep } from 'utils/delay';
    import AbcError from 'utils/error/abc-error';
    const GoodsBatchInfoPopover = () => import('src/views/layout/goods-batch-info-popover/index.vue');
    import DialogRefundBatchesSelector from '@/views-pharmacy/charge/components/refund/dialog-refund-batches-selector';
    import HospitalPharmacyCollectionTraceCodeDialog
        from '@/views-hospital/pharmacy/components/hospital-pharmacy-collection-trace-code-dialog';
    import {
        getHospitalPharmacyTraceCodeDispensingItems,
        getHospitalPharmacyTraceCodeItemsByDispensingStatus,
    } from 'views/pharmacy/constants';
    import DispenseAPI from 'api/dispensary';
    import AudioAnnouncementSettingDialog from '@/views-hospital/pharmacy/components/audio-announcement-setting-dialog';
    import TraceCode, {
        SceneTypeEnum, TraceCodeScenesEnum,
    } from '@/service/trace-code/service';
    import HospitalPharmacyUnDispensingCollectionTraceCodeDialog from '@/views-hospital/pharmacy/components/hospital-pharmacy-un-dispensing-collection-trace-code-dialog/hospital-pharmacy-un-dispensing-collection-trace-code-dialog';
    import Dispensary from 'api/dispensary';
    const PrintConfigDialogModule = () => import('@/printer/components/print-config-dialog');

    const BatchSendStatusEnum = Object.freeze({
        // 错误
        Error: 0,
        // 部分发药
        PartFinish: 1,
        // 完成
        Finish: 2,
    });

    // 默认参数
    const _params = {
        tabValue: '',
        tabValue2: 1,// 1 按药品 2 按患者
        typeId: '',
        customTypeId: '',
        patientId: '',
    };
    export default {
        name: 'HospitalPharmacyMain',
        components: {
            AbcProgressPanel,
        },
        inject: {
            $abcPage: {
                default: {},
            },
        },
        data() {
            return {
                NPListTable,
                SourceFormTypeEnum,
                dispenseOrderFormItemTypeEnum,
                dispenseOrderId: '',
                loading: false,// table loading
                tableHeight: 440,
                params: clone(_params),
                showLog: true,
                exclusive: false,
                goodsAllTypes: [], // 系统所有的药品物资商品分类以及二级分类
                selectedTypes: [], // 选中的药品分类
                buttonLoading: false,//发药退药拒发拒退按钮 loading
                showDialog: false,// 发药退药弹窗开关
                showRefund: false,// 拒发拒退弹窗开关
                showDispensingSendLoading: false,
                showStockShortage: false,// 库存不足弹窗开关
                saveLoading: false,//发药退药确定弹窗按钮 loading
                printOpt: {
                    printSelect: '处方',
                    finishSelect: [],
                },
                list: [],
                rejectList: [],// 拒发列表
                stockShortageList: [],// 库存不足
                hospitalDispensingNeedPrint: Printer.cache.get().hospitalDispensingNeedPrint, // 是否需要同时打印发药单
                isReturnOrder: false,// 是否是退药单
                emptyOpt: {
                    label: '暂无数据',
                    imgUrl: ICON_EMPTY,
                },
                isCheckedAll: true,
                isBatchMode: false,
                batchIds: [],

                sendTotalCount: 0,
                sendCurrentCount: 0,
                sendFinish: false,
                traceCodeCount: 0,

                dispenseOrderOperationTypeEnum,
            };
        },
        computed: {
            ...mapGetters(['multiPharmacyCanUse', 'userInfo', 'westernMedicineConfig', 'currentClinic', 'traceCodeConfig']),
            traceCodeCollectionCheck() {
                return this.traceCodeConfig?.collectionCheck || 0;
            },
            isPatientPrintMode() {
                return this.isReturnOrder || this.params.tabValue2 === 2;
            },
            showHospitalPharmacyTraceCode() {
                return !!this.traceCodeConfig.hospitalPharmacy;
            },
            renderKey() {
                return `${this.dispenseOrderId}${this.isReturnOrder ? 'return' : 'send'}${this.params.tabValue2}`;
            },
            goodsNameRender() {
                return (h, item) => {
                    // 按患者才展示分组
                    const showGroupLine = this.params.tabValue2 === 2 && (item.groupIds && item.groupIds.length > 1);

                    if (!item.isChineseForm) {
                        return (<div class="custom-medicine-td">
                        <div class="cell" style="display: flex;align-items:center;">
                            <span class="ellipsis" title={item.renderData.goodsName}>{item.renderData.goodsName}</span>
                            {
                                !!item.isShortage && (
                                    item.productInfo?.lockBatchOps === 1 ?
                                        (
                                            <abc-popover
                                                trigger="hover"
                                                placement="bottom-start"
                                                popper-style={{
                                                    padding: '0',
                                                }}
                                                theme="yellow"
                                            >
                                                <abc-icon
                                                    slot="reference"
                                                    icon="Attention"
                                                    color={this.$store.state.theme.style.Y2}
                                                    style="margin-left:4px"
                                                    size="12" />
                                                <PopoverLockingDetail
                                                    list={item.dispensingFormItemBatches}
                                                    form-item={item}
                                                >
                                                </PopoverLockingDetail>
                                            </abc-popover>
                                        ) :
                                        (
                                            <abc-popover
                                                placement="top"
                                                trigger="hover"
                                                theme="yellow">
                                                <abc-icon
                                                    slot="reference"
                                                    icon="Attention"
                                                    color={this.$store.state.theme.style.Y2}
                                                    style="margin-left:4px"
                                                    size="12"
                                                ></abc-icon>
                                                <span>库存不足</span>
                                            </abc-popover>
                                        )
                                )
                            }

                            {
                                /*医嘱下达的标记*/
                                this.params.tabValue2 === 2 ? null : item.renderData.tags?.map((tag) => {
                                    const name = AdviceTagEnumSingleText[tag.type];
                                    const color = AdviceTagEnumTextColor[tag.type];
                                    return <SourceTag
                                        key={name}
                                        name={name}
                                        color={color}
                                        style="margin-left: 4px;"
                                    ></SourceTag>;
                                })
                            }

                            {
                                /*医嘱下达备注*/
                                this.params.tabValue2 === 2 ? null :
                                    <span class="ellipsis remark" style="margin-left:4px"
                                          title={item.renderData.remark || ''}>{
                                        item.renderData.remark || ''
                                    }</span>
                            }
                        </div>
                        <div>
                            {
                                showGroupLine ?
                                    <div class="group-line"
                                         style={{
                                             height: `${(item.groupIds.length - 1) * 40}px`,
                                         }}></div> : ''}</div>
                    </div>);
                    }
                    return <div style="width:100%;height:100%;position:relative;">

                    {
                        item.dispensingFormItems.map((it) => {
                            return (
                                <div key={it._id} class="custom-goods-name-td">
                                    <span class="ellipsis"
                                          title={it.renderData.goodsName}>{it.renderData.goodsName}</span>
                                    <span
                                        style="margin-left: 8px; font-size:10px; color: #7a8794">{it.usageInfo.specialRequirement}</span>
                                    {
                                        !!it.isShortage && (
                                            item.productInfo?.lockBatchOps === 1 ?
                                                (
                                                    <abc-popover
                                                        trigger="hover"
                                                        placement="bottom-start"
                                                        popper-style={{
                                                            padding: '0',
                                                        }}
                                                        theme="yellow"
                                                    >
                                                        <abc-icon
                                                            slot="reference"
                                                            icon="Attention"
                                                            color={this.$store.state.theme.style.Y2}
                                                            style="margin-left: 4px"
                                                            size="12"
                                                        />
                                                        <PopoverLockingDetail
                                                            list={it.dispensingFormItemBatches}
                                                            form-item={it}
                                                        >
                                                        </PopoverLockingDetail>
                                                    </abc-popover>
                                                ) :
                                                (
                                                    <abc-popover
                                                        placement="top"
                                                        trigger="hover"
                                                        theme="yellow">
                                                        <abc-icon
                                                            slot="reference"
                                                            icon="Attention"
                                                            color={this.$store.state.theme.style.Y2}
                                                            style="margin-left: 4px"
                                                            size="12"
                                                        ></abc-icon>
                                                        <span>库存不足</span>
                                                    </abc-popover>
                                                )
                                        )
                                    }
                                </div>
                            );
                        })
                    }

                    </div>;

                };
            },

            selectRender() {
                return (h, item) => {
                    const labelMap = {
                        [dispenseOrderOperationTypeEnum.DISPENSE]: '发药',
                        [dispenseOrderOperationTypeEnum.UNDISPENSE]: '拒发',
                        [dispenseOrderOperationTypeEnum.RETURN]: '退药',
                        [dispenseOrderOperationTypeEnum.REJECT_RETURN]: '拒退',
                        [dispenseOrderOperationTypeEnum.RECORD]: '补记',
                        [dispenseOrderOperationTypeEnum.DEDUCT]: '扣库',
                    };
                    // 是否补开
                    const isReplenish = item.tagType === TagType.REPLENISH || item.tagType === TagType.URGENT_REPLENISH;

                    if (!item.isChineseForm) {
                        return <abc-select
                            value={item.dispenseType}
                            disabled={item.disabled}
                            adaptiveWidth
                            style="height:40px"
                            inputStyle={{
                                height: '40px',
                                border: '1px solid transparent',
                                borderRadius: '0px !important',
                                background: 'transparent',
                            }}
                            size="tiny"
                            placeholder="请选择"
                            showValue={labelMap[item.dispenseType]}
                            onChange={(val) => this.onSelectChange(item, val)}
                        >
                            {!this.isReturnOrder && (
                                <abc-option value={dispenseOrderOperationTypeEnum.DISPENSE} label="发药"></abc-option>)}
                            {!this.isReturnOrder && (
                                <abc-option value={dispenseOrderOperationTypeEnum.UNDISPENSE} label="拒发"></abc-option>)}

                            {this.isReturnOrder && (
                                <abc-option value={dispenseOrderOperationTypeEnum.RETURN} label="退药"></abc-option>)}
                            {this.isReturnOrder && (
                                <abc-option value={dispenseOrderOperationTypeEnum.REJECT_RETURN} label="拒退"></abc-option>)}

                            {
                                // 先隐藏“补记录，无需发药扣库”的方式，这次先不支持
                                // !this.isReturnOrder && isReplenish && (<abc-option value={dispenseOrderOperationTypeEnum.RECORD} label="补记录，无需发药扣库"></abc-option>)
                            }
                            {!this.isReturnOrder && isReplenish && (<abc-option value={dispenseOrderOperationTypeEnum.DEDUCT}
                                                                                label="仅扣库，无需发药"></abc-option>)}
                        </abc-select>;
                    }


                    return <div class="custom-td chinese">
                        {
                            item.dispensingFormItems.map((dispensingFormItem) => {
                                return <div class="custom-dd" style="padding: 0;" key={dispensingFormItem._id}>
                                    <abc-select
                                        value={dispensingFormItem.dispenseType}
                                        disabled={dispensingFormItem.disabled}
                                        adaptiveWidth
                                        inputStyle={{
                                            height: '39px',
                                            border: '1px solid transparent',
                                            borderRadius: '0px !important',
                                            background: 'transparent',
                                        }}
                                        size="tiny"
                                        placeholder="请选择"
                                        showValue={labelMap[dispensingFormItem.dispenseType]}
                                        onChange={(val) => this.onChineseSelectChange(item, dispensingFormItem, val)}
                                    >
                                        {!this.isReturnOrder && (
                                            <abc-option value={dispenseOrderOperationTypeEnum.DISPENSE} label="发药"></abc-option>)}
                                        {!this.isReturnOrder && (
                                            <abc-option value={dispenseOrderOperationTypeEnum.ALL_DESPENSE} label="整方发药"></abc-option>)}
                                        {!this.isReturnOrder && (
                                            <abc-option value={dispenseOrderOperationTypeEnum.UNDISPENSE} label="拒发"></abc-option>)}
                                        {!this.isReturnOrder && (
                                            <abc-option value={dispenseOrderOperationTypeEnum.ALL_UNDISPENSE} label="整方拒发"></abc-option>)}

                                        {this.isReturnOrder && (
                                            <abc-option value={dispenseOrderOperationTypeEnum.ALL_RETURN} label="整方退药"></abc-option>)}
                                        {this.isReturnOrder && (
                                            <abc-option value={dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN} label="整方拒退"></abc-option>)}

                                        {!this.isReturnOrder && isReplenish && (<abc-option value={dispenseOrderOperationTypeEnum.DEDUCT}
                                                                                            label="仅扣库，无需发药"></abc-option>)}
                                    </abc-select>
                                </div>;
                            })
                        }
                    </div>;
                };
            },
            checkBoxRender() {
                return (h, item) => {
                    return (
                    <div style="display: flex; justify-content: center">
                        <abc-checkbox
                            style="height: 40px"
                            value={item.checked}
                            onChange={(val) => this.onCheckboxChange(item, val)}
                        >
                        </abc-checkbox>
                    </div>
                    );
                };
            },
            patientNameRender() {
                return (_, item) => {
                    if (item.isChineseForm) {
                        return <div class="custom-td">
                            {
                                item.dispensingFormItems.map((dispensingFormItem) => {
                                    return <div
                                        class="custom-dd"
                                        key={dispensingFormItem._id}
                                        title={dispensingFormItem.renderData.patientName}
                                    >
                                        <span class="ellipsis">{dispensingFormItem.renderData.patientName}</span>
                                    </div>;
                                })
                            }
                        </div>;
                    }

                    return <div class="cell" title={item.renderData.patientName}>
                    {item.renderData.patientName}
                </div>;
                };
            },
            isVirtualPharmacy() {
                return this.$abcPage.$store.scrollParams.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY;
            },
            dispensingSheetView() {
                return this.$abcPage.$store.dispensingSheetView;
            },
            goodsTypeOptions() {
                if (this.isVirtualPharmacy) {
                    return this.goodsAllTypes.filter((item) => {
                        return item.goodsType === GoodsTypeEnum.MEDICINE && item.goodsSubType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine;
                    }) || [];
                }
                return this.goodsAllTypes || [];
            },
            tabOptions() {
                const {
                    cy, zs, qt,
                } = this.usageOptions;
                const obj = {};
                this.list.forEach((item) => {
                    const {
                        way, goodsType,
                    } = item.renderData;

                    if (goodsType === '中药') {
                        obj['中药'] = (obj['中药'] || 0) + 1;
                    }

                    if ([TagType.REPLENISH, TagType.URGENT_REPLENISH].includes(item.tagType)) {
                        obj['补开'] = (obj['补开'] || 0) + 1;
                    }

                    if (cy.list.includes(way)) {
                        obj[cy.way] = (obj[cy.way] || 0) + 1;
                    } else if (zs.list.includes(way)) {
                        obj[zs.way] = (obj[zs.way] || 0) + 1;
                    } else if (qt.list.includes(way)) {
                        obj[qt.way] = (obj[qt.way] || 0) + 1;
                    } else {
                        obj[way] = (obj[way] || 0) + 1;
                    }

                });

                return [
                    {
                        label: '全部',
                        value: '',
                        statisticsNumber: this.list.length,
                    },
                    {
                        label: '口服',
                        value: '口服',
                        statisticsNumber: obj['口服'] || '0',
                    },
                    {
                        label: '中药',
                        value: '中药',
                        statisticsNumber: obj['中药'] || '0',
                    },
                    {
                        label: '输注',
                        value: '输注',
                        statisticsNumber: obj['输注'] || '0',
                    },
                // {
                //     label: '精麻毒',
                //     value: '精麻毒',
                //     statisticsNumber: obj['精麻毒'] || '0',
                // },
                // {
                //     label: '补开',
                //     value: '补开',
                //     statisticsNumber: obj['补开'] || '0',
                // },
                ];
            },
            switchOptions() {
                return [
                    {
                        label: '按药品',
                        value: 1,
                    },
                    {
                        label: '按患者',
                        value: 2,
                    },
                ];
            },
            patientOptions() {
                const patientMap = new Map;
                return this.dispensingSheetView.dispensingSheetViewList.reduce((options, { patient }) => {
                    if (patientMap.get(patient.id)) {
                    // 不做处理
                    } else {
                        patientMap.set(patient.id, patient);
                        options.push({
                            label: patient.name,
                            value: patient.id,
                        });
                    }
                    return options;
                }, []);
            },
            // 发药/退药前是否选中药品
            isSelectDrugBeforeDispensing() {
                return !!this.selectedDispenseList?.length;
            },
            // 打印前是否选中药品
            isSelectDrugBeforePrinting() {
                return !!this.selectedPrintList?.length;
            },
            selectedAllDispensedList() {
                return this.list.filter((e) => e.checked);
            },
            selectedDispenseList() {
                return this.list.filter((e) => !e.disabled && e.checked);
            },
            selectedDispensedList() {
                return this.list.filter((e) => e.disabled && e.checked);
            },
            selectedPrintList() {
                return this.list.filter((e) => e.checked);
            },
            selectedMedicinePrintList() {
                return this.selectedPrintList.filter((it) => {
                    return !this.isMaterials(it);
                });
            },
            dispenseContent() {
                let str = '';

                const wayMap = this.selectedDispenseList.reduce((res, item) => {
                    if (item?.renderData?.goodsType) {
                        if (res[item?.renderData?.goodsType]) {
                            if (item?.renderData?.goodsType === '中药') {
                                res[item.renderData.goodsType] += (item.dispensingFormItems || []).length;
                            } else {
                                res[item.renderData.goodsType] += 1;
                            }
                        } else {
                            if (item?.renderData?.goodsType === '中药') {
                                res[item?.renderData?.goodsType] = (item.dispensingFormItems || []).length;
                            } else {
                                res[item?.renderData?.goodsType] = 1;
                            }
                        }
                    }
                    return res;
                }, {});

                Object.entries(wayMap).forEach(([k, v], index) => {
                    if (k === '中药') {
                        str += `${index === 0 ? '' : '、'}${k}（${v}味）`;
                    } else {
                        str += `${index === 0 ? '' : '、'}${k}（${v}项）`;
                    }
                });

                return str;
            },
            printOptions() {
                return getViewDistributeConfig().Print.printOptions;
            },
            pageLoading() {
                return this.$abcPage.$store.state.pageLoading;
            },
            logs() {
                return this.formatLogList(this.dispensingSheetView?.logList);
            },
            usageOptions() {
                const options = this.westernMedicineConfig.usage;

                const cy = {
                    type: 1, way: '口服', list: [],
                };
                const zs = {
                    type: 2, way: '输注', list: [],
                };
                const qt = {
                    type: 3, way: '精麻毒', list: [],
                };

                options.forEach((o) => {
                    if (o.type === cy.type) {
                        cy.list.push(o.name);
                    } else if (o.type === zs.type) {
                        zs.list.push(o.name);
                    } else if (o.type === qt.type) {
                        qt.list.push(o.name);
                    }
                });

                return {
                    cy,
                    zs,
                    qt,
                };
            },
            renderConfig() {
                return [
                    {
                        prop: 'checkbox',
                        label: '操作',
                        width: 40,
                        stretch: false,
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        headerRender: () => {
                            return (
                            <div style="display: flex; justify-content: center">
                                <abc-checkbox
                                    value={this.isCheckedAll}
                                    onChange={this.onCheckboxAll}>
                                </abc-checkbox>
                            </div>
                            );
                        },
                    },
                    {
                        prop: 'select',
                        label: '操作',
                        width: 66,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '7px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                    },
                    {
                        prop: 'patientName',
                        label: '患者',
                        width: 70,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        hidden: !(this.params.tabValue2 === 2 || this.isReturnOrder),
                    },
                    {
                        prop: 'goodsName',
                        label: '药品名称',
                        width: 240,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        measureHeight: true,
                    },
                    {
                        prop: 'statusName',
                        label: '状态',
                        width: 80,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                    {
                                        item.dispensingFormItems.map((dispensingFormItem) => {
                                            return <div
                                                class="custom-dd"
                                                key={dispensingFormItem._id}
                                                style={{ color: dispensingFormItem.renderData.statusColor }}
                                                title={dispensingFormItem.renderData.statusName}
                                            >
                                                <span class="ellipsis">{dispensingFormItem.renderData.statusName}</span>
                                            </div>;
                                        })
                                    }
                                </div>;
                            }

                            return <div
                                    class="cell"
                                    style={{ color: item.renderData.statusColor }}
                                    title={item.renderData.statusName}
                                    >
                                        {item.renderData.statusName}
                                    </div>;
                        },
                    },
                    {
                        prop: 'commit',
                        label: '待发',
                        width: 74,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                {
                                    item.dispensingFormItems.map((dispensingFormItem) => {

                                        let commitCountStr = '';
                                        let totalCountStr = '';
                                        if (
                                            dispensingFormItem.status === dispenseOrderFormItemTypeEnum.WAITING ||
                                            dispensingFormItem.status === dispenseOrderFormItemTypeEnum.RESET_DISPENSED
                                        ) {
                                            commitCountStr = dispensingFormItem.renderData.statusCountStr;
                                            totalCountStr = dispensingFormItem.renderData.statusTotalCount;
                                        }
                                        return <div
                                            key={dispensingFormItem._id}
                                            class="custom-dd"
                                            title={commitCountStr}
                                        >
                                            <span class="total-count">{totalCountStr}</span>
                                            <span class="ellipsis gray">{commitCountStr}</span>
                                        </div>;
                                    })
                                }
                            </div>;
                            }

                            const commitCount = item.renderData.commitCount ? this.formatCount(item.productInfo, item.renderData.commitCount, item.useDismounting) : '';
                            return <div
                            class="cell"
                            title={commitCount}
                        >
                            {commitCount}
                        </div>;
                        },
                        hidden: this.isReturnOrder,
                    },
                    {
                        prop: 'push',
                        label: '已发',
                        width: 70,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        render: (_, item) => {

                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                {
                                    item.dispensingFormItems.map((dispensingFormItem) => {
                                        let commitCountStr = '';
                                        let totalCountStr = '';
                                        if (dispensingFormItem.status === dispenseOrderFormItemTypeEnum.DISPENSED) {
                                            commitCountStr = dispensingFormItem.renderData.statusCountStr;
                                            totalCountStr = dispensingFormItem.renderData.statusTotalCount;
                                        }
                                        return <div
                                            key={dispensingFormItem._id}
                                            class="custom-dd"
                                            title={commitCountStr}
                                        >
                                            <span class="total-count">{totalCountStr}</span>
                                            <span class="ellipsis gray">{commitCountStr}</span>
                                        </div>;
                                    })
                                }
                            </div>;
                            }

                            const pushCount = item.renderData.pushCount ? this.formatCount(item.productInfo, item.renderData.pushCount, item.useDismounting) : '';
                            return <div
                            class="cell"
                            title={pushCount}
                        >
                            {pushCount}
                        </div>;
                        },
                        hidden: this.isReturnOrder,
                    },
                    {
                        prop: 'reject',
                        label: '拒发',
                        width: 70,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                {
                                    item.dispensingFormItems.map((dispensingFormItem) => {
                                        let commitCountStr = '';
                                        let totalCountStr = '';
                                        if (dispensingFormItem.status === dispenseOrderFormItemTypeEnum.APPLY_DISPENSE_REJECT) {
                                            commitCountStr = dispensingFormItem.renderData.statusCountStr;
                                            totalCountStr = dispensingFormItem.renderData.statusTotalCount;
                                        }
                                        return <div
                                            key={dispensingFormItem._id}
                                            class="custom-dd"
                                            title={commitCountStr}
                                        >
                                            <span class="total-count">{totalCountStr}</span>
                                            <span class="ellipsis gray">{commitCountStr}</span>
                                        </div>;
                                    })
                                }
                            </div>;
                            }

                            const rejectCount = item.renderData.rejectCount ? this.formatCount(item.productInfo, item.renderData.rejectCount, item.useDismounting) : '';
                            return <div
                            class="cell"
                            title={rejectCount}
                        >
                            {rejectCount}
                        </div>;
                        },
                        hidden: this.isReturnOrder,
                    },
                    {
                        prop: 'return',
                        label: '数量',
                        width: 70,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                {
                                    item.dispensingFormItems.map((dispensingFormItem) => {
                                        const returnCount = dispensingFormItem.renderData.statusCountStr;

                                        return <div
                                            key={dispensingFormItem._id}
                                            class="custom-dd"
                                            title={returnCount}
                                        >
                                            <span class="ellipsis">{returnCount}</span>
                                        </div>;
                                    })
                                }
                            </div>;
                            }


                            const willShowCount = item.originApplyCount ? this.formatCount(item.productInfo, item.originApplyCount, item.useDismounting) : '';

                            return <div
                            class="cell"
                            title={willShowCount}
                        >
                            {willShowCount}
                        </div>;
                        },
                        hidden: !this.isReturnOrder,
                    },
                    {
                        prop: 'manufactor',
                        label: '厂家',
                        width: 100,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                {
                                    item.dispensingFormItems.map((dispensingFormItem) => {
                                        const manufactor = dispensingFormItem.renderData.manufactor || '';
                                        return <div
                                            key={dispensingFormItem._id}
                                            class="custom-dd"
                                            title={manufactor}
                                        >
                                            <span class="ellipsis">{manufactor}</span>
                                        </div>;
                                    })
                                }
                            </div>;
                            }

                            const manufactor = item.renderData.manufactor || '';
                            return <div
                            class="cell"
                            title={manufactor}
                        >
                            {manufactor}
                        </div>;
                        },
                    },
                    {
                        prop: 'way',
                        label: '用法',
                        width: 70,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                    {
                                        item.dispensingFormItems.map((dispensingFormItem) => {
                                            return <div
                                                class="custom-dd"
                                                key={dispensingFormItem._id}
                                                title={dispensingFormItem.renderData.way}
                                            >
                                                <span class="ellipsis">{dispensingFormItem.renderData.way}</span>
                                            </div>;
                                        })
                                    }
                                </div>;
                            }

                            return <div
                            class="cell"
                            title={item.renderData.way}
                        >
                            {item.renderData.way}
                        </div>;
                        },
                    },
                    {
                        prop: 'step',
                        label: '频次',
                        width: 70,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                    {
                                        item.dispensingFormItems.map((dispensingFormItem) => {
                                            return <div
                                                class="custom-dd"
                                                key={dispensingFormItem._id}
                                                title={dispensingFormItem.renderData.step}
                                            >
                                                <span class="ellipsis">{dispensingFormItem.renderData.step}</span>
                                            </div>;
                                        })
                                    }
                                </div>;
                            }

                            return <div
                            class="cell"
                            title={item.renderData.step}
                        >
                            {item.renderData.step}
                        </div>;
                        },
                        hidden: !(this.params.tabValue2 === 2 || this.isReturnOrder),
                    },
                    {
                        prop: 'spec',
                        label: '规格',
                        width: 100,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                {
                                    item.dispensingFormItems.map((dispensingFormItem) => {
                                        return <div
                                            key={dispensingFormItem._id}
                                            class="custom-dd"
                                            title={dispensingFormItem.renderData.spec}
                                        >
                                                    <span class="ellipsis">
                                                      {dispensingFormItem.renderData.spec}
                                                    </span>
                                        </div>;
                                    })
                                }
                            </div>;
                            }
                            return <div
                            class="cell"
                            title={item.renderData.spec}
                        >
                            {item.renderData.spec}
                        </div>;
                        },
                    },
                    {
                        prop: 'goodsType',
                        label: '类型',
                        width: 70,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                    {
                                        item.dispensingFormItems.map((dispensingFormItem) => {
                                            return <div
                                                class="custom-dd"
                                                key={dispensingFormItem._id}
                                                title={dispensingFormItem.renderData.goodsType}
                                            >
                                                <span class="ellipsis">{dispensingFormItem.renderData.goodsType}</span>
                                            </div>;
                                        })
                                    }
                                </div>;
                            }

                            return <div
                            class="cell"
                            title={item.renderData.goodsType}
                        >
                            {item.renderData.goodsType}
                        </div>;
                        },
                    },
                    {
                        prop: 'price',
                        label: '单价',
                        width: 70,
                        thStyle: {
                            textAlign: 'right',
                            paddingRight: '10px',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                {
                                    item.dispensingFormItems.map((dispensingFormItem) => {
                                        return <div
                                            key={dispensingFormItem._id}
                                            class="custom-dd"
                                            style="justify-content: flex-end;"
                                            title={formatMoney(dispensingFormItem.renderData.price, false)}
                                        >
                                                    <span class="ellipsis">
                                                      {formatMoney(dispensingFormItem.renderData.price, false)}
                                                    </span>
                                        </div>;
                                    })
                                }
                            </div>;
                            }
                            const price = formatMoney(item.renderData.price);
                            return <div
                            class="cell"
                            style="text-align: right;"
                            title={price}
                        >
                            {price}
                        </div>;
                        },
                    },
                    {
                        prop: 'amount',
                        label: '金额',
                        width: 70,
                        thStyle: {
                            textAlign: 'right',
                            paddingRight: '10px',
                        },
                        render: (_, item) => {
                            if (item.isChineseForm) {
                                return <div class="custom-td">
                                {
                                    item.dispensingFormItems.map((dispensingFormItem) => {
                                        return <div
                                            key={dispensingFormItem._id}
                                            class="custom-dd"
                                            style="justify-content: flex-end;"
                                            title={formatMoney(dispensingFormItem.renderData.amount)}
                                        >
                                                    <span class="ellipsis">

                                                    {formatMoney(dispensingFormItem.renderData.amount)}
                                                    </span>
                                        </div>;
                                    })
                                }
                            </div>;
                            }
                            const amount = formatMoney(item.renderData.amount);
                            return (
                                <div
                                    class="cell"
                                    style="text-align: right;"
                                    title={amount}
                                >
                                    <GoodsBatchInfoPopover
                                        item={item}
                                        batches={this.getItemBatches(item.dispensingFormItemBatches)}
                                        className="goods-batch-info-popover"
                                    >
                                        <span>{amount}</span>
                                    </GoodsBatchInfoPopover>
                                </div>
                            );
                        },
                    },
                    {
                        prop: 'reason',
                        label: '申请原因',
                        width: 100,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        render: (_, item) => {
                            const reason = item.renderData.reason || '';
                            return <div
                                class="cell"
                                title={reason}
                            >
                                {reason}
                            </div>;
                        },
                        hidden: !this.isReturnOrder,
                    },
                    {
                        prop: 'batchNo',
                        label: '生产批号',
                        width: 140,
                        hidden: !this.isReturnOrder,
                        render: (_, item) => this.renderBatchNo(item),
                    },
                    {
                        prop: 'remark',
                        label: '备注',
                        width: 140,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        render: (_, item) => {
                            const remark = item.renderData.remark || '';
                            return <div
                            class="cell"
                        >
                            {
                                /*医嘱下达的标记*/
                                item.renderData.tags?.map((tag) => {
                                    const name = AdviceTagEnumSingleText[tag.type];
                                    const color = AdviceTagEnumTextColor[tag.type];
                                    return <SourceTag
                                        key={name}
                                        name={name}
                                        color={color}
                                        style="display:inline-block;margin-right: 4px;"
                                    ></SourceTag>;
                                })
                            }
                            <span class="ellipsis" title={remark}>{remark}</span>
                        </div>;
                        },
                        hidden: !(this.params.tabValue2 === 2 && !this.isReturnOrder),
                    },
                    {
                        prop: 'comment',
                        label: this.isReturnOrder ? '拒退原因' : '拒发原因',
                        width: 100,
                        thStyle: {
                            textAlign: 'left',
                            paddingLeft: '10px',
                        },
                        tdStyle: {
                            verticalAlign: 'unset',
                        },
                        render: (_, item) => {
                            let comment = '';
                            if (item.isChineseForm) {
                                comment = item.dispensingFormItems.find((o) => {
                                    if (this.isReturnOrder) {
                                        return o.status === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT;
                                    }
                                    return o.status === dispenseOrderFormItemTypeEnum.APPLY_DISPENSE_REJECT;

                                })?.renderData.comment || '';
                            } else {
                                comment = item.renderData.comment || '';
                            }

                            return <div
                            class="cell"
                        >
                                <span class="ellipsis" title={comment}>{comment}</span>
                        </div>;
                        },
                        hidden: !(this.params.tabValue2 === 2 || this.isReturnOrder),
                    },
                ].filter((item) => !item.hidden);
            },
            // 为了处理中药横向合并列，特殊处理，在每一条中药数据后加了一条数据，只做视图渲染，不要拿这个数据区处理其他逻辑，容易出错
            newRenderList() {
                const newList = [];
                const copiedRenderList = clone(this.renderList);
                copiedRenderList.forEach((item) => {
                    const processUsageInfo = getProcessUsageInfo(item);
                    const requirement = getRequirement(item);
                    const dispensingFormItemInfo = getDispensingFormItemInfo(item);
                    if (item.isChineseForm) {
                        newList.push(
                            {
                                ...item,
                            }, {
                                ...item,
                                groupId: null,
                                isChinesForm: false,
                                dispensingFormItems: [],
                                processUsageInfo: `${processUsageInfo}${requirement}`,
                                dispensingFormItemInfo,
                                renderData: {
                                    patientName: '',
                                    goodsName: '',
                                    status: 0,
                                    statusName: '',
                                    statusColor: '',
                                    statusCountStr: '',
                                    statusTotalCount: '',
                                    commitCount: 0,
                                    pushCount: '',
                                    rejectCount: '',
                                    returnCount: '',
                                    reason: '',
                                    manufactor: '',
                                    way: '',
                                    step: '',
                                    spec: '',
                                    cMSpec: '',
                                    goodsType: '',
                                    price: 0,
                                    amount: 0,
                                    remark: item.renderData?.remark || '',
                                    tags: item.renderData?.tags || [],
                                    comment: '',
                                    pieceCount: 0,
                                    dispenseType: -1,
                                },
                            },
                        );
                    } else {
                        newList.push(item);
                    }
                });

                return newList;
            },
            renderList() {
                const {
                    cy,
                    zs,
                // qt,
                } = this.usageOptions;

                return this.list.filter((item) => {
                    const {
                        tabValue, tabValue2, typeId, customTypeId, patientId,
                    } = this.params;
                    const {
                        typeId: _typeId, customTypeId: _customTypeId,
                    } = item.isChineseForm ? (item.dispensingFormItems?.[0]?.productInfo ?? {}) : (item.productInfo || {});
                    const {
                        way, goodsType,
                    } = item.renderData;
                    let flag = !tabValue,// 用法筛选
                        flag1 = true,// 一级分类
                        flag2 = true,// 二级分类
                        flag3 = true;// 患者
                    // 中药-判断类型过滤
                    if (tabValue === '中药') flag = tabValue === goodsType;
                    if (tabValue === '口服') flag = cy.list.includes(way);
                    if (tabValue === '输注') flag = zs.list.includes(way);
                    // if (tabValue === '精麻毒') flag = qt.list.includes(way);
                    // if (tabValue === '补开') flag = item.tagType === TagType.REPLENISH || item.tagType === TagType.URGENT_REPLENISH;

                    // 患者维度下(包括退药)-需要过滤患者
                    if ((this.isReturnOrder || tabValue2 === 2) && patientId) {
                        flag3 = item._patientId === patientId;
                    } else {
                        flag3 = true;
                    }

                    // 类型过滤
                    if (customTypeId?.length || typeId?.length) {
                        if (customTypeId?.length) {
                            flag1 = customTypeId.map(Number).includes(+_customTypeId);
                        }
                        if (typeId?.length) {
                            flag2 = typeId.map(Number).includes(+_typeId);
                        }

                    }

                    return flag && flag1 && flag2 && flag3;
                });
            },

            currentProcessText() {
                const total = this.sendTotalCount;
                const current = this.sendCurrentCount;
                const avgTime = 2;

                return `已完成${parseInt(current / total * 100)}%，预计还需${(total - current) * avgTime}s`;
            },
            needTraceCodeFormItems() {
                if (this.isReturnOrder) {
                    // 退药时所有药品都只能查看
                    return [];
                }
                return getHospitalPharmacyTraceCodeDispensingItems(this.selectedDispenseList);
            },
            dispensedTraceCodeFormItems() {
                if (this.isReturnOrder) {
                    // 退药时所有药品都只能查看
                    return getHospitalPharmacyTraceCodeItemsByDispensingStatus(this.selectedAllDispensedList);
                }
                return getHospitalPharmacyTraceCodeItemsByDispensingStatus(this.selectedDispensedList, [dispenseOrderFormItemTypeEnum.DISPENSED]);
            },
            showTraceCodeButton() {
                return [...this.needTraceCodeFormItems, ...this.dispensedTraceCodeFormItems];
            },
        },
        watch: {
            // 如果路由有变化，会再次执行该方法
            '$route': function (newVal) {
                this.dispenseOrderId = newVal.params.id;
                // TODO:未来优化点,是否重置状态
                // this.params = clone(_params);
                this.fetchData();
            },
        },
        mixins: [printCommon],

        created() {
            this.dispenseOrderId = this.$route.params.id;
            this.fetchAllGoodsTypes();
            this.fetchData();
            this.calcTraceCodeCount();

            // 暂时不刷新详情了
            // this.$abcPage.DispensingService?.addDispensingCallback({
            //     onUpdate: () => {
            //         this.fetchData(true);
            //     },
            // });
            this.$abcEventBus.$on('dispensing-batch-mode-change', this.handleDispensingBatchModeChange, this);
        },
        mounted() {
            on(document, 'keydown', this.keydownHandle);
            const tableEl = this.$refs['pharmacy-table-fixed2'].$el;
            this.tableHeight = calcTableHeight(tableEl, 40);
        },
        beforeDestroy() {
            off(document, 'keydown', this.keydownHandle);
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            // count是处理过的小单位数量，需要格式化展示
            formatCount,
            /**
             * 是否是耗材
             * 耗材包括：医疗器械、自制成品、保健药品、保健食品、其他商品
             */
            isMaterials(item) {
                const {
                    productType, productSubType,
                } = item || {};
                return (productType === GoodsTypeEnum.MATERIAL && productSubType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials) ||
                    productType === GoodsTypeEnum.GOODS;
            },
            // 是否显示批次
            showBatches(item) {
                const {
                    productInfo,
                    isChineseForm,
                    associateDispensingFormItemBatches = [],
                    dispensingFormItemBatches = [],
                } = item;
                if (isChineseForm) return false;
                const { priceType } = productInfo || {};
                // 固定售价
                if (priceType === PriceType.PRICE) return false;
                // 没有批次信息
                if ((+associateDispensingFormItemBatches.length + +dispensingFormItemBatches.length) === 0) return false;
                return true;
            },
            /**
             * @desc 批次是否确定
             * @desc 两种情况是确定的，1-只发了一个批次，2-全退
             */
            batchesIsDeterminate(item) {
                const {
                    associateDispensingFormItemBatches,
                    unitCount,
                } = item;
                const dispensingCount = associateDispensingFormItemBatches.reduce((pre, cur) => {
                    return pre + cur.unitCount;
                }, 0);

                return associateDispensingFormItemBatches.length === 1 || unitCount === dispensingCount;
            },
            // 批次渲染
            renderBatchNo(item) {
                // 无需批次信息
                if (!this.showBatches(item)) {
                    return (
                        <span
                            style={{
                                display: 'flex',
                                justifyContent: 'flex-end',
                                paddingRight: '10px',
                            }}
                        > - </span>
                    );
                }
                // 批次未确定
                if (!item.dispensingFormItemBatches && !this.batchesIsDeterminate(item)) {
                    return (
                        <span
                            style={{
                                display: 'flex',
                                justifyContent: 'flex-end',
                                paddingRight: '10px',
                                color: 'var(--abc-color-B1)',
                                cursor: 'pointer',
                            }}
                            onClick={ () => this.onSelectBatch(item)}
                        >选择批次</span>
                    );
                }
                // 批次确定
                if (!item.dispensingFormItemBatches && this.batchesIsDeterminate(item)) {
                    const dispensingFormItemBatches = clone(item.associateDispensingFormItemBatches);
                    if (item.associateDispensingFormItemBatches.length === 1) {
                        dispensingFormItemBatches[0].unitCount = item.unitCount;
                    }
                    this.$set(item, 'dispensingFormItemBatches', dispensingFormItemBatches);
                }
                if (!item.dispensingFormItemBatches) return;
                if (item.dispensingFormItemBatches.length <= 2) {
                    return (
                        <div onClick={ () => this.onSelectBatch(item)}>
                            {item.dispensingFormItemBatches.map((it) => (
                                <p key={it.batchId} style={{
                                    color: 'var(--abc-color-B1)',
                                    cursor: 'pointer',
                                    textAlign: 'right',
                                    paddingRight: '10px',
                                }}
                                >
                                    {it.batchNo}
                                </p>
                            ))}
                        </div>
                    );
                }
                return (
                    <div onClick={ () => this.onSelectBatch(item)}>
                        <p style={{
                            color: 'var(--abc-color-B1)',
                            cursor: 'pointer',
                            paddingRight: '10px',
                            textAlign: 'right',
                        }}
                        >
                            {item.dispensingFormItemBatches[0].batchNo}
                        </p>
                        <p style={{
                                color: 'var(--abc-color-B1)',
                                cursor: 'pointer',
                                paddingRight: '10px',
                                textAlign: 'right',
                            }}
                        >
                            等{item.dispensingFormItemBatches.length}个批次
                        </p>
                    </div>
                );

            },
            // 批次选择
            onSelectBatch(item) {
                const { status } = item;
                if (status !== dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE) return;
                if (!this.showBatches(item)) return;
                const cloneItem = clone(item);
                const {
                    unitCount,
                    unit,
                    associateDispensingFormItemBatches,
                    productInfo,
                    dispensingFormItemBatches,
                } = cloneItem;
                let remainingUnitCount = unitCount || 0;
                const chargeFormItemBatchInfos = associateDispensingFormItemBatches.reverse().map((x) => {
                    const {
                        unitCount: batchUnitCount,
                        batchInfo,
                    } = x;
                    let currentUnitCount = 0;
                    if (dispensingFormItemBatches) {
                        dispensingFormItemBatches.forEach((y) => {
                            if (y.batchId === batchInfo.batchId) {
                                currentUnitCount = y.unitCount;
                            }
                        });
                    } else {
                        if (batchUnitCount < remainingUnitCount) {
                            currentUnitCount = batchUnitCount;
                            remainingUnitCount = Math.max(remainingUnitCount - batchUnitCount, 0);
                        } else {
                            currentUnitCount = remainingUnitCount;
                            remainingUnitCount = 0;
                        }
                    }
                    return {
                        ...x,
                        canRefundUnitCount: x.unitCount,
                        goodsBatchInfoSnap: batchInfo,
                        unitCount: currentUnitCount,
                        expiryDate: batchInfo.expiryDate,
                    };
                });
                const customItem = {
                    unitCount,
                    unit,
                    canRefundUnitCount: unitCount,
                    chargeFormItemBatchInfos,
                    productInfo,
                };
                this._batcherDialog = new DialogRefundBatchesSelector({
                    chargeItem: customItem,
                    needCostPrice: false,
                    action: '退药',
                    isAll: true,
                    onConfirm: (data) => this.handleBatchesChange(item, data),
                });
                this._batcherDialog.generateDialog();

            },

            destroyDialog() {
                if (this._batcherDialog) {
                    this._batcherDialog.destroyDialog();
                }
            },

            handleBatchesChange(item, data) {
                const {
                    chargeFormItemBatchInfos,
                } = data;

                const dispensingFormItemBatches = chargeFormItemBatchInfos
                    .filter((x) => x.unitCount > 0)
                    .map((x) => {
                        return {
                            ...x,
                            batchInfo: {
                                ...x,
                            },
                        };
                    });

                this.list.forEach((x) => {
                    if (x.id === item.id) {
                        this.$set(x, 'dispensingFormItemBatches', dispensingFormItemBatches);
                    }
                });
                this.destroyDialog();
            },

            getItemBatches(batches) {
                return (batches || []).map((x) => {
                    const { batchInfo } = x;
                    const { expiryDate } = batchInfo || {};
                    return {
                        ...x,
                        expiryDate,
                    };
                });
            },
            cellMerge(row, col) {
                if (col.prop === 'goodsName') {
                    return {
                        colSpan: 1,
                        content: this.goodsNameRender,
                    };
                }

                if (col.prop === 'select') {
                    if (row.processUsageInfo) {
                        return {
                            colSpan: this.$route.query.qlTab === 2 ? 15 : this.params.tabValue2 === 2 ? 16 : 12,
                            content: () => {
                                return (<div class="custom-goods-name-td chinese">
                                    <div style="display: flex;">
                                        <span> {row.processUsageInfo}</span>
                                        {this.params.tabValue2 !== 2 ? (<div>
                                            {
                                                /*医嘱下达的标记*/
                                                row.renderData.tags?.map((tag) => {
                                                    const name = AdviceTagEnumSingleText[tag.type];
                                                    const color = AdviceTagEnumTextColor[tag.type];
                                                    return <SourceTag
                                                        key={name}
                                                        name={name}
                                                        color={color}
                                                        style="margin-left: 14px;"
                                                    ></SourceTag>;
                                                })
                                            }
                                            <span class="ellipsis remark" style="margin-left: 10px"
                                                  title={row.renderData.remark || ''}>{
                                                row.renderData.remark || ''
                                            }</span>
                                        </div>) : null}
                                    </div>
                                    <span style="color: #8d9aa8">{row.dispensingFormItemInfo}</span>

                                </div>);
                            },
                        };
                    }

                    return {
                        rowSpan: 1,
                        content: this.selectRender,
                    };
                }
                if (col.prop === 'checkbox') {
                    if (row.isChineseForm) {
                        return {
                            rowSpan: 2,
                            content: this.checkBoxRender,
                        };
                    }

                    return {
                        rowSpan: 1,
                        content: this.checkBoxRender,
                    };
                }
                if (col.prop === 'patientName') {
                    return {
                        rowSpan: 1,
                        content: this.patientNameRender,
                    };
                }
                return false;
            },
            formatMoney,
            formatLogList(logs = []) {
                if (!logs?.length) return [];

                return logs.map((o) => {

                    return {
                        action: o.action,
                        date: this.formatDate(o.created, 'YYYY-MM-DD HH:mm'),
                        type: o.actionName,
                        name: o.operatorName,
                        content: o.detail?.summery,
                        details: o.detail?.details,
                        newDetails: o.detail?.newDetails || [],
                    };
                });
            },
            formatBedsNo(beds) {
                if (!beds) return '-';
                const no = beds.bedNo?.toString()?.padStart(2, '0');
                return `${no}床`;
            },
            handleOperationCount(item) {
                const {
                    unitCount, doseCount, unit,
                } = item;

                return `${Big(unitCount).times(Big(doseCount))}${unit}`;
            },
            filterRejectList(rejectList) {
                return rejectList.filter((item) => {
                    if (item.isChineseForm) {
                        return item.dispensingFormItems.some((o) => o.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE) || item.dispenseType === dispenseOrderOperationTypeEnum.REJECT_RETURN;
                    }

                    return item.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE || item.dispenseType === dispenseOrderOperationTypeEnum.REJECT_RETURN;
                });
            },

            filterRejectListChineseItem(list) {
                if (!this.isReturnOrder) {
                    return list.filter((o) => o.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE);
                }

                return list;
            },

            handleExecute() {
                if (this.selectedDispenseList.some((item) => !item.dispenseType)) {
                    this.$Toast({
                        message: '请选择操作类型',
                        type: 'error',
                    });
                    return;
                }

                // 开启了追溯码采集
                if (this.traceCodeCollectionCheck && this.showHospitalPharmacyTraceCode) {
                    if (this.isReturnOrder) {
                        this.handleUnDispensing();
                    } else if (this.needTraceCodeFormItems.length) {
                        // 发药
                        TraceCode.validate({
                            scene: TraceCodeScenesEnum.PHARMACY,
                            sceneType: SceneTypeEnum.PHARMACY,
                            dataList: this.needTraceCodeFormItems,
                            getUnitInfo: (item) => {
                                return {
                                    ...item,
                                    unitCount: TraceCode.getUnitCount(item),
                                };
                            },
                            needGetMaxTraceCountList: TraceCode.isSupportTraceCodeForceCheckPharmacy(),
                        }).then((res) => {
                            if (!res.flag) {
                                this.handleFormatTraceCodeData().then(() => {
                                    this.showDialog = true;
                                });
                            } else {
                                this.showDialog = true;
                            }
                        }).catch((e) => {
                            console.log('追溯码校验报错', e);
                            this.showDialog = true;
                        });
                    } else {
                        this.showDialog = true;
                    }
                } else {
                    this.showDialog = true;
                }
            },
            // 库存不足数据分类
            onDispensing() {
                // isShortage来源于获取到数据后添加的
                const stockAdequateList = [];
                const stockShortageList = [];

                this.selectedDispenseList.forEach((item) => {
                    // 中药不整个处方发，判断逻辑修改
                    if (item.isChineseForm) {
                        if (item.dispensingFormItems.some((o) => {
                            return o.isShortage && o.dispenseType === dispenseOrderOperationTypeEnum.DISPENSE;
                        })) {
                            stockShortageList.push(item);
                        } else {
                            stockAdequateList.push(item);
                        }

                        return;
                    }

                    // 库存不足的发药
                    if (item.isShortage && item.dispenseType === dispenseOrderOperationTypeEnum.DISPENSE) {
                        stockShortageList.push(item);
                    } else {
                        stockAdequateList.push(item);
                    }
                });

                this.stockShortageList = stockShortageList.map((item) => {
                    if (item.isChineseForm) {
                        return {
                            // table渲染数据
                            isChineseForm: true,
                            goodsName: `${item.renderData.cMSpec}-${item.dispensingFormItems.filter((d) => d.isShortage).map((e) => e.renderData.goodsName).join('、')}`,
                            manufactor: '',
                            spec: '',
                            commitCount: item.dispensingFormItems.reduce((r, e) => {
                                if (e.isShortage && e.dispenseType === dispenseOrderOperationTypeEnum.DISPENSE) {
                                    r += (e.renderData?.commitCount ?? 0);
                                }
                                return r;
                            }, 0),
                            pieceCount: item.dispensingFormItems.reduce((r, e) => {
                                if (e.isShortage && e.dispenseType === dispenseOrderOperationTypeEnum.DISPENSE) {
                                    r += (e.productInfo?.pieceCount ?? 0);
                                }
                                return r;
                            }, 0),
                            children: [{
                                ...item,
                                checked: false,
                                disabled: true,
                            }],
                        };
                    }
                    let children = this.goods2patient([item], this.params.tabValue2);
                    children = children.map((it) => {
                        return {
                            ...it,
                            checked: false,
                        };
                    });
                    return {
                        goodsName: item.renderData.goodsName,
                        manufactor: item.renderData.manufactor,
                        spec: item.renderData.spec,
                        pieceCount: item.renderData.pieceCount,// 这个值是转换为小单位的可发数量
                        productInfo: item.productInfo,
                        children,
                    };
                });
                // 转换为患者维度数据保存，方便和库存不足弹窗中选择的数据，进行合并
                this.stockAdequateList = this.goods2patient(stockAdequateList, this.params.tabValue2);
            },
            // 拒发
            onRejectDispensing() {
                this.rejectList = clone(this.goods2patient(this.selectedDispenseList, this.params.tabValue2));
                this.showRefund = true;
            },
            // 拒退
            onRejectUndispensing() {
                this.rejectList = clone(this.selectedDispenseList);
                this.showRefund = true;
            },
            // 根据药品维度数据，转换成患者维度数据,viewMode是当前数据的维度,filterStatus分类型过滤
            goods2patient(
                selectedList = [],
                viewMode = 1,
                filterStatus = [
                    dispenseOrderFormItemTypeEnum.WAITING,
                    dispenseOrderFormItemTypeEnum.RESET_DISPENSED,
                    dispenseOrderFormItemTypeEnum.RESET_UNDISPENSED,
                ],
            ) {
                // 患者维度选择不需要处理数据
                if (viewMode === 2) return selectedList;

                const patientDimensionList = formatDispenseOrderViewList(this.dispensingSheetView.dispensingSheetViewList || [], 2, this.isReturnOrder);

                return patientDimensionList.filter((item) => {
                    const selectedItem = selectedList.find((s) => {
                        if (item.isChineseForm) {
                            return s._formId === item._formId;
                        }
                        return s._goodsId === item._goodsId && s.tagType === item.tagType;
                    });

                    if (selectedItem && filterStatus.includes(item.renderData?.status)) {
                        // 将选择的操作类型更新到转换后的数据中
                        item.dispenseType = selectedItem.dispenseType;

                        if (item.isChineseForm) {
                            item.dispensingFormItems.forEach((o) => {
                                const selectedFormItem = selectedItem.dispensingFormItems.find((m) => m._id === o._id);
                                if (selectedFormItem) {
                                    o.dispenseType = selectedFormItem.dispenseType;
                                }
                            });
                        }

                        return true;
                    }
                    return false;
                });
            },
            // 库存不足弹窗，已选择药品数量
            stockShortageSelectedPatient(list = []) {
                return list.reduce((res, item) => {
                    if (item.checked) {
                        res += +item.renderData?.commitCount ?? 0;// 待发数量
                    }
                    return res;
                }, 0);
            },
            // 库存不足弹窗，是否禁用 checkbox
            stockShortageCheckboxDisabled(table, item) {
                // 已选中的不需要禁用
                if (item.checked) return false;
                // 已选择的项加当前项的数量，
                const count = this.stockShortageSelectedPatient(table.children) + item.renderData?.commitCount;
                // 判断是否大于库存量
                return count > table.pieceCount;
            },
            formatDate,
            // 类型筛选
            handleChangeType() {
                console.log('handleChangeType');
                const {
                    typeIdList, customTypeIdList,
                } = cascaderDataAdapter(this.goodsTypeOptions, this.selectedTypes);

                this.params.typeId = typeIdList;
                this.params.customTypeId = customTypeIdList;
            },
            async fetchAllGoodsTypes() {
                const { data } = await GoodsAPI.fetchGoodsClassificationV3({
                    queryType: 1,
                    needCustomType: 1,
                });
                this.goodsAllTypes = data && data.list.map((item) => {
                    const children = item.customTypes || [];
                    // 不添加未指定
                    //     if (children.length) {
                    //         children.push({
                    //             id: -item.id,
                    //             name: '未指定',
                    //             sort: 999,
                    //             typeId: +item.id,
                    //         });
                    //     }
                    return {
                        ...item,
                        children,
                    };
                });
            },
            async fetchData(isRefreshOrder) {
                if (!this.dispenseOrderId) return;
                try {
                    this.loading = true;
                    if (this.isBatchMode) {
                        if (this.batchIds.length) {
                            await this.$abcPage.$store.fetchDispenseOrderBatch(
                                this.batchIds,
                                {
                                    tab: this.$abcPage.$store.scrollParams.tab,
                                },
                            );
                        } else {
                            this.$abcPage.$store.state.dispensingSheetView = {
                                dispensingSheetViewList: [],
                                created: '',
                                wardAreaView: {
                                    name: '',
                                },
                            };
                        }
                    } else {
                        await this.$abcPage.$store.fetchDispenseOrder(
                            this.dispenseOrderId,
                            {
                                tab: this.$abcPage.$store.scrollParams.tab,
                            },
                        );
                    }
                    this.isReturnOrder = this.$abcPage.$store.scrollParams.tab === 2;
                    this.onTabChange(this.isReturnOrder ? 2 : this.params.tabValue2);
                    // 等renderList生成后判断是否全选
                    this.$nextTick(() => {
                        this.isCheckedAll = this.renderList.every((e) => e.checked);
                    });
                } catch (e) {
                    console.error(e);
                } finally {
                    // 详情单刷新时，重置用户操作
                    if (isRefreshOrder) {
                        this.buttonLoading = false;//发药退药拒发拒退按钮 loading
                        this.showRefund = false;// 拒发拒退弹窗开关
                        this.showStockShortage = false;// 库存不足弹窗开关
                        this.showDialog = false;// 发药退药弹窗开关
                        this.saveLoading = false;//发药退药确定弹窗按钮 loading
                    }
                    // eslint-disable-next-line abc/no-timer-id
                    setTimeout(() => {
                        this.loading = false;
                    }, 500);
                }
            },
            // 创建修改需要的数据结构
            createDispenseOrderReq(list, isReturnOrder = false) {
                return {
                    dispenseOrderReqs: createDispenseOrderReqs(list, isReturnOrder),
                };
            },
            onTabChange(val, tabChange = false) {
                const list = formatDispenseOrderViewList(this.dispensingSheetView.dispensingSheetViewList, val, this.isReturnOrder);

                // tab切换时不使用原来的状态
                if (!tabChange) {
                    // 保持勾选状态
                    // 这里使用map优化查询
                    const oldStatusMap = this.list.reduce((total, current) => {
                        total[current.id] = {
                            checked: current.checked,
                        };
                        return total;
                    }, {});

                    list.forEach((it) => {
                        const oldStatus = oldStatusMap[it.id];
                        if (oldStatus) {
                            Object.assign(it, oldStatus);
                        }
                    });
                }
                this.list = updateGroupIds(list, true);

                // 等renderList生成后判断是否全选
                this.$nextTick(() => {
                    this.isCheckedAll = this.renderList.every((e) => e.checked);
                });
            },
            // 选择行
            onCheckboxChange(item, checked) {
                const _item = this.list.find((i) => i._id === item._id);
                _item.checked = checked;

                // 检查全选框
                this.isCheckedAll = this.renderList.every((e) => e.checked);
            },
            // 选择操作
            onSelectChange(item, type) {
                const _item = this.list.find((i) => i._id === item._id);
                _item.dispenseType = type;
            },


            findOriFormAndFormItem(form, formItem) {
                const _form = this.list.find((l) => l._id === form._id);
                if (!_form) {
                    return null;
                }

                const _formItem = _form.dispensingFormItems.find((f) => f._id === formItem._id);
                if (!_formItem) {
                    return null;
                }

                return {
                    f: _form,
                    i: _formItem,
                };
            },

            onChineseSelectChange(form, formItem, val) {
                const res = this.findOriFormAndFormItem(form, formItem);
                if (!res) return;

                const {
                    f, i,
                } = res;

                if (val === dispenseOrderOperationTypeEnum.DISPENSE || val === dispenseOrderOperationTypeEnum.UNDISPENSE) {
                    i.dispenseType = val;

                    if (f.dispensingFormItems.every((o) => o.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE)) {
                        f.dispenseType = dispenseOrderOperationTypeEnum.UNDISPENSE;
                    } else {
                        f.dispenseType = dispenseOrderOperationTypeEnum.DISPENSE;
                    }
                    return;
                }

                if (val === dispenseOrderOperationTypeEnum.ALL_DESPENSE) {
                    f.dispenseType = dispenseOrderOperationTypeEnum.DISPENSE;
                    f.dispensingFormItems.forEach((o) => {
                        o.dispenseType = dispenseOrderOperationTypeEnum.DISPENSE;
                    });
                    return;
                }

                if (val === dispenseOrderOperationTypeEnum.ALL_UNDISPENSE) {
                    f.dispenseType = dispenseOrderOperationTypeEnum.UNDISPENSE;
                    f.dispensingFormItems.forEach((o) => {
                        o.dispenseType = dispenseOrderOperationTypeEnum.UNDISPENSE;
                    });
                    return;
                }


                if (val === dispenseOrderOperationTypeEnum.ALL_RETURN) {
                    f.dispenseType = dispenseOrderOperationTypeEnum.RETURN;
                    f.dispensingFormItems.forEach((o) => {
                        o.dispenseType = dispenseOrderOperationTypeEnum.RETURN;
                    });
                    return;
                }

                if (val === dispenseOrderOperationTypeEnum.ALL_REJECT_RETURN) {
                    f.dispenseType = dispenseOrderOperationTypeEnum.REJECT_RETURN;
                    f.dispensingFormItems.forEach((o) => {
                        o.dispenseType = dispenseOrderOperationTypeEnum.REJECT_RETURN;
                    });

                    return;
                }

                i.dispenseType = val;
                f.dispenseType = val;
            },
            // 全选
            onCheckboxAll(checked) {
                this.renderList.forEach((item) => {
                    const findItem = this.list.find((it) => it.id === item.id);
                    if (findItem) {
                        this.$set(findItem, 'checked', checked);
                    }
                });
                // 等renderList生成后判断是否全选
                this.$nextTick(() => {
                    this.isCheckedAll = this.renderList.every((e) => e.checked);
                });
            },

            async batchSend(dispenseOrderReq) {
                const handleBatchTask = async (batchTaskList) => {
                    this.showDispensingSendLoading = true;
                    this.sendTotalCount = batchTaskList.length;
                    this.sendCurrentCount = 0;
                    this.sendFinish = false;
                    let currentTask = batchTaskList.shift();
                    while (currentTask) {
                        try {
                            await this.$abcPage.$store.putDispenseOrderConfirm(currentTask);
                            this.$abcEventBus.$emit('dispensing-batch-mode-id-delete', currentTask.dispenseOrderReqs[0].id);
                            this.sendCurrentCount++;
                        } catch (e) {
                            // 不是超时的错误直接结束
                            if (e.code !== AbcError.Constants.TIMEOUT.code) {
                                this.showDispensingSendLoading = false;
                                return {
                                    status: BatchSendStatusEnum.Error,
                                    error: e,
                                };
                            }
                            const isConfirm = await new Promise((resolve) => {
                                this.$confirm({
                                    type: 'warn',
                                    title: '部分药品末发药成功，请继续发药',
                                    content: `成功 ${this.sendCurrentCount} 张申请单，失败${this.sendTotalCount - this.sendCurrentCount}张申请单`,
                                    onConfirm: () => resolve(true),
                                    onCancel: () => resolve(false),
                                });
                            });
                            if (isConfirm) {
                                batchTaskList.unshift(currentTask);
                                this.showDispensingSendLoading = false;
                                return handleBatchTask(batchTaskList);
                            }
                            break;
                        }
                        currentTask = batchTaskList.shift();
                    }
                };

                const batchTaskList = dispenseOrderReq.dispenseOrderReqs.map((it) => ({
                    dispenseOrderReqs: [it],
                }));

                const taskLen = batchTaskList.length;

                const taskError = await handleBatchTask(batchTaskList);

                if (taskError) {
                    // 部分失败
                    if (taskLen !== batchTaskList.length + 1) {
                        return {
                            status: BatchSendStatusEnum.PartFinish,
                            error: taskError.error,
                        };
                    }

                    // 第一次就失败
                    return taskError;
                }

                this.sendFinish = true;
                await sleep(2_000);
                this.showDispensingSendLoading = false;
                return {
                    status: BatchSendStatusEnum.Finish,
                };
            },

            getBatchesTotalCount() {
                const tempList = this.goods2patient(this.selectedDispenseList, this.isReturnOrder ? 2 : this.params.tabValue2);
                const { dispenseOrderReqs } = this.createDispenseOrderReq(tempList);
                return dispenseOrderReqs.length;
            },

            // 执行弹窗确定
            async onConfirm() {
                // stockShortageList 库存不足的药品
                this.onDispensing();

                // 发药时有库存不足的药品
                if (!this.isReturnOrder && this.stockShortageList.length) {
                    this.showStockShortage = true;
                    this.showDialog = false;
                    return;
                }
                // 有拒发或者拒退的药品
                if (this.selectedDispenseList.some((item) => {
                    if (item.isChineseForm) {
                        return item.dispensingFormItems.some((o) => o.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE) || item.dispenseType === dispenseOrderOperationTypeEnum.REJECT_RETURN;
                    }

                    return item.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE || item.dispenseType === dispenseOrderOperationTypeEnum.REJECT_RETURN;
                })) {
                    if (this.isReturnOrder) {
                        this.onRejectUndispensing();
                    } else {
                        this.onRejectDispensing();
                    }
                    this.showDialog = false;
                    return;
                }


                // 执行时是发药并且没有库存不足的药或者是退药走原来的逻辑
                try {
                    this.saveLoading = true;
                    const tempList = this.goods2patient(this.selectedDispenseList, this.isReturnOrder ? 2 : this.params.tabValue2);
                    const dispenseOrderReq = this.createDispenseOrderReq(tempList);

                    if (!this.isReturnOrder) {
                        if (this.isBatchMode) {
                            const {
                                status, error,
                            } = await this.batchSend(dispenseOrderReq);
                            // 发药失败
                            if (status === BatchSendStatusEnum.Error) {
                                await Promise.reject(error);
                            }

                            // 部分发药失败
                            if (status === BatchSendStatusEnum.PartFinish) {
                                this.showDialog = false;
                                await this.fetchData();
                                await Promise.reject(error);
                            }
                        } else {
                            await this.$abcPage.$store.putDispenseOrderConfirm(dispenseOrderReq);
                        }
                    } else {
                        const deletedItem = [];
                        tempList.forEach((it) => {
                            if (it.isChineseForm) {
                                const dispensingFormItems = it.dispensingFormItems || [];
                                dispensingFormItems.forEach((item) => {
                                    if (item.productInfo?.status !== 1) {
                                        deletedItem.push(item);
                                    }
                                });
                            } else if (it.productInfo?.status !== 1) {
                                deletedItem.push(it);
                            }
                        });
                        if (deletedItem.length) {
                            const deletedItemsName = deletedItem.map((item) => item.name).join('、');
                            const deletedItemsGoodsId = deletedItem.map((item) => ({
                                goodsId: item.productInfo.id,
                                name: item.name,
                            }));
                            this.$confirm({
                                type: 'warn',
                                title: '商品档案已删除',
                                confirmText: '确认退药',
                                content: `${deletedItemsName}档案已删除，退药后将恢复档案并退回库存，确认退药吗？`,
                                onConfirm: async () => {
                                    // 恢复已删除的药品,接口调用会有不能恢复的情况
                                    try {
                                        await DispenseAPI.recoverGoodsArchive({
                                            items: deletedItemsGoodsId,
                                        }, true);

                                        await this.$abcPage.$store.putUndispenseOrderConfirm(dispenseOrderReq);

                                        this.showDialog = false;
                                        await this.fetchData();
                                        this.$Toast({
                                            message: '退药成功',
                                            type: 'success',
                                        });
                                    } catch (e) {
                                        if (e.code === 12818) {
                                            this.$alert({
                                                type: 'warn',
                                                title: e.message,
                                                content: e.detail,
                                            });
                                        } else {
                                            this.$Toast({
                                                type: 'error',
                                                message: e.message,
                                            });
                                        }
                                    }
                                },
                            });
                            return;
                        }
                        await this.$abcPage.$store.putUndispenseOrderConfirm(dispenseOrderReq);
                    }

                    this.showDialog = false;

                    await this.fetchData();

                    if (this.hospitalDispensingNeedPrint && !this.isReturnOrder) {
                        this.handlePrintMedicalPrescriptionBtn({
                            adviceType: 1,
                        });
                    }

                    this.$Toast({
                        message: this.isReturnOrder ? '退药成功' : '发药成功',
                        type: 'success',
                    });

                } catch (e) {
                    console.log(e);
                    this.handleSendError(e, () => {
                        this.showDialog = false;
                    });
                } finally {
                    this.saveLoading = false;
                }

            },

            handleSendError(e, onConfirm) {
                if (e?.code === 84301 || e?.code === 84302) {
                    const list = e?.detail || [];
                    const typeName = !this.isReturnOrder ? '发药' : '退药';
                    const h = this.$createElement;
                    this.$confirm({
                        type: 'warn',
                        title: `${typeName}失败`,
                        content:
                            h('div', [
                                h('div', {
                                    style: {
                                        marginBottom: '6px',
                                        color: '#000000',
                                        fontWeight: 'bold',
                                    },
                                }, `以下药品${typeName}失败，患者已不再住院中`),
                                h('div', {
                                    style: {
                                        width: '300px',
                                        maxHeight: '500px',
                                        overflowY: 'auto',
                                    },
                                }, list.map((name) => {
                                    return h('div', {
                                        style: {
                                            marginTop: '2px',
                                            color: '#7a8794',
                                        },
                                        class: {
                                            ellipsis: true,
                                        },
                                    }, name || '');
                                })),
                            ]),
                        onConfirm,
                    });
                }
            },

            onCancel() {
                this.showDialog = false;
            },
            async onRefundConfirm() {
                try {
                    this.saveLoading = true;
                    const dispenseOrderReq = this.createDispenseOrderReq(this.rejectList);

                    if (!this.isReturnOrder) {
                        if (this.isBatchMode) {
                            const {
                                status, error,
                            } = await this.batchSend(dispenseOrderReq);
                            // 发药失败
                            if (status === BatchSendStatusEnum.Error) {
                                await Promise.reject(error);
                            }

                            // 部分发药失败
                            if (status === BatchSendStatusEnum.PartFinish) {
                                this.showRefund = false;
                                await this.fetchData();
                                await Promise.reject(error);
                            }
                        } else {
                            await this.$abcPage.$store.putDispenseOrderConfirm(dispenseOrderReq);
                        }
                    } else {
                        await this.$abcPage.$store.putUndispenseOrderConfirm(dispenseOrderReq);
                    }

                    await this.fetchData();

                    if (this.hospitalDispensingNeedPrint && !this.isReturnOrder) {
                        this.handlePrintMedicalPrescriptionBtn({
                            adviceType: 1,
                        });
                    }

                    this.$Toast({
                        message: this.isReturnOrder ? '拒退成功' : '拒发成功',
                        type: 'success',
                    });

                    this.showRefund = false;
                } catch (e) {
                    console.log(e);
                    this.handleSendError(e, () => {
                        this.showRefund = false;
                    });
                } finally {
                    this.saveLoading = false;
                }
            },
            onRefundCancel() {
                this.showRefund = false;
            },
            async onStockShortageConfirm() {
                const stockShortageList = this.stockShortageList.reduce((res, item) => {
                    return res.concat(item.children?.filter((e) => e.checked) || []);
                }, []);

                // list已经是打平的患者维度数据
                const list = [...stockShortageList, ...this.stockAdequateList];

                // 无选中数据-不提交，关闭弹窗
                if (!list?.length) {
                    this.showStockShortage = false;
                    return;
                }

                // 有拒发-打开拒发弹窗
                if (this.selectedDispenseList.some((item) => {
                    if (item.isChineseForm) {
                        return item.dispensingFormItems.some((o) => o.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE);
                    }

                    return item.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE;
                })) {
                    this.rejectList = clone(list);
                    this.showRefund = true;
                    this.showDialog = false;
                    return;
                }

                const dispenseOrderReq = this.createDispenseOrderReq(list);
                try {
                    this.saveLoading = true;
                    if (this.isBatchMode) {
                        const {
                            status, error,
                        } = await this.batchSend(dispenseOrderReq);
                        // 发药失败
                        // 不关闭弹窗 因为没有发生数据变化
                        if (status === BatchSendStatusEnum.Error) {
                            await Promise.reject(error);
                        }

                        // 部分发药失败
                        if (status === BatchSendStatusEnum.PartFinish) {
                            this.showStockShortage = false;
                            await this.fetchData();
                            await Promise.reject(error);
                        }
                        await this.$abcPage.$store.putDispenseOrderConfirm(dispenseOrderReq);
                    }
                    this.showStockShortage = false;
                    await this.fetchData();

                    if (this.hospitalDispensingNeedPrint && !this.isReturnOrder) {
                        this.handlePrintMedicalPrescriptionBtn({
                            adviceType: 1,
                        });
                    }

                } catch (e) {
                    console.log(e);
                    this.handleSendError(e, () => {
                        this.showStockShortage = false;
                    });
                } finally {
                    this.saveLoading = false;
                }
            },
            onStockShortageCancel() {
                this.showStockShortage = false;
            },
            keydownHandle(e) {
                if (!this.showDialog) return;
                if (!this.isReturnOrder && this.showHospitalPharmacyTraceCode) return;

                const KEY_ENTER = 13;
                const KEY_ESC = 27;
                if (e.keyCode === KEY_ENTER) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.onConfirm();
                } else if (e.keyCode === KEY_ESC) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.onCancel();
                }
            },
            async handlePrescriptionPrint() {
                const tempList = this.goods2patient(this.selectedMedicinePrintList, this.isReturnOrder ? 2 : this.params.tabValue2);
                const { dispenseOrderReqs } = this.createDispenseOrderReq(tempList);
                const params = {
                    dispensingOrderList: dispenseOrderReqs.map((dispenseOrderReq) => ({
                        dispensingOrderId: dispenseOrderReq.id,
                        dispensingSheetIdList: dispenseOrderReq.dispenseSheetReqs.map((dispenseSheetReq) => dispenseSheetReq.id),
                    })),
                    tab: this.isReturnOrder ? 2 : 1,
                };
                await this.handlePrintMedicalPrescription({
                    adviceType: '住院药房处方',
                    reqParams: params,
                });

            },
            async getHospitalMedicineTagData(ids) {
                try {
                    return PrintAPI.fetchHospitalMedicineTagData({
                        type: 2, ids, dispenseOrderId: this.dispenseOrderId, tab: this.isReturnOrder ? 2 : 1,
                    });
                } catch (e) {
                    console.error(e);
                    return null;
                }
            },
            async getHospitalMedicineTagDataBatch(params) {
                try {
                    return PrintAPI.fetchHospitalMedicineTagData({
                        type: 3,
                        tab: 1,
                        ...params,
                    });
                } catch (e) {
                    console.error(e);
                    return null;
                }
            },
            async handleHospitalMedicineTag() {
                const tempList = this.goods2patient(this.selectedMedicinePrintList, this.isReturnOrder ? 2 : this.params.tabValue2);
                const { dispenseOrderReqs } = this.createDispenseOrderReq(tempList);
                const ids = [];
                tempList.forEach((temp) => {
                    const dispensingOrderId = temp._path?.[0]?._sheetId;
                    if (dispensingOrderId) {
                        ids.push(dispensingOrderId);
                    }
                });

                let responseData;

                if (this.isBatchMode) {
                    const params = {
                        dispensingOrderList: dispenseOrderReqs.map((dispenseOrderReq) => ({
                            dispensingOrderId: dispenseOrderReq.id,
                            dispensingSheetIdList: dispenseOrderReq.dispenseSheetReqs.map((dispenseSheetReq) => dispenseSheetReq.id),
                        })),
                        tab: this.isReturnOrder ? 2 : 1,
                    };
                    const { data } = await this.getHospitalMedicineTagDataBatch(params);
                    if (!data) return null;
                    responseData = data;
                } else {
                    const { data } = await this.getHospitalMedicineTagData(ids);
                    if (!data) return null;
                    responseData = data;
                }

                AbcPrinter.abcPrint({
                    templateKey: window.AbcPackages.AbcTemplates?.hospitalMedicineTag,
                    printConfigKey: ABCPrintConfigKeyMap.hospitalMedicineTag,
                    data: {
                        forms: responseData,
                        clinicName: this.currentClinic?.name || '',
                        isManualPreview: true,
                    },
                    mode: PrintMode.Electron,
                });
            },
            async handlePrintMedicalPrescriptionBtn(params) {
                const {
                    adviceType,
                } = params;
                if (adviceType === 'pharmacyPrintSetting') {
                    const { default: PrintConfigDialog } = await PrintConfigDialogModule();
                    new PrintConfigDialog({ scene: this.params.tabValue2 === 1 ? 'hospital-pharmacy-dispense-goods' : 'hospital-pharmacy-dispense-patient' }).generateDialogAsync({ parent: this });
                } else if (adviceType === 1) {
                    this.handlePrint();
                } else if (adviceType === 2) {
                    this.handlePrescriptionPrint();
                } else if (adviceType === 3) {
                    // 打印用药标签
                    this.handleHospitalMedicineTag();
                }
            },

            handlePrint() {
                const templateKey = this.isPatientPrintMode ? window.AbcPackages.AbcTemplates?.hospitalNursePatientDispensing : window.AbcPackages.AbcTemplates?.hospitalNurseGoodsDispensing;
                const printConfig = {
                    templateKey,
                    printConfigKey: ABCPrintConfigKeyMap.hospitalNursePatientDispensing,
                    mode: PrintMode.Electron,
                    extra: {
                        forceMultiPage: true,
                    },
                };

                // 患者维度
                let renderList = [];
                if (this.isPatientPrintMode) {
                    const renderDataMap = new Map();
                    // 过滤掉中药加工
                    const cacheSelectedPrintList = clone(this.selectedPrintList).filter((it) => it.isChinesForm !== false);
                    cacheSelectedPrintList.forEach((item) => {
                        if (!renderDataMap.has(item.patient.id)) {
                            renderDataMap.set(item.patient.id, {
                                patient: item.patient,
                                dispensingList: [],
                            });
                        }
                        const curItem = renderDataMap.get(item.patient.id);
                        curItem.dispensingList.push(item);
                    });
                    renderList = Array.from(renderDataMap.values());
                }

                const getFormatInfos = () => {
                    const wardNames = new Set();
                    const patientNames = new Set();
                    const created = new Set();
                    const pharmacyName = new Set();

                    const addToSet = ({
                        _wardName, _created, _pharmacyName, _patient: { name },
                    }) => {
                        wardNames.add(_wardName);
                        patientNames.add(name);
                        created.add(_created);
                        pharmacyName.add(_pharmacyName);
                    };

                    if (this.isPatientPrintMode) {
                        renderList.forEach(({ dispensingList }) => {
                            dispensingList.forEach(((item) => item._path.forEach(addToSet)));
                        });
                    } else {
                        this.selectedPrintList.forEach((item) => item._path.forEach(addToSet));
                    }

                    const createdList = [...created];
                    let createdResult;
                    if (createdList.length === 1) {
                        createdResult = createdList[0];
                    }
                    if (createdList.length > 1) {
                        // 看是否为同一天
                        const start = createdList[0];
                        const end = createdList[createdList.length - 1];
                        if (isSameDay(start, end)) {
                            createdResult = createdList[0];
                        } else {
                            createdResult = [start, end];
                        }
                    }

                    return {
                        wardAreaViewNames: [...wardNames].join(','),
                        patientNames: [...patientNames].join(','),
                        pharmacyNames: [...pharmacyName].join(','),
                        created: createdResult,
                    };
                };

                const batchFormatInfos = getFormatInfos();

                const printData = {
                    dispensingData: this.isPatientPrintMode ? renderList : this.selectedPrintList,
                    wardAreaViewName: this.isBatchMode ? batchFormatInfos.wardAreaViewNames : (this.dispensingSheetView?.wardAreaView?.name ?? ''),
                    pharmacyName: this.isBatchMode ? batchFormatInfos.pharmacyNames : (this.dispensingSheetView?.pharmacyName ?? ''),
                    created: this.isBatchMode ? batchFormatInfos.created : (this.dispensingSheetView?.created ?? ''),
                    medicalPrescriptionTitle: this.isReturnOrder ? '退药单' : '领药单',
                    isReturnOrder: this.isReturnOrder,
                    clinicName: this.currentClinic?.name,
                    formAppend: this.isBatchMode ? `患者：${batchFormatInfos.patientNames}` : '',
                };

                AbcPrinter.abcPrint({
                    ...printConfig,
                    data: printData,
                });
            },

            handleDispensingBatchModeChange({
                isBatchMode, batchIds, onAsyncFinish,
            }) {
                this.isBatchMode = isBatchMode;
                this.batchIds = batchIds;
                this.fetchData().then(() => onAsyncFinish());
            },

            changeHospitalDispensingNeedPrint(val) {
                this.hospitalDispensingNeedPrint = val;
                Printer.cache.set({
                    hospitalDispensingNeedPrint: val,
                });
            },

            handleDispenseSendClose() {
                this.showDispensingSendLoading = false;
                this.fetchData();
            },

            /**
             * 打开追溯码采集弹窗
             * @return {Promise<void>}
             */
            handleFormatTraceCodeData() {
                return new Promise((resolve, reject) => {
                    const confirmCallback = () => {
                        this.saveTraceCode().finally(() => {
                            this.calcTraceCodeCount();
                            resolve();
                        });
                    };
                    const cancelCallback = () => {
                        reject();
                    };
                    new HospitalPharmacyCollectionTraceCodeDialog({
                        list: this.needTraceCodeFormItems,
                        sceneType: SceneTypeEnum.PHARMACY,
                        dispensedList: this.dispensedTraceCodeFormItems,
                        confirmCallback,
                        cancelCallback,
                    }).generateDialogAsync({ parent: this });
                });
            },
            async unDispensingOnConfirmWithTraceCode(cacheSelectedDispenseList) {
                try {
                    const tempList = this.goods2patient(cacheSelectedDispenseList, 2);
                    const dispenseOrderReq = this.createDispenseOrderReq(tempList, true);

                    const deletedItem = [];
                    tempList.forEach((it) => {
                        if (it.isChineseForm) {
                            const dispensingFormItems = it.dispensingFormItems || [];
                            dispensingFormItems.forEach((item) => {
                                if (item.productInfo?.status !== 1) {
                                    deletedItem.push(item);
                                }
                            });
                        } else if (it.productInfo?.status !== 1) {
                            deletedItem.push(it);
                        }
                    });
                    if (deletedItem.length) {
                        const deletedItemsName = deletedItem.map((item) => item.name).join('、');
                        const deletedItemsGoodsId = deletedItem.map((item) => ({
                            goodsId: item.productInfo.id,
                            name: item.name,
                        }));
                        this.$confirm({
                            type: 'warn',
                            title: '商品档案已删除',
                            confirmText: '确认退药',
                            content: `${deletedItemsName}档案已删除，退药后将恢复档案并退回库存，确认退药吗？`,
                            onConfirm: async () => {
                                // 恢复已删除的药品,接口调用会有不能恢复的情况
                                try {
                                    this.loading = true;

                                    await DispenseAPI.recoverGoodsArchive({
                                        items: deletedItemsGoodsId,
                                    }, true);

                                    await this.$abcPage.$store.putUndispenseOrderConfirm(dispenseOrderReq);

                                    this.showDialog = false;
                                    await this.fetchData();
                                    this.$Toast({
                                        message: '退药成功',
                                        type: 'success',
                                    });
                                } catch (e) {
                                    if (e.code === 12818) {
                                        this.$alert({
                                            type: 'warn',
                                            title: e.message,
                                            content: e.detail,
                                        });
                                    } else {
                                        this.$Toast({
                                            type: 'error',
                                            message: e.message,
                                        });
                                    }
                                } finally {
                                    this.loading = false;
                                }
                            },
                        });
                        return;
                    }
                    this.loading = true;

                    await this.$abcPage.$store.putUndispenseOrderConfirm(dispenseOrderReq);

                    this.showDialog = false;
                    await this.fetchData();
                    this.$Toast({
                        message: this.isReturnOrder ? '退药成功' : '发药成功',
                        type: 'success',
                    });
                } catch (e) {
                    console.error(e);
                    this.handleSendError(e, () => {
                        this.showDialog = false;
                    });
                } finally {
                    this.loading = false;
                }
            },
            handleUnDispensing() {
                new HospitalPharmacyUnDispensingCollectionTraceCodeDialog({
                    list: this.selectedDispenseList,
                    onConfirm: this.unDispensingOnConfirmWithTraceCode,
                }).generateDialogAsync({ parent: this });
            },
            async saveTraceCode() {
                try {
                    if (!this.isReturnOrder && this.needTraceCodeFormItems.length) {
                        const { id } = this.dispensingSheetView;
                        await Dispensary.saveHospitalTraceCode(id, {
                            list: this.needTraceCodeFormItems.map((item) => {
                                return {
                                    id: item.id,
                                    shebaoDismountingFlag: item.shebaoDismountingFlag,
                                    traceableCodeList: item.traceableCodeList ? item.traceableCodeList.map((code) => {
                                        const {
                                            count, ...restCode
                                        } = code;
                                        return restCode;
                                    }) : [],
                                };
                            }),
                        });
                    }
                } catch (e) {
                    console.error(e);
                }
            },
            calcTraceCodeCount() {
                this.traceCodeCount = 0;
                this.needTraceCodeFormItems.forEach((item) => {
                    if (item.composeChildren) {
                        this.traceCodeCount = item.composeChildren.reduce((cur,next) => {
                            cur += (next.traceableCodeList?.length || 0);
                            return cur;
                        }, this.traceCodeCount);
                    } else {
                        this.traceCodeCount += (item.traceableCodeList?.length || 0);
                    }
                });
            },
            handleCheckNoStockShortageItem(item, table) {
                if (item.isChineseForm || this.stockShortageCheckboxDisabled(table, item)) return;
                this.$set(item, 'checked', !item.checked);
            },
            isShowMinusSign(action) {
                return [DispenseRecordActionEnum.REDRAW_TO_PHARMACY, DispenseRecordActionEnum.UNDISPENSE, DispenseRecordActionEnum.REJECT_UNDISPENSE].includes(action);
            },

            // 打开语音播报设置
            handleOpenAudioAnnouncementSetting() {
                new AudioAnnouncementSettingDialog().generateDialogAsync({ parent: this });
            },
            onAdviceTypeTabChange() {
                // 等renderList生成后判断是否全选
                this.$nextTick(() => {
                    this.isCheckedAll = this.renderList.every((e) => e.checked);
                });
            },
        },
    };
</script>

<style lang="scss" scoped>
.pharmacy-wrapper {
    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .right,
        .left {
            display: flex;
            align-items: center;

            .line {
                width: 1px;
                height: 26px;
                margin: 0 8px;
                background: $P1;
            }
        }
    }

    .main-content {
        padding-top: 0;
    }
}

.dialog-footer {
    .left {
        display: flex;
        align-items: baseline;
        margin-right: auto;

        .label {
            font-size: 14px;
        }
    }
}

.dialog-bottom-extend {
    position: absolute;
    bottom: -64px;
    left: 0;
    width: 100%;
    padding: 12px 24px;
    background: $P4;
    border-top: 1px solid $P6;
    border-radius: 0 0 var(--abc-border-radius-small) var(--abc-border-radius-small);

    .tip-text {
        margin-bottom: 6px;
        font-size: 12px;
        color: #7a8794;
        text-align: left;
    }
}

.tips {
    display: flex;
    align-items: center;
    height: 32px;
    padding: 10px 24px;
    color: $Y2;
    background: #f7ede3;
    box-shadow: inset 0 -1px 0 0 #f7ede3;
}

.stock-shortage-box {
    padding: 16px 24px;

    .goods-table {
        margin-bottom: 24px;

        .header {
            display: flex;
            align-items: center;
            height: 20px;
            padding-bottom: 8px;
            font-size: 14px;
            color: #000000;
            border-bottom: 1px solid $P6;

            .small-font {
                font-size: 12px;
                color: $T2;
            }
        }

        .body {
            li {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 40px;
                border-bottom: 1px solid $P6;
            }

            .checkbox-wrap {
                display: flex;
                align-items: center;
            }
        }

        .footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            height: 20px;
            margin-top: 8px;
            font-size: 14px;
            color: #000000;

            .gray {
                color: $T2;
            }
        }
    }
}
</style>


<style lang="scss">
@import "src/styles/abc-common.scss";
@import 'src/styles/mixin.scss';

.medical-prescription-list-table-wrapper {
    position: relative;

    &.has-inner-border {
        .tr {
            .td {
                display: flex;
                align-items: center;
                align-self: stretch;
                min-height: 40px;
                padding: 0;

                &:not(:last-child) {
                    border-right: 1px solid $P6;
                }

                .custom-td-cell {
                    padding: 0 12px;

                    .dd + .dd {
                        border-top: 1px solid $P6;
                    }

                    &.no-padding {
                        padding: 0;

                        .dd {
                            padding: 0 12px;
                        }
                    }

                    &.is-component {
                        height: 100%;
                        padding: 0;

                        > div {
                            height: 100%;
                        }
                    }
                }
            }
        }
    }

    .medical-prescription-list-table {
        background: #ffffff;
        border: 1px solid $P6;
        border-radius: var(--abc-border-radius-small);

        .table-header {
            display: flex;
            align-items: center;
            height: 32px;
            color: #8d9aa8;
            border-bottom: 1px solid $P6;
            border-radius: var(--abc-border-radius-small) var(--abc-border-radius-small) 0 0;
        }

        .table-body {
            position: relative;
            max-height: 519px;
            overflow-y: auto;
            overflow-y: overlay;

            .tr {
                display: flex;
                align-items: center;
                min-height: 40px;
                border-bottom: 1px solid $P6;

                &:last-child {
                    border-bottom: none;
                }
            }
        }

        .th,
        .td {
            padding: 0 12px;
        }

        .small-font {
            font-size: 12px;
        }

        .gray {
            color: $T2;
        }

        .td {
            .dd {
                width: 100%;
                height: 40px;
                line-height: 40px;
            }
        }
    }
}

.pharmacy-wrapper {
    .pharmacy-table {
        th {
            font-weight: normal;
            color: $T2;
            border-right: none;
        }

        .custom-medicine-td {
            position: relative;

            .group-line {
                position: absolute;
                top: 10px;
                right: 10px;
                z-index: 9;
                display: block;
                width: 7px;
                content: '';
                border-top: 2px solid #8f8f8f;
                border-right: 2px solid #8f8f8f;
                border-bottom: 2px solid #8f8f8f;
            }
        }

        .custom-td {
            display: flex;
            flex-direction: column;
            overflow: visible;

            &.chinese {
                .custom-dd {
                    &:first-child {
                        margin-bottom: -0.5px;
                    }
                }
            }

            .custom-dd {
                display: inline-flex;
                align-items: center;
                width: 100%;
                height: 40px;
                padding: 0 10px;
                font-size: 14px;

                & + .custom-dd {
                    border-top: 1px solid $P6;
                }

                .gray {
                    font-size: 12px;
                    color: $T2;
                }

                .total-count {
                    margin-right: 4px;
                }
            }
        }

        .custom-goods-name-td {
            display: inline-flex;
            align-items: center;
            width: 100%;
            height: 40px;
            padding: 0 10px;

            &.chinese {
                display: flex;
                justify-content: space-between;
            }

            & + .custom-goods-name-td {
                border-top: 1px solid $P6;
            }
        }

        .abc-source-tag {
            min-width: 18px;
        }

        .abc-table__fixed-left {
            border: 0;
        }
    }
}

.hospital-pharmacy-operation-details-popover {
    width: 510px;
    max-height: 328px;
    padding: 16px 10px 16px 16px;
    overflow: auto;

    @include scrollBar();
}

.pharmacy-operation-popover_wrapper {
    width: 510px;
    max-height: 600px;
    padding: 16px 16px 10px;
    overflow: auto;

    .pharmacy-operation-title {
        margin-bottom: 6px;
        font-weight: 500;
        line-height: 20px;
    }

    .pharmacy-operation-content {
        display: flex;
        flex-direction: column;
        font-size: 12px;

        &.is-chinese {
            flex-direction: row;
            flex-wrap: wrap;

            .pharmacy-operation-item {
                display: flex;
                justify-content: center;
                width: 25%;
                margin-bottom: 6px;

                &:nth-child(4n + 1) {
                    justify-content: flex-start;
                }

                &:nth-child(4n) {
                    justify-content: flex-end;
                }

                .pharmacy-operation-item-name {
                    max-width: 85px;
                    margin-right: 6px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }

        .pharmacy-operation-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6px;
        }
    }

    .pharmacy-operation-custom-item {
        margin-bottom: 6px;
        font-size: 12px;
        color: $T3;
    }
}

.hospital-dispense-send-loading-dialog-wrapper {
    .delete-icon-wrapper {
        position: absolute;
        top: 8px;
        right: 8px;
    }

    .current-item-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;

        .margin-left {
            margin-left: 4px;
        }
    }
}

.hospital-pharmacy-stock-shortage-item-patient {
    height: 44px;
    padding: 0 10px;
    border-bottom: 1px dashed var(--abc-color-P8);

    &:hover {
        background-color: var(--abc-color-B4);
    }
}

.hospital-pharmacy-dispensing-dialog-label {
    min-width: 70px;
    color: var(--abc-color-T2);
}
</style>

