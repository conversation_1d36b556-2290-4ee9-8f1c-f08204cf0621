// 合并选项,展示只有药品维度需要合并数据展示

import { GOODS_TYPE_NAME } from 'views/inventory/constant';
import {
    dispenseOrderFormItemType,
    dispenseOrderFormItemTypeEnum,
    dispenseOrderOperationTypeEnum,
    TagType,
} from './constant';
import { AdviceTagEnum } from '@/views-hospital/medical-prescription/utils/constants';
import Big from 'big.js';
import {
    complexCount, isChineseMedicine,
} from '@/filters';
import TraceCode from '@/service/trace-code/service';



function merge(oldItem, newItem, viewMode) {
    const item = {
        ...oldItem,
        _path: oldItem._path.concat(newItem?._path ?? []),
    };
    // 合并药品信息-数量、金额
    if (viewMode === 1) {
        let count1 = (oldItem?.doseCount ?? 0) * (oldItem?.unitCount ?? 0);
        let count2 = (newItem?.doseCount ?? 0) * (newItem?.unitCount ?? 0);
        if (!oldItem.useDismounting) {
            count1 *= (oldItem?.productInfo?.pieceNum ?? 1);
        }
        if (!newItem.useDismounting) {
            count2 *= (newItem?.productInfo?.pieceNum ?? 1);
        }
        // 不存在就创建
        if (!item.statusCount) {
            item.statusCount = {
                [oldItem.status]: count1,
            };
        } else {
            if (item.statusCount[newItem.status]) {
                item.statusCount[newItem.status] += count2;
            } else {
                item.statusCount[newItem.status] = count2;
            }
        }
        // 合并金额
        item.totalPrice = Big(oldItem.totalPrice ?? 0).plus(newItem?.totalPrice ?? 0).toNumber();
        // 汇总后端计算的unitCount
        item.unitCount = oldItem.unitCount + (newItem?.unitCount ?? 0);

        // 合并 dispensingFormItemBatches
        const oldBatchs = oldItem.dispensingFormItemBatches || [];
        const newBatchs = newItem.dispensingFormItemBatches || [];
        const _batches = [...oldBatchs, ...newBatchs];

        const _arr = _batches.reduce((acc, curr) => {
            const existingItem = acc.find((item) => item.batchId === curr.batchId);
            if (existingItem) {
                existingItem.unitCount += curr.unitCount;
            } else {
                acc.push({
                    ...curr,
                    unitCount: curr.unitCount,
                });
            }
            return acc;
        }, []);

        item.dispensingFormItemBatches = _arr;
    } else {
        console.log('合并患者数据');
    }
    return item;
}

// 格式化展示结果数据
function _formatResultItem(item, isReturnOrder) {
    let disabled = true;// 是否禁用 checkbox
    const [{
        // _sheet,
        _form,
    }] = item._path;
    const {
        type, subType, packageCount, pieceCount, pieceNum,
        prohibitPackageCount, prohibitPieceCount,
        availablePackageCount, availablePieceCount,
    } = item.productInfo;
    const typeName = GOODS_TYPE_NAME[`${type}-${subType}`];
    // 可发库存量 = 当前库存 - 禁售库存
    let _pieceCount = 0;
    if (availablePackageCount || availablePieceCount) {
        _pieceCount = (availablePackageCount || 0) * (pieceNum || 1) + (availablePieceCount || 0);
    } else {
        _pieceCount = ((packageCount || 0) * (pieceNum || 1) + (pieceCount || 0)) - ((prohibitPackageCount || 0) * (pieceNum || 1) + (prohibitPieceCount || 0));
    }
    // 待发库存量-待发、无需发药、补记录、仅扣库
    let commitCount = item.statusCount?.[dispenseOrderFormItemTypeEnum.WAITING] ?? 0;
    commitCount += item.statusCount?.[dispenseOrderFormItemTypeEnum.RESET_DISPENSED] ?? 0;
    commitCount += item.statusCount?.[dispenseOrderFormItemTypeEnum.RECORD_NOT_DISPENSE] ?? 0;
    commitCount += item.statusCount?.[dispenseOrderFormItemTypeEnum.STOCK_NOT_DISPENSE] ?? 0;
    // 已发库存量
    const pushCount = item.statusCount?.[dispenseOrderFormItemTypeEnum.DISPENSED] ?? 0;
    // 拒发库存量
    const rejectCount = item.statusCount?.[dispenseOrderFormItemTypeEnum.APPLY_DISPENSE_REJECT] ?? 0;
    // 待退库存量
    let returnCount = item.statusCount?.[dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE] ?? 0;
    returnCount += item.statusCount?.[dispenseOrderFormItemTypeEnum.RESET_UNDISPENSED] ?? 0;
    returnCount += item.statusCount?.[dispenseOrderFormItemTypeEnum.RECORD_NOT_DISPENSE] ?? 0;
    returnCount += item.statusCount?.[dispenseOrderFormItemTypeEnum.STOCK_NOT_DISPENSE] ?? 0;
    // 已退
    const returnDoneCount = item.statusCount?.[dispenseOrderFormItemTypeEnum.UNDISPENSED] ?? 0;
    // 拒退
    const rejectReturnCount = item.statusCount?.[dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT] ?? 0;

    const states = Object.keys(item.statusCount || {});
    let status = item.status || '';// 药品的状态
    let dispenseType = '';// 操作类型

    // 待退/重新发药
    if (states.every((s) => +s === dispenseOrderFormItemTypeEnum.WAITING || +s === dispenseOrderFormItemTypeEnum.RESET_DISPENSED)) {
        status = dispenseOrderFormItemTypeEnum.WAITING;
        dispenseType = dispenseOrderOperationTypeEnum.DISPENSE;
        disabled = false;
        // 全部已发
    } else if (states.every((s) => +s === dispenseOrderFormItemTypeEnum.DISPENSED)) {
        status = dispenseOrderFormItemTypeEnum.DISPENSED;
        dispenseType = dispenseOrderOperationTypeEnum.DISPENSE;
        // 全部已退
    } else if (states.every((s) => +s === dispenseOrderFormItemTypeEnum.UNDISPENSED)) {
        status = dispenseOrderFormItemTypeEnum.UNDISPENSED;
        dispenseType = dispenseOrderOperationTypeEnum.RETURN;
        // 全部拒发
    } else if (states.every((s) => +s === dispenseOrderFormItemTypeEnum.APPLY_DISPENSE_REJECT)) {
        status = dispenseOrderFormItemTypeEnum.APPLY_DISPENSE_REJECT;
        dispenseType = dispenseOrderOperationTypeEnum.UNDISPENSE;
        // 全部关闭拒发
    } else if (states.every((s) => +s === dispenseOrderFormItemTypeEnum.CLOSE)) {
        status = dispenseOrderFormItemTypeEnum.CLOSE;
        dispenseType = dispenseOrderOperationTypeEnum.UNDISPENSE;
        // 待退/重新退药
    } else if (states.every((s) => +s === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE || +s === dispenseOrderFormItemTypeEnum.RESET_UNDISPENSED)) {
        status = dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE;
        dispenseType = dispenseOrderOperationTypeEnum.RETURN;
        disabled = false;
        //     拒退
    } else if (states.every((s) => +s === dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT)) {
        status = dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE_REJECT;
        dispenseType = dispenseOrderOperationTypeEnum.REJECT_RETURN;
        // 部分发
    } else if (states.some((s) => +s === dispenseOrderFormItemTypeEnum.DISPENSED)) {
        // 会存在部分发，但是不会有部分退，退药都是以患者维度显示的
        status = dispenseOrderFormItemTypeEnum.PART_DISPENSED;
        dispenseType = dispenseOrderOperationTypeEnum.DISPENSE;
        // 并且存在待发的才允许勾选
        if (states.some((s) => +s === dispenseOrderFormItemTypeEnum.WAITING)) {
            disabled = false;
        }
        // 扣库
    } else if (states.every((s) => +s === dispenseOrderFormItemTypeEnum.STOCK_NOT_DISPENSE)) {
        dispenseType = isReturnOrder ? dispenseOrderOperationTypeEnum.RETURN : dispenseOrderOperationTypeEnum.DEDUCT;
        disabled = !isReturnOrder;
        // 存在待发
    } else if (states.some((s) => +s === dispenseOrderFormItemTypeEnum.WAITING)) {
        status = dispenseOrderFormItemTypeEnum.WAITING;
        dispenseType = dispenseOrderOperationTypeEnum.DISPENSE;
        disabled = false;
    }

    // 不使用汇总标记
    // else if (_sheet.status === dispenseOrderFormItemTypeEnum.RECORD_NOT_DISPENSE) {
    //     //_sheet上的status是汇总状态 补记
    //     dispenseType = dispenseOrderOperationTypeEnum.RECORD;
    // } else if (_sheet.status === dispenseOrderFormItemTypeEnum.STOCK_NOT_DISPENSE) {
    //     // 扣库
    //     dispenseType = dispenseOrderOperationTypeEnum.DEDUCT;
    // }

    const dispenseStatus = dispenseOrderFormItemType.find((s) => s.value === status);


    return {
        ...item,
        checked: true,
        disabled,
        dispenseType,
        isShortage: commitCount > _pieceCount,// 库存不足
        // isShortage: true,// 库存不足测试
        renderData: {
            patientName: item?.patient?.name || '',
            goodsName: item.name || '',
            status,
            statusName: dispenseStatus?.label ?? '',
            statusColor: dispenseStatus?.color ?? '',
            statusCountStr: item.statusCountStr || '',// 中药按剂显示
            statusTotalCount: item.statusTotalCount || '',// 中药总g数
            commitCount: commitCount || '',// 待发
            pushCount: pushCount || '',// 已发
            rejectCount: rejectCount || '',// 拒发
            returnCount: returnCount || '',// 待退
            rejectReturnCount: rejectReturnCount || '', // 拒退
            returnDoneCount: returnDoneCount || '', // 已退
            reason: item.usageInfo?.applyDispenseRemark || '',// 申请原因
            manufactor: item.productInfo?.manufacturerFull || item.productInfo?.manufacturer || '',
            way: _form?.usageInfo?.usage || '',
            step: _form?.usageInfo?.freq || '',
            doseCount: _form?.usageInfo?.doseCount || '',
            dosageUnit: _form?.usageInfo?.dosageUnit || '',
            days: _form?.usageInfo?.days || '',
            spec: item.productInfo?.displaySpec || '',
            cMSpec: item.productInfo?.cMSpec || '',
            goodsType: typeName || '',
            price: item.unitPrice || '',
            amount: item.totalPrice || '',
            remark: _form.usageInfo?.requirement || '',// 备注（医嘱）
            tags: _form.usageInfo?.adviceTags || [],// 标记（医嘱）
            comment: item.usageInfo?.dispenseRemark || '',// 备注（拒发、拒退原因）
            pieceCount: _pieceCount,
            dispenseType,
        },

    };
}

// 以不同显示维度打平展示，viewMode：1药品，2 患者
export function formatDispenseOrderViewList(dispensingSheetViewList = [], viewMode = 1, isReturnOrder = false) {

    let result = [];
    dispensingSheetViewList.forEach((sheet) => {
        const dispenseOrderId = sheet._dispenseOrderId;
        const wardId = sheet._wardId;
        const wardName = sheet._wardName;
        const created = sheet._created;
        const pharmacyName = sheet._pharmacyName;
        sheet.dispensingForms.forEach((form) => {
            let tagType = TagType.DEFAULT;// 默认
            const tagTypes = form.usageInfo?.adviceTags ?? [];
            if (tagTypes.length) {
                if (tagTypes.length === 2) {
                    tagType = TagType.URGENT_REPLENISH;
                } else {
                    tagType = tagTypes[0].type === AdviceTagEnum.URGENT ? TagType.URGENT : TagType.REPLENISH;
                }
            }

            // 判断是否是中药处方
            if (form.dispensingFormItems.some((item) => (item.productType === 1 && item.productSubType === 2))) {
                const itemsMap = new Map;
                // 将中药处方按照申请时间分组
                form.dispensingFormItems.forEach((item) => {
                    const key = `${item.created}`;
                    if (itemsMap.get(key)) {
                        const items = itemsMap.get(key);
                        items.push(item);
                    } else {
                        itemsMap.set(key, [item]);
                    }
                });

                itemsMap.forEach((items, key) => {
                    result.push({
                        ...form,
                        _patientId: sheet.patient?.id,
                        _formId: form.id,// 差异点
                        _dispenseOrderId: dispenseOrderId,
                        _path: [{
                            _sheetId: sheet.id,
                            _formId: form.id,
                            _sheet: sheet,
                            _form: form,
                            _wardId: wardId,
                            _wardName: wardName,
                            _created: created,
                            _pharmacyName: pharmacyName,
                            _patient: sheet.patient,
                        }],
                        _id: `${form.id}-${key}-${tagType}`,
                        patient: sheet.patient,
                        beds: sheet.beds,
                        tagType,// 前端定义医嘱标记类型
                        isChineseForm: true,// 标记是中药处方
                        dispensingFormItems: items.map((item) => {
                            return {
                                ...item,
                                _patientId: sheet.patient?.id,
                                _goodsId: item.productInfo?.id,// 差异点
                                _dispenseOrderId: dispenseOrderId,
                                _path: [{
                                    _sheetId: sheet.id,
                                    _formId: form.id,
                                    _sheet: sheet,
                                    _form: form,
                                    _wardId: wardId,
                                    _wardName: wardName,
                                    _created: created,
                                    _pharmacyName: pharmacyName,
                                    _patient: sheet.patient,
                                }],
                                _id: `${form.id}-${item.id}-${tagType}`,
                                patient: sheet.patient,
                                beds: sheet.beds,
                                tagType,// 前端定义医嘱标记类型
                                // tags: sheet.tags,// 加急、补开的标记
                            };
                        }),
                        _patientOrderId: sheet.patientOrderId,
                    });
                });

            } else {
                form.dispensingFormItems.forEach((item) => {
                    item._patient = sheet.patient;
                    item._patientOrderId = sheet.patientOrderId,
                    result.push({
                        ...item,
                        _patientId: sheet.patient?.id,
                        _goodsId: item.productInfo?.id,// 差异点
                        _dispenseOrderId: dispenseOrderId,
                        _path: [{
                            _sheetId: sheet.id,
                            _formId: form.id,
                            _formItemId: item.id,
                            _sheet: sheet,
                            _form: form,
                            _formItem: item,
                            _wardId: wardId,
                            _wardName: wardName,
                            _created: created,
                            _pharmacyName: pharmacyName,
                            _patient: sheet.patient,
                        }],
                        _id: `${form.id}-${item.id}-${tagType}`,
                        patient: sheet.patient,
                        beds: sheet.beds,
                        tagType,// 前端定义医嘱标记类型
                        groupId: sheet.groupId,
                        _patientOrderId: sheet.patientOrderId,
                    });
                });
            }
        });

    });

    // 按药品维度展示需要合并数据，后端结构默认是按患者的
    if (viewMode === 1) {
        // 需要根据医嘱标签分组，无标签、加急、补开、加急补开四种情况。在formItem才知道是什么类型的标签
        const res = [];
        const groups = {
            [TagType.DEFAULT]: {},
            [TagType.URGENT]: {},
            [TagType.REPLENISH]: {},
            [TagType.URGENT_REPLENISH]: {},
        };
        result.forEach((item) => {
            // 中药以整个处方保存
            if (item.isChineseForm) {
                res.push({
                    ...item,
                    dispensingFormItems: item.dispensingFormItems.map((it) => {
                        const {
                            unitCount, doseCount, unit,
                        } = it;
                        it.statusCountStr = `${unitCount}${unit || 'g'}*${doseCount}`;
                        it.statusTotalCount = `${unitCount * doseCount}${unit || 'g'}`;
                        it.statusCount = {
                            [it.status]: unitCount * doseCount,
                        };
                        return it;
                    }),
                });
            } else {
                const map = groups[item.tagType];
                const key = viewMode === 1 ? item._goodsId : item._patientId;
                if (map[key]) {
                    map[key] = merge(map[key], item, viewMode);
                } else {
                    // 第一条数据
                    map[key] = merge(item, {}, viewMode);
                }
            }
        });
        result = res.concat(Object.values(groups).map((map) => Object.values(map)).flat());
    } else {
        // 患者维度
        result.forEach((item) => {
            if (item.isChineseForm) {
                // 中药处方
                item.dispensingFormItems.forEach((it) => {
                    const {
                        unitCount, doseCount, unit,
                    } = it;
                    it.statusCountStr = `${unitCount}${unit || 'g'}*${doseCount}`;
                    it.statusTotalCount = `${unitCount * doseCount}${unit || 'g'}`;
                    it.statusCount = {
                        [it.status]: unitCount * doseCount,
                    };
                });
            } else {
                // 其他处方
                let count = item.doseCount * item.unitCount;
                if (!item.useDismounting) {
                    count *= item.productInfo.pieceNum;
                }
                item.originApplyCount = count;
                item.statusCount = {
                    [item.status]: count,
                };
            }

        });
    }


    return result.map((item) => {
        if (item.isChineseForm) {
            let disabled = true;// 是否禁用 checkbox
            const form = { ...item };
            const dispensingFormItems = form.dispensingFormItems.map((it) => _formatResultItem(it, isReturnOrder));
            const {
                status, statusName, statusColor, goodsType, cMSpec, reason, remark, comment, tags, dispenseType,
            } = dispensingFormItems?.[0]?.renderData || {};
            // 待发和待退才可以操作
            if ([dispenseOrderFormItemTypeEnum.WAITING, dispenseOrderFormItemTypeEnum.APPLY_UNDISPENSE].includes(status)) {
                disabled = false;
            }
            // 汇总数据
            const {
                commitCount, pushCount, rejectCount, returnCount, returnDoneCount, rejectReturnCount,
            } = dispensingFormItems.reduce((res, ite) => {
                return {
                    commitCount: res.commitCount + (ite.renderData.commitCount || 0),
                    pushCount: res.pushCount + (ite.renderData.pushCount || 0),
                    rejectCount: res.rejectCount + (ite.renderData.rejectCount || 0),
                    returnCount: res.returnCount + (ite.renderData.returnCount || 0),
                    returnDoneCount: res.returnDoneCount + (ite.renderData.returnDoneCount || 0),
                    rejectReturnCount: res.rejectReturnCount + (ite.renderData.rejectReturnCount || 0),
                };
            }, {
                commitCount: 0,
                pushCount: 0,
                rejectCount: 0,
                returnCount: 0,
                returnDoneCount: 0,
                rejectReturnCount: 0,
            });
            // 中药处方
            return {
                ...form,
                checked: true,
                disabled,
                isShortage: dispensingFormItems.some((e) => e.isShortage),// 有一个中药不足，就库存不足
                dispenseType,
                renderData: {
                    patientName: form?.patient?.name || '',
                    cMSpec,
                    status,
                    statusName,
                    statusColor,
                    commitCount: commitCount || '',// 待发
                    pushCount: pushCount || '',// 已发
                    rejectCount: rejectCount || '',// 拒发
                    returnCount: returnCount || '',// 待退
                    rejectReturnCount: rejectReturnCount || '', // 拒退
                    returnDoneCount: returnDoneCount || '', // 已退
                    way: form?.usageInfo?.usage || '',
                    step: form?.usageInfo?.freq || '',
                    goodsType,
                    reason,// 申请原因，一剂中药只展示一次
                    remark,// 医嘱备注
                    comment,// 拒发拒退原因
                    tags,// 医嘱标签
                    dispenseType,// 选中的操作类型
                },
                dispensingFormItems,
            };
        }
        return _formatResultItem(item, isReturnOrder);

    }).sort((a, b) => b.tagType - a.tagType);// 基于医嘱标签排序
}


// 根据打平数据勾选的，恢复后端返回的数据结构
export function createDispenseSheetReqs(list = [], isReturnOrder = false) {
    const dispenseSheetReqs = [];

    list.forEach((item) => {
        const paths = item._path;
        paths.forEach(({
            _sheetId, _formId, _formItem,
        }) => {
            const _sheetReq = dispenseSheetReqs.find((s) => s.id === _sheetId);
            const sheetReq = {
                id: _sheetId,
                dispenseType: item.dispenseType,
                dispenseFormReqs: [],
            };
            // 更新 sheet
            if (_sheetReq) {
                sheetReq.dispenseFormReqs = _sheetReq.dispenseFormReqs;
            } else {
                dispenseSheetReqs.push(sheetReq);
            }

            const _formReq = sheetReq.dispenseFormReqs.find((f) => f.id === _formId);
            const formReq = {
                id: _formId,
                dispenseFormItemReqs: [],
            };
            // 更新 form
            if (_formReq) {
                formReq.dispenseFormItemReqs = _formReq.dispenseFormItemReqs;
            } else {
                sheetReq.dispenseFormReqs.push(formReq);
            }
            // 中药处方只会一次处理完所有
            if (item.isChineseForm) {
                formReq.dispenseFormItemReqs = formReq.dispenseFormItemReqs.concat(
                    item.dispensingFormItems.map((it) => ({
                        id: it.id,
                        dispenseRemark: it.dispenseType === dispenseOrderOperationTypeEnum.UNDISPENSE || it.dispenseType === dispenseOrderOperationTypeEnum.REJECT_RETURN ? item.renderData?.comment : '', // 存储在item上
                        dispenseType: it.dispenseType,
                    })),
                );
            } else {
                // 更新 item
                const formItemReq = {
                    id: item.id,
                    dispenseRemark: item.renderData?.comment,
                    dispensingFormItemBatches: item.dispensingFormItemBatches,
                };
                if (isReturnOrder) {
                    if (TraceCode.isCompatibleHistoryData(item, true) || TraceCode.isNoTraceCodeGoods(item.productInfo)) {
                        const traceableCodeArr = [];
                        (item.selectedTraceCodeList ?? []).forEach((codeKeyId) => {
                            const findTraceableCode = (item._traceableCodeListOptions ?? []).find((it) => it.keyId === codeKeyId);
                            if (findTraceableCode) {
                                const {
                                    count, ...traceableCodeReq
                                } = findTraceableCode;
                                traceableCodeArr.push(traceableCodeReq);
                            }
                        });
                        formItemReq.traceableCodeList = traceableCodeArr;
                    } else {
                        formItemReq.traceableCodeList = (item.selectedTraceCodeList ?? []).map((selectedTraceCode) => {
                            const {
                                count, ...traceableCodeReq
                            } = selectedTraceCode;
                            return traceableCodeReq;
                        });
                    }
                } else if (Array.isArray(_formItem.traceableCodeList) && _formItem.traceableCodeList.length) {
                    formItemReq.traceableCodeList = _formItem.traceableCodeList;
                }
                formReq.dispenseFormItemReqs.push(formItemReq);
            }
        });
    });

    return dispenseSheetReqs;
}

export function createDispenseOrderReqs(list, isReturnOrder = false) {
    const dispenseOrders = Object.values(list.reduce((res, item) => {
        const orderId = item._dispenseOrderId;
        if (res[orderId]) {
            res[orderId].push(item);
        } else {
            res[orderId] = [item];
        }
        return res;
    }, {}));


    return dispenseOrders.map((sheets) => {
        return {
            id: sheets[0]._dispenseOrderId,
            dispenseSheetReqs: createDispenseSheetReqs(sheets, isReturnOrder),
        };
    });
}


export function setDispenseOrderInfo(dispensingSheetViewList, {
    dispenseOrderId, wardId, wardName, created, pharmacyName,
}) {
    dispensingSheetViewList.forEach((dispensingSheetView) => {
        dispensingSheetView._dispenseOrderId = dispenseOrderId;
        dispensingSheetView._wardId = wardId;
        dispensingSheetView._wardName = wardName;
        dispensingSheetView._created = created;
        dispensingSheetView._pharmacyName = pharmacyName;
    });
}

export function formatCount(goods, count, useDismounting = false) {
    let packageCount = 0;
    let pieceCount = 0;
    if (isChineseMedicine(goods)) {
        pieceCount = count;
    } else {
        if (!useDismounting) {
            const x = count % (goods.pieceNum || 1);
            packageCount = ~~(count / (goods.pieceNum || 1));
            pieceCount = Big(x).round(4, Big.roundDown).toNumber();
        } else {
            packageCount = 0;
            pieceCount = count;
        }
    }
    return complexCount({
        ...goods,
        packageCount,
        pieceCount,
    });
}


