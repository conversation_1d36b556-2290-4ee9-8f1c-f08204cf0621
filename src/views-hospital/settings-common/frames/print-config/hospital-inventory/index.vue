<template>
    <biz-fill-remain-height class="print-config-wrapper" style="height: 100%;">
        <template #header>
            <abc-manage-tabs
                :option="tabsOptions"
                :use-route-match="false"
                :value="printSelected"
                @change="changeTabsHandle"
            ></abc-manage-tabs>
        </template>
        <router-view></router-view>
    </biz-fill-remain-height>
</template>

<script>
    import { mapGetters } from 'vuex';
    import { AbcManageTabs } from '@/views/settings/components/abc-manage/index';
    import BizFillRemainHeight from '@/components-composite/setting-form-layout/src/views/fill-remain-height.vue';
    import store from '@/store';
    import { loadAbcPrint } from '@/printer/print-init/index.js';
    import AbcPrinter from '@/printer';

    export default {
        name: 'HospitalPrintInventory',
        components: {
            BizFillRemainHeight,
            AbcManageTabs,
        },
        beforeRouteEnter(to, from, next) {
            // 检查打印初始化是否完成
            const {
                chainBasic, clinicBasic,
            } = store.getters;
            const printPromise = loadAbcPrint({
                isEnableDesktopPrint: chainBasic.isEnableDesktopPrint, printConfig: clinicBasic.printConfig,
            });

            printPromise.finally(() => {
                AbcPrinter.setGlobalConfig();
                next();
            });
        },
        data() {
            return {
                printSelected: '',
            };
        },
        computed: {
            ...mapGetters(['isChainAdmin']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            tabsOptions() {
                return [{
                    label: '采购入库单',
                    value: 'inventory-print',
                    extend: [
                        'inventory-set',
                        'print-goods-in',
                    ],
                },{
                    label: '价签',
                    value: 'priceTag',
                }];
            },
        },
        watch: {
            '$route': {
                handler() {
                    this.findPrintSelected();
                },
                immediate: true,
            },
        },
        mounted() {
            this.findPrintSelected();
        },
        methods: {
            changeTabsHandle(value) {
                this.printSelected = value;
                this.$router.push({
                    name: value,
                });
            },
            findPrintSelected() {
                this.tabsOptions.forEach((item) => {
                    if (item.value === this.$route.name || (item.extend && item.extend.includes(this.$route.name))) {
                        this.printSelected = item.value;
                    }
                });
            },
        },
    };
</script>
