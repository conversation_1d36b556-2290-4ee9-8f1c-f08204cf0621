<template>
    <biz-setting-layout>
        <biz-setting-content>
            <div class="hospital-cashier-wrapper">
                <hospital-fee-bills-tab style="margin-bottom: 16px;"></hospital-fee-bills-tab>

                <div class="print-config-content">
                    <abc-form ref="printForm">
                        <div class="print-config-items">
                            <div class="group-title">
                                小票抬头
                            </div>

                            <div class="group-item">
                                <div class="group-label middle-group-label">
                                    抬头名称
                                </div>
                                <abc-form-item required class="print-form-item" :validate-event="validateName">
                                    <title-setting v-model="postData.title" :max-length="60"></title-setting>
                                </abc-form-item>

                                <div class="group-option">
                                    <abc-button type="text" @click="handleSetAllDocuments">
                                        应用至全部
                                    </abc-button>
                                </div>
                            </div>

                            <div class="group-item">
                                <div class="group-label">
                                    抬头信息
                                </div>

                                <div class="group-content">
                                    <abc-checkbox v-model="postData.clinicInfo.titleStyle" type="number" class="checkbox-no-margin">
                                        Logo
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.clinicInfo.address" type="number" class="checkbox-no-margin">
                                        地址
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.clinicInfo.mobile" type="number" class="checkbox-no-margin">
                                        电话
                                    </abc-checkbox>
                                </div>
                            </div>

                            <div class="group-item">
                                <div class="group-label">
                                    患者信息
                                </div>

                                <div class="group-content">
                                    <abc-checkbox v-model="postData.patientInfo.sex" type="number" class="checkbox-no-margin">
                                        性别
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.patientInfo.age" type="number" class="checkbox-no-margin">
                                        年龄
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.patientInfo.mobile" type="number" class="checkbox-no-margin">
                                        手机
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.patientInfo.department" type="number" class="checkbox-no-margin">
                                        科室
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.patientInfo.inPatientNo" type="number" class="checkbox-no-margin">
                                        住院号
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.patientInfo.inpatientTime" type="number" class="checkbox-no-margin">
                                        住院日期
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.patientInfo.dischargeTime" type="number" class="checkbox-no-margin">
                                        出院日期
                                    </abc-checkbox>
                                </div>
                            </div>

                            <div v-if="postData.patientInfo.mobile" class="group-item patient-group-item">
                                <div class="group-label">
                                    手机隐私保护
                                </div>
                                <div class="group-content">
                                    <abc-radio-group v-model="postData.patientInfo.mobileType" style="width: 100%;">
                                        <abc-radio :label="0" class="checkbox-no-margin">
                                            完整展示
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin">
                                            隐藏中间4位
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                        </div>

                        <div class="print-config-items">
                            <div class="group-title">
                                小票正文
                            </div>

                            <div class="group-item">
                                <div class="group-label">
                                    收费项目
                                </div>

                                <div class="group-content">
                                    <abc-checkbox v-model="postData.feeType" type="number">
                                        打印费用类型信息
                                    </abc-checkbox>
                                </div>
                            </div>

                            <div class="group-item">
                                <div class="group-label">
                                    收银信息
                                </div>
                                <div class="group-content">
                                    <abc-checkbox v-model="postData.cashierInfo.receivableFee" type="number" class="checkbox-no-margin">
                                        应付
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.cashierInfo.depositFee" type="number" class="checkbox-no-margin">
                                        预缴金
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.cashierInfo.netIncomeFee" type="number" class="checkbox-no-margin">
                                        实付
                                    </abc-checkbox>
                                </div>
                            </div>
                            <div class="group-item">
                                <div class="group-label">
                                    医保结算
                                </div>
                                <div class="group-content">
                                    <abc-checkbox v-model="postData.healthCardInfo.cardInfo" type="number" class="checkbox-no-margin">
                                        医保卡信息
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.healthCardInfo.settlementInfo" type="number" class="checkbox-no-margin">
                                        结算信息
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.healthCardInfo.balanceInfo" type="number" class="checkbox-no-margin">
                                        余额信息
                                    </abc-checkbox>
                                </div>
                            </div>

                            <div v-if="postData.healthCardInfo.settlementInfo" class="group-item">
                                <div class="group-label">
                                    小票规范
                                </div>

                                <div class="group-content">
                                    <abc-radio-group v-model="postData.healthCardInfo.zeroSettlementInfo" style="width: 100%;">
                                        <abc-radio :label="0" class="checkbox-no-margin" style="width: 50%;">
                                            不打印金额为0的医保结算信息
                                        </abc-radio>
                                        <abc-radio :label="1" class="checkbox-no-margin" style="width: 50%;">
                                            打印金额为0的医保结算信息
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>
                        </div>

                        <div class="print-config-items">
                            <div class="group-title">
                                小票页尾
                            </div>
                            <div class="group-item">
                                <div class="group-label">
                                    页尾信息
                                </div>
                                <div class="group-content">
                                    <abc-checkbox v-model="postData.clinicInfo.chargeOperator" type="number" class="checkbox-no-margin">
                                        收费员
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.clinicInfo.chargeDate" type="number" class="checkbox-no-margin">
                                        收费时间
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.clinicInfo.printDate" type="number" class="checkbox-no-margin">
                                        打印时间
                                    </abc-checkbox>
                                    <abc-checkbox v-model="postData.clinicInfo.qrCode" type="number" class="checkbox-no-margin">
                                        微诊所二维码
                                    </abc-checkbox>
                                    <abc-popover
                                        placement="top"
                                        :disabled="isOpenElectronicOrMedicalInvoice && !isLocalNetworkInvoice"
                                        trigger="hover"
                                        theme="yellow"
                                        class="checkbox-no-margin"
                                    >
                                        <abc-checkbox
                                            slot="reference"
                                            v-model="postData.invoiceCode"
                                            :disabled="!isOpenElectronicOrMedicalInvoice || isLocalNetworkInvoice"
                                            type="number"
                                        >
                                            查看电子发票
                                        </abc-checkbox>
                                        <div>
                                            <span>{{ isOpenElectronicOrMedicalInvoice ? '当前财政电子发票仅可使用财政专网访问，患者无法自主查看' : '开通电子发票后才可设置' }}</span>
                                        </div>
                                    </abc-popover>
                                    <abc-checkbox v-model="postData.clinicInfo.traceCodeQrCode" type="number" class="checkbox-no-margin">
                                        药品追溯二维码
                                    </abc-checkbox>
                                </div>
                            </div>

                            <div class="group-item">
                                <div class="group-label middle-group-label">
                                    公告提示
                                </div>
                                <div class="group-content">
                                    <abc-edit-div
                                        v-model="postData.remark"
                                        style="width: 486px; min-height: 52px; white-space: pre-wrap;"
                                        placeholder="可输入公告、提示、说明、备注等信息"
                                        :maxlength="100"
                                    ></abc-edit-div>
                                </div>
                            </div>
                        </div>
                    </abc-form>
                </div>
            </div>

            <template slot="footer">
                <biz-setting-footer>
                    <abc-button :disabled="disabled" :loading="btnLoading" @click="handleSave">
                        保存
                    </abc-button>
                    <abc-button type="blank" @click="handleReset">
                        恢复默认
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            <preview-layout is-ticket>
                <template slot="previewTab">
                    <div class="preview-tab-item preview-tab-item__active">
                        收费小票
                    </div>
                </template>
                <div slot="previewHtml" ref="previewMountPoint"></div>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import TitleSetting from 'views/settings/print-config/components/title-setting.vue';
    import clone from 'utils/clone';
    import { validateName } from 'views/inventory/goods/utils';
    import HospitalFeeBillsMixinPrint
        from '@/views-hospital/settings-common/frames/print-config/hospital-fee-bills/hospital-fee-bills-mixin-print';
    import { mapGetters } from 'vuex';
    import {
        HOSPITAL_CASHIER_DATA,
    } from '@/views-hospital/settings-common/frames/print-config/hospital-fee-bills/cashier/constant';
    import store from '@/store';
    import PreviewLayout from 'views/settings/print-config/components/preview-layout.vue';
    import { isEqual } from 'utils/lodash';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingSidebar,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import HospitalFeeBillsTab
        from '@/views-hospital/settings-common/frames/print-config/hospital-fee-bills/components/hospital-fee-bills-tab/index.vue';
    import {
        InvoiceCategory, InvoiceSupplierId,
    } from 'views/cashier/invoice/constants';
    import { InvoiceAreaCodeMap } from 'views/settings/charge-setting/invoice-setting/constant';

    const ResetData = {
        title: '', // 抬头名称
        // 抬头信息
        clinicInfo: {
            titleStyle: 0, // 抬头样式
            address: 1, // 地址
            mobile: 1, // 电话
            chargeOperator: 1, // 收费员
            chargeDate: 1, // 收费时间
            printDate: 1, // 打印时间
            remark: 0, // 收费备注
            qrCode: 1, // 微诊所二维码
            traceCodeQrCode: 0, // 追溯码二维码
        },
        // 患者信息
        patientInfo: {
            sex: 1, // 性别
            age: 1, // 年龄
            mobile: 1, // 手机
            department: 1, // 科室
            inPatientNo: 1, // 住院号
            medicalRecordNo: 0, // 病历号
            inpatientTime: 1, // 住院日期
            dischargeTime: 1, // 出院日期
            mobileType: 1, // 手机隐私保护, 0:完整展示, 1:隐藏中间4位
        },
        feeType: 1, // 打印费用类型信息
        // 收银信息
        cashierInfo: {
            receivableFee: 1, // 应付
            depositFee: 1, // 预缴金
            netIncomeFee: 1, // 实付
        },
        // 医保结算
        healthCardInfo: {
            cardInfo: 0, // 医保卡信息
            settlementInfo: 1, // 结算信息
            balanceInfo: 0, // 余额信息
            zeroSettlementInfo: 0, // 医保结算信息, 0:不打印金额为0的医保结算信息, 1:打印金额为0的医保结算信息
        },
        remark: '', // 备注
        invoiceCode: 0, // 电子发票二维码 取值 0 1 默认值 0
    };

    export default {
        name: 'HospitalCashier',
        components: {
            HospitalFeeBillsTab,
            PreviewLayout,
            TitleSetting,
            BizSettingLayout,
            BizSettingContent,
            BizSettingSidebar,
            BizSettingFooter,
        },
        mixins: [HospitalFeeBillsMixinPrint],
        data() {
            return {
                postData: clone(ResetData),
                titleMaxLength: 30, // 标题最大长度
                btnLoading: false,
                currentExampleData: HOSPITAL_CASHIER_DATA,
                cachePostData: null,
            };
        },
        computed: {
            ...mapGetters([
                'printHospitalFeeBillsConfig',
                'currentClinic',
                'clinicBasicConfig',
            ]),
            ...mapGetters('invoice',[
                'isOpenInvoice',
                'isOpenMedicalInvoice',
                'isOpenIsvDigitalInvoice',
                'isOpenNuonuoDigitalInvoice',
                'writeInvoiceConfig',
            ]),
            isOpenElectronicOrMedicalInvoice() {
                return !!(this.isOpenInvoice || this.isOpenMedicalInvoice || this.isOpenIsvDigitalInvoice || this.isOpenNuonuoDigitalInvoice);
            },
            isLocalNetworkInvoice() {
                // 默认开票是财政开票，且是 北京、贵州、江苏这些地区的博思
                const defaultMedicalInvoice = this.writeInvoiceConfig?.invoiceCategory === InvoiceCategory.MEDICAL_ELECTRONIC;
                return defaultMedicalInvoice && [InvoiceAreaCodeMap.BEIJING, InvoiceAreaCodeMap.GUIZHOU, InvoiceAreaCodeMap.JIANGSU].includes(this.medicalElectronicInfo?.invoiceAreaCode) &&
                    this.medicalElectronicInfo.invoiceSupplierId === InvoiceSupplierId.FUJIAN_BOSI;
            },
            _cachePostData: {
                get() {
                    return this.cachePostData;
                },
                set(v) {
                    this.cachePostData = v;
                },
            },
            disabled() {
                return isEqual(this.postData, this._cachePostData);
            },
            previewPage() {
                return {
                    size: 'MM80',
                    orientation: 1,
                };
            },
        },
        watch: {
            'printHospitalFeeBillsConfig.hospitalCashier': {
                async handler(val) {
                    if (val) {
                        await this.handleInitialPostData(val, 'hospitalCashier');
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        created() {
            this.currentExampleData.clinicName = this.currentClinic?.shortName || '';
            this.currentExampleData.clinicBasicConfig = this.clinicBasicConfig;
            this.$store.dispatch('invoice/initInvoiceConfig');
        },
        methods: {
            validateName,
            getCurrentTemplate() {
                return window.AbcPackages.AbcTemplates.hospitalCashier;
            },
            instanceGlobalConfigHandler(newValue) {
                const newInstanceGlobalConfig = clone(store.getters.printGlobalConfig);
                newInstanceGlobalConfig.hospitalFeeBills.hospitalCashier = newValue;
                return newInstanceGlobalConfig;
            },
            handleSave() {
                this.$refs.printForm.validate(async (val) => {
                    if (val) {
                        await this.updatePrintConfig('hospitalCashier');
                    }
                });
            },
            handleReset() {
                this.handleResetPostData(ResetData);
            },
        },
    };
</script>

<style lang="scss">
.hospital-cashier-wrapper {
    padding: 0;

    .group-content {
        width: 100%;
    }
}
</style>
