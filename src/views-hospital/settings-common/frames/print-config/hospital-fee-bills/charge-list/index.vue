<template>
    <biz-setting-layout>
        <biz-setting-content>
            <biz-setting-form-header>
                <hospital-fee-bills-tab></hospital-fee-bills-tab>
            </biz-setting-form-header>

            <abc-form ref="printForm" item-no-margin>
                <biz-setting-form :label-width="74" divider-full-screen>
                    <!-- 前记 -->
                    <biz-setting-form-group title="前记">
                        <biz-setting-form-item
                            label="抬头名称"
                            label-line-height-size="medium"
                        >
                            <abc-form-item required class="print-form-item" :validate-event="validateName">
                                <title-setting v-model="postData.title" :max-length="titleMaxLength * 2"></title-setting>
                            </abc-form-item>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <!-- 正文 -->
                    <biz-setting-form-group title="正文">
                        <biz-setting-form-item label="清单内容">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox
                                    v-model="postData.feeInfo"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    收费项目
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.feeType"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    费用类型
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.settlementDetail"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    结算明细
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.feeInfo" label="项目信息">
                            <abc-radio-group v-model="postData.feeDetail">
                                <abc-flex gap="6" vertical>
                                    <abc-radio :label="0">
                                        打印费用项目
                                    </abc-radio>
                                    <abc-radio :label="1">
                                        打印医嘱项目
                                    </abc-radio>
                                </abc-flex>
                            </abc-radio-group>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="零元项目">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox
                                    v-model="postData.isPrintNonZeroItem"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    不打印费用为0的项目
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.feeInfo" label="项目明细">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox
                                    v-model="postData.medicalGrade"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    医保等级
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.code"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    国家代码
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.spec"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    规格
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.unitPrice"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    单价
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item label="追溯码信息" :label-width="94">
                            <abc-form-item>
                                <abc-radio-group v-model="postData.traceCode">
                                    <abc-flex gap="6" vertical>
                                        <abc-radio :label="1">
                                            打印追溯码
                                        </abc-radio>
                                        <abc-radio :label="2">
                                            打印追溯码查询二维码
                                        </abc-radio>
                                        <abc-radio :label="0">
                                            不打印追溯码
                                        </abc-radio>
                                    </abc-flex>
                                </abc-radio-group>
                            </abc-form-item>
                        </biz-setting-form-item>
                    </biz-setting-form-group>

                    <!-- 页尾 -->
                    <biz-setting-form-group title="页尾">
                        <biz-setting-form-item label="页尾签字行">
                            <abc-flex gap="12" wrap="wrap">
                                <abc-checkbox
                                    v-model="postData.sellerName"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    收费员
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.chargeTime"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    收费日期
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.chargeClinic"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    收费单位
                                </abc-checkbox>
                                <abc-checkbox
                                    v-model="postData.patientSignature"
                                    type="number"
                                    class="checkbox-no-margin"
                                >
                                    患者签字
                                </abc-checkbox>
                            </abc-flex>
                        </biz-setting-form-item>

                        <biz-setting-form-item v-if="postData.chargeClinic" label="收费单位">
                            <title-setting
                                v-model="postData.chargeClinicContent"
                                :width="486"
                                :max-length="40"
                                placeholder="输入收费单位"
                            ></title-setting>
                        </biz-setting-form-item>
                    </biz-setting-form-group>
                </biz-setting-form>
            </abc-form>

            <template slot="footer">
                <biz-setting-footer>
                    <abc-button :disabled="disabled" :loading="btnLoading" @click="handleSave">
                        保存
                    </abc-button>
                    <abc-button type="blank" @click="handleReset">
                        恢复默认
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>

        <biz-setting-sidebar>
            <preview-layout class="is-a4-preview-layout">
                <template slot="previewTab">
                    <div class="preview-tab-item preview-tab-item__active">
                        医疗清单
                    </div>
                </template>
                <div slot="previewHtml" ref="previewMountPoint"></div>
            </preview-layout>
        </biz-setting-sidebar>
    </biz-setting-layout>
</template>

<script>
    import TitleSetting from 'views/settings/print-config/components/title-setting.vue';
    import clone from 'utils/clone';
    import PreviewLayout from 'views/settings/print-config/components/preview-layout.vue';
    import { validateName } from 'views/inventory/goods/utils';
    import store from '@/store';
    import { mapGetters } from 'vuex';
    import { isEqual } from 'utils/lodash';
    import HospitalFeeBillsMixinPrint
        from '@/views-hospital/settings-common/frames/print-config/hospital-fee-bills/hospital-fee-bills-mixin-print';
    import {
        CHARGE_LIST_DATA,
    } from '@/views-hospital/settings-common/frames/print-config/hospital-fee-bills/charge-list/constant';
    import { getLengthWithFullCharacter } from 'views/settings/print-config/utils';
    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingSidebar,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import HospitalFeeBillsTab
        from '@/views-hospital/settings-common/frames/print-config/hospital-fee-bills/components/hospital-fee-bills-tab/index.vue';
    import {
        BizSettingForm, BizSettingFormGroup, BizSettingFormHeader, BizSettingFormItem,
    } from '@/components-composite/setting-form/index.js';

    const ResetData = {
        'title': '', // 抬头名称 字符串必填 String
        'subtitle': '', // 副抬头名称 可选值 String
        'medicalGrade': 1, // 医保等级, 可选值 0 1, 默认 1
        'code': 1, // 国家代码, 可选值 0 1, 默认 1
        'spec': 1, // 规格, 可选值 0 1, 默认 1
        'unit': 1, // 单位, 可选值 0 1, 默认 1
        'unitPrice': 1, // 单价, 可选值 0 1, 默认 1
        'count': 1, // 数量, 可选值 0 1, 默认 1
        'feeInfo': 1, // 收费项目, 可选值 0 1, 默认 1
        'feeType': 1, // 费用类型, 可选值 0 1, 默认 1
        'settlementDetail': 1, // 结算明细, 可选值 0 1, 默认 1
        'sellerName': 1, // 收费员, 可选值 0 1, 默认 1
        'chargeTime': 1, // 收费日期, 可选值 0 1, 默认 1
        'chargeClinic': 1, // 收费单位, 可选值 0 1, 默认 1
        'chargeClinicContent': '', // 收费单位, 默认值同 title, String
        'feeDetail': 0, // 项目信息, 0: 打印费用项目, 1: 打印医嘱项目, 默认 0
        'patientSignature': 0, // 患者签字, 可选值0 1, 默认 0
        'isPrintNonZeroItem': 0, // 是否打印金额为0的项目, 可选值0 1, 默认 0
        'traceCode': 0, // 追溯码二维码, 可选值0 1, 默认 0
    };

    export default {
        name: 'NursePatientDispensing',
        components: {
            HospitalFeeBillsTab,
            PreviewLayout,
            TitleSetting,
            BizSettingLayout,
            BizSettingContent,
            BizSettingSidebar,
            BizSettingFooter,
            BizSettingForm,
            BizSettingFormItem,
            BizSettingFormGroup,
            BizSettingFormHeader,
        },
        mixins: [HospitalFeeBillsMixinPrint],
        data() {
            return {
                postData: clone(ResetData),
                cachePostData: null,
                titleMaxLength: 15, // 标题最大长度
                btnLoading: false,
                currentExampleData: CHARGE_LIST_DATA,
            };
        },
        computed: {
            ...mapGetters([
                'printHospitalFeeBillsConfig',
                'currentClinic',
            ]),
            _cachePostData: {
                get() {
                    return this.cachePostData;
                },
                set(v) {
                    this.cachePostData = v;
                },
            },
            disabled() {
                return isEqual(this.postData, this._cachePostData);
            },
        },
        watch: {
            'printHospitalFeeBillsConfig.chargeFeeList': {
                async handler(val) {
                    if (val) {
                        await this.handleInitialPostData(val, 'chargeFeeList');
                    }
                },
                immediate: true,
                deep: true,
            },
        },
        created() {
            this.currentExampleData.clinicName = this.currentClinic?.shortName || '';
        },
        methods: {
            validateName,
            getCurrentTemplate() {
                return window.AbcPackages.AbcTemplates.chargeList;
            },
            instanceGlobalConfigHandler(newValue) {
                const newInstanceGlobalConfig = clone(store.getters.printGlobalConfig);
                newInstanceGlobalConfig.hospitalFeeBills.chargeFeeList = newValue;
                return newInstanceGlobalConfig;
            },
            async handleReset() {
                const res = await this.handleResetPostData(ResetData);
                if (res) {
                    this.handleInstitution(this.postData);
                }
            },
            handleSave() {
                this.$refs.printForm.validate(async (val) => {
                    if (val) {
                        await this.updatePrintConfig('chargeFeeList');
                    }
                });
            },
            handleInstitution(data) {
                const { chargeClinicContent = '' } = data;

                if (!chargeClinicContent) {
                    const cacheClinicName = this.currentClinic.shortName || this.currentClinic.clinicName;
                    const {
                        fullCharacterLength: fullClinicCharacterLength, splitLength: splitClinicLength,
                    } = getLengthWithFullCharacter(cacheClinicName, 20);
                    if (fullClinicCharacterLength > 20) {
                        this.postData.chargeClinicContent = cacheClinicName.slice(0, splitClinicLength);
                    } else {
                        this.postData.chargeClinicContent = cacheClinicName;
                    }
                    return;
                }

                // 判断收费单位是否超过20个字符(需分区全角/半角),将超过部分拆分到副抬头
                const {
                    fullCharacterLength, splitLength,
                } = getLengthWithFullCharacter(chargeClinicContent, 20);
                if (fullCharacterLength > 20) {
                    this.postData.chargeClinicContent = chargeClinicContent.slice(0, splitLength);
                } else {
                    this.postData.chargeClinicContent = chargeClinicContent;
                }
            },
        },
    };
</script>

<style lang="scss">
.hospital-doctor-nurse-prescription-wrapper {
    padding: 0;
}
</style>
