<template>
    <biz-setting-layout>
        <biz-setting-content :content-style="{ padding: 0 }">
            <abc-manage-page v-abc-loading="loading" class="doctor-advice_execute-rules" :label-width="128">
                <abc-manage-group
                    v-if="allowOpenAdviceSupplement"
                    title="下达规则"
                >
                    <abc-manage-item
                        label="住院补开医嘱"
                    >
                        <abc-manage-layout wrap>
                            <abc-manage-tip>
                                <abc-checkbox v-model="adviceSupplementAllow" type="number">
                                    开启
                                </abc-checkbox>
                                <template slot="tip">
                                    <span>开启后，住院医生站可使用"补开医嘱"功能下达过去时间的医嘱</span>
                                    <div class="doctor-advice_open-setting">
                                        <abc-tooltip
                                            placement="bottom-start"
                                            :visible-arrow="false"
                                        >
                                            <abc-button type="text">
                                                补开医嘱规则
                                            </abc-button>
                                            <template #content>
                                                <abc-layout style="width: 324px;">
                                                    <abc-section>
                                                        <abc-p gray>
                                                            1.补开医嘱时医生可以选择过去时间作为开始时间，核对后将补过去时间的医嘱任务供护士执行
                                                        </abc-p>
                                                        <abc-p gray>
                                                            2.药品医嘱核对后可正常发药，如果药房判断已经发过药可以设置药品为"无需发药"的状态
                                                        </abc-p>
                                                        <abc-p gray>
                                                            3.检查检验医嘱核对后将不会再次生成检查检验申请单，执行后直接计费
                                                        </abc-p>
                                                    </abc-section>
                                                </abc-layout>
                                            </template>
                                        </abc-tooltip>
                                    </div>
                                </template>
                            </abc-manage-tip>
                        </abc-manage-layout>
                    </abc-manage-item>
                </abc-manage-group>
                <abc-manage-group title="执行规则">
                    <abc-manage-item
                        label="药品医嘱执行策略"
                    >
                        <abc-radio-group v-model="adviceExecuteRule">
                            <abc-manage-layout wrap>
                                <abc-manage-tip tip="完成医嘱核对后可直接执行">
                                    <abc-radio :label="0">
                                        核对后可执行
                                    </abc-radio>
                                </abc-manage-tip>
                                <abc-manage-tip tip="需要药房完成发药后才能执行">
                                    <abc-radio :label="1">
                                        发药后可执行
                                    </abc-radio>
                                </abc-manage-tip>
                            </abc-manage-layout>
                        </abc-radio-group>
                    </abc-manage-item>
                </abc-manage-group>
                <abc-manage-group title="执行时间">
                    <div class="doctor-advice-step_setting--table">
                        <abc-table
                            empty-content="暂无数据"
                            :render-config="tableHeader"
                            :loading="loading"
                            :data-list="executeTimeList"
                            @handleClickTr="handleEditItem"
                        >
                            <abc-table-cell
                                slot="step"
                                slot-scope="{ trData: item }"
                                :title="item.code"
                            >
                                <span style="display: inline-block; width: 100px;">{{ item.code }}</span><span style="color: #7a8794;">{{ item.description }}</span>
                            </abc-table-cell>
                            <abc-table-cell
                                slot="stepTime"
                                slot-scope="{ trData: item }"
                                :title="getExecuteStr(item)"
                                style=" color: var(--abc-color-theme1); cursor: pointer;"
                            >
                                {{ getExecuteStr(item) }}
                            </abc-table-cell>
                        </abc-table>
                        <doctor-advice-step-setting-dialog
                            v-if="isShowStepSetting"
                            v-model="isShowStepSetting"
                            :execute-item="curExecuteItem"
                        ></doctor-advice-step-setting-dialog>
                    </div>
                </abc-manage-group>
            </abc-manage-page>


            <template #footer>
                <biz-setting-footer>
                    <abc-button
                        :disabled="disabled"
                        :loading="btnLoading"
                        @click="updateHospitalExecuteAdviceRules"
                    >
                        保存
                    </abc-button>
                </biz-setting-footer>
            </template>
        </biz-setting-content>
    </biz-setting-layout>
</template>

<script>
    import {
        AbcManagePage,
        AbcManageGroup,
        AbcManageItem,
        AbcManageTip,
        AbcManageLayout,
    } from '@/views/settings/components/abc-manage/index';
    import {
        mapActions,
        mapGetters,
    } from 'vuex';
    import propertyAPI from 'api/property';
    import DoctorAdviceStepSettingDialog
        from '@/views-hospital/settings-common/frames/doctor-advice-step-setting/doctor-advice-step-setting-dialog.vue';
    import { getExecuteStr } from '@/views-hospital/settings-hospital/utils/execute-time';

    import {
        BizSettingLayout,
        BizSettingContent,
        BizSettingFooter,
    } from '@/components-composite/setting-form-layout/index.js';
    import { PRN_FREQ } from '@/views-hospital/medical-prescription/utils/constants';

    export default {
        name: 'DoctorAdviceExecuteRules',
        components: {
            DoctorAdviceStepSettingDialog,
            AbcManagePage,
            AbcManageGroup,
            AbcManageItem,
            AbcManageTip,
            AbcManageLayout,
            BizSettingLayout,
            BizSettingContent,
            BizSettingFooter,
        },
        data() {
            const tableHeader = {
                hasInnerBorder: false,
                list: [
                    {
                        label: '频率',
                        key: 'step',
                        style: {
                            width: '100px',
                        },
                    },
                    {
                        label: '默认执行时间',
                        key: 'stepTime',
                        style: {
                            width: '350px',
                            textAlign: 'left',
                        },
                    },
                ],
            };
            return {
                adviceExecuteRule: 0,
                adviceExecuteRuleCache: 0,
                adviceSupplementAllow: 0,
                adviceSupplementAllowCache: 0,
                btnLoading: false,
                loading: false,
                tableHeader,
                executeTimeList: [],
                isShowStepSetting: false,
                curExecuteItem: {
                    code: '',
                    executeTimingRule: {
                        dailyTimings: [],
                    },
                },
            };
        },
        computed: {
            ...mapGetters('viewDistribute', ['viewDistributeConfig' ]),
            ...mapGetters('executeTime', ['allExecuteTimeList']),
            allowOpenAdviceSupplement() {
                return this.viewDistributeConfig.Settings.openSetting.allowOpenAdviceSupplement;
            },
            disabled() {
                return this.adviceExecuteRule === this.adviceExecuteRuleCache && this.adviceSupplementAllow === this.adviceSupplementAllowCache;
            },
        },
        watch: {
            allExecuteTimeList: {
                handler(val) {
                    this.executeTimeList = val?.filter((item) => {
                        return item.code !== PRN_FREQ;
                    });
                },
                deep: true,
                immediate: true,
            },
        },
        async created() {
            this.initExecuteTimeList();
            await this.fetchHospitalExecuteAdviceRules();
        },
        methods: {
            ...mapActions('executeTime', ['initExecuteTimeList']),
            getExecuteStr,
            handleEditItem(item) {
                this.curExecuteItem = item;
                this.isShowStepSetting = true;
            },
            async fetchHospitalExecuteAdviceRules() {
                this.loading = true;
                try {
                    const { data } = await propertyAPI.getV3('clinicBasic.hospital', 'clinic');
                    const {
                        createAdvice = {},
                        executeAdvice = {},
                    } = data;
                    const {
                        beforeToday = 0,
                    } = createAdvice;
                    const {
                        afterDispensed = 0,
                    } = executeAdvice;
                    this.adviceExecuteRule = afterDispensed;
                    this.adviceExecuteRuleCache = afterDispensed;
                    this.adviceSupplementAllow = beforeToday;
                    this.adviceSupplementAllowCache = beforeToday;
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            async updateHospitalExecuteAdviceRules() {
                this.btnLoading = true;
                try {
                    await propertyAPI.updateV3('clinicBasic.hospital.executeAdvice', 'clinic', {
                        afterDispensed: this.adviceExecuteRule,
                    });
                    await propertyAPI.updateV3('clinicBasic.hospital.createAdvice', 'clinic', {
                        beforeToday: this.adviceSupplementAllow,
                    });
                    this.adviceExecuteRuleCache = this.adviceExecuteRule;
                    this.adviceSupplementAllowCache = this.adviceSupplementAllow;
                    this.$Toast.success('保存成功');
                } catch (err) {
                    console.log(err);
                    this.$Toast.error('保存失败');
                } finally {
                    this.btnLoading = false;
                }
            },
        },
    };
</script>

<style lang="scss">
.doctor-advice_execute-rules {
    width: 100%;
    height: auto;
    padding: 24px 14px 24px 24px;

    .doctor-advice_open-setting {
        display: inline-flex;
        align-items: center;

        div {
            display: inline-block;
        }
    }
}

.doctor-advice-step_setting--table {
    width: 100%;
}
</style>
