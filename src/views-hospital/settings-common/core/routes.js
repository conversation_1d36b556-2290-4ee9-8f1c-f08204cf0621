import AbcAccess from '@/access/utils';
import {
    MODULE_ID_MAP,
    RouterScope,
} from 'utils/constants';
import Introduce from 'views/edition/introduce';
import ProductCenter from 'views/settings/product-center';
import ProductCenterInvoice from 'views/settings/product-center/invoice';
import ProductCenterInvoiceDetail from 'views/settings/product-center/invoice-detail';
import ProductCenterOrder from 'views/settings/product-center/order';
import ProductCenterSettings from 'views/settings/product-center/product-center';
import Index from '../index.vue';
import { CHAIN_NOT_SUPPORT_PRICE_MAKE_UP_MODE } from '@/views-pharmacy/inventory/constant';

// 避免提前打包 以下路由异步加载
const PageAsync = () => import('./page.js');

const ClinicSetting = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/clinic/index.vue',
    ], resolve);
const ClinicBaseInfo = (resolve) =>
    require(['src/views/settings/clinic/base-info/form'], resolve);
const ClinicDepartments = (resolve) =>
    require(['src/views/settings/clinic/department/table'], resolve);
const DepartmentsForm = (resolve) =>
    require(['src/views/settings/clinic/department/form'], resolve);
const ClinicEmployee = (resolve) =>
    require(['src/views/settings/clinic/employee/table'], resolve);
const ClinicEmployeeForm = (resolve) =>
    require(['src/views/settings/clinic/employee/form'], resolve);
const medicalEquipments = (resolve) =>
    require(['src/views/settings/clinic/medical-equipment/table'], resolve);
const medicalEquipmentForm = (resolve) =>
    require(['src/views/settings/clinic/medical-equipment/form'], resolve);
const ClinicLicense = (resolve) =>
    require(['src/views/settings/clinic/license/index'], resolve);

// 费用
// const DiagnosisInspectFeeSetting = (resolve) => require([ '@/views-hospital/settings-common/frames/fee/diagnosis-inspect-fee-setting/diagnosis-inspect-fee-setting' ], resolve);
const FeeCommon = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/fee/index.vue',
    ], resolve);
// const Others = (resolve) => require(['src/views/settings/diagnosis-treatment/others/others.vue'], resolve);
const RegistrationFee = (resolve) =>
    require(['src/views-hospital/registered-fee/registered-fee.vue'], resolve);
const FeeType = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/fee/fee-type/index.vue',
    ], resolve);
const FeeItem = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/fee/fee-item/index.vue',
    ], resolve);

// 医嘱
const DoctorOrder = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/doctor-order/index.vue',
    ], resolve);
const DiagnosisExaminations = (resolve) =>
    require([
        'src/views/settings/diagnosis-treatment/examinations/index.vue',
    ], resolve);
const Targets = (resolve) =>
    require([
        'src/views/settings/diagnosis-treatment/targets/targets.vue',
    ], resolve);
const Treatments = (resolve) =>
    require([
        'src/views/settings/diagnosis-treatment/treatments/treatments.vue',
    ], resolve);
const NurseSetting = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/doctor-order/nurse/nurse-setting.vue',
    ], resolve);

const OperationSetting = (resolve) =>
    require([
        '@/views-hospital/settings-common/frames/doctor-order/surgery/surgery.vue',
    ], resolve);
// 会诊
const ConsultationItem = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/doctor-order/consultation/consultation-setting.vue',
    ], resolve);
// const Inspect = (resolve) => require([ 'src/views/settings/diagnosis-treatment/inspect/index.vue' ], resolve);
const InspectMain = (resolve) =>
    require(['src/views-hospital/inspect-setting/views/main.vue'], resolve);
const InspectDeviceDetail = (resolve) =>
    require([
        'src/views-hospital/inspect-setting/views/device-detail.vue',
    ], resolve);
const InstrumentDetail = (resolve) =>
    require([
        'src/views/settings/diagnosis-treatment/examinations/instrument/detail/index.vue',
    ], resolve);
const Compose = (resolve) =>
    require([
        'src/views/settings/diagnosis-treatment/compose/compose.vue',
    ], resolve);
// 关联设置
const Relation = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/relation/index',
    ], resolve);
const RelationTable = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/relation/relation-table',
    ], resolve);

/*打印设置*/
// 库存单据
const PrintInventoryConfig = () => import('src/views-hospital/settings-common/frames/print-config/hospital-inventory/index.vue');
const InventorySet = () => import('src/views/settings/print-config/inventory/index.vue');
const GoodsIn = () => import('src/views/settings/print-config/inventory/goodsIn/index.vue');

const PrintConfig = (resolve) =>
    require(['src/views/settings/print-config/index.vue'], resolve);
// 打印设置 医疗文书
const PrintMedicalDocuments = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/index.vue',
    ], resolve);
const PrintConfigPrescription = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/prescription/index.vue',
    ], resolve);
const PrintConfigGlassesPresciption = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/glasses-prescription/index.vue',
    ], resolve);
const PrintConfigMedical = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/medical/index.vue',
    ], resolve);
const PrintConfigExamination = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/examination/index.vue',
    ], resolve);
const PrintConfigInfusion = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/infusion/index.vue',
    ], resolve);
const PrintConfigTreatment = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/treatment/index.vue',
    ], resolve);
const PrintConfigInspection = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/inspection/index.vue',
    ], resolve);
const PrintConfigCDUS = (resolve) =>
    require(['src/views/settings/print-config/medical-documents/cdus/index.vue'],resolve);
const PrintConfigCT = (resolve) =>
    require(['src/views/settings/print-config/medical-documents/ct/index.vue'],resolve);
const PrintConfigDR = (resolve) =>
    require(['src/views/settings/print-config/medical-documents/dr/index.vue'],resolve);
const PrintConfigMR = (resolve) =>
    require(['src/views/settings/print-config/medical-documents/mr/index.vue'],resolve);
const PrintConfigIllnessCert = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/illness-cert/index.vue',
    ], resolve);
// 打印设置 小票
const PrintTickets = (resolve) =>
    require(['src/views/settings/print-config/tickets/index.vue'], resolve);
const PrintConfigCashier = (resolve) =>
    require([
        'src/views/settings/print-config/tickets/cashier/index.vue',
    ], resolve);
const PrintConfigRegistration = (resolve) =>
    require([
        'src/views/settings/print-config/tickets/registration/index.vue',
    ], resolve);
const PrintConfigDispensing = (resolve) =>
    require([
        'src/views/settings/print-config/tickets/dispensing/index.vue',
    ], resolve);
// 打印设置
const PrintReports = (resolve) =>
    require([
        'src/views/settings/print-config/medical-documents/reports.vue',
    ], resolve);
const PrintConfigMedicalBill = (resolve) =>
    require([
        'src/views/settings/print-config/medical-bills/bill/index.vue',
    ], resolve);
const PrintConfigReceipt = () => import('src/views/settings/print-config/medical-bills/receipt/index.vue');
const PrintConfigMedicalFeeList = (resolve) =>
    require([
        'src/views/settings/print-config/medical-bills/fee-list/index.vue',
    ], resolve);
const PrintMedicalStatement = (resolve) =>
    require([
        'src/views/settings/print-config/medical-bills/statement/index.vue',
    ], resolve);
// 标签打印
const PrintTags = (resolve) =>
    require(['src/views/settings/print-config/tags/index.vue'], resolve);
const PrintConfigMedicineTag = (resolve) =>
    require([
        'src/views/settings/print-config/tags/medicine/index.vue',
    ], resolve);
const PrintConfigPatientTag = (resolve) =>
    require([
        'src/views/settings/print-config/tags/patient/index.vue',
    ], resolve);

const ChargeSet = (resolve) =>
    require(['src/views/settings/charge-setting/index.vue'], resolve);
const ChargeSetBasic = (resolve) =>
    require(['src/views/settings/charge-setting/charge-setting.vue'], resolve);
const ChargeSeInvoice = (resolve) =>
    require(['src/views/settings/charge-setting/invoice-setting/index.vue'], resolve);

// 住院打印
const HospitalPrintConfig = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/index.vue'], resolve);
const HospitalMedicalDocuments = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-medical-record/index.vue'], resolve);
const DoctorMedicalPrescription = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-medical-record/doctor-medical-prescription/index.vue'], resolve);
const DoctorNursePrescription = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-medical-record/doctor-nurse-prescription/index.vue'], resolve);
const NursePatientDispensing = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-medical-record/nurse-patient-dispensing/index.vue'], resolve);
const HospitalPrescription = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-medical-record/hospital-prescription/index.vue'], resolve);
const HospitalInfusionRecord = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-medical-record/hospital-infusion-record/index.vue'], resolve);
const HospitalizationCertificate = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-medical-record/hospitalization-certificate/index.vue'],resolve);
const HospitalFeeBills = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-fee-bills/index.vue'], resolve);
const HospitalTags = () => import('src/views-hospital/settings-common/frames/print-config/hospital-tags/index.vue');
const ChargeList = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-fee-bills/charge-list/index.vue'], resolve);
const HospitalCashier = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-fee-bills/cashier/index.vue'], resolve);
const DepositReceipt = (resolve) => require(['src/views-hospital/settings-common/frames/print-config/hospital-fee-bills/deposit-receipt/index.vue'], resolve);
const HospitalMedicineTag = () => import('src/views-hospital/settings-common/frames/print-config/hospital-tags/hospital-medicine-tag/hospital-medicine-tag.vue');
const HospitalMedicalBill = () => import('views/settings/print-config/medical-bills/hospital-bill/index.vue');

// 发药设置
const DispenseSet = (resolve) =>
    require(['src/views/settings/dispense-setting/index.vue'], resolve);
const DispenseBasicSet = (resolve) =>
    require([
        'views/settings/dispense-setting/basic/dispense-setting',
    ], resolve);
const DispenseDispensingSet = (resolve) =>
    require(['views/settings/dispense-setting/dispensing/dispensing'], resolve);
const DispenseDecoctionSet = (resolve) =>
    require(['views/settings/dispense-setting/decoction/decoction'], resolve);
// const MultyPharmacySet = (resolve) => require(['views/settings/dispense-setting/multi-pharmacy/index.vue'], resolve);

const WeChatPay = (resolve) =>
    require(['src/views/settings/wechat-pay/index.vue'], resolve);

const PriceTaxrat = (resolve) =>
    require(['src/views/settings/price-taxrat/index.vue'], resolve);
// 税率和定价-定价设置
const PriceSettings = () => import('views/settings/price-taxrat/price.vue');
// 税率和定价-税率设置
const TaxratSettings = () => import('views/settings/price-taxrat/taxrat.vue');

const subClinicPriceSettings = () => import('src/views/settings/price-taxrat/sub-clinic-price-setting.vue');
const subClinicPriceSetting = () => import('src/views/settings/price-taxrat/sub-clinic-price.vue');
// const StockSet = (resolve) => require(['views/settings/stock-set/index'],resolve);
// const StockSetBasic = (resolve) => require(['views/settings/stock-set/basic'],resolve);
// const Certificate = resolve => require(['src/views/settings/certificate.vue'], resolve)
const WareHouseSet = (resolve) =>
    require(['views/settings/ware-house-setting/index'], resolve);
const WareHouseSetBasic = (resolve) =>
    require(['views/settings/ware-house-setting/basic'], resolve);
const WareHouseForm = (resolve) =>
    require(['views/settings/ware-house-setting/ware-house-detail'], resolve);
const InvoiceActionSetting = (resolve) =>
    require([
        'views/settings/ware-house-setting/invoice-action-setting',
    ], resolve);
const WarningProcurement = (resolve) =>
    require(['views/settings/ware-house-setting/warning-procurement'], resolve);
// const Certificate = resolve => require(['src/views/settings/certificate.vue'], resolve)
const GoodsTagSet = (resolve) =>
    require(['views/settings/goods-tag-setting/tag-setting'], resolve);

// 开出设置
const OpenSet = (resolve) =>
    require(['views/settings/open-setting/index.vue'], resolve);
const OpenSetting = (resolve) =>
    require(['views/settings/open-setting/open-setting.vue'], resolve);
const ReleaseRulesSetting = (resolve) =>
    require(['views/settings/open-setting/release-rules-setting.vue'], resolve);
const ReleaseRulesSettingAdd = (resolve) =>
    require(['views/settings/open-setting/release-rule-detail.vue'], resolve);

// 患者标签
const PatientTagSetIndex = (resolve) =>
    require(['views/settings/patient-tag-setting/index'], resolve);
const PatientTagSet = (resolve) =>
    require(['views/settings/patient-tag-setting/tag-setting'], resolve);

//区域检查检验中心
const AreaInspectionCenter = (resolve) => require(['src/views/settings/area-inspection-center/index.vue'],resolve);
const CooperativeStore = (resolve) => require(['views/settings/area-inspection-center/components/cooperative-store/index.vue'],resolve);
const CooperativeProject = (resolve) => require(['views/settings/area-inspection-center/components/cooperative-project/index.vue'],resolve);
const CooperativeIntroduce = (resolve) => require(['views/settings/area-inspection-center/components/cooperative-introduce/index.vue'],resolve);


// 叫号设置
const CallNumber = (resolve) =>
    require(['src/views/settings/call-number/index.vue'], resolve);

const OutpatientCall = () => import('src/views/settings/call-number/outpatient-call/index.vue');

const PharmacyCall = () => import('src/views/settings/call-number/pharmacy-call/index.vue');

// 自助登记
const SelfServiceRegistration = (resolve) =>
    require([
        'src/views/settings/self-service-registration/index.vue',
    ], resolve);
const SelfServiceRegistrationDiagnosis = (resolve) =>
    require([
        'src/views/settings/self-service-registration/self-service-registration-diagnosis.vue',
    ], resolve);
const SelfServiceRegistrationSetting = (resolve) =>
    require([
        'src/views/settings/self-service-registration/self-service-registration-setting.vue',
    ], resolve);

// 检验设置
const ExaminationSettings = (resolve) =>
    require(['src/views/settings/examination/index.vue'], resolve);
const ExaminationRule = (resolve) =>
    require(['src/views/settings/examination/rule.vue'], resolve);
const ExaminationSampleBarcodeSetting = (resolve) =>
    require(['src/views/settings/examination/sample-barcode/index.vue'], resolve);
const ExaminationSampleGroupSetting = (resolve) =>
    require(['src/views/settings/examination/sample-group/index.vue'], resolve);

// 检查设置
const InspectSettings = (resolve) =>
    require(['src/views/settings/inspect/index.vue'], resolve);
const InspectFileSync = (resolve) =>
    require(['src/views/settings/inspect/file-sync.vue'], resolve);
const InspectRule = (resolve) =>
    require(['src/views/settings/inspect/rule.vue'], resolve);

const InspectPACS = (resolve) =>
    require(['src/views/settings/inspect/pacs.vue'], resolve);

const InspectAutoReservation = (resolve) =>
    require(['src/views/settings/inspect/auto-reservation/index.vue'], resolve);

const InspectReportTemplate = (resolve) =>
    require(['src/views/settings/inspect/inspect-report-template/index.vue'], resolve);
const InspectDiagnosisAdvice = (resolve) =>
    require(['src/views/settings/inspect/diagnosis-advice/index.vue'], resolve);

// 医技设置
const SurgerySettings = () => import('views/settings/surgery/index.vue');

const SurgeryRoom = () => import('views/settings/surgery/room.vue');

// 自助服务机
const SelfService = (resolve) =>
    require(['src/views/settings/self-service/index.vue'], resolve);
const SelfServiceRule = (resolve) =>
    require(['src/views/settings/self-service/rule.vue'], resolve);
const SelfServiceScreen = (resolve) =>
    require(['src/views/settings/self-service/screen.vue'], resolve);

// 数据权限
const DataPermission = (resolve) =>
    require(['src/views/settings/data-permission/index.vue'], resolve);
const DataPermissionSettings = (resolve) =>
    require([
        'src/views/settings/data-permission/data-permission.vue',
    ], resolve);
const SafeLoginSettings = (resolve) =>
    require([
        'src/views/settings/data-permission/safe-login.vue',
    ], resolve);

// 网诊设置
const TreatOnlineSetting = (resolve) =>
    require(['src/views/settings/treat-online/treat-online.vue'], resolve);
const TreatOnlineRule = (resolve) =>
    require(['src/views/settings/treat-online/rule.vue'], resolve);
const TreatOnlineDoctors = (resolve) =>
    require(['src/views/settings/treat-online/doctors.vue'], resolve);

// 自动续费
const ContinueMedicalRecord = (resolve) =>
    require(['src/views/settings/continue-medical-record/index.vue'], resolve);
const ContinueMedicalRecordHome = (resolve) =>
    require(['src/views/settings/continue-medical-record/home.vue'], resolve);

// 家庭医生
const FamilyDoctorSettings = (resolve) =>
    require(['src/views/settings/family-doctor/index.vue'], resolve);
const FamilyDoctor = (resolve) =>
    require(['src/views/settings/family-doctor/family-doctor.vue'], resolve);

// 空中药房
const AirPharmacy = (resolve) =>
    require(['src/views/settings/air-pharmacy/index.vue'], resolve);
const AirPharmacySettings = (resolve) =>
    require(['src/views/settings/air-pharmacy/settings.vue'], resolve);
const AirPharmacyService = (resolve) =>
    require(['src/views/settings/air-pharmacy/service-pharmacy.vue'], resolve);
const AirPharmacyIntroduce = (resolve) =>
    require(['src/views/settings/air-pharmacy/service-introduce/index.vue'], resolve);

// 儿保配置
const ChildHealth = (resolve) =>
    require(['src/views/settings/child-health/index.vue'], resolve);
const ChildHealthSwitch = (resolve) =>
    require(['src/views/settings/child-health/rule.vue'], resolve);

const ChronicCare = (resolve) =>
    require(['src/views/settings/chronic-care/index.vue'], resolve);
const ChronicCareDetail = (resolve) =>
    require(['src/views/settings/chronic-care/detail.vue'], resolve);

// 用户日志
const UserLog = (resolve) =>
    require(['src/views/settings/user-log.vue'], resolve);

// lis 系统
const lisSystem = (resolve) =>
    require(['src/views/settings/lis-system/index.vue'], resolve);
const lisSystemContent = (resolve) =>
    require(['src/views/settings/lis-system/lis-system.vue'], resolve);

// 聚合支付
const aggregatePayment = (resolve) =>
    require(['src/views/settings/aggregate-payment/index'], resolve);
const aggregatePaymentContent = (resolve) =>
    require([
        'views/settings/aggregate-payment/components/aggregatePaymentContent.vue',
    ], resolve);

// 企微管家
const ScrmIndex = (resolve) =>
    require(['src/views/settings/scrm/index.vue'], resolve);
const ScrmDesc = (resolve) =>
    require(['src/views/settings/scrm/scrm-desc.vue'], resolve);

const Employees = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/employees/index.vue',
    ], resolve);

// 预约设置
const SchedulesModel = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/schedules/index.vue',
    ], resolve);
const Appointment = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/appointment/index.vue',
    ], resolve);
const Reservation = (resolve) =>
    require([
        'views/settings/registered-reservation/reservation-component.vue',
    ], resolve);

const ReservationComponent = (resolve) =>
    require([
        'views/settings/registered-reservation/reservation-component.vue',
    ], resolve);

const Schedules = (resolve) =>
    require([
        'src/views/settings/registered-reservation/schedules.vue',
    ], resolve);
const ReserveBase = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/reserve/base',
    ], resolve);
const ReserveSchedule = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/reserve/sehedule',
    ], resolve);

// 门诊设置
const OutpatientSetting = (resolve) =>
    require([
        'src/views-hospital/settings-common/frames/outpatient/index.vue',
    ], resolve);
const OutpatientBasic = (resolve) =>
    require([
        'src/views/settings/outpatient-setting/outpatient-setting.vue',
    ], resolve);

// 执行站设置
const TreatmentSettings = (resolve) =>
    require(['src/views/settings/treatment/index.vue'], resolve);
const TreatmentRule = (resolve) =>
    require(['src/views/settings/treatment/rule.vue'], resolve);

const TemplatePrescription = (resolve) =>
    require([
        'src/views-hospital/settings-outpatient/frames/template-prescription.vue',
    ], resolve);
const TemplateDiagnosisTreatment = (resolve) =>
    require([
        'src/views-hospital/settings-outpatient/frames/template-diagnosis-treatment.vue',
    ], resolve);
const TemplateMedicalRecord = (resolve) =>
    require([
        'src/views-hospital/settings-outpatient/frames/template-medical-record.vue',
    ], resolve);
const TemplateExecution = (resolve) =>
    require(['src/views/settings/templates/execution'], resolve);

// 文书
const InpatientArea = (resolve) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/inpatient-area-setting',
    ], resolve);
const InpatientAreaSetting = (resolve) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/inpatient-area-setting/inpatient-area-setting.vue',
    ], resolve);
const InpatientAreaSettingDetail = (resolve) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/inpatient-area-setting/inpatient-area-setting-detail',
    ], resolve);
const DoctorAdviceStep = (resole) =>
    require([
        'src/views-hospital/settings-common/frames/doctor-advice-step-setting',
    ], resole);

const DoctorAdviceExecute = (resole) => require(['src/views-hospital/settings-common/frames/doctor-advice-step-setting/doctor-advice-execute-rules'], resole);
const AutomaticChargeSetting = (resole) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/automatic-charge-setting/automatic-charge',
    ], resole);
const CareSetting = (resole) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/care-setting',
    ], resole);
const CareSettingDetail = (resole) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/care-setting/care-setting-detail',
    ], resole);

const MedicalDocumentManagement = (resole) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/medical-document-management-setting',
    ], resole);

const MedicalDocumentManagementMain = (resole) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/medical-document-management-setting/main',
    ], resole);

const AutoApplyCollectMedicine = (resole) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/pharmacy-setting/auto-apply-collect-medicine',
    ], resole);

const EmrSettingIndex = (resolve) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/emr-setting/index.vue',
    ], resolve);

const HospitalEmrSettingIndex = (resolve) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital-emr/index.vue',
    ], resolve);

const HospitalEmrSetting = (resolve) =>
    require([
        'src/views-hospital/settings-hospital/frames/hospital/emr-setting/hospital-emr-setting.vue',
    ], resolve);

const OutpatientEmrSetting = (resolve) =>
    require([
        '@/views-hospital/settings-hospital/frames/outpatient-emr/index.vue',
    ], resolve);


const OutpatientOtherEmr = (resolve) =>
    require([
        '@/views-hospital/settings-hospital/frames/hospital/emr-setting/hospital-outpatient-emr-setting.vue',
    ], resolve);

// 体检
const PhysicalExaminationProductIntroduce = (resolve) => require(['views/settings/physical-examination/product-introduction/index.vue'],resolve);
const PhysicalExaminationProjectSet = (resolve) =>
    require(['src/views/settings/physical-examination/project-set/index.vue'], resolve);
const PhysicalExaminationProjectExamination = (resolve) =>
    require(['src/views/settings/physical-examination/project-set/project-examination/index.vue'], resolve);
const PhysicalExaminationSet = (resolve) =>
    require(['src/views/settings/physical-examination/pe-set/index.vue'], resolve);
const PhysicalExaminationSetPublicHealth = (resolve) =>
    require(['src/views/settings/physical-examination/pe-set/public-health-set/index.vue'], resolve);

const PhysicalExaminationFlowSetting = () => import('src/views/settings/physical-examination/pe-set/flow-setting/index.vue');
const PhysicalExaminationProjectInspect = (resolve) =>
    require(['src/views/settings/physical-examination/project-set/project-inspect/index.vue'], resolve);
const PhysicalExaminationProjectOther = (resolve) =>
    require(['src/views/settings/physical-examination/project-set/project-other/index.vue'], resolve);
const PhysicalExaminationProjectCompose = (resolve) =>
    require(['src/views/settings/physical-examination/project-set/project-compose/index.vue'], resolve);

const PhysicalExaminationAssessmentSet = (resolve) =>
    require(['src/views/settings/physical-examination/assessment-set/index.vue'], resolve);
const PhysicalExaminationAssessmentOpinion = (resolve) =>
    require(['src/views/settings/physical-examination/assessment-set/assessment-opinion/index.vue'], resolve);
const PhysicalExaminationAssessmentTemplate = (resolve) =>
    require(['src/views/settings/physical-examination/assessment-set/assessment-template/index.vue'], resolve);
const PhysicalExaminationAssessmentTemplateIndividual = (resolve) =>
    require(['src/views/settings/physical-examination/assessment-set/assessment-template/individual/index.vue'], resolve);
const PhysicalExaminationAssessmentTemplatePublicHealthy = (resolve) =>
    require(['src/views/settings/physical-examination/assessment-set/assessment-template/public-healthy/index.vue'], resolve);

const ConsultationIndex = (resolve) => require(['@/views-hospital/settings-common/frames/consultation/index.vue'], resolve);
const ConsultationSetting = (resolve) => require(['@/views-hospital/settings-common/frames/consultation/setting.vue'], resolve);

// 合规监管
const RegulatoryIndex = (resolve) => require(['src/regulatory/base/view/index.vue'], resolve);
const RegulatoryReport = (resolve) => require(['src/regulatory/base/view/report/index.vue'], resolve);
const InHospitalBusiness = (resolve) => require(['src/regulatory/region/zhejiang/base/view/in-hospital-business/index.vue'], resolve);
const RegulatorySetting = (resolve) => require(['@/regulatory/region/zhejiang/hangzhou/view/setting/index.vue'], resolve);

const AntimicrobialManagementHospital = (resolve) => require(['src/views-hospital/settings-common/frames/antimicrobial-management/index.vue'], resolve);
const AntimicrobialManagement = (resolve) => require(['src/views/settings/antimicrobial-management/index.vue'], resolve);

//操作日志
const OperationLogSettings = (resolve) =>
    require([
        'src/views/settings/operation-log/index.vue',
    ],resolve);
const OperationLogDetail = (resolve) =>
    require([
        'views/settings/operation-log/components/operation-log-detail.vue',
    ],resolve);

export default {
    path: 'common-settings',
    name: '@common-settings',
    component: Index,
    meta: {
        name: '通用',
        needAuth: true,
        moduleId: MODULE_ID_MAP.setting,
        pageAsyncClass: PageAsync,
    },
    redirect: {
        name: 'baseinfo',
    },
    children: [
        // 机构人员start
        {
            path: 'clinic',
            component: ClinicSetting,
            name: 'clinic',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                needAuth: true,
            },
            redirect: {
                name: 'baseinfo',
            },
            children: [
                {
                    path: 'baseinfo',
                    component: ClinicBaseInfo,
                    name: 'baseinfo',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        name: '基本信息',
                        needAuth: true,
                    },
                },
                {
                    path: 'departments',
                    component: ClinicDepartments,
                    name: 'departments',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        name: '科室',
                        needAuth: true,
                    },
                },
                {
                    path: 'departments/:id',
                    component: DepartmentsForm,
                    name: 'departmentForm',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        needAuth: true,
                    },
                },
                {
                    path: 'equipments',
                    component: medicalEquipments,
                    name: 'medicalEquipments',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        needAuth: true,
                    },
                },
                {
                    path: 'equipments/:id',
                    component: medicalEquipmentForm,
                    name: 'medicalEquipmentForm',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        needAuth: true,
                    },
                },
                {
                    path: 'license',
                    component: ClinicLicense,
                    name: 'license',
                    meta: {
                        name: '证照资质',
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        needAuth: true,
                        scope:
                            RouterScope.CHAIN_ADMIN |
                            RouterScope.CHAIN_SUB |
                            RouterScope.SINGLE_STORE,
                    },
                },
            ],
        },
        {
            path: 'employeesModel',
            component: Employees,
            name: 'employeesModel',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                needAuth: true,
            },
            redirect: {
                name: 'employees',
            },
            children: [
                {
                    path: 'employees',
                    component: ClinicEmployee,
                    name: 'employees',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        needAuth: true,
                    },
                },
                {
                    path: 'employees-add',
                    component: ClinicEmployeeForm,
                    name: 'employeeAdd',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        needAuth: true,
                    },
                },
                {
                    path: 'employees/:id',
                    component: ClinicEmployeeForm,
                    name: 'employeeForm',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        needAuth: true,
                    },
                },
                {
                    path: 'datapermissionsettings',
                    component: DataPermissionSettings,
                    name: 'datapermissionsettings',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        name: '数据权限',
                        needAuth: true,
                    },
                },
                {
                    path: 'safeloginsettings',
                    component: SafeLoginSettings,
                    name: 'safeloginsettings',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        name: '安全登录',
                        needAuth: true,
                    },
                },
            ],
        },
        // 机构人员start
        {
            path: 'doctor-order',
            component: DoctorOrder,
            name: 'doctor-order',
            meta: {
                name: '医嘱项目',
                needAuth: true,
                needHospitalFee: true,
                moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
            },
            redirect: {
                name: 'examinations',
            },
            children: [
                {
                    path: 'examinations',
                    component: DiagnosisExaminations,
                    name: 'examinations',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '检验项',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'targets',
                    component: Targets,
                    name: 'ExaminationsTargets',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '指标项管理',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'treatments',
                    name: 'treatments',
                    component: Treatments,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '治疗项',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'inspect',
                    name: 'inspect-setting',
                    component: InspectMain,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '检查',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'device-detail',
                    name: '@deviceDetail',
                    component: InspectDeviceDetail,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '设备详情',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'nurse-setting',
                    name: 'nurse-setting',
                    component: NurseSetting,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '护理',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'consultation',
                    name: 'consultation',
                    component: ConsultationItem,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '会诊',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'operation',
                    name: 'operation',
                    component: OperationSetting,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '手术',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'instrumentDetail',
                    name: 'instrumentDetail',
                    component: InstrumentDetail,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '仪器详情',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'compose',
                    name: 'compose',
                    component: Compose,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '套餐',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'outpatient-setting',
            component: OutpatientSetting,
            name: 'outpatient-setting',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                name: '门诊设置',
                needAuth: true,
                scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
            },
            children: [
                {
                    path: 'outpatient-basic',
                    component: OutpatientBasic,
                    name: 'outpatient-basic',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        name: '门诊设置',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'treatment-settings',
            name: 'treatment-settings',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                name: '执行站设置',
                needAuth: true,
                scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
            },
            component: TreatmentSettings,
            children: [
                {
                    path: 'rule',
                    component: TreatmentRule,
                    name: 'treatmentrule',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        name: '规则设置',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'registered',
            component: Appointment,
            name: 'registered',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
            },
            redirect: {
                name: 'reservation',
            },
            children: [
                {
                    path: 'reservation',
                    component: Reservation,
                    name: 'reservation',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        needAuth: true,
                    },
                },
                {
                    path: 'treatment-reservation',
                    component: ReservationComponent,
                    name: 'treatment-reservation',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        needAuth: true,
                        visible(store) {
                            return store.getters[
                                'viewDistribute/featureTherapy'
                            ];
                        },
                    },
                },
                {
                    path: 'relation-base',
                    component: ReserveBase,
                    name: '@reserveBase',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        name: '预约基本设置',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
            ],
        },
        {
            path: 'schedulesModel',
            component: SchedulesModel,
            name: 'schedulesModel',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingSchedule,
            },
            redirect: {
                name: 'schedules',
            },
            children: [
                {
                    path: 'schedules',
                    component: Schedules,
                    name: 'schedules',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingSchedule,
                        needAuth: true,
                        scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
                    },
                },
                {
                    path: 'relation-schedule',
                    component: ReserveSchedule,
                    name: '@reserveSchedule',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingSchedule,
                        name: '检查检验排班设置',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
            ],
        },
        {
            path: 'inpatient-area',
            component: InpatientArea,
            name: '@inpatient-area',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingLive,
                needAuth: true,
            },
            redirect: {
                name: '@inpatient-area-setting',
            },
            children: [
                {
                    path: 'inpatient-area-setting',
                    component: InpatientAreaSetting,
                    name: '@inpatient-area-setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingLive,
                        name: '病区设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'inpatient-area-setting-detail/:id',
                    component: InpatientAreaSettingDetail,
                    name: '@inpatient-area-setting-detail',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingLive,
                        name: '病区设置详情',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'doctor-advice-step',
            component: DoctorAdviceStep,
            name: '@doctor-advice-step',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingLive,
                needAuth: true,
            },
            redirect: {
                name: '@doctor-advice-execute-rules',
            },
            children: [
                {
                    path: 'doctor-advice-execute-rules',
                    component: DoctorAdviceExecute,
                    name: '@doctor-advice-execute-rules',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingLive,
                        name: '医嘱执行设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'automatic-charge-setting',
                    component: AutomaticChargeSetting,
                    name: '@automatic-charge-setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingLive,
                        name: '医嘱计费设置',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'care-setting',
            component: CareSetting,
            name: '@care-setting',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingLive,
                needAuth: true,
            },
            redirect: {
                name: '@care-setting-detail',
            },
            children: [
                {
                    path: 'care-setting-detail',
                    component: CareSettingDetail,
                    name: '@care-setting-detail',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingLive,
                        name: '护理设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'auto-apply-collect-medicine',
                    component: AutoApplyCollectMedicine,
                    name: '@auto-apply-collect-medicine',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingLive,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'medical-record-management-setting',
            component: MedicalDocumentManagement,
            name: '@medical-record-management-setting',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingLive,
                needAuth: true,
            },
            redirect: {
                name: '@medical-record-management-setting-main',
            },
            children: [
                {
                    path: 'main',
                    component: MedicalDocumentManagementMain,
                    name: '@medical-record-management-setting-main',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingLive,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'fee-common',
            component: FeeCommon,
            name: 'fee-common',
            meta: {
                name: '费用项目',
                moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                needAuth: true,
                needHospitalFee: true,
            },
            children: [
                {
                    path: 'registration-fee',
                    component: RegistrationFee,
                    name: 'registration-fee',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'fee-type',
                    name: 'fee-type',
                    component: FeeType,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '费用类型',
                        needAuth: true,
                    },
                },
                {
                    path: 'fee-item',
                    name: 'fee-item',
                    component: FeeItem,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '费用项目',
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'relation',
            component: Relation,
            name: 'relation',
            meta: {
                name: '门诊用法关联项目',
                needAuth: true,
                needHospitalFee: true,
                moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
            },
            redirect: {
                name: 'outpatient-relation-table',
            },
            children: [
                {
                    path: 'outpatient-relation-table',
                    component: RelationTable,
                    name: 'outpatient-relation-table',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '门诊用法关联项目',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
                {
                    path: 'hospital-relation-table',
                    component: RelationTable,
                    name: 'hospital-relation-table',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '住院用法关联项目',
                        needAuth: true,
                        needHospitalFee: true,
                    },
                },
            ],
        },
        {
            path: 'treatonline',
            component: TreatOnlineSetting,
            name: 'treatOnlineSetting',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingWeClinic,
                name: '网诊设置',
                needAuth: true,
            },
            redirect: {
                name: 'treatOnlineRule',
            },
            children: [
                {
                    path: 'rule',
                    component: TreatOnlineRule,
                    name: 'treatOnlineRule',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingWeClinic,
                        needAuth: true,
                    },
                },
                {
                    path: 'doctors',
                    component: TreatOnlineDoctors,
                    name: 'treatOnlineDoctors',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingWeClinic,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'continuemedicalrecord',
            component: ContinueMedicalRecord,
            name: 'continuemedicalrecord',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingWeClinic,
                name: '自助续方',
                needAuth: true,
            },
            redirect: {
                name: 'continuemedicalrecord-home',
            },
            children: [
                {
                    path: 'home',
                    component: ContinueMedicalRecordHome,
                    name: 'continuemedicalrecord-home',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingWeClinic,
                        needAuth: true,
                    },
                },
                {
                    path: 'introduce',
                    component: Introduce,
                    name: 'continuemedicalrecord-introduce',
                    props: {
                        accessKey:
                            AbcAccess.accessMap.CHARGE_CLONE_PRESCRIPTION,
                        isAlwaysDisplay: true,
                        hideTitle: true,
                    },
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingWeClinic,
                        needAuth: true,
                    },
                },
            ],
        },
        // 体检系统start
        {
            path: 'physical-examination-product-introduce',
            component: PhysicalExaminationProductIntroduce,
            name: '@physicalExaminationProductIntroduce',
            meta: {
                needAuth: true,
            },
        },
        {
            path: 'physical-examination-project-set',
            component: PhysicalExaminationProjectSet,
            name: '@physicalExaminationProjectSet',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
            },
            redirect: {
                name: '@physical-project-examination',
            },
            children: [
                {
                    path: 'physical-project-examination',
                    component: PhysicalExaminationProjectExamination,
                    name: '@physical-project-examination',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                        needAuth: true,
                    },
                },
                {
                    path: 'physical-project-inspect',
                    component: PhysicalExaminationProjectInspect,
                    name: '@physical-project-inspect',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                        needAuth: true,
                    },
                },
                {
                    path: 'physical-project-other',
                    component: PhysicalExaminationProjectOther,
                    name: '@physical-project-other',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                        needAuth: true,
                    },
                },
                {
                    path: 'physical-project-compose',
                    component: PhysicalExaminationProjectCompose,
                    name: '@physical-project-compose',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'physical-examination-assessment-set',
            component: PhysicalExaminationAssessmentSet,
            name: '@physicalExaminationAssessmentSet',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
            },
            redirect: {
                name: '@physical-examination-assessment-opinion',
            },
            children: [
                {
                    path: 'physical-examination-assessment-opinion',
                    component: PhysicalExaminationAssessmentOpinion,
                    name: '@physical-examination-assessment-opinion',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                        needAuth: true,
                    },
                },
                {
                    path: 'physical-examination-assessment-template',
                    component: PhysicalExaminationAssessmentTemplate,
                    name: '@physical-examination-assessment-template',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                        needAuth: true,
                    },
                    children: [
                        {
                            path: 'individual',
                            name: '@physical-examination-assessment-template-individual',
                            component: PhysicalExaminationAssessmentTemplateIndividual,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                                needAuth: true,
                            },
                        },
                        {
                            path: 'public-healthy',
                            name: '@physical-examination-assessment-template-public-healthy',
                            component: PhysicalExaminationAssessmentTemplatePublicHealthy,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                                needAuth: true,
                            },
                        },
                        {
                            path: 'team',
                            name: '@physical-examination-assessment-template-team',
                            component: () => import('@/views/settings/physical-examination/assessment-set/assessment-template/team/index.vue'),
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                                needAuth: true,
                            },
                        },
                    ],
                },
            ],
        },
        {
            path: 'physical-examination-set',
            component: PhysicalExaminationSet,
            name: '@physicalExaminationSet',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
            },
            redirect: {
                name: '@physicalExaminationFlowSetting',
            },
            children: [
                {
                    path: 'physical-examination-flow-setting',
                    component: PhysicalExaminationFlowSetting,
                    name: '@physicalExaminationFlowSetting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                        needAuth: true,
                    },
                },
                {
                    path: 'physical-examination-set-public-health',
                    component: PhysicalExaminationSetPublicHealth,
                    name: '@physical-examination-set-public-health',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPhysicalExamination,
                        needAuth: true,
                    },
                },
            ],
        },
        // 体检系统end
        // 检查检验系统start
        {
            path: 'examination-settings',
            name: 'examination-settings',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                name: '检验设置',
                needAuth: true,
            },
            component: ExaminationSettings,
            children: [
                {
                    path: 'rule',
                    component: ExaminationRule,
                    name: 'examinationrule',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: '规则设置',
                        needAuth: true,
                        visible(store) {
                            return store.getters.isChainSubStore || store.getters.isSingleStore;
                        },
                    },
                },
                {
                    path: 'barcode',
                    component: ExaminationSampleBarcodeSetting,
                    name: '@examinationSampleBarcodeSetting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: '样本条码设置',
                        needAuth: true,
                        visible(store) {
                            return store.getters.isChainSubStore || store.getters.isSingleStore;
                        },
                    },
                },
                {
                    path: 'sample-group',
                    component: ExaminationSampleGroupSetting,
                    name: '@examinationSampleGroupSetting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: '采样组设置',
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'inspect-settings',
            name: 'inspect-settings',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                name: '检查设置',
                needAuth: true,
            },
            component: InspectSettings,
            children: [
                {
                    path: 'inspect-rule',
                    component: InspectRule,
                    name: 'inspectRule',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: '规则设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'inspect-report-template',
                    component: InspectReportTemplate,
                    name: '@inspectReportTemplate',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        needAuth: true,
                    },
                },
                {
                    path: 'inspect-diagnosis-advice',
                    component: InspectDiagnosisAdvice,
                    name: '@inspectDiagnosisAdvice',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        needAuth: true,
                    },
                },
                {
                    path: 'file-sync',
                    component: InspectFileSync,
                    name: 'inspectFileSync',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: '报告自动上传',
                        needAuth: true,
                    },
                },
                {
                    path: 'inspect-pacs',
                    component: InspectPACS,
                    name: 'inspectPACS',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: 'PACS服务设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'auto-reservation',
                    component: InspectAutoReservation,
                    name: 'autoReservation',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: '检查自动预约',
                        needAuth: true,
                    },
                },
                {
                    path: 'inspect-no',
                    component: () => import('@/views/settings/inspect/inspect-no/index.vue'),
                    name: '@inspectNo',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: '检查单号设置',
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'surgery-settings',
            name: 'surgery-settings',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                name: '手麻设置',
                needAuth: true,
            },
            component: SurgerySettings,
            children: [
                {
                    path: 'surgery-room',
                    component: SurgeryRoom,
                    name: 'surgeryRoom',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: '手术室设置',
                        needAuth: true,
                    },
                },
            ],
        },

        // 检查检验系统end
        {
            path: 'self-service-registration',
            component: SelfServiceRegistration,
            name: 'self-service-registration',
            meta: {
                moduleId: MODULE_ID_MAP.setting,
                name: '自助登记',
                needAuth: true,
            },
            redirect: {
                name: 'epidemiological-serve-introduce',
            },
            children: [
                {
                    path: 'epidemiological-setting',
                    component: SelfServiceRegistrationSetting,
                    name: 'epidemiological-setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.setting,
                        needAuth: true,
                        visible: () => {
                            // 需要有购买才能访问
                            return AbcAccess.getPurchasedByKey(
                                AbcAccess.accessMap.AUTO_REGISTER,
                            );
                        },
                    },
                },
                {
                    path: 'epidemiological-serve-introduce',
                    component: SelfServiceRegistrationDiagnosis,
                    name: 'epidemiological-serve-introduce',
                    meta: {
                        moduleId: MODULE_ID_MAP.setting,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'area-inspection-center',
            component: AreaInspectionCenter,
            name: 'InspectionCenter',
            meta: {
                moduleId: MODULE_ID_MAP.setting,
                name: '区域检查检验中心',
                needAuth: true,
            },
            children: [
                {
                    path: 'partners',
                    component: CooperativeStore,
                    name: 'partners',
                    meta: {
                        moduleId: MODULE_ID_MAP.setting,
                        name: '合作门店',
                        needAuth: true,
                        visible() {
                            return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.EXAM_CENTER_ORGAN);
                        },
                    },
                },
                {
                    path: 'project',
                    component: CooperativeProject,
                    name: 'project',
                    meta: {
                        moduleId: MODULE_ID_MAP.setting,
                        name: '服务项目',
                        needAuth: true,
                        visible() {
                            return AbcAccess.getPurchasedByKey(AbcAccess.accessMap.EXAM_CENTER_ORGAN);
                        },
                    },
                },
                {
                    path: 'introduce',
                    component: CooperativeIntroduce,
                    name: 'introduce',
                    meta: {
                        moduleId: MODULE_ID_MAP.setting,
                        name: '服务介绍',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'callnumber',
            component: CallNumber,
            name: 'callnumber',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                name: '叫号设置',
                needAuth: true,
            },
            redirect: {
                name: 'outpatient-call',
            },
            children: [
                {
                    path: 'outpatient-call',
                    component: OutpatientCall,
                    name: 'outpatient-call',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        needAuth: true,
                    },
                },
                {
                    path: 'pharmacy-call',
                    component: PharmacyCall,
                    name: 'pharmacy-call',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        needAuth: true,
                    },
                },
                {
                    path: 'inspect-call',
                    component: () => import('@/views/settings/call-number/inspect-call/index.vue'),
                    name: '@inspect-call',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        needAuth: true,
                    },
                },
                {
                    path: 'introduce',
                    component: Introduce,
                    name: 'call-number-introduce',
                    props: {
                        accessKey: AbcAccess.accessMap.CALLING_NUM,
                        isAlwaysDisplay: true,
                        hideTitle: true,
                    },
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: '',
            component: EmrSettingIndex,
            name: '@emr-setting',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                needAuth: true,
            },
            redirect: {
                name: 'hospitalMedicalDocument',
            },
            children: [
                {
                    path: 'hospital-emr-setting',
                    name: '@hospital-emr-setting',
                    component: HospitalEmrSettingIndex,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                        name: '住院文书设置',
                        needAuth: true,
                    },
                    children: [{
                        path: 'hospital-medical-document',
                        component: HospitalEmrSetting,
                        name: 'hospitalMedicalDocument',
                        meta: {
                            moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                            name: '住院文书设置',
                        },
                    }],
                },
                {
                    path: 'outpatient-emr-setting',
                    name: '@outpatient-emr-setting',
                    component: OutpatientEmrSetting,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                        name: '门诊文书设置',
                        needAuth: true,
                    },
                    children: [
                        {
                            path: 'medical-record',
                            component: TemplateMedicalRecord,
                            name: 'medicalrecordtemplate',
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                                name: '病历模板',
                            },
                        },
                        {
                            path: 'prescription',
                            component: TemplatePrescription,
                            name: 'prescriptiontemplates',
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                                name: '处方模板',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'diagnosis-treatment',
                            component: TemplateDiagnosisTreatment,
                            name: '@DiagnosisTreatmentTemplate',
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                                name: '诊疗模板',
                            },
                        },
                        {
                            path: 'execution',
                            component: TemplateExecution,
                            name: 'excutiontemplate',
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                                name: '执行记录模板',
                            },
                        },
                        {
                            path: 'other-medical-document',
                            component: OutpatientOtherEmr,
                            name: 'otherMedicalDocument',
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPaper,
                                visible(store) {
                                    return !store.getters.isChainAdmin;
                                },
                                name: '其他文书',
                            },
                        },
                    ],
                },
            ],
        },
        {
            path: 'selfservice',
            component: SelfService,
            name: 'selfservice',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingService,
                name: '自助服务机',
                needAuth: true,
            },
            redirect: {
                name: 'selfservicerule',
            },
            children: [
                {
                    path: 'rule',
                    component: SelfServiceRule,
                    name: 'selfservicerule',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingService,
                        needAuth: true,
                    },
                },
                {
                    path: 'screen',
                    component: SelfServiceScreen,
                    name: 'selfservicescreen',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingService,
                        needAuth: true,
                    },
                },
                {
                    path: 'introduce',
                    component: Introduce,
                    name: 'selfservice-introduce',
                    props: {
                        accessKey: AbcAccess.accessMap.SELF_SERVICE,
                        isAlwaysDisplay: true,
                        hideTitle: true,
                    },
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingService,
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'dispenseset',
            component: DispenseSet,
            name: 'dispenseset',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                name: '发药/加工/配送', // 药房设置
                needAuth: true,
            },
            redirect: {
                name: 'basicsettings',
            },
            children: [
                {
                    path: 'basic',
                    name: 'BasicSettings',
                    component: DispenseBasicSet,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        name: '发药设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'dispensing',
                    name: 'DispensingSettings',
                    component: DispenseDispensingSet,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        name: '配药设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'decoction',
                    name: 'DecoctionSettings',
                    component: DispenseDecoctionSet,

                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        name: '煎药设置',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'ware-house-set',
            component: WareHouseSet,
            name: 'ware-house-set',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                name: '库存设置',
                needAuth: true,
            },
            children: [
                {
                    path: 'basic',
                    name: 'wareHouseSetBasic',
                    component: WareHouseSetBasic,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        name: '库房管理',
                        needAuth: true,
                        visible(store) {
                            return !store.getters.isChainAdmin;
                        },
                    },
                },
                {
                    path: 'ware-house-add',
                    component: WareHouseForm,
                    name: 'wareHouseAdd',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        needAuth: true,
                        visible(store) {
                            return !store.getters.isChainAdmin;
                        },
                    },
                },
                {
                    path: 'basic/:id',
                    component: WareHouseForm,
                    name: 'wareHouseForm',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        needAuth: true,
                        visible(store) {
                            return !store.getters.isChainAdmin;
                        },
                    },
                },
                {
                    path: 'invoice-action-setting',
                    name: 'invoiceActionSetting',
                    component: InvoiceActionSetting,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        name: '进销存动作设置',
                        needAuth: true,
                        visible(store) {
                            return !store.getters.isChainAdmin;
                        },
                    },
                },
                {
                    path: 'warning-procurement',
                    name: 'warningProcurement',
                    component: WarningProcurement,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        name: '预警与采购设置',
                        needAuth: true,
                        visible(store) {
                            return !store.getters.isChainAdmin;
                        },
                    },
                },
                {
                    path: 'tags-management',
                    name: 'tagsManagement',
                    component: GoodsTagSet,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        name: '标签管理',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'open-set',
            component: OpenSet,
            name: 'open-set',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                name: '开出设置',
                needAuth: true,
            },
            children: [
                {
                    path: 'open-setting',
                    name: 'openSetting',
                    component: OpenSetting,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '开出规则设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'release-rules-setting',
                    name: 'releaseRulesSetting',
                    component: ReleaseRulesSetting,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '下达规则设置',
                        needAuth: true,
                    },
                },
                {
                    path: 'release-rules-setting-add',
                    name: 'releaseRulesSettingAdd',
                    component: ReleaseRulesSettingAdd,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        needAuth: true,
                    },
                },
                {
                    path: 'release-rules-setting/:id',
                    name: 'releaseRulesSettingForm',
                    component: ReleaseRulesSettingAdd,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        needAuth: true,
                    },
                },

            ],
        },
        {
            path: 'pricetaxrat',
            component: PriceTaxrat,
            name: 'pricetaxrat',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                name: '定价和税率',
                needAuth: true,
            },
            children: [
                {
                    path: 'price',
                    component: PriceSettings,
                    name: 'price-setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '定价',
                        needAuth: true,
                        visible: (store) => {
                            const isOpenCostPriceMakeUp = !(store.getters.goodsConfig.chainReview.chainExternalFlag & CHAIN_NOT_SUPPORT_PRICE_MAKE_UP_MODE);
                            return store.getters.isChainAdmin || (store.getters.isSingleStore && isOpenCostPriceMakeUp && store.state.viewDistribute.viewDistributeConfig.Inventory.isSupportCostPriceMakeUp);
                        },
                    },
                },
                {
                    path: 'taxrat',
                    component: TaxratSettings,
                    name: 'taxrat-setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAdviceAndFee,
                        name: '税率',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'sub-clinic-price-setting',
            component: subClinicPriceSettings,
            name: 'sub-clinic-price-setting',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                name: '定价设置',
                needAuth: true,
            },
            children: [
                {
                    path: 'sub-price',
                    component: subClinicPriceSetting,
                    name: 'sub-price-setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInventory,
                        name: '定价',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'patient-tag-setting',
            component: PatientTagSetIndex,
            name: 'patient-tag-setting',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingOperate,
                name: '患者设置',
                needAuth: true,
            },
            redirect: {
                name: 'tag-setting',
            },
            children: [
                {
                    path: 'tag-setting',
                    name: 'tag-setting',
                    component: PatientTagSet,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOperate,
                        name: '患者标签',
                        needAuth: true,
                    },
                },
            ],
        },

        /**
         * 住院打印
         */
        {
            path: 'hospital-print',
            component: HospitalPrintConfig,
            name: 'hospital-print',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                name: '住院打印设置',
                needAuth: true,
                scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
            },
            redirect: {
                name: 'hospital-medical-record',
            },
            children: [
                {
                    path: 'hospital-medical-record',
                    name: 'hospital-medical-record',
                    component: HospitalMedicalDocuments,
                    redirect: {
                        name: 'doctor-medical-prescription',
                    },
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '病例文书',
                        needAuth: true,
                    },
                    children: [
                        {
                            path: 'doctor-medical-prescription',
                            name: 'doctor-medical-prescription',
                            component: DoctorMedicalPrescription,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '病例文书-医嘱单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'doctor-nurse-prescription',
                            name: 'doctor-nurse-prescription',
                            component: DoctorNursePrescription,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '病例文书-医嘱执行单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'nurse-patient-dispensing',
                            name: 'nurse-patient-dispensing',
                            component: NursePatientDispensing,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '病例文书-领药退药单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'hospital-prescription',
                            name: 'hospital-prescription',
                            component: HospitalPrescription,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '病例文书-处方',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'hospital-infusion-record',
                            name: 'hospital-infusion-record',
                            component: HospitalInfusionRecord,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '病例文书-输液记录单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'hospital-hospitalization-certificate',
                            name: 'hospital-hospitalization-certificate',
                            component: HospitalizationCertificate,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '病例文书-住院证',
                                needAuth: true,
                            },
                        },
                    ],
                },
                {
                    path: 'fee-bills',
                    name: 'fee-bills',
                    component: HospitalFeeBills,
                    redirect: {
                        name: 'charge-list',
                    },
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '收费票据',
                        needAuth: true,
                    },
                    children: [
                        {
                            path: 'hospital-cashier',
                            name: 'hospital-cashier',
                            component: HospitalCashier,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '收费票据-收费小票',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'charge-list',
                            name: 'charge-list',
                            component: ChargeList,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '收费票据-医疗清单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'deposit-receipt',
                            name: 'deposit-receipt',
                            component: DepositReceipt,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '收费票据-押金收据',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-bill',
                            name: 'medical-bill',
                            component: HospitalMedicalBill,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '收费票据-住院收费票据',
                                needAuth: true,
                            },
                        },
                    ],
                },
                {
                    path: 'tags',
                    name: 'tags',
                    component: HospitalTags,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '标签',
                        needAuth: true,
                    },
                    children: [
                        {
                            path: 'hospital-medicine-tag',
                            name: 'hospital-medicine-tag',
                            component: HospitalMedicineTag,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '收费票据-押金收据',
                                needAuth: true,
                            },
                        },
                    ],
                },
            ],
        },

        /**
         * 门诊打印
         */
        {
            path: 'print',
            component: PrintConfig,
            name: 'print',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                name: '打印设置',
                needAuth: true,
                scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
            },
            redirect: {
                name: 'printMedicalDocuments',
            },
            children: [
                {
                    path: 'medicaldocuments',
                    name: 'printMedicalDocuments',
                    component: PrintMedicalDocuments,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '文书',
                    },
                    redirect: {
                        name: 'printMedicalDocumentsMedical',
                    },
                    children: [
                        {
                            path: 'medical',
                            name: 'printMedicalDocumentsMedical',
                            component: PrintConfigMedical,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '文书-病历',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'prescription',
                            name: 'printMedicalDocumentsPrescription',
                            component: PrintConfigPrescription,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '文书-处方',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'glassesprescription',
                            name: 'printMedicalDocumentsGlassesPrescription',
                            component: PrintConfigGlassesPresciption,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '文书-配镜处方',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'examination',
                            name: 'printMedicalDocumentsExamination',
                            component: PrintConfigExamination,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '文书-检查检验单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'infusion',
                            name: 'printMedicalDocumentsInfusion',
                            component: PrintConfigInfusion,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '文书-输液注射单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'treatment',
                            name: 'printMedicalDocumentsTreatment',
                            component: PrintConfigTreatment,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '文书-治疗理疗单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-technology-illnesscert',
                            name: 'medical-technology-illnesscert',
                            component: PrintConfigIllnessCert,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '报告-病情证明书',
                                needAuth: true,
                            },
                        },
                    ],
                },
                {
                    path: 'tickets',
                    name: 'printTickets',
                    component: PrintTickets,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '票据',
                        needAuth: true,
                    },
                    redirect: {
                        name: 'printTicketsRegistration',
                    },
                    children: [
                        {
                            path: 'registration',
                            name: 'printTicketsRegistration',
                            component: PrintConfigRegistration,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '票据-挂号单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'cashier',
                            name: 'printTicketsCashier',
                            component: PrintConfigCashier,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '票据-收费单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'dispensing',
                            name: 'printTicketsDispensing',
                            component: PrintConfigDispensing,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '票据-发药单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'bills',
                            name: 'printConfigMedicalBills',
                            component: PrintConfigMedicalBill,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '票据-医疗收费票据',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'receipt',
                            name: 'printConfigReceipt',
                            component: PrintConfigReceipt,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '收据',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'feelist',
                            name: 'printMedicalBillsFeeList',
                            component: PrintConfigMedicalFeeList,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '票据-医疗收费清单',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'statement',
                            name: 'printMedicalStatement',
                            component: PrintMedicalStatement,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '票据-医保结算单',
                                needAuth: true,
                            },
                        },
                    ],
                },
                {
                    path: 'reports',
                    name: 'printReports',
                    component: PrintReports,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '报告',
                        needAuth: true,
                    },
                    redirect: {
                        name: 'printMedicalDocumentsInspection',
                    },
                    children: [
                        {
                            path: 'inspection',
                            name: 'printMedicalDocumentsInspection',
                            component: PrintConfigInspection,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '报告-检验报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'eye-inspect',
                            name: '@printMedicalDocumentsEyeInspect',
                            component: () => import('@/views/settings/print-config/medical-documents/eye-inspect-report/index.vue'),
                            meta: {
                                moduleId: '7',
                                name: '报告-眼科检查报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'cdus',
                            name: 'printMedicalDocumentsCDUS',
                            component: PrintConfigCDUS,
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-彩超报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'ct',
                            name: 'printMedicalDocumentsCT',
                            component: PrintConfigCT,
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-CT报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'dr',
                            name: 'printMedicalDocumentsDR',
                            component: PrintConfigDR,
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-DR报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'mg',
                            name: 'printMedicalDocumentsMG',
                            component: () => import('@/views/settings/print-config/medical-documents/mg/index.vue'),
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-MG报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'mr',
                            name: 'printMedicalDocumentsMR',
                            component: PrintConfigMR,
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-MR报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'mr',
                            name: 'printMedicalDocumentsMR',
                            component: PrintConfigMR,
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-MR报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'illnesscert',
                            name: 'printMedicalDocumentsIllnessCert',
                            component: PrintConfigIllnessCert,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '报告-病情证明书',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'gastroscope',
                            name: '@printMedicalDocumentsGastroscope',
                            component: () => import('@/views/settings/print-config/medical-documents/gastroscope/index.vue'),
                            meta: {
                                moduleId: '7',
                                name: '报告-内窥镜报告',
                                needAuth: true,
                            },
                        },
                    ],
                },
                {
                    path: 'tags',
                    name: 'printTags',
                    component: PrintTags,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '标签',
                        needAuth: true,
                    },
                    redirect: {
                        name: 'printConfigPatientTag',
                    },
                    children: [
                        {
                            path: 'medicine',
                            name: 'printConfigMedicineTag',
                            component: PrintConfigMedicineTag,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '标签-用药标签',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'patient',
                            name: 'printConfigPatientTag',
                            component: PrintConfigPatientTag,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '标签-患者标签',
                                needAuth: true,
                            },
                        },
                    ],
                },
            ],
        },

        /**
         * 医技打印
         */
        {
            path: 'medical-technology-print',
            component: () => import('@/views-hospital/settings-common/frames/medical-technology-print-config/medical-technology-print-config.vue'),
            name: 'medical-technology-print',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                name: '医技打印设置',
                needAuth: true,
                scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
            },
            redirect: {
                name: 'medical-technology-medical-document',
            },
            children: [
                {
                    path: 'medical-technology-medical-document',
                    name: 'medical-technology-medical-document',
                    component: () => import('@/views-hospital/settings-common/frames/medical-technology-print-config/medical-documents/medical-documents.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '文书',
                    },
                    redirect: {
                        name: 'medical-technology-medical-document-examination',
                    },
                    children: [
                        {
                            path: 'medical-technology-medical-document-examination',
                            name: 'medical-technology-medical-document-examination',
                            component: PrintConfigExamination,
                            props: { from: 2 },
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '文书-检查检验单',
                                needAuth: true,
                            },
                        },
                    ],
                },
                {
                    path: 'medical-technology-reports',
                    name: 'medical-technology-reports',
                    component: PrintReports,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '报告',
                        needAuth: true,
                    },
                    redirect: {
                        name: 'medical-technology-inspection',
                    },
                    children: [
                        {
                            path: 'medical-technology-inspection',
                            name: 'medical-technology-inspection',
                            component: PrintConfigInspection,
                            props: { from: 2 },
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '报告-检验报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-technology-eye-inspect',
                            name: 'medical-technology-eye-inspect',
                            component: () => import('@/views/settings/print-config/medical-documents/eye-inspect-report/index.vue'),
                            props: { from: 2 },
                            meta: {
                                moduleId: '7',
                                name: '报告-眼科检查报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-technology-cdus',
                            name: 'medical-technology-cdus',
                            component: PrintConfigCDUS,
                            props: { from: 2 },
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-彩超报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-technology-ct',
                            name: 'medical-technology-ct',
                            component: PrintConfigCT,
                            props: { from: 2 },
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-CT报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-technology-dr',
                            name: 'medical-technology-dr',
                            component: PrintConfigDR,
                            props: { from: 2 },
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-DR报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-technology-mr',
                            name: 'medical-technology-mr',
                            component: PrintConfigMR,
                            props: { from: 2 },
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-MR报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-technology-mg',
                            name: 'medical-technology-mg',
                            component: () => import('@/views/settings/print-config/medical-documents/mg/index.vue'),
                            props: { from: 2 },
                            meta: {
                                moduleId: MODULE_ID_MAP.setting,
                                name: '报告-MG报告',
                                needAuth: true,
                            },
                        },
                        {
                            path: 'medical-technology-gastroscope',
                            name: 'medical-technology-gastroscope',
                            component: () => import('@/views/settings/print-config/medical-documents/gastroscope/index.vue'),
                            props: { from: 2 },
                            meta: {
                                moduleId: '7',
                                name: '报告-内窥镜报告',
                                needAuth: true,
                            },
                        },
                    ],
                },
            ],
        },

        {
            path: 'field-layout',
            component: () => import('views/settings/field-layout/index.vue'),
            name: 'FieldLayout',
            meta: {
                moduleId: MODULE_ID_MAP.settingSub.fieldLayoutSetting,
                name: '字段设置',
                needAuth: true,
            },
            redirect: {
                name: 'FieldLayoutMedicalRecord',
            },
            children: [
                {
                    path: 'mr',
                    name: 'FieldLayoutMedicalRecord',
                    component: () => import('views/settings/field-layout/medical-record/index.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.settingSub.fieldLayoutSetting,
                        name: '门诊病历',
                        needAuth: true,
                        scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
                    },
                },
                {
                    path: 'field-layout-patient-create',
                    name: 'FieldLayoutPatientCreate',
                    component: () => import('views/settings/field-layout/patient-create/index.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.settingSub.fieldLayoutSetting,
                        name: '患者建档',
                        needAuth: true,
                    },
                },
                {
                    path: 'field-layout-registration',
                    name: 'FieldLayoutRegistration',
                    component: () => import('views/settings/field-layout/registration/index.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.settingSub.fieldLayoutSetting,
                        name: '预约挂号',
                        needAuth: true,
                        scope: RouterScope.CHAIN_SUB | RouterScope.SINGLE_STORE,
                    },
                },
                {
                    path: 'weapp-patient-card',
                    name: 'FieldLayoutWeappPatientCard',
                    component: () => import('views/settings/field-layout/weapp-patient-card/index.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.settingSub.fieldLayoutSetting,
                        name: '微诊所就诊卡',
                        needAuth: true,
                        scope: RouterScope.CHAIN_ADMIN | RouterScope.SINGLE_STORE,
                    },
                },
            ],
        },

        /**
         * 库存单据
         */
        {
            path: 'inventory-print',
            name: 'inventory-print',
            component: PrintInventoryConfig,
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                name: '打印设置',
                needAuth: true,
            },
            redirect: {
                name: 'inventory-set',
            },
            children: [
                {
                    path: 'inventory-set',
                    name: 'inventory-set',
                    component: InventorySet,
                    redirect: {
                        name: 'print-goods-in',
                    },
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '库存入库',
                        needAuth: true,
                    },
                    children: [
                        {
                            path: 'print-goods-in',
                            name: 'print-goods-in',
                            component: GoodsIn,
                            meta: {
                                moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                                name: '库存入库单',
                                needAuth: true,
                            },
                        },
                    ],
                },
                {
                    path: 'price-tag',
                    name: 'priceTag',
                    component: () => import('src/views/settings/print-config/price-tag/index.vue'),
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingPrint,
                        name: '价签',
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'chargeset',
            component: ChargeSet,
            hidden: true,
            name: 'chargeset',
            redirect: {
                name: '@chargeset/basic',
            },
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingCashier,
                name: '收费设置',
                needAuth: true,
            },
            children: [
                {
                    path: 'basic',
                    name: '@chargeset/basic',
                    component: ChargeSetBasic,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingCashier,
                    },
                },
                {
                    path: 'wechat',
                    name: '@chargeset/wechat',
                    component: WeChatPay,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingCashier,
                    },
                },
                {
                    path: 'abc',
                    name: '@chargeset/abc',
                    component: aggregatePaymentContent,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingCashier,
                    },
                },
                {
                    path: 'invoice',
                    name: '@chargeset/invoice',
                    component: ChargeSeInvoice,
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingCashier,
                    },
                },
            ],
        },
        {
            path: 'userlog',
            component: UserLog,
            name: 'userLog',
            meta: {
                moduleId: MODULE_ID_MAP.setting,
                name: '用户设置',
                needAuth: true,
            },
        },

        {
            path: '/wechatpay',
            component: WeChatPay,
            name: 'wechatpay',
            meta: {
                moduleId: MODULE_ID_MAP.setting,
                needAuth: true,
            },
        },

        {
            path: 'datapermission',
            component: DataPermission,
            name: 'datapermission',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                name: '数据权限',
                needAuth: true,
            },
            children: [
                {
                    path: 'datapermissionsettings',
                    component: DataPermissionSettings,
                    name: 'datapermissionsettings',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingConfig,
                        name: '数据权限',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'family-doctor-settings',
            component: FamilyDoctorSettings,
            name: 'family-doctor-settings',
            meta: {
                // TODO:等分配
                name: '家庭医生',
                moduleId: MODULE_ID_MAP.settingSub.familyDoctor,
                needAuth: true,
            },
            children: [
                {
                    path: 'family-doctor',
                    component: FamilyDoctor,
                    name: 'family-doctor',
                    meta: {
                        moduleId: MODULE_ID_MAP.settingSub.familyDoctor,
                        name: '家庭医生',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'airpharmacy',
            component: AirPharmacy,
            name: 'airpharmacy',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingAirPharmacy,
                name: '空中药房',
                needAuth: true,
            },
            children: [
                {
                    path: 'airpharmacysettings',
                    component: AirPharmacySettings,
                    name: 'airpharmacysettings',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAirPharmacy,
                        name: '空中药房配置',
                        needAuth: true,
                    },
                },
                {
                    path: 'airpharmacyservice',
                    component: AirPharmacyService,
                    name: 'airpharmacyservice',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAirPharmacy,
                        name: '服务药房&药价配置',
                        needAuth: true,
                    },
                },
                {
                    path: 'airpharmacyintroduce',
                    component: AirPharmacyIntroduce,
                    name: 'airpharmacyintroduce',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingAirPharmacy,
                        name: '服务介绍',
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'child-health',
            component: ChildHealth,
            name: 'child-health-settings',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                name: '儿保系统',
                needAuth: true,
            },
            redirect: {
                name: 'child-health-switch',
            },
            children: [
                {
                    path: 'switch',
                    component: ChildHealthSwitch,
                    name: 'child-health-switch',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        needAuth: true,
                    },
                },
                {
                    path: 'introduce',
                    component: Introduce,
                    name: 'child-health-introduce',
                    props: {
                        accessKey: AbcAccess.accessMap.CHILD_HEALTH,
                        isAlwaysDisplay: true,
                        hideTitle: true,
                    },
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'chronic-care',
            component: ChronicCare,
            name: '@settings/chronic-care',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                name: '慢病管理',
                needAuth: true,
            },
        },

        {
            path: 'chronic-care/:id',
            component: ChronicCareDetail,
            name: '@settings/chronic-care-detail',
            props: true,
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingOutpatient,
                name: '慢病管理详情',
                needAuth: true,
            },
        },

        {
            path: 'operation-log',
            component: OperationLogSettings,
            name: 'OperationLogSettings',
            meta: {
                moduleId: '51720',
                name: '操作日志',
                needAuth: true,
            },
            children: [
                {
                    path: 'operation-log-detail',
                    component: OperationLogDetail,
                    name: 'OperationLogDetail',
                    meta: {
                        moduleId: '51720',
                        name: '操作日志',
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'product-center',
            component: ProductCenter,
            name: 'product-center',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingProduct,
            },
            children: [
                {
                    path: 'product-center-settings',
                    component: ProductCenterSettings,
                    name: 'product-center-settings',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingProduct,
                        name: '产品中心',
                        needAuth: true,
                        visibleOnExpired: true,
                    },
                },
            ],
        },
        {
            path: 'product-center-renewal',
            component: ProductCenterOrder,
            name: 'product-center-renewal',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingProduct,
                visibleOnExpired: true,
            },
        },
        {
            path: 'product-center-upgrade',
            component: ProductCenterOrder,
            name: 'product-center-upgrade',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingProduct,
                visibleOnExpired: true,
            },
        },
        {
            path: 'product-center-invoice',
            component: ProductCenterInvoice,
            name: 'product-center-invoice',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingProduct,
                visibleOnExpired: true,
            },
        },
        {
            path: 'product-center-invoice-detail',
            component: ProductCenterInvoiceDetail,
            name: 'product-center-invoice-detail',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingProduct,
                visibleOnExpired: true,
            },
        },
        {
            path: 'aggregate-payment',
            component: aggregatePayment,
            name: 'aggregate-payment',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingCashier,
            },
            children: [
                {
                    path: 'aggregate-payment-content',
                    component: aggregatePaymentContent,
                    name: 'aggregate-payment-content',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingCashier,
                        name: '聚合支付',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'lis-system',
            component: lisSystem,
            name: 'lis-system',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingInspect,
            },
            redirect: {
                name: 'lis-system',
            },
            children: [
                {
                    path: 'lis-system',
                    component: lisSystemContent,
                    name: 'lis-system',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingInspect,
                        name: 'LIS 系统',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'scrm-index',
            component: ScrmIndex,
            name: 'scrm-index',
            meta: {
                needAuth: true,
                moduleId: MODULE_ID_MAP.hospitalSettingOperate,
            },
            redirect: {
                name: 'scrm-desc',
            },
            children: [
                {
                    path: 'scrm-desc',
                    component: ScrmDesc,
                    name: 'scrm-desc',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingOperate,
                        name: '企微管家',
                        needAuth: true,
                    },
                },
            ],
        },

        {
            path: 'consultation',
            component: ConsultationIndex,
            name: '@consultation',
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingLive,
                needAuth: true,
            },
            children: [
                {
                    path: 'setting',
                    component: ConsultationSetting,
                    name: '@consultation-setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingLive,
                        name: '会诊设置',
                        needAuth: true,
                    },
                },
            ],
        },
        {
            path: 'regulatory',
            component: RegulatoryIndex,
            name: 'regulatory',
            redirect: {
                name: '@regulatory/report',
            },
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingRegular,
                needAuth: true,
            },
            children: [
                {
                    path: 'report',
                    component: RegulatoryReport,
                    name: '@regulatory/report',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingRegular,
                        needAuth: true,
                        label: '门诊业务',
                    },
                },
                {
                    path: 'in-hospital-business',
                    component: InHospitalBusiness,
                    name: '@regulatory/in-hospital-business',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingRegular,
                        needAuth: true,
                        name: '住院业务',
                    },
                },
                {
                    path: 'setting',
                    component: RegulatorySetting,
                    name: '@regulatory/setting',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingRegular,
                        needAuth: true,
                        name: '监管设置',
                    },
                },
            ],
        },
        {
            path: 'antimicrobial-management-hospital',
            component: AntimicrobialManagementHospital,
            name: 'antimicrobial-management-hospital',
            redirect: {
                name: 'antimicrobial-management',
            },
            meta: {
                moduleId: MODULE_ID_MAP.hospitalSettingRegular,
                needAuth: true,
            },
            children: [
                {
                    path: 'antimicrobial-management',
                    component: AntimicrobialManagement,
                    name: 'antimicrobial-management',
                    meta: {
                        moduleId: MODULE_ID_MAP.hospitalSettingRegular,
                        needAuth: true,
                        label: '抗菌用药管理',
                    },
                },
            ],
        },
    ],
};
