@import "./theme.scss";

@keyframes highlight {
    0% { background: $S2; }
    20% { background: #fffab9; }
    80% { background: #fffab9; }
    100% { background: $S2; }
}

.highlight {
    animation: highlight 1.5s ease;
}

@keyframes left-right-dou {
    0% { transform: translateX(0); }
    25% { transform: translateX(-4px); }
    50% { transform: translateX(0); }
    75% { transform: translateX(4px); }
    100% { transform: translateX(0); }
}

@keyframes loading-rotate {
    100% {
        transform: rotate(360deg);
    }
}
