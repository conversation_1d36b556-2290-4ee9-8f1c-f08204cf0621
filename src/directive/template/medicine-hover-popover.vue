<template>
    <div
        id="medicine-hover-popover"
        v-abc-loading="loading"
        @mouseenter="isInContent = true"
        @mouseleave="handleLeave"
    >
        <div class="medicine-info-wrapper">
            <div class="first-row">
                <abc-flex class="cadn" align="flex-start">
                    <div style="flex: 1;">
                        {{ medicineName }}
                    </div>
                </abc-flex>
                <abc-text v-if="config.showPharmacyName && renderGoods.pharmacyName" style="margin-right: 16px;">
                    {{ renderGoods.pharmacyName }}
                </abc-text>
                <div :class="{ 'no-data': stockStr(renderGoods) === '无库存' }" style="position: relative; margin-right: 16px;">
                    <span
                        :style="{
                            color: stockLoading ? 'transparent' : 'inherit',
                        }"
                    >{{ stockStr(renderGoods) }}</span>
                    <abc-loading-spinner
                        v-if="stockLoading"
                        small
                        gray
                        style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);"
                    ></abc-loading-spinner>
                </div>
                <div v-if="config.showPrice" class="price" :class="{ 'no-data': unitPriceStr(renderGoods) === '无价格' }">
                    <span style="margin-right: 2px; font-size: 12px;">{{ i18n.t('currencySymbol') }}</span>{{ unitPriceStr(renderGoods) }}
                </div>
            </div>

            <abc-flex class="second-row" :gap="20">
                <abc-text :theme="getSpec(renderGoods) ? '' : 'gray'">
                    {{ getSpec(renderGoods) || '规格' }}
                </abc-text>

                <abc-text :theme="noManufacturer ? 'gray' : ''">
                    {{ manufacturer }}
                </abc-text>

                <abc-text :theme="get2levelType(renderGoods) === '分类' ? 'gray' : ''">
                    {{ get2levelType(renderGoods) }}
                </abc-text>

                <abc-text v-if="isShowCostPrice" class="last-cost-price">
                    {{ lastPackageCostPrice(goods) }}
                </abc-text>
            </abc-flex>

            <abc-divider></abc-divider>

            <template v-if="showShebaoCode">
                <abc-flex justify="space-between" align="center">
                    <template v-if="renderGoods.shebaoNationalCode">
                        <abc-flex align="center" style="margin-right: 16px;">
                            医保码：<template v-if="renderGoods.medicalFeeGrade">
                                【{{ renderGoods.medicalFeeGrade | medicalFeeGrade2Str }}】
                            </template>
                            {{ renderGoods.shebaoNationalCode | formatShebaoCode }}
                            <abc-button
                                v-if="canUpdateSocialCode"
                                type="text"
                                size="small"
                                class="quick-btn"
                                style="margin-left: 8px;"
                                @click.stop.prevent="openSearchShebaoCodeDialog"
                            >
                                修改
                            </abc-button>
                        </abc-flex>
                    </template>
                    <abc-flex v-else align="center">
                        医保码：<abc-text theme="danger">
                            不刷医保/暂无编码
                        </abc-text>
                        <abc-button
                            v-if="canUpdateSocialCode"
                            type="text"
                            size="small"
                            class="quick-btn"
                            style="margin-left: 8px;"
                            @click.stop.prevent="openSearchShebaoCodeDialog"
                        >
                            去对码
                        </abc-button>
                    </abc-flex>
                </abc-flex>

                <abc-flex
                    v-if="shebaoInfo.socialName"
                    style="margin-top: 4px;"
                    justify="space-between"
                    align="center"
                >
                    <abc-text>对码药品：{{ shebaoInfo.socialName }} {{ shebaoGoodsSpec }}</abc-text>
                    <abc-text v-if="isEnableListingPrice">
                        挂网价：{{ displayListingPrice }}
                    </abc-text>
                </abc-flex>

                <abc-flex v-if="showShebaoPurchaseType" style="margin-top: 6px;">
                    集采分类：{{ renderGoods.shebao?.purchaseType ?? '-' }}
                </abc-flex>
                <abc-divider></abc-divider>
            </template>

            <div class="goods-extend-info">
                <abc-text v-if="showMedicineNmpn" theme="gray">
                    批准文号：{{ getMedicineNmpn || '-' }}
                </abc-text>
                <abc-text theme="gray">
                    商品编码：{{ renderGoods.shortId || '-' }}
                </abc-text>
                <abc-text theme="gray">
                    条码：{{ renderGoods.barCode || '-' }}
                </abc-text>
                <abc-text v-if="isPatentMedicine" theme="gray">
                    剂型：{{ renderGoods.dosageFormTypeName || '-' }}
                </abc-text>
                <abc-text theme="gray" style="grid-column: 1 / -1; word-break: break-all;">
                    备注：{{ renderGoods.remark || '-' }}
                </abc-text>
            </div>

            <biz-goods-info-tag-group
                :product-info="productInfo"
                :show-antibiotic="showAntibioticAndDangerIngredient"
                :show-danger-ingredient="showAntibioticAndDangerIngredient"
                :is-fold-tags="true"
                style="width: 100%; margin-top: 8px;"
            >
            </biz-goods-info-tag-group>
        </div>

        <template v-if="config.showF1 || config.showF2 || config.showF3">
            <div class="quick-btn-group">
                <abc-button
                    v-show="config.showF3"
                    type="text"
                    size="small"
                    class="quick-btn"
                    @click.stop.prevent="viewF3"
                >
                    编辑档案
                </abc-button>
                <abc-button
                    v-show="config.showF1"
                    type="text"
                    size="small"
                    class="quick-btn"
                    @click.stop.prevent="viewF1"
                >
                    说明书
                </abc-button>
                <abc-button
                    v-show="showF2"
                    type="text"
                    size="small"
                    class="quick-btn"
                    @click.stop.prevent="viewF2"
                >
                    调价
                </abc-button>
            </div>
        </template>
    </div>
</template>

<script>
    import { mapGetters } from 'vuex';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import {
        off, on,
    } from 'utils/dom';
    import {
        formatMoney, getSpec, goodsTypeName, isChineseMedicine,
    } from '@/filters';
    import GoodsAPI from 'api/goods/index.js';
    import { LoadingSpinner as AbcLoadingSpinner } from '@abc/ui-pc';

    import AbcAdjustPriceDialog from 'views/inventory/goods-in/components/adjust-price-dialog/index.js';
    import AbcGoodsDetailsDialog from 'views/inventory/goods/goods-details/index.js';
    import BizGoodsInfoTagGroup from '@/components-composite/biz-goods-info-tag-group/index.js';
    import {
        isNotNull, isNull, paddingMoney,
    } from '@/utils';
    import { PriceType } from 'views/common/inventory/constants';
    import { calcPriceRangeByMakeupPercent } from 'views/inventory/goods/archives/utils';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import i18n from '@/i18n';
    import InventoryService from '@/service/inventory';
    import { DISABLED } from 'views/inventory/components/social-code-autocomplete/constant';

    export default {
        components: {
            BizGoodsInfoTagGroup,
            AbcLoadingSpinner,
        },
        props: {
            goods: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            productInfo: Object,
            destroyPopper: {
                type: Function,
            },
            // 数据来源类型，local-本地传入数据，remote-需要远程获取
            dataSourceType: {
                type: String,
                default: 'local',
            },
        },
        data() {
            return {
                i18n,
                isInContent: false,
                loading: false,
                stockLoading: false,
                lastGoods: {
                    ...this.productInfo,
                },
            };
        },
        computed: {
            ...mapGetters([
                'isEnableListingPrice',
                'chainBasic',
            ]),
            canUpdateSocialCode() {
                const {
                    canUpdateSocialCode,
                } = this.config;
                return canUpdateSocialCode;
            },
            isSupportShebaoListingPrice() {
                return this.isEnableListingPrice;
            },
            isSupportCenterCode() {
                return !!this.chainBasic.shebao?.isSupportCenterCode;
            },
            shebaoInfo() {
                return this.renderGoods.shebao || {};
            },
            displayListingPrice() {
                const {
                    listingPrice,
                } = this.shebaoInfo;
                const {
                    listingPrice: listingPriceItem,
                    packageUnit,
                } = this.renderGoods || {};
                const finalListingPrice = listingPrice || listingPriceItem;
                if (isNull(finalListingPrice)) return '-';
                return `${i18n.t('currencySymbol')}${finalListingPrice}/${packageUnit || ''}`;
            },

            shebaoGoodsSpec() {
                const {
                    shebaoPackageUnit,
                    shebaoPieceNum,
                    shebaoPieceUnit,
                } = this.shebaoInfo;
                if (!shebaoPieceUnit || !shebaoPackageUnit) return '';
                return `${shebaoPieceNum + shebaoPieceUnit}/${shebaoPackageUnit}`;
            },
            // 页面渲染的信息
            renderGoods() {
                return this.dataSourceType === 'local' ? this.productInfo : this.lastGoods;
            },
            medicineName() {
                let name = '';
                if (this.renderGoods) {
                    name = this.renderGoods.medicineCadn || this.renderGoods.name;
                }
                name = name || this.goods.cadn || this.goods.name;
                return name || '';
            },
            /**
             * 是否展示 抗菌等级 & 精麻毒 标签
             * @return {boolean}
             */
            showAntibioticAndDangerIngredient() {
                return this.productInfo.type === GoodsTypeEnum.MEDICINE &&
                    [GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine, GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM].includes(this.productInfo.subType) &&
                    (isNotNull(this.productInfo.antibiotic) || !!this.productInfo.dangerIngredient);
            },
            showShebaoCode() {
                const {
                    type, subType,
                } = this.renderGoods;
                if (
                    (type === GoodsTypeEnum.MATERIAL && subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].LogisticalMaterials) || // 后期材料
                    (type === GoodsTypeEnum.MATERIAL && subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].FixedAssets) // 固定资产

                ) {
                    return false;
                }
                return true;
                // return this.config?.showShebaoCode;
            },
            showMedicineNmpn() {
                const {
                    type, subType,
                } = this.renderGoods;
                if (
                    (type === GoodsTypeEnum.MEDICINE && subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine) || // 西药
                    (type === GoodsTypeEnum.MEDICINE && subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM) || // 中成药
                    (type === GoodsTypeEnum.MATERIAL && subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials) || // 医疗器械
                    (type === GoodsTypeEnum.GOODS && subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].ManufacturedGoods) || // 自制成品
                    (type === GoodsTypeEnum.GOODS && subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].HealthCareMedicine) || // 保健药品
                    (type === GoodsTypeEnum.GOODS && subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].HealthFoods) || // 保健食品
                    (type === GoodsTypeEnum.GOODS && subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].Other) // 其他商品
                ) {
                    // 只有上面类型的药品才显示准字
                    return true;
                    // 医疗器械取注册证号

                }
                return false;
            },
            getMedicineNmpn() {
                const {
                    type, subType, certificateNo, medicineNmpn,
                } = this.renderGoods;
                if ((type === GoodsTypeEnum.MATERIAL && subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials)) {
                    return medicineNmpn || certificateNo || '-';
                }
                return medicineNmpn || '-';
            },
            noManufacturer() {
                return this.renderGoods && !this.renderGoods.manufacturer;
            },
            manufacturer() {
                if (!this.renderGoods) return '';
                const {
                    manufacturer,
                    type,
                    subType,
                } = this.renderGoods || {};
                if (manufacturer) {
                    return manufacturer;
                }
                return type === 1 && subType === 2 ? '产地厂商' : '生产厂商';

            },
            // 是否显示调价按钮
            showF2() {
                const {
                    type, subType, isSell, v2DisableStatus, disable,
                } = this.productInfo;
                const {
                    showSubSetPrice, modulePermission, isSingleStore,
                } = this.$store.getters;
                if (
                    (type === GoodsTypeEnum.MEDICINE && subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine) || // 西药
                    (type === GoodsTypeEnum.MEDICINE && subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].ChineseMedicine) || // 中药
                    (type === GoodsTypeEnum.MEDICINE && subType === GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM) || // 中成药
                    (type === GoodsTypeEnum.MATERIAL && subType === GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials) || // 医疗器械
                    (type === GoodsTypeEnum.GOODS && subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].ManufacturedGoods) || // 自制成品
                    (type === GoodsTypeEnum.GOODS && subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].HealthCareMedicine) || // 保健药品
                    (type === GoodsTypeEnum.GOODS && subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].HealthFoods) || // 保健食品
                    (type === GoodsTypeEnum.GOODS && subType === GoodsSubTypeEnum[GoodsTypeEnum.GOODS].Other) // 其他商品
                ) {
                    // 只有上面类型的药品才显示
                    const disabled = isSell && !(disable === 1 || v2DisableStatus > 0);// 药品允许销售
                    const hasAuth = (showSubSetPrice || isSingleStore) && modulePermission?.hasGoodsModule; // 有自主定价权并且有模块权限
                    return this.config.showF2 && disabled && hasAuth;
                }
                return false;
            },
            /**
             * @desc 是否显示进价，兼容lastPackageCostPrice出现在goods不同结构中
             * <AUTHOR>
             * @date 2022/10/27 11:43:11
             */
            isShowCostPrice() {
                const { showCostPrice } = this.config;
                const {
                    lastPackageCostPrice, productInfo,
                } = this.goods;
                const { lastPackageCostPrice: innerLastPackageCostPrice } = productInfo || {};
                return showCostPrice && (lastPackageCostPrice || innerLastPackageCostPrice);
            },
            // 是中西成药
            isPatentMedicine() {
                const {
                    type, subType,
                } = this.renderGoods;
                return type === GoodsTypeEnum.MEDICINE && (
                    GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine === subType ||
                    GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM === subType
                );
            },
            // 展示集采分类
            showShebaoPurchaseType() {
                const isChineseGoods = isChineseMedicine(this.renderGoods);

                return (this.isPatentMedicine || isChineseGoods) && this.$abcSocialSecurity.config.isLiaoningShenyang;
            },
        },
        created() {
            if (this.dataSourceType === 'remote') {
                this.fetchLastGoods();
            }
            if (this.productInfo) {
                this.$store?.commit('SET_F1_MEDICINE_INFO', {
                    medicineCadn: this.productInfo.medicineCadn,
                    manufacturer: this.productInfo.manufacturer,
                    componentContentNum: this.productInfo.componentContentNum,
                    componentContentUnit: this.productInfo.componentContentUnit,
                    medicineDosageNum: this.productInfo.medicineDosageNum,
                    medicineDosageUnit: this.productInfo.medicineDosageUnit,
                    pieceNum: this.productInfo.pieceNum,
                    pieceUnit: this.productInfo.pieceUnit,
                    type: this.productInfo.type,
                    subType: this.productInfo.subType,
                    typeId: this.productInfo.typeId,
                    shebaoNationalCode: this.productInfo.shebaoNationalCode,
                    displaySpec: this.productInfo.displaySpec,
                    medicineNmpn: this.productInfo.medicineNmpn,
                });
            }
        },

        mounted() {
            if (this.config.showF2 || this.config.showF3) {
                on(document, 'keydown', this.toggleF2);
            }
        },

        beforeDestroy() {
            if (this.config.showF2 || this.config.showF3) {
                off(document, 'keydown', this.toggleF2);
            }
        },

        methods: {
            openSearchShebaoCodeDialog() {
                const instance = new InventoryService();
                const {
                    nationalCode: shebaoCode,
                    nationalCodeId: shebaoCodeId,
                } = this.productInfo.shebao || {};
                instance.openSearchShebaoCodeDialog(this, {
                    productInfo: this.productInfo,
                    currentSocialInfo: {
                        shebaoCode,
                        shebaoCodeId,
                    },
                    showSearch: true,
                    sceneType: 'pharmacy',
                    isSupportShebaoListingPrice: this.isSupportShebaoListingPrice,
                    isSupportCenterCode: this.isSupportCenterCode,
                    selectSocialInfo: this.selectSocialInfo,
                    notAllowSocialPay: this.notAllowSocialPay,
                    manualShebaoCode: this.manualShebaoCode,
                });
            },

            async selectSocialInfo(obj) {
                if (!this.productInfo.id) return;
                console.log(obj);
                const postData = {
                    shebao: {
                        nationalCode: obj.shebaoCode,
                        nationalCodeId: obj.shebaoCodeId,
                        matchMode: 1,
                        medicalFeeGrade: obj.medicalFeeGrade,
                        listingPrice: obj.listingPrice,
                        standardCode: obj.standardCode,
                        priceLimit: obj.priceLimit,
                    },
                };
                try {
                    await GoodsAPI.updateGoodsShebaoInfo(this.productInfo.id, postData);
                    this.$Toast({
                        message: '保存成功',
                        type: 'success',
                    });

                    this.productInfo.listingPrice = obj.listingPrice;
                    this.productInfo.shebaoNationalCode = obj.shebaoCode;
                    this.productInfo.medicalFeeGrade = obj.medicalFeeGrade;
                    Object.assign(this.productInfo.shebao, {
                        ...postData,
                        socialName: obj.name,
                        shebaoPackageUnit: obj.packageUnit,
                        shebaoPieceNum: obj.pieceNum,
                        shebaoPieceUnit: obj.pieceUnit,
                    });
                } catch (e) {
                    console.error(e);
                }
            },
            async notAllowSocialPay(obj) {
                console.log(obj);
                const postData = {
                    shebao: {
                        nationalCode: DISABLED,
                        nationalCodeId: '',
                        matchMode: null,
                        medicalFeeGrade: null,
                        listingPrice: null,
                        standardCode: '',
                        priceLimit: null,
                    },
                };
                await GoodsAPI.updateGoodsShebaoInfo(this.productInfo.id, postData);
                this.$Toast({
                    message: '保存成功',
                    type: 'success',
                });
                this.productInfo.shebaoNationalCode = '';
                this.productInfo.medicalFeeGrade = null;
                Object.assign(this.productInfo.shebao, postData.shebao);
            },
            async manualShebaoCode(nationalCode) {
                console.log(nationalCode);
                const postData = {
                    shebao: {
                        nationalCode,
                        nationalCodeId: '',
                        matchMode: 0,
                        medicalFeeGrade: null,
                        listingPrice: null,
                        standardCode: '',
                        priceLimit: null,
                    },
                };
                await GoodsAPI.updateGoodsShebaoInfo(this.productInfo.id, postData);
                this.$Toast({
                    message: '保存成功',
                    type: 'success',
                });
                this.productInfo.shebaoNationalCode = nationalCode;
                this.productInfo.medicalFeeGrade = null;
                Object.assign(this.productInfo.shebao, postData.shebao);
            },

            getSpec,
            changePrice(priceInfo) {
                // 更新到表格项中
                Object.assign(this.productInfo, priceInfo);
            },
            get2levelType(productInfo) {
                if (!productInfo?.type) return '分类';
                const name = goodsTypeName(productInfo) || '';
                const { transGoodsClassificationName } = getViewDistributeConfig();

                return (transGoodsClassificationName(name) + (productInfo.customTypeName ? `/${productInfo.customTypeName}` : '')) || '分类';
            },
            stockStr(productInfo) {
                // if (this.config && !this.config.showStock) return '';
                if (!productInfo || !productInfo.id) return '无库存';

                const stockPieceCount = productInfo.stockPieceCount || 0,
                      stockPackageCount = productInfo.stockPackageCount || 0,
                      pieceUnit = productInfo.pieceUnit || '',
                      packageUnit = productInfo.packageUnit || '';

                let str = '';

                if (stockPackageCount === 0 && stockPieceCount === 0) {
                    str += `${stockPackageCount}${packageUnit}`;
                } else {
                    if (stockPackageCount && packageUnit) {
                        str += `${stockPackageCount}${packageUnit}`;
                    }
                    if (stockPieceCount && pieceUnit) {
                        str += `${stockPieceCount}${pieceUnit}`;
                    }
                }

                if (!str) return '无库存';
                return `可售 ${str}`;
            },
            unitPriceStr(productInfo) {
                try {
                    if (this.config && !this.config.showPrice) return '';

                    if (!productInfo) return '无价格';

                    const {
                        priceMakeupPercent,
                        maxPackageCostPrice,
                        minPackageCostPrice,
                        maxPackagePrice,
                        minPackagePrice,
                        pieceNum,
                        packagePrice,
                        packageUnit,
                        piecePrice,
                        pieceUnit,
                        priceType,
                        id,
                    } = productInfo;
                    const isChineseGoods = isChineseMedicine(productInfo);

                    if (!id) return '无价格';

                    // 进价加成
                    if (priceType === PriceType.PKG_PRICE_MAKEUP) {
                        if (isNull(priceMakeupPercent)) return '无价格';

                        const priceRange = calcPriceRangeByMakeupPercent({
                            maxPackageCostPrice,
                            minPackageCostPrice,
                            maxPackagePrice,
                            minPackagePrice,
                            pieceNum,
                            priceMakeupPercent,
                            fractionDigits: isChineseGoods ? 4 : 2,
                        });

                        if (isChineseGoods) {
                            const {
                                maxPiecePrice,
                                minPiecePrice,
                            } = priceRange;

                            if (minPiecePrice === maxPiecePrice) return `${paddingMoney(maxPiecePrice)} / ${pieceUnit}`;

                            return `${paddingMoney(minPiecePrice)}~${paddingMoney(maxPiecePrice)} / ${pieceUnit}`;
                        }
                        const {
                            maxPackagePrice: _maxPackagePrice,
                            minPackagePrice: _minPackagePrice,
                        } = priceRange;

                        if (_minPackagePrice === _maxPackagePrice) return `${paddingMoney(_maxPackagePrice)} / ${packageUnit}`;

                        return `${paddingMoney(_minPackagePrice)}~${paddingMoney(_maxPackagePrice)} / ${packageUnit}`;
                    }

                    // 固定售价
                    if (isChineseGoods) {
                        return pieceUnit ? `${formatMoney(piecePrice, false) || 0} / ${pieceUnit}` : '无价格';
                    }
                    return packageUnit ? `${formatMoney(packagePrice) || 0} / ${packageUnit}` : '无价格';
                } catch (e) {
                    console.error(e);
                }
            },
            lastPackageCostPrice(item) {
                const { productInfo } = item;
                const {
                    lastPackageCostPrice,
                    packageUnit,
                    pieceUnit,
                } = productInfo || {};
                let str = '';
                if (lastPackageCostPrice) {
                    const price = formatMoney(lastPackageCostPrice, false);
                    str += `进价 ${price}/${packageUnit || pieceUnit || ''}`;
                }
                return str;
            },

            handleLeave() {
                if (this.config.trigger !== 'hover') return;
                this.isInContent = false;
                if (typeof this.destroyPopper === 'function') {
                    this.destroyPopper();
                }
                this.$destroy();
                $(this.$el).remove();
            },

            viewF1() {
                document.dispatchEvent(
                    new window.KeyboardEvent('keydown', { keyCode: 112 }),
                );
                this.handleLeave();
            },
            viewF3() {
                document.dispatchEvent(
                    new window.KeyboardEvent('keydown', { keyCode: 114 }),
                );
            },

            viewF2() {
                document.dispatchEvent(
                    new window.KeyboardEvent('keydown', { keyCode: 113 }),
                );
            },

            toggleF2(event) {
                const KEY_F2 = 113;
                const KEY_F3 = 114;
                if (event.keyCode === KEY_F2) {
                    event.cancelBubble = true;
                    event.returnValue = false;
                    if (event.preventDefault) event.preventDefault();
                    if (event.stopPropagation) event.stopPropagation();
                    this.openAdjustPriceDialog();
                    this.handleLeave();
                    return false;
                }
                if (event.keyCode === KEY_F3) {
                    new AbcGoodsDetailsDialog({
                        value: true,
                        goods: this.productInfo,
                        goodsId: this.productInfo?.id,
                        goodsType: this.productInfo?.type,
                        stockGoodsId: this.productInfo?.id,
                        subType: this.productInfo?.subType,
                        viewType: 1,
                        ...this.config.goodDetailsConfig,
                        needCloseDialog: true,
                        hiddenStop: true,
                        updateGoods: (type, keepOrder, showToast = true) => {
                            if (type === 1 || type === 2) {
                                if (showToast) {
                                    this.$Toast({
                                        message: type === 1 ? '保存成功' : '删除成功',
                                        type: 'success',
                                    });
                                }
                            }
                            this.config?.handleGoods();
                        },
                    }).generateDialogAsync({
                        parent: this,
                    });
                    this.handleLeave();
                    return false;
                }
            },
            openAdjustPriceDialog() {
                new AbcAdjustPriceDialog({
                    goodsData: this.productInfo,
                    changePrice: this.changePrice,
                }).generateDialogAsync({
                    parent: this,
                });
            },
            async fetchLastGoods() {
                try {
                    if (this.config?.onlyStock) {
                        this.stockLoading = true;
                    } else {
                        this.loading = true;
                    }
                    const goodsId = this.productInfo.id || this.productInfo.goodsId;
                    const {
                        pharmacyNo,
                        clinicId,
                        sceneType,
                        departmentId,
                    } = this.config || {};
                    const { data } = await GoodsAPI.fetchGoods(goodsId, {
                        withShebaoCode: 1,
                        forPurchase: 1,
                        withSupplier: 1,
                        pharmacyNo,
                        clinicId,
                        sceneType,
                        departmentId,
                    });
                    if (data) {
                        this.lastGoods = data;
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                    this.stockLoading = false;
                }

            },
        },
    };
</script>
