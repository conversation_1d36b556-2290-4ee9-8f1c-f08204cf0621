<template>
    <div id="medicine-hover-popover" @mouseenter="isInContent = true" @mouseleave="handleLeave">
        <div class="medicine-info-wrapper">
            <section>
                <div class="row">
                    <div class="goods-info" style=" display: flex; flex: 1; overflow: hidden;">
                        <span class="goods-name ellipsis">{{ goodsName }}</span>

                        <!--云检标签-->
                        <biz-exam-business-tag
                            is-cloud-tag
                            :cloud-supplier-flag="goods?.productInfo?.cloudSupplierFlag"
                            style="margin-left: 4px;"
                        ></biz-exam-business-tag>

                        <span v-if="goodsTypeStr" class="gray goods-type">
                            [{{ goodsTypeStr }}]
                        </span>

                        <span v-if="deviceNameList.length" class="gray goods-type">
                            <span v-for="(name,idx) in deviceNameList" :key="idx">
                                [{{ name }}]
                            </span>
                        </span>

                        <!--外包标签-->
                        <biz-exam-business-tag
                            is-out-sourcing-tag
                            :coop-flag="goods?.productInfo?.coopFlag"
                            :type="goods?.productInfo?.type"
                            :sub-type="goods?.productInfo?.subType"
                        ></biz-exam-business-tag>
                    </div>

                    <div v-if="config.showPrice" class="price right-content" :class="{ 'no-data': unitPriceStr(productInfo) === '无价格' }">
                        <span style="margin-right: 2px; font-size: 12px;">{{ i18n.t('currencySymbol') }}</span>{{ unitPriceStr(productInfo) }}
                    </div>
                </div>

                <div class="row">
                    <abc-flex :gap="6" align="center">
                        <div>
                            性别限制：{{ goodsGender || '-' }}
                        </div>
                        <abc-icon
                            v-if="config.showGenderLimited"
                            icon="n-alert-fill"
                            :color="$store.state.theme.style.R1"
                        ></abc-icon>
                    </abc-flex>
                </div>

                <div class="row">
                    <div v-if="needExecutive" class="need-executive">
                        执行划扣：{{ productInfo.needExecutive ? '需要' : '不需要' }}
                    </div>

                    <div v-if="config.showShebaoCode && productInfo.shebaoNationalCode" class="right-content gray">
                        <span class="label">医保码：{{ productInfo.shebaoNationalCode | formatShebaoCode }}</span>
                        <span v-if="medicalFeeGrade2Str">[{{ medicalFeeGrade2Str }}]</span>
                    </div>
                </div>

                <div class="row">
                    <abc-flex>
                        <div style="white-space: nowrap;">
                            检验要求：
                        </div>
                        <div>{{ productInfo.bizExtensions?.samplingReq }}</div>
                    </abc-flex>
                </div>
            </section>

            <section v-if="feeComposeList.length">
                <ul>
                    <template v-for="(item, index) in feeComposeList">
                        <li :key="`${item.id }_${ index}`" class="fee-item">
                            <div class="name">
                                {{ item | formatGoodsName }}
                            </div>
                            <div class="content gray">
                                {{ usageInfoStr(item) }}
                            </div>
                            <div v-if="item.shebaoNationalCode" class="code gray">
                                医保码：{{ item.shebaoNationalCode | formatShebaoCode }}
                            </div>
                        </li>
                        <abc-flex v-if="item.shebao?.restriction" :key="`restriction_${item.id }_${ index}`">
                            <div class="gray">
                                限制：
                            </div>
                            <div class="gray" style="flex: 1;">
                                {{ item.shebao.restriction }}
                            </div>
                        </abc-flex>
                    </template>
                </ul>
            </section>

            <section v-if="productInfoChildren.length" class="targets-section">
                <h6 class="gray">
                    指标项：
                </h6>
                <ul>
                    <li v-for="(item, index) in productInfoChildren" :key="`${item.id }_${ index}`" class="target-item">
                        {{ item | formatGoodsName }}
                    </li>
                </ul>
            </section>
        </div>
    </div>
</template>

<script>
    import {
        formatMoney, getSpec, medicalFeeGrade2Str,
    } from 'src/filters/index';
    import {
        GoodsSubTypeEnum, GoodsTypeEnum,
    } from '@abc/constants';
    import i18n from '@/i18n';
    import TreatmentConfig from '@/assets/configure/treatment-config.js';
    import BizExamBusinessTag from 'src/components-composite/biz-exam-business-tag';

    export default {
        components: { BizExamBusinessTag },
        props: {
            goods: {
                type: Object,
                required: true,
            },
            config: {
                type: Object,
                required: true,
            },
            productInfo: Object,
            destroyPopper: {
                type: Function,
            },
        },

        data() {
            return {
                i18n,
                isInContent: false,
            };
        },
        computed: {
            goodsName() {
                let name = '';
                if (this.productInfo) {
                    name = this.productInfo.name;
                }
                name = name || this.goods.name;
                return name || '';
            },

            goodsTypeStr() {
                const {
                    type,
                    subType,
                    customTypeName,
                } = this.productInfo;
                let str = '';
                if (type === GoodsTypeEnum.EXAMINATION) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test) {
                        str = '检查';
                    } else if (subType === GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Inspect) {
                        str = '检验';
                    }
                } else if (type === GoodsTypeEnum.TREATMENT) {
                    if (subType === GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Treatment) {
                        str = '治疗';
                    } else if (subType === GoodsSubTypeEnum[GoodsTypeEnum.TREATMENT].Physiotherapy) {
                        str = '理疗';
                    }
                } else if (type === GoodsTypeEnum.OTHER) {
                    str = '其他';
                } else if (type === GoodsTypeEnum.NURSE) {
                    str = '护理';
                } else if (type === GoodsTypeEnum.SURGERY) {
                    str = '手术';
                }
                if (customTypeName) {
                    str += `-${customTypeName}`;
                }
                return str;
            },

            deviceNameList() {
                const {
                    children = [], type, subType,
                } = this.productInfo || {};

                // 手术医嘱有 children，但是保存的是手术操作，和设备含义不一样，只是共用了同一个字段
                if (type === GoodsTypeEnum.SURGERY && subType === GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY) return [];

                const deviceInfoList = children.reduce((res, child) => {
                    if (
                        !res.find((r) => r.deviceModeId === child.deviceInfo?.deviceModeId) &&
                        child.deviceInfo?.innerFlag !== 1
                    ) {
                        res.push(child.deviceInfo);
                    }
                    return res;
                }, []);

                return deviceInfoList.map((d) => d.deviceUuid);
            },

            medicalFeeGrade2Str() {
                return medicalFeeGrade2Str(this.productInfo.medicalFeeGrade || '');
            },

            needExecutive() {
                return typeof this.productInfo.needExecutive === 'number';
            },

            productInfoChildren() {
                const {
                    type, subType,
                } = this.productInfo || {};

                // 手术医嘱有 children，但是保存的是手术操作，和设备含义不一样，只是共用了同一个字段
                if (type === GoodsTypeEnum.SURGERY && subType === GoodsSubTypeEnum[GoodsTypeEnum.SURGERY].SURGERY) return [];

                return this.productInfo.children || [];
            },
            feeComposeList() {
                return this.productInfo.feeComposeList || [];
            },
            goodsGender() {
                const { gender } = this.productInfo;
                const GENDER_ENUM = {
                    0: '不限',
                    1: '男',
                    2: '女',
                };
                return GENDER_ENUM[gender];
            },
        },

        methods: {
            getSpec,
            stockStr(productInfo) {
                if (this.config && !this.config.showStock) return '';
                if (!productInfo) return '';

                const stockPieceCount = productInfo.stockPieceCount || 0,
                      stockPackageCount = productInfo.stockPackageCount || 0,
                      pieceUnit = productInfo.pieceUnit || '',
                      packageUnit = productInfo.packageUnit || '';
                let str = '';

                if (!productInfo.id || !(stockPackageCount || stockPieceCount)) return '无库存';

                if (stockPackageCount && packageUnit) {
                    str += `${stockPackageCount}${packageUnit}`;
                }
                if (stockPieceCount && pieceUnit) {
                    str += `${stockPieceCount}${pieceUnit}`;
                }
                if (!str) return '无库存';
                return `库存 ${str}`;
            },
            unitPriceStr(productInfo) {
                if (this.config && !this.config.showPrice) return '';

                if (!productInfo) return '无价格';

                const {
                    packagePrice = 0,
                    packageUnit,
                    piecePrice = 0,
                    pieceUnit,
                    subType,
                    type,
                    id,
                } = productInfo;

                if (!id) return '无价格';

                if (type === 1 && subType === 2) {
                    return pieceUnit ? `${formatMoney(piecePrice, false)} / ${pieceUnit}` : '无价格';
                }
                return packageUnit ? `${formatMoney(packagePrice)} / ${packageUnit}` : '无价格';

            },
            lastPackageCostPrice(item) {
                let str = '';
                if (item.lastPackageCostPrice) {
                    const price = formatMoney(item.lastPackageCostPrice, false);
                    str += `进价 ${price}/${item.productInfo && (item.productInfo.packageUnit || item.productInfo.pieceUnit) || ''}`;
                }
                return str;
            },

            getUnitCount(item) {
                // 有productInfo代表是 composeChildren
                let unitCount = 0;
                if (item.unitCount) {
                    unitCount = item.unitCount || 1;
                } else {
                    const _unitCount = item.composePackageCount || item.composePieceCount || 1;
                    unitCount = _unitCount * (this.goods.unitCount || 1);
                }
                return +unitCount.toFixed(2);
            },
            getUnit(item) {
                let unit = '';
                if (item.unit) {
                    unit = item.unit || '';
                } else {
                    unit = item.composeUseDismounting ? item.pieceUnit : item.packageUnit;
                }
                unit = unit || '';
                const isExaminationOrTreatment = [item.type, item.productType].some((type) =>
                    [
                        GoodsTypeEnum.TREATMENT,
                        GoodsTypeEnum.EXAMINATION,
                        GoodsTypeEnum.OTHER,
                    ].includes(type),
                );
                return isExaminationOrTreatment ? `*${unit}` : unit;
            },
            usageInfoStr(item) {
                const _arr = [];
                _arr.push(this.getUnitCount(item) + this.getUnit(item));
                // 不展示金额
                // _arr.push(`共${this.$t('currencySymbol')} ${formatMoney(item.composePrice)}`);
                return _arr.join('，');
            },

            handleLeave() {
                if (this.config.trigger !== 'hover') return;
                this.isInContent = false;
                if (typeof this.destroyPopper === 'function') {
                    this.destroyPopper();
                }
                this.$destroy();
                $(this.$el).remove();
            },

            viewF1() {
                document.dispatchEvent(
                    new window.KeyboardEvent('keydown', { keyCode: 112 }),
                );
                this.handleLeave();
            },

            filterExItemType (exItem) {
                const { itemCategory } = TreatmentConfig;
                const filterExItem = itemCategory.find((item) => {
                    return item.value === Number(exItem);
                }) || {};
                return filterExItem.name;
            },
        },
    };
</script>
