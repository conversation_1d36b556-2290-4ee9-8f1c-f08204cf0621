import { set as VueSet } from 'vue';
import {
    ShebaoPayModeByTypeEnum, ShebaoPayTypeEnum,
} from '@/common/constants/outpatient';
import { ShebaoPayMode } from '@/common/constants/inventory.js';

/**
 * @desc
 * <AUTHOR> @date
 */
export default function useOutpatientCommon() {
    const setComposeChildrenPayType = (children, val) => {
        if (!children || !children.length) return;
        children.forEach((y) => {
            if (
                y.productInfo &&
                y.productInfo.medicalFeeGrade &&
                (y.productInfo.shebaoPayMode ?? 0) < ShebaoPayMode.NO_USE
            ) {
                VueSet(y, 'payType', val);
                VueSet(y, 'shebaoPayMode', ShebaoPayModeByTypeEnum[val]);
            }
            setComposeChildrenPayType(y.composeChildren, val);
        });
    };

    const handleChangePayType = (val, item, form, itemsKey = 'prescriptionFormItems') => {
        if (val === ShebaoPayTypeEnum.FORM_SELF) {
            form[itemsKey].forEach((x) => {
                if (
                    x.productInfo &&
                    x.productInfo.medicalFeeGrade &&
                    (x.productInfo.shebaoPayMode ?? 0) < ShebaoPayMode.NO_USE
                ) {
                    VueSet(x, 'payType', ShebaoPayTypeEnum.SELF);
                    VueSet(x, 'shebaoPayMode', ShebaoPayModeByTypeEnum[ShebaoPayTypeEnum.SELF]);
                }
                setComposeChildrenPayType(x.composeChildren, ShebaoPayTypeEnum.SELF);
            });
            return;
        }
        if (
            item.productInfo &&
            item.productInfo.medicalFeeGrade &&
            (item.productInfo.shebaoPayMode ?? 0) < ShebaoPayMode.NO_USE
        ) {
            VueSet(item, 'payType', val);
            VueSet(item, 'shebaoPayMode', ShebaoPayModeByTypeEnum[val]);
            setComposeChildrenPayType(item.composeChildren, val);
        }
    };

    return {
        handleChangePayType,
    };
}
