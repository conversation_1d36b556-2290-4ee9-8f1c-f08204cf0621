import { ref } from 'vue';

/**
 * @desc 供应商拼音排序 Hook - 基于供应商数据中的 namePyFirst 字段进行拼音排序
 * <AUTHOR> Assistant
 * @date 2025-01-21
 */
export default function useSupplierPinYinSort() {
    const supplierList = ref([]);
    /**
     * 从供应商的 namePyFirst 字段中提取拼音首字母
     * @param {Object} supplier - 供应商对象
     * @returns {string} - 拼音首字母
     */
    const getSupplierPinyinFirstLetters = (supplier) => {
        if (!supplier || !supplier.namePyFirst) {
            return '';
        }

        // namePyFirst 格式: "阿斯加德哈吉斯看跌期权我饿王企鹅按文档|asijiadehajisikandieqiquanwoewangqieanwendang|asjdhjskdqqwewqeawd"
        // 我们只需要取拼音的第一个字母
        const parts = supplier.namePyFirst.split('|');

        if (parts.length >= 3) {
            // 取第三部分的第一个字母
            return parts[2].charAt(0).toUpperCase();
        } if (parts.length === 2) {
            // 如果只有两部分，取第二部分的第一个字母
            return parts[1].charAt(0).toUpperCase();
        }
        // 如果格式不符合预期，使用供应商名称的首字符
        return supplier.name ? supplier.name.charAt(0).toUpperCase() : '';

    };

    /**
     * 从供应商名称获取拼音首字母（通过在供应商列表中查找）
     * @param {string} supplierName - 供应商名称
     * @returns {string} - 拼音首字母
     */
    const getSupplierNamePinyinFirstLetters = (supplierName) => {
        if (!supplierName) return '';

        // 在供应商列表中查找匹配的供应商
        const supplier = supplierList.value.find((s) => s.name === supplierName);

        if (supplier) {
            // 如果找到了供应商对象，使用其 namePyFirst 字段
            return getSupplierPinyinFirstLetters(supplier);
        }

        // 如果没有找到，使用后备方案：取名称的前几个字符
        return supplierName.substring(0, Math.min(5, supplierName.length));
    };

    /**
     * 根据供应商名称对商品列表进行排序（用于商品列表中的供应商排序）
     * @param {Array} list - 要排序的商品数组
     * @param {Function} getSupplierFn - 获取供应商信息的函数
     * @param {string} order - 排序方向 'asc' | 'desc'
     * @returns {Array} - 排序后的数组
     */
    const sortGoodsBySupplierPinyin = (list, getSupplierFn, order = 'asc') => {
        if (!Array.isArray(list) || list.length === 0) {
            return list;
        }

        return [...list].sort((a, b) => {
            // 获取供应商名称
            const supplierNameA = getSupplierFn(a) || '';
            const supplierNameB = getSupplierFn(b) || '';

            // 如果A没有供应商，B有供应商，A排在后面
            if (!supplierNameA && supplierNameB) return 1;
            // 如果A有供应商，B没有供应商，A排在前面
            if (supplierNameA && !supplierNameB) return -1;
            // 如果都没有供应商，保持原顺序
            if (!supplierNameA && !supplierNameB) return 0;

            // 都有供应商，按拼音排序
            const pinyinA = getSupplierNamePinyinFirstLetters(supplierNameA);
            const pinyinB = getSupplierNamePinyinFirstLetters(supplierNameB);

            const compareResult = pinyinA.localeCompare(pinyinB);

            return order === 'asc' ? compareResult : -compareResult;
        });
    };

    const setSupplierList = function(list) {
        supplierList.value = list;
    };

    return {
        // 核心方法
        getSupplierPinyinFirstLetters,
        getSupplierNamePinyinFirstLetters,
        sortGoodsBySupplierPinyin,
        setSupplierList,
    };
}
