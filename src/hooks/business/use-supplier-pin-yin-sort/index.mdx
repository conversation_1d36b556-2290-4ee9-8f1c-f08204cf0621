import { Meta, Story, Canvas, Source } from '@storybook/blocks';
import * as useSupplierPinYinSortStories from './index.stories';

<Meta of={ useSupplierPinYinSortStories} />

# useSupplierPinYinSort

专门用于供应商数据的拼音排序功能，基于供应商数据中的 `namePyFirst` 字段进行准确的拼音首字母排序。

## 功能特点

- 🎯 **精确拼音识别**：基于供应商数据中的 `namePyFirst` 字段，只取拼音首字母进行排序
- 📊 **智能排序**：支持升序(asc)和降序(desc)排序，无供应商的商品自动排在最末位
- 🏢 **供应商专用**：专门针对供应商数据结构优化
- 🔍 **智能查找**：通过供应商名称在供应商列表中查找对应的拼音数据
- 🛡️ **容错处理**：对于无供应商信息的商品提供合理的后备处理
- 🔧 **易于集成**：可与表格排序等场景无缝集成

## 使用场景

- 采购订单中按供应商排序
- 商品列表按供应商拼音排序
- 库存管理中的供应商排序
- 任何需要按供应商拼音排序的场景

## 使用示例

<Canvas>
    <Story of={ useSupplierPinYinSortStories.Usage} />
</Canvas>

## API 参考

### 返回值

```typescript
{
  // 核心方法
  getSupplierPinyinFirstLetters: (supplier: Object) => string;                         // 从供应商对象获取拼音首字母
  getSupplierNamePinyinFirstLetters: (supplierName: string) => string;                 // 从供应商名称获取拼音首字母
  sortGoodsBySupplierPinyin: (list: Array, getSupplierFn: Function, order?: string) => Array;  // 商品按供应商拼音排序
}
```

### 参数说明

- `supplierList`: 供应商列表数组（hooks 初始化参数）
- `list`: 要排序的商品数组
- `getSupplierFn`: 获取供应商名称的函数
- `order`: 排序方向，'asc'(升序) 或 'desc'(降序)

## 使用方法

### 基础用法

```javascript
import usePinYinSort from '@/hooks/business/use-pin-yin-sort';

const { sortByPinyin } = usePinYinSort();

// 按名称字段排序
const sortedList = sortByPinyin(supplierList, 'name', 'asc');

// 使用自定义函数获取排序文本
const sortedList2 = sortByPinyin(dataList, (item) => item.supplier?.name || '', 'desc');
```

### 与表格排序集成

```javascript
const { createSortHandler } = usePinYinSort();

// 创建排序处理函数
const handleSortChange = createSortHandler(
  dataList,
  (item) => item.hisGoodsStockStatInfo?.lastStockInOrderSupplier || ''
);

// 在表格排序事件中使用
const onTableSort = ({ orderBy, orderType }) => {
  if (orderBy === 'supplierName') {
    const sortedData = handleSortChange(orderBy, orderType);
    // 更新数据
  }
};
```
