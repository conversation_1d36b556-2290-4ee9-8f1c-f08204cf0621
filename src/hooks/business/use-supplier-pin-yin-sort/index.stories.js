import { ref } from 'vue';
import useSupplierPinYinSort from './index';

export default {
    title: 'HOOKS/业务/useSupplierPinYinSort',
};

export const Usage = () => ({
    setup() {
        // 供应商列表数据（包含 namePyFirst 字段）
        const supplierList = [
            {
                id: 1,
                name: '北京医药公司',
                namePyFirst: '北京医药公司|beijingyiyaogongsi|bjyygz',
            },
            {
                id: 2,
                name: '上海制药厂',
                namePyFirst: '上海制药厂|shanghaiziyaochang|shzyc',
            },
            {
                id: 3,
                name: '广州药业',
                namePyFirst: '广州药业|guangzhouyaoye|gzyy',
            },
            {
                id: 4,
                name: '深圳医疗器械',
                namePyFirst: '深圳医疗器械|shenzhenyiliaoqixie|szylyqx',
            },
            {
                id: 5,
                name: '杭州生物科技',
                namePyFirst: '杭州生物科技|hangzhoushengwukeji|hzswkj',
            },
        ];

        const {
            sortGoodsBySupplierPinyin,
            getSupplierNamePinyinFirstLetters,
        } = useSupplierPinYinSort(supplierList);

        // 测试商品数据（包含有供应商和无供应商的商品）
        const goodsList = ref([
            {
                id: 1, goodsName: '阿莫西林', supplier: '北京医药公司', type: '抗生素', 
            },
            {
                id: 2, goodsName: '头孢克肟', supplier: '上海制药厂', type: '抗生素', 
            },
            {
                id: 3, goodsName: '布洛芬', supplier: '广州药业', type: '解热镇痛', 
            },
            {
                id: 4, goodsName: '体温计', supplier: '深圳医疗器械', type: '器械', 
            },
            {
                id: 5, goodsName: '维生素C', supplier: '杭州生物科技', type: '维生素', 
            },
            {
                id: 6, goodsName: '感冒灵', supplier: '', type: '感冒药', 
            }, // 无供应商
            {
                id: 7, goodsName: '创可贴', supplier: null, type: '外用', 
            }, // 无供应商
            {
                id: 8, goodsName: '酒精', supplier: undefined, type: '消毒', 
            }, // 无供应商
        ]);

        const sortedList = ref([...goodsList.value]);
        const currentOrder = ref('原始顺序');

        // 排序方法
        const sortAsc = () => {
            sortedList.value = sortGoodsBySupplierPinyin(
                goodsList.value,
                (item) => item.supplier || '',
                'asc',
            );
            currentOrder.value = '拼音升序';
        };

        const sortDesc = () => {
            sortedList.value = sortGoodsBySupplierPinyin(
                goodsList.value,
                (item) => item.supplier || '',
                'desc',
            );
            currentOrder.value = '拼音降序';
        };

        const resetOrder = () => {
            sortedList.value = [...goodsList.value];
            currentOrder.value = '原始顺序';
        };

        return {
            goodsList,
            sortedList,
            currentOrder,
            sortAsc,
            sortDesc,
            resetOrder,
            getSupplierNamePinyinFirstLetters,
        };
    },
    template: `
        <div style="padding: 20px;">
            <h3>供应商拼音排序 Hook 演示</h3>

            <div style="margin-bottom: 20px;">
                <abc-space>
                    <abc-button @click="sortAsc" type="primary">按供应商拼音升序</abc-button>
                    <abc-button @click="sortDesc" type="primary">按供应商拼音降序</abc-button>
                    <abc-button @click="resetOrder">重置顺序</abc-button>
                </abc-space>
            </div>

            <div style="margin-bottom: 20px;">
                <abc-text theme="gray">当前排序：{{ currentOrder }}</abc-text>
                <br>
                <abc-text theme="gray" size="small">
                    说明：有供应商的商品按拼音首字母排序，无供应商的商品排在最末位
                </abc-text>
            </div>

            <abc-table
                :data-list="sortedList"
                :render-config="{
                    hasInnerBorder: true,
                    list: [
                        { key: 'id', label: 'ID', style: { width: '60px' } },
                        { key: 'goodsName', label: '商品名称', style: { minWidth: '120px' } },
                        { key: 'supplier', label: '供应商', style: { minWidth: '150px' } },
                        { key: 'pinyin', label: '拼音首字母', style: { width: '100px' } },
                        { key: 'type', label: '类型', style: { width: '100px' } }
                    ]
                }"
            >
                <template #supplier="{ trData: row }">
                    <abc-table-cell>
                        <abc-text :theme="row.supplier ? 'default' : 'gray'">
                            {{ row.supplier || '无供应商' }}
                        </abc-text>
                    </abc-table-cell>
                </template>
                <template #pinyin="{ trData: row }">
                    <abc-table-cell>
                        <abc-text theme="gray" size="small">
                            {{ row.supplier ? getSupplierNamePinyinFirstLetters(row.supplier) : '-' }}
                        </abc-text>
                    </abc-table-cell>
                </template>
            </abc-table>
        </div>
    `,
});
