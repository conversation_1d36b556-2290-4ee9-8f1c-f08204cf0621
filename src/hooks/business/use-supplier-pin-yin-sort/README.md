# useSupplierPinYinSort Hook

专门用于供应商数据的拼音排序功能，基于供应商数据中的 `namePyFirst` 字段进行准确的拼音首字母排序。

## 功能特点

- 🎯 **精确拼音识别**：基于供应商数据中的 `namePyFirst` 字段，只取拼音首字母进行排序
- 📊 **智能排序**：支持升序(asc)和降序(desc)排序，无供应商的商品自动排在最末位
- 🏢 **供应商专用**：专门针对供应商数据结构优化
- 📦 **商品排序支持**：支持按供应商名称对商品列表进行排序
- 🔍 **智能查找**：通过供应商名称在供应商列表中查找对应的拼音数据
- 🛡️ **容错处理**：对于无供应商信息的商品提供合理的后备处理
- 🔧 **易于集成**：可与表格排序等场景无缝集成

## 安装使用

```javascript
import useSupplierPinYinSort from '@/hooks/business/use-supplier-pin-yin-sort';

// 使用时需要传入供应商列表
const { sortGoodsBySupplierPinyin } = useSupplierPinYinSort(supplierList);
```

## API 参考

### 返回值

```typescript
{
  // 核心方法
  getSupplierPinyinFirstLetters: (supplier: Object) => string;                         // 从供应商对象获取拼音首字母
  getSupplierNamePinyinFirstLetters: (supplierName: string) => string;                 // 从供应商名称获取拼音首字母
  sortGoodsBySupplierPinyin: (list: Array, getSupplierFn: Function, order?: string) => Array;  // 商品按供应商拼音排序
}
```

### 参数说明

- `supplierList`: 供应商列表数组（hooks 初始化参数）
- `list`: 要排序的商品数组
- `getSupplierFn`: 获取供应商名称的函数
- `order`: 排序方向，'asc'(升序) 或 'desc'(降序)

## 使用示例

### 基础用法

```javascript
import useSupplierPinYinSort from '@/hooks/business/use-supplier-pin-yin-sort';

export default {
  setup() {
    // 供应商列表（包含 namePyFirst 字段）
    const supplierList = ref([
      {
        name: '北京医药公司',
        namePyFirst: '北京医药公司|beijingyiyaogongsi|bjyygz'
      },
      {
        name: '上海制药厂',
        namePyFirst: '上海制药厂|shanghaiziyaochang|shzyc'
      },
      {
        name: '广州药业',
        namePyFirst: '广州药业|guangzhouyaoye|gzyy'
      }
    ]);

    // 传入供应商列表初始化 hooks
    const { sortGoodsBySupplierPinyin } = useSupplierPinYinSort(supplierList.value);

    const goodsList = ref([
      {
        goodsName: '阿莫西林',
        hisGoodsStockStatInfo: { lastStockInOrderSupplier: '北京医药公司' }
      },
      {
        goodsName: '头孢克肟',
        hisGoodsStockStatInfo: { lastStockInOrderSupplier: '上海制药厂' }
      },
      {
        goodsName: '布洛芬',
        hisGoodsStockStatInfo: { lastSupplierName: '广州药业' }
      },
      {
        goodsName: '维生素C',
        hisGoodsStockStatInfo: { lastStockInOrderSupplier: '' } // 无供应商
      }
    ]);

    // 按供应商拼音首字母升序排序（无供应商的排在最末位）
    const sortedList = sortGoodsBySupplierPinyin(
      goodsList.value,
      (item) => item.hisGoodsStockStatInfo?.lastStockInOrderSupplier ||
                item.hisGoodsStockStatInfo?.lastSupplierName || '',
      'asc'
    );

    return { sortedList };
  }
};
```

### 表格排序集成

```javascript
import useSupplierPinYinSort from '@/hooks/business/use-supplier-pin-yin-sort';

export default {
  setup() {
    // 获取供应商列表
    const { currentSupplierList } = useSearchSupplier({
      status: 1,
      excludeInitSupplier: true,
    });

    // 传入供应商列表初始化 hooks
    const { sortGoodsBySupplierPinyin } = useSupplierPinYinSort(currentSupplierList);

    const handleSortChange = ({ orderBy, orderType }) => {
      if (orderBy === 'lastPackageCostPrice') {
        // 按供应商名称对商品进行排序
        const sortedData = sortGoodsBySupplierPinyin(
          goodsList.value,
          (item) => item.hisGoodsStockStatInfo?.lastStockInOrderSupplier ||
                    item.hisGoodsStockStatInfo?.lastSupplierName || '',
          orderType
        );
        goodsList.value = sortedData;
      }
    };

    return { handleSortChange };
  }
};
```

### 实际项目中的使用

在采购订单对话框中的使用示例：

```javascript
// src/views-pharmacy/inventory/frames/purchase/require-goods/order-dialog.vue

import useSupplierPinYinSort from '@/hooks/business/use-supplier-pin-yin-sort';

export default {
  setup() {
    // 获取供应商列表
    const { currentSupplierList } = useSearchSupplier({
      status: 1,
      excludeInitSupplier: true,
    });

    // 传入供应商列表初始化 hooks
    const { sortGoodsBySupplierPinyin } = useSupplierPinYinSort(currentSupplierList);

    return { sortGoodsBySupplierPinyin };
  },

  methods: {
    handleSortChange({ orderBy, orderType }) {
      if (orderBy === 'lastPackageCostPrice') {
        this.sortBySupplierPinyin(orderType);
      }
    },

    sortBySupplierPinyin(orderType) {
      // 使用 hooks 中的供应商拼音排序方法
      const sortedList = this.sortGoodsBySupplierPinyin(
        this.order.list,
        (item) => item.hisGoodsStockStatInfo?.lastStockInOrderSupplier ||
                  item.hisGoodsStockStatInfo?.lastSupplierName || '',
        orderType
      );

      this.order.list = sortedList;
    }
  }
};
```

## 排序规则

采用**供应商数据中的 namePyFirst 字段**进行精确排序：

### 1. namePyFirst 字段格式
```
"阿斯加德哈吉斯看跌期权我饿王企鹅按文档|asijiadehajisikandieqiquanwoewangqieanwendang|asjdhjskdqqwewqeawd"
```

字段包含三部分，用 `|` 分隔：
- **第一部分**：中文名称
- **第二部分**：完整拼音
- **第三部分**：拼音首字母缩写

### 2. 提取规则
- **优先使用第三部分的第一个字母**：如 `asjdhjskdqqwewqeawd` → `A`
- **后备方案1**：如果只有两部分，从第二部分提取第一个字母，如 `beijingyiyaogongsi` → `B`
- **后备方案2**：如果格式异常，使用供应商名称首字符

### 3. 智能排序逻辑
- **有供应商的商品**：按拼音首字母进行升序/降序排序
- **无供应商的商品**：统一排在最末位，保持原有顺序
- **混合排序**：确保有供应商信息的商品始终排在前面

### 4. 排序优势
- ✅ **100% 准确**：基于后端提供的标准拼音数据
- ✅ **性能优异**：无需前端计算拼音
- ✅ **智能处理**：自动将无供应商商品排在末位
- ✅ **一致性强**：与系统其他拼音排序保持一致

## 排序示例

### 升序排序结果
```
1. 阿莫西林 - 阿斯加德医药公司 (A)
2. 头孢克肟 - 北京医药公司 (B)
3. 感冒灵 - 广州药业 (G)
4. 布洛芬 - 上海制药厂 (S)
5. 维生素C - 无供应商
6. 钙片 - 无供应商
```

### 降序排序结果
```
1. 布洛芬 - 上海制药厂 (S)
2. 感冒灵 - 广州药业 (G)
3. 头孢克肟 - 北京医药公司 (B)
4. 阿莫西林 - 阿斯加德医药公司 (A)
5. 维生素C - 无供应商
6. 钙片 - 无供应商
```

## 注意事项

1. **依赖数据格式**：需要供应商数据包含正确格式的 `namePyFirst` 字段
2. **供应商列表**：必须在初始化时传入完整的供应商列表
3. **后备处理**：对于格式异常的数据，会自动降级到后备方案
4. **无供应商处理**：没有供应商信息的商品会自动排在最末位
5. **性能优化**：直接使用后端提供的拼音数据，避免前端计算开销
6. **排序稳定性**：使用 `localeCompare` 方法，确保字符串比较的准确性

## 测试

可以在 Storybook 中查看完整的使用示例和测试用例：

```
HOOKS/业务/useSupplierPinYinSort
```

## 更新日志

### v1.0.0
- ✅ 基于 `namePyFirst` 字段的精确拼音排序
- ✅ 只取拼音首字母进行排序
- ✅ 智能供应商查找机制
- ✅ 无供应商商品排在末位
- ✅ 支持升序/降序排序
- ✅ 完整的容错处理
