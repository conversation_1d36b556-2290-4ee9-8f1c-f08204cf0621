import ReportAPI from 'api/report';
import {
    ToastFunc as Toast,
} from '@abc/ui-pc';

export default class CaptchaVerify {
    constructor() {
        this.appId = '190757579';
        this.captcha = null;
    }

    static getInstance() {
        if (!this._ins) {
            this._ins = new CaptchaVerify();
        }

        return this._ins;
    }

    showCaptcha() {
        try {
            if (this.captcha) return;
            // 生成一个验证码对象
            this.captcha = new window.TencentCaptcha(this.appId, this.callback.bind(this), {
                userLanguage: 'zh-cn',
            });
            // 调用方法，显示验证码
            this.captcha.show();
        } catch (error) {
            // 加载异常，调用验证码js加载错误处理函数
            this.loadErrorCallback();
        }
    }

    // 定义回调函数
    async callback(res) {
        this.captcha = null;
        if (res.ret === 0) {
            const { data } = await ReportAPI.captchaVerify({
                ticket: res.ticket,
                randStr: res.randstr,
            });
            if (data.code !== 200) {
                Toast({
                    message: '验证失败',
                    type: 'error',
                    duration: 3 * 1000,
                });
            }
        } else if (res.ret === 2) {
            console.log('用户主动关闭验证码');
        }
        console.log('callback:', res);
    }
    loadErrorCallback() {
        this.captcha = null;
        const ticket = `trerror_1001_${this.appId}_${Math.floor(new Date().getTime() / 1000)}`;
        this.callback({
            ret: 0,
            randstr: `@${Math.random().toString(36).substr(2)}`,
            ticket,
            errorCode: 1001,
            errorMessage: 'jsload_error',
        });
    }
}
