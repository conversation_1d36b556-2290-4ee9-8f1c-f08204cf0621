<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        :title="dialogTitle"
        size="small"
        @input="(val) => $emit('input', val)"
    >
        <abc-form ref="abcForm" item-block>
            <abc-form-item
                v-if="scene === 0"
                label="卡号"
                required
                hidden-red-dot
            >
                <abc-input v-model="curCardId" adaptive-width placeholder="请输入完整卡号"></abc-input>
            </abc-form-item>
            <abc-form-item
                v-if="scene === 1"
                label="密码"
                required
                hidden-red-dot
            >
                <abc-input
                    v-model="curCardPassword"
                    type="password"
                    placeholder="请输入储值卡密码"
                    adaptive-width
                ></abc-input>
            </abc-form-item>
        </abc-form>
        <div slot="footer" class="dialog-footer">
            <abc-button :loading="btnLoading" @click="handleClick">
                确定
            </abc-button>
            <abc-button type="blank" @click="visible = false">
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    import DialogCardList from '@/service/charge/components/dialog-card-list';
    import PayAPI from 'api/pay';

    export default {
        props: {
            // 0获取卡号，1获取密码
            scene: {
                type: Number,
                required: true,
            },
            chargeData: {
                type: Object,
                default: () => {
                    return {
                        thirdPartyPayCardId: '',
                        thirdPartyPayCardPassword: '',
                    };
                },
            },
            cardPassword: String,
            onConfirm: Function,
            onClose: Function,
        },
        data() {
            return {
                visible: false,
                btnLoading: false,
                curCardId: this.chargeData.thirdPartyPayCardId,
                curCardPassword: this.chargeData.thirdPartyPayCardPassword,
            };
        },
        computed: {
            dialogTitle() {
                return this.scene === 1 ? '储值卡密码' : '储值卡号';
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.onClose && this.onClose(false);
                    this.destroyElement();
                }
            },
        },
        methods: {
            async handleClick() {
                this.$refs.abcForm.validate((valid) => {
                    if (valid) {
                        this.confirmHandler();
                    }
                });
            },
            async confirmHandler() {
                if (this.scene === 0) {
                    await this.getPayInfo();
                    return;
                }
                this.getPayPassword();
            },
            async getPayInfo() {
                this.btnLoading = true;
                try {
                    const { data } = await PayAPI.getPayInfo({
                        cardId: this.curCardId,
                        payMode: this.chargeData.payMode,
                        paySubMode: this.chargeData.paySubMode,
                    });

                    const { accounts } = data;
                    if (!accounts) {
                        this.$alert({
                            type: 'warn',
                            title: '提示',
                            content: '未查询到该卡号',
                        });
                        return;
                    }

                    if (accounts.length === 1) {
                        const {
                            balance,
                        } = accounts[0];

                        if (balance <= 0) {
                            this.$alert({
                                type: 'warn',
                                title: '提示',
                                content: '该会员卡余额为 0',
                            });
                            return;
                        }
                        this.onConfirm(accounts[0]);
                        this.visible = false;
                    } else {
                        new DialogCardList({
                            accountList: accounts,
                            onConfirm: (selectedCard) => {

                                const {
                                    balance,
                                } = selectedCard;

                                if (balance <= 0) {
                                    this.$alert({
                                        type: 'warn',
                                        title: '提示',
                                        content: '该会员卡余额为 0',
                                    });
                                    return;
                                }

                                this.onConfirm(selectedCard);
                                this.visible = false;
                            },
                        }).generateDialog();
                        this.btnLoading = false;
                    }
                } catch (e) {
                    console.error(e);
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: e.message,
                    });
                } finally {
                    this.btnLoading = false;
                }
            },
            getPayPassword() {
                this.onConfirm(this.curCardPassword);
                this.visible = false;
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
