<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="选择储值卡"
        size="small"
        :show-header-border-bottom="true"
        :responsive="false"
        :fullscreen="false"
        @input="(val) => $emit('input', val)"
    >
        <abc-flex
            vertical
            justify="flex-start"
            align="flex-start"
            gap="middle"
        >
            <abc-text size="normal" theme="black">
                该手机号存在多个储值卡，请选择
            </abc-text>

            <abc-card
                radius-size="small"
                padding-size="none"
                style="width: 100%;"
                :shadow="false"
                :border="true"
            >
                <abc-flex
                    vertical
                    justify="flex-start"
                    align="flex-start"
                    gap="10"
                    style="padding: 4px 4px 4px 4px;"
                >
                    <abc-list
                        v-abc-loading="loading"
                        :data-list="cardList"
                        :scrollable="false"
                        :show-divider="true"
                        :no-close-border="true"
                        :no-top-border="true"
                        :no-hover-border="true"
                        :divider-config="{
                            size: 'normal',
                            theme: 'light',
                            margin: 'none',
                            variant: 'solid',
                            layout: 'horizontal'
                        }"
                        :show-icon="false"
                        :readonly="false"
                        size="default"
                        @click-item="handleItemClick"
                    >
                        <template
                            #append="{
                                item,
                            }"
                        >
                            <abc-space direction="horizontal" size="4">
                                <abc-radio
                                    :value="selectedCardId"
                                    :label="item.id"
                                    :enable-cancel="false"
                                    :disabled="false"
                                    :show-default-label="false"
                                >
                                </abc-radio>
                            </abc-space>
                        </template>
                    </abc-list>
                </abc-flex>
            </abc-card>
        </abc-flex>
        <div slot="footer" class="dialog-footer">
            <abc-button
                shape="square"
                variant="fill"
                theme="primary"
                size="normal"
                :loading="btnLoading"
                :disabled="!selectedCard"
                @click="handleConfirm"
            >
                确定
            </abc-button>
            <abc-button
                shape="square"
                variant="ghost"
                theme="primary"
                size="normal"
                @click="visible = false"
            >
                取消
            </abc-button>
        </div>
    </abc-dialog>
</template>

<script>
    export default {
        props: {
            onConfirm: Function,
            onClose: Function,
            accountList: {
                type: Array,
                default: () => [],
            },
        },
        data() {
            return {
                visible: false,
                loading: false,
                btnLoading: false,
                selectedCard: null,
                selectedCardId: null,
            };
        },
        computed: {
            cardList() {
                return this.accountList.map((item, index) => {
                    return {
                        id: item.cardId,
                        title: item.cardName,
                        content: item.cardId,
                        checked: index === 0,
                        ...item,
                    };
                });
            },
        },
        watch: {
            visible(val) {
                if (val) {
                    // 设置默认选中第一个
                    if (this.cardList.length > 0) {
                        this.selectedCard = this.cardList[0];
                        this.selectedCardId = this.cardList[0].id;
                    }
                } else {
                    this.onClose && this.onClose(false);
                    this.destroyElement();
                }
            },
        },
        methods: {
            handleItemClick(event, item) {
                // 清除其他选中状态
                this.cardList.forEach((card) => {
                    card.checked = false;
                });

                // 设置当前选中
                item.checked = true;
                this.selectedCard = item;
                this.selectedCardId = item.id;
            },
            async handleConfirm() {
                this.btnLoading = true;
                try {
                    this.onConfirm && this.onConfirm(this.selectedCard);
                    this.visible = false;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
