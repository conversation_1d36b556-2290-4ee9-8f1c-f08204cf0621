<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        type="warn"
        preset="confirm"
        title="追溯码风险提示"
        show-icon
        confirm-text="取消"
        cancel-text="继续绑定"
        :show-close="false"
        :on-confirm="onCancel"
        :on-cancel="onConfirm"
        append-to-body
    >
        <abc-flex vertical>
            <abc-text>
                当前绑定标识码不在市医保三码库内，请确认医保对码、标识码绑定是否正确，否则将面临医保扣款风险。以下为市医保三码库数据：
            </abc-text>

            <abc-divider variant="dashed"></abc-divider>

            <abc-flex vertical>
                <abc-descriptions
                    :column="1"
                    :label-width="112"
                    :bordered="false"
                    content-padding="0"
                >
                    <template v-for="(item, idx) in renderList">
                        <abc-descriptions-item :key="`dialog-trace-code-standard-match-${idx}`" :label="item.label">
                            <abc-text v-if="!item.custom">
                                {{ item.value }}
                            </abc-text>
                            <abc-text v-else>
                                <abc-text v-for="(code, index) in item.value" :key="index">
                                    <abc-text :theme="isWarnCode(code) ? 'warning-light' : ''">
                                        {{ code }}
                                    </abc-text>
                                    <template v-if="index !== item.value.length - 1">
                                        、
                                    </template>
                                </abc-text>
                            </abc-text>
                        </abc-descriptions-item>
                    </template>
                </abc-descriptions>
            </abc-flex>
            <abc-divider variant="dashed"></abc-divider>
            <abc-text theme="gray" size="mini">
                注：数据来源于市医保三码库，如有问题请联系当地医保部门
            </abc-text>
        </abc-flex>
    </abc-modal>
</template>

<script>
    export default {
        name: 'DialogTraceCodeStandardMatch',
        props: {
            sanMaHeYiIdentificationCodeList: {
                type: Array,
                default: () => ([]),
            },
            shebao: {
                type: Object,
                default: () => ({}),
            },
            medicineCadn: {
                type: String,
                default: '',
            },
            barCode: {
                type: String,
                default: '',
            },
            codeList: {
                type: Array,
                default: () => ([]),
            },
            onConfirm: {
                type: Function,
                default: () => {},
            },
            onCancel: {
                type: Function,
                default: () => {},
            },
        },
        data() {
            return {
                visible: false,
            };
        },
        computed: {
            renderList() {
                return [
                    {
                        label: '医保编码：',
                        value: this.shebao.nationalCode,
                    },
                    {
                        label: '药品名称：',
                        value: this.medicineCadn ?? '',
                    },
                    {
                        label: '条码：',
                        value: this.barCode ?? '',
                    },
                    {
                        label: '三码库标识码：',
                        value: this.sanMaHeYiIdentificationCodeList.filter((x) => !!x).join('、'),
                    },
                    {
                        label: '当前绑定标识码：',
                        value: this.codeList,
                        custom: true,
                    },
                ];
            },
        },
        methods: {
            isWarnCode(code) {
                return !this.sanMaHeYiIdentificationCodeList.includes(code);
            },
        },
    };
</script>
