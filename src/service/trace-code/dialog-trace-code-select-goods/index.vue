<template>
    <abc-modal
        v-if="showModal"
        v-model="showModal"
        class="abc-trace-code-select-goods-dialog"
        data-cy="abc-trace-code-select-goods-dialog"
        append-to-body
        size="large"
        disabled-keyboard
        :need-high-level="false"
        :show-footer="false"
        :on-close="close"
        @open="open"
    >
        <abc-layout class="dialog-content" style="max-width: 592px;">
            <abc-section style="text-align: center;">
                <abc-text bold size="large">
                    {{ title }}
                </abc-text>
            </abc-section>
            <abc-section style="margin-top: 24px;">
                <abc-card background="gray" radius-size="small" style="padding: 16px 0 0; overflow: hidden;">
                    <abc-flex
                        justify="center"
                        class="ellipsis"
                        :gap="8"
                        style="width: 100%;"
                    >
                        <abc-text
                            theme="black"
                            bold
                            :size="viewCode.no.length > 50 ? 'large' : 'xlarge'"
                            data-cy="trace-code-start"
                        >
                            {{ viewCode.start }}
                        </abc-text>
                        <abc-text
                            theme="gray"
                            bold
                            :size="viewCode.no.length > 50 ? 'large' : 'xlarge'"
                            data-cy="trace-code-end"
                        >
                            {{ viewCode.end }}
                        </abc-text>
                    </abc-flex>
                    <abc-flex justify="center" style="width: 100%; padding-bottom: 16px; margin-top: 4px;">
                        <!--<abc-tips size="medium">-->
                        <!--    当前扫描的追溯码产品标识码未绑定库存档案-->
                        <!--</abc-tips>-->
                    </abc-flex>
                    <abc-divider :margin="'none'"></abc-divider>
                    <abc-flex vertical :gap="8" style="width: 100%; min-height: 260px; padding: 16px 16px 56px 16px; background-color: var(--abc-color-div-white);">
                        <abc-flex vertical :gap="4">
                            <abc-text theme="gray" size="normal">
                                {{ desc }}
                            </abc-text>
                            <goods-auto-complete-cover-title
                                ref="goodsAutoCompleteRef"
                                :placeholder="placeholder"
                                focus-placeholder="商品名称 / 首字母"
                                :search.sync="searchKey"
                                :enable-barcode-detector="false"
                                need-filter-disable
                                :clinic-id="clinicId"
                                :inorder-config="0"
                                format-count-key="stock"
                                focus-show
                                show-empty
                                style="width: 100%;"
                                :resident-sugguestions="!!searchKey"
                                :clear-search-key="false"
                                :enable-local-search="false"
                                is-close-validate
                                clearable
                                :disabled="!!goodsInfo"
                                show-identification-code
                                :next-input-auto-focus="false"
                                @selectGoods="selectGoods"
                                @clear="handleClear"
                                @input="handleClear"
                            >
                                <abc-search-icon slot="prepend"></abc-search-icon>

                                <div
                                    v-if="showAddArchiveDropdown"
                                    slot="fixed-footer"
                                    class="inventory__fixed-footer-wrapper"
                                    @click.stop=""
                                >
                                    <add-archive-dropdown
                                        :goods-info="{
                                            traceableCodeNoInfo
                                        }"
                                        :success-callback="selectGoods"
                                    ></add-archive-dropdown>
                                </div>
                            </goods-auto-complete-cover-title>
                        </abc-flex>
                        <abc-descriptions
                            v-if="currentGoods"
                            size="small"
                            :column="2"
                            :label-width="28"
                            border-style="dashed"
                        >
                            <abc-descriptions-item label="规格">
                                {{ currentGoods.displaySpec || "" }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="厂家" :label-width="70">
                                {{ currentGoods.manufacturer || currentGoods.manufacturerFull || '' }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="库存">
                                {{ currentStock }}
                            </abc-descriptions-item>
                            <abc-descriptions-item label="产品标识码" content-class-name="ellipsis" :label-width="70">
                                <multiple-code-popover
                                    v-if="goodsIdentificationCodeList.length"
                                    :goods-identification-code-list="goodsIdentificationCodeList"
                                ></multiple-code-popover>
                                <abc-text v-else :theme="identificationCode.theme">
                                    {{ identificationCode.text }}
                                </abc-text>
                            </abc-descriptions-item>
                        </abc-descriptions>

                        <abc-tips-card-v2 v-if="currentGoods" data-cy="trace-code-tips-card-2" :theme="tips.theme">
                            {{
                                tips.text
                            }}
                        </abc-tips-card-v2>
                    </abc-flex>
                </abc-card>
            </abc-section>
            <abc-section style="margin-top: 24px;">
                <abc-flex justify="center" style="width: 100%;">
                    <abc-space>
                        <abc-button
                            size="large"
                            :loading="btnLoading"
                            :disabled="!currentGoods || !isSupportTraceCode"
                            style="min-width: 116px;"
                            data-cy="abc-button-confirm"
                            @click="confirm"
                        >
                            <!--{{ isBound ? '仍要采集' : '确定' }}-->
                            确定
                        </abc-button>
                        <abc-button
                            size="large"
                            variant="ghost"
                            style="min-width: 116px;"
                            data-cy="abc-button-cancel"
                            @click="close"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </abc-section>
        </abc-layout>
    </abc-modal>
</template>

<script type="text/babel">
    import GoodsAutoCompleteCoverTitle from 'views/inventory/common/goods-auto-complete-cover-title.vue';
    import { mapGetters } from 'vuex';
    import { showAssignGoodsCount } from 'views/inventory/goods-utils';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import TraceCode, { TraceableCodeOpTypeEnum } from '@/service/trace-code/service';
    import { useDialogStackManager } from 'views/inventory/hooks/useDialogStackManager';
    import MultipleCodePopover from '@/service/trace-code/components/multiple-code-popover.vue';
    // import TableCellIdentificationCode from '@/service/trace-code/components/table-cell-identification-code.vue';
    export default {
        name: 'TraceCodeSelectGoodsDialog',
        components: {
            MultipleCodePopover,
            GoodsAutoCompleteCoverTitle,
            // TableCellIdentificationCode,
            AddArchiveDropdown: () => import('views/inventory/goods/components/add-archive-dropdown.vue'),
        },
        props: {
            value: Boolean,
            // 和后端换码接口返回值一致
            keywordTraceableCodeNoInfo: {
                type: Object,
                required: true,
            },
            title: {
                type: String,
                default: '请确认追溯码关联的商品',
            },
            desc: {
                type: String,
                default: '追溯码关联的商品',
            },
            placeholder: {
                type: String,
                default: '搜索该追溯码关联的商品',
            },
            goodsInfo: Object,
            onConfirm: Function,
            onClose: Function,
            checkTraceCodeNoListFn: {
                type: Function,
                default: null,
            },
        },
        setup(props) {
            const {
                disabledKeyboard,
                pushDialogName,
                popDialogName,
            } = useDialogStackManager(props.title);

            return {
                disabledKeyboard,
                pushDialogName,
                popDialogName,
            };
        },
        data() {
            return {
                showModal: this.value,
                searchKey: '',
                btnLoading: false,
                currentGoods: null,
            };
        },
        computed: {
            ...mapGetters(['currentClinic', 'isAdmin', 'isCanCreateGoodsArchivesInInventory']),
            ...mapGetters('viewDistribute', [
                'viewDistributeConfig',
            ]),
            clinicId() {
                return this.currentClinic.clinicId;
            },
            currentStock() {
                if (!this.currentGoods) return '';
                return showAssignGoodsCount(this.currentGoods, 'stock');
            },
            goodsIdentificationCodeList() {
                if (!this.isSupportTraceCode || TraceCode.isNoTraceCodeGoods(this.currentGoods)) {
                    return [];
                }
                return TraceCode.getDrugIdentificationCodeList(this.currentGoods);
            },
            operateGoodsArchives() {
                return this.viewDistributeConfig.Inventory.operateGoodsArchives;
            },
            showAddArchiveDropdown() {
                // 使用了操作档案权限控制
                if (this.operateGoodsArchives) {
                    return true;
                }
                return this.isAdmin;
            },
            identificationCode() {
                let text = '未关联';
                let theme = 'black';

                if (this.isSupportTraceCode) {
                    // if (this.isBound) {
                    //     text = TraceCode.getDrugIdentificationCodeList(this.currentGoods).join('、');
                    //     theme = 'warning-light';
                    // }

                    if (TraceCode.isNoTraceCodeGoods(this.currentGoods)) {
                        text = '无追溯码';
                    }

                } else {
                    text = '无（非中西成药/耗材）';
                    theme = 'warning-light';
                }

                return {
                    text,
                    theme,
                };
            },
            // 是否支持追溯码
            isSupportTraceCode() {
                return TraceCode.isSupportTraceCode(this.currentGoods?.typeId);
            },
            // 商品已绑定和当前识别码不一致(无码商品不算绑定了标识码)
            // isBound() {
            //     if (this.currentGoods?.traceableCodeNoInfoList && !TraceCode.isNoTraceCodeGoods(this.currentGoods)) {
            //         return TraceCode.getDrugIdentificationCode(this.currentGoods) !== this.keywordTraceableCodeNoInfo?.traceableCodeNoInfo?.drugIdentificationCode;
            //     }
            //     return false;
            // },
            tips() {
                let text = '';
                let theme = '';
                if (this.isSupportTraceCode) {
                    // if (this.isBound) {
                    //     text = '该商品已绑定了产品标识码，与本次扫码不一致，请确定是否选错商品';
                    //     theme = 'warning';
                    // } else {
                    //     text = '系统将自动关联产品标识码，采集追溯码时自动识别商品';
                    //     theme = 'primary';
                    // }
                    text = '系统将自动关联产品标识码，采集追溯码时自动识别商品';
                    theme = 'primary';
                } else {
                    text = '该商品不是中西成药或耗材，无需采集追溯码，请确定是否选错商品';
                    theme = 'warning';
                }

                return {
                    text,
                    theme,
                };
            },

            viewCode() {
                return TraceCode.formatTraceableCode(this.keywordTraceableCodeNoInfo?.traceableCodeNoInfo);
            },

            traceableCodeNoInfo() {
                return {
                    drugIdentificationCode: this.keywordTraceableCodeNoInfo?.traceableCodeNoInfo?.drugIdentificationCode,
                };
            },
        },
        watch: {
            showModal(val) {
                this.$emit('input', val);
                if (!val) {
                    this.onClose && this.onClose();
                }
            },
        },
        created() {
            if (this.goodsInfo) {
                this.searchKey = this.goodsInfo.displayName || '';
                this.currentGoods = {
                    ...this.goodsInfo,
                };
            }

        },
        methods: {
            selectGoods(goods) {
                console.log(goods);
                this.searchKey = goods.displayName || '';
                this.currentGoods = goods;
            },
            async confirm() {
                try {
                    if (typeof this.checkTraceCodeNoListFn === 'function') {
                        const isValidate = await this.checkTraceCodeNoListFn();
                        if (!isValidate) {
                            this.close();
                            return;
                        }
                    }

                    // 未绑定才可以绑定
                    // if (!this.isBound) {
                    this.btnLoading = true;
                    const goodsId = this.currentGoods.id || this.currentGoods.goodsId;
                    await GoodsAPIV3.boundIdentificationCode(goodsId, {
                        no: this.traceableCodeNoInfo.drugIdentificationCode,
                        opType: TraceableCodeOpTypeEnum.BIND,
                    });
                    // }

                    this.onConfirm && this.onConfirm(this.currentGoods, this.keywordTraceableCodeNoInfo);
                    this.close();
                } catch (e) {
                    console.error(e);
                    // 防止重复提示
                    if (!e.alerted) {
                        this.$Toast({
                            message: e.message || '系统异常，绑定失败',
                            type: 'error',
                        });
                    }
                } finally {
                    this.btnLoading = false;
                }
            },
            open() {
                this.pushDialogName();
                this.$nextTick(() => {
                    this.$refs.goodsAutoCompleteRef.$refs.autoComplete.focus();
                });
            },
            close() {
                this.showModal = false;
                this.popDialogName();
            },
            handleClear() {
                this.currentGoods = null;
            },
        },
    };
</script>

