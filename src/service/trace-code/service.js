import GoodsV3API from '@/api/goods/index-v3';
import {
    GoodsSubTypeEnum, GoodsTypeEnum, GoodsTypeIdEnum,
} from '@abc/constants';
import {
    createGUID, getSafeNumber, isNotNull, isNull,
} from '@/utils';
import { formatDate } from '@abc/utils-date';
import { dispenseItemStatusEnum } from 'views/pharmacy/constants';
import Big from 'big.js';
import { isChineseMedicine } from '@/filters';
import { DISABLED } from 'views/inventory/components/social-code-autocomplete/constant';

/** 药品档案上面的追溯码信息
 * @typedef {Object} TraceableCodeInfo
 * @property {string} no 追溯码编号
 * @property {string} drugIdentificationCode 药品识别码
 * @property {number} traceableCodeType 追溯码类型 1-药品 10-耗材GS1 11-耗材MA
 * @property {number} type 类型 0-HAS_CODE 10-NO_CODE
 */

/**
 * @typedef {Object} TraceableCodeListItem
 * @property {string} id 追溯码列表项ID
 * @property {string} no 追溯码编号
 * @property {number} used 是否已使用
 * @property {string} batchId 批次ID
 * @property {string} batchNo 批次编号
 * @property {string} goodsId 商品ID
 * @property {string} stockInOrderId 库存入库编号
 * @property {string} stockInTime 库存入库时间
 */

/** 后端换码接口返回数据结构
 * @typedef {Object} DrugTraceInfo
 * @property {string} no 追溯码编号
 * @property {number} count 追溯码数量(相同码重复的次数)
 * @property {TraceableCodeInfo} traceableCodeNoInfo 追溯码信息
 * @property {GoodsInfo} goods 商品信息
 * @property {Array<TraceableCodeListItem>} traceableCodeList 追溯码使用情况
 */

// 追溯码类型
export const TraceableCodeTypeEnum = Object.freeze({
    HAS_CODE: 0,
    NO_CODE: 10,
});

// 标识码操作类型
export const TraceableCodeOpTypeEnum = Object.freeze({
    BIND: 0,
    UN_BUNDLING: 10,
});

// 拆零标记
export const TraceableCodeDismountingFlagEnum = Object.freeze({
    // 不拆零
    NO_DISMOUNTING: 0,
    // 系统强制拆零-医保地区强制拆零
    SYSTEM_FORCE_DISMOUNTING: 0x01,
    // 库存批次不够导致拆零
    SYSTEM_BATCH_DISMOUNTING: 0x02,
    // 系统计算拆零-发药批次拆分导致拆零
    SYSTEM_CALCULATE_DISMOUNTING: 0x04,
});

// 医保拆零标记
export const ShebaoTraceableCodeDismountingFlagEnum = Object.freeze({
    // 不拆零不允许编辑
    NO_DISMOUNTING_NO_EDIT: 0,
    // 拆零不允许编辑
    DISMOUNTING_NO_EDIT: 0b01,
    // 不拆零允许编辑
    NO_DISMOUNTING_EDIT: 0b10,
    // 拆零允许编辑
    DISMOUNTING_EDIT: 0b11,
});

// 追溯码使用场景
export const SceneTypeEnum = Object.freeze({
    NONE: 'NONE',// 放开各处校验
    GOODS_IN: 'goodsIn',// 采购入库
    GOODS_OUT: 'goodsOut',// 退货出库
    GOODS_CHECK: 'goodsCheck',// 盘点
    GOODS_TAKE: 'goodsTake',// 药店收货
    CHARGE: 'charge',// 收费
    PHARMACY_CHARGE: 'pharmacy_charge',// 药店收费
    PHARMACY: 'pharmacy',// 药房
    SELL: 'sell',// 收费，药房等销售场景
    SOCIAL: 'social',// 社保
});

export const TraceableCodeListErrorType = Object.freeze({
    NORMAL: -1,
    // 未采集
    NOT_COLLECTED: 0,
    // 数量不符
    INCONFORMITY_COUNT: 1,
    // 非本商品-汇总
    INCONFORMITY_GOODS: 2,
    // 批次不符合-汇总
    INCONFORMITY_BATCH: 3,
    // 重复-汇总
    IS_DUPLICATION: 4,
    // 追溯码使用超上限
    CODE_EXCEED_LIMIT: 5,
    //追溯码采集总量必须等于发药数量
    CODE_NOT_EQUAL: 6,
});

export const TraceableCodeListItemErrorType = Object.freeze({
    NORMAL: -1,
    // 非本商品
    INCONFORMITY_GOODS: 20,
    // 批次不符合
    INCONFORMITY_BATCH: 30,
    // 重复
    IS_DUPLICATION: 40,
    // 重复出库
    DUPLICATION_OUT: 50,
    // code 重复
    CODE_REPEAT: 60,
});

export const TraceCodeScenesEnum = Object.freeze({
    PHARMACY: 10,
    INVENTORY: 20,
});

export const ActionTextEnum = Object.freeze({
    100: '入库',
    200: '出库',
    300: '调入',
    400: '调出',
    500: '盘盈',
    600: '盘亏',
    700: '领入',
    800: '领出',
    900: '发药',
    1000: '退药',
});


// 无码申请状态
export const TraceableNoCodeApplyStatus = Object.freeze({
    APPLYING: 0, // 申报中
    SUCCESS: 1, // 已申报
    FAIL: 2, // 无效
});

//追溯码码上放心级别 null/0未知 1小 2中 3大
export const AliPackageLevelEnum = Object.freeze({
    SMALL: 1,
    MEDIUM: 2,
    BIG: 3,
});
export const AliPackageLevelLabel = Object.freeze({
    [AliPackageLevelEnum.SMALL]: '小码',
    [AliPackageLevelEnum.MEDIUM]: '中码',
    [AliPackageLevelEnum.BIG]: '大码',
});

export default class TraceCode {
    // 支持采集追溯码的商品类型
    static usableTraceCodeGoodsTypeIdList = [
        GoodsTypeIdEnum.MEDICINE_WESTERN,
        GoodsTypeIdEnum.MEDICINE_CHINESE_PATENT,
        GoodsTypeIdEnum.MATERIAL_MEDICINE_MATERIAL,
        GoodsTypeIdEnum.MATERIAL_DISINFECTANT,
    ];

    // 明确采集数量的场景值
    static traceCodeForceCheckScenes = [TraceCodeScenesEnum.PHARMACY, TraceCodeScenesEnum.INVENTORY];// 10:发药 20:库存

    // 采集追溯码是否强校验数量-现在所有地区都开启
    static isStrictCount = false;

    // 是否入驻码上放心平台
    static hasCodeSafeOpened = false;

    // 自动使用拆零追溯码
    static hasAutoUseDecomposeTraceCode = false;

    // 是否启用拆零不采模式
    static hasEnableDismountingMode = false;

    // 是否启用采集强控模式
    static hasEnableCollCheckStrictMode = false;

    // 校验追溯码长度的配置，受config影响
    static validateLengthRuleList = [
        {
            minLength: 12,
            maxLength: 120,
        },
    ];

    // 是否强校验实采必须等于应采
    static requireCollectTraceCode = 0;

    // 是否开启中码、大码拦截
    static hasInterceptAliPackageLevel() {
        return !!(window.$abcSocialSecurity.config?.isShandongJinan || window.$abcSocialSecurity.config?.isShandongQingdao);
    }

    // 是否开启 医保要求药品追溯码必须为20位或30位，且不应存在汉字或其他符号 拦截
    static hasInterceptTraceCodeFormatLength() {
        return window.$abcSocialSecurity.config?.isShandongQingdao;
    }

    static setTraceCodeFormatRuleCheckList(list) {
        this.validateLengthRuleList = list || [
            {
                minLength: 12,
                maxLength: 120,
            },
        ];
    }

    /**
     * @desc 判断是否是追溯码
     * @desc 与地区规则有关
     * <AUTHOR> Yang
     * @date 2024-10-14 17:01:00
     * @param val
     * @param isValidateFormatLength
     * @return {boolean}
     */
    static isTraceableCode(val,isValidateFormatLength = true) {
        if (!val) {
            return false;
        }

        // 追溯码目前 已知的是数字、字母、.
        if (!/^[a-zA-Z0-9]+(?:\.[a-zA-Z0-9]+)*$/.test(val)) {
            return false;
        }

        const { length } = val;

        const is69Barcode = length === 13 && /^69/.test(val);
        if (is69Barcode) return false;

        const {
            minLength,
            maxLength,
        } = this.validateLengthRuleList[0] || {
            minLength: 12,
            maxLength: 120,
        };
        if (TraceCode.hasInterceptTraceCodeFormatLength() && isValidateFormatLength) {
            return length === 20 || length === 30;
        }
        return !(minLength && length < minLength || maxLength && length > maxLength);

    }

    static async findCodeInfoInLocal(items, traceCode) {
        let res = null;
        items.forEach((item) => {
            const {
                traceableCodeNoInfoList,
            } = item.productInfo || {};
            const codeInfo = traceableCodeNoInfoList?.find((info) => {
                const {
                    drugIdentificationCode,
                } = info;
                return traceCode.indexOf(drugIdentificationCode) === 0 ||
                    traceCode.indexOf(`01${drugIdentificationCode}`) === 0;
            });
            if (codeInfo) {
                res = {
                    traceCode,
                    no: traceCode,
                    goodsInfo: item.productInfo,
                    traceableCodeNoInfo: {
                        ...codeInfo,
                        no: traceCode,// 兼容后端外层与里层no不一致的情况
                    },
                };
            }
        });
        return res;
    }

    static async fetchByCode(traceCode, refGoodsId) {
        try {
            const { data } = await GoodsV3API.fetchTraceCodeInfo({
                queryTraceCodeList: [{
                    no: traceCode,
                    refGoodsId,
                }],
            });

            const {
                goods,// 绑定商品信息
                no,// 追溯码
                traceableCodeNoInfo,// 追溯码解码信息
                traceableCodeList,// 追溯码使用详情
            } = data.list?.[0] ?? {};
            return {
                traceCode,
                no,
                traceableCodeList,
                goodsInfo: goods,
                traceableCodeNoInfo: {
                    ...traceableCodeNoInfo,
                    no,// 兼容后端外层与里层no不一致的情况
                },
            };
        } catch (error) {
            console.log(error);
            return {
                traceCode,
            };
        }

    }

    /**
     * @desc 在列表中找到可以填充追溯码的item
     * <AUTHOR> Yang
     * @date 2024-08-22 16:17:17
     * @param {Object} options
     * @param {Array} options.dataList 列表，类chargeFormItem
     * @param {object} options.traceableCodeNoInfo 扫码解码后数据
     * @param {Object} options.goodsInfo 标准goods结构
     * @param {Function} options.getUnitInfo 获取unit unitCount用于计算maxTraceCodeCount
     */
    static findCanAddTraceCodeItem(options) {
        const {
            traceableCodeNoInfo,
            dataList,
            goodsInfo,
            getUnitInfo,
        } = options;

        const curList = dataList.filter((item) => {
            const {
                traceableCodeList,
            } = item;
            const flag = traceableCodeList?.some((it) => {
                const {
                    traceableCodeNoInfo: codeInfo,
                } = it;
                return codeInfo?.drugIdentificationCode === traceableCodeNoInfo?.drugIdentificationCode;
            });
            return (item.productId || item.goodsId) === goodsInfo?.id || flag;
        });

        const res = curList.find((item) => {
            const {
                unitCount,
                unit,
                productInfo,
                _maxTraceCodeCount,
            } = getUnitInfo ? getUnitInfo(item) : item;

            let maxTraceCodeCount = _maxTraceCodeCount;
            if (!maxTraceCodeCount) {
                maxTraceCodeCount = unitCount;
                const {
                    pieceNum,
                    pieceUnit,
                } = goodsInfo || productInfo || {};
                if (unit === pieceUnit) {
                    maxTraceCodeCount = Math.ceil(unitCount / (pieceNum || 1));
                }
            }

            const collectedCount = item.traceableCodeList?.reduce((acc, cur) => acc + (+cur.count || 1), 0);
            return collectedCount < maxTraceCodeCount;
        });

        return res || curList[curList.length - 1];
    }

    static findCanAddTraceCodeItemByCollection(options) {
        const {
            traceableCodeNoInfo,
            dataList,
            goodsInfo,
            getUnitInfo,
        } = options;

        const curList = dataList.filter((item) => {
            const {
                traceableCodeList,
            } = item;
            const flag = traceableCodeList?.some((it) => {
                const {
                    traceableCodeNoInfo: codeInfo,
                } = it;
                return codeInfo?.drugIdentificationCode === traceableCodeNoInfo?.drugIdentificationCode;
            });
            return (item.productId || item.goodsId) === goodsInfo?.id || flag;
        });

        const res = curList.find((item) => {
            const {
                unit,
                productInfo,
            } = getUnitInfo ? getUnitInfo(item) : item;
            const {
                packageUnit,pieceNum,
            } = productInfo;
            const dispensingCount = TraceCode.hasEnableDismountingMode ? Big(getSafeNumber(TraceCode.getTraceCodeShouldCollectCountInfo(item)?.bigShouldCollectCount)) : TraceCode.getUnitCount(item);
            const {
                collectBigCount,collectSmallCount,
            } = TraceCode.getTraceCodeCollectCountInfo(item);
            const safeCollectBigCount = Big(getSafeNumber(collectBigCount));
            const safeCollectSmallCount = Big(getSafeNumber(collectSmallCount));
            const safePieceNum = Big(getSafeNumber(pieceNum || 1));
            const isBigUnit = TraceCode.hasEnableDismountingMode ? true : packageUnit === unit;

            if (isBigUnit) {
                const count = safeCollectBigCount.plus(safeCollectSmallCount.div(safePieceNum));
                return dispensingCount.gt(count);
            }
            return dispensingCount.gt(safeCollectSmallCount);


        });

        return res || curList[curList.length - 1];
    }

    /**
     * @desc 提交追溯码数据
     * <AUTHOR>
     * @date 2024/8/22 下午3:14
     */
    static transCodeList(list) {
        return (list || []).map((item) => {
            // 有id的数据，原样返回给后端
            if (item.id) {
                return item;
            }
            // 自己新增的数据
            return {
                ...item,
                no: item.no,
                traceableCodeNoInfo: item.traceableCodeNoInfo,
            };
        }).filter((item) => isNotNull(item.no));
    }

    static getFormItemBatchInfos(item) {
        return item.chargeFormItemBatchInfos || item.dispensingFormItemBatches;
    }

    /**
     * @desc 验证 追溯码
     * @desc 【注意】本方法的返回顺序对应提示优先级
     * <AUTHOR> Yang
     * @date 2024-08-26 19:28:20
     * @param {Object} options
     * @param {Object} options.dataItem 收费 发药 formItem结构
     * @param {Object} options.traceCodeObj traceableCodeList 子项
     * @param {Object} options.goodsInfo 标准goods结构
     * @param {String} options.sceneType 使用场景
     */
    static validateTraceCode(options) {
        const {
            dataItem,
            traceCodeObj,
            sceneType,
        } = options;
        const key = 'traceableCodeNoInfo';
        const {
            no,
            type: traceCodeType,
        } = (traceCodeObj[key] ? traceCodeObj[key] : traceCodeObj) || {};

        // 无码 不做校验
        if (traceCodeType === TraceableCodeTypeEnum.NO_CODE) {
            return {
                flag: true,
            };
        }

        // const {
        //     drugIdentificationCode: goodsCode,
        // } = goodsInfo.traceableCodeNoInfoList?.[0] || {};
        //
        // if (goodsCode && goodsCode !== code) {
        //     return {
        //         flag: false,
        //         warnText: '非本商品',
        //         warnTips: '采集的追溯码非本商品',
        //         hoverTips: '采集追溯码与该商品绑定的产品标识码不同，请确认是否采集错误',
        //         warnType: TraceableCodeListErrorType.INCONFORMITY_GOODS,
        //     };
        // }

        const traceableCodeList = traceCodeObj.traceableCodeList || [];

        // if (sceneType === SceneTypeEnum.GOODS_IN && traceableCodeList.find((item) => item.no === traceCodeObj.no)?.stockInOrderId) {
        //     return {
        //         flag: false,
        //         warnText: '重复入库',
        //         warnTips: '',
        //         hoverTips: '',
        //         warnType: TraceableCodeListErrorType.IS_DUPLICATION,
        //     };
        // }

        if (sceneType === SceneTypeEnum.NONE && traceableCodeList.some((item) => item.used > 0)) {
            const res = traceableCodeList.find((item) => item.used > 0);
            const isDismounting = this.itemIsDismounting(dataItem);
            // 拆零销售不校验追溯码
            if (res && !isDismounting) {
                const {
                    action,
                    stockOutByName,
                    stockOutTime,
                } = res;
                let str = '该追溯码';
                if (stockOutTime) {
                    str += `在${formatDate(stockOutTime, 'YYYY-MM-DD HH:mm')}，`;
                }
                str += '已';
                if (stockOutByName) {
                    str += `由${stockOutByName}`;
                }
                const hoverTips = `${str}${ActionTextEnum[action] || '使用'}。请确认是否采集错误，采集上传会有判定为“重复销售”的风险`;
                return {
                    flag: false,
                    warnText: '重复出库',
                    warnTips: '存在追溯码重复出库采集的风险',
                    hoverTips,
                    warnType: TraceableCodeListItemErrorType.DUPLICATION_OUT,
                };
            }
        }
        if (sceneType === SceneTypeEnum.NONE) {
            // const formItemBatchInfos = this.getFormItemBatchInfos(dataItem);
            // if (formItemBatchInfos) {
            //     const res = formItemBatchInfos.some((batch) => {
            //         const {
            //             batchId,
            //         } = batch;
            //         return traceableCodeList.some((it) => !it.used && `${it.batchId}` === `${batchId}`);
            //     });
            //     if (!res) {
            //         return {
            //             flag: false,
            //             warnText: '非发药批次',
            //             warnTips: '非发药批次',
            //             hoverTips: '开单时已锁定发药批次计算费用，该追溯码不属于发药批次。',
            //             warnType: TraceableCodeListItemErrorType.INCONFORMITY_BATCH,
            //         };
            //     }
            // }
            let repeatCount = 0;
            dataItem?.traceableCodeList?.forEach((it) => {
                if (it.no === no) {
                    repeatCount++;
                }
            });
            if (repeatCount > 1) {
                return {
                    flag: false,
                    warnText: '追溯码重复',
                    warnTips: '追溯码重复',
                    hoverTips: '一单中相同追溯码仅需采集一次',
                    warnType: TraceableCodeListItemErrorType.CODE_REPEAT,
                };
            }
        }
        return {
            flag: true,
        };
    }

    /**
     * @desc 验证item
     * <AUTHOR> Yang
     * @date 2024-08-26 19:28:29
     * @param {Object} options
     * @param {Object} options.dataItem 列表子项，类chargeFormItem
     * @param {Function} options.getUnitInfo 获取unit unitCount用于计算maxTraceCodeCount
     */
    static validateDataItem(options) {
        const {
            dataItem,
            getUnitInfo,
            productInfo,
            sceneType,
            traceableCodeListKey = 'traceableCodeList',
        } = options;
        if (!this.supportCollect(productInfo)) {
            return {
                flag: true,
            };
        }

        const {
            keyId,shebaoDismountingFlag,
        } = dataItem || {};

        const traceableCodeList = dataItem[traceableCodeListKey];

        const {
            typeId,
        } = dataItem?.productInfo || productInfo || {};

        const {
            unitCount,
        } = getUnitInfo ? getUnitInfo(dataItem) : dataItem;

        // 需要采集追溯码并且列表为空
        if (TraceCode.isSupportTraceCode(typeId) && !traceableCodeList?.length && !TraceCode.isShebaoDismountingFlag(shebaoDismountingFlag)) {
            if (sceneType === SceneTypeEnum.GOODS_CHECK && unitCount === 0) {
                // 特殊判断，盘点无盈亏
            } else {
                return {
                    keyId,
                    flag: false,
                    warnText: '未采集',
                    warnTips: '未采集追溯码',
                    hoverTips: '未采集追溯码',
                    warnType: TraceableCodeListErrorType.NOT_COLLECTED,
                };
            }
        }
        if ((sceneType === SceneTypeEnum.CHARGE || sceneType === SceneTypeEnum.PHARMACY || sceneType === SceneTypeEnum.PHARMACY_CHARGE) && !TraceCode.isNoTraceCodeGoods(productInfo) && !isChineseMedicine(productInfo) && traceableCodeList?.length) {
            if (TraceCode.validateTraceableCodeCount(dataItem).validateFlag) {
                return {
                    keyId,
                    flag: false,
                    warnTips: '追溯码采集总量必须等于发药数量',
                    warnType: TraceableCodeListErrorType.CODE_NOT_EQUAL,
                };
            }
        }
        if (sceneType === SceneTypeEnum.SELL || sceneType === SceneTypeEnum.PHARMACY_CHARGE) {
            if (TraceCode.hasCodeSafeOpened && !TraceCode.isCompatibleHistoryData(dataItem,true)) {
                if (traceableCodeList.some((e) => TraceCode.getTraceCollectCodeCountBySmall(productInfo,e) > TraceCode.getTraceCodeLeftCount(e))) {
                    return {
                        keyId,
                        flag: false,
                        warnTips: '追溯码使用超上限',
                        warnType: TraceableCodeListErrorType.CODE_EXCEED_LIMIT,
                    };
                }
            }
        }

        const isNeimengguFixedPointMechanism = window.$abcSocialSecurity.config?.isNeimenggu && window.$abcSocialSecurity.isOpenSocial;
        const isValidateCount = sceneType === SceneTypeEnum.GOODS_IN || sceneType === SceneTypeEnum.GOODS_TAKE;
        // 内蒙古特殊数量校验不能大于1000
        if (isValidateCount && isNeimengguFixedPointMechanism) {
            const collectedCount = traceableCodeList?.reduce((acc, cur) => acc + (+cur.count || 1), 0);
            if (collectedCount > 1000) {
                return {
                    keyId,
                    flag: false,
                    isError: true,// 有次标记会阻断流程
                    errorTips: '医保要求单次入库追溯码不可超过1000条，请拆分入库',
                    warnTips: '医保要求单次入库追溯码不可超过1000条，请拆分入库',
                    warnType: TraceableCodeListErrorType.INCONFORMITY_COUNT,
                };
            }
        }

        return {
            flag: true,
        };
    }

    /**
     * @desc 是否需要采集数量提示
     * <AUTHOR> Yang
     * @date 2025-02-12 14:01:55
     */
    static needCountWarn(dataItem, options) {
        if (!TraceCode.isStrictCount) return false;
        if (!dataItem) return false;
        const {
            _maxTraceCodeCount,
            productInfo,
        } = dataItem || {};
        if (!TraceCode.isSpecificationMatch(productInfo)) return false;
        if (!_maxTraceCodeCount) return false;

        const {
            traceableCodeListKey = 'traceableCodeList',
        } = options || {};
        const traceableCodeList = dataItem[traceableCodeListKey];
        const collectedCount = TraceCode.getTraceCodeCollectCount({
            ...dataItem,
            traceableCodeList,
        });

        return collectedCount !== _maxTraceCodeCount;
    }

    /**
     * @desc 标准 formItem
     * <AUTHOR> Yang
     * @date 2024-09-10 13:57:31
     * @param {Object} formItem 列表子项，类chargeFormItem
     */
    static itemIsDismounting(formItem) {
        const {
            pieceNum,
            packageUnit,
        } = formItem?.productInfo || {};

        const {
            unitCount,
            unit,
        } = formItem || {};
        return !(unit === packageUnit && pieceNum !== 1 && unitCount % 1 === 0);
    }

    /**
     * @desc 整体提交时异常提醒
     * <AUTHOR>
     * @date 2024/8/22 下午3:14
     * @param {Object} options
     * @param {Array} options.dataList 列表，类chargeFormItem
     * @param {Function} options.getGoodsInfo 标准goods结构
     * @param {Function} options.getUnitInfo 获取unit unitCount用于计算maxTraceCodeCount
     * @return {object} {
     *  flag: true, // true 代表验证通过
     *  errorList
     * }
     */
    static async validate(options) {
        const {
            dataList,
            getGoodsInfo,
            getUnitInfo,
            createKeyId,
            sceneType,
            traceableCodeListKey,
            needGetMaxTraceCountList = true,
        } = options || {};

        let firstErrorIndex = -1;
        const errorMap = new Map();

        let maxTraceCountList = [];

        if (needGetMaxTraceCountList) {
            try {
                maxTraceCountList = await TraceCode.getMaxTraceCountList(options);
            } catch (e) {
                console.error(e);
                maxTraceCountList = [];
            }
        }

        dataList.forEach((item, index) => {
            const productInfo = (getGoodsInfo ? getGoodsInfo(item) : item?.productInfo) || {};

            const keyId = createKeyId ? createKeyId(item) : item.keyId;

            const res = maxTraceCountList.find((it) => it.keyId === keyId);
            if (res) {
                item._maxTraceCodeCount = res.traceableCodeNum ?? 0;
                item._dismounting = res.dismounting || 0;// 是否拆零
                item._isTransformable = res.isTransformable; // 否可以转换 0:否 1:是, 换算程序无法覆盖的，系统不进行校验
                item._collectCountTransFactor = res.collectCountTransFactor || 1;
                item._useLimitPriceTargetUnit = res.useLimitPriceTargetUnit;

                (Array.isArray(res.list) ? res.list : []).forEach((countCodeInfo) => {
                    const codeInfo = item.traceableCodeList.find((x) => x.no === countCodeInfo.no);
                    if (codeInfo) {
                        if (isNull(codeInfo.hisLeftTotalPieceCount)) {
                            codeInfo.hisLeftTotalPieceCount = countCodeInfo.hisLeftTotalPieceCount;
                        }
                        if (isNull(codeInfo.unit)) {
                            codeInfo.unit = (TraceCode.isShouldApplyPieceUnit(countCodeInfo) ? productInfo.pieceUnit : productInfo.packageUnit) || codeInfo.unit || item.unit || item.pieceUnit;
                        }
                        if (isNull(codeInfo.count)) {
                            codeInfo.count = (codeInfo.hisPackageCount > 0 || codeInfo.hisPieceCount > 0) ?
                                TraceCode.isShouldApplyPieceUnit(codeInfo) ? codeInfo.hisPieceCount : codeInfo.hisPackageCount :
                                codeInfo.count;
                        }
                    }
                });
            }

            const itemRes = this.validateDataItem({
                dataItem: item,
                productInfo,
                getUnitInfo,
                sceneType,
                traceableCodeListKey,
            });
            if (!itemRes.flag) {
                firstErrorIndex = firstErrorIndex === -1 ? index : firstErrorIndex;
                const res = errorMap.get(itemRes.warnType);
                if (res) {
                    res.count += 1;
                } else {
                    errorMap.set(itemRes.warnType, {
                        ...itemRes,
                        count: 1,
                    });
                }
            }


            // code错误 count 只记录一次
            [
                TraceableCodeListErrorType.INCONFORMITY_GOODS,
                TraceableCodeListItemErrorType.DUPLICATION_OUT,
                TraceableCodeListItemErrorType.INCONFORMITY_BATCH,
            ].forEach((type) => {
                const errorRes = item.traceableCodeList?.find((it) => {
                    const codeRes = this.validateTraceCode({
                        dataItem: item,
                        traceCodeObj: it,
                        goodsInfo: productInfo,
                        sceneType,
                    });
                    return !codeRes.flag && codeRes.warnType === type;
                });
                if (errorRes) {
                    const res = errorMap.get(type);
                    if (res) {
                        res.count += 1;
                    } else {
                        errorMap.set(type, {
                            ...this.validateTraceCode({
                                dataItem: item,
                                traceCodeObj: errorRes,
                                goodsInfo: productInfo,
                                sceneType,
                            }),
                            keyId: item.keyId,
                            count: 1,
                        });
                    }
                }
            });
        });

        const errorList = [...errorMap.values()];
        return {
            flag: errorList.length === 0,
            firstErrorIndex,
            errorList,
        };
    }

    /**
     * @desc 初始化渲染追溯码列表
     * <AUTHOR>
     * @date 2024/8/22 下午3:14
     */
    static initTraceableCodeList(codeList, goods, sceneType) {
        const {
            traceableCodeNoInfoList,
        } = goods || {};

        return codeList.map((item) => {
            let warningText = '';
            let theme = '';
            let status = TraceableCodeListItemErrorType.NORMAL;
            let disabledPopover = true;

            const drugIdentificationCode = item.traceableCodeNoInfo?.drugIdentificationCode || item.drugIdentificationCode;
            const drugIdentificationCodeType = item.traceableCodeNoInfo?.type;
            const traceableCodeList = item.traceableCodeList || [];

            // 无码追溯码不校验
            if (sceneType === SceneTypeEnum.NONE && drugIdentificationCodeType !== TraceableCodeTypeEnum.NO_CODE) {
                // 非本商品
                if (traceableCodeNoInfoList?.[0]?.drugIdentificationCode !== drugIdentificationCode) {
                    status = TraceableCodeListItemErrorType.INCONFORMITY_GOODS;
                    warningText = '非本商品';
                    theme = 'warning-light';
                    disabledPopover = false;
                } else if (sceneType === SceneTypeEnum.GOODS_IN && traceableCodeList.find((e) => e.no === item.no)?.stockInOrderId) {
                    status = TraceableCodeListItemErrorType.IS_DUPLICATION;
                    warningText = '重复入库';
                    theme = 'warning-light';
                    disabledPopover = true;
                } else if (item.notCurrentBatch) {
                    status = TraceableCodeListItemErrorType.INCONFORMITY_BATCH;
                    warningText = '非本批次';
                    theme = 'warning-light';
                    disabledPopover = true;
                }
            }

            return {
                ...item,
                keyId: createGUID(),
                drugIdentificationCode,
                status,
                theme,
                warningText,
                disabledPopover,
            };
        });
    }

    /**
     * @desc 追溯码展示格式化
     * <AUTHOR>
     * @date 2024/8/29 下午5:17
     */
    static formatTraceableCode({
        drugIdentificationCode, no,
    } = {}) {
        if (!no || !drugIdentificationCode) {
            if (no) {
                return {
                    start: '',
                    end: no,
                    no,
                };
            }
            return {
                start: '',
                end: drugIdentificationCode || '',
                no: '',
            };
        }
        let start = '', end = '';
        const separator = '###';

        const arr = no.replace(drugIdentificationCode, separator).split(separator);

        if (arr.length === 2) {
            start = arr[0] + drugIdentificationCode;
            end = arr[1];
        } else {
            start = '';
            end = no || '';
        }

        return {
            no,
            start,
            end,
        };

    }

    /**
     * @desc 设置支持采集的商品类型
     * <AUTHOR>
     * @date 2024/9/6 上午10:43
     */
    static setSupportTraceCodeTypeIdList(list) {
        this.usableTraceCodeGoodsTypeIdList = list || [];
    }

    /**
     * @desc 设置采集追溯码是否强校验数量开关
     * <AUTHOR>
     * @date 2024/9/26 下午2:49
     */
    static setStrictCountSwitch(flag) {
        this.isStrictCount = flag;
    }

    /**
     * @desc 设置采集追溯码是否需要明确采集数量的场景
     * <AUTHOR>
     * @date 2024/9/6 上午10:43
     */
    static setTraceCodeForceCheckScenes(list) {
        this.traceCodeForceCheckScenes = list || [];
    }
    // 是否开通“码上放心”
    static setHasCodeSafeOpened(flag) {
        this.hasCodeSafeOpened = flag;
    }

    // 是否开启拆零不采功能
    static setHasEnableDismountingMode(flag) {
        this.hasEnableDismountingMode = flag;
    }

    // 是否启用采集强控制模式
    static setHasEnableCollCheckStrictMode(flag) {
        this.hasEnableCollCheckStrictMode = flag;
    }

    static setHasAutoUseDecomposeTraceCode(flag) {
        this.hasAutoUseDecomposeTraceCode = flag;
    }

    static setRequireCollectTraceCode(requireCollectTraceCode) {
        this.requireCollectTraceCode = requireCollectTraceCode;
    }

    /**
     * @desc 判断是否明确展示追溯码采集数量
     * <AUTHOR>
     * @date 2024/8/31 下午12:19
     */
    static isSupportTraceCodeForceCheck(scene) {
        return this.traceCodeForceCheckScenes.includes(scene);
    }

    /**
     * @desc 发药是否明确展示追溯码采集数量
     * <AUTHOR>
     * @date 2024/8/31 下午12:19
     */
    static isSupportTraceCodeForceCheckPharmacy() {
        return this.isSupportTraceCodeForceCheck(TraceCodeScenesEnum.PHARMACY);
    }

    /**
     * @desc 库存是否明确展示追溯码采集数量
     * <AUTHOR>
     * @date 2024/8/31 下午12:19
     */
    static isSupportTraceCodeForceCheckStock() {
        return this.isSupportTraceCodeForceCheck(TraceCodeScenesEnum.INVENTORY);
    }

    /**
     * @desc 判断是否支持追溯码
     * <AUTHOR>
     * @date 2024/8/31 下午12:19
     */
    static isSupportTraceCode(typeId) {
        return this.usableTraceCodeGoodsTypeIdList.includes(typeId);
    }

    /**
     * @desc 判断无码商品是否补码
     * <AUTHOR>
     * @date 2024/8/31 下午12:19
     */
    static isSupplementNoCodeGoods(goods) {
        return TraceCode.isNoTraceCodeGoods(goods) && !TraceCode.isNullCodeGoods(goods) && TraceCode.isSupportTraceCode(goods.typeId);
    }

    /**
     * @desc 判断formItem是否支持追溯码
     * <AUTHOR> Yang
     * @date 2024-09-06 15:47:44
     */
    static formItemSupportTraceCode(formItem) {
        const {
            productInfo,
            productType,
            productSubType,
            composeChildren,
        } = formItem;

        if (!productInfo) {
            if (
                productType === GoodsTypeEnum.MEDICINE &&
                [
                    GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].WesternMedicine,
                    GoodsSubTypeEnum[GoodsTypeEnum.MEDICINE].CPM,
                ].indexOf(productSubType) > -1
            ) {
                return true;
            }

            return productType === GoodsTypeEnum.MATERIAL &&
                [
                    GoodsSubTypeEnum[GoodsTypeEnum.MATERIAL].MedicalMaterials,
                ].indexOf(productSubType) > -1;
        }

        const {
            typeId,
        } = productInfo || {};
        if (typeId === GoodsTypeIdEnum.COMPOSE) {
            return composeChildren?.some((it) => this.formItemSupportTraceCode(it));
        }
        return this.usableTraceCodeGoodsTypeIdList.includes(typeId);
    }

    /**
     * @desc 计算采集数量-换算成小单位
     * <AUTHOR>
     * @date 2024/8/31 下午12:19
     */
    static getCollectCount(item, goodsInfo) {
        let {
            packageCount, pieceCount, pieceNum,
        } = item;
        packageCount = +packageCount || 0;
        pieceCount = +pieceCount || 0;
        pieceNum = pieceNum || goodsInfo?.pieceNum || 1;

        return pieceCount + Math.ceil(packageCount * pieceNum);
    }

    /**
     * @desc 打开追溯码录入操作指南
     * <AUTHOR>
     * @date 2024/9/3 下午2:51
     */
    static openTraceCodeGuide(id) {
        const currentWidth = window.innerWidth;
        const currentHeight = window.innerHeight;

        const newHeight = Math.min(Math.round(currentHeight * 0.76), 800);

        // 计算新窗口的左上角位置
        const left = (currentWidth / 2) - 600;
        const top = (currentHeight / 2) - (newHeight / 2);

        window.open(`https://${location.host}/cms/view/loginfo/${id}`, '_blank', `width=1280,height=${newHeight},top=${top},left=${left}`);
    }

    /**
     * @desc 判断药品有码还是无码药品
     * <AUTHOR>
     * @date 2024/9/10 上午9:49
     */
    static isNoTraceCodeGoods(goodsInfo) {
        const {
            traceableCodeNoInfoList,
        } = goodsInfo || {};
        return !!(traceableCodeNoInfoList || []).find((item) => item.type === TraceableCodeTypeEnum.NO_CODE);
    }

    // 特殊无码商品-无固定值后端默认是空字符串
    static isNullCodeGoods(goodsInfo) {
        return TraceCode.isNoTraceCodeGoods(goodsInfo) && isNull(TraceCode.getDefaultNoCodeIdentification(goodsInfo));
    }

    /**
     * @desc 获取药品的标识码
     * <AUTHOR>
     * @date 2024/9/10 下午2:39
     */
    static getDrugIdentificationCode(goodsInfo) {
        const {
            traceableCodeNoInfoList,
        } = goodsInfo || {};
        if (!traceableCodeNoInfoList) return '';
        return traceableCodeNoInfoList[0]?.drugIdentificationCode || '';
    }

    /**
     * @desc 药品标记码列表,如果是无码商品则只会返回一个[无追溯码]
     * <AUTHOR> Yang
     * @date 2024-10-09 14:54:46
     */
    static getDrugIdentificationCodeList(goodsInfo) {
        const isNoCodeGoods = this.isNoTraceCodeGoods(goodsInfo);
        if (isNoCodeGoods) {
            return ['无追溯码'];
        }
        const {
            traceableCodeNoInfoList,
        } = goodsInfo || {};
        return (traceableCodeNoInfoList || []).map((it) => it.drugIdentificationCode);
    }

    /**
     * @desc 获取不同地区的药品无码标识，若地区没有固定值不上报，需要自行处理为无需采集提示给用户
     * <AUTHOR>
     * @date 2024/9/10 下午2:48
     * @return {string} 无码标识-默认是药品
     */
    static getDefaultNoCodeIdentification(goodsInfo) {
        const {
            drug = '', // 药品默认追溯码
            material = '', // 耗材默认追溯码
        } = window.$abcSocialSecurity.defaultDrugTraceCode || {};

        return goodsInfo?.type === GoodsTypeEnum.MATERIAL ? material : drug;
    }

    /**
     * @desc 初始化无码 追溯码列表
     * <AUTHOR> Yang
     * @date 2024-09-10 17:59:18
     */
    static initNoTraceCodeList(options) {
        const {
            unit,
            unitCount,
            productInfo,
            _maxTraceCodeCount,
        } = options;
        if (!TraceCode.isSupplementNoCodeGoods(productInfo)) return;
        if (productInfo && TraceCode.isNoTraceCodeGoods(productInfo)) {
            const isDismounting = TraceCode.itemIsDismounting(options);
            const code = TraceCode.getDefaultNoCodeIdentification(productInfo);
            const codeInfo = {
                no: code,
                drugIdentificationCode: code,
                type: TraceableCodeTypeEnum.NO_CODE,
            };
            if (isDismounting) {
                const {
                    pieceNum,
                } = productInfo || {};

                // 中药特殊处理，只填一个无码标记
                if (isChineseMedicine(productInfo)) {
                    return [
                        {
                            ...codeInfo,
                            traceableCodeNoInfo: codeInfo,
                            keyId: createGUID(),
                            count: 1,
                            unit,
                        },
                    ];
                }

                const count = _maxTraceCodeCount || Math.ceil(Math.abs(unitCount) / pieceNum);

                return [
                    {
                        ...codeInfo,
                        traceableCodeNoInfo: codeInfo,
                        keyId: createGUID(),
                        count,
                        unit,
                    },
                ];
            }
            return [
                {
                    ...codeInfo,
                    traceableCodeNoInfo: codeInfo,
                    keyId: createGUID(),
                    count: _maxTraceCodeCount || Math.abs(unitCount) || 1,
                    unit,
                },
            ];
        }
        return [];
    }

    /**
     * @desc 合并无码列表数据
     * <AUTHOR>
     * @date 2024/9/10 下午6:12
     */
    static mergeNoTraceCodeList(item) {
        // 有码追溯码列表
        const traceableCodeList = (item.traceableCodeList || []).filter((item) => item.traceableCodeNoInfo?.type !== TraceableCodeTypeEnum.NO_CODE);
        const codeInfo = TraceCode.initNoTraceCodeObj(item);

        return traceableCodeList.concat({
            ...codeInfo,
            traceableCodeNoInfo: codeInfo,
            keyId: createGUID(),
        });
    }

    /**
     * @desc 初始化无码对象-只有一条数据
     * <AUTHOR>
     * @date 2024/10/30 下午2:45
     */
    static initNoTraceCodeObj(item) {
        let count = item.isTrans ? item.maxCount : item.unitCount;

        if (isChineseMedicine(item.goods)) {
            count = 1;
        }

        const code = TraceCode.getDefaultNoCodeIdentification(item.goods);

        const codeInfo = {
            count,
            no: code,
            drugIdentificationCode: code,
            type: TraceableCodeTypeEnum.NO_CODE,
        };
        return codeInfo;
    }
    /**
     * @desc 获取套餐拍平的items
     * <AUTHOR> Yang
     * @date 2024-09-25 15:30:53
    */
    static getFlatItems(formItems) {
        const _arr = [];
        formItems.forEach((item) => {
            if (item.productType === GoodsTypeEnum.COMPOSE && item.composeChildren?.length) {
                item.composeChildren.forEach((child) => {
                    if (TraceCode.formItemSupportTraceCode(child)) {
                        _arr.push(child);
                    }
                });
            } else {
                _arr.push(item);
            }
        });
        return _arr;
    }

    static async getMaxTraceCountList(options) {
        // if (!TraceCode.isStrictCount) return [];
        // if (!window.$abcSocialSecurity.isOpenSocial) return [];
        const {
            dataList = [],
            getGoodsInfo,
            getUnitInfo,
            createKeyId,
            scene,
            patientOrderId,
            sceneType = '',
            isNeedTraceableCodeRule = true,
        } = options || {};

        const mapCache = new Map();
        const list = await TraceCode.getCollectCodeCountList(dataList.filter((item) => {
            const productInfo = (getGoodsInfo ? getGoodsInfo(item) : item?.productInfo) || {};
            return !TraceCode.isNoTraceCodeGoods(productInfo) && !isChineseMedicine(productInfo) && TraceCode.isSupportTraceCode(productInfo.typeId);
        }).map((item) => {
            const productInfo = (getGoodsInfo ? getGoodsInfo(item) : item?.productInfo) || {};
            const {
                pieceNum,
                pieceUnit,
                packageUnit,
                medicineNmpn,
                shebao,
                type,
                subType,
                goodsId,
                medicineCadn,
            } = productInfo;
            const {
                shebaoPieceNum,
                shebaoPieceUnit,
                shebaoPackageUnit,
                shebaoMedicineNmpn,
                limitUnitType,
                priceLimit,
                specialPriceLimit,
                socialName,
            } = shebao || {};
            const {
                unitCount,
                doseCount,
                unit,
                useExternalCount,// 盘点、退货相关需要传两个值的情况带上这个开关
                packageCount: _packageCount,
                pieceCount: _pieceCount,
                traceableCodeList = [],
                shebaoDismountingFlag,
                traceableCodeRule = {},
            } = getUnitInfo ? getUnitInfo(item) : item;
            let pieceCount = useExternalCount ? _pieceCount : null, packageCount = useExternalCount ? _packageCount : null;

            if (!useExternalCount) {
                const count = (Math.abs(unitCount) || 1) * (doseCount || 1);
                if (unit === pieceUnit && unit !== packageUnit) {
                    pieceCount = count;
                } else {
                    packageCount = count;
                }
            }
            item.keyId = item.keyId || createGUID();
            const keyId = createKeyId ? createKeyId(item) : item.keyId;
            mapCache.set(keyId, productInfo);
            return {
                keyId,
                goodsType: type,
                goodsSubType: subType,
                pieceNum,
                pieceUnit,
                packageUnit,
                medicineNmpn,
                shebaoPieceNum,
                shebaoPieceUnit,
                shebaoPackageUnit,
                shebaoMedicineNmpn,
                pieceCount,
                packageCount,
                limitUnitType,
                shebaoPriceLimit: priceLimit,
                specialPriceLimit,
                goodsId,
                patientOrderId: patientOrderId ? patientOrderId : null,
                medicineCadn,
                shebaoMedicineName: socialName,
                shebaoDismountingFlag,
                traceableCodeRule: isNeedTraceableCodeRule ? traceableCodeRule : {},
                noList: (traceableCodeList || []).map((e) => {
                    const base = {
                        no: e.no,
                        id: e.id,
                        trdnFlag: e.trdnFlag,
                        traceCodeLockId: TraceCode.getTraceCodeLockId(sceneType,item),
                    };
                    if (packageUnit !== pieceUnit) {
                        if (e.unit === packageUnit) {
                            return {
                                hisPackageCount: e.hisPackageCount,
                                ...base,
                            };
                        }
                        if (e.unit === pieceUnit) {
                            return {
                                hisPieceCount: e.hisPieceCount,
                                ...base,
                            };
                        }
                    }
                    if (TraceCode.isShouldApplyPieceUnit(e)) {
                        return {
                            hisPieceCount: e.hisPieceCount,
                            ...base,
                        };
                    }
                    return {
                        hisPackageCount: e.hisPackageCount,
                        ...base,
                    };
                }),
            };
        }), scene,patientOrderId);
        return list.map((it) => {
            const productInfo = mapCache.get(it.keyId);
            // 2. 中药饮片、颗粒：无码不补码，入库和出库都只传1个无码标识 traceableCodeNum 都需要改成1 @吴蔚
            if (TraceCode.isNoTraceCodeGoods(productInfo) && isChineseMedicine(productInfo)) {
                it.traceableCodeNum = 1;
            }
            return it;
        });
    }
    static getTraceCodeLockId(sceneType, item) {
        const idMap = {
            [SceneTypeEnum.PHARMACY]: item.sourceFormItemId,
            [SceneTypeEnum.CHARGE]: item.id,
        };

        return idMap[sceneType] || null;
    }
    static async getCollectCodeCountList(list, scene,patientOrderId) {
        if (!list) return [];
        if (list.length === 0) return [];
        try {
            const { data } = await GoodsV3API.getCollectCodeCountList({
                shebaoRegion: window.$abcSocialSecurity.region,
                shebaoHisType: window.$abcSocialSecurity.basicInfo?.hospitalType || '1',
                scene,
                list,
                patientOrderId: patientOrderId ? patientOrderId : null,
            });
            return data?.list || [];
        } catch (err) {
            console.error(err);
            return [];
        }
    }

    /**
     * @desc 医保目录规格 = 1 支/盒，系统内部规格 = 1 支/盒
     * <AUTHOR>
     * @date 2024/9/26 下午7:31
     */
    static displayShebaoSpec(goods) {
        const {
            pieceNum,
            pieceUnit,
            packageUnit,
            shebao,
        } = goods || {};

        const {
            shebaoPieceNum,
            shebaoPieceUnit,
            shebaoPackageUnit,
        } = shebao || {};

        return `系统档案规格 = ${pieceNum || ''}${pieceUnit || ''}/${packageUnit || ''}，医保目录规格 = ${shebaoPieceNum || ''}${shebaoPieceUnit || ''}/${shebaoPackageUnit || ''}`;
    }

    // 获取实际unitCount
    static getUnitCount(item) {
        const {
            isCompose,
            status,
            modelRemainingUnitCount,
            unitCount,
            doseCount,
            dispensingFormId,
        } = item;
        // 药房有dispensingFormId的情况下，不需要乘以item上的数量
        if (status === undefined && !dispensingFormId && isCompose) {
            return Big(getSafeNumber(unitCount)).times(getSafeNumber(item._chargeItemCount) || 1);
        }
        //开启强采模式 实际发药数量为全部数量
        const actualCount = TraceCode.hasEnableCollCheckStrictMode ? (unitCount || 1) : (modelRemainingUnitCount || unitCount || 1);
        let res = Big(getSafeNumber(actualCount)).times(getSafeNumber(doseCount) || 1);
        if (status === dispenseItemStatusEnum.RETURN) {
            res = res.times(-1); // 使用Big.js的times方法取负值，确保返回的仍然是Big对象
        }
        return res;
    }

    /**
     * @desc goods是否支持采集
     * <AUTHOR> Yang
     * @date 2024-11-06 19:24:37
     */
    static supportCollect(goods) {
        const {
            shebao,
        } = goods || {};
        if (!shebao) return false;
        if (!shebao.nationalCode || shebao.nationalCode === DISABLED) return false;
        return !TraceCode.isNullCodeGoods(goods);
    }

    /**
     * @desc 获取追溯码默认数量
     * <AUTHOR> Yang
     * @date 2024-11-07 16:06:15
    */
    static getTraceCodeCount(trData) {
        const {
            _maxTraceCodeCount,
            _collectCountTransFactor,
        } = trData;
        const totalCount = this.getTraceCodeCollectCount(trData);
        if (totalCount >= _maxTraceCodeCount) return 1;
        if (totalCount + _collectCountTransFactor > _maxTraceCodeCount) {
            return _maxTraceCodeCount - totalCount;
        }
        return _collectCountTransFactor || 1;
    }
    static getTraceCodeCollectCount(trData) {
        return trData.traceableCodeList?.reduce((acc, cur) => {
            return acc + (+cur.count || 1);
        }, 0);
    }
    // 推荐采集追溯码
    static getTraceCodeShouldCollectCountInfo(trData) {
        if (this.hasEnableCollCheckStrictMode) {
            //开启强采模式，推荐采集和发药组合相关
            const { traceableCodeRule = {} } = trData;
            return {
                bigShouldCollectCount: traceableCodeRule?.hisPackageCount ?? 0,
                smallShouldCollectCount: this.hasEnableDismountingMode ? 0 : (traceableCodeRule?.hisPieceCount ?? 0),
            };
        }
        const {
            unit,productInfo = {},
        } = trData;
        const {
            packageUnit,pieceNum,
        } = productInfo ?? {};
        const dispensingCount = Big(TraceCode.getUnitCount(trData));
        const safePieceNum = Big(getSafeNumber(pieceNum || 1));
        const isBigUnit = packageUnit === unit;
        if (isBigUnit) {
            const bigShouldCollectCount = dispensingCount.round(0,0);
            const smallShouldCollectCount = ((dispensingCount.times(safePieceNum)).minus(bigShouldCollectCount.times(safePieceNum))).round(0,0);
            return {
                bigShouldCollectCount: bigShouldCollectCount.toNumber(),
                smallShouldCollectCount: smallShouldCollectCount.toNumber(),
            };
        }
        const bigShouldCollectCount = (dispensingCount.div(safePieceNum)).round(0,0);
        const smallShouldCollectCount = dispensingCount.minus(bigShouldCollectCount.times(safePieceNum));
        return {
            bigShouldCollectCount: bigShouldCollectCount.toNumber(),
            smallShouldCollectCount: smallShouldCollectCount.toNumber(),
        };
    }
    // 已采信息
    static getTraceCodeCollectCountInfo(trData) {
        const {
            traceableCodeList = [],unit,productInfo = {},
        } = trData;
        const {
            packageUnit,pieceUnit,pieceNum,
        } = productInfo ?? {};
        //开启强采模式，哪怕发小单位，已采也优先展示大单位，和发药组合相关
        const isBigUnit = TraceCode.hasEnableCollCheckStrictMode ? true : packageUnit === unit;
        let str = '';
        let collectBigCount = 0;
        let collectSmallCount = 0;
        if (isBigUnit) {
            const {
                _collectBigCount, _collectSmallCount,
            } = traceableCodeList.reduce((result, cur) => {
                const isDismounting = TraceCode.isShouldApplyPieceUnit(cur);
                const count = cur.count ? cur.count : (isDismounting ? cur.hisPieceCount : cur.hisPackageCount);
                const curUnit = cur.unit ? cur.unit : (isDismounting ? pieceUnit : packageUnit);
                if (TraceCode.hasEnableCollCheckStrictMode) {
                    if (!cur.trdnFlag && curUnit === packageUnit) {
                        result._collectBigCount += Number(count ?? 0);
                    }
                    if (cur.trdnFlag) {
                        if (curUnit === packageUnit) {
                            result._collectSmallCount += Number((count) ?? 0) * (pieceNum ?? 1);
                        } else {
                            result._collectSmallCount += Number(count ?? 0);
                        }
                    }
                } else {
                    if (curUnit === packageUnit) {
                        result._collectBigCount += Number(count ?? 0);
                    } else {
                        result._collectSmallCount += Number(count ?? 0);
                    }
                }
                return result;
            }, {
                _collectBigCount: 0, _collectSmallCount: 0,
            });
            const smallToBig = (Big(getSafeNumber(_collectSmallCount)).div(Big(getSafeNumber(pieceNum || 1)))).round(0,0);
            if (smallToBig.gt(Big(0)) && !TraceCode.hasEnableCollCheckStrictMode) {
                collectBigCount = _collectBigCount + smallToBig.toNumber();
                collectSmallCount = (Big(getSafeNumber(_collectSmallCount))).minus(smallToBig.times(Big(getSafeNumber(pieceNum || 1)))).toNumber();
            } else {
                collectSmallCount = _collectSmallCount;
                collectBigCount = _collectBigCount;
            }
            const bigStr = collectBigCount ? `${collectBigCount} ${packageUnit}` : (collectSmallCount ? '' : `${collectBigCount}`);
            const smallStr = collectSmallCount ? `${collectSmallCount} ${pieceUnit}` : '';
            str = bigStr + smallStr;
        } else {
            const _collectSmallCount = traceableCodeList.reduce((pre, cur) => {
                const isDismounting = TraceCode.isShouldApplyPieceUnit(cur);
                const count = cur.count ? cur.count : (isDismounting ? cur.hisPieceCount : cur.hisPackageCount);
                const curUnit = cur.unit ? cur.unit : (isDismounting ? pieceUnit : packageUnit);
                if (curUnit === packageUnit) {
                    return pre + Number(count ?? 0) * (pieceNum || 1);
                }
                return pre + Number(count ?? 0);
            }, 0);
            str = _collectSmallCount ? `${_collectSmallCount} ${unit}` : _collectSmallCount ?? 0;
            collectSmallCount = _collectSmallCount;
        }
        return {
            str,
            collectSmallCount,
            collectBigCount,
            packageUnit,
            pieceUnit,
        };
    }

    /**
     * @desc 海南对接药监追溯码查询
     * <AUTHOR>
     * @date 2024/11/28 下午2:12
     */
    static async fetchPharmaceuticalTraceCodeDetails(options) {
        const {
            dataList = [],
            customKey = 'traceableCodeList',
            beforeHook,
            afterHook,
            errorHook,
        } = options;
        try {
            // 处理获取追溯码列表数据
            const codeList = dataList.reduce((res, item) => {
                if (item[customKey]?.length) {
                    res = res.concat(TraceCode.transCodeList(item[customKey]).map((e) => e.no));
                }
                return res;
            }, []);

            if (!codeList?.length) return [];
            const traceCodes = [...new Set(codeList)].join(',');
            typeof beforeHook === 'function' && beforeHook();
            const res = await window.$abcSocialSecurity?.drugQuery(traceCodes);
            console.log('fetchPharmaceuticalTraceCodeDetails', res);
            return res?.data || [];
        } catch (e) {
            console.error('fetchPharmaceuticalTraceCodeDetails', e);
            typeof errorHook === 'function' && errorHook();
            return [];
        } finally {
            typeof afterHook === 'function' && afterHook();
        }
    }

    /**
     * @desc 海南对接药监追溯码查询
     * <AUTHOR>
     * @date 2024/12/3 上午11:34
     * @param customError 自定义错误提示
     */
    static async getPharmaceuticalTraceCodeQueryConfig(customError) {
        try {
            if (typeof window.$abcSocialSecurity?.isSupportDrugTraceQuery === 'function') {
                const isSupportDrugTraceQuery = await window.$abcSocialSecurity.isSupportDrugTraceQuery();
                return !!isSupportDrugTraceQuery;
            }
            return false;
        } catch (e) {
            if (typeof customError === 'function') {
                customError(e);
            } else {
                console.log('getPharmaceuticalTraceCodeQueryConfig', e);
            }
            return false;
        }
    }

    // !发药追溯码采集流程新版
    // 1.接口说明
    // ? 1.1 TraceCode.fetchByCode 查码接口【/api/v3/goods/query/traceable-code-list】，功能：换码以及查询码剩余数量（不管是否对接码上放心后端都应该返回）
    // ? 1.2 TraceCode.getMaxTraceCountList后端分摊码计算拆零接口 【/api/v3/goods/calculate/traceable-code-count】，功能计算当前发药的【已采应采】，【拆零标记】，以及分摊采集的追溯码数量及单位（需告诉前端如何取值）

    // 2.采集流程
    // * 2.1 打开追溯码采集弹窗时调用TraceCode.getMaxTraceCountList获取当前发药项的【已采应采】，【拆零标记】
    // * 2.2 扫码采集时调用TraceCode.fetchByCode, 获取当前追溯码的剩余数量，根据【拆零标记】取对应单位与数量，默认填入，再触发2.1操作后端重新计算分摊与拆零（删除追溯码时也要触发重算）
    /**
     * @desc 前端计算追溯码拆零标记(备用)
     * <AUTHOR>
     * @date 2025-03-19 17:59:25
     * @param trData formItem，收费、发药项
     * @return {number} dismounting 拆零标记，枚举值见 {@link TraceableCodeDismountingFlagEnum}
     */
    static calculateTraceCodeDismounting(trData) {// ?前端计算追溯码拆零标记(备用)
        const {
            // unit,// 发药单位
            _dismounting,// 拆零标记
            _maxTraceCodeCount,// 应采数量
            traceableCodeList,// 已采集追溯码列表
        } = trData;

        // !强制拆零
        // 系统强制拆零直接返回-部分地区根据医保要求所有药品强制，如：四川、贵州
        if (_dismounting === TraceableCodeDismountingFlagEnum.SYSTEM_FORCE_DISMOUNTING) {
            return TraceableCodeDismountingFlagEnum.SYSTEM_FORCE_DISMOUNTING;
        }

        // !固定特殊规则
        // 针剂固定拆零：若医保规格小单位 = 大单位（如：1瓶/瓶，1袋/袋）

        // !常规规格(由于缺少条件，部分TODO)
        // TODO 1、如果发药数转为大单位 ≠ 整数

        // TODO 2、若发药数量换算为大单位，刚好为整数，则由追溯码采集个数判定trdn_flag

        // 2.1 如果用户追溯码实采 ≤ 应采，则trdn_flag = 0（不拆零）
        if (traceableCodeList?.length <= _maxTraceCodeCount) {
            return TraceableCodeDismountingFlagEnum.NO_DISMOUNTING;
        }
        // 2.2 如果用户采集追溯码 > 应采，则trdn_flag = 1（拆零）
        if (traceableCodeList?.length > _maxTraceCodeCount) {
            return TraceableCodeDismountingFlagEnum.SYSTEM_CALCULATE_DISMOUNTING;
        }

        // TODO 3、小单位地区：需要将发药转为大单位，同时采集追溯码去重后判断

        return TraceableCodeDismountingFlagEnum.NO_DISMOUNTING;
    }
    // 获取追溯码可用数量 根据当前追溯码单位转换后 如果后台没给这个字段 就是无限
    static getTraceCodeLeftCount(traceCodeUseInfo) {
        return traceCodeUseInfo.hisLeftTotalPieceCount ?? Number.MAX_SAFE_INTEGER;
    }
    //获取追溯码当前已采数量 小单位表示
    static getTraceCollectCodeCountBySmall(productInfo,traceCode) {
        const {
            packageUnit,pieceNum,
        } = productInfo ?? {};
        const safePieceNum = Big(getSafeNumber(pieceNum || 1));
        const {
            unit,count,
        } = traceCode;
        const isBigUnit = packageUnit === unit;
        const currentSmallCount = isBigUnit ? Big(getSafeNumber(count ?? 0)).times(safePieceNum) : Big(getSafeNumber(count ?? 0));//现在这个码的当前小单位数量

        return currentSmallCount.toNumber();
    }
    // 计算重复采集后的新数量及新单位
    static getTraceCodeRepeatedCollectCount(traceCodeUseInfo,traceCode) {
        const {
            productInfo = {},
        } = traceCodeUseInfo;
        const {
            packageUnit,pieceUnit,pieceNum,
        } = productInfo ?? {};
        const safePieceNum = Big(getSafeNumber(pieceNum || 1));

        const {
            unit,count,
        } = traceCode;
        const isBigUnit = packageUnit === unit;
        const currentSmallCount = isBigUnit ? Big(getSafeNumber(count)).times(safePieceNum) : Big(getSafeNumber(count));//现在这个码的当前小单位数量

        const shouldSingleCount = isBigUnit ? safePieceNum : Big(1); //根据目前单位采集1个单位数量
        const smallCountResult = shouldSingleCount.plus(currentSmallCount); //每次最多采一个小单位数量

        const bigResult = (smallCountResult.div(safePieceNum)).round(0,0);
        const smallResult = (smallCountResult.minus(bigResult.times(safePieceNum))).toNumber();
        if (smallResult) {
            //如果小单位数量存在，说明应该拆零 小单位显示
            return {
                count: smallCountResult.toNumber(),
                unit: pieceUnit,
            };
        }
        return {
            count: bigResult.toNumber(),
            unit: packageUnit,
        };
    }
    //校验应采和已采是否对齐
    static validateTraceableCodeCount(traceCodeUseInfo) {
        const {
            unit,productInfo = {},traceableCodeRule = {},
        } = traceCodeUseInfo;
        const {
            packageUnit,pieceNum,
        } = productInfo ?? {};
        const {
            hisPackageCount,hisPieceCount,
        } = traceableCodeRule ?? {};
        const dispensingCount = TraceCode.hasEnableDismountingMode ? Big(getSafeNumber(TraceCode.getTraceCodeShouldCollectCountInfo(traceCodeUseInfo)?.bigShouldCollectCount)) : TraceCode.getUnitCount(traceCodeUseInfo);
        const {
            collectBigCount,collectSmallCount,str,
        } = TraceCode.getTraceCodeCollectCountInfo(traceCodeUseInfo);
        if (TraceCode.hasEnableCollCheckStrictMode) {
            let bigFlag = false;
            let smallFlag = false;
            if (hisPackageCount) {
                bigFlag = !(collectBigCount === hisPackageCount);
            }
            if (hisPieceCount && !TraceCode.hasEnableDismountingMode) {
                smallFlag = !(collectSmallCount === hisPieceCount);
            }
            return {
                validateFlag: bigFlag || smallFlag,
            };
        }
        const safeCollectBigCount = Big(getSafeNumber(collectBigCount));
        const safeCollectSmallCount = Big(getSafeNumber(collectSmallCount));
        const safePieceNum = Big(getSafeNumber(pieceNum || 1));
        const isBigUnit = TraceCode.hasEnableDismountingMode ? true : packageUnit === unit;
        let validateFlag = false;
        if (isBigUnit) {
            const count = safeCollectBigCount.plus(safeCollectSmallCount.div(safePieceNum));
            validateFlag = !dispensingCount.eq(count);
        } else {
            validateFlag = !dispensingCount.eq(safeCollectSmallCount);
        }
        return {
            validateFlag,
            str,
        };

    }

    // 给chargeForms回写追溯码
    static async setChargeFormsTraceCodeList(flatFormItems, chargeForms) {
        try {
            flatFormItems.forEach((r) => {
                chargeForms.forEach((form) => {
                    form.chargeFormItems.forEach((item) => {
                        if (item.productType === GoodsTypeEnum.COMPOSE && item.composeChildren?.length) {
                            const child = item.composeChildren.find((it) => (it.keyId && it.keyId === r.keyId) || (it.id && it.id === r.id));
                            if (child) {
                                child.traceableCodeList = (child.traceableCodeList ?? []).filter((it) => !!it.used).concat((r.traceableCodeList || []).filter((it) => !it.used));
                                child.shebaoDismountingFlag = r.shebaoDismountingFlag; //医保拆零标志
                                child.traceableCodeRule = r.traceableCodeRule;
                            }
                        } else {
                            // 反写追溯码
                            if ((item.keyId && item.keyId === r.keyId) || (item.id && item.id === r.id)) {
                                item.traceableCodeList = (item.traceableCodeList ?? []).filter((it) => !!it.used).concat((r.traceableCodeList || []).filter((it) => !it.used));
                                item.shebaoDismountingFlag = r.shebaoDismountingFlag; //医保拆零标志
                                item.traceableCodeRule = r.traceableCodeRule;
                            }
                        }
                    });
                });
            });
        } catch (e) {
            console.error('setChargeFormsTraceCodeList', e);
        }
    }

    static isCompatibleHistoryData(traceCodeUseInfo,disabled) {
        const isExist = traceCodeUseInfo.traceableCodeList?.some((code) => {
            return code.hisPackageCount > 0 || code.hisPieceCount > 0;
        });
        return (disabled && !isExist);
    }
    // 将小单位转为大单位+小单位显示
    static displayFormatPieceUnit(pieceCount = 0,productInfo) {
        try {
            const {
                packageUnit,pieceNum,pieceUnit,
            } = productInfo ?? {};
            const safePieceCount = Big(getSafeNumber(pieceCount));
            const safePieceNum = Big(getSafeNumber(pieceNum || 1));
            const bigResult = (safePieceCount.div(safePieceNum)).round(0,0);
            const smallResult = (safePieceCount.minus(bigResult.times(safePieceNum)));
            if (bigResult.toNumber() && smallResult.toNumber()) {
                return `${bigResult}${packageUnit}${smallResult}${pieceUnit}`;
            } if (bigResult.toNumber() && !smallResult.toNumber()) {
                return `${bigResult}${packageUnit}`;
            }
            return `${smallResult}${pieceUnit}`;
        } catch (e) {
            console.log(e);
        }
    }


    // specificationMatchStatus = 1 就认为医保和HIS规格没办法转换
    static isSpecificationMatch(productInfo) {
        const { specificationMatchStatus } = productInfo ?? {};
        return specificationMatchStatus !== 1;
    }

    static isShebaoDismountingFlag(shebaoDismountingFlag) {
        return [ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_NO_EDIT,ShebaoTraceableCodeDismountingFlagEnum.DISMOUNTING_EDIT].includes(shebaoDismountingFlag) && TraceCode.hasEnableDismountingMode;
    }

    static isShouldApplyPieceUnit(traceCode) {
        const { hisPieceCount } = traceCode ?? {};
        return !!hisPieceCount;
    }
}
