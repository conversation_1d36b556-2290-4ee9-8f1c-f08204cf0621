<template>
    <abc-popover
        ref="codePopover"
        theme="white"
        trigger="click"
        style="width: 100%; height: 90%;"
        placement="bottom-end"
        :offset="6"
        :popper-style="{
            padding: 0,
            marginTop: 0
        }"
        @show="handleSplitZeroCodeShow()"
        @hide="stopBarcodeDetect()"
    >
        <div slot="reference" style="width: 100%; height: 100%;">
            <slot></slot>
        </div>
        <abc-card
            v-abc-loading.small="loading"
            :border="false"
            style="width: 450px; height: 295px;"
        >
            <template #title>
                <abc-flex vertical :gap="10" style="padding: 10px 16px 12px 16px;">
                    <abc-flex align="center" justify="space-between">
                        <abc-text theme="black" bold>
                            历史追溯码
                        </abc-text>
                        <abc-space :size="4">
                            <abc-icon size="12" icon="n-info-circle-fill" color="var(--abc-color-P7)"></abc-icon>
                            <abc-text theme="gray" size="mini">
                                点击详情可查看历史、修正剩余量
                            </abc-text>
                        </abc-space>
                    </abc-flex>
                    <abc-input
                        ref="scanRef"
                        v-model.trim="searchKey"
                        adaptive-width
                        placeholder="扫描或输入追溯码可查询"
                        :tabindex="-1"
                        inputmode="none"
                        :disabled="disabled"
                        :loading="loading"
                        clearable
                        loading-position="left"
                        @enter="handleEnter"
                        @clear="loadData"
                    >
                        <label slot="prepend">
                            <abc-icon icon="s-scan-line" color="var(--abc-color-T3)"></abc-icon>
                        </label>
                        <abc-text v-if="searchKey" slot="appendInner" theme="gray-light">
                            敲回车录入
                        </abc-text>
                    </abc-input>
                </abc-flex>
            </template>
            <template v-if="splitZeroCodeList.length">
                <abc-list
                    custom-item-class="history-trace-code-list-item"
                    style="height: 165px; padding: 6px 0 6px 6px;"
                    show-divider
                    :divider-config="{
                        variant: 'dashed',
                        theme: 'light',
                    }"
                    no-close-border
                    :create-key="(it)=>it.keyId || it.id || it.no"
                    :data-list="splitZeroCodeList"
                >
                    <template
                        #default="{
                            item
                        }"
                    >
                        <abc-flex style="flex: 1; width: 0;" :gap="16" @click.stop="handleToUse(item)">
                            <abc-flex :gap="8" align="center">
                                <abc-text
                                    v-if="item.traceableCodeNoInfo && formatTraceableCode(item).start"
                                    theme="gray"
                                    :title="formatTraceableCode(item).start || ''"
                                    class="ellipsis"
                                    style="max-width: 100px;"
                                >
                                    {{ formatTraceableCode(item).start || '' }}
                                </abc-text>

                                <abc-text
                                    theme="gray"
                                    class="ellipsis"
                                    style="max-width: 116px;"
                                    :title="formatTraceableCode(item).end || ''"
                                >
                                    {{ formatTraceableCode(item).end || '' }}
                                </abc-text>
                            </abc-flex>

                            <abc-text theme="black" class="ellipsis" :title="`剩余：${item.dispGoodsCount ?? '-' }`">
                                剩余：{{ item.dispGoodsCount ?? '-' }}
                            </abc-text>
                        </abc-flex>
                    </template>
                    <template #append="{ item }">
                        <abc-space>
                            <abc-button
                                v-if="!disabled && item.noType !== TraceableCodeTypeEnum.NO_CODE && (item.leftPieceCount > 0 || item.leftPackageCount > 0)"
                                class="history-trace-code-list-item-btn"
                                variant="text"
                                theme="primary"
                                size="small"
                                @click.stop="handleToUse(item)"
                            >
                                使用
                            </abc-button>
                            <abc-button
                                class="history-trace-code-list-item-btn"
                                variant="text"
                                theme="primary"
                                size="small"
                                @click.stop="handleToDetail(item)"
                            >
                                详情
                            </abc-button>
                        </abc-space>
                    </template>
                </abc-list>
                <abc-divider margin="none"></abc-divider>
                <div style="padding: 8px 16px;">
                    <abc-button-pagination
                        :key="createGUID()"
                        size="large"
                        :total-count="pageParams.total"
                        :page-size="pageParams.pageSize"
                        :current-page="pageParams.pageIndex"
                        @changePage="handlePageChange"
                    ></abc-button-pagination>
                </div>
            </template>
            <div v-else style="height: 134px;">
                <abc-content-empty
                    size="mini"
                    value="暂无可用拆零追溯码"
                ></abc-content-empty>
            </div>
        </abc-card>
    </abc-popover>
</template>

<script type="text/babel">
    import TraceCode, { TraceableCodeTypeEnum } from '@/service/trace-code/service';
    import TraceCodeUsageDetailDialog from '@/service/trace-code/components/trace-code-usage-detail-dialog';
    import BarcodeDetectorV2 from 'utils/barcode-detector-v2';
    import GoodsV3API from 'api/goods/index-v3';
    import { createGUID } from '@/utils';

    export default {
        name: 'TableCellSplitZeroCode',
        props: {
            goodsId: {
                type: String,
                required: true,
            },
            disabled: {
                type: Boolean,
            },
            traceCodeUseInfo: {
                type: Object,
                default: () => ({}),
            },
        },
        data() {
            return {
                loading: false,
                TraceableCodeTypeEnum,
                pageParams: {
                    pageIndex: 0,
                    pageSize: 4,
                    total: 0,
                },
                splitZeroCodeList: [],
                searchKey: '',
            };
        },
        beforeDestroy() {
            this.stopBarcodeDetect();
        },
        methods: {
            createGUID,
            async handleSplitZeroCodeShow() {
                this.startBarcodeDetect();
                await this.loadData();
            },
            async loadData(resetPageParams = true) {
                try {
                    if (resetPageParams) {
                        this.pageParams.pageIndex = 0;
                    }
                    this.loading = true;
                    const { data } = await GoodsV3API.fetchTraceCodeAvailableLog({
                        goodsId: this.traceCodeUseInfo?.productId,
                        no: this.searchKey?.replace(/\s+/g, ''),
                        limit: this.pageParams.pageSize,
                        offset: this.pageParams.pageSize * this.pageParams.pageIndex,
                    });
                    this.splitZeroCodeList = (data.rows ?? []).filter(Boolean);
                    this.pageParams.total = data.total;
                    if (this.searchKey && (!this.splitZeroCodeList || this.splitZeroCodeList.length === 0)) {
                        this.$Toast({
                            message: '未查询到追溯码，请确认扫描或输入是否正确',
                        });
                    }
                } catch (e) {
                    console.log(e);
                } finally {
                    this.loading = false;
                }
            },
            handleToUse(item) {
                if (this.disabled || (!item.leftPieceCount && !item.leftPackageCount)) return;
                if (item.noType === TraceableCodeTypeEnum.NO_CODE) {
                    this.$Toast({
                        message: '无码商品无需采集',
                        type: 'error',
                    });
                    return;
                }
                this.$emit('add-code', {
                    traceCode: item.no,
                    traceableCodeNoInfo: item.traceableCodeNoInfo,
                });
            },
            handlePageChange(pageIndex) {
                this.pageParams.pageIndex = pageIndex;
                this.loadData(false);
            },
            formatTraceableCode(item) {
                const {
                    no,
                    traceableCodeNoInfo,
                } = item;
                const {
                    start,end,
                } = TraceCode.formatTraceableCode({
                    drugIdentificationCode: traceableCodeNoInfo?.drugIdentificationCode,
                    no,
                });
                return {
                    start,end,
                };
            },
            handleToDetail(item) {
                new TraceCodeUsageDetailDialog({
                    code: item,
                    traceCodeUseInfo: this.traceCodeUseInfo,
                    onSave: () => {
                        this.$emit('updateDetailSuccess');
                    },
                }).generateDialogAsync({ parent: this });
            },
            startBarcodeDetect() {
                if (this.disabled) return;
                if (this.barcodeDetector) return;
                this.barcodeDetector = BarcodeDetectorV2.getInstance();
                this.barcodeDetector.startDetect(this.handleMyScanCode, true, (instance) => {
                    this.isScanning = instance.isScanning;
                });
            },
            stopBarcodeDetect() {
                if (!this.barcodeDetector) return;
                this.barcodeDetector.stopDetect(this.handleMyScanCode);
                this.barcodeDetector = null;
            },
            handleMyScanCode(e, code) {
                if (e.target === this.$refs.scanRef?.$el) {
                    return;
                }
                this.searchKey = code;
                this.loadData();
            },
            handleEnter(e) {
                if (!e.target.value) return;
                this.searchKey = e.target.value;
                this.loadData();
            },
        },
    };
</script>

<style lang="scss" scoped>
.history-trace-code-list-item {
    .hover-tips {
        visibility: hidden;
    }

    .history-trace-code-list-item-btn {
        visibility: hidden;
    }

    &:hover {
        .history-trace-code-list-item-btn {
            visibility: visible;
        }

        .hover-tips {
            visibility: visible;
        }
    }
}
</style>
