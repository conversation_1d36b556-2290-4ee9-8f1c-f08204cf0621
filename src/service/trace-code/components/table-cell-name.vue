<template>
    <abc-table-cell
        vertical
        align="flex-start"
        justify="center"
        :style="{
            height: '100%',padding: '0 12px',
        }"
    >
        <abc-flex align="center" style="width: 100%;" :title="trData.name">
            <abc-text v-if="trData.isCompose" style="min-width: fit-content;" :theme="isDispensedTraceCode ? 'gray-light' : ''">
                [套餐]
            </abc-text>
            <abc-text v-else-if="trData.isGift" style="min-width: fit-content;" :theme="isDispensedTraceCode ? 'gray-light' : ''">
                [赠品]
            </abc-text>
            <abc-text class="ellipsis" :theme="isDispensedTraceCode ? 'gray-light' : ''">
                {{ trData.name }}
            </abc-text>
            <abc-text
                v-if="productInfo"
                class="ellipsis"
                :theme="isDispensedTraceCode ? 'gray-light' : 'gray'"
                size="small"
                style="min-width: 80px; margin-left: 8px;"
                :title="productInfo.displaySpec"
            >
                {{ productInfo.displaySpec }}
            </abc-text>
            <abc-tag-v2
                v-if="trData.status === dispenseItemStatusEnum.RETURN"
                style="min-width: 52px;"
                theme="danger"
                size="mini"
                variant="outline"
            >
                已退药
            </abc-tag-v2>
            <abc-text
                v-if="productInfo"
                class="ellipsis"
                :theme="isDispensedTraceCode ? 'gray-light' : 'gray'"
                size="small"
                style="width: 86px; min-width: 86px; margin-left: auto; text-align: left;"
                :title="productInfo.manufacturer"
            >
                {{ productInfo.manufacturer }}
            </abc-text>
        </abc-flex>
    </abc-table-cell>
</template>

<script type="text/babel">
    import { dispenseItemStatusEnum } from 'views/pharmacy/constants';

    export default {
        name: 'TableCellName',
        props: {
            trData: {
                type: Object,
                required: true,
            },
            isDispensedTraceCode: {
                type: Boolean,
                default: false,
            },
            isNoNeedCollect: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                dispenseItemStatusEnum,
            };
        },
        computed: {
            productInfo() {
                return this.trData.productInfo;
            },
        },
        methods: {},
    };
</script>
