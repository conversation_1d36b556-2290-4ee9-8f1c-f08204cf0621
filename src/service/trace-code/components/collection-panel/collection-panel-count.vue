<template>
    <abc-table-cell
        :style="{
            height: '100%',padding: '0 12px',
        }"
    >
        <template v-if="!isDispensed">
            <abc-popover
                v-if="trData.traceableCodeList"
                theme="yellow"
                placement="top"
                trigger="hover"
                :disabled="isNoTraceCodeGoods || isChineseMedicine || isCompatibleHistoryData || !supportCollect"
                width="354px"
            >
                <div slot="reference">
                    <template v-if="isNoTraceCodeGoods || !supportCollect">
                        -
                    </template>
                    <template v-else>
                        <abc-icon
                            v-if="!isSpecificationMatch && !isNoTraceCodeGoods && !isCompatibleHistoryData"
                            icon="s-alert-small-fill"
                            color="var(--abc-color-Y2)"
                        ></abc-icon>
                        {{ getUnitCount(trData) }}
                        {{ trData.unit }}
                    </template>
                </div>
                <collection-panel-count-description
                    :tr-data="trData"
                    :support-collect="supportCollect"
                    :is-specification-match="isSpecificationMatch"
                ></collection-panel-count-description>
            </abc-popover>
        </template>
        <template v-else>
            <template v-if="!supportCollect || isNoTraceCodeGoods">
                -
            </template>
            <abc-text v-else :theme="isDispensedTraceCode ? 'gray-light' : ''">
                {{ getUnitCount(trData) }} {{ trData.unit }}
            </abc-text>
        </template>
    </abc-table-cell>
</template>

<script>
    import TraceCode from '@/service/trace-code/service';
    import { isNull } from '@/utils';
    import CollectionPanelCountDescription
        from '@/service/trace-code/components/collection-panel/collection-panel-count-description.vue';

    export default {
        name: 'CollectionPanelCount',
        components: { CollectionPanelCountDescription },
        props: {
            trData: {
                type: Object,
                required: true,
            },
            isDispensed: {
                type: Boolean,
                default: false,
            },
            isCompatibleHistoryData: {
                type: Boolean,
                default: false,
            },
            isChineseMedicine: {
                type: Boolean,
                default: false,
            },
            isNoTraceCodeGoods: {
                type: Boolean,
                default: false,
            },
            supportCollect: {
                type: Boolean,
                default: false,
            },
            isSpecificationMatch: {
                type: Boolean,
                default: false,
            },
            isDispensedTraceCode: {
                type: Boolean,
                default: false,
            },
            isNoNeedCollect: {
                type: Boolean,
                default: false,
            },
        },
        methods: {
            isNull,
            getUnitCount(item) {
                return TraceCode.getUnitCount(item);
            },
        },
    };
</script>
