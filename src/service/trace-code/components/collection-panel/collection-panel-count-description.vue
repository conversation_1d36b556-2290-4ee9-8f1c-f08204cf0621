<template>
    <div style="max-width: 354px;">
        <abc-flex vertical>
            <template v-if="hasEnableCollCheckStrictMode">
                <abc-text bold style="margin-bottom: 8px;">
                    应采说明
                </abc-text>
                <abc-text>
                    本次发药数量 = {{ collCheckStrictModeDispensingCountStr }} {{ trData._useLimitPriceTargetUnit ? '（该药品为医保目录限价药品）' : '' }}
                </abc-text>
                <abc-text>
                    系统档案规格 =
                    <abc-text>
                        {{ trData.productInfo?.pieceNum || '' }}{{ trData.productInfo?.pieceUnit || '-' }}/{{ trData.productInfo?.packageUnit || '-' }}
                    </abc-text>
                    ，医保目录规格 =
                    <abc-text>
                        {{ trData.productInfo?.shebao?.shebaoPieceNum || '' }}{{ trData.productInfo?.shebao?.shebaoPieceUnit || '-' }}/{{ trData.productInfo?.shebao?.shebaoPackageUnit || '-' }}
                    </abc-text>
                </abc-text>
                <template v-if="trData.isShebaoDismountingFlag && hasEnableDismountingMode">
                    拆零标志已勾选，无需采集追溯码
                </template>
                <template v-else>
                    <abc-text>
                        经系统换算，最小包装追溯码数量 = {{ bigShouldCollectCount }}
                    </abc-text>
                    <abc-text v-if="supportCollect && isSpecificationMatch">
                        本次应采集追溯码 = {{ displayRecommendCountStr(trData) }}
                    </abc-text>
                </template>
                <abc-tips
                    v-if="isShowTips"
                    icon
                    theme="warning"
                >
                    需采集{{ bigShouldCollectCount }}个整装追湖码，请重新采集！
                </abc-tips>
                <abc-divider margin="small" theme="dark"></abc-divider>
                <abc-text theme="gray">
                    医保要求，在结算时费用明细上传阶段必须准确传输 “最小包装追湖码数量”，并严格按照“最小包装追溯码数量”采集追溯码。
                </abc-text>
            </template>
            <template v-else>
                <abc-text v-if="supportCollect && isSpecificationMatch">
                    系统推荐采集追溯码 = {{ displayRecommendCountStr(trData) }}
                </abc-text>
                <abc-tips
                    v-else
                    icon
                    theme="warning"
                >
                    请检查规格设置、医保对码是否有误，并在库存档案进行调整，否则可能影响追溯码采集率
                </abc-tips>
                <abc-divider margin="small" theme="dark"></abc-divider>
                <abc-text theme="gray">
                    应采说明：
                </abc-text>
                <abc-text theme="gray">
                    本次发药 = {{ getUnitCount(trData) }} {{ trData.unit }} {{ trData._useLimitPriceTargetUnit ? '（该药品为医保目录限价药品）' : '' }}
                </abc-text>
                <abc-text theme="gray">
                    系统档案规格 =
                    <abc-text :theme="(supportCollect && isSpecificationMatch) ? 'black' : 'warning-light'">
                        {{ trData.productInfo?.pieceNum || '' }}{{ trData.productInfo?.pieceUnit || '-' }}/{{ trData.productInfo?.packageUnit || '-' }}
                    </abc-text>
                    ，医保目录规格 =
                    <abc-text :theme="(supportCollect && isSpecificationMatch) ? 'black' : 'warning-light'">
                        {{ trData.productInfo?.shebao?.shebaoPieceNum || '' }}{{ trData.productInfo?.shebao?.shebaoPieceUnit || '-' }}/{{ trData.productInfo?.shebao?.shebaoPackageUnit || '-' }}
                    </abc-text>
                </abc-text>
            </template>
        </abc-flex>
    </div>
</template>

<script>
    import TraceCode from '@/service/trace-code/service';

    export default {
        name: 'CollectionPanelCountDescription',
        props: {
            trData: {
                type: Object,
                required: true,
            },
            hasEnableDismountingMode: {
                type: Boolean,
                default: false,
            },
            hasEnableCollCheckStrictMode: {
                type: Boolean,
                default: false,
            },
            supportCollect: {
                type: Boolean,
                default: false,
            },
            isSpecificationMatch: {
                type: Boolean,
                default: false,
            },
            isShowTips: {
                type: Boolean,
                default: false,
            },
        },
        computed: {
            traceableCodeRule() {
                return this.trData?.traceableCodeRule ?? {};
            },
            productInfo() {
                return this.trData?.productInfo ?? {};
            },
            bigShouldCollectCount() {
                const { bigShouldCollectCount } = TraceCode.getTraceCodeShouldCollectCountInfo(this.trData);
                return bigShouldCollectCount ?? 0;
            },
            collCheckStrictModeDispensingCountStr() {
                const {
                    hisPieceCount,hisPackageCount,
                } = this.traceableCodeRule;
                const bigStr = hisPackageCount ? `${hisPackageCount}${this.productInfo.packageUnit}` : '';
                const smallStr = hisPieceCount ? `${hisPieceCount}${this.productInfo.pieceUnit}` : '';
                return bigStr + smallStr;
            },
        },
        methods: {
            getUnitCount(item) {
                return TraceCode.getUnitCount(item);
            },
            getTraceCodeShouldCollectCountInfo(trData) {
                return TraceCode.getTraceCodeShouldCollectCountInfo(trData);
            },
            displayRecommendCountStr(trData) {
                const {
                    bigShouldCollectCount,smallShouldCollectCount,
                } = this.getTraceCodeShouldCollectCountInfo(trData);
                const {
                    productInfo = {},
                } = trData;
                const {
                    packageUnit,pieceUnit,
                } = productInfo ?? {};
                const bigStr = bigShouldCollectCount ? `${bigShouldCollectCount}${packageUnit}` : '';
                const smallStr = smallShouldCollectCount ? (this.hasEnableDismountingMode ? `（拆零${smallShouldCollectCount}${pieceUnit}不采集）` : `${smallShouldCollectCount}${pieceUnit}（拆零）`) : '';
                if (this.hasEnableDismountingMode) {
                    return bigStr + smallStr;
                }
                return bigStr + ((bigStr && smallStr) ? '+ ' : '') + smallStr;
            },
        },
    };
</script>
