<template>
    <abc-table-cell
        custom
        :style="{
            'justify-content': 'flex-start',
            height: '100%',
            padding: '6px 2px',
        }"
    >
        <abc-text v-if="hasEnableDismountingMode && trData.isShebaoDismountingFlag" theme="gray-light" style="padding: 0 10px;">
            拆零发药无需采集
        </abc-text>
        <abc-list
            v-else-if="trData.traceableCodeList && trData.traceableCodeList.length"
            style="overflow: auto;"
            show-divider
            :divider-config="{
                variant: 'dashed',
                theme: 'light',
            }"
            no-close-border
            :create-key="(it)=>it.keyId || it.id || it.no"
            :data-list="trData.traceableCodeList"
            :readonly="isNoTraceCodeGoods"
            :custom-item-class="customItemClass"
        >
            <template
                #default="{
                    item, index
                }"
            >
                <abc-popover
                    width="260px"
                    placement="bottom-end"
                    trigger="hover"
                    theme="yellow"
                    :arrow-offset="300"
                    :offset="10"
                    :open-delay="500"
                    :disabled="(!item.warnInfo || !item.warnInfo.hoverTips) && (index !== 0 || !trData.warnInfo || !trData.warnInfo.hoverTips)"
                    style="width: 100%;"
                >
                    <abc-flex
                        slot="reference"
                        align="center"
                        justify="space-between"
                        style="width: 100%; height: 26px;"
                        :title="item.no || ''"
                        :data-cy="`trace-code-collect-panel-item-${item.no}`"
                    >
                        <abc-flex
                            style="flex: 1;"
                            align="center"
                            justify="space-between"
                            :gap="8"
                        >
                            <abc-flex :style="{ 'max-width': !isNoTraceCodeGoods && !isChineseMedicine && !isCompatibleHistoryData ? (hasEnableCollCheckStrictMode ? '160px' : '190px') : 'unset' }">
                                <div v-if="item.traceableCodeNoInfo && formatTraceableCode(item).start" style="margin-right: 4px; white-space: nowrap;">
                                    <abc-text :theme="codeTheme(item, trData)" data-cy="trace-code-collect-panel-code-start">
                                        {{ formatTraceableCode(item).start || '' }}
                                    </abc-text>
                                </div>
                                <div v-if="formatTraceableCode(item).end" class="ellipsis">
                                    <abc-text data-cy="trace-code-collect-panel-code-end" :theme="isDispensedTraceCode ? 'gray-light' : ''">
                                        {{ formatTraceableCode(item).end || '' }}
                                    </abc-text>
                                </div>
                            </abc-flex>

                            <template v-if="isCompatibleHistoryData">
                                <div
                                    v-if="index === currentEditIndex"
                                    v-abc-click-outside="
                                        () => {
                                            currentEditIndex = -1
                                        }
                                    "
                                >
                                    <abc-input-number
                                        v-abc-focus-selected
                                        v-abc-auto-focus
                                        :value="item.count"
                                        size="tiny"
                                        fixed-button
                                        :config="{
                                            supportZero: false,
                                            max: 99999,
                                        }"
                                        button-placement="left"
                                        @enter="currentEditIndex = -1"
                                        @change="(val)=>handleCountChange(val,item)"
                                    ></abc-input-number>
                                </div>
                                <div
                                    v-else
                                    class="trace-code-count"
                                    :class="{
                                        'is-disabled': isNoTraceCodeGoods
                                    }"
                                    style="padding: 0 8px;"
                                    @click="handleClickCount(item, index)"
                                >
                                    ×{{ item.count || 1 }}
                                </div>
                            </template>

                            <div
                                v-if="!isNoTraceCodeGoods && !isChineseMedicine && !isCompatibleHistoryData"
                                :class="{
                                    'is-disabled': isNoTraceCodeGoods,
                                    'is-exceed-limit': isUsageOverLimit(item)
                                }"
                                style="margin-right: 4px;"
                            >
                                <abc-form-item
                                    v-if="!hasEnableCollCheckStrictMode"
                                    :validate-event="validateItemUsageOverLimit"
                                    :validate-params="item"
                                    trigger="custom-active"
                                    :error-style="{
                                        'margin-left': '-115px',backgroundColor: 'var(--popover-popper-fill-color, #fffdec)'
                                    }"
                                >
                                    <abc-space is-compact>
                                        <abc-input
                                            v-abc-focus-selected
                                            :value="item.count"
                                            type="number"
                                            size="small"
                                            :disabled="disabled"
                                            :width="50"
                                            :config="{
                                                supportZero: false,
                                                max: 99999,
                                            }"
                                            :input-custom-style="{ textAlign: 'center' }"
                                            @change="(val)=>handleCountChange(val,item)"
                                        >
                                        </abc-input>
                                        <abc-select
                                            v-model="item.unit"
                                            :width="48"
                                            :inner-width="48"
                                            :disabled="isDisabledUnitSelect"
                                            size="small"
                                            @change="(val)=>handleUnitChange(val,item)"
                                        >
                                            <abc-option
                                                v-if="productInfo.pieceUnit"
                                                :value=" productInfo.pieceUnit"
                                                :label=" productInfo.pieceUnit"
                                            ></abc-option>
                                            <abc-option
                                                v-if="productInfo.packageUnit"
                                                :value=" productInfo.packageUnit"
                                                :label=" productInfo.packageUnit"
                                            ></abc-option>
                                        </abc-select>
                                    </abc-space>
                                </abc-form-item>

                                <abc-form-item
                                    v-else
                                    :error-style="{
                                        'margin-left': '-103px',backgroundColor: 'var(--popover-popper-fill-color, #fffdec)'
                                    }"
                                    trigger="custom-active"
                                    :validate-event="validateItemUsageOverLimit"
                                    :validate-params="item"
                                >
                                    <abc-space is-compact>
                                        <abc-select
                                            v-model="item.trdnFlag"
                                            :width="62"
                                            :disabled="isDisabledTrdnFlag(item)"
                                            :input-style="{
                                                textAlign: 'left',
                                            }"
                                            size="small"
                                            @change="$emit('change')"
                                        >
                                            <abc-option
                                                :value="0"
                                                label="整装"
                                            ></abc-option>
                                            <abc-option
                                                :value="1"
                                                label="拆零"
                                            ></abc-option>
                                        </abc-select>
                                        <abc-tooltip
                                            placement="top"
                                            :open-delay="500"
                                            :disabled="!!item.trdnFlag"
                                        >
                                            <abc-input
                                                v-abc-focus-selected
                                                :value="item.count"
                                                type="number"
                                                size="small"
                                                :disabled="disabled || !item.trdnFlag"
                                                :width="62"
                                                :config="{
                                                    supportZero: false,
                                                    max: 99999,
                                                }"
                                                :input-custom-style="{ textAlign: 'center' }"
                                                @change="(val)=>handleCountChange(val,item)"
                                            >
                                                <span slot="appendInner">{{ item.unit }}</span>
                                            </abc-input>
                                            <template #content>
                                                <div style="width: 290px; text-align: justify;">
                                                    不拆零发药时，相同追溯码只允许采集一个最小包装单位
                                                </div>
                                            </template>
                                        </abc-tooltip>
                                    </abc-space>
                                </abc-form-item>
                            </div>
                        </abc-flex>
                        <abc-flex
                            align="center"
                            justify="center"
                            style="min-width: 20px;"
                        >
                            <abc-text v-if="item.warnInfo && !item.warnInfo.flag" theme="warning-light">
                                {{ item.warnInfo.warnText }}
                            </abc-text>
                            <abc-text v-else-if="index === 0 && trData.warnInfo && !trData.warnInfo.flag && trData.warnInfo.warnText" theme="warning-light">
                                {{ trData.warnInfo.warnText }}
                            </abc-text>
                            <abc-delete-icon
                                v-if="!disabled && item.type !== TraceableCodeTypeEnum.NO_CODE"
                                theme="dark"
                                class="delete-icon"
                                data-cy="trace-code-collect-dialog-td-delete"
                                @delete="handleDeleteCode(trData, index)"
                            ></abc-delete-icon>
                        </abc-flex>
                    </abc-flex>
                    <div>
                        <template v-if="item.warnInfo && !item.warnInfo.flag">
                            <abc-flex v-if="item.warnInfo.warnType === TraceableCodeListItemErrorType.INCONFORMITY_BATCH" vertical>
                                <span>{{ item.warnInfo.hoverTips }}</span>
                                <abc-divider
                                    margin="small"
                                    theme="dark"
                                    variant="dashed"
                                    size="normal"
                                ></abc-divider>
                                <abc-flex v-for="batch in getFormItemBatchInfos(trData)" :key="batch.batchId" justify="space-between">
                                    <span v-if="batch.batchId">批号：{{ batch.batchId }}</span>
                                    <span v-if="batch.batchNo">生产批号：{{ batch.batchNo }}</span>
                                </abc-flex>
                            </abc-flex>
                            <template v-else>
                                {{ item.warnInfo.hoverTips }}
                            </template>
                        </template>
                        <template v-else-if="trData.warnInfo && !trData.warnInfo.flag">
                            {{ trData.warnInfo.hoverTips }}
                        </template>
                    </div>
                </abc-popover>
            </template>
        </abc-list>
        <abc-text v-else-if="isNullCodeGoods" theme="gray-light" style="padding: 0 10px;">
            无需采集
        </abc-text>
        <abc-flex
            v-else
            align="center"
            justify="space-between"
            style="width: 100%; height: 26px;"
        >
            <div>
                <abc-text v-if="!supportCollect" theme="gray-light" style="padding: 0 10px;">
                    未对码药品无需采集
                </abc-text>
                <template v-else>
                    <abc-text v-if="trData.splitZeroCodeList?.length && !isCompatibleHistoryData" theme="gray-light" style="padding: 0 8px;">
                        有拆零追溯码可用
                    </abc-text>
                </template>
            </div>
            <abc-flex v-if="supportCollect" align="center">
                <abc-popover
                    v-if="trData.warnInfo && !trData.warnInfo.flag"
                    placement="top"
                    :offset="10"
                    trigger="hover"
                    theme="yellow"
                    style="width: 100%; padding: 5px 10px;"
                >
                    <abc-text slot="reference" theme="warning-light">
                        {{ trData.warnInfo.warnText }}
                    </abc-text>
                    <div>
                        {{ trData.warnInfo.hoverTips }}
                    </div>
                </abc-popover>
            </abc-flex>
        </abc-flex>
    </abc-table-cell>
</template>

<script type="text/babel">
    import TraceCode, {
        TraceableCodeListItemErrorType, TraceableCodeTypeEnum,
    } from '@/service/trace-code/service';
    import { isChineseMedicine } from '@/filters';
    import TraceCodeUsageDetailDialog from '@/service/trace-code/components/trace-code-usage-detail-dialog';

    export default {
        name: 'TableCellTraceCodeList',
        components: {
        },
        props: {
            trData: {
                type: Object,
                required: true,
            },
            hasCodeSafeOpened: Boolean,
            hasEnableDismountingMode: {
                type: Boolean,
                default: false,
            },
            hasEnableCollCheckStrictMode: {
                type: Boolean,
                default: false,
            },
            disabled: Boolean,
            isCompatibleHistoryData: {
                type: Boolean,
                default: false,
            },
            isDispensedTraceCode: {
                type: Boolean,
                default: false,
            },
            isNoNeedCollect: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                TraceableCodeListItemErrorType,
                TraceableCodeTypeEnum,

                currentEditIndex: -1,
            };
        },
        computed: {
            productInfo() {
                return this.trData.productInfo;
            },
            supportCollect() {
                return TraceCode.supportCollect(this.productInfo);
            },
            isNoTraceCodeGoods() {
                return TraceCode.isNoTraceCodeGoods(this.productInfo);
            },
            isNullCodeGoods() {
                return TraceCode.isNullCodeGoods(this.productInfo);
            },
            isChineseMedicine() {
                return isChineseMedicine(this.productInfo);
            },
            isDisabledUnitSelect() {
                return this.disabled || this.productInfo.pieceUnit === this.productInfo.packageUnit;
            },
            traceableCodeRule() {
                return this.trData?.traceableCodeRule ?? {};
            },
        },
        methods: {
            customItemClass(item) {
                return `trace-code-list-item trace-code-list-item-${item.keyId}`;
            },
            handleClickCount(item, index) {
                if (this.disabled) return;
                if (this.isNoTraceCodeGoods) return;
                this.currentEditIndex = index;
            },
            handleDeleteCode(item, index) {
                item.traceableCodeList.splice(index, 1);
                this.$emit('change');
            },
            handleCountChange(val,item) {
                const newVal = +val ? val : item.value;
                if (!this.isCompatibleHistoryData) {
                    const {
                        packageUnit,pieceUnit,
                    } = this.productInfo;
                    if (packageUnit !== pieceUnit) {
                        if (packageUnit === item.unit) {
                            this.$set(item,'hisPackageCount',newVal);
                        } else {
                            this.$set(item,'hisPieceCount',newVal);
                        }
                    } else {
                        if (TraceCode.isShouldApplyPieceUnit(item)) {
                            this.$set(item,'hisPieceCount',newVal);
                        } else {
                            this.$set(item,'hisPackageCount',newVal);
                        }
                    }
                }
                this.$set(item,'count',newVal);
                this.$emit('change');
            },
            handleUnitChange(val,item) {
                const { packageUnit } = this.productInfo;
                if (packageUnit === val) {
                    this.$set(item,'hisPackageCount',item.count);
                    this.$set(item,'hisPieceCount',0);
                } else {
                    this.$set(item,'hisPieceCount',item.count);
                    this.$set(item,'hisPackageCount',0);
                }
                this.$emit('change');
            },
            codeTheme(item, trData) {
                const {
                    traceableCodeNoInfo,
                    type,
                } = item;
                if (this.isDispensedTraceCode) return 'gray-light';
                if (type === TraceableCodeTypeEnum.NO_CODE || traceableCodeNoInfo?.type === TraceableCodeTypeEnum.NO_CODE) return '';
                const list = this.goodsIdentificationCodeList(trData);
                return list.some((it) => it === traceableCodeNoInfo.drugIdentificationCode) ? '' : 'warning-light';
            },
            getUnitCount(item) {
                return TraceCode.getUnitCount(item);
            },
            isUsageOverLimit(item) {
                if (this.disabled) return false;
                const leftCount = TraceCode.getTraceCodeLeftCount(item) - TraceCode.getTraceCollectCodeCountBySmall(this.productInfo,item);
                return leftCount < 0;
            },
            displayFormatPieceUnit(item,productInfo) {
                const leftCount = TraceCode.getTraceCodeLeftCount(item);
                return TraceCode.displayFormatPieceUnit(leftCount ?? 0, productInfo);
            },
            formatTraceableCode(item) {
                const {
                    no,
                    traceableCodeNoInfo,
                } = item;
                const {
                    start,end,
                } = TraceCode.formatTraceableCode({
                    drugIdentificationCode: traceableCodeNoInfo?.drugIdentificationCode,
                    no,
                });
                return {
                    start,end,
                };
            },

            goodsIdentificationCodeList(item) {
                return TraceCode.getDrugIdentificationCodeList(item.productInfo);
            },

            getFormItemBatchInfos(item) {
                return TraceCode.getFormItemBatchInfos(item);
            },
            handleToDetail(item) {
                new TraceCodeUsageDetailDialog({
                    code: item,
                    traceCodeUseInfo: this.trData,
                    onSave: () => {
                        this.$emit('updateDetailSuccess');
                    },
                }).generateDialogAsync({ parent: this });
            },
            isDisabledTrdnFlag(item) {
                const {
                    hisPackageCount,hisPieceCount,
                } = this.traceableCodeRule;
                const { trdnFlag } = item;
                let flag = false;
                if (trdnFlag) {
                    flag = !hisPackageCount;
                } else {
                    flag = !hisPieceCount;
                }
                return this.disabled || (this.hasEnableDismountingMode && !item.trdnFlag) || flag;
            },
            validateItemUsageOverLimit(value, callback, item) {
                if (this.disabled) {
                    callback({ validate: true });
                    return;
                }
                const leftCount = TraceCode.getTraceCodeLeftCount(item) - TraceCode.getTraceCollectCodeCountBySmall(this.productInfo,item);
                if (leftCount < 0) {
                    callback({
                        validate: false,
                        validateComponent: () => (
                            <abc-flex gap="4" vertical align="end">
                                <abc-tips icon theme="warning">追溯码使用超上限（剩余{ this.displayFormatPieceUnit(item,this.productInfo) }，本次使用{ item.count }{ item.unit }）</abc-tips>
                                <abc-button variant="text" size="small" onClick={() => this.handleToDetail(item)}>
                                    点此修正追溯码剩余量
                                </abc-button>
                            </abc-flex>
                        ),
                    });
                }
            },
        },
    };
</script>

<style lang="scss">
@import 'src/styles/abc-common.scss';

.trace-code-list-item-option {
    left: -19px !important;
}

.trace-code-list-item {
    .delete-icon {
        visibility: hidden;
    }

    &:hover {
        .warn-icon {
            display: none;
        }

        .delete-icon {
            visibility: visible;
        }
    }

    .trace-code-count {
        min-width: 42px;
        margin-right: 4px;
        margin-left: 8px;
        color: var(--abc-color-T2);
        text-align: center;
        white-space: nowrap;
        border: 1px solid var(--abc-color-P8);
        border-radius: var(--abc-border-radius-mini);

        &:not(.is-disabled,.is-exceed-limit):hover {
            background: var(--abc-color-P1);
            border: 1px solid var(--abc-color-P1);
        }

        &.is-exceed-limit {
            background: var(--abc-color-Y4, #fff4ea);
            border: var(--abc-border-1, 1px) solid var(--abc-color-Y2, #ff9933);
        }
    }
}
</style>
