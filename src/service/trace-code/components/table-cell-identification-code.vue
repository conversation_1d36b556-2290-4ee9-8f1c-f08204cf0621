<template>
    <abc-table-cell
        :style="{
            height: '100%',padding: '0 12px',
        }"
    >
        <abc-popover
            v-if="goodsIdentificationCodeList.length > 0"
            theme="yellow"
            placement="top"
            :disabled="isNoTraceCodeGoods || isShowRiskPopover"
            trigger="hover"
            :open-delay="500"
            style="width: 100%;"
        >
            <div slot="reference">
                <abc-flex justify="space-between" align="center">
                    <abc-text class="ellipsis" :title="goodsIdentificationCodeList[0]" :theme="isDispensedTraceCode ? 'gray-light' : ''">
                        {{ goodsIdentificationCodeList[0] }}
                    </abc-text>
                    <abc-text
                        v-if="goodsIdentificationCodeList.length > 1"
                        style="white-space: nowrap;"
                        :theme="isDispensedTraceCode ? 'gray-light' : 'success-light'"
                    >
                        +{{ goodsIdentificationCodeList.length - 1 }}
                    </abc-text>
                    <no-code-apply-popover
                        v-if="productInfo.applyNoCodeInfo && checkNeedApplySichuanNoCode(productInfo.shebao)"
                        :status="productInfo.applyNoCodeInfo.applyStatus"
                    ></no-code-apply-popover>
                    <identification-code-risk-popover v-if="isShowRiskPopover" :product-info="productInfo" :code-list="goodsIdentificationCodeList"></identification-code-risk-popover>
                </abc-flex>
            </div>
            <div>
                <abc-flex vertical>
                    <abc-text style="margin-bottom: 6px;" :theme="isDispensedTraceCode ? 'gray-light' : ''">
                        已关联的追溯码产品标识码：
                    </abc-text>
                    <abc-text
                        v-for="(code, index) in goodsIdentificationCodeList"
                        :key="index"
                        :theme="isDispensedTraceCode ? 'gray-light' : 'gray'"
                    >
                        <span style="display: inline-block; min-width: 21px;">{{ index + 1 }}.</span>{{ code }}
                    </abc-text>
                </abc-flex>
            </div>
        </abc-popover>
        <abc-text v-else :theme="isDispensedTraceCode ? 'gray-light' : 'gray'">
            未关联
        </abc-text>
    </abc-table-cell>
</template>

<script type="text/babel">
    import TraceCode from '@/service/trace-code/service';
    import NoCodeApplyPopover from '@/service/trace-code/components/no-code-apply-popover.vue';
    import useSichuanNoCode from 'views/inventory/goods/archives/hook/useSichuanNoCode';
    import IdentificationCodeRiskPopover
        from '@/service/trace-code/dialog-trace-code-standard-match/identification-code-risk-popover.vue';

    export default {
        name: 'TableCellIdentificationCode',
        components: {
            IdentificationCodeRiskPopover, NoCodeApplyPopover,
        },
        props: {
            productInfo: {
                type: Object,
                required: true,
            },
            isDispensedTraceCode: {
                type: Boolean,
                default: false,
            },
            isNoNeedCollect: {
                type: Boolean,
                default: false,
            },
        },
        setup() {
            const { checkNeedApplySichuanNoCode } = useSichuanNoCode();
            return {
                checkNeedApplySichuanNoCode,
            };
        },
        computed: {
            isNoTraceCodeGoods() {
                return TraceCode.isNoTraceCodeGoods(this.productInfo);
            },
            goodsIdentificationCodeList() {
                return TraceCode.getDrugIdentificationCodeList(this.productInfo);
            },
            isSichuan() {
                return this.$abcSocialSecurity.config.isSichuan;
            },
            isShowRiskPopover() {
                const {
                    sanMaHeYiIdentificationCodeList, shebao,
                } = this.productInfo ?? {};
                const { nationalCode } = shebao ?? {};
                return nationalCode && Array.isArray(sanMaHeYiIdentificationCodeList) && this.isExistRiskCode;
            },
            isExistRiskCode() {
                const {
                    sanMaHeYiIdentificationCodeList,
                } = this.productInfo ?? {};
                return this.goodsIdentificationCodeList.some((code) => !sanMaHeYiIdentificationCodeList.includes(code));
            },
        },
    };
</script>
