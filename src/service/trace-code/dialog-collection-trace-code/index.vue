<template>
    <abc-dialog
        v-if="visible"
        v-model="visible"
        title="追溯码采集"
        class="abc-collection-trace-code-dialog"
        append-to-body
        disabled-keyboard
        data-cy="trace-code-collect-dialog"
        size="hugely"
        responsive
    >
        <template #top-extend>
            <abc-tips-card-v2
                v-if="hasEnableCollCheckStrictMode"
                theme="primary"
                title="依码支付重要提醒"
            >
                机构在医保结算时必须确定本次<abc-text bold>
                    应采组合（整装+拆零数量）
                </abc-text>，以便算出<abc-text bold>
                    “本次应采追溯码数量”
                </abc-text>上传医保完成结算。
            </abc-tips-card-v2>
            <trace-code-activation-card v-else></trace-code-activation-card>
        </template>
        <abc-form ref="form">
            <collection-panel
                key="collection-panel"
                ref="collectionPanel"
                class="dialog-content"
                :form-items="formItems"
                :filter-item-status="filterItemStatus"
                :dispensed-form-items="dispensedFormItems"
                :confirm-inner-set="confirmInnerSet"
                :disabled="disabled"
                :is-re-report-mode="isReReportMode"
                :need-init-validate="needInitValidate"
                :callback-confirm="callbackConfirm"
                :patient-order-id="patientOrderId"
                :scene-type="sceneType"
                :save-loading.sync="btnLoading"
                :is-selected-social-pay="isSelectedSocialPay"
                :is-disabled-actual-count="isDisabledActualCount"
                @init-count="handleInitCount"
            ></collection-panel>
        </abc-form>

        <div slot="footer" class="dialog-footer">
            <abc-space v-if="shouldCollectItem" style="font-size: 14px;">
                <abc-text theme="gray">
                    采集项目
                </abc-text>
                <abc-text :theme="collectedItem < shouldCollectItem ? 'warning-light' : 'success-light'" bold>
                    {{ collectedItem }}/{{ shouldCollectItem }}
                </abc-text>
            </abc-space>
            <template v-if="disabled && !isReReportMode">
                <div style="flex: 1; padding-right: 8px;">
                </div>
                <abc-tooltip content="仅结算当月可进行修改" :disabled="canReReportTraceCode">
                    <div style="margin-right: 8px;">
                        <abc-button
                            variant="ghost"
                            theme="primary"
                            :disabled="!canReReportTraceCode"
                            :loading="btnLoading"
                            data-cy="trace-code-collect-modify-button"
                            @click.stop="onEdit"
                        >
                            修改追溯码
                        </abc-button>
                    </div>
                </abc-tooltip>
                <abc-button
                    type="blank"
                    data-cy="trace-code-collect-close-button"
                    @click.stop="close"
                >
                    关闭
                </abc-button>
            </template>
            <template v-else>
                <div style="flex: 1; padding-right: 8px;">
                </div>
                <abc-tooltip v-if="isExistDispensedItems && !isReReportMode" content="仅结算当月可进行修改" :disabled="canReReportTraceCode">
                    <div style="margin-right: 8px;">
                        <abc-button
                            variant="ghost"
                            theme="primary"
                            :loading="btnLoading"
                            :disabled="!canReReportTraceCode"
                            data-cy="trace-code-collect-modify-button"
                            @click.stop="onEdit"
                        >
                            修改追溯码
                        </abc-button>
                    </div>
                </abc-tooltip>
                <template v-if="isEnableTraceTempSave && !isReReportMode">
                    <abc-button
                        :loading="btnLoading"
                        :min-width="88"
                        data-cy="trace-code-collect-save-button"
                        @click.stop="confirm"
                    >
                        完成
                    </abc-button>
                    <abc-button
                        variant="ghost"
                        theme="primary"
                        :loading="btnLoading"
                        data-cy="trace-code-collect-temp-save-button"
                        @click.stop="confirm(false,'tempSave')"
                    >
                        暂存
                    </abc-button>
                </template>
                <abc-button
                    v-else
                    :loading="btnLoading"
                    data-cy="trace-code-collect-save-button"
                    @click.stop="confirm"
                >
                    保存
                </abc-button>
                <abc-button
                    type="blank"
                    data-cy="trace-code-collect-cancel-button"
                    @click.stop="close"
                >
                    取消
                </abc-button>
            </template>
        </div>
    </abc-dialog>
</template>

<script type="text/babel">
    import CollectionPanel from '@/service/trace-code/components/collection-panel/index.vue';
    import TraceCode from '@/service/trace-code/service';
    import { dispenseItemStatusEnum } from 'views/pharmacy/constants';
    import TraceCodeActivationCard from '@/service/trace-code/components/trace-code-activation-card.vue';

    export default {
        name: 'AbcTraceCodeDialog',
        components: {
            TraceCodeActivationCard,
            CollectionPanel,
        },
        props: {
            // 平铺的 chargeFromItems、dispensingFormItems 列表结构
            formItems: {
                type: Array,
                default: () => [],
                required: true,
            },
            // 结构同上，已发药的列表
            dispensedFormItems: {
                type: Array,
                default: () => [],
            },
            // 确定后内部直接回写追溯码
            confirmInnerSet: {
                type: Boolean,
                default: true,
            },
            disabled: Boolean,
            onConfirm: Function,
            onClose: Function,
            filterItemStatus: Array,
            // 是否需要初始化校验
            needInitValidate: {
                type: Boolean,
                default: true,
            },
            patientOrderId: {
                type: String,
                default: '',
            },
            sceneType: {
                type: String,
                default: '',
            },
            isSelectedSocialPay: {
                type: Boolean,
                default: false,
            },
            canReReportTraceCode: {
                type: Boolean,
                default: false,
            },
            isEnableTraceTempSave: {
                type: Boolean,
                default: false,
            },
            isDisabledActualCount: {
                type: Boolean,
                default: false,
            },
        },
        data() {
            return {
                visible: false,
                btnLoading: false,
                isStrictCount: false,

                shouldCollectItem: 0,
                shouldCollectCount: 0,
                collectedItem: 0,
                collectedCount: 0,
                collectCountError: false,

                isReReportMode: false, //补录模式
                hasEnableCollCheckStrictMode: false,
            };
        },
        computed: {
            isExistDispensedItems() {
                return this.dispensedFormItems?.some((item) => item.status === dispenseItemStatusEnum.DISPENSED);
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
        },
        created() {
            this.hasEnableCollCheckStrictMode = TraceCode.hasEnableCollCheckStrictMode;
            this.isStrictCount = TraceCode.isStrictCount;
        },
        destroyed() {
            this.onClose && this.onClose();
        },
        methods: {
            handleInitCount(data) {
                this.shouldCollectItem = data.shouldCollectItem;
                this.shouldCollectCount = data.shouldCollectCount;
                this.collectedItem = data.collectedItem;
                this.collectedCount = data.collectedCount;
                this.collectCountError = data.collectCountError;
            },
            async callbackConfirm(flatFormItems,action) {
                this.btnLoading = true;
                this.onConfirm && await this.onConfirm(flatFormItems,this.isReReportMode ? 'reReport' : action);
                this.btnLoading = false;
                this.visible = false;
            },
            async confirm(isNeedValidate = true,action) {
                this.btnLoading = true;
                try {
                    if (isNeedValidate) {
                        this.$refs.form.validate(async (valid) => {
                            if (valid) {
                                await this.$refs.collectionPanel.confirm(isNeedValidate,action);
                            }
                        });
                    } else {
                        await this.$refs.collectionPanel.confirm(isNeedValidate,action);
                    }
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
            close() {
                this.visible = false;
            },
            onEdit() {
                this.isReReportMode = true;
                this.$nextTick(() => {
                    this.disabled = false;
                    this.$refs.collectionPanel && this.$refs.collectionPanel.initHandler();
                });
            },
            destroyElement() {
                this.$destroy();
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>
