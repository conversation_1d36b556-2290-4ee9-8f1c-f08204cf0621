<template>
    <abc-modal
        v-if="visible"
        v-model="visible"
        class="abc-trace-code-match-dialog"
        data-cy="trace-code-select-goods-dialog"
        append-to-body
        :need-high-level="false"
        size="xlarge"
        disabled-keyboard
        :show-footer="false"
    >
        <abc-layout class="dialog-content">
            <abc-section style="text-align: center;">
                <abc-text bold size="large">
                    请勾选该追溯码对应的发药项目
                </abc-text>
            </abc-section>
            <abc-section style="margin-top: 24px;">
                <abc-card background="gray" radius-size="small" style="padding: 16px 0;">
                    <abc-flex justify="center" style="width: 100%;">
                        <abc-space>
                            <abc-text bold :size="traceCode.length > 50 ? 'large' : 'xlarge'">
                                {{ viewCode.start }}
                            </abc-text>
                            <abc-text
                                bold
                                theme="gray"
                                :size="traceCode.length > 50 ? 'large' : 'xlarge'"
                            >
                                {{ viewCode.end }}
                            </abc-text>
                        </abc-space>
                    </abc-flex>
                    <template v-if="goodsInfoStr">
                        <abc-flex justify="center" style="width: 100%; margin-top: 4px;">
                            已绑定：{{ goodsInfoStr }}
                        </abc-flex>
                        <abc-divider></abc-divider>
                        <abc-flex justify="center" style="width: 100%;">
                            <abc-tips
                                icon
                                theme="warning"
                                size="medium"
                            >
                                请确认是否采集错误，继续采集可能会有被监管机构判定为“串换销售”的风险。
                            </abc-tips>
                        </abc-flex>
                    </template>
                </abc-card>
            </abc-section>
            <abc-section style="margin-top: 12px;">
                <abc-radio-group v-model="curSelectKeyId">
                    <abc-table
                        ref="abcTable"
                        :render-config="renderConfig"
                        :data-list="curFormItems"
                        cell-size="large"
                        style="height: 320px;"
                        tr-click-trigger-checked
                        :fixed-tr-height="false"
                        :show-checked="false"
                        :need-selected="false"
                        :custom-tr-data-cy="customTrDataCy"
                        @handleClickTr="handleClickTr"
                    >
                        <template #radio="{ trData }">
                            <abc-table-cell>
                                <abc-radio :label="trData.keyId">
&nbsp;
                                </abc-radio>
                            </abc-table-cell>
                        </template>
                        <template #name="{ trData }">
                            <abc-table-cell
                                vertical
                                align="flex-start"
                                justify="center"
                            >
                                <abc-flex>
                                    <template v-if="trData.isCompose">
                                        [套餐]
                                    </template>
                                    {{ trData.name || (trData.productInfo && trData.productInfo.displayName) }}
                                </abc-flex>
                                <abc-flex v-if="trData.productInfo" style="width: 100%;">
                                    <abc-text
                                        class="ellipsis"
                                        theme="gray"
                                        size="small"
                                        style="margin-right: 8px;"
                                        :title="trData.productInfo.displaySpec"
                                    >
                                        {{ trData.productInfo.displaySpec }}
                                    </abc-text>
                                    <abc-text
                                        class="ellipsis"
                                        theme="gray"
                                        size="small"
                                        style="min-width: 86px;"
                                        :title="trData.productInfo.manufacturer"
                                    >
                                        {{ trData.productInfo.manufacturer }}
                                    </abc-text>
                                </abc-flex>
                            </abc-table-cell>
                        </template>
                        <template #drugIdentificationCode="{ trData }">
                            <table-cell-identification-code :product-info="trData.productInfo"></table-cell-identification-code>
                        </template>
                        <template #handle="{ trData }">
                            <abc-table-cell v-if="curSelectKeyId === trData.keyId">
                                <abc-text>
                                    关联产品标识码，采集追溯码自动识别商品
                                </abc-text>
                            </abc-table-cell>
                        </template>
                    </abc-table>
                </abc-radio-group>
            </abc-section>
            <abc-section style="margin-top: 24px;">
                <abc-flex justify="center" style="width: 100%;">
                    <abc-space>
                        <abc-button
                            :disabled="!curSelectKeyId"
                            size="large"
                            :loading="btnLoading"
                            style="min-width: 116px;"
                            @click="confirm"
                        >
                            确定
                        </abc-button>
                        <abc-button
                            size="large"
                            variant="ghost"
                            style="min-width: 116px;"
                            @click="close"
                        >
                            取消
                        </abc-button>
                    </abc-space>
                </abc-flex>
            </abc-section>
        </abc-layout>
    </abc-modal>
</template>

<script type="text/babel">
    import Clone from 'utils/clone';
    import GoodsAPIV3 from 'api/goods/index-v3';
    import TraceCode, {
        TraceableCodeOpTypeEnum, TraceableCodeTypeEnum,
    } from '@/service/trace-code/service';
    import TableCellIdentificationCode from '@/service/trace-code/components/table-cell-identification-code.vue';
    import {
        DialogTraceCodeStandardMatch,
    } from '@/service/trace-code/dialog-trace-code-standard-match/dialog-trace-code-standard-match';

    export default {
        name: 'AbcTraceCodeMatchDialog',
        components: {
            TableCellIdentificationCode,
        },
        props: {
            // 平铺的 chargeFromItems、dispensingFormItems 列表结构
            formItems: {
                type: Array,
                default: () => [],
                required: true,
            },
            // 药品标识码
            traceableCodeNoInfo: {
                type: Object,
            },
            // 追溯码
            traceCode: String,
            // 追溯码匹配到的goods
            goodsInfo: Object,
            onConfirm: Function,
            onClose: Function,
        },
        data() {
            return {
                visible: false,
                btnLoading: false,
                dataList: [],
                curFormItems: Clone(this.formItems),
                curSelectKeyId: null,
            };
        },
        computed: {
            viewCode() {
                return TraceCode.formatTraceableCode({
                    drugIdentificationCode: this.drugIdentificationCode,
                    no: this.traceCode,
                });
            },
            drugIdentificationCode() {
                const {
                    drugIdentificationCode,
                } = this.traceableCodeNoInfo || {};
                return drugIdentificationCode || '';
            },
            goodsInfoStr() {
                if (!this.goodsInfo) {
                    return '';
                }
                const {
                    name,
                    displayName,
                    displaySpec,
                    manufacturer,
                } = this.goodsInfo;
                return `${displayName || name} ${displaySpec || ''} ${manufacturer || ''}`;
            },
            renderConfig() {
                const list = [
                    {
                        'style': {
                            'flex': 'none',
                            'width': '40px',
                            'min-width': '40px',
                        },
                        'key': 'radio',
                        'label': '',
                    },
                    {
                        key: 'name',
                        label: '发药项目',
                        style: {
                            flex: 1,
                            width: '0',
                        },
                    },
                    {
                        key: 'drugIdentificationCode',
                        label: '产品标识码',
                        style: {
                            width: '96px',
                            maxWidth: '96px',
                        },
                    },
                    {
                        key: 'handle',
                        label: '系统处理',
                        style: {
                            width: '298px',
                            maxWidth: '298px',
                            minWidth: '298px',
                            paddingLeft: 'var(--abc-table-cell-padding-large)',
                        },
                    },
                ];
                return {
                    hasInnerBorder: false,
                    list,
                };
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.onClose && this.onClose();
                    this.destroyElement();
                }
            },
        },
        created() {
            this.curFormItems.sort((a,b) => {
                let aVal = 0;
                if (TraceCode.isNoTraceCodeGoods(a.productInfo)) {
                    aVal = 1;
                } else if (TraceCode.getDrugIdentificationCodeList(a.productInfo).length) {
                    aVal = 2;
                }
                let bVal = 0;
                if (TraceCode.isNoTraceCodeGoods(b.productInfo)) {
                    bVal = 1;
                } else if (TraceCode.getDrugIdentificationCodeList(b.productInfo).length) {
                    bVal = 2;
                }
                return aVal - bVal;
            });
        },
        methods: {
            customTrDataCy(item) {
                return `trace-code-select-goods-dialog-tr-${item.name}`;
            },
            handleOpen() {

            },
            handleClickTr(item) {
                this.curSelectKeyId = item.keyId;
            },
            isNoTraceCodeGoods(item) {
                return TraceCode.isNoTraceCodeGoods(item.productInfo);
            },
            getDrugIdentificationCode(productInfo) {
                if (TraceCode.isNoTraceCodeGoods(productInfo)) return false;
                return TraceCode.getDrugIdentificationCode(productInfo);
            },
            async confirm() {
                if (!this.curSelectKeyId) return;
                try {
                    const res = this.curFormItems.find((it) => it.keyId === this.curSelectKeyId);
                    /**
                     * @desc 当扫的追溯吗没有绑定药品
                     * <AUTHOR> Yang
                     * @date 2024-08-29 16:00:47
                    */
                    if (!this.goodsInfoStr) {
                        const traceCodeNoCheckValid = await this.checkTraceCodeNoList(this.drugIdentificationCode, res.productInfo);
                        if (!traceCodeNoCheckValid) {
                            return;
                        }
                        this.btnLoading = true;
                        await GoodsAPIV3.boundIdentificationCode(res.productId, {
                            no: this.drugIdentificationCode,
                            opType: TraceableCodeOpTypeEnum.BIND,
                        });
                        if (res.productInfo.traceableCodeNoInfoList) {
                            res.productInfo.traceableCodeNoInfoList = res.productInfo.traceableCodeNoInfoList.filter((it) => {
                                return it.type !== TraceableCodeTypeEnum.NO_CODE;
                            });

                            if (this.traceableCodeNoInfo) {
                                res.productInfo.traceableCodeNoInfoList.push(this.traceableCodeNoInfo);
                            }
                        } else {
                            if (this.traceableCodeNoInfo) {
                                this.$set(res.productInfo, 'traceableCodeNoInfoList', [
                                    this.traceableCodeNoInfo,
                                ]);
                            }
                        }
                    }
                    this.onConfirm && this.onConfirm(res);
                    this.visible = false;
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },

            async checkTraceCodeNoList(code, goodsInfo) {
                return new Promise((resolve) => {
                    const {
                        sanMaHeYiIdentificationCodeList, shebao, medicineCadn, barCode,traceableCodeNoInfoList,
                    } = goodsInfo ?? {};
                    const { nationalCode } = shebao ?? {};
                    if (nationalCode && Array.isArray(sanMaHeYiIdentificationCodeList) && !sanMaHeYiIdentificationCodeList.includes(code)) {
                        const onConfirm = () => {
                            resolve(true);
                        };
                        const onCancel = () => {
                            resolve(false);
                        };
                        new DialogTraceCodeStandardMatch({
                            sanMaHeYiIdentificationCodeList,
                            shebao,
                            medicineCadn,
                            barCode,
                            codeList: traceableCodeNoInfoList.map((x) => x.drugIdentificationCode).concat([code]),
                            onConfirm,
                            onCancel,
                        }).generateDialogAsync({ parent: this });
                    } else {
                        resolve(true);
                    }
                });
            },

            close() {
                this.visible = false;
            },
            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },
        },
    };
</script>

