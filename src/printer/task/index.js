import clone from 'utils/clone';
import PrintManager from '../manager/print-manager.js';
import Logger from 'utils/logger.js';
import { PrintMode } from '@/printer/constants';

export default class PrinterTask {
    constructor(props) {
        this.id = Date.now();
        this.templateKey = props.templateKey;
        this.template = '';
        this.printConfig = props.printConfig;
        this.data = props.data;
        this.printConfigKey = props.printConfigKey; // 打印机配置key
        this.extra = props.extra;
        // 打印份数，默认一份
        this.printCopies = props.printCopies || 1;
        if (!this.printConfig.offset && this.printConfig.deviceName && this.printConfig.key === 'fee-bill') {
            if (props.mode === PrintMode.Electron || PrintManager.getInstance().isEnableElectronPrint()) {
                this.printConfig.offset = {
                    left: 0,
                    top: 0,
                };
            } else {
                this.printConfig.offset = clone(window.AbcPackages.AbcPrint.recommendOffsetMatcher(this.printConfig.deviceName, this.printConfig.pageSize));

            }
        }
        this.AbcPrintInstance = null;
        this.staticRenderConfig = [];
        this.renderConfig = [];
        this.mode = props.mode;
        this.matchTemplateCallback = props.matchTemplateCallback;
        this.onPrintSuccess = props.onPrintSuccess;
        this.onPrintLog = props.onPrintLog;
        this.onAddPrintCount = props.onAddPrintCount;

        // 打印日志
        this.reportLogData = props.reportLogData;
        this.needReportLog = props.needReportLog;

        if (props.extra?.defaultPageSize) {
            this.printConfig.pageSize = props.extra.defaultPageSize;
        }

        if (props.extra?.defaultPageSizeReduce) {
            this.printConfig.pageSizeReduce = props.extra.defaultPageSizeReduce;
        }

        if (props.extra?.defaultPageOrient) {
            this.printConfig.orient = props.extra.defaultPageOrient;
        }

        if (!this.data) {
            Logger.reportAnalytics('printer_task_error', {
                info: '没有data',
                ...(props || {}),
            });
            throw new Error('需要指定打印数据: data');
        }
        if (!this.templateKey) {
            Logger.reportAnalytics('printer_task_error', {
                info: '没有templateKey',
                ...(props || {}),
            });
            throw new Error('需要指定打印对应的模板: templateKey');
        }
        if (!this.printConfig) {
            Logger.reportAnalytics('printer_task_error', {
                info: '没有printConfig',
                ...(props || {}),
            });
            throw new Error('需要指定打印对应的配置: printConfig');
        }
        this.generateTaskChangeHandler();
        this.getPageWidthAndHeight();
    }

    async initAbcPrintInstance() {
        this.AbcPrintInstance = new window.AbcPackages.AbcPrint({
            template: this.templateKey,
            page: {
                size: this.printConfig.pageSize,
                orientation: this.printConfig.orient,
                pageHeightLevel: this.printConfig.pageHeightLevel,
                pageSizeReduce: this.printConfig.pageSizeReduce,
            },
            originData: this.data,
            extra: this.extra,
            matchTemplateCallback: this.matchTemplateCallback,
            needReportLog: this.needReportLog,
            reportLogData: this.reportLogData,
        });

        await this.AbcPrintInstance.init(false);
        this.staticRenderConfig = await this.AbcPrintInstance.getStaticRenderConfig() || [];
        this.renderConfig = clone(this.staticRenderConfig);
    }

    /**
     * @desc 根据id获取template，真实需要渲染时再调用
     * <AUTHOR>
     * @date 2021-08-18 18:54:34
     */
    async getTemplateStr(options) {
        const currentPage = PrintManager
            .getInstance()
            .abcClientManager
            .getPageByDeviceAndPageSize(this.printConfig.deviceName, this.printConfig.pageSize);

        const {
            customStyles = {}, isPreview = false,
            isGetTemplate = false,
        } = options || {};

        if (this.needReportLog && this.reportLogData) {
            Logger.report({
                scene: this.reportLogData.scene,
                data: {
                    info: 'printTask 获取 getTemplateStr',
                    data: {
                        options: JSON.stringify(options),
                        printConfig: JSON.stringify(this.printConfig),
                        keyId: this.reportLogData.keyId,
                        currentPage: JSON.stringify(currentPage),
                        templateStr: this.template,
                    },
                },
            });
        }

        this.AbcPrintInstance.update({
            template: this.templateKey,
            page: {
                size: this.printConfig.pageSize,
                orientation: this.printConfig.orient,
                pageHeightLevel: this.printConfig.pageHeightLevel,
                pageSizeReduce: this.printConfig.pageSizeReduce,
                customStyles,
                width: currentPage?.paper?.width,
                height: currentPage?.paper?.height,
            },
            originData: this.data,
            extra: this.extra,
            matchTemplateCallback: this.matchTemplateCallback,
            needReportLog: this.needReportLog,
            reportLogData: this.reportLogData,
        });
        await this.AbcPrintInstance.init();
        await this.AbcPrintInstance.setRenderConfig(this.renderConfig);

        if (this.needReportLog && this.reportLogData) {
            Logger.report({
                scene: this.reportLogData.scene,
                data: {
                    info: 'printTask 获取 getTemplateStr, abcprint触发update之后',
                    data: {
                        printConfig: JSON.stringify(this.printConfig),
                        keyId: this.reportLogData.keyId,
                        templateStr: this.template,
                    },
                },
            });
        }
        if (isGetTemplate) {
            const templateStr = await this.AbcPrintInstance.split({
                keepPageWidth: this.mode === PrintMode.Electron,
            });
            return templateStr;
        }
        this.template = isPreview ? await this.AbcPrintInstance.splitPreview() : await this.AbcPrintInstance.split({
            keepPageWidth: this.mode === PrintMode.Electron,
        });
    }

    generateTaskChangeHandler() {
        this.changeHandler = async (printConfig, templateKey, options) => {
            this.printConfig = printConfig;
            if (templateKey) {
                await this.getTemplateStr(options);
            }
        };
    }

    getPageWidthAndHeight() {
        const {
            pageSize,
            pageHeightLevel,
        } = this.printConfig;

        if (PrintManager.getInstance().isEnableElectronPrint()) {
            let currentPage = PrintManager.getInstance()
                .abcClientManager
                .getPageListByDeviceName(this.printConfig.deviceName)
                .find((page) => page.name === this.printConfig.pageSize);

            if (!currentPage) {
                currentPage = this.AbcPrintInstance?.matchedTemplateItem?.pages?.[0];
            }
            this.printConfig.pageWidth = currentPage?.paper?.width;
            this.printConfig.pageHeight = currentPage?.paper?.height;
        }

        if (pageSize && window.AbcPackages.AbcPrint.AbcPageSizeMap) {
            const pageSizeMap = window.AbcPackages.AbcPrint.AbcPageSizeMap;
            for (const key in pageSizeMap) {
                if (pageSizeMap.hasOwnProperty(key)) {
                    const pageInfo = pageSizeMap[key];
                    if (pageInfo.name === pageSize) {
                        this.printConfig.pageWidth = pageSizeMap[key].width;
                        this.printConfig.pageHeight = pageSizeMap[key].height;
                        const levels = pageInfo.heightLevels;
                        if (levels && pageHeightLevel) {
                            for (let i = 0; i < levels.length; i++) {
                                if (levels[i].name === pageHeightLevel) {
                                    this.printConfig.pageWidth = levels[i].width;
                                    this.printConfig.pageHeight = levels[i].height;
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

