import vueStore from '@/store';
//  慢病打印
import { ABCPrintConfigKeyMap } from '@/printer/constants.js';
import AbcPrinter from '@/printer/index.js';
import { getViewDistributeConfig } from '@/views-distribute/utils.js';
export function getAbcPrintOptions(type, data, extra) {
    if (!data) {
        return null;
    }
    const options = getViewDistributeConfig().Print.printOptions;
    const DispensingTicket = 1; // 发药小票打印
    const MedicineTagCustomized = 'custom'; // 用药标签定制版
    const CashierA5 = 2; // 收费单打印
    let printOption = null;
    const dispensingConfig = vueStore.getters.printGlobalConfig.dispensing || {};
    const medicineTagConfig = vueStore.getters.printGlobalConfig.medicineTag || {};
    const cashierConfig = vueStore.getters.printGlobalConfig.cashier || {};
    for (const key in options) {
        if (type === options[key].value && options[key].templateKeyTwo) {
            if (type === '用药标签') {
                printOption = {
                    templateKey: medicineTagConfig.selectedOption === MedicineTagCustomized ? window.AbcPackages.AbcTemplates[options[key].templateKeyTwo] : window.AbcPackages.AbcTemplates[options[key].templateKey],
                    printConfigKey: ABCPrintConfigKeyMap[options[key].printConfigKey],
                    data,
                };
            } else if (type === '发药小票') {
                printOption = {
                    templateKey: dispensingConfig.format === DispensingTicket ? window.AbcPackages.AbcTemplates[options[key].templateKey] : window.AbcPackages.AbcTemplates.dispenseA5,
                    printConfigKey: ABCPrintConfigKeyMap[options[key].printConfigKey],
                    data,
                };
            } else if (type === '退药小票') {
                printOption = {
                    templateKey: dispensingConfig.format === DispensingTicket ? window.AbcPackages.AbcTemplates[options[key].templateKey] : window.AbcPackages.AbcTemplates.dispenseA5,
                    printConfigKey: ABCPrintConfigKeyMap[options[key].printConfigKey],
                    data,
                    extra: {
                        isUndispense: true,
                    },
                };
            } else {
                printOption = {
                    templateKey: cashierConfig.format === CashierA5 ? window.AbcPackages.AbcTemplates[options[key].templateKeyTwo] : window.AbcPackages.AbcTemplates[options[key].templateKey],
                    printConfigKey: ABCPrintConfigKeyMap[options[key].printConfigKey],
                    data,
                };
            }
        } else {
            if (type === options[key].value && options[key].templateKey && options[key].printConfigKey) {
                printOption = {
                    templateKey: window.AbcPackages.AbcTemplates[options[key].templateKey],
                    printConfigKey: ABCPrintConfigKeyMap[options[key].printConfigKey],
                    data,
                    extra: {
                        pdfOptions: {
                            filename: options[key].filename,
                        },
                    },
                };
                if (extra) {
                    Object.assign(printOption, {
                        extra,
                    });
                }
            }
        }

    }
    return printOption;
}

export async function printChronicCareHandler(data, finishedHandler) {
    if (!data) {
        console.warn('慢病打印没有数据');
        return;
    }
    await AbcPrinter.abcPrint({
        templateKey: window.AbcPackages.AbcTemplates.chronicCare,
        printConfigKey: ABCPrintConfigKeyMap.chronicCare,
        data,
    });

    if (typeof finishedHandler === 'function') {
        finishedHandler();
    }
}
