export const NUMBER_ICONS = Object.freeze([
    '',
    '①',
    '②',
    '③',
    '④',
    '⑤',
    '⑥',
    '⑦',
    '⑧',
    '⑨',
    '⑩',
    '⑪',
    '⑫',
    '⑬',
    '⑭',
    '⑮',
    '⑯',
    '⑰',
    '⑱',
    '⑲',
    '⑳',
    '㉑',
    '㉒',
    '㉓',
    '㉔',
    '㉕',
    '㉖',
    '㉗',
    '㉘',
    '㉙',
    '㉚',
    '㉛',
    '㉜',
    '㉝',
    '㉞',
    '㉟',
    '㊱',
    '㊲',
    '㊳',
    '㊴',
    '㊵',
    '㊶',
    '㊷',
    '㊸',
    '㊹',
    '㊺',
    '㊻',
    '㊼',
    '㊽',
    '㊾',
    '㊿',
]);

export const PRINT_SUCCESS = 0;
export const PRINT_CANCEL = -1;

export const PRINT_TYPES = Object.freeze({
    TREATMENT_EXECUTE: 'treatmentExecute', // 治疗执行单
    INFUSION_EXECUTE: 'infusionExecute', // 输注执行单
    PRESCRIPTION: 'prescription', // 处方笺
    EXAMINATION: 'examination', // 检查检验单
    MEDICAL: 'medical', // 病历
    MEDICAL_CERTIFICATE: 'medicalCertificate', // 病情证明书
    CHILD_TEST_RESULT: 'childTestResult', // 儿童测试报告
    CHILD_HEALTH_REPORT: 'childHealthReport', // 儿童健康报告
    CHILD_QUESTION_TABLE: 'childQuestionTable', // 儿童问卷
    CHRONIC_CARE: 'chronicCare', // 慢病管理
    A5_DISPENSING: 'A5Dispensing', // A5发药单打印
    STAT: 'statPrint', // 统计打印
    FAMILY_DOCTOR_AGREEMENT: 'familyDoctorAgreementPrint', // 家庭医生打印
    EXAMINATION_REPORT: 'examinationReport', // 检验报告
    CHARGE_LIST: 'chargeList', // 收费清单
    HOSPITAL_PRESCRIPTION: 'hospitalPrescription', // 住院处方
    CHARGE_SHIFT: 'chargeShift', // 交班记录
});

// 这几个医疗清单是使用的发票, 暂时指定 Lodop 打印
export const FEE_LIST_PRINT_MODE = [
    'medical-fee-list-jiangsu',
    'medical-fee-list-wujiang',
    'medical-fee-list-huaian',
    'medical-fee-list-changshu',
    'medical-fee-list-huangshi',
];

export const PRINT_PREVIEW_NEED_SHOW_PRINT_CONFIG = [
    'medical-record',
    'prescription',
    'treatment-execute',
    'infusion-execute',
    'examination',
    'examine',
    'examinationInspect',
    'examination-apply-sheet',
    'examine-apply-sheet',
    'examination-inspect-apply-sheet',
    'examination-report',
    'inspect-report',
    'eye-inspect-report',
    'hospital-inspect',
    'medical-certificate',
    'tianjin-western-prescription',
    'tianjin-chinese-prescription',
    'registration',
    'registration-tag',
    'cashier',
    'dispense',
    'medicine-tag',
    'patient-tag',
];

/**
 * label: 打印单据在系统展示的名称
 * value: 根据该值匹配打印单据 (getAbcPrintOptions)
 * templateKey: 在 abc-print 对应的模板名称
 * printConfigKey: 对应打印单据的打印机配置的 key (ABCPrintConfigKeyMap)
 */
export const PRINT_OPTIONS = Object.freeze({
    // 病历
    MEDICAL: {
        label: '病历',
        value: '病历',
        templateKey: 'medicalRecord',
        printConfigKey: 'medicalRecord',
        filename: '病历',
    },
    // 处方笺
    PRESCRIPTION: {
        label: '处方',
        value: '处方',
        templateKey: 'prescription',
        printConfigKey: 'prescription',
        filename: '处方',
    },
    // 新版处方笺
    PRESCRIPTION_V2: {
        label: '新版处方',
        value: '新版处方',
        templateKey: 'prescriptionV2',
        printConfigKey: 'prescriptionV2',
        filename: '处方',
    },
    // 输注执行单
    INFUSION_EXECUTE: {
        label: '输液注射单',
        value: '输液注射单',
        templateKey: 'infusionExecute',
        printConfigKey: 'infusionExecute',
        filename: '输液注射单',
    },
    // 治疗理疗单
    TREATMENT_EXECUTE: {
        label: '治疗理疗单',
        value: '治疗理疗单',
        templateKey: 'treatmentExecute',
        printConfigKey: 'treatmentExecute',
        filename: '治疗理疗单',
    },

    // 检查检验单
    INSPECT: {
        label: '检查单',
        value: '检查单',
        templateKey: 'examination',
        printConfigKey: 'examine',
        filename: '检查单',
    },
    // 检查检验单
    EXAMINATION: {
        label: '检验单',
        value: '检验单',
        templateKey: 'examination',
        printConfigKey: 'examinationInspect',
        filename: '检验单',
    },
    // 检查申请单
    INSPECT_APPLY_SHEET: {
        label: '检查申请单',
        value: '检查申请单',
        templateKey: 'examinationApplySheet',
        printConfigKey: 'examineApplySheet',
        filename: '检查申请单',
    },
    // 检验申请单
    EXAMINATION_APPLY_SHEET: {
        label: '检验申请单',
        value: '检验申请单',
        templateKey: 'examinationApplySheet',
        printConfigKey: 'examinationInspectApplySheet',
        filename: '检验申请单',
    },
    // 病情证明书
    MEDICAL_CERTIFICATE: {
        label: '诊断证明书',
        value: '诊断证明书',
        templateKey: 'medicalCertificate',
        printConfigKey: 'medicalCertificate',
        filename: '诊断证明书',
    },
    INFUSION_TREATMENT: {
        label: '输液注射治疗理疗',
        value: '输液注射治疗理疗',
        filename: '输液注射治疗理疗',
    },
    // 健康报告
    CHILD_HEALTH_REPORT: {
        label: '健康报告',
        value: '健康报告',
        templateKey: 'childHealthyReport',
        printConfigKey: 'childHealthyReport',
        filename: '健康报告',
    },
    // 检验报告
    EXAMINATION_REPORT: {
        label: '检验报告',
        value: '检验报告',
        templateKey: 'examinationReport',
        printConfigKey: 'examinationReport',
        filename: '检验报告',
    },

    EXAMINATION_CLOUD_REPORT: {
        label: '检验报告',
        value: '检验报告',
        templateKey: 'examinationCloudReport',
        printConfigKey: 'examinationCloudReport',
        filename: '检验报告',
    },

    INSPECT_REPORT: {
        label: '检查报告',
        value: '检查报告',
        templateKey: 'hospitalInspect',
        printConfigKey: 'hospitalInspect',
        filename: '检查报告',
    },
    //彩超报告
    cdus_REPORT: {
        label: '彩超报告',
        value: '彩超报告',
        templateKey: 'cdusReport',
        printConfigKey: 'cdusReport',
        filename: '彩超报告',
    },
    //CT报告
    ct_REPORT: {
        label: 'CT报告',
        value: 'CT报告',
        templateKey: 'ctReport',
        printConfigKey: 'ctReport',
        filename: 'CT报告',
    },
    //DR报告
    dr_REPORT: {
        label: 'DR报告',
        value: 'DR报告',
        templateKey: 'drReport',
        printConfigKey: 'drReport',
        filename: 'DR报告',
    },
    //MG报告
    mg_REPORT: {
        label: 'MG报告',
        value: 'MG报告',
        templateKey: 'mgReport',
        printConfigKey: 'mgReport',
        filename: 'MG报告',
    },
    //MR报告
    mr_REPORT: {
        label: 'MR报告',
        value: 'MR报告',
        templateKey: 'mrReport',
        printConfigKey: 'mrReport',
        filename: 'MR报告',
    },
    // 患者标签
    PATIENT_TAG: {
        label: '患者标签',
        value: '患者标签',
        templateKey: 'patientTag',
        printConfigKey: 'patientTag',
        filename: '患者标签',
    },
    // 煎药工艺卡
    DECOCTION_CRAFT_CARD: {
        label: '煎药工艺卡',
        value: '煎药工艺卡',
        templateKey: 'decoctionCraftCard',
        printConfigKey: 'decoctionCraftCard',
        filename: '煎药工艺卡',
    },
    // 用药标签
    MEDICINE_TAG: {
        label: '用药标签',
        value: '用药标签',
        templateKey: 'medicineTag',
        templateKeyTwo: 'medicineTagCustomized',
        printConfigKey: 'medicineTag',
        filename: '用药标签',
    },
    // 住院瓶贴
    HOSPITAL_MEDICINE_TAG: {
        label: '用药标签',
        value: '瓶贴',
        templateKey: 'hospitalMedicineTag',
        printConfigKey: 'hospitalMedicineTag',
        filename: '用药标签',
    },
    // 样本条码
    EXAMINATION_CODE: {
        label: '样本条码',
        value: '样本条码',
        templateKey: 'examinationTag',
        printConfigKey: 'examinationTag',
        filename: '样本条码',
    },
    // 发药单
    DISPENSING_ORDER: {
        label: '发药小票',
        value: '发药小票',
        templateKey: 'dispense',
        templateKeyTwo: 'dispenseA5',
        printConfigKey: 'dispense',
        filename: '发药小票',
    },
    // 退药小票
    UNDISPENSING_ORDER: {
        label: '退药小票',
        value: '退药小票',
        templateKey: 'dispense',
        templateKeyTwo: 'dispenseA5',
        printConfigKey: 'undispense',
        filename: '退药小票',
    },
    // 收费小票
    CASHIER: {
        label: '收费小票',
        value: '收费小票',
        templateKey: 'cashier',
        templateKeyTwo: 'cashierA5',
        printConfigKey: 'cashier',
        filename: '收费小票',
    },
    // 收据
    RECEIPT: {
        label: '收据',
        value: '收据',
        templateKey: null, // 取决于打印设置 - 收据
        printConfigKey: 'receipt',
        filename: '收据',
    },
    PHARMACY_CASHIER_V2: {
        label: '新版药店收费小票',
        value: '新版药店收费小票',
        templateKey: 'pharmacyCashierV2',
        printConfigKey: 'pharmacyCashierV2',
        filename: '新版药店收费小票',
    },
    // 退费小票
    REFUND_CASHIER: {
        label: '退费小票',
        value: '退费小票',
        templateKey: 'refundCashier',
        printConfigKey: 'cashier',
        filename: '退费小票',
    },
    // 挂号小票
    REGISTRATION: {
        label: '挂号小票',
        value: '挂号小票',
        templateKey: 'registration',
        printConfigKey: 'registration',
        filename: '挂号小票',
    },
    // 充值小票
    RECHARGE: {
        label: '充值小票',
        value: '充值小票',
        templateKey: 'recharge',
        printConfigKey: 'cashier',
        filename: '会员充值凭证',
    },
    // 退款凭证
    REFUND: {
        label: '退款凭证',
        value: '退款凭证',
        templateKey: 'refund',
        printConfigKey: 'cashier',
    },
    MEDICAL_DOCUMENT: {
        label: '医疗文书',
        value: '医疗文书',
        templateKey: 'medical-document',
        printConfigKey: 'medical-document',
        filename: '医疗文书',
    },
    HOSPITAL_DEPOSIT_RECEIPT: {
        label: '押金收据',
        value: '押金收据',
        templateKey: 'hospitalDepositReceipt',
        printConfigKey: 'hospitalDepositReceipt',
        filename: '押金收据',
    },
    HOSPITAL_HOSPITALIZATION_CERTIFICATE: {
        label: '住院证',
        value: '住院证',
        templateKey: 'hospitalizationCertificate',
        printConfigKey: 'hospitalizationCertificate',
        filename: '住院证',
    },
    SOCIAL_SETTLEMENT_SHEET: {
        label: '医保结算单',
        value: '医保结算单',
        templateKey: 'medicalFeeSocial',
        printConfigKey: 'social',
        filename: '医保结算单',
    },
    SOCIAL_INFORM_SHEET: {
        label: '医保告知单',
        value: '医保告知单',
        templateKey: 'medicalFeeSocial',
        printConfigKey: 'social',
        filename: '医保告知单',
    },
    SOCIAL_SETTLEMENT_DETAIL: {
        label: '医保费用明细',
        value: '医保费用明细',
        templateKey: 'medicalFeeSocial',
        printConfigKey: 'social',
        filename: '医保费用明细',
    },
    // 住院收费项目清单
    CHARGE_LIST: {
        label: '收费清单',
        value: '收费清单',
        templateKey: 'chargeList',
        printConfigKey: 'chargeList',
        filename: '收费清单',
    },
    // 床头卡
    HOSPITAL_BEDSIDE_CARD: {
        label: '床头卡',
        value: '床头卡',
        templateKey: 'hospitalBedsideCard',
        printConfigKey: 'hospitalBedsideCard',
    },
    // 体检系统导诊单打印
    PE_GUIDE_SHEET: {
        label: '导检单',
        value: '导检单',
        templateKey: 'peGuideSheet',
        printConfigKey: 'peGuideSheet',
        filename: '导检单',
    },

    // 住院收费项目清单批量
    CHARGE_LIST_BATCH: {
        label: '收费清单批量',
        value: '收费清单批量',
        templateKey: 'chargeListBatch',
        printConfigKey: 'chargeListBatch',
        filename: '收费清单',
    },
    HOSPITAL_PRESCRIPTION: {
        label: '住院处方',
        value: '住院处方',
        templateKey: 'hospitalPrescription',
        printConfigKey: 'hospitalPrescription',
        filename: '住院处方',
    },
    // 住院收费项目清单
    CHARGE_SHIFT: {
        label: '交班记录',
        value: '交班记录',
        templateKey: 'chargeShift',
        printConfigKey: 'chargeShift',
        filename: '交班记录',
    },
    FEE_BILL: {
        label: '医疗收费票据',
        value: '医疗收费票据',
        templateKey: 'feeBill',
        printConfigKey: 'bill',
        filename: '医疗收费票据',
    },
    FEE_LIST: {
        label: '医疗收费清单',
        value: '医疗收费清单',
        templateKey: 'feeList',
        printConfigKey: 'feeList',
        filename: '医疗收费清单',
    },
    STATISTICS: {
        label: '统计报表',
        value: '统计报表',
        templateKey: 'statReportTableTemplate',
        printConfigKey: 'statistics',
        filename: '统计报表',
    },
    HOSPITAL_NURSE_PRESCRIPTION: {
        label: '医嘱执行单',
        value: '医嘱执行单',
        templateKey: 'hospitalNursePrescription',
        printConfigKey: 'hospitalNursePrescription',
        filename: '医嘱执行单',
    },
    HOSPITAL_EXAMINATION_TAG: {
        label: '样本条码',
        value: '样本条码',
        templateKey: 'examinationTag',
        printConfigKey: 'hospitalExaminationTag',
        filename: '样本条码',
    },
    HOSPITAL_NURSE_APPLY_MEDICINE: {
        label: '领药单',
        value: '领药单',
        templateKey: 'hospitalNurseApplyMedicine',
        printConfigKey: 'hospitalNurseApplyMedicine',
        filename: '领药单',
    },
    HOSPITAL_NURSE_PATIENT_DISPENSING: {
        label: '发药单',
        value: '发药单',
        templateKey: 'hospitalNursePatientDispensing',
        printConfigKey: 'hospitalNursePatientDispensing',
        filename: '发药单',
    },
    HOSPITAL_NURSE_GOODS_DISPENSING: {
        label: '发药单',
        value: '发药单',
        templateKey: 'hospitalNurseGoodsDispensing',
        printConfigKey: 'hospitalNurseGoodsDispensing',
        filename: '发药单',
    },
    NORMAL_INSPECT_REPORT: {
        label: '检查报告',
        value: '检查报告',
        templateKey: 'inspectReport',
        printConfigKey: 'inspectReport',
        filename: '检查报告',
    },
    TIANJIN_WESTERN_PRESCRIPTION: {
        label: '天津中西成药处方',
        value: '天津中西成药处方',
        templateKey: 'tianjinWesternPrescription',
        printConfigKey: 'tianjinWesternPrescription',
        filename: '中西成药处方',
    },
    TIANJIN_CHINESE_PRESCRIPTION: {
        label: '天津中药处方',
        value: '天津中药处方',
        templateKey: 'tianjinChinesePrescription',
        printConfigKey: 'tianjinChinesePrescription',
        filename: '中药处方',
    },
    REGISTRATION_TAG: {
        label: '挂号标签',
        value: '挂号标签',
        templateKey: 'registrationTag',
        printConfigKey: 'registrationTag',
        filename: '挂号标签',
    },
    RK: {
        label: '入库单',
        value: '入库单',
        templateKey: 'goodsIn',
        printConfigKey: 'RK',
        filename: '入库单',
    },
    CK: {
        label: '出库单',
        value: '出库单',
        templateKey: 'goodsOut',
        printConfigKey: 'CK',
        filename: '出库单',
    },
    DB: {
        label: '调拨单',
        value: '调拨单',
        templateKey: 'goodsTrans',
        printConfigKey: 'DB',
        filename: '调拨单',
    },
    PD: {
        label: '盘点单',
        value: '盘点单',
        templateKey: 'goodsCheck',
        printConfigKey: 'PD',
        filename: '盘点单',
    },
    CG: {
        label: '采购单',
        value: '采购单',
        templateKey: 'goodsPurchase',
        printConfigKey: 'CG',
        filename: '采购单',
    },
    LY: {
        label: '领用单',
        value: '领用单',
        templateKey: 'goodsApply',
        printConfigKey: 'LY',
        filename: '领用单',
    },
    PE_INDIVIDUAL_REPORT: {
        label: '体检个人报告',
        value: '体检个人报告',
        templateKey: 'peIndividualReport',
        printConfigKey: 'peIndividualReport',
        filename: '体检个人报告',
    },
    EYE_INSPECT_REPORT: {
        label: '眼科检查报告',
        value: '眼科检查报告',
        templateKey: 'eyeInspectReport',
        printConfigKey: 'eyeInspectReport',
        filename: '眼科检查报告',
    },
    EYE_INSPECT_REPORT_CUSTOM: {
        label: '检查报告书',
        value: '检查报告书',
        templateKey: 'eyeInspectReportCustom',
        printConfigKey: 'eyeInspectReportCustom',
        filename: '检查报告书',
    },
    HOSPITAL_DOCTOR_MEDICAL_PRESCRIPTION: {
        label: '医嘱单',
        value: '医嘱单',
        templateKey: 'hospitalDoctorMedicalPrescription',
        printConfigKey: 'hospitalDoctorMedicalPrescription',
        filename: '医嘱单',
    },
    HOSPITAL_ADVICE_SHANDONG: {
        label: '医嘱单',
        value: '山东定制医嘱单',
        templateKey: 'hospitalAdviceShandong',
        printConfigKey: 'hospitalAdviceShandong',
        filename: '医嘱单',
    },
    TEMPERATURE_GRAPH: {
        label: '体温单',
        value: '体温单',
        templateKey: 'temperatureGraph',
        printConfigKey: 'temperatureGraph',
        filename: '体温单',
    },
    BLOOD_SUGAR: {
        label: '血糖单',
        value: '血糖单',
        templateKey: 'bloodSugarTableTemplate',
        printConfigKey: 'bloodSugar',
        filename: '血糖单',
    },
    SETTLEMENT_APPLICATION: {
        label: '结算单',
        value: '结算单',
        templateKey: 'settlementApplication',
        printConfigKey: 'settlementApplication',
        filename: '结算单',
    },
    SETTLEMENT_REVIEW: {
        label: '结算明细',
        value: '结算明细',
        templateKey: 'settlementReview',
        printConfigKey: 'settlementReview',
        filename: '结算明细',
    },
    SETTLEMENT_DETAIL: {
        label: '付款明细',
        value: '付款明细',
        templateKey: 'settlementDetail',
        printConfigKey: 'settlementDetail',
        filename: '付款明细',
    },
    EXECUTE_CERTIFICATE: {
        label: '执行凭证',
        value: '执行凭证',
        templateKey: 'executeCertificate',
        printConfigKey: 'executeCertificate',
        filename: '执行凭证',
    },
    PE_CHARGE_FEE_LIST: {
        label: '体检费用清单',
        value: '体检费用清单',
        templateKey: 'peChargeFeeList',
        printConfigKey: 'peChargeFeeList',
        filename: '体检费用清单',
    },
    // 住院收费小票
    HOSPITAL_CASHIER: {
        label: '住院收费小票',
        value: '住院收费小票',
        templateKey: 'hospitalCashier',
        printConfigKey: 'hospitalCashier',
        filename: '住院收费小票',
    },
    FAMILY_DOCTOR_AGREEMENT: {
        label: '家庭医生签约协议',
        value: '家庭医生签约协议',
        templateKey: 'familyDoctorAgreement',
        printConfigKey: 'familyDoctorAgreement',
        filename: '家庭医生签约协议',
    },
    POINTS_BILL: {
        label: '积分小票',
        value: '积分小票',
        templateKey: 'pointsBill',
        printConfigKey: 'pointsBill',
        filename: '积分小票',
    },
    PHARMACY_CASHIER: {
        label: '药店收费小票',
        value: '药店收费小票',
        templateKey: 'pharmacyCashier',
        printConfigKey: 'pharmacyCashier',
        filename: '收费小票',
    },
    PHARMACY_REFUND_CASHIER: {
        label: '药店退费小票',
        value: '药店退费小票',
        templateKey: 'pharmacyCashier',
        printConfigKey: 'pharmacyCashier',
        filename: '退费小票',
    },
    PHARMACY_RECONCILIATION_INFO: {
        label: '对账信息',
        value: '对账信息',
        templateKey: 'pharmacyReconciliationInfo',
        printConfigKey: 'pharmacyReconciliationInfo',
        filename: '对账信息',
    },

});
