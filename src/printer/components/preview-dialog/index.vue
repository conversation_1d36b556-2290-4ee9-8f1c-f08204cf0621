<template>
    <abc-dialog
        v-model="visible"
        custom-top="0"
        content-styles="padding: 0;"
        class="abc-print-preview-dialog-wrapper"
        custom-class="abc-print-preview-dialog"
        responsive
        size="hugely"
        append-to-body
        @close-dialog="cancelPrint"
    >
        <div class="dialog-content clearfix" @keydown.enter="handleEnterEvent">
            <div class="print-view">
                <iframe
                    ref="previewFrame"
                    frameborder="0"
                    width="100%"
                    height="100%"
                    :srcdoc="previewDoc"
                ></iframe>

                <abc-button
                    shape="round"
                    variant="outline"
                    theme="default"
                    icon="s-b-add-line-medium"
                    icon-color="var(--abc-color-T4)"
                    class="preview-iframe-zoom-btn"
                    style="bottom: 92px;"
                    @click="_changePreviewZoom('add')"
                >
                </abc-button>
                <abc-button
                    shape="round"
                    variant="outline"
                    theme="default"
                    icon="s-b-reduce-line-medium"
                    icon-color="var(--abc-color-T4)"
                    class="preview-iframe-zoom-btn"
                    style="bottom: 48px; margin-left: 0 !important;"
                    @click="_changePreviewZoom('reduce')"
                >
                </abc-button>
            </div>
            <div class="print-config-wrapper">
                <div class="setting-item">
                    <abc-text theme="black">
                        打印
                    </abc-text>
                </div>

                <div class="setting-item">
                    <p>目标打印机</p>
                    <abc-select
                        :key="`printer-select-key-${printerListIsUpdate}`"
                        v-model="printConfig.deviceIndex"
                        :width="240"
                        custom-class="print-preview-printer-select-wrapper"
                        placeholder="选择打印机"
                        :setting="isNewPrescriptionVersion && isABCClient()"
                        setting-text="添加打印机"
                        setting-icon="a-plus13px"
                        @change="handlePrinterChange"
                        @set="handleOpenAddPrinterDriverDialog"
                    >
                        <abc-option
                            v-for="(it) in printerList"
                            :key="it.deviceIndex"
                            :value="it.deviceIndex"
                            :label="it.name"
                        >
                            <img
                                v-if="isTinyPrinter(it.deviceIndex)"
                                class="print-preview-printer-img-icon"
                                src="~assets/images/print/printer-tiny-icon.png"
                                alt=""
                            />
                            <img
                                v-else-if="it.deviceIndex === PdfPrinterDeviceIndex"
                                class="print-preview-printer-img-icon"
                                src="~assets/images/print/icon-pdf.png"
                                alt=""
                            />
                            <img
                                v-else
                                class="print-preview-printer-img-icon"
                                src="~assets/images/print/printer-icon.png"
                                alt=""
                            />
                            <span :title="it.name + (isTinyPrinter(it.deviceIndex) ? '（小票）' : '')" class="print-preview-printer-select-option-title">{{ it.name + (isTinyPrinter(it.deviceIndex) ? '（小票）' : '') }}</span>
                            <span v-if="isNewPrescriptionVersion && it.offline" class="print-preview-printer-select-option-offline">脱机</span>
                        </abc-option>
                    </abc-select>
                </div>
                <div class="setting-item">
                    <p>尺寸和布局</p>

                    <div class="select-group">
                        <abc-select
                            :key="`printer-page-select-key-${printerListIsUpdate}`"
                            v-model="printConfig.pageSize"
                            placeholder="纸张尺寸"
                            :width="158"
                            :inner-width="240"
                            custom-class="print-page-list"
                            :disabled="disabledChangePrintConfig"
                            @change="handlePageSizeChange(true)"
                        >
                            <template v-for="(it, index) in pageSizeList">
                                <li v-if="it.isElectronPage && !pageSizeList[index - 1]?.isElectronPage" :key="`print-dialog-option-border-${index}`" class="print-dialog-option-border"></li>
                                <abc-option
                                    :key="it.paper.name"
                                    :value="it.paper.name"
                                    :label="it.paper.name"
                                >
                                    {{ it.paper.name }}
                                    <span v-if="it.isRecommend" class="print-recommend-text">推荐</span>
                                </abc-option>
                            </template>
                        </abc-select>
                        <abc-select
                            v-if="curPageHeightLevelList"
                            v-model="printConfig.pageHeightLevel"
                            :width="85"
                            placeholder="布局"
                            :disabled="disabledChangePrintConfig"
                            @change="handleChangeHeightLevelOrOrient"
                        >
                            <abc-option
                                v-for="level in curPageHeightLevelList"
                                :key="level.name"
                                :label="level.name"
                                :value="level.name"
                            ></abc-option>
                        </abc-select>
                        <abc-select
                            v-else
                            v-model="printConfig.orient"
                            :width="85"
                            placeholder="布局"
                            :disabled="disabledChangePrintConfig"
                            @change="handleChangeHeightLevelOrOrient"
                        >
                            <abc-option
                                v-for="item in currentOrientations"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></abc-option>
                        </abc-select>
                    </div>
                </div>

                <!-- 用药标签不显示该内容 -->
                <div v-if="!(isMedicineTag || disabledChangePrintConfig)" class="setting-item">
                    <p>打印份数</p>
                    <abc-input-number
                        v-model="printConfig.printCopies"
                        v-abc-focus-selected
                        :allow-input="true"
                        fixed-button
                        :width="176"
                        button-placement="left"
                        :input-custom-style="{
                            textAlign: 'center',
                        }"
                        :config="{
                            supportZero: false,
                            max: 99,
                            min: 1,
                        }"
                    >
                    </abc-input-number>
                </div>

                <abc-flex
                    class="abc-print-preview-dialog-wrapper_more-panel"
                    vertical
                    :gap="16"
                >
                    <abc-collapse v-model="collapseList" panel-theme="light" :animation="false">
                        <abc-collapse-item
                            title="更多设置"
                            value="0"
                            :border="false"
                        >
                            <div class="padding-wrapper">
                                <div class="padding-wrapper-item">
                                    <span class="padding-wrapper-label">上边距</span>
                                    <abc-input-number
                                        v-model="printConfig.pageSizeReduce.top"
                                        fixed-button
                                        button-placement="left"
                                        :width="48"
                                        :disabled="disabledChangePrintConfig || disabledChangePageSizeReduce"
                                        :config="{
                                            min: isPhysicalMargin ? Math.ceil(printerOffset.offsetY) : 0,
                                            // min: 0,
                                            max: 99,
                                            supportZero: true,
                                            supportNegative: false,
                                        }"
                                        @change="(value) => handlePageSizeReduceChange({
                                            key: 'top', value
                                        })"
                                    ></abc-input-number>
                                </div>
                                <div class="padding-wrapper-item">
                                    <span class="padding-wrapper-label">下边距</span>
                                    <abc-input-number
                                        v-model="printConfig.pageSizeReduce.bottom"
                                        fixed-button
                                        button-placement="left"
                                        :width="48"
                                        :disabled="disabledChangePrintConfig || disabledChangeReduceByPDFPrint || disabledChangePageSizeReduce"
                                        :config="{
                                            min: isPhysicalMargin ? Math.ceil(printerOffset.offsetY) : 0,
                                            // min: 0,
                                            max: 99,
                                            supportZero: true,
                                            supportNegative: false,
                                        }"
                                        @change="(value) => handlePageSizeReduceChange({
                                            key: 'bottom', value
                                        })"
                                    ></abc-input-number>
                                </div>
                            </div>

                            <div class="padding-wrapper" style="margin-top: 16px;">
                                <div class="padding-wrapper-item">
                                    <span class="padding-wrapper-label">左边距</span>
                                    <abc-input-number
                                        v-model="printConfig.pageSizeReduce.left"
                                        fixed-button
                                        button-placement="left"
                                        :width="48"
                                        :disabled="disabledChangePrintConfig || disabledChangePageSizeReduce"
                                        :config="{
                                            min: isPhysicalMargin ? Math.ceil(printerOffset.offsetX) : 0,
                                            // min: 0,
                                            max: 99,
                                            supportZero: true,
                                            supportNegative: false,
                                        }"
                                        @change="(value) => handlePageSizeReduceChange({
                                            key: 'left', value
                                        })"
                                    ></abc-input-number>
                                </div>
                                <div class="padding-wrapper-item">
                                    <span class="padding-wrapper-label">右边距</span>
                                    <abc-input-number
                                        v-model="printConfig.pageSizeReduce.right"
                                        fixed-button
                                        button-placement="left"
                                        :width="48"
                                        :disabled="disabledChangePrintConfig || disabledChangeReduceByPDFPrint || disabledChangePageSizeReduce"
                                        :config="{
                                            min: isPhysicalMargin ? Math.ceil(printerOffset.offsetX) : 0,
                                            // min: 0,
                                            max: 99,
                                            supportZero: true,
                                            supportNegative: false,
                                        }"
                                        @change="(value) => handlePageSizeReduceChange({
                                            key: 'right', value
                                        })"
                                    ></abc-input-number>
                                </div>
                            </div>

                            <template v-if="printConfig.advance && !disabledChangePrintConfig">
                                <template v-if="printConfig.advance.printRangeEnable">
                                    <div v-if="!isSupportMedicalDocumentContinuePrintMode" class="setting-item" style=" display: block; margin-top: 16px;">
                                        <p style="margin-bottom: 8px;">
                                            打印范围
                                        </p>
                                        <abc-radio-group v-model="printConfig.advance.printRange.range">
                                            <abc-flex vertical :gap="8">
                                                <abc-radio :label="PrintPageRange.All">
                                                    全部
                                                </abc-radio>
                                                <abc-radio :label="PrintPageRange.Specify">
                                                    <abc-flex align="center" :gap="4">
                                                        <span>从</span>
                                                        <abc-input
                                                            v-model="printConfig.advance.printRange.start"
                                                            :width="68"
                                                            type="number"
                                                            size="small"
                                                            @click="handleClickPageRangeInput"
                                                        ></abc-input>
                                                        <span>到</span>
                                                        <abc-input
                                                            v-model="printConfig.advance.printRange.end"
                                                            :width="68"
                                                            type="number"
                                                            size="small"
                                                            @click="handleClickPageRangeInput"
                                                        ></abc-input>
                                                        <span>页</span>
                                                    </abc-flex>
                                                </abc-radio>
                                                <abc-radio :label="PrintPageRange.SpecifyRange">
                                                    <abc-flex align="center" :gap="4">
                                                        <span>指定打印第</span>
                                                        <abc-select
                                                            v-model="printConfig.advance.printRange.specify"
                                                            :width="68"
                                                            placeholder="选择页码"
                                                            size="small"
                                                        >
                                                            <abc-option
                                                                v-for="i in pageTotal"
                                                                :key="i"
                                                                :value="i"
                                                                :label="i"
                                                            ></abc-option>
                                                        </abc-select>
                                                        <span>页</span>
                                                    </abc-flex>
                                                </abc-radio>
                                            </abc-flex>
                                        </abc-radio-group>
                                    </div>
                                    <div class="setting-item" style=" display: block; margin-top: 16px;">
                                        <p>打印方式</p>
                                        <abc-select
                                            v-model="printConfig.advance.duplexMode"
                                            :width="240"
                                            placeholder="选择打印方式"
                                        >
                                            <abc-option
                                                v-for="(it) in DuplexModeOptions"
                                                :key="it.value"
                                                :value="it.value"
                                                :label="it.label"
                                            >
                                                <abc-flex justify="space-between" style="width: 100%;">
                                                    <span>{{ it.label }}</span>
                                                    <span class="abc-print-preview-dialog_description">{{ it.desc }}</span>
                                                </abc-flex>
                                            </abc-option>
                                        </abc-select>
                                    </div>
                                </template>

                                <template v-if="printConfig.advance.paperOrientationEnable && showPaperOrientation && isSupportPaperOrientation">
                                    <div class="setting-item" style=" display: block; margin-top: 16px;">
                                        <p>内容打印方向</p>
                                        <abc-select
                                            v-model="printConfig.advance.paperOrientation"
                                            :width="240"
                                            placeholder="选择打印方向"
                                        >
                                            <abc-option
                                                v-for="(it) in PaperOrientationOptions"
                                                :key="it.value"
                                                :value="it.value"
                                                :label="it.label"
                                            >
                                                <abc-flex justify="space-between" align="center" :gap="8">
                                                    <abc-icon :icon="it.icon"></abc-icon>
                                                    <span>{{ it.label }}</span>
                                                </abc-flex>
                                            </abc-option>
                                        </abc-select>
                                    </div>
                                </template>
                            </template>

                            <template v-if="isPrinterSupportColor">
                                <div class="setting-item" style=" display: block; margin-top: 16px;">
                                    <p>颜色</p>
                                    <abc-select
                                        v-model="printConfig.printerUseColor.isColor"
                                        adaptive-width
                                        :inner-width="240"
                                        placeholder="选择打印颜色"
                                    >
                                        <abc-option
                                            :value="0"
                                            label="黑白"
                                        ></abc-option>
                                        <abc-option
                                            :value="1"
                                            label="彩色"
                                        ></abc-option>
                                    </abc-select>
                                </div>
                            </template>

                            <template v-if="isSupportedDPIs.flag">
                                <div class="setting-item">
                                    <p>打印质量</p>
                                    <abc-select
                                        :key="printConfig.deviceName || 'printer'"
                                        v-model="printConfig.dpi"
                                        :width="240"
                                        placeholder="选择打印质量"
                                    >
                                        <abc-option
                                            v-for="(dpi, dpiIndex) in isSupportedDPIs.supportedDPIs"
                                            :key="`printer-dpi-${dpiIndex}`"
                                            :value="dpiIndex"
                                            :label="`${dpi.horizontal} x ${dpi.vertical}`"
                                        ></abc-option>
                                    </abc-select>
                                </div>
                            </template>

                            <template v-if="isFeeBill">
                                <div class="padding-wrapper" style="margin-top: 16px;">
                                    <div class="padding-wrapper-item">
                                        <span class="padding-wrapper-label">上下移动</span>
                                        <abc-input-number
                                            v-model="printConfig.offset.top"
                                            fixed-button
                                            :config="{
                                                formatLength: 0,
                                                supportZero: true,
                                                supportNegative: true
                                            }"
                                            :width="60"
                                            style="width: 112px;"
                                            button-placement="left"
                                            @change="() => handlePageSizeReduceChange()"
                                        >
                                            <abc-icon slot="icon-minus" icon="arrow_up"></abc-icon>
                                            <abc-icon slot="icon-plus" icon="arrow_down"></abc-icon>
                                        </abc-input-number>
                                    </div>
                                    <div class="padding-wrapper-item">
                                        <span class="padding-wrapper-label">左右移动</span>
                                        <abc-input-number
                                            v-model="printConfig.offset.left"
                                            fixed-button
                                            :config="{
                                                formatLength: 0,
                                                supportZero: true,
                                                supportNegative: true
                                            }"
                                            style="width: 112px;"
                                            :width="60"
                                            button-placement="left"
                                            @change="() => handlePageSizeReduceChange()"
                                        >
                                            <abc-icon slot="icon-minus" icon="Arrow_Left_"></abc-icon>
                                            <abc-icon slot="icon-plus" icon="Arrow_Rgiht_"></abc-icon>
                                        </abc-input-number>
                                    </div>
                                </div>
                            </template>
                        </abc-collapse-item>
                    </abc-collapse>
                </abc-flex>

                <template v-if="isMedicineTag && printTask.staticRenderConfig.length">
                    <div
                        v-for="(config, index) in printTask.renderConfig"
                        :key="index"
                        class="setting-item"
                        style="margin-top: 16px;"
                    >
                        <p style="display: flex;" class="print-count-name-container">
                            <span style="line-height: 18px;">{{ NUMBER_ICONS[config.countId] }}</span>
                            <span class="print-count-name">{{ config.name }}</span>
                            <span>打印份数</span>
                        </p>
                        <abc-input-number
                            v-model="config.value"
                            v-abc-focus-selected
                            :allow-input="true"
                            fixed-button
                            :width="170"
                            button-placement="left"
                            :input-custom-style="{
                                textAlign: 'center',
                            }"
                            :config="{
                                supportZero: false,
                                max: 99,
                                min: 1,
                            }"
                        >
                        </abc-input-number>
                    </div>
                </template>

                <abc-flex v-if="isSupportMedicalDocumentContinuePrintMode" class="setting-item" vertical>
                    <abc-divider variant="dashed"></abc-divider>

                    <abc-flex
                        vertical
                        :gap="4"
                    >
                        <abc-text theme="gray">
                            打印方式
                        </abc-text>

                        <abc-flex vertical :gap="16">
                            <abc-tabs-v2
                                v-model="medicalDocumentContinue.select"
                                :item-min-width="120"
                                :option="medicalDocumentContinue.options"
                                type="outline"
                            ></abc-tabs-v2>

                            <abc-flex v-if="medicalDocumentContinue.select === MedicalDocumentContinueSelectType.ALL" vertical>
                                <abc-checkbox-group v-model="printConfig.advance.printRange.selectPage">
                                    <abc-flex vertical :gap="8">
                                        <abc-checkbox
                                            v-for="item in pageTotal"
                                            :key="item"
                                            :label="item - 1"
                                            style="margin-left: 0;"
                                        >
                                            第{{ item }}页
                                        </abc-checkbox>
                                    </abc-flex>
                                </abc-checkbox-group>
                            </abc-flex>

                            <abc-flex v-if="medicalDocumentContinue.select === MedicalDocumentContinueSelectType.CONTINUE" vertical :gap="8">
                                <abc-text theme="gray" size="small">
                                    从上次病历打印位置继续打印新增内容
                                </abc-text>
                                <abc-flex vertical :gap="2">
                                    <abc-text theme="gray" size="small">
                                        1.蓝线以下内容为本次续打内容
                                    </abc-text>
                                    <abc-text theme="gray" size="small">
                                        2.位置不准确时可拖动右侧按钮上下调整
                                    </abc-text>
                                    <abc-text theme="gray" size="small">
                                        3.续打时请确保放入已打印的病历
                                    </abc-text>
                                </abc-flex>
                            </abc-flex>
                        </abc-flex>
                    </abc-flex>
                </abc-flex>

                <!-- 用药标签存在staticRenderConfig,但不显示该内容 -->
                <div v-if="printTask.staticRenderConfig.length && !isMedicineTag" class="setting-item render-config-wrapper">
                    <!-- 检查检验申请单 - 分组打印配置 -->
                    <template v-if="isExamApplySheet">
                        <div
                            v-for="(config, i) in printTask.staticRenderConfig"
                            :key="i"
                            class="render-config-group"
                        >
                            <p class="render-config-group-name">
                                <span>
                                    {{ config.groupName }}
                                </span>
                                <span>
                                    {{ formatDate(config.createdDate, 'YYYY-MM-DD HH:mm') }}
                                </span>
                            </p>
                            <div class="render-config-item">
                                <abc-checkbox
                                    v-model="groupTotalChoose[i]"
                                    class="custom-checkbox"
                                    @click="handlePrintAllExamItems(i)"
                                >
                                    <span>打印所有项目</span>
                                </abc-checkbox>
                            </div>
                            <div
                                v-for="(examItem, j) in config.examItems"
                                :key="j"
                                class="render-config-item"
                            >
                                <template v-if="examItem.type === 'checkbox'">
                                    <abc-checkbox
                                        v-model="printTask.renderConfig[i].examItems[j].value"
                                        class="custom-checkbox"
                                    >
                                        <span class="checkbox-wrapper">
                                            <span class="label">{{ examItem.label }}</span>
                                        </span>
                                    </abc-checkbox>
                                </template>
                            </div>
                        </div>
                    </template>

                    <template v-else-if="isExaminationReport">
                        <abc-flex vertical :gap="24">
                            <div>
                                <p>打印选项</p>
                                <div class="examination-print-config-items">
                                    <abc-radio-group
                                        v-model="printTask.renderConfig[0].value"
                                        style="display: flex; flex-direction: column; gap: 12px;"
                                    >
                                        <abc-radio
                                            :label="printTask.renderConfig[0].options[0].value"
                                            style="margin-left: 0;"
                                        >
                                            {{ printTask.renderConfig[0].options[0].label }}
                                        </abc-radio>

                                        <template v-if="printTask.renderConfig[0].value === printMode.merge">
                                            <div class="examination-print-config-items-child">
                                                <abc-checkbox-group v-model="printTask.renderConfig[0].children[0].value">
                                                    <abc-flex vertical :gap="12">
                                                        <abc-checkbox
                                                            v-for="(item, index) in printTask.renderConfig[0].children[0].options"
                                                            :key="index"
                                                            :label="item.value"
                                                            style="margin-left: 0;"
                                                        >
                                                            {{ item.label }}
                                                        </abc-checkbox>
                                                    </abc-flex>
                                                </abc-checkbox-group>
                                            </div>
                                        </template>

                                        <abc-radio
                                            :label="printTask.renderConfig[0].options[1].value"
                                            style="margin-left: 0;"
                                        >
                                            {{ printTask.renderConfig[0].options[1].label }}
                                        </abc-radio>
                                    </abc-radio-group>
                                </div>
                            </div>

                            <div v-if="isExaminationLandscape">
                                <p>分栏设置</p>
                                <div class="examination-print-config-items">
                                    <abc-radio-group v-model="printTask.renderConfig[1].value">
                                        <abc-flex vertical :gap="12">
                                            <abc-flex :gap="12">
                                                <abc-radio
                                                    :label="printTask.renderConfig[1].options[0].value"
                                                    style="margin-left: 0;"
                                                >
                                                    {{ printTask.renderConfig[1].options[0].label }}
                                                </abc-radio>

                                                <template v-if="printTask.renderConfig[1].value === printLayout.doubleColumn">
                                                    <abc-input
                                                        v-model="printTask.renderConfig[1].children[0].value"
                                                        type="number"
                                                        :config="{
                                                            min: printTask.renderConfig[1].children[0].min || 1,
                                                            max: printTask.renderConfig[1].children[0].max || 18,
                                                        }"
                                                        :width="50"
                                                    >
                                                        <span slot="append">项/栏</span>
                                                    </abc-input>
                                                </template>
                                            </abc-flex>

                                            <abc-radio
                                                :label="printTask.renderConfig[1].options[1].value"
                                                style="margin-left: 0;"
                                            >
                                                {{ printTask.renderConfig[1].options[1].label }}
                                            </abc-radio>
                                        </abc-flex>
                                    </abc-radio-group>
                                </div>
                            </div>
                        </abc-flex>
                    </template>

                    <template v-else-if="isHospitalInspect">
                        <div>打印报告</div>
                        <abc-divider margin="small"></abc-divider>
                        <abc-flex vertical :gap="12">
                            <abc-checkbox
                                v-for="(report, i) in printTask.renderConfig"
                                :key="i"
                                v-model="report.checked"
                                type="number"
                                style="margin-left: 0;"
                            >
                                {{ report.label }}
                            </abc-checkbox>
                        </abc-flex>
                    </template>

                    <abc-flex v-else-if="isHospitalAdviceShandong" vertical>
                        <abc-text theme="gray" style="margin-bottom: 4px;">
                            打印方式
                        </abc-text>

                        <abc-radio-group :value="printTask.renderConfig[0].value" adaptive-width>
                            <abc-radio-button
                                v-for="(config, configIndex) in printTask.staticRenderConfig[0].list"
                                :key="`hospital-advice-shandong-print-${configIndex}`"
                                :label="configIndex"
                                :disabled="config.disabled && !config.disabledReasonContent"
                                @click="_onChangeHospitalShandongAdvicePrintMethod(configIndex)"
                            >
                                {{ config.label }}
                            </abc-radio-button>
                        </abc-radio-group>

                        <abc-text theme="gray" size="mini" style="margin: 16px 0 12px 0;">
                            <template v-if="printTask.renderConfig[0].value === 0">
                                使用最新格式打印全部预览医嘱内容
                            </template>
                            <template v-else-if="printTask.renderConfig[0].value === 1">
                                在已打印基础上续打红色新增医嘱内容
                            </template>
                        </abc-text>

                        <abc-flex vertical :gap="12">
                            <template v-if="printTask.renderConfig[0].value">
                                <abc-radio-group
                                    v-model="continuePrintSelectedPageIndex"
                                    item-block
                                    flex
                                    gap="8px"
                                    style="flex-direction: column;"
                                >
                                    <abc-radio
                                        v-for="printMethod in (printTask.renderConfig[0].list[1]?.pages || [])"
                                        :key="`continue-print-method-${printMethod.page}`"
                                        :label="printMethod.page"
                                        style="width: fit-content;"
                                    >
                                        {{ printMethod.label }}
                                        <abc-tag-v2
                                            v-if="printMethod.isPageUpdated"
                                            size="tiny"
                                            theme="primary"
                                            variant="outline"
                                            style="margin-left: 8px;"
                                        >
                                            有更新
                                        </abc-tag-v2>
                                        <abc-tag-v2
                                            v-if="printMethod.isPageNew"
                                            size="tiny"
                                            theme="warning"
                                            variant="outline"
                                            style="margin-left: 8px;"
                                        >
                                            未打印
                                        </abc-tag-v2>
                                    </abc-radio>
                                </abc-radio-group>
                            </template>
                            <template v-else>
                                <abc-checkbox
                                    v-for="(printMethod, printMethodIndex) in (printTask.renderConfig[0].list[0]?.pages || [])"
                                    :key="`advice-shandong-print-method-${printMethodIndex}`"
                                    v-model="printMethod.value"
                                    :disabled="printMethod.disabled"
                                    style="width: fit-content;"
                                >
                                    {{ printMethod.label }}
                                </abc-checkbox>
                            </template>
                        </abc-flex>
                    </abc-flex>

                    <template v-else-if="printContentVisible">
                        <p>打印内容</p>
                        <div class="examination-print-config-items">
                            <div v-if="isExaminationTag" class="render-config-item">
                                <abc-checkbox v-model="isTotalChoose" class="custom-checkbox" @click="changeTotalChoose(isTotalChoose)">
                                    <span>打印所有项目</span>
                                </abc-checkbox>
                            </div>
                            <div v-for="(config, index) in printTask.staticRenderConfig" :key="index" class="render-config-item">
                                <template v-if="config.type === 'checkbox'">
                                    <abc-checkbox v-model="printTask.renderConfig[index].value" class="custom-checkbox">
                                        <span class="checkbox-wrapper">
                                            <span class="label">{{ config.label }}</span>
                                            <span :class="['print-preview-append', { 'no-charge-append': !config.show }] " v-html="config.append"></span>
                                        </span>
                                    </abc-checkbox>
                                </template>
                            </div>
                        </div>
                    </template>
                </div>

                <div class="button-group">
                    <div v-if="isHospitalAdviceShandong">
                        <abc-button variant="text" @click="openAdvicePrintRecordDialog">
                            打印记录
                        </abc-button>
                    </div>
                    <div v-else-if="needShowPrintConfig" class="print-config-tooltip-wrapper">
                        <span class="print-config-tooltip-text">打印样式</span>
                        <abc-tooltip-info>
                            <span class="print-config-tooltip-info-text">若想调整文书上的显示内容、样式，</span>
                            <br />
                            <span class="print-config-tooltip-info-text">请前往【管理-打印设置】中，设置</span>
                            <br />
                            <span class="print-config-tooltip-info-text">文书打印模版</span>
                        </abc-tooltip-info>
                    </div>
                    <div v-else></div>

                    <div style="display: flex; align-items: center;">
                        <abc-tooltip
                            placement="top"
                            :content="`请放入已打印的第${continuePrintPageIndex}张病历`"
                            :disabled="!enableContinuePrintTooltip"
                        >
                        </abc-tooltip>
                        <abc-tooltip placement="top" :content="getPrintBtnTipsInfo" :disabled="!getPrintBtnTipsInfo">
                            <abc-button
                                style="margin-left: 8px;"
                                :disabled="disablePrint"
                                :loading="printLoading"
                                @click="handlePrint"
                            >
                                {{ printConfig.deviceIndex === PdfPrinterDeviceIndex ? '保存' : '打印' }}
                            </abc-button>
                        </abc-tooltip>
                        <abc-button type="blank" @click="cancelPrint">
                            取消
                        </abc-button>
                    </div>
                </div>
            </div>
        </div>
    </abc-dialog>
</template>

<script>
    import {
        AbcPrintOrientation, AbcPrintOrientationRevert, PrintMode,
    } from '@/printer/constants.js';
    import {
        off, on,
    } from 'utils/dom';
    import {
        DevToolsInputConfig, InputConfig,
    } from '@/printer/components/preview-dialog/index.js';
    import {
        DefaultPrintRangeConfig,
        GLOBAL_OFFSET,
        GLOBAL_PAGE_REDUCER,
        PaperOrientationOptions,
        PrinterUseColor,
        PrintPageRange,
    } from '@/printer/config';
    import Clone from 'utils/clone.js';
    import { formatDate } from '@abc/utils-date';
    import PrintManager from '../../manager/print-manager.js';
    import Logger from 'utils/logger.js';
    import {
        DuplexMode, DuplexModeOptions,
    } from '@/printer/utils/electron';
    import {
        getPrinterPageSizeReduce,
        getSliceByPrintConfig, HtmlToPDF,
    } from '@/printer/utils';
    import { debounce } from '@abc/utils';
    import { getViewDistributeConfig } from '@/views-distribute/utils.js';
    import {
        FEE_LIST_PRINT_MODE, PRINT_PREVIEW_NEED_SHOW_PRINT_CONFIG,
    } from '@/printer/common/constant';
    const AddPrinterDriverModalModule = () => import('@/printer/components/printer-driver/add-printer-driver-modal');
    import PrinterOfflineDialog from '@/printer/components/printer-driver/printer-offline-dialog';
    import { isABCClient } from '@/utils';
    import { injectScriptToHTML } from '@/printer/utils/electron';
    import * as medicalDocumentContinueInject from './medical-document-continue-inject.js';
    import { AdvicePrintRecordDialog } from '@/views-hospital/medical-prescription/components/advice-print-record-dialog';
    import ABCClientManager from '@/printer/manager/abc-client-manager';
    import { isEqual } from 'utils/lodash';

    const NUMBER_ICONS = Object.freeze([
        '',
        '①',
        '②',
        '③',
        '④',
        '⑤',
        '⑥',
        '⑦',
        '⑧',
        '⑨',
        '⑩',
        '⑪',
        '⑫',
        '⑬',
        '⑭',
        '⑮',
        '⑯',
        '⑰',
        '⑱',
        '⑲',
        '⑳',
        '㉑',
        '㉒',
        '㉓',
        '㉔',
        '㉕',
        '㉖',
        '㉗',
        '㉘',
        '㉙',
        '㉚',
        '㉛',
        '㉜',
        '㉝',
        '㉞',
        '㉟',
        '㊱',
        '㊲',
        '㊳',
        '㊴',
        '㊵',
        '㊶',
        '㊷',
        '㊸',
        '㊹',
        '㊺',
        '㊻',
        '㊼',
        '㊽',
        '㊾',
        '㊿',
    ]);

    const MedicalDocumentContinueSelectType = {
        ALL: 0,
        CONTINUE: 1,
    };

    export default {
        name: 'PrintPreviewDialog',
        props: {
            title: String,
            printTask: {
                type: Object,
                required: true,
            },
            printHandler: {
                type: Function,
                required: true,
            },
            printCancelHandler: {
                type: Function,
                required: true,
            },
            isDevTools: {
                type: Boolean,
                default: false,
            },
            matchRecommendPagesizeCallback: {
                type: Function,
                default: null,
            },
            needReportLog: {
                type: Boolean,
                default: false,
            },
            reportLogData: {
                type: Object,
                default: () => null,
            },
        },
        data() {
            const {
                advance, printerUseColor,
            } = this.printTask.printConfig;
            // 范围每次预览需要重置
            if (advance) {
                advance.printRange = Clone(DefaultPrintRangeConfig);
            }
            return {
                AbcPrintOrientation,
                visible: false,
                saveLoading: false,
                contentLoading: false,
                printerList: [],
                printerListIsUpdate: false,
                printConfig: {
                    deviceIndex: this.printTask.printConfig.deviceIndex,
                    deviceName: this.printTask.printConfig.deviceName,
                    driveName: this.printTask.printConfig.driveName,
                    pageSize: this.printTask.printConfig.pageSize,
                    orient: this.printTask.printConfig.orient,
                    printCopies: this.printTask.printConfig.printCopies,
                    pageHeightLevel: this.printTask.printConfig.pageHeightLevel, //纸张的几等分
                    pageSizeReduce: this.printTask.printConfig.pageSizeReduce || GLOBAL_PAGE_REDUCER,
                    offset: this.printTask.printConfig.offset || GLOBAL_OFFSET,
                    key: this.printTask.printConfig.key,
                    advance,
                    printerUseColor: Clone(printerUseColor || PrinterUseColor),
                    dpi: this.printTask.printConfig.dpi,
                    pageWidth: this.printTask.printConfig.pageWidth,
                    pageHeight: this.printTask.printConfig.pageHeight,
                },
                inputConfig: this.isDevTools ? DevToolsInputConfig : InputConfig,
                showMore: this.isDevTools,
                collapseList: [],
                isTotalChoose: false,
                groupTotalChoose: this.printTask.staticRenderConfig.map(() => (true)),
                NUMBER_ICONS,
                // 打印选项：是否合并项目打印
                printMode: {
                    merge: 0,
                    split: 1,
                },
                printLayout: {
                    singleColumn: 1,
                    doubleColumn: 2,
                },
                printerOffset: {
                    offsetX: 0,
                    offsetY: 0,
                },
                medicalDocumentContinue: {
                    select: MedicalDocumentContinueSelectType.ALL, // 当前选择的打印方式
                    options: [
                        {
                            label: '打印全部病历',
                            value: MedicalDocumentContinueSelectType.ALL,
                        },
                        {
                            label: '续打',
                            value: MedicalDocumentContinueSelectType.CONTINUE,
                        },
                    ],
                },
                continuePrintPageIndex: 1,
                previewZoom: 0.83, // 标准 210mm 高度的缩放值
                isChangePreviewZoom: false,

                printLoading: false,
            };
        },
        computed: {
            PdfPrinterDeviceIndex() {
                return -999;
            },
            MedicalDocumentContinueSelectType() {
                return MedicalDocumentContinueSelectType;
            },
            disabledChangePrintConfig() {
                return this.isHospitalAdviceShandong;
            },
            disabledChangePageSizeReduce() {
                return this.isTicketEscPosPrint;
            },
            getPrintBtnTipsInfo() {
                let info = '';
                if (this.isHospitalAdviceShandong && this.printTask.renderConfig[0].value === 1) {
                    const printMethodConfig = this.printTask.renderConfig[0].list[1];
                    const { pages } = printMethodConfig;
                    const isPrintPage = (pages || []).find((it) => {
                        return it.value;
                    });
                    if (isPrintPage) {
                        const { disabled } = isPrintPage;
                        if (disabled) {
                            info = '本页无更新，可在“打印全部医嘱“中重选本页打印';
                        }
                    }
                }
                return info;
            },
            showContinuePrintConfirmInfo() {
                let info = '';
                if (this.isHospitalAdviceShandong && this.printTask.renderConfig[0].value === 1) {
                    const printMethodConfig = this.printTask.renderConfig[0].list[1];
                    const { pages } = printMethodConfig;
                    const isPrintPage = (pages || []).find((it) => {
                        return !it.disabled && it.value;
                    });
                    if (isPrintPage) {
                        const {
                            isPageUpdated, isPageNew, label,
                        } = isPrintPage;
                        if (isPageUpdated) {
                            info = `请确认打印机已放入${label}医嘱单`;
                        } else if (isPageNew) {
                            info = '请确认打印机已放入空白打印纸';
                        }
                    }
                }
                return info;
            },
            disabledChangeReduceByPDFPrint() {
                return !!this.printTask.extra?.isPdfLodopPrint;
            },
            // 是否支持彩色打印
            isPrinterSupportColor() {
                // 小票ESC/POS不支持选择彩色
                if (this.isTicketEscPosPrint) return false;
                // Lodop打印不支持选择黑白/彩色
                if (this.printTask.mode === PrintMode.Lodop) return false;
                // 客户端打印支持选择黑白/彩色
                if (this.printTask.mode === PrintMode.Electron || PrintManager.getInstance().isEnableElectronPrint()) {
                    const printerList = PrintManager.getInstance().getPrinterList(PrintMode.Electron);
                    const curPrinter = printerList.find((printer) => printer.deviceIndex === this.printConfig.deviceIndex);
                    if (!curPrinter) return false;
                    return !!curPrinter.color;
                }
                return false;
            },
            /**
             * 是否支持选择DPI
             * @return {{flag: boolean}|{flag: boolean, supportedDPIs: *[]}}
             */
            isSupportedDPIs() {
                // 小票ESC/POS不支持选择DPI
                if (this.isTicketEscPosPrint) return { flag: false };
                // Lodop打印不支持选择DPI
                if (this.printTask.mode === PrintMode.Lodop) return { flag: false };
                // 客户端打印支持选择DPI
                if (this.printTask.mode === PrintMode.Electron || PrintManager.getInstance().isEnableElectronPrint()) {
                    const printerList = PrintManager.getInstance().getPrinterList(PrintMode.Electron);
                    const curPrinter = printerList.find((printer) => printer.deviceName === this.printConfig.deviceName);
                    if (!curPrinter) return { flag: false };
                    const { supportedDPIs } = curPrinter;
                    if (!Array.isArray(supportedDPIs) || !supportedDPIs.length) return { flag: false };
                    return {
                        flag: true,
                        supportedDPIs,
                    };
                }
                return { flag: false };
            },
            isFeeBill() {
                const isFeeListInvoice = this.printConfig.key === 'fee-list' && FEE_LIST_PRINT_MODE.includes(this.printTask?.templateKey?.businessKey);
                const isReceiptInvoice = this.printConfig.key === 'receipt';

                return this.printConfig.key === 'fee-bill' ||
                    this.printConfig.key === 'tianjin-chinese-prescription' ||
                    this.printConfig.key === 'tianjin-western-prescription' ||
                    isFeeListInvoice || isReceiptInvoice;
            },
            isFeeBillOrReceipt() {
                return this.isFeeBill || this.printConfig.key === 'receipt' || this.printConfig.key === 'price-tag';
            },
            currentOrientations() {
                const AbcPageSizeMap = window.AbcPackages.AbcPrint?.AbcPageSizeMap;
                let AbcPageSize;
                Object.keys(AbcPageSizeMap).forEach((key) => {
                    if (AbcPageSizeMap[key].name === this.printConfig.pageSize) {
                        AbcPageSize = AbcPageSizeMap[key];
                    }
                });
                if (AbcPageSize && AbcPageSize.orientations) {
                    return AbcPageSize.orientations.map((key) => {
                        return {
                            label: AbcPrintOrientationRevert[key],
                            value: key,
                        };
                    });
                }

                return [{
                    label: '纵向',
                    value: AbcPrintOrientation.portrait,
                },{
                    label: '横向',
                    value: AbcPrintOrientation.landscape,
                }];
            },
            templateInfo() {
                return this.printTask.templateKey;
            },

            /**
             * @desc 纸张列表
             * <AUTHOR>
             * @date 2021-11-24 11:22:46
             */
            pageSizeList() {
                // 不能去掉这个 console，因为这个 computed 更新依赖 this.printerListIsUpdate
                console.log('printerListIsUpdate -->', this.printerListIsUpdate);
                if (this.printConfig.deviceIndex === -1) {
                    return [];
                }
                if (this.isTicketEscPosPrint) {
                    return PrintManager.getInstance().formatPagesByTemplate(this.templateInfo.pages);
                }
                const { deviceName } = this.printConfig;
                return PrintManager.getInstance().mergedPagesByDeviceName(deviceName , this.templateInfo.pages);
            },
            disablePrint() {
                return (
                    !this.previewDoc ||
                    this.isEmptyContent ||
                    !this.printerList.length ||
                    (this.printConfig.deviceIndex === -1) ||
                    !this.printConfig.pageSize ||
                    !(this.printConfig.orient && this.printConfig.orient.toString()) ||
                    (this.hasHeightLevel && !this.printConfig.pageHeightLevel)
                );
            },
            isLandscape() {
                const { orient } = this.printConfig;
                return orient === AbcPrintOrientation.landscape;
            },
            getContentStyles() {
                return `
                    padding: 0;
                `;
            },
            getWidth() {
                const width = $('.app-container').width() || $('.app-wrapper').width();
                return width - (80 * 2);
            },
            getPreviewStyle() {
                return {
                    margin: '12px auto',
                    boxShadow: 'rgba(0, 0, 0, 0.06) 0px 0px 10px 0px, rgba(6, 15, 26, 0.07) 0px 0px 0px 1px',
                };
            },
            /**
             * @desc 当前纸张信息
             * <AUTHOR>
             * @date 2021-11-24 11:23:36
             */
            curPageInfo() {
                if (this.printTask.extra) {
                    // 如果有默认纸张配置 就不需要在配置里面找了
                    const {
                        defaultPageOrient, defaultPageSize, defaultPageSizeReduce,
                    } = this.printTask.extra;
                    if (defaultPageOrient || defaultPageSize || defaultPageSizeReduce) {
                        return null;
                    }
                }
                return this.pageSizeList.find((item) => {
                    return item.paper.name === this.printConfig.pageSize;
                }) || null;
            },
            /**
             * @desc 等分纸张列表
             * <AUTHOR>
             * @date 2021-11-24 11:24:30
             */
            curPageHeightLevelList() {
                return this.curPageInfo && this.curPageInfo.paper && this.curPageInfo.paper.heightLevels || null;
            },

            isEmptyContent() {
                // 对用药标签做单独处理
                if (this.isMedicineTag) {
                    if (this.printTask.staticRenderConfig) {
                        return !this.printTask.staticRenderConfig.length;
                    }
                    return true;
                }
                // 检验报告打印配置为模式切换，对内容无影响，必定存在可打印内容
                if (this.isExaminationReport) {
                    return false;
                }
                if (this.isHospitalInspect) {
                    return this.printTask.renderConfig.length && this.printTask.renderConfig.every((item) => item.checked === 0);
                }

                if (this.isHospitalAdviceShandong) {
                    const printMethodConfig = this.printTask.renderConfig[0].list[this.printTask.renderConfig[0].value];
                    if (printMethodConfig.disabled) return true;
                    if (!printMethodConfig.pages.length) return true;
                    // 选择续打
                    if (this.printTask.renderConfig[0].value) {
                        const selectedPage = printMethodConfig.pages.find((page) => page.value);
                        if (selectedPage) {
                            return selectedPage.disabled;
                        }
                        return true;
                    }
                    // 选择全部打印
                    return printMethodConfig.pages.every((it) => !it.value);
                }

                if (!this.printTask.staticRenderConfig || !this.printTask.staticRenderConfig.length) return false;

                const getCheckboxItemList = (data, res = []) => {
                    data.forEach((item) => {
                        if (item.type === 'checkbox') {
                            res.push(item);
                        }
                        const objectKeys = Object.keys(item);
                        for (const key of objectKeys) {
                            if (Array.isArray(item[key]) && item[key].length) {
                                getCheckboxItemList(item[key], res);
                            }
                        }
                    });
                    return res;
                };

                const checkboxItemList = getCheckboxItemList(this.printTask.renderConfig);

                return !checkboxItemList.filter((c) => c.value).length;
            },
            /**
             * @Description: 区分预览类型
             * <AUTHOR> Cai
             * @date 2022/08/02 15:13:07
            */
            isExaminationTag() {
                return this.printTask?.templateKey?.businessKey === 'examination-tag' || this.printTask?.templateKey?.businessKey === 'examination' ;
            },

            isExamApplySheet() {
                return this.printTask?.templateKey?.businessKey === 'examination-apply-sheet';
            },

            isHospitalInspect() {
                return this.printTask?.templateKey?.businessKey === 'hospital-inspect';
            },

            isHospitalAdviceShandong() {
                console.log('%c staticRenderConfig\n', 'background: green; padding: 0 5px', Clone(this.printTask.staticRenderConfig));
                console.log('%c renderConfig\n', 'background: green; padding: 0 5px', Clone(this.printTask.renderConfig));
                return this.printTask?.templateKey?.businessKey === 'hospital-advice-shandong';
            },

            isExaminationReport() {
                return [
                    'examination-report',
                    'examination-report-landscape',
                ].includes(this.printTask?.templateKey?.businessKey);
            },

            printContentVisible() {
                if (!this.printTask || !this.printTask.templateKey) return false;

                return ![
                    'examination-cloud-report',
                    'examination-report-pdf',
                ].includes(this.printTask.templateKey.businessKey);
            },

            isExaminationLandscape() {
                return this.printTask?.AbcPrintInstance?.matchedTemplateItem?.windowRegisteredName === 'ExaminationReportLandscape';
            },

            isMedicineTag() {
                return this.printTask?.templateKey?.businessKey === 'medicine-tag' ||
                    this.printTask?.templateKey?.businessKey === 'medicine-tag-customized' ||
                    this.printTask?.templateKey?.businessKey === 'hospital-medicine-tag';
            },

            DuplexModeOptions() {
                return DuplexModeOptions;
            },

            DuplexMode() {
                return DuplexMode;
            },

            PaperOrientationOptions() {
                return PaperOrientationOptions;
            },

            PrintPageRange() {
                return PrintPageRange;
            },

            pageTotal() {
                const abcPageHTMLList = window.AbcPackages.AbcPrint.splitAbcPageHTML(this.printTask.template);
                return abcPageHTMLList.length;
            },

            previewDoc() {
                const injectScrollBarStyle = `
                    <style>
                        body {
                            overflow-y: auto;
                        }
                        body::-webkit-scrollbar {
                            width: 10px;
                            height: 12px;
                        }
                        body::-webkit-scrollbar-thumb {
                            cursor: pointer;
                            background: #e6eaed;
                            background-clip: content-box;
                            border: 1px solid transparent;
                            border-radius: var(--abc-border-radius-small);
                        }
                        body::-webkit-scrollbar-thumb:hover {
                            cursor: pointer;
                            background: #dee2e6;
                            background-clip: content-box;
                            border: 1px solid #dee2e6;
                            border-radius: var(--abc-border-radius-small);
                        }
                        body::-webkit-scrollbar-thumb {
                            visibility: hidden;
                        }
                        body:hover::-webkit-scrollbar-thumb {
                            visibility: visible;
                        }
                        body::-webkit-scrollbar-track {
                            background: transparent;
                            opacity: 0;
                        }
                        body:hover::-webkit-scrollbar-track {
                            background: transparent;
                            opacity: 0;
                        }
                    </style>
                `;

                if (!this.printConfig.advance) {
                    if (this.printTask.template.includes('</head>')) {
                        return this.printTask.template.replace('</head>', `${injectScrollBarStyle}</head>`);
                    }
                    return this.printTask.template;
                }
                const {
                    slice,
                    select,
                } = getSliceByPrintConfig(this.printConfig.advance, this.pageTotal);

                const abcPageHTMLList = window.AbcPackages.AbcPrint.splitAbcPageHTML(this.printTask.template);
                const {
                    mergeAbcPageHTML,
                } = window.AbcPackages.AbcPrint;
                const html = mergeAbcPageHTML(
                    slice ? abcPageHTMLList.slice(slice[0], slice[1]) : abcPageHTMLList.filter((item, index) => select.includes(index)),
                );

                if (this.isSupportMedicalDocumentContinuePrintMode && this.printConfig.advance.printRange.range === PrintPageRange.MedicalDocumentContinue) {
                    return injectScriptToHTML(html, `(${medicalDocumentContinueInject.default.toString()})(${JSON.stringify(this._position)})`);
                }

                if (html.includes('</head>')) {
                    return html.replace('</head>', `${injectScrollBarStyle}</head>`);
                }
                return html;
            },

            showPaperOrientation() {
                const {
                    AbcPageSizeMap: {
                        A5,
                    },
                } = window.AbcPackages.AbcPrint;
                return this.printConfig.pageSize === A5.name;
            },

            // 是否客户端打印
            isPhysicalMargin() {
                const printV2Version = PrintManager.getInstance().getFeature('printConfig');
                const { prescriptionVersion } = printV2Version;
                const { printConfig = {} } = this.printTask;
                const { key } = printConfig;
                return !!(prescriptionVersion && key === 'prescriptionV2');
            },

            isSupportPaperOrientation() {
                return getViewDistributeConfig().Print.isSupportPaperOrientation;
            },
            // 是否需要展示跳转到打印设置文案
            needShowPrintConfig() {
                const { printConfig } = this.printTask;
                const { key = '' } = printConfig || {};
                return PRINT_PREVIEW_NEED_SHOW_PRINT_CONFIG.includes(key);
            },
            // 是否开启新版处方
            isNewPrescriptionVersion() {
                return PrintManager.getInstance().isNewPrescriptionVersion();
            },
            // 是否支持病历续打
            isSupportMedicalDocumentContinuePrintMode() {
                return this.printTask.extra?.isSupportMedicalDocumentContinuePrintMode;
            },
            // 是否展示续打提示
            enableContinuePrintTooltip() {
                return this.medicalDocumentContinue.select === MedicalDocumentContinueSelectType.CONTINUE;
            },
            // 续打选中的页码 index
            continuePrintSelectedPageIndex: {
                get() {
                    const continuePages = this.printTask.renderConfig[0].list[1]?.pages || [];
                    const selectedPage = continuePages.find((page) => page.value) || continuePages[0];
                    return selectedPage.page;
                },
                set(val) {
                    const continueSelectedPage = this.printTask.renderConfig[0].list[1]?.pages?.find?.((page) => page.page === val);
                    if (continueSelectedPage) {
                        this.printTask.renderConfig[0].list[1]?.pages.forEach((page) => {
                            page.value = false;
                        });
                        continueSelectedPage.value = true;
                    }
                },
            },
            isTicketEscPosPrint() {
                return !!this.printTask.extra?.isTicketEscPosPrint;
            },
        },
        watch: {
            visible(val) {
                if (!val) {
                    this.destroyElement();
                }
            },
            'printTask.renderConfig': {
                handler(val) {
                    if (this.needReportLog && this.reportLogData) {
                        Logger.report({
                            scene: this.reportLogData.scene,
                            data: {
                                info: 'printTask.renderConfig 更新',
                                data: {
                                    renderConfig: JSON.stringify(val),
                                    keyId: this.reportLogData.keyId,
                                },
                            },
                        });
                    }
                    this._renderPrint?.();
                    this.isTotalChoose = this.checkIsTotalChoose();
                },
                deep: true,
                immediate: true,
            },

            'printConfig.deviceIndex': function(val) {
                if (this.needReportLog && this.reportLogData) {
                    Logger.report({
                        scene: this.reportLogData.scene,
                        data: {
                            info: '打印机设置的deviceIndex更新',
                            data: {
                                deviceIndex: val,
                                keyId: this.reportLogData.keyId,
                            },
                        },
                    });
                }
                this.changeRecommondOffset();
            },

            'printConfig.pageSize': function(val) {
                if (this.needReportLog && this.reportLogData) {
                    Logger.report({
                        scene: this.reportLogData.scene,
                        data: {
                            info: '打印机设置的pageSize更新',
                            data: {
                                deviceIndex: val,
                                keyId: this.reportLogData.keyId,
                            },
                        },
                    });
                }
                this.changeRecommondOffset();
            },
            /**
             * 客户端打印需要对打印机物理边距做兼容处理
             */
            'printConfig.deviceName': {
                handler(val) {
                    if (this.isPhysicalMargin && val) {
                        const {
                            pageSizeReduce, printerOffset,
                        } = getPrinterPageSizeReduce({
                            deviceName: val, pageSizeReduce: this.printConfig.pageSizeReduce,
                        });
                        this.printConfig.pageSizeReduce = pageSizeReduce;
                        this.printerOffset = printerOffset;
                        this.handlePageSizeReduceChange();
                    }
                },
                immediate: true,
            },

            'medicalDocumentContinue.select': {
                handler(val) {
                    if (val === MedicalDocumentContinueSelectType.CONTINUE) {
                        this.printConfig.advance.printRange.range = PrintPageRange.MedicalDocumentContinue;
                    }
                    if (val === MedicalDocumentContinueSelectType.ALL) {
                        this.printConfig.advance.printRange.range = PrintPageRange.SelectPage;
                    }
                },
            },
        },
        created() {
            this._onChangeHospitalShandongAdvicePrintMethod = debounce(this.onChangeHospitalShandongAdvicePrintMethod, 200, true);
            this._renderPrint = debounce(this.renderPrint, 100);
            this._changePreviewZoom = debounce(this.changePreviewZoom, 100);
            this._cachePrintConfig = Clone(this.printConfig);
            // 兼容原本用户选择打印机纸张不在系统支持纸张范围内，渲染推荐纸张的数据
            if (this.printConfig.deviceIndex !== -1) {
                const pageIndex = PrintManager.getInstance().mergedPagesByDeviceName(this.printConfig.deviceName , this.templateInfo.pages).findIndex((item) => {
                    return item.paper.name === this.printConfig.pageSize;
                });
                if (pageIndex === -1) {
                    const recommendPage = PrintManager.getInstance().mergedPagesByDeviceName(this.printConfig.deviceName , this.templateInfo.pages).find((item) => {
                        return item.isRecommend;
                    });
                    this.printConfig.pageSize = recommendPage.paper.name;
                    if (this.needReportLog && this.reportLogData) {
                        Logger.report({
                            scene: this.reportLogData.scene,
                            data: {
                                info: 'preview dialog created, 没有找到纸张，使用推荐纸张',
                                data: {
                                    pageSize: this.printConfig.pageSize,
                                    _cachePrintConfig: JSON.stringify(this._cachePrintConfig),
                                    keyId: this.reportLogData.keyId,
                                },
                            },
                        });
                    }
                    this.handlePageSizeChange();
                } else {
                    if (this.needReportLog && this.reportLogData) {
                        Logger.report({
                            scene: this.reportLogData.scene,
                            data: {
                                info: 'created生命周期，触发模板更新',
                                data: {
                                    keyId: this.reportLogData.keyId,
                                },
                            },
                        });
                    }
                    this.getTemplateStr();
                }
            }
            const tempPrinterList = PrintManager.getInstance().getPrinterList(this.printTask.mode);
            if (!this.isFeeBillOrReceipt) {
                this.printerList = [
                    ...tempPrinterList,
                    {
                        deviceIndex: this.PdfPrinterDeviceIndex,
                        deviceName: '另存为PDF',
                        driveName: '另存为PDF',
                        pageList: [],
                        name: '另存为PDF',
                    },
                ];
            } else {
                this.printerList = tempPrinterList;
            }
            const cachePrinterList = Clone(this.printerList);

            // 获取一次最新的打印机列表
            ABCClientManager.getInstance().loadPrinterList(false).then(() => {
                const newPrinterList = PrintManager.getInstance().getPrinterList(this.printTask.mode);
                // 如果新的打印机列表是空的，则不更新
                if (!newPrinterList.length) return;

                if (!this.isFeeBillOrReceipt) {
                    this.printerList = [
                        ...newPrinterList,
                        {
                            deviceIndex: this.PdfPrinterDeviceIndex,
                            deviceName: '另存为PDF',
                            driveName: '另存为PDF',
                            pageList: [],
                            name: '另存为PDF',
                        },
                    ];
                } else {
                    this.printerList = newPrinterList;
                }
                this.printerListIsUpdate = !isEqual(cachePrinterList, Clone(this.printerList));
            });

            // 没有打印机时也需要渲染
            if (!this.printerList.length) {
                if (this.needReportLog && this.reportLogData) {
                    Logger.report({
                        scene: this.reportLogData.scene,
                        data: {
                            info: '无打印机列表',
                            data: {
                                keyId: this.reportLogData.keyId,
                            },
                        },
                    });
                }
                this.getTemplateStr();
            }

            // 有打印机选择到第一台
            if (this.printerList.length && this.printConfig.deviceIndex === -1) {
                this.printConfig.deviceIndex = this.printerList[0].deviceIndex;
                this.handlePrinterChange();
            }

            if (this.needReportLog && this.reportLogData) {
                Logger.report({
                    scene: this.reportLogData.scene,
                    data: {
                        info: 'print preview dialog, 打印机列表',
                        data: {
                            printerList: JSON.stringify(this.printerList),
                            keyId: this.reportLogData.keyId,
                        },
                    },
                });
            }

            // 如果支持文书续打
            // 则将printRange设置为选择页面
            // 同时监听pageToal
            // 如果有值则全选
            if (this.isSupportMedicalDocumentContinuePrintMode) {
                this._position = this.printTask.extra.defaultPosition || {
                    pageIndex: 0,
                    top: 0,
                };
                if (this._position.printMethod) {
                    this.medicalDocumentContinue.select = this._position.printMethod;
                }
                this.printConfig.advance.printRange.range = PrintPageRange.SelectPage;
                const unWatch = this.$watch(
                    () => this.pageTotal,
                    (val) => {
                        if (val) {
                            this.printConfig.advance.printRange.selectPage = new Array(val).fill(0).map((item, index) => index);
                            unWatch();
                        }
                    },
                    {},
                );

                this.$once('hook:beforeDestroy', () => {
                    unWatch();
                });

                const onMessage = (e) => {
                    if (e.data.type === 'update-continue-page-data') {
                        this._position = e.data.data;
                        this.continuePrintPageIndex = this._position.pageIndex + 1;
                    }
                };

                window.addEventListener('message', onMessage);

                this.$once('hook:beforeDestroy', () => {
                    window.removeEventListener('message', onMessage);
                });

            }
        },
        mounted() {
            on(document, 'keydown', this.handleEnterEvent);
            this.bindGodModeKeyPress();
        },
        beforeDestroy() {
            off(document, 'keydown', this.handleEnterEvent);
            off(document, 'keyup', this._keyupEvent);
            clearTimeout(this._timer);
        },
        methods: {
            isABCClient,
            async changeRecommondOffset() {
                await this.$nextTick();
                // 切换打印需要匹配最佳的打印配置
                if (this.printConfig.deviceIndex > -1 && this.printConfig.key === 'fee-bill') {
                    this.printConfig.offset = Clone(window.AbcPackages.AbcPrint.recommendOffsetMatcher(this.printConfig.deviceName, this.printConfig.pageSize));
                }
            },
            /**
             * @desc 打印渲染的htmL
             * <AUTHOR>
             * @date 2021-11-24 11:30:02
             */
            async getTemplateStr() {
                const zoom = this.getPreviewZoom();
                this.contentLoading = true;
                // 设置3sloading超时
                this._timer = setTimeout(() => {
                    this.contentLoading = false;
                }, 3000);
                await this.printTask.getTemplateStr({
                    customStyles: {
                        ...this.getPreviewStyle,
                        zoom,
                    },
                    isPreview: true,
                });
                this.contentLoading = false;
            },
            /**
             * @desc 获取预览缩放比例
             * <AUTHOR>
             * @date 2021-11-24 11:29:43
             */
            getPreviewZoom() {
                if (this.isChangePreviewZoom) return this.previewZoom;

                this.previewZoom = 0.83; // 标准 210mm 高度的缩放值
                const pageSizeInfo = this.pageSizeList.find((item) => {
                    return item.paper.name === this.printConfig.pageSize;
                });
                if (this.isTicketEscPosPrint) {
                    this.previewZoom = 0.5;
                } else if (pageSizeInfo) {
                    const paperInfo = pageSizeInfo.paper;

                    // 如果是竖向的 A4，则缩放比例设置为 0.6
                    if (this.printConfig.orient === AbcPrintOrientation.portrait && parseInt(paperInfo.width) === 210 && parseInt(paperInfo.height) === 297) {
                        this.previewZoom = 0.6;
                    } else if (this.printConfig.orient === AbcPrintOrientation.portrait && this.printConfig.pageHeightLevel === '一等分' && parseInt(paperInfo.width) === 241 && parseInt(paperInfo.height) === 280) {
                        // 如果是竖向的针式纸一等分，则缩放比例设置为 0.63
                        this.previewZoom = 0.63;
                    } else {
                        const width = this.isLandscape ? parseInt(paperInfo.height) : parseInt(paperInfo.width);
                        if (width > 210) {
                            this.previewZoom = (210 / width);
                        }
                    }
                }
                return this.previewZoom;
            },

            changePreviewZoom(type = 'add') {
                let stopReduceFlag = false;
                switch (type) {
                    case 'add':
                        this.previewZoom += 0.1;
                        break;
                    case 'reduce':
                        if (this.previewZoom <= 0.2) {
                            stopReduceFlag = true;
                            this.$Toast.error('已经是最小了');
                        } else {
                            this.previewZoom -= 0.1;
                        }
                        break;
                    default:
                        stopReduceFlag = true;
                        break;
                }
                if (!stopReduceFlag) {
                    this.isChangePreviewZoom = true;
                    this.getTemplateStr();
                }
            },

            /**
             * @desc 纸张,方向 切换
             */
            renderPrint() {
                const zoom = this.getPreviewZoom();
                // 防止 pdf 的打印机设置被缓存下来，影响下次打印 https://www.tapd.cn/tapd_fe/22044681/bug/detail/1122044681001090820
                const printDeviceIndex = this.printConfig?.deviceIndex === this.PdfPrinterDeviceIndex ?
                    this.printTask?.printConfig?.deviceIndex :
                    this.printConfig?.deviceIndex;

                this.printTask.changeHandler(
                    Object.assign(this.printTask.printConfig, this.printConfig, {
                        deviceIndex: printDeviceIndex,
                    }),
                    this.printTask.templateKey,
                    {
                        customStyles: {
                            ...this.getPreviewStyle,
                            zoom,
                        },
                        isPreview: true,
                    },
                );
            },
            /**
             * @desc 切换打印，选择推荐纸张
             * <AUTHOR>
             * @date 2021-11-24 11:26:22
             */
            handlePrinterChange() {
                // 恢复 zoom
                this.isChangePreviewZoom = false;

                const curPrinter = this.printerList.find((item) => {
                    return item.deviceIndex === this.printConfig.deviceIndex;
                });

                // 如果打印机离线了，则调起提示弹窗
                if (this.isNewPrescriptionVersion && curPrinter.offline) {
                    new PrinterOfflineDialog().generateDialogAsync({ parent: this });
                    this.cancelPrint();
                }

                if (curPrinter) {
                    const {
                        DriverName: driveName,
                        name: deviceName,
                    } = curPrinter;
                    this.printConfig.deviceName = deviceName;
                    this.printConfig.driveName = driveName;
                    this.printConfig.dpi = null;
                }
                const customRecommnedPage = typeof this.matchRecommendPagesizeCallback === 'function' && this.matchRecommendPagesizeCallback(this.pageSizeList);
                const recommendPage = customRecommnedPage || this.pageSizeList.find((item) => {
                    return item.isRecommend;
                });

                if (recommendPage) {
                    this.printConfig.pageSize = recommendPage.paper.name;
                }

                if (this.needReportLog && this.reportLogData) {
                    Logger.report({
                        scene: this.reportLogData.scene,
                        data: {
                            info: 'print preview dialog, 切换打印机，选择纸张',
                            data: {
                                printConfig: JSON.stringify(this.printConfig),
                                keyId: this.reportLogData.keyId,
                            },
                        },
                    });
                }
                this.handlePageSizeChange();
            },
            handleChangeHeightLevelOrOrient() {
                // 恢复 zoom
                this.isChangePreviewZoom = false;

                this._renderPrint();
            },
            /**
             * @desc 选择纸张，选择默认布局,改变横纵向或者等分布局
             * <AUTHOR>
             * @date 2021-11-24 11:27:39
             */
            handlePageSizeChange(resetPageSizeReduce = false) {
                // eslint-disable-next-line vue/valid-next-tick
                return this.$nextTick(() => {
                    // 恢复 zoom
                    this.isChangePreviewZoom = false;

                    if (this.curPageInfo) {
                        // 带入默认纸张方向信息
                        this.printConfig.orient = this.curPageInfo.defaultOrientation;
                        this.printConfig.pageHeightLevel = this.curPageInfo.defaultHeightLevel;
                        this.printConfig.pageWidth = this.curPageInfo.paper.width;
                        this.printConfig.pageHeight = this.curPageInfo.paper.height;
                        // 设置默认边距
                        const {
                            offsetX, offsetY,
                        } = PrintManager.getInstance().getOffsetByDeviceName(this.printConfig.deviceName);
                        // 发票不自动设置边距
                        if (!this.isFeeBill && (offsetX !== undefined || offsetY !== undefined) && resetPageSizeReduce) {
                            console.warn('使用系统自带边距');
                            this.printConfig.pageSizeReduce = {
                                bottom: offsetY,
                                left: offsetX,
                                right: offsetX,
                                top: offsetY,
                            };
                        }
                        if (this.needReportLog && this.reportLogData) {
                            Logger.report({
                                scene: this.reportLogData.scene,
                                data: {
                                    info: 'preview dialog , 切换纸张后更新的信息',
                                    data: {
                                        _cachePrintConfig: JSON.stringify(this._cachePrintConfig),
                                        printConfig: JSON.stringify(this.printConfig),
                                        keyId: this.reportLogData.keyId,
                                    },
                                },
                            });
                        }
                        console.log('切换纸张后更新的信息', this.printConfig.orient, this.printConfig.pageHeightLevel);
                    }
                    this._renderPrint();
                });
            },

            async handlePageSizeReduceChange(params) {
                // 上下左右边距不能超过边界值
                if (params) {
                    const {
                        key, value,
                    } = params;
                    this.printConfig.pageSizeReduce[key] = value;
                }

                await this.$nextTick();
                this._renderPrint();
            },

            isTinyPrinter(i) {
                if (i === this.PdfPrinterDeviceIndex) return false;
                return PrintManager.getInstance().supportTinyPrinter(i);
            },

            // 获取指定的文书打印内容
            getPrintContent(templateStr) {
                const {
                    slice,
                    select,
                } = getSliceByPrintConfig(this.printConfig.advance, this.pageTotal);

                const abcPageHTMLList = window.AbcPackages.AbcPrint.splitAbcPageHTML(templateStr);
                const {
                    mergeAbcPageHTML,
                } = window.AbcPackages.AbcPrint;
                const html = mergeAbcPageHTML(
                    slice ? abcPageHTMLList.slice(slice[0], slice[1]) : abcPageHTMLList.filter((item, index) => select.includes(index)),
                );

                if (this.isSupportMedicalDocumentContinuePrintMode && this.printConfig.advance.printRange.range === PrintPageRange.MedicalDocumentContinue) {
                    return injectScriptToHTML(html, `(${medicalDocumentContinueInject.default.toString()})(${JSON.stringify(this._position)})`);
                }

                return html;
            },
            async handleSavePdf() {
                this.printLoading = true;
                // 处理pdf纸张的问题
                let pdfWidth = null;
                let pdfHeight = null;
                let pdfOptions = {};
                let pdfBase64 = null;
                let templateStr = null;
                let fileName = `${this.printTask.printConfig.label}-${formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss')}`;
                const advanceBusinessKeyList = [
                    window.AbcPackages.AbcTemplates.temperatureGraph.businessKey,
                    window.AbcPackages.AbcTemplates.hospitalMedicalRecord.businessKey,
                ];

                const enableAdvancedPagination = advanceBusinessKeyList.includes(this.printTask.templateKey.businessKey);

                if (this.printConfig.pageWidth && this.printConfig.pageHeight) {
                    // 字符串 转换成 数字
                    pdfHeight = parseInt(this.printConfig.pageHeight);
                    pdfWidth = parseInt(this.printConfig.pageWidth);
                }

                let orientation = this.printConfig.orient === 1 ? 'portrait' : 'landscape';
                const tagPrintList = [
                    'medicine-tag',
                    'hospital-medicine-tag',
                    'medicine-tag-customized',
                    'patient-tag',
                    'examination-tag',
                    'hospital-bedside-card',
                ];
                if (tagPrintList.includes(this.printTask.templateKey.businessKey) && pdfWidth > pdfHeight && this.printConfig.orient === 1) {
                    orientation = 'landscape';
                }


                if (this.printTask.extra?.isPdfLodopPrint) {
                    pdfBase64 = this.printTask.extra.pdfBase64;
                } else {
                    templateStr = await this.printTask.getTemplateStr({
                        isGetTemplate: true,
                    });

                    if (this.printConfig.advance) {
                        console.log('this.printConfig.advance', this.printConfig.advance);
                        templateStr = this.getPrintContent(templateStr);
                    }
                }

                if (this.printTask.extra?.pdfOptions?.filename) {
                    fileName = `${this.printTask.extra.pdfOptions.filename}-${formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss')}`;
                }

                pdfOptions = {
                    jsPDF: {
                        format: pdfHeight || pdfWidth ? [pdfWidth, pdfHeight || 245] : this.printConfig.pageSize,
                        unit: 'mm',
                        orientation,
                    },
                };
                const options = {
                    filename: `${fileName}.pdf`,
                    pdfBase64,
                    html: templateStr,
                    pdfOptions,
                    margin: [
                        this.printConfig.pageSizeReduce.top,
                        this.printConfig.pageSizeReduce.left,
                        this.printConfig.pageSizeReduce.bottom,
                        this.printConfig.pageSizeReduce.right,
                    ],
                    enableAdvancedPagination,
                };

                const pdfTools = new HtmlToPDF(options);

                try {
                    console.log('pdf配置', options);
                    await pdfTools.savePDF();
                    this.visible = false;
                } catch (error) {
                    console.error('生成pdf文件失败', error);
                    throw error;
                } finally {
                    this.printLoading = false;
                }

            },

            handlePrint() {
                const next = async () => {
                    // 保存为pdf
                    if (this.printConfig.deviceIndex === this.PdfPrinterDeviceIndex) {
                        await this.handleSavePdf();

                        // 调用打印成功回调
                        if (typeof this.printTask.onPrintSuccess === 'function') {
                            this.printTask.onPrintSuccess(this.printTask);
                        }
                        if (typeof this.printTask.onPrintLog === 'function') {
                            this.printTask.onPrintLog();
                        }
                        if (typeof this.printTask.onAddPrintCount === 'function') {
                            this.printTask.onAddPrintCount();
                        }
                        return;
                    }


                    if (this.isSupportMedicalDocumentContinuePrintMode) {
                        this.printTask.extra.useDOM2Canvas = true;

                        if (this.medicalDocumentContinue.select === MedicalDocumentContinueSelectType.CONTINUE) {
                            // 续打必须启用DOM2Canvas
                            this.printTask.extra.continuePrintData = this._position;
                        }

                        const { defaultPosition = {} } = this.printTask.extra;

                        if (
                            (this.medicalDocumentContinue.select === MedicalDocumentContinueSelectType.ALL &&
                                !this.printTask.extra.defaultPosition &&
                                this.printConfig.advance.printRange.selectPage.length === this.pageTotal ||
                                this.medicalDocumentContinue.select === MedicalDocumentContinueSelectType.CONTINUE)
                        ) {
                            // 找到iframe中最后一个abc-page的最后一个元素的位置
                            const iframe = this.$refs.previewFrame;
                            const lastPageIndex = iframe.contentDocument.querySelectorAll('.abc-page').length - 1;
                            const lastPage = iframe.contentDocument.querySelectorAll('.abc-page')[lastPageIndex];
                            const lastContent = lastPage.querySelector('.abc-page-content');
                            const lastElement = lastContent.lastElementChild;
                            const top = (lastElement.offsetTop + lastContent.offsetTop + lastElement.clientHeight);

                            defaultPosition.pageIndex = lastPageIndex;
                            defaultPosition.top = top;
                        }

                        defaultPosition.printMethod = this.medicalDocumentContinue.select;

                        this.printTask.extra.onUpdatePrintPosition(defaultPosition);
                    }
                    if (this.printConfig.deviceIndex !== -1 && this.printConfig.pageSize) {
                        this.printHandler(this.printConfig);
                        this.visible = false;
                    }
                };

                if (this.enableContinuePrintTooltip) {
                    return this.$confirm({
                        type: 'info',
                        title: '打印提示',
                        content: `请确保病历第${this.continuePrintPageIndex}页已放回打印机，确定后将开始打印`,
                        onConfirm: next,
                    });
                }

                if (this.showContinuePrintConfirmInfo) {
                    this.$modal({
                        type: 'warn',
                        preset: 'alert',
                        title: '提示',
                        content: this.showContinuePrintConfirmInfo,
                        onConfirm: () => {
                            if (this.printConfig.deviceIndex !== -1 && this.printConfig.pageSize) {
                                this.printHandler(this.printConfig);
                                this.visible = false;
                            }
                        },
                    });
                    return;
                }

                next();
            },

            changeTotalChoose(totalStatus) {
                this.printTask?.renderConfig?.forEach((item) => {
                    item.value = false;
                });
                if (totalStatus) {
                    this.printTask?.renderConfig?.forEach((item) => {
                        item.value = true;
                    });
                }
            },

            checkIsTotalChoose() {
                const status = this.printTask.renderConfig.find((item) => {
                    return !item.value;
                });
                return !status;
            },

            cancelPrint() {
                Object.assign(this.printTask.printConfig, this._cachePrintConfig);
                this.visible = false;
                // 正常打印点击取消有printCancelHandler
                // 如果不预览直接打印,由于打印机匹配不上导致强制弹出预览弹窗再点击取消,就没有printCancelHandler
                typeof this.printCancelHandler === 'function' && this.printCancelHandler();
            },

            async handleDebug() {
                if (this.printConfig.deviceIndex !== -1 && this.printConfig.pageSize) {
                    this.printTask.getPageWidthAndHeight();
                    await this.printTask.getTemplateStr();
                    const printManager = PrintManager.getInstance();
                    printManager.debug(this.printTask.printConfig, this.printTask.template, this.printTask.mode, this.printTask.extra);
                    this._renderPrint();
                }
            },

            /**
             * @desc 响应回车事件触发打印
             * <AUTHOR>
             * @date 2021-11-24 11:28:48
             */
            handleEnterEvent(e) {
                const KEY_ENTER = 13;
                if (e.keyCode === KEY_ENTER) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.handlePrint();
                }
            },

            async ok() {
                if (typeof this.submit === 'function') {
                    try {
                        this.saveLoading = true;
                        await this.submit();
                        this.saveLoading = false;
                    } catch (e) {
                        this.saveLoading = false;
                    }
                } else {
                    this.visible = false;
                }
            },

            async no() {
                if (typeof this.cancel === 'function') {
                    this.cancel();
                }
                this.visible = false;
            },

            destroyElement() {
                this.$destroy(true);
                this.$el.parentNode && this.$el.parentNode.removeChild(this.$el);
            },


            bindGodModeKeyPress() {
                this._keyupEvent = (event) => {
                    const key = event.keyCode;
                    if (key === 80) {
                        this.handleDebug();
                    }
                };

                document.addEventListener('keyup', this._keyupEvent);
            },

            handlePrintAllExamItems(i) {
                const isChecked = this.groupTotalChoose[i];

                this.printTask?.renderConfig[i].examItems?.forEach((item) => {
                    item.value = isChecked;
                });
            },

            formatDate,

            handleClickPageRangeInput() {
                // input点击不能冒泡到父元素
                this.printConfig.advance.printRange.range = PrintPageRange.Specify;
            },
            async handleOpenAddPrinterDriverDialog() {
                const { default: AddPrinterDriverModal } = await AddPrinterDriverModalModule();
                new AddPrinterDriverModal().generateDialogAsync({ parent: this });
                this.cancelPrint();
            },
            onChangeHospitalShandongAdvicePrintMethod(val) {
                console.log('%c onChangeHospitalShandongAdvicePrintMethod\n', 'background: green; padding: 0 5px', val);
                const printMethodConfig = this.printTask.renderConfig[0].list[val];
                const {
                    disabled, disabledReasonContent, disabledReasonTitle, disabledPage,
                } = printMethodConfig;

                if (disabled) {
                    this.$confirm({
                        type: 'warn',
                        title: disabledReasonTitle,
                        content: disabledReasonContent,
                        confirmText: '确定',
                        showCancel: false,
                        appendToBody: true,
                        onConfirm: () => {
                            const allPrintMethodConfig = this.printTask.renderConfig[0].list[1 - val];
                            const { pages } = allPrintMethodConfig;
                            pages.forEach((pageItem) => {
                                pageItem.value = pageItem.page >= disabledPage;
                            });
                        },
                    });

                    return;
                }

                this.printTask.renderConfig[0].value = val;
            },
            openAdvicePrintRecordDialog() {
                const { data } = this.printTask;
                const {
                    patientOrderId, printCategory, adviceType, patientHospitalInfo, clinicName,
                } = data || {};
                if (patientOrderId) {
                    new AdvicePrintRecordDialog({
                        patientOrderId, printCategory, contentStylesProp: this.getContentStyles, adviceType, patientHospitalInfo, clinicName,
                    }).generateDialogAsync({ parent: this });
                }
            },
        },
    };
</script>

<style lang="scss">
    @import 'src/styles/theme';
    @import 'src/styles/mixin';

    .print-config-tooltip-wrapper {
        display: flex;
        align-items: center;
    }

    .print-config-tooltip-text {
        padding-right: 4px;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: var(--abc-color-T2);
    }

    .print-config-tooltip-info-text {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: var(--abc-color-T1);
    }

    .print-config-tooltip-info-btn {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        color: var(--abc-color-Theme1);
    }

    .print-dialog-option-border {
        display: flex;
        align-items: center;
        height: 9px;
        padding: 0 10px;
        color: #8d9aa8;
        cursor: default;

        &:hover {
            background-color: transparent;
        }

        &::after {
            display: block;
            flex: 1;
            height: 1px;
            font-size: 0;
            content: '';
            background-color: $P6;
        }
    }

    .abc-print-preview-dialog-wrapper.abc-dialog-wrapper {
        .abc-dialog-cover {
            background-color: transparent;
        }
    }

    .abc-print-preview-dialog.abc-dialog {
        overflow: hidden;
        border-radius: 0 0 8px 8px;
        box-shadow: var(--abc-shadow-1);

        .checkbox-wrapper {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 192px;
        }

        .dialog-content {
            display: flex;
            height: 100%;

            .print-view {
                position: relative;
                width: calc(100% - 274px);
                height: 100%;
                overflow: hidden;
                background-color: var(--abc-color-bg-main);

                iframe {
                    border-radius: var(--abc-border-radius-small);
                }

                .preview-iframe-zoom-btn {
                    position: absolute;
                    bottom: 48px;
                    left: 40px;
                    background-color: #81868b !important;
                    border-color: #81868b !important;
                    box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.1);

                    &:hover {
                        background-color: #9fa4a9 !important;
                        border-color: #9fa4a9 !important;
                    }

                    &:active {
                        background-color: #6d7277 !important;
                        border-color: #6d7277 !important;
                    }
                }
            }

            .print-config-wrapper {
                position: relative;
                display: flex;
                flex-direction: column;
                width: 272px;
                height: 100%;
                padding: 16px 6px 64px 16px;
                overflow: scroll;
                border-left: 1px solid var(--abc-color-P7);

                @include scrollBar();

                .show-more {
                    margin-top: 16px;
                    color: #005ed9;
                    text-align: right;
                    cursor: pointer;
                }

                .padding-wrapper {
                    display: flex;
                    justify-content: space-between;

                    .padding-wrapper-item {
                        width: 112px;

                        .padding-wrapper-label {
                            display: block;
                            margin-bottom: 4px;
                            color: $T2;
                        }

                        input {
                            text-align: center;
                        }
                    }
                }

                .setting-item {
                    display: initial;
                    margin-bottom: 16px;

                    .print-count-name-container {
                        color: $T2;

                        .print-count-name {
                            max-width: 150px;
                            margin-left: 3px;

                            @include ellipsis;
                        }
                    }

                    &.render-config-wrapper {
                        margin-top: 16px;

                        p {
                            padding: 8px 0;
                            margin-bottom: 0;
                            font-size: 14px;
                            font-weight: 500;
                            line-height: 14px;
                            color: $T1;
                            border-bottom: 1px dashed #e6eaee;
                        }

                        .render-config-item {
                            display: flex;
                            align-items: center;
                            justify-content: flex-start;
                            width: 100%;
                            height: 40px;
                            border-bottom: 1px dashed #e6eaee;

                            .abc-checkbox__label {
                                line-height: 14px;
                            }

                            &:hover {
                                cursor: pointer;
                                background: #eff3f6;
                            }
                        }

                        .render-config-group {
                            .render-config-group-name {
                                display: flex;
                                justify-content: space-between;
                            }
                        }

                        .render-config-group + .render-config-group {
                            margin-top: 12px;
                        }
                    }

                    p {
                        margin-bottom: 4px;
                        color: $T2;
                    }

                    .input-add-btn {
                        width: 32px;
                        border-top-left-radius: 0;
                        border-bottom-left-radius: 0;
                    }

                    .input-des-btn {
                        width: 32px;
                        border-top-right-radius: 0;
                        border-bottom-right-radius: 0;
                    }

                    .custom-checkbox {
                        width: 100%;
                        height: 100%;
                        padding-left: 8px;
                        margin-right: 0;

                        .abc-checkbox__inner {
                            overflow: hidden;
                            border-radius: 8px;

                            i {
                                color: #ffffff;
                                transform: scale(0.68);
                                transform-origin: center;
                            }
                        }

                        .is-checked .abc-checkbox__inner {
                            background: #0dba51;
                            border-color: #0dba51;
                        }
                    }
                }

                .select-group {
                    display: flex;
                    align-items: center;

                    .abc-select-wrapper {
                        position: relative;

                        &:hover {
                            z-index: 1;
                        }

                        &:first-child .abc-input__inner {
                            border-top-right-radius: 0;
                            border-bottom-right-radius: 0;
                        }

                        &:last-child {
                            margin-left: -1px;

                            .abc-input__inner {
                                border-top-left-radius: 0;
                                border-bottom-left-radius: 0;
                            }
                        }
                    }
                }

                .button-group {
                    position: fixed;
                    right: 0;
                    bottom: 0;
                    z-index: 2;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    width: 272px;
                    padding: 16px;
                    background: #ffffff;
                }

                .abc-print-preview-dialog-wrapper_more-panel {
                    .abc-collapse-wrapper {
                        .abc-collapse-item-wrapper {
                            .abc-collapse-item__title {
                                padding: 0 !important;
                            }

                            .abc-collapse-item__content {
                                padding-right: 0 !important;
                                padding-left: 0 !important;
                            }

                            .abc-collapse-item__wrap {
                                overflow: visible;
                            }
                        }
                    }

                    .abc-radio {
                        margin-left: 0;
                    }

                    .setting-item {
                        margin: 0;
                    }
                }

                .abc-print-preview-dialog-wrapper_more-settings {
                    padding: 12px 0 0 0;
                    cursor: pointer;
                    border-top: 1px solid var(--abc-color-P8);
                }
            }
        }
    }

    .printer-setting-select.options-wrapper {
        .abc-option-item {
            .printer-name {
                display: inline-block;
                width: 156px;
            }
        }
    }

    .abc-print-preview-dialog_description {
        font-size: 12px;
        color: $T2;
    }

    .examination-print-config-items {
        width: 252px;
        margin-top: 12px;

        .examination-print-config-items-child {
            padding-left: 24px;
        }
    }

    .print-preview-append {
        color: $G2;
    }

    .no-charge-append {
        color: $T2;
    }
</style>
