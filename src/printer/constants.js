import { CLINIC_TYPE } from 'views/common/clinic';

export const AbcPrintOrientation = {
    unknow: 0,
    portrait: 1,
    landscape: 2,
};

export const AbcPrintOrientationRevert = {
    0: '未知',
    1: '纵向',
    2: '横向',
};


export const FEE_BILL = 'fee-bill';
export const FEE_LIST = 'fee-list';
export const FEE_SOCIAL = 'fee-social';

// 打印配置
export const ABCPrintConfigKeyMap = Object.freeze({
    prescription: {
        key: 'prescription',
        subKey: 'prescription',
    },
    prescriptionV2: {
        key: 'prescription',
        subKey: 'prescriptionV2',
    },
    medicalRecord: {
        key: 'prescription',
        subKey: 'medical-record',
    },
    infusionExecute: {
        key: 'prescription',
        subKey: 'infusion-execute',
    },
    treatmentExecute: {
        key: 'prescription',
        subKey: 'treatment-execute',
    },
    // 检查检验单 - 已废弃
    examination: {
        key: 'prescription',
        subKey: 'examination',
    },
    // 检查单
    examine: {
        key: 'prescription',
        subKey: 'examine',
    },
    // 检验单
    examinationInspect: {
        key: 'prescription',
        subKey: 'examinationInspect',
    },
    // 检查检验申请单 - 已废弃
    examinationApplySheet: {
        key: 'prescription',
        subKey: 'examination-apply-sheet',
    },
    // 检查申请单
    examineApplySheet: {
        key: 'prescription',
        subKey: 'examine-apply-sheet',
    },
    // 检验申请单
    examinationInspectApplySheet: {
        key: 'prescription',
        subKey: 'examination-inspect-apply-sheet',
    },
    examinationReport: {
        key: 'prescription',
        subKey: 'examination-report',
    },
    examinationReportPdf: {
        key: 'prescription',
        subKey: 'examination-report-pdf',
    },
    examinationCloudReport: {
        key: 'prescription',
        subKey: 'examination-cloud-report',
    },
    examinationCloudIntelligentReport: {
        key: 'prescription',
        subKey: 'examination-cloud-intelligent-report',
    },
    inspectReport: {
        key: 'prescription',
        subKey: 'inspect-report',
    },
    eyeInspectReport: {
        key: 'prescription',
        subKey: 'eye-inspect-report',
    },
    eyeInspectReportCustom: {
        key: 'prescription',
        subKey: 'eye-inspect-report-custom',
    },
    medicalCertificate: {
        key: 'prescription',
        subKey: 'medical-certificate',
    },
    // 慢病管理
    chronicCare: {
        key: 'prescription',
        subKey: 'chronic-care',
    },
    // 儿保健康报告
    childHealthyReport: {
        key: 'prescription',
        subKey: 'child-healthy-report',
    },
    decoctionCraftCard: {
        key: 'prescription',
        subKey: 'decoction-craft-card',
    },
    RK: {
        key: 'inventory',
        subKey: 'goods-in',
    },
    CK: {
        key: 'inventory',
        subKey: 'goods-out',
    },
    PD: {
        key: 'inventory',
        subKey: 'goods-check',
    },
    LY: {
        key: 'inventory',
        subKey: 'goods-apply',
    },
    DB: {
        key: 'inventory',
        subKey: 'goods-trans',
    },
    CG: {
        key: 'inventory',
        subKey: 'goods-purchase',
    },
    // 统计
    statistics: {
        key: 'statistics',
        subKey: 'report',
    },
    // 小票
    cashier: {
        key: 'ticket',
        subKey: 'cashier',
    },
    // 小票
    receipt: {
        key: 'ticket',
        subKey: 'receipt',
    },
    pharmacyCashierV2: {
        key: 'ticket',
        subKey: 'pharmacy-cashier-v2',
    },
    registration: {
        key: 'ticket',
        subKey: 'registration',
    },
    dispense: {
        key: 'ticket',
        subKey: 'dispense',
    },
    undispense: {
        key: 'ticket',
        subKey: 'undispense',
    },
    patientTag: {
        key: 'ticket',
        subKey: 'patient-tag',
    },
    priceTag: {
        key: 'ticket',
        subKey: 'price-tag',
    },
    medicineTag: {
        key: 'ticket',
        subKey: 'medicine-tag',
    },
    hospitalMedicineTag: {
        key: 'ticket',
        subKey: 'hospital-medicine-tag',
    },
    examinationTag: {
        key: 'ticket',
        subKey: 'examination-tag',
    },
    examinationLabel: {
        key: 'ticket',
        subKey: 'examination-label',
    },
    inspectLabel: {
        key: 'ticket',
        subKey: 'inspect-label',
    },
    hospitalExaminationTag: {
        key: 'ticket',
        subKey: 'hospital-examination-tag',
    },
    chargeNotification: {
        key: 'ticket',
        subKey: 'charge-notification',
    },
    bill: {
        key: 'bill',
        subKey: FEE_BILL,
    },
    feeList: {
        key: 'bill',
        subKey: FEE_LIST,
    },
    social: {
        key: 'bill',
        subKey: FEE_SOCIAL,
    },
    hospitalDepositReceipt: {
        key: 'bill',
        subKey: 'hospital-deposit-receipt',
    },
    hospitalInspect: {
        key: 'prescription',
        subKey: 'hospital-inspect',
    },
    hospitalMedicalDocument: {
        key: 'prescription',
        subKey: 'hospital-medical-document',
    },
    tianjinWesternPrescription: {
        key: 'prescription',
        subKey: 'tianjin-western-prescription',
    },
    tianjinChinesePrescription: {
        key: 'prescription',
        subKey: 'tianjin-chinese-prescription',
    },
    hospitalNursePrescription: {
        key: 'prescription',
        subKey: 'hospital-nurse-prescription',
    },
    hospitalDoctorMedicalPrescription: {
        key: 'prescription',
        subKey: 'hospital-doctor-medical-prescription',
    },
    hospitalAdviceShandong: {
        key: 'prescription',
        subKey: 'hospital-advice-shandong',
    },
    hospitalNurseApplyMedicine: {
        key: 'prescription',
        subKey: 'hospital-nurse-dispensing',
    },
    hospitalNursePatientDispensing: {
        key: 'prescription',
        subKey: 'hospital-nurse-dispensing',
    },
    hospitalNurseGoodsDispensing: {
        key: 'prescription',
        subKey: 'hospital-nurse-dispensing',
    },
    hospitalBedsideCard: {
        key: 'prescription',
        subKey: 'hospital-bedside-card',
    },
    temperatureGraph: {
        key: 'prescription',
        subKey: 'temperature-graph',
    },
    temperatureTable: {
        key: 'prescription',
        subKey: 'temperature-table',
    },
    bloodSugar: {
        key: 'prescription',
        subKey: 'blood-sugar',
    },
    chargeList: {
        key: 'prescription',
        subKey: 'charge-list',
    },
    chargeListBatch: {
        key: 'prescription',
        subKey: 'charge-list-batch',
    },
    peIndividualReport: {
        key: 'prescription',
        subKey: 'pe-individual-report',
    },
    peGroupReport: {
        key: 'prescription',
        subKey: 'pe-group-report',
    },
    peIndividualReportSingle: {
        key: 'prescription',
        subKey: 'pe-individual-report-single',
    },
    peGuideSheet: {
        key: 'prescription',
        subKey: 'pe-guide-sheet',
    },
    hospitalPrescription: {
        key: 'prescription',
        subKey: 'hospital-prescription',
    },
    hospitalizationCertificate: {
        key: 'prescription',
        subKey: 'hospitalization-certificate',
    },
    chargeShift: {
        key: 'prescription',
        subKey: 'charge-shift',
    },
    registrationTag: {
        key: 'ticket',
        subKey: 'registration-tag',
    },
    orderCloudShipment: {
        key: 'orderCloud',
        subKey: 'shipment',
    },
    settlementApplication: {
        key: 'inventory',
        subKey: 'settlement-application',
    },
    settlementReview: {
        key: 'inventory',
        subKey: 'settlement-review',
    },
    settlementDetail: {
        key: 'inventory',
        subKey: 'settlement-detail',
    },
    executeCertificate: {
        key: 'ticket',
        subKey: 'execute-certificate',
    },
    peChargeFeeList: {
        key: 'prescription',
        subKey: 'pe-charge-fee-list',
    },
    abcImagePrint: {
        key: 'statistics',
        subKey: 'viewer',
    },
    // 住院收费小票
    hospitalCashier: {
        key: 'ticket',
        subKey: 'hospital-cashier',
    },
    familyDoctorAgreement: {
        key: 'prescription',
        subKey: 'family-doctor-agreement',
    },
    pointsBill: {
        key: 'ticket',
        subKey: 'points-bill',
    },
    pharmacyCashier: {
        key: 'ticket',
        subKey: 'pharmacy-cashier',
    },
    pharmacyReconciliationInfo: {
        key: 'statistics',
        subKey: 'pharmacy-reconciliation-info',
    },
});

export const PrintMode = {
    Lodop: 1,
    Electron: 2,
};

export const CLINIC_TYPE_ENUM = {
    [CLINIC_TYPE.NORMAL]: '普通诊所',
    [CLINIC_TYPE.DENTISTRY]: '牙科诊所',
    [CLINIC_TYPE.OPHTHALMOLOGY]: '眼科诊所',
    [CLINIC_TYPE.HOSPITAL]: '医院管家',
    [CLINIC_TYPE.PHARMACY]: '药店管家',
};

export const TICKET_ESC_POS_BASE_WIDTH = 12;

export const TicketCharacterCountMap = Object.freeze({
    '热敏小票（80mm）': 48,
    '热敏小票（58mm）': 32,
});

export const PrescriptionPrintType = Object.freeze({
    NORMAL_PR: 0, // 一般处方
    IMG_PR: 1, // 拍照续方
    HISTORY_PR: 2, // 历史续方
});

export const FILTER_PRINTER_LIST = [
    'Fax',
    'Microsoft Print to PDF',
    'Microsoft XPS Document Writer',
    'OneNote for Windows 10',
    '导出为WPS PDF',
    'Microsoft Print to OneNote',
    'OneNote',
    '发送至 OneNote',
    'OneNote (Desktop)',
];
