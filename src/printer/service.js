import { PrintMode } from '@/printer/constants.js';
import PrintManager from '@/printer/manager/print-manager.js';
import ElectronPrinter from './printer/electron/printer.js';
import LodopPrinter from './printer/lodop/printer.js';
import Install from '@/printer/printer/electron/install/index.js';
import ABCClientManager from '@/printer/manager/abc-client-manager';
import Logger from 'utils/logger.js';

export default class PrinterService {
    constructor() {
        this.queue = [];
        this.printingTask = null;
    }

    pushQueue(task, callback) {
        if (typeof callback === 'function') {
            this.callback = callback;
        }
        if (task instanceof Array) {
            this.queue = this.queue.concat(task);
        } else {
            this.queue.push(task);
        }
        this.checkQueue();
    }

    checkQueue() {
        if (this.queue.length && !this.printingTask) {
            this.printingTask = this.queue.pop();
            this.print();
        }
    }

    async print() {
        console.log('print start');

        // 直接通过 Lodop 打印 pdf
        if (this.printingTask.extra?.isPdfLodopPrint) {
            const printInstance = new LodopPrinter(this.printingTask);
            printInstance?.pdfLodopPrint?.();
        } else if (this.printingTask.extra?.isTicketEscPosPrint) {
            await this.printingTask.getTemplateStr();
            const printInstance = new ElectronPrinter(this.printingTask);
            printInstance.silentTicketEscPosPrint?.();
        } else {
            // 正常打印流程
            await this.printingTask.getTemplateStr();
            this.printingTask.getPageWidthAndHeight();
            const printManager = PrintManager.getInstance();
            const abcClientManager = ABCClientManager.getInstance();
            let printInstance = null;
            // 指定了mode
            if ([PrintMode.Lodop, PrintMode.Electron].includes(this.printingTask.mode)) {
                switch (this.printingTask.mode) {
                    case PrintMode.Lodop:
                        printInstance = new LodopPrinter(this.printingTask);
                        break;
                    case PrintMode.Electron:
                        // 浏览器端指定electron打印,判断是否安装了新版打印服务,否则还是使用lodop打印
                        if (printManager.getFeature('isBrowser')) {
                            if (abcClientManager.getFeature('isABCClientPrintServerOnline')) {
                                printInstance = new ElectronPrinter(this.printingTask);
                            } else {
                                printInstance = new LodopPrinter(this.printingTask);
                            }
                        } else {
                            if (!printManager.getFeature('isNewVersionElectronPrint')) {
                                new Install().generateDialog();
                                if (this.callback) {
                                    this.callback = null;
                                }
                                this.printingTask = null;
                                return;
                            }
                            printInstance = new ElectronPrinter(this.printingTask);
                        }
                        break;
                    default:
                        break;
                }
            } else {
                if (printManager.isEnableElectronPrint()) {
                    if (!printManager.getFeature('isNewVersionElectronPrint')) {
                        new Install().generateDialog();
                        if (this.callback) {
                            this.callback = null;
                        }
                        this.printingTask = null;
                        return;
                    }
                    printInstance = new ElectronPrinter(this.printingTask);
                } else {
                    printInstance = new LodopPrinter(this.printingTask);
                }
            }
            printInstance?.silentPrint?.();
        }

        if (this.printingTask.needReportLog && this.printingTask.reportLogData) {
            Logger.report({
                scene: this.printingTask.reportLogData.scene,
                data: {
                    info: 'print service 当前正在打印的数据',
                    data: {
                        printConfig: JSON.stringify(this.printingTask.printConfig),
                        template: JSON.stringify(this.printingTask.template),
                        keyId: this.printingTask.reportLogData.keyId,
                    },
                },
            });
        }

        // eslint-disable-next-line abc/no-timer-id
        setTimeout(() => {
            console.log('print finish');
            if (this.callback) {
                this.callback();
                this.callback = null;
            }
            if (typeof this.printingTask.onPrintSuccess === 'function') {
                this.printingTask.onPrintSuccess(this.printingTask);
            }
            if (typeof this.printingTask.onPrintLog === 'function') {
                this.printingTask.onPrintLog();
            }
            if (typeof this.printingTask.onAddPrintCount === 'function') {
                this.printingTask.onAddPrintCount();
            }
            this.printingTask = null;
            this.checkQueue();
        },100);
    }

    static getPrintService() {
        if (!this.service) {
            this.service = new PrinterService();
        }
        return this.service;
    }
}
