import { PrintPageRange } from '@/printer/config';
import PrintManager from '@/printer/manager/print-manager';
import Clone from 'utils/clone';
import { sleep } from 'utils/delay';
import { getPDFPageInfo } from 'views/examination/util';
import AbcPrinter from '@/printer';
import { PrintMode } from '@/printer/constants';
import HtmlToPDF from './html-to-pdf';

export function getSliceByPrintConfig(advance, pageTotal) {
    const fullSlice = [0, pageTotal];

    if (!advance) {
        return {
            slice: fullSlice,
        };
    }
    // 打印全部
    if (advance.printRange.range === PrintPageRange.All) {
        return {
            slice: fullSlice,
        };
    }
    // 打印范围
    if (advance.printRange.range === PrintPageRange.Specify) {
        const start = +advance.printRange.start;
        const end = +advance.printRange.end;

        /**
         * 只有一页，则打印全部
         */
        if (pageTotal === 1 && start === 1 && !end) {
            return {
                slice: fullSlice,
            };
        }

        // 如果没有指定页数，返回全部
        if (!start || !end || start > end) {
            return {
                slice: [-1, -1],
            };
        }

        const currentStart = start - 1;
        return {
            slice: [currentStart, end],
        };
    }
    // 指定页数
    if (advance.printRange.range === PrintPageRange.SpecifyRange) {
        return {
            slice: [+advance.printRange.specify - 1, +advance.printRange.specify],
        };
    }
    // 选择页数
    if (advance.printRange.range === PrintPageRange.SelectPage) {
        return {
            select: advance.printRange.selectPage,
        };
    }
    return {
        slice: fullSlice,
    };
}

/**
 * 根据打印机的物理边距计算出正确的系统边距配置
 * @param deviceName 打印机名称
 * @param pageSizeReduce 系统边距配置
 * @return {Object}
 */
export function getPrinterPageSizeReduce({
    deviceName, pageSizeReduce,
}) {
    const printerOffset = PrintManager.getInstance().getPrinterOffset(deviceName) || {
        offsetX: 0, offsetY: 0,
    };
    const {
        offsetX = 0, offsetY = 0,
    } = printerOffset;
    const cachePageSizeReduce = Clone(pageSizeReduce);
    if (cachePageSizeReduce.top < offsetY) {
        cachePageSizeReduce.top = Math.ceil(offsetY);
    }
    if (cachePageSizeReduce.bottom < offsetY) {
        cachePageSizeReduce.bottom = Math.ceil(offsetY);
    }
    if (cachePageSizeReduce.left < offsetX) {
        cachePageSizeReduce.left = Math.ceil(offsetX);
    }
    if (cachePageSizeReduce.right < offsetX) {
        cachePageSizeReduce.right = Math.ceil(offsetX);
    }
    return {
        pageSizeReduce: cachePageSizeReduce, printerOffset,
    };
}

// 循环调用接口
export function tryFetchPrintData(fn, totalCount = 5, delay = 300) {
    return async (...params) => {
        let count = 0;
        while (count < totalCount) {
            try {
                return await fn(...params);
            } catch (error) {
                console.error(error);
                count++;
                await sleep(delay);
            }
        }
        return {};
    };
}

/**
 * 比较版本大小
 * @param {string} currentVersion 当前版本
 * @param {string} baseVersion 基准版本
 * @return {0 | 1 | -1} 比较结果 0:版本一致 1:当前版本更高 -1:当前版本更低
 */
export function compareVersions(currentVersion, baseVersion) {
    // 将版本号拆分成数组
    const currentVersionList = currentVersion.split('.').map(Number);
    const baseVersionList = baseVersion.split('.').map(Number);

    // 比较每个数组元素
    for (let i = 0; i < Math.max(currentVersionList.length, baseVersionList.length); i++) {
        // 如果一个版本号的某一部分不存在，则视为0
        const currentVersionNum = i < currentVersionList.length ? currentVersionList[i] : 0;
        const baseVersionNum = i < baseVersionList.length ? baseVersionList[i] : 0;

        // 如果两个版本号的某一部分不相等，则返回比较结果
        if (currentVersionNum > baseVersionNum) {
            return 1;
        }
        if (currentVersionNum < baseVersionNum) {
            return -1;
        }
    }

    // 如果所有部分都相等，则返回0
    return 0;
}

/**
 * 使用 Lodop 打印 PDF，支持调整边距，优先使用 base64
 * @param {Object} params
 * @param {string} params.pdfUrl - PDF 的 URL 地址
 * @param {string} params.pdfBase64 - PDF 的 base64
 * @param {Object} params.printConfigKey - 打印的配置，保存了边距等打印配置，来源于 src/printer/config.js - ABCPrinterConfig
 * @return {Promise<void>}
 */
export async function pdfLodopPrint(params) {
    const {
        pdfUrl, pdfBase64, printConfigKey,
    } = params;

    const formatBase64 = pdfBase64 && pdfBase64.includes(',') ? pdfBase64.split(',')[1] : pdfBase64;
    const pdfPageList = await getPDFPageInfo(formatBase64 ? window.atob(formatBase64) : pdfUrl, false, null, !!formatBase64);
    AbcPrinter.abcPrint({
        templateKey: window.AbcPackages.AbcTemplates.pdfLodopPrintTemplate,
        printConfigKey,
        data: { pdfPageList },
        mode: PrintMode.Lodop,
        extra: {
            isPdfLodopPrint: true,
            pdfUrl,
            pdfBase64: formatBase64,
        },
    });
}

/**
 * 获取打印机原始信息
 * @param {string} deviceName 打印机名称
 * @return {Obejct} 打印机信息
 */
export function getPrinterInfoByDeviceName(deviceName) {
    return PrintManager.getInstance().getPrinterInfoByDeviceName(deviceName);
}

// 导出通用HTML转PDF工具
export { HtmlToPDF };
