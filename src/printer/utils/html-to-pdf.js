import html2pdf from 'html2pdf.js';
import { jsPDF } from 'jspdf';

/**
 * 通用HTML转PDF工具类
 * 支持HTML转PDF和直接处理PDF文件
 * <AUTHOR> Assistant
 * @date 2025-01-03
 */
export default class HtmlToPDF {
    /**
     * 构造函数
     * @param {Object} options - 配置选项
     * @param {string} [options.html] - HTML内容
     * @param {string} [options.pdfUrl] - PDF文件URL
     * @param {string} [options.pdfBase64] - PDF文件Base64编码
     * @param {Blob} [options.pdfBlob] - PDF文件Blob对象
     * @param {Object} [options.pdfOptions] - PDF配置选项
     * @param {string} [options.pdfOptions.filename] - 文件名
     * @param {Object} [options.pdfOptions.jsPDF] - jsPDF配置
     * @param {Object} [options.pdfOptions.html2canvas] - html2canvas配置
     * @param {Object} [options.pdfOptions.html2pdf] - html2pdf其他配置
     * @param {boolean} [options.enableAdvancedPagination] - 是否启用高级分页处理
     *
     */
    constructor(options = {}) {
        const {
            html,
            pdfUrl,
            pdfBase64,
            pdfBlob,
            enableAdvancedPagination = false,
            filename,
        } = options;

        // 验证输入参数
        const inputCount = [html, pdfUrl, pdfBase64, pdfBlob].filter(Boolean).length;
        if (inputCount === 0) {
            throw new Error('At least one of html, pdfUrl, pdfBase64, or pdfBlob is required');

        }
        if (inputCount > 1) {
            throw new Error('Only one of html, pdfUrl, pdfBase64, or pdfBlob should be provided');
        }

        this.html = html;
        this.pdfUrl = pdfUrl;
        this.pdfBase64 = pdfBase64;
        this.pdfBlob = pdfBlob;
        this.filename = filename;
        this.inputType = html ? 'html' : pdfUrl ? 'url' : pdfBase64 ? 'base64' : 'blob';
        this.enableAdvancedPagination = enableAdvancedPagination;

        // 初始化PDF配置
        this.initPDFOptions(options.pdfOptions);
    }

    /**
     * 初始化PDF配置选项
     * @private
     * @param {Object} customOptions - 自定义配置选项
     */
    initPDFOptions(customOptions = {}) {
        const defaultOptions = {
            filename: this.filename || this.generateFilename(),
            jsPDF: {
                unit: 'mm',
                format: 'a4',
                orientation: 'portrait',
            },
            pagebreak: {
                mode: ['avoid-all', 'css' ],
                after: '.abc-page', // 在具有类名page-break-after的元素后分页
            },
        };

        this.pdfOptions = {
            ...defaultOptions,
            ...customOptions,
            jsPDF: {
                ...defaultOptions.jsPDF,
                ...(customOptions.jsPDF || {}),
            },
            html2canvas: {
                useCORS: true,
                allowTaint: true,
                dpi: 192,
                letterRendering: true,
                scale: 2, // 提高清晰度
                ignoreElements: (element) => {
                    return element.classList.contains('ProseMirror-separator');
                },
                ...customOptions.html2canvas,
            },
        };
    }
    /**
     * 简单HTML转PDF（直接使用html2pdf）
     * @private
     * @param {string} html - 处理后的HTML
     * @returns {Promise<Blob>}
     */
    async convertSimpleHTMLToPDF(html) {
        const res = await html2pdf()
            .set(this.pdfOptions)
            .from(html)
            .toPdf()
            .outputPdf('blob');
        return res;

    }

    async convertHTMLToPDF() {
        const processedHtml = this.processZoomStyles(this.html);

        // 根据配置和复杂度选择转换方式
        const useAdvancedPagination = this.enableAdvancedPagination;

        if (useAdvancedPagination) {
            console.log('使用高级分页处理复杂HTML');
            const res = await this.convertComplexHTMLToPDF(processedHtml);
            return res;
        }
        console.log('使用标准方式处理简单HTML');
        const res = await this.convertSimpleHTMLToPDF(processedHtml);
        return res;
    }


    /**
     * 处理HTML中的zoom样式，转换为transform scale
     * @private
     * @param {string} html - HTML内容
     * @returns {string} 处理后的HTML
     */
    processZoomStyles(html) {
        if (!html) return html;

        // 只处理zoom样式，用transform scale替代
        const processedHtml = html.replace(/zoom\s*:\s*([0-9.]+)/gi, (match, zoomValue) => {
            const scale = parseFloat(zoomValue);
            return `transform: scale(${scale}); transform-origin: top center;`;
        });

        return processedHtml;
    }

    async convertComplexHTMLToPDF(html) {
        const pages = await this.splitHTMLIntoPages(html);
        console.log('pages', pages);
        const res = await this.mergePagesToPDF(pages);
        return res;
    }

    async splitHTMLIntoPages(html) {
        // 检查是否有预定义的分页点（.abc-page 类）
        if (html.includes('abc-page')) {
            return this.splitByPageBreaks(html);
        }
        // 没有分页点，返回原HTML作为单页
        return [html];

    }

    /**
     * 根据预定义分页点分页 - 提取.abc-page元素并保持样式一致
     * @private
     * @param {string} html - HTML内容
     * @returns {Array} 页面数组
     */
    splitByPageBreaks(html) {
        console.log('[HtmlToPDF] 开始分页处理');

        // 解析HTML文档
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');

        // 查找所有.abc-page元素
        const pageElements = doc.querySelectorAll('.abc-page');
        console.log(`[HtmlToPDF] 发现 ${pageElements.length} 个分页元素`);

        if (pageElements.length === 0) {
            console.log('[HtmlToPDF] 未找到分页元素，返回原HTML');
            return [html];
        }

        // 为每个页面创建完整的HTML文档，确保样式一致
        const pages = Array.from(pageElements).map((pageElement, index) => {
            // 获取页面元素的完整HTML（包括自身标签）
            const pageContent = pageElement.outerHTML;

            // 使用优化方法构建页面HTML
            const optimizedPageHtml = this.optimizePageHtml(html, pageContent, index);

            console.log(`[HtmlToPDF] 页面 ${index + 1} HTML长度: ${optimizedPageHtml.length}`);
            return optimizedPageHtml;
        });

        console.log(`[HtmlToPDF] 分页处理完成，共 ${pages.length} 页`);
        return pages;
    }

    /**
     * 提取HTML文档中的所有样式（包括内联样式和样式表）
     * @private
     * @param {Document} doc - HTML文档对象
     * @returns {string} 完整的样式内容
     */
    extractAllStyles(doc) {
        const styles = [];

        // 提取所有style标签
        const styleElements = doc.querySelectorAll('style');
        styleElements.forEach((styleEl) => {
            if (styleEl.textContent) {
                styles.push(styleEl.textContent);
            }
        });

        // 提取所有link标签中的CSS（如果是内联的）
        const linkElements = doc.querySelectorAll('link[rel="stylesheet"]');
        linkElements.forEach((linkEl) => {
            // 注意：这里只能处理内联的CSS，外部CSS需要特殊处理
            if (linkEl.href && linkEl.href.startsWith('data:')) {
                // 处理data URI形式的CSS
                try {
                    const cssContent = atob(linkEl.href.split(',')[1]);
                    styles.push(cssContent);
                } catch (e) {
                    console.warn('[HtmlToPDF] 无法解析CSS data URI:', e);
                }
            }
        });

        return styles.join('\n');
    }

    /**
     * 优化页面HTML，确保样式完整性
     * @private
     * @param {string} originalHtml - 原始HTML
     * @param {string} pageContent - 页面内容
     * @param {number} pageIndex - 页面索引
     * @returns {string} 优化后的页面HTML
     */
    optimizePageHtml(originalHtml, pageContent, pageIndex) {
        const parser = new DOMParser();
        const originalDoc = parser.parseFromString(originalHtml, 'text/html');

        // 提取原始文档的所有样式
        const allStyles = this.extractAllStyles(originalDoc);

        // 提取head中的其他重要内容（meta标签等）
        const metaTags = originalDoc.querySelectorAll('head meta');
        const metaContent = Array.from(metaTags).map((meta) => meta.outerHTML).join('\n');

        // 构建优化的页面HTML
        const optimizedHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                ${metaContent}
                <style>
                    /* 原始样式 */
                    ${allStyles}

                </style>
            </head>
            <body>
                ${pageContent}
            </body>
            </html>
        `;

        console.log(`[HtmlToPDF] 页面 ${pageIndex + 1} 样式优化完成`);
        return optimizedHtml;
    }
    /**
     * 合并页面为PDF
     * @private
     * @param {Array} pages - 页面数组
     * @returns {Promise<Blob>}
     */
    async mergePagesToPDF(pages) {
        const opt = {
            margin: this.pdfOptions.margin || [0, 0],
            filename: this.pdfOptions.filename,
            image: {
                type: 'jpeg', quality: 0.98,
            },
            html2canvas: this.pdfOptions.html2canvas,
            jsPDF: this.pdfOptions.jsPDF,
        };

        const doc = new jsPDF(opt.jsPDF);
        const { pageSize } = doc.internal;
        const pageWidth = pageSize.getWidth();
        const pageHeight = pageSize.getHeight();

        for (let i = 0; i < pages.length; i++) {
            const page = pages[i];

            try {
                // 使用html2pdf生成页面图片
                const pageImage = await html2pdf()
                    .from(page)
                    .set(opt)
                    .outputImg();

                if (i !== 0) {
                    doc.addPage();
                }

                console.log('pageImage', opt.margin);
                // 添加图片到PDF
                doc.addImage(
                    pageImage.src,
                    'JPEG',
                    opt.margin[0],
                    opt.margin[1],
                    pageWidth - opt.margin[0] * 2,
                    pageHeight - opt.margin[1] * 2,
                );

            } catch (error) {
                console.warn(`处理第${i + 1}页时出错:`, error);
                // 如果单页处理失败，尝试简单处理
                if (i !== 0) {
                    doc.addPage();
                }
                doc.text(`页面 ${i + 1} 处理失败`, 20, 20);
            }
        }

        return new Blob([doc.output('blob')], { type: 'application/pdf' });
    }
    /**
     * 生成默认文件名
     * @private
     * @returns {string}
     */
    generateFilename() {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
        return `document-${timestamp}.pdf`;
    }

    /**
     * 获取PDF的Blob对象
     * @returns {Promise<Blob>}
     */
    async getBlob() {
        try {
            switch (this.inputType) {
                case 'html':
                    return await this.convertHTMLToPDF();

                case 'blob':
                    return this.pdfBlob;

                case 'base64':
                    return await this.convertBase64ToBlob();

                case 'url':
                    return await this.convertUrlToBlob();

                default:
                    throw new Error('Invalid input type');
            }
        } catch (error) {
            console.error('Error generating PDF blob:', error);
            throw error;
        }
    }

    /**
     * 保存PDF到本地
     * @param {string} [customFilename] - 自定义文件名
     * @returns {Promise<void>}
     */
    async savePDF(customFilename) {
        try {
            const filename = customFilename || this.pdfOptions.filename;
            const blob = await this.getBlob();
            this.downloadBlob(blob, filename);
        } catch (error) {
            console.error('Error saving PDF:', error);
            throw error;
        }
    }

    /**
     * 将PDF URL转换为Base64
     * @private
     * @returns {Promise<string>}
     */
    async convertUrlToBase64() {
        const response = await fetch(this.pdfUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch PDF from URL: ${response.statusText}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        const base64 = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));
        return `data:application/pdf;base64,${base64}`;
    }

    /**
     * 将PDF URL转换为Blob
     * @private
     * @returns {Promise<Blob>}
     */
    async convertUrlToBlob() {
        const response = await fetch(this.pdfUrl);
        if (!response.ok) {
            throw new Error(`Failed to fetch PDF from URL: ${response.statusText}`);
        }
        try {
            await response.blob();
        } catch (e) {
            console.log('convertUrlToBlob', e);
        }
    }

    /**
     * 将Base64转换为Blob
     * @private
     * @returns {Promise<Blob>}
     */
    async convertBase64ToBlob() {
        const base64Data = this.pdfBase64.startsWith('data:') ?
            this.pdfBase64.split(',')[1] :
            this.pdfBase64;

        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return new Blob([bytes], { type: 'application/pdf' });
    }

    /**
     * 将Blob转换为Base64
     * @private
     * @returns {Promise<string>}
     */
    async convertBlobToBase64() {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(new Error('Failed to convert blob to base64'));
            reader.readAsDataURL(this.pdfBlob);
        });
    }

    /**
     * 下载Blob文件
     * @private
     * @param {Blob} blob - 文件Blob
     * @param {string} filename - 文件名
     */
    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    }
}
