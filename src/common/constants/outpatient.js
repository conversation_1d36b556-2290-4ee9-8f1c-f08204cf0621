import { ShebaoPayMode } from '@/common/constants/inventory.js';

export const MedicalRecordKeyLabelObj = {
    chiefComplaint: '主诉',
    symptomTime: '发病日期',
    presentHistory: '现病史',
    pastHistory: '既往史',
    wearGlassesHistory: '戴镜史',
    familyHistory: '家族史',
    personalHistory: '个人史',
    birthHistory: '出生史',
    obstetricalHistory: '月经婚育史',
    epidemiologicalHistory: '流行病史',
    physicalExamination: '体格检查',
    chineseExamination: '望闻切诊',
    tongue: '舌象',
    pulse: '脉象',
    oralExamination: '口腔检查',
    eyeExamination: '眼部检查',
    auxiliaryExaminations: '辅助检查',
    extendDiagnosisInfos: '诊断',
    syndrome: '辨证',
    therapy: '治法',
    chinesePrescription: '方药',
    prognosis: '预后',
    target: '目标',
    dentistryExaminations: '口腔检查', // 牙科口腔检查
    treatmentPlans: '治疗计划',
    disposals: '处置',
    allergicHistory: '过敏史',
    syndromeTreatment: '辨证论治',
    diagnosis: '诊断',
    doctorAdvice: '医嘱建议',
};

export const ToothQuadrant = Object.freeze({
    TOP_LEFT: [11, 12, 13, 14, 15, 16, 17, 18, 51,52,53,54,55],
    TOP_RIGHT: [21, 22, 23, 24, 25, 26, 27, 28, 61,62,63,64,65],
    BOTTOM_LEFT: [41,42,43,44,45,46,47,48,81,82,83,84,85],
    BOTTOM_RIGHT: [31, 32, 33, 34, 35, 36, 37, 38, 71,72,73,74,75],
});

export const ToothChildMap = Object.freeze({
    1: 'A',
    2: 'B',
    3: 'C',
    4: 'D',
    5: 'E',
});

export const OutpatientChargeTypeEnum = Object.freeze({
    DEFAULT: 0, // 默认
    NO_CHARGE: 1, // 门诊标记自备，不参与划价
});

export const OutpatientStatusEnum = Object.freeze({
    UN_DIAGNOSIS: 0, //待诊
    DIAGNOSIS: 1, //已诊
    DRAFT: 2, //草稿
    EXPIRED: 20, //过期
});

/**
 * 配镜处方类型字段
 * 0 镜框
 * 1 隐形眼镜
 * @type {{'0': string[], '1': string[]}}
 */
export const GLASSES_TYPE = {
    0: ['frameSpherical', 'frameLenticular', 'frameAxial', 'framePrism', 'frameBase', 'frameCva', 'frameAdd', 'framePupilDistance', 'framePupilHeight'],
    1: ['contactFocalLength', 'contactBozr', 'contactDiameter', 'contactLenticular', 'contactAxial'],
};


export const SignatureTypeEnum = Object.freeze({
    CDSS: 0, // cdss
    SHEBAO_RESTRICT: 1, // 医保合规
});


// item 上 payType 字段，透传给社保，与shebaoPayMode有区别，shebaoPayMode是goods属性，payType是当前item支付属性
export const ShebaoPayTypeEnum = Object.freeze({
    // 优先统筹支付
    OVERALL: 0,
    // 优先个账支付
    SELF: 10,
    // 处方内自费（跟其它几个不一样，仅前端使用，不能用作后台流转）
    FORM_SELF: 20,
    // 不使用医保支付
    NO_USE: 30,
});

export const ShebaoPayTypeByModeEnum = Object.freeze({
    [ShebaoPayMode.OVERALL]: ShebaoPayTypeEnum.OVERALL,
    [ShebaoPayMode.SELF]: ShebaoPayTypeEnum.SELF,
    [ShebaoPayMode.NO_USE]: ShebaoPayTypeEnum.NO_USE,
});

export const ShebaoPayModeByTypeEnum = Object.freeze({
    [ShebaoPayTypeEnum.OVERALL]: ShebaoPayMode.OVERALL,
    [ShebaoPayTypeEnum.SELF]: ShebaoPayMode.SELF,
    [ShebaoPayTypeEnum.NO_USE]: ShebaoPayMode.NO_USE,
});
