/* eslint-disable max-classes-per-file */

/**
 * 药品成分位/多选 0x01 精1 0x02 精2 0x04麻 0x08 毒 0x10放 0x20 麻黄碱
 * */
export class Ingredient {
    // 精1
    static JING_1 = 1 << 0; // 1
    // 精2
    static JING_2 = 1 << 1; // 2
    //麻
    static MA = 1 << 2; // 4
    //毒
    static DU = 1 << 3; // 8
    //放
    static FANG = 1 << 4; // 16
    //麻黄碱
    static MA_HUANG_JIAN = 1 << 5; // 32
}

export const IngredientArr = [
    Ingredient.JING_1,
    Ingredient.JING_2,
    Ingredient.MA,
    Ingredient.DU,
    Ingredient.FANG,
    Ingredient.MA_HUANG_JIAN,
];

export const IngredientShortEnum = {
    [Ingredient.JING_1]: '精Ⅰ',
    [Ingredient.JING_2]: '精Ⅱ',
    [Ingredient.MA]: '麻',
    [Ingredient.DU]: '毒',
    [Ingredient.FANG]: '放',
    [Ingredient.MA_HUANG_JIAN]: '麻黄碱',
};

/**
 * 限制级别: 0非限制使用级，1限制使用级，2特殊使用级
 * */
export class RestrictLevel {
    // 非限制使用级
    static NULL = 0;
    // 限制使用级
    static RESTRICT = 1;
    // 特殊使用级
    static SPECIAL = 2;
}

export const RestrictLevelEnum = {
    [RestrictLevel.NULL]: '非限制使用级',
    [RestrictLevel.RESTRICT]: '限制使用级',
    [RestrictLevel.SPECIAL]: '特殊使用级',
};

export const RestrictLevelShortEnum = {
    [RestrictLevel.NULL]: '',
    [RestrictLevel.RESTRICT]: '限',
    [RestrictLevel.SPECIAL]: '特',
};

/**
 * Big.js decimal places
 * The rounding mode (RM) used when rounding to the above decimal places.
 *  0  Towards zero (i.e. truncate, no rounding).       (ROUND_DOWN)
 *  1  To nearest neighbour. If equidistant, round up.  (ROUND_HALF_UP)
 *  2  To nearest neighbour. If equidistant, to even.   (ROUND_HALF_EVEN)
 *  3  Away from zero.                                  (ROUND_UP)
 */
export const RoundingMode = {
    ROUND_DOWN: 0,
    ROUND_HALF_UP: 1,
    ROUND_HALF_EVEN: 2,
    ROUND_UP: 3,
};

// 社保支付方式
export const ShebaoPayMode = Object.freeze({
    // 优先统筹支付
    OVERALL: 0,
    // 优先个账支付
    SELF: 1,
    // 不使用医保支付
    NO_USE: 2,
});
