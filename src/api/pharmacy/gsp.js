/*
 * <AUTHOR>
 * @DateTime 2024-01-03 10:50:48
 */
import { fetchPack } from 'utils/fetch';
import Qs from 'qs';
import { exportFileByAxios } from 'utils/excel';

export default {
    // 创建销毁申请
    createdDestroy(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/destroy/add',
            method: 'post',
            data,
        });
    },
    // 查询拆零销售记录
    fetchPieceSaleList(params = {}) {
        return fetchPack({
            url: '/api/v2/gsp/record/dismounting',
            method: 'get',
            params,
        });
    },
    // 获取待销毁商品列表
    fetchToDestroy(params = {}) {
        return fetchPack({
            url: '/api/v2/gsp/record/destroy/to-destroy/list',
            method: 'get',
            params,
        });
    },
    // 获取销毁列表
    fetchDestroy(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/destroy/list',
            method: 'get',
            params,
        });
    },
    // 获取销毁内容详情
    fetchDestroyDetail(recordId) {
        return fetchPack({
            url: `/api/v2/gsp/record/destroy/${recordId}/detail`,
            method: 'get',
        });
    },
    // 销毁处理
    doDestroy(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/destroy/do-destroy',
            method: 'post',
            data,
        });
    },
    // 获取质量可疑不合格记录列表
    fetchSuspicious(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/suspicious/list',
            method: 'get',
            params,
        });
    },
    // 获取质量可疑不合格记录详情
    fetchSuspiciousDetails(recordId) {
        return fetchPack({
            url: `/api/v2/gsp/record/suspicious/${recordId}/detail`,
            method: 'get',
        });
    },
    fetchSuspiciousRecordDetail(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/suspicious/${id}`,
            method: 'get',
        });
    },
    /**
     * 获取不合格品退货单信息
     * @param id
     * @return {Promise<Response>}
     */
    fetchSuspiciousReturnOrder(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/suspicious/${id}/return`,
            method: 'get',
        });
    },
    createSuspiciousRecord(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/suspicious/add',
            method: 'post',
            data,
        });
    },
    fetchMedicineMaintenancePlan(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine-maintenance/plan',
            method: 'get',
            params,
        });
    },
    createdMedicineMaintenancePlan(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine-maintenance/plan',
            method: 'post',
            data,
        });
    },
    updatedMedicineMaintenancePlan(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine-maintenance/plan',
            method: 'put',
            data,
        });
    },
    // 增加运输记录草稿
    createdTransportRecord(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/transport/draft',
            method: 'post',
            data,
        });
    },
    // 修改运输记录
    modifyTransportRecord(transportRecordId, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/transport/${transportRecordId}`,
            method: 'put',
            data,
        });
    },
    // 拉取运输记录详情
    fetchTransportRecord(transportRecordId) {
        return fetchPack({
            url: `/api/v2/gsp/record/transport/${transportRecordId}`,
            method: 'get',
        });
    },
    // 拉取运输记录详情
    updateReceiveOrderRecord(receiveOrderId, data) {
        return fetchPack({
            url: `/api/v3/goods/receive/orders/${receiveOrderId}/orderinfo`,
            method: 'post',
            data,
        });
    },
    // 拉取运输记录详情
    updateReceiveOrderItemRecord(receiveOrderId,receiveOrderItemId, data) {
        return fetchPack({
            url: `/api/v3/goods/receive/orders/${receiveOrderId}/${receiveOrderItemId}`,
            method: 'post',
            data,
        });
    },
    fetchTransportList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/transport',
            method: 'get',
            params,
        });
    },
    /**
     * 查询不良反应记录单列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchAdverseReactionOrderList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/adverse-reaction/order',
            method: 'get',
            params,
        });
    },
    /**
     * 新增不良反应记录单
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createAdverseReactionOrder(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/adverse-reaction/order',
            method: 'post',
            data,
        });
    },
    /**
     * 查询不良反应记录单详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchAdverseReactionOrder(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/adverse-reaction/order/${id}`,
            method: 'get',
        });
    },
    /**
     * 删除不良反应记录单详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deleteAdverseReactionOrder(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/adverse-reaction/order/${id}`,
            method: 'delete',
        });
    },
    /**
     * 修改不良反应记录单详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    updateAdverseReactionOrder(id, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/adverse-reaction/order/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 查询陈列环境记录单列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchDisplayEnvironmentOrderList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/display-environment/order',
            method: 'get',
            params,
        });
    },
    /**
     * 新增陈列环境记录单
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createDisplayEnvironmentOrder(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/display-environment/order',
            method: 'post',
            data,
        });
    },
    /**
     * 查询陈列环境记录单
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchDisplayEnvironmentOrder(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/display-environment/order/${id}`,
            method: 'get',
        });
    },
    /**
     * 查询药品养护单列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchMedicineMaintenanceOrderList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine-maintenance/order',
            method: 'get',
            params,
        });
    },
    // 导出列表
    exportMedicineMaintenanceOrderList(params) {
        const url = '/api/v2/gsp/record/medicine-maintenance/order/export';
        return exportFileByAxios({
            url,
            params,
            paramsSerializer(p) {
                return Qs.stringify(p);
            },
        });
    },
    // 查询养护单列表【以批次维度展开药品】
    fetchMedicineMaintenanceOrderItemList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine-maintenance/order-item',
            method: 'get',
            params,
        });
    },
    /**
     * 新增药品养护单
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createMedicineMaintenanceOrder(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine-maintenance/order',
            method: 'post',
            data,
        });
    },
    // 修改药品养护单
    updateMedicineMaintenanceOrder(id,data) {
        return fetchPack({
            url: `/api/v2/gsp/record/medicine-maintenance/order/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 查询药品养护单详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchMedicineMaintenanceOrder(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/medicine-maintenance/order/${id}`,
            method: 'get',
        });
    },
    /**
     * 查询温湿度记录列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchTemperatureHumidityList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/temperature-humidity',
            method: 'get',
            params,
        });
    },
    /**
     * 新增温湿度记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createTemperatureHumidity(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/temperature-humidity',
            method: 'post',
            data,
        });
    },
    /**
     * 查询温湿度记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchTemperatureHumidity(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/temperature-humidity/${id}`,
            method: 'get',
        });
    },
    /**
     * 删除温湿度记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deleteTemperatureHumidity(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/temperature-humidity/${id}`,
            method: 'delete',
        });
    },
    /**
     * 查询温湿度区域配置列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchTemperatureHumidityRegionList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/temperature-humidity/region',
            method: 'get',
            params,
        });
    },
    /**
     * 新增温湿度区域配置
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createTemperatureHumidityRegion(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/temperature-humidity/region',
            method: 'post',
            data,
        });
    },
    /**
     * 修改温湿度区域配置
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    updateTemperatureHumidityRegion(id, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/temperature-humidity/region/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 删除温湿度区域配置
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deleteTemperatureHumidityRegion(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/temperature-humidity/region/${id}`,
            method: 'delete',
        });
    },
    /**
     * 查询召回记录列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchRecallRecordList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/recall-record/list',
            method: 'get',
            params,
        });
    },
    /**
     * 查询召回记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchRecallRecordInfo(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/recall-record/${id}`,
            method: 'get',
        });
    },
    /**
     * 新增召回记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createRecallRecord(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/recall-record',
            method: 'post',
            data,
        });
    },
    /**
     * 修改召回记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    updateRecallRecord(id, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/recall-record/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 删除召回记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deleteRecallRecord(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/recall-record/${id}`,
            method: 'delete',
        });
    },
    /**
     * 查询召回退货单信息
     */
    fetchRecallReturnOrder(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/recall-record/${id}/return`,
            method: 'get',
        });
    },
    /**
     * 查询追回记录列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchRecoverRecordList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/recover-record/list',
            method: 'get',
            params,
        });
    },
    /**
     * 查询追回记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchRecoverRecordInfo(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/recover-record/${id}`,
            method: 'get',
        });
    },
    /**
     * 新增追回记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createRecoverRecord(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/recover-record',
            method: 'post',
            data,
        });
    },
    /**
     * 修改追回记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    updateRecoverRecord(id, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/recover-record/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 删除追回记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deleteRecoverRecord(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/recover-record/${id}`,
            method: 'delete',
        });
    },
    /**
     * 查询人员健康记录列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchPersonnelHealthList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/personnel_health/list',
            method: 'get',
            params,
        });
    },
    /**
     * 查询人员健康记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchPersonnelHealthInfo(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/personnel_health/${id}`,
            method: 'get',
        });
    },
    /**
     * 新增人员健康记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createPersonnelHealth(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/personnel_health',
            method: 'post',
            data,
        });
    },
    /**
     * 修改人员健康记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    updatePersonnelHealth(id, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/personnel_health/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 删除人员健康记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deletePersonnelHealth(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/personnel_health/${id}`,
            method: 'delete',
        });
    },
    /**
     * 查询人员培训记录列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchPersonnelTrainList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/personnel_train/list',
            method: 'get',
            params,
        });
    },
    /**
     * 查询人员培训记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchPersonnelTrainInfo(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/personnel_train/${id}`,
            method: 'get',
        });
    },
    /**
     * 新增人员培训记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createPersonnelTrainList(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/personnel_train',
            method: 'post',
            data,
        });
    },
    /**
     * 修改人员培训记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    updatePersonnelTrainList(id, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/personnel_train/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 删除人员培训记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deletePersonnelTrain(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/personnel_train/${id}`,
            method: 'delete',
        });
    },
    /**
     * 查询中药装斗列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchMedicineAssembleBucketList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine_assemble_bucket/order/list',
            method: 'get',
            params,
        });
    },
    /**
     * 查询中药装斗记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchMedicineAssembleBucketInfo(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/medicine_assemble_bucket/order/${id}`,
            method: 'get',
        });
    },
    /**
     * 新增中药装斗记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createMedicineAssembleBucketList(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine_assemble_bucket/order',
            method: 'post',
            data,
        });
    },
    /**
     * 修改中药装斗记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    updateMedicineAssembleBucketList(id, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/medicine_assemble_bucket/order/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 删除中药装斗记录详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deleteMedicineAssembleBucket(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/medicine_assemble_bucket/order/${id}`,
            method: 'delete',
        });
    },
    /**
     * 查询中药清斗列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchMedicineClearBucketList(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine_clear_bucket/order/list',
            method: 'get',
            params,
        });
    },
    /**
     * 查询中药清斗详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    fetchMedicineClearBucketInfo(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/medicine_clear_bucket/order/${id}`,
            method: 'get',
        });
    },
    /**
     * 新增中药清斗
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    createMedicineClearBucketList(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/medicine_clear_bucket/order',
            method: 'post',
            data,
        });
    },
    /**
     * 修改中药清斗
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    updateMedicineClearBucketList(id, data) {
        return fetchPack({
            url: `/api/v2/gsp/record/medicine_clear_bucket/order/${id}`,
            method: 'put',
            data,
        });
    },
    /**
     * 删除中药清斗详情
     * <AUTHOR>
     * @date 2023-12-27
     * @param {String} id
     * @returns {Promise<Response>}
     */
    deleteMedicineClearBucket(id) {
        return fetchPack({
            url: `/api/v2/gsp/record/medicine_clear_bucket/order/${id}`,
            method: 'delete',
        });
    },
    /**
     * 查询中验收合格列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchInspectQualifiedItems(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/inspect/qualified/items',
            method: 'get',
            params,
        });
    },
    /**
     * 查询中验收不合格列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchInspectUnqualifiedItems(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/inspect/unqualified/items',
            method: 'get',
            params,
        });
    },
    /**
     * 获取批次列表
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchBatchList(params) {
        return fetchPack({
            url: '/api/v3/goods/stocks/batches',
            method: 'get',
            params,
        });
    },
    // 通过条件获取批次列表
    fetchBatchListByGoodsId(goodsId, params) {
        return fetchPack({
            url: `/api/v3/goods/${goodsId}/pharmacy/stocks/batches`,
            method: 'get',
            params,
        });
    },
    /**
     * 获取批次列表 - 多个goodsId
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} data
     * @returns {Promise<Response>}
     */
    fetchBatchListByGoodsIds(data) {
        return fetchPack({
            url: '/api/v3/goods/stocks/batches/in-pharmacy',
            method: 'post',
            data,
        });
    },
    // 获取装斗过的数据
    fetchInstallBatchListByGoodsIds(data) {
        return fetchPack({
            url: '/api/v2/gsp/record/goods-batches-container-loaded',
            method: 'post',
            data,
        });
    },
    // 批量获取批次
    fetchHandleBatchListByGoodsIds(data) {
        return fetchPack({
            url: '/api/v3/goods/pharmacy/stocks/batches',
            method: 'post',
            data,
        });
    },
    /**
     * 获取入库记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchRecordStockInItems(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/stock/in/items',
            method: 'get',
            params,
        });
    },
    /**
     * 获取销售记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchRecordSell(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/sell',
            method: 'get',
            params,
        });
    },
    /**
     * 获取报损记录
     * <AUTHOR>
     * @date 2023-12-27
     * @param {Object} params
     * @returns {Promise<Response>}
     */
    fetchRecordStockOutItems(params) {
        return fetchPack({
            url: '/api/v2/gsp/record/stock/out/items',
            method: 'get',
            params,
        });
    },
};
