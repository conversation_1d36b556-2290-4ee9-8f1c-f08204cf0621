import fetch from 'utils/fetch';
import Qs from 'qs';
export default {
    async membersV2(params) {
        const res = await fetch.get('/api/v2/crm/members/list', { params });
        return res.data;
    },

    /**
     * @desc 根据患者id获取会员状态
     * <AUTHOR>
     * @date 2018/05/22 19:01:31
     * @params
     * @return
     */
    async findMembers(id) {
        const ret = await fetch.get(`/api/v2/crm/patients/${id}/member`);
        return ret.data;
    },

    /**
     * @desc 根据手机尾号查询会员列表
     * <AUTHOR>
     * @date 2018/05/23 17:10:05
     * @params mobile 3-4位手机尾号
     */
    async fetchMemberByMobile(mobile) {
        const ret = await fetch.get(`/api/v2/crm/patients/members/query/by-mobile-last4?key=${mobile}`);
        return ret.data;
    },

    async memberTypes() {
        const res = await fetch.get('/api/v2/patients/members/types');
        return res && res.data;
    },

    createType(data) {
        return fetch.post('/api/v2/patients/members/types', data);
    },

    updateType(id, data) {
        return fetch.put(`/api/v2/patients/members/types/${id}`, data);
    },

    dropType(id) {
        return fetch.delete(`/api/v2/patients/members/types/${id}`);
    },

    charge(id, data) {
        return fetch.post(`/api/v2/patients/members/${id}/charge`, data);
    },

    chargeV2(id, data) {
        return fetch.post(`/api/v2/crm/members/${id}/recharge`, data);
    },

    chargeStatus(id) {
        return fetch.get(`/api/v2/crm/members/recharge/${id}/status`);
    },


    refund(id, data) {
        return fetch.post(`/api/v2/patients/members/${id}/refund`, data);
    },

    refundV2(id, data) {
        return fetch.post(`/api/v2/crm/members/${id}/refund`, data);
    },

    records(id, pageIndex, pageSize) {
        return fetch.get(`/api/v2/crm/members/${id}/bills`, {
            params: {
                limit: pageSize,
                offset: pageSize * pageIndex,
            },
        });
    },
    /**
     * @desc 会员充值凭证打印
     * <AUTHOR>
     * @date 2019/06/11 15:56:44
     */
    async rechargePrint(transactionId) {
        const res = await fetch({
            url: `/api/v2/charges/member/recharge/${transactionId}/print`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data || {};

    },

    /**
     * @desc 会员退费凭证打印
     */
    async refundPrint(transactionId) {
        const res = await fetch({
            url: `/api/v2/charges/member/refund/${transactionId}/print`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data || {};
    },

    /**
     * @desc 查询患者会员可退款列表
     */
    async fetchPatientRechargeMemberRefundableList(patientId,params) {
        const res = await fetch({
            url: `/api/v2/crm/members/${patientId}/recharge-refundable/list`,
            method: 'get',
            params,
            paramsSerializer (paramsStr) {
                return Qs.stringify(paramsStr);
            },
        });
        return res && res.data || {};
    },
};
