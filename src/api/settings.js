import Qs from 'qs';
import fetch from 'utils/fetch';
import fetchPack from 'utils/fetchPack';
import { exportFileByAxios } from 'utils/excel.js';

const settings = {
    /**
     * 成员管理
     */
    employee: {
        // 删除成员的签名
        async deleteSign() {
            const res = await fetch({
                url: '/api/v3/clinics/employees/delete-hand-sign',
                method: 'put',
            });
            return res.data;
        },
        // 添加成员
        smsEmployeeCreate(data) {
            return fetch({
                url: '/api/employees',
                method: 'post',
                data,
            });
        },
        async getChainEmployees() {
            const res = await fetch({
                url: '/api/v3/clinics/employees/could-import',
            });
            return res.data;
        },
        async importEmployees(data) {
            const res = await fetch({
                url: '/api/v3/clinics/employees/chain-import',
                method: 'post',
                data,
            });
            return res.data;
        },
        async fetchClinicAllEmployees() {
            const res = await fetch({
                url: `/api/v3/clinics/employees/pages?${Qs.stringify({
                    limit: 9999,
                    showDisable: 1,
                })}`,
                method: 'get',
            });
            return res.data || {};
        },
        // 获取邀请二维码
        fetchQRCode: (clinicid, employeeid, departmentid) => {
            return fetch({
                url: `/api/qrcodes/join/${clinicid}/${employeeid}?departmentid=${departmentid}`,
                method: 'get',
            });
        },
        // 移除员工
        remove: (id) => {
            return fetch({
                url: `/api/v3/clinics/employees/${id}`,
                method: 'delete',
            });
        },
        // 获取待审核员工
        async fetchUndoList () {
            const res = await fetch({
                url: '/api/clinicemployees/employees/todo',
                disabledCancel: true,
            });
            return res.data;
        },
        /**
         * @desc 根据成员id获取成员详情
         * <AUTHOR>
         * @date 2019/07/11 20:41:17
         */
        async fetchEmployeeById(id) {
            const res = await fetch({
                url: `/api/v2/clinics/employees/${id}`,
            });
            return res.data;
        },
        /**
         * @desc 根据成员id获取成员详情
         * <AUTHOR>
         * @date 2019/07/11 20:41:17
         */
        async fetchEmployeeByIdV3(id) {
            const res = await fetch({
                url: `/api/v3/clinics/employees/${id}`,
            });
            return res.data;
        },

        /**
         * @desc 修改成员信息 审核成员信息
         * <AUTHOR>
         * @date 2019/07/11 21:49:24
         */
        async updateEmployee(id, data) {
            const res = await fetch({
                url: `/api/v3/clinics/employees/${id}`,
                method: 'put',
                data,
            });
            return res.data;
        },
        /**
         * @desc 获取系统角色模板
         * <AUTHOR>
         * @date 2020/3/28
         */
        async fetchRoles() {
            const res = await fetch({
                url: '/api/v3/clinics/roles',
                method: 'get',
            });
            return res.data;
        },
        async fetchModules() {
            const res = await fetch({
                url: '/api/v2/clinics/modules',
                method: 'get',
            });
            return res.data;
        },
        /**
         * @desc 拉取成员全部模块 V3 版本
         */
        async fetchModulesV3() {
            const res = await fetch({
                url: '/api/v3/clinics/modules',
                method: 'get',
            });
            return res.data;
        },
        // 拉取预约申请音频设置
        async fetchRegistrationAppointmentVoiceSetting() {
            const res = await fetch.get('/api/v2/property/employee?key=pc.tips.voice.waitAuditRegistration');
            return res.data;
        },
        // 设置预约申请音频设置
        async updateRegistrationAppointmentVoiceSetting(params) {
            const res = await fetch.post('/api/v2/property/employee?key=pc.tips.voice.waitAuditRegistration',params);
            return res.data;
        },
        // 候诊叫号呼叫下一位是否展示提示确认弹窗
        async fetchCallingNextTips() {
            const res = await fetch.get('/api/v2/property/employee?key=callingNextTips');
            return res.data;
        },
        // 更新候诊叫号呼叫下一位是否展示提示确认弹窗
        async updateCallingNextTips(params) {
            const res = await fetch.post('/api/v2/property/employee?key=callingNextTips', params);
            return res.data;
        },
        async updateModule(id, data) {
            const res = await fetch({
                url: `/api/v3/clinics/employees/${id}/module-permissions`,
                method: 'put',
                data,
            });
            return res.data;
        },
        // 青岛电子签名承诺书上传
        async uploadElectronicSignature(electronicSignatureCommitmentUrl) {
            const res = await fetch({
                url: `api/v3/clinics/employees/config/electronic-sig-commit?electronicSignatureCommitmentUrl=${electronicSignatureCommitmentUrl}`,
                method: 'put',
            });
            return res?.data;
        },
        /** 修改成员状态
         * @param employeeId
         * @param data { status }
         * @return {Promise<*>}
         */
        async updateEmployeeStatus(employeeId, data) {
            const res = await fetch({
                url: `/api/v3/clinics/employees/${employeeId}/status`,
                method: 'put',
                data,
            });
            return res?.data;
        },
    },
    /**
     * 定价和税率&&库存设置
     */
    clinicGoodsConfig: {
        /**
     * @desc 获取连锁门店药品相关的配置
     * <AUTHOR>
     * @date 2021/9/1 11:34 上午
     * @returns
     */
        async getClinicGoodsConfig(params = {}) {
            const res = await fetch({
                url: '/api/v3/goods/config',
                method: 'get',
                params,
            });
            return res.data;
        },
        /**
     * @desc 修改获取连锁门店药品相关的配置
     * <AUTHOR>
     * @date 2021/9/1 11:40 上午
     * @returns
     */
        async updateClinicGoodsConfig(data) {
            const res = await fetch({
                url: '/api/v3/goods/config',
                method: 'post',
                data,
            });
            return res.data;
        },

        // 库存锁库配置修改
        async updatePriceMode(data) {
            const res = await fetch({
                url: '/api/v3/goods/config/price-up',
                method: 'put',
                data,
            });
            return res.data;
        },

        // 库存锁库配置修改
        async updateStockLockConfig(data) {
            const res = await fetch({
                url: '/api/v3/goods/config/stock_lock',
                method: 'put',
                data,
            });
            return res.data;
        },

        /**
         * @desc 拉取某个Goods的子店的信息
         * <AUTHOR>
         * @date 2022/4/13 18:00
         * @returns
         */
        async getSubClinicInfos(goodsId) {
            const res = await fetch({
                url: `/api/v3/goods/${goodsId}/sub-clinic-infos`,
                method: 'get',
            });
            return res.data;
        },

        /**
         * @desc 套餐切换门店定价为启用时判断是否有未开启的项目
         * @param goodsId
         * @returns {Promise<*>}
         */
        async subClinicInfosPreCheck(goodsId, data) {
            const res = await fetch({
                url: `/api/v3/goods/${goodsId}/sub-clinic-infos/pre-check`,
                method: 'put',
                data,
            });
            return res.data;
        },

        /**
         * @desc 修改一个goods的批量改价信息
         * <AUTHOR>
         * @date 2022/4/14 18:40
         * @returns
         */
        async modifySubClinicInfos(goodsId,data) {
            const res = await fetch({
                url: `/api/v3/goods/${goodsId}/sub-clinic-infos`,
                method: 'put',
                data,
            });
            return res.data;
        },

        // 查看某个药房能否删除
        async judgePharmacyCanDel(pharmacyNo) {
            const res = await fetch({
                url: `/api/v3/goods/config/pharmacy/check-can-delete/${pharmacyNo}`,
                method: 'get',
            });

            return res.data.data;
        },

        // 更新库存设置 - 药房设置内容
        async updatePharmacyConfig(data) {
            const res = await fetch({
                url: '/api/v3/goods/config/pharmacy',
                method: 'put',
                data,
            });
            return res.data;
        },

        // 获取药房里面有多少药
        async loadPharmacyGoods() {
            const res = await fetch({
                url: '/api/v3/goods/config/pharmacy/check-goods-count',
                method: 'get',
            });

            return res.data.data;
        },
    },

    /**
     * 诊所设置
     */
    clinic: {
        /**
         * @desc  获取诊所相关配置信息
         * <AUTHOR>
         * @date 2019/04/10 18:39:13
         */
        async fetchCurrentClinicConfig() {
            const res = await fetch({
                url: '/api/v2/clinics/current/config',
                method: 'get',
            });
            return res.data;
        },
        async updateClinicConfig(data) {
            const res = await fetch({
                url: '/api/v2/clinics/current/config',
                method: 'put',
                data,
            });
            return res.data;
        },

        async fetchClinicInfoById() {
            const res = await fetch({
                url: '/api/v3/clinics',
                method: 'get',
                disabledCancel: true,
            });
            return res;
        },
        async update(data) {
            const res = await fetch({
                url: '/api/v3/clinics',
                method: 'put',
                data,
            });
            return res.data;
        },

        async licenseUpdate(data) {
            const res = await fetch({
                url: '/api/v3/clinics/certs',
                method: 'put',
                data,
            });
            return res.data;
        },


        /**
         * 获取资质类型
         * @date 2024/1/27 - 12:37:30
         * <AUTHOR>
         *
         * @async
         * @returns {unknown}
         */
        async fetchQualificationType() {
            const res = await fetch({
                url: '/api/v3/clinics/pharmacy/config/qualification_type',
                method: 'get',
            });
            return res.data;
        },

        fetchClinicDepartments: (params) => {
            return fetch({
                url: '/api/v3/clinics/departments',
                params,
            });
        },
        fetchClinicOutpatientDepartments: () => {
            return fetch({
                url: '/api/v3/clinics/departments/outpatient',
            });
        },
        fetchClinicDepartmentsWithPermission: (employeeid) => {
            return fetch({
                url: `/api/v3/clinics/departments/employees/0/${employeeid}`,
            });
        },
        /**
         * 获取当前用户所在的科室列表
         * @param params
         * @returns {*}
         */
        async fetchClinicDepartmentsByCurEmployee(params) {
            const res = await fetch({
                url: '/api/v3/clinics/departments/get-by-cur-employee',
                params,
            });
            return res.data;
        },

        /**
         * @desc 科室相关增删改查
         * <AUTHOR>
         * @date 2019/07/10 15:33:36
         */
        async createDepartment(data) {
            const res = await fetch({
                url: '/api/v3/clinics/departments',
                method: 'post',
                data,
            });
            return res.data;
        },
        async deleteDepartment(id) {
            const res = await fetch({
                url: `/api/v3/clinics/departments/${id}`,
                method: 'delete',
                customErrorTips: true,
            });
            return res.data;
        },
        async updateDepartment(id, data) {
            const res = await fetch({
                url: `/api/v3/clinics/departments/${id}`,
                method: 'put',
                data,
            });
            return res.data;
        },
        async updateDepartmentHospital(id, data) {
            const res = await fetch({
                url: `/api/v3/clinics/departments/${id}/hospital`,
                method: 'put',
                data: {
                    employeeIds: data.employeeIds,
                },
            });
            return res.data;
        },
        async fetchDepartmentById(id, params) {
            const res = await fetch({
                url: `/api/v3/clinics/departments/${id}`,
                method: 'get',
                params,
            });
            return res.data;
        },

        /**
         * @desc 医疗设备相关 列表 增删改查
         * <AUTHOR>
         * @date 2019/07/10 15:33:36
         */
        async fetchMedicalEquipments({
            offset, limit,
        }) {
            const res = await fetch({
                url: '/api/v3/clinics/equipment/list',
                params: {
                    offset,
                    limit,
                },
            });
            return res.data;
        },
        async createMedicalEquipment(data) {
            const res = await fetch({
                url: '/api/v3/clinics/equipment',
                method: 'post',
                data,
            });
            return res.data;
        },
        async deleteMedicalEquipment(id) {
            const res = await fetch({
                url: `/api/v3/clinics/equipment/${id}`,
                method: 'delete',
            });
            return res.data;
        },
        async updateMedicalEquipment(id, data) {
            const res = await fetch({
                url: `/api/v3/clinics/equipment/${id}`,
                method: 'put',
                data,
            });
            return res.data;
        },
        async fetchMedicalEquipmentById(id) {
            const res = await fetch({
                url: `/api/v3/clinics/equipment/${id}`,
                method: 'get',
            });
            return res.data;
        },

        async loadSecurityLog(params = {
            offset: 0, limit: 20,
        }) {
            const res = await fetch({
                url: '/api/v2/clinics/security-log',
                method: 'get',
                params,
            });

            return res.data;
        },


        /**
         * 获取药店经营范围
         * @date 2024/1/27 - 12:39:35
         * <AUTHOR>
         *
         * @async
         * @returns { data:{ rows: Array } }
         */
        async fetchPharmacyBusinessScope() {
            const res = await fetch({
                url: '/api/v3/clinics/pharmacy/config/business_scope',
                method: 'get',
            });

            return res.data;
        },

        async updateClinicRegFee(data) {
            const res = await fetch({
                url: '/api/v2/registrations/employee-registration-fees/categories/clinic/default',
                method: 'put',
                data,
            });
            return res.data;
        },

        async getDoctorFeeInfo(params) {
            const res = await fetch({
                url: '/api/v2/registrations/employee-registration-fees/clinic/doctor/enable-categories',
                method: 'get',
                params,
            });
            return res.data;
        },

        async updateDoctorFeeInfo(departmentId, doctorId, data) {
            const res = await fetch({
                url: `/api/v2/registrations/employee-registration-fees/categories/${departmentId}/${doctorId}`,
                method: 'put',
                data,
            });
            return res.data;
        },

        async preCheckRegistrationFee(data) {
            const res = await fetch({
                url: '/api/v2/registrations/employee-registration-fees/cancel-categories/pre-check',
                method: 'post',
                data,
            });
            return res.data;
        },
    },
    /**
     * 个人设置
     */
    personal: {
        fetchEmployeeInfo: (id) => {
            return fetch({
                url: `/api/employees/${id}`,
                method: 'get',
            });
        },

        /**
         * @desc 设置人员基本的诊所配置
         * <AUTHOR>
         * @date 2019/09/29 14:57:41
         * @params
         * @return
         */
        async updateEmployeeConfig(data) {
            const res = await fetch({
                url: '/api/v3/clinics/employees/config',
                method: 'put',
                data,
            });
            return res.data;
        },

        updateEmployeeInfo: (id, data) => {
            return fetch({
                url: `/api/employees/${id}`,
                method: 'put',
                data,
            });
        },
        /**
         * @desc 医生手写签名获取token
         * <AUTHOR>
         * @date 2018/08/29 15:32:06
         */
        async fetchSignToken() {
            const res = await fetch({
                url: '/api/tokens',
                method: 'post',
                data: {
                    scene: 'doctor_hand_sign', // 使用场景
                    expired: 7200, // 过期时间，0-7200
                },
            });
            return res.data;
        },
    },
    /**
     * 检查项
     */
    examination: {
        validateExaminationExist: (name) => {
            return fetch({
                url: `/api/clinicexaminationexists/${name}`,
                method: 'get',
            });
        },
        fetchExaminationSuggest: (query, cid) => {
            return fetch.get('/api/v2/cdss/suggests/examination', {
                params: {
                    q: query,
                    cid,
                },
            });
        },
        // 删除子检查项
        async deleteSubExamination({
            id, subId,
        }) {
            const res = await fetch({
                url: `/api/v2/examinations/${id}/items/${subId}`,
                method: 'delete',
            });
            return res.data;
        },
        // 更新子检查项
        async updateSubExamination({
            id, data,
        }) {
            const res = await fetch({
                url: `/api/v2/examinations/${id}/items`,
                method: 'put',
                data,
            });

            return res.data;
        },
        // 新增子检查项
        async addSubExamination({
            id, data,
        }) {
            const res = await fetch({
                url: `/api/v2/examinations/${id}/items`,
                method: 'post',
                data,
            });

            return res.data;
        },

        async fetchItemRelated(id) {
            const res = await fetch({
                url: `/api/v2/examinations/${id}/items`,
            });
            return res.data;
        },

        /**
         * @desc 获取管理中检验列表
         * <AUTHOR>
         * @date 2019/12/11 10:57:24
         * @params offset [Number] 0 分页开始位置
         * @params limit  [Number] 10 每页输出的个数
         */
        async fetchExaminationList(params) {
            const res = await fetch({
                url: '/api/v2/examination-goods/examinations',
                params,
            });
            return res.data;
        },

        async fetchExaminationListV3(params) {
            const res = await fetch({
                url: '/api/v3/goods/goods-list/non-stock-goods',
                params,
                paramsSerializer (params) {
                    return Qs.stringify(params, { indices: false });
                },
            });
            return res.data;
        },
        /**
         * @desc 获取检验设备列表
         * <AUTHOR>
         * @date 2022/3/29 16:00:00
         */
        async fetchExaminationDevicesList(params) {
            const res = await fetch({
                method: 'get',
                url: '/api/v3/goods/exam/assay/my-device-models',
                params,
            });
            return res.data;
        },


        /**
         * @desc 获取管理检验项目详情
         * <AUTHOR>
         * @date 2019/12/11 14:06:31
         * @params id 检查项目id
         */
        async fetchExaminationById(id) {
            const res = await fetch({
                url: `/api/v2/examination-goods/examinations/${id}`,
            });
            return res.data;
        },

        /**
         * @desc 获取系统模板项目详情
         * <AUTHOR>
         * @date 2019/12/11 14:06:31
         * @params id 检查项目id
         */
        async fetchSystemDetailByExamGoodsId(id, subType) {
            const params = {
                subType,
            };
            const res = await fetch({
                url: `/api/v2/examinations/system-goods/${id}`,
                params,
            });
            return res.data;
        },

        /**
         * @desc 搜索获取系统模板
         * <AUTHOR>
         * @date 2021/12/27 10:57:31
         */
        async searchSystemGoods(params) {
            const res = await fetch({
                url: '/api/v3/goods/search/system/goods',
                params,
                paramsSerializer (params) {
                    return Qs.stringify(params, { indices: false });
                },
            });
            return res.data;
        },

        /**
         * @desc tab切换获取系统模板
         * <AUTHOR>
         * @date 2021/12/27 10:57:31
         */
        async fetchSystemGoods(params) {
            const res = await fetch({
                url: '/api/v3/goods/system/goods',
                params,
                paramsSerializer (p) {
                    return Qs.stringify(p, { indices: false });
                },
            });
            return res.data;
        },

        /**
         * @desc 统计模板使用次数
         * <AUTHOR>
         * @date 2021/12/27 10:57:31
         */
        async useSystemTemplate(goodsId) {
            const res = await fetch({
                url: `/api/v3/goods/system/goods/${goodsId}/use`,
                method: 'put',
            });
            return res.data;
        },

        /**
         * @desc 添加检验项目
         * <AUTHOR>
         * @date 2019/12/12 15:45:31
         */
        async createExaminationGoods(data) {
            const res = await fetch({
                url: '/api/v2/examination-goods/examinations',
                method: 'post',
                data,
            });
            return res.data;
        },

        // 拉取模版
        async standardIndicators() {
            const res = await fetch({
                url: '/api/v2/examination-goods/examinations/standard-indicators',
            });
            return res.data;
        },

        /**
         * @desc 修改检验项目
         * <AUTHOR>
         * @date 2019/12/12 15:45:31
         */
        async updateExaminationGoods(id, data) {
            const res = await fetch({
                url: `/api/v2/examination-goods/examinations/${id}`,
                method: 'put',
                data,
            });
            return res.data;
        },

        /**
         * @desc 删除检验项目
         * <AUTHOR>
         * @date 2019/12/12 15:45:31
         */
        async deleteExaminationGoods(id, reqConfig = {}) {
            const res = await fetch({
                url: `/api/v2/examination-goods/examinations/${id}`,
                method: 'delete',
                ...reqConfig,
            });
            return res.data;
        },

        /**
         * @desc 获取检查指标项列表
         * <AUTHOR>
         * @date 2019/12/11 16:14:49
         */
        async fetchTargets(params) {
            const res = await fetch({
                url: '/api/v2/examination-goods/items',
                params,
            });
            return res.data;
        },

        /**
         * @desc 根据关键字搜索本地子项或连锁子项
         * @returns {Promise<*>}
         */
        async fetchChildrenItems(params) {
            const res = await fetch({
                url: '/api/v2/examination-goods/examinations/include-standard/search',
                params,
                paramsSerializer (params) {
                    return Qs.stringify(params, { indices: false });
                },
            });
            return res.data;
        },

        /**
         * @desc 获取标准检验项
         * @param params
         * @returns {Promise<*>}
         */
        async fetchStandardExamination(params) {
            const res = await fetch({
                url: '/api/v2/examinations/standard/inspections/search',
                params,
                paramsSerializer (params) {
                    return Qs.stringify(params, { indices: false });
                },
            });
            return res.data;
        },

        /**
         * @desc 获取标准指标项
         * @param params
         * @returns {Promise<*>}
         */
        async fetchStandardRefs(params) {
            const res = await fetch({
                url: '/api/v2/examinations/standard/indicators/search',
                params,
                paramsSerializer (params) {
                    return Qs.stringify(params, { indices: false });
                },
            });
            return res.data;
        },
        /**
         * @desc 获取指标项目详情
         * <AUTHOR>
         * @date 2019/12/12 11:32:29
         * @params
         * @return
         */
        async fetchTargetById(id) {
            const res = await fetch({
                url: `/api/v2/examination-goods/items/${id}`,
            });
            return res.data;
        },

        /**
         * @desc 创建指标项目
         * <AUTHOR>
         * @date 2019/12/12 14:26:53
         */
        async createTarget(data) {
            const res = await fetch({
                url: '/api/v2/examination-goods/items',
                method: 'post',
                data,
            });
            return res.data;
        },

        /**
         * @desc 修改指标项目
         * <AUTHOR>
         * @date 2019/12/12 14:26:53
         */
        async updateTarget(id, data) {
            const res = await fetch({
                url: `/api/v2/examination-goods/items/${id}`,
                method: 'put',
                data,
            });
            return res.data;
        },

        /**
         * @desc 删除指标项目
         * <AUTHOR>
         * @date 2019/12/12 15:52:19
         */
        async deleteTarget(id) {
            const res = await fetch({
                url: `/api/v2/examination-goods/items/${id}`,
                method: 'delete',
            });
            return res.data;
        },
        /**
         * @desc 根据仪器Id获取单项项目列表
         * <AUTHOR>
         * @date 2022/04/06 17:02:19
         */
        async getSingleItemList(params) {
            const res = await fetch({
                url: '/api/v2/examination-goods/examinations/select-single-items',
                method: 'get',
                params,
            });
            return res.data;
        },
        /**
         * @Author: xuzhaofei
         * @Date: 2022-12-02 17:51:38
         * @description: 申请仪器对接工单列表
         * @param {*} params
         * @return {*}
         */
        async getApplyInstrumentOrderList() {
            const res = await fetch({
                url: '/api/v3/goods/exam/devices/apply',
                method: 'get',
            });
            return res.data.data;
        },
        /**
         * @Author: xuzhaofei
         * @Date: 2022-12-02 17:51:38
         * @description: 申请仪器对接工单
         * @param {*} params
         * @return {*}
         */
        async applyInstrumentOrder(data) {
            const res = await fetch({
                url: '/api/v3/goods/exam/devices/apply',
                method: 'post',
                data,
            });
            return res;
        },
        /**
         * @Author: xuzhaofei
         * @Date: 2022-12-02 17:51:38
         * @description: 根据id获取申请仪器对接工单
         * @param {*} id
         * @return {*}
         */
        async getApplyInstrumentOrderById(id) {
            const res = await fetch({
                url: `/api/v3/goods/exam/devices/apply/${id}`,
                method: 'get',
            });
            return res?.data?.data || {};
        },
        /**
         * @Author: xuzhaofei
         * @Date: 2022-12-02 17:51:38
         * @description: 修改申请仪器对接工单
         * @param {*} data
         * @return {*}
         */
        async updateInstrumentOrder(id,data) {
            const res = await fetch({
                url: `/api/v3/goods/exam/devices/apply/${id}`,
                method: 'put',
                data,
            });

            return res;
        },

        async fetchRecommendDeviceList() {
            const res = await fetch({
                url: '/api/v3/goods/exam/mall/device-models/recommend',
                method: 'get',
                params: {
                    categoryName: '检查检验仪器',
                },
            });

            return res;
        },
    },
    /**
     * 检查 - 新
     */
    inspect: {
        // 获取连锁内检查设备列表
        async getInspectDevices(params) {
            const res = await fetch({
                url: '/api/v3/goods/exam/inspection/devices',
                method: 'get',
                disabledCancel: true,
                params,
            });
            return res && res.data;
        },

        // 增加检查设备
        async createDevice(data) {
            const res = await fetch({
                url: '/api/v3/goods/exam/inspection/devices',
                method: 'post',
                data,
            });
            return res.data;
        },

        // 修改设备信息
        async updateDevice (data) {
            const res = await fetch({
                url: `/api/v3/goods/exam/inspection/devices/${data.id}`,
                method: 'put',
                data,
            });
            return res?.data;
        },

        // 拉取设备信息
        async getInspectDevice(deviceId) {
            const res = await fetch(`/api/v3/goods/exam/inspection/devices/${deviceId}`);
            return res && res.data;
        },
        // 创建剩余指标
        batchCreate(deviceModelId,data) {
            return fetchPack({
                url: `/api/v2/examination-goods/examinations/batch-create/${deviceModelId}`,
                method: 'post',
                data,
            });
        },
        // 检验我的设备列表
        fetchMyDeviceModels(params) {
            return fetchPack({
                url: '/api/v3/goods/exam/assay/my-device-models',
                method: 'get',
                params,
            });
        },
        // 获取门店内的检验设备列表
        fetchDeviceList(params) {
            return fetchPack({
                url: '/api/v3/goods/exam/assay/devices',
                method: 'get',
                disabledCancel: true,
                params,
            });
        },
        // 检验设备型号列表
        getExamDeviceModes(params) {
            return fetchPack({
                url: '/api/v3/goods/exam/assay/device-models',
                method: 'get',
                params,
            });
        },
        // 获取检验设备型号
        getDeviceModel(deviceId, combineType = '', onlyStandardGoods = '') {
            return fetchPack(`/api/v3/goods/exam/assay/device-models/${deviceId}?&combineType=${combineType}&onlyStandardGoods=${onlyStandardGoods}`);
        },
        // 检验设备详情
        getExamDeviceDetail(deviceId) {
            return fetchPack({
                url: `/api/v3/goods/exam/assay/devices/${deviceId}`,
                disabledCancel: true,
            });
        },
        // 设备联机
        linkDevice(deviceModelId) {
            return fetchPack({
                url: `/api/v3/goods/exam/assay/devices/link/${deviceModelId}`,
                method: 'put',
            });
        },
        // 保存或更新检验设备参数
        updateDeviceParameters(deviceId, data) {
            return fetchPack({
                url: `/api/v3/goods/exam/assay/devices/parameters/${deviceId}`,
                method: 'PUT',
                data,
            });
        },
        // 删除设备
        deleteDevice(deviceId, params) {
            return fetchPack({
                url: `/api/v3/goods/exam/assay/devices/${deviceId}`,
                method: 'DELETE',
                params,
            });
        },
        // 仪器数据回传
        uploadLisData(data) {
            return fetchPack({
                url: '/api/v2/examinations/devices/callback',
                method: 'POST',
                data,
            });
        },

        // 检查预约配置
        async getInspectReservationConfig() {
            const url = '/api/v2/examinations/config';

            const res = await fetch({
                url,
                method: 'GET',
            });

            return res.data;
        },

        async updateInspectReservationConfig(data) {
            const url = '/api/v2/examinations/config';

            const res = await fetch({
                url,
                method: 'POST',
                data,
            });

            return res.data;
        },
    },

    /**
     * 治疗项
     */
    treatment: {
        validateExaminationExist: (name) => {
            return fetch({
                url: `/api/clinictreatmentexists/${name}`,
                method: 'get',
            });
        },
        fetchExaminationSuggest: (query, cid) => {
            return fetch.get('/api/v2/cdss/suggests/treatment', {
                params: {
                    q: query,
                    cid,
                },
            });
        },
        /**
         * @desc 获取商品所属套餐
         * <AUTHOR>
         * @date 2019/12/17 16:34:56
         */
        async fetchComposesByGoodsId(id, disable) {
            const disabled = disable === undefined ? '' : disable;
            const res = await fetch({
                method: 'get',
                url: `/api/v3/goods/${id}/composes?disable=${disabled}`,
            });
            return res.data;
        },
    },
    /**
     * 挂号费设置
     */
    registeredFee: {
        /**
         * 获取所有挂号费列表
         * @returns {Promise<*>}
         */
        async fetchRegisteredList() {
            const res = await fetch({
                url: '/api/v2/registrations/fee-products/for-manage',
                method: 'get',
            });
            return res && res.data;
        },
        async updateRegisteredList(data) {
            const body = {
                feeProducts: data,
            };
            const res = await fetch({
                url: '/api/v2/registrations/fee-products',
                method: 'put',
                data: body,
            });
            return res && res.data;
        },

        fetchDoctorList: async () => {
            const res = await fetch({
                url: '/api/v2/clinics/employees/reg-fees',
                method: 'get',
            });
            return res.data;
        },

        updateRegisterFee: async (data) => {
            const res = await fetch({
                url: '/api/v2/clinics/employees/reg-fees',
                method: 'put',
                data,
            });
            return res.data;
        },

        loadEmployeeRegistrationFees: async () => {
            const res = await fetch({
                url: '/api/v2/registrations/employee-registration-fees',
                method: 'get',
            });

            return res.data;
        },

        updateDefaultRegistrationFees: async (data) => {
            const res = await fetch({
                url: '/api/v2/registrations/employee-registration-fees/clinic/default',
                method: 'put',
                data,
            });

            return res.data;
        },

        updateDoctorRegistrationFees: async (data) => {
            const res = await fetch({
                url: `/api/v2/registrations/employee-registration-fees/${data.departmentId}/${data.doctorId}`,
                method: 'put',
                data,
            });

            return res.data;
        },

        loadDoctorRegistrationFee: async (params = {}) => {
            const res = await fetch({
                url: '/api/v2/registrations/employee-registration-fees/clinic/doctor/registration-fee',
                method: 'get',
                params,
            });

            return res.data;
        },

        loadDoctorRegistrationFeeByCategories: async (params = {}) => {
            const res = await fetch({
                url: '/api/v2/registrations/employee-registration-fees/clinic/doctor/enable-categories',
                method: 'get',
                params,
            });

            return res.data;
        },
        /**
         * @desc 获取goodsId是否有关联设置挂号费
         * @param goodsId
         * @returns {Promise<*>}
         */
        fetchRegistrationsWithRelateGoods: async (goodsId) => {
            const res = await fetch({
                url: `/api/v2/registrations/employee-registration-fees/check/relate-goods/${goodsId}`,
                method: 'get',
            });

            return res.data;
        },

        // 挂号费关联费用项摊费

        async updateRegistrationFeeFlatPrice(data) {
            const res = await fetch({
                url: '/api/v2/registrations/employee-registration-fees/fee-goods/flat-price',
                method: 'post',
                data,
            });
            return res.data;
        },
    },
    /**
     * 设置处方模板
     */
    commonPrescription: {
        fetchPrescriptionTemplate: () => {
            return fetch({
                url: '/api/prescriptiontemplatedoctors',
                method: 'get',
            });
        },
        // 获取经典方剂模板
        async fetchOfficialTemplate() {
            const res = await fetch({
                url: '/api/prescriptionchinesetemplates',
            });
            return res.data.data || [];
        },
        /**
         * @desc 获取处方模板 列表
         * <AUTHOR>
         * @date 2019/04/06 14:17:37
         * @params type 1 连锁模板 2 诊所的模板 3 医生的模板（自己的模板）
         */
        async fetchPrescriptionTemplatesList(type) {
            const res = await fetch({
                url: `/api/v2/registrations/prescriptions/templates/?type=${type}`,
                method: 'get',
            });
            return res.data;
        },

        async fetchPrescriptionTemplateStock(data) {
            const res = await fetch({
                url: '/api/v3/goods/query/stocks',
                method: 'post',
                data,
            });
            return res.data;
        },

        /* 处方模板优化 */

        /**
         * desc [创建处方模板]
         */
        async insertPrescriptionTemplate(data) {
            const res = await fetch({
                url: '/api/v2/outpatients/prescriptions/templates',
                method: 'post',
                data,
            });
            return res.data;
        },
        /**
         * desc [修改处方模板]
         */
        async updatePrescriptionTemplate(templateId, data) {
            const res = await fetch({
                url: `/api/v2/outpatients/prescriptions/templates/${templateId}`,
                method: 'put',
                data,
            });
            return res.data;
        },
        /**
         * desc [查询处方模板列表]
         */
        async selectPrescriptionTemplates(params) {
            const {
                type, subType, keyword = '', offset, limit, tagId = '',
            } = params;
            const res = await fetch({
                url: `/api/v2/outpatients/prescriptions/templates?type=${type}&subType=${subType}&keyword=${keyword}&tagId=${tagId}&offset=${offset}&limit=${limit}`,
                method: 'get',
            });
            return res.data;
        },
        /**
         * desc [查询处方模板详情]
         */
        async selectTargtePrescriptionTemplate(templateId) {
            const res = await fetch({
                url: `/api/v2/outpatients/prescriptions/templates/${templateId}`,
                method: 'get',
            });
            return res.data;
        },
        /**
         * desc [删除模板]
         */
        async deleteTargtePrescriptionTemplate(templateId) {
            const res = await fetch({
                url: `/api/v2/outpatients/prescriptions/templates/${templateId}`,
                method: 'delete',
            });
            return res.data;
        },
        /**
         * desc [增加模板的使用次数]
         */
        async insertPrescriptionTemplateTagsCount(templateId) {
            const res = await fetch({
                url: `/api/v2/outpatients/prescriptions/templates/${templateId}/addUseCount`,
                method: 'get',
            });
            return res.data;
        },
        /**
         * desc [查询标签列表]
         */
        async selectTagsList(subTypes) {
            const res = await fetch({
                url: `/api/v2/outpatients/prescriptions/templates/tags?subTypes=${subTypes}`,
                method: 'get',
            });
            return res.data;
        },
        /**
         * desc [查询标签对应的模板数]
         */
        async selectTagsCount(subType) {
            const res = await fetch({
                url: `/api/v2/outpatients/prescriptions/templates/tagsCount?subType=${subType}`,
                method: 'get',
            });
            return res.data;
        },
        /**
         * desc [查询标签对应的模板数]
         */
        async insertTagsUsedCount(templateId) {
            const res = await fetch({
                url: `/api/v2/outpatients/prescriptions/templates/${templateId}/addUseCount`,
                method: 'post',
            });
            return res.data;
        },
    },
    mall: {
        // 获得商城待付款订单
        async getMallWaitingPaidOrderCountTips() {
            const baseUrl = process.env.MALL_API_BASE ? `//${process.env.MALL_API_BASE}` : `//${location.host}`;
            const res = await fetch({
                url: '/api/mall/orders/waiting-paid-tips',
                baseURL: baseUrl,
                withCredentials: true,
                method: 'get',
            });
            return res.data;
        },
    },
    precautionsTemplates: {
        /**
         * @desc 获取注意事项模板
         * <AUTHOR>
         * @date 2018/05/18 12:39:06
         */
        async fetchList(pageIndex, pageSize, key) {
            const res = await fetch({
                url: `/api/precautions?pageindex=${pageIndex}&pagesize=${pageSize}&key=${key}`,
                method: 'get',
            });
            return res.data;
        },

        /**
         * @desc 新建注意事项模板
         * <AUTHOR>
         * @date 2018/05/18 15:02:20
         * @params data
         */
        async create(data) {
            const res = await fetch({
                url: '/api/precautions',
                method: 'post',
                data,
            });
            return res.data;
        },

        /**
         * @desc 修改注意事项模板
         * <AUTHOR>
         * @date 2018/05/18 15:02:20
         */
        async update(data) {
            const res = await fetch({
                url: `/api/precautions/${data.id}`,
                method: 'put',
                data,
            });
            return res.data;
        },

        /**
         * @desc 删除注意事项模板
         * <AUTHOR>
         * @date 2018/05/18 15:02:20
         */
        async deleteTemplate(id, type) {
            const res = await fetch({
                url: `/api/precautions/${id}?type=${type}`,
                method: 'delete',
            });
            return res.data;
        },

        /**
         * @desc 根据names 查询注意事项模板
         * <AUTHOR>
         * @date 2018/05/21 16:12:02
         * @params names 以逗号的字符串
         */
        async findByName(names) {
            const res = await fetch({
                url: `/api/precautions/find?names=${names}`,
            });
            return res.data;
        },
    },
    /**
     * 预约设置
     */
    appointment: {
        // async fetchClinicConfig() {
        //     let res = await fetch({
        //         url: '/api/clinic/config'
        //     });
        //     return res.data;
        // },
        // updateClinicConfig(data) {
        //     return fetch({
        //         url: '/api/clinic/config',
        //         method: 'put',
        //         data
        //     })
        // }
    },
    /**
     * 物资
     */
    material: {
        list(pageIndex, pageSize, cadn = '', materialType = '', specification = '') {
            return fetch({
                url: `/api/materials?pageindex=${pageIndex}&pagesize=${pageSize}&cadn=${cadn}&materialtype=${materialType}&specification=${specification}`,
            });
        },
    },
    /**
     * 收费设置
     */
    chargeSet: {
        /**
         * desc [查询总店的所有收费方式列表]
         */
        async selectChargesConfig() {
            const res = await fetch({
                url: '/api/v2/charges/config',
                method: 'get',
            });
            return res.data;
        },
        async fetchChargesPayModeConfig() {
            const res = await fetch({
                url: '/api/v2/charges/config/pay-mode',
                method: 'get',
            });
            return res.data;
        },
        /**
         * desc [查询可用的收费方式列表]
         */
        async selectChargesConfigAvailable() {
            const res = await fetch({
                url: '/api/v2/charges/config/available',
                method: 'get',
            });
            return res.data;
        },
        /**
         * desc [更新收费配置]
         */
        async updateChargesConfig(data) {
            const res = await fetch({
                url: '/api/v2/charges/config',
                method: 'post',
                data,
            });
            return res.data;
        },
    },
    printSet: {
        // async updatePrintSet(data) {
        // 	let res = await fetch({
        // 		url: '/api/clinic/config',
        // 		method:'put',
        // 		data,
        // 	});
        // 	return res.data;
        // },
    },

    /**
     * 重构  检查项 治疗项统一  goods
     */
    goods: {
        /**
         * @desc
         * <AUTHOR>
         * @date 2019/03/27 14:54:21
         * @params
         *   offset  分页偏移量
         *   limit   分页数量
         *   type    类型
         *   subType 子类型
         *   keyword 关键字
         *   fileds  返回的字段
         * @return
         */
        async fetchGoodsList(params) {
            const res = await fetch({
                url: '/api/v3/goods/goods-list/non-stock-goods',
                params,
                paramsSerializer (params) {
                    return Qs.stringify(params, { arrayFormat: 'comma' });
                },
            });
            return res.data;
        },
        async exportGoodsList(params) {
            const url = '/api/v3/goods/goods-list/non-stock-goods/export';
            return exportFileByAxios({
                url,
                params,
                paramsSerializer(p) {
                    return Qs.stringify(p, { arrayFormat: 'comma' });
                },
            });
        },
        async fetchAllPhysiotherapy() {
            const res = await fetch({
                url: '/api/v2/goods/all-physiotherapy',
                method: 'get',
            });
            return res && res.data;
        },
        /**
         * @desc 根据对码获取关联的项目
         * @param goodsIdList
         * @param shebaoList
         * @returns {Promise<{ data:{ items: Array, shebaoRelatedItems: Array } }>}
         */
        async fetchFeeItemRelatedGoodsList(goodsIdList, shebaoList) {
            const res = await fetch.post('/api/v3/goods/re-match/related-items', {
                goodsIdList, shebaoList,
            });
            return res.data;
        },
        async syncFeeItemPriceAndMedicalCode(data) {
            const res = await fetch({
                url: '/api/v3/goods/re-match/sync',
                method: 'put',
                data,
                disabledCancel: true,
            });
            return res.data;
        },
    },

    weChatPay: {
        async fetchWeChatPayConfig() {
            const res = await fetch({
                url: '/api/v2/wechatpay/config',
                method: 'get',
            });
            return res.data;
        },
    },

    abcPay: {
        async fetchOpenAbcConfig() {
            const res = await fetch({
                url: '/api/v2/wechatpay/apply/allin-pay',
                method: 'post',
            });
            return res.data;
        },

        async fetchOpenAbcRecord() {
            const res = await fetch({
                url: '/api/v2/wechatpay/apply/allin-pay/record',
                method: 'get',
            });
            return res.data;
        },

        /**
         * @desc 获取店信息
         * <AUTHOR>
         * @date 2021/09/17 15:36:23
         */
        async fetchStoreInfo() {
            const res = await fetch ({
                url: '/api/v2/wechatpay/opened/clinic/list',
                methods: 'get',
            });
            return res.data;
        },
    },

    /**
     * @desc 发药设置请求API
     * <AUTHOR>
     * @date 2020-05-11 10:45:14
     */
    dispensing: {
        /**
         * @desc 获取快递费表格数据
         * <AUTHOR>
         * @date 2020-05-12 11:14:24
         */
        async fetchDeliveryRuleTable(params) {
            const res = await fetch({
                url: 'api/v2/charges/rule/expresses',
                params,
                paramsSerializer (params) {
                    return Qs.stringify(params, { indices: false });
                },
            });
            return res.data;
        },
        /**
         * @desc 获取 快递费规则
         * <AUTHOR>
         * @date 2020-05-12 09:53:43
         * @params id
         */
        async fetchDeliveryRule(id) {
            const res = await fetch({
                url: `/api/v2/charges/rule/express/${id}`,
                method: 'get',
            });
            return res.data;
        },
        /**
         * @desc 更新 快递费规则
         * <AUTHOR>
         * @date 2020-05-12 09:53:43
         * @params id
         */
        async updateDeliveryRule(id, data) {
            const res = await fetch({
                url: `/api/v2/charges/rule/express/${id}`,
                method: 'put',
                data,
            });
            return res;
        },
        /**
         * @desc 创建 快递费规则
         * <AUTHOR>
         * @date 2020-05-12 09:53:43
         * @params id
         */
        async createDeliveryRule(data) {
            const res = await fetch({
                url: 'api/v2/charges/rule/express',
                method: 'post',
                data,
            });
            return res;
        },
        /**
         * @desc 改变快递费规则的启用禁用状态
         * <AUTHOR>
         * @date 2020-05-11 10:49:24
         * @params
         * @return
         */
        async updateDeliveryRuleStatus(id, data) {
            const res = await fetch({
                url: `/api/v2/charges/rule/express/${id}/status`,
                method: 'put',
                data,
            });
            return res;
        },
        /**
         * @desc 删除快递费规则
         * <AUTHOR>
         * @date 2020-05-11 10:49:24
         * @params
         * @return
         */
        async deleteDeliveryRule(id) {
            const res = await fetch({
                url: `/api/v2/charges/rule/express/${id}`,
                method: 'delete',
            });
            return res;
        },
        /**
         * @desc 获取加工费规则table 数据
         * <AUTHOR>
         * @date 2020-05-12 14:12:27
         * @params
         * @return
         */
        async fetchProcessFeeTable(params) {
            const res = await fetch({
                url: '/api/v2/charges/rule/processes',
                method: 'get',
                params,
                paramsSerializer (params) {
                    return Qs.stringify(params, { indices: false });
                },
            });
            return res.data;
        },
        /**
         * @desc 创建加工费规则
         * <AUTHOR>
         * @date 2020-05-12 14:08:58
         */
        async createProcessFeeRule(data) {
            const res = await fetch({
                url: '/api/v2/charges/rule/process',
                method: 'post',
                data,
            });
            return res.data;
        },
        async deleteProcessFeeRule(id) {
            const res = await fetch({
                url: `/api/v2/charges/rule/process/${id}`,
                method: 'delete',
            });
            return res;
        },
        /**
         * @desc 修改加工费规则
         * <AUTHOR>
         * @date 2020-05-12 14:11:24
         */
        async updateProcessFeeRule(id, data) {
            const res = await fetch({
                url: `/api/v2/charges/rule/process/${id}`,
                method: 'put',
                data,
            });
            return res.data;
        },
        /**
         * @desc 修改加工费规则 状态
         * <AUTHOR>
         * @date 2020-05-12 14:11:24
         */
        async updateProcessFeeStatus(id, data) {
            const res = await fetch({
                url: `/api/v2/charges/rule/process/${id}/status`,
                method: 'put',
                data,
            });
            return res.data;
        },
        /**
         * @desc 查询加工费规则
         * <AUTHOR>
         * @date 2020-05-12 14:11:24
         */
        async fetchProcessFeeRule(id) {
            const res = await fetch({
                url: `/api/v2/charges/rule/process/${id}`,
                method: 'get',
            });
            return res.data;
        },
        /**
         * @desc: 更新发药设置
         * @author: ff
         * @time: 2025/3/26
         */
        async updateDispensingConfig(data) {
            const res = await fetch({
                url: '/api/v2/dispensings/config/update',
                method: 'put',
                data,
            });
            return res.data;
        },
    },

    chronic: {
        async switch(data) {
            const res = await fetch({
                url: '/api/v2/crm/chronic/recovery-templates/chronic/recovery/switch',
                method: 'put',
                data,
            });

            return res.data;
        },
    },


    // 库房设置
    wareHouse: {
        // 多库房保存
        async createWarehouse(data) {
            const res = await fetch({
                url: '/api/v3/goods/config/pharmacy/storage',
                method: 'post',
                data,
            });

            return res.data;
        },
        // 多库房修改
        async updateWarehouse(id, data) {
            const res = await fetch({
                url: `/api/v3/goods/config/pharmacy/storage/${id}`,
                method: 'put',
                data,
            });

            return res?.data;
        },

        // 库房详情
        async getWareHouseDetail(wareHouseId) {
            const res = await fetch({
                url: `/api/v3/goods/config/pharmacy/storage/${wareHouseId}`,
                method: 'get',
            });

            return res?.data?.data;
        },

        // 下达规则列表
        async getPharmacyListRule(params) {
            const res = await fetch({
                url: '/api/v3/goods/config/pharmacy/rule/list',
                method: 'get',
                params,
            });

            return res?.data;
        },
        // 创建下达规则
        async createRule(data) {
            const res = await fetch({
                url: '/api/v3/goods/config/pharmacy/rule',
                method: 'post',
                data,
            });

            return res?.data;
        },

        // 修改下达规则
        async updateRule(id, data) {
            const res = await fetch({
                url: `/api/v3/goods/config/pharmacy/rule/${id}`,
                method: 'put',
                data,
            });

            return res?.data;
        },
        // 获取下达规则详情
        async getRuleDetail(id) {
            const res = await fetch({
                url: `/api/v3/goods/config/pharmacy/rule/${id}`,
                method: 'get',
            });

            return res?.data;
        },

        // 下拉规则排序
        async sortRule(data) {
            const res = await fetch({
                url: '/api/v3/goods/config/pharmacy/rule/list/sort',
                method: 'post',
                data,
            });
            return res?.data;
        },

        async fetchTags() {
            const res = await fetch({
                url: '/api/v3/goods/goods-tag-types',
                method: 'get',
            });
            return res?.data;
        },
        async updateTags(data) {
            const res = await fetch({
                url: '/api/v3/goods/goods-tag-types',
                method: 'post',
                data,
            });
            return res?.data;
        },
    },

    /**
     * 门店信息
     */
    chainInfo: {
        // 门店列表
        async getChainList(params,data) {
            const res = await fetch({
                url: '/api/v3/clinics/chain/clinics/search',
                method: 'get',
                params,
                data,
            });
            return res?.data;
        },

        // 修改门店类型
        async updateChainInfo(clinicId, busMode, data) {
            const res = await fetch({
                url: `/api/v3/clinics/${clinicId}/bus-mode/${busMode}`,
                method: 'put',
                data,
            });
            return res?.data;
        },
    },

    /**
     * 科室管理
     */
    department: {
        /** 修改科室状态
         * @param departmentId
         * @param data { status }
         * @return {Promise<*>}
         */
        async updateDepartmentStatus(departmentId, data) {
            const res = await fetch({
                url: `/api/v3/clinics/departments/${departmentId}/status`,
                method: 'put',
                data,
            });
            return res?.data;
        },
    },

    /**
     * 病区管理
     */
    ward: {
        /** 修改病区状态
         * @param wardId
         * @param data { status }
         * @return {Promise<*>}
         */
        async updateWardStatus(wardId, data) {
            const res = await fetch({
                url: `/api/ward/area/${wardId}/status`,
                method: 'put',
                data,
            });
            return res?.data;
        },
    },
    // 操作日志
    operationLog: {
        getOperationLogList: async (data) => {
            const res = await fetch({
                url: '/api/v2/trace/report/logs',
                method: 'post',
                data,
            });
            return res?.data;
        },
        getOperationTypeOption: async () => {
            const res = await fetch({
                url: '/api/v2/trace/report/logs/user-operation-types',
                method: 'get',
            });
            return res?.data;
        },
        export: async (params,filename) => {
            const url = '/api/v2/trace/report/logs/export';
            return exportFileByAxios({
                filename,
                url,
                params,
                paramsSerializer(p) {
                    return Qs.stringify(p, { arrayFormat: 'comma' });
                },
                method: 'post',
            });
        },
    },

    /**
     * 药诊互通设置
     */
    coPharmacyClinic: {
        /**
         * 总部获取当前互联互通账号列表
         * @returns {Promise<*>}
         */
        async getCoClinicListByChain(params) {
            const res = await fetch({
                url: '/api/v3/clinics/co-clinic/list-by-chain',
                method: 'get',
                params,
            });
            return res?.data;
        },
        /**
         * 子店获取当前互联互通账号列表
         * @returns {Promise<*>}
         */
        async getCoClinicListByClinic(params) {
            const res = await fetch({
                url: '/api/v3/clinics/co-clinic/list-by-clinic',
                method: 'get',
                params,
            });
            return res?.data;
        },
        /**
         * 药店删除合作诊所
         * @param coClinicId
         * @param clinicId
         * @returns {Promise<*>}
         */
        async deleteCoClinic(coClinicId, clinicId) {
            const res = await fetch({
                url: `/api/v3/clinics/co-clinic/${coClinicId}/clinic/${clinicId}`,
                method: 'delete',
            });
            return res?.data;
        },

        /**
         * 合作诊所药店统计详情
         * @returns {Promise<*>}
         */
        async getCoClinicCountInfo(params) {
            const res = await fetch({
                url: '/api/v3/clinics/co-clinic/count-info',
                method: 'get',
                params,
            });
            return res?.data;
        },
    },

    // 叫号设置
    callNumber: {
        // 拉取检查叫号屏幕列表
        async fetchInspectScreenList() {
            const { data } = await fetch.get('/api/v2/examinations/calling/devices');

            return data.data;
        },

        // 修改检查叫号屏幕配置
        async updateInspectScreenConfig(id, data) {
            const res = await fetch.put(`/api/v2/examinations/calling/devices/${id}`, data);

            return res.data;
        },

        // 拉取检查叫号配置
        async fetchInspectConfig() {
            const { data } = await fetch.get('/api/v2/examinations/calling/config');

            return data.data;
        },

        // 更新检查叫号配置
        async updateInspectConfig(data) {
            const res = await fetch.put('/api/v2/examinations/calling/config',data);

            return res.data;
        },

        // 解绑检查叫号屏幕
        async unbindInspectScreen(id) {
            const res = await fetch.put(`/api/v2/examinations/calling/devices/${id}/unbind`);

            return res.data;
        },
    },
    safeLogin: {
        getClinicSecurityLogin: async () => {
            const res = await fetch({
                url: '/api/v3/clinics/security-login',
                method: 'get',
            });
            return res?.data;
        },

        /**
         * @param {object} data
         * @param {array} data.devices - 安全登录限制mac列表
         * @param {array} data.employeeIds - 安全登录限制人员id列表
         * @return {number} data.isOpen - 安全登录限制：0关闭1打开
         * @description: 保存安全登录设置
         * @author: ff
         * @date: 2024/9/10
         */
        postClinicSecurityLogin: async (data) => {
            const res = await fetch({
                url: '/api/v3/clinics/security-login',
                method: 'post',
                data,
            });
            return res?.data;
        },
    },

    /**
     * 区域中心检查检验
     */
    areaInspectionCenter: {
        getCoopStoreList: async (params) => {
            const res = await fetch({
                url: '/api/v3/goods/config/pharmacy/coop',
                method: 'get',
                params,
            });
            return res.data?.data || {};
        },
        updateCoopStoreDetail: async (id,data) => {
            const res = await fetch({
                url: `/api/v3/goods/config/pharmacy/coop/${id}`,
                method: 'put',
                data,
            });
            return res?.data;
        },
        async copyCoopGoods(data) {
            const res = await fetch({
                url: '/api/v3/goods/coop-goods',
                method: 'post',
                data,
            });
            return res.data;
        },
        deleteCoopGoods: async(goodsId) => {
            const res = await fetch({
                url: `/api/v3/goods/coop-goods/${goodsId}`,
                method: 'delete',
            });
            return res?.data;
        },
    },

    // 外包项目
    outSourcing: {
        // 拉取中心门店列表
        async fetchCenterOrganList() {
            const { data } = await fetch.get('/api/v3/goods/config/pharmacy/coop-center', {
                params: {
                    pharmacyType: 20,
                },
            });

            return data;
        },

        /**
         * 添加中心门店的外包项目到本地
         * @param {Object<{ coopChainId: string;coopClinicId: string;pharmacyType: number;items: Array<{ goodsId: string; }> }>} data
         * @returns {Promise<*>}
         */
        async addOutSourcingGoodsFromCenterOrgan(data) {
            const { data: res } = await fetch.post('/api/v3/goods/join-coop-goods', {
                ...data,
                pharmacyType: 20,
            });

            return res.data;
        },

        /**
         * 拉取中心门店的外包项目
         * @param {object} params
         * @param {string} params.keyword
         * @param {number} params.goodsType
         * @param {number} params.goodsSubType
         * @param {number | string} params.deviceType
         * @param {string} params.coopChanId
         * @param {string} params.coopClinicId
         * @param {number} params.offset
         * @param {number} params.limit
         * @returns {Promise<undefined>}
         */
        async fetchOutSourcingGoodsFromCenterOrgan(params) {
            const { data } = await fetch.get('/api/v3/goods/coop-goods-list', {
                params: {
                    pharmacyType: 20,
                    ...params,
                },
            });

            return data;
        },
    },
    cloudDecoction: {
        async fetchCloudDecoctionConfig() {
            const res = await fetch({
                url: '/api/v3/goods/supplier/cloud-decoction',
                method: 'get',
            });
            return res?.data;
        },
        async saveCloudDecoctionConfig(data) {
            const res = await fetch({
                url: '/api/v3/goods/supplier/cloud-decoction',
                method: 'post',
                data,
            });
            return res?.data;
        },
        /**
         * @desc: 获取煎药上报
         * @author: ff
         * @time: 2025/4/14
         */
        async fetchCloudDecoctionList(params) {
            const res = await fetch({
                url: '/api/v2/dispensings/smart/prescriptions',
                method: 'get',
                params,
                paramsSerializer: (params) => {
                    return Qs.stringify(params, { indices: false });
                },
            });
            return res?.data;
        },
    },
};

export default settings;
