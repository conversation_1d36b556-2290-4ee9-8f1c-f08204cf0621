import fetch from 'utils/fetch';
export default {
    // 商城日志上报
    async report(data) {
        const res = await fetch({
            url: '/api/report',
            method: 'post',
            data,
        });
        return res.data;
    },

    async pvReport(data) {
        const res = await fetch({
            url: '/api/v2/pv/report',
            method: 'post',
            data: {
                pv: data,
            },
        });
        return res.data;
    },

    // 滑块验证码校验
    async captchaVerify(data) {
        const res = await fetch({
            url: '/api/v2/captcha/verify',
            method: 'post',
            data,
        });
        return res.data;
    },
};
