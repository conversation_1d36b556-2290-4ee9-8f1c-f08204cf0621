import fetch from 'utils/fetch';
import Qs from 'qs';
import { delayTimeout } from 'utils/delay';
import { goodsTypeIdEnum } from 'views/air-pharmacy/constants.js';
import { GoodsTypeEnum } from '@abc/constants';

/**
 * 收费相关
 */
const Charge = {
    /**
     * @desc 获取收费单列表
     * <AUTHOR>
     * @date 2019/03/21 20:34:50
     * @params 参数名    含义         缺省     默认值
     * @params keyword  搜索关键词    NO       -
     * @params offset   分页偏移量    NO       0
     * @params limit    分页数量      NO      20
     */
    async fetchQuickList(params, disabledCancel = false) {
        const res = await fetch({
            url: '/api/v2/charges',
            method: 'get',
            params,
            disabledCancel,
            paramsSerializer(p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
        return res.data;
    },

    /**
     * @desc 获取直接收费推荐的药品以及检查治疗项
     * <AUTHOR>
     * @date 2019/03/30 17:46:49
     * @params cid    门店id
     */
    async fetchExamTreat(clinicId) {
        const res = await fetch({
            url: '/api/v3/goods/query/exam-treat',
            params: {
                clinicId,
            },
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 直接收费支持复制处方
     * <AUTHOR>
     * @date 2019/11/14 14:26:41
     * @params
     * @return charge detail
     */
    async outpatientCopyToCharge(patientOrderId) {
        const res = await fetch({
            url: `/api/v2/charges/patientorder/${patientOrderId}/copy`,
        });
        return res.data;
    },

    /**
     * @desc 搜索搜有商品
     * <AUTHOR>
     * @date 2019/03/30 11:07:52
     */
    async fetchAllGoods(params) {
        const res = await fetch({
            url: '/api/v3/goods/search/all',
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { arrayFormat: 'repeat' });
            },
        });
        return res.data;
    },

    /**
     * @desc
     * <AUTHOR>
     * @date 2019/04/01 11:57:45
     * @params
     * @return
     */
    async fetchAllItem() {
        const res = await fetch({
            url: '/api/v2/goods/query/treatments_examinations',
        });
        return res.data;
    },

    getCalcChargeItemStruct(item) {
        return {
            keyId: item.keyId,
            id: item.id,
            unit: item.unit,
            name: item.name,
            unitCount: item.unitCount,
            doseCount: item.doseCount || 1,
            unitPrice: item.unitPrice,
            productId: item.productId,
            productType: item.productType,
            productSubType: item.productSubType,
            sourceUnitPrice: item.sourceUnitPrice,
            sourceTotalPrice: item.sourceTotalPrice,
            expectedUnitPrice: item.expectedUnitPrice,
            expectedTotalPriceRatio:
            item.expectedTotalPriceRatio,
            expectedTotalPrice: item.expectedTotalPrice,
            expectedDoseCount: item.expectedDoseCount,
            useDismounting: item.useDismounting,
            pharmacyType: item.pharmacyType,
            pharmacyNo: item.pharmacyNo,
            pharmacyName: item.pharmacyName,
            doctorInfo: item?.doctorInfo || null,
            isExpectedBatch: item.isExpectedBatch,
            isFixedData: item.isFixedData,
            chargeFormItemBatchInfos: item.chargeFormItemBatchInfos,
            singlePromotions: item.singlePromotions || null,
            isGift: item.isGift,
            sourceFormItemId: item.sourceFormItemId,
            sourceFormItemKeyId: item.sourceFormItemKeyId,
            giftGoodsPromotionId: item.giftGoodsPromotionId,
            traceableCodeList: item.traceableCodeList,
            shebaoDismountingFlag: item.shebaoDismountingFlag,
            traceableCodeRule: item.traceableCodeRule,
            originalChargeFormItemIds: item.originalChargeFormItemIds,
            usageInfo: item.usageInfo,
            productPrimaryId: item.productPrimaryId,
            sort: item.sort,
            composeChildren: item.composeChildren?.length ? item.composeChildren.map((child, index) => {
                child.sort = index;
                return Charge.getCalcChargeItemStruct(child);
            }) : null,
        };
    },

    /**
     * @desc 算费接口
     * <AUTHOR>
     * @date 2019/03/26 14:00:55
     */
    async calcFee(data, needSimplify = true) {
        /**
         * @desc 简化需要提交的postData,减小包体积
         * <AUTHOR> Yang
         * @date 2020-07-28 08:57:16
         */
        if (needSimplify) {
            const chargeForms = [];
            data.chargeForms.forEach((form) => {
                const chargeFormItems = form.chargeFormItems.filter((item) => {
                    return item.checked;
                });
                if (chargeFormItems.length) {
                    const tempForm = {
                        keyId: form.keyId,
                        id: form.id,
                        registration: form.registration,
                        sourceFormType: form.sourceFormType,

                        specification: form.specification,
                        deliveryInfo: form.deliveryInfo,
                        vendorId: form.vendorId,
                        vendorUsageScopeId: form.vendorUsageScopeId,
                        vendorName: form.vendorName,
                        pharmacyType: form.pharmacyType,
                        pharmacyNo: form.pharmacyNo,
                        pharmacyName: form.pharmacyName,
                        usageScopeId: form.usageScopeId,
                        medicineStateScopeId: form.medicineStateScopeId,

                        deliveryRule: form.deliveryRule,
                        processRule: form.processRule,
                        processInfo: form.processInfo,

                        totalPrice: form.totalPrice,
                        expectedTotalPrice: form.expectedTotalPrice,
                        expectedPriceFlag: form.expectedPriceFlag,
                        chargeFormItems: chargeFormItems.map((item, index) => {
                            item.sort = index;
                            return Charge.getCalcChargeItemStruct(item);
                        }),
                        usageInfo: form.usageInfo,
                        doseCount: form.doseCount,
                    };
                    chargeForms.push(tempForm);
                }
            });

            data.chargeForms = chargeForms;
        }

        const res = await fetch({
            url: '/api/v2/charges/calculate',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 获取收费单详情
     * <AUTHOR>
     * @date 2019/03/21 20:32:23
     * @params id 收费单id
     */
    async fetch(id, disabledCancel = false) {
        const res = await fetch({
            url: `/api/v2/charges/${id}`,
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },

    /**
     * @desc 获取门诊单详情
     * <AUTHOR>
     * @date 2019/04/15
     * @params id 收费单id
     */
    async fetchOutpatientDetail(id) {
        const res = await fetch({
            url: `/api/v2/charges/${id}/outpatient`,
            method: 'get',
        });
        return res.data;
    },

    async create(data, customErrorTips = true) {
        const res = await fetch({
            url: '/api/v2/charges',
            method: 'post',
            data,
            customErrorTips,
        });
        return res.data;
    },

    /**
     * @desc 收费单付费
     * <AUTHOR>
     * @date 2019/03/21 22:44:29
     * @params id charge id
     * @params data 付费数据
     */
    async paid(id, data, delayTime, customErrorTips = true) {
        const startTime = Date.now();
        const res = await fetch({
            url: `/api/v2/charges/${id}/paid`,
            method: 'put',
            data,
            customErrorTips,
        });
        if (delayTime) {
            await delayTimeout(startTime, delayTime);
        }
        return res.data;
    },

    /**
     * @desc 组合付费接口（ 单个收费单， 第二次付费）
     * <AUTHOR>
     * @date 2018/07/25 15:10:35
     */
    async repaid(id, data, delayTime, customErrorTips = true) {
        const startTime = Date.now();
        const res = await fetch({
            url: `/api/v2/charges/${id}/repaid`,
            method: 'put',
            data,
            customErrorTips,
        });
        if (delayTime) {
            await delayTimeout(startTime, delayTime);
        }
        return res.data;
    },

    /**
     * @desc 重新收费
     * <AUTHOR>
     * @date 2019/07/04 17:53:48
     */
    async renewpaid(id, data, delayTime, customErrorTips = true) {
        const startTime = Date.now();
        const res = await fetch({
            url: `/api/v2/charges/${id}/renewpaid`,
            method: 'put',
            data,
            customErrorTips,
        });
        if (delayTime) {
            await delayTimeout(startTime, delayTime);
        }
        return res.data;
    },

    /**
     * @desc 仅支付接口
     * <AUTHOR>
     * @date 2021-10-29 14:32:35
     */
    async pay(chargeSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/pay`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 收费单部分收费情况下直接退费
     * <AUTHOR>
     * @date 2019/04/09 13:34:14
     */
    async paidBack(id, data, delayTime) {
        const startTime = Date.now();
        const res = await fetch({
            url: `/api/v2/charges/${id}/paidback`,
            method: 'PUT',
            data,
        });
        if (delayTime) {
            await delayTimeout(startTime, delayTime);
        }
        return res.data;
    },

    /**
     * @desc 退费结构
     * <AUTHOR>
     * @date 2019/03/31 21:58:20
     */
    async refund(id, data, delayTime, customErrorTips = false) {
        const startTime = Date.now();
        const res = await fetch({
            url: `/api/v2/charges/${id}/refund`,
            method: 'put',
            data,
            customErrorTips,
        });
        if (delayTime) {
            await delayTimeout(startTime, delayTime);
        }
        return res.data;
    },

    /**
     * @desc 退费前校验
     */
    async refundPreCheck(id, data) {
        const res = await fetch({
            url: `/api/v2/charges/${id}/refund/pre-check`,
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * desc [已退费状态下-重新收费]
     */
    async renew(id) {
        const res = await fetch({
            url: `/api/v2/charges/${id}/renew`,
            method: 'put',
        });
        return res.data;
    },
    /**
     * @desc 修改支付方式
     * <AUTHOR>
     * @date 2019/04/11 22:44:16
     * @params id chargeid
     */
    async changePayMode(id, data) {
        const res = await fetch({
            url: `/api/v2/charges/${id}/changepaymode`,
            method: 'PUT',
            data,
        });
        return res.data;
    },

    async fetchDispensing(id) {
        const res = await fetch({
            url: `/api/v2/dispensings/chargesheet/${id}/`,
            method: 'get',
        });
        return res.data;
    },
    async fetchChargePrint(id) {
        const res = await fetch({
            url: `/api/print/charges/${id}/print`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },
    /**
     * @Description: 长护医疗清单接口
     * <AUTHOR> Cai
     * @date 2022/07/29 15:43:15
     */
    async fetchChanghuPrint(id) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/print/${id}`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },
    async updateInfoPatch(id, data) {
        const res = await fetch({
            url: `/api/v2/charges/${id}/paid/patch`,
            method: 'PUT',
            data,
        });
        return res.data;
    },
    async fetchPaystatus(id) {
        const res = await fetch({
            url: `/api/v2/charges/paystatus/${id}`,
            method: 'GET',
        });
        return res.data;
    },
    /**
     * desc [0元取消收费-在唤起医保刷卡后，若取消刷卡+收费，收费单回到待收费状态]
     */
    async fetchZeroCancel(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/cancel`,
            method: 'PUT',
        });
        return res.data;
    },

    /**
     * desc 欠费还款、长护取消支付
     */
    async putChargeCancel(combineOrderPayTransactionId) {
        const res = await fetch({
            url: `/api/v2/charges/combine-order/${combineOrderPayTransactionId}/cancel`,
            method: 'PUT',
        });
        return res.data;
    },

    /**
     * @desc 挂单：移到挂单分类，稍后再收。零售草稿和门诊待收费都支持挂单
     * <AUTHOR>
     * @date 2019/11/21 17:38:41
     */
    async hangUpOrder(data) {
        const res = await fetch({
            url: '/api/v2/charges/draft/save',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 保存：更新收费单修改内容
     * <AUTHOR>
     * @date 2020/02/12 22:28:29
     */
    async saveOrder(data, customErrorTips = false) {
        const res = await fetch({
            url: '/api/v2/charges/save',
            method: 'put',
            data,
            customErrorTips,
        });
        return res && res.data;
    },

    /**
     * @desc 删除挂单
     * <AUTHOR>
     * @date 2019/11/21 20:09:04
     */
    async deleteChargeSheet(chargeSheetId, customErrorTips = false) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}`,
            method: 'delete',
            customErrorTips,
        });
        return res.data;
    },

    /**
     * @desc 保存医保限价设置项目
     * <AUTHOR>
     * @date 2019/11/28 09:48:37
     */
    async saveLimitPrice(data) {
        const res = await fetch({
            url: '/api/v2/charges/medicare/limit/price',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 获取医保限价
     * <AUTHOR>
     * @date 2019/11/28 14:11:59
     */
    async fetchLimitPrice() {
        const res = await fetch({
            url: '/api/v2/charges/medicare/limit/price',
        });
        return res.data;
    },

    /**
     * @desc 获取患者地址信息
     * <AUTHOR>
     * @date 2020/02/11 20:41:32
     */
    async fetchPatientAddress(patientId) {
        const res = await fetch({
            url: '/api/v2/charges/delivery/address',
            params: {
                patientId,
            },
        });
        return res.data;
    },

    /**
     * @desc 获取添加患者地址h5页面二维码url
     */
    async fetchPatientUploadAddressUrl(patientId, scene) {
        const res = await fetch({
            url: '/api/v2/mc/qr-code/patient/address/h5-qr-code',
            method: 'get',
            params: {
                patientId,
                scene,
            },
            paramsSerializer(val) {
                return Qs.stringify(val);
            },

        });
        return res.data;
    },
    /**
     * @desc 获取患者地址上传h5二维码状态
     */
    async fetchPatientUploadAddressStatus(key) {
        const res = await fetch({
            url: '/api/v2/mc/qr-code/patient/address/h5-qr-code/status',
            method: 'get',
            params: {
                key,
            },
            paramsSerializer(val) {
                return Qs.stringify(val);
            },

        });
        return res.data;
    },

    /**
     * @desc 获取患者地址信息
     * <AUTHOR>
     * @date 2020/02/11 20:41:32
     */
    async checkDeliveryScope(data) {
        const res = await fetch({
            url: '/api/v2/charges/delivery/checkscope',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 获取快递公司列表
     * <AUTHOR>
     * @date 2020/02/12 19:13:47
     */
    async fetchDeliveryCompanies(params) {
        const res = await fetch({
            url: '/api/v2/charges/delivery/companies',
            disabledCancel: true,
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },

    /**
     * @desc 创建快递公司
     * <AUTHOR>
     * @date 2020/02/12 19:13:47
     */
    async createDeliveryCompany(data) {
        const res = await fetch({
            url: '/api/v2/charges/delivery/company',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 修改快递公司
     * <AUTHOR>
     * @date 2020/02/12 19:13:47
     */
    async updateDeliveryCompany(id, data) {
        const res = await fetch({
            url: `/api/v2/charges/delivery/company/${id}`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 删除快递公司
     * <AUTHOR>
     * @date 2020/02/12 19:13:47
     */
    async deleteDeliveryCompany(id) {
        const res = await fetch({
            url: `/api/v2/charges/delivery/company/${id}`,
            method: 'delete',
        });
        return res.data;
    },

    /**
     * @desc 根据地址查找快递公司
     * <AUTHOR>
     * @date 2020-05-22 17:51:57
     */
    async findDeliveryCompanyByAddress(params) {
        const res = await fetch({
            url: '/api/v2/charges/rule/express/find-company',
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },
    /**
     * @desc 根据地址信息查询可用的快递公司列表
     * <AUTHOR>
     * @date 2022-04-02 17:51:57
     */
    async findAvailableCompanyByAddress(params) {
        const res = await fetch({
            url: '/api/v2/charges/rule/express/available-companies',
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },
    /**
     * @desc 根据地址查找快递公司
     * <AUTHOR>
     * @date 2020-05-22 17:51:57
     */
    async findProcessRule(params) {
        const res = await fetch({
            url: 'api/v2/charges/rule/process/usage',
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },

    /**
     * @desc 查询推送信息
     * <AUTHOR>
     * @date 2020/02/13 14:33:01
     */
    async fetchChargesOrderDetail(data) {
        const res = await fetch({
            url: '/api/v2/charges/orderdetail',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 推送收费订单给患者
     * <AUTHOR>
     * @date 2020/02/13 14:33:01
     */
    async pushOrder(data) {
        const res = await fetch({
            url: '/api/v2/charges/pushorder',
            method: 'post',
            data,
        });
        return res && res.data;
    },

    /**
     * desc [将收费单详情转化成二维码传输数据]
     * 拉取收费单社保收费信息
     */
    async handleQrCodeData(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/shebao/chongqing/charge/${chargeSheetId}`,
            method: 'get',
        });
        return res.data;
    },

    async fetchProcessUsagesAll() {
        const res = await fetch({
            url: 'api/v2/charges/rule/process/usages',
            method: 'get',
        });
        return res.data;
    },
    /**
     * @desc 查询可用的制法列表
     * <AUTHOR>
     * @date 2020-06-01 14:19:29
     * @params
     * @return
     */
    async fetchUsageAvailable(disabledCancel) {
        const res = await fetch({
            url: '/api/v2/charges/rule/process/usages/available',
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },
    /**
     * @desc 查询所有制法列表
     * <AUTHOR>
     * @date 2022-06-20 15:31:16
     */
    async fetchAllUsage() {
        const res = await fetch({
            url: '/api/v2/charges/rule/process/usages',
            method: 'get',
        });
        return res.data;
    },
    /**
     * @desc 计算加工费
     * <AUTHOR>
     * @date 2020-06-01 18:12:54
     * @params
     * @return
     */
    async calcProcessFee(data) {
        const res = await fetch({
            url: '/api/v2/charges/calculate/process',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 计算快递费
     * <AUTHOR>
     * @date 2020-06-01 23:59:34
     */
    async calcDeliveryFee(data) {
        const res = await fetch({
            url: '/api/v2/charges/calculate/delivery',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 空中药房订单支付
     * <AUTHOR> Yang
     * @date 2020-07-09 15:18:09
     * @params
     * @return
     */
    async payAirPharmacyOrder(data) {
        const res = await fetch({
            url: '/api/v2/charges/air-pharmacy/pay',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 空中药房订单物流信息
     * <AUTHOR>
     * @date 20201-04-22 15:18:09
     * @params
     * @return
     */
    async getAirPharmacyLogisticsTrace(vendorId, orderId, logisticsId) {
        const res = await fetch(
            '/api/v2/charges/air-pharmacy/logistics-trace',
            {
                params: {
                    vendorId,
                    orderId,
                    logisticsId,
                },
            },
        );
        return res.data;
    },

    /**
     * @desc 查询订单列表
     * <AUTHOR> Yang
     * @date 2020-07-09 21:02:28
     * @params
     * @return
     */
    async fetchAirPharmacyOrder(params) {
        const res = await fetch({
            url: '/api/v2/charges/air-pharmacy/orders',
            params,
            paramsSerializer(params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },

    /**
     * @desc 查询订单详情
     * <AUTHOR> Yang
     * @date 2020-07-10 14:57:29
     * @params
     * @return
     */
    async fetchOrderDetail(orderId) {
        const res = await fetch({
            url: `/api/v2/charges/air-pharmacy/orders/${orderId}`,
        });
        return res.data;
    },

    /**
     * @desc 查询待支付的订单明细
     * <AUTHOR> Yang
     * @date 2020-07-10 15:58:34
     * @params
     * @return
     */
    async fetchWaitPayOrders(balanceId) {
        const res = await fetch({
            url: `/api/v2/charges/air-pharmacy/orders/wait-pay/${balanceId}`,
        });
        return res.data;
    },

    /**
     * @desc 取消空中订单
     * <AUTHOR> Yang
     * @date 2020-07-13 14:23:04
     * @params
     * @return
     */
    async cancelAirPharmacyOrder(orderId, data) {
        const res = await fetch({
            url: `/api/v2/charges/air-pharmacy/${orderId}/cancel`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 申请售后
     * <AUTHOR> Yang
     * @date 2020-07-13 17:24:29
     */
    async applyAirPharmacyOrder(orderId, data) {
        const res = await fetch({
            url: `/api/v2/charges/air-pharmacy/${orderId}/refund-apply`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 撤销售后单接口
     * <AUTHOR> Yang
     * @date 2020-07-21 10:02:42
     */
    async withdrawAirPharmacyAfterSale(orderId, afterSaleId) {
        const res = await fetch({
            url: `/api/v2/charges/air-pharmacy/${orderId}/withdraw-aftersale/${afterSaleId}`,
            method: 'put',
        });
        return res.data;
    },

    async unlockChargeOrder(chargeSheetId) {
        const { data } = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/un-lock`,
            method: 'put',
        });
        return data;
    },

    /**
     * @desc 开发票
     * <AUTHOR> Yang
     * @date 2020-12-10 09:23:46
     */
    async invoiceOrder(chargeSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/invoice/${chargeSheetId}`,
            method: 'put',
            data,
        });
        return res.data;
    },

    async invoiceOrderHospital(hospitalSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/invoice/${hospitalSheetId}`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 查询发票开票状态
     * <AUTHOR> Yang
     * @date 2020-12-10 14:02:33
     */
    async queryInvoiceStatus(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/invoice/${chargeSheetId}`,
        });
        return res.data;
    },

    /**
     * @desc 关闭收费单
     * <AUTHOR> Yang
     * @date 2020-12-10 09:23:46
     */
    async closeChargeOrder(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/close`,
            method: 'put',
        });
        return res.data;
    },

    /**
     * @desc 部分支付的收费单查询可支付的卡项
     * <AUTHOR>
     * @date 2021-08-11 19:54:23
     * @params chargeSheetId
     */
    async queryPatientCardByChargeSheetId(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/patient-card`,
        });
        return res.data;
    },

    /**
     * @desc 查询收费单已收费的收费方式及金额
     * <AUTHOR>
     * @date 2021-08-12 16:00:53
     */
    async queryPaidModesByChargeSheetId(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/paid-modes`,
        });
        return res.data;
    },

    /**
     * @desc 发票作废
     * <AUTHOR>
     * @date 2021-08-16 18:21:12
     * @params
     * @return
     */
    async destroyInvoice(chargeSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/invoice/${chargeSheetId}/destroy`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @Description: 住院发票作废
     * <AUTHOR> Cai
     * @date 2022/07/20 18:07:13
     */
    async destroyInvoiceHospital(hospitalSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/invoice/${hospitalSheetId}/destroy`,
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 发票冲红
     * <AUTHOR>
     * @date 2021-09-01 12:05:07
     * @params
     * @return
     */
    async chongHongInvoice(chargeSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/invoice/${chargeSheetId}/chonghong`,
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @Description: 住院发票冲红
     * <AUTHOR> Cai
     * @date 2022/07/20 18:07:53
     */
    async chongHongInvoiceHospital(hospitalSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/invoice/${hospitalSheetId}/chonghong`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 锁单且保存收费单
     * <AUTHOR>
     * @date 2021-12-30 18:03:10
     */
    async saveLockOrder(data) {
        const res = await fetch({
            url: '/api/v2/charges/lock-save',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 查询收费单入账异常列表
     * <AUTHOR>
     * @date 2021-08-12 16:00:53
     */
    async queryAbnormalTransactions(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/list-abnormal-transactions`,
        });
        return res.data;
    },

    /**
     * @desc 异常退费
     * <AUTHOR>
     * @date 2021-12-31 11:35:35
     */
    async abnormalRefund(chargeSheetId, abnormalTransactionId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/${abnormalTransactionId}/abnormal-refund`,
            method: 'put',
        });
        return res.data;
    },
    async changeChargeComment({
        chargeSheetId, chargeActionId,
    }, data) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/${chargeActionId}/charge-action`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 校验结算单是否可以结算
     * <AUTHOR>
     * @date 2022-02-22 15:09:00
     * @params hospitalSheetId 住院单id
     */
    async checkHospitalCanPaid(hospitalSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/${hospitalSheetId}/check-can-paid`,
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 住院结算单收费
     * <AUTHOR>
     * @date 2022-02-18 18:11:27
     * @params hospitalSheetId 住院单id
     * @params data {
     *   "payItem": {
     *     "amount": 0,
     *     "authCode": "string",
     *     "change": 0,
     *     "memberCardPassword": "string",
     *     "payMode": 0,
     *     "paySubMode": 0,
     *     "presentAmount": 0,
     *     "principalAmount": 0,
     *     "thirdPartyPayCardBalance": 0,
     *     "thirdPartyPayCardId": "string",
     *     "thirdPartyPayCardOwner": "string",
     *     "thirdPartyPayIdCardNum": "string",
     *     "thirdPartyPayTransactionId": "string",
     *     "transactionIds": [
     *       "string"
     *     ]
     *   },
     *   "receivableFee": 0
     * }
     */
    async hospitalDischarge(hospitalSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/${hospitalSheetId}/paid`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 结算单收费
     * <AUTHOR>
     * @date 2022-02-18 18:11:27
     * @params hospitalSheetId 住院单id
     * @params data {
     *     "payItem": {
     *     "amount": 0,
     *         "authCode": "string",
     *         "change": 0,
     *         "memberCardPassword": "string",
     *         "payMode": 0,
     *         "paySubMode": 0,
     *         "presentAmount": 0,
     *         "principalAmount": 0,
     *         "thirdPartyPayCardBalance": 0,
     *         "thirdPartyPayCardId": "string",
     *         "thirdPartyPayCardOwner": "string",
     *         "thirdPartyPayIdCardNum": "string",
     *         "thirdPartyPayTransactionId": "string",
     *         "transactionIds": ["string"]
     *      },
     *     "receivableFee": 0
     * }
     */
    async hospitalRefund(hospitalSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/${hospitalSheetId}/refund`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 查询住院结算单详情
     * <AUTHOR>
     * @date 2022-02-18 18:56:52
     * @params hospitalSheetId 住院单id
     * @params params offset: 0, limit: 5
     */
    async fetchHospitalChargeList(hospitalSheetId, params) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/${hospitalSheetId}`,
            params,
        });
        return res.data;
    },

    /**
     * @desc 查询住院支付单的支付状态
     * <AUTHOR>
     * @date 2022-02-18 18:57:19
     * @params chargeHospitalPayTransactionId
     */
    async fetchHospitalPayStatus(chargeHospitalPayTransactionId) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/paystatus/${chargeHospitalPayTransactionId}`,
        });
        return res.data;
    },

    /**
     * @desc 查询支付单的支付状态-长护和还款用 chargeType： 1、2
     * <AUTHOR>
     * @date 2022/05/16 15:21:39
     * @param combineOrderPayTransactionId
     */
    async fetchSpecialChargeTypePayStatus(combineOrderPayTransactionId) {
        const res = await fetch({
            url: `/api/v2/charges/combine-order/pay-status/${combineOrderPayTransactionId}`,
        });
        return res.data;
    },

    /**
     * @desc 将已退的结算单变为待收
     * <AUTHOR>
     * @date 2022-02-24 18:04:31
     * @params hospitalSheetId 住院单id
     */
    async renewHospitalChargeSheet(hospitalSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/${hospitalSheetId}/renew`,
            method: 'put',
        });
        return res.data;
    },

    /**
     * @desc 根据患者id查询待还的欠费单列表
     * <AUTHOR>
     * @date 2022/05/11 15:20:43
     * @param String patientId
     * @return Array
     */
    async getPatientOweList(patientId) {
        const res = await fetch({
            url: `/api/v2/charges/owe/patient/${patientId}`,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 单个欠费单还款
     * <AUTHOR>
     * @date 2022/05/11 15:31:07
     * @param chargeOweSheetId
     */
    async oweSingleRepayment(chargeOweSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/owe/${chargeOweSheetId}/paid`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 多个欠费单组合还款
     * <AUTHOR>
     * @date 2022/05/11 15:24:11
     */
    async oweRepayment(data) {
        const res = await fetch({
            url: '/api/v2/charges/owe/combine-paid',
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 获取重新收费收费单
     * <AUTHOR>
     * @date 2022-04-06 09:40:50
     */
    async getRenewChargeSheetById(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/renew`,
        });
        return res.data;
    },
    /**
     * @desc 长护收费单重新收费
     * <AUTHOR>
     * @date 2022/05/30 13:52:10
     */
    async repaidHospital(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/hospital-charge-sheet/${chargeSheetId}/renewpaid`,
            method: 'put',
        });
        return res.data;
    },

    /**
     * @desc 获取本地药房加工费算费请求数据
     * <AUTHOR>
     * @date 2022-06-17 15:07:36
     */
    getLocalProcessPostData(prescriptionForm) {
        const {
            keyId,
            doseCount,
            usageType,
            usageSubType,
            processBagUnitCount,
            totalProcessCount,
            prescriptionFormItems,
        } = prescriptionForm;

        return {
            keyId,
            doseCount,
            processInfo: {
                processBagUnitCount,
                totalProcessCount,
                subType: usageSubType,
                type: usageType,
            },
            items: prescriptionFormItems
                .filter((item) => {
                    return item.name && item.unitCount;
                })
                .map((item) => {
                    return {
                        name: item.name,
                        productId: item.productId || item.goodsId,
                        productSubType: item.productSubType || item.subType,
                        productType: item.productType || item.type,
                        unit: item.unit,
                        unitCount: item.unitCount,
                        unitPrice: item.unitPrice,
                    };
                }),
        };
    },
    /**
     * @desc 获取空中药房算费请求数据
     * <AUTHOR>
     * @date 2022-06-17 15:07:36
     */
    getAirProcessPostData(prescriptionForm) {
        const {
            id,
            keyId,
            doseCount,
            specification,

            vendorId,
            usageScopeId,
            medicineStateScopeId,

            prescriptionFormItems,
            chargeFormItems,

            deliveryInfo,
            usage,
            dailyDosage,
            freq,
            usageLevel,
            usageInfo,
            totalProcessCount,
        } = prescriptionForm;

        let goodsTypeId = goodsTypeIdEnum.ZYYP;
        if (specification.indexOf('中药颗粒') > -1) {
            goodsTypeId = goodsTypeIdEnum.ZYKL;
        }
        return {
            keyId: id || keyId,
            medicineStateScopeId,
            usageScopeId,
            goodsTypeId,
            vendorId,
            deliveryInfo,
            usageInfo: {
                usage: usage || usageInfo?.suage,
                dailyDosage: dailyDosage || usageInfo?.dailyDosage,
                freq: freq || usageInfo?.freq,
                usageLevel: usageLevel || usageInfo?.usageLevel,
                doseCount,
                totalProcessCount,
            },
            items: (prescriptionFormItems || chargeFormItems)
                .filter((item) => {
                    return (
                        item.name &&
                        item.unitCount &&
                        (item.productType === GoodsTypeEnum.MEDICINE ||
                            item.productInfo?.type === GoodsTypeEnum.MEDICINE)
                    );
                })
                .map((item) => {
                    return {
                        productId: item.productId,
                        name: item.name,
                        unitPrice: item.unitPrice,
                        unit: item.unit,
                        unitCount: item.unitCount,
                        doseCount: doseCount || item.doseCount,
                    };
                }),
        };
    },

    /**
     * @desc 计算中药处方（本地药房）加工费
     * <AUTHOR>
     * @date 2022-06-17 14:53:26
     */
    async localPharmacyCalcProcessFee(data) {
        const res = await fetch({
            url: '/api/v2/charges/outpatient/calculate/process',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 计算中药处方（空中药房）费用信息
     * <AUTHOR>
     * @date 20201-04-22 15:18:09
     */
    async airPharmacyCalculate(data) {
        const res = await fetch({
            url: '/api/v2/charges/air-pharmacy/calculate',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 虚拟药房算快递费
     * <AUTHOR>
     * @date 2023-08-29 17:59:34
     */
    async virtualPharmacyCalcDelivery(data) {
        const res = await fetch({
            url: '/api/v2/charges/virtual-pharmacy/calculate/delivery',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 根据患者id查询代还的单据数量
     * <AUTHOR>
     * @date 2023/04/07 09:40:50
     * @param <string> patientId
     * @return <object> {owingCount}
     */
    async fetchOwingCount(patientId, chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/patient/${patientId}/owing-count`,
            method: 'post',
            data: {
                patientId,
                excludeChargeSheetIds: [chargeSheetId],
            },
        });
        return res?.data || {};
    },

    /**
     * @desc 计算加工袋数
     * <AUTHOR>
     * @date 2023/06/19 17:05:54
     * @param {Object} data {dailyDosage, doseCount, freq}
     * @return {Object} {bagTotalCount, bagUnitCount}
     */
    async calculateBagCount(data) {
        const res = await fetch({
            url: '/api/v2/charges/rule/process/bag-count/calculate',
            method: 'post',
            data,
        });
        return res?.data?.data || {};
    },

    /**
     * @desc  根据患者id查询欠费总金额
     * <AUTHOR>
     * @date 2023/07/13 16:38:03
     * @param {String} patientId
     * @return {Object}
     */
    async getOwingAmount(patientId) {
        const res = await fetch({
            url: `/api/v2/charges/owe/patient/${patientId}/owing-amount`,
            method: 'get',
        });
        return res?.data?.data || {};
    },
    /**
     * @desc 登记信息详情
     * <AUTHOR>
     * @date 2024/01/29 21:34:45
     * @param {String} registerInfoId
     * @return {Object}
     */
    async getRegisterInfo(registerInfoId) {
        const res = await fetch({
            url: `/api/v2/gsp/register/${registerInfoId}`,
            method: 'get',
        });
        return res?.data?.data || {};
    },
    /**
     * @desc 实名登记
     * <AUTHOR>
     * @date 2024/01/24 10:06:55
     * @param {Object} data
     * @return {Object} res
     */
    async registerIdentity(data) {
        const res = await fetch({
            url: '/api/v2/gsp/register/add/identity',
            method: 'post',
            data,
        });
        return res?.data?.data || {};
    },
    /**
     * @desc 处方登记
     * <AUTHOR>
     * @date 2024/01/24 10:06:55
     * @param {Object} data
     * @return {Object} res
     */
    async registerPrescription(data) {
        const res = await fetch({
            url: '/api/v2/gsp/register/add/prescription',
            method: 'post',
            data,
        });
        return res?.data?.data || {};
    },

    // 药房获取销售单号
    async fetchSaleOrderList(params) {
        const res = await fetch({
            url: '/api/v2/charges/sales-order/list',
            method: 'get',
            params,
            paramsSerializer(p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
        return res.data;
    },

    /**
     * @desc 更新收费单备注
     * <AUTHOR>
     * @date 2024/03/04 09:38:31
     * @param {Object} data {chargeSheetId, remark, businessId, sceneType}
     * @param {String} data.chargeSheetId
     * @param {String} data.remark
     * @param {String} data.businessId formId 或者 itemId，如果修改收费单的备注，可以不传
     * @param {String} data.sceneType 0: 修改item备注 1：修改form备注 2：修改sheet备注
     *
     * }
     */
    async updateChargeRemark(data) {
        const res = await fetch({
            url: '/api/v2/charges/only-remark-update',
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 根据chargeSheetId查询推送支付需要的订单详情
     * <AUTHOR>
     * @date 2024/01/29 21:34:45
     * @param {String} registerInfoId
     * @return {Object}
     */
    async getChargePushPaymentOrderDetail(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/push-scan-code`,
            method: 'get',
        });
        return res?.data || {};
    },

    async printExecuteCertificate(executeRecordId) {
        const res = await fetch({
            url: `/api/print/charges/execute-records/${executeRecordId}/print-info`,
            method: 'get',
        });
        return res?.data || {};
    },

    /**
     * @desc 查询药店合作诊所处方订单列表
     * <AUTHOR> Yang
     * @date 2024-08-08 19:18:31
    */
    async fetchCooperationOrder(params) {
        const res = await fetch({
            url: '/api/v2/charges/cooperation-order',
            method: 'get',
            params,
            paramsSerializer(p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
        return res?.data || {};
    },

    /**
     * @desc 通过合作诊所订单id查询详情
     * <AUTHOR> Yang
     * @date 2024-08-08 19:18:31
    */
    async fetchCooperationDetailById(id) {
        const res = await fetch({
            url: `/api/v2/charges/cooperation-order/${id}`,
            method: 'get',
        });
        return res?.data || {};
    },

    /**
     * @desc 合作订单提单
     * <AUTHOR> Yang
     * @date 2024-08-08 19:18:31
    */
    async extractCooperationOrder(id) {
        const res = await fetch({
            url: `/api/v2/charges/cooperation-order/extract/${id}`,
            method: 'put',
        });
        return res?.data || {};
    },
    /**
     * @desc 合作订单重新提单
     * <AUTHOR> Yang
     * @date 2024-08-08 19:18:31
    */
    async reExtractCooperationOrder(id) {
        const res = await fetch({
            url: `/api/v2/charges/cooperation-order/re-extract/${id}`,
            method: 'put',
        });
        return res?.data || {};
    },

    /**
     * @desc 获取合作订单打印信息
     * <AUTHOR> Yang
     * @date 2024-08-13 15:20:58
    */
    async fetchCooperationOrderPrint(cooperationId) {
        const res = await fetch({
            url: `/api/v2/charges/${cooperationId}/print/cooperation-order`,
        });
        return res?.data || {};
    },

    /**
     * @desc 获取合作订单todo信息
     * <AUTHOR> Yang
     * @date 2024-08-13 15:20:58
    */
    async fetchCooperationOrderTodo() {
        const res = await fetch({
            url: '/api/v2/charges/cooperation-order/todo',
        });
        return res?.data || {};
    },

    /**
     * @desc 取消挂单
     * <AUTHOR> Yang
     * @date 2024-08-13 15:20:58
    */
    async cancelDraft(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/draft/cancel`,
            method: 'put',
        });
        return res?.data || {};
    },

    /**
     * @desc 保存追溯码
     * <AUTHOR> Yang
     * @date 2024-08-26 14:53:06
     * @param {string} id
     * @param {object} data
     * @param {array} data.list
     */
    async saveTraceCode(id, data) {
        const res = await fetch({
            url: `/api/v2/charges/${id}/save-traceable-code`,
            method: 'put',
            data,
        });

        return res.data;
    },

    /**
     * @desc 已关闭收费单重新收费
     * @param {string} id
     */
    async openChargeSheet(id) {
        const res = await fetch({
            url: `/api/v2/charges/${id}/open`,
            method: 'put',
        });

        return res.data;
    },

    /**
     * @desc 获取开单来源
     */
    async fetchSourceTypeList() {
        const res = await fetch({
            url: '/api/v2/charges/filter-type',
            disabledCancel: true,
            method: 'get',
        });

        return res.data;
    },

    /**
     * @desc 完善零售单信息
     * <AUTHOR> Yang
     * @date 2024-11-27 18:42:06
    */
    async improveChargeSheet(chargeSheetId, data) {
        const res = await fetch({
            url: `/api/v2/charges/direct/improve/${chargeSheetId}`,
            method: 'put',
            data,
        });
        return res?.data || {};
    },

    /**
     * @desc 收费单根据chargePayTransactionId取消支付/退费
     */
    async cancelPayByTransaction(chargePayTransactionId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargePayTransactionId}/cancel-by-charge-pay-transaction-id`,
            method: 'put',
        });
        return res?.data || {};
    },

    /**
     * @desc 续期收费单
     */
    async lockRenewByTransaction(chargePayTransactionId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargePayTransactionId}/lock-renew`,
            method: 'put',
        });
        return res?.data || {};
    },

    /**
     * @desc 查询社保收费异常详情
     */
    async fetchChargeAbnormalList(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/charge-pay-transaction/list-error-pay-transactions-by-charge-sheet-id/${chargeSheetId}`,
            method: 'get',
        });
        return res?.data || {};
    },

    /**
     * @desc 退费审核-员工业务场景生成二维码
     */
    async getBusAuthenticationQrCode(params) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/bus-authentication/qrCode',
            method: 'get',
            params,
            paramsSerializer(val) {
                return Qs.stringify(val);
            },
        });
        return res?.data || {};
    },

    /**
     * @desc 退费审核-员工业务场景查询生成二维码的状态
     */
    async getBusAuthenticationQrCodeStatus(params) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/bus-authentication/qrCode-status',
            method: 'get',
            params,
            paramsSerializer(val) {
                return Qs.stringify(val);
            },
        });
        return res?.data || {};
    },

    /**
     * @desc 退费审核-员工业务场景修改生成二维码的状态
     */
    async updateBusAuthenticationQrCodeStatus(data) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/bus-authentication/qrCode-status',
            method: 'put',
            data,
        });
        return res?.data || {};
    },

    /**
     * @desc 退费审核-员工业务场景身份验证发送短信验证码
     */
    async sendSmsVerifyCode(data) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/bus-authentication/sendSmsVerifyCode',
            method: 'post',
            data,
        });
        return res?.data || {};
    },
    /**
     * @desc 员工业务场景身份验证-获取accessToken
     */
    async verifyEmployeeBusAuthentication(data) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/bus-authentication',
            method: 'post',
            data,
            customErrorTips: true,
        });
        return res?.data || {};
    },

    /**
     * @desc 整单退费查询是否可退检查
     * @params chargeSheetId 收费单id
     * @params dispensingQueryCheck
     *          0:不查询关于发药/执行等详情数据
     *          1:会查询发药执行等详情数据,但是不会去做校验
     *          2:会查询发药执行等详情数据，也会做校验
     */
    async verifyRefundChargeOrder(chargeSheetId, dispensingQueryCheck) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/refund-detail`,
            method: 'get',
            params: {
                dispensingQueryCheck,
            },
        });
        return res?.data || {};
    },


    /**
     * @desc 批量提取
     * @param {Object} data
     * @param {Array} data.chargeSheetIds
     */
    async batchExtract(data) {
        const res = await fetch({
            url: '/api/v2/charges/draft/batch-extract',
            method: 'post',
            data,
        });
        return res?.data || {};
    },


    /**
     * @desc 获取收费单复制视图数据，用于重新收费
     * <AUTHOR> Yang
     * @date 2025-04-16
     * @param {string} chargeSheetId 收费单id
     */
    async copyChargeSheet(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/copy-view`,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc: 获取收费单的打印按钮数据
     * @author: ff
     * @time: 2025/4/24
     */
    async fetchChargePrintButtonData(chargeSheetId, disabledCancel = false) {
        const res = await fetch({
            url: `/api/v2/charges/${chargeSheetId}/print-button-data`,
            disabledCancel,
        });
        return res.data;
    },

    // 获取可议价的商品类型
    async getCanAdjustSetting(data) {
        const res = await fetch({
            url: '/api/v2/charges/product-type-match/business-keys',
            method: 'post',
            data,
        });
        return res?.data || {};
    },

    /**
     * @desc: 对比算费结果
     * @author: ff
     * @time: 2025/5/20
     */
    async compareChargeResult(data) {
        const res = await fetch({
            url: '/api/v2/charges/calculate-result-compare',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 更新长护结算单价
     */
    async updateHospitalItemPrice(data) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/update-item-price/${data.hospitalSheetId}`,
            method: 'post',
            data,
        });
        return res?.data || {};
    },

    /**
     * @desc 查询长护结算单详情
     */
    async getHospitalSimple(hospitalSheetId) {
        const res = await fetch({
            url: `/api/v2/charges/hospital/${hospitalSheetId}/simple`,
            method: 'get',
        });
        return res?.data || {};
    },
};

export default Charge;
