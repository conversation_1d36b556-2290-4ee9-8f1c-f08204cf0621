import fetch from 'utils/fetch';
import Qs from 'qs';

/**
 * 收费相关
 * @type {{fetch: ((p1?:*)), operate: ((p1?:*)), add: ((p1?:*))}}
 */
const Dispensary = {
    /**
     * @desc  重构 药房 quickList
     * <AUTHOR>
     * @date 2019/03/31 12:14:37
     */
    async fetchQuickList(params) {
        const res = await fetch({
            url: '/api/v2/dispensings',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },

    async updateVendorInfo(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/vendor-info`,
            method: 'put',
            data,
        });
        return res.data;
    },

    async fetchlistByCondition(data) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/list-by-condition',
            method: 'post',
            data,
            disabledCancel: true,
        });
        return res.data;
    },

    /**
     * @desc 重构  获取发药单详情
     * <AUTHOR>
     * @date 2019/03/31 13:54:34
     */
    async fetchDetail(id, disabledCancel = false) {
        // eslint-disable-next-line no-mixed-spaces-and-tabs
    	const res = await fetch({
            url: `/api/v2/dispensings/detail/${id}/`,
            method: 'get',
            disabledCancel,
        });
        return res.data;
    },

    /**
	 * @desc 发药
	 * <AUTHOR>
	 * @date 2019/03/31 17:05:44
	 */
    async dispense(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/dispense`,
            method: 'put',
            data,
        });
        return res.data;
    },

    async undispensePreCheck(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/undispense/pre-check`,
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
	 * @desc 退药
	 * <AUTHOR>
	 * @date 2019/03/31 19:28:56
	 */
    async undispense(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/undispense`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * 恢复商品档案
     * @param data
     * @returns {Promise<*>}
     */
    async recoverGoodsArchive(data, customErrorTips = false) {
        const res = await fetch({
            url: '/api/v3/goods/recover-goods-archive',
            method: 'put',
            data,
            customErrorTips,
        });
        return res.data;
    },

    async fetchDispensingPrint(id) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/print`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data;
    },

    /**
	 * @desc 更新发药快递公司信息
	 * <AUTHOR>
	 * @date 2020/02/12 23:40:33
	 */
    async updateDispensingsDelivery(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/delivery`,
            method: 'put',
            data,
        });
        return res.data;

    },

    /**
	 * @desc 关闭发药单
	 * <AUTHOR> Yang
	 * @date 2020-10-22 14:55:23
	 */
    async closeDispensing(id) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/close`,
            method: 'put',
        });
        return res.data;
    },

    /**
	 * @desc 重新打开发药单
	 * <AUTHOR> Yang
	 * @date 2020-10-22 14:55:23
	 */
    async reopenDispensing(id) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/reopen`,
            method: 'put',
        });
        return res.data;
    },

    /**
	 * @desc 根据时间范围获取药房总计
	 * <AUTHOR> Yang
	 * @date 2020-10-23 17:40:54
	 */
    async getDispensingSummary(params) {
        const res = await fetch({
            url: '/api/v2/dispensings/summary',
            params,
        });
        return res.data;

    },


    /**
     * @desc 获取药房列表
     */
    async fetchDispensaryList() {
        const res = await fetch({
            url: '/api/v3/goods/pharmacy',
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 获取不同药品类型对应的门店列表
     * <AUTHOR>
     * @date 2021-06-15 10:43:43
     * @params 目前有三种药房类型，对应 PharmacyTypeEnum 的取值
     * @return
     */
    async fetchPharmacyTypeClinicList(pharmacyType) {
        const res = await fetch({
            url: `/api/v3/goods/pharmacy/find-clinics-by-pharmacy-type?pharmacyType=${pharmacyType}`,
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 查询虚拟药房是否开通
     * <AUTHOR>
     * @date 2021-06-22 14:52:38
     */
    async fetchVirtualPharmacyConfig() {
        const res = await fetch({
            url: '/api/v3/goods/virtual-pharmacy/config',
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 药事服务，是否完成审核
     * <AUTHOR>
     */
    async audit(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/audit`,
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 撤销审核
     */
    async cancelAudit(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/cancel-audit`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 药事服务，是否完成调配
     * <AUTHOR>
     */
    async compound(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/compound`,
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 撤销调配
     */
    async cancelCompound(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/cancel-compound`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 药事服务，是否完成加工
     * <AUTHOR>
     */
    async process(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/processed`,
            method: 'put',
            data,
        });
        return res.data;
    },

    async loadRecord(id) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/operation/record`,
            method: 'get',
        });

        return res.data;
    },

    async complete(id) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/complete/delivery`,
            method: 'post',
        });

        return res.data;
    },

    /**
     * @desc 更新发药打印状态
     * <AUTHOR>
     * @date 2021-07-20 15:19:13
     */
    async updateDispensingPrinted(data) {
        const res = await fetch({
            url: '/api/v2/dispensings/printed/number',
            method: 'put',
            data,
        });

        return res.data;
    },

    /**
     * @desc 根据chargeSheetId获取发药状态
     * <AUTHOR>
     * @date 2023-04-04 15:48:03
     * @param {string} chargeSheetId
     */
    async getDispenseStatus(chargeSheetId) {
        const res = await fetch({
            url: `/api/v2/dispensings/charge-sheet/${chargeSheetId}/dispense-status`,
            method: 'get',
        });

        return res.data;
    },

    async checkCanDispense(id) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/re-dispense/pre-check`,
            method: 'post',
        });

        return res.data;
    },

    async calcDispenseInfo(id, forms = []) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/re-dispense/calculate`,
            method: 'post',
            data: {
                dispensingForms: forms,
            },
        });

        return res.data;
    },

    async reDispense(id, forms) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/re-dispense`,
            method: 'post',
            data: {
                dispensingForms: forms,
                id,
            },
        });

        return res.data;
    },

    /**
     * @typedef {Object} AutoDispensingParams
     * @property {string} dispenseTime 发药时间
     * @property {string} keyword 关键字
     * @property {number} pharmacyNo 库房号
     * @property {0 | 1} source 来源
     * @property {number} wardAreaId 病区号
     */
    /**
     * 获取自动发药列表
     * @param {AutoDispensingParams} data
     * @return {Promise<*[]>}
     */
    async fetchAutoDispensingSheet(data) {
        const rsp = await fetch({
            url: '/api/v2/dispensings/order/list-auto-dispensing',
            method: 'GET',
            params: data,
            paramsSerializer(v) {
                return Qs.stringify(v);
            },
        });
        return rsp.data?.data?.rows || [];
    },

    /**
     * 自动发药清单 - 退药
     * @param data
     * @return {Promise<*>}
     */
    async unDispenseAutoSheet(data) {
        const res = await fetch({
            url: '/api/v2/dispensings/order/auto-dispensing-sheet/undispense',
            method: 'PUT',
            data,
        });
        return res.data;
    },

    /**
     * 自动发药清单 - 发药
     * @param data
     * @return {Promise<*>}
     */
    async dispenseAutoSheet(data) {
        const res = await fetch({
            url: '/api/v2/dispensings/order/auto-dispensing-sheet/dispense',
            method: 'PUT',
            data,
        });
        return res.data;
    },

    async fetchAutoDispensingCount(params) {
        const res = await fetch({
            url: '/api/v2/dispensings/order/auto-dispensing-fail-count',
            method: 'GET',
            params,
            paramsSerializer (v) {
                return Qs.stringify(v);
            },
        });
        return res.data;
    },

    async fetchAutoDispensingDailyCount(params) {
        const res = await fetch({
            url: '/api/v2/dispensings/order/auto-dispensing-fail-daily-count',
            method: 'GET',
            params,
            paramsSerializer (v) {
                return Qs.stringify(v);
            },
        });
        return res.data;
    },

    /**
     * @desc 保存追溯码
     * <AUTHOR> Yang
     * @date 2024-08-26 14:53:06
     * @param {string} id
     * @param {object} data
     * @param {array} data.list
    */
    async saveTraceCode(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/${id}/save-traceable-code`,
            method: 'post',
            data,
        });

        return res.data;
    },

    /**
     * 住院 - 保存追溯码
     * @param {string} id
     * @param {Object} data
     * @param {*[]} data.list
     */
    async saveHospitalTraceCode(id, data) {
        const res = await fetch({
            url: `/api/v2/dispensings/order/${id}/save-traceable-code`,
            method: 'post',
            data,
        });

        return res.data;
    },

    /**
     * 拉取发药socket事件的分组id
     * @param params
     * @returns {Promise<{ business: string;groupId: string }>}
     */
    async fetchEventGroup(params) {
        const res = await fetch.post('/api/v2/dispensings/event-group/list', params);
        return res?.data;
    },

    /**
     * 获取待申请发药每日数量
     * @param {Object} data
     * @param {string[]} data.patientOrderIds 患者id列表
     * @param {number} data.wardAreaId 病区id
     * @return {Promise<
     *  {
     *      keyword: string,
     *      limit: number,
     *      offset: number,
     *      rows: { count: number, date: string }[],
     *      total: number
     *  }
     * >}
     */
    async fetchWaitingApplyDailyCount(data) {
        const res = await fetch({
            url: '/api/v2/dispensings/order/waiting-apply-daily-count',
            method: 'POST',
            data,
        });
        return res.data;
    },
};

export default Dispensary;
