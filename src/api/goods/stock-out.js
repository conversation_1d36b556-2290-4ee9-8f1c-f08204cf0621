// 出库

import fetch from 'utils/fetch';
import qs from 'qs';
import {
    exportFileByAxios,
} from 'utils/excel.js';
import { fetchDecode } from './help';
import Qs from 'qs';

export default {
    /**
     * @param begDate
     * @param endDate
     * @param offset
     * @param limit
     * @param clinicId 门店筛选
     */
    async list({
        type,
        begDate,
        endDate,
        offset,
        limit,
        clinicId,
        withGoodsId,
        status,
        settlementStatus,
        dateField,
        supplierId,
        pickEmployeeId,
        pharmacyType,
        pharmacyNo,
    }) {
        let ret = await fetch.get('/api/v3/goods/stocks/out/orders', {
            params: {
                type,
                begDate,
                endDate,
                offset,
                limit,
                clinicId,
                withGoodsId,
                status,
                settlementStatus,
                dateField,
                supplierId,
                pickEmployeeId,
                pharmacyType,
                pharmacyNo,
            },
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'comma' });
            },
        });
        ret = fetchDecode(ret);
        ret.rows.forEach((it) => {
            it.createdDate = new Date(it.createdDate);
        });
        return ret;
    },

    // 出库单列表导出
    async exportList({
        begDate, endDate, clinicId, status, dateField, withGoodsId, type, supplierId, pickEmployeeId,
        pharmacyType,
        pharmacyNo,
    }) {
        return exportFileByAxios({
            url: '/api/v3/goods/stocks/out/orders/export',
            params: {
                begDate,
                endDate,
                clinicId,
                status,
                dateField,
                withGoodsId,
                type,
                supplierId,
                pickEmployeeId,
                pharmacyType,
                pharmacyNo,
            },
            paramsSerializer(p) {
                return qs.stringify(p);
            },
        });
    },

    // 获取出库详单
    async getById(id, params) {
        const ret = await fetch({
            url: `/api/v3/goods/stocks/out/orders/${id}`,
            method: 'get',
            params,
        });
        return fetchDecode(ret);
    },

    // 出库单详单导出
    async exportById(id) {
        const url = `/api/v3/goods/stocks/out/orders/${id}/export`;
        return exportFileByAxios({
            url,
        });
    },

    /**
     * 创建出库单
     *
     * @param   {Object}  data
     * {
     *  type, //  出库类型：
     *     1: '领料出库',
     *     2: '报损出库',
     *     3: '退货出库',
     *  fromOrganId, // 出库门店；总店给子店出库时必填。
     *  toOrganId, // 出库到的科室（领用科室）
     *  toUserId,  // 出库到的人（领用人）
     *  comment, // 备注
     *  list,    // 详单，Array,结构如下
     * }
     * list:
     * [{
     *      goodsId,        //商品id（药品id）
     *      stockId,        //库存id
     *      pieceCount,     //小单位数量
     *      packageCount,   //大单位数量
     * }]
     *
     * @return  {[type]}
     */
    async createOrder(data) {
        const ret = await fetch.post('/api/v3/goods/stocks/out/orders', data);
        return fetchDecode(ret);
    },

    /**
     * 子店确认出库单
     *
     * @param   {String}  orderId  出库单id
     * @param   {Object}  data
     * {
     *      comment,
     *      lastModifiedDate,
     * }
     *
     * @return
     */
    async confirmOrder(orderId, data) {
        const ret = await fetch.put(`/api/v3/goods/stocks/out/orders/${orderId}/confirm`, data);
        return fetchDecode(ret);
    },
    /**
     * @desc 子店拒绝出库单
     * <AUTHOR>
     * @date 2019/09/17 10:16:18
     * @params
     * @return
     */
    async rejectOrder(id, data) {
        const ret = await fetch.put(`/api/v3/goods/stocks/out/orders/${id}/reject`, data);
        return fetchDecode(ret);
    },

    /**
     * 创建者修改出库单（确认后不能修改）
     *
     * @param   {String}  orderId  出库单id
     * @param   {Object}  data
     * {
     *      comment,
     *      list,
     *      lastModifiedDate,
     * }
     * @return  {[type]}
     */
    async updateOrder(orderId, data) {
        const ret = await fetch.put(`/api/v3/goods/stocks/out/orders/${orderId}`, data);
        return fetchDecode(ret);
    },

    /**
     * @desc 审核出库单
     * <AUTHOR>
     * @date 2019/10/17 11:33:52
     * @params
     * @return
     */
    async review(orderId, data) {
        const ret = await fetch.put(`/api/v3/goods/stocks/out/orders/${orderId}/review`, data);
        return fetchDecode(ret);
    },
    /**
     * @desc 出库单撤回  id: orderId
     * <AUTHOR>
     * @date 2019/12/12
     */
    async revokeOrder(id) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/out/orders/${id}/revoke`,
            method: 'put',
        });
        return res.data;
    },
    /**
     * @desc 退货出库，获取入库单列表
     */
    async fetchInOrder({
        clinicId,
        offset,
        limit,
        begDate,
        endDate,
        goodsId,
        listCount,
        supplierId,
        withReturnLeft,
        pharmacyNo,
        chineseMedicine = false,
    }) {
        const params = {
            clinicId,
            offset,
            limit,
            begDate,
            endDate,
            goodsId ,
            listCount,
            supplierId,
            withReturnLeft,
            pharmacyNo,
            chineseMedicine,
        };
        const res = await fetch({
            url: '/api/v3/goods/stocks/in/orders-for-stock-out',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * @desc 退货出库，获取初始化入库单列表数据，只有一条汇总的数据
     */
    async fetchInOrderWithImport({
        clinicId,
        offset,
        limit,
        begDate,
        endDate,
        goodsId,
        listCount,
        supplierId,
        withReturnLeft,
        pharmacyNo,
    }) {
        const params = {
            clinicId,
            offset,
            limit,
            begDate,
            endDate,
            goodsId ,
            listCount,
            supplierId,
            withReturnLeft,
            pharmacyNo,
        };
        const res = await fetch({
            url: '/api/v3/goods/stocks/in/orders-for-stock-out-of-import',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * @desc 搜索初始化入库单内的药品数据
     */
    async searchGoodsInImportOrder({
        keyword,
        offset,
        limit,
    }) {
        const params = {
            keyword,
            offset,
            limit,
        };
        const res = await fetch({
            url: '/api/v3/goods/stocks/in/search-import-goods',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * @desc 初始化入库单退货出库
     */
    async createImportGoodsOrder(data) {
        const res = await fetch({
            url: '/api/v3/goods/stocks/in/for-import/orders',
            method: 'post',
            data,
        });
        return res.data;
    },

    // 查询入库单草稿列表
    async getGoodsOutDraftList(params) {
        const res = await fetch({
            url: '/api/v3/goods/stocks/out/orders/draft',
            method: 'get',
            params,
            paramsSerializer (params) {
                return qs.stringify(params);
            },
        });
        return res && res.data;
    },

    // 新增入库单草稿
    async createGoodsOutDraft(data) {
        const res = await fetch({
            url: '/api/v3/goods/stocks/out/orders/draft',
            method: 'post',
            data,
        });
        return res && res.data;
    },

    /**
     * 获取入库单草稿详情
     * @param draftId 草稿 id
     */
    async getGoodsOutDraftDetail(draftId) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/out/orders/draft/${draftId}`,
            method: 'get',
        });
        return res && res.data;
    },

    /**
     * 修改入库单草稿
     * @param draftId 草稿 id
     * @param data 草稿数据
     */
    async updateGoodsOutDraft(draftId, data) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/out/orders/draft/${draftId}`,
            method: 'put',
            data,
        });
        return res && res.data;
    },

    /**
     * 删除入库单草稿
     * @param draftId 草稿 id
     */
    async deleteGoodsOutDraft(draftId) {
        const res = await fetch({
            url: `/api/v3/goods/stocks/out/orders/draft/${draftId}`,
            method: 'delete',
        });
        return res && res.data;
    },

    /**
     * 获取报损出库原因模板
     * @param type 类型 1:报损出库
     */
    async getStockOutReasonTemplate(type) {
        const res = await fetch({
            url: '/api/v3/goods/stock-out-reason/template',
            params: {
                type,
            },
            method: 'get',
        });
        return res && res.data;
    },

    /**
     * 报损出库原因模板
     * @param data
     */
    async putStockOutReasonTemplate(data) {
        const res = await fetch({
            url: '/api/v3/goods/stock-out-reason/template',
            data,
            method: 'put',
        });
        return res && res.data;
    },
};
