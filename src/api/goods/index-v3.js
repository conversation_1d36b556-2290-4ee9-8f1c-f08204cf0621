import Qs from 'qs';
import { parseDiagnosis } from 'src/filters/index.js';
import {
    exportFileByAxios,
} from 'utils/excel';
import fetch from 'utils/fetch';

export default {
    /**
     * @desc 查询goods接口
     * <AUTHOR>
     * @date 2020-11-12 14:16:37
     */
    async searchGoods(data) {
        if (data.diagnosis) {
            data.diagnosis = parseDiagnosis(data.diagnosis);
        }
        const res = await fetch({
            url: '/api/v3/goods/search',
            method: 'post',
            data,
        });
        return res.data;
    },
    async searchRecommendByPurchase(params) {
        const res = await fetch({
            url: '/api/v3/goods/stock-goods/search/recommend',
            method: 'get',
            params,
        });
        return res.data;
    },
    // 通用的goods查询, 用于统计模块
    async searchCommonGoods(data) {
        const res = await fetch({
            url: '/api/v3/goods/search/common',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 获取贯标统计的数据
     * <AUTHOR>
     * @date 2021-06-30 16:06:52
     * @params clinicId
     *         stockGoods 1 库存商品 0 非库存商品
     * @return
     */
    async getNationalCodeStatusSummary(params) {
        const paramsStr = Qs.stringify(params);
        const res = await fetch({
            url: `/api/v3/goods/shebao/impl-national-status-summary?${paramsStr}`,
            method: 'get',
        });
        return res.data;
    },
    async fecthPriceAdjustment(params) {
        const res = await fetch({
            url: '/api/v3/goods/prices/orders',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    },
    async exportPriceAdjustment(params) {
        return exportFileByAxios({
            url: '/api/v3/goods/prices/orders/export',
            params,
            paramsSerializer(p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
    },
    // 批量调价拉取数据的接口
    async getGoodsList(params) {
        const res = await fetch({
            url: '/api/v3/goods/prices/goods-list',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'comma' });
            },
        });
        return res.data;
    },
    // 药店开始调价列表数据
    async fetchPriceAdjustmentGoodsList(data) {
        const res = await fetch({
            url: '/api/v3/goods/goods-list/stock-goods/for-modify-price',
            method: 'post',
            data,
        });
        return res.data;
    },
    // 新建调价单
    async createPriceAdjustmentOrder(data) {
        const res = await fetch({
            url: '/api/v3/goods/prices/orders',
            method: 'post',
            data,
        });
        return res.data;
    },
    // 修改调价单
    async modifyPriceAdjustmentOrder(data, orderId) {
        const res = await fetch({
            url: `/api/v3/goods/prices/orders/${orderId}`,
            method: 'put',
            data,
        });
        return res.data;
    },
    // 删除调价单
    async deletePriceAdjustmentOrder(orderId) {
        const res = await fetch({
            url: `/api/v3/goods/prices/orders/${orderId}`,
            method: 'delete',
        });
        return res.data;
    },
    // 算费接口
    async pricesCalculate(data) {
        const res = await fetch({
            url: '/api/v3/goods/prices/calculate',
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @typedef {object} CalculatePriceItem
     * @property {string} goodsId
     * @property {number} packageCostPrice
     * @property {number} packagePrice
     * @property {number} pieceNum
     * @property {number} piecePrice
     * @property {number} roundingMode
     * @property {number} scaleType
     * @property {number} shebaoLimitPackagePrice
     * @property {number} avgPackageCostPrice
     * @property {number} calPackageType
     * @property {number} calPackageUpPercent
     * @property {number} calPieceType
     * @property {number} calPieceUpPercent
     */
    /**
     * @typedef {object} CalculatePriceResultItem
     * @property {string} goodsId
     * @property {number} packageCostPrice
     * @property {number} packagePrice
     * @property {number} pieceNum
     * @property {number} piecePrice
     * @property {number} roundingMode
     * @property {number} scaleType
     * @property {number} shebaoLimitPackagePrice
     * @property {number} avgPackageCostPrice
     * @property {number} calPackageType
     * @property {number} calPackageUpPercent
     * @property {number} calPieceType
     * @property {number} calPieceUpPercent
     * @property {number} calResult 0: 无变化 1: 有变化 -1: 失败
     * @property {number} newPackagePrice
     * @property {number} newPiecePrice
     * @property {number} newProfitRat
     */
    /**
     * @param {object} data - 请求参数
     * @param {CalculatePriceItem[]} data.list - 药品列表
     *
     * @returns {Promise<{list: CalculatePriceResultItem[]}>}
     */
    async calculatePrice(data) {
        const res = await fetch({
            url: '/api/v3/goods/prices/calculate/simple',
            method: 'put',
            data,
        });
        return res.data?.data;
    },
    // 获取调价单详情
    async fetchPriceAdjustmentOrderDetail(params) {
        const { orderId } = params;
        const res = await fetch({
            url: `/api/v3/goods/prices/orders/${orderId}`,
            method: 'get',
            params,
        });
        return res.data;
    },
    // 获取历史详情里面的记录
    async getHistoryInfo(params) {
        params.forPurchase = 1;
        const { orderId } = params;
        const res = await fetch({
            url: `/api/v3/goods/prices/orders/${orderId}`,
            method: 'get',
            params,
        });
        return res.data;
    },
    async updatePriceAdjustment(data) {
        const res = await fetch({
            url: '/api/v3/goods/prices',
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 获取调价药品详情
     * @param orderItemId
     * @return {Promise<*>}
     */
    async fetchPriceItemInfo(orderItemId) {
        const res = await fetch({
            url: `/api/v3/goods/prices/tips-items/${orderItemId}`,
            method: 'get',
        });
        return res.data;
    },
    async enableGoodsBatchStatus(goodsId, data) {
        const res = await fetch({
            url: `/api/v3/goods/${goodsId}/stocks/enable`,
            method: 'put',
            data,
        });
        return res.data;
    },
    // 查询某个药品库存详情
    async getGoodsStockDetail(params) {
        const res = await fetch({
            url: `/api/v3/goods/${params.goodsId}/stocks/detail`,
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'comma' });
            },
        });
        return res.data;
    },
    // 查询关联项目
    async fetchAssociationUsages(params) {
        const res = await fetch({
            url: '/api/v3/goods/association/usages',
            method: 'get',
            params,
        });
        return res.data;
    },
    // 新增规则配置
    async postInfusionConfigNew(data) {
        const res = await fetch({
            url: '/api/v3/goods/association/usages/config',
            method: 'post',
            data,
        });
        return res?.data || {};
    },
    // 获取规则配置
    async fetchInfusionConfigById(id) {
        const res = await fetch({
            url: `/api/v3/goods/association/usages/config/${id}`,
            method: 'get',
        });
        return res?.data || {};
    },
    // 更新规则配置
    async postInfusionConfigById(data) {
        const res = await fetch({
            url: `/api/v3/goods/association/usages/config/${data.id}`,
            method: 'post',
            data,
        });
        return res?.data || {};
    },
    // 删除规则配置
    async deleteInfusionConfigById(id) {
        const res = await fetch({
            url: `/api/v3/goods/association/usages/config/${id}`,
            method: 'delete',
        });
        return res?.data || {};
    },
    // 获取门诊输液关联项目配置
    async fetchInfusionTypeRelatedConfig(usageType, sourceType) {
        const res = await fetch({
            url: `/api/v3/goods/association/usages/${usageType}`,
            method: 'get',
            params: { sourceType },
        });
        return res.data;
    },
    // 更新门诊输液关联项目配置
    async updateInfusionRelatedConfig(data) {
        const res = await fetch({
            url: '/api/v3/goods/association/usages',
            method: 'post',
            data,
        });
        return res && res.data;
    },

    /**
     * @desc  重构 获取药品进价
     * <AUTHOR>
     * @date 2019/04/08 22:38:10
     */
    async fetchGoodsCostPrice(id, clinicId, pharmacyNo) {
        const res = await fetch({
            url: `/api/v3/goods/${id}/stocks/cost-range`,
            method: 'get',
            params: {
                clinicId,
                pharmacyNo,
            },
        });

        return res.data;
    },

    /**
     * @desc 拉取一个处方里面的中药的 goods信息，后台做匹配规则
     * <AUTHOR>
     * @date 2022-02-14 09:54:09
     * @params data: {
     *     clinicId // 是否拉取指定门店，可以不指定，不指定为当前登录门店Id
     *     cMSpec: "string", // 规格
     *     list: [
     *       {
     *         "goodsId": "string",
     *         "keyId": 0,
     *         "manufacturer": "string",
     *         "medicineCadn": "string",
     *         "packageCount": 0,
     *         "pieceCount": 0,
     *         "pieceUnit": "string"
     *       }
     *     ],
     *     pharmacyNo: 0 // 药房类型，可缺省，默认0
     * }
     */
    async findChineseMedicines(data) {
        const { data: res } = await fetch({
            url: '/api/v3/goods/find-chinese-medicines',
            method: 'post',
            data,
        });
        return res.data || {};
    },

    /**
     * @desc 处方换药，后台匹配
     * <AUTHOR>
     * @date 2024/08/07 14:46:13
     * @params data: {
     *   "chainId": "string",
     *   "clinicId": "string",
     *   "employeeId": "string",
     *   "disable": 0,
     *   "withDeleted": 0,
     *   "reqItems": [
     *     {
     *       "typeIdList": [0],
     *       "pharmacyType": 0,
     *       "pharmacyNo": 0,
     *       "sceneType": 0,
     *       "departmentId": "string",
     *       "wardAreaId": "string",
     *       "queryItems": [
     *         {
     *           "keyId": "string",
     *           "goodsId": "string",
     *           "medicineCadn": "string",
     *           "name": "string",
     *           "pieceUnit": "string",
     *           "packageUnit": "string",
     *           "pieceNum": 0,
     *           "componentContentNum": 0,
     *           "componentContentUnit": "string",
     *           "medicineDosageNum": 0,
     *           "medicineDosageUnit": "string",
     *           "manufacturer": "string",
     *           "barCode": "string",
     *           "shebaoCode": "string",
     *           "extendSpec": "string",
     *           "packageCount": 0,
     *           "pieceCount": 0
     *         }
     *       ]
     *     }
     *   ]
     * }
     */
    async findMedicines(data) {
        const { data: res } = await fetch({
            url: '/api/v3/goods/find-medicines-by-name',
            method: 'post',
            data,
        });
        return res.data || {};
    },

    async findMedicinesV2(data) {
        const { data: res } = await fetch({
            url: '/api/v3/goods/find-medicines-by-name-v2',
            method: 'post',
            data,
        });
        return res.data || {};
    },

    /**
     * @desc 获取费用类型列表
     * @date 2023/5/11 - 10:10:21
     * <AUTHOR>
     *
     * @async
     * @param {*} params:{
     *  innerFlag: 内置|自定义,
     *  scopeId：范围id, null - 全部，0 - 药品，10 - 检查，20 - 加工快递，30 - 挂号
     * }
     * @returns {unknown}
     */
    async getFeeTypeList(params) {
        const { data: res } = await fetch({
            url: '/api/v3/goods/fee-types',
            method: 'get',
            params,
        });
        return res.data || {};
    },

    /**
     * @desc 创建费用类型
     * @date 2023/5/11 - 10:11:49
     * <AUTHOR>
     *
     * @async
     * @param {*} data
     * @returns {unknown}
     */
    async createFeeType(data) {
        const { data: res } = await fetch({
            url: '/api/v3/goods/fee-types',
            method: 'post',
            data,
        });
        return res.data || {};
    },

    /**
     * @desc 更新费用类型
     * @date 2023/5/11 - 10:13:33
     * <AUTHOR>
     *
     * @async
     * @param {*} feeTypeId
     * @param {*} data
     * @returns {unknown}
     */
    async updateFeeType(feeTypeId, data) {
        const { data: res } = await fetch({
            url: `/api/v3/goods/fee-types/${feeTypeId}`,
            method: 'put',
            data,
        });
        return res.data || {};
    },

    /**
     * @desc 删除费用类型
     * @date 2023/5/11 - 10:15:00
     * <AUTHOR>
     *
     * @async
     * @param {*} feeTypeId
     * @returns {unknown}
     */
    async deleteFeeType(feeTypeId) {
        const { data: res } = await fetch({
            url: `/api/v3/goods/fee-types/${feeTypeId}`,
            method: 'delete',
        });
        return res.data || {};
    },

    /**
     * @desc 费用类型批量增删改
     * @param {Array}
     * @return {Object}
     */
    async batchFeeType(data) {
        const res = await fetch({
            url: '/api/v3/goods/fee-types/batch',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * 复制goods为指定goods类型
     * @param {*} data
     * @returns
     * @file https://dev.abczs.cn/swagger-ui/?urls.primaryName=sc-goods#/goods-info-controller/copyGoodsUsingPOST
     */
    async copyGoods(data) {
        const res = await fetch({
            url: '/api/v3/goods/copy',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 获取费目列表
     * @date 2023/10/20 - 10:22:04
     * <AUTHOR>
     *
     * @async
     * @returns {unknown}
     */
    async getFeeCategoryList() {
        const { data: res } = await fetch({
            url: '/api/v3/goods/sys/fee-categories',
            method: 'get',
        });
        return res.data || {};
    },

    async getFeeTypeRelateCategoryList() {
        const res = await fetch({
            url: '/api/v3/goods/fee-type-associate-category-list',
            method: 'get',
        });
        return res.data || {};
    },

    // 获取采购订单列表
    async getPurchaseOrderList(params) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/orders',
            method: 'get',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { arrayFormat: 'comma' });
            },
        });
        return res.data;
    },

    // 获取采购订单详情
    async getPurchaseOrderDetail(id) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/orders/${id}`,
            method: 'get',
        });
        return res.data;
    },

    // 创建采购订单列表
    async createPurchaseOrder(postData) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/orders',
            method: 'post',
            data: postData,
        });
        return res.data;
    },
    // 修改采购订单
    async updatePurchaseOrder(orderId, postData) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/orders/${orderId}`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    // 获取商品所有采购记录
    async getPurchaseOrderByGoodsId(params) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/orders/by-goods-id',
            method: 'get',
            params,
        });
        return res.data;
    },

    // 创建要货单
    async createRequireOrder(postData) {
        const res = await fetch({
            url: '/api/v3/goods/claim/orders',
            method: 'post',
            data: postData,
        });
        return res.data;
    },

    // 修改要货订单
    async updateClaimOrder(orderId, postData) {
        const res = await fetch({
            url: `/api/v3/goods/claim/orders/${orderId}`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },
    // 检查goods与供应商映射关系
    async checkValidToSupplierGoods(postData) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/orders/check-valid-to-supplier-goods',
            method: 'post',
            data: postData,
        });
        return res.data;
    },
    // 创建采购订单列表-拆单
    async preCreatePurchaseOrder(postData) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/orders/pre-create',
            method: 'post',
            data: postData,
        });
        return res.data;
    },

    // 收货单-可关联采购单列表
    async associablePurchaseOrder(params) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/orders/receive',
            method: 'get',
            params,
        });
        return res.data;
    },
    // 创建采购订单列表-发送到供应商
    async createPurchaseOrderSendToSupplier(postData) {
        const res = await fetch({
            url: '/api/v3/goods/purchase/orders/send-to-supplier',
            method: 'post',
            data: postData,
        });
        return res.data;
    },

    //要货同意
    async resolvePurchaseOrder(id, postData) {
        const res = await fetch({
            url: `/api/v3/goods/claim/orders/${id}/confirm`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    //要货驳回
    async rejectPurchaseOrder(id, postData) {
        const res = await fetch({
            url: `/api/v3/goods/claim/orders/${id}/reject`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    //要货终止
    async terminatePurchaseOrder(id, postData) {
        const res = await fetch({
            url: `/api/v3/goods/claim/orders/${id}/terminate`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    //要货-撤回
    async revokePurchaseOrder(id, postData) {
        const res = await fetch({
            url: `/api/v3/goods/claim/orders/${id}/revoke`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    // 收货单列表
    async getPurchaseReceiveOrderList(params) {
        const res = await fetch({
            url: '/api/v3/goods/receive/orders',
            method: 'get',
            params,
        });
        return res.data;
    },

    // 配货单列表
    async getPurchaseDistributionOrderList(params) {
        const res = await fetch({
            url: '/api/v3/goods/delivery/orders',
            method: 'get',
            params,
        });
        return res.data;
    },
    // 配货单详情
    async getPurchaseDistributionOrderDetail(id) {
        const res = await fetch({
            url: `/api/v3/goods/delivery/orders/${id}`,
            method: 'get',
        });
        return res.data;
    },
    // 创建配货单
    async createPurchaseDistributionOrder(data) {
        const res = await fetch({
            url: '/api/v3/goods/delivery/orders',
            method: 'post',
            data,
        });
        return res.data;
    },
    // 修改配货单
    async updatePurchaseDistributionOrder(id,data) {
        const res = await fetch({
            url: `/api/v3/goods/delivery/orders/${id}`,
            method: 'put',
            data,
        });
        return res.data;
    },
    // 发货配货单
    async sendPurchaseDistributionOrder(id) {
        const res = await fetch({
            url: `/api/v3/goods/delivery/orders/${id}/send`,
            method: 'put',
        });
        return res.data;
    },
    // 撤回配货单
    async revertPurchaseDistributionOrder(id) {
        const res = await fetch({
            url: `/api/v3/goods/delivery/orders/${id}/withdraw`,
            method: 'put',
        });
        return res.data;
    },
    // 删除草稿单
    async deleteDistributionOrder(id) {
        const res = await fetch({
            url: `/api/v3/goods/delivery/orders/${id}`,
            method: 'delete',
        });
        return res.data;
    },

    // 收货单详情
    async getPurchaseReceiveOrderDetail(id, params) {
        const res = await fetch({
            url: `/api/v3/goods/receive/orders/${id}`,
            method: 'get',
            params,
        });
        return res.data;
    },

    // 创建收货单
    async createPurchaseReceiveOrder(postData) {
        const res = await fetch({
            url: '/api/v3/goods/receive/orders',
            method: 'post',
            data: postData,
        });
        return res.data;
    },

    // 修改收货单
    async updatePurchaseReceiveOrder(id, postData) {
        const res = await fetch({
            url: `/api/v3/goods/receive/orders/${id}`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    // 删除收货单草稿
    async deleteReceiveOrder(id) {
        const res = await fetch({
            url: `/api/v3/goods/receive/orders/${id}`,
            method: 'delete',
        });
        return res.data;
    },
    // 删除采购单草稿
    async deletePurchaseOrder(id) {
        const res = await fetch({
            url: `/api/v3/goods/purchase/orders/${id}`,
            method: 'delete',
        });
        return res.data;
    },

    // 收货单列表
    async getPurchaseInspectOrderList(params) {
        const res = await fetch({
            url: '/api/v3/goods/inspect/orders',
            method: 'get',
            params,
        });
        return res.data;
    },

    // 收货单详情
    async getPurchaseInspectOrderDetail(id, params) {
        const res = await fetch({
            url: `/api/v3/goods/inspect/orders/${id}`,
            method: 'get',
            params,
        });
        return res.data;
    },

    // 创建验收单
    async createPurchaseInspectOrder(postData) {
        const res = await fetch({
            url: '/api/v3/goods/inspect/orders',
            method: 'post',
            data: postData,
        });
        return res.data;
    },

    // 修改验收单
    async updatePurchaseInspectOrder(id, postData) {
        const res = await fetch({
            url: `/api/v3/goods/inspect/orders/${id}`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    // 确认验收
    async confirmPurchaseInspectOrder(id, postData) {
        const res = await fetch({
            url: `/api/v3/goods/inspect/orders/${id}/confirm-inspect`,
            method: 'put',
            data: postData,
        });
        return res.data;
    },

    /**
     * @desc 获取批次信息
     * <AUTHOR>
     * @date 2024-01-20 10:35:21
     * {
     *   "clinicId": "string",
     *   "goodsList": [
     *     {
     *       "goodsId": "string",
     *       "packageCount": 0,
     *       "pieceCount": 0
     *     }
     *   ],
     *   "pharmacyNo": 0
     * }
     */
    async fetchBatchesByGoods(postData) {
        const res = await fetch({
            url: '/api/v3/goods/stocks/batches/pretend-cut',
            method: 'post',
            data: postData,
        });
        return res.data;
    },
    // 拉取某个连锁下的利润率
    async getProfitCategoryTypes(params) {
        const { data: res } = await fetch({
            url: '/api/v3/goods/profit-category-types',
            method: 'get',
            params,
        });
        return res.data || {};
    },
    // 修改利润率
    async putProfitCategoryTypes(data) {
        const { data: res } = await fetch({
            url: '/api/v3/goods/profit-category-types',
            method: 'post',
            data,
        });
        return res.data || {};
    },

    // 获取供应商商品绑定列表
    async getSupplierGoodsList(goodsId, params) {
        const res = await fetch({
            url: `/api/v3/goods/${goodsId}/supplier-goods-mapping`,
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * @desc 搜索供应商商品
     * <AUTHOR>
     * @date 2024/6/19 下午3:28
     * @return {Promise<{}>}
     */
    async searchSupplierGoods(params) {
        const res = await fetch({
            url: '/api/v3/goods/supplier/search-supplier-goods',
            method: 'get',
            params,
        });
        return res.data;
    },

    /* 绑定供应商商品
    * @param {Object} data
    * @param {Number} data.opType 操作类型 0=绑定 1换绑 2=解绑
    * @param {String} data.goodsId 商品id
    * @param {String} data.oldSupplierGoodsId 原供应商编码,换绑有值
    * @param {String} data.supplierGoodsId 供应商商品编码
    * @param {String} data.supplierId 供应商Id
    * */
    async bindSupplierGoods(data) {
        const res = await fetch({
            url: '/api/v3/goods/bind-supplier-goods',
            method: 'put',
            data,
        });
        return res.data;
    },

    async getDictionaryInfo(params) {
        const res = await fetch({
            url: '/api/v3/goods/dictionary-info',
            method: 'get',
            params,
        });
        return res.data;
    },


    /**
     * @desc 批量查询追溯码
     * <AUTHOR> Yang
     * @date 2024-08-21 18:32:43
     * @param {object} data
     * @param {number} data.pharmacyType
     * @param {number} data.pharmacyNo
     * @param {number} data.sceneType
     * @param {string} data.departmentId
     * @param {string} data.wardAreaId
     * @param {Array} data.queryTraceCodeList {no, keyId}
     */
    async fetchTraceCodeInfo(data) {
        const res = await fetch({
            url: '/api/v3/goods/query/traceable-code-list',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 单个药品绑定标识码
     * <AUTHOR>
     * @date 2024/8/27 上午10:04
     */
    async boundIdentificationCode(goodsId, data) {
        const res = await fetch({
            url: `/api/v3/goods/${goodsId}/traceable-code`,
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 批量修改追溯码
     * <AUTHOR> Yang
     * @date 2024/8/27 上午10:04
     * @param {object} data
     */
    async batchUpdateCode(data) {
        const res = await fetch({
            url: '/api/v3/goods/traceable-code',
            method: 'put',
            data,
        });
        return res.data;
    },
    /**
     * @desc 批量获取追溯码应采集数量
     * <AUTHOR> Yang
     * @date 2024/8/27 上午10:04
     * @param {object} data
     * @param {Array} data.list
     * {
     *       "keyId": "string",
     *       "pieceNum": 0,
     *       "pieceUnit": "string",
     *       "packageUnit": "string",
     *       "medicineNmpn": "string",
     *       "shebaoPieceNum": 0,
     *       "shebaoPieceUnit": "string",
     *       "shebaoPackageUnit": "string",
     *       "shebaoMedicineNmpn": "string",
     *       "pieceCount": 0,
     *       "packageCount": 0
     * }
     */
    async getCollectCodeCountList(data) {
        const res = await fetch({
            url: '/api/v3/goods/calculate/traceable-code-count',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 获取药品追溯码采集批次日志
     * <AUTHOR> Yang
     * @date 2024-11-06 09:55:50
     * @param {object} params
     * @param {String} params.goodsId
     * @param {Number} params.pharmacyNo
    */
    async fetchTraceCodeBatLog(params) {
        // 现在先固定 900
        params.action = 900;
        const res = await fetch({
            url: '/api/v3/goods/traceable-code/bat-log',
            params,
            paramsSerializer (p) {
                return Qs.stringify(p, { indices: false });
            },
        });
        return res.data;
    },

    /**
     * @desc 获取追溯码详情信息
     * @param {String} no
     * @param params
     */
    async fetchTraceCodeDetail(no,params) {
        const res = await fetch({
            url: `/api/v3/goods/traceable-code/${no}`,
            params,
        });
        return res.data;
    },

    /**
     * @desc 修改追溯码详情信息
     * @param {String} traceableCodeId
     * @param goodsId
     * @param params
     */
    async updateTraceCodeDetail(traceableCodeId,goodsId,params) {
        const res = await fetch({
            url: `/api/v3/goods/traceable-code/${traceableCodeId}?goodsId=${goodsId}`,
            method: 'put',
            data: params,
        });
        return res.data;
    },

    /**
     * @desc 获取追溯码采集日志
     * @param {String} no
     * @param params
     */
    async fetchTraceCodeLog(no,params) {
        const res = await fetch({
            url: `/api/v3/goods/traceable-code/${no}/log`,
            params,
            paramsSerializer (p) {
                return Qs.stringify(p, { indices: false });
            },
        });
        return res.data;
    },

    async fetchMedicalInsurancePriceLimitConfig() {
        const res = await fetch.get('/api/v3/goods/medicare/limit/price');

        return res.data;
    },

    /**
     * 获取医保项关联的商品列表
     * @param goodsId 对码项 goodsId
     * @param nationalCode 医保编码，新增时传
     * @returns {Promise<void>}
     */
    async fetchMedicalInsuranceItemRelateGoodsList({
        goodsId, nationalCode,
    }) {
        const res = await fetch.get('/api/v3/goods/get-limit-price-used-count', {
            params: {
                goodsId, nationalCode,
            },
        });

        return res.data;
    },
    /**
     * 拉取医保项已设置的限价规则
     * @params {Object} params
     * @params {string} params.nationalCode 医保编码
     * @params {string} params.shebaoName 医保名称
     * @returns {Promise<void>}
     */
    async fetchShebaoItemPriceLimitRule({
        nationalCode, // 医保编码
        shebaoName, // 医保名称
    }) {
        const res = await fetch.get('/api/v3/goods/medicare/limit/price/get-pay-limit-price-rule', {
            params: {
                shebaoName, nationalCode,
            },
        });

        return res.data;
    },

    /**
     * @desc 按追溯码查询药品对应批次数据
     * <AUTHOR>
     * @date 2024/12/31 下午2:27
     */
    async getChargeFormItemBatchInfos(data) {
        const { data: res } = await fetch({
            url: '/api/v3/goods/query/stocks',
            method: 'post',
            data,
        });
        return res.data || {};
    },
    /**
     * @desc 导出要货单
     * <AUTHOR>
     * @date 2025/1/12 18:00
     */
    async exportClaimOrder(orderId) {
        const url = `/api/v3/goods/claim/orders/${orderId}/export`;
        return exportFileByAxios({
            url,
        });
    },
    /**
     * @desc 导出采购单
     * <AUTHOR>
     * @date 2025/1/12 18:00
     */
    async exportPurchaseOrder(orderId) {
        const url = `/api/v3/goods/purchase/orders/${orderId}/export`;
        return exportFileByAxios({
            url,
        });
    },
    /**
     * @desc 导出收货单
     * <AUTHOR>
     * @date 2025/1/12 18:21
     */
    async exportReceiveOrder(orderId) {
        return exportFileByAxios({
            url: `/api/v3/goods/receive/orders/${orderId}/export`,
        });
    },
    /**
     * @desc 导出验收单
     * <AUTHOR>
     * @date 2025/1/12 18:37
     */
    async exportInspectOrder(orderId) {
        return exportFileByAxios({
            url: `/api/v3/goods/inspect/orders/${orderId}/export`,
        });
    },
    /**
     * @desc 导出采购单/要货单列表
     * <AUTHOR>
     * @date 2025/1/12 18:59
     */
    async exportRequireList(params) {
        return exportFileByAxios({
            url: '/api/v3/goods/purchase/orders/export',
            params,
            paramsSerializer (p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
    },
    // 导出配送接口
    async exportDeliveryList(params) {
        return exportFileByAxios({
            url: '/api/v3/goods/delivery/orders/export',
            params,
            paramsSerializer (p) {
                return Qs.stringify(p, { arrayFormat: 'comma' });
            },
        });
    },
    /**
     * @desc 导出验收单列表
     * <AUTHOR>
     * @date 2025/1/12 18:59
     */
    async exportInspectList(params) {
        return exportFileByAxios({
            url: '/api/v3/goods/inspect/orders/export',
            params,
        });
    },
    /**
     * @desc 导出退货单列表
     * <AUTHOR>
     * @date 2025/1/12 18:59
     */
    async exportReturnList(params) {
        return exportFileByAxios({
            url: '/api/v3/goods/return/orders/export',
            params,
        });
    },
    /**
     * @desc 导出收货单列表
     * <AUTHOR>
     * @date 2025/1/12 19:16
     */
    async exportReceiveList(params) {
        return exportFileByAxios({
            url: '/api/v3/goods/receive/orders/export',
            params,
        });
    },
    async searchAIRecommendGoods(data) {
        if (data.diagnosis) {
            data.diagnosis = parseDiagnosis(data.diagnosis);
        }
        const res = await fetch({
            url: '/api/v2/sc/cdss/outpatient/cloud/examine/recommend',
            method: 'post',
            data,
        });
        return {
            data: {
                list: res.data?.recommendList ?? [],
            },
        };
    },
    /**
     * @desc 获取商品会员价
     * <AUTHOR>
     * @date 2025/1/12 19:16
     */
    async fetchGoodsMemberTypePriceList(data) {
        const res = await fetch({
            url: '/api/v3/goods/calculate-goods-member-price',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 获取商品分类会员价折扣信息
     * <AUTHOR>
     * @date 2025/1/12 19:16
     */
    async fetchGoodsTypeMemberPriceDiscount(data) {
        const res = await fetch({
            url: '/api/v3/goods/calculate-goods-type-member-price',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 单个修改门店的定价权
     * <AUTHOR>
     * @date 2025/3/17 17:46
     * @param {Object} data 请求参数
     * @param {Number} data.clearFlag 0 维持原价 1 调整为总部价
     * @param {String} data.clinicId 门店id
     * @param {Number} data.enableSubSelfPriceSet 0 关闭自主定价 1 开启自主定价
     */
    async updateGoodsPricingPermission(data) {
        const res = await fetch({
            url: '/api/v3/goods/config/pricing-permission',
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 修改门店自主定价范围
     * <AUTHOR> Assistant
     * @date 2025/7/15
     * @param {Object} data 请求参数
     */
    async updateStorePricePercent(data) {
        const res = await fetch({
            url: '/api/v3/goods/config/price-percent',
            method: 'put',
            data,
        });
        return res.data;
    },


    /**
     * @desc: 申报四川上报的接口方法
     * @author: ff
     * @time: 2025/4/2
     * @param {Object} data 请求参数
     * @param {Number} data.codeStatus 申报状态
     * @param {String} data.goodsId
     */
    async sichuanNoCodeReport(data) {
        const res = await fetch({
            url: '/api/v2/supervision/national/sichuan/sichuan-have-no-code-goods-create',
            method: 'post',
            data,
        });
        return res.data;
    },
    /**
     * @desc 获取四川药品无码申报结果
     * <AUTHOR>
     * @date 2025-04-02 16:13:19
     */
    async getSichuanNoCodeResult(data) {
        const res = await fetch({
            url: '/api/v2/supervision/national/sichuan/sichuan-have-no-code-goods-query',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 获取药品追溯码采集批次日志
     * @param params
     * @returns {Promise<*|string>}
     */
    async fetchTraceCodeAvailableLog(params) {
        const res = await fetch({
            url: '/api/v3/goods/traceable-code',
            params,
            paramsSerializer (p) {
                return Qs.stringify(p, { indices: false });
            },
        });
        return res.data;
    },
};
