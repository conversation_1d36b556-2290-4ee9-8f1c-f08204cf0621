import fetch from 'utils/fetch';
import BaseAPI from 'api/base-api.js';

//CRM 患者管理相关接口
export default class AiAPI extends BaseAPI {
    /**
     * @desc 现病史推荐
     */
    static async getPresentHistorySuggestion(data) {
        const res = await fetch({
            url: '/api/v2/ai/suggestion-present-history',
            method: 'post',
            data,
        });
        return res && res.data || {};
    }

    /**
     * @desc 分析舌象
     * @param {Object} data - 请求参数
     * @param {string} data.imageUrl - 舌象图片地址
     */
    static async analyzeTongueImage(data) {
        const res = await fetch.post('/api/v2/ai/analysis-tongue', data);
        return res && res.data || {};
    }

    /**
     * @desc ai结果埋点
     */
    static async putAnalysisResult(resultId, data) {
        const res = await fetch({
            url: `/api/v2/ai/analysis-result/extend-flag/${resultId}`,
            method: 'put',
            data,
        });
        return res && res.data || {};
    }

    /**
     * @desc AI检查项目推荐
     * @param {Object} data - 请求参数
     * @param {Object} data.patient - 患者信息
     * @param {Object} data.medicalRecord - 病历信息
     * @param {number} data.offset - 偏移量
     * @param {number} data.limit - 限制数量
     */
    static async getExaminationGoodsSuggestion(data) {
        const res = await fetch({
            url: '/api/v2/ai/business/goods/suggestion/examination-goods',
            method: 'post',
            data,
        });
        return res && res.data || {};
    }

    static async getPreDiagnosisResult(businessId) {
        const res = await fetch({
            url: `/api/v2/ai/analysis/result/list-by-businessId/${businessId}`,
            method: 'get',
            params: {
                businessType: 220,
            },
        });

        return res && res.data || {};

    }
}
