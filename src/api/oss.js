import fetch from 'utils/fetch';

const OSS = {
    getOssToken() {
        return fetch({
            url: '/api/interfaces/oss/getosstoken',
            method: 'get',
        });
    },
    postAbcToolOssToken() {
        return fetch({
            url: '/api/v3/clinics/abc-tool/oss-token',
            method: 'post',
        });
    },
    async getOssProxyToken(data) {
        const res = await fetch({
            url: '/api/oss-proxy/sts-token',
            method: 'post',
            data,
        });
        return res && res.data;
    },

    async getSignFileUrl(fileUrl) {
        const res = await fetch({
            url: '/api/oss-proxy/sign-file-url',
            method: 'get',
            params: {
                fileUrl,
            },
        });
        return res && res.data;
    },
};

export default OSS;
