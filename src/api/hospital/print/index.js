import fetch from 'utils/fetch';
import BaseAPI from 'api/base-api.js';

export default class PrintAPI extends BaseAPI {
    // 护士站获取取药单
    static async updatePrintedFlag(data) {
        const res = await fetch({
            url: '/api/advice/executes/print-contents/printed-flag',
            method: 'put',
            data,
        });
        return res && res.data;
    }

    // 住院单押金信息
    static async getDepositInfo(patientOrderId) {
        const res = await fetch({
            url: `/api/his-charges/print/patient-order/${patientOrderId}/deposits`,
            method: 'get',
        });
        return res && res.data;
    }

    /**
     * 住院费用清单
     * @param patientOrderId
     * @returns {Promise<*|{}>}
     */
    static async getFeeList(patientOrderId) {
        const res = await fetch({
            url: `/api/his-charges/print/patient-order/${patientOrderId}/detail`,
            method: 'get',
        });
        return res ? res.data : {};
    }

    /**
     * 住院费用清单-批量
     * @param {Object} data
     * @returns {Promise<*|{}>}
     */
    static async getFeeListBatch(data) {
        const res = await fetch({
            url: 'api/his-charges/print/patient-orders/detail',
            method: 'post',
            data,
        });
        return res ? res.data : {};
    }

    /**
     * 医生站/护士站打印处方
     * @param data {Object} data
     * @param data.adviceIds {string[]} 医嘱id列表
     */
    static async getPrescriptionListById(data) {
        const res = await fetch({
            url: '/api/advice/advices/print-list/by-ids',
            method: 'post',
            data,
        });
        return res ? res.data : {};
    }

    /**
     * 护士站(执行/领药)、药房站打印用药标签
     * @param data
     * @param data.type {Number} 请求来源类型 1:护士站执行 2:护士站领药/药房站发药
     * @param data.ids {Array<Number>} 医嘱/发药id
     * @return {Promise<*>}
     */
    static async fetchHospitalMedicineTagData(data) {
        const res = await fetch({
            url: '/api/print/hospital/medicine-tag',
            method: 'post',
            data,
        });
        return res && res.data;
    }

    /**
     * @desc: 床头卡打印数据
     * @author: ff
     * @time: 2025/7/24
     */
    static async fetchBedsideCardData(id) {
        const res = await fetch({
            url: `api/v2/patientorders/his/${id}/hospital/print`,
            method: 'get',
        });
        return res && res.data;
    }
}
