import fetch from 'utils/fetch';
import Qs from 'qs';
import AbcAccess from '@/access/utils.js';
import BaseAPI from 'api/base-api.js';

//CRM 患者管理相关接口
export default class CrmAPI extends BaseAPI {
    // 排序
    static async updatePatientSort(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/source/types/reorder',
            method: 'put',
            data,
        });
        return res.data;
    }
    // 创建导入任务
    static async validateImportTask(data) {
        const res = await fetch({
            url: '/api/v2/crm/imports/parse',
            method: 'post',
            data,
        });
        return res.data;
    }
    // 创建导入任务
    static async createdImportTask(data) {
        const res = await fetch({
            url: '/api/v2/crm/imports/tasks',
            method: 'post',
            data,
        });
        return res.data;
    }
    // 执行导入任务
    static async crmImportTaskDone(importTaskId, data) {
        const res = await fetch({
            url: `/api/v2/crm/imports/tasks/${importTaskId}/do`,
            method: 'put',
            data,
        });
        return res.data;
    }
    // 查询导入任务
    static async fetchImportTaskByStatus(data) {
        const res = await fetch({
            url: '/api/v2/crm/imports/tasks/query',
            method: 'post',
            data,
        });
        return res.data;
    }

    // 查询导入失败的任务
    static async fetchImportTaskErrByTaskId(importTaskId, params) {
        const res = await fetch({
            url: `/api/v2/crm/imports/tasks/${importTaskId}/items`,
            method: 'get',
            params,
        });
        return res.data;
    }
    // 取消导入接口
    static async cancelImportTask(importTaskId) {
        const res = await fetch({
            url: `/api/v2/crm/imports/tasks/${importTaskId}/cancel`,
            method: 'put',
        });
        return res.data;
    }
    // 确认已经导入接口
    static async readImportTask(importTaskId) {
        const res = await fetch({
            url: `/api/v2/crm/imports/tasks/${importTaskId}/confirm`,
            method: 'put',
        });
        return res.data;
    }
    // 轮询导入任务
    static async fetchImportTask(importTaskId) {
        const res = await fetch({
            url: `/api/v2/crm/imports/tasks/${importTaskId}`,
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [拉取患者列表]
     */
    static async fetchPatients(paramsInfo) {
        const {
            limit = 30,
            offset = 0,
            index = 0,
            orderBy = 'lastOutpatientDate',
            orderType = 'desc',
        } = paramsInfo;
        const data = {
            ...paramsInfo,
        };
        delete data.limit;
        delete data.offset;
        delete data.index;
        delete data.orderBy;
        delete data.orderType;
        const res = await fetch({
            url: `/api/v2/crm/patients/abstracts?limit=${limit}&offset=${offset}&index=${index}&orderBy=${orderBy}&orderType=${orderType}`,
            method: 'post',
            data,
        });
        return res.data;
    }
    static async fetchCrmPatientFamilySharedRights() {
        const res = await fetch({
            url: 'api/v2/property/chain?key=crm.patient.family.sharedRights',
        });
        return res && res.data;
    }

    static async modifyCrmPatientFamilySharedRights(data) {
        const res = await fetch({
            url: 'api/v2/property/chain?key=crm.patient.family.sharedRights',
            method: 'post',
            data,
        });
        return res && res.data;
    }
    static async fetchPatientRegistrationInfo(id) {
        const res = await fetch({
            url: `/api/v2/registrations/${id}`,
        });
        return res && res.data;
    }

    /**
     * 搜索患者列表
     */
    static async searchPatientsV2(keyword, chainId, clinicId = '', extDataFlag = 0) {
        const res = await fetch({
            url: `/api/v2/crm/patients/query?key=${keyword}&clinicId=${clinicId}&isShowFamilyStatus=1&extDataFlag=${extDataFlag}`,
            method: 'get',
        });
        return res.data;
    }
    static async cascaderSearchPatients(params) {
        const res = await fetch({
            url: '/api/v2/crm/patients/query',
            method: 'get',
            params,
        });
        return res.data;
    }
    // 根据sceneType生成二维码
    static async fetchQRCodeType(businessId,scene = 'qw_share_patient_bind', patientId) {
        const res = await fetch({
            url: `/api/v2/mc/qr-code/scene/${scene}`,
            method: 'get',
            params: {
                businessId,
                patientId,
            },
        });
        return res.data;
    }
    /**
     * 拉取标签模版
     * <AUTHOR>
     * @date 2021-03-01
     * @returns {Promise<Object>}
     */
    static async fetchTemplateTags() {
        const res = await fetch({
            url: '/api/v2/crm/patients/tags/type/template',
            method: 'get',
        });
        return res.data;
    }
    /**
     * 批量选取模版标签
     * <AUTHOR>
     * @date 2021-03-01
     * @returns {Promise<Object>}
     */
    static async selectTemplateTags(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/tags/type/batch',
            method: 'post',
            data,
        });
        return res.data;
    }

    /**
     * 拉取标签
     * <AUTHOR>
     * @date 2021-03-01
     * @returns {Promise<Object>}
     */
    static async fetchTags() {
        const res = await fetch({
            url: '/api/v2/crm/patients/tags/type',
            method: 'get',
        });
        return res.data;
    }

    static async saveMoveTags(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/tags/type/move',
            method: 'post',
            data,
        });
        return res.data;
    }

    static async fetchAutoCreateRulesCount() {
        const res = await fetch({
            url: '/api/v2/crm/patients/revisit/task/auto-create/count',
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [新增标签]
     */
    static async fetchInsertTags(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/tags/type',
            method: 'post',
            data,
        });
        return res.data;
    }
    /**
     * 修改标签
     * <AUTHOR>
     * @date 2021-03-01
     * @param {String} id 标签或者分组id
     * @param {Object} data 提交数据
     * @returns {Promise<Object>}
     */
    static async fetchUpdateTags(id, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/tags/${id}/type`,
            method: 'PUT',
            data,
        });
        return res.data;
    }

    /**
     * 修改标签图标
     * <AUTHOR>
     * @date 2023-03-15
     * @param {String} id 标签id
     * @param {Object} data 提交数据
     * @returns {Promise<Object>}
     */
    static async fetchUpdateTagsType(tagTypeId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/tags/${tagTypeId}/type`,
            method: 'PUT',
            data,
        });
        return res.data;
    }

    /**
     * 删除标签
     * <AUTHOR>
     * @date 2021-03-01
     * @param {String} id 标签或分组id
     * @returns {Promise<Object>}
     */
    static async fetchDeleteTags(id) {
        const res = await fetch({
            url: `/api/v2/crm/patients/tags/${id}/type`,
            method: 'delete',
        });
        return res.data;
    }

    /**
     * desc [拉取首诊来源]
     */
    static async fetchPatientSource() {
        const res = await fetch({
            url: '/api/v2/crm/patients/source/types/',
        });
        return res.data;
    }

    /**
     * desc [拉取患者预览数据]
     */
    static async fetchPatientOverview(patientId, params) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}`,
            params,
            method: 'get',
        });
        return res.data;
    }
    // 拉取患者信息 防爬虫版本
    static async fetchPatientOverviewV2(patientId, params) {
        const res = await fetch({
            url: `/api/v2/crm/patients/detail/${patientId}`,
            params,
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [拉取患者合并数据]防爬虫版本
     */
    static async fetchMergePatientInfoV2(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/detail/${patientId}`,
            method: 'get',
            params: {
                isPatientMerge: true,
                chronicArchives: 1,
            },
        });
        return res.data;
    }
    /**
     * desc [拉取患者合并数据]
     */
    static async fetchMergePatientInfo(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}`,
            method: 'get',
            params: {
                isPatientMerge: true,
                chronicArchives: 1,
            },
        });
        return res.data;
    }
    /**
     * desc [患者轨迹]
     */
    static async fetchTrace(patientId, {
        offset,
        limit,
        action,
    }) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/trace`,
            method: 'get',
            params: {
                offset,
                limit,
                action,
            },
        });
        return res.data;
    }
    /**
     * desc [检查检验轨迹]
     */
    static async fetchExamination(patientId, params) {
        const res = await fetch({
            url: `/api/v2/examinations/list/by-patientId/${patientId}`,
            method: 'get',
            params,
        });
        return res.data;
    }
    /**
     * desc [拉取患者门诊、付费及累计消费信息]
     */
    static async fetchPatientBaseInfo(patientId) {
        const res = await fetch({
            url: '/api/v2/sc/stat/patient/index',
            method: 'get',
            params: {
                patientId,
            },
        });
        return res.data;
    }
    /**
     * desc [拉取患者家庭成员门诊、付费及累计消费信息]
     */
    static async fetchFamilyBaseInfo(data) {
        const res = await fetch({
            url: '/api/v2/sc/stat/patient/index',
            method: 'post',
            data,
        });
        return res.data;
    }
    /**
     * desc [拉取患者卡项信息]
     */
    static async fetchPatientCardsInfo(patientId) {
        if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.MARKET_CARD)) {
            return this.noAccessFunc('fetchPatientCardsInfo', AbcAccess.accessMap.MARKET_CARD);
        }

        const res = await fetch({
            url: `/api/v2/promotions/card/patients/${patientId}/list-brief`,
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [修改患者信息]
     */
    static async updatePatientInfo(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}`,
            method: 'put',
            data,
        });
        return res.data;
    }
    /**
     * desc [新增患者信息]
     */
    static async insertPatientInfo(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients',
            method: 'post',
            data,
        });
        return res.data;
    }
    // TODO CRM
    static async handlePatientExist(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/clinic`,
            method: 'post',
        });
        return res.data;
    }
    /**
     * desc [给患者标记标签]
     */
    static async addPatientsTags(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/tags/batch',
            method: 'post',
            data,
        });
        return res.data;
    }
    /**
     * desc [给患者删除标签]
     */
    static async deletePatientTag(patientId, tagId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/tags/${tagId}`,
            method: 'delete',
        });
        return res.data;
    }
    /**
     * desc [拉取患者的标签]
     */
    static async fetchPatientTags(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/tags`,
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [标记指定标签的患者数]
     */
    static async getExitTagsNum(tagId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/patientTags/${tagId}`,
            method: 'get',
        });
        return res.data;
    }

    /**
     * desc [修改会员信息]
     */
    static async updateMemberInfo(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/member`,
            method: 'put',
            data,
        });
        return res.data;
    }

    // 患者基础档案
    static async modifyMemberInfo(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/family/relevance/${patientId}`,
            method: 'put',
            data,
        });
        return res.data;
    }
    // 获取患者最近创建
    static async fetchPatientNearestCreatedAppointment(patientId) {
        const res = await fetch({
            url: `/api/v2/registrations/query/patient/${patientId}/wait-audit/nearest-created`,
            method: 'get',
        });
        return res.data;
    }
    // 患者家庭成员
    static async fetchFamilyMember(patientId, params = {}) {
        const res = await fetch({
            url: `/api/v2/crm/patients/family/relevance/${patientId}`,
            method: 'get',
            params,
        });
        return res.data;
    }
    static async delMemberInfo(params) {
        console.log(params);
        const res = await fetch({
            url: '/api/v2/crm/patients/family/relevance',
            method: 'delete',
            data: params,
        });
        return res.data;
    }

    /**
     * desc [修改会员卡支付密码]
     */
    static async updateMemberPassword(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/member/password`,
            method: 'post',
            data,
        });
        return res.data;
    }
    /**
     * desc [验证会员卡支付密码]
     */
    static async validateMemberPassword(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/member/password/verification`,
            method: 'post',
            data,
        });
        return res.data;
    }

    /**
     * desc [删除会员信息]
     */
    static async deleteMemberInfo(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/member`,
            method: 'delete',
        });
        return res.data;
    }
    /**
     * desc [新增会员]
     */
    static async insertMemberInfo(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/member',
            method: 'post',
            data,
        });
        return res.data;
    }
    /**
     * desc [拉取会员信息]
     */
    static async selectMemberInfo(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/member`,
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [拉取患者是否有共享会员信息]
     */
    static async selectShareMemberInfo(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/memberShare/${patientId}`,
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [拉取患者是否有共享会员信息]
     */
    static async findShareMemberInfo(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/memberShareInfo/${patientId}`,
            method: 'get',
        });
        return res.data;
    }

    /**
     * desc [拉取患者收费记录]
     */
    static async fetchPatientChargeRecord(patientId, params) {
        const res = await fetch({
            url: `/api/v2/charges/patient/${patientId}`,
            method: 'get',
            params,
        });
        return res.data;
    }
    /**
     * desc [拉取体检患者收费记录]
     */
    static async fetchPEPatientChargeRecord(patientId, params) {
        const res = await fetch({
            url: `/api/pe-charge/charge-sheets/query/${patientId}/quick-list/personal`,
            method: 'get',
            params,
        });
        return res.data;
    }
    /**
     * desc [拉取体检患者收费记录]
     */
    static async fetchInHospitalPatientChargeRecord(patientId, params) {
        const res = await fetch({
            url: `/api/his-charges/settle/${patientId}/quick-list`,
            method: 'get',
            params,
        });
        return res.data;
    }
    /**
     * desc [拉取患者预约记录]
     */
    static async fetchPatientAppointmentRecord(patientId, params = {}) {
        const res = await fetch({
            url: `/api/v2/registrations/patient/${patientId}/query`,
            method: 'get',
            params,
        });
        return res;
    }


    static async fetchPatientVisitRecord(patientId, data) {
        const {
            offset = 0, limit = 12, beginDate = '', endDate = '', executorId = null, status = 0, withAll = 1,
        } = data;
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/revisit/records`,
            method: 'get',
            params: {
                offset,
                limit,
                beginDate,
                endDate,
                executorId,
                status,
                withAll,
            },
        });
        return res?.data || {};
    }

    /**
     * desc [拉取患者治疗理疗记录]
     */
    static async fetchPatientExamRecord(params) {
        const res = await fetch({
            url: 'api/v2/charges/patient/physical-therapy/list',
            params,
        });
        return res.data;
    }

    /**
     * desc [拉取当前诊所全部医生列表]
     */
    static async fetchDoctorList() {
        const res = await fetch({
            url: '/api/v3/clinics/employees/clinic-doctors',
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [拉取当前诊所全部人员列表]
     */
    static async fetchEmployeeList() {
        const res = await fetch({
            url: '/api/v3/clinics/employees/list-by-clinic',
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [拉取当前连锁全部人员列表]
     */
    static async fetchEmployeesListByChain() {
        const res = await fetch({
            url: '/api/v3/clinics/employees/list-by-chain',
            method: 'get',
        });
        return res.data;
    }

    static async fetchRevisitEmployeeList() {
        const res = await fetch({
            url: '/api/v2/crm/patients/revisit/employee/list',
            method: 'get',
        });
        return res.data;
    }


    /**
     * desc [患者合并创建任务接口]
     */
    static async createMergeTask(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/merge/task',
            method: 'post',
            data,
        });
        return res.data;
    }
    /**
     * desc [查询患者合并任务状态接口]
     */
    static async selectMergeTaskStatus(taskId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/merge/task/${taskId}`,
            method: 'get',
        });
        return res.data;
    }

    /**
     * @desc 新增患者收货地址
     * <AUTHOR>
     * @date 2020/02/12 16:11:17
     */
    static async createPatientDelivery(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/deliveryInfo`,
            method: 'post',
            data,
        });
        return res.data;
    }

    /**
     * @desc 编辑患者收货地址
     * <AUTHOR>
     * @date 2020/02/12 16:11:17
     */
    static async updatePatientDelivery(patientId, deliveryId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/deliveryInfo/${deliveryId}`,
            method: 'put',
            data,
        });
        return res.data;
    }

    /**
     * @desc 删除患者收货地址
     * <AUTHOR>
     * @date 2020/02/12 16:11:17
     */
    static async deletePatientDelivery(patientId, deliveryId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/deliveryInfo/${deliveryId}`,
            method: 'delete',
        });
        return res.data;
    }

    /* ===============患者随访============== */

    /**
     * 创建随访任务
     * <AUTHOR>
     * @date 2020-10-16
     * @param {Object} data 入参数据
     * @returns {Promise}
     */
    static async createPatientRevisitV2(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/revisit/task',
            method: 'POST',
            data,
        });
        return res.data;
    }

    // 批量创建随访任务
    static async createPatientBatchRevisit(data) {
        const res = await fetch({
            url: 'api/v2/crm/patients/revisit/task/batch',
            method: 'POST',
            data,
        });
        return res.data;
    }
    // 设置PC端审核预约申请
    static async setAuditStatus(registrationSheetId, auditStatus, registrationReq = null) {
        const res = await fetch({
            url: `/api/v2/registrations/manage/${registrationSheetId}/audit`,
            method: 'PUT',
            data: {
                auditStatus, registrationReq,
            },
        });
        return res.data;
    }
    /**
     * 终止随访任务
     * <AUTHOR>
     * @date 2020-10-16
     * @param {String} taskId 随访单、任务id
     * @param {Object} data 入参数据
     * @returns {Promise}
     */
    static async stopPatientRevisit(taskId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/task/${taskId}`,
            method: 'PUT',
            data: {
                status: 2, cancelType: 1,
            }, // 终止状态，原因为：计划取消
        });
        return res.data;
    }
    /**
     * 删除随访任务
     * <AUTHOR>
     * @date 2020-10-16
     * @param {String} taskId 任务id
     * @returns {Promise}
     */
    static async deletePatientRevisit(taskId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/task/${taskId}`,
            method: 'DELETE',
        });
        return res.data;
    }
    /**
     * 修改随访任务
     * <AUTHOR>
     * @date 2020-10-16
     * @param {String} taskId 任务id
     * @param {Object} data 入参数据
     * @returns {Promise}
     */
    static async updatePatientRevisit(taskId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/task/${taskId}`,
            method: 'PUT',
            data,
        });
        return res.data;
    }
    /**
     * desc [创建随访模板]
     */
    static async createVisitTemplate(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/revisit/template',
            method: 'post',
            data,
        });
        return res.data;
    }
    /**
     * desc [修改随访模板]
     */
    static async updateVisitTemplate(templateId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/template/${templateId}`,
            method: 'put',
            data,
        });
        return res.data;
    }
    /**
     * desc [获取随访模板列表]
     */
    static async selectVisitTemplates(params) {
        const res = await fetch({
            url: '/api/v2/crm/patients/revisit/template',
            method: 'get',
            params,
        });
        return res.data;
    }
    /**
     * desc [删除指定随访模板]
     */
    static async deleteVisitTemplate(templateId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/template/${templateId}`,
            method: 'delete',
        });
        return res.data;
    }
    /**
     * 获取患者随访列表
     * <AUTHOR>
     * @date 2020-10-14
     * @param {Object} params 入参数据
     * @returns {Promise}
     */
    static async selectRevisitTaskListV2(params) {
        const res = await fetch({
            url: '/api/v2/crm/patients/revisit/records',
            method: 'GET',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
        });
        return res.data;
    }
    /**
     * 获取随访任务详情
     * <AUTHOR>
     * @date 2020-10-19
     * @param {String} taskId 随访计划id
     * @returns {Promise}
     */
    static async selectRevisitTaskDetail(taskId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/task/${taskId}`,
            method: 'GET',
        });
        return res.data;
    }

    static async fetchRevisitTaskRecords(taskId, limit = 10, offset = 0, beginDate = '', endDate = '') {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/task/${taskId}/records`,
            method: 'GET',
            params: {
                limit,
                offset,
                beginDate,
                endDate,
            },
        });
        return res.data;
    }
    /**
     * desc [获取随访详情]
     */
    static async selectRevisitDetail(recordId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/record/${recordId}`,
            method: 'get',
        });
        return res.data;
    }
    /**
     * desc [获取患者近三个月的随访记录]
     */
    static async selectRevisitFutureThreeMonths(patientId, params) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/revisit/records`,
            method: 'get',
            params,
        });
        return res.data;
    }
    /**
     * desc []
     */
    /**
     * 更新患者随访
     * <AUTHOR>
     * @date 2020-10-21
     * @param {String} revisitId 随访单id
     * @param {Object} data 入参数据
     * @returns {Promise}
     */
    static async updateRevisitResultV2(revisitId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/record/${revisitId}`,
            method: 'PUT',
            data,
        });
        return res.data;
    }
    /**
     * 终止患者随访
     * <AUTHOR>
     * @date 2020-10-21
     * @param {String} revisitId 随访单id
     * @param {Number} cancelType 入参数据
     * @returns {Promise}
     */
    static async stopRevisitResultV2(revisitId, cancelType) {
        const res = await fetch({
            url: `/api/v2/crm/patients/revisit/record/${revisitId}`,
            method: 'PUT',
            data: {
                status: 2,//终止状态
                cancelType,//终止原因
            },
        });
        return res.data;
    }

    /* ===============患者沟通============== */
    /**
     * 获取当前是否存在有效的会话
     * <AUTHOR>
     * @date 2020-05-06
     * @param {any} participantId 参与者id（患者id）
     * @param {any} sceneType 0-在线咨询，1-患者沟通
     */
    static async checkConversationAvailable(participantId, sceneType) {
        const res = await fetch({
            url: '/api/v2/im/conversation/available/by-participantId',
            method: 'get',
            params: {
                participantId,
                sceneType,
            },
        });
        return res.data;
    }
    /**
     * 创建患者沟通会话
     * <AUTHOR>
     * @date 2020-05-06
     * @param {any} patientId
     */
    static async createConversation(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/online/chat/${patientId}/conversation`,
            method: 'post',
        });
        return res.data;
    }

    static async fetchDepartmentsDoctors(params) {
        const res = await fetch({
            url: '/api/v2/registrations/departments/doctors',
            params,
        });
        return res && res.data;
    }
    /**
     * 关键词搜索关注微信的患者
     * <AUTHOR>
     * @date 2020-05-07
     * @param {String} key 关键词
     */
    static async fetchChatPatientBasicQuery(key) {
        const res = await fetch({
            url: '/api/v2/crm/online/chat/patients/basic/query',
            method: 'get',
            params: {
                key,
            },
        });
        return res.data;
    }
    /**
     * 医生端或患者端获取未读消息个数
     * <AUTHOR>
     * @date 2020-05-09
     * @param {Number} sceneType 0-在线咨询，1-患者沟通
     */
    static async fetchUnreadMessageCount(sceneType) {
        const res = await fetch({
            url: '/api/v2/im/message/unRead/view',
            method: 'get',
            params: { sceneType },
        });
        return res.data;
    }
    /**
     * 全部已读
     * <AUTHOR>
     * @date 2020-05-09
     * @param {Number} sceneType 0-在线咨询，1-患者沟通
     * @param {Number} type 2-全部已读
     */
    static async handleReadAllMessage(sceneType, type = 2) {
        const res = await fetch({
            url: '/api/v2/im/message/sync',
            method: 'put',
            params: {
                sceneType,
                type,
            },
        });
        return res.data;
    }

    /**
     * 通过二维码获取健康信息
     * <AUTHOR>
     * @date 2021-03-24
     * @param {String} qrCode 二维码
     * @param {String} departmentMedicalCode 科室编码
     * @returns {Promise<Object>}
     */
    static async queryPatientByExternalCode(qrCode, departmentMedicalCode) {
        const res = await fetch({
            url: '/api/v2/crm/patients/basic/by-external-code',
            method: 'post',
            data: {
                qrCode,
                departmentMedicalCode,
            },
        });
        return res.data;
    }

    /**
     * 更新社保信息接口
     * <AUTHOR>
     * @date 2020-08-13
     * @param {String} orderId 单子id
     * @param {Object} data { shebaoCardInfo }
     * @returns {Promise}
     */
    static async fillPatientOrderInfo(orderId, data) {
        const res = await fetch({
            url: `/api/v2/patientorders/${orderId}`,
            method: 'put',
            data,
        });
        return res.data;
    }
    /**
     * 患者解绑微信公众号
     * <AUTHOR>
     * @date 2020-06-29
     * @param {String} patientId
     * @returns {Promise}
     */
    static async basicUnbindWeChat(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/basic/unbind/wx`,
            method: 'put',
        });
        return res.data;
    }
    /**
     * 修改患者儿童健康档案标识
     * <AUTHOR>
     * @date 2020-05-09
     * @param {String} patientId 患者id
     * @return {Promise}
     */
    static async updatePatientBindChildHealthFile(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/child-care/archives`,
            method: 'put',
        });
        return res.data;
    }
    /**
     * 患者绑定微信获取url
     * <AUTHOR>
     * @date 2020-06-29
     * @param {String} patientId
     * @returns {Promise}
     */
    static async fetchBindWeChatUrl(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/basic/bind/wx/url`,
            method: 'get',
        });
        return res.data;
    }

    /**
     * @desc 患者获取绑定微信的二维码
     * <AUTHOR> Yang
     * @date 2020-08-26 09:15:48
     * @params
     * @return
     */
    static async fetchPatientBindQrCode(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/bind/wx/qr-code',
            method: 'post',
            data,
        });
        return res.data;
    }

    /**
     * @desc 患者根据二维码key获取绑定状态
     * <AUTHOR> Yang
     * @date 2020-08-26 09:16:49
     * @params
     * @return
     */
    static async queryPatientBindQrCodeStatus(params) {
        const res = await fetch({
            url: '/api/v2/crm/patients/bind/wx/qr-code/status',
            method: 'get',
            params,
        });
        return res.data;
    }

    /**
     * [desc] 获取当前可以领取的优惠卷
     */
    static async fetchToobtainCoupons(chainId, patientId) {
        if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.MARKET_COUPON)) {
            return this.noAccessFunc('fetchToobtainCoupons', AbcAccess.accessMap.MARKET_COUPON);
        }
        const res = await fetch.get('/api/v2/promotions/coupons/to-obtain', {
            params: {
                chainId,
                patientId,
            },
        });
        return res.data;
    }

    /**
     * [desc]获取已经领取可用优惠卷
     */
    static async fetchObtainedCoupons(chainId, patientId, offset, limit) {
        if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.MARKET_COUPON)) {
            return this.noAccessFunc('fetchObtainedCoupons', AbcAccess.accessMap.MARKET_COUPON);
        }

        const res = await fetch.get('/api/v2/promotions/coupons/obtained', {
            params: {
                chainId,
                patientId,
                offset,
                limit,
            },
        });
        return res?.data || {};
    }

    /**
     * [desc]发放优惠卷
     */
    static async addCoupon(params) {
        const res = await fetch({
            url: '/api/v2/promotions/coupons/obtain',
            method: 'post',
            data: params,
        });
        return res.data;
    }

    static async batchInvalid(params) {
        const res = await fetch({
            url: '/api/v2/promotions/coupons/batch-invalid',
            method: 'put',
            data: params,
        });
        return res.data;
    }

    /**
     * @desc 获取连锁的优惠券
     * <AUTHOR>
     * @date 2022-06-16 10:48:44
     * @params
     * @return
     */
    static async fetchChainCouponList() {
        if (!AbcAccess.getPurchasedByKey(AbcAccess.accessMap.MARKET_COUPON)) {
            return this.noAccessFunc('fetchChainCouponList', AbcAccess.accessMap.MARKET_COUPON);
        }
        const res = await fetch({
            url: '/api/v2/promotions/coupons/to-obtain/list-by-chain',
            method: 'get',
        });
        return res.data;
    }

    /**
     * @desc 批量发放优惠券
     * <AUTHOR>
     * @date 2022-06-16 11:32:20
     * @params
     * @return
     */
    static async batchObtainPatientCoupons(data) {
        const res = await fetch({
            url: '/api/v2/promotions/coupons/batch-obtain',
            method: 'post',
            data,
        });
        return res.data;
    }

    /**
     * 查询家庭医生签约信息 - 患者id
     * <AUTHOR>
     * @date 2021-05-21
     * @param {String} patientId 患者id
     * @returns {Promise<Object>}
     */
    static async fetchFamilyDoctorInfoByPatientId(patientId) {
        const res = await fetch({
            url: '/api/v2/crm/patient/family/doctor/find-all/info',
            method: 'GET',
            params: { patientId },
        });
        return res.data;
    }
    /**
     * 家庭医生取消接口
     * <AUTHOR>
     * @date 2021-05-21
     * @param {String} familyDoctorId 家庭医生签约id
     * @returns {Promise<Object>}
     */
    static async handleCancelSign(familyDoctorId) {
        const res = await fetch({
            url: `/api/v2/crm/patient/family/doctor/cancel/${familyDoctorId}`,
            method: 'PUT',
        });
        return res.data;
    }
    /**
     * 处理提交家庭医生签约信息
     * <AUTHOR>
     * @date 2021-05-21
     * @param {Object} data 签约信息
     * @returns {Promise<Object>}
     */
    static async handleSubmitFamilyDoctorSignTheContract(data) {
        const res = await fetch({
            url: '/api/v2/crm/patient/family/doctor/sign-the-contract',
            method: 'POST',
            data,
        });
        return res.data;
    }
    /**
     * 更新家庭医生签约信息 - 通过家庭医生签约id
     * <AUTHOR>
     * @date 2021-05-21
     * @param {String} familyDoctorId 家庭医生签约id
     * @param {Object} data 更新信息
     * @returns {Promise<Object>}
     */
    static async updateFamilyDoctorInfoById(familyDoctorId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patient/family/doctor/${familyDoctorId}`,
            method: 'PUT',
            data,
        });
        return res.data;
    }
    /**
     * 家庭医生解约接口
     * <AUTHOR>
     * @date 2021-05-21
     * @param {String} familyDoctorId 家庭医生签约id
     * @returns {Promise<Object>}
     */
    static async handleSignUnbind(familyDoctorId) {
        const res = await fetch({
            url: `/api/v2/crm/patient/family/doctor/termination/${familyDoctorId}`,
            method: 'PUT',
        });
        return res.data;
    }
    /**
     * 重新发起签约(生成新RandomKey接口)
     * <AUTHOR>
     * @date 2021-05-21
     * @param {String} familyDoctorId 家庭医生签约id
     * @returns {Promise<Object>}
     */
    static async handleSigning(familyDoctorId) {
        const res = await fetch({
            url: `/api/v2/crm/patient/family/doctor/${familyDoctorId}/sign-the-contract/again`,
            method: 'POST',
        });
        return res.data;
    }
    /**
     * 重家庭医生发送短信接口
     * <AUTHOR>
     * @date 2021-05-21
     * @param {Object} data 提交数据
     * @returns {Promise<Object>}
     */
    static async handleSignTheContractSendSms(data) {
        const res = await fetch({
            url: '/api/v2/crm/patient/family/doctor/sign-the-contract/send-sms',
            method: 'POST',
            data,
        });
        return res.data;
    }
    /**
     * 查询家庭医生签约状态
     * <AUTHOR>
     * @date 2021-05-21
     * @param {String} familyDoctorId 家庭医生签约id
     * @returns {Promise<Object>}
     */
    static async fetchSignFindState(familyDoctorId) {
        const res = await fetch({
            url: `/api/v2/crm/patient/family/doctor/${familyDoctorId}/find-state`,
            method: 'GET',
        });
        return res.data;
    }

    static async fetchPatientFamilyWechatBindStatus(patientId, chainId) {
        if (!chainId) {
            chainId = undefined;
        }
        const res = await fetch({
            url: '/api/v2/crm/patients/get/family/wx/status',
            method: 'post',
            data: {
                patientIds: [patientId],
                chainId,
            },
        });
        return res.data;
    }

    static async fetchCardsListBrief() {
        const res = await fetch({
            url: '/api/v2/promotions/card/list-brief',
        });

        return res.data;
    }

    static async fetchCardDetail(id) {
        const res = await fetch({
            url: `/api/v2/promotions/card/${id}`,
        });

        return res.data;
    }

    static async fetchPatientCardDetail(itemId, params) {
        const res = await fetch({
            url: `/api/v2/promotions/card/patients/${itemId}`,
            params,
        });

        return res.data;
    }

    static async putPatientCardDetail(itemId, data) {
        const res = await fetch({
            url: `/api/v2/promotions/card/patients/${itemId}`,
            method: 'put',
            data,
        });

        return res.data;
    }

    static async bindCardWithPatient(data) {
        const res = await fetch({
            url: '/api/v2/promotions/card/patients',
            method: 'post',
            data,
        });

        return res.data;
    }

    static async fetchCardholderList(params) {
        const res = await fetch({
            url: '/api/v2/promotions/card/patients/list-page',
            params,
        });

        return res.data;
    }

    static async fetchCardChargeRule(id) {
        const res = await fetch({
            url: `/api/v2/promotions/card/patients/${id}/query-charge-rule`,
        });

        return res.data;
    }

    static async cardholderCharge(data) {
        const res = await fetch({
            url: '/api/v2/promotions/card/patients/recharge',
            method: 'post',
            data,
        });

        return res.data;
    }

    static async orderStatus(id) {
        const res = await fetch({
            url: `/api/v2/promotions/card/patients/order/${id}`,
            method: 'get',
        });
        return res.data || {};
    }

    static async cardholderRefund(data) {
        const res = await fetch({
            url: '/api/v2/promotions/card/patients/refund',
            method: 'post',
            data,
        });

        return res.data;
    }

    static async cardholderDestory(data) {
        const res = await fetch({
            url: '/api/v2/promotions/card/patients/close-card',
            method: 'post',
            data,
        });

        return res.data;
    }

    static async cardholderBalanceRecord(id, pageIndex, pageSize) {
        const res = await fetch.get(`/api/v2/promotions/card/patients/${id}/list-charge-records`, {
            params: {
                limit: pageSize,
                offset: pageSize * pageIndex,
            },
        });
        return res.data;
    }

    static async cardholderDiscountRecord(id, pageIndex, pageSize) {
        const res = await fetch.get(`/api/v2/promotions/card/patients/${id}/list-deduct-records`, {
            params: {
                limit: pageSize,
                offset: pageSize * pageIndex,
            },
        });
        return res.data;
    }

    static async rechargePrint(transactionId) {
        const res = await fetch({
            url: `/api/v2/promotions/card/patients/${transactionId}/print-charge-records`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data || {};

    }
    static async openCardPrint(cardPatientId) {
        const res = await fetch({
            url: `/api/v2/promotions/card/patients/${cardPatientId}/print-card-info`,
            method: 'get',
            disabledCancel: true,
        });
        return res.data || {};

    }
    static async fetchMatchedPatients(params) {
        const res = await fetch({
            url: '/api/v2/crm/patients/matched/info',
            method: 'get',
            params,
            paramsSerializer (paramsStr) {
                return Qs.stringify(paramsStr);
            },
        });
        return res.data;
    }

    /**
     * @desc 通过身份证或者社保获取绑定的患者结构
     * <AUTHOR>
     * @date 2021-09-27 16:28:31
     * @params
     * @return
     */
    static async fetchBindedPatients(data, params = {}) {
        const res = await fetch({
            url: 'api/v2/crm/patients/bind/card/query',
            method: 'post',
            data,
            params,
        });
        return res.data;
    }
    /**
     * @desc 通过社保卡或者身份证绑定患者
     * <AUTHOR>
     * @date 2021-09-27 20:25:46
     * @params cardType 0 医保卡绑定，1 身份证绑定，不需要绑定社保信息
     * @return
     */
    static async bindPatientByCard(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/bind/card`,
            method: 'put',
            data,
        });
        return res.data;
    }

    /**
     * @desc 读取社保卡，新增患者
     */
    static async addPatientWithCard(data) {
        const res = await fetch({
            url: '/api/v2/crm/patients/with-card',
            method: 'post',
            data,
        });
        return res.data;
    }

    /**
     * @desc 解除医保卡的绑定
     */
    static async unbindPatientSoicalCard(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/shebao/info`,
            method: 'delete',
        });
        return res.data;
    }

    // 生成患者上传影像附件二维码
    static async medicalImageQrCode(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/attachment/upload/qr-code`,
            method: 'post',
            data,
        });
        return res?.data?.data;
    }

    /*
    * @desc 获取患者的病历附件
    * @param patientId 患者id
    * @param {string} params.additionalFileTypes 额外附件类型，默认返回图片
    * @returns {Promise<any>}
    */
    static async fetchMedicalRecordAttachmentsByPatientId(patientId, params) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/attachments`,
            params,
            method: 'get',
            paramsSerializer (paramsStr) {
                return Qs.stringify(paramsStr, { arrayFormat: 'repeat' });
            },
        });
        return res.data;
    }

    /*
     * @desc 保存患者的附件
     * @param patientId 患者id
     * @returns {Promise<any>}
     */
    static async saveAttachmentsByPatientId(patientId, data) {
        const res = await fetch.post(`/api/v2/crm/patients/${patientId}/attachments`, data);
        return res.data;
    }


    /**
     * 保存普通附件的标注数据
     * @param attachmentId
     * @param data
     * @returns {Promise<any>}
     */
    static async saveAttachmentsImagingMeasurementData(attachmentId, data) {
        const res = await fetch.put('/api/v2/crm/patients/attachment/label', {
            labelData: data,
            url: attachmentId,
        });
        return res.data;
    }

    /**
     * @desc 删除附件图片
     * <AUTHOR>
     * @date 2022-12-07 14:01:49
     * @param patientId
     * @param attachmentId
     */
    static async deleteAttachment(patientId, attachmentId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/attachments/${attachmentId}`,
            method: 'delete',
        });
        return res.data;
    }

    static async addOrgan(data) {
        const res = await fetch.post('/api/v2/crm/organ/create', data);
        return res.data;
    }

    static async getOrganList(params) {
        params = params || {};

        const res = await fetch({
            url: '/api/v2/crm/organ/find',
            method: 'get',
            params,
            paramsSerializer (paramsStr) {
                return Qs.stringify(paramsStr);
            },
        });
        return res.data;
    }

    static async updateOrgan(data) {
        const res = await fetch.put('/api/v2/crm/organ/update', data);
        return res.data;
    }


    /**
     * @desc 添加 或者 修改家庭成员
     * <AUTHOR>
     * @date 2023-11-08 15:37:40
     * @params
     * @return
     */
    static async updateFamilyPatient(patientId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/family/relevance/${patientId}/upsert`,
            method: 'post',
            data,
        });
        return res.data;
    }

    /**
     * @desc 查询患者业务统计数据
     * <AUTHOR>
     * @date 2024/04/22 15:42:20
     */
    static async fetchPatientBusinessStat(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/business-stat`,
            method: 'get',
        });
        return res && res.data || {};
    }

    static async modifyAttachmentName(patientId, attachmentId, data) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/attachments/${attachmentId}`,
            method: 'put',
            data,
        });

        return res.data;
    }

    static async loadRelateMember(patientId) {
        const res = await fetch({
            url: `/api/v2/crm/patients/${patientId}/relate-available-members`,
            method: 'get',
        });

        return res.data;
    }

    /**
     * @desc 查询患者卡项可退款列表
     */
    static async fetchPatientRechargeCardRefundableList(id,params) {
        const res = await fetch({
            url: `/api/v2/promotions/card/patients/${id}/recharge-refundable/list`,
            method: 'get',
            params,
            paramsSerializer (paramsStr) {
                return Qs.stringify(paramsStr);
            },
        });
        return res && res.data || {};
    }


    /**
     * @desc: 检查当前这个卡能否用ABC支付原路退回
     * @author: ff
     * @time: 2025/6/12
     */

    static async checkRefundSupportPayMode(id) {
        const res = await fetch({
            url: `/api/v2/promotions/card/patients/${id}/refund-support-pay-mode`,
            method: 'get',
        });
        return res.data;
    }
}
