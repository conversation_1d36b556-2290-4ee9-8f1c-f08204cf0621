import Qs from 'qs';
import fetch from 'utils/fetch';

/**
 * 登陆相关接口
 */

const Clinic = {
    /**
     * 获取连锁下各门店
     * <AUTHOR>
     * @date 2020-09-25
     * @returns {Promise}
     */
    async chainClinicV3() {
        const res = await fetch({
            url: '/api/v3/clinics/chain/clinics',
            methods: 'get',
        });
        return res.data;
    },
    getModules: () => {
        return fetch({
            url: '/api/domainmodules',
            method: 'get',
        });
    },
    // 审核通过
    approve: (data) => {
        return fetch({
            url: '/api/employees/approve',
            method: 'post',
            data,
        });
    },


    // clinicid全小写，后端参数写错了不敢改了
    async getClinicEmployee(disabledCancel, keyword = '', clinicid = '', params) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/list-by-clinic',
            method: 'get',
            disabledCancel,
            params: {
                keyword,
                clinicid,
                ...params,
            },
        });
        return res.data;
    },

    async fetchConsultationDoctors() {
        const res = await fetch({
            url: '/api/v3/clinics/employees/hospital/consultation-doctors',
            method: 'get',
        });
        return res.data;
    },

    /**
     * @desc 获取连锁||单店里面所有人员
     * <AUTHOR>
     * @date 2019/05/20 10:44:52
     */
    async fetchChainEmployees(params) {
        const res = await fetch({
            url: '/api/v2/clinics/chain/employees',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * @desc 门诊日志获取连锁||单店里面所有人员
     * <AUTHOR>
     * @date 2021/12/02 04:19:30
     */
    async fetchChainEmployeesByOutPatient(params) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/list-by-clinic',
            method: 'get',
            params,
        });
        return res.data;
    },

    /**
     * 获取连锁下各门店人员
     * <AUTHOR>
     * @date 2020-09-25
     * @param {Object} params 查询参数
     * @returns {Promise}
     */
    async fetchChainEmployeesNew(params) {
        const res = await fetch({
            url: '/api/v3/clinics/chain/clinic-employees',
            method: 'get',
            params,
        });
        return res.data;
    },


    /**
     * @desc 获取诊所的医生标签
     * <AUTHOR>
     * @date 2019/12/09 20:24:06
     */
    async fetchChainTags() {
        const res = await fetch({
            url: '/api/v3/clinics/tags/list-chain-tags',
            method: 'get',
        });
        return {
            data: (res.data && res.data.data && res.data.data.rows) || [],
        };
    },

    /**
     * @desc 新增tag
     * <AUTHOR>
     * @date 2019/12/10 14:38:48
     */
    async createChainTag(data) {
        const res = await fetch({
            url: '/api/v3/clinics/tags',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 修改tag
     * <AUTHOR>
     * @date 2019/12/10 15:13:17
     * @params
     * @return
     */
    async updateChainTag(id, data) {
        const res = await fetch({
            url: `/api/v3/clinics/tags/${id}`,
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 删除tag
     * <AUTHOR>
     * @date 2019/12/10 14:39:54
     */
    async deleteChainTag(id) {
        const res = await fetch({
            url: `/api/v3/clinics/tags/${id}`,
            method: 'delete',
        });
        return res.data;
    },

    /**
     * @desc 2.获取网诊医生列表
     * <AUTHOR>
     * @date 2020/02/15 00:39:28
     */
    async fetchOnlineDoctors(params) {
        const res = await fetch({
            url: '/api/v3/clinics/online-doctors',
            params,
        });
        return res.data;
    },

    async fetchAllSetOnlineDoctors() {
        const res = await fetch({
            url: '/api/v3/clinics/online-doctors/list-by-chain',
        });
        return res.data;
    },

    /**
     * @desc 添加网诊医生
     * <AUTHOR>
     * @date 2020/02/15 00:39:28
     */
    async addOnlineDoctor(data) {
        const res = await fetch({
            url: '/api/v3/clinics/online-doctors/add',
            method: 'post',
            data,
        });
        return res.data;
    },

    /**
     * @desc 5.设置默认
     * <AUTHOR>
     * @date 2020/02/15 19:22:34
     * @params
     * @return
     */
    async setOnlineDefault(data) {
        const res = await fetch({
            url: '/api/v3/clinics/online-doctors/default',
            method: 'put',
            data,
        });
        return res.data;
    },
    async setOnlineDoctor(data) {
        const res = await fetch({
            url: '/api/v3/clinics/online-doctors',
            method: 'post',
            data,
        });
        return res.data;
    },

    async deleteOnlineDoctor(id) {
        const res = await fetch({
            url: `/api/v3/clinics/online-doctors/${id}`,
            method: 'delete',
        });
        return res.data;
    },
    /**
     * @desc 获取网诊医生状态
     * <AUTHOR>
     * @date 2020/02/17 12:11:54
     */
    async fetchOnlineDoctorStatus() {
        const res = await fetch({
            url: '/api/v3/clinics/online-doctors/status',
        });
        return res.data;
    },

    /**
     * @desc 网诊医生上下线
     * <AUTHOR>
     * @date 2020/02/17 12:12:24
     */
    async onlineDoctorStatusSwitch(data) {
        const res = await fetch({
            url: '/api/v3/clinics/online-doctors/status',
            method: 'put',
            data,
        });
        return res.data;
    },

    /**
     * @desc 更新 网诊医生 自动上/下线配置
     * <AUTHOR>
     * @date 2021-07-16 11:41:07
     */
    async updateOnlineDoctorCustomConfig(data) {
        const res = await fetch({
            url: '/api/v3/clinics/online-doctors/auto/config',
            method: 'put',
            data,
        });
        return res.data;
    },


    /**
     * @desc 诊所成员列表（支持按权限查询）
     * <AUTHOR>
     * @date 2020/02/12 20:50:48
     * @params
     * @return
     */
    async fetchEmployeeByModuleId(params, disabledCancel) {
        const res = await fetch({
            url: '/api/v3/clinics/employees',
            params,
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },
            disabledCancel,
        });
        return {
            data: (res.data && res.data.data && res.data.data.rows) || [],
        };
    },

    /**
     * @desc 获取连锁下的全部医生
     * <AUTHOR>
     * @date 2020/10/22 19:41:43
     * @params
     * @return
     */
    async fetchChainDoctors() {
        const res = await fetch.get('/api/v3/clinics/employees/chain-doctors');
        return res.data;
    },
    /**
     * @desc 获取门店下面全部人员
     * <AUTHOR>
     * @date 2020-12-11 11:20:56
     */
    async fetchCurClinicEmployees() {
        const res = await fetch.get('/api/v3/clinics/employees/clinic-employees');
        return res && res.data;
    },
    /**
     * @desc 获取连锁下的全部雇员
     * @return rows 成员列表
     */
    async fetchChainClinicEmployees() {
        const res = await fetch.get('/api/v3/clinics/employees/chain-employees');
        return res && res.data;
    },
    /**
     * @desc 获取scrm版本信息
     */
    async getScrmEdition() {
        const { data } = await fetch({
            url: '/api/v3/clinics/employees/scrm/current-edition',
        });
        return data.data;
    },
    /**
     * @desc 获取诊所信息
     */
    async fetchClinicInformation(id) {
        const res = await fetch({
            url: `/api/v3/clinics/${id}`,
            method: 'get',
        });
        return res && res.data;
    },

    /**
     * 查询当前门店前端版本
     * params 可不传，默认返回 "abc-cis-static-pc-service"
     * @return {Promise<*>}
     */
    async fetchFrontendVersion(params) {
        const res = await fetch({
            url: '/api/v3/clinics/frontend-version',
            method: 'get',
            params,
        });
        return res?.data;
    },

    /**
     * @desc 查询当前门店数据权限
     */
    async fetchDataPermission() {
        const res = await fetch.get('/api/v3/clinics/data-permission');
        return res && res.data;
    },

    /**
     * @desc 修改当前门店数据权
     */
    async updateDataPermission(data) {
        const res = await fetch({
            url: '/api/v3/clinics/data-permission',
            method: 'post',
            data,
        });
        return res && res.data;
    },

    /**
     * 员工行为上报
     * @param key
     * @param name
     * @returns {Promise<*>}
     */
    async postEmployeeButtonClick(key, name) {
        const params = {
            buttonKey: key,
            buttonName: name,
            fromWay: 0,
        };
        const res = await fetch({
            url: '/api/v3/clinics/activity/employee/button-click',
            method: 'post',
            data: params,
        });
        return res.data;
    },
    /**
     * @desc 获取门店当前销售人员
     * <AUTHOR>
     * @date 2022-10-24 13:50:56
     */
    async fetchClinicsEmployeesSeller() {
        const res = await fetch.get('/api/v3/clinics/employees/seller');
        return res && res.data;
    },

    /**
     * @desc 获取字节流员工信息
     * <AUTHOR>
     * @date 2022-12-02 16:08:37
     * @params code 企业微信扫码后的回调
     * @return
     */
    async fetchABCSeller(code) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/seller/login/by-code',
            method: 'get',
            params: {
                code,
            },
            paramsSerializer (params) {
                return Qs.stringify(params, { indices: false });
            },

        });
        return res && res.data;
    },
    // 医院管家：查询门诊医生列表
    async fetchHospitalOutpatientDoctors() {
        const res = await fetch.get('/api/v3/clinics/employees/hospital/outpatient-doctors');
        return res && res.data;
    },
    // 查询门店门诊科室列表
    async fetchOutpatientDepartments() {
        const res = await fetch.get('/api/v3/clinics/departments/outpatient');
        return res && res.data;
    },
    // 查询门店发票信息
    async fetchClinicsInvoiceConfig() {
        const res = await fetch.get('/api/v3/clinics/invoice/config');
        return res && res.data;
    },
    // 门店发票信息配置
    async postClinicsInvoiceConfig(params) {
        const res = await fetch({
            url: '/api/v3/clinics/invoice/config',
            method: 'post',
            data: params,
        });
        return res.data;
    },
    // 申请发票
    async postClinicsInvoiceOrderApply(data) {
        const res = await fetch({
            url: '/api/v3/clinics/invoice/apply/submit',
            method: 'post',
            data,
        });
        return res.data;
    },
    // 门店发票列表
    async fetchClinicsInvoiceOrderApply() {
        const res = await fetch.get('/api/v3/clinics/invoice/apply');
        return res && res.data;
    },
    // 查询发票抬头列表
    async fetchClinicsInvoiceCorp(keyword) {
        const res = await fetch.get(`/api/v3/clinics/invoice/corp?keyword=${keyword}`);
        return res && res.data;
    },
    // 查询发票税号
    async fetchClinicsInvoiceCorpByInvoiceCode(invoiceCode) {
        const res = await fetch.get(`/api/v3/clinics/invoice/corp/by-invoice-code?invoiceCode=${invoiceCode}`);
        return res && res.data;
    },

    // 查询门店管理员列表
    async fetchClinicAdminMemberList() {
        const res = await fetch.get('/api/v3/clinics/employees/admins');
        return res && res.data;
    },

    // 查询门店管理员列表
    async fetchClinicRolesTree() {
        const res = await fetch.get('/api/v3/clinics/roles-tree');
        return res && res.data;
    },

    /**
     * @desc 添加门店角色
     * @param params
     * @return {Promise<any>}
     */
    async addClinicRole(params) {
        const res = await fetch.post('/api/v3/clinics/role/add', params);
        return res && res.data;
    },

    /**
     * @desc 更新门店角色
     * @param roleId
     * @param params
     * @return {Promise<any>}
     */
    async updateClinicRole(roleId, params) {
        const res = await fetch.put(`/api/v3/clinics/role/${roleId}/update`, params);
        return res && res.data;
    },

    /**
     * @desc 删除门店角色
     * @param roleId
     * @return {Promise<any>}
     */
    async deleteClinicRole(roleId) {
        const res = await fetch.delete(`/api/v3/clinics/role/${roleId}/delete`);
        return res && res.data;
    },

    /**
     * @desc 查询雇员概要信息
     * @param roleId
     * @return {Promise<any>}
     */
    async queryEmployeeSummary(roleId) {
        const res = await fetch.get(`/api/v3/clinics/employees/summary?roleId=${roleId}`);
        return res && res.data;
    },

    async fetchClinicEmployeeList() {
        const res = await fetch.get('/api/v3/clinics/employees/list-by-clinic');
        return res && res.data;
    },

    async fetchClinicEmployeeDetail(id) {
        const res = await fetch.get(`/api/v3/clinics/employees/${id}/brief`);
        return res && res.data;
    },
    // 拉取诊所工单列表
    async fetchClinicWorkOrderList(params) {
        const res = await fetch({
            method: 'get',
            url: '/api/v3/clinics/list/by-clinic-id',
            params,
        });
        return res && res.data;
    },

    // 拉取诊所工单列表
    async fetchClinicWorkOrderProcessingCount() {
        const res = await fetch.get('/api/v3/clinics/ticket/processing-count');
        return res && res.data;
    },
    // 查询最新一条OA工单回复
    async fetchClinicWorkOrderReply(ticketId) {
        const res = await fetch.get(`/api/v3/clinics/ticket/reply/${ticketId}`);
        return res && res.data;
    },

    /**
     * @typedef {Object} PrintBrandList
     * @property {string} id 品牌id
     * @property {string} name 品牌名称
     */
    /**
     * @desc 获取打印机品牌列表
     * @return {Promise<PrintBrandList[]>}
     */
    async fetchPrinterBrandList() {
        const res = await fetch.get('/api/v3/clinics/printer/brand/list');
        const { rows } = res.data?.data || {};
        return rows || [];
    },

    /**
     * @typedef {Object} PrintModelList
     * @property {string} id 型号id
     * @property {string} name 型号名称
     * @property {string} brandId 品牌id
     * @property {string} brandName 品牌名称
     */
    /**
     * 根据打印机品牌id获取打印机型号列表
     * @param {string} brandId 品牌id
     * @return {Promise<PrintModelList[]>}
     */
    async fetchPrinterModelList(brandId) {
        const res = await fetch({
            url: '/api/v3/clinics/printer/model/list',
            params: {
                brandId,
            },
            paramsSerializer(val) {
                return Qs.stringify(val);
            },
        });
        const { rows } = res.data?.data || {};
        return rows || [];
    },

    /**
     * @typedef {Object} PrinterDriver
     * @property {string} brandId 品牌id
     * @property {string} brandName 品牌名称
     * @property {string} createdByName 创建人
     * @property {string} createdTime 创建时间
     * @property {string} driverUrl 驱动下载地址
     * @property {string} id 驱动id
     * @property {PrintModelList[]} modelViewList 型号列表
     * @property {string} name 驱动名称
     * @property {string} ossId 驱动名称
     * @property {string} remark 备注
     * @property {number[]} supportSystem 支持的操作系统 0:Win7 1:Win8 2:Win10及以上
     */
    /**
     * @typedef {Object} PrinterDriverParams
     * @property {string} brandId 品牌id
     * @property {string} modelId 型号id
     * @property {number} supportSystem 当前操作系统 0:Win7 1:Win8 2:Win10及以上
     */
    /**
     * 根据打印机品牌id和打印机型号id以及当前操作系统获取打印机驱动
     * @param {PrinterDriverParams} params
     * @return {Promise<PrinterDriver>}
     */
    async fetchPrinterDriver(params) {
        const res = await fetch({
            url: '/api/v3/clinics/printer/driver/detail-by-brand-and-model',
            params,
            paramsSerializer(val) {
                return Qs.stringify(val);
            },
        });
        return res.data?.data || {};
    },

    /**
     * 腾讯地图机构选址
     */
    async searchAddress(params) {
        const res = await fetch({
            url: '/api/v3/clinics/map/address-search',
            method: 'get',
            params,
            paramsSerializer(val) {
                return Qs.stringify(val);
            },
        });
        return res.data?.data;
    },

    /**
     * @desc 根据地图选址结果获取省市区信息
     * @param data
     */
    async searchAddressRegion(params) {
        const res = await fetch({
            url: '/api/v3/clinics/region/by-name',
            method: 'get',
            params,
            paramsSerializer(val) {
                return Qs.stringify(val);
            },
        });
        return res.data?.data;
    },

    async fetchListByCondition(data) {
        const res = await fetch({
            url: '/api/v3/clinics/employees/list-by-condition',
            method: 'post',
            data,
            disabledCancel: true,
        });
        const { data: resData } = res.data || {};
        const { rows } = resData || {};
        return rows || [];
    },

    /**
     * 查询虚拟手机号详情
     */
    async fetchVirtualMobileDetail(mobile) {
        const res = await fetch({
            url: `/api/v3/clinics/employees/virtual-mobile/${mobile}`,
            method: 'get',
            customErrorTips: true,
        });
        return res.data?.data;
    },

    fetchCharConfig() {
        return fetch.get('/api/v3/clinics/chat-config');
    },
};

export default Clinic;
