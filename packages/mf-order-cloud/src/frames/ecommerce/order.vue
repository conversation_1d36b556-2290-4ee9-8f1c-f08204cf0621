<template>
    <abc-card class="content-card-wrapper ecommerce-order-wrapper" :border="false">
        <div class="ecommerce-order-content">
            <abc-layout preset="page-table">
                <abc-layout-header>
                    <abc-flex vertical :gap="16">
                        <abc-tabs-v2
                            v-model="currentTab"
                            :option="tabOptions"
                            size="middle"
                            type="outline"
                            @change="onChangeTab"
                        ></abc-tabs-v2>
                        <abc-flex justify="space-between">
                            <abc-space>
                                <abc-input
                                    v-model.trim="searchParams.keyword"
                                    type="text"
                                    placeholder="订单号/快递号/商品/备注留言"
                                    :width="240"
                                    clearable
                                    @icon-click="searchParams.keyword = ''"
                                >
                                    <abc-search-icon slot="prepend"></abc-search-icon>
                                </abc-input>
                                <abc-space is-compact compact-block>
                                    <abc-select
                                        v-if="currentTab === 3"
                                        :key="currentTab"
                                        v-model="searchParams.dateType"
                                        :width="90"
                                        @change="handleFilterChange"
                                    >
                                        <abc-option :value="3" label="发货时间"></abc-option>
                                        <abc-option :value="1" label="支付时间"></abc-option>
                                    </abc-select>
                                    <abc-select
                                        v-else
                                        :key="currentTab"
                                        v-model="searchParams.dateType"
                                        :width="90"
                                        @change="handleFilterChange"
                                    >
                                        <abc-option :value="1" label="支付时间"></abc-option>
                                        <abc-option :disabled="currentTab === 0" :value="2" label="打印时间"></abc-option>
                                    </abc-select>
                                    <abc-date-picker
                                        v-model="datePickerValue"
                                        :picker-options="pickerOptions"
                                        type="daterange"
                                        :width="256"
                                        :clearable="false"
                                        placeholder="选择日期范围"
                                        @change="handleChangeDate"
                                    >
                                    </abc-date-picker>
                                </abc-space>

                                <abc-select
                                    v-model="searchParams.ecMallId"
                                    :width="150"
                                    clearable
                                    placeholder="网店"
                                    @change="handleFilterChange"
                                >
                                    <abc-option
                                        v-for="item in bindMallList"
                                        :key="item.ecMallId"
                                        :label="item.mallName"
                                        :value="item.ecMallId"
                                    ></abc-option>
                                </abc-select>
                                <abc-select
                                    v-model="searchParams.remarkFilter"
                                    :width="120"
                                    clearable
                                    placeholder="备注留言"
                                    @change="handleFilterChange"
                                >
                                    <abc-option :value="1" label="有商家备注"></abc-option>
                                    <abc-option :value="2" label="无商家备注"></abc-option>
                                    <abc-option :value="3" label="有买家留言"></abc-option>
                                    <abc-option :value="4" label="无买家留言"></abc-option>
                                    <abc-option :value="5" label="无备注/留言"></abc-option>
                                </abc-select>

                                <abc-select
                                    v-model="searchParams.refundStatus"
                                    :width="150"
                                    clearable
                                    placeholder="售后状态"
                                    @change="handleFilterChange"
                                >
                                    <abc-option :value="ECOrderRefundStatusEnum.NONE" label="无售后或售后关闭"></abc-option>
                                    <abc-option :value="ECOrderRefundStatusEnum.HANDING" label="售后中"></abc-option>
                                    <abc-option :value="ECOrderRefundStatusEnum.REFUND_SUCCESS" label="售后成功"></abc-option>
                                </abc-select>
                                <abc-select
                                    v-model="filterAddress"
                                    multiple
                                    clearable
                                    with-search
                                    :fetch-suggestions="handleSearchAddress"
                                    :width="150"
                                    multi-label-mode="text"
                                    placeholder="收货地区"
                                    @change="handleFilterChange"
                                >
                                    <abc-option
                                        v-for="option in filterAddressOptions"
                                        :key="option.value"
                                        :value="option.value"
                                        :label="option.label"
                                    ></abc-option>
                                </abc-select>
                            </abc-space>
                            <abc-space v-if="!disabledOperate">
                                <template v-if="currentTab === 0">
                                    <abc-flex align="center">
                                        <abc-button :count="checkedList.length" @click="printWaybill">
                                            打印快递单
                                        </abc-button>
                                        <abc-button :count="checkedList.length" type="blank" @click="handlePrintShipment">
                                            打印发货单
                                        </abc-button>
                                    </abc-flex>
                                    <abc-dropdown placement="bottom-end" @change="handleBatchChange">
                                        <abc-button
                                            slot="reference"
                                            icon="n-more-line-medium"
                                            type="blank"
                                        >
                                        </abc-button>
                                        <abc-dropdown-item label="批量锁定" value="lock"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量添加备注" value="batchAdd"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量删除备注" value="batchDelete"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量合并订单" value="batchMerge"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量拆分订单" value="batchSplit"></abc-dropdown-item>
                                    </abc-dropdown>
                                </template>
                                <template v-else-if="currentTab === 1">
                                    <abc-button
                                        v-if="currentTab > 0"
                                        :loading="btnLoading"
                                        :count="checkedList.length"
                                        @click="handleBatchShipment"
                                    >
                                        发货
                                    </abc-button>
                                    <abc-dropdown placement="bottom-start" @change="handlePrintChange">
                                        <abc-button slot="reference" type="blank" :count="checkedList.length">
                                            打印快递单
                                        </abc-button>
                                        <abc-dropdown-item value="rePrint" style="flex-direction: column;">
                                            <div>按原单号补打</div>
                                            <div style="font-size: 12px; color: var(--abc-color-T2);">
                                                原打印快递单遗失补打
                                            </div>
                                        </abc-dropdown-item>
                                        <abc-dropdown-item value="newPrint" style="flex-direction: column;">
                                            <div>新增单号打印</div>
                                            <div style="font-size: 12px; color: var(--abc-color-T2);">
                                                多包裹发货，漏发补发
                                            </div>
                                        </abc-dropdown-item>
                                    </abc-dropdown>

                                    <abc-button :count="checkedList.length" type="blank" @click="handlePrintShipment">
                                        打印发货单
                                    </abc-button>
                                    <abc-dropdown placement="bottom-end" @change="handleBatchChange">
                                        <abc-button
                                            slot="reference"
                                            icon="n-more-line-medium"
                                            type="blank"
                                        >
                                        </abc-button>
                                        <abc-dropdown-item label="批量锁定" value="lock"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量添加备注" value="batchAdd"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量删除备注" value="batchDelete"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量合并订单" value="batchMerge"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量拆分订单" value="batchSplit"></abc-dropdown-item>
                                    </abc-dropdown>
                                </template>
                                <template v-if="currentTab === 2">
                                    <abc-button :count="checkedList.length" @click="handleBatchUnlock">
                                        批量解锁
                                    </abc-button>
                                    <abc-dropdown placement="bottom-end" @change="handleBatchChange">
                                        <abc-button
                                            slot="reference"
                                            icon="n-more-line-medium"
                                            type="blank"
                                        >
                                        </abc-button>
                                        <abc-dropdown-item label="批量添加备注" value="batchAdd"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量删除备注" value="batchDelete"></abc-dropdown-item>
                                    </abc-dropdown>
                                </template>
                                <template v-if="currentTab === 3">
                                    <abc-dropdown placement="bottom-start" @change="handlePrintChange">
                                        <abc-button slot="reference" type="blank" :count="checkedList.length">
                                            打印快递单
                                        </abc-button>
                                        <abc-dropdown-item value="rePrint" style="flex-direction: column;">
                                            <div>按原单号补打</div>
                                            <div style="font-size: 12px; color: var(--abc-color-T2);">
                                                原打印快递单遗失补打
                                            </div>
                                        </abc-dropdown-item>
                                        <abc-dropdown-item value="newPrint" style="flex-direction: column;">
                                            <div>新增单号打印</div>
                                            <div style="font-size: 12px; color: var(--abc-color-T2);">
                                                多包裹发货，漏发补发
                                            </div>
                                        </abc-dropdown-item>
                                    </abc-dropdown>

                                    <abc-button :count="checkedList.length" type="blank" @click="handlePrintShipment">
                                        打印发货单
                                    </abc-button>

                                    <abc-dropdown placement="bottom-end" @change="handleBatchChange">
                                        <abc-button
                                            slot="reference"
                                            icon="n-more-line-medium"
                                            type="blank"
                                        >
                                        </abc-button>
                                        <abc-dropdown-item label="批量添加备注" value="batchAdd"></abc-dropdown-item>
                                        <abc-dropdown-item label="批量删除备注" value="batchDelete"></abc-dropdown-item>
                                    </abc-dropdown>
                                </template>
                            </abc-space>
                        </abc-flex>
                    </abc-flex>
                </abc-layout-header>
                <abc-layout-content>
                    <abc-table
                        ref="ecommerceTable"
                        :render-config="tableConfig"
                        :data-list="orderList"
                        cell-padding-size="large"
                        :loading="contentLoading"
                        :tr-click-trigger-checked="false"
                        :show-checked="false"
                        :need-selected="false"
                        class="ecommerce-table"
                        :show-hover-tr-bg="false"
                    >
                        <template #receiverInfo="{ trData: item }">
                            <table-cell-receiver
                                :disabled="disabledOperate || !!item.orderLock"
                                :order-info="item"
                                @refresh="fetchEcOrderList"
                            ></table-cell-receiver>
                        </template>
                        <template #platform="{ trData: item }">
                            <abc-table-cell class="ecommerce-table-cell" vertical justify="center">
                                {{ ECTypeText[item.ecType] }}
                            </abc-table-cell>
                        </template>
                        <template #orderTotal="{ trData: item }">
                            <abc-table-cell
                                class="ecommerce-table-cell"
                                vertical
                                align="flex-start"
                                justify="center"
                            >
                                <div class="ecommerce-table-cell__row">
                                    {{ item.mallName }}
                                </div>
                                <div class="ecommerce-table-cell__row">
                                    {{ getOrderTotalInfo(item) }}
                                </div>
                            </abc-table-cell>
                        </template>
                        <template #orderGoods="{ trData: item }">
                            <table-cell-goods :goods-list="item.goodsList"></table-cell-goods>
                        </template>
                        <template #remark="{ trData: item }">
                            <table-cell-remark :order="item" :disabled="disabledOperate"></table-cell-remark>
                        </template>
                        <template #payTime="{ trData: item }">
                            <abc-table-cell
                                class="ecommerce-table-cell"
                                vertical
                                align="flex-start"
                                justify="center"
                            >
                                <div
                                    v-if="[ECOrderRefundStatusEnum.REFUND_SUCCESS,ECOrderRefundStatusEnum.HANDING].indexOf(item.refundStatus) > -1"
                                    class="ecommerce-table-cell__row"
                                >
                                    -
                                </div>
                                <div
                                    v-else
                                    class="ecommerce-table-cell__row"
                                    :class="{
                                        danger: item.shipTimeRemaining < 0
                                    }"
                                >
                                    {{ getLeftTimeStr(item) }}
                                </div>
                                <div class="ecommerce-table-cell__row">
                                    <abc-p>
                                        {{ item.payTime | formatDate('YYYY-MM-DD HH:mm') }} 支付
                                    </abc-p>
                                </div>
                            </abc-table-cell>
                        </template>
                        <template #stateTime="{ trData: item }">
                            <abc-table-cell
                                class="ecommerce-table-cell"
                                vertical
                                justify="center"
                                align="flex-start"
                            >
                                <abc-text v-if="item.shippingTime">
                                    {{ item.shippingTime | formatDate('YYYY-MM-DD HH:mm') }} 发货
                                </abc-text>
                                <abc-text v-if="item.waybillPrintTime">
                                    {{ item.waybillPrintTime | formatDate('YYYY-MM-DD HH:mm') }} 打单
                                </abc-text>
                            </abc-table-cell>
                        </template>
                        <template #deliveryInfo="{ trData: item }">
                            <table-cell-trace :order-info="item" :disabled="disabledOperate" @refresh="fetchEcOrderList"></table-cell-trace>
                        </template>
                        <template #orderOperate="{ trData: item }">
                            <table-cell-operate :order-info="item" :disabled="disabledOperate" @refresh="fetchEcOrderList"></table-cell-operate>
                        </template>
                    </abc-table>
                </abc-layout-content>
                <abc-layout-footer>
                    <abc-pagination
                        :pagination-params="paginationParams"
                        :count="paginationParams.count"
                        :show-total-page="true"
                        :page-sizes="[20, 50, 100]"
                        show-size
                        @current-change="pageChange"
                        @size-change="sizeChange"
                    ></abc-pagination>
                </abc-layout-footer>
            </abc-layout>
        </div>

        <abc-modal
            v-if="showError"
            v-model="showError"
            preset="confirm"
            show-icon
            type="warn"
            size="medium"
            dialog-content-styles="min-width: 360px"
            :show-confirm="false"
            title="提示"
        >
            <div v-for="err in errorList" :key="err.errorCode">
                <template v-if="err.errorCode === 1">
                    有 {{ err.goodsCount }} 个SKU库存不足
                </template>
                <template v-else-if="err.errorCode === 2">
                    有 {{ err.goodsCount }} 个SKU未绑定ABC商品
                </template>
                ，影响 {{ err.orderIds.length }} 个订单。
            </div>
            <template #footerPrepend>
                <abc-button :loading="btnLoading" @click="batchShipOrder(1)">
                    锁单后发货
                </abc-button>
                <abc-button :loading="btnLoading" type="blank" @click="batchShipOrder(2)">
                    仍对全部订单发货
                </abc-button>
            </template>
        </abc-modal>
    </abc-card>
</template>

<script type="text/ecmascript-6">
    import {
        prevDate,
        formatDate,
    } from '@abc/utils-date';
    import ECOrderAPI from '@/api/order';
    import { ECTypeText } from '@/utils/constants';
    import ECAuthAPI from '@/api/auth';
    import {
        formatMoney, debounce,
    } from '@abc/utils';
    import TableCellReceiver from '../../components/table-cell-receiver.vue';
    import TableCellGoods from '../../components/table-cell-goods.vue';
    import TableCellRemark from '../../components/table-cell-remark.vue';
    import TableCellTrace from '../../components/table-cell-trace.vue';
    import TableCellOperate from '../../components/table-cell-operate.vue';

    import * as business from 'MfFeEngine/business';
    import AbcSocket from 'MfBase/single-socket';
    import {
        ECOrderRefundStatusEnum,
        ECOrderStatusEnum,
        MergeStatusEnum,
        ProvinceList,
        EcShopTypeEnum,
    } from '../../utils/constants';
    import DialogExpressPrint from '../../components/dialog-express-print';
    import DialogExpressRePrint from '../../components/dialog-express-re-print';
    import DialogShipmentPrint from '../../components/dialog-shipment-print';
    import DialogRemarkForm from '../../components/dialog-remark-form';
    import PrintManager from '@/printer/manager/print-manager';
    import { PharmacyOrderCloudRouterNameKeys } from '../../core/routes.js';
    import { EditionKeyEnum } from 'MfBase/access-constant';

    import { mapGetters } from 'vuex';

    export default {
        name: 'PharmacyOrderCloudMain',
        components: {
            TableCellGoods,
            TableCellReceiver,
            TableCellRemark,
            TableCellTrace,
            TableCellOperate,
        },
        filters: {
            formatDate,
        },
        inject: {
            $abcPage: {
                default: {},
            },
            eCOrderStat: {
                default: () => ({
                    toShipUnPrintCount: 0,
                    toShipPrintedCount: 0,
                    toShipLockedCount: 0,
                }),
            },
        },
        data() {
            const now = new Date();
            const today = formatDate(now);
            // 两天前
            const last7Day = formatDate(prevDate(now, 7));
            const last30Day = formatDate(prevDate(now, 30));
            const last90Day = formatDate(prevDate(now, 90));
            const last365Day = formatDate(prevDate(now, 365));

            return {
                ECTypeText,
                ECOrderRefundStatusEnum,
                currentTab: 0,
                loading: false,
                contentLoading: false,
                btnLoading: false,
                showError: false,
                errorList: [],
                searchAddressKey: '',
                searchParams: {
                    keyword: '',
                    orderStatus: 1, // 订单状态 null：全部，1：待发货，2：已发货待签收，3：已签收 0：异常
                    printStatus: 0, // 打印状态，null：全部，0：未打印，1：已打印
                    beginDate: last7Day,
                    endDate: today,
                    dateType: 1, // 时间类型，1：支付时间；2：打印时间； 3：发货时间
                    remarkFilter: null, // 备注过滤，null：全部，1：有商家备注，2：无商家备注，3：有买家留言，4：无买家留言，5：无备注/留言
                    lockFilter: null, // null：全部，0：未锁定，1：已锁定
                    refundStatus: ECOrderRefundStatusEnum.NONE,
                    ecMallId: '',
                    offset: 0,
                    limit: 100,
                },
                filterAddress: [],
                datePickerValue: [last7Day, today],
                pickerStartDate: '', // 获取开始选择时间
                pickerOptions: {
                    onPick: ({
                        minDate, maxDate,
                    }) => {
                        if (minDate) {
                            this.pickerStartDate = minDate.getTime();
                        }
                        if (maxDate) {
                            this.pickerStartDate = '';
                        }
                    },
                    disabledDate: (time) => {
                        const day31 = (365 - 1) * 24 * 3600 * 1000;
                        if (this.pickerStartDate !== '') {
                            let maxTime = this.pickerStartDate + day31;
                            const minTime = this.pickerStartDate - day31;
                            if (maxTime > new Date()) {
                                maxTime = new Date();
                            }
                            return time.getTime() > maxTime ||
                                time.getTime() < minTime ||
                                time.getTime() > Date.now();
                        }
                        return time.getTime() > Date.now();
                    },
                    shortcuts: [
                        {
                            text: '今天',
                            onClick(picker) {
                                picker([
                                    today,
                                    today,
                                    '今天',
                                ]);
                            },
                        },
                        {
                            text: '近7天',
                            onClick(picker) {
                                picker([
                                    last7Day,
                                    today,
                                    '近7天',
                                ]);
                            },
                        },
                        {
                            text: '近30天',
                            onClick(picker) {
                                picker([
                                    last30Day,
                                    today,
                                    '近30天',
                                ]);
                            },
                        },
                        {
                            text: '近90天',
                            onClick(picker) {
                                picker([
                                    last90Day,
                                    today,
                                    '近90天',
                                ]);
                            },
                        },
                        {
                            text: '近1年',
                            onClick(picker) {
                                picker([
                                    last365Day,
                                    today,
                                    '近1年',
                                ]);
                            },
                        },
                    ],
                },
                addressOptions: ProvinceList,

                orderList: [],
                bindMallList: [],

                tableTrHeight: 72,
                orderTotalCount: 0,
                isInDesktop: false,
                hasBindedEc: false,
            };
        },
        computed: {
            ...mapGetters([
                'isChainAdmin',
                'isAdmin',
                'isChainSubStore',
                'isSingleStore',
                'userInfo',
                'currentEdition',
                'subClinics',
            ]),
            tabOptions() {
                return [
                    {
                        label: '待发货-未打印',
                        value: 0,
                        maxNoticeNumber: 99,
                        noticeNumber: this.eCOrderStat?.toShipUnPrintCount,
                    },
                    {
                        label: '待发货-已打印',
                        value: 1,
                        maxNoticeNumber: 99,
                        noticeNumber: this.eCOrderStat?.toShipPrintedCount,
                    },
                    {
                        label: '待发货-已锁定',
                        value: 2,
                        maxNoticeNumber: 99,
                        noticeNumber: this.eCOrderStat?.toShipLockedCount,
                    },
                    {
                        label: '已发货',
                        value: 3,
                    },
                ];
            },

            disabledOperate() {
                return this.isChainAdmin;
            },
            showOrderWarn() {
                return this.isInDesktop && !this.isChainAdmin;
            },
            tableConfig() {
                let list = [];
                if (!this.disabledOperate) {
                    list.push({
                        label: ' ',
                        isCheckbox: true,
                        style: {
                            flex: 'none',
                            width: '41px',
                            display: 'flex',
                            alignItems: 'center',
                        },
                    });
                }
                list = list.concat([
                    {
                        key: 'receiverInfo',
                        label: '收件信息',
                        style: {
                            flex: 4,
                            minWidth: '200px',
                        },
                    },
                    {
                        key: 'platform',
                        label: '平台',
                        style: {
                            width: '67px',
                            maxWidth: '67px',
                            textAlign: 'center',
                        },
                    },
                    {
                        key: 'orderTotal',
                        label: '订单合计',
                        style: {
                            flex: 4,
                            minWidth: '200px',
                        },
                    },
                    {
                        key: 'orderGoods',
                        label: '订单SKU',
                        style: {
                            flex: 5,
                            minWidth: '300px',
                        },
                    },
                    {
                        key: 'remark',
                        label: '备注留言',
                        style: {
                            flex: 'none',
                            width: '176px',
                        },
                    },
                    {
                        key: 'payTime',
                        label: '发货剩余/支付时间',
                        style: {
                            flex: 'none',
                            width: '170px',
                        },
                    },
                    {
                        key: 'stateTime',
                        label: '打单/发货时间',
                        style: {
                            flex: 'none',
                            width: '170px',
                        },
                        hidden: !this.currentTab,
                    },
                    {
                        key: 'deliveryInfo',
                        label: '快递信息',
                        style: {
                            flex: 'none',
                            width: '148px',
                        },
                    },
                    {
                        key: 'orderOperate',
                        label: '订单编号/操作',
                        pinned: 'right',
                        style: {
                            width: '206px',
                            minWidth: '206px',
                            maxWidth: '206px',
                        },
                    },
                ]).filter((item) => item.hidden !== true);
                return {
                    hasInnerBorder: true,
                    list,
                };
            },
            allChecked: {
                get() {
                    return this.orderList.length && this.orderList.every((it) => it.checked);
                },
                set(val) {
                    this.orderList.forEach((it) => {
                        this.$set(it, 'checked', val);
                    });
                },
            },
            checkedList() {
                return this.orderList.filter((it) => it.checked);
            },
            filterAddressOptions() {
                return this.addressOptions.filter((item) => {
                    const searchAddressKey = this.searchAddressKey.toLowerCase();
                    return item.label.indexOf(searchAddressKey) > -1 ||
                        item.py.indexOf(searchAddressKey) > -1 ||
                        item.pyFirst.indexOf(searchAddressKey) > -1;
                });
            },
            paginationParams() {
                const {
                    limit: pageSize, offset,
                } = this.searchParams;
                const pageIndex = Math.ceil(offset / pageSize);
                return {
                    pageIndex,
                    pageSize,
                    count: this.orderTotalCount,
                };
            },
            calcTabAccess() {
                const isBasicEdition = this.currentEdition.key === EditionKeyEnum.BASIC;
                const subClinicList = this.subClinics.filter((item) => !item.chainAdmin);
                const hasProfessional = subClinicList.some((item) => item.edition.key === EditionKeyEnum.PROFESSIONAL);
                if (this.isSingleStore) {
                    if (isBasicEdition) {
                        return false;
                    }
                    return true;
                }

                if (this.isChainSubStore) {
                    if (isBasicEdition) {
                        if (hasProfessional) {
                            return true;
                        }
                        return false;
                    }
                    return true;
                }

                if (this.isChainAdmin) {
                    if (subClinicList.every((item) => item.edition.key === EditionKeyEnum.BASIC)) {
                        return false;
                    }

                    if (subClinicList.some((item) => item.edition.key === EditionKeyEnum.PROFESSIONAL)) {
                        return true;
                    }
                }

                return true;
            },
        },
        watch: {
            'searchParams.keyword': function() {
                this._debounceFetch();
            },
            '$route.query': function(val) {
                const ecCode = localStorage.getItem('ec_code');
                if (ecCode) {
                    this.$router.push({
                        name: PharmacyOrderCloudRouterNameKeys.onlineStore,
                        query: {
                            code: ecCode,
                        },
                    });
                } else {
                    this.currentTab = +val.tab || 0;
                    this.onChangeTab();
                }
            },
        },
        async created() {
            await this.$store.dispatch('fetchChainSubClinics');//拉取所有门店数据
            await this.getBindAuthorizedEc();
            this._debounceFetch = debounce(this.fetchEcOrderList, 250, true);
            const { socket } = AbcSocket.getSocket();
            if (business.ECOrderService) {
                this._SocketService = new business.ECOrderService(socket);
                this._SocketService.onECOrderWaitingShipment(this.refreshWaitingOrder);
            }
            PrintManager.getInstance();

            this.currentTab = +this.$route.query.tab || 0;
            this.onChangeTab();
            this.isInDesktop = window.electronFlag;
            this.getMallList();
            // 路由导航改动，屏蔽此逻辑
            // if (!this.calcTabAccess || (this.calcTabAccess && !this.hasBindedEc)) {
            //     if (+localStorage.getItem('first_go_order_cloud')) {
            //         this.$router.push({
            //             name: PharmacyOrderCloudRouterNameKeys.ecSettingsServiceIntro,
            //         });
            //         localStorage.setItem('first_go_order_cloud', 0);
            //     }
            // }
        },
        updated() {
            this.$nextTick(() => {
                this.calcScrollbar();
            });
        },
        mounted() {
        },
        beforeDestroy() {
            this._SocketService?.destroy();
            this._SocketService = null;
            this._timer && clearTimeout(this._timer);
            this._dialogShipmentPrint && this._dialogShipmentPrint.destroyDialog();
            this._dialogRemarkForm && this._dialogRemarkForm.destroyDialog();
            this._dialogExpressPrint && this._dialogExpressPrint.destroyDialog();
            this._dialogExpressRePrint && this._dialogExpressRePrint.destroyDialog();
        },
        methods: {
            async getBindAuthorizedEc() {
                try {
                    const res = await ECAuthAPI.fetchAuthorizedEcList({
                        limit: 10,
                        offset: 0,
                    });
                    this.hasBindedEc = !!res?.rows?.length;
                } catch (err) {
                    console.error(err);
                }
            },
            async getMallList() {
                try {
                    const res = await ECAuthAPI.fetchBindAuthList({
                        offset: 0,
                        limit: 1000,
                    });
                    this.bindMallList = (res?.rows || []).filter((item) => item.shopType === EcShopTypeEnum.B2C);
                } catch (err) {
                    console.error(err);
                }
            },
            handleChangeAllChecked() {
                this.dataList.forEach((item) => {
                    const { timeGroupViews } = item || {};
                    timeGroupViews.forEach((it) => {
                        it.checked = item.checked;
                    });
                });
            },
            handleFilterChange() {
                this.fetchEcOrderList();
            },
            handleChangeDate() {
                this.pickerStartDate = '';
                this.searchParams.beginDate = this.datePickerValue[0];
                this.searchParams.endDate = this.datePickerValue[1];
                this.fetchEcOrderList();
            },
            calcScrollbar() {
                // 计算是否有滚动条
                if (!this.$refs.ecommerceTableBody) return;
                const $tableBody = this.$refs.ecommerceTableBody.$el;
                if (!$tableBody) return;
                const $table = this.$refs.ecommerceTable;
                $table.hasScrollbar = $tableBody.scrollHeight > $tableBody.clientHeight;
            },
            refreshWaitingOrder(data) {
                console.log('refreshWaitingOrder', data);
                this._debounceFetch();
            },
            getLeftTimeStr(item) {
                if (item.orderStatus !== ECOrderStatusEnum.WAIT) return '';
                const { shipTimeRemaining } = item;
                let seconds = Math.abs(shipTimeRemaining);
                if (!seconds) {
                    return '';
                }
                let str = '';
                const days = Math.floor(seconds / 86400);
                seconds %= 86400;
                let hours = Math.floor(seconds / 3600);
                seconds %= 3600;
                let minutes = Math.floor(seconds / 60);

                if (days) {
                    str += `${days}天`;
                }
                if (hours) {
                    hours = hours.toString().length === 1 ? `0${hours}` : hours;
                    str += `${hours}时`;
                }
                if (minutes) {
                    minutes = minutes.toString().length === 1 ? `0${minutes}` : minutes;
                    str += `${minutes}分`;
                }

                if (shipTimeRemaining > 0) {
                    return `${str}后逾期`;
                }
                return `已逾期 ${str}`;
            },
            shipTimeRemainingCountdown() {
                if (this._timer) {
                    clearInterval(this._timer);
                }
                this._timer = setInterval(() => {

                    this.orderList.forEach((item) => {
                        item.shipTimeRemaining -= 1;
                    });

                }, 1000);
            },

            pageChange(pageIndex) {
                this.searchParams.offset = (pageIndex - 1) * this.searchParams.limit;
                this.fetchEcOrderList();
            },
            sizeChange(pageSize) {
                this.searchParams.limit = pageSize;
                this.fetchEcOrderList();
            },
            async fetchEcOrderList(loading = true) {
                this.contentLoading = loading;
                const {
                    offset,
                } = this.searchParams;
                try {
                    this.filterAddress = this.filterAddress || [];
                    const res = await ECOrderAPI.fetchEcOrderList({
                        ...this.searchParams,
                        areaCodes: this.filterAddress.join(','),
                    });
                    if (offset !== this.searchParams.offset) return;
                    this.orderList = (res.rows || []).map((item) => {
                        item.checked = false;
                        return item;
                    }) || [];
                    this.orderTotalCount = res.total || 0;
                    this.shipTimeRemainingCountdown();
                } catch (err) {
                    console.error(err);
                } finally {
                    this.contentLoading = false;
                }
            },
            getOrderTotalInfo(item) {
                const {
                    actualAmount, totalPrice, goodsList,
                } = item;
                const totalCount = goodsList.reduce((total, it) => {
                    return total + +it.goodsCount;
                }, 0);
                return `总数: ${totalCount}，总价: ${formatMoney(totalPrice)}，实收: ${formatMoney(actualAmount)}`;
            },
            handleSearchAddress(keyword) {
                this.searchAddressKey = keyword || '';
            },
            handlePrintShipment() {
                if (this.checkedList.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '请勾选订单',
                    });
                    return;
                }
                this._dialogShipmentPrint = new DialogShipmentPrint({
                    shipPrintConfig: this.$abcPage.$store.shipPrintConfig,
                    orderList: this.checkedList,
                    onFinish: this.fetchEcOrderList,
                });
                this._dialogShipmentPrint.generateDialogAsync({
                    parent: this,
                });
            },

            async handleBatchShipment() {
                if (this.checkedList.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '请勾选订单',
                    });
                    return;
                }
                this.$confirm({
                    type: 'warn',
                    title: '发货确认',
                    content: '发货完成后，订单SKU绑定的ABC商城库存将自动下账并销售出库。是否确定？',
                    onConfirm: this.batchShipmentSubmit,
                });
            },
            async batchShipmentSubmit() {
                try {
                    this.errorList = [];
                    let patiPageCode = await this.$abcPage.getPatiPageCode({
                        ecMallIds: this.checkedList.map((item) => item.ecMallId),
                    });
                    patiPageCode = patiPageCode || {};
                    const res = await ECOrderAPI.preCheckOrder({
                        ...patiPageCode,
                        orderIds: this.checkedList.map((item) => item.id),
                    });
                    const errorMap = new Map();
                    res?.errorGoodsList?.forEach((it) => {
                        const _err = errorMap.get(it.errorCode);
                        if (_err) {
                            _err.goodsCount += 1;
                            _err.orderIds = _err.orderIds.concat(it.orderIds || []);
                        } else {
                            errorMap.set(it.errorCode, {
                                errorCode: it.errorCode,
                                errorMsg: it.errorMsg,
                                goodsCount: 1,
                                orderIds: it.orderIds || [],
                            });
                        }
                    });
                    errorMap.forEach((item) => {
                        this.errorList.push(item);
                    });
                    if (this.errorList.length) {
                        this.showError = true;
                        return;
                    }

                    this.batchShipOrder(1, patiPageCode);
                } catch (e) {
                    console.error(e);
                }
            },

            async batchShipOrder(shipMode = 1, patiPageCode) {
                try {
                    this.btnLoading = true;

                    const res = await ECOrderAPI.batchShipOrder({
                        ...patiPageCode,
                        orderIds: this.checkedList.map((item) => item.id),
                        shipMode,
                    });
                    if (res.errorOrders && res.errorOrders.length) {
                        this.$alert({
                            type: 'warn',
                            title: `有${res.errorOrders.length}个订单发货失败`,
                            content: res.errorOrders.map((it) => `${it.orderId} - ${it.errorMsg}`),
                        });
                    } else {
                        this.$Toast({
                            message: '发货成功',
                            type: 'success',
                        });
                    }
                    this.fetchEcOrderList();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                    this.showError = false;
                }
            },

            onChangeTab() {
                this.searchParams.lockFilter = 0;
                this.searchParams.dateType = 1;
                this.searchParams.refundStatus = ECOrderRefundStatusEnum.NONE;

                if (this.currentTab === 0) {
                    this.searchParams.orderStatus = 1;
                    this.searchParams.printStatus = 0;
                } else if (this.currentTab === 1) {
                    this.searchParams.orderStatus = 1;
                    this.searchParams.printStatus = 1;
                } else if (this.currentTab === 2) {
                    this.searchParams.orderStatus = null;
                    this.searchParams.printStatus = null;
                    this.searchParams.lockFilter = 1;
                } else if (this.currentTab === 3) {
                    this.searchParams.orderStatus = 2;
                    this.searchParams.dateType = 3;
                    this.searchParams.printStatus = null;
                    this.searchParams.refundStatus = null;
                } else {
                    this.searchParams.orderStatus = null;
                    this.searchParams.printStatus = null;
                }
                this.$router.replace({
                    query: {
                        ...this.$route.query,
                        tab: this.currentTab,
                    },
                });
                this._debounceFetch();
            },

            handleBatchChange(val) {
                if (this.checkedList.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '请勾选订单',
                    });
                    return;
                }
                if (val === 'lock') {
                    this.orderLockSubmit(this.checkedList, 1);
                } else if (val === 'batchAdd') {
                    this._dialogRemarkForm = new DialogRemarkForm({
                        orderList: this.checkedList,
                        onRefresh: this.fetchEcOrderList,
                    });
                    this._dialogRemarkForm.generateDialogAsync();
                } else if (val === 'batchDelete') {
                    this.batchDeleteHandler();
                } else if (val === 'batchMerge') {
                    this.batchMergeHandler();
                } else if (val === 'batchSplit') {
                    this.batchSplitHandler();
                }
            },
            async batchDeleteHandler() {
                const hasRemarkList = this.checkedList.filter((it) => it.remark);
                if (hasRemarkList.length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '无可删除备注',
                        content: '所选订单中均没有商家备注',
                    });
                    return;
                }
                this.$confirm({
                    type: 'warn',
                    title: '删除备注确认',
                    content: `有${hasRemarkList.length}个订单存在商家备注，是否确认删除`,
                    onConfirm: () => {
                        this.deleteOrderRemark(hasRemarkList);
                    },
                });
            },
            async deleteOrderRemark(checkedList) {
                await ECOrderAPI.updateOrderRemark({
                    orderIds: checkedList.map((it) => it.id),
                    note: null,
                });
                this.$Toast({
                    type: 'success',
                    message: '删除备注成功',
                });
                this.fetchEcOrderList(false);
            },
            handleBatchUnlock() {
                if (this.checkedList.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '请勾选订单',
                    });
                    return;
                }
                this.orderLockSubmit(this.checkedList, 0);
            },
            async orderLockSubmit(order, orderLock = 0) {
                await ECOrderAPI.orderLock({
                    orderIds: order.map((it) => it.id),
                    orderLock,
                });
                this.$Toast({
                    message: orderLock ? '锁定成功' : '解锁成功',
                    type: 'success',
                });
                this.fetchEcOrderList(false);
            },
            handlePrintChange(val) {
                if (val === 'rePrint') {
                    this.rePrintWaybill();
                    return;
                }
                this.printWaybill();
            },
            printWaybill() {
                if (this.checkedList.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '请勾选订单',
                    });
                    return;
                }
                this._dialogExpressPrint = new DialogExpressPrint({
                    waybillType: 1,
                    shipPrintConfig: this.$abcPage.$store.shipPrintConfig,
                    orderList: this.checkedList,
                    onFinish: this.fetchEcOrderList,
                });
                this._dialogExpressPrint.generateDialogAsync({
                    parent: this,
                });
            },
            /**
             * @desc 按原单号补打
             * <AUTHOR>
             * @date 2024-04-17 17:07:14
             */
            rePrintWaybill() {
                if (this.checkedList.length === 0) {
                    this.$Toast({
                        type: 'error',
                        message: '请勾选订单',
                    });
                    return;
                }
                // 获取打印过的订单
                const printedList = this.checkedList.filter((it) => it.waybillPrintStatus);
                if (printedList.length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '订单未打印过快递单',
                        content: `选择的 ${this.checkedList.length} 个订单未打印过快递单，无法按原单号打印快递单`,
                    });
                    return;
                }
                if (printedList.length !== this.checkedList.length) {
                    const _length = this.checkedList.length - printedList.length;
                    this.$confirm({
                        type: 'warn',
                        title: '部分订单未打印过快递单',
                        content: [
                            `有 ${_length} 个订单未打印过快递单，可对剩余 ${printedList.length} 个订单按原单号`,
                            '打印快递单，是否继续打印',
                        ],
                        confirmText: '继续打印',
                        onConfirm: () => {
                            this.showExpressRePrintDialog(printedList);
                        },
                    });
                    return;
                }

                this.showExpressRePrintDialog();
            },
            showExpressRePrintDialog(list) {
                this._dialogExpressRePrint = new DialogExpressRePrint({
                    shipPrintConfig: this.$abcPage.$store.shipPrintConfig,
                    orderList: list || this.checkedList,
                    onFinish: this.fetchEcOrderList,
                });
                this._dialogExpressRePrint.generateDialogAsync({
                    parent: this,
                });
            },
            batchMergeHandler() {
                const mergeMap = new Map();
                const refundList = [];
                this.checkedList.forEach((it) => {
                    if (
                        [
                            ECOrderRefundStatusEnum.HANDING,
                            ECOrderRefundStatusEnum.REFUND_SUCCESS,
                        ].indexOf(it.refundStatus) > -1
                    ) {
                        refundList.push(it);
                    }

                    if (it.mergeStatus === MergeStatusEnum.WAIT) {
                        const res = mergeMap.get(it.mergeTag);
                        if (res) {
                            res.push(it);
                        } else {
                            mergeMap.set(it.mergeTag, [it]);
                        }
                    }
                });
                const res = Array.from(mergeMap.values()).filter((it) => it.length > 1);
                const canMergeList = res.reduce((acc, it) => {
                    acc = acc.concat(it);
                    return acc;
                }, []);
                if (canMergeList.length) {
                    this.$confirm({
                        type: 'warn',
                        title: '合并订单确认',
                        content: `有 ${canMergeList.length} 个订单存收件人姓名、手机、地址一致的情况，可合并为 ${res.length} 个订单。是否确认合并？`,
                        onConfirm: () => {
                            this.batchMergeSubmit(canMergeList);
                        },
                    });
                    return;
                }

                const content = [
                    '所选订单中不存在收件人姓名、手机、地址一致的情况，无法合并。',
                ];
                if (refundList && refundList.length) {
                    content.push('售后订单无法合并');
                }
                this.$alert({
                    type: 'warn',
                    title: '无可合并订单',
                    content,
                });
            },
            async batchMergeSubmit(canMergeOrderList) {
                try {
                    this.btnLoading = true;
                    await ECOrderAPI.mergeOrders({
                        orderIds: canMergeOrderList.map((it) => it.id),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '合并订单成功',
                    });
                    this.fetchEcOrderList();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },
            batchSplitHandler() {
                const canSplitList = this.checkedList.filter((it) => {
                    return it.mergeStatus === MergeStatusEnum.MERGE_ED;
                });
                if (canSplitList.length) {
                    const total = canSplitList.reduce((acc, it) => {
                        acc += it.detailOrders.length;
                        return acc;
                    }, 0);
                    this.$confirm({
                        type: 'warn',
                        title: '拆分订单确认',
                        content: `有 ${canSplitList.length} 个合并发货的订单，可拆分成 ${total} 个原始订单。是否确认拆分？`,
                        onConfirm: () => {
                            this.batchSplitSubmit(canSplitList);
                        },
                    });
                    return;
                }
                this.$alert({
                    type: 'warn',
                    title: '无可拆分订单',
                    content: '所选订单中不存在合并发货的订单，无法拆分。',
                });
            },
            async batchSplitSubmit(canSplitList) {
                try {
                    this.btnLoading = true;
                    await ECOrderAPI.splitOrders({
                        orderIds: canSplitList.map((it) => it.id),
                    });
                    this.$Toast({
                        type: 'success',
                        message: '拆分订单成功',
                    });
                    this.fetchEcOrderList();
                } catch (e) {
                    console.error(e);
                } finally {
                    this.btnLoading = false;
                }
            },

        },
    };
</script>

<style lang="scss">
    .ecommerce-order-wrapper {
        position: relative;

        .ecommerce-order-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .order-cloud-warn-wrapper {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 1;
        }
    }
</style>

