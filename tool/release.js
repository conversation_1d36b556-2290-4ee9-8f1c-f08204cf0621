// 用于发布业务组件scr/components-composite
const { promisify } = require('util');
const fs = require('fs');
const { exec } = require('child_process');
const { execSync } = require('child_process');
const path = require('path');
const rootDir = process.cwd();
const pkgPath = path.join(rootDir, 'package.json');

const needReviseBranchMap = new Map([
    ['master', 'latest'],
    ['gray', 'next'],
    ['rc', 'rc'],
    ['develop', 'beta'],
]);

// 封装执行 shell 命令的函数
const executeShellCommand = async (command) => {
    const execPromisified = promisify(exec);
    const {
        stdout,
        stderr
    } = await execPromisified(command);
    if (stderr) {
        throw new Error(`执行命令失败：${stderr}`);
    }
    return stdout.trim();
};

/**
 * 版本比较 VersionCompare
 * @param {String} curVersion 当前版本
 * @param {String} supportVersion 比较版本
 * @return {Boolean} false 当前版本小于比较版本返回 true
 */
const versionCompare = (curVersion, supportVersion) => {
    if (!curVersion) {
        return false;
    }
    if (!supportVersion) {
        return false;
    }
    // 相等 也是比较关键的一步
    if (curVersion === supportVersion) {
        return true;
    }
    const curArr = curVersion.split('.');
    const supportArr = supportVersion.split('.');
    for (let i = 0; i < curArr.length; i += 1) {
        // 只有当两个版本号不相等才比较
        if (+curArr[i] !== +supportArr[i]) {
            // 直接返回 结果，中止循环
            return +curArr[i] > +supportArr[i];
        }
    }
    return false;
};

// 同步远程版本
const syncRemoteVersions = () => {
    try {
        const pkg = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'));

        console.group('----------远程最新版本----------');
        let res = execSync(`npm view ${pkg.name} versions --json`).toString().trim();
        res = JSON.parse(res);
        res = res.filter((it) => it.indexOf('canary') === -1 && it.indexOf('-') === -1);
        const remoteV = res[res.length - 1];
        // 对比本地和远程tag，用最新的那一个
        if (versionCompare(remoteV, pkg.version)) {
            pkg.version = remoteV;
        }
        console.log(pkg.name, pkg.version);
        console.groupEnd('----------远程最新版本----------');

        fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2));
    } catch (error) {
        console.error(`同步远程版本：${error.message}`);
    }
};
const publishPackages = (tag, currentBranch) => {
    // 发布包
    if (tag) {
        // 增加补丁版本号
        execSync('yarn version --patch --no-git-tag-version');

        // 读取新版本号
        const pkgJson = JSON.parse(fs.readFileSync(pkgPath, 'utf-8'));
        const newVersion = pkgJson.version;
        console.log(`----------新版本号：${newVersion}----------`);
        execSync(`yarn publish --tag ${tag} --access public --non-interactive`);
        // 推送到远程
        execSync(`git add . && git commit -m 'chore(build-biz): release v${newVersion}' && git push origin ${currentBranch}`);
        console.log(`----------git push origin ${currentBranch} success----------`);
    } else {
        console.log(`----------没有tag：发布canary版本----------`);
        execSync('yarn canary');
        execSync(`git add . && git commit -m 'chore(build-biz): publish canary version' && git push origin ${currentBranch}`);
    }
};
// 主函数
const runLernaVersion = async () => {
    const currentBranch = await executeShellCommand('git rev-parse --abbrev-ref HEAD');
    console.log(`----------当前分支：${currentBranch}----------`);

    const tag = needReviseBranchMap.get(currentBranch);
    console.log(`----------当前tag：${tag}----------`);
    execSync('yarn install');
    console.log('----------yarn install success----------');

    console.log('----------yarn build start----------');
    execSync('yarn build');
    console.log('----------yarn build success----------');

    execSync('yarn vetur');
    console.log('----------yarn vetur success----------');

    syncRemoteVersions();
    console.log('----------syncRemoteVersions success----------');

    publishPackages(tag, currentBranch);
    console.log('----------publishPackages success----------');
};

runLernaVersion().catch((error) => {
    console.error(`发布版本失败：${error.message}`);
    console.error(error);
});

